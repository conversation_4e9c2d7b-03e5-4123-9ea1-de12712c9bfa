{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport GlobalHistogramBinarizer from './GlobalHistogramBinarizer';\nimport BitMatrix from './BitMatrix';\n/**\n * This class implements a local thresholding algorithm, which while slower than the\n * GlobalHistogramBinarizer, is fairly efficient for what it does. It is designed for\n * high frequency images of barcodes with black data on white backgrounds. For this application,\n * it does a much better job than a global blackpoint with severe shadows and gradients.\n * However it tends to produce artifacts on lower frequency images and is therefore not\n * a good general purpose binarizer for uses outside ZXing.\n *\n * This class extends GlobalHistogramBinarizer, using the older histogram approach for 1D readers,\n * and the newer local approach for 2D readers. 1D decoding using a per-row histogram is already\n * inherently local, and only fails for horizontal gradients. We can revisit that problem later,\n * but for now it was not a win to use local blocks for 1D.\n *\n * This Binarizer is the default for the unit tests and the recommended class for library users.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar HybridBinarizer = /** @class */function (_super) {\n  __extends(HybridBinarizer, _super);\n  function HybridBinarizer(source) {\n    var _this = _super.call(this, source) || this;\n    _this.matrix = null;\n    return _this;\n  }\n  /**\n   * Calculates the final BitMatrix once for all requests. This could be called once from the\n   * constructor instead, but there are some advantages to doing it lazily, such as making\n   * profiling easier, and not doing heavy lifting when callers don't expect it.\n   */\n  /*@Override*/\n  HybridBinarizer.prototype.getBlackMatrix = function () {\n    if (this.matrix !== null) {\n      return this.matrix;\n    }\n    var source = this.getLuminanceSource();\n    var width = source.getWidth();\n    var height = source.getHeight();\n    if (width >= HybridBinarizer.MINIMUM_DIMENSION && height >= HybridBinarizer.MINIMUM_DIMENSION) {\n      var luminances = source.getMatrix();\n      var subWidth = width >> HybridBinarizer.BLOCK_SIZE_POWER;\n      if ((width & HybridBinarizer.BLOCK_SIZE_MASK) !== 0) {\n        subWidth++;\n      }\n      var subHeight = height >> HybridBinarizer.BLOCK_SIZE_POWER;\n      if ((height & HybridBinarizer.BLOCK_SIZE_MASK) !== 0) {\n        subHeight++;\n      }\n      var blackPoints = HybridBinarizer.calculateBlackPoints(luminances, subWidth, subHeight, width, height);\n      var newMatrix = new BitMatrix(width, height);\n      HybridBinarizer.calculateThresholdForBlock(luminances, subWidth, subHeight, width, height, blackPoints, newMatrix);\n      this.matrix = newMatrix;\n    } else {\n      // If the image is too small, fall back to the global histogram approach.\n      this.matrix = _super.prototype.getBlackMatrix.call(this);\n    }\n    return this.matrix;\n  };\n  /*@Override*/\n  HybridBinarizer.prototype.createBinarizer = function (source) {\n    return new HybridBinarizer(source);\n  };\n  /**\n   * For each block in the image, calculate the average black point using a 5x5 grid\n   * of the blocks around it. Also handles the corner cases (fractional blocks are computed based\n   * on the last pixels in the row/column which are also used in the previous block).\n   */\n  HybridBinarizer.calculateThresholdForBlock = function (luminances, subWidth /*int*/, subHeight /*int*/, width /*int*/, height /*int*/, blackPoints, matrix) {\n    var maxYOffset = height - HybridBinarizer.BLOCK_SIZE;\n    var maxXOffset = width - HybridBinarizer.BLOCK_SIZE;\n    for (var y = 0; y < subHeight; y++) {\n      var yoffset = y << HybridBinarizer.BLOCK_SIZE_POWER;\n      if (yoffset > maxYOffset) {\n        yoffset = maxYOffset;\n      }\n      var top_1 = HybridBinarizer.cap(y, 2, subHeight - 3);\n      for (var x = 0; x < subWidth; x++) {\n        var xoffset = x << HybridBinarizer.BLOCK_SIZE_POWER;\n        if (xoffset > maxXOffset) {\n          xoffset = maxXOffset;\n        }\n        var left = HybridBinarizer.cap(x, 2, subWidth - 3);\n        var sum = 0;\n        for (var z = -2; z <= 2; z++) {\n          var blackRow = blackPoints[top_1 + z];\n          sum += blackRow[left - 2] + blackRow[left - 1] + blackRow[left] + blackRow[left + 1] + blackRow[left + 2];\n        }\n        var average = sum / 25;\n        HybridBinarizer.thresholdBlock(luminances, xoffset, yoffset, average, width, matrix);\n      }\n    }\n  };\n  HybridBinarizer.cap = function (value /*int*/, min /*int*/, max /*int*/) {\n    return value < min ? min : value > max ? max : value;\n  };\n  /**\n   * Applies a single threshold to a block of pixels.\n   */\n  HybridBinarizer.thresholdBlock = function (luminances, xoffset /*int*/, yoffset /*int*/, threshold /*int*/, stride /*int*/, matrix) {\n    for (var y = 0, offset = yoffset * stride + xoffset; y < HybridBinarizer.BLOCK_SIZE; y++, offset += stride) {\n      for (var x = 0; x < HybridBinarizer.BLOCK_SIZE; x++) {\n        // Comparison needs to be <= so that black == 0 pixels are black even if the threshold is 0.\n        if ((luminances[offset + x] & 0xFF) <= threshold) {\n          matrix.set(xoffset + x, yoffset + y);\n        }\n      }\n    }\n  };\n  /**\n   * Calculates a single black point for each block of pixels and saves it away.\n   * See the following thread for a discussion of this algorithm:\n   *  http://groups.google.com/group/zxing/browse_thread/thread/d06efa2c35a7ddc0\n   */\n  HybridBinarizer.calculateBlackPoints = function (luminances, subWidth /*int*/, subHeight /*int*/, width /*int*/, height /*int*/) {\n    var maxYOffset = height - HybridBinarizer.BLOCK_SIZE;\n    var maxXOffset = width - HybridBinarizer.BLOCK_SIZE;\n    // tslint:disable-next-line:whitespace\n    var blackPoints = new Array(subHeight); // subWidth\n    for (var y = 0; y < subHeight; y++) {\n      blackPoints[y] = new Int32Array(subWidth);\n      var yoffset = y << HybridBinarizer.BLOCK_SIZE_POWER;\n      if (yoffset > maxYOffset) {\n        yoffset = maxYOffset;\n      }\n      for (var x = 0; x < subWidth; x++) {\n        var xoffset = x << HybridBinarizer.BLOCK_SIZE_POWER;\n        if (xoffset > maxXOffset) {\n          xoffset = maxXOffset;\n        }\n        var sum = 0;\n        var min = 0xFF;\n        var max = 0;\n        for (var yy = 0, offset = yoffset * width + xoffset; yy < HybridBinarizer.BLOCK_SIZE; yy++, offset += width) {\n          for (var xx = 0; xx < HybridBinarizer.BLOCK_SIZE; xx++) {\n            var pixel = luminances[offset + xx] & 0xFF;\n            sum += pixel;\n            // still looking for good contrast\n            if (pixel < min) {\n              min = pixel;\n            }\n            if (pixel > max) {\n              max = pixel;\n            }\n          }\n          // short-circuit min/max tests once dynamic range is met\n          if (max - min > HybridBinarizer.MIN_DYNAMIC_RANGE) {\n            // finish the rest of the rows quickly\n            for (yy++, offset += width; yy < HybridBinarizer.BLOCK_SIZE; yy++, offset += width) {\n              for (var xx = 0; xx < HybridBinarizer.BLOCK_SIZE; xx++) {\n                sum += luminances[offset + xx] & 0xFF;\n              }\n            }\n          }\n        }\n        // The default estimate is the average of the values in the block.\n        var average = sum >> HybridBinarizer.BLOCK_SIZE_POWER * 2;\n        if (max - min <= HybridBinarizer.MIN_DYNAMIC_RANGE) {\n          // If variation within the block is low, assume this is a block with only light or only\n          // dark pixels. In that case we do not want to use the average, as it would divide this\n          // low contrast area into black and white pixels, essentially creating data out of noise.\n          //\n          // The default assumption is that the block is light/background. Since no estimate for\n          // the level of dark pixels exists locally, use half the min for the block.\n          average = min / 2;\n          if (y > 0 && x > 0) {\n            // Correct the \"white background\" assumption for blocks that have neighbors by comparing\n            // the pixels in this block to the previously calculated black points. This is based on\n            // the fact that dark barcode symbology is always surrounded by some amount of light\n            // background for which reasonable black point estimates were made. The bp estimated at\n            // the boundaries is used for the interior.\n            // The (min < bp) is arbitrary but works better than other heuristics that were tried.\n            var averageNeighborBlackPoint = (blackPoints[y - 1][x] + 2 * blackPoints[y][x - 1] + blackPoints[y - 1][x - 1]) / 4;\n            if (min < averageNeighborBlackPoint) {\n              average = averageNeighborBlackPoint;\n            }\n          }\n        }\n        blackPoints[y][x] = average;\n      }\n    }\n    return blackPoints;\n  };\n  // This class uses 5x5 blocks to compute local luminance, where each block is 8x8 pixels.\n  // So this is the smallest dimension in each axis we can accept.\n  HybridBinarizer.BLOCK_SIZE_POWER = 3;\n  HybridBinarizer.BLOCK_SIZE = 1 << HybridBinarizer.BLOCK_SIZE_POWER; // ...0100...00\n  HybridBinarizer.BLOCK_SIZE_MASK = HybridBinarizer.BLOCK_SIZE - 1; // ...0011...11\n  HybridBinarizer.MINIMUM_DIMENSION = HybridBinarizer.BLOCK_SIZE * 5;\n  HybridBinarizer.MIN_DYNAMIC_RANGE = 24;\n  return HybridBinarizer;\n}(GlobalHistogramBinarizer);\nexport default HybridBinarizer;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "GlobalHistogramBinarizer", "BitMatrix", "HybridBinarizer", "_super", "source", "_this", "call", "matrix", "getBlackMatrix", "getLuminanceSource", "width", "getWidth", "height", "getHeight", "MINIMUM_DIMENSION", "luminances", "getMatrix", "subWidth", "BLOCK_SIZE_POWER", "BLOCK_SIZE_MASK", "subHeight", "blackPoints", "calculateBlackPoints", "newMatrix", "calculateThresholdForBlock", "createBinarizer", "maxYOffset", "BLOCK_SIZE", "maxXOffset", "y", "yoffset", "top_1", "cap", "x", "xoffset", "left", "sum", "z", "blackRow", "average", "thresholdBlock", "value", "min", "max", "threshold", "stride", "offset", "set", "Int32Array", "yy", "xx", "pixel", "MIN_DYNAMIC_RANGE", "averageNeighborBlackPoint"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/HybridBinarizer.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport GlobalHistogramBinarizer from './GlobalHistogramBinarizer';\nimport BitMatrix from './BitMatrix';\n/**\n * This class implements a local thresholding algorithm, which while slower than the\n * GlobalHistogramBinarizer, is fairly efficient for what it does. It is designed for\n * high frequency images of barcodes with black data on white backgrounds. For this application,\n * it does a much better job than a global blackpoint with severe shadows and gradients.\n * However it tends to produce artifacts on lower frequency images and is therefore not\n * a good general purpose binarizer for uses outside ZXing.\n *\n * This class extends GlobalHistogramBinarizer, using the older histogram approach for 1D readers,\n * and the newer local approach for 2D readers. 1D decoding using a per-row histogram is already\n * inherently local, and only fails for horizontal gradients. We can revisit that problem later,\n * but for now it was not a win to use local blocks for 1D.\n *\n * This Binarizer is the default for the unit tests and the recommended class for library users.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar HybridBinarizer = /** @class */ (function (_super) {\n    __extends(HybridBinarizer, _super);\n    function HybridBinarizer(source) {\n        var _this = _super.call(this, source) || this;\n        _this.matrix = null;\n        return _this;\n    }\n    /**\n     * Calculates the final BitMatrix once for all requests. This could be called once from the\n     * constructor instead, but there are some advantages to doing it lazily, such as making\n     * profiling easier, and not doing heavy lifting when callers don't expect it.\n     */\n    /*@Override*/\n    HybridBinarizer.prototype.getBlackMatrix = function () {\n        if (this.matrix !== null) {\n            return this.matrix;\n        }\n        var source = this.getLuminanceSource();\n        var width = source.getWidth();\n        var height = source.getHeight();\n        if (width >= HybridBinarizer.MINIMUM_DIMENSION && height >= HybridBinarizer.MINIMUM_DIMENSION) {\n            var luminances = source.getMatrix();\n            var subWidth = width >> HybridBinarizer.BLOCK_SIZE_POWER;\n            if ((width & HybridBinarizer.BLOCK_SIZE_MASK) !== 0) {\n                subWidth++;\n            }\n            var subHeight = height >> HybridBinarizer.BLOCK_SIZE_POWER;\n            if ((height & HybridBinarizer.BLOCK_SIZE_MASK) !== 0) {\n                subHeight++;\n            }\n            var blackPoints = HybridBinarizer.calculateBlackPoints(luminances, subWidth, subHeight, width, height);\n            var newMatrix = new BitMatrix(width, height);\n            HybridBinarizer.calculateThresholdForBlock(luminances, subWidth, subHeight, width, height, blackPoints, newMatrix);\n            this.matrix = newMatrix;\n        }\n        else {\n            // If the image is too small, fall back to the global histogram approach.\n            this.matrix = _super.prototype.getBlackMatrix.call(this);\n        }\n        return this.matrix;\n    };\n    /*@Override*/\n    HybridBinarizer.prototype.createBinarizer = function (source) {\n        return new HybridBinarizer(source);\n    };\n    /**\n     * For each block in the image, calculate the average black point using a 5x5 grid\n     * of the blocks around it. Also handles the corner cases (fractional blocks are computed based\n     * on the last pixels in the row/column which are also used in the previous block).\n     */\n    HybridBinarizer.calculateThresholdForBlock = function (luminances, subWidth /*int*/, subHeight /*int*/, width /*int*/, height /*int*/, blackPoints, matrix) {\n        var maxYOffset = height - HybridBinarizer.BLOCK_SIZE;\n        var maxXOffset = width - HybridBinarizer.BLOCK_SIZE;\n        for (var y = 0; y < subHeight; y++) {\n            var yoffset = y << HybridBinarizer.BLOCK_SIZE_POWER;\n            if (yoffset > maxYOffset) {\n                yoffset = maxYOffset;\n            }\n            var top_1 = HybridBinarizer.cap(y, 2, subHeight - 3);\n            for (var x = 0; x < subWidth; x++) {\n                var xoffset = x << HybridBinarizer.BLOCK_SIZE_POWER;\n                if (xoffset > maxXOffset) {\n                    xoffset = maxXOffset;\n                }\n                var left = HybridBinarizer.cap(x, 2, subWidth - 3);\n                var sum = 0;\n                for (var z = -2; z <= 2; z++) {\n                    var blackRow = blackPoints[top_1 + z];\n                    sum += blackRow[left - 2] + blackRow[left - 1] + blackRow[left] + blackRow[left + 1] + blackRow[left + 2];\n                }\n                var average = sum / 25;\n                HybridBinarizer.thresholdBlock(luminances, xoffset, yoffset, average, width, matrix);\n            }\n        }\n    };\n    HybridBinarizer.cap = function (value /*int*/, min /*int*/, max /*int*/) {\n        return value < min ? min : value > max ? max : value;\n    };\n    /**\n     * Applies a single threshold to a block of pixels.\n     */\n    HybridBinarizer.thresholdBlock = function (luminances, xoffset /*int*/, yoffset /*int*/, threshold /*int*/, stride /*int*/, matrix) {\n        for (var y = 0, offset = yoffset * stride + xoffset; y < HybridBinarizer.BLOCK_SIZE; y++, offset += stride) {\n            for (var x = 0; x < HybridBinarizer.BLOCK_SIZE; x++) {\n                // Comparison needs to be <= so that black == 0 pixels are black even if the threshold is 0.\n                if ((luminances[offset + x] & 0xFF) <= threshold) {\n                    matrix.set(xoffset + x, yoffset + y);\n                }\n            }\n        }\n    };\n    /**\n     * Calculates a single black point for each block of pixels and saves it away.\n     * See the following thread for a discussion of this algorithm:\n     *  http://groups.google.com/group/zxing/browse_thread/thread/d06efa2c35a7ddc0\n     */\n    HybridBinarizer.calculateBlackPoints = function (luminances, subWidth /*int*/, subHeight /*int*/, width /*int*/, height /*int*/) {\n        var maxYOffset = height - HybridBinarizer.BLOCK_SIZE;\n        var maxXOffset = width - HybridBinarizer.BLOCK_SIZE;\n        // tslint:disable-next-line:whitespace\n        var blackPoints = new Array(subHeight); // subWidth\n        for (var y = 0; y < subHeight; y++) {\n            blackPoints[y] = new Int32Array(subWidth);\n            var yoffset = y << HybridBinarizer.BLOCK_SIZE_POWER;\n            if (yoffset > maxYOffset) {\n                yoffset = maxYOffset;\n            }\n            for (var x = 0; x < subWidth; x++) {\n                var xoffset = x << HybridBinarizer.BLOCK_SIZE_POWER;\n                if (xoffset > maxXOffset) {\n                    xoffset = maxXOffset;\n                }\n                var sum = 0;\n                var min = 0xFF;\n                var max = 0;\n                for (var yy = 0, offset = yoffset * width + xoffset; yy < HybridBinarizer.BLOCK_SIZE; yy++, offset += width) {\n                    for (var xx = 0; xx < HybridBinarizer.BLOCK_SIZE; xx++) {\n                        var pixel = luminances[offset + xx] & 0xFF;\n                        sum += pixel;\n                        // still looking for good contrast\n                        if (pixel < min) {\n                            min = pixel;\n                        }\n                        if (pixel > max) {\n                            max = pixel;\n                        }\n                    }\n                    // short-circuit min/max tests once dynamic range is met\n                    if (max - min > HybridBinarizer.MIN_DYNAMIC_RANGE) {\n                        // finish the rest of the rows quickly\n                        for (yy++, offset += width; yy < HybridBinarizer.BLOCK_SIZE; yy++, offset += width) {\n                            for (var xx = 0; xx < HybridBinarizer.BLOCK_SIZE; xx++) {\n                                sum += luminances[offset + xx] & 0xFF;\n                            }\n                        }\n                    }\n                }\n                // The default estimate is the average of the values in the block.\n                var average = sum >> (HybridBinarizer.BLOCK_SIZE_POWER * 2);\n                if (max - min <= HybridBinarizer.MIN_DYNAMIC_RANGE) {\n                    // If variation within the block is low, assume this is a block with only light or only\n                    // dark pixels. In that case we do not want to use the average, as it would divide this\n                    // low contrast area into black and white pixels, essentially creating data out of noise.\n                    //\n                    // The default assumption is that the block is light/background. Since no estimate for\n                    // the level of dark pixels exists locally, use half the min for the block.\n                    average = min / 2;\n                    if (y > 0 && x > 0) {\n                        // Correct the \"white background\" assumption for blocks that have neighbors by comparing\n                        // the pixels in this block to the previously calculated black points. This is based on\n                        // the fact that dark barcode symbology is always surrounded by some amount of light\n                        // background for which reasonable black point estimates were made. The bp estimated at\n                        // the boundaries is used for the interior.\n                        // The (min < bp) is arbitrary but works better than other heuristics that were tried.\n                        var averageNeighborBlackPoint = (blackPoints[y - 1][x] + (2 * blackPoints[y][x - 1]) + blackPoints[y - 1][x - 1]) / 4;\n                        if (min < averageNeighborBlackPoint) {\n                            average = averageNeighborBlackPoint;\n                        }\n                    }\n                }\n                blackPoints[y][x] = average;\n            }\n        }\n        return blackPoints;\n    };\n    // This class uses 5x5 blocks to compute local luminance, where each block is 8x8 pixels.\n    // So this is the smallest dimension in each axis we can accept.\n    HybridBinarizer.BLOCK_SIZE_POWER = 3;\n    HybridBinarizer.BLOCK_SIZE = 1 << HybridBinarizer.BLOCK_SIZE_POWER; // ...0100...00\n    HybridBinarizer.BLOCK_SIZE_MASK = HybridBinarizer.BLOCK_SIZE - 1; // ...0011...11\n    HybridBinarizer.MINIMUM_DIMENSION = HybridBinarizer.BLOCK_SIZE * 5;\n    HybridBinarizer.MIN_DYNAMIC_RANGE = 24;\n    return HybridBinarizer;\n}(GlobalHistogramBinarizer));\nexport default HybridBinarizer;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,SAAS,MAAM,aAAa;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,UAAUC,MAAM,EAAE;EACnDjB,SAAS,CAACgB,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAACE,MAAM,EAAE;IAC7B,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC,IAAI,IAAI;IAC7CC,KAAK,CAACE,MAAM,GAAG,IAAI;IACnB,OAAOF,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACI;EACAH,eAAe,CAACJ,SAAS,CAACU,cAAc,GAAG,YAAY;IACnD,IAAI,IAAI,CAACD,MAAM,KAAK,IAAI,EAAE;MACtB,OAAO,IAAI,CAACA,MAAM;IACtB;IACA,IAAIH,MAAM,GAAG,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACtC,IAAIC,KAAK,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC;IAC7B,IAAIC,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,CAAC;IAC/B,IAAIH,KAAK,IAAIR,eAAe,CAACY,iBAAiB,IAAIF,MAAM,IAAIV,eAAe,CAACY,iBAAiB,EAAE;MAC3F,IAAIC,UAAU,GAAGX,MAAM,CAACY,SAAS,CAAC,CAAC;MACnC,IAAIC,QAAQ,GAAGP,KAAK,IAAIR,eAAe,CAACgB,gBAAgB;MACxD,IAAI,CAACR,KAAK,GAAGR,eAAe,CAACiB,eAAe,MAAM,CAAC,EAAE;QACjDF,QAAQ,EAAE;MACd;MACA,IAAIG,SAAS,GAAGR,MAAM,IAAIV,eAAe,CAACgB,gBAAgB;MAC1D,IAAI,CAACN,MAAM,GAAGV,eAAe,CAACiB,eAAe,MAAM,CAAC,EAAE;QAClDC,SAAS,EAAE;MACf;MACA,IAAIC,WAAW,GAAGnB,eAAe,CAACoB,oBAAoB,CAACP,UAAU,EAAEE,QAAQ,EAAEG,SAAS,EAAEV,KAAK,EAAEE,MAAM,CAAC;MACtG,IAAIW,SAAS,GAAG,IAAItB,SAAS,CAACS,KAAK,EAAEE,MAAM,CAAC;MAC5CV,eAAe,CAACsB,0BAA0B,CAACT,UAAU,EAAEE,QAAQ,EAAEG,SAAS,EAAEV,KAAK,EAAEE,MAAM,EAAES,WAAW,EAAEE,SAAS,CAAC;MAClH,IAAI,CAAChB,MAAM,GAAGgB,SAAS;IAC3B,CAAC,MACI;MACD;MACA,IAAI,CAAChB,MAAM,GAAGJ,MAAM,CAACL,SAAS,CAACU,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC;IAC5D;IACA,OAAO,IAAI,CAACC,MAAM;EACtB,CAAC;EACD;EACAL,eAAe,CAACJ,SAAS,CAAC2B,eAAe,GAAG,UAAUrB,MAAM,EAAE;IAC1D,OAAO,IAAIF,eAAe,CAACE,MAAM,CAAC;EACtC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIF,eAAe,CAACsB,0BAA0B,GAAG,UAAUT,UAAU,EAAEE,QAAQ,CAAC,SAASG,SAAS,CAAC,SAASV,KAAK,CAAC,SAASE,MAAM,CAAC,SAASS,WAAW,EAAEd,MAAM,EAAE;IACxJ,IAAImB,UAAU,GAAGd,MAAM,GAAGV,eAAe,CAACyB,UAAU;IACpD,IAAIC,UAAU,GAAGlB,KAAK,GAAGR,eAAe,CAACyB,UAAU;IACnD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,EAAES,CAAC,EAAE,EAAE;MAChC,IAAIC,OAAO,GAAGD,CAAC,IAAI3B,eAAe,CAACgB,gBAAgB;MACnD,IAAIY,OAAO,GAAGJ,UAAU,EAAE;QACtBI,OAAO,GAAGJ,UAAU;MACxB;MACA,IAAIK,KAAK,GAAG7B,eAAe,CAAC8B,GAAG,CAACH,CAAC,EAAE,CAAC,EAAET,SAAS,GAAG,CAAC,CAAC;MACpD,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,QAAQ,EAAEgB,CAAC,EAAE,EAAE;QAC/B,IAAIC,OAAO,GAAGD,CAAC,IAAI/B,eAAe,CAACgB,gBAAgB;QACnD,IAAIgB,OAAO,GAAGN,UAAU,EAAE;UACtBM,OAAO,GAAGN,UAAU;QACxB;QACA,IAAIO,IAAI,GAAGjC,eAAe,CAAC8B,GAAG,CAACC,CAAC,EAAE,CAAC,EAAEhB,QAAQ,GAAG,CAAC,CAAC;QAClD,IAAImB,GAAG,GAAG,CAAC;QACX,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1B,IAAIC,QAAQ,GAAGjB,WAAW,CAACU,KAAK,GAAGM,CAAC,CAAC;UACrCD,GAAG,IAAIE,QAAQ,CAACH,IAAI,GAAG,CAAC,CAAC,GAAGG,QAAQ,CAACH,IAAI,GAAG,CAAC,CAAC,GAAGG,QAAQ,CAACH,IAAI,CAAC,GAAGG,QAAQ,CAACH,IAAI,GAAG,CAAC,CAAC,GAAGG,QAAQ,CAACH,IAAI,GAAG,CAAC,CAAC;QAC7G;QACA,IAAII,OAAO,GAAGH,GAAG,GAAG,EAAE;QACtBlC,eAAe,CAACsC,cAAc,CAACzB,UAAU,EAAEmB,OAAO,EAAEJ,OAAO,EAAES,OAAO,EAAE7B,KAAK,EAAEH,MAAM,CAAC;MACxF;IACJ;EACJ,CAAC;EACDL,eAAe,CAAC8B,GAAG,GAAG,UAAUS,KAAK,CAAC,SAASC,GAAG,CAAC,SAASC,GAAG,CAAC,SAAS;IACrE,OAAOF,KAAK,GAAGC,GAAG,GAAGA,GAAG,GAAGD,KAAK,GAAGE,GAAG,GAAGA,GAAG,GAAGF,KAAK;EACxD,CAAC;EACD;AACJ;AACA;EACIvC,eAAe,CAACsC,cAAc,GAAG,UAAUzB,UAAU,EAAEmB,OAAO,CAAC,SAASJ,OAAO,CAAC,SAASc,SAAS,CAAC,SAASC,MAAM,CAAC,SAAStC,MAAM,EAAE;IAChI,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEiB,MAAM,GAAGhB,OAAO,GAAGe,MAAM,GAAGX,OAAO,EAAEL,CAAC,GAAG3B,eAAe,CAACyB,UAAU,EAAEE,CAAC,EAAE,EAAEiB,MAAM,IAAID,MAAM,EAAE;MACxG,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,eAAe,CAACyB,UAAU,EAAEM,CAAC,EAAE,EAAE;QACjD;QACA,IAAI,CAAClB,UAAU,CAAC+B,MAAM,GAAGb,CAAC,CAAC,GAAG,IAAI,KAAKW,SAAS,EAAE;UAC9CrC,MAAM,CAACwC,GAAG,CAACb,OAAO,GAAGD,CAAC,EAAEH,OAAO,GAAGD,CAAC,CAAC;QACxC;MACJ;IACJ;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI3B,eAAe,CAACoB,oBAAoB,GAAG,UAAUP,UAAU,EAAEE,QAAQ,CAAC,SAASG,SAAS,CAAC,SAASV,KAAK,CAAC,SAASE,MAAM,CAAC,SAAS;IAC7H,IAAIc,UAAU,GAAGd,MAAM,GAAGV,eAAe,CAACyB,UAAU;IACpD,IAAIC,UAAU,GAAGlB,KAAK,GAAGR,eAAe,CAACyB,UAAU;IACnD;IACA,IAAIN,WAAW,GAAG,IAAI5B,KAAK,CAAC2B,SAAS,CAAC,CAAC,CAAC;IACxC,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,SAAS,EAAES,CAAC,EAAE,EAAE;MAChCR,WAAW,CAACQ,CAAC,CAAC,GAAG,IAAImB,UAAU,CAAC/B,QAAQ,CAAC;MACzC,IAAIa,OAAO,GAAGD,CAAC,IAAI3B,eAAe,CAACgB,gBAAgB;MACnD,IAAIY,OAAO,GAAGJ,UAAU,EAAE;QACtBI,OAAO,GAAGJ,UAAU;MACxB;MACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,QAAQ,EAAEgB,CAAC,EAAE,EAAE;QAC/B,IAAIC,OAAO,GAAGD,CAAC,IAAI/B,eAAe,CAACgB,gBAAgB;QACnD,IAAIgB,OAAO,GAAGN,UAAU,EAAE;UACtBM,OAAO,GAAGN,UAAU;QACxB;QACA,IAAIQ,GAAG,GAAG,CAAC;QACX,IAAIM,GAAG,GAAG,IAAI;QACd,IAAIC,GAAG,GAAG,CAAC;QACX,KAAK,IAAIM,EAAE,GAAG,CAAC,EAAEH,MAAM,GAAGhB,OAAO,GAAGpB,KAAK,GAAGwB,OAAO,EAAEe,EAAE,GAAG/C,eAAe,CAACyB,UAAU,EAAEsB,EAAE,EAAE,EAAEH,MAAM,IAAIpC,KAAK,EAAE;UACzG,KAAK,IAAIwC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGhD,eAAe,CAACyB,UAAU,EAAEuB,EAAE,EAAE,EAAE;YACpD,IAAIC,KAAK,GAAGpC,UAAU,CAAC+B,MAAM,GAAGI,EAAE,CAAC,GAAG,IAAI;YAC1Cd,GAAG,IAAIe,KAAK;YACZ;YACA,IAAIA,KAAK,GAAGT,GAAG,EAAE;cACbA,GAAG,GAAGS,KAAK;YACf;YACA,IAAIA,KAAK,GAAGR,GAAG,EAAE;cACbA,GAAG,GAAGQ,KAAK;YACf;UACJ;UACA;UACA,IAAIR,GAAG,GAAGD,GAAG,GAAGxC,eAAe,CAACkD,iBAAiB,EAAE;YAC/C;YACA,KAAKH,EAAE,EAAE,EAAEH,MAAM,IAAIpC,KAAK,EAAEuC,EAAE,GAAG/C,eAAe,CAACyB,UAAU,EAAEsB,EAAE,EAAE,EAAEH,MAAM,IAAIpC,KAAK,EAAE;cAChF,KAAK,IAAIwC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGhD,eAAe,CAACyB,UAAU,EAAEuB,EAAE,EAAE,EAAE;gBACpDd,GAAG,IAAIrB,UAAU,CAAC+B,MAAM,GAAGI,EAAE,CAAC,GAAG,IAAI;cACzC;YACJ;UACJ;QACJ;QACA;QACA,IAAIX,OAAO,GAAGH,GAAG,IAAKlC,eAAe,CAACgB,gBAAgB,GAAG,CAAE;QAC3D,IAAIyB,GAAG,GAAGD,GAAG,IAAIxC,eAAe,CAACkD,iBAAiB,EAAE;UAChD;UACA;UACA;UACA;UACA;UACA;UACAb,OAAO,GAAGG,GAAG,GAAG,CAAC;UACjB,IAAIb,CAAC,GAAG,CAAC,IAAII,CAAC,GAAG,CAAC,EAAE;YAChB;YACA;YACA;YACA;YACA;YACA;YACA,IAAIoB,yBAAyB,GAAG,CAAChC,WAAW,CAACQ,CAAC,GAAG,CAAC,CAAC,CAACI,CAAC,CAAC,GAAI,CAAC,GAAGZ,WAAW,CAACQ,CAAC,CAAC,CAACI,CAAC,GAAG,CAAC,CAAE,GAAGZ,WAAW,CAACQ,CAAC,GAAG,CAAC,CAAC,CAACI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACrH,IAAIS,GAAG,GAAGW,yBAAyB,EAAE;cACjCd,OAAO,GAAGc,yBAAyB;YACvC;UACJ;QACJ;QACAhC,WAAW,CAACQ,CAAC,CAAC,CAACI,CAAC,CAAC,GAAGM,OAAO;MAC/B;IACJ;IACA,OAAOlB,WAAW;EACtB,CAAC;EACD;EACA;EACAnB,eAAe,CAACgB,gBAAgB,GAAG,CAAC;EACpChB,eAAe,CAACyB,UAAU,GAAG,CAAC,IAAIzB,eAAe,CAACgB,gBAAgB,CAAC,CAAC;EACpEhB,eAAe,CAACiB,eAAe,GAAGjB,eAAe,CAACyB,UAAU,GAAG,CAAC,CAAC,CAAC;EAClEzB,eAAe,CAACY,iBAAiB,GAAGZ,eAAe,CAACyB,UAAU,GAAG,CAAC;EAClEzB,eAAe,CAACkD,iBAAiB,GAAG,EAAE;EACtC,OAAOlD,eAAe;AAC1B,CAAC,CAACF,wBAAwB,CAAE;AAC5B,eAAeE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}