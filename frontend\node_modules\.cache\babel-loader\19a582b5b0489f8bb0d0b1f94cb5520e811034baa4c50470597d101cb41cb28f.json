{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Represents some type of metadata about the result of the decoding that the decoder\n * wishes to communicate back to the caller.\n *\n * <AUTHOR> Owen\n */\nvar ResultMetadataType;\n(function (ResultMetadataType) {\n  /**\n   * Unspecified, application-specific metadata. Maps to an unspecified {@link Object}.\n   */\n  ResultMetadataType[ResultMetadataType[\"OTHER\"] = 0] = \"OTHER\";\n  /**\n   * Denotes the likely approximate orientation of the barcode in the image. This value\n   * is given as degrees rotated clockwise from the normal, upright orientation.\n   * For example a 1D barcode which was found by reading top-to-bottom would be\n   * said to have orientation \"90\". This key maps to an {@link Integer} whose\n   * value is in the range [0,360).\n   */\n  ResultMetadataType[ResultMetadataType[\"ORIENTATION\"] = 1] = \"ORIENTATION\";\n  /**\n   * <p>2D barcode formats typically encode text, but allow for a sort of 'byte mode'\n   * which is sometimes used to encode binary data. While {@link Result} makes available\n   * the complete raw bytes in the barcode for these formats, it does not offer the bytes\n   * from the byte segments alone.</p>\n   *\n   * <p>This maps to a {@link java.util.List} of byte arrays corresponding to the\n   * raw bytes in the byte segments in the barcode, in order.</p>\n   */\n  ResultMetadataType[ResultMetadataType[\"BYTE_SEGMENTS\"] = 2] = \"BYTE_SEGMENTS\";\n  /**\n   * Error correction level used, if applicable. The value type depends on the\n   * format, but is typically a String.\n   */\n  ResultMetadataType[ResultMetadataType[\"ERROR_CORRECTION_LEVEL\"] = 3] = \"ERROR_CORRECTION_LEVEL\";\n  /**\n   * For some periodicals, indicates the issue number as an {@link Integer}.\n   */\n  ResultMetadataType[ResultMetadataType[\"ISSUE_NUMBER\"] = 4] = \"ISSUE_NUMBER\";\n  /**\n   * For some products, indicates the suggested retail price in the barcode as a\n   * formatted {@link String}.\n   */\n  ResultMetadataType[ResultMetadataType[\"SUGGESTED_PRICE\"] = 5] = \"SUGGESTED_PRICE\";\n  /**\n   * For some products, the possible country of manufacture as a {@link String} denoting the\n   * ISO country code. Some map to multiple possible countries, like \"US/CA\".\n   */\n  ResultMetadataType[ResultMetadataType[\"POSSIBLE_COUNTRY\"] = 6] = \"POSSIBLE_COUNTRY\";\n  /**\n   * For some products, the extension text\n   */\n  ResultMetadataType[ResultMetadataType[\"UPC_EAN_EXTENSION\"] = 7] = \"UPC_EAN_EXTENSION\";\n  /**\n   * PDF417-specific metadata\n   */\n  ResultMetadataType[ResultMetadataType[\"PDF417_EXTRA_METADATA\"] = 8] = \"PDF417_EXTRA_METADATA\";\n  /**\n   * If the code format supports structured append and the current scanned code is part of one then the\n   * sequence number is given with it.\n   */\n  ResultMetadataType[ResultMetadataType[\"STRUCTURED_APPEND_SEQUENCE\"] = 9] = \"STRUCTURED_APPEND_SEQUENCE\";\n  /**\n   * If the code format supports structured append and the current scanned code is part of one then the\n   * parity is given with it.\n   */\n  ResultMetadataType[ResultMetadataType[\"STRUCTURED_APPEND_PARITY\"] = 10] = \"STRUCTURED_APPEND_PARITY\";\n})(ResultMetadataType || (ResultMetadataType = {}));\nexport default ResultMetadataType;", "map": {"version": 3, "names": ["ResultMetadataType"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/ResultMetadataType.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Represents some type of metadata about the result of the decoding that the decoder\n * wishes to communicate back to the caller.\n *\n * <AUTHOR> Owen\n */\nvar ResultMetadataType;\n(function (ResultMetadataType) {\n    /**\n     * Unspecified, application-specific metadata. Maps to an unspecified {@link Object}.\n     */\n    ResultMetadataType[ResultMetadataType[\"OTHER\"] = 0] = \"OTHER\";\n    /**\n     * Denotes the likely approximate orientation of the barcode in the image. This value\n     * is given as degrees rotated clockwise from the normal, upright orientation.\n     * For example a 1D barcode which was found by reading top-to-bottom would be\n     * said to have orientation \"90\". This key maps to an {@link Integer} whose\n     * value is in the range [0,360).\n     */\n    ResultMetadataType[ResultMetadataType[\"ORIENTATION\"] = 1] = \"ORIENTATION\";\n    /**\n     * <p>2D barcode formats typically encode text, but allow for a sort of 'byte mode'\n     * which is sometimes used to encode binary data. While {@link Result} makes available\n     * the complete raw bytes in the barcode for these formats, it does not offer the bytes\n     * from the byte segments alone.</p>\n     *\n     * <p>This maps to a {@link java.util.List} of byte arrays corresponding to the\n     * raw bytes in the byte segments in the barcode, in order.</p>\n     */\n    ResultMetadataType[ResultMetadataType[\"BYTE_SEGMENTS\"] = 2] = \"BYTE_SEGMENTS\";\n    /**\n     * Error correction level used, if applicable. The value type depends on the\n     * format, but is typically a String.\n     */\n    ResultMetadataType[ResultMetadataType[\"ERROR_CORRECTION_LEVEL\"] = 3] = \"ERROR_CORRECTION_LEVEL\";\n    /**\n     * For some periodicals, indicates the issue number as an {@link Integer}.\n     */\n    ResultMetadataType[ResultMetadataType[\"ISSUE_NUMBER\"] = 4] = \"ISSUE_NUMBER\";\n    /**\n     * For some products, indicates the suggested retail price in the barcode as a\n     * formatted {@link String}.\n     */\n    ResultMetadataType[ResultMetadataType[\"SUGGESTED_PRICE\"] = 5] = \"SUGGESTED_PRICE\";\n    /**\n     * For some products, the possible country of manufacture as a {@link String} denoting the\n     * ISO country code. Some map to multiple possible countries, like \"US/CA\".\n     */\n    ResultMetadataType[ResultMetadataType[\"POSSIBLE_COUNTRY\"] = 6] = \"POSSIBLE_COUNTRY\";\n    /**\n     * For some products, the extension text\n     */\n    ResultMetadataType[ResultMetadataType[\"UPC_EAN_EXTENSION\"] = 7] = \"UPC_EAN_EXTENSION\";\n    /**\n     * PDF417-specific metadata\n     */\n    ResultMetadataType[ResultMetadataType[\"PDF417_EXTRA_METADATA\"] = 8] = \"PDF417_EXTRA_METADATA\";\n    /**\n     * If the code format supports structured append and the current scanned code is part of one then the\n     * sequence number is given with it.\n     */\n    ResultMetadataType[ResultMetadataType[\"STRUCTURED_APPEND_SEQUENCE\"] = 9] = \"STRUCTURED_APPEND_SEQUENCE\";\n    /**\n     * If the code format supports structured append and the current scanned code is part of one then the\n     * parity is given with it.\n     */\n    ResultMetadataType[ResultMetadataType[\"STRUCTURED_APPEND_PARITY\"] = 10] = \"STRUCTURED_APPEND_PARITY\";\n})(ResultMetadataType || (ResultMetadataType = {}));\nexport default ResultMetadataType;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,kBAAkB;AACtB,CAAC,UAAUA,kBAAkB,EAAE;EAC3B;AACJ;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC7D;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EACzE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EAC7E;AACJ;AACA;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,GAAG,wBAAwB;EAC/F;AACJ;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EAC3E;AACJ;AACA;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,GAAG,iBAAiB;EACjF;AACJ;AACA;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EACnF;AACJ;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;EACrF;AACJ;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,GAAG,uBAAuB;EAC7F;AACJ;AACA;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,GAAG,4BAA4B;EACvG;AACJ;AACA;AACA;EACIA,kBAAkB,CAACA,kBAAkB,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC,GAAG,0BAA0B;AACxG,CAAC,EAAEA,kBAAkB,KAAKA,kBAAkB,GAAG,CAAC,CAAC,CAAC,CAAC;AACnD,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}