{"ast": null, "code": "/**\n * @deprecated Moving to @zxing/browser\n *\n * Video input device metadata containing the id and label of the device if available.\n */\nvar VideoInputDevice = /** @class */function () {\n  /**\n   * Creates an instance of VideoInputDevice.\n   *\n   * @param {string} deviceId the video input device id\n   * @param {string} label the label of the device if available\n   */\n  function VideoInputDevice(deviceId, label, groupId) {\n    this.deviceId = deviceId;\n    this.label = label;\n    /** @inheritdoc */\n    this.kind = 'videoinput';\n    this.groupId = groupId || undefined;\n  }\n  /** @inheritdoc */\n  VideoInputDevice.prototype.toJSON = function () {\n    return {\n      kind: this.kind,\n      groupId: this.groupId,\n      deviceId: this.deviceId,\n      label: this.label\n    };\n  };\n  return VideoInputDevice;\n}();\nexport { VideoInputDevice };", "map": {"version": 3, "names": ["VideoInputDevice", "deviceId", "label", "groupId", "kind", "undefined", "prototype", "toJSON"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/browser/VideoInputDevice.js"], "sourcesContent": ["/**\n * @deprecated Moving to @zxing/browser\n *\n * Video input device metadata containing the id and label of the device if available.\n */\nvar VideoInputDevice = /** @class */ (function () {\n    /**\n     * Creates an instance of VideoInputDevice.\n     *\n     * @param {string} deviceId the video input device id\n     * @param {string} label the label of the device if available\n     */\n    function VideoInputDevice(deviceId, label, groupId) {\n        this.deviceId = deviceId;\n        this.label = label;\n        /** @inheritdoc */\n        this.kind = 'videoinput';\n        this.groupId = groupId || undefined;\n    }\n    /** @inheritdoc */\n    VideoInputDevice.prototype.toJSON = function () {\n        return {\n            kind: this.kind,\n            groupId: this.groupId,\n            deviceId: this.deviceId,\n            label: this.label,\n        };\n    };\n    return VideoInputDevice;\n}());\nexport { VideoInputDevice };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,gBAAgB,GAAG,aAAe,YAAY;EAC9C;AACJ;AACA;AACA;AACA;AACA;EACI,SAASA,gBAAgBA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,EAAE;IAChD,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB;IACA,IAAI,CAACE,IAAI,GAAG,YAAY;IACxB,IAAI,CAACD,OAAO,GAAGA,OAAO,IAAIE,SAAS;EACvC;EACA;EACAL,gBAAgB,CAACM,SAAS,CAACC,MAAM,GAAG,YAAY;IAC5C,OAAO;MACHH,IAAI,EAAE,IAAI,CAACA,IAAI;MACfD,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,KAAK,EAAE,IAAI,CAACA;IAChB,CAAC;EACL,CAAC;EACD,OAAOF,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,SAASA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}