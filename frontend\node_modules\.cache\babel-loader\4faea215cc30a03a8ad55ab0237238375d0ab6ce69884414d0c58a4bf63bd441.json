{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport StringBuilder from '../util/StringBuilder';\nimport System from '../util/System';\nimport OneDReader from './OneDReader';\n/**\n * <p>Decodes ITF barcodes.</p>\n *\n * <AUTHOR>\n */\nvar ITFReader = /** @class */function (_super) {\n  __extends(ITFReader, _super);\n  function ITFReader() {\n    // private static W = 3; // Pixel width of a 3x wide line\n    // private static w = 2; // Pixel width of a 2x wide line\n    // private static N = 1; // Pixed width of a narrow line\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    // Stores the actual narrow line width of the image being decoded.\n    _this.narrowLineWidth = -1;\n    return _this;\n  }\n  // See ITFWriter.PATTERNS\n  /*\n     /!**\n   * Patterns of Wide / Narrow lines to indicate each digit\n   *!/\n  */\n  ITFReader.prototype.decodeRow = function (rowNumber, row, hints) {\n    var e_1, _a;\n    // Find out where the Middle section (payload) starts & ends\n    var startRange = this.decodeStart(row);\n    var endRange = this.decodeEnd(row);\n    var result = new StringBuilder();\n    ITFReader.decodeMiddle(row, startRange[1], endRange[0], result);\n    var resultString = result.toString();\n    var allowedLengths = null;\n    if (hints != null) {\n      allowedLengths = hints.get(DecodeHintType.ALLOWED_LENGTHS);\n    }\n    if (allowedLengths == null) {\n      allowedLengths = ITFReader.DEFAULT_ALLOWED_LENGTHS;\n    }\n    // To avoid false positives with 2D barcodes (and other patterns), make\n    // an assumption that the decoded string must be a 'standard' length if it's short\n    var length = resultString.length;\n    var lengthOK = false;\n    var maxAllowedLength = 0;\n    try {\n      for (var allowedLengths_1 = __values(allowedLengths), allowedLengths_1_1 = allowedLengths_1.next(); !allowedLengths_1_1.done; allowedLengths_1_1 = allowedLengths_1.next()) {\n        var value = allowedLengths_1_1.value;\n        if (length === value) {\n          lengthOK = true;\n          break;\n        }\n        if (value > maxAllowedLength) {\n          maxAllowedLength = value;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (allowedLengths_1_1 && !allowedLengths_1_1.done && (_a = allowedLengths_1.return)) _a.call(allowedLengths_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    if (!lengthOK && length > maxAllowedLength) {\n      lengthOK = true;\n    }\n    if (!lengthOK) {\n      throw new FormatException();\n    }\n    var points = [new ResultPoint(startRange[1], rowNumber), new ResultPoint(endRange[0], rowNumber)];\n    var resultReturn = new Result(resultString, null,\n    // no natural byte representation for these barcodes\n    0, points, BarcodeFormat.ITF, new Date().getTime());\n    return resultReturn;\n  };\n  /*\n  /!**\n   * @param row          row of black/white values to search\n   * @param payloadStart offset of start pattern\n   * @param resultString {@link StringBuilder} to append decoded chars to\n   * @throws NotFoundException if decoding could not complete successfully\n   *!/*/\n  ITFReader.decodeMiddle = function (row, payloadStart, payloadEnd, resultString) {\n    // Digits are interleaved in pairs - 5 black lines for one digit, and the\n    // 5\n    // interleaved white lines for the second digit.\n    // Therefore, need to scan 10 lines and then\n    // split these into two arrays\n    var counterDigitPair = new Int32Array(10); // 10\n    var counterBlack = new Int32Array(5); // 5\n    var counterWhite = new Int32Array(5); // 5\n    counterDigitPair.fill(0);\n    counterBlack.fill(0);\n    counterWhite.fill(0);\n    while (payloadStart < payloadEnd) {\n      // Get 10 runs of black/white.\n      OneDReader.recordPattern(row, payloadStart, counterDigitPair);\n      // Split them into each array\n      for (var k = 0; k < 5; k++) {\n        var twoK = 2 * k;\n        counterBlack[k] = counterDigitPair[twoK];\n        counterWhite[k] = counterDigitPair[twoK + 1];\n      }\n      var bestMatch = ITFReader.decodeDigit(counterBlack);\n      resultString.append(bestMatch.toString());\n      bestMatch = this.decodeDigit(counterWhite);\n      resultString.append(bestMatch.toString());\n      counterDigitPair.forEach(function (counterDigit) {\n        payloadStart += counterDigit;\n      });\n    }\n  };\n  /*/!**\n   * Identify where the start of the middle / payload section starts.\n   *\n   * @param row row of black/white values to search\n   * @return Array, containing index of start of 'start block' and end of\n   *         'start block'\n   *!/*/\n  ITFReader.prototype.decodeStart = function (row) {\n    var endStart = ITFReader.skipWhiteSpace(row);\n    var startPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.START_PATTERN);\n    // Determine the width of a narrow line in pixels. We can do this by\n    // getting the width of the start pattern and dividing by 4 because its\n    // made up of 4 narrow lines.\n    this.narrowLineWidth = (startPattern[1] - startPattern[0]) / 4;\n    this.validateQuietZone(row, startPattern[0]);\n    return startPattern;\n  };\n  /*/!**\n   * The start & end patterns must be pre/post fixed by a quiet zone. This\n   * zone must be at least 10 times the width of a narrow line.  Scan back until\n   * we either get to the start of the barcode or match the necessary number of\n   * quiet zone pixels.\n   *\n   * Note: Its assumed the row is reversed when using this method to find\n   * quiet zone after the end pattern.\n   *\n   * ref: http://www.barcode-1.net/i25code.html\n   *\n   * @param row bit array representing the scanned barcode.\n   * @param startPattern index into row of the start or end pattern.\n   * @throws NotFoundException if the quiet zone cannot be found\n   *!/*/\n  ITFReader.prototype.validateQuietZone = function (row, startPattern) {\n    var quietCount = this.narrowLineWidth * 10; // expect to find this many pixels of quiet zone\n    // if there are not so many pixel at all let's try as many as possible\n    quietCount = quietCount < startPattern ? quietCount : startPattern;\n    for (var i = startPattern - 1; quietCount > 0 && i >= 0; i--) {\n      if (row.get(i)) {\n        break;\n      }\n      quietCount--;\n    }\n    if (quietCount !== 0) {\n      // Unable to find the necessary number of quiet zone pixels.\n      throw new NotFoundException();\n    }\n  };\n  /*\n  /!**\n   * Skip all whitespace until we get to the first black line.\n   *\n   * @param row row of black/white values to search\n   * @return index of the first black line.\n   * @throws NotFoundException Throws exception if no black lines are found in the row\n   *!/*/\n  ITFReader.skipWhiteSpace = function (row) {\n    var width = row.getSize();\n    var endStart = row.getNextSet(0);\n    if (endStart === width) {\n      throw new NotFoundException();\n    }\n    return endStart;\n  };\n  /*/!**\n   * Identify where the end of the middle / payload section ends.\n   *\n   * @param row row of black/white values to search\n   * @return Array, containing index of start of 'end block' and end of 'end\n   *         block'\n   *!/*/\n  ITFReader.prototype.decodeEnd = function (row) {\n    // For convenience, reverse the row and then\n    // search from 'the start' for the end block\n    row.reverse();\n    try {\n      var endStart = ITFReader.skipWhiteSpace(row);\n      var endPattern = void 0;\n      try {\n        endPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.END_PATTERN_REVERSED[0]);\n      } catch (error) {\n        if (error instanceof NotFoundException) {\n          endPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.END_PATTERN_REVERSED[1]);\n        }\n      }\n      // The start & end patterns must be pre/post fixed by a quiet zone. This\n      // zone must be at least 10 times the width of a narrow line.\n      // ref: http://www.barcode-1.net/i25code.html\n      this.validateQuietZone(row, endPattern[0]);\n      // Now recalculate the indices of where the 'endblock' starts & stops to\n      // accommodate\n      // the reversed nature of the search\n      var temp = endPattern[0];\n      endPattern[0] = row.getSize() - endPattern[1];\n      endPattern[1] = row.getSize() - temp;\n      return endPattern;\n    } finally {\n      // Put the row back the right way.\n      row.reverse();\n    }\n  };\n  /*\n  /!**\n   * @param row       row of black/white values to search\n   * @param rowOffset position to start search\n   * @param pattern   pattern of counts of number of black and white pixels that are\n   *                  being searched for as a pattern\n   * @return start/end horizontal offset of guard pattern, as an array of two\n   *         ints\n   * @throws NotFoundException if pattern is not found\n   *!/*/\n  ITFReader.findGuardPattern = function (row, rowOffset, pattern) {\n    var patternLength = pattern.length;\n    var counters = new Int32Array(patternLength);\n    var width = row.getSize();\n    var isWhite = false;\n    var counterPosition = 0;\n    var patternStart = rowOffset;\n    counters.fill(0);\n    for (var x = rowOffset; x < width; x++) {\n      if (row.get(x) !== isWhite) {\n        counters[counterPosition]++;\n      } else {\n        if (counterPosition === patternLength - 1) {\n          if (OneDReader.patternMatchVariance(counters, pattern, ITFReader.MAX_INDIVIDUAL_VARIANCE) < ITFReader.MAX_AVG_VARIANCE) {\n            return [patternStart, x];\n          }\n          patternStart += counters[0] + counters[1];\n          System.arraycopy(counters, 2, counters, 0, counterPosition - 1);\n          counters[counterPosition - 1] = 0;\n          counters[counterPosition] = 0;\n          counterPosition--;\n        } else {\n          counterPosition++;\n        }\n        counters[counterPosition] = 1;\n        isWhite = !isWhite;\n      }\n    }\n    throw new NotFoundException();\n  };\n  /*/!**\n   * Attempts to decode a sequence of ITF black/white lines into single\n   * digit.\n   *\n   * @param counters the counts of runs of observed black/white/black/... values\n   * @return The decoded digit\n   * @throws NotFoundException if digit cannot be decoded\n   *!/*/\n  ITFReader.decodeDigit = function (counters) {\n    var bestVariance = ITFReader.MAX_AVG_VARIANCE; // worst variance we'll accept\n    var bestMatch = -1;\n    var max = ITFReader.PATTERNS.length;\n    for (var i = 0; i < max; i++) {\n      var pattern = ITFReader.PATTERNS[i];\n      var variance = OneDReader.patternMatchVariance(counters, pattern, ITFReader.MAX_INDIVIDUAL_VARIANCE);\n      if (variance < bestVariance) {\n        bestVariance = variance;\n        bestMatch = i;\n      } else if (variance === bestVariance) {\n        // if we find a second 'best match' with the same variance, we can not reliably report to have a suitable match\n        bestMatch = -1;\n      }\n    }\n    if (bestMatch >= 0) {\n      return bestMatch % 10;\n    } else {\n      throw new NotFoundException();\n    }\n  };\n  ITFReader.PATTERNS = [Int32Array.from([1, 1, 2, 2, 1]), Int32Array.from([2, 1, 1, 1, 2]), Int32Array.from([1, 2, 1, 1, 2]), Int32Array.from([2, 2, 1, 1, 1]), Int32Array.from([1, 1, 2, 1, 2]), Int32Array.from([2, 1, 2, 1, 1]), Int32Array.from([1, 2, 2, 1, 1]), Int32Array.from([1, 1, 1, 2, 2]), Int32Array.from([2, 1, 1, 2, 1]), Int32Array.from([1, 2, 1, 2, 1]), Int32Array.from([1, 1, 3, 3, 1]), Int32Array.from([3, 1, 1, 1, 3]), Int32Array.from([1, 3, 1, 1, 3]), Int32Array.from([3, 3, 1, 1, 1]), Int32Array.from([1, 1, 3, 1, 3]), Int32Array.from([3, 1, 3, 1, 1]), Int32Array.from([1, 3, 3, 1, 1]), Int32Array.from([1, 1, 1, 3, 3]), Int32Array.from([3, 1, 1, 3, 1]), Int32Array.from([1, 3, 1, 3, 1]) // 9\n  ];\n  ITFReader.MAX_AVG_VARIANCE = 0.38;\n  ITFReader.MAX_INDIVIDUAL_VARIANCE = 0.5;\n  /* /!** Valid ITF lengths. Anything longer than the largest value is also allowed. *!/*/\n  ITFReader.DEFAULT_ALLOWED_LENGTHS = [6, 8, 10, 12, 14];\n  /*/!**\n   * Start/end guard pattern.\n   *\n   * Note: The end pattern is reversed because the row is reversed before\n   * searching for the END_PATTERN\n   *!/*/\n  ITFReader.START_PATTERN = Int32Array.from([1, 1, 1, 1]);\n  ITFReader.END_PATTERN_REVERSED = [Int32Array.from([1, 1, 2]), Int32Array.from([1, 1, 3]) // 3x\n  ];\n  return ITFReader;\n}(OneDReader);\nexport default ITFReader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BarcodeFormat", "DecodeHintType", "FormatException", "NotFoundException", "Result", "ResultPoint", "StringBuilder", "System", "OneDReader", "ITFReader", "_super", "_this", "apply", "arguments", "narrowLineWidth", "decodeRow", "rowNumber", "row", "hints", "e_1", "_a", "startRange", "decodeStart", "endRange", "decodeEnd", "result", "decodeMiddle", "resultString", "toString", "allowedLengths", "get", "ALLOWED_LENGTHS", "DEFAULT_ALLOWED_LENGTHS", "lengthOK", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedLengths_1", "allowedLengths_1_1", "e_1_1", "error", "return", "points", "resultReturn", "ITF", "Date", "getTime", "payloadStart", "payloadEnd", "counterDigitPair", "Int32Array", "counterBlack", "counterWhite", "fill", "recordPattern", "k", "twoK", "bestMatch", "decodeDigit", "append", "for<PERSON>ach", "counterDigit", "endStart", "skipWhiteSpace", "startPattern", "<PERSON><PERSON><PERSON><PERSON>att<PERSON>", "START_PATTERN", "validateQuietZone", "quietCount", "width", "getSize", "getNextSet", "reverse", "endPattern", "END_PATTERN_REVERSED", "temp", "rowOffset", "pattern", "<PERSON><PERSON><PERSON><PERSON>", "counters", "<PERSON><PERSON><PERSON><PERSON>", "counterPosition", "patternStart", "x", "patternMatchVariance", "MAX_INDIVIDUAL_VARIANCE", "MAX_AVG_VARIANCE", "arraycopy", "bestVariance", "max", "PATTERNS", "variance", "from"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/ITFReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport StringBuilder from '../util/StringBuilder';\nimport System from '../util/System';\nimport OneDReader from './OneDReader';\n/**\n * <p>Decodes ITF barcodes.</p>\n *\n * <AUTHOR>\n */\nvar ITFReader = /** @class */ (function (_super) {\n    __extends(ITFReader, _super);\n    function ITFReader() {\n        // private static W = 3; // Pixel width of a 3x wide line\n        // private static w = 2; // Pixel width of a 2x wide line\n        // private static N = 1; // Pixed width of a narrow line\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        // Stores the actual narrow line width of the image being decoded.\n        _this.narrowLineWidth = -1;\n        return _this;\n    }\n    // See ITFWriter.PATTERNS\n    /*\n  \n    /!**\n     * Patterns of Wide / Narrow lines to indicate each digit\n     *!/\n    */\n    ITFReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a;\n        // Find out where the Middle section (payload) starts & ends\n        var startRange = this.decodeStart(row);\n        var endRange = this.decodeEnd(row);\n        var result = new StringBuilder();\n        ITFReader.decodeMiddle(row, startRange[1], endRange[0], result);\n        var resultString = result.toString();\n        var allowedLengths = null;\n        if (hints != null) {\n            allowedLengths = hints.get(DecodeHintType.ALLOWED_LENGTHS);\n        }\n        if (allowedLengths == null) {\n            allowedLengths = ITFReader.DEFAULT_ALLOWED_LENGTHS;\n        }\n        // To avoid false positives with 2D barcodes (and other patterns), make\n        // an assumption that the decoded string must be a 'standard' length if it's short\n        var length = resultString.length;\n        var lengthOK = false;\n        var maxAllowedLength = 0;\n        try {\n            for (var allowedLengths_1 = __values(allowedLengths), allowedLengths_1_1 = allowedLengths_1.next(); !allowedLengths_1_1.done; allowedLengths_1_1 = allowedLengths_1.next()) {\n                var value = allowedLengths_1_1.value;\n                if (length === value) {\n                    lengthOK = true;\n                    break;\n                }\n                if (value > maxAllowedLength) {\n                    maxAllowedLength = value;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (allowedLengths_1_1 && !allowedLengths_1_1.done && (_a = allowedLengths_1.return)) _a.call(allowedLengths_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (!lengthOK && length > maxAllowedLength) {\n            lengthOK = true;\n        }\n        if (!lengthOK) {\n            throw new FormatException();\n        }\n        var points = [new ResultPoint(startRange[1], rowNumber), new ResultPoint(endRange[0], rowNumber)];\n        var resultReturn = new Result(resultString, null, // no natural byte representation for these barcodes\n        0, points, BarcodeFormat.ITF, new Date().getTime());\n        return resultReturn;\n    };\n    /*\n    /!**\n     * @param row          row of black/white values to search\n     * @param payloadStart offset of start pattern\n     * @param resultString {@link StringBuilder} to append decoded chars to\n     * @throws NotFoundException if decoding could not complete successfully\n     *!/*/\n    ITFReader.decodeMiddle = function (row, payloadStart, payloadEnd, resultString) {\n        // Digits are interleaved in pairs - 5 black lines for one digit, and the\n        // 5\n        // interleaved white lines for the second digit.\n        // Therefore, need to scan 10 lines and then\n        // split these into two arrays\n        var counterDigitPair = new Int32Array(10); // 10\n        var counterBlack = new Int32Array(5); // 5\n        var counterWhite = new Int32Array(5); // 5\n        counterDigitPair.fill(0);\n        counterBlack.fill(0);\n        counterWhite.fill(0);\n        while (payloadStart < payloadEnd) {\n            // Get 10 runs of black/white.\n            OneDReader.recordPattern(row, payloadStart, counterDigitPair);\n            // Split them into each array\n            for (var k = 0; k < 5; k++) {\n                var twoK = 2 * k;\n                counterBlack[k] = counterDigitPair[twoK];\n                counterWhite[k] = counterDigitPair[twoK + 1];\n            }\n            var bestMatch = ITFReader.decodeDigit(counterBlack);\n            resultString.append(bestMatch.toString());\n            bestMatch = this.decodeDigit(counterWhite);\n            resultString.append(bestMatch.toString());\n            counterDigitPair.forEach(function (counterDigit) {\n                payloadStart += counterDigit;\n            });\n        }\n    };\n    /*/!**\n     * Identify where the start of the middle / payload section starts.\n     *\n     * @param row row of black/white values to search\n     * @return Array, containing index of start of 'start block' and end of\n     *         'start block'\n     *!/*/\n    ITFReader.prototype.decodeStart = function (row) {\n        var endStart = ITFReader.skipWhiteSpace(row);\n        var startPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.START_PATTERN);\n        // Determine the width of a narrow line in pixels. We can do this by\n        // getting the width of the start pattern and dividing by 4 because its\n        // made up of 4 narrow lines.\n        this.narrowLineWidth = (startPattern[1] - startPattern[0]) / 4;\n        this.validateQuietZone(row, startPattern[0]);\n        return startPattern;\n    };\n    /*/!**\n     * The start & end patterns must be pre/post fixed by a quiet zone. This\n     * zone must be at least 10 times the width of a narrow line.  Scan back until\n     * we either get to the start of the barcode or match the necessary number of\n     * quiet zone pixels.\n     *\n     * Note: Its assumed the row is reversed when using this method to find\n     * quiet zone after the end pattern.\n     *\n     * ref: http://www.barcode-1.net/i25code.html\n     *\n     * @param row bit array representing the scanned barcode.\n     * @param startPattern index into row of the start or end pattern.\n     * @throws NotFoundException if the quiet zone cannot be found\n     *!/*/\n    ITFReader.prototype.validateQuietZone = function (row, startPattern) {\n        var quietCount = this.narrowLineWidth * 10; // expect to find this many pixels of quiet zone\n        // if there are not so many pixel at all let's try as many as possible\n        quietCount = quietCount < startPattern ? quietCount : startPattern;\n        for (var i = startPattern - 1; quietCount > 0 && i >= 0; i--) {\n            if (row.get(i)) {\n                break;\n            }\n            quietCount--;\n        }\n        if (quietCount !== 0) {\n            // Unable to find the necessary number of quiet zone pixels.\n            throw new NotFoundException();\n        }\n    };\n    /*\n    /!**\n     * Skip all whitespace until we get to the first black line.\n     *\n     * @param row row of black/white values to search\n     * @return index of the first black line.\n     * @throws NotFoundException Throws exception if no black lines are found in the row\n     *!/*/\n    ITFReader.skipWhiteSpace = function (row) {\n        var width = row.getSize();\n        var endStart = row.getNextSet(0);\n        if (endStart === width) {\n            throw new NotFoundException();\n        }\n        return endStart;\n    };\n    /*/!**\n     * Identify where the end of the middle / payload section ends.\n     *\n     * @param row row of black/white values to search\n     * @return Array, containing index of start of 'end block' and end of 'end\n     *         block'\n     *!/*/\n    ITFReader.prototype.decodeEnd = function (row) {\n        // For convenience, reverse the row and then\n        // search from 'the start' for the end block\n        row.reverse();\n        try {\n            var endStart = ITFReader.skipWhiteSpace(row);\n            var endPattern = void 0;\n            try {\n                endPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.END_PATTERN_REVERSED[0]);\n            }\n            catch (error) {\n                if (error instanceof NotFoundException) {\n                    endPattern = ITFReader.findGuardPattern(row, endStart, ITFReader.END_PATTERN_REVERSED[1]);\n                }\n            }\n            // The start & end patterns must be pre/post fixed by a quiet zone. This\n            // zone must be at least 10 times the width of a narrow line.\n            // ref: http://www.barcode-1.net/i25code.html\n            this.validateQuietZone(row, endPattern[0]);\n            // Now recalculate the indices of where the 'endblock' starts & stops to\n            // accommodate\n            // the reversed nature of the search\n            var temp = endPattern[0];\n            endPattern[0] = row.getSize() - endPattern[1];\n            endPattern[1] = row.getSize() - temp;\n            return endPattern;\n        }\n        finally {\n            // Put the row back the right way.\n            row.reverse();\n        }\n    };\n    /*\n    /!**\n     * @param row       row of black/white values to search\n     * @param rowOffset position to start search\n     * @param pattern   pattern of counts of number of black and white pixels that are\n     *                  being searched for as a pattern\n     * @return start/end horizontal offset of guard pattern, as an array of two\n     *         ints\n     * @throws NotFoundException if pattern is not found\n     *!/*/\n    ITFReader.findGuardPattern = function (row, rowOffset, pattern) {\n        var patternLength = pattern.length;\n        var counters = new Int32Array(patternLength);\n        var width = row.getSize();\n        var isWhite = false;\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        counters.fill(0);\n        for (var x = rowOffset; x < width; x++) {\n            if (row.get(x) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    if (OneDReader.patternMatchVariance(counters, pattern, ITFReader.MAX_INDIVIDUAL_VARIANCE) < ITFReader.MAX_AVG_VARIANCE) {\n                        return [patternStart, x];\n                    }\n                    patternStart += counters[0] + counters[1];\n                    System.arraycopy(counters, 2, counters, 0, counterPosition - 1);\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    /*/!**\n     * Attempts to decode a sequence of ITF black/white lines into single\n     * digit.\n     *\n     * @param counters the counts of runs of observed black/white/black/... values\n     * @return The decoded digit\n     * @throws NotFoundException if digit cannot be decoded\n     *!/*/\n    ITFReader.decodeDigit = function (counters) {\n        var bestVariance = ITFReader.MAX_AVG_VARIANCE; // worst variance we'll accept\n        var bestMatch = -1;\n        var max = ITFReader.PATTERNS.length;\n        for (var i = 0; i < max; i++) {\n            var pattern = ITFReader.PATTERNS[i];\n            var variance = OneDReader.patternMatchVariance(counters, pattern, ITFReader.MAX_INDIVIDUAL_VARIANCE);\n            if (variance < bestVariance) {\n                bestVariance = variance;\n                bestMatch = i;\n            }\n            else if (variance === bestVariance) {\n                // if we find a second 'best match' with the same variance, we can not reliably report to have a suitable match\n                bestMatch = -1;\n            }\n        }\n        if (bestMatch >= 0) {\n            return bestMatch % 10;\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    ITFReader.PATTERNS = [\n        Int32Array.from([1, 1, 2, 2, 1]),\n        Int32Array.from([2, 1, 1, 1, 2]),\n        Int32Array.from([1, 2, 1, 1, 2]),\n        Int32Array.from([2, 2, 1, 1, 1]),\n        Int32Array.from([1, 1, 2, 1, 2]),\n        Int32Array.from([2, 1, 2, 1, 1]),\n        Int32Array.from([1, 2, 2, 1, 1]),\n        Int32Array.from([1, 1, 1, 2, 2]),\n        Int32Array.from([2, 1, 1, 2, 1]),\n        Int32Array.from([1, 2, 1, 2, 1]),\n        Int32Array.from([1, 1, 3, 3, 1]),\n        Int32Array.from([3, 1, 1, 1, 3]),\n        Int32Array.from([1, 3, 1, 1, 3]),\n        Int32Array.from([3, 3, 1, 1, 1]),\n        Int32Array.from([1, 1, 3, 1, 3]),\n        Int32Array.from([3, 1, 3, 1, 1]),\n        Int32Array.from([1, 3, 3, 1, 1]),\n        Int32Array.from([1, 1, 1, 3, 3]),\n        Int32Array.from([3, 1, 1, 3, 1]),\n        Int32Array.from([1, 3, 1, 3, 1]) // 9\n    ];\n    ITFReader.MAX_AVG_VARIANCE = 0.38;\n    ITFReader.MAX_INDIVIDUAL_VARIANCE = 0.5;\n    /* /!** Valid ITF lengths. Anything longer than the largest value is also allowed. *!/*/\n    ITFReader.DEFAULT_ALLOWED_LENGTHS = [6, 8, 10, 12, 14];\n    /*/!**\n     * Start/end guard pattern.\n     *\n     * Note: The end pattern is reversed because the row is reversed before\n     * searching for the END_PATTERN\n     *!/*/\n    ITFReader.START_PATTERN = Int32Array.from([1, 1, 1, 1]);\n    ITFReader.END_PATTERN_REVERSED = [\n        Int32Array.from([1, 1, 2]),\n        Int32Array.from([1, 1, 3]) // 3x\n    ];\n    return ITFReader;\n}(OneDReader));\nexport default ITFReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,UAAU,MAAM,cAAc;AACrC;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC7CrC,SAAS,CAACoC,SAAS,EAAEC,MAAM,CAAC;EAC5B,SAASD,SAASA,CAAA,EAAG;IACjB;IACA;IACA;IACA,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpE;IACAF,KAAK,CAACG,eAAe,GAAG,CAAC,CAAC;IAC1B,OAAOH,KAAK;EAChB;EACA;EACA;AACJ;AACA;AACA;AACA;EAEIF,SAAS,CAACxB,SAAS,CAAC8B,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAE;IAC7D,IAAIC,GAAG,EAAEC,EAAE;IACX;IACA,IAAIC,UAAU,GAAG,IAAI,CAACC,WAAW,CAACL,GAAG,CAAC;IACtC,IAAIM,QAAQ,GAAG,IAAI,CAACC,SAAS,CAACP,GAAG,CAAC;IAClC,IAAIQ,MAAM,GAAG,IAAInB,aAAa,CAAC,CAAC;IAChCG,SAAS,CAACiB,YAAY,CAACT,GAAG,EAAEI,UAAU,CAAC,CAAC,CAAC,EAAEE,QAAQ,CAAC,CAAC,CAAC,EAAEE,MAAM,CAAC;IAC/D,IAAIE,YAAY,GAAGF,MAAM,CAACG,QAAQ,CAAC,CAAC;IACpC,IAAIC,cAAc,GAAG,IAAI;IACzB,IAAIX,KAAK,IAAI,IAAI,EAAE;MACfW,cAAc,GAAGX,KAAK,CAACY,GAAG,CAAC7B,cAAc,CAAC8B,eAAe,CAAC;IAC9D;IACA,IAAIF,cAAc,IAAI,IAAI,EAAE;MACxBA,cAAc,GAAGpB,SAAS,CAACuB,uBAAuB;IACtD;IACA;IACA;IACA,IAAIrC,MAAM,GAAGgC,YAAY,CAAChC,MAAM;IAChC,IAAIsC,QAAQ,GAAG,KAAK;IACpB,IAAIC,gBAAgB,GAAG,CAAC;IACxB,IAAI;MACA,KAAK,IAAIC,gBAAgB,GAAGhD,QAAQ,CAAC0C,cAAc,CAAC,EAAEO,kBAAkB,GAAGD,gBAAgB,CAACvC,IAAI,CAAC,CAAC,EAAE,CAACwC,kBAAkB,CAACtC,IAAI,EAAEsC,kBAAkB,GAAGD,gBAAgB,CAACvC,IAAI,CAAC,CAAC,EAAE;QACxK,IAAIC,KAAK,GAAGuC,kBAAkB,CAACvC,KAAK;QACpC,IAAIF,MAAM,KAAKE,KAAK,EAAE;UAClBoC,QAAQ,GAAG,IAAI;UACf;QACJ;QACA,IAAIpC,KAAK,GAAGqC,gBAAgB,EAAE;UAC1BA,gBAAgB,GAAGrC,KAAK;QAC5B;MACJ;IACJ,CAAC,CACD,OAAOwC,KAAK,EAAE;MAAElB,GAAG,GAAG;QAAEmB,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAID,kBAAkB,IAAI,CAACA,kBAAkB,CAACtC,IAAI,KAAKsB,EAAE,GAAGe,gBAAgB,CAACI,MAAM,CAAC,EAAEnB,EAAE,CAAC1B,IAAI,CAACyC,gBAAgB,CAAC;MACnH,CAAC,SACO;QAAE,IAAIhB,GAAG,EAAE,MAAMA,GAAG,CAACmB,KAAK;MAAE;IACxC;IACA,IAAI,CAACL,QAAQ,IAAItC,MAAM,GAAGuC,gBAAgB,EAAE;MACxCD,QAAQ,GAAG,IAAI;IACnB;IACA,IAAI,CAACA,QAAQ,EAAE;MACX,MAAM,IAAI/B,eAAe,CAAC,CAAC;IAC/B;IACA,IAAIsC,MAAM,GAAG,CAAC,IAAInC,WAAW,CAACgB,UAAU,CAAC,CAAC,CAAC,EAAEL,SAAS,CAAC,EAAE,IAAIX,WAAW,CAACkB,QAAQ,CAAC,CAAC,CAAC,EAAEP,SAAS,CAAC,CAAC;IACjG,IAAIyB,YAAY,GAAG,IAAIrC,MAAM,CAACuB,YAAY,EAAE,IAAI;IAAE;IAClD,CAAC,EAAEa,MAAM,EAAExC,aAAa,CAAC0C,GAAG,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;IACnD,OAAOH,YAAY;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIhC,SAAS,CAACiB,YAAY,GAAG,UAAUT,GAAG,EAAE4B,YAAY,EAAEC,UAAU,EAAEnB,YAAY,EAAE;IAC5E;IACA;IACA;IACA;IACA;IACA,IAAIoB,gBAAgB,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3C,IAAIC,YAAY,GAAG,IAAID,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACtC,IAAIE,YAAY,GAAG,IAAIF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACtCD,gBAAgB,CAACI,IAAI,CAAC,CAAC,CAAC;IACxBF,YAAY,CAACE,IAAI,CAAC,CAAC,CAAC;IACpBD,YAAY,CAACC,IAAI,CAAC,CAAC,CAAC;IACpB,OAAON,YAAY,GAAGC,UAAU,EAAE;MAC9B;MACAtC,UAAU,CAAC4C,aAAa,CAACnC,GAAG,EAAE4B,YAAY,EAAEE,gBAAgB,CAAC;MAC7D;MACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAIC,IAAI,GAAG,CAAC,GAAGD,CAAC;QAChBJ,YAAY,CAACI,CAAC,CAAC,GAAGN,gBAAgB,CAACO,IAAI,CAAC;QACxCJ,YAAY,CAACG,CAAC,CAAC,GAAGN,gBAAgB,CAACO,IAAI,GAAG,CAAC,CAAC;MAChD;MACA,IAAIC,SAAS,GAAG9C,SAAS,CAAC+C,WAAW,CAACP,YAAY,CAAC;MACnDtB,YAAY,CAAC8B,MAAM,CAACF,SAAS,CAAC3B,QAAQ,CAAC,CAAC,CAAC;MACzC2B,SAAS,GAAG,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC;MAC1CvB,YAAY,CAAC8B,MAAM,CAACF,SAAS,CAAC3B,QAAQ,CAAC,CAAC,CAAC;MACzCmB,gBAAgB,CAACW,OAAO,CAAC,UAAUC,YAAY,EAAE;QAC7Cd,YAAY,IAAIc,YAAY;MAChC,CAAC,CAAC;IACN;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlD,SAAS,CAACxB,SAAS,CAACqC,WAAW,GAAG,UAAUL,GAAG,EAAE;IAC7C,IAAI2C,QAAQ,GAAGnD,SAAS,CAACoD,cAAc,CAAC5C,GAAG,CAAC;IAC5C,IAAI6C,YAAY,GAAGrD,SAAS,CAACsD,gBAAgB,CAAC9C,GAAG,EAAE2C,QAAQ,EAAEnD,SAAS,CAACuD,aAAa,CAAC;IACrF;IACA;IACA;IACA,IAAI,CAAClD,eAAe,GAAG,CAACgD,YAAY,CAAC,CAAC,CAAC,GAAGA,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9D,IAAI,CAACG,iBAAiB,CAAChD,GAAG,EAAE6C,YAAY,CAAC,CAAC,CAAC,CAAC;IAC5C,OAAOA,YAAY;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrD,SAAS,CAACxB,SAAS,CAACgF,iBAAiB,GAAG,UAAUhD,GAAG,EAAE6C,YAAY,EAAE;IACjE,IAAII,UAAU,GAAG,IAAI,CAACpD,eAAe,GAAG,EAAE,CAAC,CAAC;IAC5C;IACAoD,UAAU,GAAGA,UAAU,GAAGJ,YAAY,GAAGI,UAAU,GAAGJ,YAAY;IAClE,KAAK,IAAIrE,CAAC,GAAGqE,YAAY,GAAG,CAAC,EAAEI,UAAU,GAAG,CAAC,IAAIzE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1D,IAAIwB,GAAG,CAACa,GAAG,CAACrC,CAAC,CAAC,EAAE;QACZ;MACJ;MACAyE,UAAU,EAAE;IAChB;IACA,IAAIA,UAAU,KAAK,CAAC,EAAE;MAClB;MACA,MAAM,IAAI/D,iBAAiB,CAAC,CAAC;IACjC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIM,SAAS,CAACoD,cAAc,GAAG,UAAU5C,GAAG,EAAE;IACtC,IAAIkD,KAAK,GAAGlD,GAAG,CAACmD,OAAO,CAAC,CAAC;IACzB,IAAIR,QAAQ,GAAG3C,GAAG,CAACoD,UAAU,CAAC,CAAC,CAAC;IAChC,IAAIT,QAAQ,KAAKO,KAAK,EAAE;MACpB,MAAM,IAAIhE,iBAAiB,CAAC,CAAC;IACjC;IACA,OAAOyD,QAAQ;EACnB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACInD,SAAS,CAACxB,SAAS,CAACuC,SAAS,GAAG,UAAUP,GAAG,EAAE;IAC3C;IACA;IACAA,GAAG,CAACqD,OAAO,CAAC,CAAC;IACb,IAAI;MACA,IAAIV,QAAQ,GAAGnD,SAAS,CAACoD,cAAc,CAAC5C,GAAG,CAAC;MAC5C,IAAIsD,UAAU,GAAG,KAAK,CAAC;MACvB,IAAI;QACAA,UAAU,GAAG9D,SAAS,CAACsD,gBAAgB,CAAC9C,GAAG,EAAE2C,QAAQ,EAAEnD,SAAS,CAAC+D,oBAAoB,CAAC,CAAC,CAAC,CAAC;MAC7F,CAAC,CACD,OAAOlC,KAAK,EAAE;QACV,IAAIA,KAAK,YAAYnC,iBAAiB,EAAE;UACpCoE,UAAU,GAAG9D,SAAS,CAACsD,gBAAgB,CAAC9C,GAAG,EAAE2C,QAAQ,EAAEnD,SAAS,CAAC+D,oBAAoB,CAAC,CAAC,CAAC,CAAC;QAC7F;MACJ;MACA;MACA;MACA;MACA,IAAI,CAACP,iBAAiB,CAAChD,GAAG,EAAEsD,UAAU,CAAC,CAAC,CAAC,CAAC;MAC1C;MACA;MACA;MACA,IAAIE,IAAI,GAAGF,UAAU,CAAC,CAAC,CAAC;MACxBA,UAAU,CAAC,CAAC,CAAC,GAAGtD,GAAG,CAACmD,OAAO,CAAC,CAAC,GAAGG,UAAU,CAAC,CAAC,CAAC;MAC7CA,UAAU,CAAC,CAAC,CAAC,GAAGtD,GAAG,CAACmD,OAAO,CAAC,CAAC,GAAGK,IAAI;MACpC,OAAOF,UAAU;IACrB,CAAC,SACO;MACJ;MACAtD,GAAG,CAACqD,OAAO,CAAC,CAAC;IACjB;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI7D,SAAS,CAACsD,gBAAgB,GAAG,UAAU9C,GAAG,EAAEyD,SAAS,EAAEC,OAAO,EAAE;IAC5D,IAAIC,aAAa,GAAGD,OAAO,CAAChF,MAAM;IAClC,IAAIkF,QAAQ,GAAG,IAAI7B,UAAU,CAAC4B,aAAa,CAAC;IAC5C,IAAIT,KAAK,GAAGlD,GAAG,CAACmD,OAAO,CAAC,CAAC;IACzB,IAAIU,OAAO,GAAG,KAAK;IACnB,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIC,YAAY,GAAGN,SAAS;IAC5BG,QAAQ,CAAC1B,IAAI,CAAC,CAAC,CAAC;IAChB,KAAK,IAAI8B,CAAC,GAAGP,SAAS,EAAEO,CAAC,GAAGd,KAAK,EAAEc,CAAC,EAAE,EAAE;MACpC,IAAIhE,GAAG,CAACa,GAAG,CAACmD,CAAC,CAAC,KAAKH,OAAO,EAAE;QACxBD,QAAQ,CAACE,eAAe,CAAC,EAAE;MAC/B,CAAC,MACI;QACD,IAAIA,eAAe,KAAKH,aAAa,GAAG,CAAC,EAAE;UACvC,IAAIpE,UAAU,CAAC0E,oBAAoB,CAACL,QAAQ,EAAEF,OAAO,EAAElE,SAAS,CAAC0E,uBAAuB,CAAC,GAAG1E,SAAS,CAAC2E,gBAAgB,EAAE;YACpH,OAAO,CAACJ,YAAY,EAAEC,CAAC,CAAC;UAC5B;UACAD,YAAY,IAAIH,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzCtE,MAAM,CAAC8E,SAAS,CAACR,QAAQ,EAAE,CAAC,EAAEA,QAAQ,EAAE,CAAC,EAAEE,eAAe,GAAG,CAAC,CAAC;UAC/DF,QAAQ,CAACE,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;UACjCF,QAAQ,CAACE,eAAe,CAAC,GAAG,CAAC;UAC7BA,eAAe,EAAE;QACrB,CAAC,MACI;UACDA,eAAe,EAAE;QACrB;QACAF,QAAQ,CAACE,eAAe,CAAC,GAAG,CAAC;QAC7BD,OAAO,GAAG,CAACA,OAAO;MACtB;IACJ;IACA,MAAM,IAAI3E,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIM,SAAS,CAAC+C,WAAW,GAAG,UAAUqB,QAAQ,EAAE;IACxC,IAAIS,YAAY,GAAG7E,SAAS,CAAC2E,gBAAgB,CAAC,CAAC;IAC/C,IAAI7B,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIgC,GAAG,GAAG9E,SAAS,CAAC+E,QAAQ,CAAC7F,MAAM;IACnC,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8F,GAAG,EAAE9F,CAAC,EAAE,EAAE;MAC1B,IAAIkF,OAAO,GAAGlE,SAAS,CAAC+E,QAAQ,CAAC/F,CAAC,CAAC;MACnC,IAAIgG,QAAQ,GAAGjF,UAAU,CAAC0E,oBAAoB,CAACL,QAAQ,EAAEF,OAAO,EAAElE,SAAS,CAAC0E,uBAAuB,CAAC;MACpG,IAAIM,QAAQ,GAAGH,YAAY,EAAE;QACzBA,YAAY,GAAGG,QAAQ;QACvBlC,SAAS,GAAG9D,CAAC;MACjB,CAAC,MACI,IAAIgG,QAAQ,KAAKH,YAAY,EAAE;QAChC;QACA/B,SAAS,GAAG,CAAC,CAAC;MAClB;IACJ;IACA,IAAIA,SAAS,IAAI,CAAC,EAAE;MAChB,OAAOA,SAAS,GAAG,EAAE;IACzB,CAAC,MACI;MACD,MAAM,IAAIpD,iBAAiB,CAAC,CAAC;IACjC;EACJ,CAAC;EACDM,SAAS,CAAC+E,QAAQ,GAAG,CACjBxC,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChC1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAAA,CACpC;EACDjF,SAAS,CAAC2E,gBAAgB,GAAG,IAAI;EACjC3E,SAAS,CAAC0E,uBAAuB,GAAG,GAAG;EACvC;EACA1E,SAAS,CAACuB,uBAAuB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACtD;AACJ;AACA;AACA;AACA;AACA;EACIvB,SAAS,CAACuD,aAAa,GAAGhB,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACvDjF,SAAS,CAAC+D,oBAAoB,GAAG,CAC7BxB,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC1B1C,UAAU,CAAC0C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EAAA,CAC9B;EACD,OAAOjF,SAAS;AACpB,CAAC,CAACD,UAAU,CAAE;AACd,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}