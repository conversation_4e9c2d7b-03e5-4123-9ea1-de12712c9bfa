{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _merge = require(\"../help/merge.js\");\nvar _merge2 = _interopRequireDefault(_merge);\nvar _shared = require(\"./shared.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nvar CanvasRenderer = function () {\n  function CanvasRenderer(canvas, encodings, options) {\n    _classCallCheck(this, CanvasRenderer);\n    this.canvas = canvas;\n    this.encodings = encodings;\n    this.options = options;\n  }\n  _createClass(CanvasRenderer, [{\n    key: \"render\",\n    value: function render() {\n      // Abort if the browser does not support HTML5 canvas\n      if (!this.canvas.getContext) {\n        throw new Error('The browser does not support canvas.');\n      }\n      this.prepareCanvas();\n      for (var i = 0; i < this.encodings.length; i++) {\n        var encodingOptions = (0, _merge2.default)(this.options, this.encodings[i].options);\n        this.drawCanvasBarcode(encodingOptions, this.encodings[i]);\n        this.drawCanvasText(encodingOptions, this.encodings[i]);\n        this.moveCanvasDrawing(this.encodings[i]);\n      }\n      this.restoreCanvas();\n    }\n  }, {\n    key: \"prepareCanvas\",\n    value: function prepareCanvas() {\n      // Get the canvas context\n      var ctx = this.canvas.getContext(\"2d\");\n      ctx.save();\n      (0, _shared.calculateEncodingAttributes)(this.encodings, this.options, ctx);\n      var totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);\n      var maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);\n      this.canvas.width = totalWidth + this.options.marginLeft + this.options.marginRight;\n      this.canvas.height = maxHeight;\n\n      // Paint the canvas\n      ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n      if (this.options.background) {\n        ctx.fillStyle = this.options.background;\n        ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n      }\n      ctx.translate(this.options.marginLeft, 0);\n    }\n  }, {\n    key: \"drawCanvasBarcode\",\n    value: function drawCanvasBarcode(options, encoding) {\n      // Get the canvas context\n      var ctx = this.canvas.getContext(\"2d\");\n      var binary = encoding.data;\n\n      // Creates the barcode out of the encoded binary\n      var yFrom;\n      if (options.textPosition == \"top\") {\n        yFrom = options.marginTop + options.fontSize + options.textMargin;\n      } else {\n        yFrom = options.marginTop;\n      }\n      ctx.fillStyle = options.lineColor;\n      for (var b = 0; b < binary.length; b++) {\n        var x = b * options.width + encoding.barcodePadding;\n        if (binary[b] === \"1\") {\n          ctx.fillRect(x, yFrom, options.width, options.height);\n        } else if (binary[b]) {\n          ctx.fillRect(x, yFrom, options.width, options.height * binary[b]);\n        }\n      }\n    }\n  }, {\n    key: \"drawCanvasText\",\n    value: function drawCanvasText(options, encoding) {\n      // Get the canvas context\n      var ctx = this.canvas.getContext(\"2d\");\n      var font = options.fontOptions + \" \" + options.fontSize + \"px \" + options.font;\n\n      // Draw the text if displayValue is set\n      if (options.displayValue) {\n        var x, y;\n        if (options.textPosition == \"top\") {\n          y = options.marginTop + options.fontSize - options.textMargin;\n        } else {\n          y = options.height + options.textMargin + options.marginTop + options.fontSize;\n        }\n        ctx.font = font;\n\n        // Draw the text in the correct X depending on the textAlign option\n        if (options.textAlign == \"left\" || encoding.barcodePadding > 0) {\n          x = 0;\n          ctx.textAlign = 'left';\n        } else if (options.textAlign == \"right\") {\n          x = encoding.width - 1;\n          ctx.textAlign = 'right';\n        }\n        // In all other cases, center the text\n        else {\n          x = encoding.width / 2;\n          ctx.textAlign = 'center';\n        }\n        ctx.fillText(encoding.text, x, y);\n      }\n    }\n  }, {\n    key: \"moveCanvasDrawing\",\n    value: function moveCanvasDrawing(encoding) {\n      var ctx = this.canvas.getContext(\"2d\");\n      ctx.translate(encoding.width, 0);\n    }\n  }, {\n    key: \"restoreCanvas\",\n    value: function restoreCanvas() {\n      // Get the canvas context\n      var ctx = this.canvas.getContext(\"2d\");\n      ctx.restore();\n    }\n  }]);\n  return CanvasRenderer;\n}();\nexports.default = CanvasRenderer;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_merge", "require", "_merge2", "_interopRequireDefault", "_shared", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "canvas", "encodings", "options", "render", "getContext", "Error", "prepare<PERSON><PERSON><PERSON>", "encodingOptions", "drawCanvasBarcode", "drawCanvasText", "moveCanvasDrawing", "restoreCanvas", "ctx", "save", "calculateEncodingAttributes", "totalWidth", "getTotalWidthOfEncodings", "maxHeight", "getMaximumHeightOfEncodings", "width", "marginLeft", "marginRight", "height", "clearRect", "background", "fillStyle", "fillRect", "translate", "encoding", "binary", "data", "yFrom", "textPosition", "marginTop", "fontSize", "textMargin", "lineColor", "b", "x", "barcodePadding", "font", "fontOptions", "displayValue", "y", "textAlign", "fillText", "text", "restore"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/renderers/canvas.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _merge = require(\"../help/merge.js\");\n\nvar _merge2 = _interopRequireDefault(_merge);\n\nvar _shared = require(\"./shared.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar CanvasRenderer = function () {\n\tfunction CanvasRenderer(canvas, encodings, options) {\n\t\t_classCallCheck(this, CanvasRenderer);\n\n\t\tthis.canvas = canvas;\n\t\tthis.encodings = encodings;\n\t\tthis.options = options;\n\t}\n\n\t_createClass(CanvasRenderer, [{\n\t\tkey: \"render\",\n\t\tvalue: function render() {\n\t\t\t// Abort if the browser does not support HTML5 canvas\n\t\t\tif (!this.canvas.getContext) {\n\t\t\t\tthrow new Error('The browser does not support canvas.');\n\t\t\t}\n\n\t\t\tthis.prepareCanvas();\n\t\t\tfor (var i = 0; i < this.encodings.length; i++) {\n\t\t\t\tvar encodingOptions = (0, _merge2.default)(this.options, this.encodings[i].options);\n\n\t\t\t\tthis.drawCanvasBarcode(encodingOptions, this.encodings[i]);\n\t\t\t\tthis.drawCanvasText(encodingOptions, this.encodings[i]);\n\n\t\t\t\tthis.moveCanvasDrawing(this.encodings[i]);\n\t\t\t}\n\n\t\t\tthis.restoreCanvas();\n\t\t}\n\t}, {\n\t\tkey: \"prepareCanvas\",\n\t\tvalue: function prepareCanvas() {\n\t\t\t// Get the canvas context\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tctx.save();\n\n\t\t\t(0, _shared.calculateEncodingAttributes)(this.encodings, this.options, ctx);\n\t\t\tvar totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);\n\t\t\tvar maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);\n\n\t\t\tthis.canvas.width = totalWidth + this.options.marginLeft + this.options.marginRight;\n\n\t\t\tthis.canvas.height = maxHeight;\n\n\t\t\t// Paint the canvas\n\t\t\tctx.clearRect(0, 0, this.canvas.width, this.canvas.height);\n\t\t\tif (this.options.background) {\n\t\t\t\tctx.fillStyle = this.options.background;\n\t\t\t\tctx.fillRect(0, 0, this.canvas.width, this.canvas.height);\n\t\t\t}\n\n\t\t\tctx.translate(this.options.marginLeft, 0);\n\t\t}\n\t}, {\n\t\tkey: \"drawCanvasBarcode\",\n\t\tvalue: function drawCanvasBarcode(options, encoding) {\n\t\t\t// Get the canvas context\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tvar binary = encoding.data;\n\n\t\t\t// Creates the barcode out of the encoded binary\n\t\t\tvar yFrom;\n\t\t\tif (options.textPosition == \"top\") {\n\t\t\t\tyFrom = options.marginTop + options.fontSize + options.textMargin;\n\t\t\t} else {\n\t\t\t\tyFrom = options.marginTop;\n\t\t\t}\n\n\t\t\tctx.fillStyle = options.lineColor;\n\n\t\t\tfor (var b = 0; b < binary.length; b++) {\n\t\t\t\tvar x = b * options.width + encoding.barcodePadding;\n\n\t\t\t\tif (binary[b] === \"1\") {\n\t\t\t\t\tctx.fillRect(x, yFrom, options.width, options.height);\n\t\t\t\t} else if (binary[b]) {\n\t\t\t\t\tctx.fillRect(x, yFrom, options.width, options.height * binary[b]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"drawCanvasText\",\n\t\tvalue: function drawCanvasText(options, encoding) {\n\t\t\t// Get the canvas context\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tvar font = options.fontOptions + \" \" + options.fontSize + \"px \" + options.font;\n\n\t\t\t// Draw the text if displayValue is set\n\t\t\tif (options.displayValue) {\n\t\t\t\tvar x, y;\n\n\t\t\t\tif (options.textPosition == \"top\") {\n\t\t\t\t\ty = options.marginTop + options.fontSize - options.textMargin;\n\t\t\t\t} else {\n\t\t\t\t\ty = options.height + options.textMargin + options.marginTop + options.fontSize;\n\t\t\t\t}\n\n\t\t\t\tctx.font = font;\n\n\t\t\t\t// Draw the text in the correct X depending on the textAlign option\n\t\t\t\tif (options.textAlign == \"left\" || encoding.barcodePadding > 0) {\n\t\t\t\t\tx = 0;\n\t\t\t\t\tctx.textAlign = 'left';\n\t\t\t\t} else if (options.textAlign == \"right\") {\n\t\t\t\t\tx = encoding.width - 1;\n\t\t\t\t\tctx.textAlign = 'right';\n\t\t\t\t}\n\t\t\t\t// In all other cases, center the text\n\t\t\t\telse {\n\t\t\t\t\t\tx = encoding.width / 2;\n\t\t\t\t\t\tctx.textAlign = 'center';\n\t\t\t\t\t}\n\n\t\t\t\tctx.fillText(encoding.text, x, y);\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"moveCanvasDrawing\",\n\t\tvalue: function moveCanvasDrawing(encoding) {\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tctx.translate(encoding.width, 0);\n\t\t}\n\t}, {\n\t\tkey: \"restoreCanvas\",\n\t\tvalue: function restoreCanvas() {\n\t\t\t// Get the canvas context\n\t\t\tvar ctx = this.canvas.getContext(\"2d\");\n\n\t\t\tctx.restore();\n\t\t}\n\t}]);\n\n\treturn CanvasRenderer;\n}();\n\nexports.default = CanvasRenderer;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,MAAM,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAExC,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,IAAII,OAAO,GAAGH,OAAO,CAAC,aAAa,CAAC;AAEpC,SAASE,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEb,WAAW,EAAE;EAAE,IAAI,EAAEa,QAAQ,YAAYb,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIc,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,IAAIC,cAAc,GAAG,YAAY;EAChC,SAASA,cAAcA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE;IACnDN,eAAe,CAAC,IAAI,EAAEG,cAAc,CAAC;IAErC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;EACvB;EAEA7B,YAAY,CAAC0B,cAAc,EAAE,CAAC;IAC7BhB,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAAS+B,MAAMA,CAAA,EAAG;MACxB;MACA,IAAI,CAAC,IAAI,CAACH,MAAM,CAACI,UAAU,EAAE;QAC5B,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;MACxD;MAEA,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,KAAK,IAAI7B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACwB,SAAS,CAACvB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/C,IAAI8B,eAAe,GAAG,CAAC,CAAC,EAAEjB,OAAO,CAACK,OAAO,EAAE,IAAI,CAACO,OAAO,EAAE,IAAI,CAACD,SAAS,CAACxB,CAAC,CAAC,CAACyB,OAAO,CAAC;QAEnF,IAAI,CAACM,iBAAiB,CAACD,eAAe,EAAE,IAAI,CAACN,SAAS,CAACxB,CAAC,CAAC,CAAC;QAC1D,IAAI,CAACgC,cAAc,CAACF,eAAe,EAAE,IAAI,CAACN,SAAS,CAACxB,CAAC,CAAC,CAAC;QAEvD,IAAI,CAACiC,iBAAiB,CAAC,IAAI,CAACT,SAAS,CAACxB,CAAC,CAAC,CAAC;MAC1C;MAEA,IAAI,CAACkC,aAAa,CAAC,CAAC;IACrB;EACD,CAAC,EAAE;IACF5B,GAAG,EAAE,eAAe;IACpBX,KAAK,EAAE,SAASkC,aAAaA,CAAA,EAAG;MAC/B;MACA,IAAIM,GAAG,GAAG,IAAI,CAACZ,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MAEtCQ,GAAG,CAACC,IAAI,CAAC,CAAC;MAEV,CAAC,CAAC,EAAErB,OAAO,CAACsB,2BAA2B,EAAE,IAAI,CAACb,SAAS,EAAE,IAAI,CAACC,OAAO,EAAEU,GAAG,CAAC;MAC3E,IAAIG,UAAU,GAAG,CAAC,CAAC,EAAEvB,OAAO,CAACwB,wBAAwB,EAAE,IAAI,CAACf,SAAS,CAAC;MACtE,IAAIgB,SAAS,GAAG,CAAC,CAAC,EAAEzB,OAAO,CAAC0B,2BAA2B,EAAE,IAAI,CAACjB,SAAS,CAAC;MAExE,IAAI,CAACD,MAAM,CAACmB,KAAK,GAAGJ,UAAU,GAAG,IAAI,CAACb,OAAO,CAACkB,UAAU,GAAG,IAAI,CAAClB,OAAO,CAACmB,WAAW;MAEnF,IAAI,CAACrB,MAAM,CAACsB,MAAM,GAAGL,SAAS;;MAE9B;MACAL,GAAG,CAACW,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACvB,MAAM,CAACmB,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACsB,MAAM,CAAC;MAC1D,IAAI,IAAI,CAACpB,OAAO,CAACsB,UAAU,EAAE;QAC5BZ,GAAG,CAACa,SAAS,GAAG,IAAI,CAACvB,OAAO,CAACsB,UAAU;QACvCZ,GAAG,CAACc,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC1B,MAAM,CAACmB,KAAK,EAAE,IAAI,CAACnB,MAAM,CAACsB,MAAM,CAAC;MAC1D;MAEAV,GAAG,CAACe,SAAS,CAAC,IAAI,CAACzB,OAAO,CAACkB,UAAU,EAAE,CAAC,CAAC;IAC1C;EACD,CAAC,EAAE;IACFrC,GAAG,EAAE,mBAAmB;IACxBX,KAAK,EAAE,SAASoC,iBAAiBA,CAACN,OAAO,EAAE0B,QAAQ,EAAE;MACpD;MACA,IAAIhB,GAAG,GAAG,IAAI,CAACZ,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MAEtC,IAAIyB,MAAM,GAAGD,QAAQ,CAACE,IAAI;;MAE1B;MACA,IAAIC,KAAK;MACT,IAAI7B,OAAO,CAAC8B,YAAY,IAAI,KAAK,EAAE;QAClCD,KAAK,GAAG7B,OAAO,CAAC+B,SAAS,GAAG/B,OAAO,CAACgC,QAAQ,GAAGhC,OAAO,CAACiC,UAAU;MAClE,CAAC,MAAM;QACNJ,KAAK,GAAG7B,OAAO,CAAC+B,SAAS;MAC1B;MAEArB,GAAG,CAACa,SAAS,GAAGvB,OAAO,CAACkC,SAAS;MAEjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACnD,MAAM,EAAE2D,CAAC,EAAE,EAAE;QACvC,IAAIC,CAAC,GAAGD,CAAC,GAAGnC,OAAO,CAACiB,KAAK,GAAGS,QAAQ,CAACW,cAAc;QAEnD,IAAIV,MAAM,CAACQ,CAAC,CAAC,KAAK,GAAG,EAAE;UACtBzB,GAAG,CAACc,QAAQ,CAACY,CAAC,EAAEP,KAAK,EAAE7B,OAAO,CAACiB,KAAK,EAAEjB,OAAO,CAACoB,MAAM,CAAC;QACtD,CAAC,MAAM,IAAIO,MAAM,CAACQ,CAAC,CAAC,EAAE;UACrBzB,GAAG,CAACc,QAAQ,CAACY,CAAC,EAAEP,KAAK,EAAE7B,OAAO,CAACiB,KAAK,EAAEjB,OAAO,CAACoB,MAAM,GAAGO,MAAM,CAACQ,CAAC,CAAC,CAAC;QAClE;MACD;IACD;EACD,CAAC,EAAE;IACFtD,GAAG,EAAE,gBAAgB;IACrBX,KAAK,EAAE,SAASqC,cAAcA,CAACP,OAAO,EAAE0B,QAAQ,EAAE;MACjD;MACA,IAAIhB,GAAG,GAAG,IAAI,CAACZ,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MAEtC,IAAIoC,IAAI,GAAGtC,OAAO,CAACuC,WAAW,GAAG,GAAG,GAAGvC,OAAO,CAACgC,QAAQ,GAAG,KAAK,GAAGhC,OAAO,CAACsC,IAAI;;MAE9E;MACA,IAAItC,OAAO,CAACwC,YAAY,EAAE;QACzB,IAAIJ,CAAC,EAAEK,CAAC;QAER,IAAIzC,OAAO,CAAC8B,YAAY,IAAI,KAAK,EAAE;UAClCW,CAAC,GAAGzC,OAAO,CAAC+B,SAAS,GAAG/B,OAAO,CAACgC,QAAQ,GAAGhC,OAAO,CAACiC,UAAU;QAC9D,CAAC,MAAM;UACNQ,CAAC,GAAGzC,OAAO,CAACoB,MAAM,GAAGpB,OAAO,CAACiC,UAAU,GAAGjC,OAAO,CAAC+B,SAAS,GAAG/B,OAAO,CAACgC,QAAQ;QAC/E;QAEAtB,GAAG,CAAC4B,IAAI,GAAGA,IAAI;;QAEf;QACA,IAAItC,OAAO,CAAC0C,SAAS,IAAI,MAAM,IAAIhB,QAAQ,CAACW,cAAc,GAAG,CAAC,EAAE;UAC/DD,CAAC,GAAG,CAAC;UACL1B,GAAG,CAACgC,SAAS,GAAG,MAAM;QACvB,CAAC,MAAM,IAAI1C,OAAO,CAAC0C,SAAS,IAAI,OAAO,EAAE;UACxCN,CAAC,GAAGV,QAAQ,CAACT,KAAK,GAAG,CAAC;UACtBP,GAAG,CAACgC,SAAS,GAAG,OAAO;QACxB;QACA;QAAA,KACK;UACHN,CAAC,GAAGV,QAAQ,CAACT,KAAK,GAAG,CAAC;UACtBP,GAAG,CAACgC,SAAS,GAAG,QAAQ;QACzB;QAEDhC,GAAG,CAACiC,QAAQ,CAACjB,QAAQ,CAACkB,IAAI,EAAER,CAAC,EAAEK,CAAC,CAAC;MAClC;IACD;EACD,CAAC,EAAE;IACF5D,GAAG,EAAE,mBAAmB;IACxBX,KAAK,EAAE,SAASsC,iBAAiBA,CAACkB,QAAQ,EAAE;MAC3C,IAAIhB,GAAG,GAAG,IAAI,CAACZ,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MAEtCQ,GAAG,CAACe,SAAS,CAACC,QAAQ,CAACT,KAAK,EAAE,CAAC,CAAC;IACjC;EACD,CAAC,EAAE;IACFpC,GAAG,EAAE,eAAe;IACpBX,KAAK,EAAE,SAASuC,aAAaA,CAAA,EAAG;MAC/B;MACA,IAAIC,GAAG,GAAG,IAAI,CAACZ,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;MAEtCQ,GAAG,CAACmC,OAAO,CAAC,CAAC;IACd;EACD,CAAC,CAAC,CAAC;EAEH,OAAOhD,cAAc;AACtB,CAAC,CAAC,CAAC;AAEH5B,OAAO,CAACwB,OAAO,GAAGI,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}