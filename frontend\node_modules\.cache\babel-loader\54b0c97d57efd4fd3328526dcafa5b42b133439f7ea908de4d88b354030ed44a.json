{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport InvertedLuminanceSource from '../core/InvertedLuminanceSource';\nimport LuminanceSource from '../core/LuminanceSource';\nimport IllegalArgumentException from '../core/IllegalArgumentException';\n/**\n * @deprecated Moving to @zxing/browser\n */\nvar HTMLCanvasElementLuminanceSource = /** @class */function (_super) {\n  __extends(HTMLCanvasElementLuminanceSource, _super);\n  function HTMLCanvasElementLuminanceSource(canvas, doAutoInvert) {\n    if (doAutoInvert === void 0) {\n      doAutoInvert = false;\n    }\n    var _this = _super.call(this, canvas.width, canvas.height) || this;\n    _this.canvas = canvas;\n    _this.tempCanvasElement = null;\n    _this.buffer = HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData(canvas, doAutoInvert);\n    return _this;\n  }\n  HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData = function (canvas, doAutoInvert) {\n    if (doAutoInvert === void 0) {\n      doAutoInvert = false;\n    }\n    var imageData = canvas.getContext('2d').getImageData(0, 0, canvas.width, canvas.height);\n    return HTMLCanvasElementLuminanceSource.toGrayscaleBuffer(imageData.data, canvas.width, canvas.height, doAutoInvert);\n  };\n  HTMLCanvasElementLuminanceSource.toGrayscaleBuffer = function (imageBuffer, width, height, doAutoInvert) {\n    if (doAutoInvert === void 0) {\n      doAutoInvert = false;\n    }\n    var grayscaleBuffer = new Uint8ClampedArray(width * height);\n    HTMLCanvasElementLuminanceSource.FRAME_INDEX = !HTMLCanvasElementLuminanceSource.FRAME_INDEX;\n    if (HTMLCanvasElementLuminanceSource.FRAME_INDEX || !doAutoInvert) {\n      for (var i = 0, j = 0, length_1 = imageBuffer.length; i < length_1; i += 4, j++) {\n        var gray = void 0;\n        var alpha = imageBuffer[i + 3];\n        // The color of fully-transparent pixels is irrelevant. They are often, technically, fully-transparent\n        // black (0 alpha, and then 0 RGB). They are often used, of course as the \"white\" area in a\n        // barcode image. Force any such pixel to be white:\n        if (alpha === 0) {\n          gray = 0xFF;\n        } else {\n          var pixelR = imageBuffer[i];\n          var pixelG = imageBuffer[i + 1];\n          var pixelB = imageBuffer[i + 2];\n          // .299R + 0.587G + 0.114B (YUV/YIQ for PAL and NTSC),\n          // (306*R) >> 10 is approximately equal to R*0.299, and so on.\n          // 0x200 >> 10 is 0.5, it implements rounding.\n          gray = 306 * pixelR + 601 * pixelG + 117 * pixelB + 0x200 >> 10;\n        }\n        grayscaleBuffer[j] = gray;\n      }\n    } else {\n      for (var i = 0, j = 0, length_2 = imageBuffer.length; i < length_2; i += 4, j++) {\n        var gray = void 0;\n        var alpha = imageBuffer[i + 3];\n        // The color of fully-transparent pixels is irrelevant. They are often, technically, fully-transparent\n        // black (0 alpha, and then 0 RGB). They are often used, of course as the \"white\" area in a\n        // barcode image. Force any such pixel to be white:\n        if (alpha === 0) {\n          gray = 0xFF;\n        } else {\n          var pixelR = imageBuffer[i];\n          var pixelG = imageBuffer[i + 1];\n          var pixelB = imageBuffer[i + 2];\n          // .299R + 0.587G + 0.114B (YUV/YIQ for PAL and NTSC),\n          // (306*R) >> 10 is approximately equal to R*0.299, and so on.\n          // 0x200 >> 10 is 0.5, it implements rounding.\n          gray = 306 * pixelR + 601 * pixelG + 117 * pixelB + 0x200 >> 10;\n        }\n        grayscaleBuffer[j] = 0xFF - gray;\n      }\n    }\n    return grayscaleBuffer;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n    if (y < 0 || y >= this.getHeight()) {\n      throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n    }\n    var width = this.getWidth();\n    var start = y * width;\n    if (row === null) {\n      row = this.buffer.slice(start, start + width);\n    } else {\n      if (row.length < width) {\n        row = new Uint8ClampedArray(width);\n      }\n      // The underlying raster of image consists of bytes with the luminance values\n      // TODO: can avoid set/slice?\n      row.set(this.buffer.slice(start, start + width));\n    }\n    return row;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.getMatrix = function () {\n    return this.buffer;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.isCropSupported = function () {\n    return true;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n    _super.prototype.crop.call(this, left, top, width, height);\n    return this;\n  };\n  /**\n   * This is always true, since the image is a gray-scale image.\n   *\n   * @return true\n   */\n  HTMLCanvasElementLuminanceSource.prototype.isRotateSupported = function () {\n    return true;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.rotateCounterClockwise = function () {\n    this.rotate(-90);\n    return this;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.rotateCounterClockwise45 = function () {\n    this.rotate(-45);\n    return this;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.getTempCanvasElement = function () {\n    if (null === this.tempCanvasElement) {\n      var tempCanvasElement = this.canvas.ownerDocument.createElement('canvas');\n      tempCanvasElement.width = this.canvas.width;\n      tempCanvasElement.height = this.canvas.height;\n      this.tempCanvasElement = tempCanvasElement;\n    }\n    return this.tempCanvasElement;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.rotate = function (angle) {\n    var tempCanvasElement = this.getTempCanvasElement();\n    var tempContext = tempCanvasElement.getContext('2d');\n    var angleRadians = angle * HTMLCanvasElementLuminanceSource.DEGREE_TO_RADIANS;\n    // Calculate and set new dimensions for temp canvas\n    var width = this.canvas.width;\n    var height = this.canvas.height;\n    var newWidth = Math.ceil(Math.abs(Math.cos(angleRadians)) * width + Math.abs(Math.sin(angleRadians)) * height);\n    var newHeight = Math.ceil(Math.abs(Math.sin(angleRadians)) * width + Math.abs(Math.cos(angleRadians)) * height);\n    tempCanvasElement.width = newWidth;\n    tempCanvasElement.height = newHeight;\n    // Draw at center of temp canvas to prevent clipping of image data\n    tempContext.translate(newWidth / 2, newHeight / 2);\n    tempContext.rotate(angleRadians);\n    tempContext.drawImage(this.canvas, width / -2, height / -2);\n    this.buffer = HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData(tempCanvasElement);\n    return this;\n  };\n  HTMLCanvasElementLuminanceSource.prototype.invert = function () {\n    return new InvertedLuminanceSource(this);\n  };\n  HTMLCanvasElementLuminanceSource.DEGREE_TO_RADIANS = Math.PI / 180;\n  HTMLCanvasElementLuminanceSource.FRAME_INDEX = true;\n  return HTMLCanvasElementLuminanceSource;\n}(LuminanceSource);\nexport { HTMLCanvasElementLuminanceSource };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "InvertedLuminanceSource", "LuminanceSource", "IllegalArgumentException", "HTMLCanvasElementLuminanceSource", "_super", "canvas", "doAutoInvert", "_this", "call", "width", "height", "tempCanvasElement", "buffer", "makeBufferFromCanvasImageData", "imageData", "getContext", "getImageData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "imageBuffer", "grayscale<PERSON><PERSON>er", "Uint8ClampedArray", "FRAME_INDEX", "i", "j", "length_1", "length", "gray", "alpha", "pixelR", "pixelG", "pixelB", "length_2", "getRow", "y", "row", "getHeight", "getWidth", "start", "slice", "set", "getMatrix", "isCropSupported", "crop", "left", "top", "isRotateSupported", "rotateCounterClockwise", "rotate", "rotateCounterClockwise45", "getTempCanvasElement", "ownerDocument", "createElement", "angle", "tempContext", "angleRadians", "DEGREE_TO_RADIANS", "newWidth", "Math", "ceil", "abs", "cos", "sin", "newHeight", "translate", "drawImage", "invert", "PI"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/browser/HTMLCanvasElementLuminanceSource.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport InvertedLuminanceSource from '../core/InvertedLuminanceSource';\nimport LuminanceSource from '../core/LuminanceSource';\nimport IllegalArgumentException from '../core/IllegalArgumentException';\n/**\n * @deprecated Moving to @zxing/browser\n */\nvar HTMLCanvasElementLuminanceSource = /** @class */ (function (_super) {\n    __extends(HTMLCanvasElementLuminanceSource, _super);\n    function HTMLCanvasElementLuminanceSource(canvas, doAutoInvert) {\n        if (doAutoInvert === void 0) { doAutoInvert = false; }\n        var _this = _super.call(this, canvas.width, canvas.height) || this;\n        _this.canvas = canvas;\n        _this.tempCanvasElement = null;\n        _this.buffer = HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData(canvas, doAutoInvert);\n        return _this;\n    }\n    HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData = function (canvas, doAutoInvert) {\n        if (doAutoInvert === void 0) { doAutoInvert = false; }\n        var imageData = canvas.getContext('2d').getImageData(0, 0, canvas.width, canvas.height);\n        return HTMLCanvasElementLuminanceSource.toGrayscaleBuffer(imageData.data, canvas.width, canvas.height, doAutoInvert);\n    };\n    HTMLCanvasElementLuminanceSource.toGrayscaleBuffer = function (imageBuffer, width, height, doAutoInvert) {\n        if (doAutoInvert === void 0) { doAutoInvert = false; }\n        var grayscaleBuffer = new Uint8ClampedArray(width * height);\n        HTMLCanvasElementLuminanceSource.FRAME_INDEX = !HTMLCanvasElementLuminanceSource.FRAME_INDEX;\n        if (HTMLCanvasElementLuminanceSource.FRAME_INDEX || !doAutoInvert) {\n            for (var i = 0, j = 0, length_1 = imageBuffer.length; i < length_1; i += 4, j++) {\n                var gray = void 0;\n                var alpha = imageBuffer[i + 3];\n                // The color of fully-transparent pixels is irrelevant. They are often, technically, fully-transparent\n                // black (0 alpha, and then 0 RGB). They are often used, of course as the \"white\" area in a\n                // barcode image. Force any such pixel to be white:\n                if (alpha === 0) {\n                    gray = 0xFF;\n                }\n                else {\n                    var pixelR = imageBuffer[i];\n                    var pixelG = imageBuffer[i + 1];\n                    var pixelB = imageBuffer[i + 2];\n                    // .299R + 0.587G + 0.114B (YUV/YIQ for PAL and NTSC),\n                    // (306*R) >> 10 is approximately equal to R*0.299, and so on.\n                    // 0x200 >> 10 is 0.5, it implements rounding.\n                    gray = (306 * pixelR +\n                        601 * pixelG +\n                        117 * pixelB +\n                        0x200) >> 10;\n                }\n                grayscaleBuffer[j] = gray;\n            }\n        }\n        else {\n            for (var i = 0, j = 0, length_2 = imageBuffer.length; i < length_2; i += 4, j++) {\n                var gray = void 0;\n                var alpha = imageBuffer[i + 3];\n                // The color of fully-transparent pixels is irrelevant. They are often, technically, fully-transparent\n                // black (0 alpha, and then 0 RGB). They are often used, of course as the \"white\" area in a\n                // barcode image. Force any such pixel to be white:\n                if (alpha === 0) {\n                    gray = 0xFF;\n                }\n                else {\n                    var pixelR = imageBuffer[i];\n                    var pixelG = imageBuffer[i + 1];\n                    var pixelB = imageBuffer[i + 2];\n                    // .299R + 0.587G + 0.114B (YUV/YIQ for PAL and NTSC),\n                    // (306*R) >> 10 is approximately equal to R*0.299, and so on.\n                    // 0x200 >> 10 is 0.5, it implements rounding.\n                    gray = (306 * pixelR +\n                        601 * pixelG +\n                        117 * pixelB +\n                        0x200) >> 10;\n                }\n                grayscaleBuffer[j] = 0xFF - gray;\n            }\n        }\n        return grayscaleBuffer;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n        if (y < 0 || y >= this.getHeight()) {\n            throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n        }\n        var width = this.getWidth();\n        var start = y * width;\n        if (row === null) {\n            row = this.buffer.slice(start, start + width);\n        }\n        else {\n            if (row.length < width) {\n                row = new Uint8ClampedArray(width);\n            }\n            // The underlying raster of image consists of bytes with the luminance values\n            // TODO: can avoid set/slice?\n            row.set(this.buffer.slice(start, start + width));\n        }\n        return row;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.getMatrix = function () {\n        return this.buffer;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.isCropSupported = function () {\n        return true;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        _super.prototype.crop.call(this, left, top, width, height);\n        return this;\n    };\n    /**\n     * This is always true, since the image is a gray-scale image.\n     *\n     * @return true\n     */\n    HTMLCanvasElementLuminanceSource.prototype.isRotateSupported = function () {\n        return true;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.rotateCounterClockwise = function () {\n        this.rotate(-90);\n        return this;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.rotateCounterClockwise45 = function () {\n        this.rotate(-45);\n        return this;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.getTempCanvasElement = function () {\n        if (null === this.tempCanvasElement) {\n            var tempCanvasElement = this.canvas.ownerDocument.createElement('canvas');\n            tempCanvasElement.width = this.canvas.width;\n            tempCanvasElement.height = this.canvas.height;\n            this.tempCanvasElement = tempCanvasElement;\n        }\n        return this.tempCanvasElement;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.rotate = function (angle) {\n        var tempCanvasElement = this.getTempCanvasElement();\n        var tempContext = tempCanvasElement.getContext('2d');\n        var angleRadians = angle * HTMLCanvasElementLuminanceSource.DEGREE_TO_RADIANS;\n        // Calculate and set new dimensions for temp canvas\n        var width = this.canvas.width;\n        var height = this.canvas.height;\n        var newWidth = Math.ceil(Math.abs(Math.cos(angleRadians)) * width + Math.abs(Math.sin(angleRadians)) * height);\n        var newHeight = Math.ceil(Math.abs(Math.sin(angleRadians)) * width + Math.abs(Math.cos(angleRadians)) * height);\n        tempCanvasElement.width = newWidth;\n        tempCanvasElement.height = newHeight;\n        // Draw at center of temp canvas to prevent clipping of image data\n        tempContext.translate(newWidth / 2, newHeight / 2);\n        tempContext.rotate(angleRadians);\n        tempContext.drawImage(this.canvas, width / -2, height / -2);\n        this.buffer = HTMLCanvasElementLuminanceSource.makeBufferFromCanvasImageData(tempCanvasElement);\n        return this;\n    };\n    HTMLCanvasElementLuminanceSource.prototype.invert = function () {\n        return new InvertedLuminanceSource(this);\n    };\n    HTMLCanvasElementLuminanceSource.DEGREE_TO_RADIANS = Math.PI / 180;\n    HTMLCanvasElementLuminanceSource.FRAME_INDEX = true;\n    return HTMLCanvasElementLuminanceSource;\n}(LuminanceSource));\nexport { HTMLCanvasElementLuminanceSource };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,uBAAuB,MAAM,iCAAiC;AACrE,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,wBAAwB,MAAM,kCAAkC;AACvE;AACA;AACA;AACA,IAAIC,gCAAgC,GAAG,aAAe,UAAUC,MAAM,EAAE;EACpElB,SAAS,CAACiB,gCAAgC,EAAEC,MAAM,CAAC;EACnD,SAASD,gCAAgCA,CAACE,MAAM,EAAEC,YAAY,EAAE;IAC5D,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;MAAEA,YAAY,GAAG,KAAK;IAAE;IACrD,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,EAAEH,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,MAAM,CAAC,IAAI,IAAI;IAClEH,KAAK,CAACF,MAAM,GAAGA,MAAM;IACrBE,KAAK,CAACI,iBAAiB,GAAG,IAAI;IAC9BJ,KAAK,CAACK,MAAM,GAAGT,gCAAgC,CAACU,6BAA6B,CAACR,MAAM,EAAEC,YAAY,CAAC;IACnG,OAAOC,KAAK;EAChB;EACAJ,gCAAgC,CAACU,6BAA6B,GAAG,UAAUR,MAAM,EAAEC,YAAY,EAAE;IAC7F,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;MAAEA,YAAY,GAAG,KAAK;IAAE;IACrD,IAAIQ,SAAS,GAAGT,MAAM,CAACU,UAAU,CAAC,IAAI,CAAC,CAACC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAEX,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,MAAM,CAAC;IACvF,OAAOP,gCAAgC,CAACc,iBAAiB,CAACH,SAAS,CAACI,IAAI,EAAEb,MAAM,CAACI,KAAK,EAAEJ,MAAM,CAACK,MAAM,EAAEJ,YAAY,CAAC;EACxH,CAAC;EACDH,gCAAgC,CAACc,iBAAiB,GAAG,UAAUE,WAAW,EAAEV,KAAK,EAAEC,MAAM,EAAEJ,YAAY,EAAE;IACrG,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;MAAEA,YAAY,GAAG,KAAK;IAAE;IACrD,IAAIc,eAAe,GAAG,IAAIC,iBAAiB,CAACZ,KAAK,GAAGC,MAAM,CAAC;IAC3DP,gCAAgC,CAACmB,WAAW,GAAG,CAACnB,gCAAgC,CAACmB,WAAW;IAC5F,IAAInB,gCAAgC,CAACmB,WAAW,IAAI,CAAChB,YAAY,EAAE;MAC/D,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,QAAQ,GAAGN,WAAW,CAACO,MAAM,EAAEH,CAAC,GAAGE,QAAQ,EAAEF,CAAC,IAAI,CAAC,EAAEC,CAAC,EAAE,EAAE;QAC7E,IAAIG,IAAI,GAAG,KAAK,CAAC;QACjB,IAAIC,KAAK,GAAGT,WAAW,CAACI,CAAC,GAAG,CAAC,CAAC;QAC9B;QACA;QACA;QACA,IAAIK,KAAK,KAAK,CAAC,EAAE;UACbD,IAAI,GAAG,IAAI;QACf,CAAC,MACI;UACD,IAAIE,MAAM,GAAGV,WAAW,CAACI,CAAC,CAAC;UAC3B,IAAIO,MAAM,GAAGX,WAAW,CAACI,CAAC,GAAG,CAAC,CAAC;UAC/B,IAAIQ,MAAM,GAAGZ,WAAW,CAACI,CAAC,GAAG,CAAC,CAAC;UAC/B;UACA;UACA;UACAI,IAAI,GAAI,GAAG,GAAGE,MAAM,GAChB,GAAG,GAAGC,MAAM,GACZ,GAAG,GAAGC,MAAM,GACZ,KAAK,IAAK,EAAE;QACpB;QACAX,eAAe,CAACI,CAAC,CAAC,GAAGG,IAAI;MAC7B;IACJ,CAAC,MACI;MACD,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEQ,QAAQ,GAAGb,WAAW,CAACO,MAAM,EAAEH,CAAC,GAAGS,QAAQ,EAAET,CAAC,IAAI,CAAC,EAAEC,CAAC,EAAE,EAAE;QAC7E,IAAIG,IAAI,GAAG,KAAK,CAAC;QACjB,IAAIC,KAAK,GAAGT,WAAW,CAACI,CAAC,GAAG,CAAC,CAAC;QAC9B;QACA;QACA;QACA,IAAIK,KAAK,KAAK,CAAC,EAAE;UACbD,IAAI,GAAG,IAAI;QACf,CAAC,MACI;UACD,IAAIE,MAAM,GAAGV,WAAW,CAACI,CAAC,CAAC;UAC3B,IAAIO,MAAM,GAAGX,WAAW,CAACI,CAAC,GAAG,CAAC,CAAC;UAC/B,IAAIQ,MAAM,GAAGZ,WAAW,CAACI,CAAC,GAAG,CAAC,CAAC;UAC/B;UACA;UACA;UACAI,IAAI,GAAI,GAAG,GAAGE,MAAM,GAChB,GAAG,GAAGC,MAAM,GACZ,GAAG,GAAGC,MAAM,GACZ,KAAK,IAAK,EAAE;QACpB;QACAX,eAAe,CAACI,CAAC,CAAC,GAAG,IAAI,GAAGG,IAAI;MACpC;IACJ;IACA,OAAOP,eAAe;EAC1B,CAAC;EACDjB,gCAAgC,CAACL,SAAS,CAACmC,MAAM,GAAG,UAAUC,CAAC,CAAC,SAASC,GAAG,EAAE;IAC1E,IAAID,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAI,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE;MAChC,MAAM,IAAIlC,wBAAwB,CAAC,sCAAsC,GAAGgC,CAAC,CAAC;IAClF;IACA,IAAIzB,KAAK,GAAG,IAAI,CAAC4B,QAAQ,CAAC,CAAC;IAC3B,IAAIC,KAAK,GAAGJ,CAAC,GAAGzB,KAAK;IACrB,IAAI0B,GAAG,KAAK,IAAI,EAAE;MACdA,GAAG,GAAG,IAAI,CAACvB,MAAM,CAAC2B,KAAK,CAACD,KAAK,EAAEA,KAAK,GAAG7B,KAAK,CAAC;IACjD,CAAC,MACI;MACD,IAAI0B,GAAG,CAACT,MAAM,GAAGjB,KAAK,EAAE;QACpB0B,GAAG,GAAG,IAAId,iBAAiB,CAACZ,KAAK,CAAC;MACtC;MACA;MACA;MACA0B,GAAG,CAACK,GAAG,CAAC,IAAI,CAAC5B,MAAM,CAAC2B,KAAK,CAACD,KAAK,EAAEA,KAAK,GAAG7B,KAAK,CAAC,CAAC;IACpD;IACA,OAAO0B,GAAG;EACd,CAAC;EACDhC,gCAAgC,CAACL,SAAS,CAAC2C,SAAS,GAAG,YAAY;IAC/D,OAAO,IAAI,CAAC7B,MAAM;EACtB,CAAC;EACDT,gCAAgC,CAACL,SAAS,CAAC4C,eAAe,GAAG,YAAY;IACrE,OAAO,IAAI;EACf,CAAC;EACDvC,gCAAgC,CAACL,SAAS,CAAC6C,IAAI,GAAG,UAAUC,IAAI,CAAC,SAASC,GAAG,CAAC,SAASpC,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IAClHN,MAAM,CAACN,SAAS,CAAC6C,IAAI,CAACnC,IAAI,CAAC,IAAI,EAAEoC,IAAI,EAAEC,GAAG,EAAEpC,KAAK,EAAEC,MAAM,CAAC;IAC1D,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIP,gCAAgC,CAACL,SAAS,CAACgD,iBAAiB,GAAG,YAAY;IACvE,OAAO,IAAI;EACf,CAAC;EACD3C,gCAAgC,CAACL,SAAS,CAACiD,sBAAsB,GAAG,YAAY;IAC5E,IAAI,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC;IAChB,OAAO,IAAI;EACf,CAAC;EACD7C,gCAAgC,CAACL,SAAS,CAACmD,wBAAwB,GAAG,YAAY;IAC9E,IAAI,CAACD,MAAM,CAAC,CAAC,EAAE,CAAC;IAChB,OAAO,IAAI;EACf,CAAC;EACD7C,gCAAgC,CAACL,SAAS,CAACoD,oBAAoB,GAAG,YAAY;IAC1E,IAAI,IAAI,KAAK,IAAI,CAACvC,iBAAiB,EAAE;MACjC,IAAIA,iBAAiB,GAAG,IAAI,CAACN,MAAM,CAAC8C,aAAa,CAACC,aAAa,CAAC,QAAQ,CAAC;MACzEzC,iBAAiB,CAACF,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACI,KAAK;MAC3CE,iBAAiB,CAACD,MAAM,GAAG,IAAI,CAACL,MAAM,CAACK,MAAM;MAC7C,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC9C;IACA,OAAO,IAAI,CAACA,iBAAiB;EACjC,CAAC;EACDR,gCAAgC,CAACL,SAAS,CAACkD,MAAM,GAAG,UAAUK,KAAK,EAAE;IACjE,IAAI1C,iBAAiB,GAAG,IAAI,CAACuC,oBAAoB,CAAC,CAAC;IACnD,IAAII,WAAW,GAAG3C,iBAAiB,CAACI,UAAU,CAAC,IAAI,CAAC;IACpD,IAAIwC,YAAY,GAAGF,KAAK,GAAGlD,gCAAgC,CAACqD,iBAAiB;IAC7E;IACA,IAAI/C,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACI,KAAK;IAC7B,IAAIC,MAAM,GAAG,IAAI,CAACL,MAAM,CAACK,MAAM;IAC/B,IAAI+C,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACF,IAAI,CAACG,GAAG,CAACN,YAAY,CAAC,CAAC,GAAG9C,KAAK,GAAGiD,IAAI,CAACE,GAAG,CAACF,IAAI,CAACI,GAAG,CAACP,YAAY,CAAC,CAAC,GAAG7C,MAAM,CAAC;IAC9G,IAAIqD,SAAS,GAAGL,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,GAAG,CAACF,IAAI,CAACI,GAAG,CAACP,YAAY,CAAC,CAAC,GAAG9C,KAAK,GAAGiD,IAAI,CAACE,GAAG,CAACF,IAAI,CAACG,GAAG,CAACN,YAAY,CAAC,CAAC,GAAG7C,MAAM,CAAC;IAC/GC,iBAAiB,CAACF,KAAK,GAAGgD,QAAQ;IAClC9C,iBAAiB,CAACD,MAAM,GAAGqD,SAAS;IACpC;IACAT,WAAW,CAACU,SAAS,CAACP,QAAQ,GAAG,CAAC,EAAEM,SAAS,GAAG,CAAC,CAAC;IAClDT,WAAW,CAACN,MAAM,CAACO,YAAY,CAAC;IAChCD,WAAW,CAACW,SAAS,CAAC,IAAI,CAAC5D,MAAM,EAAEI,KAAK,GAAG,CAAC,CAAC,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACE,MAAM,GAAGT,gCAAgC,CAACU,6BAA6B,CAACF,iBAAiB,CAAC;IAC/F,OAAO,IAAI;EACf,CAAC;EACDR,gCAAgC,CAACL,SAAS,CAACoE,MAAM,GAAG,YAAY;IAC5D,OAAO,IAAIlE,uBAAuB,CAAC,IAAI,CAAC;EAC5C,CAAC;EACDG,gCAAgC,CAACqD,iBAAiB,GAAGE,IAAI,CAACS,EAAE,GAAG,GAAG;EAClEhE,gCAAgC,CAACmB,WAAW,GAAG,IAAI;EACnD,OAAOnB,gCAAgC;AAC3C,CAAC,CAACF,eAAe,CAAE;AACnB,SAASE,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}