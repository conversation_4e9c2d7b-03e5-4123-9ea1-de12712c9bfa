{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport Version from './Version';\nimport FormatInformation from './FormatInformation';\nimport DataMask from './DataMask';\nimport FormatException from '../../FormatException';\n/**\n * <AUTHOR>\n */\nvar BitMatrixParser = /** @class */function () {\n  /**\n   * @param bitMatrix {@link BitMatrix} to parse\n   * @throws FormatException if dimension is not >= 21 and 1 mod 4\n   */\n  function BitMatrixParser(bitMatrix) {\n    var dimension = bitMatrix.getHeight();\n    if (dimension < 21 || (dimension & 0x03) !== 1) {\n      throw new FormatException();\n    }\n    this.bitMatrix = bitMatrix;\n  }\n  /**\n   * <p>Reads format information from one of its two locations within the QR Code.</p>\n   *\n   * @return {@link FormatInformation} encapsulating the QR Code's format info\n   * @throws FormatException if both format information locations cannot be parsed as\n   * the valid encoding of format information\n   */\n  BitMatrixParser.prototype.readFormatInformation = function () {\n    if (this.parsedFormatInfo !== null && this.parsedFormatInfo !== undefined) {\n      return this.parsedFormatInfo;\n    }\n    // Read top-left format info bits\n    var formatInfoBits1 = 0;\n    for (var i = 0; i < 6; i++) {\n      formatInfoBits1 = this.copyBit(i, 8, formatInfoBits1);\n    }\n    // .. and skip a bit in the timing pattern ...\n    formatInfoBits1 = this.copyBit(7, 8, formatInfoBits1);\n    formatInfoBits1 = this.copyBit(8, 8, formatInfoBits1);\n    formatInfoBits1 = this.copyBit(8, 7, formatInfoBits1);\n    // .. and skip a bit in the timing pattern ...\n    for (var j = 5; j >= 0; j--) {\n      formatInfoBits1 = this.copyBit(8, j, formatInfoBits1);\n    }\n    // Read the top-right/bottom-left pattern too\n    var dimension = this.bitMatrix.getHeight();\n    var formatInfoBits2 = 0;\n    var jMin = dimension - 7;\n    for (var j = dimension - 1; j >= jMin; j--) {\n      formatInfoBits2 = this.copyBit(8, j, formatInfoBits2);\n    }\n    for (var i = dimension - 8; i < dimension; i++) {\n      formatInfoBits2 = this.copyBit(i, 8, formatInfoBits2);\n    }\n    this.parsedFormatInfo = FormatInformation.decodeFormatInformation(formatInfoBits1, formatInfoBits2);\n    if (this.parsedFormatInfo !== null) {\n      return this.parsedFormatInfo;\n    }\n    throw new FormatException();\n  };\n  /**\n   * <p>Reads version information from one of its two locations within the QR Code.</p>\n   *\n   * @return {@link Version} encapsulating the QR Code's version\n   * @throws FormatException if both version information locations cannot be parsed as\n   * the valid encoding of version information\n   */\n  BitMatrixParser.prototype.readVersion = function () {\n    if (this.parsedVersion !== null && this.parsedVersion !== undefined) {\n      return this.parsedVersion;\n    }\n    var dimension = this.bitMatrix.getHeight();\n    var provisionalVersion = Math.floor((dimension - 17) / 4);\n    if (provisionalVersion <= 6) {\n      return Version.getVersionForNumber(provisionalVersion);\n    }\n    // Read top-right version info: 3 wide by 6 tall\n    var versionBits = 0;\n    var ijMin = dimension - 11;\n    for (var j = 5; j >= 0; j--) {\n      for (var i = dimension - 9; i >= ijMin; i--) {\n        versionBits = this.copyBit(i, j, versionBits);\n      }\n    }\n    var theParsedVersion = Version.decodeVersionInformation(versionBits);\n    if (theParsedVersion !== null && theParsedVersion.getDimensionForVersion() === dimension) {\n      this.parsedVersion = theParsedVersion;\n      return theParsedVersion;\n    }\n    // Hmm, failed. Try bottom left: 6 wide by 3 tall\n    versionBits = 0;\n    for (var i = 5; i >= 0; i--) {\n      for (var j = dimension - 9; j >= ijMin; j--) {\n        versionBits = this.copyBit(i, j, versionBits);\n      }\n    }\n    theParsedVersion = Version.decodeVersionInformation(versionBits);\n    if (theParsedVersion !== null && theParsedVersion.getDimensionForVersion() === dimension) {\n      this.parsedVersion = theParsedVersion;\n      return theParsedVersion;\n    }\n    throw new FormatException();\n  };\n  BitMatrixParser.prototype.copyBit = function (i /*int*/, j /*int*/, versionBits /*int*/) {\n    var bit = this.isMirror ? this.bitMatrix.get(j, i) : this.bitMatrix.get(i, j);\n    return bit ? versionBits << 1 | 0x1 : versionBits << 1;\n  };\n  /**\n   * <p>Reads the bits in the {@link BitMatrix} representing the finder pattern in the\n   * correct order in order to reconstruct the codewords bytes contained within the\n   * QR Code.</p>\n   *\n   * @return bytes encoded within the QR Code\n   * @throws FormatException if the exact number of bytes expected is not read\n   */\n  BitMatrixParser.prototype.readCodewords = function () {\n    var formatInfo = this.readFormatInformation();\n    var version = this.readVersion();\n    // Get the data mask for the format used in this QR Code. This will exclude\n    // some bits from reading as we wind through the bit matrix.\n    var dataMask = DataMask.values.get(formatInfo.getDataMask());\n    var dimension = this.bitMatrix.getHeight();\n    dataMask.unmaskBitMatrix(this.bitMatrix, dimension);\n    var functionPattern = version.buildFunctionPattern();\n    var readingUp = true;\n    var result = new Uint8Array(version.getTotalCodewords());\n    var resultOffset = 0;\n    var currentByte = 0;\n    var bitsRead = 0;\n    // Read columns in pairs, from right to left\n    for (var j = dimension - 1; j > 0; j -= 2) {\n      if (j === 6) {\n        // Skip whole column with vertical alignment pattern\n        // saves time and makes the other code proceed more cleanly\n        j--;\n      }\n      // Read alternatingly from bottom to top then top to bottom\n      for (var count = 0; count < dimension; count++) {\n        var i = readingUp ? dimension - 1 - count : count;\n        for (var col = 0; col < 2; col++) {\n          // Ignore bits covered by the function pattern\n          if (!functionPattern.get(j - col, i)) {\n            // Read a bit\n            bitsRead++;\n            currentByte <<= 1;\n            if (this.bitMatrix.get(j - col, i)) {\n              currentByte |= 1;\n            }\n            // If we've made a whole byte, save it off\n            if (bitsRead === 8) {\n              result[resultOffset++] = /*(byte) */currentByte;\n              bitsRead = 0;\n              currentByte = 0;\n            }\n          }\n        }\n      }\n      readingUp = !readingUp; // readingUp ^= true; // readingUp = !readingUp; // switch directions\n    }\n    if (resultOffset !== version.getTotalCodewords()) {\n      throw new FormatException();\n    }\n    return result;\n  };\n  /**\n   * Revert the mask removal done while reading the code words. The bit matrix should revert to its original state.\n   */\n  BitMatrixParser.prototype.remask = function () {\n    if (this.parsedFormatInfo === null) {\n      return; // We have no format information, and have no data mask\n    }\n    var dataMask = DataMask.values.get(this.parsedFormatInfo.getDataMask());\n    var dimension = this.bitMatrix.getHeight();\n    dataMask.unmaskBitMatrix(this.bitMatrix, dimension);\n  };\n  /**\n   * Prepare the parser for a mirrored operation.\n   * This flag has effect only on the {@link #readFormatInformation()} and the\n   * {@link #readVersion()}. Before proceeding with {@link #readCodewords()} the\n   * {@link #mirror()} method should be called.\n   *\n   * @param mirror Whether to read version and format information mirrored.\n   */\n  BitMatrixParser.prototype.setMirror = function (isMirror) {\n    this.parsedVersion = null;\n    this.parsedFormatInfo = null;\n    this.isMirror = isMirror;\n  };\n  /** Mirror the bit matrix in order to attempt a second reading. */\n  BitMatrixParser.prototype.mirror = function () {\n    var bitMatrix = this.bitMatrix;\n    for (var x = 0, width = bitMatrix.getWidth(); x < width; x++) {\n      for (var y = x + 1, height = bitMatrix.getHeight(); y < height; y++) {\n        if (bitMatrix.get(x, y) !== bitMatrix.get(y, x)) {\n          bitMatrix.flip(y, x);\n          bitMatrix.flip(x, y);\n        }\n      }\n    }\n  };\n  return BitMatrixParser;\n}();\nexport default BitMatrixParser;", "map": {"version": 3, "names": ["Version", "FormatInformation", "DataMask", "FormatException", "BitMatrixParser", "bitMatrix", "dimension", "getHeight", "prototype", "readFormatInformation", "parsedFormatInfo", "undefined", "formatInfoBits1", "i", "copyBit", "j", "formatInfoBits2", "jMin", "decodeFormatInformation", "readVersion", "parsedVersion", "provisionalVersion", "Math", "floor", "getVersionForNumber", "versionBits", "ijMin", "theParsedVersion", "decodeVersionInformation", "getDimensionForVersion", "bit", "is<PERSON><PERSON><PERSON><PERSON>", "get", "readCodewords", "formatInfo", "version", "dataMask", "values", "getDataMask", "unmaskBitMatrix", "functionPattern", "buildFunctionPattern", "readingUp", "result", "Uint8Array", "getTotalCodewords", "resultOffset", "currentByte", "bitsRead", "count", "col", "remask", "setMirror", "mirror", "x", "width", "getWidth", "y", "height", "flip"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/BitMatrixParser.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport Version from './Version';\nimport FormatInformation from './FormatInformation';\nimport DataMask from './DataMask';\nimport FormatException from '../../FormatException';\n/**\n * <AUTHOR>\n */\nvar BitMatrixParser = /** @class */ (function () {\n    /**\n     * @param bitMatrix {@link BitMatrix} to parse\n     * @throws FormatException if dimension is not >= 21 and 1 mod 4\n     */\n    function BitMatrixParser(bitMatrix) {\n        var dimension = bitMatrix.getHeight();\n        if (dimension < 21 || (dimension & 0x03) !== 1) {\n            throw new FormatException();\n        }\n        this.bitMatrix = bitMatrix;\n    }\n    /**\n     * <p>Reads format information from one of its two locations within the QR Code.</p>\n     *\n     * @return {@link FormatInformation} encapsulating the QR Code's format info\n     * @throws FormatException if both format information locations cannot be parsed as\n     * the valid encoding of format information\n     */\n    BitMatrixParser.prototype.readFormatInformation = function () {\n        if (this.parsedFormatInfo !== null && this.parsedFormatInfo !== undefined) {\n            return this.parsedFormatInfo;\n        }\n        // Read top-left format info bits\n        var formatInfoBits1 = 0;\n        for (var i = 0; i < 6; i++) {\n            formatInfoBits1 = this.copyBit(i, 8, formatInfoBits1);\n        }\n        // .. and skip a bit in the timing pattern ...\n        formatInfoBits1 = this.copyBit(7, 8, formatInfoBits1);\n        formatInfoBits1 = this.copyBit(8, 8, formatInfoBits1);\n        formatInfoBits1 = this.copyBit(8, 7, formatInfoBits1);\n        // .. and skip a bit in the timing pattern ...\n        for (var j = 5; j >= 0; j--) {\n            formatInfoBits1 = this.copyBit(8, j, formatInfoBits1);\n        }\n        // Read the top-right/bottom-left pattern too\n        var dimension = this.bitMatrix.getHeight();\n        var formatInfoBits2 = 0;\n        var jMin = dimension - 7;\n        for (var j = dimension - 1; j >= jMin; j--) {\n            formatInfoBits2 = this.copyBit(8, j, formatInfoBits2);\n        }\n        for (var i = dimension - 8; i < dimension; i++) {\n            formatInfoBits2 = this.copyBit(i, 8, formatInfoBits2);\n        }\n        this.parsedFormatInfo = FormatInformation.decodeFormatInformation(formatInfoBits1, formatInfoBits2);\n        if (this.parsedFormatInfo !== null) {\n            return this.parsedFormatInfo;\n        }\n        throw new FormatException();\n    };\n    /**\n     * <p>Reads version information from one of its two locations within the QR Code.</p>\n     *\n     * @return {@link Version} encapsulating the QR Code's version\n     * @throws FormatException if both version information locations cannot be parsed as\n     * the valid encoding of version information\n     */\n    BitMatrixParser.prototype.readVersion = function () {\n        if (this.parsedVersion !== null && this.parsedVersion !== undefined) {\n            return this.parsedVersion;\n        }\n        var dimension = this.bitMatrix.getHeight();\n        var provisionalVersion = Math.floor((dimension - 17) / 4);\n        if (provisionalVersion <= 6) {\n            return Version.getVersionForNumber(provisionalVersion);\n        }\n        // Read top-right version info: 3 wide by 6 tall\n        var versionBits = 0;\n        var ijMin = dimension - 11;\n        for (var j = 5; j >= 0; j--) {\n            for (var i = dimension - 9; i >= ijMin; i--) {\n                versionBits = this.copyBit(i, j, versionBits);\n            }\n        }\n        var theParsedVersion = Version.decodeVersionInformation(versionBits);\n        if (theParsedVersion !== null && theParsedVersion.getDimensionForVersion() === dimension) {\n            this.parsedVersion = theParsedVersion;\n            return theParsedVersion;\n        }\n        // Hmm, failed. Try bottom left: 6 wide by 3 tall\n        versionBits = 0;\n        for (var i = 5; i >= 0; i--) {\n            for (var j = dimension - 9; j >= ijMin; j--) {\n                versionBits = this.copyBit(i, j, versionBits);\n            }\n        }\n        theParsedVersion = Version.decodeVersionInformation(versionBits);\n        if (theParsedVersion !== null && theParsedVersion.getDimensionForVersion() === dimension) {\n            this.parsedVersion = theParsedVersion;\n            return theParsedVersion;\n        }\n        throw new FormatException();\n    };\n    BitMatrixParser.prototype.copyBit = function (i /*int*/, j /*int*/, versionBits /*int*/) {\n        var bit = this.isMirror ? this.bitMatrix.get(j, i) : this.bitMatrix.get(i, j);\n        return bit ? (versionBits << 1) | 0x1 : versionBits << 1;\n    };\n    /**\n     * <p>Reads the bits in the {@link BitMatrix} representing the finder pattern in the\n     * correct order in order to reconstruct the codewords bytes contained within the\n     * QR Code.</p>\n     *\n     * @return bytes encoded within the QR Code\n     * @throws FormatException if the exact number of bytes expected is not read\n     */\n    BitMatrixParser.prototype.readCodewords = function () {\n        var formatInfo = this.readFormatInformation();\n        var version = this.readVersion();\n        // Get the data mask for the format used in this QR Code. This will exclude\n        // some bits from reading as we wind through the bit matrix.\n        var dataMask = DataMask.values.get(formatInfo.getDataMask());\n        var dimension = this.bitMatrix.getHeight();\n        dataMask.unmaskBitMatrix(this.bitMatrix, dimension);\n        var functionPattern = version.buildFunctionPattern();\n        var readingUp = true;\n        var result = new Uint8Array(version.getTotalCodewords());\n        var resultOffset = 0;\n        var currentByte = 0;\n        var bitsRead = 0;\n        // Read columns in pairs, from right to left\n        for (var j = dimension - 1; j > 0; j -= 2) {\n            if (j === 6) {\n                // Skip whole column with vertical alignment pattern\n                // saves time and makes the other code proceed more cleanly\n                j--;\n            }\n            // Read alternatingly from bottom to top then top to bottom\n            for (var count = 0; count < dimension; count++) {\n                var i = readingUp ? dimension - 1 - count : count;\n                for (var col = 0; col < 2; col++) {\n                    // Ignore bits covered by the function pattern\n                    if (!functionPattern.get(j - col, i)) {\n                        // Read a bit\n                        bitsRead++;\n                        currentByte <<= 1;\n                        if (this.bitMatrix.get(j - col, i)) {\n                            currentByte |= 1;\n                        }\n                        // If we've made a whole byte, save it off\n                        if (bitsRead === 8) {\n                            result[resultOffset++] = /*(byte) */ currentByte;\n                            bitsRead = 0;\n                            currentByte = 0;\n                        }\n                    }\n                }\n            }\n            readingUp = !readingUp; // readingUp ^= true; // readingUp = !readingUp; // switch directions\n        }\n        if (resultOffset !== version.getTotalCodewords()) {\n            throw new FormatException();\n        }\n        return result;\n    };\n    /**\n     * Revert the mask removal done while reading the code words. The bit matrix should revert to its original state.\n     */\n    BitMatrixParser.prototype.remask = function () {\n        if (this.parsedFormatInfo === null) {\n            return; // We have no format information, and have no data mask\n        }\n        var dataMask = DataMask.values.get(this.parsedFormatInfo.getDataMask());\n        var dimension = this.bitMatrix.getHeight();\n        dataMask.unmaskBitMatrix(this.bitMatrix, dimension);\n    };\n    /**\n     * Prepare the parser for a mirrored operation.\n     * This flag has effect only on the {@link #readFormatInformation()} and the\n     * {@link #readVersion()}. Before proceeding with {@link #readCodewords()} the\n     * {@link #mirror()} method should be called.\n     *\n     * @param mirror Whether to read version and format information mirrored.\n     */\n    BitMatrixParser.prototype.setMirror = function (isMirror) {\n        this.parsedVersion = null;\n        this.parsedFormatInfo = null;\n        this.isMirror = isMirror;\n    };\n    /** Mirror the bit matrix in order to attempt a second reading. */\n    BitMatrixParser.prototype.mirror = function () {\n        var bitMatrix = this.bitMatrix;\n        for (var x = 0, width = bitMatrix.getWidth(); x < width; x++) {\n            for (var y = x + 1, height = bitMatrix.getHeight(); y < height; y++) {\n                if (bitMatrix.get(x, y) !== bitMatrix.get(y, x)) {\n                    bitMatrix.flip(y, x);\n                    bitMatrix.flip(x, y);\n                }\n            }\n        }\n    };\n    return BitMatrixParser;\n}());\nexport default BitMatrixParser;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,OAAO,MAAM,WAAW;AAC/B,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,eAAe,MAAM,uBAAuB;AACnD;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C;AACJ;AACA;AACA;EACI,SAASA,eAAeA,CAACC,SAAS,EAAE;IAChC,IAAIC,SAAS,GAAGD,SAAS,CAACE,SAAS,CAAC,CAAC;IACrC,IAAID,SAAS,GAAG,EAAE,IAAI,CAACA,SAAS,GAAG,IAAI,MAAM,CAAC,EAAE;MAC5C,MAAM,IAAIH,eAAe,CAAC,CAAC;IAC/B;IACA,IAAI,CAACE,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,eAAe,CAACI,SAAS,CAACC,qBAAqB,GAAG,YAAY;IAC1D,IAAI,IAAI,CAACC,gBAAgB,KAAK,IAAI,IAAI,IAAI,CAACA,gBAAgB,KAAKC,SAAS,EAAE;MACvE,OAAO,IAAI,CAACD,gBAAgB;IAChC;IACA;IACA,IAAIE,eAAe,GAAG,CAAC;IACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxBD,eAAe,GAAG,IAAI,CAACE,OAAO,CAACD,CAAC,EAAE,CAAC,EAAED,eAAe,CAAC;IACzD;IACA;IACAA,eAAe,GAAG,IAAI,CAACE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEF,eAAe,CAAC;IACrDA,eAAe,GAAG,IAAI,CAACE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEF,eAAe,CAAC;IACrDA,eAAe,GAAG,IAAI,CAACE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAEF,eAAe,CAAC;IACrD;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzBH,eAAe,GAAG,IAAI,CAACE,OAAO,CAAC,CAAC,EAAEC,CAAC,EAAEH,eAAe,CAAC;IACzD;IACA;IACA,IAAIN,SAAS,GAAG,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC,CAAC;IAC1C,IAAIS,eAAe,GAAG,CAAC;IACvB,IAAIC,IAAI,GAAGX,SAAS,GAAG,CAAC;IACxB,KAAK,IAAIS,CAAC,GAAGT,SAAS,GAAG,CAAC,EAAES,CAAC,IAAIE,IAAI,EAAEF,CAAC,EAAE,EAAE;MACxCC,eAAe,GAAG,IAAI,CAACF,OAAO,CAAC,CAAC,EAAEC,CAAC,EAAEC,eAAe,CAAC;IACzD;IACA,KAAK,IAAIH,CAAC,GAAGP,SAAS,GAAG,CAAC,EAAEO,CAAC,GAAGP,SAAS,EAAEO,CAAC,EAAE,EAAE;MAC5CG,eAAe,GAAG,IAAI,CAACF,OAAO,CAACD,CAAC,EAAE,CAAC,EAAEG,eAAe,CAAC;IACzD;IACA,IAAI,CAACN,gBAAgB,GAAGT,iBAAiB,CAACiB,uBAAuB,CAACN,eAAe,EAAEI,eAAe,CAAC;IACnG,IAAI,IAAI,CAACN,gBAAgB,KAAK,IAAI,EAAE;MAChC,OAAO,IAAI,CAACA,gBAAgB;IAChC;IACA,MAAM,IAAIP,eAAe,CAAC,CAAC;EAC/B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,eAAe,CAACI,SAAS,CAACW,WAAW,GAAG,YAAY;IAChD,IAAI,IAAI,CAACC,aAAa,KAAK,IAAI,IAAI,IAAI,CAACA,aAAa,KAAKT,SAAS,EAAE;MACjE,OAAO,IAAI,CAACS,aAAa;IAC7B;IACA,IAAId,SAAS,GAAG,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC,CAAC;IAC1C,IAAIc,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACjB,SAAS,GAAG,EAAE,IAAI,CAAC,CAAC;IACzD,IAAIe,kBAAkB,IAAI,CAAC,EAAE;MACzB,OAAOrB,OAAO,CAACwB,mBAAmB,CAACH,kBAAkB,CAAC;IAC1D;IACA;IACA,IAAII,WAAW,GAAG,CAAC;IACnB,IAAIC,KAAK,GAAGpB,SAAS,GAAG,EAAE;IAC1B,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzB,KAAK,IAAIF,CAAC,GAAGP,SAAS,GAAG,CAAC,EAAEO,CAAC,IAAIa,KAAK,EAAEb,CAAC,EAAE,EAAE;QACzCY,WAAW,GAAG,IAAI,CAACX,OAAO,CAACD,CAAC,EAAEE,CAAC,EAAEU,WAAW,CAAC;MACjD;IACJ;IACA,IAAIE,gBAAgB,GAAG3B,OAAO,CAAC4B,wBAAwB,CAACH,WAAW,CAAC;IACpE,IAAIE,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,CAACE,sBAAsB,CAAC,CAAC,KAAKvB,SAAS,EAAE;MACtF,IAAI,CAACc,aAAa,GAAGO,gBAAgB;MACrC,OAAOA,gBAAgB;IAC3B;IACA;IACAF,WAAW,GAAG,CAAC;IACf,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzB,KAAK,IAAIE,CAAC,GAAGT,SAAS,GAAG,CAAC,EAAES,CAAC,IAAIW,KAAK,EAAEX,CAAC,EAAE,EAAE;QACzCU,WAAW,GAAG,IAAI,CAACX,OAAO,CAACD,CAAC,EAAEE,CAAC,EAAEU,WAAW,CAAC;MACjD;IACJ;IACAE,gBAAgB,GAAG3B,OAAO,CAAC4B,wBAAwB,CAACH,WAAW,CAAC;IAChE,IAAIE,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,CAACE,sBAAsB,CAAC,CAAC,KAAKvB,SAAS,EAAE;MACtF,IAAI,CAACc,aAAa,GAAGO,gBAAgB;MACrC,OAAOA,gBAAgB;IAC3B;IACA,MAAM,IAAIxB,eAAe,CAAC,CAAC;EAC/B,CAAC;EACDC,eAAe,CAACI,SAAS,CAACM,OAAO,GAAG,UAAUD,CAAC,CAAC,SAASE,CAAC,CAAC,SAASU,WAAW,CAAC,SAAS;IACrF,IAAIK,GAAG,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAI,CAAC1B,SAAS,CAAC2B,GAAG,CAACjB,CAAC,EAAEF,CAAC,CAAC,GAAG,IAAI,CAACR,SAAS,CAAC2B,GAAG,CAACnB,CAAC,EAAEE,CAAC,CAAC;IAC7E,OAAOe,GAAG,GAAIL,WAAW,IAAI,CAAC,GAAI,GAAG,GAAGA,WAAW,IAAI,CAAC;EAC5D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIrB,eAAe,CAACI,SAAS,CAACyB,aAAa,GAAG,YAAY;IAClD,IAAIC,UAAU,GAAG,IAAI,CAACzB,qBAAqB,CAAC,CAAC;IAC7C,IAAI0B,OAAO,GAAG,IAAI,CAAChB,WAAW,CAAC,CAAC;IAChC;IACA;IACA,IAAIiB,QAAQ,GAAGlC,QAAQ,CAACmC,MAAM,CAACL,GAAG,CAACE,UAAU,CAACI,WAAW,CAAC,CAAC,CAAC;IAC5D,IAAIhC,SAAS,GAAG,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC,CAAC;IAC1C6B,QAAQ,CAACG,eAAe,CAAC,IAAI,CAAClC,SAAS,EAAEC,SAAS,CAAC;IACnD,IAAIkC,eAAe,GAAGL,OAAO,CAACM,oBAAoB,CAAC,CAAC;IACpD,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,MAAM,GAAG,IAAIC,UAAU,CAACT,OAAO,CAACU,iBAAiB,CAAC,CAAC,CAAC;IACxD,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,QAAQ,GAAG,CAAC;IAChB;IACA,KAAK,IAAIjC,CAAC,GAAGT,SAAS,GAAG,CAAC,EAAES,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACvC,IAAIA,CAAC,KAAK,CAAC,EAAE;QACT;QACA;QACAA,CAAC,EAAE;MACP;MACA;MACA,KAAK,IAAIkC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG3C,SAAS,EAAE2C,KAAK,EAAE,EAAE;QAC5C,IAAIpC,CAAC,GAAG6B,SAAS,GAAGpC,SAAS,GAAG,CAAC,GAAG2C,KAAK,GAAGA,KAAK;QACjD,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;UAC9B;UACA,IAAI,CAACV,eAAe,CAACR,GAAG,CAACjB,CAAC,GAAGmC,GAAG,EAAErC,CAAC,CAAC,EAAE;YAClC;YACAmC,QAAQ,EAAE;YACVD,WAAW,KAAK,CAAC;YACjB,IAAI,IAAI,CAAC1C,SAAS,CAAC2B,GAAG,CAACjB,CAAC,GAAGmC,GAAG,EAAErC,CAAC,CAAC,EAAE;cAChCkC,WAAW,IAAI,CAAC;YACpB;YACA;YACA,IAAIC,QAAQ,KAAK,CAAC,EAAE;cAChBL,MAAM,CAACG,YAAY,EAAE,CAAC,GAAG,WAAYC,WAAW;cAChDC,QAAQ,GAAG,CAAC;cACZD,WAAW,GAAG,CAAC;YACnB;UACJ;QACJ;MACJ;MACAL,SAAS,GAAG,CAACA,SAAS,CAAC,CAAC;IAC5B;IACA,IAAII,YAAY,KAAKX,OAAO,CAACU,iBAAiB,CAAC,CAAC,EAAE;MAC9C,MAAM,IAAI1C,eAAe,CAAC,CAAC;IAC/B;IACA,OAAOwC,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACIvC,eAAe,CAACI,SAAS,CAAC2C,MAAM,GAAG,YAAY;IAC3C,IAAI,IAAI,CAACzC,gBAAgB,KAAK,IAAI,EAAE;MAChC,OAAO,CAAC;IACZ;IACA,IAAI0B,QAAQ,GAAGlC,QAAQ,CAACmC,MAAM,CAACL,GAAG,CAAC,IAAI,CAACtB,gBAAgB,CAAC4B,WAAW,CAAC,CAAC,CAAC;IACvE,IAAIhC,SAAS,GAAG,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC,CAAC;IAC1C6B,QAAQ,CAACG,eAAe,CAAC,IAAI,CAAClC,SAAS,EAAEC,SAAS,CAAC;EACvD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,eAAe,CAACI,SAAS,CAAC4C,SAAS,GAAG,UAAUrB,QAAQ,EAAE;IACtD,IAAI,CAACX,aAAa,GAAG,IAAI;IACzB,IAAI,CAACV,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACqB,QAAQ,GAAGA,QAAQ;EAC5B,CAAC;EACD;EACA3B,eAAe,CAACI,SAAS,CAAC6C,MAAM,GAAG,YAAY;IAC3C,IAAIhD,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEC,KAAK,GAAGlD,SAAS,CAACmD,QAAQ,CAAC,CAAC,EAAEF,CAAC,GAAGC,KAAK,EAAED,CAAC,EAAE,EAAE;MAC1D,KAAK,IAAIG,CAAC,GAAGH,CAAC,GAAG,CAAC,EAAEI,MAAM,GAAGrD,SAAS,CAACE,SAAS,CAAC,CAAC,EAAEkD,CAAC,GAAGC,MAAM,EAAED,CAAC,EAAE,EAAE;QACjE,IAAIpD,SAAS,CAAC2B,GAAG,CAACsB,CAAC,EAAEG,CAAC,CAAC,KAAKpD,SAAS,CAAC2B,GAAG,CAACyB,CAAC,EAAEH,CAAC,CAAC,EAAE;UAC7CjD,SAAS,CAACsD,IAAI,CAACF,CAAC,EAAEH,CAAC,CAAC;UACpBjD,SAAS,CAACsD,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC;QACxB;MACJ;IACJ;EACJ,CAAC;EACD,OAAOrD,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}