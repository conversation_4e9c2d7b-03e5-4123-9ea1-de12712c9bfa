{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport AI01decoder from './AI01decoder';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI01AndOtherAIs = /** @class */function (_super) {\n  __extends(AI01AndOtherAIs, _super);\n  // the second one is the encodation method, and the other two are for the variable length\n  function AI01AndOtherAIs(information) {\n    return _super.call(this, information) || this;\n  }\n  AI01AndOtherAIs.prototype.parseInformation = function () {\n    var buff = new StringBuilder();\n    buff.append('(01)');\n    var initialGtinPosition = buff.length();\n    var firstGtinDigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01AndOtherAIs.HEADER_SIZE, 4);\n    buff.append(firstGtinDigit);\n    this.encodeCompressedGtinWithoutAI(buff, AI01AndOtherAIs.HEADER_SIZE + 4, initialGtinPosition);\n    return this.getGeneralDecoder().decodeAllCodes(buff, AI01AndOtherAIs.HEADER_SIZE + 44);\n  };\n  AI01AndOtherAIs.HEADER_SIZE = 1 + 1 + 2; // first bit encodes the linkage flag,\n  return AI01AndOtherAIs;\n}(AI01decoder);\nexport default AI01AndOtherAIs;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "AI01decoder", "StringBuilder", "AI01AndOtherAIs", "_super", "information", "call", "parseInformation", "buff", "append", "initialGtinPosition", "length", "firstGtinDigit", "getGeneralDecoder", "extractNumericValueFromBitArray", "HEADER_SIZE", "encodeCompressedGtinWithoutAI", "decodeAllCodes"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01AndOtherAIs.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01decoder from './AI01decoder';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI01AndOtherAIs = /** @class */ (function (_super) {\n    __extends(AI01AndOtherAIs, _super);\n    // the second one is the encodation method, and the other two are for the variable length\n    function AI01AndOtherAIs(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01AndOtherAIs.prototype.parseInformation = function () {\n        var buff = new StringBuilder();\n        buff.append('(01)');\n        var initialGtinPosition = buff.length();\n        var firstGtinDigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01AndOtherAIs.HEADER_SIZE, 4);\n        buff.append(firstGtinDigit);\n        this.encodeCompressedGtinWithoutAI(buff, AI01AndOtherAIs.HEADER_SIZE + 4, initialGtinPosition);\n        return this.getGeneralDecoder().decodeAllCodes(buff, AI01AndOtherAIs.HEADER_SIZE + 44);\n    };\n    AI01AndOtherAIs.HEADER_SIZE = 1 + 1 + 2; // first bit encodes the linkage flag,\n    return AI01AndOtherAIs;\n}(AI01decoder));\nexport default AI01AndOtherAIs;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,WAAW,MAAM,eAAe;AACvC,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,IAAIC,eAAe,GAAG,aAAe,UAAUC,MAAM,EAAE;EACnDjB,SAAS,CAACgB,eAAe,EAAEC,MAAM,CAAC;EAClC;EACA,SAASD,eAAeA,CAACE,WAAW,EAAE;IAClC,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,WAAW,CAAC,IAAI,IAAI;EACjD;EACAF,eAAe,CAACJ,SAAS,CAACQ,gBAAgB,GAAG,YAAY;IACrD,IAAIC,IAAI,GAAG,IAAIN,aAAa,CAAC,CAAC;IAC9BM,IAAI,CAACC,MAAM,CAAC,MAAM,CAAC;IACnB,IAAIC,mBAAmB,GAAGF,IAAI,CAACG,MAAM,CAAC,CAAC;IACvC,IAAIC,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAACC,+BAA+B,CAACX,eAAe,CAACY,WAAW,EAAE,CAAC,CAAC;IAC7GP,IAAI,CAACC,MAAM,CAACG,cAAc,CAAC;IAC3B,IAAI,CAACI,6BAA6B,CAACR,IAAI,EAAEL,eAAe,CAACY,WAAW,GAAG,CAAC,EAAEL,mBAAmB,CAAC;IAC9F,OAAO,IAAI,CAACG,iBAAiB,CAAC,CAAC,CAACI,cAAc,CAACT,IAAI,EAAEL,eAAe,CAACY,WAAW,GAAG,EAAE,CAAC;EAC1F,CAAC;EACDZ,eAAe,CAACY,WAAW,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EACzC,OAAOZ,eAAe;AAC1B,CAAC,CAACF,WAAW,CAAE;AACf,eAAeE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}