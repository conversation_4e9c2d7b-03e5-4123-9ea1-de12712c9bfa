{"ast": null, "code": "import WhiteRectangleDetector from '../../common/detector/WhiteRectangleDetector';\nimport DetectorResult from '../../common/DetectorResult';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport NotFoundException from '../../NotFoundException';\nimport ResultPoint from '../../ResultPoint';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates logic that can detect a Data Matrix Code in an image, even if the Data Matrix Code\n * is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR>\n */\nvar Detector = /** @class */function () {\n  function Detector(image) {\n    this.image = image;\n    this.rectangleDetector = new WhiteRectangleDetector(this.image);\n  }\n  /**\n   * <p>Detects a Data Matrix Code in an image.</p>\n   *\n   * @return {@link DetectorResult} encapsulating results of detecting a Data Matrix Code\n   * @throws NotFoundException if no Data Matrix Code can be found\n   */\n  Detector.prototype.detect = function () {\n    var cornerPoints = this.rectangleDetector.detect();\n    var points = this.detectSolid1(cornerPoints);\n    points = this.detectSolid2(points);\n    points[3] = this.correctTopRight(points);\n    if (!points[3]) {\n      throw new NotFoundException();\n    }\n    points = this.shiftToModuleCenter(points);\n    var topLeft = points[0];\n    var bottomLeft = points[1];\n    var bottomRight = points[2];\n    var topRight = points[3];\n    var dimensionTop = this.transitionsBetween(topLeft, topRight) + 1;\n    var dimensionRight = this.transitionsBetween(bottomRight, topRight) + 1;\n    if ((dimensionTop & 0x01) === 1) {\n      dimensionTop += 1;\n    }\n    if ((dimensionRight & 0x01) === 1) {\n      dimensionRight += 1;\n    }\n    if (4 * dimensionTop < 7 * dimensionRight && 4 * dimensionRight < 7 * dimensionTop) {\n      // The matrix is square\n      dimensionTop = dimensionRight = Math.max(dimensionTop, dimensionRight);\n    }\n    var bits = Detector.sampleGrid(this.image, topLeft, bottomLeft, bottomRight, topRight, dimensionTop, dimensionRight);\n    return new DetectorResult(bits, [topLeft, bottomLeft, bottomRight, topRight]);\n  };\n  Detector.shiftPoint = function (point, to, div) {\n    var x = (to.getX() - point.getX()) / (div + 1);\n    var y = (to.getY() - point.getY()) / (div + 1);\n    return new ResultPoint(point.getX() + x, point.getY() + y);\n  };\n  Detector.moveAway = function (point, fromX, fromY) {\n    var x = point.getX();\n    var y = point.getY();\n    if (x < fromX) {\n      x -= 1;\n    } else {\n      x += 1;\n    }\n    if (y < fromY) {\n      y -= 1;\n    } else {\n      y += 1;\n    }\n    return new ResultPoint(x, y);\n  };\n  /**\n   * Detect a solid side which has minimum transition.\n   */\n  Detector.prototype.detectSolid1 = function (cornerPoints) {\n    // 0  2\n    // 1  3\n    var pointA = cornerPoints[0];\n    var pointB = cornerPoints[1];\n    var pointC = cornerPoints[3];\n    var pointD = cornerPoints[2];\n    var trAB = this.transitionsBetween(pointA, pointB);\n    var trBC = this.transitionsBetween(pointB, pointC);\n    var trCD = this.transitionsBetween(pointC, pointD);\n    var trDA = this.transitionsBetween(pointD, pointA);\n    // 0..3\n    // :  :\n    // 1--2\n    var min = trAB;\n    var points = [pointD, pointA, pointB, pointC];\n    if (min > trBC) {\n      min = trBC;\n      points[0] = pointA;\n      points[1] = pointB;\n      points[2] = pointC;\n      points[3] = pointD;\n    }\n    if (min > trCD) {\n      min = trCD;\n      points[0] = pointB;\n      points[1] = pointC;\n      points[2] = pointD;\n      points[3] = pointA;\n    }\n    if (min > trDA) {\n      points[0] = pointC;\n      points[1] = pointD;\n      points[2] = pointA;\n      points[3] = pointB;\n    }\n    return points;\n  };\n  /**\n   * Detect a second solid side next to first solid side.\n   */\n  Detector.prototype.detectSolid2 = function (points) {\n    // A..D\n    // :  :\n    // B--C\n    var pointA = points[0];\n    var pointB = points[1];\n    var pointC = points[2];\n    var pointD = points[3];\n    // Transition detection on the edge is not stable.\n    // To safely detect, shift the points to the module center.\n    var tr = this.transitionsBetween(pointA, pointD);\n    var pointBs = Detector.shiftPoint(pointB, pointC, (tr + 1) * 4);\n    var pointCs = Detector.shiftPoint(pointC, pointB, (tr + 1) * 4);\n    var trBA = this.transitionsBetween(pointBs, pointA);\n    var trCD = this.transitionsBetween(pointCs, pointD);\n    // 0..3\n    // |  :\n    // 1--2\n    if (trBA < trCD) {\n      // solid sides: A-B-C\n      points[0] = pointA;\n      points[1] = pointB;\n      points[2] = pointC;\n      points[3] = pointD;\n    } else {\n      // solid sides: B-C-D\n      points[0] = pointB;\n      points[1] = pointC;\n      points[2] = pointD;\n      points[3] = pointA;\n    }\n    return points;\n  };\n  /**\n   * Calculates the corner position of the white top right module.\n   */\n  Detector.prototype.correctTopRight = function (points) {\n    // A..D\n    // |  :\n    // B--C\n    var pointA = points[0];\n    var pointB = points[1];\n    var pointC = points[2];\n    var pointD = points[3];\n    // shift points for safe transition detection.\n    var trTop = this.transitionsBetween(pointA, pointD);\n    var trRight = this.transitionsBetween(pointB, pointD);\n    var pointAs = Detector.shiftPoint(pointA, pointB, (trRight + 1) * 4);\n    var pointCs = Detector.shiftPoint(pointC, pointB, (trTop + 1) * 4);\n    trTop = this.transitionsBetween(pointAs, pointD);\n    trRight = this.transitionsBetween(pointCs, pointD);\n    var candidate1 = new ResultPoint(pointD.getX() + (pointC.getX() - pointB.getX()) / (trTop + 1), pointD.getY() + (pointC.getY() - pointB.getY()) / (trTop + 1));\n    var candidate2 = new ResultPoint(pointD.getX() + (pointA.getX() - pointB.getX()) / (trRight + 1), pointD.getY() + (pointA.getY() - pointB.getY()) / (trRight + 1));\n    if (!this.isValid(candidate1)) {\n      if (this.isValid(candidate2)) {\n        return candidate2;\n      }\n      return null;\n    }\n    if (!this.isValid(candidate2)) {\n      return candidate1;\n    }\n    var sumc1 = this.transitionsBetween(pointAs, candidate1) + this.transitionsBetween(pointCs, candidate1);\n    var sumc2 = this.transitionsBetween(pointAs, candidate2) + this.transitionsBetween(pointCs, candidate2);\n    if (sumc1 > sumc2) {\n      return candidate1;\n    } else {\n      return candidate2;\n    }\n  };\n  /**\n   * Shift the edge points to the module center.\n   */\n  Detector.prototype.shiftToModuleCenter = function (points) {\n    // A..D\n    // |  :\n    // B--C\n    var pointA = points[0];\n    var pointB = points[1];\n    var pointC = points[2];\n    var pointD = points[3];\n    // calculate pseudo dimensions\n    var dimH = this.transitionsBetween(pointA, pointD) + 1;\n    var dimV = this.transitionsBetween(pointC, pointD) + 1;\n    // shift points for safe dimension detection\n    var pointAs = Detector.shiftPoint(pointA, pointB, dimV * 4);\n    var pointCs = Detector.shiftPoint(pointC, pointB, dimH * 4);\n    //  calculate more precise dimensions\n    dimH = this.transitionsBetween(pointAs, pointD) + 1;\n    dimV = this.transitionsBetween(pointCs, pointD) + 1;\n    if ((dimH & 0x01) === 1) {\n      dimH += 1;\n    }\n    if ((dimV & 0x01) === 1) {\n      dimV += 1;\n    }\n    // WhiteRectangleDetector returns points inside of the rectangle.\n    // I want points on the edges.\n    var centerX = (pointA.getX() + pointB.getX() + pointC.getX() + pointD.getX()) / 4;\n    var centerY = (pointA.getY() + pointB.getY() + pointC.getY() + pointD.getY()) / 4;\n    pointA = Detector.moveAway(pointA, centerX, centerY);\n    pointB = Detector.moveAway(pointB, centerX, centerY);\n    pointC = Detector.moveAway(pointC, centerX, centerY);\n    pointD = Detector.moveAway(pointD, centerX, centerY);\n    var pointBs;\n    var pointDs;\n    // shift points to the center of each modules\n    pointAs = Detector.shiftPoint(pointA, pointB, dimV * 4);\n    pointAs = Detector.shiftPoint(pointAs, pointD, dimH * 4);\n    pointBs = Detector.shiftPoint(pointB, pointA, dimV * 4);\n    pointBs = Detector.shiftPoint(pointBs, pointC, dimH * 4);\n    pointCs = Detector.shiftPoint(pointC, pointD, dimV * 4);\n    pointCs = Detector.shiftPoint(pointCs, pointB, dimH * 4);\n    pointDs = Detector.shiftPoint(pointD, pointC, dimV * 4);\n    pointDs = Detector.shiftPoint(pointDs, pointA, dimH * 4);\n    return [pointAs, pointBs, pointCs, pointDs];\n  };\n  Detector.prototype.isValid = function (p) {\n    return p.getX() >= 0 && p.getX() < this.image.getWidth() && p.getY() > 0 && p.getY() < this.image.getHeight();\n  };\n  Detector.sampleGrid = function (image, topLeft, bottomLeft, bottomRight, topRight, dimensionX, dimensionY) {\n    var sampler = GridSamplerInstance.getInstance();\n    return sampler.sampleGrid(image, dimensionX, dimensionY, 0.5, 0.5, dimensionX - 0.5, 0.5, dimensionX - 0.5, dimensionY - 0.5, 0.5, dimensionY - 0.5, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRight.getX(), bottomRight.getY(), bottomLeft.getX(), bottomLeft.getY());\n  };\n  /**\n   * Counts the number of black/white transitions between two points, using something like Bresenham's algorithm.\n   */\n  Detector.prototype.transitionsBetween = function (from, to) {\n    // See QR Code Detector, sizeOfBlackWhiteBlackRun()\n    var fromX = Math.trunc(from.getX());\n    var fromY = Math.trunc(from.getY());\n    var toX = Math.trunc(to.getX());\n    var toY = Math.trunc(to.getY());\n    var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);\n    if (steep) {\n      var temp = fromX;\n      fromX = fromY;\n      fromY = temp;\n      temp = toX;\n      toX = toY;\n      toY = temp;\n    }\n    var dx = Math.abs(toX - fromX);\n    var dy = Math.abs(toY - fromY);\n    var error = -dx / 2;\n    var ystep = fromY < toY ? 1 : -1;\n    var xstep = fromX < toX ? 1 : -1;\n    var transitions = 0;\n    var inBlack = this.image.get(steep ? fromY : fromX, steep ? fromX : fromY);\n    for (var x = fromX, y = fromY; x !== toX; x += xstep) {\n      var isBlack = this.image.get(steep ? y : x, steep ? x : y);\n      if (isBlack !== inBlack) {\n        transitions++;\n        inBlack = isBlack;\n      }\n      error += dy;\n      if (error > 0) {\n        if (y === toY) {\n          break;\n        }\n        y += ystep;\n        error -= dx;\n      }\n    }\n    return transitions;\n  };\n  return Detector;\n}();\nexport default Detector;", "map": {"version": 3, "names": ["WhiteRectangleDetector", "DetectorResult", "GridSamplerInstance", "NotFoundException", "ResultPoint", "Detector", "image", "rectangleDetector", "prototype", "detect", "cornerPoints", "points", "detectSolid1", "detectSolid2", "correctTopRight", "shiftToModuleCenter", "topLeft", "bottomLeft", "bottomRight", "topRight", "dimensionTop", "transitionsBetween", "dimensionRight", "Math", "max", "bits", "sampleGrid", "shiftPoint", "point", "to", "div", "x", "getX", "y", "getY", "moveAway", "fromX", "fromY", "pointA", "pointB", "pointC", "pointD", "trAB", "trBC", "trCD", "trDA", "min", "tr", "pointBs", "pointCs", "trBA", "trTop", "trRight", "pointAs", "candidate1", "candidate2", "<PERSON><PERSON><PERSON><PERSON>", "sumc1", "sumc2", "dimH", "dimV", "centerX", "centerY", "pointDs", "p", "getWidth", "getHeight", "dimensionX", "dimensionY", "sampler", "getInstance", "from", "trunc", "toX", "toY", "steep", "abs", "temp", "dx", "dy", "error", "ystep", "xstep", "transitions", "inBlack", "get", "isBlack"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/detector/Detector.js"], "sourcesContent": ["import WhiteRectangleDetector from '../../common/detector/WhiteRectangleDetector';\nimport DetectorResult from '../../common/DetectorResult';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport NotFoundException from '../../NotFoundException';\nimport ResultPoint from '../../ResultPoint';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates logic that can detect a Data Matrix Code in an image, even if the Data Matrix Code\n * is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR>\n */\nvar Detector = /** @class */ (function () {\n    function Detector(image) {\n        this.image = image;\n        this.rectangleDetector = new WhiteRectangleDetector(this.image);\n    }\n    /**\n     * <p>Detects a Data Matrix Code in an image.</p>\n     *\n     * @return {@link DetectorResult} encapsulating results of detecting a Data Matrix Code\n     * @throws NotFoundException if no Data Matrix Code can be found\n     */\n    Detector.prototype.detect = function () {\n        var cornerPoints = this.rectangleDetector.detect();\n        var points = this.detectSolid1(cornerPoints);\n        points = this.detectSolid2(points);\n        points[3] = this.correctTopRight(points);\n        if (!points[3]) {\n            throw new NotFoundException();\n        }\n        points = this.shiftToModuleCenter(points);\n        var topLeft = points[0];\n        var bottomLeft = points[1];\n        var bottomRight = points[2];\n        var topRight = points[3];\n        var dimensionTop = this.transitionsBetween(topLeft, topRight) + 1;\n        var dimensionRight = this.transitionsBetween(bottomRight, topRight) + 1;\n        if ((dimensionTop & 0x01) === 1) {\n            dimensionTop += 1;\n        }\n        if ((dimensionRight & 0x01) === 1) {\n            dimensionRight += 1;\n        }\n        if (4 * dimensionTop < 7 * dimensionRight && 4 * dimensionRight < 7 * dimensionTop) {\n            // The matrix is square\n            dimensionTop = dimensionRight = Math.max(dimensionTop, dimensionRight);\n        }\n        var bits = Detector.sampleGrid(this.image, topLeft, bottomLeft, bottomRight, topRight, dimensionTop, dimensionRight);\n        return new DetectorResult(bits, [topLeft, bottomLeft, bottomRight, topRight]);\n    };\n    Detector.shiftPoint = function (point, to, div) {\n        var x = (to.getX() - point.getX()) / (div + 1);\n        var y = (to.getY() - point.getY()) / (div + 1);\n        return new ResultPoint(point.getX() + x, point.getY() + y);\n    };\n    Detector.moveAway = function (point, fromX, fromY) {\n        var x = point.getX();\n        var y = point.getY();\n        if (x < fromX) {\n            x -= 1;\n        }\n        else {\n            x += 1;\n        }\n        if (y < fromY) {\n            y -= 1;\n        }\n        else {\n            y += 1;\n        }\n        return new ResultPoint(x, y);\n    };\n    /**\n     * Detect a solid side which has minimum transition.\n     */\n    Detector.prototype.detectSolid1 = function (cornerPoints) {\n        // 0  2\n        // 1  3\n        var pointA = cornerPoints[0];\n        var pointB = cornerPoints[1];\n        var pointC = cornerPoints[3];\n        var pointD = cornerPoints[2];\n        var trAB = this.transitionsBetween(pointA, pointB);\n        var trBC = this.transitionsBetween(pointB, pointC);\n        var trCD = this.transitionsBetween(pointC, pointD);\n        var trDA = this.transitionsBetween(pointD, pointA);\n        // 0..3\n        // :  :\n        // 1--2\n        var min = trAB;\n        var points = [pointD, pointA, pointB, pointC];\n        if (min > trBC) {\n            min = trBC;\n            points[0] = pointA;\n            points[1] = pointB;\n            points[2] = pointC;\n            points[3] = pointD;\n        }\n        if (min > trCD) {\n            min = trCD;\n            points[0] = pointB;\n            points[1] = pointC;\n            points[2] = pointD;\n            points[3] = pointA;\n        }\n        if (min > trDA) {\n            points[0] = pointC;\n            points[1] = pointD;\n            points[2] = pointA;\n            points[3] = pointB;\n        }\n        return points;\n    };\n    /**\n     * Detect a second solid side next to first solid side.\n     */\n    Detector.prototype.detectSolid2 = function (points) {\n        // A..D\n        // :  :\n        // B--C\n        var pointA = points[0];\n        var pointB = points[1];\n        var pointC = points[2];\n        var pointD = points[3];\n        // Transition detection on the edge is not stable.\n        // To safely detect, shift the points to the module center.\n        var tr = this.transitionsBetween(pointA, pointD);\n        var pointBs = Detector.shiftPoint(pointB, pointC, (tr + 1) * 4);\n        var pointCs = Detector.shiftPoint(pointC, pointB, (tr + 1) * 4);\n        var trBA = this.transitionsBetween(pointBs, pointA);\n        var trCD = this.transitionsBetween(pointCs, pointD);\n        // 0..3\n        // |  :\n        // 1--2\n        if (trBA < trCD) {\n            // solid sides: A-B-C\n            points[0] = pointA;\n            points[1] = pointB;\n            points[2] = pointC;\n            points[3] = pointD;\n        }\n        else {\n            // solid sides: B-C-D\n            points[0] = pointB;\n            points[1] = pointC;\n            points[2] = pointD;\n            points[3] = pointA;\n        }\n        return points;\n    };\n    /**\n     * Calculates the corner position of the white top right module.\n     */\n    Detector.prototype.correctTopRight = function (points) {\n        // A..D\n        // |  :\n        // B--C\n        var pointA = points[0];\n        var pointB = points[1];\n        var pointC = points[2];\n        var pointD = points[3];\n        // shift points for safe transition detection.\n        var trTop = this.transitionsBetween(pointA, pointD);\n        var trRight = this.transitionsBetween(pointB, pointD);\n        var pointAs = Detector.shiftPoint(pointA, pointB, (trRight + 1) * 4);\n        var pointCs = Detector.shiftPoint(pointC, pointB, (trTop + 1) * 4);\n        trTop = this.transitionsBetween(pointAs, pointD);\n        trRight = this.transitionsBetween(pointCs, pointD);\n        var candidate1 = new ResultPoint(pointD.getX() + (pointC.getX() - pointB.getX()) / (trTop + 1), pointD.getY() + (pointC.getY() - pointB.getY()) / (trTop + 1));\n        var candidate2 = new ResultPoint(pointD.getX() + (pointA.getX() - pointB.getX()) / (trRight + 1), pointD.getY() + (pointA.getY() - pointB.getY()) / (trRight + 1));\n        if (!this.isValid(candidate1)) {\n            if (this.isValid(candidate2)) {\n                return candidate2;\n            }\n            return null;\n        }\n        if (!this.isValid(candidate2)) {\n            return candidate1;\n        }\n        var sumc1 = this.transitionsBetween(pointAs, candidate1) + this.transitionsBetween(pointCs, candidate1);\n        var sumc2 = this.transitionsBetween(pointAs, candidate2) + this.transitionsBetween(pointCs, candidate2);\n        if (sumc1 > sumc2) {\n            return candidate1;\n        }\n        else {\n            return candidate2;\n        }\n    };\n    /**\n     * Shift the edge points to the module center.\n     */\n    Detector.prototype.shiftToModuleCenter = function (points) {\n        // A..D\n        // |  :\n        // B--C\n        var pointA = points[0];\n        var pointB = points[1];\n        var pointC = points[2];\n        var pointD = points[3];\n        // calculate pseudo dimensions\n        var dimH = this.transitionsBetween(pointA, pointD) + 1;\n        var dimV = this.transitionsBetween(pointC, pointD) + 1;\n        // shift points for safe dimension detection\n        var pointAs = Detector.shiftPoint(pointA, pointB, dimV * 4);\n        var pointCs = Detector.shiftPoint(pointC, pointB, dimH * 4);\n        //  calculate more precise dimensions\n        dimH = this.transitionsBetween(pointAs, pointD) + 1;\n        dimV = this.transitionsBetween(pointCs, pointD) + 1;\n        if ((dimH & 0x01) === 1) {\n            dimH += 1;\n        }\n        if ((dimV & 0x01) === 1) {\n            dimV += 1;\n        }\n        // WhiteRectangleDetector returns points inside of the rectangle.\n        // I want points on the edges.\n        var centerX = (pointA.getX() + pointB.getX() + pointC.getX() + pointD.getX()) / 4;\n        var centerY = (pointA.getY() + pointB.getY() + pointC.getY() + pointD.getY()) / 4;\n        pointA = Detector.moveAway(pointA, centerX, centerY);\n        pointB = Detector.moveAway(pointB, centerX, centerY);\n        pointC = Detector.moveAway(pointC, centerX, centerY);\n        pointD = Detector.moveAway(pointD, centerX, centerY);\n        var pointBs;\n        var pointDs;\n        // shift points to the center of each modules\n        pointAs = Detector.shiftPoint(pointA, pointB, dimV * 4);\n        pointAs = Detector.shiftPoint(pointAs, pointD, dimH * 4);\n        pointBs = Detector.shiftPoint(pointB, pointA, dimV * 4);\n        pointBs = Detector.shiftPoint(pointBs, pointC, dimH * 4);\n        pointCs = Detector.shiftPoint(pointC, pointD, dimV * 4);\n        pointCs = Detector.shiftPoint(pointCs, pointB, dimH * 4);\n        pointDs = Detector.shiftPoint(pointD, pointC, dimV * 4);\n        pointDs = Detector.shiftPoint(pointDs, pointA, dimH * 4);\n        return [pointAs, pointBs, pointCs, pointDs];\n    };\n    Detector.prototype.isValid = function (p) {\n        return p.getX() >= 0 && p.getX() < this.image.getWidth() && p.getY() > 0 && p.getY() < this.image.getHeight();\n    };\n    Detector.sampleGrid = function (image, topLeft, bottomLeft, bottomRight, topRight, dimensionX, dimensionY) {\n        var sampler = GridSamplerInstance.getInstance();\n        return sampler.sampleGrid(image, dimensionX, dimensionY, 0.5, 0.5, dimensionX - 0.5, 0.5, dimensionX - 0.5, dimensionY - 0.5, 0.5, dimensionY - 0.5, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRight.getX(), bottomRight.getY(), bottomLeft.getX(), bottomLeft.getY());\n    };\n    /**\n     * Counts the number of black/white transitions between two points, using something like Bresenham's algorithm.\n     */\n    Detector.prototype.transitionsBetween = function (from, to) {\n        // See QR Code Detector, sizeOfBlackWhiteBlackRun()\n        var fromX = Math.trunc(from.getX());\n        var fromY = Math.trunc(from.getY());\n        var toX = Math.trunc(to.getX());\n        var toY = Math.trunc(to.getY());\n        var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);\n        if (steep) {\n            var temp = fromX;\n            fromX = fromY;\n            fromY = temp;\n            temp = toX;\n            toX = toY;\n            toY = temp;\n        }\n        var dx = Math.abs(toX - fromX);\n        var dy = Math.abs(toY - fromY);\n        var error = -dx / 2;\n        var ystep = fromY < toY ? 1 : -1;\n        var xstep = fromX < toX ? 1 : -1;\n        var transitions = 0;\n        var inBlack = this.image.get(steep ? fromY : fromX, steep ? fromX : fromY);\n        for (var x = fromX, y = fromY; x !== toX; x += xstep) {\n            var isBlack = this.image.get(steep ? y : x, steep ? x : y);\n            if (isBlack !== inBlack) {\n                transitions++;\n                inBlack = isBlack;\n            }\n            error += dy;\n            if (error > 0) {\n                if (y === toY) {\n                    break;\n                }\n                y += ystep;\n                error -= dx;\n            }\n        }\n        return transitions;\n    };\n    return Detector;\n}());\nexport default Detector;\n"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,8CAA8C;AACjF,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACC,KAAK,EAAE;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,iBAAiB,GAAG,IAAIP,sBAAsB,CAAC,IAAI,CAACM,KAAK,CAAC;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACID,QAAQ,CAACG,SAAS,CAACC,MAAM,GAAG,YAAY;IACpC,IAAIC,YAAY,GAAG,IAAI,CAACH,iBAAiB,CAACE,MAAM,CAAC,CAAC;IAClD,IAAIE,MAAM,GAAG,IAAI,CAACC,YAAY,CAACF,YAAY,CAAC;IAC5CC,MAAM,GAAG,IAAI,CAACE,YAAY,CAACF,MAAM,CAAC;IAClCA,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAACG,eAAe,CAACH,MAAM,CAAC;IACxC,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,EAAE;MACZ,MAAM,IAAIR,iBAAiB,CAAC,CAAC;IACjC;IACAQ,MAAM,GAAG,IAAI,CAACI,mBAAmB,CAACJ,MAAM,CAAC;IACzC,IAAIK,OAAO,GAAGL,MAAM,CAAC,CAAC,CAAC;IACvB,IAAIM,UAAU,GAAGN,MAAM,CAAC,CAAC,CAAC;IAC1B,IAAIO,WAAW,GAAGP,MAAM,CAAC,CAAC,CAAC;IAC3B,IAAIQ,QAAQ,GAAGR,MAAM,CAAC,CAAC,CAAC;IACxB,IAAIS,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACL,OAAO,EAAEG,QAAQ,CAAC,GAAG,CAAC;IACjE,IAAIG,cAAc,GAAG,IAAI,CAACD,kBAAkB,CAACH,WAAW,EAAEC,QAAQ,CAAC,GAAG,CAAC;IACvE,IAAI,CAACC,YAAY,GAAG,IAAI,MAAM,CAAC,EAAE;MAC7BA,YAAY,IAAI,CAAC;IACrB;IACA,IAAI,CAACE,cAAc,GAAG,IAAI,MAAM,CAAC,EAAE;MAC/BA,cAAc,IAAI,CAAC;IACvB;IACA,IAAI,CAAC,GAAGF,YAAY,GAAG,CAAC,GAAGE,cAAc,IAAI,CAAC,GAAGA,cAAc,GAAG,CAAC,GAAGF,YAAY,EAAE;MAChF;MACAA,YAAY,GAAGE,cAAc,GAAGC,IAAI,CAACC,GAAG,CAACJ,YAAY,EAAEE,cAAc,CAAC;IAC1E;IACA,IAAIG,IAAI,GAAGpB,QAAQ,CAACqB,UAAU,CAAC,IAAI,CAACpB,KAAK,EAAEU,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,YAAY,EAAEE,cAAc,CAAC;IACpH,OAAO,IAAIrB,cAAc,CAACwB,IAAI,EAAE,CAACT,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,QAAQ,CAAC,CAAC;EACjF,CAAC;EACDd,QAAQ,CAACsB,UAAU,GAAG,UAAUC,KAAK,EAAEC,EAAE,EAAEC,GAAG,EAAE;IAC5C,IAAIC,CAAC,GAAG,CAACF,EAAE,CAACG,IAAI,CAAC,CAAC,GAAGJ,KAAK,CAACI,IAAI,CAAC,CAAC,KAAKF,GAAG,GAAG,CAAC,CAAC;IAC9C,IAAIG,CAAC,GAAG,CAACJ,EAAE,CAACK,IAAI,CAAC,CAAC,GAAGN,KAAK,CAACM,IAAI,CAAC,CAAC,KAAKJ,GAAG,GAAG,CAAC,CAAC;IAC9C,OAAO,IAAI1B,WAAW,CAACwB,KAAK,CAACI,IAAI,CAAC,CAAC,GAAGD,CAAC,EAAEH,KAAK,CAACM,IAAI,CAAC,CAAC,GAAGD,CAAC,CAAC;EAC9D,CAAC;EACD5B,QAAQ,CAAC8B,QAAQ,GAAG,UAAUP,KAAK,EAAEQ,KAAK,EAAEC,KAAK,EAAE;IAC/C,IAAIN,CAAC,GAAGH,KAAK,CAACI,IAAI,CAAC,CAAC;IACpB,IAAIC,CAAC,GAAGL,KAAK,CAACM,IAAI,CAAC,CAAC;IACpB,IAAIH,CAAC,GAAGK,KAAK,EAAE;MACXL,CAAC,IAAI,CAAC;IACV,CAAC,MACI;MACDA,CAAC,IAAI,CAAC;IACV;IACA,IAAIE,CAAC,GAAGI,KAAK,EAAE;MACXJ,CAAC,IAAI,CAAC;IACV,CAAC,MACI;MACDA,CAAC,IAAI,CAAC;IACV;IACA,OAAO,IAAI7B,WAAW,CAAC2B,CAAC,EAAEE,CAAC,CAAC;EAChC,CAAC;EACD;AACJ;AACA;EACI5B,QAAQ,CAACG,SAAS,CAACI,YAAY,GAAG,UAAUF,YAAY,EAAE;IACtD;IACA;IACA,IAAI4B,MAAM,GAAG5B,YAAY,CAAC,CAAC,CAAC;IAC5B,IAAI6B,MAAM,GAAG7B,YAAY,CAAC,CAAC,CAAC;IAC5B,IAAI8B,MAAM,GAAG9B,YAAY,CAAC,CAAC,CAAC;IAC5B,IAAI+B,MAAM,GAAG/B,YAAY,CAAC,CAAC,CAAC;IAC5B,IAAIgC,IAAI,GAAG,IAAI,CAACrB,kBAAkB,CAACiB,MAAM,EAAEC,MAAM,CAAC;IAClD,IAAII,IAAI,GAAG,IAAI,CAACtB,kBAAkB,CAACkB,MAAM,EAAEC,MAAM,CAAC;IAClD,IAAII,IAAI,GAAG,IAAI,CAACvB,kBAAkB,CAACmB,MAAM,EAAEC,MAAM,CAAC;IAClD,IAAII,IAAI,GAAG,IAAI,CAACxB,kBAAkB,CAACoB,MAAM,EAAEH,MAAM,CAAC;IAClD;IACA;IACA;IACA,IAAIQ,GAAG,GAAGJ,IAAI;IACd,IAAI/B,MAAM,GAAG,CAAC8B,MAAM,EAAEH,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC;IAC7C,IAAIM,GAAG,GAAGH,IAAI,EAAE;MACZG,GAAG,GAAGH,IAAI;MACVhC,MAAM,CAAC,CAAC,CAAC,GAAG2B,MAAM;MAClB3B,MAAM,CAAC,CAAC,CAAC,GAAG4B,MAAM;MAClB5B,MAAM,CAAC,CAAC,CAAC,GAAG6B,MAAM;MAClB7B,MAAM,CAAC,CAAC,CAAC,GAAG8B,MAAM;IACtB;IACA,IAAIK,GAAG,GAAGF,IAAI,EAAE;MACZE,GAAG,GAAGF,IAAI;MACVjC,MAAM,CAAC,CAAC,CAAC,GAAG4B,MAAM;MAClB5B,MAAM,CAAC,CAAC,CAAC,GAAG6B,MAAM;MAClB7B,MAAM,CAAC,CAAC,CAAC,GAAG8B,MAAM;MAClB9B,MAAM,CAAC,CAAC,CAAC,GAAG2B,MAAM;IACtB;IACA,IAAIQ,GAAG,GAAGD,IAAI,EAAE;MACZlC,MAAM,CAAC,CAAC,CAAC,GAAG6B,MAAM;MAClB7B,MAAM,CAAC,CAAC,CAAC,GAAG8B,MAAM;MAClB9B,MAAM,CAAC,CAAC,CAAC,GAAG2B,MAAM;MAClB3B,MAAM,CAAC,CAAC,CAAC,GAAG4B,MAAM;IACtB;IACA,OAAO5B,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACIN,QAAQ,CAACG,SAAS,CAACK,YAAY,GAAG,UAAUF,MAAM,EAAE;IAChD;IACA;IACA;IACA,IAAI2B,MAAM,GAAG3B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI4B,MAAM,GAAG5B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI6B,MAAM,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI8B,MAAM,GAAG9B,MAAM,CAAC,CAAC,CAAC;IACtB;IACA;IACA,IAAIoC,EAAE,GAAG,IAAI,CAAC1B,kBAAkB,CAACiB,MAAM,EAAEG,MAAM,CAAC;IAChD,IAAIO,OAAO,GAAG3C,QAAQ,CAACsB,UAAU,CAACY,MAAM,EAAEC,MAAM,EAAE,CAACO,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAIE,OAAO,GAAG5C,QAAQ,CAACsB,UAAU,CAACa,MAAM,EAAED,MAAM,EAAE,CAACQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAC/D,IAAIG,IAAI,GAAG,IAAI,CAAC7B,kBAAkB,CAAC2B,OAAO,EAAEV,MAAM,CAAC;IACnD,IAAIM,IAAI,GAAG,IAAI,CAACvB,kBAAkB,CAAC4B,OAAO,EAAER,MAAM,CAAC;IACnD;IACA;IACA;IACA,IAAIS,IAAI,GAAGN,IAAI,EAAE;MACb;MACAjC,MAAM,CAAC,CAAC,CAAC,GAAG2B,MAAM;MAClB3B,MAAM,CAAC,CAAC,CAAC,GAAG4B,MAAM;MAClB5B,MAAM,CAAC,CAAC,CAAC,GAAG6B,MAAM;MAClB7B,MAAM,CAAC,CAAC,CAAC,GAAG8B,MAAM;IACtB,CAAC,MACI;MACD;MACA9B,MAAM,CAAC,CAAC,CAAC,GAAG4B,MAAM;MAClB5B,MAAM,CAAC,CAAC,CAAC,GAAG6B,MAAM;MAClB7B,MAAM,CAAC,CAAC,CAAC,GAAG8B,MAAM;MAClB9B,MAAM,CAAC,CAAC,CAAC,GAAG2B,MAAM;IACtB;IACA,OAAO3B,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACIN,QAAQ,CAACG,SAAS,CAACM,eAAe,GAAG,UAAUH,MAAM,EAAE;IACnD;IACA;IACA;IACA,IAAI2B,MAAM,GAAG3B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI4B,MAAM,GAAG5B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI6B,MAAM,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI8B,MAAM,GAAG9B,MAAM,CAAC,CAAC,CAAC;IACtB;IACA,IAAIwC,KAAK,GAAG,IAAI,CAAC9B,kBAAkB,CAACiB,MAAM,EAAEG,MAAM,CAAC;IACnD,IAAIW,OAAO,GAAG,IAAI,CAAC/B,kBAAkB,CAACkB,MAAM,EAAEE,MAAM,CAAC;IACrD,IAAIY,OAAO,GAAGhD,QAAQ,CAACsB,UAAU,CAACW,MAAM,EAAEC,MAAM,EAAE,CAACa,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC;IACpE,IAAIH,OAAO,GAAG5C,QAAQ,CAACsB,UAAU,CAACa,MAAM,EAAED,MAAM,EAAE,CAACY,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC;IAClEA,KAAK,GAAG,IAAI,CAAC9B,kBAAkB,CAACgC,OAAO,EAAEZ,MAAM,CAAC;IAChDW,OAAO,GAAG,IAAI,CAAC/B,kBAAkB,CAAC4B,OAAO,EAAER,MAAM,CAAC;IAClD,IAAIa,UAAU,GAAG,IAAIlD,WAAW,CAACqC,MAAM,CAACT,IAAI,CAAC,CAAC,GAAG,CAACQ,MAAM,CAACR,IAAI,CAAC,CAAC,GAAGO,MAAM,CAACP,IAAI,CAAC,CAAC,KAAKmB,KAAK,GAAG,CAAC,CAAC,EAAEV,MAAM,CAACP,IAAI,CAAC,CAAC,GAAG,CAACM,MAAM,CAACN,IAAI,CAAC,CAAC,GAAGK,MAAM,CAACL,IAAI,CAAC,CAAC,KAAKiB,KAAK,GAAG,CAAC,CAAC,CAAC;IAC9J,IAAII,UAAU,GAAG,IAAInD,WAAW,CAACqC,MAAM,CAACT,IAAI,CAAC,CAAC,GAAG,CAACM,MAAM,CAACN,IAAI,CAAC,CAAC,GAAGO,MAAM,CAACP,IAAI,CAAC,CAAC,KAAKoB,OAAO,GAAG,CAAC,CAAC,EAAEX,MAAM,CAACP,IAAI,CAAC,CAAC,GAAG,CAACI,MAAM,CAACJ,IAAI,CAAC,CAAC,GAAGK,MAAM,CAACL,IAAI,CAAC,CAAC,KAAKkB,OAAO,GAAG,CAAC,CAAC,CAAC;IAClK,IAAI,CAAC,IAAI,CAACI,OAAO,CAACF,UAAU,CAAC,EAAE;MAC3B,IAAI,IAAI,CAACE,OAAO,CAACD,UAAU,CAAC,EAAE;QAC1B,OAAOA,UAAU;MACrB;MACA,OAAO,IAAI;IACf;IACA,IAAI,CAAC,IAAI,CAACC,OAAO,CAACD,UAAU,CAAC,EAAE;MAC3B,OAAOD,UAAU;IACrB;IACA,IAAIG,KAAK,GAAG,IAAI,CAACpC,kBAAkB,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAG,IAAI,CAACjC,kBAAkB,CAAC4B,OAAO,EAAEK,UAAU,CAAC;IACvG,IAAII,KAAK,GAAG,IAAI,CAACrC,kBAAkB,CAACgC,OAAO,EAAEE,UAAU,CAAC,GAAG,IAAI,CAAClC,kBAAkB,CAAC4B,OAAO,EAAEM,UAAU,CAAC;IACvG,IAAIE,KAAK,GAAGC,KAAK,EAAE;MACf,OAAOJ,UAAU;IACrB,CAAC,MACI;MACD,OAAOC,UAAU;IACrB;EACJ,CAAC;EACD;AACJ;AACA;EACIlD,QAAQ,CAACG,SAAS,CAACO,mBAAmB,GAAG,UAAUJ,MAAM,EAAE;IACvD;IACA;IACA;IACA,IAAI2B,MAAM,GAAG3B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI4B,MAAM,GAAG5B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI6B,MAAM,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACtB,IAAI8B,MAAM,GAAG9B,MAAM,CAAC,CAAC,CAAC;IACtB;IACA,IAAIgD,IAAI,GAAG,IAAI,CAACtC,kBAAkB,CAACiB,MAAM,EAAEG,MAAM,CAAC,GAAG,CAAC;IACtD,IAAImB,IAAI,GAAG,IAAI,CAACvC,kBAAkB,CAACmB,MAAM,EAAEC,MAAM,CAAC,GAAG,CAAC;IACtD;IACA,IAAIY,OAAO,GAAGhD,QAAQ,CAACsB,UAAU,CAACW,MAAM,EAAEC,MAAM,EAAEqB,IAAI,GAAG,CAAC,CAAC;IAC3D,IAAIX,OAAO,GAAG5C,QAAQ,CAACsB,UAAU,CAACa,MAAM,EAAED,MAAM,EAAEoB,IAAI,GAAG,CAAC,CAAC;IAC3D;IACAA,IAAI,GAAG,IAAI,CAACtC,kBAAkB,CAACgC,OAAO,EAAEZ,MAAM,CAAC,GAAG,CAAC;IACnDmB,IAAI,GAAG,IAAI,CAACvC,kBAAkB,CAAC4B,OAAO,EAAER,MAAM,CAAC,GAAG,CAAC;IACnD,IAAI,CAACkB,IAAI,GAAG,IAAI,MAAM,CAAC,EAAE;MACrBA,IAAI,IAAI,CAAC;IACb;IACA,IAAI,CAACC,IAAI,GAAG,IAAI,MAAM,CAAC,EAAE;MACrBA,IAAI,IAAI,CAAC;IACb;IACA;IACA;IACA,IAAIC,OAAO,GAAG,CAACvB,MAAM,CAACN,IAAI,CAAC,CAAC,GAAGO,MAAM,CAACP,IAAI,CAAC,CAAC,GAAGQ,MAAM,CAACR,IAAI,CAAC,CAAC,GAAGS,MAAM,CAACT,IAAI,CAAC,CAAC,IAAI,CAAC;IACjF,IAAI8B,OAAO,GAAG,CAACxB,MAAM,CAACJ,IAAI,CAAC,CAAC,GAAGK,MAAM,CAACL,IAAI,CAAC,CAAC,GAAGM,MAAM,CAACN,IAAI,CAAC,CAAC,GAAGO,MAAM,CAACP,IAAI,CAAC,CAAC,IAAI,CAAC;IACjFI,MAAM,GAAGjC,QAAQ,CAAC8B,QAAQ,CAACG,MAAM,EAAEuB,OAAO,EAAEC,OAAO,CAAC;IACpDvB,MAAM,GAAGlC,QAAQ,CAAC8B,QAAQ,CAACI,MAAM,EAAEsB,OAAO,EAAEC,OAAO,CAAC;IACpDtB,MAAM,GAAGnC,QAAQ,CAAC8B,QAAQ,CAACK,MAAM,EAAEqB,OAAO,EAAEC,OAAO,CAAC;IACpDrB,MAAM,GAAGpC,QAAQ,CAAC8B,QAAQ,CAACM,MAAM,EAAEoB,OAAO,EAAEC,OAAO,CAAC;IACpD,IAAId,OAAO;IACX,IAAIe,OAAO;IACX;IACAV,OAAO,GAAGhD,QAAQ,CAACsB,UAAU,CAACW,MAAM,EAAEC,MAAM,EAAEqB,IAAI,GAAG,CAAC,CAAC;IACvDP,OAAO,GAAGhD,QAAQ,CAACsB,UAAU,CAAC0B,OAAO,EAAEZ,MAAM,EAAEkB,IAAI,GAAG,CAAC,CAAC;IACxDX,OAAO,GAAG3C,QAAQ,CAACsB,UAAU,CAACY,MAAM,EAAED,MAAM,EAAEsB,IAAI,GAAG,CAAC,CAAC;IACvDZ,OAAO,GAAG3C,QAAQ,CAACsB,UAAU,CAACqB,OAAO,EAAER,MAAM,EAAEmB,IAAI,GAAG,CAAC,CAAC;IACxDV,OAAO,GAAG5C,QAAQ,CAACsB,UAAU,CAACa,MAAM,EAAEC,MAAM,EAAEmB,IAAI,GAAG,CAAC,CAAC;IACvDX,OAAO,GAAG5C,QAAQ,CAACsB,UAAU,CAACsB,OAAO,EAAEV,MAAM,EAAEoB,IAAI,GAAG,CAAC,CAAC;IACxDI,OAAO,GAAG1D,QAAQ,CAACsB,UAAU,CAACc,MAAM,EAAED,MAAM,EAAEoB,IAAI,GAAG,CAAC,CAAC;IACvDG,OAAO,GAAG1D,QAAQ,CAACsB,UAAU,CAACoC,OAAO,EAAEzB,MAAM,EAAEqB,IAAI,GAAG,CAAC,CAAC;IACxD,OAAO,CAACN,OAAO,EAAEL,OAAO,EAAEC,OAAO,EAAEc,OAAO,CAAC;EAC/C,CAAC;EACD1D,QAAQ,CAACG,SAAS,CAACgD,OAAO,GAAG,UAAUQ,CAAC,EAAE;IACtC,OAAOA,CAAC,CAAChC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAIgC,CAAC,CAAChC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC1B,KAAK,CAAC2D,QAAQ,CAAC,CAAC,IAAID,CAAC,CAAC9B,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI8B,CAAC,CAAC9B,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC5B,KAAK,CAAC4D,SAAS,CAAC,CAAC;EACjH,CAAC;EACD7D,QAAQ,CAACqB,UAAU,GAAG,UAAUpB,KAAK,EAAEU,OAAO,EAAEC,UAAU,EAAEC,WAAW,EAAEC,QAAQ,EAAEgD,UAAU,EAAEC,UAAU,EAAE;IACvG,IAAIC,OAAO,GAAGnE,mBAAmB,CAACoE,WAAW,CAAC,CAAC;IAC/C,OAAOD,OAAO,CAAC3C,UAAU,CAACpB,KAAK,EAAE6D,UAAU,EAAEC,UAAU,EAAE,GAAG,EAAE,GAAG,EAAED,UAAU,GAAG,GAAG,EAAE,GAAG,EAAEA,UAAU,GAAG,GAAG,EAAEC,UAAU,GAAG,GAAG,EAAE,GAAG,EAAEA,UAAU,GAAG,GAAG,EAAEpD,OAAO,CAACgB,IAAI,CAAC,CAAC,EAAEhB,OAAO,CAACkB,IAAI,CAAC,CAAC,EAAEf,QAAQ,CAACa,IAAI,CAAC,CAAC,EAAEb,QAAQ,CAACe,IAAI,CAAC,CAAC,EAAEhB,WAAW,CAACc,IAAI,CAAC,CAAC,EAAEd,WAAW,CAACgB,IAAI,CAAC,CAAC,EAAEjB,UAAU,CAACe,IAAI,CAAC,CAAC,EAAEf,UAAU,CAACiB,IAAI,CAAC,CAAC,CAAC;EACxS,CAAC;EACD;AACJ;AACA;EACI7B,QAAQ,CAACG,SAAS,CAACa,kBAAkB,GAAG,UAAUkD,IAAI,EAAE1C,EAAE,EAAE;IACxD;IACA,IAAIO,KAAK,GAAGb,IAAI,CAACiD,KAAK,CAACD,IAAI,CAACvC,IAAI,CAAC,CAAC,CAAC;IACnC,IAAIK,KAAK,GAAGd,IAAI,CAACiD,KAAK,CAACD,IAAI,CAACrC,IAAI,CAAC,CAAC,CAAC;IACnC,IAAIuC,GAAG,GAAGlD,IAAI,CAACiD,KAAK,CAAC3C,EAAE,CAACG,IAAI,CAAC,CAAC,CAAC;IAC/B,IAAI0C,GAAG,GAAGnD,IAAI,CAACiD,KAAK,CAAC3C,EAAE,CAACK,IAAI,CAAC,CAAC,CAAC;IAC/B,IAAIyC,KAAK,GAAGpD,IAAI,CAACqD,GAAG,CAACF,GAAG,GAAGrC,KAAK,CAAC,GAAGd,IAAI,CAACqD,GAAG,CAACH,GAAG,GAAGrC,KAAK,CAAC;IACzD,IAAIuC,KAAK,EAAE;MACP,IAAIE,IAAI,GAAGzC,KAAK;MAChBA,KAAK,GAAGC,KAAK;MACbA,KAAK,GAAGwC,IAAI;MACZA,IAAI,GAAGJ,GAAG;MACVA,GAAG,GAAGC,GAAG;MACTA,GAAG,GAAGG,IAAI;IACd;IACA,IAAIC,EAAE,GAAGvD,IAAI,CAACqD,GAAG,CAACH,GAAG,GAAGrC,KAAK,CAAC;IAC9B,IAAI2C,EAAE,GAAGxD,IAAI,CAACqD,GAAG,CAACF,GAAG,GAAGrC,KAAK,CAAC;IAC9B,IAAI2C,KAAK,GAAG,CAACF,EAAE,GAAG,CAAC;IACnB,IAAIG,KAAK,GAAG5C,KAAK,GAAGqC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,IAAIQ,KAAK,GAAG9C,KAAK,GAAGqC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,IAAIU,WAAW,GAAG,CAAC;IACnB,IAAIC,OAAO,GAAG,IAAI,CAAC9E,KAAK,CAAC+E,GAAG,CAACV,KAAK,GAAGtC,KAAK,GAAGD,KAAK,EAAEuC,KAAK,GAAGvC,KAAK,GAAGC,KAAK,CAAC;IAC1E,KAAK,IAAIN,CAAC,GAAGK,KAAK,EAAEH,CAAC,GAAGI,KAAK,EAAEN,CAAC,KAAK0C,GAAG,EAAE1C,CAAC,IAAImD,KAAK,EAAE;MAClD,IAAII,OAAO,GAAG,IAAI,CAAChF,KAAK,CAAC+E,GAAG,CAACV,KAAK,GAAG1C,CAAC,GAAGF,CAAC,EAAE4C,KAAK,GAAG5C,CAAC,GAAGE,CAAC,CAAC;MAC1D,IAAIqD,OAAO,KAAKF,OAAO,EAAE;QACrBD,WAAW,EAAE;QACbC,OAAO,GAAGE,OAAO;MACrB;MACAN,KAAK,IAAID,EAAE;MACX,IAAIC,KAAK,GAAG,CAAC,EAAE;QACX,IAAI/C,CAAC,KAAKyC,GAAG,EAAE;UACX;QACJ;QACAzC,CAAC,IAAIgD,KAAK;QACVD,KAAK,IAAIF,EAAE;MACf;IACJ;IACA,OAAOK,WAAW;EACtB,CAAC;EACD,OAAO9E,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}