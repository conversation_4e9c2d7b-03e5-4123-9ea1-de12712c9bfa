{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _get = function get(object, property, receiver) {\n  if (object === null) object = Function.prototype;\n  var desc = Object.getOwnPropertyDescriptor(object, property);\n  if (desc === undefined) {\n    var parent = Object.getPrototypeOf(object);\n    if (parent === null) {\n      return undefined;\n    } else {\n      return get(parent, property, receiver);\n    }\n  } else if (\"value\" in desc) {\n    return desc.value;\n  } else {\n    var getter = desc.get;\n    if (getter === undefined) {\n      return undefined;\n    }\n    return getter.call(receiver);\n  }\n};\nvar _EAN2 = require('./EAN');\nvar _EAN3 = _interopRequireDefault(_EAN2);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n} // Encoding documentation:\n// http://www.barcodeisland.com/ean8.phtml\n\n// Calculate the checksum digit\nvar checksum = function checksum(number) {\n  var res = number.substr(0, 7).split('').map(function (n) {\n    return +n;\n  }).reduce(function (sum, a, idx) {\n    return idx % 2 ? sum + a : sum + a * 3;\n  }, 0);\n  return (10 - res % 10) % 10;\n};\nvar EAN8 = function (_EAN) {\n  _inherits(EAN8, _EAN);\n  function EAN8(data, options) {\n    _classCallCheck(this, EAN8);\n\n    // Add checksum if it does not exist\n    if (data.search(/^[0-9]{7}$/) !== -1) {\n      data += checksum(data);\n    }\n    return _possibleConstructorReturn(this, (EAN8.__proto__ || Object.getPrototypeOf(EAN8)).call(this, data, options));\n  }\n  _createClass(EAN8, [{\n    key: 'valid',\n    value: function valid() {\n      return this.data.search(/^[0-9]{8}$/) !== -1 && +this.data[7] === checksum(this.data);\n    }\n  }, {\n    key: 'leftText',\n    value: function leftText() {\n      return _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'leftText', this).call(this, 0, 4);\n    }\n  }, {\n    key: 'leftEncode',\n    value: function leftEncode() {\n      var data = this.data.substr(0, 4);\n      return _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'leftEncode', this).call(this, data, 'LLLL');\n    }\n  }, {\n    key: 'rightText',\n    value: function rightText() {\n      return _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'rightText', this).call(this, 4, 4);\n    }\n  }, {\n    key: 'rightEncode',\n    value: function rightEncode() {\n      var data = this.data.substr(4, 4);\n      return _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'rightEncode', this).call(this, data, 'RRRR');\n    }\n  }]);\n  return EAN8;\n}(_EAN3.default);\nexports.default = EAN8;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_get", "get", "object", "property", "receiver", "Function", "desc", "getOwnPropertyDescriptor", "undefined", "parent", "getPrototypeOf", "getter", "call", "_EAN2", "require", "_EAN3", "_interopRequireDefault", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "checksum", "number", "res", "substr", "split", "map", "n", "reduce", "sum", "a", "idx", "EAN8", "_EAN", "data", "options", "search", "valid", "leftText", "leftEncode", "rightText", "rightEncode"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN8.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _get = function get(object, property, receiver) { if (object === null) object = Function.prototype; var desc = Object.getOwnPropertyDescriptor(object, property); if (desc === undefined) { var parent = Object.getPrototypeOf(object); if (parent === null) { return undefined; } else { return get(parent, property, receiver); } } else if (\"value\" in desc) { return desc.value; } else { var getter = desc.get; if (getter === undefined) { return undefined; } return getter.call(receiver); } };\n\nvar _EAN2 = require('./EAN');\n\nvar _EAN3 = _interopRequireDefault(_EAN2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// http://www.barcodeisland.com/ean8.phtml\n\n// Calculate the checksum digit\nvar checksum = function checksum(number) {\n\tvar res = number.substr(0, 7).split('').map(function (n) {\n\t\treturn +n;\n\t}).reduce(function (sum, a, idx) {\n\t\treturn idx % 2 ? sum + a : sum + a * 3;\n\t}, 0);\n\n\treturn (10 - res % 10) % 10;\n};\n\nvar EAN8 = function (_EAN) {\n\t_inherits(EAN8, _EAN);\n\n\tfunction EAN8(data, options) {\n\t\t_classCallCheck(this, EAN8);\n\n\t\t// Add checksum if it does not exist\n\t\tif (data.search(/^[0-9]{7}$/) !== -1) {\n\t\t\tdata += checksum(data);\n\t\t}\n\n\t\treturn _possibleConstructorReturn(this, (EAN8.__proto__ || Object.getPrototypeOf(EAN8)).call(this, data, options));\n\t}\n\n\t_createClass(EAN8, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{8}$/) !== -1 && +this.data[7] === checksum(this.data);\n\t\t}\n\t}, {\n\t\tkey: 'leftText',\n\t\tvalue: function leftText() {\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'leftText', this).call(this, 0, 4);\n\t\t}\n\t}, {\n\t\tkey: 'leftEncode',\n\t\tvalue: function leftEncode() {\n\t\t\tvar data = this.data.substr(0, 4);\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'leftEncode', this).call(this, data, 'LLLL');\n\t\t}\n\t}, {\n\t\tkey: 'rightText',\n\t\tvalue: function rightText() {\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'rightText', this).call(this, 4, 4);\n\t\t}\n\t}, {\n\t\tkey: 'rightEncode',\n\t\tvalue: function rightEncode() {\n\t\t\tvar data = this.data.substr(4, 4);\n\t\t\treturn _get(EAN8.prototype.__proto__ || Object.getPrototypeOf(EAN8.prototype), 'rightEncode', this).call(this, data, 'RRRR');\n\t\t}\n\t}]);\n\n\treturn EAN8;\n}(_EAN3.default);\n\nexports.default = EAN8;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,IAAI,GAAG,SAASC,GAAGA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;EAAE,IAAIF,MAAM,KAAK,IAAI,EAAEA,MAAM,GAAGG,QAAQ,CAACN,SAAS;EAAE,IAAIO,IAAI,GAAGzB,MAAM,CAAC0B,wBAAwB,CAACL,MAAM,EAAEC,QAAQ,CAAC;EAAE,IAAIG,IAAI,KAAKE,SAAS,EAAE;IAAE,IAAIC,MAAM,GAAG5B,MAAM,CAAC6B,cAAc,CAACR,MAAM,CAAC;IAAE,IAAIO,MAAM,KAAK,IAAI,EAAE;MAAE,OAAOD,SAAS;IAAE,CAAC,MAAM;MAAE,OAAOP,GAAG,CAACQ,MAAM,EAAEN,QAAQ,EAAEC,QAAQ,CAAC;IAAE;EAAE,CAAC,MAAM,IAAI,OAAO,IAAIE,IAAI,EAAE;IAAE,OAAOA,IAAI,CAACtB,KAAK;EAAE,CAAC,MAAM;IAAE,IAAI2B,MAAM,GAAGL,IAAI,CAACL,GAAG;IAAE,IAAIU,MAAM,KAAKH,SAAS,EAAE;MAAE,OAAOA,SAAS;IAAE;IAAE,OAAOG,MAAM,CAACC,IAAI,CAACR,QAAQ,CAAC;EAAE;AAAE,CAAC;AAE1e,IAAIS,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE5B,IAAIC,KAAK,GAAGC,sBAAsB,CAACH,KAAK,CAAC;AAEzC,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEzB,WAAW,EAAE;EAAE,IAAI,EAAEyB,QAAQ,YAAYzB,WAAW,CAAC,EAAE;IAAE,MAAM,IAAI0B,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEZ,IAAI,EAAE;EAAE,IAAI,CAACY,IAAI,EAAE;IAAE,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOb,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGY,IAAI;AAAE;AAE/O,SAASE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIN,SAAS,CAAC,0DAA0D,GAAG,OAAOM,UAAU,CAAC;EAAE;EAAED,QAAQ,CAAC5B,SAAS,GAAGlB,MAAM,CAACgD,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC7B,SAAS,EAAE;IAAE+B,WAAW,EAAE;MAAE9C,KAAK,EAAE2C,QAAQ;MAAEnC,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAImC,UAAU,EAAE/C,MAAM,CAACkD,cAAc,GAAGlD,MAAM,CAACkD,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AAAE,CAAC,CAAC;AAC/e;;AAEA;AACA,IAAIK,QAAQ,GAAG,SAASA,QAAQA,CAACC,MAAM,EAAE;EACxC,IAAIC,GAAG,GAAGD,MAAM,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;IACxD,OAAO,CAACA,CAAC;EACV,CAAC,CAAC,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEC,CAAC,EAAEC,GAAG,EAAE;IAChC,OAAOA,GAAG,GAAG,CAAC,GAAGF,GAAG,GAAGC,CAAC,GAAGD,GAAG,GAAGC,CAAC,GAAG,CAAC;EACvC,CAAC,EAAE,CAAC,CAAC;EAEL,OAAO,CAAC,EAAE,GAAGP,GAAG,GAAG,EAAE,IAAI,EAAE;AAC5B,CAAC;AAED,IAAIS,IAAI,GAAG,UAAUC,IAAI,EAAE;EAC1BnB,SAAS,CAACkB,IAAI,EAAEC,IAAI,CAAC;EAErB,SAASD,IAAIA,CAACE,IAAI,EAAEC,OAAO,EAAE;IAC5B3B,eAAe,CAAC,IAAI,EAAEwB,IAAI,CAAC;;IAE3B;IACA,IAAIE,IAAI,CAACE,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE;MACrCF,IAAI,IAAIb,QAAQ,CAACa,IAAI,CAAC;IACvB;IAEA,OAAOvB,0BAA0B,CAAC,IAAI,EAAE,CAACqB,IAAI,CAACZ,SAAS,IAAInD,MAAM,CAAC6B,cAAc,CAACkC,IAAI,CAAC,EAAEhC,IAAI,CAAC,IAAI,EAAEkC,IAAI,EAAEC,OAAO,CAAC,CAAC;EACnH;EAEA9D,YAAY,CAAC2D,IAAI,EAAE,CAAC;IACnBjD,GAAG,EAAE,OAAO;IACZX,KAAK,EAAE,SAASiE,KAAKA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACH,IAAI,CAACE,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACF,IAAI,CAAC,CAAC,CAAC,KAAKb,QAAQ,CAAC,IAAI,CAACa,IAAI,CAAC;IACtF;EACD,CAAC,EAAE;IACFnD,GAAG,EAAE,UAAU;IACfX,KAAK,EAAE,SAASkE,QAAQA,CAAA,EAAG;MAC1B,OAAOlD,IAAI,CAAC4C,IAAI,CAAC7C,SAAS,CAACiC,SAAS,IAAInD,MAAM,CAAC6B,cAAc,CAACkC,IAAI,CAAC7C,SAAS,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,CAACa,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IAClH;EACD,CAAC,EAAE;IACFjB,GAAG,EAAE,YAAY;IACjBX,KAAK,EAAE,SAASmE,UAAUA,CAAA,EAAG;MAC5B,IAAIL,IAAI,GAAG,IAAI,CAACA,IAAI,CAACV,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACjC,OAAOpC,IAAI,CAAC4C,IAAI,CAAC7C,SAAS,CAACiC,SAAS,IAAInD,MAAM,CAAC6B,cAAc,CAACkC,IAAI,CAAC7C,SAAS,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,CAACa,IAAI,CAAC,IAAI,EAAEkC,IAAI,EAAE,MAAM,CAAC;IAC5H;EACD,CAAC,EAAE;IACFnD,GAAG,EAAE,WAAW;IAChBX,KAAK,EAAE,SAASoE,SAASA,CAAA,EAAG;MAC3B,OAAOpD,IAAI,CAAC4C,IAAI,CAAC7C,SAAS,CAACiC,SAAS,IAAInD,MAAM,CAAC6B,cAAc,CAACkC,IAAI,CAAC7C,SAAS,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,CAACa,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACnH;EACD,CAAC,EAAE;IACFjB,GAAG,EAAE,aAAa;IAClBX,KAAK,EAAE,SAASqE,WAAWA,CAAA,EAAG;MAC7B,IAAIP,IAAI,GAAG,IAAI,CAACA,IAAI,CAACV,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACjC,OAAOpC,IAAI,CAAC4C,IAAI,CAAC7C,SAAS,CAACiC,SAAS,IAAInD,MAAM,CAAC6B,cAAc,CAACkC,IAAI,CAAC7C,SAAS,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,CAACa,IAAI,CAAC,IAAI,EAAEkC,IAAI,EAAE,MAAM,CAAC;IAC7H;EACD,CAAC,CAAC,CAAC;EAEH,OAAOF,IAAI;AACZ,CAAC,CAAC7B,KAAK,CAACI,OAAO,CAAC;AAEhBpC,OAAO,CAACoC,OAAO,GAAGyB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}