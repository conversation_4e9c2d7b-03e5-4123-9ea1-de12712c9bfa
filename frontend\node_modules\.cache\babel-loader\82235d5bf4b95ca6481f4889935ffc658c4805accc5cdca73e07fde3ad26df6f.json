{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/**\n * <p>This class implements a perspective transform in two dimensions. Given four source and four\n * destination points, it will compute the transformation implied between them. The code is based\n * directly upon section 3.4.2 of <PERSON>'s \"Digital Image Warping\"; see pages 54-56.</p>\n *\n * <AUTHOR>\n */\nvar PerspectiveTransform = /** @class */function () {\n  function PerspectiveTransform(a11 /*float*/, a21 /*float*/, a31 /*float*/, a12 /*float*/, a22 /*float*/, a32 /*float*/, a13 /*float*/, a23 /*float*/, a33 /*float*/) {\n    this.a11 = a11;\n    this.a21 = a21;\n    this.a31 = a31;\n    this.a12 = a12;\n    this.a22 = a22;\n    this.a32 = a32;\n    this.a13 = a13;\n    this.a23 = a23;\n    this.a33 = a33;\n  }\n  PerspectiveTransform.quadrilateralToQuadrilateral = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/, x0p /*float*/, y0p /*float*/, x1p /*float*/, y1p /*float*/, x2p /*float*/, y2p /*float*/, x3p /*float*/, y3p /*float*/) {\n    var qToS = PerspectiveTransform.quadrilateralToSquare(x0, y0, x1, y1, x2, y2, x3, y3);\n    var sToQ = PerspectiveTransform.squareToQuadrilateral(x0p, y0p, x1p, y1p, x2p, y2p, x3p, y3p);\n    return sToQ.times(qToS);\n  };\n  PerspectiveTransform.prototype.transformPoints = function (points) {\n    var max = points.length;\n    var a11 = this.a11;\n    var a12 = this.a12;\n    var a13 = this.a13;\n    var a21 = this.a21;\n    var a22 = this.a22;\n    var a23 = this.a23;\n    var a31 = this.a31;\n    var a32 = this.a32;\n    var a33 = this.a33;\n    for (var i = 0; i < max; i += 2) {\n      var x = points[i];\n      var y = points[i + 1];\n      var denominator = a13 * x + a23 * y + a33;\n      points[i] = (a11 * x + a21 * y + a31) / denominator;\n      points[i + 1] = (a12 * x + a22 * y + a32) / denominator;\n    }\n  };\n  PerspectiveTransform.prototype.transformPointsWithValues = function (xValues, yValues) {\n    var a11 = this.a11;\n    var a12 = this.a12;\n    var a13 = this.a13;\n    var a21 = this.a21;\n    var a22 = this.a22;\n    var a23 = this.a23;\n    var a31 = this.a31;\n    var a32 = this.a32;\n    var a33 = this.a33;\n    var n = xValues.length;\n    for (var i = 0; i < n; i++) {\n      var x = xValues[i];\n      var y = yValues[i];\n      var denominator = a13 * x + a23 * y + a33;\n      xValues[i] = (a11 * x + a21 * y + a31) / denominator;\n      yValues[i] = (a12 * x + a22 * y + a32) / denominator;\n    }\n  };\n  PerspectiveTransform.squareToQuadrilateral = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/) {\n    var dx3 = x0 - x1 + x2 - x3;\n    var dy3 = y0 - y1 + y2 - y3;\n    if (dx3 === 0.0 && dy3 === 0.0) {\n      // Affine\n      return new PerspectiveTransform(x1 - x0, x2 - x1, x0, y1 - y0, y2 - y1, y0, 0.0, 0.0, 1.0);\n    } else {\n      var dx1 = x1 - x2;\n      var dx2 = x3 - x2;\n      var dy1 = y1 - y2;\n      var dy2 = y3 - y2;\n      var denominator = dx1 * dy2 - dx2 * dy1;\n      var a13 = (dx3 * dy2 - dx2 * dy3) / denominator;\n      var a23 = (dx1 * dy3 - dx3 * dy1) / denominator;\n      return new PerspectiveTransform(x1 - x0 + a13 * x1, x3 - x0 + a23 * x3, x0, y1 - y0 + a13 * y1, y3 - y0 + a23 * y3, y0, a13, a23, 1.0);\n    }\n  };\n  PerspectiveTransform.quadrilateralToSquare = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/) {\n    // Here, the adjoint serves as the inverse:\n    return PerspectiveTransform.squareToQuadrilateral(x0, y0, x1, y1, x2, y2, x3, y3).buildAdjoint();\n  };\n  PerspectiveTransform.prototype.buildAdjoint = function () {\n    // Adjoint is the transpose of the cofactor matrix:\n    return new PerspectiveTransform(this.a22 * this.a33 - this.a23 * this.a32, this.a23 * this.a31 - this.a21 * this.a33, this.a21 * this.a32 - this.a22 * this.a31, this.a13 * this.a32 - this.a12 * this.a33, this.a11 * this.a33 - this.a13 * this.a31, this.a12 * this.a31 - this.a11 * this.a32, this.a12 * this.a23 - this.a13 * this.a22, this.a13 * this.a21 - this.a11 * this.a23, this.a11 * this.a22 - this.a12 * this.a21);\n  };\n  PerspectiveTransform.prototype.times = function (other) {\n    return new PerspectiveTransform(this.a11 * other.a11 + this.a21 * other.a12 + this.a31 * other.a13, this.a11 * other.a21 + this.a21 * other.a22 + this.a31 * other.a23, this.a11 * other.a31 + this.a21 * other.a32 + this.a31 * other.a33, this.a12 * other.a11 + this.a22 * other.a12 + this.a32 * other.a13, this.a12 * other.a21 + this.a22 * other.a22 + this.a32 * other.a23, this.a12 * other.a31 + this.a22 * other.a32 + this.a32 * other.a33, this.a13 * other.a11 + this.a23 * other.a12 + this.a33 * other.a13, this.a13 * other.a21 + this.a23 * other.a22 + this.a33 * other.a23, this.a13 * other.a31 + this.a23 * other.a32 + this.a33 * other.a33);\n  };\n  return PerspectiveTransform;\n}();\nexport default PerspectiveTransform;", "map": {"version": 3, "names": ["PerspectiveTransform", "a11", "a21", "a31", "a12", "a22", "a32", "a13", "a23", "a33", "quadrilateralToQuadrilateral", "x0", "y0", "x1", "y1", "x2", "y2", "x3", "y3", "x0p", "y0p", "x1p", "y1p", "x2p", "y2p", "x3p", "y3p", "qToS", "quadrilateralToSquare", "sToQ", "squareToQuadrilateral", "times", "prototype", "transformPoints", "points", "max", "length", "i", "x", "y", "denominator", "transformPointsWithValues", "xValues", "yV<PERSON><PERSON>", "n", "dx3", "dy3", "dx1", "dx2", "dy1", "dy2", "buildAdjoint", "other"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/PerspectiveTransform.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/**\n * <p>This class implements a perspective transform in two dimensions. Given four source and four\n * destination points, it will compute the transformation implied between them. The code is based\n * directly upon section 3.4.2 of <PERSON>'s \"Digital Image Warping\"; see pages 54-56.</p>\n *\n * <AUTHOR>\n */\nvar PerspectiveTransform = /** @class */ (function () {\n    function PerspectiveTransform(a11 /*float*/, a21 /*float*/, a31 /*float*/, a12 /*float*/, a22 /*float*/, a32 /*float*/, a13 /*float*/, a23 /*float*/, a33 /*float*/) {\n        this.a11 = a11;\n        this.a21 = a21;\n        this.a31 = a31;\n        this.a12 = a12;\n        this.a22 = a22;\n        this.a32 = a32;\n        this.a13 = a13;\n        this.a23 = a23;\n        this.a33 = a33;\n    }\n    PerspectiveTransform.quadrilateralToQuadrilateral = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/, x0p /*float*/, y0p /*float*/, x1p /*float*/, y1p /*float*/, x2p /*float*/, y2p /*float*/, x3p /*float*/, y3p /*float*/) {\n        var qToS = PerspectiveTransform.quadrilateralToSquare(x0, y0, x1, y1, x2, y2, x3, y3);\n        var sToQ = PerspectiveTransform.squareToQuadrilateral(x0p, y0p, x1p, y1p, x2p, y2p, x3p, y3p);\n        return sToQ.times(qToS);\n    };\n    PerspectiveTransform.prototype.transformPoints = function (points) {\n        var max = points.length;\n        var a11 = this.a11;\n        var a12 = this.a12;\n        var a13 = this.a13;\n        var a21 = this.a21;\n        var a22 = this.a22;\n        var a23 = this.a23;\n        var a31 = this.a31;\n        var a32 = this.a32;\n        var a33 = this.a33;\n        for (var i = 0; i < max; i += 2) {\n            var x = points[i];\n            var y = points[i + 1];\n            var denominator = a13 * x + a23 * y + a33;\n            points[i] = (a11 * x + a21 * y + a31) / denominator;\n            points[i + 1] = (a12 * x + a22 * y + a32) / denominator;\n        }\n    };\n    PerspectiveTransform.prototype.transformPointsWithValues = function (xValues, yValues) {\n        var a11 = this.a11;\n        var a12 = this.a12;\n        var a13 = this.a13;\n        var a21 = this.a21;\n        var a22 = this.a22;\n        var a23 = this.a23;\n        var a31 = this.a31;\n        var a32 = this.a32;\n        var a33 = this.a33;\n        var n = xValues.length;\n        for (var i = 0; i < n; i++) {\n            var x = xValues[i];\n            var y = yValues[i];\n            var denominator = a13 * x + a23 * y + a33;\n            xValues[i] = (a11 * x + a21 * y + a31) / denominator;\n            yValues[i] = (a12 * x + a22 * y + a32) / denominator;\n        }\n    };\n    PerspectiveTransform.squareToQuadrilateral = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/) {\n        var dx3 = x0 - x1 + x2 - x3;\n        var dy3 = y0 - y1 + y2 - y3;\n        if (dx3 === 0.0 && dy3 === 0.0) {\n            // Affine\n            return new PerspectiveTransform(x1 - x0, x2 - x1, x0, y1 - y0, y2 - y1, y0, 0.0, 0.0, 1.0);\n        }\n        else {\n            var dx1 = x1 - x2;\n            var dx2 = x3 - x2;\n            var dy1 = y1 - y2;\n            var dy2 = y3 - y2;\n            var denominator = dx1 * dy2 - dx2 * dy1;\n            var a13 = (dx3 * dy2 - dx2 * dy3) / denominator;\n            var a23 = (dx1 * dy3 - dx3 * dy1) / denominator;\n            return new PerspectiveTransform(x1 - x0 + a13 * x1, x3 - x0 + a23 * x3, x0, y1 - y0 + a13 * y1, y3 - y0 + a23 * y3, y0, a13, a23, 1.0);\n        }\n    };\n    PerspectiveTransform.quadrilateralToSquare = function (x0 /*float*/, y0 /*float*/, x1 /*float*/, y1 /*float*/, x2 /*float*/, y2 /*float*/, x3 /*float*/, y3 /*float*/) {\n        // Here, the adjoint serves as the inverse:\n        return PerspectiveTransform.squareToQuadrilateral(x0, y0, x1, y1, x2, y2, x3, y3).buildAdjoint();\n    };\n    PerspectiveTransform.prototype.buildAdjoint = function () {\n        // Adjoint is the transpose of the cofactor matrix:\n        return new PerspectiveTransform(this.a22 * this.a33 - this.a23 * this.a32, this.a23 * this.a31 - this.a21 * this.a33, this.a21 * this.a32 - this.a22 * this.a31, this.a13 * this.a32 - this.a12 * this.a33, this.a11 * this.a33 - this.a13 * this.a31, this.a12 * this.a31 - this.a11 * this.a32, this.a12 * this.a23 - this.a13 * this.a22, this.a13 * this.a21 - this.a11 * this.a23, this.a11 * this.a22 - this.a12 * this.a21);\n    };\n    PerspectiveTransform.prototype.times = function (other) {\n        return new PerspectiveTransform(this.a11 * other.a11 + this.a21 * other.a12 + this.a31 * other.a13, this.a11 * other.a21 + this.a21 * other.a22 + this.a31 * other.a23, this.a11 * other.a31 + this.a21 * other.a32 + this.a31 * other.a33, this.a12 * other.a11 + this.a22 * other.a12 + this.a32 * other.a13, this.a12 * other.a21 + this.a22 * other.a22 + this.a32 * other.a23, this.a12 * other.a31 + this.a22 * other.a32 + this.a32 * other.a33, this.a13 * other.a11 + this.a23 * other.a12 + this.a33 * other.a13, this.a13 * other.a21 + this.a23 * other.a22 + this.a33 * other.a23, this.a13 * other.a31 + this.a23 * other.a32 + this.a33 * other.a33);\n    };\n    return PerspectiveTransform;\n}());\nexport default PerspectiveTransform;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,oBAAoB,GAAG,aAAe,YAAY;EAClD,SAASA,oBAAoBA,CAACC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAW;IACjK,IAAI,CAACR,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;EACAT,oBAAoB,CAACU,4BAA4B,GAAG,UAAUC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAWC,GAAG,CAAC,WAAW;IAClS,IAAIC,IAAI,GAAG3B,oBAAoB,CAAC4B,qBAAqB,CAACjB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IACrF,IAAIW,IAAI,GAAG7B,oBAAoB,CAAC8B,qBAAqB,CAACX,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,CAAC;IAC7F,OAAOG,IAAI,CAACE,KAAK,CAACJ,IAAI,CAAC;EAC3B,CAAC;EACD3B,oBAAoB,CAACgC,SAAS,CAACC,eAAe,GAAG,UAAUC,MAAM,EAAE;IAC/D,IAAIC,GAAG,GAAGD,MAAM,CAACE,MAAM;IACvB,IAAInC,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIL,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIL,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC7B,IAAIC,CAAC,GAAGJ,MAAM,CAACG,CAAC,CAAC;MACjB,IAAIE,CAAC,GAAGL,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC;MACrB,IAAIG,WAAW,GAAGjC,GAAG,GAAG+B,CAAC,GAAG9B,GAAG,GAAG+B,CAAC,GAAG9B,GAAG;MACzCyB,MAAM,CAACG,CAAC,CAAC,GAAG,CAACpC,GAAG,GAAGqC,CAAC,GAAGpC,GAAG,GAAGqC,CAAC,GAAGpC,GAAG,IAAIqC,WAAW;MACnDN,MAAM,CAACG,CAAC,GAAG,CAAC,CAAC,GAAG,CAACjC,GAAG,GAAGkC,CAAC,GAAGjC,GAAG,GAAGkC,CAAC,GAAGjC,GAAG,IAAIkC,WAAW;IAC3D;EACJ,CAAC;EACDxC,oBAAoB,CAACgC,SAAS,CAACS,yBAAyB,GAAG,UAAUC,OAAO,EAAEC,OAAO,EAAE;IACnF,IAAI1C,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIL,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIL,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAIG,GAAG,GAAG,IAAI,CAACA,GAAG;IAClB,IAAImC,CAAC,GAAGF,OAAO,CAACN,MAAM;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGO,CAAC,EAAEP,CAAC,EAAE,EAAE;MACxB,IAAIC,CAAC,GAAGI,OAAO,CAACL,CAAC,CAAC;MAClB,IAAIE,CAAC,GAAGI,OAAO,CAACN,CAAC,CAAC;MAClB,IAAIG,WAAW,GAAGjC,GAAG,GAAG+B,CAAC,GAAG9B,GAAG,GAAG+B,CAAC,GAAG9B,GAAG;MACzCiC,OAAO,CAACL,CAAC,CAAC,GAAG,CAACpC,GAAG,GAAGqC,CAAC,GAAGpC,GAAG,GAAGqC,CAAC,GAAGpC,GAAG,IAAIqC,WAAW;MACpDG,OAAO,CAACN,CAAC,CAAC,GAAG,CAACjC,GAAG,GAAGkC,CAAC,GAAGjC,GAAG,GAAGkC,CAAC,GAAGjC,GAAG,IAAIkC,WAAW;IACxD;EACJ,CAAC;EACDxC,oBAAoB,CAAC8B,qBAAqB,GAAG,UAAUnB,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAW;IACnK,IAAI2B,GAAG,GAAGlC,EAAE,GAAGE,EAAE,GAAGE,EAAE,GAAGE,EAAE;IAC3B,IAAI6B,GAAG,GAAGlC,EAAE,GAAGE,EAAE,GAAGE,EAAE,GAAGE,EAAE;IAC3B,IAAI2B,GAAG,KAAK,GAAG,IAAIC,GAAG,KAAK,GAAG,EAAE;MAC5B;MACA,OAAO,IAAI9C,oBAAoB,CAACa,EAAE,GAAGF,EAAE,EAAEI,EAAE,GAAGF,EAAE,EAAEF,EAAE,EAAEG,EAAE,GAAGF,EAAE,EAAEI,EAAE,GAAGF,EAAE,EAAEF,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAC9F,CAAC,MACI;MACD,IAAImC,GAAG,GAAGlC,EAAE,GAAGE,EAAE;MACjB,IAAIiC,GAAG,GAAG/B,EAAE,GAAGF,EAAE;MACjB,IAAIkC,GAAG,GAAGnC,EAAE,GAAGE,EAAE;MACjB,IAAIkC,GAAG,GAAGhC,EAAE,GAAGF,EAAE;MACjB,IAAIwB,WAAW,GAAGO,GAAG,GAAGG,GAAG,GAAGF,GAAG,GAAGC,GAAG;MACvC,IAAI1C,GAAG,GAAG,CAACsC,GAAG,GAAGK,GAAG,GAAGF,GAAG,GAAGF,GAAG,IAAIN,WAAW;MAC/C,IAAIhC,GAAG,GAAG,CAACuC,GAAG,GAAGD,GAAG,GAAGD,GAAG,GAAGI,GAAG,IAAIT,WAAW;MAC/C,OAAO,IAAIxC,oBAAoB,CAACa,EAAE,GAAGF,EAAE,GAAGJ,GAAG,GAAGM,EAAE,EAAEI,EAAE,GAAGN,EAAE,GAAGH,GAAG,GAAGS,EAAE,EAAEN,EAAE,EAAEG,EAAE,GAAGF,EAAE,GAAGL,GAAG,GAAGO,EAAE,EAAEI,EAAE,GAAGN,EAAE,GAAGJ,GAAG,GAAGU,EAAE,EAAEN,EAAE,EAAEL,GAAG,EAAEC,GAAG,EAAE,GAAG,CAAC;IAC1I;EACJ,CAAC;EACDR,oBAAoB,CAAC4B,qBAAqB,GAAG,UAAUjB,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAW;IACnK;IACA,OAAOlB,oBAAoB,CAAC8B,qBAAqB,CAACnB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAACiC,YAAY,CAAC,CAAC;EACpG,CAAC;EACDnD,oBAAoB,CAACgC,SAAS,CAACmB,YAAY,GAAG,YAAY;IACtD;IACA,OAAO,IAAInD,oBAAoB,CAAC,IAAI,CAACK,GAAG,GAAG,IAAI,CAACI,GAAG,GAAG,IAAI,CAACD,GAAG,GAAG,IAAI,CAACF,GAAG,EAAE,IAAI,CAACE,GAAG,GAAG,IAAI,CAACL,GAAG,GAAG,IAAI,CAACD,GAAG,GAAG,IAAI,CAACO,GAAG,EAAE,IAAI,CAACP,GAAG,GAAG,IAAI,CAACI,GAAG,GAAG,IAAI,CAACD,GAAG,GAAG,IAAI,CAACF,GAAG,EAAE,IAAI,CAACI,GAAG,GAAG,IAAI,CAACD,GAAG,GAAG,IAAI,CAACF,GAAG,GAAG,IAAI,CAACK,GAAG,EAAE,IAAI,CAACR,GAAG,GAAG,IAAI,CAACQ,GAAG,GAAG,IAAI,CAACF,GAAG,GAAG,IAAI,CAACJ,GAAG,EAAE,IAAI,CAACC,GAAG,GAAG,IAAI,CAACD,GAAG,GAAG,IAAI,CAACF,GAAG,GAAG,IAAI,CAACK,GAAG,EAAE,IAAI,CAACF,GAAG,GAAG,IAAI,CAACI,GAAG,GAAG,IAAI,CAACD,GAAG,GAAG,IAAI,CAACF,GAAG,EAAE,IAAI,CAACE,GAAG,GAAG,IAAI,CAACL,GAAG,GAAG,IAAI,CAACD,GAAG,GAAG,IAAI,CAACO,GAAG,EAAE,IAAI,CAACP,GAAG,GAAG,IAAI,CAACI,GAAG,GAAG,IAAI,CAACD,GAAG,GAAG,IAAI,CAACF,GAAG,CAAC;EACta,CAAC;EACDF,oBAAoB,CAACgC,SAAS,CAACD,KAAK,GAAG,UAAUqB,KAAK,EAAE;IACpD,OAAO,IAAIpD,oBAAoB,CAAC,IAAI,CAACC,GAAG,GAAGmD,KAAK,CAACnD,GAAG,GAAG,IAAI,CAACC,GAAG,GAAGkD,KAAK,CAAChD,GAAG,GAAG,IAAI,CAACD,GAAG,GAAGiD,KAAK,CAAC7C,GAAG,EAAE,IAAI,CAACN,GAAG,GAAGmD,KAAK,CAAClD,GAAG,GAAG,IAAI,CAACA,GAAG,GAAGkD,KAAK,CAAC/C,GAAG,GAAG,IAAI,CAACF,GAAG,GAAGiD,KAAK,CAAC5C,GAAG,EAAE,IAAI,CAACP,GAAG,GAAGmD,KAAK,CAACjD,GAAG,GAAG,IAAI,CAACD,GAAG,GAAGkD,KAAK,CAAC9C,GAAG,GAAG,IAAI,CAACH,GAAG,GAAGiD,KAAK,CAAC3C,GAAG,EAAE,IAAI,CAACL,GAAG,GAAGgD,KAAK,CAACnD,GAAG,GAAG,IAAI,CAACI,GAAG,GAAG+C,KAAK,CAAChD,GAAG,GAAG,IAAI,CAACE,GAAG,GAAG8C,KAAK,CAAC7C,GAAG,EAAE,IAAI,CAACH,GAAG,GAAGgD,KAAK,CAAClD,GAAG,GAAG,IAAI,CAACG,GAAG,GAAG+C,KAAK,CAAC/C,GAAG,GAAG,IAAI,CAACC,GAAG,GAAG8C,KAAK,CAAC5C,GAAG,EAAE,IAAI,CAACJ,GAAG,GAAGgD,KAAK,CAACjD,GAAG,GAAG,IAAI,CAACE,GAAG,GAAG+C,KAAK,CAAC9C,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG8C,KAAK,CAAC3C,GAAG,EAAE,IAAI,CAACF,GAAG,GAAG6C,KAAK,CAACnD,GAAG,GAAG,IAAI,CAACO,GAAG,GAAG4C,KAAK,CAAChD,GAAG,GAAG,IAAI,CAACK,GAAG,GAAG2C,KAAK,CAAC7C,GAAG,EAAE,IAAI,CAACA,GAAG,GAAG6C,KAAK,CAAClD,GAAG,GAAG,IAAI,CAACM,GAAG,GAAG4C,KAAK,CAAC/C,GAAG,GAAG,IAAI,CAACI,GAAG,GAAG2C,KAAK,CAAC5C,GAAG,EAAE,IAAI,CAACD,GAAG,GAAG6C,KAAK,CAACjD,GAAG,GAAG,IAAI,CAACK,GAAG,GAAG4C,KAAK,CAAC9C,GAAG,GAAG,IAAI,CAACG,GAAG,GAAG2C,KAAK,CAAC3C,GAAG,CAAC;EACvoB,CAAC;EACD,OAAOT,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,eAAeA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}