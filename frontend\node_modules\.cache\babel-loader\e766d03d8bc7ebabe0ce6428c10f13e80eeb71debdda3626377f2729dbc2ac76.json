{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.FormatException;\nimport FormatException from '../../FormatException';\n// import com.google.zxing.common.CharacterSetECI;\nimport CharacterSetECI from '../../common/CharacterSetECI';\n// import com.google.zxing.common.DecoderResult;\nimport DecoderResult from '../../common/DecoderResult';\n// import com.google.zxing.pdf417.PDF417ResultMetadata;\nimport PDF417ResultMetadata from '../PDF417ResultMetadata';\n// import java.io.ByteArrayOutputStream;\n// import java.math.BigInteger;\n// import java.nio.charset.Charset;\n// import java.nio.charset.StandardCharsets;\n// import java.util.Arrays;\nimport Arrays from '../../util/Arrays';\nimport StringBuilder from '../../util/StringBuilder';\nimport Integer from '../../util/Integer';\nimport Long from '../../util/Long';\nimport ByteArrayOutputStream from '../../util/ByteArrayOutputStream';\nimport StringEncoding from '../../util/StringEncoding';\n/*private*/\nvar Mode;\n(function (Mode) {\n  Mode[Mode[\"ALPHA\"] = 0] = \"ALPHA\";\n  Mode[Mode[\"LOWER\"] = 1] = \"LOWER\";\n  Mode[Mode[\"MIXED\"] = 2] = \"MIXED\";\n  Mode[Mode[\"PUNCT\"] = 3] = \"PUNCT\";\n  Mode[Mode[\"ALPHA_SHIFT\"] = 4] = \"ALPHA_SHIFT\";\n  Mode[Mode[\"PUNCT_SHIFT\"] = 5] = \"PUNCT_SHIFT\";\n})(Mode || (Mode = {}));\n/**\n * Indirectly access the global BigInt constructor, it\n * allows browsers that doesn't support BigInt to run\n * the library without breaking due to \"undefined BigInt\"\n * errors.\n */\nfunction getBigIntConstructor() {\n  if (typeof window !== 'undefined') {\n    return window['BigInt'] || null;\n  }\n  if (typeof global !== 'undefined') {\n    return global['BigInt'] || null;\n  }\n  if (typeof self !== 'undefined') {\n    return self['BigInt'] || null;\n  }\n  throw new Error('Can\\'t search globals for BigInt!');\n}\n/**\n * Used to store the BigInt constructor.\n */\nvar BigInteger;\n/**\n * This function creates a bigint value. It allows browsers\n * that doesn't support BigInt to run the rest of the library\n * by not directly accessing the BigInt constructor.\n */\nfunction createBigInt(num) {\n  if (typeof BigInteger === 'undefined') {\n    BigInteger = getBigIntConstructor();\n  }\n  if (BigInteger === null) {\n    throw new Error('BigInt is not supported!');\n  }\n  return BigInteger(num);\n}\nfunction getEXP900() {\n  // in Java - array with length = 16\n  var EXP900 = [];\n  EXP900[0] = createBigInt(1);\n  var nineHundred = createBigInt(900);\n  EXP900[1] = nineHundred;\n  // in Java - array with length = 16\n  for (var i /*int*/ = 2; i < 16; i++) {\n    EXP900[i] = EXP900[i - 1] * nineHundred;\n  }\n  return EXP900;\n}\n/**\n * <p>This class contains the methods for decoding the PDF417 codewords.</p>\n *\n * <AUTHOR> Lab (<EMAIL>)\n * <AUTHOR> Grau\n */\nvar DecodedBitStreamParser = /** @class */function () {\n  function DecodedBitStreamParser() {}\n  //   private DecodedBitStreamParser() {\n  // }\n  /**\n   *\n   * @param codewords\n   * @param ecLevel\n   *\n   * @throws FormatException\n   */\n  DecodedBitStreamParser.decode = function (codewords, ecLevel) {\n    // pass encoding to result (will be used for decode symbols in byte mode)\n    var result = new StringBuilder('');\n    // let encoding: Charset = StandardCharsets.ISO_8859_1;\n    var encoding = CharacterSetECI.ISO8859_1;\n    /**\n     * @note the next command is specific from this TypeScript library\n     * because TS can't properly cast some values to char and\n     * convert it to string later correctly due to encoding\n     * differences from Java version. As reported here:\n     * https://github.com/zxing-js/library/pull/264/files#r382831593\n     */\n    result.enableDecoding(encoding);\n    // Get compaction mode\n    var codeIndex = 1;\n    var code = codewords[codeIndex++];\n    var resultMetadata = new PDF417ResultMetadata();\n    while (codeIndex < codewords[0]) {\n      switch (code) {\n        case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n          codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, result);\n          break;\n        case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n        case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n          codeIndex = DecodedBitStreamParser.byteCompaction(code, codewords, encoding, codeIndex, result);\n          break;\n        case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n          result.append(/*(char)*/codewords[codeIndex++]);\n          break;\n        case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n          codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex, result);\n          break;\n        case DecodedBitStreamParser.ECI_CHARSET:\n          var charsetECI = CharacterSetECI.getCharacterSetECIByValue(codewords[codeIndex++]);\n          // encoding = Charset.forName(charsetECI.getName());\n          break;\n        case DecodedBitStreamParser.ECI_GENERAL_PURPOSE:\n          // Can't do anything with generic ECI; skip its 2 characters\n          codeIndex += 2;\n          break;\n        case DecodedBitStreamParser.ECI_USER_DEFINED:\n          // Can't do anything with user ECI; skip its 1 character\n          codeIndex++;\n          break;\n        case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n          codeIndex = DecodedBitStreamParser.decodeMacroBlock(codewords, codeIndex, resultMetadata);\n          break;\n        case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n        case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n          // Should not see these outside a macro block\n          throw new FormatException();\n        default:\n          // Default to text compaction. During testing numerous barcodes\n          // appeared to be missing the starting mode. In these cases defaulting\n          // to text compaction seems to work.\n          codeIndex--;\n          codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, result);\n          break;\n      }\n      if (codeIndex < codewords.length) {\n        code = codewords[codeIndex++];\n      } else {\n        throw FormatException.getFormatInstance();\n      }\n    }\n    if (result.length() === 0) {\n      throw FormatException.getFormatInstance();\n    }\n    var decoderResult = new DecoderResult(null, result.toString(), null, ecLevel);\n    decoderResult.setOther(resultMetadata);\n    return decoderResult;\n  };\n  /**\n   *\n   * @param int\n   * @param param1\n   * @param codewords\n   * @param int\n   * @param codeIndex\n   * @param PDF417ResultMetadata\n   * @param resultMetadata\n   *\n   * @throws FormatException\n   */\n  // @SuppressWarnings(\"deprecation\")\n  DecodedBitStreamParser.decodeMacroBlock = function (codewords, codeIndex, resultMetadata) {\n    if (codeIndex + DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS > codewords[0]) {\n      // we must have at least two bytes left for the segment index\n      throw FormatException.getFormatInstance();\n    }\n    var segmentIndexArray = new Int32Array(DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS);\n    for (var i /*int*/ = 0; i < DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS; i++, codeIndex++) {\n      segmentIndexArray[i] = codewords[codeIndex];\n    }\n    resultMetadata.setSegmentIndex(Integer.parseInt(DecodedBitStreamParser.decodeBase900toBase10(segmentIndexArray, DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS)));\n    var fileId = new StringBuilder();\n    codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, fileId);\n    resultMetadata.setFileId(fileId.toString());\n    var optionalFieldsStart = -1;\n    if (codewords[codeIndex] === DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD) {\n      optionalFieldsStart = codeIndex + 1;\n    }\n    while (codeIndex < codewords[0]) {\n      switch (codewords[codeIndex]) {\n        case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n          codeIndex++;\n          switch (codewords[codeIndex]) {\n            case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:\n              var fileName = new StringBuilder();\n              codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, fileName);\n              resultMetadata.setFileName(fileName.toString());\n              break;\n            case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SENDER:\n              var sender = new StringBuilder();\n              codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, sender);\n              resultMetadata.setSender(sender.toString());\n              break;\n            case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:\n              var addressee = new StringBuilder();\n              codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, addressee);\n              resultMetadata.setAddressee(addressee.toString());\n              break;\n            case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:\n              var segmentCount = new StringBuilder();\n              codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, segmentCount);\n              resultMetadata.setSegmentCount(Integer.parseInt(segmentCount.toString()));\n              break;\n            case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:\n              var timestamp = new StringBuilder();\n              codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, timestamp);\n              resultMetadata.setTimestamp(Long.parseLong(timestamp.toString()));\n              break;\n            case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:\n              var checksum = new StringBuilder();\n              codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, checksum);\n              resultMetadata.setChecksum(Integer.parseInt(checksum.toString()));\n              break;\n            case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:\n              var fileSize = new StringBuilder();\n              codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, fileSize);\n              resultMetadata.setFileSize(Long.parseLong(fileSize.toString()));\n              break;\n            default:\n              throw FormatException.getFormatInstance();\n          }\n          break;\n        case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n          codeIndex++;\n          resultMetadata.setLastSegment(true);\n          break;\n        default:\n          throw FormatException.getFormatInstance();\n      }\n    }\n    // copy optional fields to additional options\n    if (optionalFieldsStart !== -1) {\n      var optionalFieldsLength = codeIndex - optionalFieldsStart;\n      if (resultMetadata.isLastSegment()) {\n        // do not include terminator\n        optionalFieldsLength--;\n      }\n      resultMetadata.setOptionalData(Arrays.copyOfRange(codewords, optionalFieldsStart, optionalFieldsStart + optionalFieldsLength));\n    }\n    return codeIndex;\n  };\n  /**\n   * Text Compaction mode (see 5.4.1.5) permits all printable ASCII characters to be\n   * encoded, i.e. values 32 - 126 inclusive in accordance with ISO/IEC 646 (IRV), as\n   * well as selected control characters.\n   *\n   * @param codewords The array of codewords (data + error)\n   * @param codeIndex The current index into the codeword array.\n   * @param result    The decoded data is appended to the result.\n   * @return The next index into the codeword array.\n   */\n  DecodedBitStreamParser.textCompaction = function (codewords, codeIndex, result) {\n    // 2 character per codeword\n    var textCompactionData = new Int32Array((codewords[0] - codeIndex) * 2);\n    // Used to hold the byte compaction value if there is a mode shift\n    var byteCompactionData = new Int32Array((codewords[0] - codeIndex) * 2);\n    var index = 0;\n    var end = false;\n    while (codeIndex < codewords[0] && !end) {\n      var code = codewords[codeIndex++];\n      if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n        textCompactionData[index] = code / 30;\n        textCompactionData[index + 1] = code % 30;\n        index += 2;\n      } else {\n        switch (code) {\n          case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n            // reinitialize text compaction mode to alpha sub mode\n            textCompactionData[index++] = DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH;\n            break;\n          case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n          case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n          case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n          case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n          case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n          case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n            codeIndex--;\n            end = true;\n            break;\n          case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n            // The Mode Shift codeword 913 shall cause a temporary\n            // switch from Text Compaction mode to Byte Compaction mode.\n            // This switch shall be in effect for only the next codeword,\n            // after which the mode shall revert to the prevailing sub-mode\n            // of the Text Compaction mode. Codeword 913 is only available\n            // in Text Compaction mode; its use is described in 5.4.2.4.\n            textCompactionData[index] = DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE;\n            code = codewords[codeIndex++];\n            byteCompactionData[index] = code;\n            index++;\n            break;\n        }\n      }\n    }\n    DecodedBitStreamParser.decodeTextCompaction(textCompactionData, byteCompactionData, index, result);\n    return codeIndex;\n  };\n  /**\n   * The Text Compaction mode includes all the printable ASCII characters\n   * (i.e. values from 32 to 126) and three ASCII control characters: HT or tab\n   * (9: e), LF or line feed (10: e), and CR or carriage\n   * return (13: e). The Text Compaction mode also includes various latch\n   * and shift characters which are used exclusively within the mode. The Text\n   * Compaction mode encodes up to 2 characters per codeword. The compaction rules\n   * for converting data into PDF417 codewords are defined in 5.4.2.2. The sub-mode\n   * switches are defined in 5.4.2.3.\n   *\n   * @param textCompactionData The text compaction data.\n   * @param byteCompactionData The byte compaction data if there\n   *                           was a mode shift.\n   * @param length             The size of the text compaction and byte compaction data.\n   * @param result             The decoded data is appended to the result.\n   */\n  DecodedBitStreamParser.decodeTextCompaction = function (textCompactionData, byteCompactionData, length, result) {\n    // Beginning from an initial state of the Alpha sub-mode\n    // The default compaction mode for PDF417 in effect at the start of each symbol shall always be Text\n    // Compaction mode Alpha sub-mode (alphabetic: uppercase). A latch codeword from another mode to the Text\n    // Compaction mode shall always switch to the Text Compaction Alpha sub-mode.\n    var subMode = Mode.ALPHA;\n    var priorToShiftMode = Mode.ALPHA;\n    var i = 0;\n    while (i < length) {\n      var subModeCh = textCompactionData[i];\n      var ch = /*char*/'';\n      switch (subMode) {\n        case Mode.ALPHA:\n          // Alpha (alphabetic: uppercase)\n          if (subModeCh < 26) {\n            // Upper case Alpha Character\n            // Note: 65 = 'A' ASCII -> there is byte code of symbol\n            ch = /*(char)('A' + subModeCh) */String.fromCharCode(65 + subModeCh);\n          } else {\n            switch (subModeCh) {\n              case 26:\n                ch = ' ';\n                break;\n              case DecodedBitStreamParser.LL:\n                subMode = Mode.LOWER;\n                break;\n              case DecodedBitStreamParser.ML:\n                subMode = Mode.MIXED;\n                break;\n              case DecodedBitStreamParser.PS:\n                // Shift to punctuation\n                priorToShiftMode = subMode;\n                subMode = Mode.PUNCT_SHIFT;\n                break;\n              case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                result.append(/*(char)*/byteCompactionData[i]);\n                break;\n              case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                subMode = Mode.ALPHA;\n                break;\n            }\n          }\n          break;\n        case Mode.LOWER:\n          // Lower (alphabetic: lowercase)\n          if (subModeCh < 26) {\n            ch = /*(char)('a' + subModeCh)*/String.fromCharCode(97 + subModeCh);\n          } else {\n            switch (subModeCh) {\n              case 26:\n                ch = ' ';\n                break;\n              case DecodedBitStreamParser.AS:\n                // Shift to alpha\n                priorToShiftMode = subMode;\n                subMode = Mode.ALPHA_SHIFT;\n                break;\n              case DecodedBitStreamParser.ML:\n                subMode = Mode.MIXED;\n                break;\n              case DecodedBitStreamParser.PS:\n                // Shift to punctuation\n                priorToShiftMode = subMode;\n                subMode = Mode.PUNCT_SHIFT;\n                break;\n              case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                // TODO Does this need to use the current character encoding? See other occurrences below\n                result.append(/*(char)*/byteCompactionData[i]);\n                break;\n              case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                subMode = Mode.ALPHA;\n                break;\n            }\n          }\n          break;\n        case Mode.MIXED:\n          // Mixed (punctuation: e)\n          if (subModeCh < DecodedBitStreamParser.PL) {\n            ch = DecodedBitStreamParser.MIXED_CHARS[subModeCh];\n          } else {\n            switch (subModeCh) {\n              case DecodedBitStreamParser.PL:\n                subMode = Mode.PUNCT;\n                break;\n              case 26:\n                ch = ' ';\n                break;\n              case DecodedBitStreamParser.LL:\n                subMode = Mode.LOWER;\n                break;\n              case DecodedBitStreamParser.AL:\n                subMode = Mode.ALPHA;\n                break;\n              case DecodedBitStreamParser.PS:\n                // Shift to punctuation\n                priorToShiftMode = subMode;\n                subMode = Mode.PUNCT_SHIFT;\n                break;\n              case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                result.append(/*(char)*/byteCompactionData[i]);\n                break;\n              case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                subMode = Mode.ALPHA;\n                break;\n            }\n          }\n          break;\n        case Mode.PUNCT:\n          // Punctuation\n          if (subModeCh < DecodedBitStreamParser.PAL) {\n            ch = DecodedBitStreamParser.PUNCT_CHARS[subModeCh];\n          } else {\n            switch (subModeCh) {\n              case DecodedBitStreamParser.PAL:\n                subMode = Mode.ALPHA;\n                break;\n              case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                result.append(/*(char)*/byteCompactionData[i]);\n                break;\n              case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                subMode = Mode.ALPHA;\n                break;\n            }\n          }\n          break;\n        case Mode.ALPHA_SHIFT:\n          // Restore sub-mode\n          subMode = priorToShiftMode;\n          if (subModeCh < 26) {\n            ch = /*(char)('A' + subModeCh)*/String.fromCharCode(65 + subModeCh);\n          } else {\n            switch (subModeCh) {\n              case 26:\n                ch = ' ';\n                break;\n              case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                subMode = Mode.ALPHA;\n                break;\n            }\n          }\n          break;\n        case Mode.PUNCT_SHIFT:\n          // Restore sub-mode\n          subMode = priorToShiftMode;\n          if (subModeCh < DecodedBitStreamParser.PAL) {\n            ch = DecodedBitStreamParser.PUNCT_CHARS[subModeCh];\n          } else {\n            switch (subModeCh) {\n              case DecodedBitStreamParser.PAL:\n                subMode = Mode.ALPHA;\n                break;\n              case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                // PS before Shift-to-Byte is used as a padding character,\n                // see 5.4.2.4 of the specification\n                result.append(/*(char)*/byteCompactionData[i]);\n                break;\n              case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                subMode = Mode.ALPHA;\n                break;\n            }\n          }\n          break;\n      }\n      // if (ch !== 0) {\n      if (ch !== '') {\n        // Append decoded character to result\n        result.append(ch);\n      }\n      i++;\n    }\n  };\n  /**\n   * Byte Compaction mode (see 5.4.3) permits all 256 possible 8-bit byte values to be encoded.\n   * This includes all ASCII characters value 0 to 127 inclusive and provides for international\n   * character set support.\n   *\n   * @param mode      The byte compaction mode i.e. 901 or 924\n   * @param codewords The array of codewords (data + error)\n   * @param encoding  Currently active character encoding\n   * @param codeIndex The current index into the codeword array.\n   * @param result    The decoded data is appended to the result.\n   * @return The next index into the codeword array.\n   */\n  DecodedBitStreamParser.byteCompaction = function (mode, codewords, encoding, codeIndex, result) {\n    var decodedBytes = new ByteArrayOutputStream();\n    var count = 0;\n    var value = /*long*/0;\n    var end = false;\n    switch (mode) {\n      case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n        // Total number of Byte Compaction characters to be encoded\n        // is not a multiple of 6\n        var byteCompactedCodewords = new Int32Array(6);\n        var nextCode = codewords[codeIndex++];\n        while (codeIndex < codewords[0] && !end) {\n          byteCompactedCodewords[count++] = nextCode;\n          // Base 900\n          value = 900 * value + nextCode;\n          nextCode = codewords[codeIndex++];\n          // perhaps it should be ok to check only nextCode >= TEXT_COMPACTION_MODE_LATCH\n          switch (nextCode) {\n            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n            case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n            case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n            case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n            case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n              codeIndex--;\n              end = true;\n              break;\n            default:\n              if (count % 5 === 0 && count > 0) {\n                // Decode every 5 codewords\n                // Convert to Base 256\n                for (var j /*int*/ = 0; j < 6; ++j) {\n                  /* @note\n                   * JavaScript stores numbers as 64 bits floating point numbers, but all bitwise operations are performed on 32 bits binary numbers.\n                   * So the next bitwise operation could not be done with simple numbers\n                   */\n                  decodedBytes.write(/*(byte)*/Number(createBigInt(value) >> createBigInt(8 * (5 - j))));\n                }\n                value = 0;\n                count = 0;\n              }\n              break;\n          }\n        }\n        // if the end of all codewords is reached the last codeword needs to be added\n        if (codeIndex === codewords[0] && nextCode < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n          byteCompactedCodewords[count++] = nextCode;\n        }\n        // If Byte Compaction mode is invoked with codeword 901,\n        // the last group of codewords is interpreted directly\n        // as one byte per codeword, without compaction.\n        for (var i /*int*/ = 0; i < count; i++) {\n          decodedBytes.write(/*(byte)*/byteCompactedCodewords[i]);\n        }\n        break;\n      case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n        // Total number of Byte Compaction characters to be encoded\n        // is an integer multiple of 6\n        while (codeIndex < codewords[0] && !end) {\n          var code = codewords[codeIndex++];\n          if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n            count++;\n            // Base 900\n            value = 900 * value + code;\n          } else {\n            switch (code) {\n              case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n              case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n              case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n              case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n              case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n              case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n              case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                codeIndex--;\n                end = true;\n                break;\n            }\n          }\n          if (count % 5 === 0 && count > 0) {\n            // Decode every 5 codewords\n            // Convert to Base 256\n            /* @note\n             * JavaScript stores numbers as 64 bits floating point numbers, but all bitwise operations are performed on 32 bits binary numbers.\n             * So the next bitwise operation could not be done with simple numbers\n            */\n            for (var j /*int*/ = 0; j < 6; ++j) {\n              decodedBytes.write(/*(byte)*/Number(createBigInt(value) >> createBigInt(8 * (5 - j))));\n            }\n            value = 0;\n            count = 0;\n          }\n        }\n        break;\n    }\n    result.append(StringEncoding.decode(decodedBytes.toByteArray(), encoding));\n    return codeIndex;\n  };\n  /**\n   * Numeric Compaction mode (see 5.4.4) permits efficient encoding of numeric data strings.\n   *\n   * @param codewords The array of codewords (data + error)\n   * @param codeIndex The current index into the codeword array.\n   * @param result    The decoded data is appended to the result.\n   * @return The next index into the codeword array.\n   *\n   * @throws FormatException\n   */\n  DecodedBitStreamParser.numericCompaction = function (codewords, codeIndex /*int*/, result) {\n    var count = 0;\n    var end = false;\n    var numericCodewords = new Int32Array(DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS);\n    while (codeIndex < codewords[0] && !end) {\n      var code = codewords[codeIndex++];\n      if (codeIndex === codewords[0]) {\n        end = true;\n      }\n      if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n        numericCodewords[count] = code;\n        count++;\n      } else {\n        switch (code) {\n          case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n          case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n          case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n          case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n          case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n          case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n            codeIndex--;\n            end = true;\n            break;\n        }\n      }\n      if ((count % DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS === 0 || code === DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH || end) && count > 0) {\n        // Re-invoking Numeric Compaction mode (by using codeword 902\n        // while in Numeric Compaction mode) serves  to terminate the\n        // current Numeric Compaction mode grouping as described in 5.4.4.2,\n        // and then to start a new one grouping.\n        result.append(DecodedBitStreamParser.decodeBase900toBase10(numericCodewords, count));\n        count = 0;\n      }\n    }\n    return codeIndex;\n  };\n  /**\n   * Convert a list of Numeric Compacted codewords from Base 900 to Base 10.\n   *\n   * @param codewords The array of codewords\n   * @param count     The number of codewords\n   * @return The decoded string representing the Numeric data.\n   *\n   * EXAMPLE\n   * Encode the fifteen digit numeric string 000213298174000\n   * Prefix the numeric string with a 1 and set the initial value of\n   * t = 1 000 213 298 174 000\n   * Calculate codeword 0\n   * d0 = 1 000 213 298 174 000 mod 900 = 200\n   *\n   * t = 1 000 213 298 174 000 div 900 = 1 111 348 109 082\n   * Calculate codeword 1\n   * d1 = 1 111 348 109 082 mod 900 = 282\n   *\n   * t = 1 111 348 109 082 div 900 = 1 234 831 232\n   * Calculate codeword 2\n   * d2 = 1 234 831 232 mod 900 = 632\n   *\n   * t = 1 234 831 232 div 900 = 1 372 034\n   * Calculate codeword 3\n   * d3 = 1 372 034 mod 900 = 434\n   *\n   * t = 1 372 034 div 900 = 1 524\n   * Calculate codeword 4\n   * d4 = 1 524 mod 900 = 624\n   *\n   * t = 1 524 div 900 = 1\n   * Calculate codeword 5\n   * d5 = 1 mod 900 = 1\n   * t = 1 div 900 = 0\n   * Codeword sequence is: 1, 624, 434, 632, 282, 200\n   *\n   * Decode the above codewords involves\n   *   1 x 900 power of 5 + 624 x 900 power of 4 + 434 x 900 power of 3 +\n   * 632 x 900 power of 2 + 282 x 900 power of 1 + 200 x 900 power of 0 = 1000213298174000\n   *\n   * Remove leading 1 =>  Result is 000213298174000\n   *\n   * @throws FormatException\n   */\n  DecodedBitStreamParser.decodeBase900toBase10 = function (codewords, count) {\n    var result = createBigInt(0);\n    for (var i /*int*/ = 0; i < count; i++) {\n      result += DecodedBitStreamParser.EXP900[count - i - 1] * createBigInt(codewords[i]);\n    }\n    var resultString = result.toString();\n    if (resultString.charAt(0) !== '1') {\n      throw new FormatException();\n    }\n    return resultString.substring(1);\n  };\n  DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH = 900;\n  DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH = 901;\n  DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH = 902;\n  DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6 = 924;\n  DecodedBitStreamParser.ECI_USER_DEFINED = 925;\n  DecodedBitStreamParser.ECI_GENERAL_PURPOSE = 926;\n  DecodedBitStreamParser.ECI_CHARSET = 927;\n  DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK = 928;\n  DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD = 923;\n  DecodedBitStreamParser.MACRO_PDF417_TERMINATOR = 922;\n  DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE = 913;\n  DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS = 15;\n  DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME = 0;\n  DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT = 1;\n  DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP = 2;\n  DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SENDER = 3;\n  DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE = 4;\n  DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE = 5;\n  DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM = 6;\n  DecodedBitStreamParser.PL = 25;\n  DecodedBitStreamParser.LL = 27;\n  DecodedBitStreamParser.AS = 27;\n  DecodedBitStreamParser.ML = 28;\n  DecodedBitStreamParser.AL = 28;\n  DecodedBitStreamParser.PS = 29;\n  DecodedBitStreamParser.PAL = 29;\n  DecodedBitStreamParser.PUNCT_CHARS = ';<>@[\\\\]_`~!\\r\\t,:\\n-.$/\"|*()?{}\\'';\n  DecodedBitStreamParser.MIXED_CHARS = '0123456789&\\r\\t,:#-.$/+%*=^';\n  /**\n   * Table containing values for the exponent of 900.\n   * This is used in the numeric compaction decode algorithm.\n   */\n  DecodedBitStreamParser.EXP900 = getBigIntConstructor() ? getEXP900() : [];\n  DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS = 2;\n  return DecodedBitStreamParser;\n}();\nexport default DecodedBitStreamParser;", "map": {"version": 3, "names": ["FormatException", "CharacterSetECI", "DecoderResult", "PDF417ResultMetadata", "<PERSON><PERSON><PERSON>", "StringBuilder", "Integer", "<PERSON>", "ByteArrayOutputStream", "StringEncoding", "Mode", "getBigIntConstructor", "window", "global", "self", "Error", "BigInteger", "createBigInt", "num", "getEXP900", "EXP900", "nine<PERSON><PERSON><PERSON>", "i", "DecodedBitStreamParser", "decode", "codewords", "ecLevel", "result", "encoding", "ISO8859_1", "enableDecoding", "codeIndex", "code", "resultMetadata", "TEXT_COMPACTION_MODE_LATCH", "textCompaction", "BYTE_COMPACTION_MODE_LATCH", "BYTE_COMPACTION_MODE_LATCH_6", "byteCompaction", "MODE_SHIFT_TO_BYTE_COMPACTION_MODE", "append", "NUMERIC_COMPACTION_MODE_LATCH", "numericCompaction", "ECI_CHARSET", "charsetECI", "getCharacterSetECIByValue", "ECI_GENERAL_PURPOSE", "ECI_USER_DEFINED", "BEGIN_MACRO_PDF417_CONTROL_BLOCK", "decodeMacroBlock", "BEGIN_MACRO_PDF417_OPTIONAL_FIELD", "MACRO_PDF417_TERMINATOR", "length", "getFormatInstance", "decoderResult", "toString", "setOther", "NUMBER_OF_SEQUENCE_CODEWORDS", "segmentIndexArray", "Int32Array", "setSegmentIndex", "parseInt", "decodeBase900toBase10", "fileId", "setFileId", "optionalFieldsStart", "MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME", "fileName", "setFileName", "MACRO_PDF417_OPTIONAL_FIELD_SENDER", "sender", "setSender", "MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE", "addressee", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT", "segmentCount", "setSegmentCount", "MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP", "timestamp", "setTimestamp", "parseLong", "MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM", "checksum", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE", "fileSize", "setFileSize", "setLastSegment", "optionalField<PERSON><PERSON><PERSON><PERSON>", "isLastSegment", "setOptionalData", "copyOfRange", "textCompactionData", "byteCompactionData", "index", "end", "decodeTextCompaction", "subMode", "ALPHA", "priorToShiftMode", "subModeCh", "ch", "String", "fromCharCode", "LL", "LOWER", "ML", "MIXED", "PS", "PUNCT_SHIFT", "AS", "ALPHA_SHIFT", "PL", "MIXED_CHARS", "PUNCT", "AL", "PAL", "PUNCT_CHARS", "mode", "decodedBytes", "count", "value", "byteCompactedCodewords", "nextCode", "j", "write", "Number", "toByteArray", "numericCodewords", "MAX_NUMERIC_CODEWORDS", "resultString", "char<PERSON>t", "substring"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/DecodedBitStreamParser.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.FormatException;\nimport FormatException from '../../FormatException';\n// import com.google.zxing.common.CharacterSetECI;\nimport CharacterSetECI from '../../common/CharacterSetECI';\n// import com.google.zxing.common.DecoderResult;\nimport DecoderResult from '../../common/DecoderResult';\n// import com.google.zxing.pdf417.PDF417ResultMetadata;\nimport PDF417ResultMetadata from '../PDF417ResultMetadata';\n// import java.io.ByteArrayOutputStream;\n// import java.math.BigInteger;\n// import java.nio.charset.Charset;\n// import java.nio.charset.StandardCharsets;\n// import java.util.Arrays;\nimport Arrays from '../../util/Arrays';\nimport StringBuilder from '../../util/StringBuilder';\nimport Integer from '../../util/Integer';\nimport Long from '../../util/Long';\nimport ByteArrayOutputStream from '../../util/ByteArrayOutputStream';\nimport StringEncoding from '../../util/StringEncoding';\n/*private*/ var Mode;\n(function (Mode) {\n    Mode[Mode[\"ALPHA\"] = 0] = \"ALPHA\";\n    Mode[Mode[\"LOWER\"] = 1] = \"LOWER\";\n    Mode[Mode[\"MIXED\"] = 2] = \"MIXED\";\n    Mode[Mode[\"PUNCT\"] = 3] = \"PUNCT\";\n    Mode[Mode[\"ALPHA_SHIFT\"] = 4] = \"ALPHA_SHIFT\";\n    Mode[Mode[\"PUNCT_SHIFT\"] = 5] = \"PUNCT_SHIFT\";\n})(Mode || (Mode = {}));\n/**\n * Indirectly access the global BigInt constructor, it\n * allows browsers that doesn't support BigInt to run\n * the library without breaking due to \"undefined BigInt\"\n * errors.\n */\nfunction getBigIntConstructor() {\n    if (typeof window !== 'undefined') {\n        return window['BigInt'] || null;\n    }\n    if (typeof global !== 'undefined') {\n        return global['BigInt'] || null;\n    }\n    if (typeof self !== 'undefined') {\n        return self['BigInt'] || null;\n    }\n    throw new Error('Can\\'t search globals for BigInt!');\n}\n/**\n * Used to store the BigInt constructor.\n */\nvar BigInteger;\n/**\n * This function creates a bigint value. It allows browsers\n * that doesn't support BigInt to run the rest of the library\n * by not directly accessing the BigInt constructor.\n */\nfunction createBigInt(num) {\n    if (typeof BigInteger === 'undefined') {\n        BigInteger = getBigIntConstructor();\n    }\n    if (BigInteger === null) {\n        throw new Error('BigInt is not supported!');\n    }\n    return BigInteger(num);\n}\nfunction getEXP900() {\n    // in Java - array with length = 16\n    var EXP900 = [];\n    EXP900[0] = createBigInt(1);\n    var nineHundred = createBigInt(900);\n    EXP900[1] = nineHundred;\n    // in Java - array with length = 16\n    for (var i /*int*/ = 2; i < 16; i++) {\n        EXP900[i] = EXP900[i - 1] * nineHundred;\n    }\n    return EXP900;\n}\n/**\n * <p>This class contains the methods for decoding the PDF417 codewords.</p>\n *\n * <AUTHOR> Lab (<EMAIL>)\n * <AUTHOR> Grau\n */\nvar DecodedBitStreamParser = /** @class */ (function () {\n    function DecodedBitStreamParser() {\n    }\n    //   private DecodedBitStreamParser() {\n    // }\n    /**\n     *\n     * @param codewords\n     * @param ecLevel\n     *\n     * @throws FormatException\n     */\n    DecodedBitStreamParser.decode = function (codewords, ecLevel) {\n        // pass encoding to result (will be used for decode symbols in byte mode)\n        var result = new StringBuilder('');\n        // let encoding: Charset = StandardCharsets.ISO_8859_1;\n        var encoding = CharacterSetECI.ISO8859_1;\n        /**\n         * @note the next command is specific from this TypeScript library\n         * because TS can't properly cast some values to char and\n         * convert it to string later correctly due to encoding\n         * differences from Java version. As reported here:\n         * https://github.com/zxing-js/library/pull/264/files#r382831593\n         */\n        result.enableDecoding(encoding);\n        // Get compaction mode\n        var codeIndex = 1;\n        var code = codewords[codeIndex++];\n        var resultMetadata = new PDF417ResultMetadata();\n        while (codeIndex < codewords[0]) {\n            switch (code) {\n                case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                    codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, result);\n                    break;\n                case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                    codeIndex = DecodedBitStreamParser.byteCompaction(code, codewords, encoding, codeIndex, result);\n                    break;\n                case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                    result.append(/*(char)*/ codewords[codeIndex++]);\n                    break;\n                case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n                    codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex, result);\n                    break;\n                case DecodedBitStreamParser.ECI_CHARSET:\n                    var charsetECI = CharacterSetECI.getCharacterSetECIByValue(codewords[codeIndex++]);\n                    // encoding = Charset.forName(charsetECI.getName());\n                    break;\n                case DecodedBitStreamParser.ECI_GENERAL_PURPOSE:\n                    // Can't do anything with generic ECI; skip its 2 characters\n                    codeIndex += 2;\n                    break;\n                case DecodedBitStreamParser.ECI_USER_DEFINED:\n                    // Can't do anything with user ECI; skip its 1 character\n                    codeIndex++;\n                    break;\n                case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                    codeIndex = DecodedBitStreamParser.decodeMacroBlock(codewords, codeIndex, resultMetadata);\n                    break;\n                case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                    // Should not see these outside a macro block\n                    throw new FormatException();\n                default:\n                    // Default to text compaction. During testing numerous barcodes\n                    // appeared to be missing the starting mode. In these cases defaulting\n                    // to text compaction seems to work.\n                    codeIndex--;\n                    codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, result);\n                    break;\n            }\n            if (codeIndex < codewords.length) {\n                code = codewords[codeIndex++];\n            }\n            else {\n                throw FormatException.getFormatInstance();\n            }\n        }\n        if (result.length() === 0) {\n            throw FormatException.getFormatInstance();\n        }\n        var decoderResult = new DecoderResult(null, result.toString(), null, ecLevel);\n        decoderResult.setOther(resultMetadata);\n        return decoderResult;\n    };\n    /**\n     *\n     * @param int\n     * @param param1\n     * @param codewords\n     * @param int\n     * @param codeIndex\n     * @param PDF417ResultMetadata\n     * @param resultMetadata\n     *\n     * @throws FormatException\n     */\n    // @SuppressWarnings(\"deprecation\")\n    DecodedBitStreamParser.decodeMacroBlock = function (codewords, codeIndex, resultMetadata) {\n        if (codeIndex + DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS > codewords[0]) {\n            // we must have at least two bytes left for the segment index\n            throw FormatException.getFormatInstance();\n        }\n        var segmentIndexArray = new Int32Array(DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS);\n        for (var i /*int*/ = 0; i < DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS; i++, codeIndex++) {\n            segmentIndexArray[i] = codewords[codeIndex];\n        }\n        resultMetadata.setSegmentIndex(Integer.parseInt(DecodedBitStreamParser.decodeBase900toBase10(segmentIndexArray, DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS)));\n        var fileId = new StringBuilder();\n        codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex, fileId);\n        resultMetadata.setFileId(fileId.toString());\n        var optionalFieldsStart = -1;\n        if (codewords[codeIndex] === DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD) {\n            optionalFieldsStart = codeIndex + 1;\n        }\n        while (codeIndex < codewords[0]) {\n            switch (codewords[codeIndex]) {\n                case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                    codeIndex++;\n                    switch (codewords[codeIndex]) {\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME:\n                            var fileName = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, fileName);\n                            resultMetadata.setFileName(fileName.toString());\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SENDER:\n                            var sender = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, sender);\n                            resultMetadata.setSender(sender.toString());\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE:\n                            var addressee = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.textCompaction(codewords, codeIndex + 1, addressee);\n                            resultMetadata.setAddressee(addressee.toString());\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT:\n                            var segmentCount = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, segmentCount);\n                            resultMetadata.setSegmentCount(Integer.parseInt(segmentCount.toString()));\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP:\n                            var timestamp = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, timestamp);\n                            resultMetadata.setTimestamp(Long.parseLong(timestamp.toString()));\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM:\n                            var checksum = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, checksum);\n                            resultMetadata.setChecksum(Integer.parseInt(checksum.toString()));\n                            break;\n                        case DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE:\n                            var fileSize = new StringBuilder();\n                            codeIndex = DecodedBitStreamParser.numericCompaction(codewords, codeIndex + 1, fileSize);\n                            resultMetadata.setFileSize(Long.parseLong(fileSize.toString()));\n                            break;\n                        default:\n                            throw FormatException.getFormatInstance();\n                    }\n                    break;\n                case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                    codeIndex++;\n                    resultMetadata.setLastSegment(true);\n                    break;\n                default:\n                    throw FormatException.getFormatInstance();\n            }\n        }\n        // copy optional fields to additional options\n        if (optionalFieldsStart !== -1) {\n            var optionalFieldsLength = codeIndex - optionalFieldsStart;\n            if (resultMetadata.isLastSegment()) {\n                // do not include terminator\n                optionalFieldsLength--;\n            }\n            resultMetadata.setOptionalData(Arrays.copyOfRange(codewords, optionalFieldsStart, optionalFieldsStart + optionalFieldsLength));\n        }\n        return codeIndex;\n    };\n    /**\n     * Text Compaction mode (see 5.4.1.5) permits all printable ASCII characters to be\n     * encoded, i.e. values 32 - 126 inclusive in accordance with ISO/IEC 646 (IRV), as\n     * well as selected control characters.\n     *\n     * @param codewords The array of codewords (data + error)\n     * @param codeIndex The current index into the codeword array.\n     * @param result    The decoded data is appended to the result.\n     * @return The next index into the codeword array.\n     */\n    DecodedBitStreamParser.textCompaction = function (codewords, codeIndex, result) {\n        // 2 character per codeword\n        var textCompactionData = new Int32Array((codewords[0] - codeIndex) * 2);\n        // Used to hold the byte compaction value if there is a mode shift\n        var byteCompactionData = new Int32Array((codewords[0] - codeIndex) * 2);\n        var index = 0;\n        var end = false;\n        while ((codeIndex < codewords[0]) && !end) {\n            var code = codewords[codeIndex++];\n            if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n                textCompactionData[index] = code / 30;\n                textCompactionData[index + 1] = code % 30;\n                index += 2;\n            }\n            else {\n                switch (code) {\n                    case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                        // reinitialize text compaction mode to alpha sub mode\n                        textCompactionData[index++] = DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH;\n                        break;\n                    case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                    case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                    case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n                    case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                    case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                    case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                        codeIndex--;\n                        end = true;\n                        break;\n                    case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                        // The Mode Shift codeword 913 shall cause a temporary\n                        // switch from Text Compaction mode to Byte Compaction mode.\n                        // This switch shall be in effect for only the next codeword,\n                        // after which the mode shall revert to the prevailing sub-mode\n                        // of the Text Compaction mode. Codeword 913 is only available\n                        // in Text Compaction mode; its use is described in 5.4.2.4.\n                        textCompactionData[index] = DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE;\n                        code = codewords[codeIndex++];\n                        byteCompactionData[index] = code;\n                        index++;\n                        break;\n                }\n            }\n        }\n        DecodedBitStreamParser.decodeTextCompaction(textCompactionData, byteCompactionData, index, result);\n        return codeIndex;\n    };\n    /**\n     * The Text Compaction mode includes all the printable ASCII characters\n     * (i.e. values from 32 to 126) and three ASCII control characters: HT or tab\n     * (9: e), LF or line feed (10: e), and CR or carriage\n     * return (13: e). The Text Compaction mode also includes various latch\n     * and shift characters which are used exclusively within the mode. The Text\n     * Compaction mode encodes up to 2 characters per codeword. The compaction rules\n     * for converting data into PDF417 codewords are defined in 5.4.2.2. The sub-mode\n     * switches are defined in 5.4.2.3.\n     *\n     * @param textCompactionData The text compaction data.\n     * @param byteCompactionData The byte compaction data if there\n     *                           was a mode shift.\n     * @param length             The size of the text compaction and byte compaction data.\n     * @param result             The decoded data is appended to the result.\n     */\n    DecodedBitStreamParser.decodeTextCompaction = function (textCompactionData, byteCompactionData, length, result) {\n        // Beginning from an initial state of the Alpha sub-mode\n        // The default compaction mode for PDF417 in effect at the start of each symbol shall always be Text\n        // Compaction mode Alpha sub-mode (alphabetic: uppercase). A latch codeword from another mode to the Text\n        // Compaction mode shall always switch to the Text Compaction Alpha sub-mode.\n        var subMode = Mode.ALPHA;\n        var priorToShiftMode = Mode.ALPHA;\n        var i = 0;\n        while (i < length) {\n            var subModeCh = textCompactionData[i];\n            var ch = /*char*/ '';\n            switch (subMode) {\n                case Mode.ALPHA:\n                    // Alpha (alphabetic: uppercase)\n                    if (subModeCh < 26) {\n                        // Upper case Alpha Character\n                        // Note: 65 = 'A' ASCII -> there is byte code of symbol\n                        ch = /*(char)('A' + subModeCh) */ String.fromCharCode(65 + subModeCh);\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case 26:\n                                ch = ' ';\n                                break;\n                            case DecodedBitStreamParser.LL:\n                                subMode = Mode.LOWER;\n                                break;\n                            case DecodedBitStreamParser.ML:\n                                subMode = Mode.MIXED;\n                                break;\n                            case DecodedBitStreamParser.PS:\n                                // Shift to punctuation\n                                priorToShiftMode = subMode;\n                                subMode = Mode.PUNCT_SHIFT;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.LOWER:\n                    // Lower (alphabetic: lowercase)\n                    if (subModeCh < 26) {\n                        ch = /*(char)('a' + subModeCh)*/ String.fromCharCode(97 + subModeCh);\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case 26:\n                                ch = ' ';\n                                break;\n                            case DecodedBitStreamParser.AS:\n                                // Shift to alpha\n                                priorToShiftMode = subMode;\n                                subMode = Mode.ALPHA_SHIFT;\n                                break;\n                            case DecodedBitStreamParser.ML:\n                                subMode = Mode.MIXED;\n                                break;\n                            case DecodedBitStreamParser.PS:\n                                // Shift to punctuation\n                                priorToShiftMode = subMode;\n                                subMode = Mode.PUNCT_SHIFT;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                // TODO Does this need to use the current character encoding? See other occurrences below\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.MIXED:\n                    // Mixed (punctuation: e)\n                    if (subModeCh < DecodedBitStreamParser.PL) {\n                        ch = DecodedBitStreamParser.MIXED_CHARS[subModeCh];\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case DecodedBitStreamParser.PL:\n                                subMode = Mode.PUNCT;\n                                break;\n                            case 26:\n                                ch = ' ';\n                                break;\n                            case DecodedBitStreamParser.LL:\n                                subMode = Mode.LOWER;\n                                break;\n                            case DecodedBitStreamParser.AL:\n                                subMode = Mode.ALPHA;\n                                break;\n                            case DecodedBitStreamParser.PS:\n                                // Shift to punctuation\n                                priorToShiftMode = subMode;\n                                subMode = Mode.PUNCT_SHIFT;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.PUNCT:\n                    // Punctuation\n                    if (subModeCh < DecodedBitStreamParser.PAL) {\n                        ch = DecodedBitStreamParser.PUNCT_CHARS[subModeCh];\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case DecodedBitStreamParser.PAL:\n                                subMode = Mode.ALPHA;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.ALPHA_SHIFT:\n                    // Restore sub-mode\n                    subMode = priorToShiftMode;\n                    if (subModeCh < 26) {\n                        ch = /*(char)('A' + subModeCh)*/ String.fromCharCode(65 + subModeCh);\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case 26:\n                                ch = ' ';\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n                case Mode.PUNCT_SHIFT:\n                    // Restore sub-mode\n                    subMode = priorToShiftMode;\n                    if (subModeCh < DecodedBitStreamParser.PAL) {\n                        ch = DecodedBitStreamParser.PUNCT_CHARS[subModeCh];\n                    }\n                    else {\n                        switch (subModeCh) {\n                            case DecodedBitStreamParser.PAL:\n                                subMode = Mode.ALPHA;\n                                break;\n                            case DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE:\n                                // PS before Shift-to-Byte is used as a padding character,\n                                // see 5.4.2.4 of the specification\n                                result.append(/*(char)*/ byteCompactionData[i]);\n                                break;\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                                subMode = Mode.ALPHA;\n                                break;\n                        }\n                    }\n                    break;\n            }\n            // if (ch !== 0) {\n            if (ch !== '') {\n                // Append decoded character to result\n                result.append(ch);\n            }\n            i++;\n        }\n    };\n    /**\n     * Byte Compaction mode (see 5.4.3) permits all 256 possible 8-bit byte values to be encoded.\n     * This includes all ASCII characters value 0 to 127 inclusive and provides for international\n     * character set support.\n     *\n     * @param mode      The byte compaction mode i.e. 901 or 924\n     * @param codewords The array of codewords (data + error)\n     * @param encoding  Currently active character encoding\n     * @param codeIndex The current index into the codeword array.\n     * @param result    The decoded data is appended to the result.\n     * @return The next index into the codeword array.\n     */\n    DecodedBitStreamParser.byteCompaction = function (mode, codewords, encoding, codeIndex, result) {\n        var decodedBytes = new ByteArrayOutputStream();\n        var count = 0;\n        var value = /*long*/ 0;\n        var end = false;\n        switch (mode) {\n            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                // Total number of Byte Compaction characters to be encoded\n                // is not a multiple of 6\n                var byteCompactedCodewords = new Int32Array(6);\n                var nextCode = codewords[codeIndex++];\n                while ((codeIndex < codewords[0]) && !end) {\n                    byteCompactedCodewords[count++] = nextCode;\n                    // Base 900\n                    value = 900 * value + nextCode;\n                    nextCode = codewords[codeIndex++];\n                    // perhaps it should be ok to check only nextCode >= TEXT_COMPACTION_MODE_LATCH\n                    switch (nextCode) {\n                        case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                        case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                        case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n                        case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                        case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                        case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                        case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                            codeIndex--;\n                            end = true;\n                            break;\n                        default:\n                            if ((count % 5 === 0) && (count > 0)) {\n                                // Decode every 5 codewords\n                                // Convert to Base 256\n                                for (var j /*int*/ = 0; j < 6; ++j) {\n                                    /* @note\n                                     * JavaScript stores numbers as 64 bits floating point numbers, but all bitwise operations are performed on 32 bits binary numbers.\n                                     * So the next bitwise operation could not be done with simple numbers\n                                     */\n                                    decodedBytes.write(/*(byte)*/ Number(createBigInt(value) >> createBigInt(8 * (5 - j))));\n                                }\n                                value = 0;\n                                count = 0;\n                            }\n                            break;\n                    }\n                }\n                // if the end of all codewords is reached the last codeword needs to be added\n                if (codeIndex === codewords[0] && nextCode < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n                    byteCompactedCodewords[count++] = nextCode;\n                }\n                // If Byte Compaction mode is invoked with codeword 901,\n                // the last group of codewords is interpreted directly\n                // as one byte per codeword, without compaction.\n                for (var i /*int*/ = 0; i < count; i++) {\n                    decodedBytes.write(/*(byte)*/ byteCompactedCodewords[i]);\n                }\n                break;\n            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                // Total number of Byte Compaction characters to be encoded\n                // is an integer multiple of 6\n                while (codeIndex < codewords[0] && !end) {\n                    var code = codewords[codeIndex++];\n                    if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n                        count++;\n                        // Base 900\n                        value = 900 * value + code;\n                    }\n                    else {\n                        switch (code) {\n                            case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                            case DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH:\n                            case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                            case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                            case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                            case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                                codeIndex--;\n                                end = true;\n                                break;\n                        }\n                    }\n                    if ((count % 5 === 0) && (count > 0)) {\n                        // Decode every 5 codewords\n                        // Convert to Base 256\n                        /* @note\n                         * JavaScript stores numbers as 64 bits floating point numbers, but all bitwise operations are performed on 32 bits binary numbers.\n                         * So the next bitwise operation could not be done with simple numbers\n                        */\n                        for (var j /*int*/ = 0; j < 6; ++j) {\n                            decodedBytes.write(/*(byte)*/ Number(createBigInt(value) >> createBigInt(8 * (5 - j))));\n                        }\n                        value = 0;\n                        count = 0;\n                    }\n                }\n                break;\n        }\n        result.append(StringEncoding.decode(decodedBytes.toByteArray(), encoding));\n        return codeIndex;\n    };\n    /**\n     * Numeric Compaction mode (see 5.4.4) permits efficient encoding of numeric data strings.\n     *\n     * @param codewords The array of codewords (data + error)\n     * @param codeIndex The current index into the codeword array.\n     * @param result    The decoded data is appended to the result.\n     * @return The next index into the codeword array.\n     *\n     * @throws FormatException\n     */\n    DecodedBitStreamParser.numericCompaction = function (codewords, codeIndex /*int*/, result) {\n        var count = 0;\n        var end = false;\n        var numericCodewords = new Int32Array(DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS);\n        while (codeIndex < codewords[0] && !end) {\n            var code = codewords[codeIndex++];\n            if (codeIndex === codewords[0]) {\n                end = true;\n            }\n            if (code < DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH) {\n                numericCodewords[count] = code;\n                count++;\n            }\n            else {\n                switch (code) {\n                    case DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH:\n                    case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH:\n                    case DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6:\n                    case DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK:\n                    case DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD:\n                    case DecodedBitStreamParser.MACRO_PDF417_TERMINATOR:\n                        codeIndex--;\n                        end = true;\n                        break;\n                }\n            }\n            if ((count % DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS === 0 || code === DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH || end) && count > 0) {\n                // Re-invoking Numeric Compaction mode (by using codeword 902\n                // while in Numeric Compaction mode) serves  to terminate the\n                // current Numeric Compaction mode grouping as described in 5.4.4.2,\n                // and then to start a new one grouping.\n                result.append(DecodedBitStreamParser.decodeBase900toBase10(numericCodewords, count));\n                count = 0;\n            }\n        }\n        return codeIndex;\n    };\n    /**\n     * Convert a list of Numeric Compacted codewords from Base 900 to Base 10.\n     *\n     * @param codewords The array of codewords\n     * @param count     The number of codewords\n     * @return The decoded string representing the Numeric data.\n     *\n     * EXAMPLE\n     * Encode the fifteen digit numeric string 000213298174000\n     * Prefix the numeric string with a 1 and set the initial value of\n     * t = 1 000 213 298 174 000\n     * Calculate codeword 0\n     * d0 = 1 000 213 298 174 000 mod 900 = 200\n     *\n     * t = 1 000 213 298 174 000 div 900 = 1 111 348 109 082\n     * Calculate codeword 1\n     * d1 = 1 111 348 109 082 mod 900 = 282\n     *\n     * t = 1 111 348 109 082 div 900 = 1 234 831 232\n     * Calculate codeword 2\n     * d2 = 1 234 831 232 mod 900 = 632\n     *\n     * t = 1 234 831 232 div 900 = 1 372 034\n     * Calculate codeword 3\n     * d3 = 1 372 034 mod 900 = 434\n     *\n     * t = 1 372 034 div 900 = 1 524\n     * Calculate codeword 4\n     * d4 = 1 524 mod 900 = 624\n     *\n     * t = 1 524 div 900 = 1\n     * Calculate codeword 5\n     * d5 = 1 mod 900 = 1\n     * t = 1 div 900 = 0\n     * Codeword sequence is: 1, 624, 434, 632, 282, 200\n     *\n     * Decode the above codewords involves\n     *   1 x 900 power of 5 + 624 x 900 power of 4 + 434 x 900 power of 3 +\n     * 632 x 900 power of 2 + 282 x 900 power of 1 + 200 x 900 power of 0 = 1000213298174000\n     *\n     * Remove leading 1 =>  Result is 000213298174000\n     *\n     * @throws FormatException\n     */\n    DecodedBitStreamParser.decodeBase900toBase10 = function (codewords, count) {\n        var result = createBigInt(0);\n        for (var i /*int*/ = 0; i < count; i++) {\n            result += DecodedBitStreamParser.EXP900[count - i - 1] * createBigInt(codewords[i]);\n        }\n        var resultString = result.toString();\n        if (resultString.charAt(0) !== '1') {\n            throw new FormatException();\n        }\n        return resultString.substring(1);\n    };\n    DecodedBitStreamParser.TEXT_COMPACTION_MODE_LATCH = 900;\n    DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH = 901;\n    DecodedBitStreamParser.NUMERIC_COMPACTION_MODE_LATCH = 902;\n    DecodedBitStreamParser.BYTE_COMPACTION_MODE_LATCH_6 = 924;\n    DecodedBitStreamParser.ECI_USER_DEFINED = 925;\n    DecodedBitStreamParser.ECI_GENERAL_PURPOSE = 926;\n    DecodedBitStreamParser.ECI_CHARSET = 927;\n    DecodedBitStreamParser.BEGIN_MACRO_PDF417_CONTROL_BLOCK = 928;\n    DecodedBitStreamParser.BEGIN_MACRO_PDF417_OPTIONAL_FIELD = 923;\n    DecodedBitStreamParser.MACRO_PDF417_TERMINATOR = 922;\n    DecodedBitStreamParser.MODE_SHIFT_TO_BYTE_COMPACTION_MODE = 913;\n    DecodedBitStreamParser.MAX_NUMERIC_CODEWORDS = 15;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_NAME = 0;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SEGMENT_COUNT = 1;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_TIME_STAMP = 2;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_SENDER = 3;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_ADDRESSEE = 4;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_FILE_SIZE = 5;\n    DecodedBitStreamParser.MACRO_PDF417_OPTIONAL_FIELD_CHECKSUM = 6;\n    DecodedBitStreamParser.PL = 25;\n    DecodedBitStreamParser.LL = 27;\n    DecodedBitStreamParser.AS = 27;\n    DecodedBitStreamParser.ML = 28;\n    DecodedBitStreamParser.AL = 28;\n    DecodedBitStreamParser.PS = 29;\n    DecodedBitStreamParser.PAL = 29;\n    DecodedBitStreamParser.PUNCT_CHARS = ';<>@[\\\\]_`~!\\r\\t,:\\n-.$/\"|*()?{}\\'';\n    DecodedBitStreamParser.MIXED_CHARS = '0123456789&\\r\\t,:#-.$/+%*=^';\n    /**\n     * Table containing values for the exponent of 900.\n     * This is used in the numeric compaction decode algorithm.\n     */\n    DecodedBitStreamParser.EXP900 = getBigIntConstructor() ? getEXP900() : [];\n    DecodedBitStreamParser.NUMBER_OF_SEQUENCE_CODEWORDS = 2;\n    return DecodedBitStreamParser;\n}());\nexport default DecodedBitStreamParser;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,eAAe,MAAM,uBAAuB;AACnD;AACA,OAAOC,eAAe,MAAM,8BAA8B;AAC1D;AACA,OAAOC,aAAa,MAAM,4BAA4B;AACtD;AACA,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D;AACA;AACA;AACA;AACA;AACA,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,qBAAqB,MAAM,kCAAkC;AACpE,OAAOC,cAAc,MAAM,2BAA2B;AACtD;AAAY,IAAIC,IAAI;AACpB,CAAC,UAAUA,IAAI,EAAE;EACbA,IAAI,CAACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACjCA,IAAI,CAACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACjCA,IAAI,CAACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACjCA,IAAI,CAACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACjCA,IAAI,CAACA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC7CA,IAAI,CAACA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;AACjD,CAAC,EAAEA,IAAI,KAAKA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI;EACnC;EACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IAC/B,OAAOA,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI;EACnC;EACA,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC7B,OAAOA,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI;EACjC;EACA,MAAM,IAAIC,KAAK,CAAC,mCAAmC,CAAC;AACxD;AACA;AACA;AACA;AACA,IAAIC,UAAU;AACd;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,GAAG,EAAE;EACvB,IAAI,OAAOF,UAAU,KAAK,WAAW,EAAE;IACnCA,UAAU,GAAGL,oBAAoB,CAAC,CAAC;EACvC;EACA,IAAIK,UAAU,KAAK,IAAI,EAAE;IACrB,MAAM,IAAID,KAAK,CAAC,0BAA0B,CAAC;EAC/C;EACA,OAAOC,UAAU,CAACE,GAAG,CAAC;AAC1B;AACA,SAASC,SAASA,CAAA,EAAG;EACjB;EACA,IAAIC,MAAM,GAAG,EAAE;EACfA,MAAM,CAAC,CAAC,CAAC,GAAGH,YAAY,CAAC,CAAC,CAAC;EAC3B,IAAII,WAAW,GAAGJ,YAAY,CAAC,GAAG,CAAC;EACnCG,MAAM,CAAC,CAAC,CAAC,GAAGC,WAAW;EACvB;EACA,KAAK,IAAIC,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;IACjCF,MAAM,CAACE,CAAC,CAAC,GAAGF,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC,GAAGD,WAAW;EAC3C;EACA,OAAOD,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIG,sBAAsB,GAAG,aAAe,YAAY;EACpD,SAASA,sBAAsBA,CAAA,EAAG,CAClC;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,sBAAsB,CAACC,MAAM,GAAG,UAAUC,SAAS,EAAEC,OAAO,EAAE;IAC1D;IACA,IAAIC,MAAM,GAAG,IAAItB,aAAa,CAAC,EAAE,CAAC;IAClC;IACA,IAAIuB,QAAQ,GAAG3B,eAAe,CAAC4B,SAAS;IACxC;AACR;AACA;AACA;AACA;AACA;AACA;IACQF,MAAM,CAACG,cAAc,CAACF,QAAQ,CAAC;IAC/B;IACA,IAAIG,SAAS,GAAG,CAAC;IACjB,IAAIC,IAAI,GAAGP,SAAS,CAACM,SAAS,EAAE,CAAC;IACjC,IAAIE,cAAc,GAAG,IAAI9B,oBAAoB,CAAC,CAAC;IAC/C,OAAO4B,SAAS,GAAGN,SAAS,CAAC,CAAC,CAAC,EAAE;MAC7B,QAAQO,IAAI;QACR,KAAKT,sBAAsB,CAACW,0BAA0B;UAClDH,SAAS,GAAGR,sBAAsB,CAACY,cAAc,CAACV,SAAS,EAAEM,SAAS,EAAEJ,MAAM,CAAC;UAC/E;QACJ,KAAKJ,sBAAsB,CAACa,0BAA0B;QACtD,KAAKb,sBAAsB,CAACc,4BAA4B;UACpDN,SAAS,GAAGR,sBAAsB,CAACe,cAAc,CAACN,IAAI,EAAEP,SAAS,EAAEG,QAAQ,EAAEG,SAAS,EAAEJ,MAAM,CAAC;UAC/F;QACJ,KAAKJ,sBAAsB,CAACgB,kCAAkC;UAC1DZ,MAAM,CAACa,MAAM,CAAC,UAAWf,SAAS,CAACM,SAAS,EAAE,CAAC,CAAC;UAChD;QACJ,KAAKR,sBAAsB,CAACkB,6BAA6B;UACrDV,SAAS,GAAGR,sBAAsB,CAACmB,iBAAiB,CAACjB,SAAS,EAAEM,SAAS,EAAEJ,MAAM,CAAC;UAClF;QACJ,KAAKJ,sBAAsB,CAACoB,WAAW;UACnC,IAAIC,UAAU,GAAG3C,eAAe,CAAC4C,yBAAyB,CAACpB,SAAS,CAACM,SAAS,EAAE,CAAC,CAAC;UAClF;UACA;QACJ,KAAKR,sBAAsB,CAACuB,mBAAmB;UAC3C;UACAf,SAAS,IAAI,CAAC;UACd;QACJ,KAAKR,sBAAsB,CAACwB,gBAAgB;UACxC;UACAhB,SAAS,EAAE;UACX;QACJ,KAAKR,sBAAsB,CAACyB,gCAAgC;UACxDjB,SAAS,GAAGR,sBAAsB,CAAC0B,gBAAgB,CAACxB,SAAS,EAAEM,SAAS,EAAEE,cAAc,CAAC;UACzF;QACJ,KAAKV,sBAAsB,CAAC2B,iCAAiC;QAC7D,KAAK3B,sBAAsB,CAAC4B,uBAAuB;UAC/C;UACA,MAAM,IAAInD,eAAe,CAAC,CAAC;QAC/B;UACI;UACA;UACA;UACA+B,SAAS,EAAE;UACXA,SAAS,GAAGR,sBAAsB,CAACY,cAAc,CAACV,SAAS,EAAEM,SAAS,EAAEJ,MAAM,CAAC;UAC/E;MACR;MACA,IAAII,SAAS,GAAGN,SAAS,CAAC2B,MAAM,EAAE;QAC9BpB,IAAI,GAAGP,SAAS,CAACM,SAAS,EAAE,CAAC;MACjC,CAAC,MACI;QACD,MAAM/B,eAAe,CAACqD,iBAAiB,CAAC,CAAC;MAC7C;IACJ;IACA,IAAI1B,MAAM,CAACyB,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;MACvB,MAAMpD,eAAe,CAACqD,iBAAiB,CAAC,CAAC;IAC7C;IACA,IAAIC,aAAa,GAAG,IAAIpD,aAAa,CAAC,IAAI,EAAEyB,MAAM,CAAC4B,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE7B,OAAO,CAAC;IAC7E4B,aAAa,CAACE,QAAQ,CAACvB,cAAc,CAAC;IACtC,OAAOqB,aAAa;EACxB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA/B,sBAAsB,CAAC0B,gBAAgB,GAAG,UAAUxB,SAAS,EAAEM,SAAS,EAAEE,cAAc,EAAE;IACtF,IAAIF,SAAS,GAAGR,sBAAsB,CAACkC,4BAA4B,GAAGhC,SAAS,CAAC,CAAC,CAAC,EAAE;MAChF;MACA,MAAMzB,eAAe,CAACqD,iBAAiB,CAAC,CAAC;IAC7C;IACA,IAAIK,iBAAiB,GAAG,IAAIC,UAAU,CAACpC,sBAAsB,CAACkC,4BAA4B,CAAC;IAC3F,KAAK,IAAInC,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGC,sBAAsB,CAACkC,4BAA4B,EAAEnC,CAAC,EAAE,EAAES,SAAS,EAAE,EAAE;MAC/F2B,iBAAiB,CAACpC,CAAC,CAAC,GAAGG,SAAS,CAACM,SAAS,CAAC;IAC/C;IACAE,cAAc,CAAC2B,eAAe,CAACtD,OAAO,CAACuD,QAAQ,CAACtC,sBAAsB,CAACuC,qBAAqB,CAACJ,iBAAiB,EAAEnC,sBAAsB,CAACkC,4BAA4B,CAAC,CAAC,CAAC;IACtK,IAAIM,MAAM,GAAG,IAAI1D,aAAa,CAAC,CAAC;IAChC0B,SAAS,GAAGR,sBAAsB,CAACY,cAAc,CAACV,SAAS,EAAEM,SAAS,EAAEgC,MAAM,CAAC;IAC/E9B,cAAc,CAAC+B,SAAS,CAACD,MAAM,CAACR,QAAQ,CAAC,CAAC,CAAC;IAC3C,IAAIU,mBAAmB,GAAG,CAAC,CAAC;IAC5B,IAAIxC,SAAS,CAACM,SAAS,CAAC,KAAKR,sBAAsB,CAAC2B,iCAAiC,EAAE;MACnFe,mBAAmB,GAAGlC,SAAS,GAAG,CAAC;IACvC;IACA,OAAOA,SAAS,GAAGN,SAAS,CAAC,CAAC,CAAC,EAAE;MAC7B,QAAQA,SAAS,CAACM,SAAS,CAAC;QACxB,KAAKR,sBAAsB,CAAC2B,iCAAiC;UACzDnB,SAAS,EAAE;UACX,QAAQN,SAAS,CAACM,SAAS,CAAC;YACxB,KAAKR,sBAAsB,CAAC2C,qCAAqC;cAC7D,IAAIC,QAAQ,GAAG,IAAI9D,aAAa,CAAC,CAAC;cAClC0B,SAAS,GAAGR,sBAAsB,CAACY,cAAc,CAACV,SAAS,EAAEM,SAAS,GAAG,CAAC,EAAEoC,QAAQ,CAAC;cACrFlC,cAAc,CAACmC,WAAW,CAACD,QAAQ,CAACZ,QAAQ,CAAC,CAAC,CAAC;cAC/C;YACJ,KAAKhC,sBAAsB,CAAC8C,kCAAkC;cAC1D,IAAIC,MAAM,GAAG,IAAIjE,aAAa,CAAC,CAAC;cAChC0B,SAAS,GAAGR,sBAAsB,CAACY,cAAc,CAACV,SAAS,EAAEM,SAAS,GAAG,CAAC,EAAEuC,MAAM,CAAC;cACnFrC,cAAc,CAACsC,SAAS,CAACD,MAAM,CAACf,QAAQ,CAAC,CAAC,CAAC;cAC3C;YACJ,KAAKhC,sBAAsB,CAACiD,qCAAqC;cAC7D,IAAIC,SAAS,GAAG,IAAIpE,aAAa,CAAC,CAAC;cACnC0B,SAAS,GAAGR,sBAAsB,CAACY,cAAc,CAACV,SAAS,EAAEM,SAAS,GAAG,CAAC,EAAE0C,SAAS,CAAC;cACtFxC,cAAc,CAACyC,YAAY,CAACD,SAAS,CAAClB,QAAQ,CAAC,CAAC,CAAC;cACjD;YACJ,KAAKhC,sBAAsB,CAACoD,yCAAyC;cACjE,IAAIC,YAAY,GAAG,IAAIvE,aAAa,CAAC,CAAC;cACtC0B,SAAS,GAAGR,sBAAsB,CAACmB,iBAAiB,CAACjB,SAAS,EAAEM,SAAS,GAAG,CAAC,EAAE6C,YAAY,CAAC;cAC5F3C,cAAc,CAAC4C,eAAe,CAACvE,OAAO,CAACuD,QAAQ,CAACe,YAAY,CAACrB,QAAQ,CAAC,CAAC,CAAC,CAAC;cACzE;YACJ,KAAKhC,sBAAsB,CAACuD,sCAAsC;cAC9D,IAAIC,SAAS,GAAG,IAAI1E,aAAa,CAAC,CAAC;cACnC0B,SAAS,GAAGR,sBAAsB,CAACmB,iBAAiB,CAACjB,SAAS,EAAEM,SAAS,GAAG,CAAC,EAAEgD,SAAS,CAAC;cACzF9C,cAAc,CAAC+C,YAAY,CAACzE,IAAI,CAAC0E,SAAS,CAACF,SAAS,CAACxB,QAAQ,CAAC,CAAC,CAAC,CAAC;cACjE;YACJ,KAAKhC,sBAAsB,CAAC2D,oCAAoC;cAC5D,IAAIC,QAAQ,GAAG,IAAI9E,aAAa,CAAC,CAAC;cAClC0B,SAAS,GAAGR,sBAAsB,CAACmB,iBAAiB,CAACjB,SAAS,EAAEM,SAAS,GAAG,CAAC,EAAEoD,QAAQ,CAAC;cACxFlD,cAAc,CAACmD,WAAW,CAAC9E,OAAO,CAACuD,QAAQ,CAACsB,QAAQ,CAAC5B,QAAQ,CAAC,CAAC,CAAC,CAAC;cACjE;YACJ,KAAKhC,sBAAsB,CAAC8D,qCAAqC;cAC7D,IAAIC,QAAQ,GAAG,IAAIjF,aAAa,CAAC,CAAC;cAClC0B,SAAS,GAAGR,sBAAsB,CAACmB,iBAAiB,CAACjB,SAAS,EAAEM,SAAS,GAAG,CAAC,EAAEuD,QAAQ,CAAC;cACxFrD,cAAc,CAACsD,WAAW,CAAChF,IAAI,CAAC0E,SAAS,CAACK,QAAQ,CAAC/B,QAAQ,CAAC,CAAC,CAAC,CAAC;cAC/D;YACJ;cACI,MAAMvD,eAAe,CAACqD,iBAAiB,CAAC,CAAC;UACjD;UACA;QACJ,KAAK9B,sBAAsB,CAAC4B,uBAAuB;UAC/CpB,SAAS,EAAE;UACXE,cAAc,CAACuD,cAAc,CAAC,IAAI,CAAC;UACnC;QACJ;UACI,MAAMxF,eAAe,CAACqD,iBAAiB,CAAC,CAAC;MACjD;IACJ;IACA;IACA,IAAIY,mBAAmB,KAAK,CAAC,CAAC,EAAE;MAC5B,IAAIwB,oBAAoB,GAAG1D,SAAS,GAAGkC,mBAAmB;MAC1D,IAAIhC,cAAc,CAACyD,aAAa,CAAC,CAAC,EAAE;QAChC;QACAD,oBAAoB,EAAE;MAC1B;MACAxD,cAAc,CAAC0D,eAAe,CAACvF,MAAM,CAACwF,WAAW,CAACnE,SAAS,EAAEwC,mBAAmB,EAAEA,mBAAmB,GAAGwB,oBAAoB,CAAC,CAAC;IAClI;IACA,OAAO1D,SAAS;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,sBAAsB,CAACY,cAAc,GAAG,UAAUV,SAAS,EAAEM,SAAS,EAAEJ,MAAM,EAAE;IAC5E;IACA,IAAIkE,kBAAkB,GAAG,IAAIlC,UAAU,CAAC,CAAClC,SAAS,CAAC,CAAC,CAAC,GAAGM,SAAS,IAAI,CAAC,CAAC;IACvE;IACA,IAAI+D,kBAAkB,GAAG,IAAInC,UAAU,CAAC,CAAClC,SAAS,CAAC,CAAC,CAAC,GAAGM,SAAS,IAAI,CAAC,CAAC;IACvE,IAAIgE,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAG,KAAK;IACf,OAAQjE,SAAS,GAAGN,SAAS,CAAC,CAAC,CAAC,IAAK,CAACuE,GAAG,EAAE;MACvC,IAAIhE,IAAI,GAAGP,SAAS,CAACM,SAAS,EAAE,CAAC;MACjC,IAAIC,IAAI,GAAGT,sBAAsB,CAACW,0BAA0B,EAAE;QAC1D2D,kBAAkB,CAACE,KAAK,CAAC,GAAG/D,IAAI,GAAG,EAAE;QACrC6D,kBAAkB,CAACE,KAAK,GAAG,CAAC,CAAC,GAAG/D,IAAI,GAAG,EAAE;QACzC+D,KAAK,IAAI,CAAC;MACd,CAAC,MACI;QACD,QAAQ/D,IAAI;UACR,KAAKT,sBAAsB,CAACW,0BAA0B;YAClD;YACA2D,kBAAkB,CAACE,KAAK,EAAE,CAAC,GAAGxE,sBAAsB,CAACW,0BAA0B;YAC/E;UACJ,KAAKX,sBAAsB,CAACa,0BAA0B;UACtD,KAAKb,sBAAsB,CAACc,4BAA4B;UACxD,KAAKd,sBAAsB,CAACkB,6BAA6B;UACzD,KAAKlB,sBAAsB,CAACyB,gCAAgC;UAC5D,KAAKzB,sBAAsB,CAAC2B,iCAAiC;UAC7D,KAAK3B,sBAAsB,CAAC4B,uBAAuB;YAC/CpB,SAAS,EAAE;YACXiE,GAAG,GAAG,IAAI;YACV;UACJ,KAAKzE,sBAAsB,CAACgB,kCAAkC;YAC1D;YACA;YACA;YACA;YACA;YACA;YACAsD,kBAAkB,CAACE,KAAK,CAAC,GAAGxE,sBAAsB,CAACgB,kCAAkC;YACrFP,IAAI,GAAGP,SAAS,CAACM,SAAS,EAAE,CAAC;YAC7B+D,kBAAkB,CAACC,KAAK,CAAC,GAAG/D,IAAI;YAChC+D,KAAK,EAAE;YACP;QACR;MACJ;IACJ;IACAxE,sBAAsB,CAAC0E,oBAAoB,CAACJ,kBAAkB,EAAEC,kBAAkB,EAAEC,KAAK,EAAEpE,MAAM,CAAC;IAClG,OAAOI,SAAS;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,sBAAsB,CAAC0E,oBAAoB,GAAG,UAAUJ,kBAAkB,EAAEC,kBAAkB,EAAE1C,MAAM,EAAEzB,MAAM,EAAE;IAC5G;IACA;IACA;IACA;IACA,IAAIuE,OAAO,GAAGxF,IAAI,CAACyF,KAAK;IACxB,IAAIC,gBAAgB,GAAG1F,IAAI,CAACyF,KAAK;IACjC,IAAI7E,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG8B,MAAM,EAAE;MACf,IAAIiD,SAAS,GAAGR,kBAAkB,CAACvE,CAAC,CAAC;MACrC,IAAIgF,EAAE,GAAG,QAAS,EAAE;MACpB,QAAQJ,OAAO;QACX,KAAKxF,IAAI,CAACyF,KAAK;UACX;UACA,IAAIE,SAAS,GAAG,EAAE,EAAE;YAChB;YACA;YACAC,EAAE,GAAG,4BAA6BC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,SAAS,CAAC;UACzE,CAAC,MACI;YACD,QAAQA,SAAS;cACb,KAAK,EAAE;gBACHC,EAAE,GAAG,GAAG;gBACR;cACJ,KAAK/E,sBAAsB,CAACkF,EAAE;gBAC1BP,OAAO,GAAGxF,IAAI,CAACgG,KAAK;gBACpB;cACJ,KAAKnF,sBAAsB,CAACoF,EAAE;gBAC1BT,OAAO,GAAGxF,IAAI,CAACkG,KAAK;gBACpB;cACJ,KAAKrF,sBAAsB,CAACsF,EAAE;gBAC1B;gBACAT,gBAAgB,GAAGF,OAAO;gBAC1BA,OAAO,GAAGxF,IAAI,CAACoG,WAAW;gBAC1B;cACJ,KAAKvF,sBAAsB,CAACgB,kCAAkC;gBAC1DZ,MAAM,CAACa,MAAM,CAAC,UAAWsD,kBAAkB,CAACxE,CAAC,CAAC,CAAC;gBAC/C;cACJ,KAAKC,sBAAsB,CAACW,0BAA0B;gBAClDgE,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;YACR;UACJ;UACA;QACJ,KAAKzF,IAAI,CAACgG,KAAK;UACX;UACA,IAAIL,SAAS,GAAG,EAAE,EAAE;YAChBC,EAAE,GAAG,2BAA4BC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,SAAS,CAAC;UACxE,CAAC,MACI;YACD,QAAQA,SAAS;cACb,KAAK,EAAE;gBACHC,EAAE,GAAG,GAAG;gBACR;cACJ,KAAK/E,sBAAsB,CAACwF,EAAE;gBAC1B;gBACAX,gBAAgB,GAAGF,OAAO;gBAC1BA,OAAO,GAAGxF,IAAI,CAACsG,WAAW;gBAC1B;cACJ,KAAKzF,sBAAsB,CAACoF,EAAE;gBAC1BT,OAAO,GAAGxF,IAAI,CAACkG,KAAK;gBACpB;cACJ,KAAKrF,sBAAsB,CAACsF,EAAE;gBAC1B;gBACAT,gBAAgB,GAAGF,OAAO;gBAC1BA,OAAO,GAAGxF,IAAI,CAACoG,WAAW;gBAC1B;cACJ,KAAKvF,sBAAsB,CAACgB,kCAAkC;gBAC1D;gBACAZ,MAAM,CAACa,MAAM,CAAC,UAAWsD,kBAAkB,CAACxE,CAAC,CAAC,CAAC;gBAC/C;cACJ,KAAKC,sBAAsB,CAACW,0BAA0B;gBAClDgE,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;YACR;UACJ;UACA;QACJ,KAAKzF,IAAI,CAACkG,KAAK;UACX;UACA,IAAIP,SAAS,GAAG9E,sBAAsB,CAAC0F,EAAE,EAAE;YACvCX,EAAE,GAAG/E,sBAAsB,CAAC2F,WAAW,CAACb,SAAS,CAAC;UACtD,CAAC,MACI;YACD,QAAQA,SAAS;cACb,KAAK9E,sBAAsB,CAAC0F,EAAE;gBAC1Bf,OAAO,GAAGxF,IAAI,CAACyG,KAAK;gBACpB;cACJ,KAAK,EAAE;gBACHb,EAAE,GAAG,GAAG;gBACR;cACJ,KAAK/E,sBAAsB,CAACkF,EAAE;gBAC1BP,OAAO,GAAGxF,IAAI,CAACgG,KAAK;gBACpB;cACJ,KAAKnF,sBAAsB,CAAC6F,EAAE;gBAC1BlB,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;cACJ,KAAK5E,sBAAsB,CAACsF,EAAE;gBAC1B;gBACAT,gBAAgB,GAAGF,OAAO;gBAC1BA,OAAO,GAAGxF,IAAI,CAACoG,WAAW;gBAC1B;cACJ,KAAKvF,sBAAsB,CAACgB,kCAAkC;gBAC1DZ,MAAM,CAACa,MAAM,CAAC,UAAWsD,kBAAkB,CAACxE,CAAC,CAAC,CAAC;gBAC/C;cACJ,KAAKC,sBAAsB,CAACW,0BAA0B;gBAClDgE,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;YACR;UACJ;UACA;QACJ,KAAKzF,IAAI,CAACyG,KAAK;UACX;UACA,IAAId,SAAS,GAAG9E,sBAAsB,CAAC8F,GAAG,EAAE;YACxCf,EAAE,GAAG/E,sBAAsB,CAAC+F,WAAW,CAACjB,SAAS,CAAC;UACtD,CAAC,MACI;YACD,QAAQA,SAAS;cACb,KAAK9E,sBAAsB,CAAC8F,GAAG;gBAC3BnB,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;cACJ,KAAK5E,sBAAsB,CAACgB,kCAAkC;gBAC1DZ,MAAM,CAACa,MAAM,CAAC,UAAWsD,kBAAkB,CAACxE,CAAC,CAAC,CAAC;gBAC/C;cACJ,KAAKC,sBAAsB,CAACW,0BAA0B;gBAClDgE,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;YACR;UACJ;UACA;QACJ,KAAKzF,IAAI,CAACsG,WAAW;UACjB;UACAd,OAAO,GAAGE,gBAAgB;UAC1B,IAAIC,SAAS,GAAG,EAAE,EAAE;YAChBC,EAAE,GAAG,2BAA4BC,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGH,SAAS,CAAC;UACxE,CAAC,MACI;YACD,QAAQA,SAAS;cACb,KAAK,EAAE;gBACHC,EAAE,GAAG,GAAG;gBACR;cACJ,KAAK/E,sBAAsB,CAACW,0BAA0B;gBAClDgE,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;YACR;UACJ;UACA;QACJ,KAAKzF,IAAI,CAACoG,WAAW;UACjB;UACAZ,OAAO,GAAGE,gBAAgB;UAC1B,IAAIC,SAAS,GAAG9E,sBAAsB,CAAC8F,GAAG,EAAE;YACxCf,EAAE,GAAG/E,sBAAsB,CAAC+F,WAAW,CAACjB,SAAS,CAAC;UACtD,CAAC,MACI;YACD,QAAQA,SAAS;cACb,KAAK9E,sBAAsB,CAAC8F,GAAG;gBAC3BnB,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;cACJ,KAAK5E,sBAAsB,CAACgB,kCAAkC;gBAC1D;gBACA;gBACAZ,MAAM,CAACa,MAAM,CAAC,UAAWsD,kBAAkB,CAACxE,CAAC,CAAC,CAAC;gBAC/C;cACJ,KAAKC,sBAAsB,CAACW,0BAA0B;gBAClDgE,OAAO,GAAGxF,IAAI,CAACyF,KAAK;gBACpB;YACR;UACJ;UACA;MACR;MACA;MACA,IAAIG,EAAE,KAAK,EAAE,EAAE;QACX;QACA3E,MAAM,CAACa,MAAM,CAAC8D,EAAE,CAAC;MACrB;MACAhF,CAAC,EAAE;IACP;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,sBAAsB,CAACe,cAAc,GAAG,UAAUiF,IAAI,EAAE9F,SAAS,EAAEG,QAAQ,EAAEG,SAAS,EAAEJ,MAAM,EAAE;IAC5F,IAAI6F,YAAY,GAAG,IAAIhH,qBAAqB,CAAC,CAAC;IAC9C,IAAIiH,KAAK,GAAG,CAAC;IACb,IAAIC,KAAK,GAAG,QAAS,CAAC;IACtB,IAAI1B,GAAG,GAAG,KAAK;IACf,QAAQuB,IAAI;MACR,KAAKhG,sBAAsB,CAACa,0BAA0B;QAClD;QACA;QACA,IAAIuF,sBAAsB,GAAG,IAAIhE,UAAU,CAAC,CAAC,CAAC;QAC9C,IAAIiE,QAAQ,GAAGnG,SAAS,CAACM,SAAS,EAAE,CAAC;QACrC,OAAQA,SAAS,GAAGN,SAAS,CAAC,CAAC,CAAC,IAAK,CAACuE,GAAG,EAAE;UACvC2B,sBAAsB,CAACF,KAAK,EAAE,CAAC,GAAGG,QAAQ;UAC1C;UACAF,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAGE,QAAQ;UAC9BA,QAAQ,GAAGnG,SAAS,CAACM,SAAS,EAAE,CAAC;UACjC;UACA,QAAQ6F,QAAQ;YACZ,KAAKrG,sBAAsB,CAACW,0BAA0B;YACtD,KAAKX,sBAAsB,CAACa,0BAA0B;YACtD,KAAKb,sBAAsB,CAACkB,6BAA6B;YACzD,KAAKlB,sBAAsB,CAACc,4BAA4B;YACxD,KAAKd,sBAAsB,CAACyB,gCAAgC;YAC5D,KAAKzB,sBAAsB,CAAC2B,iCAAiC;YAC7D,KAAK3B,sBAAsB,CAAC4B,uBAAuB;cAC/CpB,SAAS,EAAE;cACXiE,GAAG,GAAG,IAAI;cACV;YACJ;cACI,IAAKyB,KAAK,GAAG,CAAC,KAAK,CAAC,IAAMA,KAAK,GAAG,CAAE,EAAE;gBAClC;gBACA;gBACA,KAAK,IAAII,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;kBAChC;AACpC;AACA;AACA;kBACoCL,YAAY,CAACM,KAAK,CAAC,UAAWC,MAAM,CAAC9G,YAAY,CAACyG,KAAK,CAAC,IAAIzG,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG4G,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC3F;gBACAH,KAAK,GAAG,CAAC;gBACTD,KAAK,GAAG,CAAC;cACb;cACA;UACR;QACJ;QACA;QACA,IAAI1F,SAAS,KAAKN,SAAS,CAAC,CAAC,CAAC,IAAImG,QAAQ,GAAGrG,sBAAsB,CAACW,0BAA0B,EAAE;UAC5FyF,sBAAsB,CAACF,KAAK,EAAE,CAAC,GAAGG,QAAQ;QAC9C;QACA;QACA;QACA;QACA,KAAK,IAAItG,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGmG,KAAK,EAAEnG,CAAC,EAAE,EAAE;UACpCkG,YAAY,CAACM,KAAK,CAAC,UAAWH,sBAAsB,CAACrG,CAAC,CAAC,CAAC;QAC5D;QACA;MACJ,KAAKC,sBAAsB,CAACc,4BAA4B;QACpD;QACA;QACA,OAAON,SAAS,GAAGN,SAAS,CAAC,CAAC,CAAC,IAAI,CAACuE,GAAG,EAAE;UACrC,IAAIhE,IAAI,GAAGP,SAAS,CAACM,SAAS,EAAE,CAAC;UACjC,IAAIC,IAAI,GAAGT,sBAAsB,CAACW,0BAA0B,EAAE;YAC1DuF,KAAK,EAAE;YACP;YACAC,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG1F,IAAI;UAC9B,CAAC,MACI;YACD,QAAQA,IAAI;cACR,KAAKT,sBAAsB,CAACW,0BAA0B;cACtD,KAAKX,sBAAsB,CAACa,0BAA0B;cACtD,KAAKb,sBAAsB,CAACkB,6BAA6B;cACzD,KAAKlB,sBAAsB,CAACc,4BAA4B;cACxD,KAAKd,sBAAsB,CAACyB,gCAAgC;cAC5D,KAAKzB,sBAAsB,CAAC2B,iCAAiC;cAC7D,KAAK3B,sBAAsB,CAAC4B,uBAAuB;gBAC/CpB,SAAS,EAAE;gBACXiE,GAAG,GAAG,IAAI;gBACV;YACR;UACJ;UACA,IAAKyB,KAAK,GAAG,CAAC,KAAK,CAAC,IAAMA,KAAK,GAAG,CAAE,EAAE;YAClC;YACA;YACA;AACxB;AACA;AACA;YACwB,KAAK,IAAII,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;cAChCL,YAAY,CAACM,KAAK,CAAC,UAAWC,MAAM,CAAC9G,YAAY,CAACyG,KAAK,CAAC,IAAIzG,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG4G,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3F;YACAH,KAAK,GAAG,CAAC;YACTD,KAAK,GAAG,CAAC;UACb;QACJ;QACA;IACR;IACA9F,MAAM,CAACa,MAAM,CAAC/B,cAAc,CAACe,MAAM,CAACgG,YAAY,CAACQ,WAAW,CAAC,CAAC,EAAEpG,QAAQ,CAAC,CAAC;IAC1E,OAAOG,SAAS;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,sBAAsB,CAACmB,iBAAiB,GAAG,UAAUjB,SAAS,EAAEM,SAAS,CAAC,SAASJ,MAAM,EAAE;IACvF,IAAI8F,KAAK,GAAG,CAAC;IACb,IAAIzB,GAAG,GAAG,KAAK;IACf,IAAIiC,gBAAgB,GAAG,IAAItE,UAAU,CAACpC,sBAAsB,CAAC2G,qBAAqB,CAAC;IACnF,OAAOnG,SAAS,GAAGN,SAAS,CAAC,CAAC,CAAC,IAAI,CAACuE,GAAG,EAAE;MACrC,IAAIhE,IAAI,GAAGP,SAAS,CAACM,SAAS,EAAE,CAAC;MACjC,IAAIA,SAAS,KAAKN,SAAS,CAAC,CAAC,CAAC,EAAE;QAC5BuE,GAAG,GAAG,IAAI;MACd;MACA,IAAIhE,IAAI,GAAGT,sBAAsB,CAACW,0BAA0B,EAAE;QAC1D+F,gBAAgB,CAACR,KAAK,CAAC,GAAGzF,IAAI;QAC9ByF,KAAK,EAAE;MACX,CAAC,MACI;QACD,QAAQzF,IAAI;UACR,KAAKT,sBAAsB,CAACW,0BAA0B;UACtD,KAAKX,sBAAsB,CAACa,0BAA0B;UACtD,KAAKb,sBAAsB,CAACc,4BAA4B;UACxD,KAAKd,sBAAsB,CAACyB,gCAAgC;UAC5D,KAAKzB,sBAAsB,CAAC2B,iCAAiC;UAC7D,KAAK3B,sBAAsB,CAAC4B,uBAAuB;YAC/CpB,SAAS,EAAE;YACXiE,GAAG,GAAG,IAAI;YACV;QACR;MACJ;MACA,IAAI,CAACyB,KAAK,GAAGlG,sBAAsB,CAAC2G,qBAAqB,KAAK,CAAC,IAAIlG,IAAI,KAAKT,sBAAsB,CAACkB,6BAA6B,IAAIuD,GAAG,KAAKyB,KAAK,GAAG,CAAC,EAAE;QACnJ;QACA;QACA;QACA;QACA9F,MAAM,CAACa,MAAM,CAACjB,sBAAsB,CAACuC,qBAAqB,CAACmE,gBAAgB,EAAER,KAAK,CAAC,CAAC;QACpFA,KAAK,GAAG,CAAC;MACb;IACJ;IACA,OAAO1F,SAAS;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,sBAAsB,CAACuC,qBAAqB,GAAG,UAAUrC,SAAS,EAAEgG,KAAK,EAAE;IACvE,IAAI9F,MAAM,GAAGV,YAAY,CAAC,CAAC,CAAC;IAC5B,KAAK,IAAIK,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGmG,KAAK,EAAEnG,CAAC,EAAE,EAAE;MACpCK,MAAM,IAAIJ,sBAAsB,CAACH,MAAM,CAACqG,KAAK,GAAGnG,CAAC,GAAG,CAAC,CAAC,GAAGL,YAAY,CAACQ,SAAS,CAACH,CAAC,CAAC,CAAC;IACvF;IACA,IAAI6G,YAAY,GAAGxG,MAAM,CAAC4B,QAAQ,CAAC,CAAC;IACpC,IAAI4E,YAAY,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MAChC,MAAM,IAAIpI,eAAe,CAAC,CAAC;IAC/B;IACA,OAAOmI,YAAY,CAACE,SAAS,CAAC,CAAC,CAAC;EACpC,CAAC;EACD9G,sBAAsB,CAACW,0BAA0B,GAAG,GAAG;EACvDX,sBAAsB,CAACa,0BAA0B,GAAG,GAAG;EACvDb,sBAAsB,CAACkB,6BAA6B,GAAG,GAAG;EAC1DlB,sBAAsB,CAACc,4BAA4B,GAAG,GAAG;EACzDd,sBAAsB,CAACwB,gBAAgB,GAAG,GAAG;EAC7CxB,sBAAsB,CAACuB,mBAAmB,GAAG,GAAG;EAChDvB,sBAAsB,CAACoB,WAAW,GAAG,GAAG;EACxCpB,sBAAsB,CAACyB,gCAAgC,GAAG,GAAG;EAC7DzB,sBAAsB,CAAC2B,iCAAiC,GAAG,GAAG;EAC9D3B,sBAAsB,CAAC4B,uBAAuB,GAAG,GAAG;EACpD5B,sBAAsB,CAACgB,kCAAkC,GAAG,GAAG;EAC/DhB,sBAAsB,CAAC2G,qBAAqB,GAAG,EAAE;EACjD3G,sBAAsB,CAAC2C,qCAAqC,GAAG,CAAC;EAChE3C,sBAAsB,CAACoD,yCAAyC,GAAG,CAAC;EACpEpD,sBAAsB,CAACuD,sCAAsC,GAAG,CAAC;EACjEvD,sBAAsB,CAAC8C,kCAAkC,GAAG,CAAC;EAC7D9C,sBAAsB,CAACiD,qCAAqC,GAAG,CAAC;EAChEjD,sBAAsB,CAAC8D,qCAAqC,GAAG,CAAC;EAChE9D,sBAAsB,CAAC2D,oCAAoC,GAAG,CAAC;EAC/D3D,sBAAsB,CAAC0F,EAAE,GAAG,EAAE;EAC9B1F,sBAAsB,CAACkF,EAAE,GAAG,EAAE;EAC9BlF,sBAAsB,CAACwF,EAAE,GAAG,EAAE;EAC9BxF,sBAAsB,CAACoF,EAAE,GAAG,EAAE;EAC9BpF,sBAAsB,CAAC6F,EAAE,GAAG,EAAE;EAC9B7F,sBAAsB,CAACsF,EAAE,GAAG,EAAE;EAC9BtF,sBAAsB,CAAC8F,GAAG,GAAG,EAAE;EAC/B9F,sBAAsB,CAAC+F,WAAW,GAAG,oCAAoC;EACzE/F,sBAAsB,CAAC2F,WAAW,GAAG,6BAA6B;EAClE;AACJ;AACA;AACA;EACI3F,sBAAsB,CAACH,MAAM,GAAGT,oBAAoB,CAAC,CAAC,GAAGQ,SAAS,CAAC,CAAC,GAAG,EAAE;EACzEI,sBAAsB,CAACkC,4BAA4B,GAAG,CAAC;EACvD,OAAOlC,sBAAsB;AACjC,CAAC,CAAC,CAAE;AACJ,eAAeA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}