{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>This class contains utility methods for performing mathematical operations over\n * the Galois Fields. Operations use a given primitive polynomial in calculations.</p>\n *\n * <p>Throughout this package, elements of the GF are represented as an {@code int}\n * for convenience and speed (but at the cost of memory).\n * </p>\n *\n * <AUTHOR>\n * <AUTHOR>\n */\nvar AbstractGenericGF = /** @class */function () {\n  function AbstractGenericGF() {}\n  /**\n   * @return 2 to the power of a in GF(size)\n   */\n  AbstractGenericGF.prototype.exp = function (a) {\n    return this.expTable[a];\n  };\n  /**\n   * @return base 2 log of a in GF(size)\n   */\n  AbstractGenericGF.prototype.log = function (a /*int*/) {\n    if (a === 0) {\n      throw new IllegalArgumentException();\n    }\n    return this.logTable[a];\n  };\n  /**\n   * Implements both addition and subtraction -- they are the same in GF(size).\n   *\n   * @return sum/difference of a and b\n   */\n  AbstractGenericGF.addOrSubtract = function (a /*int*/, b /*int*/) {\n    return a ^ b;\n  };\n  return AbstractGenericGF;\n}();\nexport default AbstractGenericGF;", "map": {"version": 3, "names": ["IllegalArgumentException", "AbstractGenericGF", "prototype", "exp", "a", "expTable", "log", "logTable", "addOrSubtract", "b"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/reedsolomon/AbstractGenericGF.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>This class contains utility methods for performing mathematical operations over\n * the Galois Fields. Operations use a given primitive polynomial in calculations.</p>\n *\n * <p>Throughout this package, elements of the GF are represented as an {@code int}\n * for convenience and speed (but at the cost of memory).\n * </p>\n *\n * <AUTHOR>\n * <AUTHOR>\n */\nvar AbstractGenericGF = /** @class */ (function () {\n    function AbstractGenericGF() {\n    }\n    /**\n     * @return 2 to the power of a in GF(size)\n     */\n    AbstractGenericGF.prototype.exp = function (a) {\n        return this.expTable[a];\n    };\n    /**\n     * @return base 2 log of a in GF(size)\n     */\n    AbstractGenericGF.prototype.log = function (a /*int*/) {\n        if (a === 0) {\n            throw new IllegalArgumentException();\n        }\n        return this.logTable[a];\n    };\n    /**\n     * Implements both addition and subtraction -- they are the same in GF(size).\n     *\n     * @return sum/difference of a and b\n     */\n    AbstractGenericGF.addOrSubtract = function (a /*int*/, b /*int*/) {\n        return a ^ b;\n    };\n    return AbstractGenericGF;\n}());\nexport default AbstractGenericGF;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,aAAe,YAAY;EAC/C,SAASA,iBAAiBA,CAAA,EAAG,CAC7B;EACA;AACJ;AACA;EACIA,iBAAiB,CAACC,SAAS,CAACC,GAAG,GAAG,UAAUC,CAAC,EAAE;IAC3C,OAAO,IAAI,CAACC,QAAQ,CAACD,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;EACIH,iBAAiB,CAACC,SAAS,CAACI,GAAG,GAAG,UAAUF,CAAC,CAAC,SAAS;IACnD,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,MAAM,IAAIJ,wBAAwB,CAAC,CAAC;IACxC;IACA,OAAO,IAAI,CAACO,QAAQ,CAACH,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIH,iBAAiB,CAACO,aAAa,GAAG,UAAUJ,CAAC,CAAC,SAASK,CAAC,CAAC,SAAS;IAC9D,OAAOL,CAAC,GAAGK,CAAC;EAChB,CAAC;EACD,OAAOR,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AACJ,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}