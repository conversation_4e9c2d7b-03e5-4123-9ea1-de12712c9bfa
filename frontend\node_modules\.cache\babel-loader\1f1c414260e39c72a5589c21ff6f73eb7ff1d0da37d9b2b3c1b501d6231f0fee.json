{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport StringBuilder from '../../util/StringBuilder';\n/**\n * <AUTHOR> (<PERSON><PERSON>) - creator\n * <AUTHOR> (<PERSON>) - ported from C++\n */\nvar QRCode = /** @class */function () {\n  function QRCode() {\n    this.maskPattern = -1;\n  }\n  QRCode.prototype.getMode = function () {\n    return this.mode;\n  };\n  QRCode.prototype.getECLevel = function () {\n    return this.ecLevel;\n  };\n  QRCode.prototype.getVersion = function () {\n    return this.version;\n  };\n  QRCode.prototype.getMaskPattern = function () {\n    return this.maskPattern;\n  };\n  QRCode.prototype.getMatrix = function () {\n    return this.matrix;\n  };\n  /*@Override*/\n  QRCode.prototype.toString = function () {\n    var result = new StringBuilder(); // (200)\n    result.append('<<\\n');\n    result.append(' mode: ');\n    result.append(this.mode ? this.mode.toString() : 'null');\n    result.append('\\n ecLevel: ');\n    result.append(this.ecLevel ? this.ecLevel.toString() : 'null');\n    result.append('\\n version: ');\n    result.append(this.version ? this.version.toString() : 'null');\n    result.append('\\n maskPattern: ');\n    result.append(this.maskPattern.toString());\n    if (this.matrix) {\n      result.append('\\n matrix:\\n');\n      result.append(this.matrix.toString());\n    } else {\n      result.append('\\n matrix: null\\n');\n    }\n    result.append('>>\\n');\n    return result.toString();\n  };\n  QRCode.prototype.setMode = function (value) {\n    this.mode = value;\n  };\n  QRCode.prototype.setECLevel = function (value) {\n    this.ecLevel = value;\n  };\n  QRCode.prototype.setVersion = function (version) {\n    this.version = version;\n  };\n  QRCode.prototype.setMaskPattern = function (value /*int*/) {\n    this.maskPattern = value;\n  };\n  QRCode.prototype.setMatrix = function (value) {\n    this.matrix = value;\n  };\n  // Check if \"mask_pattern\" is valid.\n  QRCode.isValidMaskPattern = function (maskPattern /*int*/) {\n    return maskPattern >= 0 && maskPattern < QRCode.NUM_MASK_PATTERNS;\n  };\n  QRCode.NUM_MASK_PATTERNS = 8;\n  return QRCode;\n}();\nexport default QRCode;", "map": {"version": 3, "names": ["StringBuilder", "QRCode", "maskPattern", "prototype", "getMode", "mode", "getECLevel", "ecLevel", "getVersion", "version", "getMaskPattern", "getMatrix", "matrix", "toString", "result", "append", "setMode", "value", "setECLevel", "setVersion", "setMaskPattern", "setMatrix", "isValidMaskPattern", "NUM_MASK_PATTERNS"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/encoder/QRCode.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport StringBuilder from '../../util/StringBuilder';\n/**\n * <AUTHOR> (<PERSON><PERSON>) - creator\n * <AUTHOR> (<PERSON>) - ported from C++\n */\nvar QRCode = /** @class */ (function () {\n    function QRCode() {\n        this.maskPattern = -1;\n    }\n    QRCode.prototype.getMode = function () {\n        return this.mode;\n    };\n    QRCode.prototype.getECLevel = function () {\n        return this.ecLevel;\n    };\n    QRCode.prototype.getVersion = function () {\n        return this.version;\n    };\n    QRCode.prototype.getMaskPattern = function () {\n        return this.maskPattern;\n    };\n    QRCode.prototype.getMatrix = function () {\n        return this.matrix;\n    };\n    /*@Override*/\n    QRCode.prototype.toString = function () {\n        var result = new StringBuilder(); // (200)\n        result.append('<<\\n');\n        result.append(' mode: ');\n        result.append(this.mode ? this.mode.toString() : 'null');\n        result.append('\\n ecLevel: ');\n        result.append(this.ecLevel ? this.ecLevel.toString() : 'null');\n        result.append('\\n version: ');\n        result.append(this.version ? this.version.toString() : 'null');\n        result.append('\\n maskPattern: ');\n        result.append(this.maskPattern.toString());\n        if (this.matrix) {\n            result.append('\\n matrix:\\n');\n            result.append(this.matrix.toString());\n        }\n        else {\n            result.append('\\n matrix: null\\n');\n        }\n        result.append('>>\\n');\n        return result.toString();\n    };\n    QRCode.prototype.setMode = function (value) {\n        this.mode = value;\n    };\n    QRCode.prototype.setECLevel = function (value) {\n        this.ecLevel = value;\n    };\n    QRCode.prototype.setVersion = function (version) {\n        this.version = version;\n    };\n    QRCode.prototype.setMaskPattern = function (value /*int*/) {\n        this.maskPattern = value;\n    };\n    QRCode.prototype.setMatrix = function (value) {\n        this.matrix = value;\n    };\n    // Check if \"mask_pattern\" is valid.\n    QRCode.isValidMaskPattern = function (maskPattern /*int*/) {\n        return maskPattern >= 0 && maskPattern < QRCode.NUM_MASK_PATTERNS;\n    };\n    QRCode.NUM_MASK_PATTERNS = 8;\n    return QRCode;\n}());\nexport default QRCode;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,0BAA0B;AACpD;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAAA,EAAG;IACd,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;EACzB;EACAD,MAAM,CAACE,SAAS,CAACC,OAAO,GAAG,YAAY;IACnC,OAAO,IAAI,CAACC,IAAI;EACpB,CAAC;EACDJ,MAAM,CAACE,SAAS,CAACG,UAAU,GAAG,YAAY;IACtC,OAAO,IAAI,CAACC,OAAO;EACvB,CAAC;EACDN,MAAM,CAACE,SAAS,CAACK,UAAU,GAAG,YAAY;IACtC,OAAO,IAAI,CAACC,OAAO;EACvB,CAAC;EACDR,MAAM,CAACE,SAAS,CAACO,cAAc,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACR,WAAW;EAC3B,CAAC;EACDD,MAAM,CAACE,SAAS,CAACQ,SAAS,GAAG,YAAY;IACrC,OAAO,IAAI,CAACC,MAAM;EACtB,CAAC;EACD;EACAX,MAAM,CAACE,SAAS,CAACU,QAAQ,GAAG,YAAY;IACpC,IAAIC,MAAM,GAAG,IAAId,aAAa,CAAC,CAAC,CAAC,CAAC;IAClCc,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC;IACrBD,MAAM,CAACC,MAAM,CAAC,SAAS,CAAC;IACxBD,MAAM,CAACC,MAAM,CAAC,IAAI,CAACV,IAAI,GAAG,IAAI,CAACA,IAAI,CAACQ,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;IACxDC,MAAM,CAACC,MAAM,CAAC,cAAc,CAAC;IAC7BD,MAAM,CAACC,MAAM,CAAC,IAAI,CAACR,OAAO,GAAG,IAAI,CAACA,OAAO,CAACM,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;IAC9DC,MAAM,CAACC,MAAM,CAAC,cAAc,CAAC;IAC7BD,MAAM,CAACC,MAAM,CAAC,IAAI,CAACN,OAAO,GAAG,IAAI,CAACA,OAAO,CAACI,QAAQ,CAAC,CAAC,GAAG,MAAM,CAAC;IAC9DC,MAAM,CAACC,MAAM,CAAC,kBAAkB,CAAC;IACjCD,MAAM,CAACC,MAAM,CAAC,IAAI,CAACb,WAAW,CAACW,QAAQ,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,CAACD,MAAM,EAAE;MACbE,MAAM,CAACC,MAAM,CAAC,cAAc,CAAC;MAC7BD,MAAM,CAACC,MAAM,CAAC,IAAI,CAACH,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;IACzC,CAAC,MACI;MACDC,MAAM,CAACC,MAAM,CAAC,mBAAmB,CAAC;IACtC;IACAD,MAAM,CAACC,MAAM,CAAC,MAAM,CAAC;IACrB,OAAOD,MAAM,CAACD,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACDZ,MAAM,CAACE,SAAS,CAACa,OAAO,GAAG,UAAUC,KAAK,EAAE;IACxC,IAAI,CAACZ,IAAI,GAAGY,KAAK;EACrB,CAAC;EACDhB,MAAM,CAACE,SAAS,CAACe,UAAU,GAAG,UAAUD,KAAK,EAAE;IAC3C,IAAI,CAACV,OAAO,GAAGU,KAAK;EACxB,CAAC;EACDhB,MAAM,CAACE,SAAS,CAACgB,UAAU,GAAG,UAAUV,OAAO,EAAE;IAC7C,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B,CAAC;EACDR,MAAM,CAACE,SAAS,CAACiB,cAAc,GAAG,UAAUH,KAAK,CAAC,SAAS;IACvD,IAAI,CAACf,WAAW,GAAGe,KAAK;EAC5B,CAAC;EACDhB,MAAM,CAACE,SAAS,CAACkB,SAAS,GAAG,UAAUJ,KAAK,EAAE;IAC1C,IAAI,CAACL,MAAM,GAAGK,KAAK;EACvB,CAAC;EACD;EACAhB,MAAM,CAACqB,kBAAkB,GAAG,UAAUpB,WAAW,CAAC,SAAS;IACvD,OAAOA,WAAW,IAAI,CAAC,IAAIA,WAAW,GAAGD,MAAM,CAACsB,iBAAiB;EACrE,CAAC;EACDtB,MAAM,CAACsB,iBAAiB,GAAG,CAAC;EAC5B,OAAOtB,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}