{"ast": null, "code": "import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ToggleButtonGroupContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ToggleButtonGroupContext.displayName = 'ToggleButtonGroupContext';\n}\nexport default ToggleButtonGroupContext;", "map": {"version": 3, "names": ["React", "ToggleButtonGroupContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/material/ToggleButtonGroup/ToggleButtonGroupContext.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst ToggleButtonGroupContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ToggleButtonGroupContext.displayName = 'ToggleButtonGroupContext';\n}\nexport default ToggleButtonGroupContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AACrE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,wBAAwB,CAACK,WAAW,GAAG,0BAA0B;AACnE;AACA,eAAeL,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}