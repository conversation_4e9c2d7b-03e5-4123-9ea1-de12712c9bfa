{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport BitSource from '../../common/BitSource';\nimport CharacterSetECI from '../../common/CharacterSetECI';\nimport DecoderResult from '../../common/DecoderResult';\nimport StringUtils from '../../common/StringUtils';\nimport FormatException from '../../FormatException';\nimport StringBuilder from '../../util/StringBuilder';\nimport StringEncoding from '../../util/StringEncoding';\nimport Mode from './Mode';\n/*import java.io.UnsupportedEncodingException;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collection;*/\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * <p>QR Codes can encode text as bits in one of several modes, and can use multiple modes\n * in one QR Code. This class decodes the bits back into text.</p>\n *\n * <p>See ISO 18004:2006, 6.4.3 - 6.4.7</p>\n *\n * <AUTHOR> Owen\n */\nvar DecodedBitStreamParser = /** @class */function () {\n  function DecodedBitStreamParser() {}\n  DecodedBitStreamParser.decode = function (bytes, version, ecLevel, hints) {\n    var bits = new BitSource(bytes);\n    var result = new StringBuilder();\n    var byteSegments = new Array(); // 1\n    // TYPESCRIPTPORT: I do not use constructor with size 1 as in original Java means capacity and the array length is checked below\n    var symbolSequence = -1;\n    var parityData = -1;\n    try {\n      var currentCharacterSetECI = null;\n      var fc1InEffect = false;\n      var mode = void 0;\n      do {\n        // While still another segment to read...\n        if (bits.available() < 4) {\n          // OK, assume we're done. Really, a TERMINATOR mode should have been recorded here\n          mode = Mode.TERMINATOR;\n        } else {\n          var modeBits = bits.readBits(4);\n          mode = Mode.forBits(modeBits); // mode is encoded by 4 bits\n        }\n        switch (mode) {\n          case Mode.TERMINATOR:\n            break;\n          case Mode.FNC1_FIRST_POSITION:\n          case Mode.FNC1_SECOND_POSITION:\n            // We do little with FNC1 except alter the parsed result a bit according to the spec\n            fc1InEffect = true;\n            break;\n          case Mode.STRUCTURED_APPEND:\n            if (bits.available() < 16) {\n              throw new FormatException();\n            }\n            // sequence number and parity is added later to the result metadata\n            // Read next 8 bits (symbol sequence #) and 8 bits (data: parity), then continue\n            symbolSequence = bits.readBits(8);\n            parityData = bits.readBits(8);\n            break;\n          case Mode.ECI:\n            // Count doesn't apply to ECI\n            var value = DecodedBitStreamParser.parseECIValue(bits);\n            currentCharacterSetECI = CharacterSetECI.getCharacterSetECIByValue(value);\n            if (currentCharacterSetECI === null) {\n              throw new FormatException();\n            }\n            break;\n          case Mode.HANZI:\n            // First handle Hanzi mode which does not start with character count\n            // Chinese mode contains a sub set indicator right after mode indicator\n            var subset = bits.readBits(4);\n            var countHanzi = bits.readBits(mode.getCharacterCountBits(version));\n            if (subset === DecodedBitStreamParser.GB2312_SUBSET) {\n              DecodedBitStreamParser.decodeHanziSegment(bits, result, countHanzi);\n            }\n            break;\n          default:\n            // \"Normal\" QR code modes:\n            // How many characters will follow, encoded in this mode?\n            var count = bits.readBits(mode.getCharacterCountBits(version));\n            switch (mode) {\n              case Mode.NUMERIC:\n                DecodedBitStreamParser.decodeNumericSegment(bits, result, count);\n                break;\n              case Mode.ALPHANUMERIC:\n                DecodedBitStreamParser.decodeAlphanumericSegment(bits, result, count, fc1InEffect);\n                break;\n              case Mode.BYTE:\n                DecodedBitStreamParser.decodeByteSegment(bits, result, count, currentCharacterSetECI, byteSegments, hints);\n                break;\n              case Mode.KANJI:\n                DecodedBitStreamParser.decodeKanjiSegment(bits, result, count);\n                break;\n              default:\n                throw new FormatException();\n            }\n            break;\n        }\n      } while (mode !== Mode.TERMINATOR);\n    } catch (iae /*: IllegalArgumentException*/) {\n      // from readBits() calls\n      throw new FormatException();\n    }\n    return new DecoderResult(bytes, result.toString(), byteSegments.length === 0 ? null : byteSegments, ecLevel === null ? null : ecLevel.toString(), symbolSequence, parityData);\n  };\n  /**\n   * See specification GBT 18284-2000\n   */\n  DecodedBitStreamParser.decodeHanziSegment = function (bits, result, count /*int*/) {\n    // Don't crash trying to read more bits than we have available.\n    if (count * 13 > bits.available()) {\n      throw new FormatException();\n    }\n    // Each character will require 2 bytes. Read the characters as 2-byte pairs\n    // and decode as GB2312 afterwards\n    var buffer = new Uint8Array(2 * count);\n    var offset = 0;\n    while (count > 0) {\n      // Each 13 bits encodes a 2-byte character\n      var twoBytes = bits.readBits(13);\n      var assembledTwoBytes = twoBytes / 0x060 << 8 & 0xFFFFFFFF | twoBytes % 0x060;\n      if (assembledTwoBytes < 0x003BF) {\n        // In the 0xA1A1 to 0xAAFE range\n        assembledTwoBytes += 0x0A1A1;\n      } else {\n        // In the 0xB0A1 to 0xFAFE range\n        assembledTwoBytes += 0x0A6A1;\n      }\n      buffer[offset] = /*(byte) */assembledTwoBytes >> 8 & 0xFF;\n      buffer[offset + 1] = /*(byte) */assembledTwoBytes & 0xFF;\n      offset += 2;\n      count--;\n    }\n    try {\n      result.append(StringEncoding.decode(buffer, StringUtils.GB2312));\n      // TYPESCRIPTPORT: TODO: implement GB2312 decode. StringView from MDN could be a starting point\n    } catch (ignored /*: UnsupportedEncodingException*/) {\n      throw new FormatException(ignored);\n    }\n  };\n  DecodedBitStreamParser.decodeKanjiSegment = function (bits, result, count /*int*/) {\n    // Don't crash trying to read more bits than we have available.\n    if (count * 13 > bits.available()) {\n      throw new FormatException();\n    }\n    // Each character will require 2 bytes. Read the characters as 2-byte pairs\n    // and decode as Shift_JIS afterwards\n    var buffer = new Uint8Array(2 * count);\n    var offset = 0;\n    while (count > 0) {\n      // Each 13 bits encodes a 2-byte character\n      var twoBytes = bits.readBits(13);\n      var assembledTwoBytes = twoBytes / 0x0C0 << 8 & 0xFFFFFFFF | twoBytes % 0x0C0;\n      if (assembledTwoBytes < 0x01F00) {\n        // In the 0x8140 to 0x9FFC range\n        assembledTwoBytes += 0x08140;\n      } else {\n        // In the 0xE040 to 0xEBBF range\n        assembledTwoBytes += 0x0C140;\n      }\n      buffer[offset] = /*(byte) */assembledTwoBytes >> 8;\n      buffer[offset + 1] = /*(byte) */assembledTwoBytes;\n      offset += 2;\n      count--;\n    }\n    // Shift_JIS may not be supported in some environments:\n    try {\n      result.append(StringEncoding.decode(buffer, StringUtils.SHIFT_JIS));\n      // TYPESCRIPTPORT: TODO: implement SHIFT_JIS decode. StringView from MDN could be a starting point\n    } catch (ignored /*: UnsupportedEncodingException*/) {\n      throw new FormatException(ignored);\n    }\n  };\n  DecodedBitStreamParser.decodeByteSegment = function (bits, result, count /*int*/, currentCharacterSetECI, byteSegments, hints) {\n    // Don't crash trying to read more bits than we have available.\n    if (8 * count > bits.available()) {\n      throw new FormatException();\n    }\n    var readBytes = new Uint8Array(count);\n    for (var i = 0; i < count; i++) {\n      readBytes[i] = /*(byte) */bits.readBits(8);\n    }\n    var encoding;\n    if (currentCharacterSetECI === null) {\n      // The spec isn't clear on this mode; see\n      // section 6.4.5: t does not say which encoding to assuming\n      // upon decoding. I have seen ISO-8859-1 used as well as\n      // Shift_JIS -- without anything like an ECI designator to\n      // give a hint.\n      encoding = StringUtils.guessEncoding(readBytes, hints);\n    } else {\n      encoding = currentCharacterSetECI.getName();\n    }\n    try {\n      result.append(StringEncoding.decode(readBytes, encoding));\n    } catch (ignored /*: UnsupportedEncodingException*/) {\n      throw new FormatException(ignored);\n    }\n    byteSegments.push(readBytes);\n  };\n  DecodedBitStreamParser.toAlphaNumericChar = function (value /*int*/) {\n    if (value >= DecodedBitStreamParser.ALPHANUMERIC_CHARS.length) {\n      throw new FormatException();\n    }\n    return DecodedBitStreamParser.ALPHANUMERIC_CHARS[value];\n  };\n  DecodedBitStreamParser.decodeAlphanumericSegment = function (bits, result, count /*int*/, fc1InEffect) {\n    // Read two characters at a time\n    var start = result.length();\n    while (count > 1) {\n      if (bits.available() < 11) {\n        throw new FormatException();\n      }\n      var nextTwoCharsBits = bits.readBits(11);\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(nextTwoCharsBits / 45)));\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(nextTwoCharsBits % 45));\n      count -= 2;\n    }\n    if (count === 1) {\n      // special case: one character left\n      if (bits.available() < 6) {\n        throw new FormatException();\n      }\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(bits.readBits(6)));\n    }\n    // See section 6.4.8.1, 6.4.8.2\n    if (fc1InEffect) {\n      // We need to massage the result a bit if in an FNC1 mode:\n      for (var i = start; i < result.length(); i++) {\n        if (result.charAt(i) === '%') {\n          if (i < result.length() - 1 && result.charAt(i + 1) === '%') {\n            // %% is rendered as %\n            result.deleteCharAt(i + 1);\n          } else {\n            // In alpha mode, % should be converted to FNC1 separator 0x1D\n            result.setCharAt(i, String.fromCharCode(0x1D));\n          }\n        }\n      }\n    }\n  };\n  DecodedBitStreamParser.decodeNumericSegment = function (bits, result, count /*int*/) {\n    // Read three digits at a time\n    while (count >= 3) {\n      // Each 10 bits encodes three digits\n      if (bits.available() < 10) {\n        throw new FormatException();\n      }\n      var threeDigitsBits = bits.readBits(10);\n      if (threeDigitsBits >= 1000) {\n        throw new FormatException();\n      }\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(threeDigitsBits / 100)));\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(threeDigitsBits / 10) % 10));\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(threeDigitsBits % 10));\n      count -= 3;\n    }\n    if (count === 2) {\n      // Two digits left over to read, encoded in 7 bits\n      if (bits.available() < 7) {\n        throw new FormatException();\n      }\n      var twoDigitsBits = bits.readBits(7);\n      if (twoDigitsBits >= 100) {\n        throw new FormatException();\n      }\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(twoDigitsBits / 10)));\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(twoDigitsBits % 10));\n    } else if (count === 1) {\n      // One digit left over to read\n      if (bits.available() < 4) {\n        throw new FormatException();\n      }\n      var digitBits = bits.readBits(4);\n      if (digitBits >= 10) {\n        throw new FormatException();\n      }\n      result.append(DecodedBitStreamParser.toAlphaNumericChar(digitBits));\n    }\n  };\n  DecodedBitStreamParser.parseECIValue = function (bits) {\n    var firstByte = bits.readBits(8);\n    if ((firstByte & 0x80) === 0) {\n      // just one byte\n      return firstByte & 0x7F;\n    }\n    if ((firstByte & 0xC0) === 0x80) {\n      // two bytes\n      var secondByte = bits.readBits(8);\n      return (firstByte & 0x3F) << 8 & 0xFFFFFFFF | secondByte;\n    }\n    if ((firstByte & 0xE0) === 0xC0) {\n      // three bytes\n      var secondThirdBytes = bits.readBits(16);\n      return (firstByte & 0x1F) << 16 & 0xFFFFFFFF | secondThirdBytes;\n    }\n    throw new FormatException();\n  };\n  /**\n   * See ISO 18004:2006, 6.4.4 Table 5\n   */\n  DecodedBitStreamParser.ALPHANUMERIC_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:';\n  DecodedBitStreamParser.GB2312_SUBSET = 1;\n  return DecodedBitStreamParser;\n}();\nexport default DecodedBitStreamParser;\n// function Uint8ArrayToString(a: Uint8Array): string {\n//     const CHUNK_SZ = 0x8000;\n//     const c = new StringBuilder();\n//     for (let i = 0, length = a.length; i < length; i += CHUNK_SZ) {\n//         c.append(String.fromCharCode.apply(null, a.subarray(i, i + CHUNK_SZ)));\n//     }\n//     return c.toString();\n// }", "map": {"version": 3, "names": ["BitSource", "CharacterSetECI", "DecoderResult", "StringUtils", "FormatException", "StringBuilder", "StringEncoding", "Mode", "DecodedBitStreamParser", "decode", "bytes", "version", "ecLevel", "hints", "bits", "result", "byteSegments", "Array", "symbolSequence", "parityData", "currentCharacterSetECI", "fc1InEffect", "mode", "available", "TERMINATOR", "modeBits", "readBits", "forBits", "FNC1_FIRST_POSITION", "FNC1_SECOND_POSITION", "STRUCTURED_APPEND", "ECI", "value", "parseECIValue", "getCharacterSetECIByValue", "HANZI", "subset", "<PERSON><PERSON><PERSON><PERSON>", "getCharacterCountBits", "GB2312_SUBSET", "decodeHanziSegment", "count", "NUMERIC", "decodeNumericSegment", "ALPHANUMERIC", "decodeAlphanumericSegment", "BYTE", "decodeByteSegment", "KANJI", "decodeKanjiSegment", "iae", "toString", "length", "buffer", "Uint8Array", "offset", "twoBytes", "assembledTwoBytes", "append", "GB2312", "ignored", "SHIFT_JIS", "readBytes", "i", "encoding", "guessEncoding", "getName", "push", "toAlphaNumericChar", "ALPHANUMERIC_CHARS", "start", "nextTwoCharsBits", "Math", "floor", "char<PERSON>t", "deleteCharAt", "setCharAt", "String", "fromCharCode", "threeDigitsBits", "twoDigitsBits", "digitBits", "firstByte", "secondByte", "secondThirdBytes"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/DecodedBitStreamParser.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport BitSource from '../../common/BitSource';\nimport CharacterSetECI from '../../common/CharacterSetECI';\nimport DecoderResult from '../../common/DecoderResult';\nimport StringUtils from '../../common/StringUtils';\nimport FormatException from '../../FormatException';\nimport StringBuilder from '../../util/StringBuilder';\nimport StringEncoding from '../../util/StringEncoding';\nimport Mode from './Mode';\n/*import java.io.UnsupportedEncodingException;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collection;*/\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * <p>QR Codes can encode text as bits in one of several modes, and can use multiple modes\n * in one QR Code. This class decodes the bits back into text.</p>\n *\n * <p>See ISO 18004:2006, 6.4.3 - 6.4.7</p>\n *\n * <AUTHOR> Owen\n */\nvar DecodedBitStreamParser = /** @class */ (function () {\n    function DecodedBitStreamParser() {\n    }\n    DecodedBitStreamParser.decode = function (bytes, version, ecLevel, hints) {\n        var bits = new BitSource(bytes);\n        var result = new StringBuilder();\n        var byteSegments = new Array(); // 1\n        // TYPESCRIPTPORT: I do not use constructor with size 1 as in original Java means capacity and the array length is checked below\n        var symbolSequence = -1;\n        var parityData = -1;\n        try {\n            var currentCharacterSetECI = null;\n            var fc1InEffect = false;\n            var mode = void 0;\n            do {\n                // While still another segment to read...\n                if (bits.available() < 4) {\n                    // OK, assume we're done. Really, a TERMINATOR mode should have been recorded here\n                    mode = Mode.TERMINATOR;\n                }\n                else {\n                    var modeBits = bits.readBits(4);\n                    mode = Mode.forBits(modeBits); // mode is encoded by 4 bits\n                }\n                switch (mode) {\n                    case Mode.TERMINATOR:\n                        break;\n                    case Mode.FNC1_FIRST_POSITION:\n                    case Mode.FNC1_SECOND_POSITION:\n                        // We do little with FNC1 except alter the parsed result a bit according to the spec\n                        fc1InEffect = true;\n                        break;\n                    case Mode.STRUCTURED_APPEND:\n                        if (bits.available() < 16) {\n                            throw new FormatException();\n                        }\n                        // sequence number and parity is added later to the result metadata\n                        // Read next 8 bits (symbol sequence #) and 8 bits (data: parity), then continue\n                        symbolSequence = bits.readBits(8);\n                        parityData = bits.readBits(8);\n                        break;\n                    case Mode.ECI:\n                        // Count doesn't apply to ECI\n                        var value = DecodedBitStreamParser.parseECIValue(bits);\n                        currentCharacterSetECI = CharacterSetECI.getCharacterSetECIByValue(value);\n                        if (currentCharacterSetECI === null) {\n                            throw new FormatException();\n                        }\n                        break;\n                    case Mode.HANZI:\n                        // First handle Hanzi mode which does not start with character count\n                        // Chinese mode contains a sub set indicator right after mode indicator\n                        var subset = bits.readBits(4);\n                        var countHanzi = bits.readBits(mode.getCharacterCountBits(version));\n                        if (subset === DecodedBitStreamParser.GB2312_SUBSET) {\n                            DecodedBitStreamParser.decodeHanziSegment(bits, result, countHanzi);\n                        }\n                        break;\n                    default:\n                        // \"Normal\" QR code modes:\n                        // How many characters will follow, encoded in this mode?\n                        var count = bits.readBits(mode.getCharacterCountBits(version));\n                        switch (mode) {\n                            case Mode.NUMERIC:\n                                DecodedBitStreamParser.decodeNumericSegment(bits, result, count);\n                                break;\n                            case Mode.ALPHANUMERIC:\n                                DecodedBitStreamParser.decodeAlphanumericSegment(bits, result, count, fc1InEffect);\n                                break;\n                            case Mode.BYTE:\n                                DecodedBitStreamParser.decodeByteSegment(bits, result, count, currentCharacterSetECI, byteSegments, hints);\n                                break;\n                            case Mode.KANJI:\n                                DecodedBitStreamParser.decodeKanjiSegment(bits, result, count);\n                                break;\n                            default:\n                                throw new FormatException();\n                        }\n                        break;\n                }\n            } while (mode !== Mode.TERMINATOR);\n        }\n        catch (iae /*: IllegalArgumentException*/) {\n            // from readBits() calls\n            throw new FormatException();\n        }\n        return new DecoderResult(bytes, result.toString(), byteSegments.length === 0 ? null : byteSegments, ecLevel === null ? null : ecLevel.toString(), symbolSequence, parityData);\n    };\n    /**\n     * See specification GBT 18284-2000\n     */\n    DecodedBitStreamParser.decodeHanziSegment = function (bits, result, count /*int*/) {\n        // Don't crash trying to read more bits than we have available.\n        if (count * 13 > bits.available()) {\n            throw new FormatException();\n        }\n        // Each character will require 2 bytes. Read the characters as 2-byte pairs\n        // and decode as GB2312 afterwards\n        var buffer = new Uint8Array(2 * count);\n        var offset = 0;\n        while (count > 0) {\n            // Each 13 bits encodes a 2-byte character\n            var twoBytes = bits.readBits(13);\n            var assembledTwoBytes = (((twoBytes / 0x060) << 8) & 0xFFFFFFFF) | (twoBytes % 0x060);\n            if (assembledTwoBytes < 0x003BF) {\n                // In the 0xA1A1 to 0xAAFE range\n                assembledTwoBytes += 0x0A1A1;\n            }\n            else {\n                // In the 0xB0A1 to 0xFAFE range\n                assembledTwoBytes += 0x0A6A1;\n            }\n            buffer[offset] = /*(byte) */ ((assembledTwoBytes >> 8) & 0xFF);\n            buffer[offset + 1] = /*(byte) */ (assembledTwoBytes & 0xFF);\n            offset += 2;\n            count--;\n        }\n        try {\n            result.append(StringEncoding.decode(buffer, StringUtils.GB2312));\n            // TYPESCRIPTPORT: TODO: implement GB2312 decode. StringView from MDN could be a starting point\n        }\n        catch (ignored /*: UnsupportedEncodingException*/) {\n            throw new FormatException(ignored);\n        }\n    };\n    DecodedBitStreamParser.decodeKanjiSegment = function (bits, result, count /*int*/) {\n        // Don't crash trying to read more bits than we have available.\n        if (count * 13 > bits.available()) {\n            throw new FormatException();\n        }\n        // Each character will require 2 bytes. Read the characters as 2-byte pairs\n        // and decode as Shift_JIS afterwards\n        var buffer = new Uint8Array(2 * count);\n        var offset = 0;\n        while (count > 0) {\n            // Each 13 bits encodes a 2-byte character\n            var twoBytes = bits.readBits(13);\n            var assembledTwoBytes = (((twoBytes / 0x0C0) << 8) & 0xFFFFFFFF) | (twoBytes % 0x0C0);\n            if (assembledTwoBytes < 0x01F00) {\n                // In the 0x8140 to 0x9FFC range\n                assembledTwoBytes += 0x08140;\n            }\n            else {\n                // In the 0xE040 to 0xEBBF range\n                assembledTwoBytes += 0x0C140;\n            }\n            buffer[offset] = /*(byte) */ (assembledTwoBytes >> 8);\n            buffer[offset + 1] = /*(byte) */ assembledTwoBytes;\n            offset += 2;\n            count--;\n        }\n        // Shift_JIS may not be supported in some environments:\n        try {\n            result.append(StringEncoding.decode(buffer, StringUtils.SHIFT_JIS));\n            // TYPESCRIPTPORT: TODO: implement SHIFT_JIS decode. StringView from MDN could be a starting point\n        }\n        catch (ignored /*: UnsupportedEncodingException*/) {\n            throw new FormatException(ignored);\n        }\n    };\n    DecodedBitStreamParser.decodeByteSegment = function (bits, result, count /*int*/, currentCharacterSetECI, byteSegments, hints) {\n        // Don't crash trying to read more bits than we have available.\n        if (8 * count > bits.available()) {\n            throw new FormatException();\n        }\n        var readBytes = new Uint8Array(count);\n        for (var i = 0; i < count; i++) {\n            readBytes[i] = /*(byte) */ bits.readBits(8);\n        }\n        var encoding;\n        if (currentCharacterSetECI === null) {\n            // The spec isn't clear on this mode; see\n            // section 6.4.5: t does not say which encoding to assuming\n            // upon decoding. I have seen ISO-8859-1 used as well as\n            // Shift_JIS -- without anything like an ECI designator to\n            // give a hint.\n            encoding = StringUtils.guessEncoding(readBytes, hints);\n        }\n        else {\n            encoding = currentCharacterSetECI.getName();\n        }\n        try {\n            result.append(StringEncoding.decode(readBytes, encoding));\n        }\n        catch (ignored /*: UnsupportedEncodingException*/) {\n            throw new FormatException(ignored);\n        }\n        byteSegments.push(readBytes);\n    };\n    DecodedBitStreamParser.toAlphaNumericChar = function (value /*int*/) {\n        if (value >= DecodedBitStreamParser.ALPHANUMERIC_CHARS.length) {\n            throw new FormatException();\n        }\n        return DecodedBitStreamParser.ALPHANUMERIC_CHARS[value];\n    };\n    DecodedBitStreamParser.decodeAlphanumericSegment = function (bits, result, count /*int*/, fc1InEffect) {\n        // Read two characters at a time\n        var start = result.length();\n        while (count > 1) {\n            if (bits.available() < 11) {\n                throw new FormatException();\n            }\n            var nextTwoCharsBits = bits.readBits(11);\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(nextTwoCharsBits / 45)));\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(nextTwoCharsBits % 45));\n            count -= 2;\n        }\n        if (count === 1) {\n            // special case: one character left\n            if (bits.available() < 6) {\n                throw new FormatException();\n            }\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(bits.readBits(6)));\n        }\n        // See section 6.4.8.1, 6.4.8.2\n        if (fc1InEffect) {\n            // We need to massage the result a bit if in an FNC1 mode:\n            for (var i = start; i < result.length(); i++) {\n                if (result.charAt(i) === '%') {\n                    if (i < result.length() - 1 && result.charAt(i + 1) === '%') {\n                        // %% is rendered as %\n                        result.deleteCharAt(i + 1);\n                    }\n                    else {\n                        // In alpha mode, % should be converted to FNC1 separator 0x1D\n                        result.setCharAt(i, String.fromCharCode(0x1D));\n                    }\n                }\n            }\n        }\n    };\n    DecodedBitStreamParser.decodeNumericSegment = function (bits, result, count /*int*/) {\n        // Read three digits at a time\n        while (count >= 3) {\n            // Each 10 bits encodes three digits\n            if (bits.available() < 10) {\n                throw new FormatException();\n            }\n            var threeDigitsBits = bits.readBits(10);\n            if (threeDigitsBits >= 1000) {\n                throw new FormatException();\n            }\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(threeDigitsBits / 100)));\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(threeDigitsBits / 10) % 10));\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(threeDigitsBits % 10));\n            count -= 3;\n        }\n        if (count === 2) {\n            // Two digits left over to read, encoded in 7 bits\n            if (bits.available() < 7) {\n                throw new FormatException();\n            }\n            var twoDigitsBits = bits.readBits(7);\n            if (twoDigitsBits >= 100) {\n                throw new FormatException();\n            }\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(Math.floor(twoDigitsBits / 10)));\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(twoDigitsBits % 10));\n        }\n        else if (count === 1) {\n            // One digit left over to read\n            if (bits.available() < 4) {\n                throw new FormatException();\n            }\n            var digitBits = bits.readBits(4);\n            if (digitBits >= 10) {\n                throw new FormatException();\n            }\n            result.append(DecodedBitStreamParser.toAlphaNumericChar(digitBits));\n        }\n    };\n    DecodedBitStreamParser.parseECIValue = function (bits) {\n        var firstByte = bits.readBits(8);\n        if ((firstByte & 0x80) === 0) {\n            // just one byte\n            return firstByte & 0x7F;\n        }\n        if ((firstByte & 0xC0) === 0x80) {\n            // two bytes\n            var secondByte = bits.readBits(8);\n            return (((firstByte & 0x3F) << 8) & 0xFFFFFFFF) | secondByte;\n        }\n        if ((firstByte & 0xE0) === 0xC0) {\n            // three bytes\n            var secondThirdBytes = bits.readBits(16);\n            return (((firstByte & 0x1F) << 16) & 0xFFFFFFFF) | secondThirdBytes;\n        }\n        throw new FormatException();\n    };\n    /**\n     * See ISO 18004:2006, 6.4.4 Table 5\n     */\n    DecodedBitStreamParser.ALPHANUMERIC_CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:';\n    DecodedBitStreamParser.GB2312_SUBSET = 1;\n    return DecodedBitStreamParser;\n}());\nexport default DecodedBitStreamParser;\n// function Uint8ArrayToString(a: Uint8Array): string {\n//     const CHUNK_SZ = 0x8000;\n//     const c = new StringBuilder();\n//     for (let i = 0, length = a.length; i < length; i += CHUNK_SZ) {\n//         c.append(String.fromCharCode.apply(null, a.subarray(i, i + CHUNK_SZ)));\n//     }\n//     return c.toString();\n// }\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,IAAI,MAAM,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,aAAe,YAAY;EACpD,SAASA,sBAAsBA,CAAA,EAAG,CAClC;EACAA,sBAAsB,CAACC,MAAM,GAAG,UAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACtE,IAAIC,IAAI,GAAG,IAAId,SAAS,CAACU,KAAK,CAAC;IAC/B,IAAIK,MAAM,GAAG,IAAIV,aAAa,CAAC,CAAC;IAChC,IAAIW,YAAY,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAAC;IAChC;IACA,IAAIC,cAAc,GAAG,CAAC,CAAC;IACvB,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI;MACA,IAAIC,sBAAsB,GAAG,IAAI;MACjC,IAAIC,WAAW,GAAG,KAAK;MACvB,IAAIC,IAAI,GAAG,KAAK,CAAC;MACjB,GAAG;QACC;QACA,IAAIR,IAAI,CAACS,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;UACtB;UACAD,IAAI,GAAGf,IAAI,CAACiB,UAAU;QAC1B,CAAC,MACI;UACD,IAAIC,QAAQ,GAAGX,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;UAC/BJ,IAAI,GAAGf,IAAI,CAACoB,OAAO,CAACF,QAAQ,CAAC,CAAC,CAAC;QACnC;QACA,QAAQH,IAAI;UACR,KAAKf,IAAI,CAACiB,UAAU;YAChB;UACJ,KAAKjB,IAAI,CAACqB,mBAAmB;UAC7B,KAAKrB,IAAI,CAACsB,oBAAoB;YAC1B;YACAR,WAAW,GAAG,IAAI;YAClB;UACJ,KAAKd,IAAI,CAACuB,iBAAiB;YACvB,IAAIhB,IAAI,CAACS,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE;cACvB,MAAM,IAAInB,eAAe,CAAC,CAAC;YAC/B;YACA;YACA;YACAc,cAAc,GAAGJ,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;YACjCP,UAAU,GAAGL,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;YAC7B;UACJ,KAAKnB,IAAI,CAACwB,GAAG;YACT;YACA,IAAIC,KAAK,GAAGxB,sBAAsB,CAACyB,aAAa,CAACnB,IAAI,CAAC;YACtDM,sBAAsB,GAAGnB,eAAe,CAACiC,yBAAyB,CAACF,KAAK,CAAC;YACzE,IAAIZ,sBAAsB,KAAK,IAAI,EAAE;cACjC,MAAM,IAAIhB,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAKG,IAAI,CAAC4B,KAAK;YACX;YACA;YACA,IAAIC,MAAM,GAAGtB,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;YAC7B,IAAIW,UAAU,GAAGvB,IAAI,CAACY,QAAQ,CAACJ,IAAI,CAACgB,qBAAqB,CAAC3B,OAAO,CAAC,CAAC;YACnE,IAAIyB,MAAM,KAAK5B,sBAAsB,CAAC+B,aAAa,EAAE;cACjD/B,sBAAsB,CAACgC,kBAAkB,CAAC1B,IAAI,EAAEC,MAAM,EAAEsB,UAAU,CAAC;YACvE;YACA;UACJ;YACI;YACA;YACA,IAAII,KAAK,GAAG3B,IAAI,CAACY,QAAQ,CAACJ,IAAI,CAACgB,qBAAqB,CAAC3B,OAAO,CAAC,CAAC;YAC9D,QAAQW,IAAI;cACR,KAAKf,IAAI,CAACmC,OAAO;gBACblC,sBAAsB,CAACmC,oBAAoB,CAAC7B,IAAI,EAAEC,MAAM,EAAE0B,KAAK,CAAC;gBAChE;cACJ,KAAKlC,IAAI,CAACqC,YAAY;gBAClBpC,sBAAsB,CAACqC,yBAAyB,CAAC/B,IAAI,EAAEC,MAAM,EAAE0B,KAAK,EAAEpB,WAAW,CAAC;gBAClF;cACJ,KAAKd,IAAI,CAACuC,IAAI;gBACVtC,sBAAsB,CAACuC,iBAAiB,CAACjC,IAAI,EAAEC,MAAM,EAAE0B,KAAK,EAAErB,sBAAsB,EAAEJ,YAAY,EAAEH,KAAK,CAAC;gBAC1G;cACJ,KAAKN,IAAI,CAACyC,KAAK;gBACXxC,sBAAsB,CAACyC,kBAAkB,CAACnC,IAAI,EAAEC,MAAM,EAAE0B,KAAK,CAAC;gBAC9D;cACJ;gBACI,MAAM,IAAIrC,eAAe,CAAC,CAAC;YACnC;YACA;QACR;MACJ,CAAC,QAAQkB,IAAI,KAAKf,IAAI,CAACiB,UAAU;IACrC,CAAC,CACD,OAAO0B,GAAG,CAAC,gCAAgC;MACvC;MACA,MAAM,IAAI9C,eAAe,CAAC,CAAC;IAC/B;IACA,OAAO,IAAIF,aAAa,CAACQ,KAAK,EAAEK,MAAM,CAACoC,QAAQ,CAAC,CAAC,EAAEnC,YAAY,CAACoC,MAAM,KAAK,CAAC,GAAG,IAAI,GAAGpC,YAAY,EAAEJ,OAAO,KAAK,IAAI,GAAG,IAAI,GAAGA,OAAO,CAACuC,QAAQ,CAAC,CAAC,EAAEjC,cAAc,EAAEC,UAAU,CAAC;EACjL,CAAC;EACD;AACJ;AACA;EACIX,sBAAsB,CAACgC,kBAAkB,GAAG,UAAU1B,IAAI,EAAEC,MAAM,EAAE0B,KAAK,CAAC,SAAS;IAC/E;IACA,IAAIA,KAAK,GAAG,EAAE,GAAG3B,IAAI,CAACS,SAAS,CAAC,CAAC,EAAE;MAC/B,MAAM,IAAInB,eAAe,CAAC,CAAC;IAC/B;IACA;IACA;IACA,IAAIiD,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC,GAAGb,KAAK,CAAC;IACtC,IAAIc,MAAM,GAAG,CAAC;IACd,OAAOd,KAAK,GAAG,CAAC,EAAE;MACd;MACA,IAAIe,QAAQ,GAAG1C,IAAI,CAACY,QAAQ,CAAC,EAAE,CAAC;MAChC,IAAI+B,iBAAiB,GAAMD,QAAQ,GAAG,KAAK,IAAK,CAAC,GAAI,UAAU,GAAKA,QAAQ,GAAG,KAAM;MACrF,IAAIC,iBAAiB,GAAG,OAAO,EAAE;QAC7B;QACAA,iBAAiB,IAAI,OAAO;MAChC,CAAC,MACI;QACD;QACAA,iBAAiB,IAAI,OAAO;MAChC;MACAJ,MAAM,CAACE,MAAM,CAAC,GAAG,WAAcE,iBAAiB,IAAI,CAAC,GAAI,IAAK;MAC9DJ,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,WAAaE,iBAAiB,GAAG,IAAK;MAC3DF,MAAM,IAAI,CAAC;MACXd,KAAK,EAAE;IACX;IACA,IAAI;MACA1B,MAAM,CAAC2C,MAAM,CAACpD,cAAc,CAACG,MAAM,CAAC4C,MAAM,EAAElD,WAAW,CAACwD,MAAM,CAAC,CAAC;MAChE;IACJ,CAAC,CACD,OAAOC,OAAO,CAAC,oCAAoC;MAC/C,MAAM,IAAIxD,eAAe,CAACwD,OAAO,CAAC;IACtC;EACJ,CAAC;EACDpD,sBAAsB,CAACyC,kBAAkB,GAAG,UAAUnC,IAAI,EAAEC,MAAM,EAAE0B,KAAK,CAAC,SAAS;IAC/E;IACA,IAAIA,KAAK,GAAG,EAAE,GAAG3B,IAAI,CAACS,SAAS,CAAC,CAAC,EAAE;MAC/B,MAAM,IAAInB,eAAe,CAAC,CAAC;IAC/B;IACA;IACA;IACA,IAAIiD,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC,GAAGb,KAAK,CAAC;IACtC,IAAIc,MAAM,GAAG,CAAC;IACd,OAAOd,KAAK,GAAG,CAAC,EAAE;MACd;MACA,IAAIe,QAAQ,GAAG1C,IAAI,CAACY,QAAQ,CAAC,EAAE,CAAC;MAChC,IAAI+B,iBAAiB,GAAMD,QAAQ,GAAG,KAAK,IAAK,CAAC,GAAI,UAAU,GAAKA,QAAQ,GAAG,KAAM;MACrF,IAAIC,iBAAiB,GAAG,OAAO,EAAE;QAC7B;QACAA,iBAAiB,IAAI,OAAO;MAChC,CAAC,MACI;QACD;QACAA,iBAAiB,IAAI,OAAO;MAChC;MACAJ,MAAM,CAACE,MAAM,CAAC,GAAG,WAAaE,iBAAiB,IAAI,CAAE;MACrDJ,MAAM,CAACE,MAAM,GAAG,CAAC,CAAC,GAAG,WAAYE,iBAAiB;MAClDF,MAAM,IAAI,CAAC;MACXd,KAAK,EAAE;IACX;IACA;IACA,IAAI;MACA1B,MAAM,CAAC2C,MAAM,CAACpD,cAAc,CAACG,MAAM,CAAC4C,MAAM,EAAElD,WAAW,CAAC0D,SAAS,CAAC,CAAC;MACnE;IACJ,CAAC,CACD,OAAOD,OAAO,CAAC,oCAAoC;MAC/C,MAAM,IAAIxD,eAAe,CAACwD,OAAO,CAAC;IACtC;EACJ,CAAC;EACDpD,sBAAsB,CAACuC,iBAAiB,GAAG,UAAUjC,IAAI,EAAEC,MAAM,EAAE0B,KAAK,CAAC,SAASrB,sBAAsB,EAAEJ,YAAY,EAAEH,KAAK,EAAE;IAC3H;IACA,IAAI,CAAC,GAAG4B,KAAK,GAAG3B,IAAI,CAACS,SAAS,CAAC,CAAC,EAAE;MAC9B,MAAM,IAAInB,eAAe,CAAC,CAAC;IAC/B;IACA,IAAI0D,SAAS,GAAG,IAAIR,UAAU,CAACb,KAAK,CAAC;IACrC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtB,KAAK,EAAEsB,CAAC,EAAE,EAAE;MAC5BD,SAAS,CAACC,CAAC,CAAC,GAAG,WAAYjD,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;IAC/C;IACA,IAAIsC,QAAQ;IACZ,IAAI5C,sBAAsB,KAAK,IAAI,EAAE;MACjC;MACA;MACA;MACA;MACA;MACA4C,QAAQ,GAAG7D,WAAW,CAAC8D,aAAa,CAACH,SAAS,EAAEjD,KAAK,CAAC;IAC1D,CAAC,MACI;MACDmD,QAAQ,GAAG5C,sBAAsB,CAAC8C,OAAO,CAAC,CAAC;IAC/C;IACA,IAAI;MACAnD,MAAM,CAAC2C,MAAM,CAACpD,cAAc,CAACG,MAAM,CAACqD,SAAS,EAAEE,QAAQ,CAAC,CAAC;IAC7D,CAAC,CACD,OAAOJ,OAAO,CAAC,oCAAoC;MAC/C,MAAM,IAAIxD,eAAe,CAACwD,OAAO,CAAC;IACtC;IACA5C,YAAY,CAACmD,IAAI,CAACL,SAAS,CAAC;EAChC,CAAC;EACDtD,sBAAsB,CAAC4D,kBAAkB,GAAG,UAAUpC,KAAK,CAAC,SAAS;IACjE,IAAIA,KAAK,IAAIxB,sBAAsB,CAAC6D,kBAAkB,CAACjB,MAAM,EAAE;MAC3D,MAAM,IAAIhD,eAAe,CAAC,CAAC;IAC/B;IACA,OAAOI,sBAAsB,CAAC6D,kBAAkB,CAACrC,KAAK,CAAC;EAC3D,CAAC;EACDxB,sBAAsB,CAACqC,yBAAyB,GAAG,UAAU/B,IAAI,EAAEC,MAAM,EAAE0B,KAAK,CAAC,SAASpB,WAAW,EAAE;IACnG;IACA,IAAIiD,KAAK,GAAGvD,MAAM,CAACqC,MAAM,CAAC,CAAC;IAC3B,OAAOX,KAAK,GAAG,CAAC,EAAE;MACd,IAAI3B,IAAI,CAACS,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE;QACvB,MAAM,IAAInB,eAAe,CAAC,CAAC;MAC/B;MACA,IAAImE,gBAAgB,GAAGzD,IAAI,CAACY,QAAQ,CAAC,EAAE,CAAC;MACxCX,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACI,IAAI,CAACC,KAAK,CAACF,gBAAgB,GAAG,EAAE,CAAC,CAAC,CAAC;MAC3FxD,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACG,gBAAgB,GAAG,EAAE,CAAC,CAAC;MAC/E9B,KAAK,IAAI,CAAC;IACd;IACA,IAAIA,KAAK,KAAK,CAAC,EAAE;MACb;MACA,IAAI3B,IAAI,CAACS,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,IAAInB,eAAe,CAAC,CAAC;MAC/B;MACAW,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACtD,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9E;IACA;IACA,IAAIL,WAAW,EAAE;MACb;MACA,KAAK,IAAI0C,CAAC,GAAGO,KAAK,EAAEP,CAAC,GAAGhD,MAAM,CAACqC,MAAM,CAAC,CAAC,EAAEW,CAAC,EAAE,EAAE;QAC1C,IAAIhD,MAAM,CAAC2D,MAAM,CAACX,CAAC,CAAC,KAAK,GAAG,EAAE;UAC1B,IAAIA,CAAC,GAAGhD,MAAM,CAACqC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAIrC,MAAM,CAAC2D,MAAM,CAACX,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YACzD;YACAhD,MAAM,CAAC4D,YAAY,CAACZ,CAAC,GAAG,CAAC,CAAC;UAC9B,CAAC,MACI;YACD;YACAhD,MAAM,CAAC6D,SAAS,CAACb,CAAC,EAAEc,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC,CAAC;UAClD;QACJ;MACJ;IACJ;EACJ,CAAC;EACDtE,sBAAsB,CAACmC,oBAAoB,GAAG,UAAU7B,IAAI,EAAEC,MAAM,EAAE0B,KAAK,CAAC,SAAS;IACjF;IACA,OAAOA,KAAK,IAAI,CAAC,EAAE;MACf;MACA,IAAI3B,IAAI,CAACS,SAAS,CAAC,CAAC,GAAG,EAAE,EAAE;QACvB,MAAM,IAAInB,eAAe,CAAC,CAAC;MAC/B;MACA,IAAI2E,eAAe,GAAGjE,IAAI,CAACY,QAAQ,CAAC,EAAE,CAAC;MACvC,IAAIqD,eAAe,IAAI,IAAI,EAAE;QACzB,MAAM,IAAI3E,eAAe,CAAC,CAAC;MAC/B;MACAW,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACI,IAAI,CAACC,KAAK,CAACM,eAAe,GAAG,GAAG,CAAC,CAAC,CAAC;MAC3FhE,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACI,IAAI,CAACC,KAAK,CAACM,eAAe,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;MAC/FhE,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACW,eAAe,GAAG,EAAE,CAAC,CAAC;MAC9EtC,KAAK,IAAI,CAAC;IACd;IACA,IAAIA,KAAK,KAAK,CAAC,EAAE;MACb;MACA,IAAI3B,IAAI,CAACS,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,IAAInB,eAAe,CAAC,CAAC;MAC/B;MACA,IAAI4E,aAAa,GAAGlE,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;MACpC,IAAIsD,aAAa,IAAI,GAAG,EAAE;QACtB,MAAM,IAAI5E,eAAe,CAAC,CAAC;MAC/B;MACAW,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACI,IAAI,CAACC,KAAK,CAACO,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC;MACxFjE,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACY,aAAa,GAAG,EAAE,CAAC,CAAC;IAChF,CAAC,MACI,IAAIvC,KAAK,KAAK,CAAC,EAAE;MAClB;MACA,IAAI3B,IAAI,CAACS,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,IAAInB,eAAe,CAAC,CAAC;MAC/B;MACA,IAAI6E,SAAS,GAAGnE,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;MAChC,IAAIuD,SAAS,IAAI,EAAE,EAAE;QACjB,MAAM,IAAI7E,eAAe,CAAC,CAAC;MAC/B;MACAW,MAAM,CAAC2C,MAAM,CAAClD,sBAAsB,CAAC4D,kBAAkB,CAACa,SAAS,CAAC,CAAC;IACvE;EACJ,CAAC;EACDzE,sBAAsB,CAACyB,aAAa,GAAG,UAAUnB,IAAI,EAAE;IACnD,IAAIoE,SAAS,GAAGpE,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;IAChC,IAAI,CAACwD,SAAS,GAAG,IAAI,MAAM,CAAC,EAAE;MAC1B;MACA,OAAOA,SAAS,GAAG,IAAI;IAC3B;IACA,IAAI,CAACA,SAAS,GAAG,IAAI,MAAM,IAAI,EAAE;MAC7B;MACA,IAAIC,UAAU,GAAGrE,IAAI,CAACY,QAAQ,CAAC,CAAC,CAAC;MACjC,OAAS,CAACwD,SAAS,GAAG,IAAI,KAAK,CAAC,GAAI,UAAU,GAAIC,UAAU;IAChE;IACA,IAAI,CAACD,SAAS,GAAG,IAAI,MAAM,IAAI,EAAE;MAC7B;MACA,IAAIE,gBAAgB,GAAGtE,IAAI,CAACY,QAAQ,CAAC,EAAE,CAAC;MACxC,OAAS,CAACwD,SAAS,GAAG,IAAI,KAAK,EAAE,GAAI,UAAU,GAAIE,gBAAgB;IACvE;IACA,MAAM,IAAIhF,eAAe,CAAC,CAAC;EAC/B,CAAC;EACD;AACJ;AACA;EACII,sBAAsB,CAAC6D,kBAAkB,GAAG,+CAA+C;EAC3F7D,sBAAsB,CAAC+B,aAAa,GAAG,CAAC;EACxC,OAAO/B,sBAAsB;AACjC,CAAC,CAAC,CAAE;AACJ,eAAeA,sBAAsB;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}