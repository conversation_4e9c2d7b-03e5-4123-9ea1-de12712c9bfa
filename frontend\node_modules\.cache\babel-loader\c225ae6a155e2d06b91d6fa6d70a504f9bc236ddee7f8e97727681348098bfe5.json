{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will\n * use blocks of differing sizes within one version, so, this encapsulates the parameters for\n * each set of blocks. It also holds the number of error-correction codewords per block since it\n * will be the same across all blocks within one version.</p>\n */\nvar ECBlocks = /** @class */function () {\n  function ECBlocks(ecCodewordsPerBlock /*int*/) {\n    var ecBlocks = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      ecBlocks[_i - 1] = arguments[_i];\n    }\n    this.ecCodewordsPerBlock = ecCodewordsPerBlock;\n    this.ecBlocks = ecBlocks;\n  }\n  ECBlocks.prototype.getECCodewordsPerBlock = function () {\n    return this.ecCodewordsPerBlock;\n  };\n  ECBlocks.prototype.getNumBlocks = function () {\n    var e_1, _a;\n    var total = 0;\n    var ecBlocks = this.ecBlocks;\n    try {\n      for (var ecBlocks_1 = __values(ecBlocks), ecBlocks_1_1 = ecBlocks_1.next(); !ecBlocks_1_1.done; ecBlocks_1_1 = ecBlocks_1.next()) {\n        var ecBlock = ecBlocks_1_1.value;\n        total += ecBlock.getCount();\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (ecBlocks_1_1 && !ecBlocks_1_1.done && (_a = ecBlocks_1.return)) _a.call(ecBlocks_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    return total;\n  };\n  ECBlocks.prototype.getTotalECCodewords = function () {\n    return this.ecCodewordsPerBlock * this.getNumBlocks();\n  };\n  ECBlocks.prototype.getECBlocks = function () {\n    return this.ecBlocks;\n  };\n  return ECBlocks;\n}();\nexport default ECBlocks;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "ECBlocks", "ecCodewordsPerBlock", "ecBlocks", "_i", "arguments", "prototype", "getECCodewordsPerBlock", "getNumBlocks", "e_1", "_a", "total", "ecBlocks_1", "ecBlocks_1_1", "ecBlock", "getCount", "e_1_1", "error", "return", "getTotalECCodewords", "getECBlocks"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/ECBlocks.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will\n * use blocks of differing sizes within one version, so, this encapsulates the parameters for\n * each set of blocks. It also holds the number of error-correction codewords per block since it\n * will be the same across all blocks within one version.</p>\n */\nvar ECBlocks = /** @class */ (function () {\n    function ECBlocks(ecCodewordsPerBlock /*int*/) {\n        var ecBlocks = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            ecBlocks[_i - 1] = arguments[_i];\n        }\n        this.ecCodewordsPerBlock = ecCodewordsPerBlock;\n        this.ecBlocks = ecBlocks;\n    }\n    ECBlocks.prototype.getECCodewordsPerBlock = function () {\n        return this.ecCodewordsPerBlock;\n    };\n    ECBlocks.prototype.getNumBlocks = function () {\n        var e_1, _a;\n        var total = 0;\n        var ecBlocks = this.ecBlocks;\n        try {\n            for (var ecBlocks_1 = __values(ecBlocks), ecBlocks_1_1 = ecBlocks_1.next(); !ecBlocks_1_1.done; ecBlocks_1_1 = ecBlocks_1.next()) {\n                var ecBlock = ecBlocks_1_1.value;\n                total += ecBlock.getCount();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecBlocks_1_1 && !ecBlocks_1_1.done && (_a = ecBlocks_1.return)) _a.call(ecBlocks_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return total;\n    };\n    ECBlocks.prototype.getTotalECCodewords = function () {\n        return this.ecCodewordsPerBlock * this.getNumBlocks();\n    };\n    ECBlocks.prototype.getECBlocks = function () {\n        return this.ecBlocks;\n    };\n    return ECBlocks;\n}());\nexport default ECBlocks;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIW,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACC,mBAAmB,CAAC,SAAS;IAC3C,IAAIC,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACT,MAAM,EAAEQ,EAAE,EAAE,EAAE;MAC1CD,QAAQ,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IACpC;IACA,IAAI,CAACF,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAF,QAAQ,CAACK,SAAS,CAACC,sBAAsB,GAAG,YAAY;IACpD,OAAO,IAAI,CAACL,mBAAmB;EACnC,CAAC;EACDD,QAAQ,CAACK,SAAS,CAACE,YAAY,GAAG,YAAY;IAC1C,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIR,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAI;MACA,KAAK,IAAIS,UAAU,GAAGxB,QAAQ,CAACe,QAAQ,CAAC,EAAEU,YAAY,GAAGD,UAAU,CAACf,IAAI,CAAC,CAAC,EAAE,CAACgB,YAAY,CAACd,IAAI,EAAEc,YAAY,GAAGD,UAAU,CAACf,IAAI,CAAC,CAAC,EAAE;QAC9H,IAAIiB,OAAO,GAAGD,YAAY,CAACf,KAAK;QAChCa,KAAK,IAAIG,OAAO,CAACC,QAAQ,CAAC,CAAC;MAC/B;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEP,GAAG,GAAG;QAAEQ,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,YAAY,IAAI,CAACA,YAAY,CAACd,IAAI,KAAKW,EAAE,GAAGE,UAAU,CAACM,MAAM,CAAC,EAAER,EAAE,CAACf,IAAI,CAACiB,UAAU,CAAC;MAC3F,CAAC,SACO;QAAE,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAACQ,KAAK;MAAE;IACxC;IACA,OAAON,KAAK;EAChB,CAAC;EACDV,QAAQ,CAACK,SAAS,CAACa,mBAAmB,GAAG,YAAY;IACjD,OAAO,IAAI,CAACjB,mBAAmB,GAAG,IAAI,CAACM,YAAY,CAAC,CAAC;EACzD,CAAC;EACDP,QAAQ,CAACK,SAAS,CAACc,WAAW,GAAG,YAAY;IACzC,OAAO,IAAI,CAACjB,QAAQ;EACxB,CAAC;EACD,OAAOF,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}