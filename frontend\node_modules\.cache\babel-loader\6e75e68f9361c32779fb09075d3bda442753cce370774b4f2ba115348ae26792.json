{"ast": null, "code": "/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\n// import UPCEANReader from './UPCEANReader';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport ResultMetadataType from '../ResultMetadataType';\nimport NotFoundException from '../NotFoundException';\n/**\n * @see UPCEANExtension2Support\n */\nvar UPCEANExtension5Support = /** @class */function () {\n  function UPCEANExtension5Support() {\n    this.CHECK_DIGIT_ENCODINGS = [0x18, 0x14, 0x12, 0x11, 0x0C, 0x06, 0x03, 0x0A, 0x09, 0x05];\n    this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n    this.decodeRowStringBuffer = '';\n  }\n  UPCEANExtension5Support.prototype.decodeRow = function (rowNumber, row, extensionStartRange) {\n    var result = this.decodeRowStringBuffer;\n    var end = this.decodeMiddle(row, extensionStartRange, result);\n    var resultString = result.toString();\n    var extensionData = UPCEANExtension5Support.parseExtensionString(resultString);\n    var resultPoints = [new ResultPoint((extensionStartRange[0] + extensionStartRange[1]) / 2.0, rowNumber), new ResultPoint(end, rowNumber)];\n    var extensionResult = new Result(resultString, null, 0, resultPoints, BarcodeFormat.UPC_EAN_EXTENSION, new Date().getTime());\n    if (extensionData != null) {\n      extensionResult.putAllMetadata(extensionData);\n    }\n    return extensionResult;\n  };\n  UPCEANExtension5Support.prototype.decodeMiddle = function (row, startRange, resultString) {\n    var e_1, _a;\n    var counters = this.decodeMiddleCounters;\n    counters[0] = 0;\n    counters[1] = 0;\n    counters[2] = 0;\n    counters[3] = 0;\n    var end = row.getSize();\n    var rowOffset = startRange[1];\n    var lgPatternFound = 0;\n    for (var x = 0; x < 5 && rowOffset < end; x++) {\n      var bestMatch = AbstractUPCEANReader.decodeDigit(row, counters, rowOffset, AbstractUPCEANReader.L_AND_G_PATTERNS);\n      resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch % 10);\n      try {\n        for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n          var counter = counters_1_1.value;\n          rowOffset += counter;\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      if (bestMatch >= 10) {\n        lgPatternFound |= 1 << 4 - x;\n      }\n      if (x !== 4) {\n        // Read off separator if not last\n        rowOffset = row.getNextSet(rowOffset);\n        rowOffset = row.getNextUnset(rowOffset);\n      }\n    }\n    if (resultString.length !== 5) {\n      throw new NotFoundException();\n    }\n    var checkDigit = this.determineCheckDigit(lgPatternFound);\n    if (UPCEANExtension5Support.extensionChecksum(resultString.toString()) !== checkDigit) {\n      throw new NotFoundException();\n    }\n    return rowOffset;\n  };\n  UPCEANExtension5Support.extensionChecksum = function (s) {\n    var length = s.length;\n    var sum = 0;\n    for (var i = length - 2; i >= 0; i -= 2) {\n      sum += s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n    }\n    sum *= 3;\n    for (var i = length - 1; i >= 0; i -= 2) {\n      sum += s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n    }\n    sum *= 3;\n    return sum % 10;\n  };\n  UPCEANExtension5Support.prototype.determineCheckDigit = function (lgPatternFound) {\n    for (var d = 0; d < 10; d++) {\n      if (lgPatternFound === this.CHECK_DIGIT_ENCODINGS[d]) {\n        return d;\n      }\n    }\n    throw new NotFoundException();\n  };\n  UPCEANExtension5Support.parseExtensionString = function (raw) {\n    if (raw.length !== 5) {\n      return null;\n    }\n    var value = UPCEANExtension5Support.parseExtension5String(raw);\n    if (value == null) {\n      return null;\n    }\n    return new Map([[ResultMetadataType.SUGGESTED_PRICE, value]]);\n  };\n  UPCEANExtension5Support.parseExtension5String = function (raw) {\n    var currency;\n    switch (raw.charAt(0)) {\n      case '0':\n        currency = '£';\n        break;\n      case '5':\n        currency = '$';\n        break;\n      case '9':\n        // Reference: http://www.jollytech.com\n        switch (raw) {\n          case '90000':\n            // No suggested retail price\n            return null;\n          case '99991':\n            // Complementary\n            return '0.00';\n          case '99990':\n            return 'Used';\n        }\n        // Otherwise... unknown currency?\n        currency = '';\n        break;\n      default:\n        currency = '';\n        break;\n    }\n    var rawAmount = parseInt(raw.substring(1));\n    var unitsString = (rawAmount / 100).toString();\n    var hundredths = rawAmount % 100;\n    var hundredthsString = hundredths < 10 ? '0' + hundredths : hundredths.toString(); // fixme\n    return currency + unitsString + '.' + hundredthsString;\n  };\n  return UPCEANExtension5Support;\n}();\nexport default UPCEANExtension5Support;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BarcodeFormat", "AbstractUPCEANReader", "Result", "ResultPoint", "ResultMetadataType", "NotFoundException", "UPCEANExtension5Support", "CHECK_DIGIT_ENCODINGS", "decodeMiddleCounters", "Int32Array", "from", "decodeRowStringBuffer", "prototype", "decodeRow", "rowNumber", "row", "extensionStartRange", "result", "end", "decodeMiddle", "resultString", "toString", "extensionData", "parseExtensionString", "resultPoints", "extensionResult", "UPC_EAN_EXTENSION", "Date", "getTime", "putAllMetadata", "startRange", "e_1", "_a", "counters", "getSize", "rowOffset", "lgPatternFound", "x", "bestMatch", "decodeDigit", "L_AND_G_PATTERNS", "String", "fromCharCode", "charCodeAt", "counters_1", "counters_1_1", "counter", "e_1_1", "error", "return", "getNextSet", "getNextUnset", "checkDigit", "determineCheckDigit", "extensionChecksum", "sum", "char<PERSON>t", "d", "raw", "parseExtension5String", "Map", "SUGGESTED_PRICE", "currency", "rawAmount", "parseInt", "substring", "unitsString", "hundredths", "hundredthsString"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/UPCEANExtension5Support.js"], "sourcesContent": ["/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\n// import UPCEANReader from './UPCEANReader';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport ResultMetadataType from '../ResultMetadataType';\nimport NotFoundException from '../NotFoundException';\n/**\n * @see UPCEANExtension2Support\n */\nvar UPCEANExtension5Support = /** @class */ (function () {\n    function UPCEANExtension5Support() {\n        this.CHECK_DIGIT_ENCODINGS = [0x18, 0x14, 0x12, 0x11, 0x0C, 0x06, 0x03, 0x0A, 0x09, 0x05];\n        this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n        this.decodeRowStringBuffer = '';\n    }\n    UPCEANExtension5Support.prototype.decodeRow = function (rowNumber, row, extensionStartRange) {\n        var result = this.decodeRowStringBuffer;\n        var end = this.decodeMiddle(row, extensionStartRange, result);\n        var resultString = result.toString();\n        var extensionData = UPCEANExtension5Support.parseExtensionString(resultString);\n        var resultPoints = [\n            new ResultPoint((extensionStartRange[0] + extensionStartRange[1]) / 2.0, rowNumber),\n            new ResultPoint(end, rowNumber)\n        ];\n        var extensionResult = new Result(resultString, null, 0, resultPoints, BarcodeFormat.UPC_EAN_EXTENSION, new Date().getTime());\n        if (extensionData != null) {\n            extensionResult.putAllMetadata(extensionData);\n        }\n        return extensionResult;\n    };\n    UPCEANExtension5Support.prototype.decodeMiddle = function (row, startRange, resultString) {\n        var e_1, _a;\n        var counters = this.decodeMiddleCounters;\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        var lgPatternFound = 0;\n        for (var x = 0; x < 5 && rowOffset < end; x++) {\n            var bestMatch = AbstractUPCEANReader.decodeDigit(row, counters, rowOffset, AbstractUPCEANReader.L_AND_G_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch % 10));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (bestMatch >= 10) {\n                lgPatternFound |= 1 << (4 - x);\n            }\n            if (x !== 4) {\n                // Read off separator if not last\n                rowOffset = row.getNextSet(rowOffset);\n                rowOffset = row.getNextUnset(rowOffset);\n            }\n        }\n        if (resultString.length !== 5) {\n            throw new NotFoundException();\n        }\n        var checkDigit = this.determineCheckDigit(lgPatternFound);\n        if (UPCEANExtension5Support.extensionChecksum(resultString.toString()) !== checkDigit) {\n            throw new NotFoundException();\n        }\n        return rowOffset;\n    };\n    UPCEANExtension5Support.extensionChecksum = function (s) {\n        var length = s.length;\n        var sum = 0;\n        for (var i = length - 2; i >= 0; i -= 2) {\n            sum += s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n        }\n        sum *= 3;\n        for (var i = length - 1; i >= 0; i -= 2) {\n            sum += s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n        }\n        sum *= 3;\n        return sum % 10;\n    };\n    UPCEANExtension5Support.prototype.determineCheckDigit = function (lgPatternFound) {\n        for (var d = 0; d < 10; d++) {\n            if (lgPatternFound === this.CHECK_DIGIT_ENCODINGS[d]) {\n                return d;\n            }\n        }\n        throw new NotFoundException();\n    };\n    UPCEANExtension5Support.parseExtensionString = function (raw) {\n        if (raw.length !== 5) {\n            return null;\n        }\n        var value = UPCEANExtension5Support.parseExtension5String(raw);\n        if (value == null) {\n            return null;\n        }\n        return new Map([[ResultMetadataType.SUGGESTED_PRICE, value]]);\n    };\n    UPCEANExtension5Support.parseExtension5String = function (raw) {\n        var currency;\n        switch (raw.charAt(0)) {\n            case '0':\n                currency = '£';\n                break;\n            case '5':\n                currency = '$';\n                break;\n            case '9':\n                // Reference: http://www.jollytech.com\n                switch (raw) {\n                    case '90000':\n                        // No suggested retail price\n                        return null;\n                    case '99991':\n                        // Complementary\n                        return '0.00';\n                    case '99990':\n                        return 'Used';\n                }\n                // Otherwise... unknown currency?\n                currency = '';\n                break;\n            default:\n                currency = '';\n                break;\n        }\n        var rawAmount = parseInt(raw.substring(1));\n        var unitsString = (rawAmount / 100).toString();\n        var hundredths = rawAmount % 100;\n        var hundredthsString = hundredths < 10 ? '0' + hundredths : hundredths.toString(); // fixme\n        return currency + unitsString + '.' + hundredthsString;\n    };\n    return UPCEANExtension5Support;\n}());\nexport default UPCEANExtension5Support;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,aAAa,MAAM,kBAAkB;AAC5C;AACA,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA;AACA;AACA,IAAIC,uBAAuB,GAAG,aAAe,YAAY;EACrD,SAASA,uBAAuBA,CAAA,EAAG;IAC/B,IAAI,CAACC,qBAAqB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACzF,IAAI,CAACC,oBAAoB,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzD,IAAI,CAACC,qBAAqB,GAAG,EAAE;EACnC;EACAL,uBAAuB,CAACM,SAAS,CAACC,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,mBAAmB,EAAE;IACzF,IAAIC,MAAM,GAAG,IAAI,CAACN,qBAAqB;IACvC,IAAIO,GAAG,GAAG,IAAI,CAACC,YAAY,CAACJ,GAAG,EAAEC,mBAAmB,EAAEC,MAAM,CAAC;IAC7D,IAAIG,YAAY,GAAGH,MAAM,CAACI,QAAQ,CAAC,CAAC;IACpC,IAAIC,aAAa,GAAGhB,uBAAuB,CAACiB,oBAAoB,CAACH,YAAY,CAAC;IAC9E,IAAII,YAAY,GAAG,CACf,IAAIrB,WAAW,CAAC,CAACa,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,IAAI,GAAG,EAAEF,SAAS,CAAC,EACnF,IAAIX,WAAW,CAACe,GAAG,EAAEJ,SAAS,CAAC,CAClC;IACD,IAAIW,eAAe,GAAG,IAAIvB,MAAM,CAACkB,YAAY,EAAE,IAAI,EAAE,CAAC,EAAEI,YAAY,EAAExB,aAAa,CAAC0B,iBAAiB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;IAC5H,IAAIN,aAAa,IAAI,IAAI,EAAE;MACvBG,eAAe,CAACI,cAAc,CAACP,aAAa,CAAC;IACjD;IACA,OAAOG,eAAe;EAC1B,CAAC;EACDnB,uBAAuB,CAACM,SAAS,CAACO,YAAY,GAAG,UAAUJ,GAAG,EAAEe,UAAU,EAAEV,YAAY,EAAE;IACtF,IAAIW,GAAG,EAAEC,EAAE;IACX,IAAIC,QAAQ,GAAG,IAAI,CAACzB,oBAAoB;IACxCyB,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACf,IAAIf,GAAG,GAAGH,GAAG,CAACmB,OAAO,CAAC,CAAC;IACvB,IAAIC,SAAS,GAAGL,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAIM,cAAc,GAAG,CAAC;IACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAIF,SAAS,GAAGjB,GAAG,EAAEmB,CAAC,EAAE,EAAE;MAC3C,IAAIC,SAAS,GAAGrC,oBAAoB,CAACsC,WAAW,CAACxB,GAAG,EAAEkB,QAAQ,EAAEE,SAAS,EAAElC,oBAAoB,CAACuC,gBAAgB,CAAC;MACjHpB,YAAY,IAAIqB,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGL,SAAS,GAAG,EAAG,CAAC;MACzE,IAAI;QACA,KAAK,IAAIM,UAAU,IAAIb,GAAG,GAAG,KAAK,CAAC,EAAE5C,QAAQ,CAAC8C,QAAQ,CAAC,CAAC,EAAEY,YAAY,GAAGD,UAAU,CAAChD,IAAI,CAAC,CAAC,EAAE,CAACiD,YAAY,CAAC/C,IAAI,EAAE+C,YAAY,GAAGD,UAAU,CAAChD,IAAI,CAAC,CAAC,EAAE;UAC9I,IAAIkD,OAAO,GAAGD,YAAY,CAAChD,KAAK;UAChCsC,SAAS,IAAIW,OAAO;QACxB;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAEhB,GAAG,GAAG;UAAEiB,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,YAAY,IAAI,CAACA,YAAY,CAAC/C,IAAI,KAAKkC,EAAE,GAAGY,UAAU,CAACK,MAAM,CAAC,EAAEjB,EAAE,CAACtC,IAAI,CAACkD,UAAU,CAAC;QAC3F,CAAC,SACO;UAAE,IAAIb,GAAG,EAAE,MAAMA,GAAG,CAACiB,KAAK;QAAE;MACxC;MACA,IAAIV,SAAS,IAAI,EAAE,EAAE;QACjBF,cAAc,IAAI,CAAC,IAAK,CAAC,GAAGC,CAAE;MAClC;MACA,IAAIA,CAAC,KAAK,CAAC,EAAE;QACT;QACAF,SAAS,GAAGpB,GAAG,CAACmC,UAAU,CAACf,SAAS,CAAC;QACrCA,SAAS,GAAGpB,GAAG,CAACoC,YAAY,CAAChB,SAAS,CAAC;MAC3C;IACJ;IACA,IAAIf,YAAY,CAACzB,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIU,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI+C,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAACjB,cAAc,CAAC;IACzD,IAAI9B,uBAAuB,CAACgD,iBAAiB,CAAClC,YAAY,CAACC,QAAQ,CAAC,CAAC,CAAC,KAAK+B,UAAU,EAAE;MACnF,MAAM,IAAI/C,iBAAiB,CAAC,CAAC;IACjC;IACA,OAAO8B,SAAS;EACpB,CAAC;EACD7B,uBAAuB,CAACgD,iBAAiB,GAAG,UAAUjE,CAAC,EAAE;IACrD,IAAIM,MAAM,GAAGN,CAAC,CAACM,MAAM;IACrB,IAAI4D,GAAG,GAAG,CAAC;IACX,KAAK,IAAI9D,CAAC,GAAGE,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACrC8D,GAAG,IAAIlE,CAAC,CAACmE,MAAM,CAAC/D,CAAC,CAAC,CAACkD,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;IACxD;IACAY,GAAG,IAAI,CAAC;IACR,KAAK,IAAI9D,CAAC,GAAGE,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACrC8D,GAAG,IAAIlE,CAAC,CAACmE,MAAM,CAAC/D,CAAC,CAAC,CAACkD,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;IACxD;IACAY,GAAG,IAAI,CAAC;IACR,OAAOA,GAAG,GAAG,EAAE;EACnB,CAAC;EACDjD,uBAAuB,CAACM,SAAS,CAACyC,mBAAmB,GAAG,UAAUjB,cAAc,EAAE;IAC9E,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzB,IAAIrB,cAAc,KAAK,IAAI,CAAC7B,qBAAqB,CAACkD,CAAC,CAAC,EAAE;QAClD,OAAOA,CAAC;MACZ;IACJ;IACA,MAAM,IAAIpD,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACDC,uBAAuB,CAACiB,oBAAoB,GAAG,UAAUmC,GAAG,EAAE;IAC1D,IAAIA,GAAG,CAAC/D,MAAM,KAAK,CAAC,EAAE;MAClB,OAAO,IAAI;IACf;IACA,IAAIE,KAAK,GAAGS,uBAAuB,CAACqD,qBAAqB,CAACD,GAAG,CAAC;IAC9D,IAAI7D,KAAK,IAAI,IAAI,EAAE;MACf,OAAO,IAAI;IACf;IACA,OAAO,IAAI+D,GAAG,CAAC,CAAC,CAACxD,kBAAkB,CAACyD,eAAe,EAAEhE,KAAK,CAAC,CAAC,CAAC;EACjE,CAAC;EACDS,uBAAuB,CAACqD,qBAAqB,GAAG,UAAUD,GAAG,EAAE;IAC3D,IAAII,QAAQ;IACZ,QAAQJ,GAAG,CAACF,MAAM,CAAC,CAAC,CAAC;MACjB,KAAK,GAAG;QACJM,QAAQ,GAAG,GAAG;QACd;MACJ,KAAK,GAAG;QACJA,QAAQ,GAAG,GAAG;QACd;MACJ,KAAK,GAAG;QACJ;QACA,QAAQJ,GAAG;UACP,KAAK,OAAO;YACR;YACA,OAAO,IAAI;UACf,KAAK,OAAO;YACR;YACA,OAAO,MAAM;UACjB,KAAK,OAAO;YACR,OAAO,MAAM;QACrB;QACA;QACAI,QAAQ,GAAG,EAAE;QACb;MACJ;QACIA,QAAQ,GAAG,EAAE;QACb;IACR;IACA,IAAIC,SAAS,GAAGC,QAAQ,CAACN,GAAG,CAACO,SAAS,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAIC,WAAW,GAAG,CAACH,SAAS,GAAG,GAAG,EAAE1C,QAAQ,CAAC,CAAC;IAC9C,IAAI8C,UAAU,GAAGJ,SAAS,GAAG,GAAG;IAChC,IAAIK,gBAAgB,GAAGD,UAAU,GAAG,EAAE,GAAG,GAAG,GAAGA,UAAU,GAAGA,UAAU,CAAC9C,QAAQ,CAAC,CAAC,CAAC,CAAC;IACnF,OAAOyC,QAAQ,GAAGI,WAAW,GAAG,GAAG,GAAGE,gBAAgB;EAC1D,CAAC;EACD,OAAO9D,uBAAuB;AAClC,CAAC,CAAC,CAAE;AACJ,eAAeA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}