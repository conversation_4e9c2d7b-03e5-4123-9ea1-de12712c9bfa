{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _SET_BY_CODE;\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\n// constants for internal usage\nvar SET_A = exports.SET_A = 0;\nvar SET_B = exports.SET_B = 1;\nvar SET_C = exports.SET_C = 2;\n\n// Special characters\nvar SHIFT = exports.SHIFT = 98;\nvar START_A = exports.START_A = 103;\nvar START_B = exports.START_B = 104;\nvar START_C = exports.START_C = 105;\nvar MODULO = exports.MODULO = 103;\nvar STOP = exports.STOP = 106;\nvar FNC1 = exports.FNC1 = 207;\n\n// Get set by start code\nvar SET_BY_CODE = exports.SET_BY_CODE = (_SET_BY_CODE = {}, _defineProperty(_SET_BY_CODE, START_A, SET_A), _defineProperty(_SET_BY_CODE, START_B, SET_B), _defineProperty(_SET_BY_CODE, START_C, SET_C), _SET_BY_CODE);\n\n// Get next set by code\nvar SWAP = exports.SWAP = {\n  101: SET_A,\n  100: SET_B,\n  99: SET_C\n};\nvar A_START_CHAR = exports.A_START_CHAR = String.fromCharCode(208); // START_A + 105\nvar B_START_CHAR = exports.B_START_CHAR = String.fromCharCode(209); // START_B + 105\nvar C_START_CHAR = exports.C_START_CHAR = String.fromCharCode(210); // START_C + 105\n\n// 128A (Code Set A)\n// ASCII characters 00 to 95 (0–9, A–Z and control codes), special characters, and FNC 1–4\nvar A_CHARS = exports.A_CHARS = \"[\\x00-\\x5F\\xC8-\\xCF]\";\n\n// 128B (Code Set B)\n// ASCII characters 32 to 127 (0–9, A–Z, a–z), special characters, and FNC 1–4\nvar B_CHARS = exports.B_CHARS = \"[\\x20-\\x7F\\xC8-\\xCF]\";\n\n// 128C (Code Set C)\n// 00–99 (encodes two digits with a single code point) and FNC1\nvar C_CHARS = exports.C_CHARS = \"(\\xCF*[0-9]{2}\\xCF*)\";\n\n// CODE128 includes 107 symbols:\n// 103 data symbols, 3 start symbols (A, B and C), and 1 stop symbol (the last one)\n// Each symbol consist of three black bars (1) and three white spaces (0).\nvar BARS = exports.BARS = [11011001100, 11001101100, 11001100110, 10010011000, 10010001100, 10001001100, 10011001000, 10011000100, 10001100100, 11001001000, 11001000100, 11000100100, 10110011100, 10011011100, 10011001110, 10111001100, 10011101100, 10011100110, 11001110010, 11001011100, 11001001110, 11011100100, 11001110100, 11101101110, 11101001100, 11100101100, 11100100110, 11101100100, 11100110100, 11100110010, 11011011000, 11011000110, 11000110110, 10100011000, 10001011000, 10001000110, 10110001000, 10001101000, 10001100010, 11010001000, 11000101000, 11000100010, 10110111000, 10110001110, 10001101110, 10111011000, 10111000110, 10001110110, 11101110110, 11010001110, 11000101110, 11011101000, 11011100010, 11011101110, 11101011000, 11101000110, 11100010110, 11101101000, 11101100010, 11100011010, 11101111010, 11001000010, 11110001010, 10100110000, 10100001100, 10010110000, 10010000110, 10000101100, 10000100110, 10110010000, 10110000100, 10011010000, 10011000010, 10000110100, 10000110010, 11000010010, 11001010000, 11110111010, 11000010100, 10001111010, 10100111100, 10010111100, 10010011110, 10111100100, 10011110100, 10011110010, 11110100100, 11110010100, 11110010010, 11011011110, 11011110110, 11110110110, 10101111000, 10100011110, 10001011110, 10111101000, 10111100010, 11110101000, 11110100010, 10111011110, 10111101110, 11101011110, 11110101110, ***********, ***********, ***********, 1100011101011];", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_SET_BY_CODE", "_defineProperty", "obj", "key", "enumerable", "configurable", "writable", "SET_A", "SET_B", "SET_C", "SHIFT", "START_A", "START_B", "START_C", "MODULO", "STOP", "FNC1", "SET_BY_CODE", "SWAP", "A_START_CHAR", "String", "fromCharCode", "B_START_CHAR", "C_START_CHAR", "A_CHARS", "B_CHARS", "C_CHARS", "BARS"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/CODE128/constants.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _SET_BY_CODE;\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n// constants for internal usage\nvar SET_A = exports.SET_A = 0;\nvar SET_B = exports.SET_B = 1;\nvar SET_C = exports.SET_C = 2;\n\n// Special characters\nvar SHIFT = exports.SHIFT = 98;\nvar START_A = exports.START_A = 103;\nvar START_B = exports.START_B = 104;\nvar START_C = exports.START_C = 105;\nvar MODULO = exports.MODULO = 103;\nvar STOP = exports.STOP = 106;\nvar FNC1 = exports.FNC1 = 207;\n\n// Get set by start code\nvar SET_BY_CODE = exports.SET_BY_CODE = (_SET_BY_CODE = {}, _defineProperty(_SET_BY_CODE, START_A, SET_A), _defineProperty(_SET_BY_CODE, START_B, SET_B), _defineProperty(_SET_BY_CODE, START_C, SET_C), _SET_BY_CODE);\n\n// Get next set by code\nvar SWAP = exports.SWAP = {\n\t101: SET_A,\n\t100: SET_B,\n\t99: SET_C\n};\n\nvar A_START_CHAR = exports.A_START_CHAR = String.fromCharCode(208); // START_A + 105\nvar B_START_CHAR = exports.B_START_CHAR = String.fromCharCode(209); // START_B + 105\nvar C_START_CHAR = exports.C_START_CHAR = String.fromCharCode(210); // START_C + 105\n\n// 128A (Code Set A)\n// ASCII characters 00 to 95 (0–9, A–Z and control codes), special characters, and FNC 1–4\nvar A_CHARS = exports.A_CHARS = \"[\\x00-\\x5F\\xC8-\\xCF]\";\n\n// 128B (Code Set B)\n// ASCII characters 32 to 127 (0–9, A–Z, a–z), special characters, and FNC 1–4\nvar B_CHARS = exports.B_CHARS = \"[\\x20-\\x7F\\xC8-\\xCF]\";\n\n// 128C (Code Set C)\n// 00–99 (encodes two digits with a single code point) and FNC1\nvar C_CHARS = exports.C_CHARS = \"(\\xCF*[0-9]{2}\\xCF*)\";\n\n// CODE128 includes 107 symbols:\n// 103 data symbols, 3 start symbols (A, B and C), and 1 stop symbol (the last one)\n// Each symbol consist of three black bars (1) and three white spaces (0).\nvar BARS = exports.BARS = [11011001100, 11001101100, 11001100110, 10010011000, 10010001100, 10001001100, 10011001000, 10011000100, 10001100100, 11001001000, 11001000100, 11000100100, 10110011100, 10011011100, 10011001110, 10111001100, 10011101100, 10011100110, 11001110010, 11001011100, 11001001110, 11011100100, 11001110100, 11101101110, 11101001100, 11100101100, 11100100110, 11101100100, 11100110100, 11100110010, 11011011000, 11011000110, 11000110110, 10100011000, 10001011000, 10001000110, 10110001000, 10001101000, 10001100010, 11010001000, 11000101000, 11000100010, 10110111000, 10110001110, 10001101110, 10111011000, 10111000110, 10001110110, 11101110110, 11010001110, 11000101110, 11011101000, 11011100010, 11011101110, 11101011000, 11101000110, 11100010110, 11101101000, 11101100010, 11100011010, 11101111010, 11001000010, 11110001010, 10100110000, 10100001100, 10010110000, 10010000110, 10000101100, 10000100110, 10110010000, 10110000100, 10011010000, 10011000010, 10000110100, 10000110010, 11000010010, 11001010000, 11110111010, 11000010100, 10001111010, 10100111100, 10010111100, 10010011110, 10111100100, 10011110100, 10011110010, 11110100100, 11110010100, 11110010010, 11011011110, 11011110110, 11110110110, 10101111000, 10100011110, 10001011110, 10111101000, 10111100010, 11110101000, 11110100010, 10111011110, 10111101110, 11101011110, 11110101110, ***********, ***********, ***********, 1100011101011];"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY;AAEhB,SAASC,eAAeA,CAACC,GAAG,EAAEC,GAAG,EAAEJ,KAAK,EAAE;EAAE,IAAII,GAAG,IAAID,GAAG,EAAE;IAAEN,MAAM,CAACC,cAAc,CAACK,GAAG,EAAEC,GAAG,EAAE;MAAEJ,KAAK,EAAEA,KAAK;MAAEK,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAEJ,GAAG,CAACC,GAAG,CAAC,GAAGJ,KAAK;EAAE;EAAE,OAAOG,GAAG;AAAE;;AAEhN;AACA,IAAIK,KAAK,GAAGT,OAAO,CAACS,KAAK,GAAG,CAAC;AAC7B,IAAIC,KAAK,GAAGV,OAAO,CAACU,KAAK,GAAG,CAAC;AAC7B,IAAIC,KAAK,GAAGX,OAAO,CAACW,KAAK,GAAG,CAAC;;AAE7B;AACA,IAAIC,KAAK,GAAGZ,OAAO,CAACY,KAAK,GAAG,EAAE;AAC9B,IAAIC,OAAO,GAAGb,OAAO,CAACa,OAAO,GAAG,GAAG;AACnC,IAAIC,OAAO,GAAGd,OAAO,CAACc,OAAO,GAAG,GAAG;AACnC,IAAIC,OAAO,GAAGf,OAAO,CAACe,OAAO,GAAG,GAAG;AACnC,IAAIC,MAAM,GAAGhB,OAAO,CAACgB,MAAM,GAAG,GAAG;AACjC,IAAIC,IAAI,GAAGjB,OAAO,CAACiB,IAAI,GAAG,GAAG;AAC7B,IAAIC,IAAI,GAAGlB,OAAO,CAACkB,IAAI,GAAG,GAAG;;AAE7B;AACA,IAAIC,WAAW,GAAGnB,OAAO,CAACmB,WAAW,IAAIjB,YAAY,GAAG,CAAC,CAAC,EAAEC,eAAe,CAACD,YAAY,EAAEW,OAAO,EAAEJ,KAAK,CAAC,EAAEN,eAAe,CAACD,YAAY,EAAEY,OAAO,EAAEJ,KAAK,CAAC,EAAEP,eAAe,CAACD,YAAY,EAAEa,OAAO,EAAEJ,KAAK,CAAC,EAAET,YAAY,CAAC;;AAEtN;AACA,IAAIkB,IAAI,GAAGpB,OAAO,CAACoB,IAAI,GAAG;EACzB,GAAG,EAAEX,KAAK;EACV,GAAG,EAAEC,KAAK;EACV,EAAE,EAAEC;AACL,CAAC;AAED,IAAIU,YAAY,GAAGrB,OAAO,CAACqB,YAAY,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;AACpE,IAAIC,YAAY,GAAGxB,OAAO,CAACwB,YAAY,GAAGF,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;AACpE,IAAIE,YAAY,GAAGzB,OAAO,CAACyB,YAAY,GAAGH,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;;AAEpE;AACA;AACA,IAAIG,OAAO,GAAG1B,OAAO,CAAC0B,OAAO,GAAG,sBAAsB;;AAEtD;AACA;AACA,IAAIC,OAAO,GAAG3B,OAAO,CAAC2B,OAAO,GAAG,sBAAsB;;AAEtD;AACA;AACA,IAAIC,OAAO,GAAG5B,OAAO,CAAC4B,OAAO,GAAG,sBAAsB;;AAEtD;AACA;AACA;AACA,IAAIC,IAAI,GAAG7B,OAAO,CAAC6B,IAAI,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}