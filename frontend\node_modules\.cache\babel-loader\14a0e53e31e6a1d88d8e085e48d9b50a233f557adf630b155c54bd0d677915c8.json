{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { PickersContext } from \"../internals/components/PickersProvider.js\";\n\n/**\n * Returns the context passed by the picker that wraps the current component.\n */\nexport const usePickersContext = () => {\n  const value = React.useContext(PickersContext);\n  if (value == null) {\n    throw new Error(['MUI X: The `usePickersContext` can only be called in fields that are used as a slot of a picker component'].join('\\n'));\n  }\n  return value;\n};", "map": {"version": 3, "names": ["React", "PickersContext", "usePickersContext", "value", "useContext", "Error", "join"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/hooks/usePickersContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { PickersContext } from \"../internals/components/PickersProvider.js\";\n\n/**\n * Returns the context passed by the picker that wraps the current component.\n */\nexport const usePickersContext = () => {\n  const value = React.useContext(PickersContext);\n  if (value == null) {\n    throw new Error(['MUI X: The `usePickersContext` can only be called in fields that are used as a slot of a picker component'].join('\\n'));\n  }\n  return value;\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,4CAA4C;;AAE3E;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EACrC,MAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,cAAc,CAAC;EAC9C,IAAIE,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIE,KAAK,CAAC,CAAC,2GAA2G,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAC3I;EACA,OAAOH,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}