{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport FormatException from '../../../../FormatException';\nimport DecodedObject from './DecodedObject';\nvar DecodedNumeric = /** @class */function (_super) {\n  __extends(DecodedNumeric, _super);\n  function DecodedNumeric(newPosition, firstDigit, secondDigit) {\n    var _this = _super.call(this, newPosition) || this;\n    if (firstDigit < 0 || firstDigit > 10 || secondDigit < 0 || secondDigit > 10) {\n      throw new FormatException();\n    }\n    _this.firstDigit = firstDigit;\n    _this.secondDigit = secondDigit;\n    return _this;\n  }\n  DecodedNumeric.prototype.getFirstDigit = function () {\n    return this.firstDigit;\n  };\n  DecodedNumeric.prototype.getSecondDigit = function () {\n    return this.secondDigit;\n  };\n  DecodedNumeric.prototype.getValue = function () {\n    return this.firstDigit * 10 + this.secondDigit;\n  };\n  DecodedNumeric.prototype.isFirstDigitFNC1 = function () {\n    return this.firstDigit === DecodedNumeric.FNC1;\n  };\n  DecodedNumeric.prototype.isSecondDigitFNC1 = function () {\n    return this.secondDigit === DecodedNumeric.FNC1;\n  };\n  DecodedNumeric.prototype.isAnyFNC1 = function () {\n    return this.firstDigit === DecodedNumeric.FNC1 || this.secondDigit === DecodedNumeric.FNC1;\n  };\n  DecodedNumeric.FNC1 = 10;\n  return DecodedNumeric;\n}(DecodedObject);\nexport default DecodedNumeric;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "FormatException", "DecodedObject", "DecodedNumeric", "_super", "newPosition", "firstDigit", "second<PERSON><PERSON><PERSON>", "_this", "call", "getFirstDigit", "getSecondDigit", "getValue", "isFirstDigitFNC1", "FNC1", "isSecondDigitFNC1", "isAnyFNC1"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedNumeric.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport FormatException from '../../../../FormatException';\nimport DecodedObject from './DecodedObject';\nvar DecodedNumeric = /** @class */ (function (_super) {\n    __extends(DecodedNumeric, _super);\n    function DecodedNumeric(newPosition, firstDigit, secondDigit) {\n        var _this = _super.call(this, newPosition) || this;\n        if (firstDigit < 0 || firstDigit > 10 || secondDigit < 0 || secondDigit > 10) {\n            throw new FormatException();\n        }\n        _this.firstDigit = firstDigit;\n        _this.secondDigit = secondDigit;\n        return _this;\n    }\n    DecodedNumeric.prototype.getFirstDigit = function () {\n        return this.firstDigit;\n    };\n    DecodedNumeric.prototype.getSecondDigit = function () {\n        return this.secondDigit;\n    };\n    DecodedNumeric.prototype.getValue = function () {\n        return this.firstDigit * 10 + this.secondDigit;\n    };\n    DecodedNumeric.prototype.isFirstDigitFNC1 = function () {\n        return this.firstDigit === DecodedNumeric.FNC1;\n    };\n    DecodedNumeric.prototype.isSecondDigitFNC1 = function () {\n        return this.secondDigit === DecodedNumeric.FNC1;\n    };\n    DecodedNumeric.prototype.isAnyFNC1 = function () {\n        return this.firstDigit === DecodedNumeric.FNC1 || this.secondDigit === DecodedNumeric.FNC1;\n    };\n    DecodedNumeric.FNC1 = 10;\n    return DecodedNumeric;\n}(DecodedObject));\nexport default DecodedNumeric;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,eAAe,MAAM,6BAA6B;AACzD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,IAAIC,cAAc,GAAG,aAAe,UAAUC,MAAM,EAAE;EAClDjB,SAAS,CAACgB,cAAc,EAAEC,MAAM,CAAC;EACjC,SAASD,cAAcA,CAACE,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAC1D,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,WAAW,CAAC,IAAI,IAAI;IAClD,IAAIC,UAAU,GAAG,CAAC,IAAIA,UAAU,GAAG,EAAE,IAAIC,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAG,EAAE,EAAE;MAC1E,MAAM,IAAIN,eAAe,CAAC,CAAC;IAC/B;IACAO,KAAK,CAACF,UAAU,GAAGA,UAAU;IAC7BE,KAAK,CAACD,WAAW,GAAGA,WAAW;IAC/B,OAAOC,KAAK;EAChB;EACAL,cAAc,CAACJ,SAAS,CAACW,aAAa,GAAG,YAAY;IACjD,OAAO,IAAI,CAACJ,UAAU;EAC1B,CAAC;EACDH,cAAc,CAACJ,SAAS,CAACY,cAAc,GAAG,YAAY;IAClD,OAAO,IAAI,CAACJ,WAAW;EAC3B,CAAC;EACDJ,cAAc,CAACJ,SAAS,CAACa,QAAQ,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACN,UAAU,GAAG,EAAE,GAAG,IAAI,CAACC,WAAW;EAClD,CAAC;EACDJ,cAAc,CAACJ,SAAS,CAACc,gBAAgB,GAAG,YAAY;IACpD,OAAO,IAAI,CAACP,UAAU,KAAKH,cAAc,CAACW,IAAI;EAClD,CAAC;EACDX,cAAc,CAACJ,SAAS,CAACgB,iBAAiB,GAAG,YAAY;IACrD,OAAO,IAAI,CAACR,WAAW,KAAKJ,cAAc,CAACW,IAAI;EACnD,CAAC;EACDX,cAAc,CAACJ,SAAS,CAACiB,SAAS,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACV,UAAU,KAAKH,cAAc,CAACW,IAAI,IAAI,IAAI,CAACP,WAAW,KAAKJ,cAAc,CAACW,IAAI;EAC9F,CAAC;EACDX,cAAc,CAACW,IAAI,GAAG,EAAE;EACxB,OAAOX,cAAc;AACzB,CAAC,CAACD,aAAa,CAAE;AACjB,eAAeC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}