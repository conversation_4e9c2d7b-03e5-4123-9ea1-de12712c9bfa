{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Learning Projects\\\\uni-core-business-suite_v3\\\\frontend\\\\src\\\\components\\\\BankPaymentVoucherForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Card, CardContent, Typography, Button, Grid, TextField, FormControl, InputLabel, Select, MenuItem, Alert, Divider, Chip, Autocomplete, FormHelperText } from \"@mui/material\";\nimport { Save as SaveIcon, Check as ApproveIcon, PostAdd as PostIcon, Cancel as CancelIcon } from \"@mui/icons-material\";\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\nimport dayjs from \"dayjs\";\nimport axios from \"../utils/axiosConfig\";\nimport { formatCurrency, formatAccountingNumber, parseAccountingNumber, fixDecimalPlaces } from \"../utils/numberUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BankPaymentVoucherForm = ({\n  voucherId,\n  readOnly = false,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [voucher, setVoucher] = useState({\n    voucherType: 'BP',\n    // Bank Payment\n    voucherDate: dayjs(),\n    // Voucher creation date\n    transactionDate: dayjs(),\n    // Actual transaction date\n    amount: '',\n    fromAccountId: '',\n    // Bank account (credit)\n    toAccountId: '',\n    // Debit account (vendor, expense, etc.)\n    relatedPartyType: '',\n    relatedPartyId: '',\n    relatedPartyName: '',\n    paymentMethod: 'Bank Transfer',\n    chequeNumber: '',\n    chequeDate: null,\n    bankReference: '',\n    narration: '',\n    description: '',\n    status: 'Draft',\n    referenceDocuments: []\n  });\n  const [accounts, setAccounts] = useState([]);\n  const [bankAccounts, setBankAccounts] = useState([]);\n  const [vendors, setVendors] = useState([]);\n  const [purchaseInvoices, setPurchaseInvoices] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [selectedVendor, setSelectedVendor] = useState(null);\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\n  const paymentMethods = ['Bank Transfer', 'Cheque', 'Online Transfer', 'Card', 'Other'];\n  useEffect(() => {\n    fetchInitialData();\n    if (voucherId) {\n      fetchVoucher();\n    }\n  }, [voucherId]);\n\n  // Fetch vendor's purchase invoices when vendor is selected\n  useEffect(() => {\n    if (selectedVendor) {\n      fetchVendorInvoices(selectedVendor._id);\n    } else {\n      setPurchaseInvoices([]);\n      setSelectedInvoice(null);\n    }\n  }, [selectedVendor]);\n\n  // Update amount when invoice is selected\n  useEffect(() => {\n    if (selectedInvoice) {\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\n\n      // Create a detailed narration\n      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber || 'Unknown'}`;\n\n      // If there are returns, add details\n      if (selectedInvoice.hasReturns && selectedInvoice.returnDetails) {\n        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\n        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount || 0)}, Returns: ${formatCurrency(selectedInvoice.returnAmount || 0)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount || 0)})`;\n      }\n      setVoucher(prev => ({\n        ...prev,\n        amount: remainingAmount.toFixed(2),\n        narration\n      }));\n    }\n  }, [selectedInvoice]);\n\n  // Add error handling for ResizeObserver\n  useEffect(() => {\n    // Suppress ResizeObserver loop error\n    const originalError = window.console.error;\n    window.console.error = (...args) => {\n      var _args$, _args$$includes;\n      if ((_args$ = args[0]) !== null && _args$ !== void 0 && (_args$$includes = _args$.includes) !== null && _args$$includes !== void 0 && _args$$includes.call(_args$, 'ResizeObserver loop')) {\n        // Ignore ResizeObserver loop errors\n        return;\n      }\n      originalError(...args);\n    };\n    return () => {\n      window.console.error = originalError;\n    };\n  }, []);\n  const fetchInitialData = async () => {\n    try {\n      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([axios.get(\"/api/accounts?active=true\"), axios.get(\"/api/vendors\"), axios.get(\"/api/bank-accounts\")]);\n      setAccounts(accountsRes.data);\n      setVendors(vendorsRes.data);\n\n      // Map bank accounts to their corresponding chart of accounts entries\n      const bankAccs = bankAccountsRes.data;\n      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);\n\n      // Get bank account details from chart of accounts\n      if (bankAccountIds.length > 0) {\n        const bankAccountsDetails = accountsRes.data.filter(acc => bankAccountIds.includes(acc._id));\n        setBankAccounts(bankAccountsDetails);\n      }\n    } catch (error) {\n      console.error(\"Error fetching initial data:\", error);\n    }\n  };\n  const fetchVoucher = async () => {\n    try {\n      var _voucherData$fromAcco, _voucherData$toAccoun;\n      setLoading(true);\n      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);\n      const voucherData = response.data;\n      setVoucher({\n        ...voucherData,\n        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt || new Date()),\n        transactionDate: dayjs(voucherData.transactionDate || new Date()),\n        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,\n        fromAccountId: ((_voucherData$fromAcco = voucherData.fromAccountId) === null || _voucherData$fromAcco === void 0 ? void 0 : _voucherData$fromAcco._id) || voucherData.fromAccountId,\n        toAccountId: ((_voucherData$toAccoun = voucherData.toAccountId) === null || _voucherData$toAccoun === void 0 ? void 0 : _voucherData$toAccoun._id) || voucherData.toAccountId,\n        amount: parseFloat(voucherData.amount || 0).toFixed(2)\n      });\n\n      // If this is a vendor payment, fetch the vendor and invoice details\n      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {\n        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);\n        setSelectedVendor(vendorRes.data);\n\n        // If there's a reference to a purchase invoice\n        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {\n          const invoiceRef = voucherData.referenceDocuments.find(doc => doc.documentType === 'PurchaseInvoice');\n          if (invoiceRef && invoiceRef.documentId) {\n            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);\n            setSelectedInvoice(invoiceRes.data);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching voucher:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchVendorInvoices = async vendorId => {\n    try {\n      console.log(`Fetching invoices for vendor: ${vendorId}`);\n      // Fetch unpaid or partially paid invoices for this vendor\n      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);\n      console.log('Vendor invoices response:', response.data);\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        setPurchaseInvoices(response.data);\n      } else {\n        console.log('No unpaid invoices found for this vendor');\n        setPurchaseInvoices([]);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error fetching vendor invoices:\", error);\n      alert(`Failed to fetch vendor invoices: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message}`);\n      setPurchaseInvoices([]);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setVoucher(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when field is updated\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n  const handleVendorChange = vendor => {\n    setSelectedVendor(vendor);\n    setSelectedInvoice(null); // Clear selected invoice when vendor changes\n\n    if (vendor) {\n      // Update voucher with vendor details\n      setVoucher(prev => ({\n        ...prev,\n        relatedPartyType: 'Vendor',\n        relatedPartyId: vendor._id,\n        relatedPartyName: vendor.name,\n        toAccountId: vendor.accountId || '',\n        // Set vendor's account as debit account\n        amount: '',\n        // Clear amount when vendor changes\n        narration: '',\n        // Clear narration when vendor changes\n        referenceDocuments: [] // Clear reference documents\n      }));\n    } else {\n      // Clear vendor-related fields\n      setVoucher(prev => ({\n        ...prev,\n        relatedPartyType: '',\n        relatedPartyId: '',\n        relatedPartyName: '',\n        toAccountId: '',\n        amount: '',\n        narration: '',\n        referenceDocuments: []\n      }));\n    }\n  };\n  const handleInvoiceChange = invoice => {\n    setSelectedInvoice(invoice);\n    if (invoice) {\n      console.log('Selected invoice:', invoice);\n\n      // Use the adjusted remaining amount that accounts for returns\n      const remainingAmount = invoice.remainingAmount;\n      console.log(`Using adjusted remaining amount: ${remainingAmount}`);\n\n      // Create a detailed narration\n      let narration = `Payment against invoice ${invoice.invoiceNumber}`;\n\n      // If there are returns, add details\n      if (invoice.hasReturns) {\n        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\n        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;\n      }\n\n      // Update amount field with remaining amount\n      setVoucher(prev => ({\n        ...prev,\n        amount: remainingAmount.toString(),\n        narration\n      }));\n\n      // Update reference documents\n      const referenceDoc = {\n        documentType: 'PurchaseInvoice',\n        documentId: invoice._id,\n        documentNumber: invoice.invoiceNumber,\n        allocatedAmount: remainingAmount,\n        originalAmount: invoice.originalAmount,\n        returnAmount: invoice.returnAmount,\n        adjustedAmount: invoice.adjustedAmount\n      };\n      setVoucher(prev => ({\n        ...prev,\n        referenceDocuments: [referenceDoc]\n      }));\n    } else {\n      setVoucher(prev => ({\n        ...prev,\n        referenceDocuments: []\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!voucher.fromAccountId) newErrors.fromAccountId = \"Bank is required\";\n    if (!voucher.toAccountId) newErrors.toAccountId = \"Title (debit account) is required\";\n    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {\n      newErrors.amount = \"Valid amount is required\";\n    }\n    if (!voucher.voucherDate) newErrors.voucherDate = \"Voucher date is required\";\n    if (!voucher.transactionDate) newErrors.transactionDate = \"Transaction date is required\";\n    if (!voucher.narration) newErrors.narration = \"Narration is required\";\n\n    // Vendor-invoice linkage validation\n    if (selectedVendor && selectedInvoice) {\n      // Ensure the selected invoice belongs to the selected vendor\n      if (selectedInvoice.vendorId !== selectedVendor._id) {\n        newErrors.invoice = \"Selected invoice does not belong to the selected vendor\";\n      }\n\n      // Ensure payment amount doesn't exceed remaining amount\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\n      const paymentAmount = parseFloat(voucher.amount) || 0;\n      if (paymentAmount > remainingAmount) {\n        newErrors.amount = `Payment amount cannot exceed remaining amount of ${remainingAmount.toFixed(2)}`;\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSave = async () => {\n    if (!validateForm()) return;\n    try {\n      setLoading(true);\n      const voucherData = {\n        ...voucher,\n        voucherDate: voucher.voucherDate.toISOString(),\n        transactionDate: voucher.transactionDate.toISOString(),\n        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,\n        amount: parseFloat(voucher.amount)\n      };\n      let response;\n      if (voucherId) {\n        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);\n      } else {\n        response = await axios.post(\"/api/payment-vouchers\", voucherData);\n      }\n      if (onSave) onSave(response.data.voucher);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error(\"Error saving voucher:\", error);\n      alert(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Error saving bank payment voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApprove = async () => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\n      if (onSave) onSave();\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error(\"Error approving voucher:\", error);\n      alert(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || \"Error approving voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePost = async () => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\n      if (onSave) onSave();\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error(\"Error posting voucher:\", error);\n      alert(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || \"Error posting voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');\n  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';\n  const canPost = !readOnly && voucherId && voucher.status === 'Approved';\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n      dateAdapter: AdapterDayjs,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Bank Payment Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 17\n              }, this), voucherId && /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Voucher No.\",\n                value: voucher.voucherNumber || '',\n                margin: \"normal\",\n                InputProps: {\n                  readOnly: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                label: \"Voucher Date\",\n                value: voucher.voucherDate,\n                onChange: newValue => handleInputChange('voucherDate', newValue),\n                format: \"DD/MMM/YYYY\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  fullWidth: true,\n                  margin: \"normal\",\n                  error: !!errors.voucherDate,\n                  helperText: errors.voucherDate,\n                  InputProps: {\n                    ...params.InputProps,\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 21\n                }, this),\n                readOnly: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 419,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                label: \"Transaction Date\",\n                value: voucher.transactionDate,\n                onChange: newValue => handleInputChange('transactionDate', newValue),\n                format: \"DD/MMM/YYYY\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  fullWidth: true,\n                  margin: \"normal\",\n                  error: !!errors.transactionDate,\n                  helperText: errors.transactionDate,\n                  InputProps: {\n                    ...params.InputProps,\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 21\n                }, this),\n                readOnly: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Transaction ID\",\n                value: voucher.bankReference || '',\n                onChange: e => handleInputChange('bankReference', e.target.value),\n                margin: \"normal\",\n                InputProps: {\n                  readOnly: !canEdit\n                },\n                helperText: \"Optional field for internal reference or payment ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.fromAccountId,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Bank\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.fromAccountId,\n                  onChange: e => handleInputChange('fromAccountId', e.target.value),\n                  label: \"Bank\",\n                  disabled: !canEdit,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Select Bank\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 486,\n                    columnNumber: 21\n                  }, this), bankAccounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: account._id,\n                    children: account.accountName\n                  }, account._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 490,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 19\n                }, this), errors.fromAccountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.fromAccountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 496,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Payment Method\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.paymentMethod,\n                  onChange: e => handleInputChange('paymentMethod', e.target.value),\n                  label: \"Payment Method\",\n                  disabled: !canEdit,\n                  children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: method,\n                    children: method\n                  }, method, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 17\n              }, this), voucher.paymentMethod === 'Cheque' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Cheque Number\",\n                  value: voucher.chequeNumber || '',\n                  onChange: e => handleInputChange('chequeNumber', e.target.value),\n                  margin: \"normal\",\n                  InputProps: {\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 520,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                  label: \"Cheque Date\",\n                  value: voucher.chequeDate,\n                  onChange: newValue => handleInputChange('chequeDate', newValue),\n                  renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                    ...params,\n                    fullWidth: true,\n                    margin: \"normal\",\n                    InputProps: {\n                      ...params.InputProps,\n                      readOnly: !canEdit\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 534,\n                    columnNumber: 25\n                  }, this),\n                  readOnly: !canEdit\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Payment Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: vendors,\n                getOptionLabel: vendor => vendor.name || '',\n                value: selectedVendor,\n                onChange: (event, newValue) => handleVendorChange(newValue),\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Vendor\",\n                  margin: \"normal\",\n                  fullWidth: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 567,\n                  columnNumber: 21\n                }, this),\n                disabled: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 561,\n                columnNumber: 17\n              }, this), selectedVendor && /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: purchaseInvoices,\n                getOptionLabel: invoice => {\n                  if (!invoice) return '';\n                  const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;\n                  const invoiceDate = new Date(invoice.invoiceDate).toLocaleDateString('en-GB', {\n                    day: '2-digit',\n                    month: 'short',\n                    year: 'numeric'\n                  });\n\n                  // If there are returns, show the adjusted amount\n                  if (invoice.hasReturns) {\n                    return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} (Returns Applied)`;\n                  }\n\n                  // Otherwise just show the remaining amount\n                  return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} outstanding`;\n                },\n                value: selectedInvoice,\n                onChange: (event, newValue) => handleInvoiceChange(newValue),\n                renderOption: (props, invoice) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%',\n                      maxWidth: '100%',\n                      overflow: 'hidden'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"medium\",\n                      noWrap: true,\n                      children: [invoice.invoiceNumber, \" - \", formatCurrency(invoice.remainingAmount), \" outstanding\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 604,\n                      columnNumber: 27\n                    }, this), invoice.hasReturns && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        display: \"block\",\n                        noWrap: true,\n                        children: [\"Original: \", formatCurrency(invoice.originalAmount), \" | Returns: \", formatCurrency(invoice.returnAmount), \" | Adjusted: \", formatCurrency(invoice.adjustedAmount)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 609,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"primary\",\n                        display: \"block\",\n                        noWrap: true,\n                        children: [invoice.returnDetails.length, \" return(s) applied -\", invoice.returnDetails.map((ret, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [ret.returnNumber, \" (\", ret.status, \")\", idx < invoice.returnDetails.length - 1 ? ', ' : '']\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 617,\n                          columnNumber: 35\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 614,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 603,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 23\n                }, this),\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Purchase Invoice\",\n                  margin: \"normal\",\n                  fullWidth: true,\n                  error: purchaseInvoices.length === 0 || !!errors.invoice,\n                  helperText: errors.invoice || (purchaseInvoices.length === 0 ? \"No unpaid invoices found for this vendor\" : \"Optional: Select invoice for payment tracking. Partial payments are supported.\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 23\n                }, this),\n                disabled: !canEdit || purchaseInvoices.length === 0,\n                noOptionsText: \"No unpaid invoices found\",\n                ListboxProps: {\n                  style: {\n                    maxHeight: '200px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 19\n              }, this), !selectedVendor && /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.toAccountId,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.toAccountId,\n                  onChange: e => handleInputChange('toAccountId', e.target.value),\n                  label: \"Title\",\n                  disabled: !canEdit,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Select Account\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 665,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 664,\n                    columnNumber: 23\n                  }, this), accounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: account._id,\n                    children: [account.accountCode, \" - \", account.accountName]\n                  }, account._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 658,\n                  columnNumber: 21\n                }, this), errors.toAccountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.toAccountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 674,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 652,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Amount\",\n                type: \"number\",\n                value: voucher.amount,\n                onChange: e => handleInputChange('amount', e.target.value),\n                margin: \"normal\",\n                error: !!errors.amount,\n                helperText: errors.amount,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 680,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Narration\",\n                value: voucher.narration,\n                onChange: e => handleInputChange('narration', e.target.value),\n                margin: \"normal\",\n                multiline: true,\n                rows: 2,\n                error: !!errors.narration,\n                helperText: errors.narration,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Description\",\n                value: voucher.description || '',\n                onChange: e => handleInputChange('description', e.target.value),\n                margin: \"normal\",\n                multiline: true,\n                rows: 2,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 707,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              gap: 2,\n              mt: 2\n            },\n            children: [onCancel && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 30\n              }, this),\n              onClick: onCancel,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this), canEdit && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 739,\n                columnNumber: 30\n              }, this),\n              onClick: handleSave,\n              disabled: loading,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 17\n            }, this), canApprove && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"warning\",\n              startIcon: /*#__PURE__*/_jsxDEV(ApproveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 30\n              }, this),\n              onClick: handleApprove,\n              disabled: loading,\n              children: \"Approve\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 17\n            }, this), canPost && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              startIcon: /*#__PURE__*/_jsxDEV(PostIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 763,\n                columnNumber: 30\n              }, this),\n              onClick: handlePost,\n              disabled: loading,\n              children: \"Post to Ledger\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 760,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 396,\n    columnNumber: 5\n  }, this);\n};\n_s(BankPaymentVoucherForm, \"buRCZTx5Ubo79tZQuzwSeBiGLKU=\");\n_c = BankPaymentVoucherForm;\nexport default BankPaymentVoucherForm;\nvar _c;\n$RefreshReg$(_c, \"BankPaymentVoucherForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Grid", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Divider", "Chip", "Autocomplete", "FormHelperText", "Save", "SaveIcon", "Check", "ApproveIcon", "PostAdd", "PostIcon", "Cancel", "CancelIcon", "DatePicker", "LocalizationProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayjs", "axios", "formatCurrency", "formatAccountingNumber", "parseAccountingNumber", "fixDecimalPlaces", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BankPaymentVoucherForm", "voucherId", "readOnly", "onSave", "onCancel", "_s", "voucher", "setVoucher", "voucherType", "voucherDate", "transactionDate", "amount", "fromAccountId", "toAccountId", "relatedPartyType", "relatedPartyId", "relatedPartyName", "paymentMethod", "chequeNumber", "chequeDate", "bankReference", "narration", "description", "status", "referenceDocuments", "accounts", "setAccounts", "bankAccounts", "setBankAccounts", "vendors", "setVendors", "purchaseInvoices", "setPurchaseInvoices", "loading", "setLoading", "errors", "setErrors", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVendor", "selectedInvoice", "setSelectedInvoice", "paymentMethods", "fetchInitialData", "fetchVoucher", "fetchVendorInvoices", "_id", "remainingAmount", "invoiceNumber", "hasReturns", "returnDetails", "returnStatuses", "map", "ret", "returnNumber", "join", "originalAmount", "returnAmount", "adjustedAmount", "prev", "toFixed", "originalError", "window", "console", "error", "args", "_args$", "_args$$includes", "includes", "call", "accountsRes", "vendorsRes", "bankAccountsRes", "Promise", "all", "get", "data", "bankAccs", "bankAccountIds", "bank", "accountId", "filter", "id", "length", "bankAccountsDetails", "acc", "_voucherData$fromAcco", "_voucherData$toAccoun", "response", "voucherData", "createdAt", "Date", "parseFloat", "vendorRes", "invoiceRef", "find", "doc", "documentType", "documentId", "invoiceRes", "vendorId", "log", "Array", "isArray", "_error$response", "_error$response$data", "alert", "message", "handleInputChange", "field", "value", "handleVendorChange", "vendor", "name", "handleInvoiceChange", "invoice", "toString", "referenceDoc", "documentNumber", "allocatedAmount", "validateForm", "newErrors", "isNaN", "paymentAmount", "Object", "keys", "handleSave", "toISOString", "put", "post", "_error$response2", "_error$response2$data", "handleApprove", "_error$response3", "_error$response3$data", "handlePost", "_error$response4", "_error$response4$data", "canEdit", "canApprove", "canPost", "children", "dateAdapter", "container", "spacing", "item", "xs", "md", "variant", "sx", "mb", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "label", "voucherNumber", "margin", "InputProps", "onChange", "newValue", "format", "renderInput", "params", "helperText", "e", "target", "disabled", "account", "accountName", "method", "options", "getOptionLabel", "event", "invoiceLabel", "invoiceDate", "toLocaleDateString", "day", "month", "year", "renderOption", "props", "width", "max<PERSON><PERSON><PERSON>", "overflow", "fontWeight", "noWrap", "color", "display", "idx", "noOptionsText", "ListboxProps", "style", "maxHeight", "accountCode", "type", "multiline", "rows", "justifyContent", "gap", "mt", "startIcon", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/components/BankPaymentVoucherForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  Button,\r\n  Grid,\r\n  TextField,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Alert,\r\n  Divider,\r\n  Chip,\r\n  Autocomplete,\r\n  FormHelperText\r\n} from \"@mui/material\";\r\nimport {\r\n  Save as SaveIcon,\r\n  Check as ApproveIcon,\r\n  PostAdd as PostIcon,\r\n  Cancel as CancelIcon\r\n} from \"@mui/icons-material\";\r\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\r\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\r\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\r\nimport dayjs from \"dayjs\";\r\nimport axios from \"../utils/axiosConfig\";\r\nimport { formatCurrency, formatAccountingNumber, parseAccountingNumber, fixDecimalPlaces } from \"../utils/numberUtils\";\r\n\r\nconst BankPaymentVoucherForm = ({ voucherId, readOnly = false, onSave, onCancel }) => {\r\n  const [voucher, setVoucher] = useState({\r\n    voucherType: 'BP', // Bank Payment\r\n    voucherDate: dayjs(), // Voucher creation date\r\n    transactionDate: dayjs(), // Actual transaction date\r\n    amount: '',\r\n    fromAccountId: '', // Bank account (credit)\r\n    toAccountId: '', // Debit account (vendor, expense, etc.)\r\n    relatedPartyType: '',\r\n    relatedPartyId: '',\r\n    relatedPartyName: '',\r\n    paymentMethod: 'Bank Transfer',\r\n    chequeNumber: '',\r\n    chequeDate: null,\r\n    bankReference: '',\r\n    narration: '',\r\n    description: '',\r\n    status: 'Draft',\r\n    referenceDocuments: []\r\n  });\r\n\r\n  const [accounts, setAccounts] = useState([]);\r\n  const [bankAccounts, setBankAccounts] = useState([]);\r\n  const [vendors, setVendors] = useState([]);\r\n  const [purchaseInvoices, setPurchaseInvoices] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [errors, setErrors] = useState({});\r\n  const [selectedVendor, setSelectedVendor] = useState(null);\r\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\r\n\r\n  const paymentMethods = [\r\n    'Bank Transfer',\r\n    'Cheque',\r\n    'Online Transfer',\r\n    'Card',\r\n    'Other'\r\n  ];\r\n\r\n  useEffect(() => {\r\n    fetchInitialData();\r\n    if (voucherId) {\r\n      fetchVoucher();\r\n    }\r\n  }, [voucherId]);\r\n\r\n  // Fetch vendor's purchase invoices when vendor is selected\r\n  useEffect(() => {\r\n    if (selectedVendor) {\r\n      fetchVendorInvoices(selectedVendor._id);\r\n    } else {\r\n      setPurchaseInvoices([]);\r\n      setSelectedInvoice(null);\r\n    }\r\n  }, [selectedVendor]);\r\n\r\n  // Update amount when invoice is selected\r\n  useEffect(() => {\r\n    if (selectedInvoice) {\r\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\r\n\r\n      // Create a detailed narration\r\n      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber || 'Unknown'}`;\r\n\r\n      // If there are returns, add details\r\n      if (selectedInvoice.hasReturns && selectedInvoice.returnDetails) {\r\n        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\r\n        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount || 0)}, Returns: ${formatCurrency(selectedInvoice.returnAmount || 0)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount || 0)})`;\r\n      }\r\n\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        amount: remainingAmount.toFixed(2),\r\n        narration\r\n      }));\r\n    }\r\n  }, [selectedInvoice]);\r\n\r\n  // Add error handling for ResizeObserver\r\n  useEffect(() => {\r\n    // Suppress ResizeObserver loop error\r\n    const originalError = window.console.error;\r\n    window.console.error = (...args) => {\r\n      if (args[0]?.includes?.('ResizeObserver loop')) {\r\n        // Ignore ResizeObserver loop errors\r\n        return;\r\n      }\r\n      originalError(...args);\r\n    };\r\n\r\n    return () => {\r\n      window.console.error = originalError;\r\n    };\r\n  }, []);\r\n\r\n  const fetchInitialData = async () => {\r\n    try {\r\n      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([\r\n        axios.get(\"/api/accounts?active=true\"),\r\n        axios.get(\"/api/vendors\"),\r\n        axios.get(\"/api/bank-accounts\")\r\n      ]);\r\n\r\n      setAccounts(accountsRes.data);\r\n      setVendors(vendorsRes.data);\r\n      \r\n      // Map bank accounts to their corresponding chart of accounts entries\r\n      const bankAccs = bankAccountsRes.data;\r\n      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);\r\n      \r\n      // Get bank account details from chart of accounts\r\n      if (bankAccountIds.length > 0) {\r\n        const bankAccountsDetails = accountsRes.data.filter(acc => \r\n          bankAccountIds.includes(acc._id)\r\n        );\r\n        setBankAccounts(bankAccountsDetails);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching initial data:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchVoucher = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);\r\n      const voucherData = response.data;\r\n      \r\n      setVoucher({\r\n        ...voucherData,\r\n        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt || new Date()),\r\n        transactionDate: dayjs(voucherData.transactionDate || new Date()),\r\n        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,\r\n        fromAccountId: voucherData.fromAccountId?._id || voucherData.fromAccountId,\r\n        toAccountId: voucherData.toAccountId?._id || voucherData.toAccountId,\r\n        amount: parseFloat(voucherData.amount || 0).toFixed(2)\r\n      });\r\n\r\n      // If this is a vendor payment, fetch the vendor and invoice details\r\n      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {\r\n        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);\r\n        setSelectedVendor(vendorRes.data);\r\n        \r\n        // If there's a reference to a purchase invoice\r\n        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {\r\n          const invoiceRef = voucherData.referenceDocuments.find(doc => \r\n            doc.documentType === 'PurchaseInvoice'\r\n          );\r\n          \r\n          if (invoiceRef && invoiceRef.documentId) {\r\n            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);\r\n            setSelectedInvoice(invoiceRes.data);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching voucher:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchVendorInvoices = async (vendorId) => {\r\n    try {\r\n      console.log(`Fetching invoices for vendor: ${vendorId}`);\r\n      // Fetch unpaid or partially paid invoices for this vendor\r\n      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);\r\n      console.log('Vendor invoices response:', response.data);\r\n      \r\n      if (Array.isArray(response.data) && response.data.length > 0) {\r\n        setPurchaseInvoices(response.data);\r\n      } else {\r\n        console.log('No unpaid invoices found for this vendor');\r\n        setPurchaseInvoices([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching vendor invoices:\", error);\r\n      alert(`Failed to fetch vendor invoices: ${error.response?.data?.message || error.message}`);\r\n      setPurchaseInvoices([]);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setVoucher(prev => ({ ...prev, [field]: value }));\r\n    \r\n    // Clear error when field is updated\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: null }));\r\n    }\r\n  };\r\n\r\n  const handleVendorChange = (vendor) => {\r\n    setSelectedVendor(vendor);\r\n    setSelectedInvoice(null); // Clear selected invoice when vendor changes\r\n\r\n    if (vendor) {\r\n      // Update voucher with vendor details\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        relatedPartyType: 'Vendor',\r\n        relatedPartyId: vendor._id,\r\n        relatedPartyName: vendor.name,\r\n        toAccountId: vendor.accountId || '', // Set vendor's account as debit account\r\n        amount: '', // Clear amount when vendor changes\r\n        narration: '', // Clear narration when vendor changes\r\n        referenceDocuments: [] // Clear reference documents\r\n      }));\r\n    } else {\r\n      // Clear vendor-related fields\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        relatedPartyType: '',\r\n        relatedPartyId: '',\r\n        relatedPartyName: '',\r\n        toAccountId: '',\r\n        amount: '',\r\n        narration: '',\r\n        referenceDocuments: []\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleInvoiceChange = (invoice) => {\r\n    setSelectedInvoice(invoice);\r\n    \r\n    if (invoice) {\r\n      console.log('Selected invoice:', invoice);\r\n      \r\n      // Use the adjusted remaining amount that accounts for returns\r\n      const remainingAmount = invoice.remainingAmount;\r\n      \r\n      console.log(`Using adjusted remaining amount: ${remainingAmount}`);\r\n      \r\n      // Create a detailed narration\r\n      let narration = `Payment against invoice ${invoice.invoiceNumber}`;\r\n      \r\n      // If there are returns, add details\r\n      if (invoice.hasReturns) {\r\n        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\r\n        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;\r\n      }\r\n      \r\n      // Update amount field with remaining amount\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        amount: remainingAmount.toString(),\r\n        narration\r\n      }));\r\n      \r\n      // Update reference documents\r\n      const referenceDoc = {\r\n        documentType: 'PurchaseInvoice',\r\n        documentId: invoice._id,\r\n        documentNumber: invoice.invoiceNumber,\r\n        allocatedAmount: remainingAmount,\r\n        originalAmount: invoice.originalAmount,\r\n        returnAmount: invoice.returnAmount,\r\n        adjustedAmount: invoice.adjustedAmount\r\n      };\r\n      \r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        referenceDocuments: [referenceDoc]\r\n      }));\r\n    } else {\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        referenceDocuments: []\r\n      }));\r\n    }\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n\r\n    if (!voucher.fromAccountId) newErrors.fromAccountId = \"Bank is required\";\r\n    if (!voucher.toAccountId) newErrors.toAccountId = \"Title (debit account) is required\";\r\n    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {\r\n      newErrors.amount = \"Valid amount is required\";\r\n    }\r\n    if (!voucher.voucherDate) newErrors.voucherDate = \"Voucher date is required\";\r\n    if (!voucher.transactionDate) newErrors.transactionDate = \"Transaction date is required\";\r\n    if (!voucher.narration) newErrors.narration = \"Narration is required\";\r\n\r\n    // Vendor-invoice linkage validation\r\n    if (selectedVendor && selectedInvoice) {\r\n      // Ensure the selected invoice belongs to the selected vendor\r\n      if (selectedInvoice.vendorId !== selectedVendor._id) {\r\n        newErrors.invoice = \"Selected invoice does not belong to the selected vendor\";\r\n      }\r\n\r\n      // Ensure payment amount doesn't exceed remaining amount\r\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\r\n      const paymentAmount = parseFloat(voucher.amount) || 0;\r\n      if (paymentAmount > remainingAmount) {\r\n        newErrors.amount = `Payment amount cannot exceed remaining amount of ${remainingAmount.toFixed(2)}`;\r\n      }\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!validateForm()) return;\r\n    \r\n    try {\r\n      setLoading(true);\r\n      \r\n      const voucherData = {\r\n        ...voucher,\r\n        voucherDate: voucher.voucherDate.toISOString(),\r\n        transactionDate: voucher.transactionDate.toISOString(),\r\n        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,\r\n        amount: parseFloat(voucher.amount)\r\n      };\r\n\r\n      let response;\r\n      if (voucherId) {\r\n        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);\r\n      } else {\r\n        response = await axios.post(\"/api/payment-vouchers\", voucherData);\r\n      }\r\n\r\n      if (onSave) onSave(response.data.voucher);\r\n    } catch (error) {\r\n      console.error(\"Error saving voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error saving bank payment voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleApprove = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\r\n      if (onSave) onSave();\r\n    } catch (error) {\r\n      console.error(\"Error approving voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error approving voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePost = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\r\n      if (onSave) onSave();\r\n    } catch (error) {\r\n      console.error(\"Error posting voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error posting voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');\r\n  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';\r\n  const canPost = !readOnly && voucherId && voucher.status === 'Approved';\r\n\r\n  return (\r\n    <Box>\r\n      <LocalizationProvider dateAdapter={AdapterDayjs}>\r\n        <Grid container spacing={3}>\r\n          {/* Left Column */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\r\n              <CardContent>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Bank Payment Details\r\n                </Typography>\r\n                \r\n                {/* Voucher Number (shown only for existing vouchers) */}\r\n                {voucherId && (\r\n                  <TextField\r\n                    fullWidth\r\n                    label=\"Voucher No.\"\r\n                    value={voucher.voucherNumber || ''}\r\n                    margin=\"normal\"\r\n                    InputProps={{ readOnly: true }}\r\n                  />\r\n                )}\r\n\r\n                {/* Voucher Date */}\r\n                <DatePicker\r\n                  label=\"Voucher Date\"\r\n                  value={voucher.voucherDate}\r\n                  onChange={(newValue) => handleInputChange('voucherDate', newValue)}\r\n                  format=\"DD/MMM/YYYY\"\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      fullWidth\r\n                      margin=\"normal\"\r\n                      error={!!errors.voucherDate}\r\n                      helperText={errors.voucherDate}\r\n                      InputProps={{\r\n                        ...params.InputProps,\r\n                        readOnly: !canEdit\r\n                      }}\r\n                    />\r\n                  )}\r\n                  readOnly={!canEdit}\r\n                />\r\n\r\n                {/* Transaction Date */}\r\n                <DatePicker\r\n                  label=\"Transaction Date\"\r\n                  value={voucher.transactionDate}\r\n                  onChange={(newValue) => handleInputChange('transactionDate', newValue)}\r\n                  format=\"DD/MMM/YYYY\"\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      fullWidth\r\n                      margin=\"normal\"\r\n                      error={!!errors.transactionDate}\r\n                      helperText={errors.transactionDate}\r\n                      InputProps={{\r\n                        ...params.InputProps,\r\n                        readOnly: !canEdit\r\n                      }}\r\n                    />\r\n                  )}\r\n                  readOnly={!canEdit}\r\n                />\r\n                \r\n                {/* Transaction ID / Reference */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Transaction ID\"\r\n                  value={voucher.bankReference || ''}\r\n                  onChange={(e) => handleInputChange('bankReference', e.target.value)}\r\n                  margin=\"normal\"\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                  helperText=\"Optional field for internal reference or payment ID\"\r\n                />\r\n\r\n                {/* Bank Account (Credit Account) */}\r\n                <FormControl\r\n                  fullWidth\r\n                  margin=\"normal\"\r\n                  error={!!errors.fromAccountId}\r\n                >\r\n                  <InputLabel>Bank</InputLabel>\r\n                  <Select\r\n                    value={voucher.fromAccountId}\r\n                    onChange={(e) => handleInputChange('fromAccountId', e.target.value)}\r\n                    label=\"Bank\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    <MenuItem value=\"\">\r\n                      <em>Select Bank</em>\r\n                    </MenuItem>\r\n                    {bankAccounts.map((account) => (\r\n                      <MenuItem key={account._id} value={account._id}>\r\n                        {account.accountName}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                  {errors.fromAccountId && (\r\n                    <FormHelperText>{errors.fromAccountId}</FormHelperText>\r\n                  )}\r\n                </FormControl>\r\n                \r\n                {/* Payment Method */}\r\n                <FormControl fullWidth margin=\"normal\">\r\n                  <InputLabel>Payment Method</InputLabel>\r\n                  <Select\r\n                    value={voucher.paymentMethod}\r\n                    onChange={(e) => handleInputChange('paymentMethod', e.target.value)}\r\n                    label=\"Payment Method\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    {paymentMethods.map((method) => (\r\n                      <MenuItem key={method} value={method}>\r\n                        {method}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n                \r\n                {/* Cheque Details (shown only for cheque payments) */}\r\n                {voucher.paymentMethod === 'Cheque' && (\r\n                  <>\r\n                    <TextField\r\n                      fullWidth\r\n                      label=\"Cheque Number\"\r\n                      value={voucher.chequeNumber || ''}\r\n                      onChange={(e) => handleInputChange('chequeNumber', e.target.value)}\r\n                      margin=\"normal\"\r\n                      InputProps={{ readOnly: !canEdit }}\r\n                    />\r\n                    \r\n                    <DatePicker\r\n                      label=\"Cheque Date\"\r\n                      value={voucher.chequeDate}\r\n                      onChange={(newValue) => handleInputChange('chequeDate', newValue)}\r\n                      renderInput={(params) => (\r\n                        <TextField\r\n                          {...params}\r\n                          fullWidth\r\n                          margin=\"normal\"\r\n                          InputProps={{\r\n                            ...params.InputProps,\r\n                            readOnly: !canEdit\r\n                          }}\r\n                        />\r\n                      )}\r\n                      readOnly={!canEdit}\r\n                    />\r\n                  </>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          \r\n          {/* Right Column */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\r\n              <CardContent>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Payment Details\r\n                </Typography>\r\n                \r\n                {/* Vendor Selection */}\r\n                <Autocomplete\r\n                  options={vendors}\r\n                  getOptionLabel={(vendor) => vendor.name || ''}\r\n                  value={selectedVendor}\r\n                  onChange={(event, newValue) => handleVendorChange(newValue)}\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      label=\"Vendor\"\r\n                      margin=\"normal\"\r\n                      fullWidth\r\n                    />\r\n                  )}\r\n                  disabled={!canEdit}\r\n                />\r\n                \r\n                {/* Purchase Invoice Selection (shown only if vendor is selected) */}\r\n                {selectedVendor && (\r\n                  <Autocomplete\r\n                    options={purchaseInvoices}\r\n                    getOptionLabel={(invoice) => {\r\n                      if (!invoice) return '';\r\n\r\n                      const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;\r\n                      const invoiceDate = new Date(invoice.invoiceDate).toLocaleDateString('en-GB', {\r\n                        day: '2-digit',\r\n                        month: 'short',\r\n                        year: 'numeric'\r\n                      });\r\n\r\n                      // If there are returns, show the adjusted amount\r\n                      if (invoice.hasReturns) {\r\n                        return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} (Returns Applied)`;\r\n                      }\r\n\r\n                      // Otherwise just show the remaining amount\r\n                      return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} outstanding`;\r\n                    }}\r\n                    value={selectedInvoice}\r\n                    onChange={(event, newValue) => handleInvoiceChange(newValue)}\r\n                    renderOption={(props, invoice) => (\r\n                      <li {...props}>\r\n                        <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>\r\n                          <Typography variant=\"body1\" fontWeight=\"medium\" noWrap>\r\n                            {invoice.invoiceNumber} - {formatCurrency(invoice.remainingAmount)} outstanding\r\n                          </Typography>\r\n                          {invoice.hasReturns && (\r\n                            <>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" noWrap>\r\n                                Original: {formatCurrency(invoice.originalAmount)} | \r\n                                Returns: {formatCurrency(invoice.returnAmount)} | \r\n                                Adjusted: {formatCurrency(invoice.adjustedAmount)}\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"primary\" display=\"block\" noWrap>\r\n                                {invoice.returnDetails.length} return(s) applied - \r\n                                {invoice.returnDetails.map((ret, idx) => (\r\n                                  <span key={idx}>\r\n                                    {ret.returnNumber} ({ret.status})\r\n                                    {idx < invoice.returnDetails.length - 1 ? ', ' : ''}\r\n                                  </span>\r\n                                ))}\r\n                              </Typography>\r\n                            </>\r\n                          )}\r\n                        </Box>\r\n                      </li>\r\n                    )}\r\n                    renderInput={(params) => (\r\n                      <TextField\r\n                        {...params}\r\n                        label=\"Purchase Invoice\"\r\n                        margin=\"normal\"\r\n                        fullWidth\r\n                        error={purchaseInvoices.length === 0 || !!errors.invoice}\r\n                        helperText={\r\n                          errors.invoice ||\r\n                          (purchaseInvoices.length === 0 ? \"No unpaid invoices found for this vendor\" :\r\n                          \"Optional: Select invoice for payment tracking. Partial payments are supported.\")\r\n                        }\r\n                      />\r\n                    )}\r\n                    disabled={!canEdit || purchaseInvoices.length === 0}\r\n                    noOptionsText=\"No unpaid invoices found\"\r\n                    ListboxProps={{\r\n                      style: { maxHeight: '200px' }\r\n                    }}\r\n                  />\r\n                )}\r\n                \r\n                {/* Title (Debit Account - shown if no vendor is selected) */}\r\n                {!selectedVendor && (\r\n                  <FormControl\r\n                    fullWidth\r\n                    margin=\"normal\"\r\n                    error={!!errors.toAccountId}\r\n                  >\r\n                    <InputLabel>Title</InputLabel>\r\n                    <Select\r\n                      value={voucher.toAccountId}\r\n                      onChange={(e) => handleInputChange('toAccountId', e.target.value)}\r\n                      label=\"Title\"\r\n                      disabled={!canEdit}\r\n                    >\r\n                      <MenuItem value=\"\">\r\n                        <em>Select Account</em>\r\n                      </MenuItem>\r\n                      {accounts.map((account) => (\r\n                        <MenuItem key={account._id} value={account._id}>\r\n                          {account.accountCode} - {account.accountName}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                    {errors.toAccountId && (\r\n                      <FormHelperText>{errors.toAccountId}</FormHelperText>\r\n                    )}\r\n                  </FormControl>\r\n                )}\r\n                \r\n                {/* Amount */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Amount\"\r\n                  type=\"number\"\r\n                  value={voucher.amount}\r\n                  onChange={(e) => handleInputChange('amount', e.target.value)}\r\n                  margin=\"normal\"\r\n                  error={!!errors.amount}\r\n                  helperText={errors.amount}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n                \r\n                {/* Narration */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Narration\"\r\n                  value={voucher.narration}\r\n                  onChange={(e) => handleInputChange('narration', e.target.value)}\r\n                  margin=\"normal\"\r\n                  multiline\r\n                  rows={2}\r\n                  error={!!errors.narration}\r\n                  helperText={errors.narration}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n                \r\n                {/* Description */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Description\"\r\n                  value={voucher.description || ''}\r\n                  onChange={(e) => handleInputChange('description', e.target.value)}\r\n                  margin=\"normal\"\r\n                  multiline\r\n                  rows={2}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          \r\n          {/* Action Buttons */}\r\n          <Grid item xs={12}>\r\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>\r\n              {onCancel && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  color=\"secondary\"\r\n                  startIcon={<CancelIcon />}\r\n                  onClick={onCancel}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n              )}\r\n              \r\n              {canEdit && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  startIcon={<SaveIcon />}\r\n                  onClick={handleSave}\r\n                  disabled={loading}\r\n                >\r\n                  Save\r\n                </Button>\r\n              )}\r\n              \r\n              {canApprove && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"warning\"\r\n                  startIcon={<ApproveIcon />}\r\n                  onClick={handleApprove}\r\n                  disabled={loading}\r\n                >\r\n                  Approve\r\n                </Button>\r\n              )}\r\n              \r\n              {canPost && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"success\"\r\n                  startIcon={<PostIcon />}\r\n                  onClick={handlePost}\r\n                  disabled={loading}\r\n                >\r\n                  Post to Ledger\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          </Grid>\r\n        </Grid>\r\n      </LocalizationProvider>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default BankPaymentVoucherForm; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,YAAY,EACZC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,WAAW,EACpBC,OAAO,IAAIC,QAAQ,EACnBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,cAAc,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvH,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,QAAQ,GAAG,KAAK;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC;IACrC+C,WAAW,EAAE,IAAI;IAAE;IACnBC,WAAW,EAAEnB,KAAK,CAAC,CAAC;IAAE;IACtBoB,eAAe,EAAEpB,KAAK,CAAC,CAAC;IAAE;IAC1BqB,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE,EAAE;IAAE;IACnBC,WAAW,EAAE,EAAE;IAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,eAAe;IAC9BC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkE,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACoE,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0E,MAAM,EAAEC,SAAS,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC4E,cAAc,EAAEC,iBAAiB,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAMgF,cAAc,GAAG,CACrB,eAAe,EACf,QAAQ,EACR,iBAAiB,EACjB,MAAM,EACN,OAAO,CACR;EAED/E,SAAS,CAAC,MAAM;IACdgF,gBAAgB,CAAC,CAAC;IAClB,IAAIzC,SAAS,EAAE;MACb0C,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC1C,SAAS,CAAC,CAAC;;EAEf;EACAvC,SAAS,CAAC,MAAM;IACd,IAAI2E,cAAc,EAAE;MAClBO,mBAAmB,CAACP,cAAc,CAACQ,GAAG,CAAC;IACzC,CAAC,MAAM;MACLb,mBAAmB,CAAC,EAAE,CAAC;MACvBQ,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;;EAEpB;EACA3E,SAAS,CAAC,MAAM;IACd,IAAI6E,eAAe,EAAE;MACnB,MAAMO,eAAe,GAAGP,eAAe,CAACO,eAAe,IAAI,CAAC;;MAE5D;MACA,IAAIzB,SAAS,GAAG,2BAA2BkB,eAAe,CAACQ,aAAa,IAAI,SAAS,EAAE;;MAEvF;MACA,IAAIR,eAAe,CAACS,UAAU,IAAIT,eAAe,CAACU,aAAa,EAAE;QAC/D,MAAMC,cAAc,GAAGX,eAAe,CAACU,aAAa,CAACE,GAAG,CAACC,GAAG,IAAI,GAAGA,GAAG,CAACC,YAAY,KAAKD,GAAG,CAAC7B,MAAM,GAAG,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAAC;QACjHjC,SAAS,GAAG,2BAA2BkB,eAAe,CAACQ,aAAa,eAAevD,cAAc,CAAC+C,eAAe,CAACgB,cAAc,IAAI,CAAC,CAAC,cAAc/D,cAAc,CAAC+C,eAAe,CAACiB,YAAY,IAAI,CAAC,CAAC,KAAKN,cAAc,gBAAgB1D,cAAc,CAAC+C,eAAe,CAACkB,cAAc,IAAI,CAAC,CAAC,GAAG;MAChS;MAEAlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP/C,MAAM,EAAEmC,eAAe,CAACa,OAAO,CAAC,CAAC,CAAC;QAClCtC;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACkB,eAAe,CAAC,CAAC;;EAErB;EACA7E,SAAS,CAAC,MAAM;IACd;IACA,MAAMkG,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACC,KAAK;IAC1CF,MAAM,CAACC,OAAO,CAACC,KAAK,GAAG,CAAC,GAAGC,IAAI,KAAK;MAAA,IAAAC,MAAA,EAAAC,eAAA;MAClC,KAAAD,MAAA,GAAID,IAAI,CAAC,CAAC,CAAC,cAAAC,MAAA,gBAAAC,eAAA,GAAPD,MAAA,CAASE,QAAQ,cAAAD,eAAA,eAAjBA,eAAA,CAAAE,IAAA,CAAAH,MAAA,EAAoB,qBAAqB,CAAC,EAAE;QAC9C;QACA;MACF;MACAL,aAAa,CAAC,GAAGI,IAAI,CAAC;IACxB,CAAC;IAED,OAAO,MAAM;MACXH,MAAM,CAACC,OAAO,CAACC,KAAK,GAAGH,aAAa;IACtC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMlB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM,CAAC2B,WAAW,EAAEC,UAAU,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnElF,KAAK,CAACmF,GAAG,CAAC,2BAA2B,CAAC,EACtCnF,KAAK,CAACmF,GAAG,CAAC,cAAc,CAAC,EACzBnF,KAAK,CAACmF,GAAG,CAAC,oBAAoB,CAAC,CAChC,CAAC;MAEFhD,WAAW,CAAC2C,WAAW,CAACM,IAAI,CAAC;MAC7B7C,UAAU,CAACwC,UAAU,CAACK,IAAI,CAAC;;MAE3B;MACA,MAAMC,QAAQ,GAAGL,eAAe,CAACI,IAAI;MACrC,MAAME,cAAc,GAAGD,QAAQ,CAACzB,GAAG,CAAC2B,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC;;MAE5E;MACA,IAAIJ,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMC,mBAAmB,GAAGd,WAAW,CAACM,IAAI,CAACK,MAAM,CAACI,GAAG,IACrDP,cAAc,CAACV,QAAQ,CAACiB,GAAG,CAACvC,GAAG,CACjC,CAAC;QACDjB,eAAe,CAACuD,mBAAmB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMpB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAA0C,qBAAA,EAAAC,qBAAA;MACFpD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqD,QAAQ,GAAG,MAAMhG,KAAK,CAACmF,GAAG,CAAC,yBAAyBzE,SAAS,EAAE,CAAC;MACtE,MAAMuF,WAAW,GAAGD,QAAQ,CAACZ,IAAI;MAEjCpE,UAAU,CAAC;QACT,GAAGiF,WAAW;QACd/E,WAAW,EAAE+E,WAAW,CAAC/E,WAAW,GAAGnB,KAAK,CAACkG,WAAW,CAAC/E,WAAW,CAAC,GAAGnB,KAAK,CAACkG,WAAW,CAACC,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAAC;QAClHhF,eAAe,EAAEpB,KAAK,CAACkG,WAAW,CAAC9E,eAAe,IAAI,IAAIgF,IAAI,CAAC,CAAC,CAAC;QACjEvE,UAAU,EAAEqE,WAAW,CAACrE,UAAU,GAAG7B,KAAK,CAACkG,WAAW,CAACrE,UAAU,CAAC,GAAG,IAAI;QACzEP,aAAa,EAAE,EAAAyE,qBAAA,GAAAG,WAAW,CAAC5E,aAAa,cAAAyE,qBAAA,uBAAzBA,qBAAA,CAA2BxC,GAAG,KAAI2C,WAAW,CAAC5E,aAAa;QAC1EC,WAAW,EAAE,EAAAyE,qBAAA,GAAAE,WAAW,CAAC3E,WAAW,cAAAyE,qBAAA,uBAAvBA,qBAAA,CAAyBzC,GAAG,KAAI2C,WAAW,CAAC3E,WAAW;QACpEF,MAAM,EAAEgF,UAAU,CAACH,WAAW,CAAC7E,MAAM,IAAI,CAAC,CAAC,CAACgD,OAAO,CAAC,CAAC;MACvD,CAAC,CAAC;;MAEF;MACA,IAAI6B,WAAW,CAAC1E,gBAAgB,KAAK,QAAQ,IAAI0E,WAAW,CAACzE,cAAc,EAAE;QAC3E,MAAM6E,SAAS,GAAG,MAAMrG,KAAK,CAACmF,GAAG,CAAC,gBAAgBc,WAAW,CAACzE,cAAc,EAAE,CAAC;QAC/EuB,iBAAiB,CAACsD,SAAS,CAACjB,IAAI,CAAC;;QAEjC;QACA,IAAIa,WAAW,CAAChE,kBAAkB,IAAIgE,WAAW,CAAChE,kBAAkB,CAAC0D,MAAM,GAAG,CAAC,EAAE;UAC/E,MAAMW,UAAU,GAAGL,WAAW,CAAChE,kBAAkB,CAACsE,IAAI,CAACC,GAAG,IACxDA,GAAG,CAACC,YAAY,KAAK,iBACvB,CAAC;UAED,IAAIH,UAAU,IAAIA,UAAU,CAACI,UAAU,EAAE;YACvC,MAAMC,UAAU,GAAG,MAAM3G,KAAK,CAACmF,GAAG,CAAC,0BAA0BmB,UAAU,CAACI,UAAU,EAAE,CAAC;YACrFzD,kBAAkB,CAAC0D,UAAU,CAACvB,IAAI,CAAC;UACrC;QACF;MACF;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAG,MAAOuD,QAAQ,IAAK;IAC9C,IAAI;MACFrC,OAAO,CAACsC,GAAG,CAAC,iCAAiCD,QAAQ,EAAE,CAAC;MACxD;MACA,MAAMZ,QAAQ,GAAG,MAAMhG,KAAK,CAACmF,GAAG,CAAC,yCAAyCyB,QAAQ,EAAE,CAAC;MACrFrC,OAAO,CAACsC,GAAG,CAAC,2BAA2B,EAAEb,QAAQ,CAACZ,IAAI,CAAC;MAEvD,IAAI0B,KAAK,CAACC,OAAO,CAACf,QAAQ,CAACZ,IAAI,CAAC,IAAIY,QAAQ,CAACZ,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;QAC5DlD,mBAAmB,CAACuD,QAAQ,CAACZ,IAAI,CAAC;MACpC,CAAC,MAAM;QACLb,OAAO,CAACsC,GAAG,CAAC,0CAA0C,CAAC;QACvDpE,mBAAmB,CAAC,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MAAA,IAAAwC,eAAA,EAAAC,oBAAA;MACd1C,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD0C,KAAK,CAAC,oCAAoC,EAAAF,eAAA,GAAAxC,KAAK,CAACwB,QAAQ,cAAAgB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB5B,IAAI,cAAA6B,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI3C,KAAK,CAAC2C,OAAO,EAAE,CAAC;MAC3F1E,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAM2E,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CtG,UAAU,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACkD,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAI1E,MAAM,CAACyE,KAAK,CAAC,EAAE;MACjBxE,SAAS,CAACsB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACkD,KAAK,GAAG;MAAK,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAME,kBAAkB,GAAIC,MAAM,IAAK;IACrCzE,iBAAiB,CAACyE,MAAM,CAAC;IACzBvE,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE1B,IAAIuE,MAAM,EAAE;MACV;MACAxG,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP5C,gBAAgB,EAAE,QAAQ;QAC1BC,cAAc,EAAEgG,MAAM,CAAClE,GAAG;QAC1B7B,gBAAgB,EAAE+F,MAAM,CAACC,IAAI;QAC7BnG,WAAW,EAAEkG,MAAM,CAAChC,SAAS,IAAI,EAAE;QAAE;QACrCpE,MAAM,EAAE,EAAE;QAAE;QACZU,SAAS,EAAE,EAAE;QAAE;QACfG,kBAAkB,EAAE,EAAE,CAAC;MACzB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACAjB,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP5C,gBAAgB,EAAE,EAAE;QACpBC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBH,WAAW,EAAE,EAAE;QACfF,MAAM,EAAE,EAAE;QACVU,SAAS,EAAE,EAAE;QACbG,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMyF,mBAAmB,GAAIC,OAAO,IAAK;IACvC1E,kBAAkB,CAAC0E,OAAO,CAAC;IAE3B,IAAIA,OAAO,EAAE;MACXpD,OAAO,CAACsC,GAAG,CAAC,mBAAmB,EAAEc,OAAO,CAAC;;MAEzC;MACA,MAAMpE,eAAe,GAAGoE,OAAO,CAACpE,eAAe;MAE/CgB,OAAO,CAACsC,GAAG,CAAC,oCAAoCtD,eAAe,EAAE,CAAC;;MAElE;MACA,IAAIzB,SAAS,GAAG,2BAA2B6F,OAAO,CAACnE,aAAa,EAAE;;MAElE;MACA,IAAImE,OAAO,CAAClE,UAAU,EAAE;QACtB,MAAME,cAAc,GAAGgE,OAAO,CAACjE,aAAa,CAACE,GAAG,CAACC,GAAG,IAAI,GAAGA,GAAG,CAACC,YAAY,KAAKD,GAAG,CAAC7B,MAAM,GAAG,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAAC;QACzGjC,SAAS,GAAG,2BAA2B6F,OAAO,CAACnE,aAAa,eAAevD,cAAc,CAAC0H,OAAO,CAAC3D,cAAc,CAAC,cAAc/D,cAAc,CAAC0H,OAAO,CAAC1D,YAAY,CAAC,KAAKN,cAAc,gBAAgB1D,cAAc,CAAC0H,OAAO,CAACzD,cAAc,CAAC,GAAG;MACjP;;MAEA;MACAlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP/C,MAAM,EAAEmC,eAAe,CAACqE,QAAQ,CAAC,CAAC;QAClC9F;MACF,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM+F,YAAY,GAAG;QACnBpB,YAAY,EAAE,iBAAiB;QAC/BC,UAAU,EAAEiB,OAAO,CAACrE,GAAG;QACvBwE,cAAc,EAAEH,OAAO,CAACnE,aAAa;QACrCuE,eAAe,EAAExE,eAAe;QAChCS,cAAc,EAAE2D,OAAO,CAAC3D,cAAc;QACtCC,YAAY,EAAE0D,OAAO,CAAC1D,YAAY;QAClCC,cAAc,EAAEyD,OAAO,CAACzD;MAC1B,CAAC;MAEDlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPlC,kBAAkB,EAAE,CAAC4F,YAAY;MACnC,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL7G,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPlC,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAM+F,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAClH,OAAO,CAACM,aAAa,EAAE4G,SAAS,CAAC5G,aAAa,GAAG,kBAAkB;IACxE,IAAI,CAACN,OAAO,CAACO,WAAW,EAAE2G,SAAS,CAAC3G,WAAW,GAAG,mCAAmC;IACrF,IAAI,CAACP,OAAO,CAACK,MAAM,IAAI8G,KAAK,CAAC9B,UAAU,CAACrF,OAAO,CAACK,MAAM,CAAC,CAAC,IAAIgF,UAAU,CAACrF,OAAO,CAACK,MAAM,CAAC,IAAI,CAAC,EAAE;MAC3F6G,SAAS,CAAC7G,MAAM,GAAG,0BAA0B;IAC/C;IACA,IAAI,CAACL,OAAO,CAACG,WAAW,EAAE+G,SAAS,CAAC/G,WAAW,GAAG,0BAA0B;IAC5E,IAAI,CAACH,OAAO,CAACI,eAAe,EAAE8G,SAAS,CAAC9G,eAAe,GAAG,8BAA8B;IACxF,IAAI,CAACJ,OAAO,CAACe,SAAS,EAAEmG,SAAS,CAACnG,SAAS,GAAG,uBAAuB;;IAErE;IACA,IAAIgB,cAAc,IAAIE,eAAe,EAAE;MACrC;MACA,IAAIA,eAAe,CAAC4D,QAAQ,KAAK9D,cAAc,CAACQ,GAAG,EAAE;QACnD2E,SAAS,CAACN,OAAO,GAAG,yDAAyD;MAC/E;;MAEA;MACA,MAAMpE,eAAe,GAAGP,eAAe,CAACO,eAAe,IAAI,CAAC;MAC5D,MAAM4E,aAAa,GAAG/B,UAAU,CAACrF,OAAO,CAACK,MAAM,CAAC,IAAI,CAAC;MACrD,IAAI+G,aAAa,GAAG5E,eAAe,EAAE;QACnC0E,SAAS,CAAC7G,MAAM,GAAG,oDAAoDmC,eAAe,CAACa,OAAO,CAAC,CAAC,CAAC,EAAE;MACrG;IACF;IAEAvB,SAAS,CAACoF,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACtC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAM2C,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErB,IAAI;MACFrF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMsD,WAAW,GAAG;QAClB,GAAGlF,OAAO;QACVG,WAAW,EAAEH,OAAO,CAACG,WAAW,CAACqH,WAAW,CAAC,CAAC;QAC9CpH,eAAe,EAAEJ,OAAO,CAACI,eAAe,CAACoH,WAAW,CAAC,CAAC;QACtD3G,UAAU,EAAEb,OAAO,CAACa,UAAU,GAAGb,OAAO,CAACa,UAAU,CAAC2G,WAAW,CAAC,CAAC,GAAG,IAAI;QACxEnH,MAAM,EAAEgF,UAAU,CAACrF,OAAO,CAACK,MAAM;MACnC,CAAC;MAED,IAAI4E,QAAQ;MACZ,IAAItF,SAAS,EAAE;QACbsF,QAAQ,GAAG,MAAMhG,KAAK,CAACwI,GAAG,CAAC,yBAAyB9H,SAAS,EAAE,EAAEuF,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLD,QAAQ,GAAG,MAAMhG,KAAK,CAACyI,IAAI,CAAC,uBAAuB,EAAExC,WAAW,CAAC;MACnE;MAEA,IAAIrF,MAAM,EAAEA,MAAM,CAACoF,QAAQ,CAACZ,IAAI,CAACrE,OAAO,CAAC;IAC3C,CAAC,CAAC,OAAOyD,KAAK,EAAE;MAAA,IAAAkE,gBAAA,EAAAC,qBAAA;MACdpE,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C0C,KAAK,CAAC,EAAAwB,gBAAA,GAAAlE,KAAK,CAACwB,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtD,IAAI,cAAAuD,qBAAA,uBAApBA,qBAAA,CAAsBxB,OAAO,KAAI,mCAAmC,CAAC;IAC7E,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFjG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM3C,KAAK,CAACwI,GAAG,CAAC,yBAAyB9H,SAAS,UAAU,CAAC;MAC7D,IAAIE,MAAM,EAAEA,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4D,KAAK,EAAE;MAAA,IAAAqE,gBAAA,EAAAC,qBAAA;MACdvE,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD0C,KAAK,CAAC,EAAA2B,gBAAA,GAAArE,KAAK,CAACwB,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzD,IAAI,cAAA0D,qBAAA,uBAApBA,qBAAA,CAAsB3B,OAAO,KAAI,yBAAyB,CAAC;IACnE,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFpG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM3C,KAAK,CAACwI,GAAG,CAAC,yBAAyB9H,SAAS,OAAO,CAAC;MAC1D,IAAIE,MAAM,EAAEA,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4D,KAAK,EAAE;MAAA,IAAAwE,gBAAA,EAAAC,qBAAA;MACd1E,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C0C,KAAK,CAAC,EAAA8B,gBAAA,GAAAxE,KAAK,CAACwB,QAAQ,cAAAgD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5D,IAAI,cAAA6D,qBAAA,uBAApBA,qBAAA,CAAsB9B,OAAO,KAAI,uBAAuB,CAAC;IACjE,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuG,OAAO,GAAG,CAACvI,QAAQ,KAAK,CAACD,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,OAAO,CAAC;EACvE,MAAMmH,UAAU,GAAG,CAACxI,QAAQ,IAAID,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,OAAO;EACvE,MAAMoH,OAAO,GAAG,CAACzI,QAAQ,IAAID,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,UAAU;EAEvE,oBACE1B,OAAA,CAAClC,GAAG;IAAAiL,QAAA,eACF/I,OAAA,CAACT,oBAAoB;MAACyJ,WAAW,EAAExJ,YAAa;MAAAuJ,QAAA,eAC9C/I,OAAA,CAAC7B,IAAI;QAAC8K,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAH,QAAA,gBAEzB/I,OAAA,CAAC7B,IAAI;UAACgL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACvB/I,OAAA,CAACjC,IAAI;YAACuL,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACrC/I,OAAA,CAAChC,WAAW;cAAA+K,QAAA,gBACV/I,OAAA,CAAC/B,UAAU;gBAACqL,OAAO,EAAC,IAAI;gBAACG,YAAY;gBAAAV,QAAA,EAAC;cAEtC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAGZzJ,SAAS,iBACRJ,OAAA,CAAC5B,SAAS;gBACR0L,SAAS;gBACTC,KAAK,EAAC,aAAa;gBACnB/C,KAAK,EAAEvG,OAAO,CAACuJ,aAAa,IAAI,EAAG;gBACnCC,MAAM,EAAC,QAAQ;gBACfC,UAAU,EAAE;kBAAE7J,QAAQ,EAAE;gBAAK;cAAE;gBAAAqJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACF,eAGD7J,OAAA,CAACV,UAAU;gBACTyK,KAAK,EAAC,cAAc;gBACpB/C,KAAK,EAAEvG,OAAO,CAACG,WAAY;gBAC3BuJ,QAAQ,EAAGC,QAAQ,IAAKtD,iBAAiB,CAAC,aAAa,EAAEsD,QAAQ,CAAE;gBACnEC,MAAM,EAAC,aAAa;gBACpBC,WAAW,EAAGC,MAAM,iBAClBvK,OAAA,CAAC5B,SAAS;kBAAA,GACJmM,MAAM;kBACVT,SAAS;kBACTG,MAAM,EAAC,QAAQ;kBACf/F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAAC1B,WAAY;kBAC5B4J,UAAU,EAAElI,MAAM,CAAC1B,WAAY;kBAC/BsJ,UAAU,EAAE;oBACV,GAAGK,MAAM,CAACL,UAAU;oBACpB7J,QAAQ,EAAE,CAACuI;kBACb;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFxJ,QAAQ,EAAE,CAACuI;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGF7J,OAAA,CAACV,UAAU;gBACTyK,KAAK,EAAC,kBAAkB;gBACxB/C,KAAK,EAAEvG,OAAO,CAACI,eAAgB;gBAC/BsJ,QAAQ,EAAGC,QAAQ,IAAKtD,iBAAiB,CAAC,iBAAiB,EAAEsD,QAAQ,CAAE;gBACvEC,MAAM,EAAC,aAAa;gBACpBC,WAAW,EAAGC,MAAM,iBAClBvK,OAAA,CAAC5B,SAAS;kBAAA,GACJmM,MAAM;kBACVT,SAAS;kBACTG,MAAM,EAAC,QAAQ;kBACf/F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACzB,eAAgB;kBAChC2J,UAAU,EAAElI,MAAM,CAACzB,eAAgB;kBACnCqJ,UAAU,EAAE;oBACV,GAAGK,MAAM,CAACL,UAAU;oBACpB7J,QAAQ,EAAE,CAACuI;kBACb;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFxJ,QAAQ,EAAE,CAACuI;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGF7J,OAAA,CAAC5B,SAAS;gBACR0L,SAAS;gBACTC,KAAK,EAAC,gBAAgB;gBACtB/C,KAAK,EAAEvG,OAAO,CAACc,aAAa,IAAI,EAAG;gBACnC4I,QAAQ,EAAGM,CAAC,IAAK3D,iBAAiB,CAAC,eAAe,EAAE2D,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;gBACpEiD,MAAM,EAAC,QAAQ;gBACfC,UAAU,EAAE;kBAAE7J,QAAQ,EAAE,CAACuI;gBAAQ,CAAE;gBACnC4B,UAAU,EAAC;cAAqD;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eAGF7J,OAAA,CAAC3B,WAAW;gBACVyL,SAAS;gBACTG,MAAM,EAAC,QAAQ;gBACf/F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACvB,aAAc;gBAAAgI,QAAA,gBAE9B/I,OAAA,CAAC1B,UAAU;kBAAAyK,QAAA,EAAC;gBAAI;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7B7J,OAAA,CAACzB,MAAM;kBACLyI,KAAK,EAAEvG,OAAO,CAACM,aAAc;kBAC7BoJ,QAAQ,EAAGM,CAAC,IAAK3D,iBAAiB,CAAC,eAAe,EAAE2D,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;kBACpE+C,KAAK,EAAC,MAAM;kBACZY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,gBAEnB/I,OAAA,CAACxB,QAAQ;oBAACwI,KAAK,EAAC,EAAE;oBAAA+B,QAAA,eAChB/I,OAAA;sBAAA+I,QAAA,EAAI;oBAAW;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,EACV/H,YAAY,CAACwB,GAAG,CAAEsH,OAAO,iBACxB5K,OAAA,CAACxB,QAAQ;oBAAmBwI,KAAK,EAAE4D,OAAO,CAAC5H,GAAI;oBAAA+F,QAAA,EAC5C6B,OAAO,CAACC;kBAAW,GADPD,OAAO,CAAC5H,GAAG;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRvH,MAAM,CAACvB,aAAa,iBACnBf,OAAA,CAACnB,cAAc;kBAAAkK,QAAA,EAAEzG,MAAM,CAACvB;gBAAa;kBAAA2I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eAGd7J,OAAA,CAAC3B,WAAW;gBAACyL,SAAS;gBAACG,MAAM,EAAC,QAAQ;gBAAAlB,QAAA,gBACpC/I,OAAA,CAAC1B,UAAU;kBAAAyK,QAAA,EAAC;gBAAc;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvC7J,OAAA,CAACzB,MAAM;kBACLyI,KAAK,EAAEvG,OAAO,CAACW,aAAc;kBAC7B+I,QAAQ,EAAGM,CAAC,IAAK3D,iBAAiB,CAAC,eAAe,EAAE2D,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;kBACpE+C,KAAK,EAAC,gBAAgB;kBACtBY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,EAElBnG,cAAc,CAACU,GAAG,CAAEwH,MAAM,iBACzB9K,OAAA,CAACxB,QAAQ;oBAAcwI,KAAK,EAAE8D,MAAO;oBAAA/B,QAAA,EAClC+B;kBAAM,GADMA,MAAM;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGbpJ,OAAO,CAACW,aAAa,KAAK,QAAQ,iBACjCpB,OAAA,CAAAE,SAAA;gBAAA6I,QAAA,gBACE/I,OAAA,CAAC5B,SAAS;kBACR0L,SAAS;kBACTC,KAAK,EAAC,eAAe;kBACrB/C,KAAK,EAAEvG,OAAO,CAACY,YAAY,IAAI,EAAG;kBAClC8I,QAAQ,EAAGM,CAAC,IAAK3D,iBAAiB,CAAC,cAAc,EAAE2D,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;kBACnEiD,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBAAE7J,QAAQ,EAAE,CAACuI;kBAAQ;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eAEF7J,OAAA,CAACV,UAAU;kBACTyK,KAAK,EAAC,aAAa;kBACnB/C,KAAK,EAAEvG,OAAO,CAACa,UAAW;kBAC1B6I,QAAQ,EAAGC,QAAQ,IAAKtD,iBAAiB,CAAC,YAAY,EAAEsD,QAAQ,CAAE;kBAClEE,WAAW,EAAGC,MAAM,iBAClBvK,OAAA,CAAC5B,SAAS;oBAAA,GACJmM,MAAM;oBACVT,SAAS;oBACTG,MAAM,EAAC,QAAQ;oBACfC,UAAU,EAAE;sBACV,GAAGK,MAAM,CAACL,UAAU;sBACpB7J,QAAQ,EAAE,CAACuI;oBACb;kBAAE;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACD;kBACFxJ,QAAQ,EAAE,CAACuI;gBAAQ;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA,eACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP7J,OAAA,CAAC7B,IAAI;UAACgL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACvB/I,OAAA,CAACjC,IAAI;YAACuL,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACrC/I,OAAA,CAAChC,WAAW;cAAA+K,QAAA,gBACV/I,OAAA,CAAC/B,UAAU;gBAACqL,OAAO,EAAC,IAAI;gBAACG,YAAY;gBAAAV,QAAA,EAAC;cAEtC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAGb7J,OAAA,CAACpB,YAAY;gBACXmM,OAAO,EAAE/I,OAAQ;gBACjBgJ,cAAc,EAAG9D,MAAM,IAAKA,MAAM,CAACC,IAAI,IAAI,EAAG;gBAC9CH,KAAK,EAAExE,cAAe;gBACtB2H,QAAQ,EAAEA,CAACc,KAAK,EAAEb,QAAQ,KAAKnD,kBAAkB,CAACmD,QAAQ,CAAE;gBAC5DE,WAAW,EAAGC,MAAM,iBAClBvK,OAAA,CAAC5B,SAAS;kBAAA,GACJmM,MAAM;kBACVR,KAAK,EAAC,QAAQ;kBACdE,MAAM,EAAC,QAAQ;kBACfH,SAAS;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACD;gBACFc,QAAQ,EAAE,CAAC/B;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EAGDrH,cAAc,iBACbxC,OAAA,CAACpB,YAAY;gBACXmM,OAAO,EAAE7I,gBAAiB;gBAC1B8I,cAAc,EAAG3D,OAAO,IAAK;kBAC3B,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;kBAEvB,MAAM6D,YAAY,GAAG,GAAG7D,OAAO,CAACnE,aAAa,IAAI,SAAS,EAAE;kBAC5D,MAAMiI,WAAW,GAAG,IAAItF,IAAI,CAACwB,OAAO,CAAC8D,WAAW,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;oBAC5EC,GAAG,EAAE,SAAS;oBACdC,KAAK,EAAE,OAAO;oBACdC,IAAI,EAAE;kBACR,CAAC,CAAC;;kBAEF;kBACA,IAAIlE,OAAO,CAAClE,UAAU,EAAE;oBACtB,OAAO,GAAG+H,YAAY,KAAKC,WAAW,OAAOxL,cAAc,CAAC0H,OAAO,CAACpE,eAAe,CAAC,oBAAoB;kBAC1G;;kBAEA;kBACA,OAAO,GAAGiI,YAAY,KAAKC,WAAW,OAAOxL,cAAc,CAAC0H,OAAO,CAACpE,eAAe,CAAC,cAAc;gBACpG,CAAE;gBACF+D,KAAK,EAAEtE,eAAgB;gBACvByH,QAAQ,EAAEA,CAACc,KAAK,EAAEb,QAAQ,KAAKhD,mBAAmB,CAACgD,QAAQ,CAAE;gBAC7DoB,YAAY,EAAEA,CAACC,KAAK,EAAEpE,OAAO,kBAC3BrH,OAAA;kBAAA,GAAQyL,KAAK;kBAAA1C,QAAA,eACX/I,OAAA,CAAClC,GAAG;oBAACyL,EAAE,EAAE;sBAAEmC,KAAK,EAAE,MAAM;sBAAEC,QAAQ,EAAE,MAAM;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/D/I,OAAA,CAAC/B,UAAU;sBAACqL,OAAO,EAAC,OAAO;sBAACuC,UAAU,EAAC,QAAQ;sBAACC,MAAM;sBAAA/C,QAAA,GACnD1B,OAAO,CAACnE,aAAa,EAAC,KAAG,EAACvD,cAAc,CAAC0H,OAAO,CAACpE,eAAe,CAAC,EAAC,cACrE;oBAAA;sBAAAyG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,EACZxC,OAAO,CAAClE,UAAU,iBACjBnD,OAAA,CAAAE,SAAA;sBAAA6I,QAAA,gBACE/I,OAAA,CAAC/B,UAAU;wBAACqL,OAAO,EAAC,SAAS;wBAACyC,KAAK,EAAC,gBAAgB;wBAACC,OAAO,EAAC,OAAO;wBAACF,MAAM;wBAAA/C,QAAA,GAAC,YAChE,EAACpJ,cAAc,CAAC0H,OAAO,CAAC3D,cAAc,CAAC,EAAC,cACzC,EAAC/D,cAAc,CAAC0H,OAAO,CAAC1D,YAAY,CAAC,EAAC,eACrC,EAAChE,cAAc,CAAC0H,OAAO,CAACzD,cAAc,CAAC;sBAAA;wBAAA8F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACb7J,OAAA,CAAC/B,UAAU;wBAACqL,OAAO,EAAC,SAAS;wBAACyC,KAAK,EAAC,SAAS;wBAACC,OAAO,EAAC,OAAO;wBAACF,MAAM;wBAAA/C,QAAA,GACjE1B,OAAO,CAACjE,aAAa,CAACiC,MAAM,EAAC,sBAC9B,EAACgC,OAAO,CAACjE,aAAa,CAACE,GAAG,CAAC,CAACC,GAAG,EAAE0I,GAAG,kBAClCjM,OAAA;0BAAA+I,QAAA,GACGxF,GAAG,CAACC,YAAY,EAAC,IAAE,EAACD,GAAG,CAAC7B,MAAM,EAAC,GAChC,EAACuK,GAAG,GAAG5E,OAAO,CAACjE,aAAa,CAACiC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE;wBAAA,GAF1C4G,GAAG;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAGR,CACP,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA,eACb,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACJ;gBACFS,WAAW,EAAGC,MAAM,iBAClBvK,OAAA,CAAC5B,SAAS;kBAAA,GACJmM,MAAM;kBACVR,KAAK,EAAC,kBAAkB;kBACxBE,MAAM,EAAC,QAAQ;kBACfH,SAAS;kBACT5F,KAAK,EAAEhC,gBAAgB,CAACmD,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC/C,MAAM,CAAC+E,OAAQ;kBACzDmD,UAAU,EACRlI,MAAM,CAAC+E,OAAO,KACbnF,gBAAgB,CAACmD,MAAM,KAAK,CAAC,GAAG,0CAA0C,GAC3E,gFAAgF;gBACjF;kBAAAqE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACD;gBACFc,QAAQ,EAAE,CAAC/B,OAAO,IAAI1G,gBAAgB,CAACmD,MAAM,KAAK,CAAE;gBACpD6G,aAAa,EAAC,0BAA0B;gBACxCC,YAAY,EAAE;kBACZC,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAC9B;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EAGA,CAACrH,cAAc,iBACdxC,OAAA,CAAC3B,WAAW;gBACVyL,SAAS;gBACTG,MAAM,EAAC,QAAQ;gBACf/F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACtB,WAAY;gBAAA+H,QAAA,gBAE5B/I,OAAA,CAAC1B,UAAU;kBAAAyK,QAAA,EAAC;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9B7J,OAAA,CAACzB,MAAM;kBACLyI,KAAK,EAAEvG,OAAO,CAACO,WAAY;kBAC3BmJ,QAAQ,EAAGM,CAAC,IAAK3D,iBAAiB,CAAC,aAAa,EAAE2D,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;kBAClE+C,KAAK,EAAC,OAAO;kBACbY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,gBAEnB/I,OAAA,CAACxB,QAAQ;oBAACwI,KAAK,EAAC,EAAE;oBAAA+B,QAAA,eAChB/I,OAAA;sBAAA+I,QAAA,EAAI;oBAAc;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACVjI,QAAQ,CAAC0B,GAAG,CAAEsH,OAAO,iBACpB5K,OAAA,CAACxB,QAAQ;oBAAmBwI,KAAK,EAAE4D,OAAO,CAAC5H,GAAI;oBAAA+F,QAAA,GAC5C6B,OAAO,CAAC0B,WAAW,EAAC,KAAG,EAAC1B,OAAO,CAACC,WAAW;kBAAA,GAD/BD,OAAO,CAAC5H,GAAG;oBAAA0G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRvH,MAAM,CAACtB,WAAW,iBACjBhB,OAAA,CAACnB,cAAc;kBAAAkK,QAAA,EAAEzG,MAAM,CAACtB;gBAAW;kBAAA0I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACrD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CACd,eAGD7J,OAAA,CAAC5B,SAAS;gBACR0L,SAAS;gBACTC,KAAK,EAAC,QAAQ;gBACdwC,IAAI,EAAC,QAAQ;gBACbvF,KAAK,EAAEvG,OAAO,CAACK,MAAO;gBACtBqJ,QAAQ,EAAGM,CAAC,IAAK3D,iBAAiB,CAAC,QAAQ,EAAE2D,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;gBAC7DiD,MAAM,EAAC,QAAQ;gBACf/F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACxB,MAAO;gBACvB0J,UAAU,EAAElI,MAAM,CAACxB,MAAO;gBAC1BoJ,UAAU,EAAE;kBAAE7J,QAAQ,EAAE,CAACuI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAGF7J,OAAA,CAAC5B,SAAS;gBACR0L,SAAS;gBACTC,KAAK,EAAC,WAAW;gBACjB/C,KAAK,EAAEvG,OAAO,CAACe,SAAU;gBACzB2I,QAAQ,EAAGM,CAAC,IAAK3D,iBAAiB,CAAC,WAAW,EAAE2D,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;gBAChEiD,MAAM,EAAC,QAAQ;gBACfuC,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRvI,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACd,SAAU;gBAC1BgJ,UAAU,EAAElI,MAAM,CAACd,SAAU;gBAC7B0I,UAAU,EAAE;kBAAE7J,QAAQ,EAAE,CAACuI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAGF7J,OAAA,CAAC5B,SAAS;gBACR0L,SAAS;gBACTC,KAAK,EAAC,aAAa;gBACnB/C,KAAK,EAAEvG,OAAO,CAACgB,WAAW,IAAI,EAAG;gBACjC0I,QAAQ,EAAGM,CAAC,IAAK3D,iBAAiB,CAAC,aAAa,EAAE2D,CAAC,CAACC,MAAM,CAAC1D,KAAK,CAAE;gBAClEiD,MAAM,EAAC,QAAQ;gBACfuC,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRvC,UAAU,EAAE;kBAAE7J,QAAQ,EAAE,CAACuI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP7J,OAAA,CAAC7B,IAAI;UAACgL,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAL,QAAA,eAChB/I,OAAA,CAAClC,GAAG;YAACyL,EAAE,EAAE;cAAEyC,OAAO,EAAE,MAAM;cAAEU,cAAc,EAAE,UAAU;cAAEC,GAAG,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA7D,QAAA,GACrExI,QAAQ,iBACPP,OAAA,CAAC9B,MAAM;cACLoL,OAAO,EAAC,UAAU;cAClByC,KAAK,EAAC,WAAW;cACjBc,SAAS,eAAE7M,OAAA,CAACX,UAAU;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BiD,OAAO,EAAEvM,QAAS;cAAAwI,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAjB,OAAO,iBACN5I,OAAA,CAAC9B,MAAM;cACLoL,OAAO,EAAC,WAAW;cACnByC,KAAK,EAAC,SAAS;cACfc,SAAS,eAAE7M,OAAA,CAACjB,QAAQ;gBAAA2K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBiD,OAAO,EAAE9E,UAAW;cACpB2C,QAAQ,EAAEvI,OAAQ;cAAA2G,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAhB,UAAU,iBACT7I,OAAA,CAAC9B,MAAM;cACLoL,OAAO,EAAC,WAAW;cACnByC,KAAK,EAAC,SAAS;cACfc,SAAS,eAAE7M,OAAA,CAACf,WAAW;gBAAAyK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BiD,OAAO,EAAExE,aAAc;cACvBqC,QAAQ,EAAEvI,OAAQ;cAAA2G,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAf,OAAO,iBACN9I,OAAA,CAAC9B,MAAM;cACLoL,OAAO,EAAC,WAAW;cACnByC,KAAK,EAAC,SAAS;cACfc,SAAS,eAAE7M,OAAA,CAACb,QAAQ;gBAAAuK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBiD,OAAO,EAAErE,UAAW;cACpBkC,QAAQ,EAAEvI,OAAQ;cAAA2G,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEV,CAAC;AAACrJ,EAAA,CAvuBIL,sBAAsB;AAAA4M,EAAA,GAAtB5M,sBAAsB;AAyuB5B,eAAeA,sBAAsB;AAAC,IAAA4M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}