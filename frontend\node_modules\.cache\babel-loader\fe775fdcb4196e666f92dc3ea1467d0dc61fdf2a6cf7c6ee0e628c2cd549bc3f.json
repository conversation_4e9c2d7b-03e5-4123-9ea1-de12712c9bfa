{"ast": null, "code": "// browser\nexport * from './browser/BrowserAztecCodeReader';\nexport * from './browser/BrowserBarcodeReader';\nexport * from './browser/BrowserCodeReader';\nexport * from './browser/BrowserDatamatrixCodeReader';\nexport * from './browser/BrowserMultiFormatReader';\nexport * from './browser/BrowserPDF417Reader';\nexport * from './browser/BrowserQRCodeReader';\nexport * from './browser/BrowserQRCodeSvgWriter';\nexport * from './browser/DecodeContinuouslyCallback';\nexport * from './browser/HTMLCanvasElementLuminanceSource';\nexport * from './browser/HTMLVisualMediaElement';\nexport * from './browser/VideoInputDevice';", "map": {"version": 3, "names": [], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/browser.js"], "sourcesContent": ["// browser\nexport * from './browser/BrowserAztecCodeReader';\nexport * from './browser/BrowserBarcodeReader';\nexport * from './browser/BrowserCodeReader';\nexport * from './browser/BrowserDatamatrixCodeReader';\nexport * from './browser/BrowserMultiFormatReader';\nexport * from './browser/BrowserPDF417Reader';\nexport * from './browser/BrowserQRCodeReader';\nexport * from './browser/BrowserQRCodeSvgWriter';\nexport * from './browser/DecodeContinuouslyCallback';\nexport * from './browser/HTMLCanvasElementLuminanceSource';\nexport * from './browser/HTMLVisualMediaElement';\nexport * from './browser/VideoInputDevice';\n"], "mappings": "AAAA;AACA,cAAc,kCAAkC;AAChD,cAAc,gCAAgC;AAC9C,cAAc,6BAA6B;AAC3C,cAAc,uCAAuC;AACrD,cAAc,oCAAoC;AAClD,cAAc,+BAA+B;AAC7C,cAAc,+BAA+B;AAC7C,cAAc,kCAAkC;AAChD,cAAc,sCAAsC;AACpD,cAAc,4CAA4C;AAC1D,cAAc,kCAAkC;AAChD,cAAc,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}