{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing {*/\nimport System from './util/System';\nimport LuminanceSource from './LuminanceSource';\nimport InvertedLuminanceSource from './InvertedLuminanceSource';\nimport IllegalArgumentException from './IllegalArgumentException';\n/**\n * This object extends LuminanceSource around an array of YUV data returned from the camera driver,\n * with the option to crop to a rectangle within the full data. This can be used to exclude\n * superfluous pixels around the perimeter and speed up decoding.\n *\n * It works for any pixel format where the Y channel is planar and appears first, including\n * YCbCr_420_SP and YCbCr_422_SP.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar PlanarYUVLuminanceSource = /** @class */function (_super) {\n  __extends(PlanarYUVLuminanceSource, _super);\n  function PlanarYUVLuminanceSource(yuvData, dataWidth /*int*/, dataHeight /*int*/, left /*int*/, top /*int*/, width /*int*/, height /*int*/, reverseHorizontal) {\n    var _this = _super.call(this, width, height) || this;\n    _this.yuvData = yuvData;\n    _this.dataWidth = dataWidth;\n    _this.dataHeight = dataHeight;\n    _this.left = left;\n    _this.top = top;\n    if (left + width > dataWidth || top + height > dataHeight) {\n      throw new IllegalArgumentException('Crop rectangle does not fit within image data.');\n    }\n    if (reverseHorizontal) {\n      _this.reverseHorizontal(width, height);\n    }\n    return _this;\n  }\n  /*@Override*/\n  PlanarYUVLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n    if (y < 0 || y >= this.getHeight()) {\n      throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n    }\n    var width = this.getWidth();\n    if (row === null || row === undefined || row.length < width) {\n      row = new Uint8ClampedArray(width);\n    }\n    var offset = (y + this.top) * this.dataWidth + this.left;\n    System.arraycopy(this.yuvData, offset, row, 0, width);\n    return row;\n  };\n  /*@Override*/\n  PlanarYUVLuminanceSource.prototype.getMatrix = function () {\n    var width = this.getWidth();\n    var height = this.getHeight();\n    // If the caller asks for the entire underlying image, save the copy and give them the\n    // original data. The docs specifically warn that result.length must be ignored.\n    if (width === this.dataWidth && height === this.dataHeight) {\n      return this.yuvData;\n    }\n    var area = width * height;\n    var matrix = new Uint8ClampedArray(area);\n    var inputOffset = this.top * this.dataWidth + this.left;\n    // If the width matches the full width of the underlying data, perform a single copy.\n    if (width === this.dataWidth) {\n      System.arraycopy(this.yuvData, inputOffset, matrix, 0, area);\n      return matrix;\n    }\n    // Otherwise copy one cropped row at a time.\n    for (var y = 0; y < height; y++) {\n      var outputOffset = y * width;\n      System.arraycopy(this.yuvData, inputOffset, matrix, outputOffset, width);\n      inputOffset += this.dataWidth;\n    }\n    return matrix;\n  };\n  /*@Override*/\n  PlanarYUVLuminanceSource.prototype.isCropSupported = function () {\n    return true;\n  };\n  /*@Override*/\n  PlanarYUVLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n    return new PlanarYUVLuminanceSource(this.yuvData, this.dataWidth, this.dataHeight, this.left + left, this.top + top, width, height, false);\n  };\n  PlanarYUVLuminanceSource.prototype.renderThumbnail = function () {\n    var width = this.getWidth() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n    var height = this.getHeight() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n    var pixels = new Int32Array(width * height);\n    var yuv = this.yuvData;\n    var inputOffset = this.top * this.dataWidth + this.left;\n    for (var y = 0; y < height; y++) {\n      var outputOffset = y * width;\n      for (var x = 0; x < width; x++) {\n        var grey = yuv[inputOffset + x * PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR] & 0xff;\n        pixels[outputOffset + x] = 0xFF000000 | grey * 0x00010101;\n      }\n      inputOffset += this.dataWidth * PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n    }\n    return pixels;\n  };\n  /**\n   * @return width of image from {@link #renderThumbnail()}\n   */\n  PlanarYUVLuminanceSource.prototype.getThumbnailWidth = function () {\n    return this.getWidth() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n  };\n  /**\n   * @return height of image from {@link #renderThumbnail()}\n   */\n  PlanarYUVLuminanceSource.prototype.getThumbnailHeight = function () {\n    return this.getHeight() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n  };\n  PlanarYUVLuminanceSource.prototype.reverseHorizontal = function (width /*int*/, height /*int*/) {\n    var yuvData = this.yuvData;\n    for (var y = 0, rowStart = this.top * this.dataWidth + this.left; y < height; y++, rowStart += this.dataWidth) {\n      var middle = rowStart + width / 2;\n      for (var x1 = rowStart, x2 = rowStart + width - 1; x1 < middle; x1++, x2--) {\n        var temp = yuvData[x1];\n        yuvData[x1] = yuvData[x2];\n        yuvData[x2] = temp;\n      }\n    }\n  };\n  PlanarYUVLuminanceSource.prototype.invert = function () {\n    return new InvertedLuminanceSource(this);\n  };\n  PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR = 2;\n  return PlanarYUVLuminanceSource;\n}(LuminanceSource);\nexport default PlanarYUVLuminanceSource;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "System", "LuminanceSource", "InvertedLuminanceSource", "IllegalArgumentException", "PlanarYUVLuminanceSource", "_super", "yuvData", "dataWidth", "dataHeight", "left", "top", "width", "height", "reverseHorizontal", "_this", "call", "getRow", "y", "row", "getHeight", "getWidth", "undefined", "length", "Uint8ClampedArray", "offset", "arraycopy", "getMatrix", "area", "matrix", "inputOffset", "outputOffset", "isCropSupported", "crop", "renderThumbnail", "THUMBNAIL_SCALE_FACTOR", "pixels", "Int32Array", "yuv", "x", "grey", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getThumbnailHeight", "rowStart", "middle", "x1", "x2", "temp", "invert"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/PlanarYUVLuminanceSource.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing {*/\nimport System from './util/System';\nimport LuminanceSource from './LuminanceSource';\nimport InvertedLuminanceSource from './InvertedLuminanceSource';\nimport IllegalArgumentException from './IllegalArgumentException';\n/**\n * This object extends LuminanceSource around an array of YUV data returned from the camera driver,\n * with the option to crop to a rectangle within the full data. This can be used to exclude\n * superfluous pixels around the perimeter and speed up decoding.\n *\n * It works for any pixel format where the Y channel is planar and appears first, including\n * YCbCr_420_SP and YCbCr_422_SP.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar PlanarYUVLuminanceSource = /** @class */ (function (_super) {\n    __extends(PlanarYUVLuminanceSource, _super);\n    function PlanarYUVLuminanceSource(yuvData, dataWidth /*int*/, dataHeight /*int*/, left /*int*/, top /*int*/, width /*int*/, height /*int*/, reverseHorizontal) {\n        var _this = _super.call(this, width, height) || this;\n        _this.yuvData = yuvData;\n        _this.dataWidth = dataWidth;\n        _this.dataHeight = dataHeight;\n        _this.left = left;\n        _this.top = top;\n        if (left + width > dataWidth || top + height > dataHeight) {\n            throw new IllegalArgumentException('Crop rectangle does not fit within image data.');\n        }\n        if (reverseHorizontal) {\n            _this.reverseHorizontal(width, height);\n        }\n        return _this;\n    }\n    /*@Override*/\n    PlanarYUVLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n        if (y < 0 || y >= this.getHeight()) {\n            throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n        }\n        var width = this.getWidth();\n        if (row === null || row === undefined || row.length < width) {\n            row = new Uint8ClampedArray(width);\n        }\n        var offset = (y + this.top) * this.dataWidth + this.left;\n        System.arraycopy(this.yuvData, offset, row, 0, width);\n        return row;\n    };\n    /*@Override*/\n    PlanarYUVLuminanceSource.prototype.getMatrix = function () {\n        var width = this.getWidth();\n        var height = this.getHeight();\n        // If the caller asks for the entire underlying image, save the copy and give them the\n        // original data. The docs specifically warn that result.length must be ignored.\n        if (width === this.dataWidth && height === this.dataHeight) {\n            return this.yuvData;\n        }\n        var area = width * height;\n        var matrix = new Uint8ClampedArray(area);\n        var inputOffset = this.top * this.dataWidth + this.left;\n        // If the width matches the full width of the underlying data, perform a single copy.\n        if (width === this.dataWidth) {\n            System.arraycopy(this.yuvData, inputOffset, matrix, 0, area);\n            return matrix;\n        }\n        // Otherwise copy one cropped row at a time.\n        for (var y = 0; y < height; y++) {\n            var outputOffset = y * width;\n            System.arraycopy(this.yuvData, inputOffset, matrix, outputOffset, width);\n            inputOffset += this.dataWidth;\n        }\n        return matrix;\n    };\n    /*@Override*/\n    PlanarYUVLuminanceSource.prototype.isCropSupported = function () {\n        return true;\n    };\n    /*@Override*/\n    PlanarYUVLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        return new PlanarYUVLuminanceSource(this.yuvData, this.dataWidth, this.dataHeight, this.left + left, this.top + top, width, height, false);\n    };\n    PlanarYUVLuminanceSource.prototype.renderThumbnail = function () {\n        var width = this.getWidth() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n        var height = this.getHeight() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n        var pixels = new Int32Array(width * height);\n        var yuv = this.yuvData;\n        var inputOffset = this.top * this.dataWidth + this.left;\n        for (var y = 0; y < height; y++) {\n            var outputOffset = y * width;\n            for (var x = 0; x < width; x++) {\n                var grey = yuv[inputOffset + x * PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR] & 0xff;\n                pixels[outputOffset + x] = 0xFF000000 | (grey * 0x00010101);\n            }\n            inputOffset += this.dataWidth * PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n        }\n        return pixels;\n    };\n    /**\n     * @return width of image from {@link #renderThumbnail()}\n     */\n    PlanarYUVLuminanceSource.prototype.getThumbnailWidth = function () {\n        return this.getWidth() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n    };\n    /**\n     * @return height of image from {@link #renderThumbnail()}\n     */\n    PlanarYUVLuminanceSource.prototype.getThumbnailHeight = function () {\n        return this.getHeight() / PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR;\n    };\n    PlanarYUVLuminanceSource.prototype.reverseHorizontal = function (width /*int*/, height /*int*/) {\n        var yuvData = this.yuvData;\n        for (var y = 0, rowStart = this.top * this.dataWidth + this.left; y < height; y++, rowStart += this.dataWidth) {\n            var middle = rowStart + width / 2;\n            for (var x1 = rowStart, x2 = rowStart + width - 1; x1 < middle; x1++, x2--) {\n                var temp = yuvData[x1];\n                yuvData[x1] = yuvData[x2];\n                yuvData[x2] = temp;\n            }\n        }\n    };\n    PlanarYUVLuminanceSource.prototype.invert = function () {\n        return new InvertedLuminanceSource(this);\n    };\n    PlanarYUVLuminanceSource.THUMBNAIL_SCALE_FACTOR = 2;\n    return PlanarYUVLuminanceSource;\n}(LuminanceSource));\nexport default PlanarYUVLuminanceSource;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,MAAM,MAAM,eAAe;AAClC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,wBAAwB,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC5DnB,SAAS,CAACkB,wBAAwB,EAAEC,MAAM,CAAC;EAC3C,SAASD,wBAAwBA,CAACE,OAAO,EAAEC,SAAS,CAAC,SAASC,UAAU,CAAC,SAASC,IAAI,CAAC,SAASC,GAAG,CAAC,SAASC,KAAK,CAAC,SAASC,MAAM,CAAC,SAASC,iBAAiB,EAAE;IAC3J,IAAIC,KAAK,GAAGT,MAAM,CAACU,IAAI,CAAC,IAAI,EAAEJ,KAAK,EAAEC,MAAM,CAAC,IAAI,IAAI;IACpDE,KAAK,CAACR,OAAO,GAAGA,OAAO;IACvBQ,KAAK,CAACP,SAAS,GAAGA,SAAS;IAC3BO,KAAK,CAACN,UAAU,GAAGA,UAAU;IAC7BM,KAAK,CAACL,IAAI,GAAGA,IAAI;IACjBK,KAAK,CAACJ,GAAG,GAAGA,GAAG;IACf,IAAID,IAAI,GAAGE,KAAK,GAAGJ,SAAS,IAAIG,GAAG,GAAGE,MAAM,GAAGJ,UAAU,EAAE;MACvD,MAAM,IAAIL,wBAAwB,CAAC,gDAAgD,CAAC;IACxF;IACA,IAAIU,iBAAiB,EAAE;MACnBC,KAAK,CAACD,iBAAiB,CAACF,KAAK,EAAEC,MAAM,CAAC;IAC1C;IACA,OAAOE,KAAK;EAChB;EACA;EACAV,wBAAwB,CAACN,SAAS,CAACkB,MAAM,GAAG,UAAUC,CAAC,CAAC,SAASC,GAAG,EAAE;IAClE,IAAID,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAI,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE;MAChC,MAAM,IAAIhB,wBAAwB,CAAC,sCAAsC,GAAGc,CAAC,CAAC;IAClF;IACA,IAAIN,KAAK,GAAG,IAAI,CAACS,QAAQ,CAAC,CAAC;IAC3B,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKG,SAAS,IAAIH,GAAG,CAACI,MAAM,GAAGX,KAAK,EAAE;MACzDO,GAAG,GAAG,IAAIK,iBAAiB,CAACZ,KAAK,CAAC;IACtC;IACA,IAAIa,MAAM,GAAG,CAACP,CAAC,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACH,SAAS,GAAG,IAAI,CAACE,IAAI;IACxDT,MAAM,CAACyB,SAAS,CAAC,IAAI,CAACnB,OAAO,EAAEkB,MAAM,EAAEN,GAAG,EAAE,CAAC,EAAEP,KAAK,CAAC;IACrD,OAAOO,GAAG;EACd,CAAC;EACD;EACAd,wBAAwB,CAACN,SAAS,CAAC4B,SAAS,GAAG,YAAY;IACvD,IAAIf,KAAK,GAAG,IAAI,CAACS,QAAQ,CAAC,CAAC;IAC3B,IAAIR,MAAM,GAAG,IAAI,CAACO,SAAS,CAAC,CAAC;IAC7B;IACA;IACA,IAAIR,KAAK,KAAK,IAAI,CAACJ,SAAS,IAAIK,MAAM,KAAK,IAAI,CAACJ,UAAU,EAAE;MACxD,OAAO,IAAI,CAACF,OAAO;IACvB;IACA,IAAIqB,IAAI,GAAGhB,KAAK,GAAGC,MAAM;IACzB,IAAIgB,MAAM,GAAG,IAAIL,iBAAiB,CAACI,IAAI,CAAC;IACxC,IAAIE,WAAW,GAAG,IAAI,CAACnB,GAAG,GAAG,IAAI,CAACH,SAAS,GAAG,IAAI,CAACE,IAAI;IACvD;IACA,IAAIE,KAAK,KAAK,IAAI,CAACJ,SAAS,EAAE;MAC1BP,MAAM,CAACyB,SAAS,CAAC,IAAI,CAACnB,OAAO,EAAEuB,WAAW,EAAED,MAAM,EAAE,CAAC,EAAED,IAAI,CAAC;MAC5D,OAAOC,MAAM;IACjB;IACA;IACA,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC7B,IAAIa,YAAY,GAAGb,CAAC,GAAGN,KAAK;MAC5BX,MAAM,CAACyB,SAAS,CAAC,IAAI,CAACnB,OAAO,EAAEuB,WAAW,EAAED,MAAM,EAAEE,YAAY,EAAEnB,KAAK,CAAC;MACxEkB,WAAW,IAAI,IAAI,CAACtB,SAAS;IACjC;IACA,OAAOqB,MAAM;EACjB,CAAC;EACD;EACAxB,wBAAwB,CAACN,SAAS,CAACiC,eAAe,GAAG,YAAY;IAC7D,OAAO,IAAI;EACf,CAAC;EACD;EACA3B,wBAAwB,CAACN,SAAS,CAACkC,IAAI,GAAG,UAAUvB,IAAI,CAAC,SAASC,GAAG,CAAC,SAASC,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IAC1G,OAAO,IAAIR,wBAAwB,CAAC,IAAI,CAACE,OAAO,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,IAAI,GAAGA,IAAI,EAAE,IAAI,CAACC,GAAG,GAAGA,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAE,KAAK,CAAC;EAC9I,CAAC;EACDR,wBAAwB,CAACN,SAAS,CAACmC,eAAe,GAAG,YAAY;IAC7D,IAAItB,KAAK,GAAG,IAAI,CAACS,QAAQ,CAAC,CAAC,GAAGhB,wBAAwB,CAAC8B,sBAAsB;IAC7E,IAAItB,MAAM,GAAG,IAAI,CAACO,SAAS,CAAC,CAAC,GAAGf,wBAAwB,CAAC8B,sBAAsB;IAC/E,IAAIC,MAAM,GAAG,IAAIC,UAAU,CAACzB,KAAK,GAAGC,MAAM,CAAC;IAC3C,IAAIyB,GAAG,GAAG,IAAI,CAAC/B,OAAO;IACtB,IAAIuB,WAAW,GAAG,IAAI,CAACnB,GAAG,GAAG,IAAI,CAACH,SAAS,GAAG,IAAI,CAACE,IAAI;IACvD,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC7B,IAAIa,YAAY,GAAGb,CAAC,GAAGN,KAAK;MAC5B,KAAK,IAAI2B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3B,KAAK,EAAE2B,CAAC,EAAE,EAAE;QAC5B,IAAIC,IAAI,GAAGF,GAAG,CAACR,WAAW,GAAGS,CAAC,GAAGlC,wBAAwB,CAAC8B,sBAAsB,CAAC,GAAG,IAAI;QACxFC,MAAM,CAACL,YAAY,GAAGQ,CAAC,CAAC,GAAG,UAAU,GAAIC,IAAI,GAAG,UAAW;MAC/D;MACAV,WAAW,IAAI,IAAI,CAACtB,SAAS,GAAGH,wBAAwB,CAAC8B,sBAAsB;IACnF;IACA,OAAOC,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACI/B,wBAAwB,CAACN,SAAS,CAAC0C,iBAAiB,GAAG,YAAY;IAC/D,OAAO,IAAI,CAACpB,QAAQ,CAAC,CAAC,GAAGhB,wBAAwB,CAAC8B,sBAAsB;EAC5E,CAAC;EACD;AACJ;AACA;EACI9B,wBAAwB,CAACN,SAAS,CAAC2C,kBAAkB,GAAG,YAAY;IAChE,OAAO,IAAI,CAACtB,SAAS,CAAC,CAAC,GAAGf,wBAAwB,CAAC8B,sBAAsB;EAC7E,CAAC;EACD9B,wBAAwB,CAACN,SAAS,CAACe,iBAAiB,GAAG,UAAUF,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IAC5F,IAAIN,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEyB,QAAQ,GAAG,IAAI,CAAChC,GAAG,GAAG,IAAI,CAACH,SAAS,GAAG,IAAI,CAACE,IAAI,EAAEQ,CAAC,GAAGL,MAAM,EAAEK,CAAC,EAAE,EAAEyB,QAAQ,IAAI,IAAI,CAACnC,SAAS,EAAE;MAC3G,IAAIoC,MAAM,GAAGD,QAAQ,GAAG/B,KAAK,GAAG,CAAC;MACjC,KAAK,IAAIiC,EAAE,GAAGF,QAAQ,EAAEG,EAAE,GAAGH,QAAQ,GAAG/B,KAAK,GAAG,CAAC,EAAEiC,EAAE,GAAGD,MAAM,EAAEC,EAAE,EAAE,EAAEC,EAAE,EAAE,EAAE;QACxE,IAAIC,IAAI,GAAGxC,OAAO,CAACsC,EAAE,CAAC;QACtBtC,OAAO,CAACsC,EAAE,CAAC,GAAGtC,OAAO,CAACuC,EAAE,CAAC;QACzBvC,OAAO,CAACuC,EAAE,CAAC,GAAGC,IAAI;MACtB;IACJ;EACJ,CAAC;EACD1C,wBAAwB,CAACN,SAAS,CAACiD,MAAM,GAAG,YAAY;IACpD,OAAO,IAAI7C,uBAAuB,CAAC,IAAI,CAAC;EAC5C,CAAC;EACDE,wBAAwB,CAAC8B,sBAAsB,GAAG,CAAC;EACnD,OAAO9B,wBAAwB;AACnC,CAAC,CAACH,eAAe,CAAE;AACnB,eAAeG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}