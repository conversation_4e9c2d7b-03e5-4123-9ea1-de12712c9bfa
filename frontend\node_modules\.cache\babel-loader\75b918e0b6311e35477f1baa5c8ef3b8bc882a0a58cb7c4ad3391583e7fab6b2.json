{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417;\n/**\n * <AUTHOR> Grau\n */\nvar PDF417ResultMetadata = /** @class */function () {\n  function PDF417ResultMetadata() {\n    this.segmentCount = -1;\n    this.fileSize = -1;\n    this.timestamp = -1;\n    this.checksum = -1;\n  }\n  /**\n   * The Segment ID represents the segment of the whole file distributed over different symbols.\n   *\n   * @return File segment index\n   */\n  PDF417ResultMetadata.prototype.getSegmentIndex = function () {\n    return this.segmentIndex;\n  };\n  PDF417ResultMetadata.prototype.setSegmentIndex = function (segmentIndex) {\n    this.segmentIndex = segmentIndex;\n  };\n  /**\n   * Is the same for each related PDF417 symbol\n   *\n   * @return File ID\n   */\n  PDF417ResultMetadata.prototype.getFileId = function () {\n    return this.fileId;\n  };\n  PDF417ResultMetadata.prototype.setFileId = function (fileId) {\n    this.fileId = fileId;\n  };\n  /**\n   * @return always null\n   * @deprecated use dedicated already parsed fields\n   */\n  //   @Deprecated\n  PDF417ResultMetadata.prototype.getOptionalData = function () {\n    return this.optionalData;\n  };\n  /**\n   * @param optionalData old optional data format as int array\n   * @deprecated parse and use new fields\n   */\n  //   @Deprecated\n  PDF417ResultMetadata.prototype.setOptionalData = function (optionalData) {\n    this.optionalData = optionalData;\n  };\n  /**\n   * @return true if it is the last segment\n   */\n  PDF417ResultMetadata.prototype.isLastSegment = function () {\n    return this.lastSegment;\n  };\n  PDF417ResultMetadata.prototype.setLastSegment = function (lastSegment) {\n    this.lastSegment = lastSegment;\n  };\n  /**\n   * @return count of segments, -1 if not set\n   */\n  PDF417ResultMetadata.prototype.getSegmentCount = function () {\n    return this.segmentCount;\n  };\n  PDF417ResultMetadata.prototype.setSegmentCount = function (segmentCount /*int*/) {\n    this.segmentCount = segmentCount;\n  };\n  PDF417ResultMetadata.prototype.getSender = function () {\n    return this.sender || null;\n  };\n  PDF417ResultMetadata.prototype.setSender = function (sender) {\n    this.sender = sender;\n  };\n  PDF417ResultMetadata.prototype.getAddressee = function () {\n    return this.addressee || null;\n  };\n  PDF417ResultMetadata.prototype.setAddressee = function (addressee) {\n    this.addressee = addressee;\n  };\n  /**\n   * Filename of the encoded file\n   *\n   * @return filename\n   */\n  PDF417ResultMetadata.prototype.getFileName = function () {\n    return this.fileName;\n  };\n  PDF417ResultMetadata.prototype.setFileName = function (fileName) {\n    this.fileName = fileName;\n  };\n  /**\n   * filesize in bytes of the encoded file\n   *\n   * @return filesize in bytes, -1 if not set\n   */\n  PDF417ResultMetadata.prototype.getFileSize = function () {\n    return this.fileSize;\n  };\n  PDF417ResultMetadata.prototype.setFileSize = function (fileSize /*long*/) {\n    this.fileSize = fileSize;\n  };\n  /**\n   * 16-bit CRC checksum using CCITT-16\n   *\n   * @return crc checksum, -1 if not set\n   */\n  PDF417ResultMetadata.prototype.getChecksum = function () {\n    return this.checksum;\n  };\n  PDF417ResultMetadata.prototype.setChecksum = function (checksum /*int*/) {\n    this.checksum = checksum;\n  };\n  /**\n   * unix epock timestamp, elapsed seconds since 1970-01-01\n   *\n   * @return elapsed seconds, -1 if not set\n   */\n  PDF417ResultMetadata.prototype.getTimestamp = function () {\n    return this.timestamp;\n  };\n  PDF417ResultMetadata.prototype.setTimestamp = function (timestamp /*long*/) {\n    this.timestamp = timestamp;\n  };\n  return PDF417ResultMetadata;\n}();\nexport default PDF417ResultMetadata;", "map": {"version": 3, "names": ["PDF417ResultMetadata", "segmentCount", "fileSize", "timestamp", "checksum", "prototype", "getSegmentIndex", "segmentIndex", "setSegmentIndex", "getFileId", "fileId", "setFileId", "getOptionalData", "optionalData", "setOptionalData", "isLastSegment", "lastSegment", "setLastSegment", "getSegmentCount", "setSegmentCount", "getSender", "sender", "setSender", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addressee", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getFileName", "fileName", "setFileName", "getFileSize", "setFileSize", "get<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getTimestamp", "setTimestamp"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/PDF417ResultMetadata.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417;\n/**\n * <AUTHOR> Grau\n */\nvar PDF417ResultMetadata = /** @class */ (function () {\n    function PDF417ResultMetadata() {\n        this.segmentCount = -1;\n        this.fileSize = -1;\n        this.timestamp = -1;\n        this.checksum = -1;\n    }\n    /**\n     * The Segment ID represents the segment of the whole file distributed over different symbols.\n     *\n     * @return File segment index\n     */\n    PDF417ResultMetadata.prototype.getSegmentIndex = function () {\n        return this.segmentIndex;\n    };\n    PDF417ResultMetadata.prototype.setSegmentIndex = function (segmentIndex) {\n        this.segmentIndex = segmentIndex;\n    };\n    /**\n     * Is the same for each related PDF417 symbol\n     *\n     * @return File ID\n     */\n    PDF417ResultMetadata.prototype.getFileId = function () {\n        return this.fileId;\n    };\n    PDF417ResultMetadata.prototype.setFileId = function (fileId) {\n        this.fileId = fileId;\n    };\n    /**\n     * @return always null\n     * @deprecated use dedicated already parsed fields\n     */\n    //   @Deprecated\n    PDF417ResultMetadata.prototype.getOptionalData = function () {\n        return this.optionalData;\n    };\n    /**\n     * @param optionalData old optional data format as int array\n     * @deprecated parse and use new fields\n     */\n    //   @Deprecated\n    PDF417ResultMetadata.prototype.setOptionalData = function (optionalData) {\n        this.optionalData = optionalData;\n    };\n    /**\n     * @return true if it is the last segment\n     */\n    PDF417ResultMetadata.prototype.isLastSegment = function () {\n        return this.lastSegment;\n    };\n    PDF417ResultMetadata.prototype.setLastSegment = function (lastSegment) {\n        this.lastSegment = lastSegment;\n    };\n    /**\n     * @return count of segments, -1 if not set\n     */\n    PDF417ResultMetadata.prototype.getSegmentCount = function () {\n        return this.segmentCount;\n    };\n    PDF417ResultMetadata.prototype.setSegmentCount = function (segmentCount /*int*/) {\n        this.segmentCount = segmentCount;\n    };\n    PDF417ResultMetadata.prototype.getSender = function () {\n        return this.sender || null;\n    };\n    PDF417ResultMetadata.prototype.setSender = function (sender) {\n        this.sender = sender;\n    };\n    PDF417ResultMetadata.prototype.getAddressee = function () {\n        return this.addressee || null;\n    };\n    PDF417ResultMetadata.prototype.setAddressee = function (addressee) {\n        this.addressee = addressee;\n    };\n    /**\n     * Filename of the encoded file\n     *\n     * @return filename\n     */\n    PDF417ResultMetadata.prototype.getFileName = function () {\n        return this.fileName;\n    };\n    PDF417ResultMetadata.prototype.setFileName = function (fileName) {\n        this.fileName = fileName;\n    };\n    /**\n     * filesize in bytes of the encoded file\n     *\n     * @return filesize in bytes, -1 if not set\n     */\n    PDF417ResultMetadata.prototype.getFileSize = function () {\n        return this.fileSize;\n    };\n    PDF417ResultMetadata.prototype.setFileSize = function (fileSize /*long*/) {\n        this.fileSize = fileSize;\n    };\n    /**\n     * 16-bit CRC checksum using CCITT-16\n     *\n     * @return crc checksum, -1 if not set\n     */\n    PDF417ResultMetadata.prototype.getChecksum = function () {\n        return this.checksum;\n    };\n    PDF417ResultMetadata.prototype.setChecksum = function (checksum /*int*/) {\n        this.checksum = checksum;\n    };\n    /**\n     * unix epock timestamp, elapsed seconds since 1970-01-01\n     *\n     * @return elapsed seconds, -1 if not set\n     */\n    PDF417ResultMetadata.prototype.getTimestamp = function () {\n        return this.timestamp;\n    };\n    PDF417ResultMetadata.prototype.setTimestamp = function (timestamp /*long*/) {\n        this.timestamp = timestamp;\n    };\n    return PDF417ResultMetadata;\n}());\nexport default PDF417ResultMetadata;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,oBAAoB,GAAG,aAAe,YAAY;EAClD,SAASA,oBAAoBA,CAAA,EAAG;IAC5B,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;EACtB;EACA;AACJ;AACA;AACA;AACA;EACIJ,oBAAoB,CAACK,SAAS,CAACC,eAAe,GAAG,YAAY;IACzD,OAAO,IAAI,CAACC,YAAY;EAC5B,CAAC;EACDP,oBAAoB,CAACK,SAAS,CAACG,eAAe,GAAG,UAAUD,YAAY,EAAE;IACrE,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIP,oBAAoB,CAACK,SAAS,CAACI,SAAS,GAAG,YAAY;IACnD,OAAO,IAAI,CAACC,MAAM;EACtB,CAAC;EACDV,oBAAoB,CAACK,SAAS,CAACM,SAAS,GAAG,UAAUD,MAAM,EAAE;IACzD,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB,CAAC;EACD;AACJ;AACA;AACA;EACI;EACAV,oBAAoB,CAACK,SAAS,CAACO,eAAe,GAAG,YAAY;IACzD,OAAO,IAAI,CAACC,YAAY;EAC5B,CAAC;EACD;AACJ;AACA;AACA;EACI;EACAb,oBAAoB,CAACK,SAAS,CAACS,eAAe,GAAG,UAAUD,YAAY,EAAE;IACrE,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC,CAAC;EACD;AACJ;AACA;EACIb,oBAAoB,CAACK,SAAS,CAACU,aAAa,GAAG,YAAY;IACvD,OAAO,IAAI,CAACC,WAAW;EAC3B,CAAC;EACDhB,oBAAoB,CAACK,SAAS,CAACY,cAAc,GAAG,UAAUD,WAAW,EAAE;IACnE,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC,CAAC;EACD;AACJ;AACA;EACIhB,oBAAoB,CAACK,SAAS,CAACa,eAAe,GAAG,YAAY;IACzD,OAAO,IAAI,CAACjB,YAAY;EAC5B,CAAC;EACDD,oBAAoB,CAACK,SAAS,CAACc,eAAe,GAAG,UAAUlB,YAAY,CAAC,SAAS;IAC7E,IAAI,CAACA,YAAY,GAAGA,YAAY;EACpC,CAAC;EACDD,oBAAoB,CAACK,SAAS,CAACe,SAAS,GAAG,YAAY;IACnD,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI;EAC9B,CAAC;EACDrB,oBAAoB,CAACK,SAAS,CAACiB,SAAS,GAAG,UAAUD,MAAM,EAAE;IACzD,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB,CAAC;EACDrB,oBAAoB,CAACK,SAAS,CAACkB,YAAY,GAAG,YAAY;IACtD,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI;EACjC,CAAC;EACDxB,oBAAoB,CAACK,SAAS,CAACoB,YAAY,GAAG,UAAUD,SAAS,EAAE;IAC/D,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIxB,oBAAoB,CAACK,SAAS,CAACqB,WAAW,GAAG,YAAY;IACrD,OAAO,IAAI,CAACC,QAAQ;EACxB,CAAC;EACD3B,oBAAoB,CAACK,SAAS,CAACuB,WAAW,GAAG,UAAUD,QAAQ,EAAE;IAC7D,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI3B,oBAAoB,CAACK,SAAS,CAACwB,WAAW,GAAG,YAAY;IACrD,OAAO,IAAI,CAAC3B,QAAQ;EACxB,CAAC;EACDF,oBAAoB,CAACK,SAAS,CAACyB,WAAW,GAAG,UAAU5B,QAAQ,CAAC,UAAU;IACtE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIF,oBAAoB,CAACK,SAAS,CAAC0B,WAAW,GAAG,YAAY;IACrD,OAAO,IAAI,CAAC3B,QAAQ;EACxB,CAAC;EACDJ,oBAAoB,CAACK,SAAS,CAAC2B,WAAW,GAAG,UAAU5B,QAAQ,CAAC,SAAS;IACrE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIJ,oBAAoB,CAACK,SAAS,CAAC4B,YAAY,GAAG,YAAY;IACtD,OAAO,IAAI,CAAC9B,SAAS;EACzB,CAAC;EACDH,oBAAoB,CAACK,SAAS,CAAC6B,YAAY,GAAG,UAAU/B,SAAS,CAAC,UAAU;IACxE,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B,CAAC;EACD,OAAOH,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,eAAeA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}