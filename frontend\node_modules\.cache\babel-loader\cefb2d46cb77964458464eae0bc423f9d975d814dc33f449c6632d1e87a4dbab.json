{"ast": null, "code": "/**\n * <p>Encapsulates the parameters for one error-correction block in one symbol version.\n * This includes the number of data codewords, and the number of times a block with these\n * parameters is used consecutively in the QR code version's format.</p>\n */\nvar ECB = /** @class */function () {\n  function ECB(count /*int*/, dataCodewords /*int*/) {\n    this.count = count;\n    this.dataCodewords = dataCodewords;\n  }\n  ECB.prototype.getCount = function () {\n    return this.count;\n  };\n  ECB.prototype.getDataCodewords = function () {\n    return this.dataCodewords;\n  };\n  return ECB;\n}();\nexport default ECB;", "map": {"version": 3, "names": ["ECB", "count", "dataCodewords", "prototype", "getCount", "getDataCodewords"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/ECB.js"], "sourcesContent": ["/**\n * <p>Encapsulates the parameters for one error-correction block in one symbol version.\n * This includes the number of data codewords, and the number of times a block with these\n * parameters is used consecutively in the QR code version's format.</p>\n */\nvar ECB = /** @class */ (function () {\n    function ECB(count /*int*/, dataCodewords /*int*/) {\n        this.count = count;\n        this.dataCodewords = dataCodewords;\n    }\n    ECB.prototype.getCount = function () {\n        return this.count;\n    };\n    ECB.prototype.getDataCodewords = function () {\n        return this.dataCodewords;\n    };\n    return ECB;\n}());\nexport default ECB;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,IAAIA,GAAG,GAAG,aAAe,YAAY;EACjC,SAASA,GAAGA,CAACC,KAAK,CAAC,SAASC,aAAa,CAAC,SAAS;IAC/C,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACAF,GAAG,CAACG,SAAS,CAACC,QAAQ,GAAG,YAAY;IACjC,OAAO,IAAI,CAACH,KAAK;EACrB,CAAC;EACDD,GAAG,CAACG,SAAS,CAACE,gBAAgB,GAAG,YAAY;IACzC,OAAO,IAAI,CAACH,aAAa;EAC7B,CAAC;EACD,OAAOF,GAAG;AACd,CAAC,CAAC,CAAE;AACJ,eAAeA,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}