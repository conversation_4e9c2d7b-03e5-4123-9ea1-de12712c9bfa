{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport { C40Encoder } from './C40Encoder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { X12_ENCODATION, ASCII_ENCODATION, X12_UNLATCH } from './constants';\nvar X12Encoder = /** @class */function (_super) {\n  __extends(X12Encoder, _super);\n  function X12Encoder() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  X12Encoder.prototype.getEncodingMode = function () {\n    return X12_ENCODATION;\n  };\n  X12Encoder.prototype.encode = function (context) {\n    // step C\n    var buffer = new StringBuilder();\n    while (context.hasMoreCharacters()) {\n      var c = context.getCurrentChar();\n      context.pos++;\n      this.encodeChar(c, buffer);\n      var count = buffer.length();\n      if (count % 3 === 0) {\n        this.writeNextTriplet(context, buffer);\n        var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n        if (newMode !== this.getEncodingMode()) {\n          // Return to ASCII encodation, which will actually handle latch to new mode\n          context.signalEncoderChange(ASCII_ENCODATION);\n          break;\n        }\n      }\n    }\n    this.handleEOD(context, buffer);\n  };\n  X12Encoder.prototype.encodeChar = function (c, sb) {\n    switch (c) {\n      case 13:\n        // CR (Carriage return)\n        sb.append(0);\n        break;\n      case '*'.charCodeAt(0):\n        sb.append(1);\n        break;\n      case '>'.charCodeAt(0):\n        sb.append(2);\n        break;\n      case ' '.charCodeAt(0):\n        sb.append(3);\n        break;\n      default:\n        if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n          sb.append(c - 48 + 4);\n        } else if (c >= 'A'.charCodeAt(0) && c <= 'Z'.charCodeAt(0)) {\n          sb.append(c - 65 + 14);\n        } else {\n          HighLevelEncoder.illegalCharacter(StringUtils.getCharAt(c));\n        }\n        break;\n    }\n    return 1;\n  };\n  X12Encoder.prototype.handleEOD = function (context, buffer) {\n    context.updateSymbolInfo();\n    var available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();\n    var count = buffer.length();\n    context.pos -= count;\n    if (context.getRemainingCharacters() > 1 || available > 1 || context.getRemainingCharacters() !== available) {\n      context.writeCodeword(X12_UNLATCH);\n    }\n    if (context.getNewEncoding() < 0) {\n      context.signalEncoderChange(ASCII_ENCODATION);\n    }\n  };\n  return X12Encoder;\n}(C40Encoder);\nexport { X12Encoder };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "StringUtils", "StringBuilder", "C40Encoder", "HighLevelEncoder", "X12_ENCODATION", "ASCII_ENCODATION", "X12_UNLATCH", "X12Encoder", "_super", "apply", "arguments", "getEncodingMode", "encode", "context", "buffer", "hasMoreCharacters", "c", "getCurrentChar", "pos", "encodeChar", "count", "length", "writeNextTriplet", "newMode", "lookAheadTest", "getMessage", "signalEncoderChange", "handleEOD", "sb", "append", "charCodeAt", "illegalCharacter", "getCharAt", "updateSymbolInfo", "available", "getSymbolInfo", "getDataCapacity", "getCodewordCount", "getRemainingCharacters", "writeCodeword", "getNewEncoding"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/X12Encoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport { C40Encoder } from './C40Encoder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { X12_ENCODATION, ASCII_ENCODATION, X12_UNLATCH } from './constants';\nvar X12Encoder = /** @class */ (function (_super) {\n    __extends(X12Encoder, _super);\n    function X12Encoder() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    X12Encoder.prototype.getEncodingMode = function () {\n        return X12_ENCODATION;\n    };\n    X12Encoder.prototype.encode = function (context) {\n        // step C\n        var buffer = new StringBuilder();\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            context.pos++;\n            this.encodeChar(c, buffer);\n            var count = buffer.length();\n            if (count % 3 === 0) {\n                this.writeNextTriplet(context, buffer);\n                var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n                if (newMode !== this.getEncodingMode()) {\n                    // Return to ASCII encodation, which will actually handle latch to new mode\n                    context.signalEncoderChange(ASCII_ENCODATION);\n                    break;\n                }\n            }\n        }\n        this.handleEOD(context, buffer);\n    };\n    X12Encoder.prototype.encodeChar = function (c, sb) {\n        switch (c) {\n            case 13: // CR (Carriage return)\n                sb.append(0);\n                break;\n            case '*'.charCodeAt(0):\n                sb.append(1);\n                break;\n            case '>'.charCodeAt(0):\n                sb.append(2);\n                break;\n            case ' '.charCodeAt(0):\n                sb.append(3);\n                break;\n            default:\n                if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n                    sb.append(c - 48 + 4);\n                }\n                else if (c >= 'A'.charCodeAt(0) && c <= 'Z'.charCodeAt(0)) {\n                    sb.append(c - 65 + 14);\n                }\n                else {\n                    HighLevelEncoder.illegalCharacter(StringUtils.getCharAt(c));\n                }\n                break;\n        }\n        return 1;\n    };\n    X12Encoder.prototype.handleEOD = function (context, buffer) {\n        context.updateSymbolInfo();\n        var available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();\n        var count = buffer.length();\n        context.pos -= count;\n        if (context.getRemainingCharacters() > 1 ||\n            available > 1 ||\n            context.getRemainingCharacters() !== available) {\n            context.writeCodeword(X12_UNLATCH);\n        }\n        if (context.getNewEncoding() < 0) {\n            context.signalEncoderChange(ASCII_ENCODATION);\n        }\n    };\n    return X12Encoder;\n}(C40Encoder));\nexport { X12Encoder };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,aAAa;AAC3E,IAAIC,UAAU,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC9CtB,SAAS,CAACqB,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IAClB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACAH,UAAU,CAACT,SAAS,CAACa,eAAe,GAAG,YAAY;IAC/C,OAAOP,cAAc;EACzB,CAAC;EACDG,UAAU,CAACT,SAAS,CAACc,MAAM,GAAG,UAAUC,OAAO,EAAE;IAC7C;IACA,IAAIC,MAAM,GAAG,IAAIb,aAAa,CAAC,CAAC;IAChC,OAAOY,OAAO,CAACE,iBAAiB,CAAC,CAAC,EAAE;MAChC,IAAIC,CAAC,GAAGH,OAAO,CAACI,cAAc,CAAC,CAAC;MAChCJ,OAAO,CAACK,GAAG,EAAE;MACb,IAAI,CAACC,UAAU,CAACH,CAAC,EAAEF,MAAM,CAAC;MAC1B,IAAIM,KAAK,GAAGN,MAAM,CAACO,MAAM,CAAC,CAAC;MAC3B,IAAID,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;QACjB,IAAI,CAACE,gBAAgB,CAACT,OAAO,EAAEC,MAAM,CAAC;QACtC,IAAIS,OAAO,GAAGpB,gBAAgB,CAACqB,aAAa,CAACX,OAAO,CAACY,UAAU,CAAC,CAAC,EAAEZ,OAAO,CAACK,GAAG,EAAE,IAAI,CAACP,eAAe,CAAC,CAAC,CAAC;QACvG,IAAIY,OAAO,KAAK,IAAI,CAACZ,eAAe,CAAC,CAAC,EAAE;UACpC;UACAE,OAAO,CAACa,mBAAmB,CAACrB,gBAAgB,CAAC;UAC7C;QACJ;MACJ;IACJ;IACA,IAAI,CAACsB,SAAS,CAACd,OAAO,EAAEC,MAAM,CAAC;EACnC,CAAC;EACDP,UAAU,CAACT,SAAS,CAACqB,UAAU,GAAG,UAAUH,CAAC,EAAEY,EAAE,EAAE;IAC/C,QAAQZ,CAAC;MACL,KAAK,EAAE;QAAE;QACLY,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC;QACZ;MACJ,KAAK,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;QAClBF,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC;QACZ;MACJ,KAAK,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;QAClBF,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC;QACZ;MACJ,KAAK,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC;QAClBF,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC;QACZ;MACJ;QACI,IAAIb,CAAC,IAAI,GAAG,CAACc,UAAU,CAAC,CAAC,CAAC,IAAId,CAAC,IAAI,GAAG,CAACc,UAAU,CAAC,CAAC,CAAC,EAAE;UAClDF,EAAE,CAACC,MAAM,CAACb,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACzB,CAAC,MACI,IAAIA,CAAC,IAAI,GAAG,CAACc,UAAU,CAAC,CAAC,CAAC,IAAId,CAAC,IAAI,GAAG,CAACc,UAAU,CAAC,CAAC,CAAC,EAAE;UACvDF,EAAE,CAACC,MAAM,CAACb,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;QAC1B,CAAC,MACI;UACDb,gBAAgB,CAAC4B,gBAAgB,CAAC/B,WAAW,CAACgC,SAAS,CAAChB,CAAC,CAAC,CAAC;QAC/D;QACA;IACR;IACA,OAAO,CAAC;EACZ,CAAC;EACDT,UAAU,CAACT,SAAS,CAAC6B,SAAS,GAAG,UAAUd,OAAO,EAAEC,MAAM,EAAE;IACxDD,OAAO,CAACoB,gBAAgB,CAAC,CAAC;IAC1B,IAAIC,SAAS,GAAGrB,OAAO,CAACsB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,GAAGvB,OAAO,CAACwB,gBAAgB,CAAC,CAAC;IACtF,IAAIjB,KAAK,GAAGN,MAAM,CAACO,MAAM,CAAC,CAAC;IAC3BR,OAAO,CAACK,GAAG,IAAIE,KAAK;IACpB,IAAIP,OAAO,CAACyB,sBAAsB,CAAC,CAAC,GAAG,CAAC,IACpCJ,SAAS,GAAG,CAAC,IACbrB,OAAO,CAACyB,sBAAsB,CAAC,CAAC,KAAKJ,SAAS,EAAE;MAChDrB,OAAO,CAAC0B,aAAa,CAACjC,WAAW,CAAC;IACtC;IACA,IAAIO,OAAO,CAAC2B,cAAc,CAAC,CAAC,GAAG,CAAC,EAAE;MAC9B3B,OAAO,CAACa,mBAAmB,CAACrB,gBAAgB,CAAC;IACjD;EACJ,CAAC;EACD,OAAOE,UAAU;AACrB,CAAC,CAACL,UAAU,CAAE;AACd,SAASK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}