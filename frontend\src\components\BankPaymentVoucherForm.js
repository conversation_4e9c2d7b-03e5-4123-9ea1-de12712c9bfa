import React, { useState, useEffect } from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Divider,
  Chip,
  Autocomplete,
  FormHelperText
} from "@mui/material";
import {
  Save as SaveIcon,
  Check as ApproveIcon,
  PostAdd as PostIcon,
  Cancel as CancelIcon
} from "@mui/icons-material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";
import axios from "../utils/axiosConfig";
import { formatCurrency, formatAccountingNumber, parseAccountingNumber, fixDecimalPlaces } from "../utils/numberUtils";

const BankPaymentVoucherForm = ({ voucherId, readOnly = false, onSave, onCancel }) => {
  const [voucher, setVoucher] = useState({
    voucherType: 'BP', // Bank Payment
    voucherDate: dayjs(), // Voucher creation date
    transactionDate: dayjs(), // Actual transaction date
    amount: '',
    fromAccountId: '', // Bank account (credit)
    toAccountId: '', // Debit account (vendor, expense, etc.)
    relatedPartyType: '',
    relatedPartyId: '',
    relatedPartyName: '',
    paymentMethod: 'Bank Transfer',
    chequeNumber: '',
    chequeDate: null,
    bankReference: '',
    narration: '',
    description: '',
    status: 'Draft',
    referenceDocuments: []
  });

  // State for formatted amount display
  const [displayAmount, setDisplayAmount] = useState('');

  // Flag to prevent amount override when loading existing voucher
  const [isLoadingExistingVoucher, setIsLoadingExistingVoucher] = useState(false);

  const [accounts, setAccounts] = useState([]);
  const [bankAccounts, setBankAccounts] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [purchaseInvoices, setPurchaseInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [selectedInvoice, setSelectedInvoice] = useState(null);

  const paymentMethods = [
    'Bank Transfer',
    'Cheque',
    'Online Transfer',
    'Card',
    'Other'
  ];

  const statusOptions = [
    'Draft',
    'Approved',
    'Posted',
    'Cancelled'
  ];

  useEffect(() => {
    fetchInitialData();
    if (voucherId) {
      fetchVoucher();
    }
  }, [voucherId]);

  // Fetch vendor's purchase invoices when vendor is selected
  useEffect(() => {
    if (selectedVendor) {
      fetchVendorInvoices(selectedVendor._id);
    } else {
      setPurchaseInvoices([]);
      setSelectedInvoice(null);
    }
  }, [selectedVendor]);

  // Update amount when invoice is selected (but not when loading existing voucher)
  useEffect(() => {
    console.log('🔍 DEBUG: selectedInvoice useEffect triggered', {
      selectedInvoice: selectedInvoice ? selectedInvoice.invoiceNumber : 'null',
      isLoadingExistingVoucher,
      remainingAmount: selectedInvoice?.remainingAmount
    });

    if (selectedInvoice && !isLoadingExistingVoucher) {
      console.log('🔍 DEBUG: Updating amount from selected invoice');
      const remainingAmount = selectedInvoice.remainingAmount || 0;

      // Create a detailed narration
      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber || 'Unknown'}`;

      // If there are returns, add details
      if (selectedInvoice.hasReturns && selectedInvoice.returnDetails) {
        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');
        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount || 0)}, Returns: ${formatCurrency(selectedInvoice.returnAmount || 0)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount || 0)})`;
      }

      const fixedAmount = fixDecimalPlaces(remainingAmount);
      console.log('🔍 DEBUG: Setting amount from invoice:', fixedAmount);
      setVoucher(prev => ({
        ...prev,
        amount: fixedAmount,
        narration
      }));
      setDisplayAmount(formatAccountingNumber(fixedAmount));
    } else {
      console.log('🔍 DEBUG: Skipping amount update - either no invoice or loading existing voucher');
    }
  }, [selectedInvoice, isLoadingExistingVoucher]);

  // Add error handling for ResizeObserver
  useEffect(() => {
    // Suppress ResizeObserver loop error
    const originalError = window.console.error;
    window.console.error = (...args) => {
      if (args[0]?.includes?.('ResizeObserver loop')) {
        // Ignore ResizeObserver loop errors
        return;
      }
      originalError(...args);
    };

    return () => {
      window.console.error = originalError;
    };
  }, []);

  const fetchInitialData = async () => {
    try {
      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([
        axios.get("/api/accounts?active=true"),
        axios.get("/api/vendors"),
        axios.get("/api/bank-accounts")
      ]);

      setAccounts(accountsRes.data);
      setVendors(vendorsRes.data);
      
      // Map bank accounts to their corresponding chart of accounts entries
      const bankAccs = bankAccountsRes.data;
      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);
      
      // Get bank account details from chart of accounts
      if (bankAccountIds.length > 0) {
        const bankAccountsDetails = accountsRes.data.filter(acc => 
          bankAccountIds.includes(acc._id)
        );
        setBankAccounts(bankAccountsDetails);
      }
    } catch (error) {
      console.error("Error fetching initial data:", error);
    }
  };

  const fetchVoucher = async () => {
    try {
      setLoading(true);
      setIsLoadingExistingVoucher(true); // Prevent amount override

      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);
      const voucherData = response.data;

      console.log('🔍 DEBUG: Fetched voucher data:', voucherData);
      console.log('🔍 DEBUG: Original amount from API:', voucherData.amount);

      const fixedAmount = fixDecimalPlaces(voucherData.amount);
      console.log('🔍 DEBUG: Fixed amount:', fixedAmount);

      setVoucher({
        ...voucherData,
        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt || new Date()),
        transactionDate: dayjs(voucherData.transactionDate || new Date()),
        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,
        fromAccountId: voucherData.fromAccountId?._id || voucherData.fromAccountId,
        toAccountId: voucherData.toAccountId?._id || voucherData.toAccountId,
        amount: fixedAmount
      });

      const formattedAmount = formatAccountingNumber(fixedAmount);
      console.log('🔍 DEBUG: Formatted amount for display:', formattedAmount);
      console.log('🔍 DEBUG: Type of fixedAmount:', typeof fixedAmount, 'Value:', fixedAmount);

      // Test the formatting function directly
      console.log('🔍 DEBUG: Testing formatAccountingNumber with 110245.90:', formatAccountingNumber(110245.90));
      console.log('🔍 DEBUG: Testing formatAccountingNumber with "110245.90":', formatAccountingNumber("110245.90"));

      setDisplayAmount(formattedAmount);

      // If this is a vendor payment, fetch the vendor and invoice details
      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {
        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);
        setSelectedVendor(vendorRes.data);

        // If there's a reference to a purchase invoice
        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {
          const invoiceRef = voucherData.referenceDocuments.find(doc =>
            doc.documentType === 'PurchaseInvoice'
          );

          if (invoiceRef && invoiceRef.documentId) {
            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);
            setSelectedInvoice(invoiceRes.data);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching voucher:", error);
    } finally {
      setLoading(false);
      setIsLoadingExistingVoucher(false); // Reset flag after loading is complete
    }
  };

  const fetchVendorInvoices = async (vendorId) => {
    try {
      console.log(`Fetching invoices for vendor: ${vendorId}`);
      // Fetch unpaid or partially paid invoices for this vendor
      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);
      console.log('Vendor invoices response:', response.data);
      
      if (Array.isArray(response.data) && response.data.length > 0) {
        setPurchaseInvoices(response.data);
      } else {
        console.log('No unpaid invoices found for this vendor');
        setPurchaseInvoices([]);
      }
    } catch (error) {
      console.error("Error fetching vendor invoices:", error);
      alert(`Failed to fetch vendor invoices: ${error.response?.data?.message || error.message}`);
      setPurchaseInvoices([]);
    }
  };

  const handleInputChange = (field, value) => {
    if (field === 'amount') {
      // Handle amount field with accounting format
      console.log('🔍 DEBUG: Amount input change - value:', value);
      const parsedAmount = parseAccountingNumber(value);
      console.log('🔍 DEBUG: Amount input change - parsed:', parsedAmount);
      setVoucher(prev => ({ ...prev, [field]: parsedAmount }));
      setDisplayAmount(value); // Keep the user's input for display
    } else {
      setVoucher(prev => ({ ...prev, [field]: value }));
    }

    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  // Handle amount field blur to format the display
  const handleAmountBlur = () => {
    console.log('🔍 DEBUG: Amount blur - voucher.amount:', voucher.amount);
    if (voucher.amount) {
      const formattedAmount = formatAccountingNumber(voucher.amount);
      console.log('🔍 DEBUG: Amount blur - formatted:', formattedAmount);
      setDisplayAmount(formattedAmount);
    }
  };

  // Handle amount field focus to show raw number
  const handleAmountFocus = () => {
    console.log('🔍 DEBUG: Amount focus - voucher.amount:', voucher.amount);
    setDisplayAmount(voucher.amount);
  };

  const handleVendorChange = (vendor) => {
    setSelectedVendor(vendor);
    setSelectedInvoice(null); // Clear selected invoice when vendor changes

    if (vendor) {
      // Update voucher with vendor details
      setVoucher(prev => ({
        ...prev,
        relatedPartyType: 'Vendor',
        relatedPartyId: vendor._id,
        relatedPartyName: vendor.name,
        toAccountId: vendor.accountId || '', // Set vendor's account as debit account
        amount: '', // Clear amount when vendor changes
        narration: '', // Clear narration when vendor changes
        referenceDocuments: [] // Clear reference documents
      }));
    } else {
      // Clear vendor-related fields
      setVoucher(prev => ({
        ...prev,
        relatedPartyType: '',
        relatedPartyId: '',
        relatedPartyName: '',
        toAccountId: '',
        amount: '',
        narration: '',
        referenceDocuments: []
      }));
    }
  };

  const handleInvoiceChange = (invoice) => {
    setSelectedInvoice(invoice);
    
    if (invoice) {
      console.log('Selected invoice:', invoice);
      
      // Use the adjusted remaining amount that accounts for returns
      const remainingAmount = invoice.remainingAmount;
      
      console.log(`Using adjusted remaining amount: ${remainingAmount}`);
      
      // Create a detailed narration
      let narration = `Payment against invoice ${invoice.invoiceNumber}`;
      
      // If there are returns, add details
      if (invoice.hasReturns) {
        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');
        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;
      }
      
      // Update amount field with remaining amount (fixed to 2 decimals)
      const fixedAmount = fixDecimalPlaces(remainingAmount);
      setVoucher(prev => ({
        ...prev,
        amount: fixedAmount,
        narration
      }));
      setDisplayAmount(formatAccountingNumber(fixedAmount));
      
      // Update reference documents
      const referenceDoc = {
        documentType: 'PurchaseInvoice',
        documentId: invoice._id,
        documentNumber: invoice.invoiceNumber,
        allocatedAmount: remainingAmount,
        originalAmount: invoice.originalAmount,
        returnAmount: invoice.returnAmount,
        adjustedAmount: invoice.adjustedAmount
      };
      
      setVoucher(prev => ({
        ...prev,
        referenceDocuments: [referenceDoc]
      }));
    } else {
      setVoucher(prev => ({
        ...prev,
        referenceDocuments: []
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!voucher.fromAccountId) newErrors.fromAccountId = "Bank is required";
    if (!voucher.toAccountId) newErrors.toAccountId = "Title (debit account) is required";
    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {
      newErrors.amount = "Valid amount is required";
    }
    if (!voucher.voucherDate) newErrors.voucherDate = "Voucher date is required";
    if (!voucher.transactionDate) newErrors.transactionDate = "Transaction date is required";
    if (!voucher.narration) newErrors.narration = "Narration is required";

    // Vendor-invoice linkage validation
    if (selectedVendor && selectedInvoice) {
      // Ensure the selected invoice belongs to the selected vendor
      if (selectedInvoice.vendorId !== selectedVendor._id) {
        newErrors.invoice = "Selected invoice does not belong to the selected vendor";
      }

      // Ensure payment amount doesn't exceed remaining amount
      const remainingAmount = selectedInvoice.remainingAmount || 0;
      const paymentAmount = parseFloat(voucher.amount) || 0;
      if (paymentAmount > remainingAmount) {
        newErrors.amount = `Payment amount cannot exceed remaining amount of ${remainingAmount.toFixed(2)}`;
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;
    
    try {
      setLoading(true);
      
      const voucherData = {
        ...voucher,
        voucherDate: voucher.voucherDate.toISOString(),
        transactionDate: voucher.transactionDate.toISOString(),
        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,
        amount: parseFloat(voucher.amount)
      };

      let response;
      if (voucherId) {
        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);
      } else {
        response = await axios.post("/api/payment-vouchers", voucherData);
      }

      if (onSave) onSave(response.data.voucher);
    } catch (error) {
      console.error("Error saving voucher:", error);
      alert(error.response?.data?.message || "Error saving bank payment voucher");
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async () => {
    try {
      setLoading(true);
      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);
      if (onSave) onSave();
    } catch (error) {
      console.error("Error approving voucher:", error);
      alert(error.response?.data?.message || "Error approving voucher");
    } finally {
      setLoading(false);
    }
  };

  const handlePost = async () => {
    try {
      setLoading(true);
      await axios.put(`/api/payment-vouchers/${voucherId}/post`);
      if (onSave) onSave();
    } catch (error) {
      console.error("Error posting voucher:", error);
      alert(error.response?.data?.message || "Error posting voucher");
    } finally {
      setLoading(false);
    }
  };

  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');
  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';
  const canPost = !readOnly && voucherId && voucher.status === 'Approved';

  return (
    <Box>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Grid container spacing={3}>
          {/* Left Column */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Bank Payment Details
                </Typography>
                
                {/* Voucher Number (shown only for existing vouchers) */}
                {voucherId && (
                  <TextField
                    fullWidth
                    label="Voucher No."
                    value={voucher.voucherNumber || ''}
                    margin="normal"
                    InputProps={{ readOnly: true }}
                  />
                )}

                {/* Voucher Date */}
                <DatePicker
                  label="Voucher Date"
                  value={voucher.voucherDate}
                  onChange={(newValue) => handleInputChange('voucherDate', newValue)}
                  format="DD/MMM/YYYY"
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      margin="normal"
                      error={!!errors.voucherDate}
                      helperText={errors.voucherDate}
                      InputProps={{
                        ...params.InputProps,
                        readOnly: !canEdit
                      }}
                    />
                  )}
                  readOnly={!canEdit}
                />

                {/* Transaction Date */}
                <DatePicker
                  label="Transaction Date"
                  value={voucher.transactionDate}
                  onChange={(newValue) => handleInputChange('transactionDate', newValue)}
                  format="DD/MMM/YYYY"
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      fullWidth
                      margin="normal"
                      error={!!errors.transactionDate}
                      helperText={errors.transactionDate}
                      InputProps={{
                        ...params.InputProps,
                        readOnly: !canEdit
                      }}
                    />
                  )}
                  readOnly={!canEdit}
                />
                
                {/* Transaction ID / Reference */}
                <TextField
                  fullWidth
                  label="Transaction ID"
                  value={voucher.bankReference || ''}
                  onChange={(e) => handleInputChange('bankReference', e.target.value)}
                  margin="normal"
                  InputProps={{ readOnly: !canEdit }}
                  helperText="Optional field for internal reference or payment ID"
                />

                {/* Status */}
                <FormControl fullWidth margin="normal" error={!!errors.status}>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={voucher.status || 'Draft'}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    label="Status"
                    disabled={!canEdit}
                  >
                    {statusOptions.map((status) => (
                      <MenuItem key={status} value={status}>
                        {status}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.status && (
                    <FormHelperText>{errors.status}</FormHelperText>
                  )}
                </FormControl>

                {/* Bank Account (Credit Account) */}
                <FormControl
                  fullWidth
                  margin="normal"
                  error={!!errors.fromAccountId}
                >
                  <InputLabel>Bank</InputLabel>
                  <Select
                    value={voucher.fromAccountId}
                    onChange={(e) => handleInputChange('fromAccountId', e.target.value)}
                    label="Bank"
                    disabled={!canEdit}
                  >
                    <MenuItem value="">
                      <em>Select Bank</em>
                    </MenuItem>
                    {bankAccounts.map((account) => (
                      <MenuItem key={account._id} value={account._id}>
                        {account.accountName}
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.fromAccountId && (
                    <FormHelperText>{errors.fromAccountId}</FormHelperText>
                  )}
                </FormControl>
                
                {/* Payment Method */}
                <FormControl fullWidth margin="normal">
                  <InputLabel>Payment Method</InputLabel>
                  <Select
                    value={voucher.paymentMethod}
                    onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
                    label="Payment Method"
                    disabled={!canEdit}
                  >
                    {paymentMethods.map((method) => (
                      <MenuItem key={method} value={method}>
                        {method}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
                
                {/* Cheque Details (shown only for cheque payments) */}
                {voucher.paymentMethod === 'Cheque' && (
                  <>
                    <TextField
                      fullWidth
                      label="Cheque Number"
                      value={voucher.chequeNumber || ''}
                      onChange={(e) => handleInputChange('chequeNumber', e.target.value)}
                      margin="normal"
                      InputProps={{ readOnly: !canEdit }}
                    />
                    
                    <DatePicker
                      label="Cheque Date"
                      value={voucher.chequeDate}
                      onChange={(newValue) => handleInputChange('chequeDate', newValue)}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          fullWidth
                          margin="normal"
                          InputProps={{
                            ...params.InputProps,
                            readOnly: !canEdit
                          }}
                        />
                      )}
                      readOnly={!canEdit}
                    />
                  </>
                )}
              </CardContent>
            </Card>
          </Grid>
          
          {/* Right Column */}
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Payment Details
                </Typography>
                
                {/* Vendor Selection */}
                <Autocomplete
                  options={vendors}
                  getOptionLabel={(vendor) => vendor.name || ''}
                  value={selectedVendor}
                  onChange={(event, newValue) => handleVendorChange(newValue)}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="Vendor"
                      margin="normal"
                      fullWidth
                    />
                  )}
                  disabled={!canEdit}
                />
                
                {/* Purchase Invoice Selection (shown only if vendor is selected) */}
                {selectedVendor && (
                  <Autocomplete
                    options={purchaseInvoices}
                    getOptionLabel={(invoice) => {
                      if (!invoice) return '';

                      const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;
                      const invoiceDate = new Date(invoice.invoiceDate).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: 'short',
                        year: 'numeric'
                      });

                      // If there are returns, show the adjusted amount
                      if (invoice.hasReturns) {
                        return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} (Returns Applied)`;
                      }

                      // Otherwise just show the remaining amount
                      return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} outstanding`;
                    }}
                    value={selectedInvoice}
                    onChange={(event, newValue) => handleInvoiceChange(newValue)}
                    renderOption={(props, invoice) => (
                      <li {...props}>
                        <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>
                          <Typography variant="body1" fontWeight="medium" noWrap>
                            {invoice.invoiceNumber} - {formatCurrency(invoice.remainingAmount)} outstanding
                          </Typography>
                          {invoice.hasReturns && (
                            <>
                              <Typography variant="caption" color="text.secondary" display="block" noWrap>
                                Original: {formatCurrency(invoice.originalAmount)} | 
                                Returns: {formatCurrency(invoice.returnAmount)} | 
                                Adjusted: {formatCurrency(invoice.adjustedAmount)}
                              </Typography>
                              <Typography variant="caption" color="primary" display="block" noWrap>
                                {invoice.returnDetails.length} return(s) applied - 
                                {invoice.returnDetails.map((ret, idx) => (
                                  <span key={idx}>
                                    {ret.returnNumber} ({ret.status})
                                    {idx < invoice.returnDetails.length - 1 ? ', ' : ''}
                                  </span>
                                ))}
                              </Typography>
                            </>
                          )}
                        </Box>
                      </li>
                    )}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Purchase Invoice"
                        margin="normal"
                        fullWidth
                        error={purchaseInvoices.length === 0 || !!errors.invoice}
                        helperText={
                          errors.invoice ||
                          (purchaseInvoices.length === 0 ? "No unpaid invoices found for this vendor" :
                          "Optional: Select invoice for payment tracking. Partial payments are supported.")
                        }
                      />
                    )}
                    disabled={!canEdit || purchaseInvoices.length === 0}
                    noOptionsText="No unpaid invoices found"
                    ListboxProps={{
                      style: { maxHeight: '200px' }
                    }}
                  />
                )}
                
                {/* Title (Debit Account - shown if no vendor is selected) */}
                {!selectedVendor && (
                  <FormControl
                    fullWidth
                    margin="normal"
                    error={!!errors.toAccountId}
                  >
                    <InputLabel>Title</InputLabel>
                    <Select
                      value={voucher.toAccountId}
                      onChange={(e) => handleInputChange('toAccountId', e.target.value)}
                      label="Title"
                      disabled={!canEdit}
                    >
                      <MenuItem value="">
                        <em>Select Account</em>
                      </MenuItem>
                      {accounts.map((account) => (
                        <MenuItem key={account._id} value={account._id}>
                          {account.accountCode} - {account.accountName}
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.toAccountId && (
                      <FormHelperText>{errors.toAccountId}</FormHelperText>
                    )}
                  </FormControl>
                )}
                
                {/* Amount */}
                <TextField
                  fullWidth
                  label="Amount"
                  value={displayAmount}
                  onChange={(e) => handleInputChange('amount', e.target.value)}
                  onBlur={handleAmountBlur}
                  onFocus={handleAmountFocus}
                  margin="normal"
                  error={!!errors.amount}
                  helperText={errors.amount || "Amount will be formatted with commas and 2 decimal places"}
                  InputProps={{
                    readOnly: !canEdit,
                    inputProps: { style: { textAlign: 'right' } }
                  }}
                />
                
                {/* Narration */}
                <TextField
                  fullWidth
                  label="Narration"
                  value={voucher.narration}
                  onChange={(e) => handleInputChange('narration', e.target.value)}
                  margin="normal"
                  multiline
                  rows={2}
                  error={!!errors.narration}
                  helperText={errors.narration}
                  InputProps={{ readOnly: !canEdit }}
                />
                
                {/* Description */}
                <TextField
                  fullWidth
                  label="Description"
                  value={voucher.description || ''}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  margin="normal"
                  multiline
                  rows={2}
                  InputProps={{ readOnly: !canEdit }}
                />
              </CardContent>
            </Card>
          </Grid>
          
          {/* Action Buttons */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>
              {onCancel && (
                <Button
                  variant="outlined"
                  color="secondary"
                  startIcon={<CancelIcon />}
                  onClick={onCancel}
                >
                  Cancel
                </Button>
              )}
              
              {canEdit && (
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<SaveIcon />}
                  onClick={handleSave}
                  disabled={loading}
                >
                  Save
                </Button>
              )}
              
              {canApprove && (
                <Button
                  variant="contained"
                  color="warning"
                  startIcon={<ApproveIcon />}
                  onClick={handleApprove}
                  disabled={loading}
                >
                  Approve
                </Button>
              )}
              
              {canPost && (
                <Button
                  variant="contained"
                  color="success"
                  startIcon={<PostIcon />}
                  onClick={handlePost}
                  disabled={loading}
                >
                  Post to Ledger
                </Button>
              )}
            </Box>
          </Grid>
        </Grid>
      </LocalizationProvider>
    </Box>
  );
};

export default BankPaymentVoucherForm; 