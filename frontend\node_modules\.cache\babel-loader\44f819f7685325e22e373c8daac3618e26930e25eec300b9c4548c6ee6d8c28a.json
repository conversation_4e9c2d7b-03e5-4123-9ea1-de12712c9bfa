{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Learning Projects\\\\uni-core-business-suite_v3\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from \"react\";\nimport { BrowserRouter as Router, Routes, Route, Navigate } from \"react-router-dom\";\nimport { CssBaseline } from \"@mui/material\";\nimport { Dashboard as DashboardIcon, People as PeopleIcon, Settings as SettingsIcon, Business as BusinessIcon, Inventory, Category as CategoryIcon, Receipt as ReceiptIcon, AccountBalance as AccountBalanceIcon, List as ListIcon, Store as StoreIcon, Undo as UndoIcon, AssignmentReturn as ReturnIcon, Contacts as ContactsIcon, ShoppingBag as PurchasesIcon, Storefront as SalesIcon, PersonAdd as AddPersonIcon, Add as AddIcon, Assessment as StockTrackingIcon, Security as SecurityIcon, Group as UsersIcon, QrCode as QrCodeIcon, Payment as PaymentIcon } from \"@mui/icons-material\";\n\n// Import Layout component\nimport Layout from \"./components/Layout\";\n\n// Import pages and components\nimport Dashboard from \"./pages/Dashboard\";\nimport AddCustomerPage from \"./pages/AddCustomerPage\";\nimport AddVendorPage from \"./pages/AddVendorPage\";\nimport UserManagementPage from \"./pages/UserManagementPage\";\nimport UsersPage from \"./pages/UsersPage\";\nimport RolesPage from \"./pages/RolesPage\";\nimport LoginPage from \"./pages/LoginPage\";\nimport ManageOrders from \"./components/ManageOrders\";\nimport CustomerList from \"./components/CustomerList\";\nimport SettingsPage from \"./pages/SettingsPage\";\nimport CompanySetupPage from \"./pages/CompanySetupPage\";\nimport BankAccountsPage from \"./pages/BankAccountsPage\";\nimport ItemsPage from \"./pages/ItemsPage\";\nimport ContactsPage from \"./pages/ContactsPage\";\nimport PurchasesPage from \"./pages/PurchasesPage\";\nimport SalesPage from \"./pages/SalesPage\";\nimport ManageCategories from \"./components/ManageCategories\";\nimport ManageItems from \"./components/ManageItems\";\nimport OrderListPage from \"./pages/OrderListPage\";\nimport VendorList from \"./components/VendorList\";\nimport ManagePurchaseInvoices from \"./components/ManagePurchaseInvoices\";\nimport PurchaseInvoiceList from \"./components/PurchaseInvoiceList\";\nimport StockTracking from \"./components/StockTracking\";\nimport ManagePurchaseReturns from \"./components/ManagePurchaseReturns\";\nimport PurchaseReturnList from \"./components/PurchaseReturnList\";\nimport ManageSalesReturns from \"./components/ManageSalesReturns\";\nimport SalesReturnList from \"./components/SalesReturnList\";\nimport BarcodeGenerator from \"./components/BarcodeGenerator\";\nimport AccountingPage from \"./pages/AccountingPage\";\nimport ChartOfAccounts from \"./components/ChartOfAccounts\";\nimport GeneralLedger from \"./components/GeneralLedger\";\nimport SubsidiaryLedger from \"./components/SubsidiaryLedger\";\nimport AddBankAccountPage from \"./pages/AddBankAccountPage\";\nimport BankPaymentsPage from \"./pages/BankPaymentsPage\";\nimport ManageBankPaymentPage from \"./pages/ManageBankPaymentPage\";\n\n// Define menu items with icons and paths\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst menuItems = [{\n  title: \"Dashboard\",\n  icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 11\n  }, this),\n  path: \"/\"\n}, {\n  title: \"Contacts\",\n  icon: /*#__PURE__*/_jsxDEV(ContactsIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 11\n  }, this),\n  path: \"/contacts\",\n  // Landing page for contacts\n  subItems: [{\n    title: \"Customer List\",\n    icon: /*#__PURE__*/_jsxDEV(PeopleIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 15\n    }, this),\n    path: \"/customers\"\n  }, {\n    title: \"Add Customer\",\n    icon: /*#__PURE__*/_jsxDEV(AddPersonIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 15\n    }, this),\n    path: \"/add-customer\"\n  }, {\n    title: \"Vendor List\",\n    icon: /*#__PURE__*/_jsxDEV(StoreIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 15\n    }, this),\n    path: \"/vendors\"\n  }, {\n    title: \"Add Vendor\",\n    icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 15\n    }, this),\n    path: \"/add-vendor\"\n  }]\n}, {\n  title: \"Purchases\",\n  icon: /*#__PURE__*/_jsxDEV(PurchasesIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 11\n  }, this),\n  path: \"/purchases\",\n  // Landing page for purchases\n  subItems: [{\n    title: \"Invoice List\",\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 15\n    }, this),\n    path: \"/purchase-invoices\"\n  }, {\n    title: \"Create Invoice\",\n    icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 15\n    }, this),\n    path: \"/create-purchase-invoice\"\n  }, {\n    title: \"Return List\",\n    icon: /*#__PURE__*/_jsxDEV(UndoIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 15\n    }, this),\n    path: \"/purchase-returns\"\n  }, {\n    title: \"Create Return\",\n    icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 15\n    }, this),\n    path: \"/create-purchase-return\"\n  }]\n}, {\n  title: \"Sales\",\n  icon: /*#__PURE__*/_jsxDEV(SalesIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 133,\n    columnNumber: 11\n  }, this),\n  path: \"/sales\",\n  // Landing page for sales\n  subItems: [{\n    title: \"Order List\",\n    icon: /*#__PURE__*/_jsxDEV(ListIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 15\n    }, this),\n    path: \"/orders\"\n  }, {\n    title: \"Create Order\",\n    icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 15\n    }, this),\n    path: \"/manage-order\"\n  }, {\n    title: \"Return List\",\n    icon: /*#__PURE__*/_jsxDEV(ReturnIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 15\n    }, this),\n    path: \"/sales-returns\"\n  }, {\n    title: \"Create Return\",\n    icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 15\n    }, this),\n    path: \"/create-sales-return\"\n  }]\n}, {\n  title: \"Stock Tracking\",\n  icon: /*#__PURE__*/_jsxDEV(StockTrackingIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 11\n  }, this),\n  path: \"/stock-tracking\"\n}, {\n  title: \"Accounting & Finance\",\n  icon: /*#__PURE__*/_jsxDEV(AccountBalanceIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 165,\n    columnNumber: 11\n  }, this),\n  path: \"/accounting\",\n  subItems: [{\n    title: \"Chart of Accounts\",\n    icon: /*#__PURE__*/_jsxDEV(AccountBalanceIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 15\n    }, this),\n    path: \"/accounting/chart-of-accounts\"\n  }, {\n    title: \"General Ledger\",\n    icon: /*#__PURE__*/_jsxDEV(ReceiptIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 15\n    }, this),\n    path: \"/accounting/general-ledger\"\n  }, {\n    title: \"Bank Accounts\",\n    icon: /*#__PURE__*/_jsxDEV(AccountBalanceIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 15\n    }, this),\n    path: \"/bank-accounts\"\n  }, {\n    title: \"Bank Payments\",\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 15\n    }, this),\n    path: \"/accounting/bank-payments\"\n  }, {\n    title: \"Bank Receipts\",\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 15\n    }, this),\n    path: \"/accounting/bank-receipts\"\n  }, {\n    title: \"Cash Payments\",\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 15\n    }, this),\n    path: \"/accounting/cash-payments\"\n  }, {\n    title: \"Cash Receipts\",\n    icon: /*#__PURE__*/_jsxDEV(PaymentIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 15\n    }, this),\n    path: \"/accounting/cash-receipts\"\n  }]\n}, {\n  title: \"Products & Services\",\n  icon: /*#__PURE__*/_jsxDEV(Inventory, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 11\n  }, this),\n  path: \"/items-page\",\n  subItems: [{\n    title: \"All Items\",\n    icon: /*#__PURE__*/_jsxDEV(Inventory, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 15\n    }, this),\n    path: \"/items-page\"\n  }, {\n    title: \"Categories\",\n    icon: /*#__PURE__*/_jsxDEV(CategoryIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 15\n    }, this),\n    path: \"/manage-categories\"\n  }, {\n    title: \"Manage Items\",\n    icon: /*#__PURE__*/_jsxDEV(ReceiptIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 15\n    }, this),\n    path: \"/manage-items\"\n  }, {\n    title: \"Barcode Generator\",\n    icon: /*#__PURE__*/_jsxDEV(QrCodeIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 15\n    }, this),\n    path: \"/barcode-generator\"\n  }]\n}, {\n  title: \"Settings\",\n  icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 11\n  }, this),\n  path: \"/settings\",\n  subItems: [{\n    title: \"General Settings\",\n    icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 15\n    }, this),\n    path: \"/settings\"\n  }, {\n    title: \"Company Setup\",\n    icon: /*#__PURE__*/_jsxDEV(BusinessIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 15\n    }, this),\n    path: \"/company-setup\"\n  }]\n}, {\n  title: \"User Management\",\n  icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 251,\n    columnNumber: 11\n  }, this),\n  path: \"/user-management\",\n  subItems: [{\n    title: \"Users\",\n    icon: /*#__PURE__*/_jsxDEV(UsersIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 15\n    }, this),\n    path: \"/users\"\n  }, {\n    title: \"Roles & Permissions\",\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 15\n    }, this),\n    path: \"/roles\"\n  }]\n}];\nconst App = () => {\n  _s();\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is already logged in\n    const token = localStorage.getItem(\"authToken\");\n    const userData = localStorage.getItem(\"currentUser\");\n    if (token && userData) {\n      try {\n        const user = JSON.parse(userData);\n        setCurrentUser(user);\n        setIsAuthenticated(true);\n      } catch (error) {\n        console.error(\"Error parsing user data:\", error);\n        localStorage.removeItem(\"authToken\");\n        localStorage.removeItem(\"currentUser\");\n      }\n    }\n    setLoading(false);\n  }, []);\n  const handleLogin = (user, token) => {\n    setCurrentUser(user);\n    setIsAuthenticated(true);\n    localStorage.setItem(\"authToken\", token);\n    localStorage.setItem(\"currentUser\", JSON.stringify(user));\n  };\n  const updateCurrentUser = updatedUser => {\n    setCurrentUser(updatedUser);\n    localStorage.setItem(\"currentUser\", JSON.stringify(updatedUser));\n  };\n  const handleLogout = () => {\n    setCurrentUser(null);\n    setIsAuthenticated(false);\n    localStorage.removeItem(\"authToken\");\n    localStorage.removeItem(\"currentUser\");\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 12\n    }, this);\n  }\n  if (!isAuthenticated) {\n    return /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Router, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(LoginPage, {\n              onLogin: handleLogin\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/login\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        menuItems: menuItems,\n        currentUser: currentUser,\n        onLogout: handleLogout,\n        onUserUpdate: updateCurrentUser,\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 336,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/contacts\",\n            element: /*#__PURE__*/_jsxDEV(ContactsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/purchases\",\n            element: /*#__PURE__*/_jsxDEV(PurchasesPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/sales\",\n            element: /*#__PURE__*/_jsxDEV(SalesPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/accounting\",\n            element: /*#__PURE__*/_jsxDEV(AccountingPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/user-management\",\n            element: /*#__PURE__*/_jsxDEV(UserManagementPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/add-customer\",\n            element: /*#__PURE__*/_jsxDEV(AddCustomerPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/add-vendor\",\n            element: /*#__PURE__*/_jsxDEV(AddVendorPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/manage-order\",\n            element: /*#__PURE__*/_jsxDEV(ManageOrders, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/orders\",\n            element: /*#__PURE__*/_jsxDEV(OrderListPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 44\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/customers\",\n            element: /*#__PURE__*/_jsxDEV(CustomerList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 47\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/vendors\",\n            element: /*#__PURE__*/_jsxDEV(VendorList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/purchase-invoices\",\n            element: /*#__PURE__*/_jsxDEV(PurchaseInvoiceList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/create-purchase-invoice\",\n            element: /*#__PURE__*/_jsxDEV(ManagePurchaseInvoices, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 61\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/purchase-returns\",\n            element: /*#__PURE__*/_jsxDEV(PurchaseReturnList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 54\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/create-purchase-return\",\n            element: /*#__PURE__*/_jsxDEV(ManagePurchaseReturns, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 60\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/sales-returns\",\n            element: /*#__PURE__*/_jsxDEV(SalesReturnList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/create-sales-return\",\n            element: /*#__PURE__*/_jsxDEV(ManageSalesReturns, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 57\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/barcode-generator\",\n            element: /*#__PURE__*/_jsxDEV(BarcodeGenerator, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/accounting/chart-of-accounts\",\n            element: /*#__PURE__*/_jsxDEV(ChartOfAccounts, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 66\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/accounting/general-ledger\",\n            element: /*#__PURE__*/_jsxDEV(GeneralLedger, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 63\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/accounting/subsidiary-ledgers\",\n            element: /*#__PURE__*/_jsxDEV(SubsidiaryLedger, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 67\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/accounting/bank-payments\",\n            element: /*#__PURE__*/_jsxDEV(BankPaymentsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 62\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/ledger/:accountId\",\n            element: /*#__PURE__*/_jsxDEV(GeneralLedger, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/stock-tracking\",\n            element: /*#__PURE__*/_jsxDEV(StockTracking, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/settings\",\n            element: /*#__PURE__*/_jsxDEV(SettingsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/company-setup\",\n            element: /*#__PURE__*/_jsxDEV(CompanySetupPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/bank-accounts\",\n            element: /*#__PURE__*/_jsxDEV(BankAccountsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/add-bank-account\",\n            element: /*#__PURE__*/_jsxDEV(AddBankAccountPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 54\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/items-page\",\n            element: /*#__PURE__*/_jsxDEV(ItemsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 48\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/manage-categories\",\n            element: /*#__PURE__*/_jsxDEV(ManageCategories, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 55\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/manage-items\",\n            element: /*#__PURE__*/_jsxDEV(ManageItems, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/users\",\n            element: /*#__PURE__*/_jsxDEV(UsersPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/add-user\",\n            element: /*#__PURE__*/_jsxDEV(UsersPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/roles\",\n            element: /*#__PURE__*/_jsxDEV(RolesPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/add-role\",\n            element: /*#__PURE__*/_jsxDEV(RolesPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(App, \"JiCjNPhbX/z/+1rb8Y3uBqaeHqs=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "CssBaseline", "Dashboard", "DashboardIcon", "People", "PeopleIcon", "Settings", "SettingsIcon", "Business", "BusinessIcon", "Inventory", "Category", "CategoryIcon", "Receipt", "ReceiptIcon", "AccountBalance", "AccountBalanceIcon", "List", "ListIcon", "Store", "StoreIcon", "Undo", "UndoIcon", "AssignmentReturn", "ReturnIcon", "Contacts", "ContactsIcon", "ShoppingBag", "PurchasesIcon", "Storefront", "SalesIcon", "PersonAdd", "AddPersonIcon", "Add", "AddIcon", "Assessment", "StockTrackingIcon", "Security", "SecurityIcon", "Group", "UsersIcon", "QrCode", "QrCodeIcon", "Payment", "PaymentIcon", "Layout", "AddCustomerPage", "AddVendorPage", "UserManagementPage", "UsersPage", "RolesPage", "LoginPage", "ManageOrders", "CustomerList", "SettingsPage", "CompanySetupPage", "BankAccountsPage", "ItemsPage", "ContactsPage", "PurchasesPage", "SalesPage", "ManageCategories", "ManageItems", "OrderListPage", "VendorList", "ManagePurchaseInvoices", "PurchaseInvoiceList", "StockTracking", "ManagePurchaseReturns", "PurchaseReturnList", "ManageSalesReturns", "SalesReturnList", "BarcodeGenerator", "AccountingPage", "ChartOfAccounts", "GeneralLedger", "SubsidiaryLedger", "AddBankAccountPage", "BankPaymentsPage", "ManageBankPaymentPage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "menuItems", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "subItems", "App", "_s", "isAuthenticated", "setIsAuthenticated", "currentUser", "setCurrentUser", "loading", "setLoading", "token", "localStorage", "getItem", "userData", "user", "JSON", "parse", "error", "console", "removeItem", "handleLogin", "setItem", "stringify", "updateCurrentUser", "updatedUser", "handleLogout", "children", "element", "onLogin", "to", "replace", "onLogout", "onUserUpdate", "_c", "$RefreshReg$"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/App.js"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from \"react-router-dom\";\r\nimport { CssBaseline } from \"@mui/material\";\r\nimport {\r\n  Dashboard as DashboardIcon,\r\n  People as PeopleIcon,\r\n  Settings as SettingsIcon,\r\n  Business as BusinessIcon,\r\n  Inventory,\r\n  Category as CategoryIcon,\r\n  Receipt as ReceiptIcon,\r\n  AccountBalance as AccountBalanceIcon,\r\n  List as ListIcon,\r\n  Store as StoreIcon,\r\n  Undo as UndoIcon,\r\n  AssignmentReturn as ReturnIcon,\r\n  Contacts as ContactsIcon,\r\n  ShoppingBag as PurchasesIcon,\r\n  Storefront as SalesIcon,\r\n  PersonAdd as AddPersonIcon,\r\n  Add as AddIcon,\r\n  Assessment as StockTrackingIcon,\r\n  Security as SecurityIcon,\r\n  Group as UsersIcon,\r\n  QrCode as QrCodeIcon,\r\n  Payment as PaymentIcon\r\n} from \"@mui/icons-material\";\r\n\r\n// Import Layout component\r\nimport Layout from \"./components/Layout\";\r\n\r\n// Import pages and components\r\nimport Dashboard from \"./pages/Dashboard\";\r\n\r\nimport AddCustomerPage from \"./pages/AddCustomerPage\";\r\nimport AddVendorPage from \"./pages/AddVendorPage\";\r\nimport UserManagementPage from \"./pages/UserManagementPage\";\r\nimport UsersPage from \"./pages/UsersPage\";\r\nimport RolesPage from \"./pages/RolesPage\";\r\nimport LoginPage from \"./pages/LoginPage\";\r\nimport ManageOrders from \"./components/ManageOrders\";\r\nimport CustomerList from \"./components/CustomerList\";\r\nimport SettingsPage from \"./pages/SettingsPage\";\r\nimport CompanySetupPage from \"./pages/CompanySetupPage\";\r\nimport BankAccountsPage from \"./pages/BankAccountsPage\";\r\nimport ItemsPage from \"./pages/ItemsPage\";\r\nimport ContactsPage from \"./pages/ContactsPage\";\r\nimport PurchasesPage from \"./pages/PurchasesPage\";\r\nimport SalesPage from \"./pages/SalesPage\";\r\nimport ManageCategories from \"./components/ManageCategories\";\r\nimport ManageItems from \"./components/ManageItems\";\r\nimport OrderListPage from \"./pages/OrderListPage\";\r\nimport VendorList from \"./components/VendorList\";\r\nimport ManagePurchaseInvoices from \"./components/ManagePurchaseInvoices\";\r\nimport PurchaseInvoiceList from \"./components/PurchaseInvoiceList\";\r\nimport StockTracking from \"./components/StockTracking\";\r\nimport ManagePurchaseReturns from \"./components/ManagePurchaseReturns\";\r\nimport PurchaseReturnList from \"./components/PurchaseReturnList\";\r\nimport ManageSalesReturns from \"./components/ManageSalesReturns\";\r\nimport SalesReturnList from \"./components/SalesReturnList\";\r\nimport BarcodeGenerator from \"./components/BarcodeGenerator\";\r\nimport AccountingPage from \"./pages/AccountingPage\";\r\nimport ChartOfAccounts from \"./components/ChartOfAccounts\";\r\nimport GeneralLedger from \"./components/GeneralLedger\";\r\nimport SubsidiaryLedger from \"./components/SubsidiaryLedger\";\r\nimport AddBankAccountPage from \"./pages/AddBankAccountPage\";\r\nimport BankPaymentsPage from \"./pages/BankPaymentsPage\";\r\nimport ManageBankPaymentPage from \"./pages/ManageBankPaymentPage\";\r\n\r\n// Define menu items with icons and paths\r\nconst menuItems = [\r\n  {\r\n    title: \"Dashboard\",\r\n    icon: <DashboardIcon />,\r\n    path: \"/\",\r\n  },\r\n  {\r\n    title: \"Contacts\",\r\n    icon: <ContactsIcon />,\r\n    path: \"/contacts\", // Landing page for contacts\r\n    subItems: [\r\n      {\r\n        title: \"Customer List\",\r\n        icon: <PeopleIcon />,\r\n        path: \"/customers\",\r\n      },\r\n      {\r\n        title: \"Add Customer\",\r\n        icon: <AddPersonIcon />,\r\n        path: \"/add-customer\",\r\n      },\r\n      {\r\n        title: \"Vendor List\",\r\n        icon: <StoreIcon />,\r\n        path: \"/vendors\",\r\n      },\r\n      {\r\n        title: \"Add Vendor\",\r\n        icon: <BusinessIcon />,\r\n        path: \"/add-vendor\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Purchases\",\r\n    icon: <PurchasesIcon />,\r\n    path: \"/purchases\", // Landing page for purchases\r\n    subItems: [\r\n      {\r\n        title: \"Invoice List\",\r\n        icon: <ListIcon />,\r\n        path: \"/purchase-invoices\",\r\n      },\r\n      {\r\n        title: \"Create Invoice\",\r\n        icon: <AddIcon />,\r\n        path: \"/create-purchase-invoice\",\r\n      },\r\n      {\r\n        title: \"Return List\",\r\n        icon: <UndoIcon />,\r\n        path: \"/purchase-returns\",\r\n      },\r\n      {\r\n        title: \"Create Return\",\r\n        icon: <AddIcon />,\r\n        path: \"/create-purchase-return\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Sales\",\r\n    icon: <SalesIcon />,\r\n    path: \"/sales\", // Landing page for sales\r\n    subItems: [\r\n      {\r\n        title: \"Order List\",\r\n        icon: <ListIcon />,\r\n        path: \"/orders\",\r\n      },\r\n      {\r\n        title: \"Create Order\",\r\n        icon: <AddIcon />,\r\n        path: \"/manage-order\",\r\n      },\r\n      {\r\n        title: \"Return List\",\r\n        icon: <ReturnIcon />,\r\n        path: \"/sales-returns\",\r\n      },\r\n      {\r\n        title: \"Create Return\",\r\n        icon: <AddIcon />,\r\n        path: \"/create-sales-return\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Stock Tracking\",\r\n    icon: <StockTrackingIcon />,\r\n    path: \"/stock-tracking\",\r\n  },\r\n  {\r\n    title: \"Accounting & Finance\",\r\n    icon: <AccountBalanceIcon />,\r\n    path: \"/accounting\",\r\n    subItems: [\r\n      {\r\n        title: \"Chart of Accounts\",\r\n        icon: <AccountBalanceIcon />,\r\n        path: \"/accounting/chart-of-accounts\",\r\n      },\r\n      {\r\n        title: \"General Ledger\",\r\n        icon: <ReceiptIcon />,\r\n        path: \"/accounting/general-ledger\",\r\n      },\r\n      {\r\n        title: \"Bank Accounts\",\r\n        icon: <AccountBalanceIcon />,\r\n        path: \"/bank-accounts\",\r\n      },\r\n      {\r\n        title: \"Bank Payments\",\r\n        icon: <PaymentIcon />,\r\n        path: \"/accounting/bank-payments\",\r\n      },\r\n      {\r\n        title: \"Bank Receipts\",\r\n        icon: <PaymentIcon />,\r\n        path: \"/accounting/bank-receipts\",\r\n      },\r\n      {\r\n        title: \"Cash Payments\",\r\n        icon: <PaymentIcon />,\r\n        path: \"/accounting/cash-payments\",\r\n      },\r\n      {\r\n        title: \"Cash Receipts\",\r\n        icon: <PaymentIcon />,\r\n        path: \"/accounting/cash-receipts\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Products & Services\",\r\n    icon: <Inventory />,\r\n    path: \"/items-page\",\r\n    subItems: [\r\n      {\r\n        title: \"All Items\",\r\n        icon: <Inventory />,\r\n        path: \"/items-page\",\r\n      },\r\n      {\r\n        title: \"Categories\",\r\n        icon: <CategoryIcon />,\r\n        path: \"/manage-categories\",\r\n      },\r\n      {\r\n        title: \"Manage Items\",\r\n        icon: <ReceiptIcon />,\r\n        path: \"/manage-items\",\r\n      },\r\n      {\r\n        title: \"Barcode Generator\",\r\n        icon: <QrCodeIcon />,\r\n        path: \"/barcode-generator\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"Settings\",\r\n    icon: <SettingsIcon />,\r\n    path: \"/settings\",\r\n    subItems: [\r\n      {\r\n        title: \"General Settings\",\r\n        icon: <SettingsIcon />,\r\n        path: \"/settings\",\r\n      },\r\n      {\r\n        title: \"Company Setup\",\r\n        icon: <BusinessIcon />,\r\n        path: \"/company-setup\",\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    title: \"User Management\",\r\n    icon: <SecurityIcon />,\r\n    path: \"/user-management\",\r\n    subItems: [\r\n      {\r\n        title: \"Users\",\r\n        icon: <UsersIcon />,\r\n        path: \"/users\",\r\n      },\r\n      {\r\n        title: \"Roles & Permissions\",\r\n        icon: <SecurityIcon />,\r\n        path: \"/roles\",\r\n      },\r\n    ],\r\n  },\r\n];\r\n\r\nconst App = () => {\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n  const [currentUser, setCurrentUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // Check if user is already logged in\r\n    const token = localStorage.getItem(\"authToken\");\r\n    const userData = localStorage.getItem(\"currentUser\");\r\n\r\n    if (token && userData) {\r\n      try {\r\n        const user = JSON.parse(userData);\r\n        setCurrentUser(user);\r\n        setIsAuthenticated(true);\r\n      } catch (error) {\r\n        console.error(\"Error parsing user data:\", error);\r\n        localStorage.removeItem(\"authToken\");\r\n        localStorage.removeItem(\"currentUser\");\r\n      }\r\n    }\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  const handleLogin = (user, token) => {\r\n    setCurrentUser(user);\r\n    setIsAuthenticated(true);\r\n    localStorage.setItem(\"authToken\", token);\r\n    localStorage.setItem(\"currentUser\", JSON.stringify(user));\r\n  };\r\n\r\n  const updateCurrentUser = (updatedUser) => {\r\n    setCurrentUser(updatedUser);\r\n    localStorage.setItem(\"currentUser\", JSON.stringify(updatedUser));\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    setCurrentUser(null);\r\n    setIsAuthenticated(false);\r\n    localStorage.removeItem(\"authToken\");\r\n    localStorage.removeItem(\"currentUser\");\r\n  };\r\n\r\n  if (loading) {\r\n    return <CssBaseline />;\r\n  }\r\n\r\n  if (!isAuthenticated) {\r\n    return (\r\n      <>\r\n        <CssBaseline />\r\n        <Router>\r\n          <Routes>\r\n            <Route path=\"/login\" element={<LoginPage onLogin={handleLogin} />} />\r\n            <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\r\n          </Routes>\r\n        </Router>\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <CssBaseline />\r\n      <Router>\r\n        <Layout menuItems={menuItems} currentUser={currentUser} onLogout={handleLogout} onUserUpdate={updateCurrentUser}>\r\n          <Routes>\r\n            <Route path=\"/login\" element={<Navigate to=\"/\" replace />} />\r\n            <Route path=\"/\" element={<Dashboard />} />\r\n\r\n            {/* Landing Pages for Grouped Menu Items */}\r\n            <Route path=\"/contacts\" element={<ContactsPage />} />\r\n            <Route path=\"/purchases\" element={<PurchasesPage />} />\r\n            <Route path=\"/sales\" element={<SalesPage />} />\r\n            <Route path=\"/accounting\" element={<AccountingPage />} />\r\n            <Route path=\"/user-management\" element={<UserManagementPage />} />\r\n\r\n            {/* Individual Pages */}\r\n            <Route path=\"/add-customer\" element={<AddCustomerPage />} />\r\n            <Route path=\"/add-vendor\" element={<AddVendorPage />} />\r\n            <Route path=\"/manage-order\" element={<ManageOrders />} />\r\n            <Route path=\"/orders\" element={<OrderListPage />} />\r\n            <Route path=\"/customers\" element={<CustomerList />} />\r\n\r\n            {/* Vendor Management Routes */}\r\n            <Route path=\"/vendors\" element={<VendorList />} />\r\n\r\n            {/* Purchase Invoice Routes */}\r\n            <Route path=\"/purchase-invoices\" element={<PurchaseInvoiceList />} />\r\n            <Route path=\"/create-purchase-invoice\" element={<ManagePurchaseInvoices />} />\r\n\r\n            {/* Purchase Return Routes */}\r\n            <Route path=\"/purchase-returns\" element={<PurchaseReturnList />} />\r\n            <Route path=\"/create-purchase-return\" element={<ManagePurchaseReturns />} />\r\n\r\n            {/* Sales Return Routes */}\r\n            <Route path=\"/sales-returns\" element={<SalesReturnList />} />\r\n            <Route path=\"/create-sales-return\" element={<ManageSalesReturns />} />\r\n\r\n            {/* Barcode Generator Route */}\r\n            <Route path=\"/barcode-generator\" element={<BarcodeGenerator />} />\r\n\r\n            {/* Accounting Routes */}\r\n            <Route path=\"/accounting/chart-of-accounts\" element={<ChartOfAccounts />} />\r\n            <Route path=\"/accounting/general-ledger\" element={<GeneralLedger />} />\r\n            <Route path=\"/accounting/subsidiary-ledgers\" element={<SubsidiaryLedger />} />\r\n            <Route path=\"/accounting/bank-payments\" element={<BankPaymentsPage />} />\r\n            <Route path=\"/ledger/:accountId\" element={<GeneralLedger />} />\r\n\r\n            <Route path=\"/stock-tracking\" element={<StockTracking />} />\r\n            <Route path=\"/settings\" element={<SettingsPage />} />\r\n            <Route path=\"/company-setup\" element={<CompanySetupPage />} />\r\n            <Route path=\"/bank-accounts\" element={<BankAccountsPage />} />\r\n            <Route path=\"/add-bank-account\" element={<AddBankAccountPage />} />\r\n\r\n            {/* Product & Services Management Routes */}\r\n            <Route path=\"/items-page\" element={<ItemsPage />} />\r\n            <Route path=\"/manage-categories\" element={<ManageCategories />} />\r\n            <Route path=\"/manage-items\" element={<ManageItems />} />\r\n\r\n            {/* User Management Routes */}\r\n            <Route path=\"/users\" element={<UsersPage />} />\r\n            <Route path=\"/add-user\" element={<UsersPage />} />\r\n            <Route path=\"/roles\" element={<RolesPage />} />\r\n            <Route path=\"/add-role\" element={<RolesPage />} />\r\n\r\n          </Routes>\r\n        </Layout>\r\n      </Router>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default App;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,WAAW,QAAQ,eAAe;AAC3C,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,QAAQ,IAAIC,YAAY,EACxBC,SAAS,EACTC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,WAAW,EACtBC,cAAc,IAAIC,kBAAkB,EACpCC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,SAAS,EAClBC,IAAI,IAAIC,QAAQ,EAChBC,gBAAgB,IAAIC,UAAU,EAC9BC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,aAAa,EAC5BC,UAAU,IAAIC,SAAS,EACvBC,SAAS,IAAIC,aAAa,EAC1BC,GAAG,IAAIC,OAAO,EACdC,UAAU,IAAIC,iBAAiB,EAC/BC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;;AAE5B;AACA,OAAOC,MAAM,MAAM,qBAAqB;;AAExC;AACA,OAAO3C,SAAS,MAAM,mBAAmB;AAEzC,OAAO4C,eAAe,MAAM,yBAAyB;AACrD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,qBAAqB,MAAM,oCAAoC;AACtE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,kBAAkB,MAAM,4BAA4B;AAC3D,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,qBAAqB,MAAM,+BAA+B;;AAEjE;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,SAAS,GAAG,CAChB;EACEC,KAAK,EAAE,WAAW;EAClBC,IAAI,eAAEL,OAAA,CAAC9E,aAAa;IAAAoF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvBC,IAAI,EAAE;AACR,CAAC,EACD;EACEN,KAAK,EAAE,UAAU;EACjBC,IAAI,eAAEL,OAAA,CAACvD,YAAY;IAAA6D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtBC,IAAI,EAAE,WAAW;EAAE;EACnBC,QAAQ,EAAE,CACR;IACEP,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAAC5E,UAAU;MAAAkF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,cAAc;IACrBC,IAAI,eAAEL,OAAA,CAACjD,aAAa;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEL,OAAA,CAAC7D,SAAS;MAAAmE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,YAAY;IACnBC,IAAI,eAAEL,OAAA,CAACxE,YAAY;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC;AAEL,CAAC,EACD;EACEN,KAAK,EAAE,WAAW;EAClBC,IAAI,eAAEL,OAAA,CAACrD,aAAa;IAAA2D,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACvBC,IAAI,EAAE,YAAY;EAAE;EACpBC,QAAQ,EAAE,CACR;IACEP,KAAK,EAAE,cAAc;IACrBC,IAAI,eAAEL,OAAA,CAAC/D,QAAQ;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,gBAAgB;IACvBC,IAAI,eAAEL,OAAA,CAAC/C,OAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEL,OAAA,CAAC3D,QAAQ;MAAAiE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAAC/C,OAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,IAAI,EAAE;EACR,CAAC;AAEL,CAAC,EACD;EACEN,KAAK,EAAE,OAAO;EACdC,IAAI,eAAEL,OAAA,CAACnD,SAAS;IAAAyD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACnBC,IAAI,EAAE,QAAQ;EAAE;EAChBC,QAAQ,EAAE,CACR;IACEP,KAAK,EAAE,YAAY;IACnBC,IAAI,eAAEL,OAAA,CAAC/D,QAAQ;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAClBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,cAAc;IACrBC,IAAI,eAAEL,OAAA,CAAC/C,OAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,aAAa;IACpBC,IAAI,eAAEL,OAAA,CAACzD,UAAU;MAAA+D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAAC/C,OAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACjBC,IAAI,EAAE;EACR,CAAC;AAEL,CAAC,EACD;EACEN,KAAK,EAAE,gBAAgB;EACvBC,IAAI,eAAEL,OAAA,CAAC7C,iBAAiB;IAAAmD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3BC,IAAI,EAAE;AACR,CAAC,EACD;EACEN,KAAK,EAAE,sBAAsB;EAC7BC,IAAI,eAAEL,OAAA,CAACjE,kBAAkB;IAAAuE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC5BC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,CACR;IACEP,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,eAAEL,OAAA,CAACjE,kBAAkB;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,gBAAgB;IACvBC,IAAI,eAAEL,OAAA,CAACnE,WAAW;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAACjE,kBAAkB;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC5BC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAACrC,WAAW;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAACrC,WAAW;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAACrC,WAAW;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAACrC,WAAW;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC;AAEL,CAAC,EACD;EACEN,KAAK,EAAE,qBAAqB;EAC5BC,IAAI,eAAEL,OAAA,CAACvE,SAAS;IAAA6E,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACnBC,IAAI,EAAE,aAAa;EACnBC,QAAQ,EAAE,CACR;IACEP,KAAK,EAAE,WAAW;IAClBC,IAAI,eAAEL,OAAA,CAACvE,SAAS;MAAA6E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,YAAY;IACnBC,IAAI,eAAEL,OAAA,CAACrE,YAAY;MAAA2E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,cAAc;IACrBC,IAAI,eAAEL,OAAA,CAACnE,WAAW;MAAAyE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,eAAEL,OAAA,CAACvC,UAAU;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE;EACR,CAAC;AAEL,CAAC,EACD;EACEN,KAAK,EAAE,UAAU;EACjBC,IAAI,eAAEL,OAAA,CAAC1E,YAAY;IAAAgF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtBC,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE,CACR;IACEP,KAAK,EAAE,kBAAkB;IACzBC,IAAI,eAAEL,OAAA,CAAC1E,YAAY;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,eAAe;IACtBC,IAAI,eAAEL,OAAA,CAACxE,YAAY;MAAA8E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC;AAEL,CAAC,EACD;EACEN,KAAK,EAAE,iBAAiB;EACxBC,IAAI,eAAEL,OAAA,CAAC3C,YAAY;IAAAiD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACtBC,IAAI,EAAE,kBAAkB;EACxBC,QAAQ,EAAE,CACR;IACEP,KAAK,EAAE,OAAO;IACdC,IAAI,eAAEL,OAAA,CAACzC,SAAS;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,KAAK,EAAE,qBAAqB;IAC5BC,IAAI,eAAEL,OAAA,CAAC3C,YAAY;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC;AAEL,CAAC,CACF;AAED,MAAME,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtG,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuG,WAAW,EAAEC,cAAc,CAAC,GAAGxG,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACyG,OAAO,EAAEC,UAAU,CAAC,GAAG1G,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAM0G,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IAC/C,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;IAEpD,IAAIF,KAAK,IAAIG,QAAQ,EAAE;MACrB,IAAI;QACF,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC;QACjCN,cAAc,CAACO,IAAI,CAAC;QACpBT,kBAAkB,CAAC,IAAI,CAAC;MAC1B,CAAC,CAAC,OAAOY,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChDN,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;QACpCR,YAAY,CAACQ,UAAU,CAAC,aAAa,CAAC;MACxC;IACF;IACAV,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,WAAW,GAAGA,CAACN,IAAI,EAAEJ,KAAK,KAAK;IACnCH,cAAc,CAACO,IAAI,CAAC;IACpBT,kBAAkB,CAAC,IAAI,CAAC;IACxBM,YAAY,CAACU,OAAO,CAAC,WAAW,EAAEX,KAAK,CAAC;IACxCC,YAAY,CAACU,OAAO,CAAC,aAAa,EAAEN,IAAI,CAACO,SAAS,CAACR,IAAI,CAAC,CAAC;EAC3D,CAAC;EAED,MAAMS,iBAAiB,GAAIC,WAAW,IAAK;IACzCjB,cAAc,CAACiB,WAAW,CAAC;IAC3Bb,YAAY,CAACU,OAAO,CAAC,aAAa,EAAEN,IAAI,CAACO,SAAS,CAACE,WAAW,CAAC,CAAC;EAClE,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzBlB,cAAc,CAAC,IAAI,CAAC;IACpBF,kBAAkB,CAAC,KAAK,CAAC;IACzBM,YAAY,CAACQ,UAAU,CAAC,WAAW,CAAC;IACpCR,YAAY,CAACQ,UAAU,CAAC,aAAa,CAAC;EACxC,CAAC;EAED,IAAIX,OAAO,EAAE;IACX,oBAAOlB,OAAA,CAAChF,WAAW;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxB;EAEA,IAAI,CAACK,eAAe,EAAE;IACpB,oBACEd,OAAA,CAAAE,SAAA;MAAAkC,QAAA,gBACEpC,OAAA,CAAChF,WAAW;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACfT,OAAA,CAACpF,MAAM;QAAAwH,QAAA,eACLpC,OAAA,CAACnF,MAAM;UAAAuH,QAAA,gBACLpC,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,QAAQ;YAAC2B,OAAO,eAAErC,OAAA,CAAC9B,SAAS;cAACoE,OAAO,EAAER;YAAY;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,GAAG;YAAC2B,OAAO,eAAErC,OAAA,CAACjF,QAAQ;cAACwH,EAAE,EAAC,QAAQ;cAACC,OAAO;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA,eACT,CAAC;EAEP;EAEA,oBACET,OAAA,CAAAE,SAAA;IAAAkC,QAAA,gBACEpC,OAAA,CAAChF,WAAW;MAAAsF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfT,OAAA,CAACpF,MAAM;MAAAwH,QAAA,eACLpC,OAAA,CAACpC,MAAM;QAACuC,SAAS,EAAEA,SAAU;QAACa,WAAW,EAAEA,WAAY;QAACyB,QAAQ,EAAEN,YAAa;QAACO,YAAY,EAAET,iBAAkB;QAAAG,QAAA,eAC9GpC,OAAA,CAACnF,MAAM;UAAAuH,QAAA,gBACLpC,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,QAAQ;YAAC2B,OAAO,eAAErC,OAAA,CAACjF,QAAQ;cAACwH,EAAE,EAAC,GAAG;cAACC,OAAO;YAAA;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,GAAG;YAAC2B,OAAO,eAAErC,OAAA,CAAC/E,SAAS;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG1CT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,WAAW;YAAC2B,OAAO,eAAErC,OAAA,CAACvB,YAAY;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,YAAY;YAAC2B,OAAO,eAAErC,OAAA,CAACtB,aAAa;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,QAAQ;YAAC2B,OAAO,eAAErC,OAAA,CAACrB,SAAS;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,aAAa;YAAC2B,OAAO,eAAErC,OAAA,CAACR,cAAc;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,kBAAkB;YAAC2B,OAAO,eAAErC,OAAA,CAACjC,kBAAkB;cAAAuC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGlET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,eAAe;YAAC2B,OAAO,eAAErC,OAAA,CAACnC,eAAe;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,aAAa;YAAC2B,OAAO,eAAErC,OAAA,CAAClC,aAAa;cAAAwC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,eAAe;YAAC2B,OAAO,eAAErC,OAAA,CAAC7B,YAAY;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,SAAS;YAAC2B,OAAO,eAAErC,OAAA,CAAClB,aAAa;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,YAAY;YAAC2B,OAAO,eAAErC,OAAA,CAAC5B,YAAY;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGtDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,UAAU;YAAC2B,OAAO,eAAErC,OAAA,CAACjB,UAAU;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGlDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,oBAAoB;YAAC2B,OAAO,eAAErC,OAAA,CAACf,mBAAmB;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,0BAA0B;YAAC2B,OAAO,eAAErC,OAAA,CAAChB,sBAAsB;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG9ET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,mBAAmB;YAAC2B,OAAO,eAAErC,OAAA,CAACZ,kBAAkB;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,yBAAyB;YAAC2B,OAAO,eAAErC,OAAA,CAACb,qBAAqB;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAG5ET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,gBAAgB;YAAC2B,OAAO,eAAErC,OAAA,CAACV,eAAe;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,sBAAsB;YAAC2B,OAAO,eAAErC,OAAA,CAACX,kBAAkB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGtET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,oBAAoB;YAAC2B,OAAO,eAAErC,OAAA,CAACT,gBAAgB;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGlET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,+BAA+B;YAAC2B,OAAO,eAAErC,OAAA,CAACP,eAAe;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5ET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,4BAA4B;YAAC2B,OAAO,eAAErC,OAAA,CAACN,aAAa;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,gCAAgC;YAAC2B,OAAO,eAAErC,OAAA,CAACL,gBAAgB;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9ET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,2BAA2B;YAAC2B,OAAO,eAAErC,OAAA,CAACH,gBAAgB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,oBAAoB;YAAC2B,OAAO,eAAErC,OAAA,CAACN,aAAa;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAE/DT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,iBAAiB;YAAC2B,OAAO,eAAErC,OAAA,CAACd,aAAa;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,WAAW;YAAC2B,OAAO,eAAErC,OAAA,CAAC3B,YAAY;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,gBAAgB;YAAC2B,OAAO,eAAErC,OAAA,CAAC1B,gBAAgB;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,gBAAgB;YAAC2B,OAAO,eAAErC,OAAA,CAACzB,gBAAgB;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9DT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,mBAAmB;YAAC2B,OAAO,eAAErC,OAAA,CAACJ,kBAAkB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGnET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,aAAa;YAAC2B,OAAO,eAAErC,OAAA,CAACxB,SAAS;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,oBAAoB;YAAC2B,OAAO,eAAErC,OAAA,CAACpB,gBAAgB;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClET,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,eAAe;YAAC2B,OAAO,eAAErC,OAAA,CAACnB,WAAW;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGxDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,QAAQ;YAAC2B,OAAO,eAAErC,OAAA,CAAChC,SAAS;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,WAAW;YAAC2B,OAAO,eAAErC,OAAA,CAAChC,SAAS;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClDT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,QAAQ;YAAC2B,OAAO,eAAErC,OAAA,CAAC/B,SAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/CT,OAAA,CAAClF,KAAK;YAAC4F,IAAI,EAAC,WAAW;YAAC2B,OAAO,eAAErC,OAAA,CAAC/B,SAAS;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE5C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACT,CAAC;AAEP,CAAC;AAACI,EAAA,CAnIID,GAAG;AAAA+B,EAAA,GAAH/B,GAAG;AAqIT,eAAeA,GAAG;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}