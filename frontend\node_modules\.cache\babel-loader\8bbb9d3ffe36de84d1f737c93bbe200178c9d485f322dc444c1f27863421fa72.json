{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport UPCEANReader from './UPCEANReader';\nimport StringBuilder from '../util/StringBuilder';\nimport NotFoundException from '../NotFoundException';\nimport BarcodeFormat from '../BarcodeFormat';\n// package com.google.zxing.oned;\n// import com.google.zxing.BarcodeFormat;\n// import com.google.zxing.FormatException;\n// import com.google.zxing.NotFoundException;\n// import com.google.zxing.common.BitArray;\n/**\n * <p>Implements decoding of the UPC-E format.</p>\n * <p><a href=\"http://www.barcodeisland.com/upce.phtml\">This</a> is a great reference for\n * UPC-E information.</p>\n *\n * <AUTHOR> Owen\n *\n * @source https://github.com/zxing/zxing/blob/3c96923276dd5785d58eb970b6ba3f80d36a9505/core/src/main/java/com/google/zxing/oned/UPCEReader.java\n *\n * @experimental\n */\nvar UPCEReader = /** @class */function (_super) {\n  __extends(UPCEReader, _super);\n  function UPCEReader() {\n    var _this = _super.call(this) || this;\n    _this.decodeMiddleCounters = new Int32Array(4);\n    return _this;\n  }\n  /**\n   * @throws NotFoundException\n   */\n  // @Override\n  UPCEReader.prototype.decodeMiddle = function (row, startRange, result) {\n    var e_1, _a;\n    var counters = this.decodeMiddleCounters.map(function (x) {\n      return x;\n    });\n    counters[0] = 0;\n    counters[1] = 0;\n    counters[2] = 0;\n    counters[3] = 0;\n    var end = row.getSize();\n    var rowOffset = startRange[1];\n    var lgPatternFound = 0;\n    for (var x = 0; x < 6 && rowOffset < end; x++) {\n      var bestMatch = UPCEReader.decodeDigit(row, counters, rowOffset, UPCEReader.L_AND_G_PATTERNS);\n      result += String.fromCharCode('0'.charCodeAt(0) + bestMatch % 10);\n      try {\n        for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n          var counter = counters_1_1.value;\n          rowOffset += counter;\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      if (bestMatch >= 10) {\n        lgPatternFound |= 1 << 5 - x;\n      }\n    }\n    UPCEReader.determineNumSysAndCheckDigit(new StringBuilder(result), lgPatternFound);\n    return rowOffset;\n  };\n  /**\n   * @throws NotFoundException\n   */\n  // @Override\n  UPCEReader.prototype.decodeEnd = function (row, endStart) {\n    return UPCEReader.findGuardPatternWithoutCounters(row, endStart, true, UPCEReader.MIDDLE_END_PATTERN);\n  };\n  /**\n   * @throws FormatException\n   */\n  // @Override\n  UPCEReader.prototype.checkChecksum = function (s) {\n    return UPCEANReader.checkChecksum(UPCEReader.convertUPCEtoUPCA(s));\n  };\n  /**\n   * @throws NotFoundException\n   */\n  UPCEReader.determineNumSysAndCheckDigit = function (resultString, lgPatternFound) {\n    for (var numSys = 0; numSys <= 1; numSys++) {\n      for (var d = 0; d < 10; d++) {\n        if (lgPatternFound === this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[numSys][d]) {\n          resultString.insert(0, /*(char)*/'0' + numSys);\n          resultString.append(/*(char)*/'0' + d);\n          return;\n        }\n      }\n    }\n    throw NotFoundException.getNotFoundInstance();\n  };\n  // @Override\n  UPCEReader.prototype.getBarcodeFormat = function () {\n    return BarcodeFormat.UPC_E;\n  };\n  /**\n   * Expands a UPC-E value back into its full, equivalent UPC-A code value.\n   *\n   * @param upce UPC-E code as string of digits\n   * @return equivalent UPC-A code as string of digits\n   */\n  UPCEReader.convertUPCEtoUPCA = function (upce) {\n    // the following line is equivalent to upce.getChars(1, 7, upceChars, 0);\n    var upceChars = upce.slice(1, 7).split('').map(function (x) {\n      return x.charCodeAt(0);\n    });\n    var result = new StringBuilder(/*12*/);\n    result.append(upce.charAt(0));\n    var lastChar = upceChars[5];\n    switch (lastChar) {\n      case 0:\n      case 1:\n      case 2:\n        result.appendChars(upceChars, 0, 2);\n        result.append(lastChar);\n        result.append('0000');\n        result.appendChars(upceChars, 2, 3);\n        break;\n      case 3:\n        result.appendChars(upceChars, 0, 3);\n        result.append('00000');\n        result.appendChars(upceChars, 3, 2);\n        break;\n      case 4:\n        result.appendChars(upceChars, 0, 4);\n        result.append('00000');\n        result.append(upceChars[4]);\n        break;\n      default:\n        result.appendChars(upceChars, 0, 5);\n        result.append('0000');\n        result.append(lastChar);\n        break;\n    }\n    // Only append check digit in conversion if supplied\n    if (upce.length >= 8) {\n      result.append(upce.charAt(7));\n    }\n    return result.toString();\n  };\n  /**\n   * The pattern that marks the middle, and end, of a UPC-E pattern.\n   * There is no \"second half\" to a UPC-E barcode.\n   */\n  UPCEReader.MIDDLE_END_PATTERN = Int32Array.from([1, 1, 1, 1, 1, 1]);\n  // For an UPC-E barcode, the final digit is represented by the parities used\n  // to encode the middle six digits, according to the table below.\n  //\n  //                Parity of next 6 digits\n  //    Digit   0     1     2     3     4     5\n  //       0    Even   Even  Even Odd  Odd   Odd\n  //       1    Even   Even  Odd  Even Odd   Odd\n  //       2    Even   Even  Odd  Odd  Even  Odd\n  //       3    Even   Even  Odd  Odd  Odd   Even\n  //       4    Even   Odd   Even Even Odd   Odd\n  //       5    Even   Odd   Odd  Even Even  Odd\n  //       6    Even   Odd   Odd  Odd  Even  Even\n  //       7    Even   Odd   Even Odd  Even  Odd\n  //       8    Even   Odd   Even Odd  Odd   Even\n  //       9    Even   Odd   Odd  Even Odd   Even\n  //\n  // The encoding is represented by the following array, which is a bit pattern\n  // using Odd = 0 and Even = 1. For example, 5 is represented by:\n  //\n  //              Odd Even Even Odd Odd Even\n  // in binary:\n  //                0    1    1   0   0    1   == 0x19\n  //\n  /**\n   * See {@link #L_AND_G_PATTERNS}; these values similarly represent patterns of\n   * even-odd parity encodings of digits that imply both the number system (0 or 1)\n   * used, and the check digit.\n   */\n  UPCEReader.NUMSYS_AND_CHECK_DIGIT_PATTERNS = [Int32Array.from([0x38, 0x34, 0x32, 0x31, 0x2C, 0x26, 0x23, 0x2A, 0x29, 0x25]), Int32Array.from([0x07, 0x0B, 0x0D, 0x0E, 0x13, 0x19, 0x1C, 0x15, 0x16, 0x1])];\n  return UPCEReader;\n}(UPCEANReader);\nexport default UPCEReader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "UPCEANReader", "StringBuilder", "NotFoundException", "BarcodeFormat", "UPCEReader", "_super", "_this", "decodeMiddleCounters", "Int32Array", "decodeMiddle", "row", "startRange", "result", "e_1", "_a", "counters", "map", "x", "end", "getSize", "rowOffset", "lgPatternFound", "bestMatch", "decodeDigit", "L_AND_G_PATTERNS", "String", "fromCharCode", "charCodeAt", "counters_1", "counters_1_1", "counter", "e_1_1", "error", "return", "determineNumSysAndCheckDigit", "decodeEnd", "endStart", "findGuardPatternWithoutCounters", "MIDDLE_END_PATTERN", "checkChecksum", "convertUPCEtoUPCA", "resultString", "numSys", "NUMSYS_AND_CHECK_DIGIT_PATTERNS", "insert", "append", "getNotFoundInstance", "getBarcodeFormat", "UPC_E", "upce", "upceChars", "slice", "split", "char<PERSON>t", "lastChar", "appendChars", "toString", "from"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/UPCEReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport UPCEANReader from './UPCEANReader';\nimport StringBuilder from '../util/StringBuilder';\nimport NotFoundException from '../NotFoundException';\nimport BarcodeFormat from '../BarcodeFormat';\n// package com.google.zxing.oned;\n// import com.google.zxing.BarcodeFormat;\n// import com.google.zxing.FormatException;\n// import com.google.zxing.NotFoundException;\n// import com.google.zxing.common.BitArray;\n/**\n * <p>Implements decoding of the UPC-E format.</p>\n * <p><a href=\"http://www.barcodeisland.com/upce.phtml\">This</a> is a great reference for\n * UPC-E information.</p>\n *\n * <AUTHOR> Owen\n *\n * @source https://github.com/zxing/zxing/blob/3c96923276dd5785d58eb970b6ba3f80d36a9505/core/src/main/java/com/google/zxing/oned/UPCEReader.java\n *\n * @experimental\n */\nvar UPCEReader = /** @class */ (function (_super) {\n    __extends(UPCEReader, _super);\n    function UPCEReader() {\n        var _this = _super.call(this) || this;\n        _this.decodeMiddleCounters = new Int32Array(4);\n        return _this;\n    }\n    /**\n     * @throws NotFoundException\n     */\n    // @Override\n    UPCEReader.prototype.decodeMiddle = function (row, startRange, result) {\n        var e_1, _a;\n        var counters = this.decodeMiddleCounters.map(function (x) { return x; });\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        var lgPatternFound = 0;\n        for (var x = 0; x < 6 && rowOffset < end; x++) {\n            var bestMatch = UPCEReader.decodeDigit(row, counters, rowOffset, UPCEReader.L_AND_G_PATTERNS);\n            result += String.fromCharCode(('0'.charCodeAt(0) + bestMatch % 10));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (bestMatch >= 10) {\n                lgPatternFound |= 1 << (5 - x);\n            }\n        }\n        UPCEReader.determineNumSysAndCheckDigit(new StringBuilder(result), lgPatternFound);\n        return rowOffset;\n    };\n    /**\n     * @throws NotFoundException\n     */\n    // @Override\n    UPCEReader.prototype.decodeEnd = function (row, endStart) {\n        return UPCEReader.findGuardPatternWithoutCounters(row, endStart, true, UPCEReader.MIDDLE_END_PATTERN);\n    };\n    /**\n     * @throws FormatException\n     */\n    // @Override\n    UPCEReader.prototype.checkChecksum = function (s) {\n        return UPCEANReader.checkChecksum(UPCEReader.convertUPCEtoUPCA(s));\n    };\n    /**\n     * @throws NotFoundException\n     */\n    UPCEReader.determineNumSysAndCheckDigit = function (resultString, lgPatternFound) {\n        for (var numSys = 0; numSys <= 1; numSys++) {\n            for (var d = 0; d < 10; d++) {\n                if (lgPatternFound === this.NUMSYS_AND_CHECK_DIGIT_PATTERNS[numSys][d]) {\n                    resultString.insert(0, /*(char)*/ ('0' + numSys));\n                    resultString.append(/*(char)*/ ('0' + d));\n                    return;\n                }\n            }\n        }\n        throw NotFoundException.getNotFoundInstance();\n    };\n    // @Override\n    UPCEReader.prototype.getBarcodeFormat = function () {\n        return BarcodeFormat.UPC_E;\n    };\n    /**\n     * Expands a UPC-E value back into its full, equivalent UPC-A code value.\n     *\n     * @param upce UPC-E code as string of digits\n     * @return equivalent UPC-A code as string of digits\n     */\n    UPCEReader.convertUPCEtoUPCA = function (upce) {\n        // the following line is equivalent to upce.getChars(1, 7, upceChars, 0);\n        var upceChars = upce.slice(1, 7).split('').map(function (x) { return x.charCodeAt(0); });\n        var result = new StringBuilder( /*12*/);\n        result.append(upce.charAt(0));\n        var lastChar = upceChars[5];\n        switch (lastChar) {\n            case 0:\n            case 1:\n            case 2:\n                result.appendChars(upceChars, 0, 2);\n                result.append(lastChar);\n                result.append('0000');\n                result.appendChars(upceChars, 2, 3);\n                break;\n            case 3:\n                result.appendChars(upceChars, 0, 3);\n                result.append('00000');\n                result.appendChars(upceChars, 3, 2);\n                break;\n            case 4:\n                result.appendChars(upceChars, 0, 4);\n                result.append('00000');\n                result.append(upceChars[4]);\n                break;\n            default:\n                result.appendChars(upceChars, 0, 5);\n                result.append('0000');\n                result.append(lastChar);\n                break;\n        }\n        // Only append check digit in conversion if supplied\n        if (upce.length >= 8) {\n            result.append(upce.charAt(7));\n        }\n        return result.toString();\n    };\n    /**\n     * The pattern that marks the middle, and end, of a UPC-E pattern.\n     * There is no \"second half\" to a UPC-E barcode.\n     */\n    UPCEReader.MIDDLE_END_PATTERN = Int32Array.from([1, 1, 1, 1, 1, 1]);\n    // For an UPC-E barcode, the final digit is represented by the parities used\n    // to encode the middle six digits, according to the table below.\n    //\n    //                Parity of next 6 digits\n    //    Digit   0     1     2     3     4     5\n    //       0    Even   Even  Even Odd  Odd   Odd\n    //       1    Even   Even  Odd  Even Odd   Odd\n    //       2    Even   Even  Odd  Odd  Even  Odd\n    //       3    Even   Even  Odd  Odd  Odd   Even\n    //       4    Even   Odd   Even Even Odd   Odd\n    //       5    Even   Odd   Odd  Even Even  Odd\n    //       6    Even   Odd   Odd  Odd  Even  Even\n    //       7    Even   Odd   Even Odd  Even  Odd\n    //       8    Even   Odd   Even Odd  Odd   Even\n    //       9    Even   Odd   Odd  Even Odd   Even\n    //\n    // The encoding is represented by the following array, which is a bit pattern\n    // using Odd = 0 and Even = 1. For example, 5 is represented by:\n    //\n    //              Odd Even Even Odd Odd Even\n    // in binary:\n    //                0    1    1   0   0    1   == 0x19\n    //\n    /**\n     * See {@link #L_AND_G_PATTERNS}; these values similarly represent patterns of\n     * even-odd parity encodings of digits that imply both the number system (0 or 1)\n     * used, and the check digit.\n     */\n    UPCEReader.NUMSYS_AND_CHECK_DIGIT_PATTERNS = [\n        Int32Array.from([0x38, 0x34, 0x32, 0x31, 0x2C, 0x26, 0x23, 0x2A, 0x29, 0x25]),\n        Int32Array.from([0x07, 0x0B, 0x0D, 0x0E, 0x13, 0x19, 0x1C, 0x15, 0x16, 0x1]),\n    ];\n    return UPCEReader;\n}(UPCEANReader));\nexport default UPCEReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC9ChC,SAAS,CAAC+B,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IAClB,IAAIE,KAAK,GAAGD,MAAM,CAACX,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCY,KAAK,CAACC,oBAAoB,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;IAC9C,OAAOF,KAAK;EAChB;EACA;AACJ;AACA;EACI;EACAF,UAAU,CAACnB,SAAS,CAACwB,YAAY,GAAG,UAAUC,GAAG,EAAEC,UAAU,EAAEC,MAAM,EAAE;IACnE,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIC,QAAQ,GAAG,IAAI,CAACR,oBAAoB,CAACS,GAAG,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC;IAAE,CAAC,CAAC;IACxEF,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACf,IAAIG,GAAG,GAAGR,GAAG,CAACS,OAAO,CAAC,CAAC;IACvB,IAAIC,SAAS,GAAGT,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAIU,cAAc,GAAG,CAAC;IACtB,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAIG,SAAS,GAAGF,GAAG,EAAED,CAAC,EAAE,EAAE;MAC3C,IAAIK,SAAS,GAAGlB,UAAU,CAACmB,WAAW,CAACb,GAAG,EAAEK,QAAQ,EAAEK,SAAS,EAAEhB,UAAU,CAACoB,gBAAgB,CAAC;MAC7FZ,MAAM,IAAIa,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGL,SAAS,GAAG,EAAG,CAAC;MACnE,IAAI;QACA,KAAK,IAAIM,UAAU,IAAIf,GAAG,GAAG,KAAK,CAAC,EAAE1B,QAAQ,CAAC4B,QAAQ,CAAC,CAAC,EAAEc,YAAY,GAAGD,UAAU,CAAChC,IAAI,CAAC,CAAC,EAAE,CAACiC,YAAY,CAAC/B,IAAI,EAAE+B,YAAY,GAAGD,UAAU,CAAChC,IAAI,CAAC,CAAC,EAAE;UAC9I,IAAIkC,OAAO,GAAGD,YAAY,CAAChC,KAAK;UAChCuB,SAAS,IAAIU,OAAO;QACxB;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAElB,GAAG,GAAG;UAAEmB,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,YAAY,IAAI,CAACA,YAAY,CAAC/B,IAAI,KAAKgB,EAAE,GAAGc,UAAU,CAACK,MAAM,CAAC,EAAEnB,EAAE,CAACpB,IAAI,CAACkC,UAAU,CAAC;QAC3F,CAAC,SACO;UAAE,IAAIf,GAAG,EAAE,MAAMA,GAAG,CAACmB,KAAK;QAAE;MACxC;MACA,IAAIV,SAAS,IAAI,EAAE,EAAE;QACjBD,cAAc,IAAI,CAAC,IAAK,CAAC,GAAGJ,CAAE;MAClC;IACJ;IACAb,UAAU,CAAC8B,4BAA4B,CAAC,IAAIjC,aAAa,CAACW,MAAM,CAAC,EAAES,cAAc,CAAC;IAClF,OAAOD,SAAS;EACpB,CAAC;EACD;AACJ;AACA;EACI;EACAhB,UAAU,CAACnB,SAAS,CAACkD,SAAS,GAAG,UAAUzB,GAAG,EAAE0B,QAAQ,EAAE;IACtD,OAAOhC,UAAU,CAACiC,+BAA+B,CAAC3B,GAAG,EAAE0B,QAAQ,EAAE,IAAI,EAAEhC,UAAU,CAACkC,kBAAkB,CAAC;EACzG,CAAC;EACD;AACJ;AACA;EACI;EACAlC,UAAU,CAACnB,SAAS,CAACsD,aAAa,GAAG,UAAUlD,CAAC,EAAE;IAC9C,OAAOW,YAAY,CAACuC,aAAa,CAACnC,UAAU,CAACoC,iBAAiB,CAACnD,CAAC,CAAC,CAAC;EACtE,CAAC;EACD;AACJ;AACA;EACIe,UAAU,CAAC8B,4BAA4B,GAAG,UAAUO,YAAY,EAAEpB,cAAc,EAAE;IAC9E,KAAK,IAAIqB,MAAM,GAAG,CAAC,EAAEA,MAAM,IAAI,CAAC,EAAEA,MAAM,EAAE,EAAE;MACxC,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACzB,IAAI8C,cAAc,KAAK,IAAI,CAACsB,+BAA+B,CAACD,MAAM,CAAC,CAACnE,CAAC,CAAC,EAAE;UACpEkE,YAAY,CAACG,MAAM,CAAC,CAAC,EAAE,UAAY,GAAG,GAAGF,MAAO,CAAC;UACjDD,YAAY,CAACI,MAAM,CAAC,UAAY,GAAG,GAAGtE,CAAE,CAAC;UACzC;QACJ;MACJ;IACJ;IACA,MAAM2B,iBAAiB,CAAC4C,mBAAmB,CAAC,CAAC;EACjD,CAAC;EACD;EACA1C,UAAU,CAACnB,SAAS,CAAC8D,gBAAgB,GAAG,YAAY;IAChD,OAAO5C,aAAa,CAAC6C,KAAK;EAC9B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI5C,UAAU,CAACoC,iBAAiB,GAAG,UAAUS,IAAI,EAAE;IAC3C;IACA,IAAIC,SAAS,GAAGD,IAAI,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,KAAK,CAAC,EAAE,CAAC,CAACpC,GAAG,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACU,UAAU,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;IACxF,IAAIf,MAAM,GAAG,IAAIX,aAAa,CAAE,OAAO;IACvCW,MAAM,CAACiC,MAAM,CAACI,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7B,IAAIC,QAAQ,GAAGJ,SAAS,CAAC,CAAC,CAAC;IAC3B,QAAQI,QAAQ;MACZ,KAAK,CAAC;MACN,KAAK,CAAC;MACN,KAAK,CAAC;QACF1C,MAAM,CAAC2C,WAAW,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACnCtC,MAAM,CAACiC,MAAM,CAACS,QAAQ,CAAC;QACvB1C,MAAM,CAACiC,MAAM,CAAC,MAAM,CAAC;QACrBjC,MAAM,CAAC2C,WAAW,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACnC;MACJ,KAAK,CAAC;QACFtC,MAAM,CAAC2C,WAAW,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACnCtC,MAAM,CAACiC,MAAM,CAAC,OAAO,CAAC;QACtBjC,MAAM,CAAC2C,WAAW,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACnC;MACJ,KAAK,CAAC;QACFtC,MAAM,CAAC2C,WAAW,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACnCtC,MAAM,CAACiC,MAAM,CAAC,OAAO,CAAC;QACtBjC,MAAM,CAACiC,MAAM,CAACK,SAAS,CAAC,CAAC,CAAC,CAAC;QAC3B;MACJ;QACItC,MAAM,CAAC2C,WAAW,CAACL,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;QACnCtC,MAAM,CAACiC,MAAM,CAAC,MAAM,CAAC;QACrBjC,MAAM,CAACiC,MAAM,CAACS,QAAQ,CAAC;QACvB;IACR;IACA;IACA,IAAIL,IAAI,CAACtD,MAAM,IAAI,CAAC,EAAE;MAClBiB,MAAM,CAACiC,MAAM,CAACI,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;IACjC;IACA,OAAOzC,MAAM,CAAC4C,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;EACIpD,UAAU,CAACkC,kBAAkB,GAAG9B,UAAU,CAACiD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;EACIrD,UAAU,CAACuC,+BAA+B,GAAG,CACzCnC,UAAU,CAACiD,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAC7EjD,UAAU,CAACiD,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,CAC/E;EACD,OAAOrD,UAAU;AACrB,CAAC,CAACJ,YAAY,CAAE;AAChB,eAAeI,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}