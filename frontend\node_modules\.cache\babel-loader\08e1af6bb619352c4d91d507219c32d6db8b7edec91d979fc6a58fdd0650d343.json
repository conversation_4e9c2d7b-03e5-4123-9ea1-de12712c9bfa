{"ast": null, "code": "var DataCharacter = /** @class */function () {\n  function DataCharacter(value, checksumPortion) {\n    this.value = value;\n    this.checksumPortion = checksumPortion;\n  }\n  DataCharacter.prototype.getValue = function () {\n    return this.value;\n  };\n  DataCharacter.prototype.getChecksumPortion = function () {\n    return this.checksumPortion;\n  };\n  DataCharacter.prototype.toString = function () {\n    return this.value + '(' + this.checksumPortion + ')';\n  };\n  DataCharacter.prototype.equals = function (o) {\n    if (!(o instanceof DataCharacter)) {\n      return false;\n    }\n    var that = o;\n    return this.value === that.value && this.checksumPortion === that.checksumPortion;\n  };\n  DataCharacter.prototype.hashCode = function () {\n    return this.value ^ this.checksumPortion;\n  };\n  return DataCharacter;\n}();\nexport default DataCharacter;", "map": {"version": 3, "names": ["DataCharacter", "value", "checksumPortion", "prototype", "getValue", "getChecksumPortion", "toString", "equals", "o", "that", "hashCode"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/DataCharacter.js"], "sourcesContent": ["var DataCharacter = /** @class */ (function () {\n    function DataCharacter(value, checksumPortion) {\n        this.value = value;\n        this.checksumPortion = checksumPortion;\n    }\n    DataCharacter.prototype.getValue = function () {\n        return this.value;\n    };\n    DataCharacter.prototype.getChecksumPortion = function () {\n        return this.checksumPortion;\n    };\n    DataCharacter.prototype.toString = function () {\n        return this.value + '(' + this.checksumPortion + ')';\n    };\n    DataCharacter.prototype.equals = function (o) {\n        if (!(o instanceof DataCharacter)) {\n            return false;\n        }\n        var that = o;\n        return this.value === that.value && this.checksumPortion === that.checksumPortion;\n    };\n    DataCharacter.prototype.hashCode = function () {\n        return this.value ^ this.checksumPortion;\n    };\n    return DataCharacter;\n}());\nexport default DataCharacter;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAG,aAAe,YAAY;EAC3C,SAASA,aAAaA,CAACC,KAAK,EAAEC,eAAe,EAAE;IAC3C,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACAF,aAAa,CAACG,SAAS,CAACC,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACH,KAAK;EACrB,CAAC;EACDD,aAAa,CAACG,SAAS,CAACE,kBAAkB,GAAG,YAAY;IACrD,OAAO,IAAI,CAACH,eAAe;EAC/B,CAAC;EACDF,aAAa,CAACG,SAAS,CAACG,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACL,KAAK,GAAG,GAAG,GAAG,IAAI,CAACC,eAAe,GAAG,GAAG;EACxD,CAAC;EACDF,aAAa,CAACG,SAAS,CAACI,MAAM,GAAG,UAAUC,CAAC,EAAE;IAC1C,IAAI,EAAEA,CAAC,YAAYR,aAAa,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAIS,IAAI,GAAGD,CAAC;IACZ,OAAO,IAAI,CAACP,KAAK,KAAKQ,IAAI,CAACR,KAAK,IAAI,IAAI,CAACC,eAAe,KAAKO,IAAI,CAACP,eAAe;EACrF,CAAC;EACDF,aAAa,CAACG,SAAS,CAACO,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACT,KAAK,GAAG,IAAI,CAACC,eAAe;EAC5C,CAAC;EACD,OAAOF,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}