{"ast": null, "code": "import * as React from 'react';\nimport { LocalizationProvider } from \"../../LocalizationProvider/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const PickersContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Provides the context for the various parts of a picker component:\n * - contextValue: the context for the picker sub-components.\n * - localizationProvider: the translations passed through the props and through a parent LocalizationProvider.\n *\n * @ignore - do not document.\n */\nexport function PickersProvider(props) {\n  const {\n    contextValue,\n    localeText,\n    children\n  } = props;\n  return /*#__PURE__*/_jsx(PickersContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(LocalizationProvider, {\n      localeText: localeText,\n      children: children\n    })\n  });\n}", "map": {"version": 3, "names": ["React", "LocalizationProvider", "jsx", "_jsx", "PickersContext", "createContext", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "contextValue", "localeText", "children", "Provider", "value"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/components/PickersProvider.js"], "sourcesContent": ["import * as React from 'react';\nimport { LocalizationProvider } from \"../../LocalizationProvider/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const PickersContext = /*#__PURE__*/React.createContext(null);\n\n/**\n * Provides the context for the various parts of a picker component:\n * - contextValue: the context for the picker sub-components.\n * - localizationProvider: the translations passed through the props and through a parent LocalizationProvider.\n *\n * @ignore - do not document.\n */\nexport function PickersProvider(props) {\n  const {\n    contextValue,\n    localeText,\n    children\n  } = props;\n  return /*#__PURE__*/_jsx(PickersContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(LocalizationProvider, {\n      localeText: localeText,\n      children: children\n    })\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,cAAc,GAAG,aAAaJ,KAAK,CAACK,aAAa,CAAC,IAAI,CAAC;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,MAAM;IACJC,YAAY;IACZC,UAAU;IACVC;EACF,CAAC,GAAGH,KAAK;EACT,OAAO,aAAaJ,IAAI,CAACC,cAAc,CAACO,QAAQ,EAAE;IAChDC,KAAK,EAAEJ,YAAY;IACnBE,QAAQ,EAAE,aAAaP,IAAI,CAACF,oBAAoB,EAAE;MAChDQ,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}