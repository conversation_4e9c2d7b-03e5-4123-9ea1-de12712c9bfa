{"ast": null, "code": "import Arrays from '../../util/Arrays';\n/**\n * Symbol Character Placement Program. Adapted from Annex M.1 in ISO/IEC 16022:2000(E).\n */\nvar DefaultPlacement = /** @class */function () {\n  /**\n   * Main constructor\n   *\n   * @param codewords the codewords to place\n   * @param numcols   the number of columns\n   * @param numrows   the number of rows\n   */\n  function DefaultPlacement(codewords, numcols, numrows) {\n    this.codewords = codewords;\n    this.numcols = numcols;\n    this.numrows = numrows;\n    this.bits = new Uint8Array(numcols * numrows);\n    Arrays.fill(this.bits, 2); // Initialize with \"not set\" value\n  }\n  DefaultPlacement.prototype.getNumrows = function () {\n    return this.numrows;\n  };\n  DefaultPlacement.prototype.getNumcols = function () {\n    return this.numcols;\n  };\n  DefaultPlacement.prototype.getBits = function () {\n    return this.bits;\n  };\n  DefaultPlacement.prototype.getBit = function (col, row) {\n    return this.bits[row * this.numcols + col] === 1;\n  };\n  DefaultPlacement.prototype.setBit = function (col, row, bit) {\n    this.bits[row * this.numcols + col] = bit ? 1 : 0;\n  };\n  DefaultPlacement.prototype.noBit = function (col, row) {\n    return this.bits[row * this.numcols + col] === 2;\n  };\n  DefaultPlacement.prototype.place = function () {\n    var pos = 0;\n    var row = 4;\n    var col = 0;\n    do {\n      // repeatedly first check for one of the special corner cases, then...\n      if (row === this.numrows && col === 0) {\n        this.corner1(pos++);\n      }\n      if (row === this.numrows - 2 && col === 0 && this.numcols % 4 !== 0) {\n        this.corner2(pos++);\n      }\n      if (row === this.numrows - 2 && col === 0 && this.numcols % 8 === 4) {\n        this.corner3(pos++);\n      }\n      if (row === this.numrows + 4 && col === 2 && this.numcols % 8 === 0) {\n        this.corner4(pos++);\n      }\n      // sweep upward diagonally, inserting successive characters...\n      do {\n        if (row < this.numrows && col >= 0 && this.noBit(col, row)) {\n          this.utah(row, col, pos++);\n        }\n        row -= 2;\n        col += 2;\n      } while (row >= 0 && col < this.numcols);\n      row++;\n      col += 3;\n      // and then sweep downward diagonally, inserting successive characters, ...\n      do {\n        if (row >= 0 && col < this.numcols && this.noBit(col, row)) {\n          this.utah(row, col, pos++);\n        }\n        row += 2;\n        col -= 2;\n      } while (row < this.numrows && col >= 0);\n      row += 3;\n      col++;\n      // ...until the entire array is scanned\n    } while (row < this.numrows || col < this.numcols);\n    // Lastly, if the lower right-hand corner is untouched, fill in fixed pattern\n    if (this.noBit(this.numcols - 1, this.numrows - 1)) {\n      this.setBit(this.numcols - 1, this.numrows - 1, true);\n      this.setBit(this.numcols - 2, this.numrows - 2, true);\n    }\n  };\n  DefaultPlacement.prototype.module = function (row, col, pos, bit) {\n    if (row < 0) {\n      row += this.numrows;\n      col += 4 - (this.numrows + 4) % 8;\n    }\n    if (col < 0) {\n      col += this.numcols;\n      row += 4 - (this.numcols + 4) % 8;\n    }\n    // Note the conversion:\n    var v = this.codewords.charCodeAt(pos);\n    v &= 1 << 8 - bit;\n    this.setBit(col, row, v !== 0);\n  };\n  /**\n   * Places the 8 bits of a utah-shaped symbol character in ECC200.\n   *\n   * @param row the row\n   * @param col the column\n   * @param pos character position\n   */\n  DefaultPlacement.prototype.utah = function (row, col, pos) {\n    this.module(row - 2, col - 2, pos, 1);\n    this.module(row - 2, col - 1, pos, 2);\n    this.module(row - 1, col - 2, pos, 3);\n    this.module(row - 1, col - 1, pos, 4);\n    this.module(row - 1, col, pos, 5);\n    this.module(row, col - 2, pos, 6);\n    this.module(row, col - 1, pos, 7);\n    this.module(row, col, pos, 8);\n  };\n  DefaultPlacement.prototype.corner1 = function (pos) {\n    this.module(this.numrows - 1, 0, pos, 1);\n    this.module(this.numrows - 1, 1, pos, 2);\n    this.module(this.numrows - 1, 2, pos, 3);\n    this.module(0, this.numcols - 2, pos, 4);\n    this.module(0, this.numcols - 1, pos, 5);\n    this.module(1, this.numcols - 1, pos, 6);\n    this.module(2, this.numcols - 1, pos, 7);\n    this.module(3, this.numcols - 1, pos, 8);\n  };\n  DefaultPlacement.prototype.corner2 = function (pos) {\n    this.module(this.numrows - 3, 0, pos, 1);\n    this.module(this.numrows - 2, 0, pos, 2);\n    this.module(this.numrows - 1, 0, pos, 3);\n    this.module(0, this.numcols - 4, pos, 4);\n    this.module(0, this.numcols - 3, pos, 5);\n    this.module(0, this.numcols - 2, pos, 6);\n    this.module(0, this.numcols - 1, pos, 7);\n    this.module(1, this.numcols - 1, pos, 8);\n  };\n  DefaultPlacement.prototype.corner3 = function (pos) {\n    this.module(this.numrows - 3, 0, pos, 1);\n    this.module(this.numrows - 2, 0, pos, 2);\n    this.module(this.numrows - 1, 0, pos, 3);\n    this.module(0, this.numcols - 2, pos, 4);\n    this.module(0, this.numcols - 1, pos, 5);\n    this.module(1, this.numcols - 1, pos, 6);\n    this.module(2, this.numcols - 1, pos, 7);\n    this.module(3, this.numcols - 1, pos, 8);\n  };\n  DefaultPlacement.prototype.corner4 = function (pos) {\n    this.module(this.numrows - 1, 0, pos, 1);\n    this.module(this.numrows - 1, this.numcols - 1, pos, 2);\n    this.module(0, this.numcols - 3, pos, 3);\n    this.module(0, this.numcols - 2, pos, 4);\n    this.module(0, this.numcols - 1, pos, 5);\n    this.module(1, this.numcols - 3, pos, 6);\n    this.module(1, this.numcols - 2, pos, 7);\n    this.module(1, this.numcols - 1, pos, 8);\n  };\n  return DefaultPlacement;\n}();\nexport default DefaultPlacement;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "DefaultPlacement", "codewords", "numcols", "numrows", "bits", "Uint8Array", "fill", "prototype", "getNumrows", "getNumcols", "getBits", "getBit", "col", "row", "setBit", "bit", "noBit", "place", "pos", "corner1", "corner2", "corner3", "corner4", "utah", "module", "v", "charCodeAt"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/DefaultPlacement.js"], "sourcesContent": ["import Arrays from '../../util/Arrays';\n/**\n * Symbol Character Placement Program. Adapted from Annex M.1 in ISO/IEC 16022:2000(E).\n */\nvar DefaultPlacement = /** @class */ (function () {\n    /**\n     * Main constructor\n     *\n     * @param codewords the codewords to place\n     * @param numcols   the number of columns\n     * @param numrows   the number of rows\n     */\n    function DefaultPlacement(codewords, numcols, numrows) {\n        this.codewords = codewords;\n        this.numcols = numcols;\n        this.numrows = numrows;\n        this.bits = new Uint8Array(numcols * numrows);\n        Arrays.fill(this.bits, 2); // Initialize with \"not set\" value\n    }\n    DefaultPlacement.prototype.getNumrows = function () {\n        return this.numrows;\n    };\n    DefaultPlacement.prototype.getNumcols = function () {\n        return this.numcols;\n    };\n    DefaultPlacement.prototype.getBits = function () {\n        return this.bits;\n    };\n    DefaultPlacement.prototype.getBit = function (col, row) {\n        return this.bits[row * this.numcols + col] === 1;\n    };\n    DefaultPlacement.prototype.setBit = function (col, row, bit) {\n        this.bits[row * this.numcols + col] = bit ? 1 : 0;\n    };\n    DefaultPlacement.prototype.noBit = function (col, row) {\n        return this.bits[row * this.numcols + col] === 2;\n    };\n    DefaultPlacement.prototype.place = function () {\n        var pos = 0;\n        var row = 4;\n        var col = 0;\n        do {\n            // repeatedly first check for one of the special corner cases, then...\n            if (row === this.numrows && col === 0) {\n                this.corner1(pos++);\n            }\n            if (row === this.numrows - 2 && col === 0 && this.numcols % 4 !== 0) {\n                this.corner2(pos++);\n            }\n            if (row === this.numrows - 2 && col === 0 && this.numcols % 8 === 4) {\n                this.corner3(pos++);\n            }\n            if (row === this.numrows + 4 && col === 2 && this.numcols % 8 === 0) {\n                this.corner4(pos++);\n            }\n            // sweep upward diagonally, inserting successive characters...\n            do {\n                if (row < this.numrows && col >= 0 && this.noBit(col, row)) {\n                    this.utah(row, col, pos++);\n                }\n                row -= 2;\n                col += 2;\n            } while (row >= 0 && col < this.numcols);\n            row++;\n            col += 3;\n            // and then sweep downward diagonally, inserting successive characters, ...\n            do {\n                if (row >= 0 && col < this.numcols && this.noBit(col, row)) {\n                    this.utah(row, col, pos++);\n                }\n                row += 2;\n                col -= 2;\n            } while (row < this.numrows && col >= 0);\n            row += 3;\n            col++;\n            // ...until the entire array is scanned\n        } while (row < this.numrows || col < this.numcols);\n        // Lastly, if the lower right-hand corner is untouched, fill in fixed pattern\n        if (this.noBit(this.numcols - 1, this.numrows - 1)) {\n            this.setBit(this.numcols - 1, this.numrows - 1, true);\n            this.setBit(this.numcols - 2, this.numrows - 2, true);\n        }\n    };\n    DefaultPlacement.prototype.module = function (row, col, pos, bit) {\n        if (row < 0) {\n            row += this.numrows;\n            col += 4 - ((this.numrows + 4) % 8);\n        }\n        if (col < 0) {\n            col += this.numcols;\n            row += 4 - ((this.numcols + 4) % 8);\n        }\n        // Note the conversion:\n        var v = this.codewords.charCodeAt(pos);\n        v &= 1 << (8 - bit);\n        this.setBit(col, row, v !== 0);\n    };\n    /**\n     * Places the 8 bits of a utah-shaped symbol character in ECC200.\n     *\n     * @param row the row\n     * @param col the column\n     * @param pos character position\n     */\n    DefaultPlacement.prototype.utah = function (row, col, pos) {\n        this.module(row - 2, col - 2, pos, 1);\n        this.module(row - 2, col - 1, pos, 2);\n        this.module(row - 1, col - 2, pos, 3);\n        this.module(row - 1, col - 1, pos, 4);\n        this.module(row - 1, col, pos, 5);\n        this.module(row, col - 2, pos, 6);\n        this.module(row, col - 1, pos, 7);\n        this.module(row, col, pos, 8);\n    };\n    DefaultPlacement.prototype.corner1 = function (pos) {\n        this.module(this.numrows - 1, 0, pos, 1);\n        this.module(this.numrows - 1, 1, pos, 2);\n        this.module(this.numrows - 1, 2, pos, 3);\n        this.module(0, this.numcols - 2, pos, 4);\n        this.module(0, this.numcols - 1, pos, 5);\n        this.module(1, this.numcols - 1, pos, 6);\n        this.module(2, this.numcols - 1, pos, 7);\n        this.module(3, this.numcols - 1, pos, 8);\n    };\n    DefaultPlacement.prototype.corner2 = function (pos) {\n        this.module(this.numrows - 3, 0, pos, 1);\n        this.module(this.numrows - 2, 0, pos, 2);\n        this.module(this.numrows - 1, 0, pos, 3);\n        this.module(0, this.numcols - 4, pos, 4);\n        this.module(0, this.numcols - 3, pos, 5);\n        this.module(0, this.numcols - 2, pos, 6);\n        this.module(0, this.numcols - 1, pos, 7);\n        this.module(1, this.numcols - 1, pos, 8);\n    };\n    DefaultPlacement.prototype.corner3 = function (pos) {\n        this.module(this.numrows - 3, 0, pos, 1);\n        this.module(this.numrows - 2, 0, pos, 2);\n        this.module(this.numrows - 1, 0, pos, 3);\n        this.module(0, this.numcols - 2, pos, 4);\n        this.module(0, this.numcols - 1, pos, 5);\n        this.module(1, this.numcols - 1, pos, 6);\n        this.module(2, this.numcols - 1, pos, 7);\n        this.module(3, this.numcols - 1, pos, 8);\n    };\n    DefaultPlacement.prototype.corner4 = function (pos) {\n        this.module(this.numrows - 1, 0, pos, 1);\n        this.module(this.numrows - 1, this.numcols - 1, pos, 2);\n        this.module(0, this.numcols - 3, pos, 3);\n        this.module(0, this.numcols - 2, pos, 4);\n        this.module(0, this.numcols - 1, pos, 5);\n        this.module(1, this.numcols - 3, pos, 6);\n        this.module(1, this.numcols - 2, pos, 7);\n        this.module(1, this.numcols - 1, pos, 8);\n    };\n    return DefaultPlacement;\n}());\nexport default DefaultPlacement;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AACtC;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAe,YAAY;EAC9C;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;IACnD,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAG,IAAIC,UAAU,CAACH,OAAO,GAAGC,OAAO,CAAC;IAC7CJ,MAAM,CAACO,IAAI,CAAC,IAAI,CAACF,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;EAC/B;EACAJ,gBAAgB,CAACO,SAAS,CAACC,UAAU,GAAG,YAAY;IAChD,OAAO,IAAI,CAACL,OAAO;EACvB,CAAC;EACDH,gBAAgB,CAACO,SAAS,CAACE,UAAU,GAAG,YAAY;IAChD,OAAO,IAAI,CAACP,OAAO;EACvB,CAAC;EACDF,gBAAgB,CAACO,SAAS,CAACG,OAAO,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACN,IAAI;EACpB,CAAC;EACDJ,gBAAgB,CAACO,SAAS,CAACI,MAAM,GAAG,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACpD,OAAO,IAAI,CAACT,IAAI,CAACS,GAAG,GAAG,IAAI,CAACX,OAAO,GAAGU,GAAG,CAAC,KAAK,CAAC;EACpD,CAAC;EACDZ,gBAAgB,CAACO,SAAS,CAACO,MAAM,GAAG,UAAUF,GAAG,EAAEC,GAAG,EAAEE,GAAG,EAAE;IACzD,IAAI,CAACX,IAAI,CAACS,GAAG,GAAG,IAAI,CAACX,OAAO,GAAGU,GAAG,CAAC,GAAGG,GAAG,GAAG,CAAC,GAAG,CAAC;EACrD,CAAC;EACDf,gBAAgB,CAACO,SAAS,CAACS,KAAK,GAAG,UAAUJ,GAAG,EAAEC,GAAG,EAAE;IACnD,OAAO,IAAI,CAACT,IAAI,CAACS,GAAG,GAAG,IAAI,CAACX,OAAO,GAAGU,GAAG,CAAC,KAAK,CAAC;EACpD,CAAC;EACDZ,gBAAgB,CAACO,SAAS,CAACU,KAAK,GAAG,YAAY;IAC3C,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIL,GAAG,GAAG,CAAC;IACX,IAAID,GAAG,GAAG,CAAC;IACX,GAAG;MACC;MACA,IAAIC,GAAG,KAAK,IAAI,CAACV,OAAO,IAAIS,GAAG,KAAK,CAAC,EAAE;QACnC,IAAI,CAACO,OAAO,CAACD,GAAG,EAAE,CAAC;MACvB;MACA,IAAIL,GAAG,KAAK,IAAI,CAACV,OAAO,GAAG,CAAC,IAAIS,GAAG,KAAK,CAAC,IAAI,IAAI,CAACV,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;QACjE,IAAI,CAACkB,OAAO,CAACF,GAAG,EAAE,CAAC;MACvB;MACA,IAAIL,GAAG,KAAK,IAAI,CAACV,OAAO,GAAG,CAAC,IAAIS,GAAG,KAAK,CAAC,IAAI,IAAI,CAACV,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;QACjE,IAAI,CAACmB,OAAO,CAACH,GAAG,EAAE,CAAC;MACvB;MACA,IAAIL,GAAG,KAAK,IAAI,CAACV,OAAO,GAAG,CAAC,IAAIS,GAAG,KAAK,CAAC,IAAI,IAAI,CAACV,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE;QACjE,IAAI,CAACoB,OAAO,CAACJ,GAAG,EAAE,CAAC;MACvB;MACA;MACA,GAAG;QACC,IAAIL,GAAG,GAAG,IAAI,CAACV,OAAO,IAAIS,GAAG,IAAI,CAAC,IAAI,IAAI,CAACI,KAAK,CAACJ,GAAG,EAAEC,GAAG,CAAC,EAAE;UACxD,IAAI,CAACU,IAAI,CAACV,GAAG,EAAED,GAAG,EAAEM,GAAG,EAAE,CAAC;QAC9B;QACAL,GAAG,IAAI,CAAC;QACRD,GAAG,IAAI,CAAC;MACZ,CAAC,QAAQC,GAAG,IAAI,CAAC,IAAID,GAAG,GAAG,IAAI,CAACV,OAAO;MACvCW,GAAG,EAAE;MACLD,GAAG,IAAI,CAAC;MACR;MACA,GAAG;QACC,IAAIC,GAAG,IAAI,CAAC,IAAID,GAAG,GAAG,IAAI,CAACV,OAAO,IAAI,IAAI,CAACc,KAAK,CAACJ,GAAG,EAAEC,GAAG,CAAC,EAAE;UACxD,IAAI,CAACU,IAAI,CAACV,GAAG,EAAED,GAAG,EAAEM,GAAG,EAAE,CAAC;QAC9B;QACAL,GAAG,IAAI,CAAC;QACRD,GAAG,IAAI,CAAC;MACZ,CAAC,QAAQC,GAAG,GAAG,IAAI,CAACV,OAAO,IAAIS,GAAG,IAAI,CAAC;MACvCC,GAAG,IAAI,CAAC;MACRD,GAAG,EAAE;MACL;IACJ,CAAC,QAAQC,GAAG,GAAG,IAAI,CAACV,OAAO,IAAIS,GAAG,GAAG,IAAI,CAACV,OAAO;IACjD;IACA,IAAI,IAAI,CAACc,KAAK,CAAC,IAAI,CAACd,OAAO,GAAG,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;MAChD,IAAI,CAACW,MAAM,CAAC,IAAI,CAACZ,OAAO,GAAG,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC;MACrD,IAAI,CAACW,MAAM,CAAC,IAAI,CAACZ,OAAO,GAAG,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC;IACzD;EACJ,CAAC;EACDH,gBAAgB,CAACO,SAAS,CAACiB,MAAM,GAAG,UAAUX,GAAG,EAAED,GAAG,EAAEM,GAAG,EAAEH,GAAG,EAAE;IAC9D,IAAIF,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,IAAI,IAAI,CAACV,OAAO;MACnBS,GAAG,IAAI,CAAC,GAAI,CAAC,IAAI,CAACT,OAAO,GAAG,CAAC,IAAI,CAAE;IACvC;IACA,IAAIS,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,IAAI,IAAI,CAACV,OAAO;MACnBW,GAAG,IAAI,CAAC,GAAI,CAAC,IAAI,CAACX,OAAO,GAAG,CAAC,IAAI,CAAE;IACvC;IACA;IACA,IAAIuB,CAAC,GAAG,IAAI,CAACxB,SAAS,CAACyB,UAAU,CAACR,GAAG,CAAC;IACtCO,CAAC,IAAI,CAAC,IAAK,CAAC,GAAGV,GAAI;IACnB,IAAI,CAACD,MAAM,CAACF,GAAG,EAAEC,GAAG,EAAEY,CAAC,KAAK,CAAC,CAAC;EAClC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIzB,gBAAgB,CAACO,SAAS,CAACgB,IAAI,GAAG,UAAUV,GAAG,EAAED,GAAG,EAAEM,GAAG,EAAE;IACvD,IAAI,CAACM,MAAM,CAACX,GAAG,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,EAAEM,GAAG,EAAE,CAAC,CAAC;IACrC,IAAI,CAACM,MAAM,CAACX,GAAG,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,EAAEM,GAAG,EAAE,CAAC,CAAC;IACrC,IAAI,CAACM,MAAM,CAACX,GAAG,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,EAAEM,GAAG,EAAE,CAAC,CAAC;IACrC,IAAI,CAACM,MAAM,CAACX,GAAG,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,EAAEM,GAAG,EAAE,CAAC,CAAC;IACrC,IAAI,CAACM,MAAM,CAACX,GAAG,GAAG,CAAC,EAAED,GAAG,EAAEM,GAAG,EAAE,CAAC,CAAC;IACjC,IAAI,CAACM,MAAM,CAACX,GAAG,EAAED,GAAG,GAAG,CAAC,EAAEM,GAAG,EAAE,CAAC,CAAC;IACjC,IAAI,CAACM,MAAM,CAACX,GAAG,EAAED,GAAG,GAAG,CAAC,EAAEM,GAAG,EAAE,CAAC,CAAC;IACjC,IAAI,CAACM,MAAM,CAACX,GAAG,EAAED,GAAG,EAAEM,GAAG,EAAE,CAAC,CAAC;EACjC,CAAC;EACDlB,gBAAgB,CAACO,SAAS,CAACY,OAAO,GAAG,UAAUD,GAAG,EAAE;IAChD,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;EAC5C,CAAC;EACDlB,gBAAgB,CAACO,SAAS,CAACa,OAAO,GAAG,UAAUF,GAAG,EAAE;IAChD,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;EAC5C,CAAC;EACDlB,gBAAgB,CAACO,SAAS,CAACc,OAAO,GAAG,UAAUH,GAAG,EAAE;IAChD,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;EAC5C,CAAC;EACDlB,gBAAgB,CAACO,SAAS,CAACe,OAAO,GAAG,UAAUJ,GAAG,EAAE;IAChD,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEe,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,IAAI,CAACrB,OAAO,GAAG,CAAC,EAAE,IAAI,CAACD,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACvD,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;IACxC,IAAI,CAACM,MAAM,CAAC,CAAC,EAAE,IAAI,CAACtB,OAAO,GAAG,CAAC,EAAEgB,GAAG,EAAE,CAAC,CAAC;EAC5C,CAAC;EACD,OAAOlB,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}