{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { applyLocalizedDigits, cleanLeadingZeros, doesSectionFormatHaveLeadingZeros, getDateSectionConfigFromFormatToken, removeLocalizedDigits } from \"./useField.utils.js\";\nconst expandFormat = ({\n  utils,\n  format\n}) => {\n  // Expand the provided format\n  let formatExpansionOverflow = 10;\n  let prevFormat = format;\n  let nextFormat = utils.expandFormat(format);\n  while (nextFormat !== prevFormat) {\n    prevFormat = nextFormat;\n    nextFormat = utils.expandFormat(prevFormat);\n    formatExpansionOverflow -= 1;\n    if (formatExpansionOverflow < 0) {\n      throw new Error('MUI X: The format expansion seems to be in an infinite loop. Please open an issue with the format passed to the picker component.');\n    }\n  }\n  return nextFormat;\n};\nconst getEscapedPartsFromFormat = ({\n  utils,\n  expandedFormat\n}) => {\n  const escapedParts = [];\n  const {\n    start: startChar,\n    end: endChar\n  } = utils.escapedCharacters;\n  const regExp = new RegExp(`(\\\\${startChar}[^\\\\${endChar}]*\\\\${endChar})+`, 'g');\n  let match = null;\n  // eslint-disable-next-line no-cond-assign\n  while (match = regExp.exec(expandedFormat)) {\n    escapedParts.push({\n      start: match.index,\n      end: regExp.lastIndex - 1\n    });\n  }\n  return escapedParts;\n};\nconst getSectionPlaceholder = (utils, localeText, sectionConfig, sectionFormat) => {\n  switch (sectionConfig.type) {\n    case 'year':\n      {\n        return localeText.fieldYearPlaceholder({\n          digitAmount: utils.formatByString(utils.date(undefined, 'default'), sectionFormat).length,\n          format: sectionFormat\n        });\n      }\n    case 'month':\n      {\n        return localeText.fieldMonthPlaceholder({\n          contentType: sectionConfig.contentType,\n          format: sectionFormat\n        });\n      }\n    case 'day':\n      {\n        return localeText.fieldDayPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'weekDay':\n      {\n        return localeText.fieldWeekDayPlaceholder({\n          contentType: sectionConfig.contentType,\n          format: sectionFormat\n        });\n      }\n    case 'hours':\n      {\n        return localeText.fieldHoursPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'minutes':\n      {\n        return localeText.fieldMinutesPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'seconds':\n      {\n        return localeText.fieldSecondsPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'meridiem':\n      {\n        return localeText.fieldMeridiemPlaceholder({\n          format: sectionFormat\n        });\n      }\n    default:\n      {\n        return sectionFormat;\n      }\n  }\n};\nconst createSection = ({\n  utils,\n  date,\n  shouldRespectLeadingZeros,\n  localeText,\n  localizedDigits,\n  now,\n  token,\n  startSeparator\n}) => {\n  if (token === '') {\n    throw new Error('MUI X: Should not call `commitToken` with an empty token');\n  }\n  const sectionConfig = getDateSectionConfigFromFormatToken(utils, token);\n  const hasLeadingZerosInFormat = doesSectionFormatHaveLeadingZeros(utils, sectionConfig.contentType, sectionConfig.type, token);\n  const hasLeadingZerosInInput = shouldRespectLeadingZeros ? hasLeadingZerosInFormat : sectionConfig.contentType === 'digit';\n  const isValidDate = date != null && utils.isValid(date);\n  let sectionValue = isValidDate ? utils.formatByString(date, token) : '';\n  let maxLength = null;\n  if (hasLeadingZerosInInput) {\n    if (hasLeadingZerosInFormat) {\n      maxLength = sectionValue === '' ? utils.formatByString(now, token).length : sectionValue.length;\n    } else {\n      if (sectionConfig.maxLength == null) {\n        throw new Error(`MUI X: The token ${token} should have a 'maxDigitNumber' property on it's adapter`);\n      }\n      maxLength = sectionConfig.maxLength;\n      if (isValidDate) {\n        sectionValue = applyLocalizedDigits(cleanLeadingZeros(removeLocalizedDigits(sectionValue, localizedDigits), maxLength), localizedDigits);\n      }\n    }\n  }\n  return _extends({}, sectionConfig, {\n    format: token,\n    maxLength,\n    value: sectionValue,\n    placeholder: getSectionPlaceholder(utils, localeText, sectionConfig, token),\n    hasLeadingZerosInFormat,\n    hasLeadingZerosInInput,\n    startSeparator,\n    endSeparator: '',\n    modified: false\n  });\n};\nconst buildSections = params => {\n  const {\n    utils,\n    expandedFormat,\n    escapedParts\n  } = params;\n  const now = utils.date(undefined);\n  const sections = [];\n  let startSeparator = '';\n\n  // This RegExp tests if the beginning of a string corresponds to a supported token\n  const validTokens = Object.keys(utils.formatTokenMap).sort((a, b) => b.length - a.length); // Sort to put longest word first\n\n  const regExpFirstWordInFormat = /^([a-zA-Z]+)/;\n  const regExpWordOnlyComposedOfTokens = new RegExp(`^(${validTokens.join('|')})*$`);\n  const regExpFirstTokenInWord = new RegExp(`^(${validTokens.join('|')})`);\n  const getEscapedPartOfCurrentChar = i => escapedParts.find(escapeIndex => escapeIndex.start <= i && escapeIndex.end >= i);\n  let i = 0;\n  while (i < expandedFormat.length) {\n    const escapedPartOfCurrentChar = getEscapedPartOfCurrentChar(i);\n    const isEscapedChar = escapedPartOfCurrentChar != null;\n    const firstWordInFormat = regExpFirstWordInFormat.exec(expandedFormat.slice(i))?.[1];\n\n    // The first word in the format is only composed of tokens.\n    // We extract those tokens to create a new sections.\n    if (!isEscapedChar && firstWordInFormat != null && regExpWordOnlyComposedOfTokens.test(firstWordInFormat)) {\n      let word = firstWordInFormat;\n      while (word.length > 0) {\n        const firstWord = regExpFirstTokenInWord.exec(word)[1];\n        word = word.slice(firstWord.length);\n        sections.push(createSection(_extends({}, params, {\n          now,\n          token: firstWord,\n          startSeparator\n        })));\n        startSeparator = '';\n      }\n      i += firstWordInFormat.length;\n    }\n    // The remaining format does not start with a token,\n    // We take the first character and add it to the current section's end separator.\n    else {\n      const char = expandedFormat[i];\n\n      // If we are on the opening or closing character of an escaped part of the format,\n      // Then we ignore this character.\n      const isEscapeBoundary = isEscapedChar && escapedPartOfCurrentChar?.start === i || escapedPartOfCurrentChar?.end === i;\n      if (!isEscapeBoundary) {\n        if (sections.length === 0) {\n          startSeparator += char;\n        } else {\n          sections[sections.length - 1].endSeparator += char;\n        }\n      }\n      i += 1;\n    }\n  }\n  if (sections.length === 0 && startSeparator.length > 0) {\n    sections.push({\n      type: 'empty',\n      contentType: 'letter',\n      maxLength: null,\n      format: '',\n      value: '',\n      placeholder: '',\n      hasLeadingZerosInFormat: false,\n      hasLeadingZerosInInput: false,\n      startSeparator,\n      endSeparator: '',\n      modified: false\n    });\n  }\n  return sections;\n};\nconst postProcessSections = ({\n  isRtl,\n  formatDensity,\n  sections\n}) => {\n  return sections.map(section => {\n    const cleanSeparator = separator => {\n      let cleanedSeparator = separator;\n      if (isRtl && cleanedSeparator !== null && cleanedSeparator.includes(' ')) {\n        cleanedSeparator = `\\u2069${cleanedSeparator}\\u2066`;\n      }\n      if (formatDensity === 'spacious' && ['/', '.', '-'].includes(cleanedSeparator)) {\n        cleanedSeparator = ` ${cleanedSeparator} `;\n      }\n      return cleanedSeparator;\n    };\n    section.startSeparator = cleanSeparator(section.startSeparator);\n    section.endSeparator = cleanSeparator(section.endSeparator);\n    return section;\n  });\n};\nexport const buildSectionsFromFormat = params => {\n  let expandedFormat = expandFormat(params);\n  if (params.isRtl && params.enableAccessibleFieldDOMStructure) {\n    expandedFormat = expandedFormat.split(' ').reverse().join(' ');\n  }\n  const escapedParts = getEscapedPartsFromFormat(_extends({}, params, {\n    expandedFormat\n  }));\n  const sections = buildSections(_extends({}, params, {\n    expandedFormat,\n    escapedParts\n  }));\n  return postProcessSections(_extends({}, params, {\n    sections\n  }));\n};", "map": {"version": 3, "names": ["_extends", "applyLocalizedDigits", "cleanLeadingZeros", "doesSectionFormatHaveLeadingZeros", "getDateSectionConfigFromFormatToken", "removeLocalizedDigits", "expandFormat", "utils", "format", "formatExpansionOverflow", "prevFormat", "nextFormat", "Error", "getEscapedPartsFromFormat", "expandedFormat", "escapedParts", "start", "startChar", "end", "endChar", "escapedCharacters", "regExp", "RegExp", "match", "exec", "push", "index", "lastIndex", "getSectionPlaceholder", "localeText", "sectionConfig", "sectionFormat", "type", "fieldYearPlaceholder", "digitAmount", "formatByString", "date", "undefined", "length", "fieldMonthPlaceholder", "contentType", "fieldDayPlaceholder", "fieldWeekDayPlaceholder", "fieldHoursPlaceholder", "fieldMinutesPlaceholder", "fieldSecondsPlaceholder", "fieldMeridiemPlaceholder", "createSection", "shouldRespectLeadingZeros", "localizedDigits", "now", "token", "startSeparator", "hasLeadingZerosInFormat", "hasLeadingZerosInInput", "isValidDate", "<PERSON><PERSON><PERSON><PERSON>", "sectionValue", "max<PERSON><PERSON><PERSON>", "value", "placeholder", "endSeparator", "modified", "buildSections", "params", "sections", "validTokens", "Object", "keys", "formatTokenMap", "sort", "a", "b", "regExpFirstWordInFormat", "regExpWordOnlyComposedOfTokens", "join", "regExpFirstTokenInWord", "getEscapedPartOfCurrentChar", "i", "find", "escapeIndex", "escapedPartOfCurrentChar", "isEscapedChar", "firstWordInFormat", "slice", "test", "word", "firstWord", "char", "isEscapeBoundary", "postProcessSections", "isRtl", "formatDensity", "map", "section", "cleanSeparator", "separator", "cleanedSeparator", "includes", "buildSectionsFromFormat", "enableAccessibleFieldDOMStructure", "split", "reverse"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/buildSectionsFromFormat.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { applyLocalizedDigits, cleanLeadingZeros, doesSectionFormatHaveLeadingZeros, getDateSectionConfigFromFormatToken, removeLocalizedDigits } from \"./useField.utils.js\";\nconst expandFormat = ({\n  utils,\n  format\n}) => {\n  // Expand the provided format\n  let formatExpansionOverflow = 10;\n  let prevFormat = format;\n  let nextFormat = utils.expandFormat(format);\n  while (nextFormat !== prevFormat) {\n    prevFormat = nextFormat;\n    nextFormat = utils.expandFormat(prevFormat);\n    formatExpansionOverflow -= 1;\n    if (formatExpansionOverflow < 0) {\n      throw new Error('MUI X: The format expansion seems to be in an infinite loop. Please open an issue with the format passed to the picker component.');\n    }\n  }\n  return nextFormat;\n};\nconst getEscapedPartsFromFormat = ({\n  utils,\n  expandedFormat\n}) => {\n  const escapedParts = [];\n  const {\n    start: startChar,\n    end: endChar\n  } = utils.escapedCharacters;\n  const regExp = new RegExp(`(\\\\${startChar}[^\\\\${endChar}]*\\\\${endChar})+`, 'g');\n  let match = null;\n  // eslint-disable-next-line no-cond-assign\n  while (match = regExp.exec(expandedFormat)) {\n    escapedParts.push({\n      start: match.index,\n      end: regExp.lastIndex - 1\n    });\n  }\n  return escapedParts;\n};\nconst getSectionPlaceholder = (utils, localeText, sectionConfig, sectionFormat) => {\n  switch (sectionConfig.type) {\n    case 'year':\n      {\n        return localeText.fieldYearPlaceholder({\n          digitAmount: utils.formatByString(utils.date(undefined, 'default'), sectionFormat).length,\n          format: sectionFormat\n        });\n      }\n    case 'month':\n      {\n        return localeText.fieldMonthPlaceholder({\n          contentType: sectionConfig.contentType,\n          format: sectionFormat\n        });\n      }\n    case 'day':\n      {\n        return localeText.fieldDayPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'weekDay':\n      {\n        return localeText.fieldWeekDayPlaceholder({\n          contentType: sectionConfig.contentType,\n          format: sectionFormat\n        });\n      }\n    case 'hours':\n      {\n        return localeText.fieldHoursPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'minutes':\n      {\n        return localeText.fieldMinutesPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'seconds':\n      {\n        return localeText.fieldSecondsPlaceholder({\n          format: sectionFormat\n        });\n      }\n    case 'meridiem':\n      {\n        return localeText.fieldMeridiemPlaceholder({\n          format: sectionFormat\n        });\n      }\n    default:\n      {\n        return sectionFormat;\n      }\n  }\n};\nconst createSection = ({\n  utils,\n  date,\n  shouldRespectLeadingZeros,\n  localeText,\n  localizedDigits,\n  now,\n  token,\n  startSeparator\n}) => {\n  if (token === '') {\n    throw new Error('MUI X: Should not call `commitToken` with an empty token');\n  }\n  const sectionConfig = getDateSectionConfigFromFormatToken(utils, token);\n  const hasLeadingZerosInFormat = doesSectionFormatHaveLeadingZeros(utils, sectionConfig.contentType, sectionConfig.type, token);\n  const hasLeadingZerosInInput = shouldRespectLeadingZeros ? hasLeadingZerosInFormat : sectionConfig.contentType === 'digit';\n  const isValidDate = date != null && utils.isValid(date);\n  let sectionValue = isValidDate ? utils.formatByString(date, token) : '';\n  let maxLength = null;\n  if (hasLeadingZerosInInput) {\n    if (hasLeadingZerosInFormat) {\n      maxLength = sectionValue === '' ? utils.formatByString(now, token).length : sectionValue.length;\n    } else {\n      if (sectionConfig.maxLength == null) {\n        throw new Error(`MUI X: The token ${token} should have a 'maxDigitNumber' property on it's adapter`);\n      }\n      maxLength = sectionConfig.maxLength;\n      if (isValidDate) {\n        sectionValue = applyLocalizedDigits(cleanLeadingZeros(removeLocalizedDigits(sectionValue, localizedDigits), maxLength), localizedDigits);\n      }\n    }\n  }\n  return _extends({}, sectionConfig, {\n    format: token,\n    maxLength,\n    value: sectionValue,\n    placeholder: getSectionPlaceholder(utils, localeText, sectionConfig, token),\n    hasLeadingZerosInFormat,\n    hasLeadingZerosInInput,\n    startSeparator,\n    endSeparator: '',\n    modified: false\n  });\n};\nconst buildSections = params => {\n  const {\n    utils,\n    expandedFormat,\n    escapedParts\n  } = params;\n  const now = utils.date(undefined);\n  const sections = [];\n  let startSeparator = '';\n\n  // This RegExp tests if the beginning of a string corresponds to a supported token\n  const validTokens = Object.keys(utils.formatTokenMap).sort((a, b) => b.length - a.length); // Sort to put longest word first\n\n  const regExpFirstWordInFormat = /^([a-zA-Z]+)/;\n  const regExpWordOnlyComposedOfTokens = new RegExp(`^(${validTokens.join('|')})*$`);\n  const regExpFirstTokenInWord = new RegExp(`^(${validTokens.join('|')})`);\n  const getEscapedPartOfCurrentChar = i => escapedParts.find(escapeIndex => escapeIndex.start <= i && escapeIndex.end >= i);\n  let i = 0;\n  while (i < expandedFormat.length) {\n    const escapedPartOfCurrentChar = getEscapedPartOfCurrentChar(i);\n    const isEscapedChar = escapedPartOfCurrentChar != null;\n    const firstWordInFormat = regExpFirstWordInFormat.exec(expandedFormat.slice(i))?.[1];\n\n    // The first word in the format is only composed of tokens.\n    // We extract those tokens to create a new sections.\n    if (!isEscapedChar && firstWordInFormat != null && regExpWordOnlyComposedOfTokens.test(firstWordInFormat)) {\n      let word = firstWordInFormat;\n      while (word.length > 0) {\n        const firstWord = regExpFirstTokenInWord.exec(word)[1];\n        word = word.slice(firstWord.length);\n        sections.push(createSection(_extends({}, params, {\n          now,\n          token: firstWord,\n          startSeparator\n        })));\n        startSeparator = '';\n      }\n      i += firstWordInFormat.length;\n    }\n    // The remaining format does not start with a token,\n    // We take the first character and add it to the current section's end separator.\n    else {\n      const char = expandedFormat[i];\n\n      // If we are on the opening or closing character of an escaped part of the format,\n      // Then we ignore this character.\n      const isEscapeBoundary = isEscapedChar && escapedPartOfCurrentChar?.start === i || escapedPartOfCurrentChar?.end === i;\n      if (!isEscapeBoundary) {\n        if (sections.length === 0) {\n          startSeparator += char;\n        } else {\n          sections[sections.length - 1].endSeparator += char;\n        }\n      }\n      i += 1;\n    }\n  }\n  if (sections.length === 0 && startSeparator.length > 0) {\n    sections.push({\n      type: 'empty',\n      contentType: 'letter',\n      maxLength: null,\n      format: '',\n      value: '',\n      placeholder: '',\n      hasLeadingZerosInFormat: false,\n      hasLeadingZerosInInput: false,\n      startSeparator,\n      endSeparator: '',\n      modified: false\n    });\n  }\n  return sections;\n};\nconst postProcessSections = ({\n  isRtl,\n  formatDensity,\n  sections\n}) => {\n  return sections.map(section => {\n    const cleanSeparator = separator => {\n      let cleanedSeparator = separator;\n      if (isRtl && cleanedSeparator !== null && cleanedSeparator.includes(' ')) {\n        cleanedSeparator = `\\u2069${cleanedSeparator}\\u2066`;\n      }\n      if (formatDensity === 'spacious' && ['/', '.', '-'].includes(cleanedSeparator)) {\n        cleanedSeparator = ` ${cleanedSeparator} `;\n      }\n      return cleanedSeparator;\n    };\n    section.startSeparator = cleanSeparator(section.startSeparator);\n    section.endSeparator = cleanSeparator(section.endSeparator);\n    return section;\n  });\n};\nexport const buildSectionsFromFormat = params => {\n  let expandedFormat = expandFormat(params);\n  if (params.isRtl && params.enableAccessibleFieldDOMStructure) {\n    expandedFormat = expandedFormat.split(' ').reverse().join(' ');\n  }\n  const escapedParts = getEscapedPartsFromFormat(_extends({}, params, {\n    expandedFormat\n  }));\n  const sections = buildSections(_extends({}, params, {\n    expandedFormat,\n    escapedParts\n  }));\n  return postProcessSections(_extends({}, params, {\n    sections\n  }));\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,oBAAoB,EAAEC,iBAAiB,EAAEC,iCAAiC,EAAEC,mCAAmC,EAAEC,qBAAqB,QAAQ,qBAAqB;AAC5K,MAAMC,YAAY,GAAGA,CAAC;EACpBC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ;EACA,IAAIC,uBAAuB,GAAG,EAAE;EAChC,IAAIC,UAAU,GAAGF,MAAM;EACvB,IAAIG,UAAU,GAAGJ,KAAK,CAACD,YAAY,CAACE,MAAM,CAAC;EAC3C,OAAOG,UAAU,KAAKD,UAAU,EAAE;IAChCA,UAAU,GAAGC,UAAU;IACvBA,UAAU,GAAGJ,KAAK,CAACD,YAAY,CAACI,UAAU,CAAC;IAC3CD,uBAAuB,IAAI,CAAC;IAC5B,IAAIA,uBAAuB,GAAG,CAAC,EAAE;MAC/B,MAAM,IAAIG,KAAK,CAAC,mIAAmI,CAAC;IACtJ;EACF;EACA,OAAOD,UAAU;AACnB,CAAC;AACD,MAAME,yBAAyB,GAAGA,CAAC;EACjCN,KAAK;EACLO;AACF,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAG,EAAE;EACvB,MAAM;IACJC,KAAK,EAAEC,SAAS;IAChBC,GAAG,EAAEC;EACP,CAAC,GAAGZ,KAAK,CAACa,iBAAiB;EAC3B,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAAC,MAAML,SAAS,OAAOE,OAAO,OAAOA,OAAO,IAAI,EAAE,GAAG,CAAC;EAC/E,IAAII,KAAK,GAAG,IAAI;EAChB;EACA,OAAOA,KAAK,GAAGF,MAAM,CAACG,IAAI,CAACV,cAAc,CAAC,EAAE;IAC1CC,YAAY,CAACU,IAAI,CAAC;MAChBT,KAAK,EAAEO,KAAK,CAACG,KAAK;MAClBR,GAAG,EAAEG,MAAM,CAACM,SAAS,GAAG;IAC1B,CAAC,CAAC;EACJ;EACA,OAAOZ,YAAY;AACrB,CAAC;AACD,MAAMa,qBAAqB,GAAGA,CAACrB,KAAK,EAAEsB,UAAU,EAAEC,aAAa,EAAEC,aAAa,KAAK;EACjF,QAAQD,aAAa,CAACE,IAAI;IACxB,KAAK,MAAM;MACT;QACE,OAAOH,UAAU,CAACI,oBAAoB,CAAC;UACrCC,WAAW,EAAE3B,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAAC6B,IAAI,CAACC,SAAS,EAAE,SAAS,CAAC,EAAEN,aAAa,CAAC,CAACO,MAAM;UACzF9B,MAAM,EAAEuB;QACV,CAAC,CAAC;MACJ;IACF,KAAK,OAAO;MACV;QACE,OAAOF,UAAU,CAACU,qBAAqB,CAAC;UACtCC,WAAW,EAAEV,aAAa,CAACU,WAAW;UACtChC,MAAM,EAAEuB;QACV,CAAC,CAAC;MACJ;IACF,KAAK,KAAK;MACR;QACE,OAAOF,UAAU,CAACY,mBAAmB,CAAC;UACpCjC,MAAM,EAAEuB;QACV,CAAC,CAAC;MACJ;IACF,KAAK,SAAS;MACZ;QACE,OAAOF,UAAU,CAACa,uBAAuB,CAAC;UACxCF,WAAW,EAAEV,aAAa,CAACU,WAAW;UACtChC,MAAM,EAAEuB;QACV,CAAC,CAAC;MACJ;IACF,KAAK,OAAO;MACV;QACE,OAAOF,UAAU,CAACc,qBAAqB,CAAC;UACtCnC,MAAM,EAAEuB;QACV,CAAC,CAAC;MACJ;IACF,KAAK,SAAS;MACZ;QACE,OAAOF,UAAU,CAACe,uBAAuB,CAAC;UACxCpC,MAAM,EAAEuB;QACV,CAAC,CAAC;MACJ;IACF,KAAK,SAAS;MACZ;QACE,OAAOF,UAAU,CAACgB,uBAAuB,CAAC;UACxCrC,MAAM,EAAEuB;QACV,CAAC,CAAC;MACJ;IACF,KAAK,UAAU;MACb;QACE,OAAOF,UAAU,CAACiB,wBAAwB,CAAC;UACzCtC,MAAM,EAAEuB;QACV,CAAC,CAAC;MACJ;IACF;MACE;QACE,OAAOA,aAAa;MACtB;EACJ;AACF,CAAC;AACD,MAAMgB,aAAa,GAAGA,CAAC;EACrBxC,KAAK;EACL6B,IAAI;EACJY,yBAAyB;EACzBnB,UAAU;EACVoB,eAAe;EACfC,GAAG;EACHC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,IAAID,KAAK,KAAK,EAAE,EAAE;IAChB,MAAM,IAAIvC,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EACA,MAAMkB,aAAa,GAAG1B,mCAAmC,CAACG,KAAK,EAAE4C,KAAK,CAAC;EACvE,MAAME,uBAAuB,GAAGlD,iCAAiC,CAACI,KAAK,EAAEuB,aAAa,CAACU,WAAW,EAAEV,aAAa,CAACE,IAAI,EAAEmB,KAAK,CAAC;EAC9H,MAAMG,sBAAsB,GAAGN,yBAAyB,GAAGK,uBAAuB,GAAGvB,aAAa,CAACU,WAAW,KAAK,OAAO;EAC1H,MAAMe,WAAW,GAAGnB,IAAI,IAAI,IAAI,IAAI7B,KAAK,CAACiD,OAAO,CAACpB,IAAI,CAAC;EACvD,IAAIqB,YAAY,GAAGF,WAAW,GAAGhD,KAAK,CAAC4B,cAAc,CAACC,IAAI,EAAEe,KAAK,CAAC,GAAG,EAAE;EACvE,IAAIO,SAAS,GAAG,IAAI;EACpB,IAAIJ,sBAAsB,EAAE;IAC1B,IAAID,uBAAuB,EAAE;MAC3BK,SAAS,GAAGD,YAAY,KAAK,EAAE,GAAGlD,KAAK,CAAC4B,cAAc,CAACe,GAAG,EAAEC,KAAK,CAAC,CAACb,MAAM,GAAGmB,YAAY,CAACnB,MAAM;IACjG,CAAC,MAAM;MACL,IAAIR,aAAa,CAAC4B,SAAS,IAAI,IAAI,EAAE;QACnC,MAAM,IAAI9C,KAAK,CAAC,oBAAoBuC,KAAK,0DAA0D,CAAC;MACtG;MACAO,SAAS,GAAG5B,aAAa,CAAC4B,SAAS;MACnC,IAAIH,WAAW,EAAE;QACfE,YAAY,GAAGxD,oBAAoB,CAACC,iBAAiB,CAACG,qBAAqB,CAACoD,YAAY,EAAER,eAAe,CAAC,EAAES,SAAS,CAAC,EAAET,eAAe,CAAC;MAC1I;IACF;EACF;EACA,OAAOjD,QAAQ,CAAC,CAAC,CAAC,EAAE8B,aAAa,EAAE;IACjCtB,MAAM,EAAE2C,KAAK;IACbO,SAAS;IACTC,KAAK,EAAEF,YAAY;IACnBG,WAAW,EAAEhC,qBAAqB,CAACrB,KAAK,EAAEsB,UAAU,EAAEC,aAAa,EAAEqB,KAAK,CAAC;IAC3EE,uBAAuB;IACvBC,sBAAsB;IACtBF,cAAc;IACdS,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE;EACZ,CAAC,CAAC;AACJ,CAAC;AACD,MAAMC,aAAa,GAAGC,MAAM,IAAI;EAC9B,MAAM;IACJzD,KAAK;IACLO,cAAc;IACdC;EACF,CAAC,GAAGiD,MAAM;EACV,MAAMd,GAAG,GAAG3C,KAAK,CAAC6B,IAAI,CAACC,SAAS,CAAC;EACjC,MAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAIb,cAAc,GAAG,EAAE;;EAEvB;EACA,MAAMc,WAAW,GAAGC,MAAM,CAACC,IAAI,CAAC7D,KAAK,CAAC8D,cAAc,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAClC,MAAM,GAAGiC,CAAC,CAACjC,MAAM,CAAC,CAAC,CAAC;;EAE3F,MAAMmC,uBAAuB,GAAG,cAAc;EAC9C,MAAMC,8BAA8B,GAAG,IAAIpD,MAAM,CAAC,KAAK4C,WAAW,CAACS,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;EAClF,MAAMC,sBAAsB,GAAG,IAAItD,MAAM,CAAC,KAAK4C,WAAW,CAACS,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EACxE,MAAME,2BAA2B,GAAGC,CAAC,IAAI/D,YAAY,CAACgE,IAAI,CAACC,WAAW,IAAIA,WAAW,CAAChE,KAAK,IAAI8D,CAAC,IAAIE,WAAW,CAAC9D,GAAG,IAAI4D,CAAC,CAAC;EACzH,IAAIA,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGhE,cAAc,CAACwB,MAAM,EAAE;IAChC,MAAM2C,wBAAwB,GAAGJ,2BAA2B,CAACC,CAAC,CAAC;IAC/D,MAAMI,aAAa,GAAGD,wBAAwB,IAAI,IAAI;IACtD,MAAME,iBAAiB,GAAGV,uBAAuB,CAACjD,IAAI,CAACV,cAAc,CAACsE,KAAK,CAACN,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;IAEpF;IACA;IACA,IAAI,CAACI,aAAa,IAAIC,iBAAiB,IAAI,IAAI,IAAIT,8BAA8B,CAACW,IAAI,CAACF,iBAAiB,CAAC,EAAE;MACzG,IAAIG,IAAI,GAAGH,iBAAiB;MAC5B,OAAOG,IAAI,CAAChD,MAAM,GAAG,CAAC,EAAE;QACtB,MAAMiD,SAAS,GAAGX,sBAAsB,CAACpD,IAAI,CAAC8D,IAAI,CAAC,CAAC,CAAC,CAAC;QACtDA,IAAI,GAAGA,IAAI,CAACF,KAAK,CAACG,SAAS,CAACjD,MAAM,CAAC;QACnC2B,QAAQ,CAACxC,IAAI,CAACsB,aAAa,CAAC/C,QAAQ,CAAC,CAAC,CAAC,EAAEgE,MAAM,EAAE;UAC/Cd,GAAG;UACHC,KAAK,EAAEoC,SAAS;UAChBnC;QACF,CAAC,CAAC,CAAC,CAAC;QACJA,cAAc,GAAG,EAAE;MACrB;MACA0B,CAAC,IAAIK,iBAAiB,CAAC7C,MAAM;IAC/B;IACA;IACA;IAAA,KACK;MACH,MAAMkD,IAAI,GAAG1E,cAAc,CAACgE,CAAC,CAAC;;MAE9B;MACA;MACA,MAAMW,gBAAgB,GAAGP,aAAa,IAAID,wBAAwB,EAAEjE,KAAK,KAAK8D,CAAC,IAAIG,wBAAwB,EAAE/D,GAAG,KAAK4D,CAAC;MACtH,IAAI,CAACW,gBAAgB,EAAE;QACrB,IAAIxB,QAAQ,CAAC3B,MAAM,KAAK,CAAC,EAAE;UACzBc,cAAc,IAAIoC,IAAI;QACxB,CAAC,MAAM;UACLvB,QAAQ,CAACA,QAAQ,CAAC3B,MAAM,GAAG,CAAC,CAAC,CAACuB,YAAY,IAAI2B,IAAI;QACpD;MACF;MACAV,CAAC,IAAI,CAAC;IACR;EACF;EACA,IAAIb,QAAQ,CAAC3B,MAAM,KAAK,CAAC,IAAIc,cAAc,CAACd,MAAM,GAAG,CAAC,EAAE;IACtD2B,QAAQ,CAACxC,IAAI,CAAC;MACZO,IAAI,EAAE,OAAO;MACbQ,WAAW,EAAE,QAAQ;MACrBkB,SAAS,EAAE,IAAI;MACflD,MAAM,EAAE,EAAE;MACVmD,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfP,uBAAuB,EAAE,KAAK;MAC9BC,sBAAsB,EAAE,KAAK;MAC7BF,cAAc;MACdS,YAAY,EAAE,EAAE;MAChBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;EACA,OAAOG,QAAQ;AACjB,CAAC;AACD,MAAMyB,mBAAmB,GAAGA,CAAC;EAC3BC,KAAK;EACLC,aAAa;EACb3B;AACF,CAAC,KAAK;EACJ,OAAOA,QAAQ,CAAC4B,GAAG,CAACC,OAAO,IAAI;IAC7B,MAAMC,cAAc,GAAGC,SAAS,IAAI;MAClC,IAAIC,gBAAgB,GAAGD,SAAS;MAChC,IAAIL,KAAK,IAAIM,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxED,gBAAgB,GAAG,SAASA,gBAAgB,QAAQ;MACtD;MACA,IAAIL,aAAa,KAAK,UAAU,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACM,QAAQ,CAACD,gBAAgB,CAAC,EAAE;QAC9EA,gBAAgB,GAAG,IAAIA,gBAAgB,GAAG;MAC5C;MACA,OAAOA,gBAAgB;IACzB,CAAC;IACDH,OAAO,CAAC1C,cAAc,GAAG2C,cAAc,CAACD,OAAO,CAAC1C,cAAc,CAAC;IAC/D0C,OAAO,CAACjC,YAAY,GAAGkC,cAAc,CAACD,OAAO,CAACjC,YAAY,CAAC;IAC3D,OAAOiC,OAAO;EAChB,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,MAAMK,uBAAuB,GAAGnC,MAAM,IAAI;EAC/C,IAAIlD,cAAc,GAAGR,YAAY,CAAC0D,MAAM,CAAC;EACzC,IAAIA,MAAM,CAAC2B,KAAK,IAAI3B,MAAM,CAACoC,iCAAiC,EAAE;IAC5DtF,cAAc,GAAGA,cAAc,CAACuF,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC3B,IAAI,CAAC,GAAG,CAAC;EAChE;EACA,MAAM5D,YAAY,GAAGF,yBAAyB,CAACb,QAAQ,CAAC,CAAC,CAAC,EAAEgE,MAAM,EAAE;IAClElD;EACF,CAAC,CAAC,CAAC;EACH,MAAMmD,QAAQ,GAAGF,aAAa,CAAC/D,QAAQ,CAAC,CAAC,CAAC,EAAEgE,MAAM,EAAE;IAClDlD,cAAc;IACdC;EACF,CAAC,CAAC,CAAC;EACH,OAAO2E,mBAAmB,CAAC1F,QAAQ,CAAC,CAAC,CAAC,EAAEgE,MAAM,EAAE;IAC9CC;EACF,CAAC,CAAC,CAAC;AACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}