{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport ChecksumException from '../../ChecksumException';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport BitMatrixParser from './BitMatrixParser';\nimport DataBlock from './DataBlock';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>The main class which implements Data Matrix Code decoding -- as opposed to locating and extracting\n * the Data Matrix Code from an image.</p>\n *\n * <AUTHOR> (Brian Brown)\n */\nvar Decoder = /** @class */function () {\n  function Decoder() {\n    this.rsDecoder = new ReedSolomonDecoder(GenericGF.DATA_MATRIX_FIELD_256);\n  }\n  /**\n   * <p>Decodes a Data Matrix Code represented as a {@link BitMatrix}. A 1 or \"true\" is taken\n   * to mean a black module.</p>\n   *\n   * @param bits booleans representing white/black Data Matrix Code modules\n   * @return text and bytes encoded within the Data Matrix Code\n   * @throws FormatException if the Data Matrix Code cannot be decoded\n   * @throws ChecksumException if error correction fails\n   */\n  Decoder.prototype.decode = function (bits) {\n    var e_1, _a;\n    // Construct a parser and read version, error-correction level\n    var parser = new BitMatrixParser(bits);\n    var version = parser.getVersion();\n    // Read codewords\n    var codewords = parser.readCodewords();\n    // Separate into data blocks\n    var dataBlocks = DataBlock.getDataBlocks(codewords, version);\n    // Count total number of data bytes\n    var totalBytes = 0;\n    try {\n      for (var dataBlocks_1 = __values(dataBlocks), dataBlocks_1_1 = dataBlocks_1.next(); !dataBlocks_1_1.done; dataBlocks_1_1 = dataBlocks_1.next()) {\n        var db = dataBlocks_1_1.value;\n        totalBytes += db.getNumDataCodewords();\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (dataBlocks_1_1 && !dataBlocks_1_1.done && (_a = dataBlocks_1.return)) _a.call(dataBlocks_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    var resultBytes = new Uint8Array(totalBytes);\n    var dataBlocksCount = dataBlocks.length;\n    // Error-correct and copy data blocks together into a stream of bytes\n    for (var j = 0; j < dataBlocksCount; j++) {\n      var dataBlock = dataBlocks[j];\n      var codewordBytes = dataBlock.getCodewords();\n      var numDataCodewords = dataBlock.getNumDataCodewords();\n      this.correctErrors(codewordBytes, numDataCodewords);\n      for (var i = 0; i < numDataCodewords; i++) {\n        // De-interlace data blocks.\n        resultBytes[i * dataBlocksCount + j] = codewordBytes[i];\n      }\n    }\n    // Decode the contents of that stream of bytes\n    return DecodedBitStreamParser.decode(resultBytes);\n  };\n  /**\n   * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n   * correct the errors in-place using Reed-Solomon error correction.</p>\n   *\n   * @param codewordBytes data and error correction codewords\n   * @param numDataCodewords number of codewords that are data bytes\n   * @throws ChecksumException if error correction fails\n   */\n  Decoder.prototype.correctErrors = function (codewordBytes, numDataCodewords) {\n    // const numCodewords = codewordBytes.length;\n    // First read into an array of ints\n    var codewordsInts = new Int32Array(codewordBytes);\n    // for (let i = 0; i < numCodewords; i++) {\n    //   codewordsInts[i] = codewordBytes[i] & 0xFF;\n    // }\n    try {\n      this.rsDecoder.decode(codewordsInts, codewordBytes.length - numDataCodewords);\n    } catch (ignored /* ReedSolomonException */) {\n      throw new ChecksumException();\n    }\n    // Copy back into array of bytes -- only need to worry about the bytes that were data\n    // We don't care about errors in the error-correction codewords\n    for (var i = 0; i < numDataCodewords; i++) {\n      codewordBytes[i] = codewordsInts[i];\n    }\n  };\n  return Decoder;\n}();\nexport default Decoder;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "ChecksumException", "GenericGF", "ReedSolomonDecoder", "BitMatrixParser", "DataBlock", "DecodedBitStreamParser", "Decoder", "rsDecoder", "DATA_MATRIX_FIELD_256", "prototype", "decode", "bits", "e_1", "_a", "parser", "version", "getVersion", "codewords", "readCodewords", "dataBlocks", "getDataBlocks", "totalBytes", "dataBlocks_1", "dataBlocks_1_1", "db", "getNumDataCodewords", "e_1_1", "error", "return", "resultBytes", "Uint8Array", "dataBlocksCount", "j", "dataBlock", "codewordBytes", "getCodewords", "numDataCodewords", "correctErrors", "codewordsInts", "Int32Array", "ignored"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/decoder/Decoder.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport ChecksumException from '../../ChecksumException';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport BitMatrixParser from './BitMatrixParser';\nimport DataBlock from './DataBlock';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>The main class which implements Data Matrix Code decoding -- as opposed to locating and extracting\n * the Data Matrix Code from an image.</p>\n *\n * <AUTHOR> (Brian Brown)\n */\nvar Decoder = /** @class */ (function () {\n    function Decoder() {\n        this.rsDecoder = new ReedSolomonDecoder(GenericGF.DATA_MATRIX_FIELD_256);\n    }\n    /**\n     * <p>Decodes a Data Matrix Code represented as a {@link BitMatrix}. A 1 or \"true\" is taken\n     * to mean a black module.</p>\n     *\n     * @param bits booleans representing white/black Data Matrix Code modules\n     * @return text and bytes encoded within the Data Matrix Code\n     * @throws FormatException if the Data Matrix Code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.decode = function (bits) {\n        var e_1, _a;\n        // Construct a parser and read version, error-correction level\n        var parser = new BitMatrixParser(bits);\n        var version = parser.getVersion();\n        // Read codewords\n        var codewords = parser.readCodewords();\n        // Separate into data blocks\n        var dataBlocks = DataBlock.getDataBlocks(codewords, version);\n        // Count total number of data bytes\n        var totalBytes = 0;\n        try {\n            for (var dataBlocks_1 = __values(dataBlocks), dataBlocks_1_1 = dataBlocks_1.next(); !dataBlocks_1_1.done; dataBlocks_1_1 = dataBlocks_1.next()) {\n                var db = dataBlocks_1_1.value;\n                totalBytes += db.getNumDataCodewords();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (dataBlocks_1_1 && !dataBlocks_1_1.done && (_a = dataBlocks_1.return)) _a.call(dataBlocks_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var resultBytes = new Uint8Array(totalBytes);\n        var dataBlocksCount = dataBlocks.length;\n        // Error-correct and copy data blocks together into a stream of bytes\n        for (var j = 0; j < dataBlocksCount; j++) {\n            var dataBlock = dataBlocks[j];\n            var codewordBytes = dataBlock.getCodewords();\n            var numDataCodewords = dataBlock.getNumDataCodewords();\n            this.correctErrors(codewordBytes, numDataCodewords);\n            for (var i = 0; i < numDataCodewords; i++) {\n                // De-interlace data blocks.\n                resultBytes[i * dataBlocksCount + j] = codewordBytes[i];\n            }\n        }\n        // Decode the contents of that stream of bytes\n        return DecodedBitStreamParser.decode(resultBytes);\n    };\n    /**\n     * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n     * correct the errors in-place using Reed-Solomon error correction.</p>\n     *\n     * @param codewordBytes data and error correction codewords\n     * @param numDataCodewords number of codewords that are data bytes\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.correctErrors = function (codewordBytes, numDataCodewords) {\n        // const numCodewords = codewordBytes.length;\n        // First read into an array of ints\n        var codewordsInts = new Int32Array(codewordBytes);\n        // for (let i = 0; i < numCodewords; i++) {\n        //   codewordsInts[i] = codewordBytes[i] & 0xFF;\n        // }\n        try {\n            this.rsDecoder.decode(codewordsInts, codewordBytes.length - numDataCodewords);\n        }\n        catch (ignored /* ReedSolomonException */) {\n            throw new ChecksumException();\n        }\n        // Copy back into array of bytes -- only need to worry about the bytes that were data\n        // We don't care about errors in the error-correction codewords\n        for (var i = 0; i < numDataCodewords; i++) {\n            codewordBytes[i] = codewordsInts[i];\n        }\n    };\n    return Decoder;\n}());\nexport default Decoder;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG;IACf,IAAI,CAACC,SAAS,GAAG,IAAIL,kBAAkB,CAACD,SAAS,CAACO,qBAAqB,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,OAAO,CAACG,SAAS,CAACC,MAAM,GAAG,UAAUC,IAAI,EAAE;IACvC,IAAIC,GAAG,EAAEC,EAAE;IACX;IACA,IAAIC,MAAM,GAAG,IAAIX,eAAe,CAACQ,IAAI,CAAC;IACtC,IAAII,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,CAAC;IACjC;IACA,IAAIC,SAAS,GAAGH,MAAM,CAACI,aAAa,CAAC,CAAC;IACtC;IACA,IAAIC,UAAU,GAAGf,SAAS,CAACgB,aAAa,CAACH,SAAS,EAAEF,OAAO,CAAC;IAC5D;IACA,IAAIM,UAAU,GAAG,CAAC;IAClB,IAAI;MACA,KAAK,IAAIC,YAAY,GAAGnC,QAAQ,CAACgC,UAAU,CAAC,EAAEI,cAAc,GAAGD,YAAY,CAAC1B,IAAI,CAAC,CAAC,EAAE,CAAC2B,cAAc,CAACzB,IAAI,EAAEyB,cAAc,GAAGD,YAAY,CAAC1B,IAAI,CAAC,CAAC,EAAE;QAC5I,IAAI4B,EAAE,GAAGD,cAAc,CAAC1B,KAAK;QAC7BwB,UAAU,IAAIG,EAAE,CAACC,mBAAmB,CAAC,CAAC;MAC1C;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEd,GAAG,GAAG;QAAEe,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,cAAc,IAAI,CAACA,cAAc,CAACzB,IAAI,KAAKe,EAAE,GAAGS,YAAY,CAACM,MAAM,CAAC,EAAEf,EAAE,CAACnB,IAAI,CAAC4B,YAAY,CAAC;MACnG,CAAC,SACO;QAAE,IAAIV,GAAG,EAAE,MAAMA,GAAG,CAACe,KAAK;MAAE;IACxC;IACA,IAAIE,WAAW,GAAG,IAAIC,UAAU,CAACT,UAAU,CAAC;IAC5C,IAAIU,eAAe,GAAGZ,UAAU,CAACxB,MAAM;IACvC;IACA,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,eAAe,EAAEC,CAAC,EAAE,EAAE;MACtC,IAAIC,SAAS,GAAGd,UAAU,CAACa,CAAC,CAAC;MAC7B,IAAIE,aAAa,GAAGD,SAAS,CAACE,YAAY,CAAC,CAAC;MAC5C,IAAIC,gBAAgB,GAAGH,SAAS,CAACR,mBAAmB,CAAC,CAAC;MACtD,IAAI,CAACY,aAAa,CAACH,aAAa,EAAEE,gBAAgB,CAAC;MACnD,KAAK,IAAI3C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,gBAAgB,EAAE3C,CAAC,EAAE,EAAE;QACvC;QACAoC,WAAW,CAACpC,CAAC,GAAGsC,eAAe,GAAGC,CAAC,CAAC,GAAGE,aAAa,CAACzC,CAAC,CAAC;MAC3D;IACJ;IACA;IACA,OAAOY,sBAAsB,CAACK,MAAM,CAACmB,WAAW,CAAC;EACrD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIvB,OAAO,CAACG,SAAS,CAAC4B,aAAa,GAAG,UAAUH,aAAa,EAAEE,gBAAgB,EAAE;IACzE;IACA;IACA,IAAIE,aAAa,GAAG,IAAIC,UAAU,CAACL,aAAa,CAAC;IACjD;IACA;IACA;IACA,IAAI;MACA,IAAI,CAAC3B,SAAS,CAACG,MAAM,CAAC4B,aAAa,EAAEJ,aAAa,CAACvC,MAAM,GAAGyC,gBAAgB,CAAC;IACjF,CAAC,CACD,OAAOI,OAAO,CAAC,4BAA4B;MACvC,MAAM,IAAIxC,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA;IACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,gBAAgB,EAAE3C,CAAC,EAAE,EAAE;MACvCyC,aAAa,CAACzC,CAAC,CAAC,GAAG6C,aAAa,CAAC7C,CAAC,CAAC;IACvC;EACJ,CAAC;EACD,OAAOa,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}