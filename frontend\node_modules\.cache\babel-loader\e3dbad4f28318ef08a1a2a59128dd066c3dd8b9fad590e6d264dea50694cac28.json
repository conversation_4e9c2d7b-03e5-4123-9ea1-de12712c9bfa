{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n// The position in the array is the (checksum) value\nvar SYMBOLS = exports.SYMBOLS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '-', '.', ' ', '$', '/', '+', '%',\n// Only used for csum and multi-symbols character encodings\n'($)', '(%)', '(/)', '(+)',\n// Start/Stop\n'\\xff'];\n\n// Order matches SYMBOLS array\nvar BINARIES = exports.BINARIES = ['100010100', '101001000', '101000100', '101000010', '100101000', '100100100', '100100010', '101010000', '100010010', '100001010', '110101000', '110100100', '110100010', '110010100', '110010010', '110001010', '101101000', '101100100', '101100010', '100110100', '100011010', '101011000', '101001100', '101000110', '100101100', '100010110', '110110100', '110110010', '110101100', '110100110', '110010110', '110011010', '101101100', '101100110', '100110110', '100111010', '100101110', '111010100', '111010010', '111001010', '101101110', '101110110', '110101110', '100100110', '111011010', '111010110', '100110010', '101011110'];\n\n// Multi-symbol characters (Full ASCII Code 93)\nvar MULTI_SYMBOLS = exports.MULTI_SYMBOLS = {\n  '\\x00': ['(%)', 'U'],\n  '\\x01': ['($)', 'A'],\n  '\\x02': ['($)', 'B'],\n  '\\x03': ['($)', 'C'],\n  '\\x04': ['($)', 'D'],\n  '\\x05': ['($)', 'E'],\n  '\\x06': ['($)', 'F'],\n  '\\x07': ['($)', 'G'],\n  '\\x08': ['($)', 'H'],\n  '\\x09': ['($)', 'I'],\n  '\\x0a': ['($)', 'J'],\n  '\\x0b': ['($)', 'K'],\n  '\\x0c': ['($)', 'L'],\n  '\\x0d': ['($)', 'M'],\n  '\\x0e': ['($)', 'N'],\n  '\\x0f': ['($)', 'O'],\n  '\\x10': ['($)', 'P'],\n  '\\x11': ['($)', 'Q'],\n  '\\x12': ['($)', 'R'],\n  '\\x13': ['($)', 'S'],\n  '\\x14': ['($)', 'T'],\n  '\\x15': ['($)', 'U'],\n  '\\x16': ['($)', 'V'],\n  '\\x17': ['($)', 'W'],\n  '\\x18': ['($)', 'X'],\n  '\\x19': ['($)', 'Y'],\n  '\\x1a': ['($)', 'Z'],\n  '\\x1b': ['(%)', 'A'],\n  '\\x1c': ['(%)', 'B'],\n  '\\x1d': ['(%)', 'C'],\n  '\\x1e': ['(%)', 'D'],\n  '\\x1f': ['(%)', 'E'],\n  '!': ['(/)', 'A'],\n  '\"': ['(/)', 'B'],\n  '#': ['(/)', 'C'],\n  '&': ['(/)', 'F'],\n  '\\'': ['(/)', 'G'],\n  '(': ['(/)', 'H'],\n  ')': ['(/)', 'I'],\n  '*': ['(/)', 'J'],\n  ',': ['(/)', 'L'],\n  ':': ['(/)', 'Z'],\n  ';': ['(%)', 'F'],\n  '<': ['(%)', 'G'],\n  '=': ['(%)', 'H'],\n  '>': ['(%)', 'I'],\n  '?': ['(%)', 'J'],\n  '@': ['(%)', 'V'],\n  '[': ['(%)', 'K'],\n  '\\\\': ['(%)', 'L'],\n  ']': ['(%)', 'M'],\n  '^': ['(%)', 'N'],\n  '_': ['(%)', 'O'],\n  '`': ['(%)', 'W'],\n  'a': ['(+)', 'A'],\n  'b': ['(+)', 'B'],\n  'c': ['(+)', 'C'],\n  'd': ['(+)', 'D'],\n  'e': ['(+)', 'E'],\n  'f': ['(+)', 'F'],\n  'g': ['(+)', 'G'],\n  'h': ['(+)', 'H'],\n  'i': ['(+)', 'I'],\n  'j': ['(+)', 'J'],\n  'k': ['(+)', 'K'],\n  'l': ['(+)', 'L'],\n  'm': ['(+)', 'M'],\n  'n': ['(+)', 'N'],\n  'o': ['(+)', 'O'],\n  'p': ['(+)', 'P'],\n  'q': ['(+)', 'Q'],\n  'r': ['(+)', 'R'],\n  's': ['(+)', 'S'],\n  't': ['(+)', 'T'],\n  'u': ['(+)', 'U'],\n  'v': ['(+)', 'V'],\n  'w': ['(+)', 'W'],\n  'x': ['(+)', 'X'],\n  'y': ['(+)', 'Y'],\n  'z': ['(+)', 'Z'],\n  '{': ['(%)', 'P'],\n  '|': ['(%)', 'Q'],\n  '}': ['(%)', 'R'],\n  '~': ['(%)', 'S'],\n  '\\x7f': ['(%)', 'T']\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "SYMBOLS", "BINARIES", "MULTI_SYMBOLS"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/CODE93/constants.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n// The position in the array is the (checksum) value\nvar SYMBOLS = exports.SYMBOLS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '-', '.', ' ', '$', '/', '+', '%',\n// Only used for csum and multi-symbols character encodings\n'($)', '(%)', '(/)', '(+)',\n// Start/Stop\n'\\xff'];\n\n// Order matches SYMBOLS array\nvar BINARIES = exports.BINARIES = ['100010100', '101001000', '101000100', '101000010', '100101000', '100100100', '100100010', '101010000', '100010010', '100001010', '110101000', '110100100', '110100010', '110010100', '110010010', '110001010', '101101000', '101100100', '101100010', '100110100', '100011010', '101011000', '101001100', '101000110', '100101100', '100010110', '110110100', '110110010', '110101100', '110100110', '110010110', '110011010', '101101100', '101100110', '100110110', '100111010', '100101110', '111010100', '111010010', '111001010', '101101110', '101110110', '110101110', '100100110', '111011010', '111010110', '100110010', '101011110'];\n\n// Multi-symbol characters (Full ASCII Code 93)\nvar MULTI_SYMBOLS = exports.MULTI_SYMBOLS = {\n\t'\\x00': ['(%)', 'U'],\n\t'\\x01': ['($)', 'A'],\n\t'\\x02': ['($)', 'B'],\n\t'\\x03': ['($)', 'C'],\n\t'\\x04': ['($)', 'D'],\n\t'\\x05': ['($)', 'E'],\n\t'\\x06': ['($)', 'F'],\n\t'\\x07': ['($)', 'G'],\n\t'\\x08': ['($)', 'H'],\n\t'\\x09': ['($)', 'I'],\n\t'\\x0a': ['($)', 'J'],\n\t'\\x0b': ['($)', 'K'],\n\t'\\x0c': ['($)', 'L'],\n\t'\\x0d': ['($)', 'M'],\n\t'\\x0e': ['($)', 'N'],\n\t'\\x0f': ['($)', 'O'],\n\t'\\x10': ['($)', 'P'],\n\t'\\x11': ['($)', 'Q'],\n\t'\\x12': ['($)', 'R'],\n\t'\\x13': ['($)', 'S'],\n\t'\\x14': ['($)', 'T'],\n\t'\\x15': ['($)', 'U'],\n\t'\\x16': ['($)', 'V'],\n\t'\\x17': ['($)', 'W'],\n\t'\\x18': ['($)', 'X'],\n\t'\\x19': ['($)', 'Y'],\n\t'\\x1a': ['($)', 'Z'],\n\t'\\x1b': ['(%)', 'A'],\n\t'\\x1c': ['(%)', 'B'],\n\t'\\x1d': ['(%)', 'C'],\n\t'\\x1e': ['(%)', 'D'],\n\t'\\x1f': ['(%)', 'E'],\n\t'!': ['(/)', 'A'],\n\t'\"': ['(/)', 'B'],\n\t'#': ['(/)', 'C'],\n\t'&': ['(/)', 'F'],\n\t'\\'': ['(/)', 'G'],\n\t'(': ['(/)', 'H'],\n\t')': ['(/)', 'I'],\n\t'*': ['(/)', 'J'],\n\t',': ['(/)', 'L'],\n\t':': ['(/)', 'Z'],\n\t';': ['(%)', 'F'],\n\t'<': ['(%)', 'G'],\n\t'=': ['(%)', 'H'],\n\t'>': ['(%)', 'I'],\n\t'?': ['(%)', 'J'],\n\t'@': ['(%)', 'V'],\n\t'[': ['(%)', 'K'],\n\t'\\\\': ['(%)', 'L'],\n\t']': ['(%)', 'M'],\n\t'^': ['(%)', 'N'],\n\t'_': ['(%)', 'O'],\n\t'`': ['(%)', 'W'],\n\t'a': ['(+)', 'A'],\n\t'b': ['(+)', 'B'],\n\t'c': ['(+)', 'C'],\n\t'd': ['(+)', 'D'],\n\t'e': ['(+)', 'E'],\n\t'f': ['(+)', 'F'],\n\t'g': ['(+)', 'G'],\n\t'h': ['(+)', 'H'],\n\t'i': ['(+)', 'I'],\n\t'j': ['(+)', 'J'],\n\t'k': ['(+)', 'K'],\n\t'l': ['(+)', 'L'],\n\t'm': ['(+)', 'M'],\n\t'n': ['(+)', 'N'],\n\t'o': ['(+)', 'O'],\n\t'p': ['(+)', 'P'],\n\t'q': ['(+)', 'Q'],\n\t'r': ['(+)', 'R'],\n\t's': ['(+)', 'S'],\n\t't': ['(+)', 'T'],\n\t'u': ['(+)', 'U'],\n\t'v': ['(+)', 'V'],\n\t'w': ['(+)', 'W'],\n\t'x': ['(+)', 'X'],\n\t'y': ['(+)', 'Y'],\n\t'z': ['(+)', 'Z'],\n\t'{': ['(%)', 'P'],\n\t'|': ['(%)', 'Q'],\n\t'}': ['(%)', 'R'],\n\t'~': ['(%)', 'S'],\n\t'\\x7f': ['(%)', 'T']\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AACF;AACA,IAAIC,OAAO,GAAGF,OAAO,CAACE,OAAO,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;AACtP;AACA,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;AAC1B;AACA,MAAM,CAAC;;AAEP;AACA,IAAIC,QAAQ,GAAGH,OAAO,CAACG,QAAQ,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,CAAC;;AAElpB;AACA,IAAIC,aAAa,GAAGJ,OAAO,CAACI,aAAa,GAAG;EAC3C,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACpB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAClB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EAClB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC;EACjB,MAAM,EAAE,CAAC,KAAK,EAAE,GAAG;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}