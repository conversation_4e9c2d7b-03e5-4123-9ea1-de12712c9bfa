{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport MathUtils from '../../common/detector/MathUtils';\nimport DetectorResult from '../../common/DetectorResult';\n// import GridSampler from '../../common/GridSampler';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport PerspectiveTransform from '../../common/PerspectiveTransform';\nimport DecodeHintType from '../../DecodeHintType';\nimport NotFoundException from '../../NotFoundException';\nimport ResultPoint from '../../ResultPoint';\nimport Version from '../decoder/Version';\nimport AlignmentPatternFinder from './AlignmentPatternFinder';\nimport FinderPatternFinder from './FinderPatternFinder';\n/*import java.util.Map;*/\n/**\n * <p>Encapsulates logic that can detect a QR Code in an image, even if the QR Code\n * is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR> Owen\n */\nvar Detector = /** @class */function () {\n  function Detector(image) {\n    this.image = image;\n  }\n  Detector.prototype.getImage = function () {\n    return this.image;\n  };\n  Detector.prototype.getResultPointCallback = function () {\n    return this.resultPointCallback;\n  };\n  /**\n   * <p>Detects a QR Code in an image.</p>\n   *\n   * @return {@link DetectorResult} encapsulating results of detecting a QR Code\n   * @throws NotFoundException if QR Code cannot be found\n   * @throws FormatException if a QR Code cannot be decoded\n   */\n  // public detect(): DetectorResult /*throws NotFoundException, FormatException*/ {\n  //   return detect(null)\n  // }\n  /**\n   * <p>Detects a QR Code in an image.</p>\n   *\n   * @param hints optional hints to detector\n   * @return {@link DetectorResult} encapsulating results of detecting a QR Code\n   * @throws NotFoundException if QR Code cannot be found\n   * @throws FormatException if a QR Code cannot be decoded\n   */\n  Detector.prototype.detect = function (hints) {\n    this.resultPointCallback = hints === null || hints === undefined ? null : /*(ResultPointCallback) */hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n    var finder = new FinderPatternFinder(this.image, this.resultPointCallback);\n    var info = finder.find(hints);\n    return this.processFinderPatternInfo(info);\n  };\n  Detector.prototype.processFinderPatternInfo = function (info) {\n    var topLeft = info.getTopLeft();\n    var topRight = info.getTopRight();\n    var bottomLeft = info.getBottomLeft();\n    var moduleSize = this.calculateModuleSize(topLeft, topRight, bottomLeft);\n    if (moduleSize < 1.0) {\n      throw new NotFoundException('No pattern found in proccess finder.');\n    }\n    var dimension = Detector.computeDimension(topLeft, topRight, bottomLeft, moduleSize);\n    var provisionalVersion = Version.getProvisionalVersionForDimension(dimension);\n    var modulesBetweenFPCenters = provisionalVersion.getDimensionForVersion() - 7;\n    var alignmentPattern = null;\n    // Anything above version 1 has an alignment pattern\n    if (provisionalVersion.getAlignmentPatternCenters().length > 0) {\n      // Guess where a \"bottom right\" finder pattern would have been\n      var bottomRightX = topRight.getX() - topLeft.getX() + bottomLeft.getX();\n      var bottomRightY = topRight.getY() - topLeft.getY() + bottomLeft.getY();\n      // Estimate that alignment pattern is closer by 3 modules\n      // from \"bottom right\" to known top left location\n      var correctionToTopLeft = 1.0 - 3.0 / modulesBetweenFPCenters;\n      var estAlignmentX = /*(int) */Math.floor(topLeft.getX() + correctionToTopLeft * (bottomRightX - topLeft.getX()));\n      var estAlignmentY = /*(int) */Math.floor(topLeft.getY() + correctionToTopLeft * (bottomRightY - topLeft.getY()));\n      // Kind of arbitrary -- expand search radius before giving up\n      for (var i = 4; i <= 16; i <<= 1) {\n        try {\n          alignmentPattern = this.findAlignmentInRegion(moduleSize, estAlignmentX, estAlignmentY, i);\n          break;\n        } catch (re /*NotFoundException*/) {\n          if (!(re instanceof NotFoundException)) {\n            throw re;\n          }\n          // try next round\n        }\n      }\n      // If we didn't find alignment pattern... well try anyway without it\n    }\n    var transform = Detector.createTransform(topLeft, topRight, bottomLeft, alignmentPattern, dimension);\n    var bits = Detector.sampleGrid(this.image, transform, dimension);\n    var points;\n    if (alignmentPattern === null) {\n      points = [bottomLeft, topLeft, topRight];\n    } else {\n      points = [bottomLeft, topLeft, topRight, alignmentPattern];\n    }\n    return new DetectorResult(bits, points);\n  };\n  Detector.createTransform = function (topLeft, topRight, bottomLeft, alignmentPattern, dimension /*int*/) {\n    var dimMinusThree = dimension - 3.5;\n    var bottomRightX; /*float*/\n    var bottomRightY; /*float*/\n    var sourceBottomRightX; /*float*/\n    var sourceBottomRightY; /*float*/\n    if (alignmentPattern !== null) {\n      bottomRightX = alignmentPattern.getX();\n      bottomRightY = alignmentPattern.getY();\n      sourceBottomRightX = dimMinusThree - 3.0;\n      sourceBottomRightY = sourceBottomRightX;\n    } else {\n      // Don't have an alignment pattern, just make up the bottom-right point\n      bottomRightX = topRight.getX() - topLeft.getX() + bottomLeft.getX();\n      bottomRightY = topRight.getY() - topLeft.getY() + bottomLeft.getY();\n      sourceBottomRightX = dimMinusThree;\n      sourceBottomRightY = dimMinusThree;\n    }\n    return PerspectiveTransform.quadrilateralToQuadrilateral(3.5, 3.5, dimMinusThree, 3.5, sourceBottomRightX, sourceBottomRightY, 3.5, dimMinusThree, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRightX, bottomRightY, bottomLeft.getX(), bottomLeft.getY());\n  };\n  Detector.sampleGrid = function (image, transform, dimension /*int*/) {\n    var sampler = GridSamplerInstance.getInstance();\n    return sampler.sampleGridWithTransform(image, dimension, dimension, transform);\n  };\n  /**\n   * <p>Computes the dimension (number of modules on a size) of the QR Code based on the position\n   * of the finder patterns and estimated module size.</p>\n   */\n  Detector.computeDimension = function (topLeft, topRight, bottomLeft, moduleSize /*float*/) {\n    var tltrCentersDimension = MathUtils.round(ResultPoint.distance(topLeft, topRight) / moduleSize);\n    var tlblCentersDimension = MathUtils.round(ResultPoint.distance(topLeft, bottomLeft) / moduleSize);\n    var dimension = Math.floor((tltrCentersDimension + tlblCentersDimension) / 2) + 7;\n    switch (dimension & 0x03) {\n      // mod 4\n      case 0:\n        dimension++;\n        break;\n      // 1? do nothing\n      case 2:\n        dimension--;\n        break;\n      case 3:\n        throw new NotFoundException('Dimensions could be not found.');\n    }\n    return dimension;\n  };\n  /**\n   * <p>Computes an average estimated module size based on estimated derived from the positions\n   * of the three finder patterns.</p>\n   *\n   * @param topLeft detected top-left finder pattern center\n   * @param topRight detected top-right finder pattern center\n   * @param bottomLeft detected bottom-left finder pattern center\n   * @return estimated module size\n   */\n  Detector.prototype.calculateModuleSize = function (topLeft, topRight, bottomLeft) {\n    // Take the average\n    return (this.calculateModuleSizeOneWay(topLeft, topRight) + this.calculateModuleSizeOneWay(topLeft, bottomLeft)) / 2.0;\n  };\n  /**\n   * <p>Estimates module size based on two finder patterns -- it uses\n   * {@link #sizeOfBlackWhiteBlackRunBothWays(int, int, int, int)} to figure the\n   * width of each, measuring along the axis between their centers.</p>\n   */\n  Detector.prototype.calculateModuleSizeOneWay = function (pattern, otherPattern) {\n    var moduleSizeEst1 = this.sizeOfBlackWhiteBlackRunBothWays(/*(int) */Math.floor(pattern.getX()), /*(int) */Math.floor(pattern.getY()), /*(int) */Math.floor(otherPattern.getX()), /*(int) */Math.floor(otherPattern.getY()));\n    var moduleSizeEst2 = this.sizeOfBlackWhiteBlackRunBothWays(/*(int) */Math.floor(otherPattern.getX()), /*(int) */Math.floor(otherPattern.getY()), /*(int) */Math.floor(pattern.getX()), /*(int) */Math.floor(pattern.getY()));\n    if (isNaN(moduleSizeEst1)) {\n      return moduleSizeEst2 / 7.0;\n    }\n    if (isNaN(moduleSizeEst2)) {\n      return moduleSizeEst1 / 7.0;\n    }\n    // Average them, and divide by 7 since we've counted the width of 3 black modules,\n    // and 1 white and 1 black module on either side. Ergo, divide sum by 14.\n    return (moduleSizeEst1 + moduleSizeEst2) / 14.0;\n  };\n  /**\n   * See {@link #sizeOfBlackWhiteBlackRun(int, int, int, int)}; computes the total width of\n   * a finder pattern by looking for a black-white-black run from the center in the direction\n   * of another point (another finder pattern center), and in the opposite direction too.\n   */\n  Detector.prototype.sizeOfBlackWhiteBlackRunBothWays = function (fromX /*int*/, fromY /*int*/, toX /*int*/, toY /*int*/) {\n    var result = this.sizeOfBlackWhiteBlackRun(fromX, fromY, toX, toY);\n    // Now count other way -- don't run off image though of course\n    var scale = 1.0;\n    var otherToX = fromX - (toX - fromX);\n    if (otherToX < 0) {\n      scale = fromX / (/*(float) */fromX - otherToX);\n      otherToX = 0;\n    } else if (otherToX >= this.image.getWidth()) {\n      scale = (this.image.getWidth() - 1 - fromX) / (/*(float) */otherToX - fromX);\n      otherToX = this.image.getWidth() - 1;\n    }\n    var otherToY = /*(int) */Math.floor(fromY - (toY - fromY) * scale);\n    scale = 1.0;\n    if (otherToY < 0) {\n      scale = fromY / (/*(float) */fromY - otherToY);\n      otherToY = 0;\n    } else if (otherToY >= this.image.getHeight()) {\n      scale = (this.image.getHeight() - 1 - fromY) / (/*(float) */otherToY - fromY);\n      otherToY = this.image.getHeight() - 1;\n    }\n    otherToX = /*(int) */Math.floor(fromX + (otherToX - fromX) * scale);\n    result += this.sizeOfBlackWhiteBlackRun(fromX, fromY, otherToX, otherToY);\n    // Middle pixel is double-counted this way; subtract 1\n    return result - 1.0;\n  };\n  /**\n   * <p>This method traces a line from a point in the image, in the direction towards another point.\n   * It begins in a black region, and keeps going until it finds white, then black, then white again.\n   * It reports the distance from the start to this point.</p>\n   *\n   * <p>This is used when figuring out how wide a finder pattern is, when the finder pattern\n   * may be skewed or rotated.</p>\n   */\n  Detector.prototype.sizeOfBlackWhiteBlackRun = function (fromX /*int*/, fromY /*int*/, toX /*int*/, toY /*int*/) {\n    // Mild variant of Bresenham's algorithm\n    // see http://en.wikipedia.org/wiki/Bresenham's_line_algorithm\n    var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);\n    if (steep) {\n      var temp = fromX;\n      fromX = fromY;\n      fromY = temp;\n      temp = toX;\n      toX = toY;\n      toY = temp;\n    }\n    var dx = Math.abs(toX - fromX);\n    var dy = Math.abs(toY - fromY);\n    var error = -dx / 2;\n    var xstep = fromX < toX ? 1 : -1;\n    var ystep = fromY < toY ? 1 : -1;\n    // In black pixels, looking for white, first or second time.\n    var state = 0;\n    // Loop up until x == toX, but not beyond\n    var xLimit = toX + xstep;\n    for (var x = fromX, y = fromY; x !== xLimit; x += xstep) {\n      var realX = steep ? y : x;\n      var realY = steep ? x : y;\n      // Does current pixel mean we have moved white to black or vice versa?\n      // Scanning black in state 0,2 and white in state 1, so if we find the wrong\n      // color, advance to next state or end if we are in state 2 already\n      if (state === 1 === this.image.get(realX, realY)) {\n        if (state === 2) {\n          return MathUtils.distance(x, y, fromX, fromY);\n        }\n        state++;\n      }\n      error += dy;\n      if (error > 0) {\n        if (y === toY) {\n          break;\n        }\n        y += ystep;\n        error -= dx;\n      }\n    }\n    // Found black-white-black; give the benefit of the doubt that the next pixel outside the image\n    // is \"white\" so this last point at (toX+xStep,toY) is the right ending. This is really a\n    // small approximation; (toX+xStep,toY+yStep) might be really correct. Ignore this.\n    if (state === 2) {\n      return MathUtils.distance(toX + xstep, toY, fromX, fromY);\n    }\n    // else we didn't find even black-white-black; no estimate is really possible\n    return NaN;\n  };\n  /**\n   * <p>Attempts to locate an alignment pattern in a limited region of the image, which is\n   * guessed to contain it. This method uses {@link AlignmentPattern}.</p>\n   *\n   * @param overallEstModuleSize estimated module size so far\n   * @param estAlignmentX x coordinate of center of area probably containing alignment pattern\n   * @param estAlignmentY y coordinate of above\n   * @param allowanceFactor number of pixels in all directions to search from the center\n   * @return {@link AlignmentPattern} if found, or null otherwise\n   * @throws NotFoundException if an unexpected error occurs during detection\n   */\n  Detector.prototype.findAlignmentInRegion = function (overallEstModuleSize /*float*/, estAlignmentX /*int*/, estAlignmentY /*int*/, allowanceFactor /*float*/) {\n    // Look for an alignment pattern (3 modules in size) around where it\n    // should be\n    var allowance = /*(int) */Math.floor(allowanceFactor * overallEstModuleSize);\n    var alignmentAreaLeftX = Math.max(0, estAlignmentX - allowance);\n    var alignmentAreaRightX = Math.min(this.image.getWidth() - 1, estAlignmentX + allowance);\n    if (alignmentAreaRightX - alignmentAreaLeftX < overallEstModuleSize * 3) {\n      throw new NotFoundException('Alignment top exceeds estimated module size.');\n    }\n    var alignmentAreaTopY = Math.max(0, estAlignmentY - allowance);\n    var alignmentAreaBottomY = Math.min(this.image.getHeight() - 1, estAlignmentY + allowance);\n    if (alignmentAreaBottomY - alignmentAreaTopY < overallEstModuleSize * 3) {\n      throw new NotFoundException('Alignment bottom exceeds estimated module size.');\n    }\n    var alignmentFinder = new AlignmentPatternFinder(this.image, alignmentAreaLeftX, alignmentAreaTopY, alignmentAreaRightX - alignmentAreaLeftX, alignmentAreaBottomY - alignmentAreaTopY, overallEstModuleSize, this.resultPointCallback);\n    return alignmentFinder.find();\n  };\n  return Detector;\n}();\nexport default Detector;", "map": {"version": 3, "names": ["MathUtils", "DetectorResult", "GridSamplerInstance", "PerspectiveTransform", "DecodeHintType", "NotFoundException", "ResultPoint", "Version", "AlignmentPatternFinder", "FinderPattern<PERSON>inder", "Detector", "image", "prototype", "getImage", "getResultPointCallback", "resultPointCallback", "detect", "hints", "undefined", "get", "NEED_RESULT_POINT_CALLBACK", "finder", "info", "find", "processFinderPatternInfo", "topLeft", "getTopLeft", "topRight", "getTopRight", "bottomLeft", "getBottomLeft", "moduleSize", "calculateModuleSize", "dimension", "computeDimension", "provisionalVersion", "getProvisionalVersionForDimension", "modulesBetweenFPCenters", "getDimensionForVersion", "alignmentPattern", "getAlignmentPatternCenters", "length", "bottomRightX", "getX", "bottomRightY", "getY", "correctionToTopLeft", "estAlignmentX", "Math", "floor", "estAlignmentY", "i", "findAlignmentInRegion", "re", "transform", "createTransform", "bits", "sampleGrid", "points", "dim<PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceBottomRightX", "sourceBottomRightY", "quadrilateralToQuadrilateral", "sampler", "getInstance", "sampleGridWithTransform", "tltrCentersDimension", "round", "distance", "tlblCentersDimension", "calculateModuleSizeOneWay", "pattern", "otherPattern", "moduleSizeEst1", "sizeOfBlackWhiteBlackRunBothWays", "moduleSizeEst2", "isNaN", "fromX", "fromY", "toX", "toY", "result", "sizeOfBlackWhiteBlackRun", "scale", "otherToX", "getWidth", "otherToY", "getHeight", "steep", "abs", "temp", "dx", "dy", "error", "xstep", "ystep", "state", "xLimit", "x", "y", "realX", "realY", "NaN", "overallEstModuleSize", "allowanceFactor", "allowance", "alignmentAreaLeftX", "max", "alignmentAreaRightX", "min", "alignmentAreaTopY", "alignmentAreaBottomY", "alignmentFinder"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/detector/Detector.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport MathUtils from '../../common/detector/MathUtils';\nimport DetectorResult from '../../common/DetectorResult';\n// import GridSampler from '../../common/GridSampler';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport PerspectiveTransform from '../../common/PerspectiveTransform';\nimport DecodeHintType from '../../DecodeHintType';\nimport NotFoundException from '../../NotFoundException';\nimport ResultPoint from '../../ResultPoint';\nimport Version from '../decoder/Version';\nimport AlignmentPatternFinder from './AlignmentPatternFinder';\nimport FinderPatternFinder from './FinderPatternFinder';\n/*import java.util.Map;*/\n/**\n * <p>Encapsulates logic that can detect a QR Code in an image, even if the QR Code\n * is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR> Owen\n */\nvar Detector = /** @class */ (function () {\n    function Detector(image) {\n        this.image = image;\n    }\n    Detector.prototype.getImage = function () {\n        return this.image;\n    };\n    Detector.prototype.getResultPointCallback = function () {\n        return this.resultPointCallback;\n    };\n    /**\n     * <p>Detects a QR Code in an image.</p>\n     *\n     * @return {@link DetectorResult} encapsulating results of detecting a QR Code\n     * @throws NotFoundException if QR Code cannot be found\n     * @throws FormatException if a QR Code cannot be decoded\n     */\n    // public detect(): DetectorResult /*throws NotFoundException, FormatException*/ {\n    //   return detect(null)\n    // }\n    /**\n     * <p>Detects a QR Code in an image.</p>\n     *\n     * @param hints optional hints to detector\n     * @return {@link DetectorResult} encapsulating results of detecting a QR Code\n     * @throws NotFoundException if QR Code cannot be found\n     * @throws FormatException if a QR Code cannot be decoded\n     */\n    Detector.prototype.detect = function (hints) {\n        this.resultPointCallback = (hints === null || hints === undefined) ? null :\n            /*(ResultPointCallback) */ hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n        var finder = new FinderPatternFinder(this.image, this.resultPointCallback);\n        var info = finder.find(hints);\n        return this.processFinderPatternInfo(info);\n    };\n    Detector.prototype.processFinderPatternInfo = function (info) {\n        var topLeft = info.getTopLeft();\n        var topRight = info.getTopRight();\n        var bottomLeft = info.getBottomLeft();\n        var moduleSize = this.calculateModuleSize(topLeft, topRight, bottomLeft);\n        if (moduleSize < 1.0) {\n            throw new NotFoundException('No pattern found in proccess finder.');\n        }\n        var dimension = Detector.computeDimension(topLeft, topRight, bottomLeft, moduleSize);\n        var provisionalVersion = Version.getProvisionalVersionForDimension(dimension);\n        var modulesBetweenFPCenters = provisionalVersion.getDimensionForVersion() - 7;\n        var alignmentPattern = null;\n        // Anything above version 1 has an alignment pattern\n        if (provisionalVersion.getAlignmentPatternCenters().length > 0) {\n            // Guess where a \"bottom right\" finder pattern would have been\n            var bottomRightX = topRight.getX() - topLeft.getX() + bottomLeft.getX();\n            var bottomRightY = topRight.getY() - topLeft.getY() + bottomLeft.getY();\n            // Estimate that alignment pattern is closer by 3 modules\n            // from \"bottom right\" to known top left location\n            var correctionToTopLeft = 1.0 - 3.0 / modulesBetweenFPCenters;\n            var estAlignmentX = /*(int) */ Math.floor(topLeft.getX() + correctionToTopLeft * (bottomRightX - topLeft.getX()));\n            var estAlignmentY = /*(int) */ Math.floor(topLeft.getY() + correctionToTopLeft * (bottomRightY - topLeft.getY()));\n            // Kind of arbitrary -- expand search radius before giving up\n            for (var i = 4; i <= 16; i <<= 1) {\n                try {\n                    alignmentPattern = this.findAlignmentInRegion(moduleSize, estAlignmentX, estAlignmentY, i);\n                    break;\n                }\n                catch (re /*NotFoundException*/) {\n                    if (!(re instanceof NotFoundException)) {\n                        throw re;\n                    }\n                    // try next round\n                }\n            }\n            // If we didn't find alignment pattern... well try anyway without it\n        }\n        var transform = Detector.createTransform(topLeft, topRight, bottomLeft, alignmentPattern, dimension);\n        var bits = Detector.sampleGrid(this.image, transform, dimension);\n        var points;\n        if (alignmentPattern === null) {\n            points = [bottomLeft, topLeft, topRight];\n        }\n        else {\n            points = [bottomLeft, topLeft, topRight, alignmentPattern];\n        }\n        return new DetectorResult(bits, points);\n    };\n    Detector.createTransform = function (topLeft, topRight, bottomLeft, alignmentPattern, dimension /*int*/) {\n        var dimMinusThree = dimension - 3.5;\n        var bottomRightX; /*float*/\n        var bottomRightY; /*float*/\n        var sourceBottomRightX; /*float*/\n        var sourceBottomRightY; /*float*/\n        if (alignmentPattern !== null) {\n            bottomRightX = alignmentPattern.getX();\n            bottomRightY = alignmentPattern.getY();\n            sourceBottomRightX = dimMinusThree - 3.0;\n            sourceBottomRightY = sourceBottomRightX;\n        }\n        else {\n            // Don't have an alignment pattern, just make up the bottom-right point\n            bottomRightX = (topRight.getX() - topLeft.getX()) + bottomLeft.getX();\n            bottomRightY = (topRight.getY() - topLeft.getY()) + bottomLeft.getY();\n            sourceBottomRightX = dimMinusThree;\n            sourceBottomRightY = dimMinusThree;\n        }\n        return PerspectiveTransform.quadrilateralToQuadrilateral(3.5, 3.5, dimMinusThree, 3.5, sourceBottomRightX, sourceBottomRightY, 3.5, dimMinusThree, topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRightX, bottomRightY, bottomLeft.getX(), bottomLeft.getY());\n    };\n    Detector.sampleGrid = function (image, transform, dimension /*int*/) {\n        var sampler = GridSamplerInstance.getInstance();\n        return sampler.sampleGridWithTransform(image, dimension, dimension, transform);\n    };\n    /**\n     * <p>Computes the dimension (number of modules on a size) of the QR Code based on the position\n     * of the finder patterns and estimated module size.</p>\n     */\n    Detector.computeDimension = function (topLeft, topRight, bottomLeft, moduleSize /*float*/) {\n        var tltrCentersDimension = MathUtils.round(ResultPoint.distance(topLeft, topRight) / moduleSize);\n        var tlblCentersDimension = MathUtils.round(ResultPoint.distance(topLeft, bottomLeft) / moduleSize);\n        var dimension = Math.floor((tltrCentersDimension + tlblCentersDimension) / 2) + 7;\n        switch (dimension & 0x03) { // mod 4\n            case 0:\n                dimension++;\n                break;\n            // 1? do nothing\n            case 2:\n                dimension--;\n                break;\n            case 3:\n                throw new NotFoundException('Dimensions could be not found.');\n        }\n        return dimension;\n    };\n    /**\n     * <p>Computes an average estimated module size based on estimated derived from the positions\n     * of the three finder patterns.</p>\n     *\n     * @param topLeft detected top-left finder pattern center\n     * @param topRight detected top-right finder pattern center\n     * @param bottomLeft detected bottom-left finder pattern center\n     * @return estimated module size\n     */\n    Detector.prototype.calculateModuleSize = function (topLeft, topRight, bottomLeft) {\n        // Take the average\n        return (this.calculateModuleSizeOneWay(topLeft, topRight) +\n            this.calculateModuleSizeOneWay(topLeft, bottomLeft)) / 2.0;\n    };\n    /**\n     * <p>Estimates module size based on two finder patterns -- it uses\n     * {@link #sizeOfBlackWhiteBlackRunBothWays(int, int, int, int)} to figure the\n     * width of each, measuring along the axis between their centers.</p>\n     */\n    Detector.prototype.calculateModuleSizeOneWay = function (pattern, otherPattern) {\n        var moduleSizeEst1 = this.sizeOfBlackWhiteBlackRunBothWays(/*(int) */ Math.floor(pattern.getX()), \n        /*(int) */ Math.floor(pattern.getY()), \n        /*(int) */ Math.floor(otherPattern.getX()), \n        /*(int) */ Math.floor(otherPattern.getY()));\n        var moduleSizeEst2 = this.sizeOfBlackWhiteBlackRunBothWays(/*(int) */ Math.floor(otherPattern.getX()), \n        /*(int) */ Math.floor(otherPattern.getY()), \n        /*(int) */ Math.floor(pattern.getX()), \n        /*(int) */ Math.floor(pattern.getY()));\n        if (isNaN(moduleSizeEst1)) {\n            return moduleSizeEst2 / 7.0;\n        }\n        if (isNaN(moduleSizeEst2)) {\n            return moduleSizeEst1 / 7.0;\n        }\n        // Average them, and divide by 7 since we've counted the width of 3 black modules,\n        // and 1 white and 1 black module on either side. Ergo, divide sum by 14.\n        return (moduleSizeEst1 + moduleSizeEst2) / 14.0;\n    };\n    /**\n     * See {@link #sizeOfBlackWhiteBlackRun(int, int, int, int)}; computes the total width of\n     * a finder pattern by looking for a black-white-black run from the center in the direction\n     * of another point (another finder pattern center), and in the opposite direction too.\n     */\n    Detector.prototype.sizeOfBlackWhiteBlackRunBothWays = function (fromX /*int*/, fromY /*int*/, toX /*int*/, toY /*int*/) {\n        var result = this.sizeOfBlackWhiteBlackRun(fromX, fromY, toX, toY);\n        // Now count other way -- don't run off image though of course\n        var scale = 1.0;\n        var otherToX = fromX - (toX - fromX);\n        if (otherToX < 0) {\n            scale = fromX / /*(float) */ (fromX - otherToX);\n            otherToX = 0;\n        }\n        else if (otherToX >= this.image.getWidth()) {\n            scale = (this.image.getWidth() - 1 - fromX) / /*(float) */ (otherToX - fromX);\n            otherToX = this.image.getWidth() - 1;\n        }\n        var otherToY = /*(int) */ Math.floor(fromY - (toY - fromY) * scale);\n        scale = 1.0;\n        if (otherToY < 0) {\n            scale = fromY / /*(float) */ (fromY - otherToY);\n            otherToY = 0;\n        }\n        else if (otherToY >= this.image.getHeight()) {\n            scale = (this.image.getHeight() - 1 - fromY) / /*(float) */ (otherToY - fromY);\n            otherToY = this.image.getHeight() - 1;\n        }\n        otherToX = /*(int) */ Math.floor(fromX + (otherToX - fromX) * scale);\n        result += this.sizeOfBlackWhiteBlackRun(fromX, fromY, otherToX, otherToY);\n        // Middle pixel is double-counted this way; subtract 1\n        return result - 1.0;\n    };\n    /**\n     * <p>This method traces a line from a point in the image, in the direction towards another point.\n     * It begins in a black region, and keeps going until it finds white, then black, then white again.\n     * It reports the distance from the start to this point.</p>\n     *\n     * <p>This is used when figuring out how wide a finder pattern is, when the finder pattern\n     * may be skewed or rotated.</p>\n     */\n    Detector.prototype.sizeOfBlackWhiteBlackRun = function (fromX /*int*/, fromY /*int*/, toX /*int*/, toY /*int*/) {\n        // Mild variant of Bresenham's algorithm\n        // see http://en.wikipedia.org/wiki/Bresenham's_line_algorithm\n        var steep = Math.abs(toY - fromY) > Math.abs(toX - fromX);\n        if (steep) {\n            var temp = fromX;\n            fromX = fromY;\n            fromY = temp;\n            temp = toX;\n            toX = toY;\n            toY = temp;\n        }\n        var dx = Math.abs(toX - fromX);\n        var dy = Math.abs(toY - fromY);\n        var error = -dx / 2;\n        var xstep = fromX < toX ? 1 : -1;\n        var ystep = fromY < toY ? 1 : -1;\n        // In black pixels, looking for white, first or second time.\n        var state = 0;\n        // Loop up until x == toX, but not beyond\n        var xLimit = toX + xstep;\n        for (var x = fromX, y = fromY; x !== xLimit; x += xstep) {\n            var realX = steep ? y : x;\n            var realY = steep ? x : y;\n            // Does current pixel mean we have moved white to black or vice versa?\n            // Scanning black in state 0,2 and white in state 1, so if we find the wrong\n            // color, advance to next state or end if we are in state 2 already\n            if ((state === 1) === this.image.get(realX, realY)) {\n                if (state === 2) {\n                    return MathUtils.distance(x, y, fromX, fromY);\n                }\n                state++;\n            }\n            error += dy;\n            if (error > 0) {\n                if (y === toY) {\n                    break;\n                }\n                y += ystep;\n                error -= dx;\n            }\n        }\n        // Found black-white-black; give the benefit of the doubt that the next pixel outside the image\n        // is \"white\" so this last point at (toX+xStep,toY) is the right ending. This is really a\n        // small approximation; (toX+xStep,toY+yStep) might be really correct. Ignore this.\n        if (state === 2) {\n            return MathUtils.distance(toX + xstep, toY, fromX, fromY);\n        }\n        // else we didn't find even black-white-black; no estimate is really possible\n        return NaN;\n    };\n    /**\n     * <p>Attempts to locate an alignment pattern in a limited region of the image, which is\n     * guessed to contain it. This method uses {@link AlignmentPattern}.</p>\n     *\n     * @param overallEstModuleSize estimated module size so far\n     * @param estAlignmentX x coordinate of center of area probably containing alignment pattern\n     * @param estAlignmentY y coordinate of above\n     * @param allowanceFactor number of pixels in all directions to search from the center\n     * @return {@link AlignmentPattern} if found, or null otherwise\n     * @throws NotFoundException if an unexpected error occurs during detection\n     */\n    Detector.prototype.findAlignmentInRegion = function (overallEstModuleSize /*float*/, estAlignmentX /*int*/, estAlignmentY /*int*/, allowanceFactor /*float*/) {\n        // Look for an alignment pattern (3 modules in size) around where it\n        // should be\n        var allowance = /*(int) */ Math.floor(allowanceFactor * overallEstModuleSize);\n        var alignmentAreaLeftX = Math.max(0, estAlignmentX - allowance);\n        var alignmentAreaRightX = Math.min(this.image.getWidth() - 1, estAlignmentX + allowance);\n        if (alignmentAreaRightX - alignmentAreaLeftX < overallEstModuleSize * 3) {\n            throw new NotFoundException('Alignment top exceeds estimated module size.');\n        }\n        var alignmentAreaTopY = Math.max(0, estAlignmentY - allowance);\n        var alignmentAreaBottomY = Math.min(this.image.getHeight() - 1, estAlignmentY + allowance);\n        if (alignmentAreaBottomY - alignmentAreaTopY < overallEstModuleSize * 3) {\n            throw new NotFoundException('Alignment bottom exceeds estimated module size.');\n        }\n        var alignmentFinder = new AlignmentPatternFinder(this.image, alignmentAreaLeftX, alignmentAreaTopY, alignmentAreaRightX - alignmentAreaLeftX, alignmentAreaBottomY - alignmentAreaTopY, overallEstModuleSize, this.resultPointCallback);\n        return alignmentFinder.find();\n    };\n    return Detector;\n}());\nexport default Detector;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,SAAS,MAAM,iCAAiC;AACvD,OAAOC,cAAc,MAAM,6BAA6B;AACxD;AACA,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,oBAAoB,MAAM,mCAAmC;AACpE,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACC,KAAK,EAAE;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAD,QAAQ,CAACE,SAAS,CAACC,QAAQ,GAAG,YAAY;IACtC,OAAO,IAAI,CAACF,KAAK;EACrB,CAAC;EACDD,QAAQ,CAACE,SAAS,CAACE,sBAAsB,GAAG,YAAY;IACpD,OAAO,IAAI,CAACC,mBAAmB;EACnC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIL,QAAQ,CAACE,SAAS,CAACI,MAAM,GAAG,UAAUC,KAAK,EAAE;IACzC,IAAI,CAACF,mBAAmB,GAAIE,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,GAAI,IAAI,GACrE,0BAA2BD,KAAK,CAACE,GAAG,CAACf,cAAc,CAACgB,0BAA0B,CAAC;IACnF,IAAIC,MAAM,GAAG,IAAIZ,mBAAmB,CAAC,IAAI,CAACE,KAAK,EAAE,IAAI,CAACI,mBAAmB,CAAC;IAC1E,IAAIO,IAAI,GAAGD,MAAM,CAACE,IAAI,CAACN,KAAK,CAAC;IAC7B,OAAO,IAAI,CAACO,wBAAwB,CAACF,IAAI,CAAC;EAC9C,CAAC;EACDZ,QAAQ,CAACE,SAAS,CAACY,wBAAwB,GAAG,UAAUF,IAAI,EAAE;IAC1D,IAAIG,OAAO,GAAGH,IAAI,CAACI,UAAU,CAAC,CAAC;IAC/B,IAAIC,QAAQ,GAAGL,IAAI,CAACM,WAAW,CAAC,CAAC;IACjC,IAAIC,UAAU,GAAGP,IAAI,CAACQ,aAAa,CAAC,CAAC;IACrC,IAAIC,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAACP,OAAO,EAAEE,QAAQ,EAAEE,UAAU,CAAC;IACxE,IAAIE,UAAU,GAAG,GAAG,EAAE;MAClB,MAAM,IAAI1B,iBAAiB,CAAC,sCAAsC,CAAC;IACvE;IACA,IAAI4B,SAAS,GAAGvB,QAAQ,CAACwB,gBAAgB,CAACT,OAAO,EAAEE,QAAQ,EAAEE,UAAU,EAAEE,UAAU,CAAC;IACpF,IAAII,kBAAkB,GAAG5B,OAAO,CAAC6B,iCAAiC,CAACH,SAAS,CAAC;IAC7E,IAAII,uBAAuB,GAAGF,kBAAkB,CAACG,sBAAsB,CAAC,CAAC,GAAG,CAAC;IAC7E,IAAIC,gBAAgB,GAAG,IAAI;IAC3B;IACA,IAAIJ,kBAAkB,CAACK,0BAA0B,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC,EAAE;MAC5D;MACA,IAAIC,YAAY,GAAGf,QAAQ,CAACgB,IAAI,CAAC,CAAC,GAAGlB,OAAO,CAACkB,IAAI,CAAC,CAAC,GAAGd,UAAU,CAACc,IAAI,CAAC,CAAC;MACvE,IAAIC,YAAY,GAAGjB,QAAQ,CAACkB,IAAI,CAAC,CAAC,GAAGpB,OAAO,CAACoB,IAAI,CAAC,CAAC,GAAGhB,UAAU,CAACgB,IAAI,CAAC,CAAC;MACvE;MACA;MACA,IAAIC,mBAAmB,GAAG,GAAG,GAAG,GAAG,GAAGT,uBAAuB;MAC7D,IAAIU,aAAa,GAAG,UAAWC,IAAI,CAACC,KAAK,CAACxB,OAAO,CAACkB,IAAI,CAAC,CAAC,GAAGG,mBAAmB,IAAIJ,YAAY,GAAGjB,OAAO,CAACkB,IAAI,CAAC,CAAC,CAAC,CAAC;MACjH,IAAIO,aAAa,GAAG,UAAWF,IAAI,CAACC,KAAK,CAACxB,OAAO,CAACoB,IAAI,CAAC,CAAC,GAAGC,mBAAmB,IAAIF,YAAY,GAAGnB,OAAO,CAACoB,IAAI,CAAC,CAAC,CAAC,CAAC;MACjH;MACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI;UACAZ,gBAAgB,GAAG,IAAI,CAACa,qBAAqB,CAACrB,UAAU,EAAEgB,aAAa,EAAEG,aAAa,EAAEC,CAAC,CAAC;UAC1F;QACJ,CAAC,CACD,OAAOE,EAAE,CAAC,uBAAuB;UAC7B,IAAI,EAAEA,EAAE,YAAYhD,iBAAiB,CAAC,EAAE;YACpC,MAAMgD,EAAE;UACZ;UACA;QACJ;MACJ;MACA;IACJ;IACA,IAAIC,SAAS,GAAG5C,QAAQ,CAAC6C,eAAe,CAAC9B,OAAO,EAAEE,QAAQ,EAAEE,UAAU,EAAEU,gBAAgB,EAAEN,SAAS,CAAC;IACpG,IAAIuB,IAAI,GAAG9C,QAAQ,CAAC+C,UAAU,CAAC,IAAI,CAAC9C,KAAK,EAAE2C,SAAS,EAAErB,SAAS,CAAC;IAChE,IAAIyB,MAAM;IACV,IAAInB,gBAAgB,KAAK,IAAI,EAAE;MAC3BmB,MAAM,GAAG,CAAC7B,UAAU,EAAEJ,OAAO,EAAEE,QAAQ,CAAC;IAC5C,CAAC,MACI;MACD+B,MAAM,GAAG,CAAC7B,UAAU,EAAEJ,OAAO,EAAEE,QAAQ,EAAEY,gBAAgB,CAAC;IAC9D;IACA,OAAO,IAAItC,cAAc,CAACuD,IAAI,EAAEE,MAAM,CAAC;EAC3C,CAAC;EACDhD,QAAQ,CAAC6C,eAAe,GAAG,UAAU9B,OAAO,EAAEE,QAAQ,EAAEE,UAAU,EAAEU,gBAAgB,EAAEN,SAAS,CAAC,SAAS;IACrG,IAAI0B,aAAa,GAAG1B,SAAS,GAAG,GAAG;IACnC,IAAIS,YAAY,CAAC,CAAC;IAClB,IAAIE,YAAY,CAAC,CAAC;IAClB,IAAIgB,kBAAkB,CAAC,CAAC;IACxB,IAAIC,kBAAkB,CAAC,CAAC;IACxB,IAAItB,gBAAgB,KAAK,IAAI,EAAE;MAC3BG,YAAY,GAAGH,gBAAgB,CAACI,IAAI,CAAC,CAAC;MACtCC,YAAY,GAAGL,gBAAgB,CAACM,IAAI,CAAC,CAAC;MACtCe,kBAAkB,GAAGD,aAAa,GAAG,GAAG;MACxCE,kBAAkB,GAAGD,kBAAkB;IAC3C,CAAC,MACI;MACD;MACAlB,YAAY,GAAIf,QAAQ,CAACgB,IAAI,CAAC,CAAC,GAAGlB,OAAO,CAACkB,IAAI,CAAC,CAAC,GAAId,UAAU,CAACc,IAAI,CAAC,CAAC;MACrEC,YAAY,GAAIjB,QAAQ,CAACkB,IAAI,CAAC,CAAC,GAAGpB,OAAO,CAACoB,IAAI,CAAC,CAAC,GAAIhB,UAAU,CAACgB,IAAI,CAAC,CAAC;MACrEe,kBAAkB,GAAGD,aAAa;MAClCE,kBAAkB,GAAGF,aAAa;IACtC;IACA,OAAOxD,oBAAoB,CAAC2D,4BAA4B,CAAC,GAAG,EAAE,GAAG,EAAEH,aAAa,EAAE,GAAG,EAAEC,kBAAkB,EAAEC,kBAAkB,EAAE,GAAG,EAAEF,aAAa,EAAElC,OAAO,CAACkB,IAAI,CAAC,CAAC,EAAElB,OAAO,CAACoB,IAAI,CAAC,CAAC,EAAElB,QAAQ,CAACgB,IAAI,CAAC,CAAC,EAAEhB,QAAQ,CAACkB,IAAI,CAAC,CAAC,EAAEH,YAAY,EAAEE,YAAY,EAAEf,UAAU,CAACc,IAAI,CAAC,CAAC,EAAEd,UAAU,CAACgB,IAAI,CAAC,CAAC,CAAC;EAC1R,CAAC;EACDnC,QAAQ,CAAC+C,UAAU,GAAG,UAAU9C,KAAK,EAAE2C,SAAS,EAAErB,SAAS,CAAC,SAAS;IACjE,IAAI8B,OAAO,GAAG7D,mBAAmB,CAAC8D,WAAW,CAAC,CAAC;IAC/C,OAAOD,OAAO,CAACE,uBAAuB,CAACtD,KAAK,EAAEsB,SAAS,EAAEA,SAAS,EAAEqB,SAAS,CAAC;EAClF,CAAC;EACD;AACJ;AACA;AACA;EACI5C,QAAQ,CAACwB,gBAAgB,GAAG,UAAUT,OAAO,EAAEE,QAAQ,EAAEE,UAAU,EAAEE,UAAU,CAAC,WAAW;IACvF,IAAImC,oBAAoB,GAAGlE,SAAS,CAACmE,KAAK,CAAC7D,WAAW,CAAC8D,QAAQ,CAAC3C,OAAO,EAAEE,QAAQ,CAAC,GAAGI,UAAU,CAAC;IAChG,IAAIsC,oBAAoB,GAAGrE,SAAS,CAACmE,KAAK,CAAC7D,WAAW,CAAC8D,QAAQ,CAAC3C,OAAO,EAAEI,UAAU,CAAC,GAAGE,UAAU,CAAC;IAClG,IAAIE,SAAS,GAAGe,IAAI,CAACC,KAAK,CAAC,CAACiB,oBAAoB,GAAGG,oBAAoB,IAAI,CAAC,CAAC,GAAG,CAAC;IACjF,QAAQpC,SAAS,GAAG,IAAI;MAAI;MACxB,KAAK,CAAC;QACFA,SAAS,EAAE;QACX;MACJ;MACA,KAAK,CAAC;QACFA,SAAS,EAAE;QACX;MACJ,KAAK,CAAC;QACF,MAAM,IAAI5B,iBAAiB,CAAC,gCAAgC,CAAC;IACrE;IACA,OAAO4B,SAAS;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIvB,QAAQ,CAACE,SAAS,CAACoB,mBAAmB,GAAG,UAAUP,OAAO,EAAEE,QAAQ,EAAEE,UAAU,EAAE;IAC9E;IACA,OAAO,CAAC,IAAI,CAACyC,yBAAyB,CAAC7C,OAAO,EAAEE,QAAQ,CAAC,GACrD,IAAI,CAAC2C,yBAAyB,CAAC7C,OAAO,EAAEI,UAAU,CAAC,IAAI,GAAG;EAClE,CAAC;EACD;AACJ;AACA;AACA;AACA;EACInB,QAAQ,CAACE,SAAS,CAAC0D,yBAAyB,GAAG,UAAUC,OAAO,EAAEC,YAAY,EAAE;IAC5E,IAAIC,cAAc,GAAG,IAAI,CAACC,gCAAgC,CAAC,UAAW1B,IAAI,CAACC,KAAK,CAACsB,OAAO,CAAC5B,IAAI,CAAC,CAAC,CAAC,EAChG,UAAWK,IAAI,CAACC,KAAK,CAACsB,OAAO,CAAC1B,IAAI,CAAC,CAAC,CAAC,EACrC,UAAWG,IAAI,CAACC,KAAK,CAACuB,YAAY,CAAC7B,IAAI,CAAC,CAAC,CAAC,EAC1C,UAAWK,IAAI,CAACC,KAAK,CAACuB,YAAY,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3C,IAAI8B,cAAc,GAAG,IAAI,CAACD,gCAAgC,CAAC,UAAW1B,IAAI,CAACC,KAAK,CAACuB,YAAY,CAAC7B,IAAI,CAAC,CAAC,CAAC,EACrG,UAAWK,IAAI,CAACC,KAAK,CAACuB,YAAY,CAAC3B,IAAI,CAAC,CAAC,CAAC,EAC1C,UAAWG,IAAI,CAACC,KAAK,CAACsB,OAAO,CAAC5B,IAAI,CAAC,CAAC,CAAC,EACrC,UAAWK,IAAI,CAACC,KAAK,CAACsB,OAAO,CAAC1B,IAAI,CAAC,CAAC,CAAC,CAAC;IACtC,IAAI+B,KAAK,CAACH,cAAc,CAAC,EAAE;MACvB,OAAOE,cAAc,GAAG,GAAG;IAC/B;IACA,IAAIC,KAAK,CAACD,cAAc,CAAC,EAAE;MACvB,OAAOF,cAAc,GAAG,GAAG;IAC/B;IACA;IACA;IACA,OAAO,CAACA,cAAc,GAAGE,cAAc,IAAI,IAAI;EACnD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIjE,QAAQ,CAACE,SAAS,CAAC8D,gCAAgC,GAAG,UAAUG,KAAK,CAAC,SAASC,KAAK,CAAC,SAASC,GAAG,CAAC,SAASC,GAAG,CAAC,SAAS;IACpH,IAAIC,MAAM,GAAG,IAAI,CAACC,wBAAwB,CAACL,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,CAAC;IAClE;IACA,IAAIG,KAAK,GAAG,GAAG;IACf,IAAIC,QAAQ,GAAGP,KAAK,IAAIE,GAAG,GAAGF,KAAK,CAAC;IACpC,IAAIO,QAAQ,GAAG,CAAC,EAAE;MACdD,KAAK,GAAGN,KAAK,IAAG,YAAcA,KAAK,GAAGO,QAAQ,CAAC;MAC/CA,QAAQ,GAAG,CAAC;IAChB,CAAC,MACI,IAAIA,QAAQ,IAAI,IAAI,CAACzE,KAAK,CAAC0E,QAAQ,CAAC,CAAC,EAAE;MACxCF,KAAK,GAAG,CAAC,IAAI,CAACxE,KAAK,CAAC0E,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAGR,KAAK,KAAI,YAAcO,QAAQ,GAAGP,KAAK,CAAC;MAC7EO,QAAQ,GAAG,IAAI,CAACzE,KAAK,CAAC0E,QAAQ,CAAC,CAAC,GAAG,CAAC;IACxC;IACA,IAAIC,QAAQ,GAAG,UAAWtC,IAAI,CAACC,KAAK,CAAC6B,KAAK,GAAG,CAACE,GAAG,GAAGF,KAAK,IAAIK,KAAK,CAAC;IACnEA,KAAK,GAAG,GAAG;IACX,IAAIG,QAAQ,GAAG,CAAC,EAAE;MACdH,KAAK,GAAGL,KAAK,IAAG,YAAcA,KAAK,GAAGQ,QAAQ,CAAC;MAC/CA,QAAQ,GAAG,CAAC;IAChB,CAAC,MACI,IAAIA,QAAQ,IAAI,IAAI,CAAC3E,KAAK,CAAC4E,SAAS,CAAC,CAAC,EAAE;MACzCJ,KAAK,GAAG,CAAC,IAAI,CAACxE,KAAK,CAAC4E,SAAS,CAAC,CAAC,GAAG,CAAC,GAAGT,KAAK,KAAI,YAAcQ,QAAQ,GAAGR,KAAK,CAAC;MAC9EQ,QAAQ,GAAG,IAAI,CAAC3E,KAAK,CAAC4E,SAAS,CAAC,CAAC,GAAG,CAAC;IACzC;IACAH,QAAQ,GAAG,UAAWpC,IAAI,CAACC,KAAK,CAAC4B,KAAK,GAAG,CAACO,QAAQ,GAAGP,KAAK,IAAIM,KAAK,CAAC;IACpEF,MAAM,IAAI,IAAI,CAACC,wBAAwB,CAACL,KAAK,EAAEC,KAAK,EAAEM,QAAQ,EAAEE,QAAQ,CAAC;IACzE;IACA,OAAOL,MAAM,GAAG,GAAG;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIvE,QAAQ,CAACE,SAAS,CAACsE,wBAAwB,GAAG,UAAUL,KAAK,CAAC,SAASC,KAAK,CAAC,SAASC,GAAG,CAAC,SAASC,GAAG,CAAC,SAAS;IAC5G;IACA;IACA,IAAIQ,KAAK,GAAGxC,IAAI,CAACyC,GAAG,CAACT,GAAG,GAAGF,KAAK,CAAC,GAAG9B,IAAI,CAACyC,GAAG,CAACV,GAAG,GAAGF,KAAK,CAAC;IACzD,IAAIW,KAAK,EAAE;MACP,IAAIE,IAAI,GAAGb,KAAK;MAChBA,KAAK,GAAGC,KAAK;MACbA,KAAK,GAAGY,IAAI;MACZA,IAAI,GAAGX,GAAG;MACVA,GAAG,GAAGC,GAAG;MACTA,GAAG,GAAGU,IAAI;IACd;IACA,IAAIC,EAAE,GAAG3C,IAAI,CAACyC,GAAG,CAACV,GAAG,GAAGF,KAAK,CAAC;IAC9B,IAAIe,EAAE,GAAG5C,IAAI,CAACyC,GAAG,CAACT,GAAG,GAAGF,KAAK,CAAC;IAC9B,IAAIe,KAAK,GAAG,CAACF,EAAE,GAAG,CAAC;IACnB,IAAIG,KAAK,GAAGjB,KAAK,GAAGE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,IAAIgB,KAAK,GAAGjB,KAAK,GAAGE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC;IACA,IAAIgB,KAAK,GAAG,CAAC;IACb;IACA,IAAIC,MAAM,GAAGlB,GAAG,GAAGe,KAAK;IACxB,KAAK,IAAII,CAAC,GAAGrB,KAAK,EAAEsB,CAAC,GAAGrB,KAAK,EAAEoB,CAAC,KAAKD,MAAM,EAAEC,CAAC,IAAIJ,KAAK,EAAE;MACrD,IAAIM,KAAK,GAAGZ,KAAK,GAAGW,CAAC,GAAGD,CAAC;MACzB,IAAIG,KAAK,GAAGb,KAAK,GAAGU,CAAC,GAAGC,CAAC;MACzB;MACA;MACA;MACA,IAAKH,KAAK,KAAK,CAAC,KAAM,IAAI,CAACrF,KAAK,CAACQ,GAAG,CAACiF,KAAK,EAAEC,KAAK,CAAC,EAAE;QAChD,IAAIL,KAAK,KAAK,CAAC,EAAE;UACb,OAAOhG,SAAS,CAACoE,QAAQ,CAAC8B,CAAC,EAAEC,CAAC,EAAEtB,KAAK,EAAEC,KAAK,CAAC;QACjD;QACAkB,KAAK,EAAE;MACX;MACAH,KAAK,IAAID,EAAE;MACX,IAAIC,KAAK,GAAG,CAAC,EAAE;QACX,IAAIM,CAAC,KAAKnB,GAAG,EAAE;UACX;QACJ;QACAmB,CAAC,IAAIJ,KAAK;QACVF,KAAK,IAAIF,EAAE;MACf;IACJ;IACA;IACA;IACA;IACA,IAAIK,KAAK,KAAK,CAAC,EAAE;MACb,OAAOhG,SAAS,CAACoE,QAAQ,CAACW,GAAG,GAAGe,KAAK,EAAEd,GAAG,EAAEH,KAAK,EAAEC,KAAK,CAAC;IAC7D;IACA;IACA,OAAOwB,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5F,QAAQ,CAACE,SAAS,CAACwC,qBAAqB,GAAG,UAAUmD,oBAAoB,CAAC,WAAWxD,aAAa,CAAC,SAASG,aAAa,CAAC,SAASsD,eAAe,CAAC,WAAW;IAC1J;IACA;IACA,IAAIC,SAAS,GAAG,UAAWzD,IAAI,CAACC,KAAK,CAACuD,eAAe,GAAGD,oBAAoB,CAAC;IAC7E,IAAIG,kBAAkB,GAAG1D,IAAI,CAAC2D,GAAG,CAAC,CAAC,EAAE5D,aAAa,GAAG0D,SAAS,CAAC;IAC/D,IAAIG,mBAAmB,GAAG5D,IAAI,CAAC6D,GAAG,CAAC,IAAI,CAAClG,KAAK,CAAC0E,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEtC,aAAa,GAAG0D,SAAS,CAAC;IACxF,IAAIG,mBAAmB,GAAGF,kBAAkB,GAAGH,oBAAoB,GAAG,CAAC,EAAE;MACrE,MAAM,IAAIlG,iBAAiB,CAAC,8CAA8C,CAAC;IAC/E;IACA,IAAIyG,iBAAiB,GAAG9D,IAAI,CAAC2D,GAAG,CAAC,CAAC,EAAEzD,aAAa,GAAGuD,SAAS,CAAC;IAC9D,IAAIM,oBAAoB,GAAG/D,IAAI,CAAC6D,GAAG,CAAC,IAAI,CAAClG,KAAK,CAAC4E,SAAS,CAAC,CAAC,GAAG,CAAC,EAAErC,aAAa,GAAGuD,SAAS,CAAC;IAC1F,IAAIM,oBAAoB,GAAGD,iBAAiB,GAAGP,oBAAoB,GAAG,CAAC,EAAE;MACrE,MAAM,IAAIlG,iBAAiB,CAAC,iDAAiD,CAAC;IAClF;IACA,IAAI2G,eAAe,GAAG,IAAIxG,sBAAsB,CAAC,IAAI,CAACG,KAAK,EAAE+F,kBAAkB,EAAEI,iBAAiB,EAAEF,mBAAmB,GAAGF,kBAAkB,EAAEK,oBAAoB,GAAGD,iBAAiB,EAAEP,oBAAoB,EAAE,IAAI,CAACxF,mBAAmB,CAAC;IACvO,OAAOiG,eAAe,CAACzF,IAAI,CAAC,CAAC;EACjC,CAAC;EACD,OAAOb,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}