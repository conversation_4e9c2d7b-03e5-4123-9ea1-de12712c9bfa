{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport AI01decoder from './AI01decoder';\nvar AI01weightDecoder = /** @class */function (_super) {\n  __extends(AI01weightDecoder, _super);\n  function AI01weightDecoder(information) {\n    return _super.call(this, information) || this;\n  }\n  AI01weightDecoder.prototype.encodeCompressedWeight = function (buf, currentPos, weightSize) {\n    var originalWeightNumeric = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos, weightSize);\n    this.addWeightCode(buf, originalWeightNumeric);\n    var weightNumeric = this.checkWeight(originalWeightNumeric);\n    var currentDivisor = 100000;\n    for (var i = 0; i < 5; ++i) {\n      if (weightNumeric / currentDivisor === 0) {\n        buf.append('0');\n      }\n      currentDivisor /= 10;\n    }\n    buf.append(weightNumeric);\n  };\n  return AI01weightDecoder;\n}(AI01decoder);\nexport default AI01weightDecoder;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "AI01decoder", "AI01weightDecoder", "_super", "information", "call", "encodeCompressedWeight", "buf", "currentPos", "weightSize", "originalWeightNumeric", "getGeneralDecoder", "extractNumericValueFromBitArray", "addWeightCode", "weightNumeric", "checkWeight", "currentDivisor", "i", "append"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01weightDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01decoder from './AI01decoder';\nvar AI01weightDecoder = /** @class */ (function (_super) {\n    __extends(AI01weightDecoder, _super);\n    function AI01weightDecoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01weightDecoder.prototype.encodeCompressedWeight = function (buf, currentPos, weightSize) {\n        var originalWeightNumeric = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos, weightSize);\n        this.addWeightCode(buf, originalWeightNumeric);\n        var weightNumeric = this.checkWeight(originalWeightNumeric);\n        var currentDivisor = 100000;\n        for (var i = 0; i < 5; ++i) {\n            if (weightNumeric / currentDivisor === 0) {\n                buf.append('0');\n            }\n            currentDivisor /= 10;\n        }\n        buf.append(weightNumeric);\n    };\n    return AI01weightDecoder;\n}(AI01decoder));\nexport default AI01weightDecoder;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,WAAW,MAAM,eAAe;AACvC,IAAIC,iBAAiB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACrDhB,SAAS,CAACe,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAACE,WAAW,EAAE;IACpC,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,WAAW,CAAC,IAAI,IAAI;EACjD;EACAF,iBAAiB,CAACH,SAAS,CAACO,sBAAsB,GAAG,UAAUC,GAAG,EAAEC,UAAU,EAAEC,UAAU,EAAE;IACxF,IAAIC,qBAAqB,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAACC,+BAA+B,CAACJ,UAAU,EAAEC,UAAU,CAAC;IAC5G,IAAI,CAACI,aAAa,CAACN,GAAG,EAAEG,qBAAqB,CAAC;IAC9C,IAAII,aAAa,GAAG,IAAI,CAACC,WAAW,CAACL,qBAAqB,CAAC;IAC3D,IAAIM,cAAc,GAAG,MAAM;IAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACxB,IAAIH,aAAa,GAAGE,cAAc,KAAK,CAAC,EAAE;QACtCT,GAAG,CAACW,MAAM,CAAC,GAAG,CAAC;MACnB;MACAF,cAAc,IAAI,EAAE;IACxB;IACAT,GAAG,CAACW,MAAM,CAACJ,aAAa,CAAC;EAC7B,CAAC;EACD,OAAOZ,iBAAiB;AAC5B,CAAC,CAACD,WAAW,CAAE;AACf,eAAeC,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}