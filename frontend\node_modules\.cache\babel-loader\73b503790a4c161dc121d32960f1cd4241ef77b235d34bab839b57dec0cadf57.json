{"ast": null, "code": "import UnsupportedOperationException from '../UnsupportedOperationException';\nimport CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Responsible for en/decoding strings.\n */\nvar StringEncoding = /** @class */function () {\n  function StringEncoding() {}\n  /**\n   * Decodes some Uint8Array to a string format.\n   */\n  StringEncoding.decode = function (bytes, encoding) {\n    var encodingName = this.encodingName(encoding);\n    if (this.customDecoder) {\n      return this.customDecoder(bytes, encodingName);\n    }\n    // Increases browser support.\n    if (typeof TextDecoder === 'undefined' || this.shouldDecodeOnFallback(encodingName)) {\n      return this.decodeFallback(bytes, encodingName);\n    }\n    return new TextDecoder(encodingName).decode(bytes);\n  };\n  /**\n   * Checks if the decoding method should use the fallback for decoding\n   * once Node TextDecoder doesn't support all encoding formats.\n   *\n   * @param encodingName\n   */\n  StringEncoding.shouldDecodeOnFallback = function (encodingName) {\n    return !StringEncoding.isBrowser() && encodingName === 'ISO-8859-1';\n  };\n  /**\n   * Encodes some string into a Uint8Array.\n   */\n  StringEncoding.encode = function (s, encoding) {\n    var encodingName = this.encodingName(encoding);\n    if (this.customEncoder) {\n      return this.customEncoder(s, encodingName);\n    }\n    // Increases browser support.\n    if (typeof TextEncoder === 'undefined') {\n      return this.encodeFallback(s);\n    }\n    // TextEncoder only encodes to UTF8 by default as specified by encoding.spec.whatwg.org\n    return new TextEncoder().encode(s);\n  };\n  StringEncoding.isBrowser = function () {\n    return typeof window !== 'undefined' && {}.toString.call(window) === '[object Window]';\n  };\n  /**\n   * Returns the string value from some encoding character set.\n   */\n  StringEncoding.encodingName = function (encoding) {\n    return typeof encoding === 'string' ? encoding : encoding.getName();\n  };\n  /**\n   * Returns character set from some encoding character set.\n   */\n  StringEncoding.encodingCharacterSet = function (encoding) {\n    if (encoding instanceof CharacterSetECI) {\n      return encoding;\n    }\n    return CharacterSetECI.getCharacterSetECIByName(encoding);\n  };\n  /**\n   * Runs a fallback for the native decoding funcion.\n   */\n  StringEncoding.decodeFallback = function (bytes, encoding) {\n    var characterSet = this.encodingCharacterSet(encoding);\n    if (StringEncoding.isDecodeFallbackSupported(characterSet)) {\n      var s = '';\n      for (var i = 0, length_1 = bytes.length; i < length_1; i++) {\n        var h = bytes[i].toString(16);\n        if (h.length < 2) {\n          h = '0' + h;\n        }\n        s += '%' + h;\n      }\n      return decodeURIComponent(s);\n    }\n    if (characterSet.equals(CharacterSetECI.UnicodeBigUnmarked)) {\n      return String.fromCharCode.apply(null, new Uint16Array(bytes.buffer));\n    }\n    throw new UnsupportedOperationException(\"Encoding \" + this.encodingName(encoding) + \" not supported by fallback.\");\n  };\n  StringEncoding.isDecodeFallbackSupported = function (characterSet) {\n    return characterSet.equals(CharacterSetECI.UTF8) || characterSet.equals(CharacterSetECI.ISO8859_1) || characterSet.equals(CharacterSetECI.ASCII);\n  };\n  /**\n   * Runs a fallback for the native encoding funcion.\n   *\n   * @see https://stackoverflow.com/a/17192845/4367683\n   */\n  StringEncoding.encodeFallback = function (s) {\n    var encodedURIstring = btoa(unescape(encodeURIComponent(s)));\n    var charList = encodedURIstring.split('');\n    var uintArray = [];\n    for (var i = 0; i < charList.length; i++) {\n      uintArray.push(charList[i].charCodeAt(0));\n    }\n    return new Uint8Array(uintArray);\n  };\n  return StringEncoding;\n}();\nexport default StringEncoding;", "map": {"version": 3, "names": ["UnsupportedOperationException", "CharacterSetECI", "StringEncoding", "decode", "bytes", "encoding", "encodingName", "customDecoder", "TextDecoder", "shouldDecodeOnFallback", "decodeFallback", "<PERSON><PERSON><PERSON><PERSON>", "encode", "s", "customEncoder", "TextEncoder", "encodeFallback", "window", "toString", "call", "getName", "encodingCharacterSet", "getCharacterSetECIByName", "characterSet", "isDecodeFallbackSupported", "i", "length_1", "length", "h", "decodeURIComponent", "equals", "UnicodeBigUnmarked", "String", "fromCharCode", "apply", "Uint16Array", "buffer", "UTF8", "ISO8859_1", "ASCII", "encodedURIstring", "btoa", "unescape", "encodeURIComponent", "charList", "split", "uintArray", "push", "charCodeAt", "Uint8Array"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/StringEncoding.js"], "sourcesContent": ["import UnsupportedOperationException from '../UnsupportedOperationException';\nimport CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Responsible for en/decoding strings.\n */\nvar StringEncoding = /** @class */ (function () {\n    function StringEncoding() {\n    }\n    /**\n     * Decodes some Uint8Array to a string format.\n     */\n    StringEncoding.decode = function (bytes, encoding) {\n        var encodingName = this.encodingName(encoding);\n        if (this.customDecoder) {\n            return this.customDecoder(bytes, encodingName);\n        }\n        // Increases browser support.\n        if (typeof TextDecoder === 'undefined' || this.shouldDecodeOnFallback(encodingName)) {\n            return this.decodeFallback(bytes, encodingName);\n        }\n        return new TextDecoder(encodingName).decode(bytes);\n    };\n    /**\n     * Checks if the decoding method should use the fallback for decoding\n     * once Node TextDecoder doesn't support all encoding formats.\n     *\n     * @param encodingName\n     */\n    StringEncoding.shouldDecodeOnFallback = function (encodingName) {\n        return !StringEncoding.isBrowser() && encodingName === 'ISO-8859-1';\n    };\n    /**\n     * Encodes some string into a Uint8Array.\n     */\n    StringEncoding.encode = function (s, encoding) {\n        var encodingName = this.encodingName(encoding);\n        if (this.customEncoder) {\n            return this.customEncoder(s, encodingName);\n        }\n        // Increases browser support.\n        if (typeof TextEncoder === 'undefined') {\n            return this.encodeFallback(s);\n        }\n        // TextEncoder only encodes to UTF8 by default as specified by encoding.spec.whatwg.org\n        return new TextEncoder().encode(s);\n    };\n    StringEncoding.isBrowser = function () {\n        return (typeof window !== 'undefined' && {}.toString.call(window) === '[object Window]');\n    };\n    /**\n     * Returns the string value from some encoding character set.\n     */\n    StringEncoding.encodingName = function (encoding) {\n        return typeof encoding === 'string'\n            ? encoding\n            : encoding.getName();\n    };\n    /**\n     * Returns character set from some encoding character set.\n     */\n    StringEncoding.encodingCharacterSet = function (encoding) {\n        if (encoding instanceof CharacterSetECI) {\n            return encoding;\n        }\n        return CharacterSetECI.getCharacterSetECIByName(encoding);\n    };\n    /**\n     * Runs a fallback for the native decoding funcion.\n     */\n    StringEncoding.decodeFallback = function (bytes, encoding) {\n        var characterSet = this.encodingCharacterSet(encoding);\n        if (StringEncoding.isDecodeFallbackSupported(characterSet)) {\n            var s = '';\n            for (var i = 0, length_1 = bytes.length; i < length_1; i++) {\n                var h = bytes[i].toString(16);\n                if (h.length < 2) {\n                    h = '0' + h;\n                }\n                s += '%' + h;\n            }\n            return decodeURIComponent(s);\n        }\n        if (characterSet.equals(CharacterSetECI.UnicodeBigUnmarked)) {\n            return String.fromCharCode.apply(null, new Uint16Array(bytes.buffer));\n        }\n        throw new UnsupportedOperationException(\"Encoding \" + this.encodingName(encoding) + \" not supported by fallback.\");\n    };\n    StringEncoding.isDecodeFallbackSupported = function (characterSet) {\n        return characterSet.equals(CharacterSetECI.UTF8) ||\n            characterSet.equals(CharacterSetECI.ISO8859_1) ||\n            characterSet.equals(CharacterSetECI.ASCII);\n    };\n    /**\n     * Runs a fallback for the native encoding funcion.\n     *\n     * @see https://stackoverflow.com/a/17192845/4367683\n     */\n    StringEncoding.encodeFallback = function (s) {\n        var encodedURIstring = btoa(unescape(encodeURIComponent(s)));\n        var charList = encodedURIstring.split('');\n        var uintArray = [];\n        for (var i = 0; i < charList.length; i++) {\n            uintArray.push(charList[i].charCodeAt(0));\n        }\n        return new Uint8Array(uintArray);\n    };\n    return StringEncoding;\n}());\nexport default StringEncoding;\n"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,kCAAkC;AAC5E,OAAOC,eAAe,MAAM,2BAA2B;AACvD;AACA;AACA;AACA,IAAIC,cAAc,GAAG,aAAe,YAAY;EAC5C,SAASA,cAAcA,CAAA,EAAG,CAC1B;EACA;AACJ;AACA;EACIA,cAAc,CAACC,MAAM,GAAG,UAAUC,KAAK,EAAEC,QAAQ,EAAE;IAC/C,IAAIC,YAAY,GAAG,IAAI,CAACA,YAAY,CAACD,QAAQ,CAAC;IAC9C,IAAI,IAAI,CAACE,aAAa,EAAE;MACpB,OAAO,IAAI,CAACA,aAAa,CAACH,KAAK,EAAEE,YAAY,CAAC;IAClD;IACA;IACA,IAAI,OAAOE,WAAW,KAAK,WAAW,IAAI,IAAI,CAACC,sBAAsB,CAACH,YAAY,CAAC,EAAE;MACjF,OAAO,IAAI,CAACI,cAAc,CAACN,KAAK,EAAEE,YAAY,CAAC;IACnD;IACA,OAAO,IAAIE,WAAW,CAACF,YAAY,CAAC,CAACH,MAAM,CAACC,KAAK,CAAC;EACtD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIF,cAAc,CAACO,sBAAsB,GAAG,UAAUH,YAAY,EAAE;IAC5D,OAAO,CAACJ,cAAc,CAACS,SAAS,CAAC,CAAC,IAAIL,YAAY,KAAK,YAAY;EACvE,CAAC;EACD;AACJ;AACA;EACIJ,cAAc,CAACU,MAAM,GAAG,UAAUC,CAAC,EAAER,QAAQ,EAAE;IAC3C,IAAIC,YAAY,GAAG,IAAI,CAACA,YAAY,CAACD,QAAQ,CAAC;IAC9C,IAAI,IAAI,CAACS,aAAa,EAAE;MACpB,OAAO,IAAI,CAACA,aAAa,CAACD,CAAC,EAAEP,YAAY,CAAC;IAC9C;IACA;IACA,IAAI,OAAOS,WAAW,KAAK,WAAW,EAAE;MACpC,OAAO,IAAI,CAACC,cAAc,CAACH,CAAC,CAAC;IACjC;IACA;IACA,OAAO,IAAIE,WAAW,CAAC,CAAC,CAACH,MAAM,CAACC,CAAC,CAAC;EACtC,CAAC;EACDX,cAAc,CAACS,SAAS,GAAG,YAAY;IACnC,OAAQ,OAAOM,MAAM,KAAK,WAAW,IAAI,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,CAACF,MAAM,CAAC,KAAK,iBAAiB;EAC3F,CAAC;EACD;AACJ;AACA;EACIf,cAAc,CAACI,YAAY,GAAG,UAAUD,QAAQ,EAAE;IAC9C,OAAO,OAAOA,QAAQ,KAAK,QAAQ,GAC7BA,QAAQ,GACRA,QAAQ,CAACe,OAAO,CAAC,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;EACIlB,cAAc,CAACmB,oBAAoB,GAAG,UAAUhB,QAAQ,EAAE;IACtD,IAAIA,QAAQ,YAAYJ,eAAe,EAAE;MACrC,OAAOI,QAAQ;IACnB;IACA,OAAOJ,eAAe,CAACqB,wBAAwB,CAACjB,QAAQ,CAAC;EAC7D,CAAC;EACD;AACJ;AACA;EACIH,cAAc,CAACQ,cAAc,GAAG,UAAUN,KAAK,EAAEC,QAAQ,EAAE;IACvD,IAAIkB,YAAY,GAAG,IAAI,CAACF,oBAAoB,CAAChB,QAAQ,CAAC;IACtD,IAAIH,cAAc,CAACsB,yBAAyB,CAACD,YAAY,CAAC,EAAE;MACxD,IAAIV,CAAC,GAAG,EAAE;MACV,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEC,QAAQ,GAAGtB,KAAK,CAACuB,MAAM,EAAEF,CAAC,GAAGC,QAAQ,EAAED,CAAC,EAAE,EAAE;QACxD,IAAIG,CAAC,GAAGxB,KAAK,CAACqB,CAAC,CAAC,CAACP,QAAQ,CAAC,EAAE,CAAC;QAC7B,IAAIU,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE;UACdC,CAAC,GAAG,GAAG,GAAGA,CAAC;QACf;QACAf,CAAC,IAAI,GAAG,GAAGe,CAAC;MAChB;MACA,OAAOC,kBAAkB,CAAChB,CAAC,CAAC;IAChC;IACA,IAAIU,YAAY,CAACO,MAAM,CAAC7B,eAAe,CAAC8B,kBAAkB,CAAC,EAAE;MACzD,OAAOC,MAAM,CAACC,YAAY,CAACC,KAAK,CAAC,IAAI,EAAE,IAAIC,WAAW,CAAC/B,KAAK,CAACgC,MAAM,CAAC,CAAC;IACzE;IACA,MAAM,IAAIpC,6BAA6B,CAAC,WAAW,GAAG,IAAI,CAACM,YAAY,CAACD,QAAQ,CAAC,GAAG,6BAA6B,CAAC;EACtH,CAAC;EACDH,cAAc,CAACsB,yBAAyB,GAAG,UAAUD,YAAY,EAAE;IAC/D,OAAOA,YAAY,CAACO,MAAM,CAAC7B,eAAe,CAACoC,IAAI,CAAC,IAC5Cd,YAAY,CAACO,MAAM,CAAC7B,eAAe,CAACqC,SAAS,CAAC,IAC9Cf,YAAY,CAACO,MAAM,CAAC7B,eAAe,CAACsC,KAAK,CAAC;EAClD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIrC,cAAc,CAACc,cAAc,GAAG,UAAUH,CAAC,EAAE;IACzC,IAAI2B,gBAAgB,GAAGC,IAAI,CAACC,QAAQ,CAACC,kBAAkB,CAAC9B,CAAC,CAAC,CAAC,CAAC;IAC5D,IAAI+B,QAAQ,GAAGJ,gBAAgB,CAACK,KAAK,CAAC,EAAE,CAAC;IACzC,IAAIC,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,QAAQ,CAACjB,MAAM,EAAEF,CAAC,EAAE,EAAE;MACtCqB,SAAS,CAACC,IAAI,CAACH,QAAQ,CAACnB,CAAC,CAAC,CAACuB,UAAU,CAAC,CAAC,CAAC,CAAC;IAC7C;IACA,OAAO,IAAIC,UAAU,CAACH,SAAS,CAAC;EACpC,CAAC;EACD,OAAO5C,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}