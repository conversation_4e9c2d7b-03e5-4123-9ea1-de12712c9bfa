{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\nvar __spread = this && this.__spread || function () {\n  for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));\n  return ar;\n};\nimport { MACRO_05_HEADER, MACRO_06_HEADER, MACRO_TRAILER } from './constants';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { MinimalECIInput } from '../../common/MinimalECIInput';\nimport Integer from '../../util/Integer';\nvar Mode;\n(function (Mode) {\n  Mode[Mode[\"ASCII\"] = 0] = \"ASCII\";\n  Mode[Mode[\"C40\"] = 1] = \"C40\";\n  Mode[Mode[\"TEXT\"] = 2] = \"TEXT\";\n  Mode[Mode[\"X12\"] = 3] = \"X12\";\n  Mode[Mode[\"EDF\"] = 4] = \"EDF\";\n  Mode[Mode[\"B256\"] = 5] = \"B256\";\n})(Mode || (Mode = {}));\nvar C40_SHIFT2_CHARS = ['!', '\"', '#', '$', '%', '&', \"'\", '(', ')', '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?', '@', '[', '\\\\', ']', '^', '_'];\nvar MinimalEncoder = /** @class */function () {\n  function MinimalEncoder() {}\n  MinimalEncoder.isExtendedASCII = function (ch, fnc1) {\n    return ch !== fnc1 && ch >= 128 && ch <= 255;\n  };\n  MinimalEncoder.isInC40Shift1Set = function (ch) {\n    return ch <= 31;\n  };\n  MinimalEncoder.isInC40Shift2Set = function (ch, fnc1) {\n    var e_1, _a;\n    try {\n      for (var C40_SHIFT2_CHARS_1 = __values(C40_SHIFT2_CHARS), C40_SHIFT2_CHARS_1_1 = C40_SHIFT2_CHARS_1.next(); !C40_SHIFT2_CHARS_1_1.done; C40_SHIFT2_CHARS_1_1 = C40_SHIFT2_CHARS_1.next()) {\n        var c40Shift2Char = C40_SHIFT2_CHARS_1_1.value;\n        if (c40Shift2Char.charCodeAt(0) === ch) {\n          return true;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (C40_SHIFT2_CHARS_1_1 && !C40_SHIFT2_CHARS_1_1.done && (_a = C40_SHIFT2_CHARS_1.return)) _a.call(C40_SHIFT2_CHARS_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    return ch === fnc1;\n  };\n  MinimalEncoder.isInTextShift1Set = function (ch) {\n    return this.isInC40Shift1Set(ch);\n  };\n  MinimalEncoder.isInTextShift2Set = function (ch, fnc1) {\n    return this.isInC40Shift2Set(ch, fnc1);\n  };\n  /**\n   * Performs message encoding of a DataMatrix message\n   *\n   * @param msg the message\n   * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n   *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n   *   charset to encode any character in the input that can be encoded by it if the charset is among the\n   *   supported charsets.\n   * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not a GS1\n   *   bar code. If the value is not -1 then a FNC1 is also prepended.\n   * @param shape requested shape.\n   * @return the encoded message (the char values range from 0 to 255)\n   */\n  MinimalEncoder.encodeHighLevel = function (msg, priorityCharset, fnc1, shape) {\n    if (priorityCharset === void 0) {\n      priorityCharset = null;\n    }\n    if (fnc1 === void 0) {\n      fnc1 = -1;\n    }\n    if (shape === void 0) {\n      shape = 0 /* FORCE_NONE */;\n    }\n    var macroId = 0;\n    if (msg.startsWith(MACRO_05_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n      macroId = 5;\n      msg = msg.substring(MACRO_05_HEADER.length, msg.length - 2);\n    } else if (msg.startsWith(MACRO_06_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n      macroId = 6;\n      msg = msg.substring(MACRO_06_HEADER.length, msg.length - 2);\n    }\n    return decodeURIComponent(escape(String.fromCharCode.apply(String, __spread(this.encode(msg, priorityCharset, fnc1, shape, macroId)))));\n  };\n  /**\n   * Encodes input minimally and returns an array of the codewords\n   *\n   * @param input The string to encode\n   * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n   *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n   *   charset to encode any character in the input that can be encoded by it if the charset is among the\n   *   supported charsets.\n   * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not a GS1\n   *   bar code. If the value is not -1 then a FNC1 is also prepended.\n   * @param shape requested shape.\n   * @param macroId Prepends the specified macro function in case that a value of 5 or 6 is specified.\n   * @return An array of bytes representing the codewords of a minimal encoding.\n   */\n  MinimalEncoder.encode = function (input, priorityCharset, fnc1, shape, macroId) {\n    return this.encodeMinimally(new Input(input, priorityCharset, fnc1, shape, macroId)).getBytes();\n  };\n  MinimalEncoder.addEdge = function (edges, edge) {\n    var vertexIndex = edge.fromPosition + edge.characterLength;\n    if (edges[vertexIndex][edge.getEndMode()] === null || edges[vertexIndex][edge.getEndMode()].cachedTotalSize > edge.cachedTotalSize) {\n      edges[vertexIndex][edge.getEndMode()] = edge;\n    }\n  };\n  /** @return the number of words in which the string starting at from can be encoded in c40 or text mode.\n   *  The number of characters encoded is returned in characterLength.\n   *  The number of characters encoded is also minimal in the sense that the algorithm stops as soon\n   *  as a character encoding fills a C40 word competely (three C40 values). An exception is at the\n   *  end of the string where two C40 values are allowed (according to the spec the third c40 value\n   *  is filled  with 0 (Shift 1) in this case).\n   */\n  MinimalEncoder.getNumberOfC40Words = function (input, from, c40, characterLength) {\n    var thirdsCount = 0;\n    for (var i = from; i < input.length(); i++) {\n      if (input.isECI(i)) {\n        characterLength[0] = 0;\n        return 0;\n      }\n      var ci = input.charAt(i);\n      if (c40 && HighLevelEncoder.isNativeC40(ci) || !c40 && HighLevelEncoder.isNativeText(ci)) {\n        thirdsCount++; // native\n      } else if (!MinimalEncoder.isExtendedASCII(ci, input.getFNC1Character())) {\n        thirdsCount += 2; // shift\n      } else {\n        var asciiValue = ci & 0xff;\n        if (asciiValue >= 128 && (c40 && HighLevelEncoder.isNativeC40(asciiValue - 128) || !c40 && HighLevelEncoder.isNativeText(asciiValue - 128))) {\n          thirdsCount += 3; // shift, Upper shift\n        } else {\n          thirdsCount += 4; // shift, Upper shift, shift\n        }\n      }\n      if (thirdsCount % 3 === 0 || (thirdsCount - 2) % 3 === 0 && i + 1 === input.length()) {\n        characterLength[0] = i - from + 1;\n        return Math.ceil(thirdsCount / 3.0);\n      }\n    }\n    characterLength[0] = 0;\n    return 0;\n  };\n  MinimalEncoder.addEdges = function (input, edges, from, previous) {\n    var e_2, _a;\n    if (input.isECI(from)) {\n      this.addEdge(edges, new Edge(input, Mode.ASCII, from, 1, previous));\n      return;\n    }\n    var ch = input.charAt(from);\n    if (previous === null || previous.getEndMode() !== Mode.EDF) {\n      // not possible to unlatch a full EDF edge to something\n      // else\n      if (HighLevelEncoder.isDigit(ch) && input.haveNCharacters(from, 2) && HighLevelEncoder.isDigit(input.charAt(from + 1))) {\n        // two digits ASCII encoded\n        this.addEdge(edges, new Edge(input, Mode.ASCII, from, 2, previous));\n      } else {\n        // one ASCII encoded character or an extended character via Upper Shift\n        this.addEdge(edges, new Edge(input, Mode.ASCII, from, 1, previous));\n      }\n      var modes = [Mode.C40, Mode.TEXT];\n      try {\n        for (var modes_1 = __values(modes), modes_1_1 = modes_1.next(); !modes_1_1.done; modes_1_1 = modes_1.next()) {\n          var mode = modes_1_1.value;\n          var characterLength = [];\n          if (MinimalEncoder.getNumberOfC40Words(input, from, mode === Mode.C40, characterLength) > 0) {\n            this.addEdge(edges, new Edge(input, mode, from, characterLength[0], previous));\n          }\n        }\n      } catch (e_2_1) {\n        e_2 = {\n          error: e_2_1\n        };\n      } finally {\n        try {\n          if (modes_1_1 && !modes_1_1.done && (_a = modes_1.return)) _a.call(modes_1);\n        } finally {\n          if (e_2) throw e_2.error;\n        }\n      }\n      if (input.haveNCharacters(from, 3) && HighLevelEncoder.isNativeX12(input.charAt(from)) && HighLevelEncoder.isNativeX12(input.charAt(from + 1)) && HighLevelEncoder.isNativeX12(input.charAt(from + 2))) {\n        this.addEdge(edges, new Edge(input, Mode.X12, from, 3, previous));\n      }\n      this.addEdge(edges, new Edge(input, Mode.B256, from, 1, previous));\n    }\n    // We create 4 EDF edges,  with 1, 2 3 or 4 characters length. The fourth normally doesn't have a latch to ASCII\n    // unless it is 2 characters away from the end of the input.\n    var i;\n    for (i = 0; i < 3; i++) {\n      var pos = from + i;\n      if (input.haveNCharacters(pos, 1) && HighLevelEncoder.isNativeEDIFACT(input.charAt(pos))) {\n        this.addEdge(edges, new Edge(input, Mode.EDF, from, i + 1, previous));\n      } else {\n        break;\n      }\n    }\n    if (i === 3 && input.haveNCharacters(from, 4) && HighLevelEncoder.isNativeEDIFACT(input.charAt(from + 3))) {\n      this.addEdge(edges, new Edge(input, Mode.EDF, from, 4, previous));\n    }\n  };\n  MinimalEncoder.encodeMinimally = function (input) {\n    /* The minimal encoding is computed by Dijkstra. The acyclic graph is modeled as follows:\n     * A vertex represents a combination of a position in the input and an encoding mode where position 0\n     * denotes the position left of the first character, 1 the position left of the second character and so on.\n     * Likewise the end vertices are located after the last character at position input.length().\n     * For any position there might be up to six vertices, one for each of the encoding types ASCII, C40, TEXT, X12,\n     * EDF and B256.\n     *\n     * As an example consider the input string \"ABC123\" then at position 0 there is only one vertex with the default\n     * ASCII encodation. At position 3 there might be vertices for the types ASCII, C40, X12, EDF and B256.\n     *\n     * An edge leading to such a vertex encodes one or more of the characters left of the position that the vertex\n     * represents. It encodes the characters in the encoding mode of the vertex that it ends on. In other words,\n     * all edges leading to a particular vertex encode the same characters (the length of the suffix can vary) using the same\n     * encoding mode.\n     * As an example consider the input string \"ABC123\" and the vertex (4,EDF). Possible edges leading to this vertex\n     * are:\n     *   (0,ASCII)  --EDF(ABC1)--> (4,EDF)\n     *   (1,ASCII)  --EDF(BC1)-->  (4,EDF)\n     *   (1,B256)   --EDF(BC1)-->  (4,EDF)\n     *   (1,EDF)    --EDF(BC1)-->  (4,EDF)\n     *   (2,ASCII)  --EDF(C1)-->   (4,EDF)\n     *   (2,B256)   --EDF(C1)-->   (4,EDF)\n     *   (2,EDF)    --EDF(C1)-->   (4,EDF)\n     *   (3,ASCII)  --EDF(1)-->    (4,EDF)\n     *   (3,B256)   --EDF(1)-->    (4,EDF)\n     *   (3,EDF)    --EDF(1)-->    (4,EDF)\n     *   (3,C40)    --EDF(1)-->    (4,EDF)\n     *   (3,X12)    --EDF(1)-->    (4,EDF)\n     *\n     * The edges leading to a vertex are stored in such a way that there is a fast way to enumerate the edges ending\n     * on a particular vertex.\n     *\n     * The algorithm processes the vertices in order of their position thereby performing the following:\n     *\n     * For every vertex at position i the algorithm enumerates the edges ending on the vertex and removes all but the\n     * shortest from that list.\n     * Then it processes the vertices for the position i+1. If i+1 == input.length() then the algorithm ends\n     * and chooses the the edge with the smallest size from any of the edges leading to vertices at this position.\n     * Otherwise the algorithm computes all possible outgoing edges for the vertices at the position i+1\n     *\n     * Examples:\n     * The process is illustrated by showing the graph (edges) after each iteration from left to right over the input:\n     * An edge is drawn as follows \"(\" + fromVertex + \") -- \" + encodingMode + \"(\" + encodedInput + \") (\" +\n     * accumulatedSize + \") --> (\" + toVertex + \")\"\n     *\n     * Example 1 encoding the string \"ABCDEFG\":\n     *\n     *\n     * Situation after adding edges to the start vertex (0,ASCII)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n     * (0,ASCII) B256(A) (3) --> (1,B256)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n     * (0,ASCII) C40(ABC) (3) --> (3,C40)\n     * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n     * (0,ASCII) X12(ABC) (3) --> (3,X12)\n     * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n     * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n     *\n     * Situation after adding edges to vertices at position 1\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n     * (0,ASCII) B256(A) (3) --> (1,B256)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n     * (0,ASCII) C40(ABC) (3) --> (3,C40)\n     * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n     * (0,ASCII) X12(ABC) (3) --> (3,X12)\n     * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n     * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) B256(B) (4) --> (2,B256)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BC) (5) --> (3,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCD) (5) --> (4,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) ASCII(B) (4) --> (2,ASCII)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n     * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BC) (6) --> (3,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) C40(BCD) (5) --> (4,C40)\n     * (0,ASCII) B256(A) (3) --> (1,B256) TEXT(BCD) (7) --> (4,TEXT)\n     * (0,ASCII) B256(A) (3) --> (1,B256) X12(BCD) (5) --> (4,X12)\n     * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCD) (6) --> (4,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCDE) (6) --> (5,EDF)\n     *\n     * Edge \"(1,ASCII) ASCII(B) (2) --> (2,ASCII)\" is minimal for the vertex (2,ASCII) so that edge \"(1,B256) ASCII(B) (4) --> (2,ASCII)\" is removed.\n     * Edge \"(1,B256) B256(B) (3) --> (2,B256)\" is minimal for the vertext (2,B256) so that the edge \"(1,ASCII) B256(B) (4) --> (2,B256)\" is removed.\n     *\n     * Situation after adding edges to vertices at position 2\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n     * (0,ASCII) B256(A) (3) --> (1,B256)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n     * (0,ASCII) C40(ABC) (3) --> (3,C40)\n     * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n     * (0,ASCII) X12(ABC) (3) --> (3,X12)\n     * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n     * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BC) (5) --> (3,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCD) (5) --> (4,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n     * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BC) (6) --> (3,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) C40(BCD) (5) --> (4,C40)\n     * (0,ASCII) B256(A) (3) --> (1,B256) TEXT(BCD) (7) --> (4,TEXT)\n     * (0,ASCII) B256(A) (3) --> (1,B256) X12(BCD) (5) --> (4,X12)\n     * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCD) (6) --> (4,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCDE) (6) --> (5,EDF)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF) ASCII(C) (5) --> (3,ASCII)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF) B256(C) (6) --> (3,B256)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CD) (7) --> (4,EDF)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF) C40(CDE) (6) --> (5,C40)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF) TEXT(CDE) (8) --> (5,TEXT)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF) X12(CDE) (6) --> (5,X12)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CDE) (7) --> (5,EDF)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CDEF) (7) --> (6,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) B256(C) (5) --> (3,B256)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CD) (6) --> (4,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) C40(CDE) (5) --> (5,C40)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) TEXT(CDE) (7) --> (5,TEXT)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) X12(CDE) (5) --> (5,X12)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDE) (6) --> (5,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDEF) (6) --> (6,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) ASCII(C) (4) --> (3,ASCII)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CD) (6) --> (4,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) C40(CDE) (5) --> (5,C40)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) TEXT(CDE) (7) --> (5,TEXT)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) X12(CDE) (5) --> (5,X12)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CDE) (6) --> (5,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CDEF) (6) --> (6,EDF)\n     *\n     * Edge \"(2,ASCII) ASCII(C) (3) --> (3,ASCII)\" is minimal for the vertex (3,ASCII) so that edges \"(2,EDF) ASCII(C) (5) --> (3,ASCII)\"\n     * and \"(2,B256) ASCII(C) (4) --> (3,ASCII)\" can be removed.\n     * Edge \"(0,ASCII) EDF(ABC) (4) --> (3,EDF)\" is minimal for the vertex (3,EDF) so that edges \"(1,ASCII) EDF(BC) (5) --> (3,EDF)\"\n     * and \"(1,B256) EDF(BC) (6) --> (3,EDF)\" can be removed.\n     * Edge \"(2,B256) B256(C) (4) --> (3,B256)\" is minimal for the vertex (3,B256) so that edges \"(2,ASCII) B256(C) (5) --> (3,B256)\"\n     * and \"(2,EDF) B256(C) (6) --> (3,B256)\" can be removed.\n     *\n     * This continues for vertices 3 thru 7\n     *\n     * Situation after adding edges to vertices at position 7\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n     * (0,ASCII) B256(A) (3) --> (1,B256)\n     * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n     * (0,ASCII) C40(ABC) (3) --> (3,C40)\n     * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n     * (0,ASCII) X12(ABC) (3) --> (3,X12)\n     * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n     * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n     * (0,ASCII) C40(ABC) (3) --> (3,C40) C40(DEF) (5) --> (6,C40)\n     * (0,ASCII) X12(ABC) (3) --> (3,X12) X12(DEF) (5) --> (6,X12)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) C40(CDE) (5) --> (5,C40)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) TEXT(CDE) (7) --> (5,TEXT)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) X12(CDE) (5) --> (5,X12)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDEF) (6) --> (6,EDF)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40) C40(EFG) (6) --> (7,C40)    //Solution 1\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12) X12(EFG) (6) --> (7,X12)    //Solution 2\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) TEXT(DEF) (8) --> (6,TEXT)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) EDF(DEFG) (7) --> (7,EDF)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) TEXT(EFG) (9) --> (7,TEXT)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII) ASCII(F) (6) --> (6,ASCII)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256) B256(F) (7) --> (6,B256)\n     * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII) ASCII(F) (6) --> (6,ASCII) ASCII(G) (7) --> (7,ASCII)\n     * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256) B256(F) (7) --> (6,B256) B256(G) (8) --> (7,B256)\n     *\n     * Hence a minimal encoding of \"ABCDEFG\" is either ASCII(A),C40(BCDEFG) or ASCII(A), X12(BCDEFG) with a size of 5 bytes.\n     */\n    var inputLength = input.length();\n    // Array that represents vertices. There is a vertex for every character and mode.\n    // The last dimension in the array below encodes the 6 modes ASCII, C40, TEXT, X12, EDF and B256\n    var edges = Array(inputLength + 1).fill(null).map(function () {\n      return Array(6).fill(0);\n    });\n    this.addEdges(input, edges, 0, null);\n    for (var i = 1; i <= inputLength; i++) {\n      for (var j = 0; j < 6; j++) {\n        if (edges[i][j] !== null && i < inputLength) {\n          this.addEdges(input, edges, i, edges[i][j]);\n        }\n      }\n      // optimize memory by removing edges that have been passed.\n      for (var j = 0; j < 6; j++) {\n        edges[i - 1][j] = null;\n      }\n    }\n    var minimalJ = -1;\n    var minimalSize = Integer.MAX_VALUE;\n    for (var j = 0; j < 6; j++) {\n      if (edges[inputLength][j] !== null) {\n        var edge = edges[inputLength][j];\n        var size = j >= 1 && j <= 3 ? edge.cachedTotalSize + 1 : edge.cachedTotalSize; // C40, TEXT and X12 need an\n        // extra unlatch at the end\n        if (size < minimalSize) {\n          minimalSize = size;\n          minimalJ = j;\n        }\n      }\n    }\n    if (minimalJ < 0) {\n      throw new Error('Failed to encode \"' + input + '\"');\n    }\n    return new Result(edges[inputLength][minimalJ]);\n  };\n  return MinimalEncoder;\n}();\nexport { MinimalEncoder };\nvar Result = /** @class */function () {\n  function Result(solution) {\n    var input = solution.input;\n    var size = 0;\n    var bytesAL = [];\n    var randomizePostfixLength = [];\n    var randomizeLengths = [];\n    if ((solution.mode === Mode.C40 || solution.mode === Mode.TEXT || solution.mode === Mode.X12) && solution.getEndMode() !== Mode.ASCII) {\n      size += this.prepend(Edge.getBytes(254), bytesAL);\n    }\n    var current = solution;\n    while (current !== null) {\n      size += this.prepend(current.getDataBytes(), bytesAL);\n      if (current.previous === null || current.getPreviousStartMode() !== current.getMode()) {\n        if (current.getMode() === Mode.B256) {\n          if (size <= 249) {\n            bytesAL.unshift(size);\n            size++;\n          } else {\n            bytesAL.unshift(size % 250);\n            bytesAL.unshift(size / 250 + 249);\n            size += 2;\n          }\n          randomizePostfixLength.push(bytesAL.length);\n          randomizeLengths.push(size);\n        }\n        this.prepend(current.getLatchBytes(), bytesAL);\n        size = 0;\n      }\n      current = current.previous;\n    }\n    if (input.getMacroId() === 5) {\n      size += this.prepend(Edge.getBytes(236), bytesAL);\n    } else if (input.getMacroId() === 6) {\n      size += this.prepend(Edge.getBytes(237), bytesAL);\n    }\n    if (input.getFNC1Character() > 0) {\n      size += this.prepend(Edge.getBytes(232), bytesAL);\n    }\n    for (var i = 0; i < randomizePostfixLength.length; i++) {\n      this.applyRandomPattern(bytesAL, bytesAL.length - randomizePostfixLength[i], randomizeLengths[i]);\n    }\n    // add padding\n    var capacity = solution.getMinSymbolSize(bytesAL.length);\n    if (bytesAL.length < capacity) {\n      bytesAL.push(129);\n    }\n    while (bytesAL.length < capacity) {\n      bytesAL.push(this.randomize253State(bytesAL.length + 1));\n    }\n    this.bytes = new Uint8Array(bytesAL.length);\n    for (var i = 0; i < this.bytes.length; i++) {\n      this.bytes[i] = bytesAL[i];\n    }\n  }\n  Result.prototype.prepend = function (bytes, into) {\n    for (var i = bytes.length - 1; i >= 0; i--) {\n      into.unshift(bytes[i]);\n    }\n    return bytes.length;\n  };\n  Result.prototype.randomize253State = function (codewordPosition) {\n    var pseudoRandom = 149 * codewordPosition % 253 + 1;\n    var tempVariable = 129 + pseudoRandom;\n    return tempVariable <= 254 ? tempVariable : tempVariable - 254;\n  };\n  Result.prototype.applyRandomPattern = function (bytesAL, startPosition, length) {\n    for (var i = 0; i < length; i++) {\n      // See \"B.1 253-state algorithm\n      var Pad_codeword_position = startPosition + i;\n      var Pad_codeword_value = bytesAL[Pad_codeword_position] & 0xff;\n      var pseudo_random_number = 149 * (Pad_codeword_position + 1) % 255 + 1;\n      var temp_variable = Pad_codeword_value + pseudo_random_number;\n      bytesAL[Pad_codeword_position] = temp_variable <= 255 ? temp_variable : temp_variable - 256;\n    }\n  };\n  Result.prototype.getBytes = function () {\n    return this.bytes;\n  };\n  return Result;\n}();\nvar Edge = /** @class */function () {\n  function Edge(input, mode, fromPosition, characterLength, previous) {\n    this.input = input;\n    this.mode = mode;\n    this.fromPosition = fromPosition;\n    this.characterLength = characterLength;\n    this.previous = previous;\n    this.allCodewordCapacities = [3, 5, 8, 10, 12, 16, 18, 22, 30, 32, 36, 44, 49, 62, 86, 114, 144, 174, 204, 280, 368, 456, 576, 696, 816, 1050, 1304, 1558];\n    this.squareCodewordCapacities = [3, 5, 8, 12, 18, 22, 30, 36, 44, 62, 86, 114, 144, 174, 204, 280, 368, 456, 576, 696, 816, 1050, 1304, 1558];\n    this.rectangularCodewordCapacities = [5, 10, 16, 33, 32, 49];\n    if (!(fromPosition + characterLength <= input.length())) {\n      throw new Error('Invalid edge');\n    }\n    var size = previous !== null ? previous.cachedTotalSize : 0;\n    var previousMode = this.getPreviousMode();\n    /*\n     * Switching modes\n     * ASCII -> C40: latch 230\n     * ASCII -> TEXT: latch 239\n     * ASCII -> X12: latch 238\n     * ASCII -> EDF: latch 240\n     * ASCII -> B256: latch 231\n     * C40 -> ASCII: word(c1,c2,c3), 254\n     * TEXT -> ASCII: word(c1,c2,c3), 254\n     * X12 -> ASCII: word(c1,c2,c3), 254\n     * EDIFACT -> ASCII: Unlatch character,0,0,0 or c1,Unlatch character,0,0 or c1,c2,Unlatch character,0 or\n     * c1,c2,c3,Unlatch character\n     * B256 -> ASCII: without latch after n bytes\n     */\n    switch (mode) {\n      case Mode.ASCII:\n        size++;\n        if (input.isECI(fromPosition) || MinimalEncoder.isExtendedASCII(input.charAt(fromPosition), input.getFNC1Character())) {\n          size++;\n        }\n        if (previousMode === Mode.C40 || previousMode === Mode.TEXT || previousMode === Mode.X12) {\n          size++; // unlatch 254 to ASCII\n        }\n        break;\n      case Mode.B256:\n        size++;\n        if (previousMode !== Mode.B256) {\n          size++; // byte count\n        } else if (this.getB256Size() === 250) {\n          size++; // extra byte count\n        }\n        if (previousMode === Mode.ASCII) {\n          size++; // latch to B256\n        } else if (previousMode === Mode.C40 || previousMode === Mode.TEXT || previousMode === Mode.X12) {\n          size += 2; // unlatch to ASCII, latch to B256\n        }\n        break;\n      case Mode.C40:\n      case Mode.TEXT:\n      case Mode.X12:\n        if (mode === Mode.X12) {\n          size += 2;\n        } else {\n          var charLen = [];\n          size += MinimalEncoder.getNumberOfC40Words(input, fromPosition, mode === Mode.C40, charLen) * 2;\n        }\n        if (previousMode === Mode.ASCII || previousMode === Mode.B256) {\n          size++; // additional byte for latch from ASCII to this mode\n        } else if (previousMode !== mode && (previousMode === Mode.C40 || previousMode === Mode.TEXT || previousMode === Mode.X12)) {\n          size += 2; // unlatch 254 to ASCII followed by latch to this mode\n        }\n        break;\n      case Mode.EDF:\n        size += 3;\n        if (previousMode === Mode.ASCII || previousMode === Mode.B256) {\n          size++; // additional byte for latch from ASCII to this mode\n        } else if (previousMode === Mode.C40 || previousMode === Mode.TEXT || previousMode === Mode.X12) {\n          size += 2; // unlatch 254 to ASCII followed by latch to this mode\n        }\n        break;\n    }\n    this.cachedTotalSize = size;\n  }\n  // does not count beyond 250\n  Edge.prototype.getB256Size = function () {\n    var cnt = 0;\n    var current = this;\n    while (current !== null && current.mode === Mode.B256 && cnt <= 250) {\n      cnt++;\n      current = current.previous;\n    }\n    return cnt;\n  };\n  Edge.prototype.getPreviousStartMode = function () {\n    return this.previous === null ? Mode.ASCII : this.previous.mode;\n  };\n  Edge.prototype.getPreviousMode = function () {\n    return this.previous === null ? Mode.ASCII : this.previous.getEndMode();\n  };\n  /** Returns Mode.ASCII in case that:\n   *  - Mode is EDIFACT and characterLength is less than 4 or the remaining characters can be encoded in at most 2\n   *    ASCII bytes.\n   *  - Mode is C40, TEXT or X12 and the remaining characters can be encoded in at most 1 ASCII byte.\n   *  Returns mode in all other cases.\n   * */\n  Edge.prototype.getEndMode = function () {\n    if (this.mode === Mode.EDF) {\n      if (this.characterLength < 4) {\n        return Mode.ASCII;\n      }\n      var lastASCII = this.getLastASCII(); // see 5.2.8.2 EDIFACT encodation Rules\n      if (lastASCII > 0 && this.getCodewordsRemaining(this.cachedTotalSize + lastASCII) <= 2 - lastASCII) {\n        return Mode.ASCII;\n      }\n    }\n    if (this.mode === Mode.C40 || this.mode === Mode.TEXT || this.mode === Mode.X12) {\n      // see 5.2.5.2 C40 encodation rules and 5.2.7.2 ANSI X12 encodation rules\n      if (this.fromPosition + this.characterLength >= this.input.length() && this.getCodewordsRemaining(this.cachedTotalSize) === 0) {\n        return Mode.ASCII;\n      }\n      var lastASCII = this.getLastASCII();\n      if (lastASCII === 1 && this.getCodewordsRemaining(this.cachedTotalSize + 1) === 0) {\n        return Mode.ASCII;\n      }\n    }\n    return this.mode;\n  };\n  Edge.prototype.getMode = function () {\n    return this.mode;\n  };\n  /** Peeks ahead and returns 1 if the postfix consists of exactly two digits, 2 if the postfix consists of exactly\n   *  two consecutive digits and a non extended character or of 4 digits.\n   *  Returns 0 in any other case\n   **/\n  Edge.prototype.getLastASCII = function () {\n    var length = this.input.length();\n    var from = this.fromPosition + this.characterLength;\n    if (length - from > 4 || from >= length) {\n      return 0;\n    }\n    if (length - from === 1) {\n      if (MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character())) {\n        return 0;\n      }\n      return 1;\n    }\n    if (length - from === 2) {\n      if (MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character()) || MinimalEncoder.isExtendedASCII(this.input.charAt(from + 1), this.input.getFNC1Character())) {\n        return 0;\n      }\n      if (HighLevelEncoder.isDigit(this.input.charAt(from)) && HighLevelEncoder.isDigit(this.input.charAt(from + 1))) {\n        return 1;\n      }\n      return 2;\n    }\n    if (length - from === 3) {\n      if (HighLevelEncoder.isDigit(this.input.charAt(from)) && HighLevelEncoder.isDigit(this.input.charAt(from + 1)) && !MinimalEncoder.isExtendedASCII(this.input.charAt(from + 2), this.input.getFNC1Character())) {\n        return 2;\n      }\n      if (HighLevelEncoder.isDigit(this.input.charAt(from + 1)) && HighLevelEncoder.isDigit(this.input.charAt(from + 2)) && !MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character())) {\n        return 2;\n      }\n      return 0;\n    }\n    if (HighLevelEncoder.isDigit(this.input.charAt(from)) && HighLevelEncoder.isDigit(this.input.charAt(from + 1)) && HighLevelEncoder.isDigit(this.input.charAt(from + 2)) && HighLevelEncoder.isDigit(this.input.charAt(from + 3))) {\n      return 2;\n    }\n    return 0;\n  };\n  /** Returns the capacity in codewords of the smallest symbol that has enough capacity to fit the given minimal\n   * number of codewords.\n   **/\n  Edge.prototype.getMinSymbolSize = function (minimum) {\n    var e_3, _a, e_4, _b, e_5, _c;\n    switch (this.input.getShapeHint()) {\n      case 1 /* FORCE_SQUARE */:\n        try {\n          for (var _d = __values(this.squareCodewordCapacities), _e = _d.next(); !_e.done; _e = _d.next()) {\n            var capacity = _e.value;\n            if (capacity >= minimum) {\n              return capacity;\n            }\n          }\n        } catch (e_3_1) {\n          e_3 = {\n            error: e_3_1\n          };\n        } finally {\n          try {\n            if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n          } finally {\n            if (e_3) throw e_3.error;\n          }\n        }\n        break;\n      case 2 /* FORCE_RECTANGLE */:\n        try {\n          for (var _f = __values(this.rectangularCodewordCapacities), _g = _f.next(); !_g.done; _g = _f.next()) {\n            var capacity = _g.value;\n            if (capacity >= minimum) {\n              return capacity;\n            }\n          }\n        } catch (e_4_1) {\n          e_4 = {\n            error: e_4_1\n          };\n        } finally {\n          try {\n            if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n          } finally {\n            if (e_4) throw e_4.error;\n          }\n        }\n        break;\n    }\n    try {\n      for (var _h = __values(this.allCodewordCapacities), _j = _h.next(); !_j.done; _j = _h.next()) {\n        var capacity = _j.value;\n        if (capacity >= minimum) {\n          return capacity;\n        }\n      }\n    } catch (e_5_1) {\n      e_5 = {\n        error: e_5_1\n      };\n    } finally {\n      try {\n        if (_j && !_j.done && (_c = _h.return)) _c.call(_h);\n      } finally {\n        if (e_5) throw e_5.error;\n      }\n    }\n    return this.allCodewordCapacities[this.allCodewordCapacities.length - 1];\n  };\n  /** Returns the remaining capacity in codewords of the smallest symbol that has enough capacity to fit the given\n   * minimal number of codewords.\n   **/\n  Edge.prototype.getCodewordsRemaining = function (minimum) {\n    return this.getMinSymbolSize(minimum) - minimum;\n  };\n  Edge.getBytes = function (c1, c2) {\n    var result = new Uint8Array(c2 ? 2 : 1);\n    result[0] = c1;\n    if (c2) {\n      result[1] = c2;\n    }\n    return result;\n  };\n  Edge.prototype.setC40Word = function (bytes, offset, c1, c2, c3) {\n    var val16 = 1600 * (c1 & 0xff) + 40 * (c2 & 0xff) + (c3 & 0xff) + 1;\n    bytes[offset] = val16 / 256;\n    bytes[offset + 1] = val16 % 256;\n  };\n  Edge.prototype.getX12Value = function (c) {\n    return c === 13 ? 0 : c === 42 ? 1 : c === 62 ? 2 : c === 32 ? 3 : c >= 48 && c <= 57 ? c - 44 : c >= 65 && c <= 90 ? c - 51 : c;\n  };\n  Edge.prototype.getX12Words = function () {\n    if (!(this.characterLength % 3 === 0)) {\n      throw new Error('X12 words must be a multiple of 3');\n    }\n    var result = new Uint8Array(this.characterLength / 3 * 2);\n    for (var i = 0; i < result.length; i += 2) {\n      this.setC40Word(result, i, this.getX12Value(this.input.charAt(this.fromPosition + i / 2 * 3)), this.getX12Value(this.input.charAt(this.fromPosition + i / 2 * 3 + 1)), this.getX12Value(this.input.charAt(this.fromPosition + i / 2 * 3 + 2)));\n    }\n    return result;\n  };\n  Edge.prototype.getShiftValue = function (c, c40, fnc1) {\n    return c40 && MinimalEncoder.isInC40Shift1Set(c) || !c40 && MinimalEncoder.isInTextShift1Set(c) ? 0 : c40 && MinimalEncoder.isInC40Shift2Set(c, fnc1) || !c40 && MinimalEncoder.isInTextShift2Set(c, fnc1) ? 1 : 2;\n  };\n  Edge.prototype.getC40Value = function (c40, setIndex, c, fnc1) {\n    if (c === fnc1) {\n      if (!(setIndex === 2)) {\n        throw new Error('FNC1 cannot be used in C40 shift 2');\n      }\n      return 27;\n    }\n    if (c40) {\n      return c <= 31 ? c : c === 32 ? 3 : c <= 47 ? c - 33 : c <= 57 ? c - 44 : c <= 64 ? c - 43 : c <= 90 ? c - 51 : c <= 95 ? c - 69 : c <= 127 ? c - 96 : c;\n    } else {\n      return c === 0 ? 0 : setIndex === 0 && c <= 3 ? c - 1 // is this a bug in the spec?\n      : setIndex === 1 && c <= 31 ? c : c === 32 ? 3 : c >= 33 && c <= 47 ? c - 33 : c >= 48 && c <= 57 ? c - 44 : c >= 58 && c <= 64 ? c - 43 : c >= 65 && c <= 90 ? c - 64 : c >= 91 && c <= 95 ? c - 69 : c === 96 ? 0 : c >= 97 && c <= 122 ? c - 83 : c >= 123 && c <= 127 ? c - 96 : c;\n    }\n  };\n  Edge.prototype.getC40Words = function (c40, fnc1) {\n    var c40Values = [];\n    for (var i = 0; i < this.characterLength; i++) {\n      var ci = this.input.charAt(this.fromPosition + i);\n      if (c40 && HighLevelEncoder.isNativeC40(ci) || !c40 && HighLevelEncoder.isNativeText(ci)) {\n        c40Values.push(this.getC40Value(c40, 0, ci, fnc1));\n      } else if (!MinimalEncoder.isExtendedASCII(ci, fnc1)) {\n        var shiftValue = this.getShiftValue(ci, c40, fnc1);\n        c40Values.push(shiftValue); // Shift[123]\n        c40Values.push(this.getC40Value(c40, shiftValue, ci, fnc1));\n      } else {\n        var asciiValue = (ci & 0xff) - 128;\n        if (c40 && HighLevelEncoder.isNativeC40(asciiValue) || !c40 && HighLevelEncoder.isNativeText(asciiValue)) {\n          c40Values.push(1); // Shift 2\n          c40Values.push(30); // Upper Shift\n          c40Values.push(this.getC40Value(c40, 0, asciiValue, fnc1));\n        } else {\n          c40Values.push(1); // Shift 2\n          c40Values.push(30); // Upper Shift\n          var shiftValue = this.getShiftValue(asciiValue, c40, fnc1);\n          c40Values.push(shiftValue); // Shift[123]\n          c40Values.push(this.getC40Value(c40, shiftValue, asciiValue, fnc1));\n        }\n      }\n    }\n    if (c40Values.length % 3 !== 0) {\n      if (!((c40Values.length - 2) % 3 === 0 && this.fromPosition + this.characterLength === this.input.length())) {\n        throw new Error('C40 words must be a multiple of 3');\n      }\n      c40Values.push(0); // pad with 0 (Shift 1)\n    }\n    var result = new Uint8Array(c40Values.length / 3 * 2);\n    var byteIndex = 0;\n    for (var i = 0; i < c40Values.length; i += 3) {\n      this.setC40Word(result, byteIndex, c40Values[i] & 0xff, c40Values[i + 1] & 0xff, c40Values[i + 2] & 0xff);\n      byteIndex += 2;\n    }\n    return result;\n  };\n  Edge.prototype.getEDFBytes = function () {\n    var numberOfThirds = Math.ceil(this.characterLength / 4.0);\n    var result = new Uint8Array(numberOfThirds * 3);\n    var pos = this.fromPosition;\n    var endPos = Math.min(this.fromPosition + this.characterLength - 1, this.input.length() - 1);\n    for (var i = 0; i < numberOfThirds; i += 3) {\n      var edfValues = [];\n      for (var j = 0; j < 4; j++) {\n        if (pos <= endPos) {\n          edfValues[j] = this.input.charAt(pos++) & 0x3f;\n        } else {\n          edfValues[j] = pos === endPos + 1 ? 0x1f : 0;\n        }\n      }\n      var val24 = edfValues[0] << 18;\n      val24 |= edfValues[1] << 12;\n      val24 |= edfValues[2] << 6;\n      val24 |= edfValues[3];\n      result[i] = val24 >> 16 & 0xff;\n      result[i + 1] = val24 >> 8 & 0xff;\n      result[i + 2] = val24 & 0xff;\n    }\n    return result;\n  };\n  Edge.prototype.getLatchBytes = function () {\n    switch (this.getPreviousMode()) {\n      case Mode.ASCII:\n      case Mode.B256:\n        // after B256 ends (via length) we are back to ASCII\n        switch (this.mode) {\n          case Mode.B256:\n            return Edge.getBytes(231);\n          case Mode.C40:\n            return Edge.getBytes(230);\n          case Mode.TEXT:\n            return Edge.getBytes(239);\n          case Mode.X12:\n            return Edge.getBytes(238);\n          case Mode.EDF:\n            return Edge.getBytes(240);\n        }\n        break;\n      case Mode.C40:\n      case Mode.TEXT:\n      case Mode.X12:\n        if (this.mode !== this.getPreviousMode()) {\n          switch (this.mode) {\n            case Mode.ASCII:\n              return Edge.getBytes(254);\n            case Mode.B256:\n              return Edge.getBytes(254, 231);\n            case Mode.C40:\n              return Edge.getBytes(254, 230);\n            case Mode.TEXT:\n              return Edge.getBytes(254, 239);\n            case Mode.X12:\n              return Edge.getBytes(254, 238);\n            case Mode.EDF:\n              return Edge.getBytes(254, 240);\n          }\n        }\n        break;\n      case Mode.EDF:\n        // The rightmost EDIFACT edge always contains an unlatch character\n        if (this.mode !== Mode.EDF) {\n          throw new Error('Cannot switch from EDF to ' + this.mode);\n        }\n        break;\n    }\n    return new Uint8Array(0);\n  };\n  // Important: The function does not return the length bytes (one or two) in case of B256 encoding\n  Edge.prototype.getDataBytes = function () {\n    switch (this.mode) {\n      case Mode.ASCII:\n        if (this.input.isECI(this.fromPosition)) {\n          return Edge.getBytes(241, this.input.getECIValue(this.fromPosition) + 1);\n        } else if (MinimalEncoder.isExtendedASCII(this.input.charAt(this.fromPosition), this.input.getFNC1Character())) {\n          return Edge.getBytes(235, this.input.charAt(this.fromPosition) - 127);\n        } else if (this.characterLength === 2) {\n          return Edge.getBytes(this.input.charAt(this.fromPosition) * 10 + this.input.charAt(this.fromPosition + 1) + 130);\n        } else if (this.input.isFNC1(this.fromPosition)) {\n          return Edge.getBytes(232);\n        } else {\n          return Edge.getBytes(this.input.charAt(this.fromPosition) + 1);\n        }\n      case Mode.B256:\n        return Edge.getBytes(this.input.charAt(this.fromPosition));\n      case Mode.C40:\n        return this.getC40Words(true, this.input.getFNC1Character());\n      case Mode.TEXT:\n        return this.getC40Words(false, this.input.getFNC1Character());\n      case Mode.X12:\n        return this.getX12Words();\n      case Mode.EDF:\n        return this.getEDFBytes();\n    }\n  };\n  return Edge;\n}();\nvar Input = /** @class */function (_super) {\n  __extends(Input, _super);\n  function Input(stringToEncode, priorityCharset, fnc1, shape, macroId) {\n    var _this = _super.call(this, stringToEncode, priorityCharset, fnc1) || this;\n    _this.shape = shape;\n    _this.macroId = macroId;\n    return _this;\n  }\n  Input.prototype.getMacroId = function () {\n    return this.macroId;\n  };\n  Input.prototype.getShapeHint = function () {\n    return this.shape;\n  };\n  return Input;\n}(MinimalECIInput);", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "__read", "n", "r", "ar", "e", "push", "error", "__spread", "arguments", "concat", "MACRO_05_HEADER", "MACRO_06_HEADER", "MACRO_TRAILER", "HighLevelEncoder", "MinimalECIInput", "Integer", "Mode", "C40_SHIFT2_CHARS", "MinimalEncoder", "isExtendedASCII", "ch", "fnc1", "isInC40Shift1Set", "isInC40Shift2Set", "e_1", "_a", "C40_SHIFT2_CHARS_1", "C40_SHIFT2_CHARS_1_1", "c40Shift2Char", "charCodeAt", "e_1_1", "return", "isInTextShift1Set", "isInTextShift2Set", "encodeHighLevel", "msg", "priorityCharset", "shape", "macroId", "startsWith", "endsWith", "substring", "decodeURIComponent", "escape", "String", "fromCharCode", "apply", "encode", "input", "encodeMinimally", "Input", "getBytes", "addEdge", "edges", "edge", "vertexIndex", "fromPosition", "<PERSON><PERSON><PERSON><PERSON>", "getEndMode", "cachedTotalSize", "getNumberOfC40Words", "from", "c40", "thirdsCount", "isECI", "ci", "char<PERSON>t", "isNativeC40", "isNativeText", "getFNC1Character", "asciiValue", "Math", "ceil", "addEdges", "previous", "e_2", "Edge", "ASCII", "EDF", "isDigit", "haveNCharacters", "modes", "C40", "TEXT", "modes_1", "modes_1_1", "mode", "e_2_1", "isNativeX12", "X12", "B256", "pos", "isNativeEDIFACT", "inputLength", "fill", "map", "j", "minimalJ", "minimalSize", "MAX_VALUE", "size", "Error", "Result", "solution", "bytesAL", "randomizePostfixLength", "randomizeLengths", "prepend", "current", "getDataBytes", "getPreviousStartMode", "getMode", "unshift", "getLatchBytes", "getMacroId", "applyRandomPattern", "capacity", "getMinSymbolSize", "randomize253State", "bytes", "Uint8Array", "into", "codewordPosition", "pseudoRandom", "tempVariable", "startPosition", "Pad_codeword_position", "Pad_codeword_value", "pseudo_random_number", "temp_variable", "allCodewordCapacities", "squareCodewordCapacities", "rectangularCodewordCapacities", "previousMode", "getPreviousMode", "getB256Size", "char<PERSON>en", "cnt", "lastASCII", "getLastASCII", "getCodewordsRemaining", "minimum", "e_3", "e_4", "_b", "e_5", "_c", "getShapeHint", "_d", "_e", "e_3_1", "_f", "_g", "e_4_1", "_h", "_j", "e_5_1", "c1", "c2", "result", "setC40Word", "offset", "c3", "val16", "getX12Value", "c", "getX12Words", "getShiftValue", "getC40Value", "setIndex", "getC40Words", "c40Values", "shiftValue", "byteIndex", "getEDFBytes", "numberOfThirds", "endPos", "min", "ed<PERSON><PERSON><PERSON><PERSON>", "val24", "getECIValue", "isFNC1", "_super", "stringToEncode", "_this"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/MinimalEncoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spread = (this && this.__spread) || function () {\n    for (var ar = [], i = 0; i < arguments.length; i++) ar = ar.concat(__read(arguments[i]));\n    return ar;\n};\nimport { MACRO_05_HEADER, MACRO_06_HEADER, MACRO_TRAILER, } from './constants';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { MinimalECIInput } from '../../common/MinimalECIInput';\nimport Integer from '../../util/Integer';\nvar Mode;\n(function (Mode) {\n    Mode[Mode[\"ASCII\"] = 0] = \"ASCII\";\n    Mode[Mode[\"C40\"] = 1] = \"C40\";\n    Mode[Mode[\"TEXT\"] = 2] = \"TEXT\";\n    Mode[Mode[\"X12\"] = 3] = \"X12\";\n    Mode[Mode[\"EDF\"] = 4] = \"EDF\";\n    Mode[Mode[\"B256\"] = 5] = \"B256\";\n})(Mode || (Mode = {}));\nvar C40_SHIFT2_CHARS = [\n    '!',\n    '\"',\n    '#',\n    '$',\n    '%',\n    '&',\n    \"'\",\n    '(',\n    ')',\n    '*',\n    '+',\n    ',',\n    '-',\n    '.',\n    '/',\n    ':',\n    ';',\n    '<',\n    '=',\n    '>',\n    '?',\n    '@',\n    '[',\n    '\\\\',\n    ']',\n    '^',\n    '_',\n];\nvar MinimalEncoder = /** @class */ (function () {\n    function MinimalEncoder() {\n    }\n    MinimalEncoder.isExtendedASCII = function (ch, fnc1) {\n        return ch !== fnc1 && ch >= 128 && ch <= 255;\n    };\n    MinimalEncoder.isInC40Shift1Set = function (ch) {\n        return ch <= 31;\n    };\n    MinimalEncoder.isInC40Shift2Set = function (ch, fnc1) {\n        var e_1, _a;\n        try {\n            for (var C40_SHIFT2_CHARS_1 = __values(C40_SHIFT2_CHARS), C40_SHIFT2_CHARS_1_1 = C40_SHIFT2_CHARS_1.next(); !C40_SHIFT2_CHARS_1_1.done; C40_SHIFT2_CHARS_1_1 = C40_SHIFT2_CHARS_1.next()) {\n                var c40Shift2Char = C40_SHIFT2_CHARS_1_1.value;\n                if (c40Shift2Char.charCodeAt(0) === ch) {\n                    return true;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (C40_SHIFT2_CHARS_1_1 && !C40_SHIFT2_CHARS_1_1.done && (_a = C40_SHIFT2_CHARS_1.return)) _a.call(C40_SHIFT2_CHARS_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return ch === fnc1;\n    };\n    MinimalEncoder.isInTextShift1Set = function (ch) {\n        return this.isInC40Shift1Set(ch);\n    };\n    MinimalEncoder.isInTextShift2Set = function (ch, fnc1) {\n        return this.isInC40Shift2Set(ch, fnc1);\n    };\n    /**\n     * Performs message encoding of a DataMatrix message\n     *\n     * @param msg the message\n     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n     *   charset to encode any character in the input that can be encoded by it if the charset is among the\n     *   supported charsets.\n     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not a GS1\n     *   bar code. If the value is not -1 then a FNC1 is also prepended.\n     * @param shape requested shape.\n     * @return the encoded message (the char values range from 0 to 255)\n     */\n    MinimalEncoder.encodeHighLevel = function (msg, priorityCharset, fnc1, shape) {\n        if (priorityCharset === void 0) { priorityCharset = null; }\n        if (fnc1 === void 0) { fnc1 = -1; }\n        if (shape === void 0) { shape = 0 /* FORCE_NONE */; }\n        var macroId = 0;\n        if (msg.startsWith(MACRO_05_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n            macroId = 5;\n            msg = msg.substring(MACRO_05_HEADER.length, msg.length - 2);\n        }\n        else if (msg.startsWith(MACRO_06_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n            macroId = 6;\n            msg = msg.substring(MACRO_06_HEADER.length, msg.length - 2);\n        }\n        return decodeURIComponent(escape(String.fromCharCode.apply(String, __spread(this.encode(msg, priorityCharset, fnc1, shape, macroId)))));\n    };\n    /**\n     * Encodes input minimally and returns an array of the codewords\n     *\n     * @param input The string to encode\n     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n     *   charset to encode any character in the input that can be encoded by it if the charset is among the\n     *   supported charsets.\n     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not a GS1\n     *   bar code. If the value is not -1 then a FNC1 is also prepended.\n     * @param shape requested shape.\n     * @param macroId Prepends the specified macro function in case that a value of 5 or 6 is specified.\n     * @return An array of bytes representing the codewords of a minimal encoding.\n     */\n    MinimalEncoder.encode = function (input, priorityCharset, fnc1, shape, macroId) {\n        return this.encodeMinimally(new Input(input, priorityCharset, fnc1, shape, macroId)).getBytes();\n    };\n    MinimalEncoder.addEdge = function (edges, edge) {\n        var vertexIndex = edge.fromPosition + edge.characterLength;\n        if (edges[vertexIndex][edge.getEndMode()] === null ||\n            edges[vertexIndex][edge.getEndMode()].cachedTotalSize >\n                edge.cachedTotalSize) {\n            edges[vertexIndex][edge.getEndMode()] = edge;\n        }\n    };\n    /** @return the number of words in which the string starting at from can be encoded in c40 or text mode.\n     *  The number of characters encoded is returned in characterLength.\n     *  The number of characters encoded is also minimal in the sense that the algorithm stops as soon\n     *  as a character encoding fills a C40 word competely (three C40 values). An exception is at the\n     *  end of the string where two C40 values are allowed (according to the spec the third c40 value\n     *  is filled  with 0 (Shift 1) in this case).\n     */\n    MinimalEncoder.getNumberOfC40Words = function (input, from, c40, characterLength) {\n        var thirdsCount = 0;\n        for (var i = from; i < input.length(); i++) {\n            if (input.isECI(i)) {\n                characterLength[0] = 0;\n                return 0;\n            }\n            var ci = input.charAt(i);\n            if ((c40 && HighLevelEncoder.isNativeC40(ci)) ||\n                (!c40 && HighLevelEncoder.isNativeText(ci))) {\n                thirdsCount++; // native\n            }\n            else if (!MinimalEncoder.isExtendedASCII(ci, input.getFNC1Character())) {\n                thirdsCount += 2; // shift\n            }\n            else {\n                var asciiValue = ci & 0xff;\n                if (asciiValue >= 128 &&\n                    ((c40 && HighLevelEncoder.isNativeC40(asciiValue - 128)) ||\n                        (!c40 && HighLevelEncoder.isNativeText(asciiValue - 128)))) {\n                    thirdsCount += 3; // shift, Upper shift\n                }\n                else {\n                    thirdsCount += 4; // shift, Upper shift, shift\n                }\n            }\n            if (thirdsCount % 3 === 0 ||\n                ((thirdsCount - 2) % 3 === 0 && i + 1 === input.length())) {\n                characterLength[0] = i - from + 1;\n                return Math.ceil(thirdsCount / 3.0);\n            }\n        }\n        characterLength[0] = 0;\n        return 0;\n    };\n    MinimalEncoder.addEdges = function (input, edges, from, previous) {\n        var e_2, _a;\n        if (input.isECI(from)) {\n            this.addEdge(edges, new Edge(input, Mode.ASCII, from, 1, previous));\n            return;\n        }\n        var ch = input.charAt(from);\n        if (previous === null || previous.getEndMode() !== Mode.EDF) {\n            // not possible to unlatch a full EDF edge to something\n            // else\n            if (HighLevelEncoder.isDigit(ch) &&\n                input.haveNCharacters(from, 2) &&\n                HighLevelEncoder.isDigit(input.charAt(from + 1))) {\n                // two digits ASCII encoded\n                this.addEdge(edges, new Edge(input, Mode.ASCII, from, 2, previous));\n            }\n            else {\n                // one ASCII encoded character or an extended character via Upper Shift\n                this.addEdge(edges, new Edge(input, Mode.ASCII, from, 1, previous));\n            }\n            var modes = [Mode.C40, Mode.TEXT];\n            try {\n                for (var modes_1 = __values(modes), modes_1_1 = modes_1.next(); !modes_1_1.done; modes_1_1 = modes_1.next()) {\n                    var mode = modes_1_1.value;\n                    var characterLength = [];\n                    if (MinimalEncoder.getNumberOfC40Words(input, from, mode === Mode.C40, characterLength) > 0) {\n                        this.addEdge(edges, new Edge(input, mode, from, characterLength[0], previous));\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (modes_1_1 && !modes_1_1.done && (_a = modes_1.return)) _a.call(modes_1);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n            if (input.haveNCharacters(from, 3) &&\n                HighLevelEncoder.isNativeX12(input.charAt(from)) &&\n                HighLevelEncoder.isNativeX12(input.charAt(from + 1)) &&\n                HighLevelEncoder.isNativeX12(input.charAt(from + 2))) {\n                this.addEdge(edges, new Edge(input, Mode.X12, from, 3, previous));\n            }\n            this.addEdge(edges, new Edge(input, Mode.B256, from, 1, previous));\n        }\n        // We create 4 EDF edges,  with 1, 2 3 or 4 characters length. The fourth normally doesn't have a latch to ASCII\n        // unless it is 2 characters away from the end of the input.\n        var i;\n        for (i = 0; i < 3; i++) {\n            var pos = from + i;\n            if (input.haveNCharacters(pos, 1) &&\n                HighLevelEncoder.isNativeEDIFACT(input.charAt(pos))) {\n                this.addEdge(edges, new Edge(input, Mode.EDF, from, i + 1, previous));\n            }\n            else {\n                break;\n            }\n        }\n        if (i === 3 &&\n            input.haveNCharacters(from, 4) &&\n            HighLevelEncoder.isNativeEDIFACT(input.charAt(from + 3))) {\n            this.addEdge(edges, new Edge(input, Mode.EDF, from, 4, previous));\n        }\n    };\n    MinimalEncoder.encodeMinimally = function (input) {\n        /* The minimal encoding is computed by Dijkstra. The acyclic graph is modeled as follows:\n         * A vertex represents a combination of a position in the input and an encoding mode where position 0\n         * denotes the position left of the first character, 1 the position left of the second character and so on.\n         * Likewise the end vertices are located after the last character at position input.length().\n         * For any position there might be up to six vertices, one for each of the encoding types ASCII, C40, TEXT, X12,\n         * EDF and B256.\n         *\n         * As an example consider the input string \"ABC123\" then at position 0 there is only one vertex with the default\n         * ASCII encodation. At position 3 there might be vertices for the types ASCII, C40, X12, EDF and B256.\n         *\n         * An edge leading to such a vertex encodes one or more of the characters left of the position that the vertex\n         * represents. It encodes the characters in the encoding mode of the vertex that it ends on. In other words,\n         * all edges leading to a particular vertex encode the same characters (the length of the suffix can vary) using the same\n         * encoding mode.\n         * As an example consider the input string \"ABC123\" and the vertex (4,EDF). Possible edges leading to this vertex\n         * are:\n         *   (0,ASCII)  --EDF(ABC1)--> (4,EDF)\n         *   (1,ASCII)  --EDF(BC1)-->  (4,EDF)\n         *   (1,B256)   --EDF(BC1)-->  (4,EDF)\n         *   (1,EDF)    --EDF(BC1)-->  (4,EDF)\n         *   (2,ASCII)  --EDF(C1)-->   (4,EDF)\n         *   (2,B256)   --EDF(C1)-->   (4,EDF)\n         *   (2,EDF)    --EDF(C1)-->   (4,EDF)\n         *   (3,ASCII)  --EDF(1)-->    (4,EDF)\n         *   (3,B256)   --EDF(1)-->    (4,EDF)\n         *   (3,EDF)    --EDF(1)-->    (4,EDF)\n         *   (3,C40)    --EDF(1)-->    (4,EDF)\n         *   (3,X12)    --EDF(1)-->    (4,EDF)\n         *\n         * The edges leading to a vertex are stored in such a way that there is a fast way to enumerate the edges ending\n         * on a particular vertex.\n         *\n         * The algorithm processes the vertices in order of their position thereby performing the following:\n         *\n         * For every vertex at position i the algorithm enumerates the edges ending on the vertex and removes all but the\n         * shortest from that list.\n         * Then it processes the vertices for the position i+1. If i+1 == input.length() then the algorithm ends\n         * and chooses the the edge with the smallest size from any of the edges leading to vertices at this position.\n         * Otherwise the algorithm computes all possible outgoing edges for the vertices at the position i+1\n         *\n         * Examples:\n         * The process is illustrated by showing the graph (edges) after each iteration from left to right over the input:\n         * An edge is drawn as follows \"(\" + fromVertex + \") -- \" + encodingMode + \"(\" + encodedInput + \") (\" +\n         * accumulatedSize + \") --> (\" + toVertex + \")\"\n         *\n         * Example 1 encoding the string \"ABCDEFG\":\n         *\n         *\n         * Situation after adding edges to the start vertex (0,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40)\n         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12)\n         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n         *\n         * Situation after adding edges to vertices at position 1\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40)\n         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12)\n         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) B256(B) (4) --> (2,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BC) (5) --> (3,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCD) (5) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) ASCII(B) (4) --> (2,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BC) (6) --> (3,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) C40(BCD) (5) --> (4,C40)\n         * (0,ASCII) B256(A) (3) --> (1,B256) TEXT(BCD) (7) --> (4,TEXT)\n         * (0,ASCII) B256(A) (3) --> (1,B256) X12(BCD) (5) --> (4,X12)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCD) (6) --> (4,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCDE) (6) --> (5,EDF)\n         *\n         * Edge \"(1,ASCII) ASCII(B) (2) --> (2,ASCII)\" is minimal for the vertex (2,ASCII) so that edge \"(1,B256) ASCII(B) (4) --> (2,ASCII)\" is removed.\n         * Edge \"(1,B256) B256(B) (3) --> (2,B256)\" is minimal for the vertext (2,B256) so that the edge \"(1,ASCII) B256(B) (4) --> (2,B256)\" is removed.\n         *\n         * Situation after adding edges to vertices at position 2\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40)\n         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12)\n         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BC) (5) --> (3,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCD) (5) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BC) (6) --> (3,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) C40(BCD) (5) --> (4,C40)\n         * (0,ASCII) B256(A) (3) --> (1,B256) TEXT(BCD) (7) --> (4,TEXT)\n         * (0,ASCII) B256(A) (3) --> (1,B256) X12(BCD) (5) --> (4,X12)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCD) (6) --> (4,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) EDF(BCDE) (6) --> (5,EDF)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) ASCII(C) (5) --> (3,ASCII)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) B256(C) (6) --> (3,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CD) (7) --> (4,EDF)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) C40(CDE) (6) --> (5,C40)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) TEXT(CDE) (8) --> (5,TEXT)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) X12(CDE) (6) --> (5,X12)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CDE) (7) --> (5,EDF)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF) EDF(CDEF) (7) --> (6,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) B256(C) (5) --> (3,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CD) (6) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) C40(CDE) (5) --> (5,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) TEXT(CDE) (7) --> (5,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) X12(CDE) (5) --> (5,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDE) (6) --> (5,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDEF) (6) --> (6,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) ASCII(C) (4) --> (3,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CD) (6) --> (4,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) C40(CDE) (5) --> (5,C40)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) TEXT(CDE) (7) --> (5,TEXT)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) X12(CDE) (5) --> (5,X12)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CDE) (6) --> (5,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) EDF(CDEF) (6) --> (6,EDF)\n         *\n         * Edge \"(2,ASCII) ASCII(C) (3) --> (3,ASCII)\" is minimal for the vertex (3,ASCII) so that edges \"(2,EDF) ASCII(C) (5) --> (3,ASCII)\"\n         * and \"(2,B256) ASCII(C) (4) --> (3,ASCII)\" can be removed.\n         * Edge \"(0,ASCII) EDF(ABC) (4) --> (3,EDF)\" is minimal for the vertex (3,EDF) so that edges \"(1,ASCII) EDF(BC) (5) --> (3,EDF)\"\n         * and \"(1,B256) EDF(BC) (6) --> (3,EDF)\" can be removed.\n         * Edge \"(2,B256) B256(C) (4) --> (3,B256)\" is minimal for the vertex (3,B256) so that edges \"(2,ASCII) B256(C) (5) --> (3,B256)\"\n         * and \"(2,EDF) B256(C) (6) --> (3,B256)\" can be removed.\n         *\n         * This continues for vertices 3 thru 7\n         *\n         * Situation after adding edges to vertices at position 7\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256)\n         * (0,ASCII) EDF(AB) (4) --> (2,EDF)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40)\n         * (0,ASCII) TEXT(ABC) (5) --> (3,TEXT)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12)\n         * (0,ASCII) EDF(ABC) (4) --> (3,EDF)\n         * (0,ASCII) EDF(ABCD) (4) --> (4,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) TEXT(BCD) (6) --> (4,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) EDF(BCDE) (5) --> (5,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256)\n         * (0,ASCII) C40(ABC) (3) --> (3,C40) C40(DEF) (5) --> (6,C40)\n         * (0,ASCII) X12(ABC) (3) --> (3,X12) X12(DEF) (5) --> (6,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) C40(CDE) (5) --> (5,C40)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) TEXT(CDE) (7) --> (5,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) X12(CDE) (5) --> (5,X12)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) EDF(CDEF) (6) --> (6,EDF)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) C40(BCD) (4) --> (4,C40) C40(EFG) (6) --> (7,C40)    //Solution 1\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) X12(BCD) (4) --> (4,X12) X12(EFG) (6) --> (7,X12)    //Solution 2\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) TEXT(DEF) (8) --> (6,TEXT)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) EDF(DEFG) (7) --> (7,EDF)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) TEXT(EFG) (9) --> (7,TEXT)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII) ASCII(F) (6) --> (6,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256) B256(F) (7) --> (6,B256)\n         * (0,ASCII) ASCII(A) (1) --> (1,ASCII) ASCII(B) (2) --> (2,ASCII) ASCII(C) (3) --> (3,ASCII) ASCII(D) (4) --> (4,ASCII) ASCII(E) (5) --> (5,ASCII) ASCII(F) (6) --> (6,ASCII) ASCII(G) (7) --> (7,ASCII)\n         * (0,ASCII) B256(A) (3) --> (1,B256) B256(B) (3) --> (2,B256) B256(C) (4) --> (3,B256) B256(D) (5) --> (4,B256) B256(E) (6) --> (5,B256) B256(F) (7) --> (6,B256) B256(G) (8) --> (7,B256)\n         *\n         * Hence a minimal encoding of \"ABCDEFG\" is either ASCII(A),C40(BCDEFG) or ASCII(A), X12(BCDEFG) with a size of 5 bytes.\n         */\n        var inputLength = input.length();\n        // Array that represents vertices. There is a vertex for every character and mode.\n        // The last dimension in the array below encodes the 6 modes ASCII, C40, TEXT, X12, EDF and B256\n        var edges = Array(inputLength + 1)\n            .fill(null)\n            .map(function () { return Array(6).fill(0); });\n        this.addEdges(input, edges, 0, null);\n        for (var i = 1; i <= inputLength; i++) {\n            for (var j = 0; j < 6; j++) {\n                if (edges[i][j] !== null && i < inputLength) {\n                    this.addEdges(input, edges, i, edges[i][j]);\n                }\n            }\n            // optimize memory by removing edges that have been passed.\n            for (var j = 0; j < 6; j++) {\n                edges[i - 1][j] = null;\n            }\n        }\n        var minimalJ = -1;\n        var minimalSize = Integer.MAX_VALUE;\n        for (var j = 0; j < 6; j++) {\n            if (edges[inputLength][j] !== null) {\n                var edge = edges[inputLength][j];\n                var size = j >= 1 && j <= 3 ? edge.cachedTotalSize + 1 : edge.cachedTotalSize; // C40, TEXT and X12 need an\n                // extra unlatch at the end\n                if (size < minimalSize) {\n                    minimalSize = size;\n                    minimalJ = j;\n                }\n            }\n        }\n        if (minimalJ < 0) {\n            throw new Error('Failed to encode \"' + input + '\"');\n        }\n        return new Result(edges[inputLength][minimalJ]);\n    };\n    return MinimalEncoder;\n}());\nexport { MinimalEncoder };\nvar Result = /** @class */ (function () {\n    function Result(solution) {\n        var input = solution.input;\n        var size = 0;\n        var bytesAL = [];\n        var randomizePostfixLength = [];\n        var randomizeLengths = [];\n        if ((solution.mode === Mode.C40 ||\n            solution.mode === Mode.TEXT ||\n            solution.mode === Mode.X12) &&\n            solution.getEndMode() !== Mode.ASCII) {\n            size += this.prepend(Edge.getBytes(254), bytesAL);\n        }\n        var current = solution;\n        while (current !== null) {\n            size += this.prepend(current.getDataBytes(), bytesAL);\n            if (current.previous === null ||\n                current.getPreviousStartMode() !== current.getMode()) {\n                if (current.getMode() === Mode.B256) {\n                    if (size <= 249) {\n                        bytesAL.unshift(size);\n                        size++;\n                    }\n                    else {\n                        bytesAL.unshift(size % 250);\n                        bytesAL.unshift(size / 250 + 249);\n                        size += 2;\n                    }\n                    randomizePostfixLength.push(bytesAL.length);\n                    randomizeLengths.push(size);\n                }\n                this.prepend(current.getLatchBytes(), bytesAL);\n                size = 0;\n            }\n            current = current.previous;\n        }\n        if (input.getMacroId() === 5) {\n            size += this.prepend(Edge.getBytes(236), bytesAL);\n        }\n        else if (input.getMacroId() === 6) {\n            size += this.prepend(Edge.getBytes(237), bytesAL);\n        }\n        if (input.getFNC1Character() > 0) {\n            size += this.prepend(Edge.getBytes(232), bytesAL);\n        }\n        for (var i = 0; i < randomizePostfixLength.length; i++) {\n            this.applyRandomPattern(bytesAL, bytesAL.length - randomizePostfixLength[i], randomizeLengths[i]);\n        }\n        // add padding\n        var capacity = solution.getMinSymbolSize(bytesAL.length);\n        if (bytesAL.length < capacity) {\n            bytesAL.push(129);\n        }\n        while (bytesAL.length < capacity) {\n            bytesAL.push(this.randomize253State(bytesAL.length + 1));\n        }\n        this.bytes = new Uint8Array(bytesAL.length);\n        for (var i = 0; i < this.bytes.length; i++) {\n            this.bytes[i] = bytesAL[i];\n        }\n    }\n    Result.prototype.prepend = function (bytes, into) {\n        for (var i = bytes.length - 1; i >= 0; i--) {\n            into.unshift(bytes[i]);\n        }\n        return bytes.length;\n    };\n    Result.prototype.randomize253State = function (codewordPosition) {\n        var pseudoRandom = ((149 * codewordPosition) % 253) + 1;\n        var tempVariable = 129 + pseudoRandom;\n        return tempVariable <= 254 ? tempVariable : tempVariable - 254;\n    };\n    Result.prototype.applyRandomPattern = function (bytesAL, startPosition, length) {\n        for (var i = 0; i < length; i++) {\n            // See \"B.1 253-state algorithm\n            var Pad_codeword_position = startPosition + i;\n            var Pad_codeword_value = bytesAL[Pad_codeword_position] & 0xff;\n            var pseudo_random_number = ((149 * (Pad_codeword_position + 1)) % 255) + 1;\n            var temp_variable = Pad_codeword_value + pseudo_random_number;\n            bytesAL[Pad_codeword_position] =\n                temp_variable <= 255 ? temp_variable : temp_variable - 256;\n        }\n    };\n    Result.prototype.getBytes = function () {\n        return this.bytes;\n    };\n    return Result;\n}());\nvar Edge = /** @class */ (function () {\n    function Edge(input, mode, fromPosition, characterLength, previous) {\n        this.input = input;\n        this.mode = mode;\n        this.fromPosition = fromPosition;\n        this.characterLength = characterLength;\n        this.previous = previous;\n        this.allCodewordCapacities = [\n            3, 5, 8, 10, 12, 16, 18, 22, 30, 32, 36, 44, 49, 62, 86, 114, 144, 174, 204,\n            280, 368, 456, 576, 696, 816, 1050, 1304, 1558,\n        ];\n        this.squareCodewordCapacities = [\n            3, 5, 8, 12, 18, 22, 30, 36, 44, 62, 86, 114, 144, 174, 204, 280, 368, 456,\n            576, 696, 816, 1050, 1304, 1558,\n        ];\n        this.rectangularCodewordCapacities = [5, 10, 16, 33, 32, 49];\n        if (!(fromPosition + characterLength <= input.length())) {\n            throw new Error('Invalid edge');\n        }\n        var size = previous !== null ? previous.cachedTotalSize : 0;\n        var previousMode = this.getPreviousMode();\n        /*\n         * Switching modes\n         * ASCII -> C40: latch 230\n         * ASCII -> TEXT: latch 239\n         * ASCII -> X12: latch 238\n         * ASCII -> EDF: latch 240\n         * ASCII -> B256: latch 231\n         * C40 -> ASCII: word(c1,c2,c3), 254\n         * TEXT -> ASCII: word(c1,c2,c3), 254\n         * X12 -> ASCII: word(c1,c2,c3), 254\n         * EDIFACT -> ASCII: Unlatch character,0,0,0 or c1,Unlatch character,0,0 or c1,c2,Unlatch character,0 or\n         * c1,c2,c3,Unlatch character\n         * B256 -> ASCII: without latch after n bytes\n         */\n        switch (mode) {\n            case Mode.ASCII:\n                size++;\n                if (input.isECI(fromPosition) ||\n                    MinimalEncoder.isExtendedASCII(input.charAt(fromPosition), input.getFNC1Character())) {\n                    size++;\n                }\n                if (previousMode === Mode.C40 ||\n                    previousMode === Mode.TEXT ||\n                    previousMode === Mode.X12) {\n                    size++; // unlatch 254 to ASCII\n                }\n                break;\n            case Mode.B256:\n                size++;\n                if (previousMode !== Mode.B256) {\n                    size++; // byte count\n                }\n                else if (this.getB256Size() === 250) {\n                    size++; // extra byte count\n                }\n                if (previousMode === Mode.ASCII) {\n                    size++; // latch to B256\n                }\n                else if (previousMode === Mode.C40 ||\n                    previousMode === Mode.TEXT ||\n                    previousMode === Mode.X12) {\n                    size += 2; // unlatch to ASCII, latch to B256\n                }\n                break;\n            case Mode.C40:\n            case Mode.TEXT:\n            case Mode.X12:\n                if (mode === Mode.X12) {\n                    size += 2;\n                }\n                else {\n                    var charLen = [];\n                    size +=\n                        MinimalEncoder.getNumberOfC40Words(input, fromPosition, mode === Mode.C40, charLen) * 2;\n                }\n                if (previousMode === Mode.ASCII || previousMode === Mode.B256) {\n                    size++; // additional byte for latch from ASCII to this mode\n                }\n                else if (previousMode !== mode &&\n                    (previousMode === Mode.C40 ||\n                        previousMode === Mode.TEXT ||\n                        previousMode === Mode.X12)) {\n                    size += 2; // unlatch 254 to ASCII followed by latch to this mode\n                }\n                break;\n            case Mode.EDF:\n                size += 3;\n                if (previousMode === Mode.ASCII || previousMode === Mode.B256) {\n                    size++; // additional byte for latch from ASCII to this mode\n                }\n                else if (previousMode === Mode.C40 ||\n                    previousMode === Mode.TEXT ||\n                    previousMode === Mode.X12) {\n                    size += 2; // unlatch 254 to ASCII followed by latch to this mode\n                }\n                break;\n        }\n        this.cachedTotalSize = size;\n    }\n    // does not count beyond 250\n    Edge.prototype.getB256Size = function () {\n        var cnt = 0;\n        var current = this;\n        while (current !== null && current.mode === Mode.B256 && cnt <= 250) {\n            cnt++;\n            current = current.previous;\n        }\n        return cnt;\n    };\n    Edge.prototype.getPreviousStartMode = function () {\n        return this.previous === null ? Mode.ASCII : this.previous.mode;\n    };\n    Edge.prototype.getPreviousMode = function () {\n        return this.previous === null ? Mode.ASCII : this.previous.getEndMode();\n    };\n    /** Returns Mode.ASCII in case that:\n     *  - Mode is EDIFACT and characterLength is less than 4 or the remaining characters can be encoded in at most 2\n     *    ASCII bytes.\n     *  - Mode is C40, TEXT or X12 and the remaining characters can be encoded in at most 1 ASCII byte.\n     *  Returns mode in all other cases.\n     * */\n    Edge.prototype.getEndMode = function () {\n        if (this.mode === Mode.EDF) {\n            if (this.characterLength < 4) {\n                return Mode.ASCII;\n            }\n            var lastASCII = this.getLastASCII(); // see 5.2.8.2 EDIFACT encodation Rules\n            if (lastASCII > 0 &&\n                this.getCodewordsRemaining(this.cachedTotalSize + lastASCII) <=\n                    2 - lastASCII) {\n                return Mode.ASCII;\n            }\n        }\n        if (this.mode === Mode.C40 ||\n            this.mode === Mode.TEXT ||\n            this.mode === Mode.X12) {\n            // see 5.2.5.2 C40 encodation rules and 5.2.7.2 ANSI X12 encodation rules\n            if (this.fromPosition + this.characterLength >= this.input.length() &&\n                this.getCodewordsRemaining(this.cachedTotalSize) === 0) {\n                return Mode.ASCII;\n            }\n            var lastASCII = this.getLastASCII();\n            if (lastASCII === 1 &&\n                this.getCodewordsRemaining(this.cachedTotalSize + 1) === 0) {\n                return Mode.ASCII;\n            }\n        }\n        return this.mode;\n    };\n    Edge.prototype.getMode = function () {\n        return this.mode;\n    };\n    /** Peeks ahead and returns 1 if the postfix consists of exactly two digits, 2 if the postfix consists of exactly\n     *  two consecutive digits and a non extended character or of 4 digits.\n     *  Returns 0 in any other case\n     **/\n    Edge.prototype.getLastASCII = function () {\n        var length = this.input.length();\n        var from = this.fromPosition + this.characterLength;\n        if (length - from > 4 || from >= length) {\n            return 0;\n        }\n        if (length - from === 1) {\n            if (MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character())) {\n                return 0;\n            }\n            return 1;\n        }\n        if (length - from === 2) {\n            if (MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character()) ||\n                MinimalEncoder.isExtendedASCII(this.input.charAt(from + 1), this.input.getFNC1Character())) {\n                return 0;\n            }\n            if (HighLevelEncoder.isDigit(this.input.charAt(from)) &&\n                HighLevelEncoder.isDigit(this.input.charAt(from + 1))) {\n                return 1;\n            }\n            return 2;\n        }\n        if (length - from === 3) {\n            if (HighLevelEncoder.isDigit(this.input.charAt(from)) &&\n                HighLevelEncoder.isDigit(this.input.charAt(from + 1)) &&\n                !MinimalEncoder.isExtendedASCII(this.input.charAt(from + 2), this.input.getFNC1Character())) {\n                return 2;\n            }\n            if (HighLevelEncoder.isDigit(this.input.charAt(from + 1)) &&\n                HighLevelEncoder.isDigit(this.input.charAt(from + 2)) &&\n                !MinimalEncoder.isExtendedASCII(this.input.charAt(from), this.input.getFNC1Character())) {\n                return 2;\n            }\n            return 0;\n        }\n        if (HighLevelEncoder.isDigit(this.input.charAt(from)) &&\n            HighLevelEncoder.isDigit(this.input.charAt(from + 1)) &&\n            HighLevelEncoder.isDigit(this.input.charAt(from + 2)) &&\n            HighLevelEncoder.isDigit(this.input.charAt(from + 3))) {\n            return 2;\n        }\n        return 0;\n    };\n    /** Returns the capacity in codewords of the smallest symbol that has enough capacity to fit the given minimal\n     * number of codewords.\n     **/\n    Edge.prototype.getMinSymbolSize = function (minimum) {\n        var e_3, _a, e_4, _b, e_5, _c;\n        switch (this.input.getShapeHint()) {\n            case 1 /* FORCE_SQUARE */:\n                try {\n                    for (var _d = __values(this.squareCodewordCapacities), _e = _d.next(); !_e.done; _e = _d.next()) {\n                        var capacity = _e.value;\n                        if (capacity >= minimum) {\n                            return capacity;\n                        }\n                    }\n                }\n                catch (e_3_1) { e_3 = { error: e_3_1 }; }\n                finally {\n                    try {\n                        if (_e && !_e.done && (_a = _d.return)) _a.call(_d);\n                    }\n                    finally { if (e_3) throw e_3.error; }\n                }\n                break;\n            case 2 /* FORCE_RECTANGLE */:\n                try {\n                    for (var _f = __values(this.rectangularCodewordCapacities), _g = _f.next(); !_g.done; _g = _f.next()) {\n                        var capacity = _g.value;\n                        if (capacity >= minimum) {\n                            return capacity;\n                        }\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (_g && !_g.done && (_b = _f.return)) _b.call(_f);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n                break;\n        }\n        try {\n            for (var _h = __values(this.allCodewordCapacities), _j = _h.next(); !_j.done; _j = _h.next()) {\n                var capacity = _j.value;\n                if (capacity >= minimum) {\n                    return capacity;\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_j && !_j.done && (_c = _h.return)) _c.call(_h);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        return this.allCodewordCapacities[this.allCodewordCapacities.length - 1];\n    };\n    /** Returns the remaining capacity in codewords of the smallest symbol that has enough capacity to fit the given\n     * minimal number of codewords.\n     **/\n    Edge.prototype.getCodewordsRemaining = function (minimum) {\n        return this.getMinSymbolSize(minimum) - minimum;\n    };\n    Edge.getBytes = function (c1, c2) {\n        var result = new Uint8Array(c2 ? 2 : 1);\n        result[0] = c1;\n        if (c2) {\n            result[1] = c2;\n        }\n        return result;\n    };\n    Edge.prototype.setC40Word = function (bytes, offset, c1, c2, c3) {\n        var val16 = 1600 * (c1 & 0xff) + 40 * (c2 & 0xff) + (c3 & 0xff) + 1;\n        bytes[offset] = val16 / 256;\n        bytes[offset + 1] = val16 % 256;\n    };\n    Edge.prototype.getX12Value = function (c) {\n        return c === 13\n            ? 0\n            : c === 42\n                ? 1\n                : c === 62\n                    ? 2\n                    : c === 32\n                        ? 3\n                        : c >= 48 && c <= 57\n                            ? c - 44\n                            : c >= 65 && c <= 90\n                                ? c - 51\n                                : c;\n    };\n    Edge.prototype.getX12Words = function () {\n        if (!(this.characterLength % 3 === 0)) {\n            throw new Error('X12 words must be a multiple of 3');\n        }\n        var result = new Uint8Array((this.characterLength / 3) * 2);\n        for (var i = 0; i < result.length; i += 2) {\n            this.setC40Word(result, i, this.getX12Value(this.input.charAt(this.fromPosition + (i / 2) * 3)), this.getX12Value(this.input.charAt(this.fromPosition + (i / 2) * 3 + 1)), this.getX12Value(this.input.charAt(this.fromPosition + (i / 2) * 3 + 2)));\n        }\n        return result;\n    };\n    Edge.prototype.getShiftValue = function (c, c40, fnc1) {\n        return (c40 && MinimalEncoder.isInC40Shift1Set(c)) ||\n            (!c40 && MinimalEncoder.isInTextShift1Set(c))\n            ? 0\n            : (c40 && MinimalEncoder.isInC40Shift2Set(c, fnc1)) ||\n                (!c40 && MinimalEncoder.isInTextShift2Set(c, fnc1))\n                ? 1\n                : 2;\n    };\n    Edge.prototype.getC40Value = function (c40, setIndex, c, fnc1) {\n        if (c === fnc1) {\n            if (!(setIndex === 2)) {\n                throw new Error('FNC1 cannot be used in C40 shift 2');\n            }\n            return 27;\n        }\n        if (c40) {\n            return c <= 31\n                ? c\n                : c === 32\n                    ? 3\n                    : c <= 47\n                        ? c - 33\n                        : c <= 57\n                            ? c - 44\n                            : c <= 64\n                                ? c - 43\n                                : c <= 90\n                                    ? c - 51\n                                    : c <= 95\n                                        ? c - 69\n                                        : c <= 127\n                                            ? c - 96\n                                            : c;\n        }\n        else {\n            return c === 0\n                ? 0\n                : setIndex === 0 && c <= 3\n                    ? c - 1 // is this a bug in the spec?\n                    : setIndex === 1 && c <= 31\n                        ? c\n                        : c === 32\n                            ? 3\n                            : c >= 33 && c <= 47\n                                ? c - 33\n                                : c >= 48 && c <= 57\n                                    ? c - 44\n                                    : c >= 58 && c <= 64\n                                        ? c - 43\n                                        : c >= 65 && c <= 90\n                                            ? c - 64\n                                            : c >= 91 && c <= 95\n                                                ? c - 69\n                                                : c === 96\n                                                    ? 0\n                                                    : c >= 97 && c <= 122\n                                                        ? c - 83\n                                                        : c >= 123 && c <= 127\n                                                            ? c - 96\n                                                            : c;\n        }\n    };\n    Edge.prototype.getC40Words = function (c40, fnc1) {\n        var c40Values = [];\n        for (var i = 0; i < this.characterLength; i++) {\n            var ci = this.input.charAt(this.fromPosition + i);\n            if ((c40 && HighLevelEncoder.isNativeC40(ci)) ||\n                (!c40 && HighLevelEncoder.isNativeText(ci))) {\n                c40Values.push(this.getC40Value(c40, 0, ci, fnc1));\n            }\n            else if (!MinimalEncoder.isExtendedASCII(ci, fnc1)) {\n                var shiftValue = this.getShiftValue(ci, c40, fnc1);\n                c40Values.push(shiftValue); // Shift[123]\n                c40Values.push(this.getC40Value(c40, shiftValue, ci, fnc1));\n            }\n            else {\n                var asciiValue = (ci & 0xff) - 128;\n                if ((c40 && HighLevelEncoder.isNativeC40(asciiValue)) ||\n                    (!c40 && HighLevelEncoder.isNativeText(asciiValue))) {\n                    c40Values.push(1); // Shift 2\n                    c40Values.push(30); // Upper Shift\n                    c40Values.push(this.getC40Value(c40, 0, asciiValue, fnc1));\n                }\n                else {\n                    c40Values.push(1); // Shift 2\n                    c40Values.push(30); // Upper Shift\n                    var shiftValue = this.getShiftValue(asciiValue, c40, fnc1);\n                    c40Values.push(shiftValue); // Shift[123]\n                    c40Values.push(this.getC40Value(c40, shiftValue, asciiValue, fnc1));\n                }\n            }\n        }\n        if (c40Values.length % 3 !== 0) {\n            if (!((c40Values.length - 2) % 3 === 0 &&\n                this.fromPosition + this.characterLength === this.input.length())) {\n                throw new Error('C40 words must be a multiple of 3');\n            }\n            c40Values.push(0); // pad with 0 (Shift 1)\n        }\n        var result = new Uint8Array((c40Values.length / 3) * 2);\n        var byteIndex = 0;\n        for (var i = 0; i < c40Values.length; i += 3) {\n            this.setC40Word(result, byteIndex, c40Values[i] & 0xff, c40Values[i + 1] & 0xff, c40Values[i + 2] & 0xff);\n            byteIndex += 2;\n        }\n        return result;\n    };\n    Edge.prototype.getEDFBytes = function () {\n        var numberOfThirds = Math.ceil(this.characterLength / 4.0);\n        var result = new Uint8Array(numberOfThirds * 3);\n        var pos = this.fromPosition;\n        var endPos = Math.min(this.fromPosition + this.characterLength - 1, this.input.length() - 1);\n        for (var i = 0; i < numberOfThirds; i += 3) {\n            var edfValues = [];\n            for (var j = 0; j < 4; j++) {\n                if (pos <= endPos) {\n                    edfValues[j] = this.input.charAt(pos++) & 0x3f;\n                }\n                else {\n                    edfValues[j] = pos === endPos + 1 ? 0x1f : 0;\n                }\n            }\n            var val24 = edfValues[0] << 18;\n            val24 |= edfValues[1] << 12;\n            val24 |= edfValues[2] << 6;\n            val24 |= edfValues[3];\n            result[i] = (val24 >> 16) & 0xff;\n            result[i + 1] = (val24 >> 8) & 0xff;\n            result[i + 2] = val24 & 0xff;\n        }\n        return result;\n    };\n    Edge.prototype.getLatchBytes = function () {\n        switch (this.getPreviousMode()) {\n            case Mode.ASCII:\n            case Mode.B256: // after B256 ends (via length) we are back to ASCII\n                switch (this.mode) {\n                    case Mode.B256:\n                        return Edge.getBytes(231);\n                    case Mode.C40:\n                        return Edge.getBytes(230);\n                    case Mode.TEXT:\n                        return Edge.getBytes(239);\n                    case Mode.X12:\n                        return Edge.getBytes(238);\n                    case Mode.EDF:\n                        return Edge.getBytes(240);\n                }\n                break;\n            case Mode.C40:\n            case Mode.TEXT:\n            case Mode.X12:\n                if (this.mode !== this.getPreviousMode()) {\n                    switch (this.mode) {\n                        case Mode.ASCII:\n                            return Edge.getBytes(254);\n                        case Mode.B256:\n                            return Edge.getBytes(254, 231);\n                        case Mode.C40:\n                            return Edge.getBytes(254, 230);\n                        case Mode.TEXT:\n                            return Edge.getBytes(254, 239);\n                        case Mode.X12:\n                            return Edge.getBytes(254, 238);\n                        case Mode.EDF:\n                            return Edge.getBytes(254, 240);\n                    }\n                }\n                break;\n            case Mode.EDF:\n                // The rightmost EDIFACT edge always contains an unlatch character\n                if (this.mode !== Mode.EDF) {\n                    throw new Error('Cannot switch from EDF to ' + this.mode);\n                }\n                break;\n        }\n        return new Uint8Array(0);\n    };\n    // Important: The function does not return the length bytes (one or two) in case of B256 encoding\n    Edge.prototype.getDataBytes = function () {\n        switch (this.mode) {\n            case Mode.ASCII:\n                if (this.input.isECI(this.fromPosition)) {\n                    return Edge.getBytes(241, this.input.getECIValue(this.fromPosition) + 1);\n                }\n                else if (MinimalEncoder.isExtendedASCII(this.input.charAt(this.fromPosition), this.input.getFNC1Character())) {\n                    return Edge.getBytes(235, this.input.charAt(this.fromPosition) - 127);\n                }\n                else if (this.characterLength === 2) {\n                    return Edge.getBytes(this.input.charAt(this.fromPosition) * 10 +\n                        this.input.charAt(this.fromPosition + 1) +\n                        130);\n                }\n                else if (this.input.isFNC1(this.fromPosition)) {\n                    return Edge.getBytes(232);\n                }\n                else {\n                    return Edge.getBytes(this.input.charAt(this.fromPosition) + 1);\n                }\n            case Mode.B256:\n                return Edge.getBytes(this.input.charAt(this.fromPosition));\n            case Mode.C40:\n                return this.getC40Words(true, this.input.getFNC1Character());\n            case Mode.TEXT:\n                return this.getC40Words(false, this.input.getFNC1Character());\n            case Mode.X12:\n                return this.getX12Words();\n            case Mode.EDF:\n                return this.getEDFBytes();\n        }\n    };\n    return Edge;\n}());\nvar Input = /** @class */ (function (_super) {\n    __extends(Input, _super);\n    function Input(stringToEncode, priorityCharset, fnc1, shape, macroId) {\n        var _this = _super.call(this, stringToEncode, priorityCharset, fnc1) || this;\n        _this.shape = shape;\n        _this.macroId = macroId;\n        return _this;\n    }\n    Input.prototype.getMacroId = function () {\n        return this.macroId;\n    };\n    Input.prototype.getShapeHint = function () {\n        return this.shape;\n    };\n    return Input;\n}(MinimalECIInput));\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,IAAIW,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUZ,CAAC,EAAEa,CAAC,EAAE;EAClD,IAAIT,CAAC,GAAG,OAAOF,MAAM,KAAK,UAAU,IAAIF,CAAC,CAACE,MAAM,CAACC,QAAQ,CAAC;EAC1D,IAAI,CAACC,CAAC,EAAE,OAAOJ,CAAC;EAChB,IAAIK,CAAC,GAAGD,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;IAAEc,CAAC;IAAEC,EAAE,GAAG,EAAE;IAAEC,CAAC;EAChC,IAAI;IACA,OAAO,CAACH,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAACC,CAAC,GAAGT,CAAC,CAACG,IAAI,CAAC,CAAC,EAAEE,IAAI,EAAEK,EAAE,CAACE,IAAI,CAACH,CAAC,CAACL,KAAK,CAAC;EAC9E,CAAC,CACD,OAAOS,KAAK,EAAE;IAAEF,CAAC,GAAG;MAAEE,KAAK,EAAEA;IAAM,CAAC;EAAE,CAAC,SAC/B;IACJ,IAAI;MACA,IAAIJ,CAAC,IAAI,CAACA,CAAC,CAACJ,IAAI,KAAKN,CAAC,GAAGC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAED,CAAC,CAACE,IAAI,CAACD,CAAC,CAAC;IACpD,CAAC,SACO;MAAE,IAAIW,CAAC,EAAE,MAAMA,CAAC,CAACE,KAAK;IAAE;EACpC;EACA,OAAOH,EAAE;AACb,CAAC;AACD,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,YAAY;EAClD,KAAK,IAAIJ,EAAE,GAAG,EAAE,EAAEV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,SAAS,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAEU,EAAE,GAAGA,EAAE,CAACM,MAAM,CAACT,MAAM,CAACQ,SAAS,CAACf,CAAC,CAAC,CAAC,CAAC;EACxF,OAAOU,EAAE;AACb,CAAC;AACD,SAASO,eAAe,EAAEC,eAAe,EAAEC,aAAa,QAAS,aAAa;AAC9E,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,eAAe,QAAQ,8BAA8B;AAC9D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,IAAIC,IAAI;AACR,CAAC,UAAUA,IAAI,EAAE;EACbA,IAAI,CAACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACjCA,IAAI,CAACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC7BA,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/BA,IAAI,CAACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC7BA,IAAI,CAACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC7BA,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACnC,CAAC,EAAEA,IAAI,KAAKA,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;AACvB,IAAIC,gBAAgB,GAAG,CACnB,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,GAAG,EACH,IAAI,EACJ,GAAG,EACH,GAAG,EACH,GAAG,CACN;AACD,IAAIC,cAAc,GAAG,aAAe,YAAY;EAC5C,SAASA,cAAcA,CAAA,EAAG,CAC1B;EACAA,cAAc,CAACC,eAAe,GAAG,UAAUC,EAAE,EAAEC,IAAI,EAAE;IACjD,OAAOD,EAAE,KAAKC,IAAI,IAAID,EAAE,IAAI,GAAG,IAAIA,EAAE,IAAI,GAAG;EAChD,CAAC;EACDF,cAAc,CAACI,gBAAgB,GAAG,UAAUF,EAAE,EAAE;IAC5C,OAAOA,EAAE,IAAI,EAAE;EACnB,CAAC;EACDF,cAAc,CAACK,gBAAgB,GAAG,UAAUH,EAAE,EAAEC,IAAI,EAAE;IAClD,IAAIG,GAAG,EAAEC,EAAE;IACX,IAAI;MACA,KAAK,IAAIC,kBAAkB,GAAGvC,QAAQ,CAAC8B,gBAAgB,CAAC,EAAEU,oBAAoB,GAAGD,kBAAkB,CAAC9B,IAAI,CAAC,CAAC,EAAE,CAAC+B,oBAAoB,CAAC7B,IAAI,EAAE6B,oBAAoB,GAAGD,kBAAkB,CAAC9B,IAAI,CAAC,CAAC,EAAE;QACtL,IAAIgC,aAAa,GAAGD,oBAAoB,CAAC9B,KAAK;QAC9C,IAAI+B,aAAa,CAACC,UAAU,CAAC,CAAC,CAAC,KAAKT,EAAE,EAAE;UACpC,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,CACD,OAAOU,KAAK,EAAE;MAAEN,GAAG,GAAG;QAAElB,KAAK,EAAEwB;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,oBAAoB,IAAI,CAACA,oBAAoB,CAAC7B,IAAI,KAAK2B,EAAE,GAAGC,kBAAkB,CAACK,MAAM,CAAC,EAAEN,EAAE,CAAC/B,IAAI,CAACgC,kBAAkB,CAAC;MAC3H,CAAC,SACO;QAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAAClB,KAAK;MAAE;IACxC;IACA,OAAOc,EAAE,KAAKC,IAAI;EACtB,CAAC;EACDH,cAAc,CAACc,iBAAiB,GAAG,UAAUZ,EAAE,EAAE;IAC7C,OAAO,IAAI,CAACE,gBAAgB,CAACF,EAAE,CAAC;EACpC,CAAC;EACDF,cAAc,CAACe,iBAAiB,GAAG,UAAUb,EAAE,EAAEC,IAAI,EAAE;IACnD,OAAO,IAAI,CAACE,gBAAgB,CAACH,EAAE,EAAEC,IAAI,CAAC;EAC1C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,cAAc,CAACgB,eAAe,GAAG,UAAUC,GAAG,EAAEC,eAAe,EAAEf,IAAI,EAAEgB,KAAK,EAAE;IAC1E,IAAID,eAAe,KAAK,KAAK,CAAC,EAAE;MAAEA,eAAe,GAAG,IAAI;IAAE;IAC1D,IAAIf,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG,CAAC,CAAC;IAAE;IAClC,IAAIgB,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC,CAAC;IAAkB;IACpD,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIH,GAAG,CAACI,UAAU,CAAC7B,eAAe,CAAC,IAAIyB,GAAG,CAACK,QAAQ,CAAC5B,aAAa,CAAC,EAAE;MAChE0B,OAAO,GAAG,CAAC;MACXH,GAAG,GAAGA,GAAG,CAACM,SAAS,CAAC/B,eAAe,CAACf,MAAM,EAAEwC,GAAG,CAACxC,MAAM,GAAG,CAAC,CAAC;IAC/D,CAAC,MACI,IAAIwC,GAAG,CAACI,UAAU,CAAC5B,eAAe,CAAC,IAAIwB,GAAG,CAACK,QAAQ,CAAC5B,aAAa,CAAC,EAAE;MACrE0B,OAAO,GAAG,CAAC;MACXH,GAAG,GAAGA,GAAG,CAACM,SAAS,CAAC9B,eAAe,CAAChB,MAAM,EAAEwC,GAAG,CAACxC,MAAM,GAAG,CAAC,CAAC;IAC/D;IACA,OAAO+C,kBAAkB,CAACC,MAAM,CAACC,MAAM,CAACC,YAAY,CAACC,KAAK,CAACF,MAAM,EAAErC,QAAQ,CAAC,IAAI,CAACwC,MAAM,CAACZ,GAAG,EAAEC,eAAe,EAAEf,IAAI,EAAEgB,KAAK,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3I,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpB,cAAc,CAAC6B,MAAM,GAAG,UAAUC,KAAK,EAAEZ,eAAe,EAAEf,IAAI,EAAEgB,KAAK,EAAEC,OAAO,EAAE;IAC5E,OAAO,IAAI,CAACW,eAAe,CAAC,IAAIC,KAAK,CAACF,KAAK,EAAEZ,eAAe,EAAEf,IAAI,EAAEgB,KAAK,EAAEC,OAAO,CAAC,CAAC,CAACa,QAAQ,CAAC,CAAC;EACnG,CAAC;EACDjC,cAAc,CAACkC,OAAO,GAAG,UAAUC,KAAK,EAAEC,IAAI,EAAE;IAC5C,IAAIC,WAAW,GAAGD,IAAI,CAACE,YAAY,GAAGF,IAAI,CAACG,eAAe;IAC1D,IAAIJ,KAAK,CAACE,WAAW,CAAC,CAACD,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,IAC9CL,KAAK,CAACE,WAAW,CAAC,CAACD,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,CAACC,eAAe,GACjDL,IAAI,CAACK,eAAe,EAAE;MAC1BN,KAAK,CAACE,WAAW,CAAC,CAACD,IAAI,CAACI,UAAU,CAAC,CAAC,CAAC,GAAGJ,IAAI;IAChD;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIpC,cAAc,CAAC0C,mBAAmB,GAAG,UAAUZ,KAAK,EAAEa,IAAI,EAAEC,GAAG,EAAEL,eAAe,EAAE;IAC9E,IAAIM,WAAW,GAAG,CAAC;IACnB,KAAK,IAAItE,CAAC,GAAGoE,IAAI,EAAEpE,CAAC,GAAGuD,KAAK,CAACrD,MAAM,CAAC,CAAC,EAAEF,CAAC,EAAE,EAAE;MACxC,IAAIuD,KAAK,CAACgB,KAAK,CAACvE,CAAC,CAAC,EAAE;QAChBgE,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC;QACtB,OAAO,CAAC;MACZ;MACA,IAAIQ,EAAE,GAAGjB,KAAK,CAACkB,MAAM,CAACzE,CAAC,CAAC;MACxB,IAAKqE,GAAG,IAAIjD,gBAAgB,CAACsD,WAAW,CAACF,EAAE,CAAC,IACvC,CAACH,GAAG,IAAIjD,gBAAgB,CAACuD,YAAY,CAACH,EAAE,CAAE,EAAE;QAC7CF,WAAW,EAAE,CAAC,CAAC;MACnB,CAAC,MACI,IAAI,CAAC7C,cAAc,CAACC,eAAe,CAAC8C,EAAE,EAAEjB,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC,EAAE;QACpEN,WAAW,IAAI,CAAC,CAAC,CAAC;MACtB,CAAC,MACI;QACD,IAAIO,UAAU,GAAGL,EAAE,GAAG,IAAI;QAC1B,IAAIK,UAAU,IAAI,GAAG,KACfR,GAAG,IAAIjD,gBAAgB,CAACsD,WAAW,CAACG,UAAU,GAAG,GAAG,CAAC,IAClD,CAACR,GAAG,IAAIjD,gBAAgB,CAACuD,YAAY,CAACE,UAAU,GAAG,GAAG,CAAE,CAAC,EAAE;UAChEP,WAAW,IAAI,CAAC,CAAC,CAAC;QACtB,CAAC,MACI;UACDA,WAAW,IAAI,CAAC,CAAC,CAAC;QACtB;MACJ;MACA,IAAIA,WAAW,GAAG,CAAC,KAAK,CAAC,IACpB,CAACA,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAItE,CAAC,GAAG,CAAC,KAAKuD,KAAK,CAACrD,MAAM,CAAC,CAAE,EAAE;QAC3D8D,eAAe,CAAC,CAAC,CAAC,GAAGhE,CAAC,GAAGoE,IAAI,GAAG,CAAC;QACjC,OAAOU,IAAI,CAACC,IAAI,CAACT,WAAW,GAAG,GAAG,CAAC;MACvC;IACJ;IACAN,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC;IACtB,OAAO,CAAC;EACZ,CAAC;EACDvC,cAAc,CAACuD,QAAQ,GAAG,UAAUzB,KAAK,EAAEK,KAAK,EAAEQ,IAAI,EAAEa,QAAQ,EAAE;IAC9D,IAAIC,GAAG,EAAElD,EAAE;IACX,IAAIuB,KAAK,CAACgB,KAAK,CAACH,IAAI,CAAC,EAAE;MACnB,IAAI,CAACT,OAAO,CAACC,KAAK,EAAE,IAAIuB,IAAI,CAAC5B,KAAK,EAAEhC,IAAI,CAAC6D,KAAK,EAAEhB,IAAI,EAAE,CAAC,EAAEa,QAAQ,CAAC,CAAC;MACnE;IACJ;IACA,IAAItD,EAAE,GAAG4B,KAAK,CAACkB,MAAM,CAACL,IAAI,CAAC;IAC3B,IAAIa,QAAQ,KAAK,IAAI,IAAIA,QAAQ,CAAChB,UAAU,CAAC,CAAC,KAAK1C,IAAI,CAAC8D,GAAG,EAAE;MACzD;MACA;MACA,IAAIjE,gBAAgB,CAACkE,OAAO,CAAC3D,EAAE,CAAC,IAC5B4B,KAAK,CAACgC,eAAe,CAACnB,IAAI,EAAE,CAAC,CAAC,IAC9BhD,gBAAgB,CAACkE,OAAO,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;QAClD;QACA,IAAI,CAACT,OAAO,CAACC,KAAK,EAAE,IAAIuB,IAAI,CAAC5B,KAAK,EAAEhC,IAAI,CAAC6D,KAAK,EAAEhB,IAAI,EAAE,CAAC,EAAEa,QAAQ,CAAC,CAAC;MACvE,CAAC,MACI;QACD;QACA,IAAI,CAACtB,OAAO,CAACC,KAAK,EAAE,IAAIuB,IAAI,CAAC5B,KAAK,EAAEhC,IAAI,CAAC6D,KAAK,EAAEhB,IAAI,EAAE,CAAC,EAAEa,QAAQ,CAAC,CAAC;MACvE;MACA,IAAIO,KAAK,GAAG,CAACjE,IAAI,CAACkE,GAAG,EAAElE,IAAI,CAACmE,IAAI,CAAC;MACjC,IAAI;QACA,KAAK,IAAIC,OAAO,GAAGjG,QAAQ,CAAC8F,KAAK,CAAC,EAAEI,SAAS,GAAGD,OAAO,CAACxF,IAAI,CAAC,CAAC,EAAE,CAACyF,SAAS,CAACvF,IAAI,EAAEuF,SAAS,GAAGD,OAAO,CAACxF,IAAI,CAAC,CAAC,EAAE;UACzG,IAAI0F,IAAI,GAAGD,SAAS,CAACxF,KAAK;UAC1B,IAAI4D,eAAe,GAAG,EAAE;UACxB,IAAIvC,cAAc,CAAC0C,mBAAmB,CAACZ,KAAK,EAAEa,IAAI,EAAEyB,IAAI,KAAKtE,IAAI,CAACkE,GAAG,EAAEzB,eAAe,CAAC,GAAG,CAAC,EAAE;YACzF,IAAI,CAACL,OAAO,CAACC,KAAK,EAAE,IAAIuB,IAAI,CAAC5B,KAAK,EAAEsC,IAAI,EAAEzB,IAAI,EAAEJ,eAAe,CAAC,CAAC,CAAC,EAAEiB,QAAQ,CAAC,CAAC;UAClF;QACJ;MACJ,CAAC,CACD,OAAOa,KAAK,EAAE;QAAEZ,GAAG,GAAG;UAAErE,KAAK,EAAEiF;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,SAAS,IAAI,CAACA,SAAS,CAACvF,IAAI,KAAK2B,EAAE,GAAG2D,OAAO,CAACrD,MAAM,CAAC,EAAEN,EAAE,CAAC/B,IAAI,CAAC0F,OAAO,CAAC;QAC/E,CAAC,SACO;UAAE,IAAIT,GAAG,EAAE,MAAMA,GAAG,CAACrE,KAAK;QAAE;MACxC;MACA,IAAI0C,KAAK,CAACgC,eAAe,CAACnB,IAAI,EAAE,CAAC,CAAC,IAC9BhD,gBAAgB,CAAC2E,WAAW,CAACxC,KAAK,CAACkB,MAAM,CAACL,IAAI,CAAC,CAAC,IAChDhD,gBAAgB,CAAC2E,WAAW,CAACxC,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,IACpDhD,gBAAgB,CAAC2E,WAAW,CAACxC,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;QACtD,IAAI,CAACT,OAAO,CAACC,KAAK,EAAE,IAAIuB,IAAI,CAAC5B,KAAK,EAAEhC,IAAI,CAACyE,GAAG,EAAE5B,IAAI,EAAE,CAAC,EAAEa,QAAQ,CAAC,CAAC;MACrE;MACA,IAAI,CAACtB,OAAO,CAACC,KAAK,EAAE,IAAIuB,IAAI,CAAC5B,KAAK,EAAEhC,IAAI,CAAC0E,IAAI,EAAE7B,IAAI,EAAE,CAAC,EAAEa,QAAQ,CAAC,CAAC;IACtE;IACA;IACA;IACA,IAAIjF,CAAC;IACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpB,IAAIkG,GAAG,GAAG9B,IAAI,GAAGpE,CAAC;MAClB,IAAIuD,KAAK,CAACgC,eAAe,CAACW,GAAG,EAAE,CAAC,CAAC,IAC7B9E,gBAAgB,CAAC+E,eAAe,CAAC5C,KAAK,CAACkB,MAAM,CAACyB,GAAG,CAAC,CAAC,EAAE;QACrD,IAAI,CAACvC,OAAO,CAACC,KAAK,EAAE,IAAIuB,IAAI,CAAC5B,KAAK,EAAEhC,IAAI,CAAC8D,GAAG,EAAEjB,IAAI,EAAEpE,CAAC,GAAG,CAAC,EAAEiF,QAAQ,CAAC,CAAC;MACzE,CAAC,MACI;QACD;MACJ;IACJ;IACA,IAAIjF,CAAC,KAAK,CAAC,IACPuD,KAAK,CAACgC,eAAe,CAACnB,IAAI,EAAE,CAAC,CAAC,IAC9BhD,gBAAgB,CAAC+E,eAAe,CAAC5C,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;MAC1D,IAAI,CAACT,OAAO,CAACC,KAAK,EAAE,IAAIuB,IAAI,CAAC5B,KAAK,EAAEhC,IAAI,CAAC8D,GAAG,EAAEjB,IAAI,EAAE,CAAC,EAAEa,QAAQ,CAAC,CAAC;IACrE;EACJ,CAAC;EACDxD,cAAc,CAAC+B,eAAe,GAAG,UAAUD,KAAK,EAAE;IAC9C;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI6C,WAAW,GAAG7C,KAAK,CAACrD,MAAM,CAAC,CAAC;IAChC;IACA;IACA,IAAI0D,KAAK,GAAGzE,KAAK,CAACiH,WAAW,GAAG,CAAC,CAAC,CAC7BC,IAAI,CAAC,IAAI,CAAC,CACVC,GAAG,CAAC,YAAY;MAAE,OAAOnH,KAAK,CAAC,CAAC,CAAC,CAACkH,IAAI,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;IAClD,IAAI,CAACrB,QAAQ,CAACzB,KAAK,EAAEK,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC;IACpC,KAAK,IAAI5D,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIoG,WAAW,EAAEpG,CAAC,EAAE,EAAE;MACnC,KAAK,IAAIuG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAI3C,KAAK,CAAC5D,CAAC,CAAC,CAACuG,CAAC,CAAC,KAAK,IAAI,IAAIvG,CAAC,GAAGoG,WAAW,EAAE;UACzC,IAAI,CAACpB,QAAQ,CAACzB,KAAK,EAAEK,KAAK,EAAE5D,CAAC,EAAE4D,KAAK,CAAC5D,CAAC,CAAC,CAACuG,CAAC,CAAC,CAAC;QAC/C;MACJ;MACA;MACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB3C,KAAK,CAAC5D,CAAC,GAAG,CAAC,CAAC,CAACuG,CAAC,CAAC,GAAG,IAAI;MAC1B;IACJ;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,WAAW,GAAGnF,OAAO,CAACoF,SAAS;IACnC,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAI3C,KAAK,CAACwC,WAAW,CAAC,CAACG,CAAC,CAAC,KAAK,IAAI,EAAE;QAChC,IAAI1C,IAAI,GAAGD,KAAK,CAACwC,WAAW,CAAC,CAACG,CAAC,CAAC;QAChC,IAAII,IAAI,GAAGJ,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,GAAG1C,IAAI,CAACK,eAAe,GAAG,CAAC,GAAGL,IAAI,CAACK,eAAe,CAAC,CAAC;QAC/E;QACA,IAAIyC,IAAI,GAAGF,WAAW,EAAE;UACpBA,WAAW,GAAGE,IAAI;UAClBH,QAAQ,GAAGD,CAAC;QAChB;MACJ;IACJ;IACA,IAAIC,QAAQ,GAAG,CAAC,EAAE;MACd,MAAM,IAAII,KAAK,CAAC,oBAAoB,GAAGrD,KAAK,GAAG,GAAG,CAAC;IACvD;IACA,OAAO,IAAIsD,MAAM,CAACjD,KAAK,CAACwC,WAAW,CAAC,CAACI,QAAQ,CAAC,CAAC;EACnD,CAAC;EACD,OAAO/E,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,SAASA,cAAc;AACvB,IAAIoF,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAACC,QAAQ,EAAE;IACtB,IAAIvD,KAAK,GAAGuD,QAAQ,CAACvD,KAAK;IAC1B,IAAIoD,IAAI,GAAG,CAAC;IACZ,IAAII,OAAO,GAAG,EAAE;IAChB,IAAIC,sBAAsB,GAAG,EAAE;IAC/B,IAAIC,gBAAgB,GAAG,EAAE;IACzB,IAAI,CAACH,QAAQ,CAACjB,IAAI,KAAKtE,IAAI,CAACkE,GAAG,IAC3BqB,QAAQ,CAACjB,IAAI,KAAKtE,IAAI,CAACmE,IAAI,IAC3BoB,QAAQ,CAACjB,IAAI,KAAKtE,IAAI,CAACyE,GAAG,KAC1Bc,QAAQ,CAAC7C,UAAU,CAAC,CAAC,KAAK1C,IAAI,CAAC6D,KAAK,EAAE;MACtCuB,IAAI,IAAI,IAAI,CAACO,OAAO,CAAC/B,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC,EAAEqD,OAAO,CAAC;IACrD;IACA,IAAII,OAAO,GAAGL,QAAQ;IACtB,OAAOK,OAAO,KAAK,IAAI,EAAE;MACrBR,IAAI,IAAI,IAAI,CAACO,OAAO,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,EAAEL,OAAO,CAAC;MACrD,IAAII,OAAO,CAAClC,QAAQ,KAAK,IAAI,IACzBkC,OAAO,CAACE,oBAAoB,CAAC,CAAC,KAAKF,OAAO,CAACG,OAAO,CAAC,CAAC,EAAE;QACtD,IAAIH,OAAO,CAACG,OAAO,CAAC,CAAC,KAAK/F,IAAI,CAAC0E,IAAI,EAAE;UACjC,IAAIU,IAAI,IAAI,GAAG,EAAE;YACbI,OAAO,CAACQ,OAAO,CAACZ,IAAI,CAAC;YACrBA,IAAI,EAAE;UACV,CAAC,MACI;YACDI,OAAO,CAACQ,OAAO,CAACZ,IAAI,GAAG,GAAG,CAAC;YAC3BI,OAAO,CAACQ,OAAO,CAACZ,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;YACjCA,IAAI,IAAI,CAAC;UACb;UACAK,sBAAsB,CAACpG,IAAI,CAACmG,OAAO,CAAC7G,MAAM,CAAC;UAC3C+G,gBAAgB,CAACrG,IAAI,CAAC+F,IAAI,CAAC;QAC/B;QACA,IAAI,CAACO,OAAO,CAACC,OAAO,CAACK,aAAa,CAAC,CAAC,EAAET,OAAO,CAAC;QAC9CJ,IAAI,GAAG,CAAC;MACZ;MACAQ,OAAO,GAAGA,OAAO,CAAClC,QAAQ;IAC9B;IACA,IAAI1B,KAAK,CAACkE,UAAU,CAAC,CAAC,KAAK,CAAC,EAAE;MAC1Bd,IAAI,IAAI,IAAI,CAACO,OAAO,CAAC/B,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC,EAAEqD,OAAO,CAAC;IACrD,CAAC,MACI,IAAIxD,KAAK,CAACkE,UAAU,CAAC,CAAC,KAAK,CAAC,EAAE;MAC/Bd,IAAI,IAAI,IAAI,CAACO,OAAO,CAAC/B,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC,EAAEqD,OAAO,CAAC;IACrD;IACA,IAAIxD,KAAK,CAACqB,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE;MAC9B+B,IAAI,IAAI,IAAI,CAACO,OAAO,CAAC/B,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC,EAAEqD,OAAO,CAAC;IACrD;IACA,KAAK,IAAI/G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgH,sBAAsB,CAAC9G,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpD,IAAI,CAAC0H,kBAAkB,CAACX,OAAO,EAAEA,OAAO,CAAC7G,MAAM,GAAG8G,sBAAsB,CAAChH,CAAC,CAAC,EAAEiH,gBAAgB,CAACjH,CAAC,CAAC,CAAC;IACrG;IACA;IACA,IAAI2H,QAAQ,GAAGb,QAAQ,CAACc,gBAAgB,CAACb,OAAO,CAAC7G,MAAM,CAAC;IACxD,IAAI6G,OAAO,CAAC7G,MAAM,GAAGyH,QAAQ,EAAE;MAC3BZ,OAAO,CAACnG,IAAI,CAAC,GAAG,CAAC;IACrB;IACA,OAAOmG,OAAO,CAAC7G,MAAM,GAAGyH,QAAQ,EAAE;MAC9BZ,OAAO,CAACnG,IAAI,CAAC,IAAI,CAACiH,iBAAiB,CAACd,OAAO,CAAC7G,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5D;IACA,IAAI,CAAC4H,KAAK,GAAG,IAAIC,UAAU,CAAChB,OAAO,CAAC7G,MAAM,CAAC;IAC3C,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8H,KAAK,CAAC5H,MAAM,EAAEF,CAAC,EAAE,EAAE;MACxC,IAAI,CAAC8H,KAAK,CAAC9H,CAAC,CAAC,GAAG+G,OAAO,CAAC/G,CAAC,CAAC;IAC9B;EACJ;EACA6G,MAAM,CAACrH,SAAS,CAAC0H,OAAO,GAAG,UAAUY,KAAK,EAAEE,IAAI,EAAE;IAC9C,KAAK,IAAIhI,CAAC,GAAG8H,KAAK,CAAC5H,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxCgI,IAAI,CAACT,OAAO,CAACO,KAAK,CAAC9H,CAAC,CAAC,CAAC;IAC1B;IACA,OAAO8H,KAAK,CAAC5H,MAAM;EACvB,CAAC;EACD2G,MAAM,CAACrH,SAAS,CAACqI,iBAAiB,GAAG,UAAUI,gBAAgB,EAAE;IAC7D,IAAIC,YAAY,GAAK,GAAG,GAAGD,gBAAgB,GAAI,GAAG,GAAI,CAAC;IACvD,IAAIE,YAAY,GAAG,GAAG,GAAGD,YAAY;IACrC,OAAOC,YAAY,IAAI,GAAG,GAAGA,YAAY,GAAGA,YAAY,GAAG,GAAG;EAClE,CAAC;EACDtB,MAAM,CAACrH,SAAS,CAACkI,kBAAkB,GAAG,UAAUX,OAAO,EAAEqB,aAAa,EAAElI,MAAM,EAAE;IAC5E,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC7B;MACA,IAAIqI,qBAAqB,GAAGD,aAAa,GAAGpI,CAAC;MAC7C,IAAIsI,kBAAkB,GAAGvB,OAAO,CAACsB,qBAAqB,CAAC,GAAG,IAAI;MAC9D,IAAIE,oBAAoB,GAAK,GAAG,IAAIF,qBAAqB,GAAG,CAAC,CAAC,GAAI,GAAG,GAAI,CAAC;MAC1E,IAAIG,aAAa,GAAGF,kBAAkB,GAAGC,oBAAoB;MAC7DxB,OAAO,CAACsB,qBAAqB,CAAC,GAC1BG,aAAa,IAAI,GAAG,GAAGA,aAAa,GAAGA,aAAa,GAAG,GAAG;IAClE;EACJ,CAAC;EACD3B,MAAM,CAACrH,SAAS,CAACkE,QAAQ,GAAG,YAAY;IACpC,OAAO,IAAI,CAACoE,KAAK;EACrB,CAAC;EACD,OAAOjB,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,IAAI1B,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAAC5B,KAAK,EAAEsC,IAAI,EAAE9B,YAAY,EAAEC,eAAe,EAAEiB,QAAQ,EAAE;IAChE,IAAI,CAAC1B,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACsC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC9B,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACiB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACwD,qBAAqB,GAAG,CACzB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC3E,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CACjD;IACD,IAAI,CAACC,wBAAwB,GAAG,CAC5B,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC1E,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAClC;IACD,IAAI,CAACC,6BAA6B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5D,IAAI,EAAE5E,YAAY,GAAGC,eAAe,IAAIT,KAAK,CAACrD,MAAM,CAAC,CAAC,CAAC,EAAE;MACrD,MAAM,IAAI0G,KAAK,CAAC,cAAc,CAAC;IACnC;IACA,IAAID,IAAI,GAAG1B,QAAQ,KAAK,IAAI,GAAGA,QAAQ,CAACf,eAAe,GAAG,CAAC;IAC3D,IAAI0E,YAAY,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACzC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,QAAQhD,IAAI;MACR,KAAKtE,IAAI,CAAC6D,KAAK;QACXuB,IAAI,EAAE;QACN,IAAIpD,KAAK,CAACgB,KAAK,CAACR,YAAY,CAAC,IACzBtC,cAAc,CAACC,eAAe,CAAC6B,KAAK,CAACkB,MAAM,CAACV,YAAY,CAAC,EAAER,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC,EAAE;UACtF+B,IAAI,EAAE;QACV;QACA,IAAIiC,YAAY,KAAKrH,IAAI,CAACkE,GAAG,IACzBmD,YAAY,KAAKrH,IAAI,CAACmE,IAAI,IAC1BkD,YAAY,KAAKrH,IAAI,CAACyE,GAAG,EAAE;UAC3BW,IAAI,EAAE,CAAC,CAAC;QACZ;QACA;MACJ,KAAKpF,IAAI,CAAC0E,IAAI;QACVU,IAAI,EAAE;QACN,IAAIiC,YAAY,KAAKrH,IAAI,CAAC0E,IAAI,EAAE;UAC5BU,IAAI,EAAE,CAAC,CAAC;QACZ,CAAC,MACI,IAAI,IAAI,CAACmC,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;UACjCnC,IAAI,EAAE,CAAC,CAAC;QACZ;QACA,IAAIiC,YAAY,KAAKrH,IAAI,CAAC6D,KAAK,EAAE;UAC7BuB,IAAI,EAAE,CAAC,CAAC;QACZ,CAAC,MACI,IAAIiC,YAAY,KAAKrH,IAAI,CAACkE,GAAG,IAC9BmD,YAAY,KAAKrH,IAAI,CAACmE,IAAI,IAC1BkD,YAAY,KAAKrH,IAAI,CAACyE,GAAG,EAAE;UAC3BW,IAAI,IAAI,CAAC,CAAC,CAAC;QACf;QACA;MACJ,KAAKpF,IAAI,CAACkE,GAAG;MACb,KAAKlE,IAAI,CAACmE,IAAI;MACd,KAAKnE,IAAI,CAACyE,GAAG;QACT,IAAIH,IAAI,KAAKtE,IAAI,CAACyE,GAAG,EAAE;UACnBW,IAAI,IAAI,CAAC;QACb,CAAC,MACI;UACD,IAAIoC,OAAO,GAAG,EAAE;UAChBpC,IAAI,IACAlF,cAAc,CAAC0C,mBAAmB,CAACZ,KAAK,EAAEQ,YAAY,EAAE8B,IAAI,KAAKtE,IAAI,CAACkE,GAAG,EAAEsD,OAAO,CAAC,GAAG,CAAC;QAC/F;QACA,IAAIH,YAAY,KAAKrH,IAAI,CAAC6D,KAAK,IAAIwD,YAAY,KAAKrH,IAAI,CAAC0E,IAAI,EAAE;UAC3DU,IAAI,EAAE,CAAC,CAAC;QACZ,CAAC,MACI,IAAIiC,YAAY,KAAK/C,IAAI,KACzB+C,YAAY,KAAKrH,IAAI,CAACkE,GAAG,IACtBmD,YAAY,KAAKrH,IAAI,CAACmE,IAAI,IAC1BkD,YAAY,KAAKrH,IAAI,CAACyE,GAAG,CAAC,EAAE;UAChCW,IAAI,IAAI,CAAC,CAAC,CAAC;QACf;QACA;MACJ,KAAKpF,IAAI,CAAC8D,GAAG;QACTsB,IAAI,IAAI,CAAC;QACT,IAAIiC,YAAY,KAAKrH,IAAI,CAAC6D,KAAK,IAAIwD,YAAY,KAAKrH,IAAI,CAAC0E,IAAI,EAAE;UAC3DU,IAAI,EAAE,CAAC,CAAC;QACZ,CAAC,MACI,IAAIiC,YAAY,KAAKrH,IAAI,CAACkE,GAAG,IAC9BmD,YAAY,KAAKrH,IAAI,CAACmE,IAAI,IAC1BkD,YAAY,KAAKrH,IAAI,CAACyE,GAAG,EAAE;UAC3BW,IAAI,IAAI,CAAC,CAAC,CAAC;QACf;QACA;IACR;IACA,IAAI,CAACzC,eAAe,GAAGyC,IAAI;EAC/B;EACA;EACAxB,IAAI,CAAC3F,SAAS,CAACsJ,WAAW,GAAG,YAAY;IACrC,IAAIE,GAAG,GAAG,CAAC;IACX,IAAI7B,OAAO,GAAG,IAAI;IAClB,OAAOA,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACtB,IAAI,KAAKtE,IAAI,CAAC0E,IAAI,IAAI+C,GAAG,IAAI,GAAG,EAAE;MACjEA,GAAG,EAAE;MACL7B,OAAO,GAAGA,OAAO,CAAClC,QAAQ;IAC9B;IACA,OAAO+D,GAAG;EACd,CAAC;EACD7D,IAAI,CAAC3F,SAAS,CAAC6H,oBAAoB,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACpC,QAAQ,KAAK,IAAI,GAAG1D,IAAI,CAAC6D,KAAK,GAAG,IAAI,CAACH,QAAQ,CAACY,IAAI;EACnE,CAAC;EACDV,IAAI,CAAC3F,SAAS,CAACqJ,eAAe,GAAG,YAAY;IACzC,OAAO,IAAI,CAAC5D,QAAQ,KAAK,IAAI,GAAG1D,IAAI,CAAC6D,KAAK,GAAG,IAAI,CAACH,QAAQ,CAAChB,UAAU,CAAC,CAAC;EAC3E,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIkB,IAAI,CAAC3F,SAAS,CAACyE,UAAU,GAAG,YAAY;IACpC,IAAI,IAAI,CAAC4B,IAAI,KAAKtE,IAAI,CAAC8D,GAAG,EAAE;MACxB,IAAI,IAAI,CAACrB,eAAe,GAAG,CAAC,EAAE;QAC1B,OAAOzC,IAAI,CAAC6D,KAAK;MACrB;MACA,IAAI6D,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC,CAAC;MACrC,IAAID,SAAS,GAAG,CAAC,IACb,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAACjF,eAAe,GAAG+E,SAAS,CAAC,IACxD,CAAC,GAAGA,SAAS,EAAE;QACnB,OAAO1H,IAAI,CAAC6D,KAAK;MACrB;IACJ;IACA,IAAI,IAAI,CAACS,IAAI,KAAKtE,IAAI,CAACkE,GAAG,IACtB,IAAI,CAACI,IAAI,KAAKtE,IAAI,CAACmE,IAAI,IACvB,IAAI,CAACG,IAAI,KAAKtE,IAAI,CAACyE,GAAG,EAAE;MACxB;MACA,IAAI,IAAI,CAACjC,YAAY,GAAG,IAAI,CAACC,eAAe,IAAI,IAAI,CAACT,KAAK,CAACrD,MAAM,CAAC,CAAC,IAC/D,IAAI,CAACiJ,qBAAqB,CAAC,IAAI,CAACjF,eAAe,CAAC,KAAK,CAAC,EAAE;QACxD,OAAO3C,IAAI,CAAC6D,KAAK;MACrB;MACA,IAAI6D,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MACnC,IAAID,SAAS,KAAK,CAAC,IACf,IAAI,CAACE,qBAAqB,CAAC,IAAI,CAACjF,eAAe,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC5D,OAAO3C,IAAI,CAAC6D,KAAK;MACrB;IACJ;IACA,OAAO,IAAI,CAACS,IAAI;EACpB,CAAC;EACDV,IAAI,CAAC3F,SAAS,CAAC8H,OAAO,GAAG,YAAY;IACjC,OAAO,IAAI,CAACzB,IAAI;EACpB,CAAC;EACD;AACJ;AACA;AACA;EACIV,IAAI,CAAC3F,SAAS,CAAC0J,YAAY,GAAG,YAAY;IACtC,IAAIhJ,MAAM,GAAG,IAAI,CAACqD,KAAK,CAACrD,MAAM,CAAC,CAAC;IAChC,IAAIkE,IAAI,GAAG,IAAI,CAACL,YAAY,GAAG,IAAI,CAACC,eAAe;IACnD,IAAI9D,MAAM,GAAGkE,IAAI,GAAG,CAAC,IAAIA,IAAI,IAAIlE,MAAM,EAAE;MACrC,OAAO,CAAC;IACZ;IACA,IAAIA,MAAM,GAAGkE,IAAI,KAAK,CAAC,EAAE;MACrB,IAAI3C,cAAc,CAACC,eAAe,CAAC,IAAI,CAAC6B,KAAK,CAACkB,MAAM,CAACL,IAAI,CAAC,EAAE,IAAI,CAACb,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC,EAAE;QACxF,OAAO,CAAC;MACZ;MACA,OAAO,CAAC;IACZ;IACA,IAAI1E,MAAM,GAAGkE,IAAI,KAAK,CAAC,EAAE;MACrB,IAAI3C,cAAc,CAACC,eAAe,CAAC,IAAI,CAAC6B,KAAK,CAACkB,MAAM,CAACL,IAAI,CAAC,EAAE,IAAI,CAACb,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC,IACtFnD,cAAc,CAACC,eAAe,CAAC,IAAI,CAAC6B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAACb,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC,EAAE;QAC5F,OAAO,CAAC;MACZ;MACA,IAAIxD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,CAAC,CAAC,IACjDhD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;QACvD,OAAO,CAAC;MACZ;MACA,OAAO,CAAC;IACZ;IACA,IAAIlE,MAAM,GAAGkE,IAAI,KAAK,CAAC,EAAE;MACrB,IAAIhD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,CAAC,CAAC,IACjDhD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,IACrD,CAAC3C,cAAc,CAACC,eAAe,CAAC,IAAI,CAAC6B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAACb,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC,EAAE;QAC7F,OAAO,CAAC;MACZ;MACA,IAAIxD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,IACrDhD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,IACrD,CAAC3C,cAAc,CAACC,eAAe,CAAC,IAAI,CAAC6B,KAAK,CAACkB,MAAM,CAACL,IAAI,CAAC,EAAE,IAAI,CAACb,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC,EAAE;QACzF,OAAO,CAAC;MACZ;MACA,OAAO,CAAC;IACZ;IACA,IAAIxD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,CAAC,CAAC,IACjDhD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,IACrDhD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,IACrDhD,gBAAgB,CAACkE,OAAO,CAAC,IAAI,CAAC/B,KAAK,CAACkB,MAAM,CAACL,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE;MACvD,OAAO,CAAC;IACZ;IACA,OAAO,CAAC;EACZ,CAAC;EACD;AACJ;AACA;EACIe,IAAI,CAAC3F,SAAS,CAACoI,gBAAgB,GAAG,UAAUwB,OAAO,EAAE;IACjD,IAAIC,GAAG,EAAErH,EAAE,EAAEsH,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IAC7B,QAAQ,IAAI,CAAClG,KAAK,CAACmG,YAAY,CAAC,CAAC;MAC7B,KAAK,CAAC,CAAC;QACH,IAAI;UACA,KAAK,IAAIC,EAAE,GAAGjK,QAAQ,CAAC,IAAI,CAACgJ,wBAAwB,CAAC,EAAEkB,EAAE,GAAGD,EAAE,CAACxJ,IAAI,CAAC,CAAC,EAAE,CAACyJ,EAAE,CAACvJ,IAAI,EAAEuJ,EAAE,GAAGD,EAAE,CAACxJ,IAAI,CAAC,CAAC,EAAE;YAC7F,IAAIwH,QAAQ,GAAGiC,EAAE,CAACxJ,KAAK;YACvB,IAAIuH,QAAQ,IAAIyB,OAAO,EAAE;cACrB,OAAOzB,QAAQ;YACnB;UACJ;QACJ,CAAC,CACD,OAAOkC,KAAK,EAAE;UAAER,GAAG,GAAG;YAAExI,KAAK,EAAEgJ;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAID,EAAE,IAAI,CAACA,EAAE,CAACvJ,IAAI,KAAK2B,EAAE,GAAG2H,EAAE,CAACrH,MAAM,CAAC,EAAEN,EAAE,CAAC/B,IAAI,CAAC0J,EAAE,CAAC;UACvD,CAAC,SACO;YAAE,IAAIN,GAAG,EAAE,MAAMA,GAAG,CAACxI,KAAK;UAAE;QACxC;QACA;MACJ,KAAK,CAAC,CAAC;QACH,IAAI;UACA,KAAK,IAAIiJ,EAAE,GAAGpK,QAAQ,CAAC,IAAI,CAACiJ,6BAA6B,CAAC,EAAEoB,EAAE,GAAGD,EAAE,CAAC3J,IAAI,CAAC,CAAC,EAAE,CAAC4J,EAAE,CAAC1J,IAAI,EAAE0J,EAAE,GAAGD,EAAE,CAAC3J,IAAI,CAAC,CAAC,EAAE;YAClG,IAAIwH,QAAQ,GAAGoC,EAAE,CAAC3J,KAAK;YACvB,IAAIuH,QAAQ,IAAIyB,OAAO,EAAE;cACrB,OAAOzB,QAAQ;YACnB;UACJ;QACJ,CAAC,CACD,OAAOqC,KAAK,EAAE;UAAEV,GAAG,GAAG;YAAEzI,KAAK,EAAEmJ;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAID,EAAE,IAAI,CAACA,EAAE,CAAC1J,IAAI,KAAKkJ,EAAE,GAAGO,EAAE,CAACxH,MAAM,CAAC,EAAEiH,EAAE,CAACtJ,IAAI,CAAC6J,EAAE,CAAC;UACvD,CAAC,SACO;YAAE,IAAIR,GAAG,EAAE,MAAMA,GAAG,CAACzI,KAAK;UAAE;QACxC;QACA;IACR;IACA,IAAI;MACA,KAAK,IAAIoJ,EAAE,GAAGvK,QAAQ,CAAC,IAAI,CAAC+I,qBAAqB,CAAC,EAAEyB,EAAE,GAAGD,EAAE,CAAC9J,IAAI,CAAC,CAAC,EAAE,CAAC+J,EAAE,CAAC7J,IAAI,EAAE6J,EAAE,GAAGD,EAAE,CAAC9J,IAAI,CAAC,CAAC,EAAE;QAC1F,IAAIwH,QAAQ,GAAGuC,EAAE,CAAC9J,KAAK;QACvB,IAAIuH,QAAQ,IAAIyB,OAAO,EAAE;UACrB,OAAOzB,QAAQ;QACnB;MACJ;IACJ,CAAC,CACD,OAAOwC,KAAK,EAAE;MAAEX,GAAG,GAAG;QAAE3I,KAAK,EAAEsJ;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAID,EAAE,IAAI,CAACA,EAAE,CAAC7J,IAAI,KAAKoJ,EAAE,GAAGQ,EAAE,CAAC3H,MAAM,CAAC,EAAEmH,EAAE,CAACxJ,IAAI,CAACgK,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIT,GAAG,EAAE,MAAMA,GAAG,CAAC3I,KAAK;MAAE;IACxC;IACA,OAAO,IAAI,CAAC4H,qBAAqB,CAAC,IAAI,CAACA,qBAAqB,CAACvI,MAAM,GAAG,CAAC,CAAC;EAC5E,CAAC;EACD;AACJ;AACA;EACIiF,IAAI,CAAC3F,SAAS,CAAC2J,qBAAqB,GAAG,UAAUC,OAAO,EAAE;IACtD,OAAO,IAAI,CAACxB,gBAAgB,CAACwB,OAAO,CAAC,GAAGA,OAAO;EACnD,CAAC;EACDjE,IAAI,CAACzB,QAAQ,GAAG,UAAU0G,EAAE,EAAEC,EAAE,EAAE;IAC9B,IAAIC,MAAM,GAAG,IAAIvC,UAAU,CAACsC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;IACvCC,MAAM,CAAC,CAAC,CAAC,GAAGF,EAAE;IACd,IAAIC,EAAE,EAAE;MACJC,MAAM,CAAC,CAAC,CAAC,GAAGD,EAAE;IAClB;IACA,OAAOC,MAAM;EACjB,CAAC;EACDnF,IAAI,CAAC3F,SAAS,CAAC+K,UAAU,GAAG,UAAUzC,KAAK,EAAE0C,MAAM,EAAEJ,EAAE,EAAEC,EAAE,EAAEI,EAAE,EAAE;IAC7D,IAAIC,KAAK,GAAG,IAAI,IAAIN,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,IAAIC,EAAE,GAAG,IAAI,CAAC,IAAII,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;IACnE3C,KAAK,CAAC0C,MAAM,CAAC,GAAGE,KAAK,GAAG,GAAG;IAC3B5C,KAAK,CAAC0C,MAAM,GAAG,CAAC,CAAC,GAAGE,KAAK,GAAG,GAAG;EACnC,CAAC;EACDvF,IAAI,CAAC3F,SAAS,CAACmL,WAAW,GAAG,UAAUC,CAAC,EAAE;IACtC,OAAOA,CAAC,KAAK,EAAE,GACT,CAAC,GACDA,CAAC,KAAK,EAAE,GACJ,CAAC,GACDA,CAAC,KAAK,EAAE,GACJ,CAAC,GACDA,CAAC,KAAK,EAAE,GACJ,CAAC,GACDA,CAAC,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,GACdA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,GACdA,CAAC,GAAG,EAAE,GACNA,CAAC;EAC/B,CAAC;EACDzF,IAAI,CAAC3F,SAAS,CAACqL,WAAW,GAAG,YAAY;IACrC,IAAI,EAAE,IAAI,CAAC7G,eAAe,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACnC,MAAM,IAAI4C,KAAK,CAAC,mCAAmC,CAAC;IACxD;IACA,IAAI0D,MAAM,GAAG,IAAIvC,UAAU,CAAE,IAAI,CAAC/D,eAAe,GAAG,CAAC,GAAI,CAAC,CAAC;IAC3D,KAAK,IAAIhE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsK,MAAM,CAACpK,MAAM,EAAEF,CAAC,IAAI,CAAC,EAAE;MACvC,IAAI,CAACuK,UAAU,CAACD,MAAM,EAAEtK,CAAC,EAAE,IAAI,CAAC2K,WAAW,CAAC,IAAI,CAACpH,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,GAAI/D,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC2K,WAAW,CAAC,IAAI,CAACpH,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,GAAI/D,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC2K,WAAW,CAAC,IAAI,CAACpH,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,GAAI/D,CAAC,GAAG,CAAC,GAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxP;IACA,OAAOsK,MAAM;EACjB,CAAC;EACDnF,IAAI,CAAC3F,SAAS,CAACsL,aAAa,GAAG,UAAUF,CAAC,EAAEvG,GAAG,EAAEzC,IAAI,EAAE;IACnD,OAAQyC,GAAG,IAAI5C,cAAc,CAACI,gBAAgB,CAAC+I,CAAC,CAAC,IAC5C,CAACvG,GAAG,IAAI5C,cAAc,CAACc,iBAAiB,CAACqI,CAAC,CAAE,GAC3C,CAAC,GACAvG,GAAG,IAAI5C,cAAc,CAACK,gBAAgB,CAAC8I,CAAC,EAAEhJ,IAAI,CAAC,IAC7C,CAACyC,GAAG,IAAI5C,cAAc,CAACe,iBAAiB,CAACoI,CAAC,EAAEhJ,IAAI,CAAE,GACjD,CAAC,GACD,CAAC;EACf,CAAC;EACDuD,IAAI,CAAC3F,SAAS,CAACuL,WAAW,GAAG,UAAU1G,GAAG,EAAE2G,QAAQ,EAAEJ,CAAC,EAAEhJ,IAAI,EAAE;IAC3D,IAAIgJ,CAAC,KAAKhJ,IAAI,EAAE;MACZ,IAAI,EAAEoJ,QAAQ,KAAK,CAAC,CAAC,EAAE;QACnB,MAAM,IAAIpE,KAAK,CAAC,oCAAoC,CAAC;MACzD;MACA,OAAO,EAAE;IACb;IACA,IAAIvC,GAAG,EAAE;MACL,OAAOuG,CAAC,IAAI,EAAE,GACRA,CAAC,GACDA,CAAC,KAAK,EAAE,GACJ,CAAC,GACDA,CAAC,IAAI,EAAE,GACHA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,GACHA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,GACHA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,GACHA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,GACHA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,GAAG,GACJA,CAAC,GAAG,EAAE,GACNA,CAAC;IACvC,CAAC,MACI;MACD,OAAOA,CAAC,KAAK,CAAC,GACR,CAAC,GACDI,QAAQ,KAAK,CAAC,IAAIJ,CAAC,IAAI,CAAC,GACpBA,CAAC,GAAG,CAAC,CAAC;MAAA,EACNI,QAAQ,KAAK,CAAC,IAAIJ,CAAC,IAAI,EAAE,GACrBA,CAAC,GACDA,CAAC,KAAK,EAAE,GACJ,CAAC,GACDA,CAAC,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,GACdA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,GACdA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,GACdA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,GACdA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,EAAE,IAAIA,CAAC,IAAI,EAAE,GACdA,CAAC,GAAG,EAAE,GACNA,CAAC,KAAK,EAAE,GACJ,CAAC,GACDA,CAAC,IAAI,EAAE,IAAIA,CAAC,IAAI,GAAG,GACfA,CAAC,GAAG,EAAE,GACNA,CAAC,IAAI,GAAG,IAAIA,CAAC,IAAI,GAAG,GAChBA,CAAC,GAAG,EAAE,GACNA,CAAC;IACvD;EACJ,CAAC;EACDzF,IAAI,CAAC3F,SAAS,CAACyL,WAAW,GAAG,UAAU5G,GAAG,EAAEzC,IAAI,EAAE;IAC9C,IAAIsJ,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIlL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACgE,eAAe,EAAEhE,CAAC,EAAE,EAAE;MAC3C,IAAIwE,EAAE,GAAG,IAAI,CAACjB,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,GAAG/D,CAAC,CAAC;MACjD,IAAKqE,GAAG,IAAIjD,gBAAgB,CAACsD,WAAW,CAACF,EAAE,CAAC,IACvC,CAACH,GAAG,IAAIjD,gBAAgB,CAACuD,YAAY,CAACH,EAAE,CAAE,EAAE;QAC7C0G,SAAS,CAACtK,IAAI,CAAC,IAAI,CAACmK,WAAW,CAAC1G,GAAG,EAAE,CAAC,EAAEG,EAAE,EAAE5C,IAAI,CAAC,CAAC;MACtD,CAAC,MACI,IAAI,CAACH,cAAc,CAACC,eAAe,CAAC8C,EAAE,EAAE5C,IAAI,CAAC,EAAE;QAChD,IAAIuJ,UAAU,GAAG,IAAI,CAACL,aAAa,CAACtG,EAAE,EAAEH,GAAG,EAAEzC,IAAI,CAAC;QAClDsJ,SAAS,CAACtK,IAAI,CAACuK,UAAU,CAAC,CAAC,CAAC;QAC5BD,SAAS,CAACtK,IAAI,CAAC,IAAI,CAACmK,WAAW,CAAC1G,GAAG,EAAE8G,UAAU,EAAE3G,EAAE,EAAE5C,IAAI,CAAC,CAAC;MAC/D,CAAC,MACI;QACD,IAAIiD,UAAU,GAAG,CAACL,EAAE,GAAG,IAAI,IAAI,GAAG;QAClC,IAAKH,GAAG,IAAIjD,gBAAgB,CAACsD,WAAW,CAACG,UAAU,CAAC,IAC/C,CAACR,GAAG,IAAIjD,gBAAgB,CAACuD,YAAY,CAACE,UAAU,CAAE,EAAE;UACrDqG,SAAS,CAACtK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UACnBsK,SAAS,CAACtK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;UACpBsK,SAAS,CAACtK,IAAI,CAAC,IAAI,CAACmK,WAAW,CAAC1G,GAAG,EAAE,CAAC,EAAEQ,UAAU,EAAEjD,IAAI,CAAC,CAAC;QAC9D,CAAC,MACI;UACDsJ,SAAS,CAACtK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UACnBsK,SAAS,CAACtK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;UACpB,IAAIuK,UAAU,GAAG,IAAI,CAACL,aAAa,CAACjG,UAAU,EAAER,GAAG,EAAEzC,IAAI,CAAC;UAC1DsJ,SAAS,CAACtK,IAAI,CAACuK,UAAU,CAAC,CAAC,CAAC;UAC5BD,SAAS,CAACtK,IAAI,CAAC,IAAI,CAACmK,WAAW,CAAC1G,GAAG,EAAE8G,UAAU,EAAEtG,UAAU,EAAEjD,IAAI,CAAC,CAAC;QACvE;MACJ;IACJ;IACA,IAAIsJ,SAAS,CAAChL,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;MAC5B,IAAI,EAAE,CAACgL,SAAS,CAAChL,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAClC,IAAI,CAAC6D,YAAY,GAAG,IAAI,CAACC,eAAe,KAAK,IAAI,CAACT,KAAK,CAACrD,MAAM,CAAC,CAAC,CAAC,EAAE;QACnE,MAAM,IAAI0G,KAAK,CAAC,mCAAmC,CAAC;MACxD;MACAsE,SAAS,CAACtK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACvB;IACA,IAAI0J,MAAM,GAAG,IAAIvC,UAAU,CAAEmD,SAAS,CAAChL,MAAM,GAAG,CAAC,GAAI,CAAC,CAAC;IACvD,IAAIkL,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIpL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkL,SAAS,CAAChL,MAAM,EAAEF,CAAC,IAAI,CAAC,EAAE;MAC1C,IAAI,CAACuK,UAAU,CAACD,MAAM,EAAEc,SAAS,EAAEF,SAAS,CAAClL,CAAC,CAAC,GAAG,IAAI,EAAEkL,SAAS,CAAClL,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,EAAEkL,SAAS,CAAClL,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;MACzGoL,SAAS,IAAI,CAAC;IAClB;IACA,OAAOd,MAAM;EACjB,CAAC;EACDnF,IAAI,CAAC3F,SAAS,CAAC6L,WAAW,GAAG,YAAY;IACrC,IAAIC,cAAc,GAAGxG,IAAI,CAACC,IAAI,CAAC,IAAI,CAACf,eAAe,GAAG,GAAG,CAAC;IAC1D,IAAIsG,MAAM,GAAG,IAAIvC,UAAU,CAACuD,cAAc,GAAG,CAAC,CAAC;IAC/C,IAAIpF,GAAG,GAAG,IAAI,CAACnC,YAAY;IAC3B,IAAIwH,MAAM,GAAGzG,IAAI,CAAC0G,GAAG,CAAC,IAAI,CAACzH,YAAY,GAAG,IAAI,CAACC,eAAe,GAAG,CAAC,EAAE,IAAI,CAACT,KAAK,CAACrD,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5F,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsL,cAAc,EAAEtL,CAAC,IAAI,CAAC,EAAE;MACxC,IAAIyL,SAAS,GAAG,EAAE;MAClB,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAIL,GAAG,IAAIqF,MAAM,EAAE;UACfE,SAAS,CAAClF,CAAC,CAAC,GAAG,IAAI,CAAChD,KAAK,CAACkB,MAAM,CAACyB,GAAG,EAAE,CAAC,GAAG,IAAI;QAClD,CAAC,MACI;UACDuF,SAAS,CAAClF,CAAC,CAAC,GAAGL,GAAG,KAAKqF,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC;QAChD;MACJ;MACA,IAAIG,KAAK,GAAGD,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;MAC9BC,KAAK,IAAID,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;MAC3BC,KAAK,IAAID,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;MAC1BC,KAAK,IAAID,SAAS,CAAC,CAAC,CAAC;MACrBnB,MAAM,CAACtK,CAAC,CAAC,GAAI0L,KAAK,IAAI,EAAE,GAAI,IAAI;MAChCpB,MAAM,CAACtK,CAAC,GAAG,CAAC,CAAC,GAAI0L,KAAK,IAAI,CAAC,GAAI,IAAI;MACnCpB,MAAM,CAACtK,CAAC,GAAG,CAAC,CAAC,GAAG0L,KAAK,GAAG,IAAI;IAChC;IACA,OAAOpB,MAAM;EACjB,CAAC;EACDnF,IAAI,CAAC3F,SAAS,CAACgI,aAAa,GAAG,YAAY;IACvC,QAAQ,IAAI,CAACqB,eAAe,CAAC,CAAC;MAC1B,KAAKtH,IAAI,CAAC6D,KAAK;MACf,KAAK7D,IAAI,CAAC0E,IAAI;QAAE;QACZ,QAAQ,IAAI,CAACJ,IAAI;UACb,KAAKtE,IAAI,CAAC0E,IAAI;YACV,OAAOd,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC;UAC7B,KAAKnC,IAAI,CAACkE,GAAG;YACT,OAAON,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC;UAC7B,KAAKnC,IAAI,CAACmE,IAAI;YACV,OAAOP,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC;UAC7B,KAAKnC,IAAI,CAACyE,GAAG;YACT,OAAOb,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC;UAC7B,KAAKnC,IAAI,CAAC8D,GAAG;YACT,OAAOF,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC;QACjC;QACA;MACJ,KAAKnC,IAAI,CAACkE,GAAG;MACb,KAAKlE,IAAI,CAACmE,IAAI;MACd,KAAKnE,IAAI,CAACyE,GAAG;QACT,IAAI,IAAI,CAACH,IAAI,KAAK,IAAI,CAACgD,eAAe,CAAC,CAAC,EAAE;UACtC,QAAQ,IAAI,CAAChD,IAAI;YACb,KAAKtE,IAAI,CAAC6D,KAAK;cACX,OAAOD,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC;YAC7B,KAAKnC,IAAI,CAAC0E,IAAI;cACV,OAAOd,IAAI,CAACzB,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;YAClC,KAAKnC,IAAI,CAACkE,GAAG;cACT,OAAON,IAAI,CAACzB,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;YAClC,KAAKnC,IAAI,CAACmE,IAAI;cACV,OAAOP,IAAI,CAACzB,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;YAClC,KAAKnC,IAAI,CAACyE,GAAG;cACT,OAAOb,IAAI,CAACzB,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;YAClC,KAAKnC,IAAI,CAAC8D,GAAG;cACT,OAAOF,IAAI,CAACzB,QAAQ,CAAC,GAAG,EAAE,GAAG,CAAC;UACtC;QACJ;QACA;MACJ,KAAKnC,IAAI,CAAC8D,GAAG;QACT;QACA,IAAI,IAAI,CAACQ,IAAI,KAAKtE,IAAI,CAAC8D,GAAG,EAAE;UACxB,MAAM,IAAIuB,KAAK,CAAC,4BAA4B,GAAG,IAAI,CAACf,IAAI,CAAC;QAC7D;QACA;IACR;IACA,OAAO,IAAIkC,UAAU,CAAC,CAAC,CAAC;EAC5B,CAAC;EACD;EACA5C,IAAI,CAAC3F,SAAS,CAAC4H,YAAY,GAAG,YAAY;IACtC,QAAQ,IAAI,CAACvB,IAAI;MACb,KAAKtE,IAAI,CAAC6D,KAAK;QACX,IAAI,IAAI,CAAC7B,KAAK,CAACgB,KAAK,CAAC,IAAI,CAACR,YAAY,CAAC,EAAE;UACrC,OAAOoB,IAAI,CAACzB,QAAQ,CAAC,GAAG,EAAE,IAAI,CAACH,KAAK,CAACoI,WAAW,CAAC,IAAI,CAAC5H,YAAY,CAAC,GAAG,CAAC,CAAC;QAC5E,CAAC,MACI,IAAItC,cAAc,CAACC,eAAe,CAAC,IAAI,CAAC6B,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,CAAC,EAAE,IAAI,CAACR,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC,EAAE;UAC1G,OAAOO,IAAI,CAACzB,QAAQ,CAAC,GAAG,EAAE,IAAI,CAACH,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,CAAC,GAAG,GAAG,CAAC;QACzE,CAAC,MACI,IAAI,IAAI,CAACC,eAAe,KAAK,CAAC,EAAE;UACjC,OAAOmB,IAAI,CAACzB,QAAQ,CAAC,IAAI,CAACH,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,CAAC,GAAG,EAAE,GAC1D,IAAI,CAACR,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,GAAG,CAAC,CAAC,GACxC,GAAG,CAAC;QACZ,CAAC,MACI,IAAI,IAAI,CAACR,KAAK,CAACqI,MAAM,CAAC,IAAI,CAAC7H,YAAY,CAAC,EAAE;UAC3C,OAAOoB,IAAI,CAACzB,QAAQ,CAAC,GAAG,CAAC;QAC7B,CAAC,MACI;UACD,OAAOyB,IAAI,CAACzB,QAAQ,CAAC,IAAI,CAACH,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,CAAC,GAAG,CAAC,CAAC;QAClE;MACJ,KAAKxC,IAAI,CAAC0E,IAAI;QACV,OAAOd,IAAI,CAACzB,QAAQ,CAAC,IAAI,CAACH,KAAK,CAACkB,MAAM,CAAC,IAAI,CAACV,YAAY,CAAC,CAAC;MAC9D,KAAKxC,IAAI,CAACkE,GAAG;QACT,OAAO,IAAI,CAACwF,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC1H,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC;MAChE,KAAKrD,IAAI,CAACmE,IAAI;QACV,OAAO,IAAI,CAACuF,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC1H,KAAK,CAACqB,gBAAgB,CAAC,CAAC,CAAC;MACjE,KAAKrD,IAAI,CAACyE,GAAG;QACT,OAAO,IAAI,CAAC6E,WAAW,CAAC,CAAC;MAC7B,KAAKtJ,IAAI,CAAC8D,GAAG;QACT,OAAO,IAAI,CAACgG,WAAW,CAAC,CAAC;IACjC;EACJ,CAAC;EACD,OAAOlG,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,IAAI1B,KAAK,GAAG,aAAe,UAAUoI,MAAM,EAAE;EACzCjN,SAAS,CAAC6E,KAAK,EAAEoI,MAAM,CAAC;EACxB,SAASpI,KAAKA,CAACqI,cAAc,EAAEnJ,eAAe,EAAEf,IAAI,EAAEgB,KAAK,EAAEC,OAAO,EAAE;IAClE,IAAIkJ,KAAK,GAAGF,MAAM,CAAC5L,IAAI,CAAC,IAAI,EAAE6L,cAAc,EAAEnJ,eAAe,EAAEf,IAAI,CAAC,IAAI,IAAI;IAC5EmK,KAAK,CAACnJ,KAAK,GAAGA,KAAK;IACnBmJ,KAAK,CAAClJ,OAAO,GAAGA,OAAO;IACvB,OAAOkJ,KAAK;EAChB;EACAtI,KAAK,CAACjE,SAAS,CAACiI,UAAU,GAAG,YAAY;IACrC,OAAO,IAAI,CAAC5E,OAAO;EACvB,CAAC;EACDY,KAAK,CAACjE,SAAS,CAACkK,YAAY,GAAG,YAAY;IACvC,OAAO,IAAI,CAAC9G,KAAK;EACrB,CAAC;EACD,OAAOa,KAAK;AAChB,CAAC,CAACpC,eAAe,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}