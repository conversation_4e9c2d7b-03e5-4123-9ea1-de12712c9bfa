{"ast": null, "code": "// The Latch Table shows, for each pair of Modes, the optimal method for\n// getting from one mode to another.  In the worst possible case, this can\n// be up to 14 bits.  In the best possible case, we are already there!\n// The high half-word of each entry gives the number of bits.\n// The low half-word of each entry are the actual bits necessary to change\nexport var LATCH_TABLE = [Int32Array.from([0, (5 << 16) + 28, (5 << 16) + 30, (5 << 16) + 29, (10 << 16) + (29 << 5) + 30 // UPPER -> MIXED -> PUNCT\n]), Int32Array.from([(9 << 16) + (30 << 4) + 14, 0, (5 << 16) + 30, (5 << 16) + 29, (10 << 16) + (29 << 5) + 30 // LOWER -> MIXED -> PUNCT\n]), Int32Array.from([(4 << 16) + 14, (9 << 16) + (14 << 5) + 28, 0, (9 << 16) + (14 << 5) + 29, (14 << 16) + (14 << 10) + (29 << 5) + 30\n// DIGIT -> UPPER -> MIXED -> PUNCT\n]), Int32Array.from([(5 << 16) + 29, (5 << 16) + 28, (10 << 16) + (29 << 5) + 30, 0, (5 << 16) + 30 // MIXED -> PUNCT\n]), Int32Array.from([(5 << 16) + 31, (10 << 16) + (31 << 5) + 28, (10 << 16) + (31 << 5) + 30, (10 << 16) + (31 << 5) + 29, 0])];", "map": {"version": 3, "names": ["LATCH_TABLE", "Int32Array", "from"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/LatchTable.js"], "sourcesContent": ["// The Latch Table shows, for each pair of Modes, the optimal method for\n// getting from one mode to another.  In the worst possible case, this can\n// be up to 14 bits.  In the best possible case, we are already there!\n// The high half-word of each entry gives the number of bits.\n// The low half-word of each entry are the actual bits necessary to change\nexport var LATCH_TABLE = [\n    Int32Array.from([\n        0,\n        (5 << 16) + 28,\n        (5 << 16) + 30,\n        (5 << 16) + 29,\n        (10 << 16) + (29 << 5) + 30 // UPPER -> MIXED -> PUNCT\n    ]),\n    Int32Array.from([\n        (9 << 16) + (30 << 4) + 14,\n        0,\n        (5 << 16) + 30,\n        (5 << 16) + 29,\n        (10 << 16) + (29 << 5) + 30 // LOWER -> MIXED -> PUNCT\n    ]),\n    Int32Array.from([\n        (4 << 16) + 14,\n        (9 << 16) + (14 << 5) + 28,\n        0,\n        (9 << 16) + (14 << 5) + 29,\n        (14 << 16) + (14 << 10) + (29 << 5) + 30\n        // DIGIT -> UPPER -> MIXED -> PUNCT\n    ]),\n    Int32Array.from([\n        (5 << 16) + 29,\n        (5 << 16) + 28,\n        (10 << 16) + (29 << 5) + 30,\n        0,\n        (5 << 16) + 30 // MIXED -> PUNCT\n    ]),\n    Int32Array.from([\n        (5 << 16) + 31,\n        (10 << 16) + (31 << 5) + 28,\n        (10 << 16) + (31 << 5) + 30,\n        (10 << 16) + (31 << 5) + 29,\n        0\n    ])\n];\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,WAAW,GAAG,CACrBC,UAAU,CAACC,IAAI,CAAC,CACZ,CAAC,EACD,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAAA,CAC/B,CAAC,EACFD,UAAU,CAACC,IAAI,CAAC,CACZ,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAC1B,CAAC,EACD,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;AAAA,CAC/B,CAAC,EACFD,UAAU,CAACC,IAAI,CAAC,CACZ,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAC1B,CAAC,EACD,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAC1B,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG;AACtC;AAAA,CACH,CAAC,EACFD,UAAU,CAACC,IAAI,CAAC,CACZ,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAC3B,CAAC,EACD,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;AAAA,CAClB,CAAC,EACFD,UAAU,CAACC,IAAI,CAAC,CACZ,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EACd,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAC3B,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAC3B,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAC3B,CAAC,CACJ,CAAC,CACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}