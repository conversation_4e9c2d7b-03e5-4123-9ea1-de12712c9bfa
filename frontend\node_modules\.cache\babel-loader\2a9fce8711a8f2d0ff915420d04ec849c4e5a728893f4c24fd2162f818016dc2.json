{"ast": null, "code": "import { getMonthsInYear } from \"../../utils/date-utils.js\";\nexport const getDateSectionConfigFromFormatToken = (utils, formatToken) => {\n  const config = utils.formatTokenMap[formatToken];\n  if (config == null) {\n    throw new Error([`MUI X: The token \"${formatToken}\" is not supported by the Date and Time Pickers.`, 'Please try using another token or open an issue on https://github.com/mui/mui-x/issues/new/choose if you think it should be supported.'].join('\\n'));\n  }\n  if (typeof config === 'string') {\n    return {\n      type: config,\n      contentType: config === 'meridiem' ? 'letter' : 'digit',\n      maxLength: undefined\n    };\n  }\n  return {\n    type: config.sectionType,\n    contentType: config.contentType,\n    maxLength: config.maxLength\n  };\n};\nconst getDeltaFromKeyCode = keyCode => {\n  switch (keyCode) {\n    case 'ArrowUp':\n      return 1;\n    case 'ArrowDown':\n      return -1;\n    case 'PageUp':\n      return 5;\n    case 'PageDown':\n      return -5;\n    default:\n      return 0;\n  }\n};\nexport const getDaysInWeekStr = (utils, format) => {\n  const elements = [];\n  const now = utils.date(undefined, 'default');\n  const startDate = utils.startOfWeek(now);\n  const endDate = utils.endOfWeek(now);\n  let current = startDate;\n  while (utils.isBefore(current, endDate)) {\n    elements.push(current);\n    current = utils.addDays(current, 1);\n  }\n  return elements.map(weekDay => utils.formatByString(weekDay, format));\n};\nexport const getLetterEditingOptions = (utils, timezone, sectionType, format) => {\n  switch (sectionType) {\n    case 'month':\n      {\n        return getMonthsInYear(utils, utils.date(undefined, timezone)).map(month => utils.formatByString(month, format));\n      }\n    case 'weekDay':\n      {\n        return getDaysInWeekStr(utils, format);\n      }\n    case 'meridiem':\n      {\n        const now = utils.date(undefined, timezone);\n        return [utils.startOfDay(now), utils.endOfDay(now)].map(date => utils.formatByString(date, format));\n      }\n    default:\n      {\n        return [];\n      }\n  }\n};\n\n// This format should be the same on all the adapters\n// If some adapter does not respect this convention, then we will need to hardcode the format on each adapter.\nexport const FORMAT_SECONDS_NO_LEADING_ZEROS = 's';\nconst NON_LOCALIZED_DIGITS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];\nexport const getLocalizedDigits = utils => {\n  const today = utils.date(undefined);\n  const formattedZero = utils.formatByString(utils.setSeconds(today, 0), FORMAT_SECONDS_NO_LEADING_ZEROS);\n  if (formattedZero === '0') {\n    return NON_LOCALIZED_DIGITS;\n  }\n  return Array.from({\n    length: 10\n  }).map((_, index) => utils.formatByString(utils.setSeconds(today, index), FORMAT_SECONDS_NO_LEADING_ZEROS));\n};\nexport const removeLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  const digits = [];\n  let currentFormattedDigit = '';\n  for (let i = 0; i < valueStr.length; i += 1) {\n    currentFormattedDigit += valueStr[i];\n    const matchingDigitIndex = localizedDigits.indexOf(currentFormattedDigit);\n    if (matchingDigitIndex > -1) {\n      digits.push(matchingDigitIndex.toString());\n      currentFormattedDigit = '';\n    }\n  }\n  return digits.join('');\n};\nexport const applyLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  return valueStr.split('').map(char => localizedDigits[Number(char)]).join('');\n};\nexport const isStringNumber = (valueStr, localizedDigits) => {\n  const nonLocalizedValueStr = removeLocalizedDigits(valueStr, localizedDigits);\n  // `Number(' ')` returns `0` even if ' ' is not a valid number.\n  return nonLocalizedValueStr !== ' ' && !Number.isNaN(Number(nonLocalizedValueStr));\n};\n\n/**\n * Remove the leading zeroes to a digit section value.\n * E.g.: `03` => `3`\n * Warning: Should only be called with non-localized digits. Call `removeLocalizedDigits` with your value if needed.\n */\nexport const cleanLeadingZeros = (valueStr, size) => {\n  let cleanValueStr = valueStr;\n\n  // Remove the leading zeros\n  cleanValueStr = Number(cleanValueStr).toString();\n\n  // Add enough leading zeros to fill the section\n  while (cleanValueStr.length < size) {\n    cleanValueStr = `0${cleanValueStr}`;\n  }\n  return cleanValueStr;\n};\nexport const cleanDigitSectionValue = (utils, value, sectionBoundaries, localizedDigits, section) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (section.type !== 'day' && section.contentType === 'digit-with-letter') {\n      throw new Error([`MUI X: The token \"${section.format}\" is a digit format with letter in it.'\n             This type of format is only supported for 'day' sections`].join('\\n'));\n    }\n  }\n  if (section.type === 'day' && section.contentType === 'digit-with-letter') {\n    const date = utils.setDate(sectionBoundaries.longestMonth, value);\n    return utils.formatByString(date, section.format);\n  }\n\n  // queryValue without leading `0` (`01` => `1`)\n  let valueStr = value.toString();\n  if (section.hasLeadingZerosInInput) {\n    valueStr = cleanLeadingZeros(valueStr, section.maxLength);\n  }\n  return applyLocalizedDigits(valueStr, localizedDigits);\n};\nexport const adjustSectionValue = (utils, timezone, section, keyCode, sectionsValueBoundaries, localizedDigits, activeDate, stepsAttributes) => {\n  const delta = getDeltaFromKeyCode(keyCode);\n  const isStart = keyCode === 'Home';\n  const isEnd = keyCode === 'End';\n  const shouldSetAbsolute = section.value === '' || isStart || isEnd;\n  const adjustDigitSection = () => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: activeDate,\n      format: section.format,\n      contentType: section.contentType\n    });\n    const getCleanValue = value => cleanDigitSectionValue(utils, value, sectionBoundaries, localizedDigits, section);\n    const step = section.type === 'minutes' && stepsAttributes?.minutesStep ? stepsAttributes.minutesStep : 1;\n    const currentSectionValue = parseInt(removeLocalizedDigits(section.value, localizedDigits), 10);\n    let newSectionValueNumber = currentSectionValue + delta * step;\n    if (shouldSetAbsolute) {\n      if (section.type === 'year' && !isEnd && !isStart) {\n        return utils.formatByString(utils.date(undefined, timezone), section.format);\n      }\n      if (delta > 0 || isStart) {\n        newSectionValueNumber = sectionBoundaries.minimum;\n      } else {\n        newSectionValueNumber = sectionBoundaries.maximum;\n      }\n    }\n    if (newSectionValueNumber % step !== 0) {\n      if (delta < 0 || isStart) {\n        newSectionValueNumber += step - (step + newSectionValueNumber) % step; // for JS -3 % 5 = -3 (should be 2)\n      }\n      if (delta > 0 || isEnd) {\n        newSectionValueNumber -= newSectionValueNumber % step;\n      }\n    }\n    if (newSectionValueNumber > sectionBoundaries.maximum) {\n      return getCleanValue(sectionBoundaries.minimum + (newSectionValueNumber - sectionBoundaries.maximum - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    if (newSectionValueNumber < sectionBoundaries.minimum) {\n      return getCleanValue(sectionBoundaries.maximum - (sectionBoundaries.minimum - newSectionValueNumber - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    return getCleanValue(newSectionValueNumber);\n  };\n  const adjustLetterSection = () => {\n    const options = getLetterEditingOptions(utils, timezone, section.type, section.format);\n    if (options.length === 0) {\n      return section.value;\n    }\n    if (shouldSetAbsolute) {\n      if (delta > 0 || isStart) {\n        return options[0];\n      }\n      return options[options.length - 1];\n    }\n    const currentOptionIndex = options.indexOf(section.value);\n    const newOptionIndex = (currentOptionIndex + delta) % options.length;\n    const clampedIndex = (newOptionIndex + options.length) % options.length;\n    return options[clampedIndex];\n  };\n  if (section.contentType === 'digit' || section.contentType === 'digit-with-letter') {\n    return adjustDigitSection();\n  }\n  return adjustLetterSection();\n};\nexport const getSectionVisibleValue = (section, target, localizedDigits) => {\n  let value = section.value || section.placeholder;\n  const hasLeadingZeros = target === 'non-input' ? section.hasLeadingZerosInFormat : section.hasLeadingZerosInInput;\n  if (target === 'non-input' && section.hasLeadingZerosInInput && !section.hasLeadingZerosInFormat) {\n    value = Number(removeLocalizedDigits(value, localizedDigits)).toString();\n  }\n\n  // In the input, we add an empty character at the end of each section without leading zeros.\n  // This makes sure that `onChange` will always be fired.\n  // Otherwise, when your input value equals `1/dd/yyyy` (format `M/DD/YYYY` on DayJs),\n  // If you press `1`, on the first section, the new value is also `1/dd/yyyy`,\n  // So the browser will not fire the input `onChange`.\n  const shouldAddInvisibleSpace = ['input-rtl', 'input-ltr'].includes(target) && section.contentType === 'digit' && !hasLeadingZeros && value.length === 1;\n  if (shouldAddInvisibleSpace) {\n    value = `${value}\\u200e`;\n  }\n  if (target === 'input-rtl') {\n    value = `\\u2068${value}\\u2069`;\n  }\n  return value;\n};\nexport const changeSectionValueFormat = (utils, valueStr, currentFormat, newFormat) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (getDateSectionConfigFromFormatToken(utils, currentFormat).type === 'weekDay') {\n      throw new Error(\"changeSectionValueFormat doesn't support week day formats\");\n    }\n  }\n  return utils.formatByString(utils.parse(valueStr, currentFormat), newFormat);\n};\nconst isFourDigitYearFormat = (utils, format) => utils.formatByString(utils.date(undefined, 'system'), format).length === 4;\nexport const doesSectionFormatHaveLeadingZeros = (utils, contentType, sectionType, format) => {\n  if (contentType !== 'digit') {\n    return false;\n  }\n  const now = utils.date(undefined, 'default');\n  switch (sectionType) {\n    // We can't use `changeSectionValueFormat`, because  `utils.parse('1', 'YYYY')` returns `1971` instead of `1`.\n    case 'year':\n      {\n        if (isFourDigitYearFormat(utils, format)) {\n          const formatted0001 = utils.formatByString(utils.setYear(now, 1), format);\n          return formatted0001 === '0001';\n        }\n        const formatted2001 = utils.formatByString(utils.setYear(now, 2001), format);\n        return formatted2001 === '01';\n      }\n    case 'month':\n      {\n        return utils.formatByString(utils.startOfYear(now), format).length > 1;\n      }\n    case 'day':\n      {\n        return utils.formatByString(utils.startOfMonth(now), format).length > 1;\n      }\n    case 'weekDay':\n      {\n        return utils.formatByString(utils.startOfWeek(now), format).length > 1;\n      }\n    case 'hours':\n      {\n        return utils.formatByString(utils.setHours(now, 1), format).length > 1;\n      }\n    case 'minutes':\n      {\n        return utils.formatByString(utils.setMinutes(now, 1), format).length > 1;\n      }\n    case 'seconds':\n      {\n        return utils.formatByString(utils.setSeconds(now, 1), format).length > 1;\n      }\n    default:\n      {\n        throw new Error('Invalid section type');\n      }\n  }\n};\n\n/**\n * Some date libraries like `dayjs` don't support parsing from date with escaped characters.\n * To make sure that the parsing works, we are building a format and a date without any separator.\n */\nexport const getDateFromDateSections = (utils, sections, localizedDigits) => {\n  // If we have both a day and a weekDay section,\n  // Then we skip the weekDay in the parsing because libraries like dayjs can't parse complicated formats containing a weekDay.\n  // dayjs(dayjs().format('dddd MMMM D YYYY'), 'dddd MMMM D YYYY')) // returns `Invalid Date` even if the format is valid.\n  const shouldSkipWeekDays = sections.some(section => section.type === 'day');\n  const sectionFormats = [];\n  const sectionValues = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const shouldSkip = shouldSkipWeekDays && section.type === 'weekDay';\n    if (!shouldSkip) {\n      sectionFormats.push(section.format);\n      sectionValues.push(getSectionVisibleValue(section, 'non-input', localizedDigits));\n    }\n  }\n  const formatWithoutSeparator = sectionFormats.join(' ');\n  const dateWithoutSeparatorStr = sectionValues.join(' ');\n  return utils.parse(dateWithoutSeparatorStr, formatWithoutSeparator);\n};\nexport const createDateStrForV7HiddenInputFromSections = sections => sections.map(section => {\n  return `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`;\n}).join('');\nexport const createDateStrForV6InputFromSections = (sections, localizedDigits, isRtl) => {\n  const formattedSections = sections.map(section => {\n    const dateValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    return `${section.startSeparator}${dateValue}${section.endSeparator}`;\n  });\n  const dateStr = formattedSections.join('');\n  if (!isRtl) {\n    return dateStr;\n  }\n\n  // \\u2066: start left-to-right isolation\n  // \\u2067: start right-to-left isolation\n  // \\u2068: start first strong character isolation\n  // \\u2069: pop isolation\n  // wrap into an isolated group such that separators can split the string in smaller ones by adding \\u2069\\u2068\n  return `\\u2066${dateStr}\\u2069`;\n};\nexport const getSectionsBoundaries = (utils, localizedDigits, timezone) => {\n  const today = utils.date(undefined, timezone);\n  const endOfYear = utils.endOfYear(today);\n  const endOfDay = utils.endOfDay(today);\n  const {\n    maxDaysInMonth,\n    longestMonth\n  } = getMonthsInYear(utils, today).reduce((acc, month) => {\n    const daysInMonth = utils.getDaysInMonth(month);\n    if (daysInMonth > acc.maxDaysInMonth) {\n      return {\n        maxDaysInMonth: daysInMonth,\n        longestMonth: month\n      };\n    }\n    return acc;\n  }, {\n    maxDaysInMonth: 0,\n    longestMonth: null\n  });\n  return {\n    year: ({\n      format\n    }) => ({\n      minimum: 0,\n      maximum: isFourDigitYearFormat(utils, format) ? 9999 : 99\n    }),\n    month: () => ({\n      minimum: 1,\n      // Assumption: All years have the same amount of months\n      maximum: utils.getMonth(endOfYear) + 1\n    }),\n    day: ({\n      currentDate\n    }) => ({\n      minimum: 1,\n      maximum: currentDate != null && utils.isValid(currentDate) ? utils.getDaysInMonth(currentDate) : maxDaysInMonth,\n      longestMonth: longestMonth\n    }),\n    weekDay: ({\n      format,\n      contentType\n    }) => {\n      if (contentType === 'digit') {\n        const daysInWeek = getDaysInWeekStr(utils, format).map(Number);\n        return {\n          minimum: Math.min(...daysInWeek),\n          maximum: Math.max(...daysInWeek)\n        };\n      }\n      return {\n        minimum: 1,\n        maximum: 7\n      };\n    },\n    hours: ({\n      format\n    }) => {\n      const lastHourInDay = utils.getHours(endOfDay);\n      const hasMeridiem = removeLocalizedDigits(utils.formatByString(utils.endOfDay(today), format), localizedDigits) !== lastHourInDay.toString();\n      if (hasMeridiem) {\n        return {\n          minimum: 1,\n          maximum: Number(removeLocalizedDigits(utils.formatByString(utils.startOfDay(today), format), localizedDigits))\n        };\n      }\n      return {\n        minimum: 0,\n        maximum: lastHourInDay\n      };\n    },\n    minutes: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of minutes\n      maximum: utils.getMinutes(endOfDay)\n    }),\n    seconds: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of seconds\n      maximum: utils.getSeconds(endOfDay)\n    }),\n    meridiem: () => ({\n      minimum: 0,\n      maximum: 1\n    }),\n    empty: () => ({\n      minimum: 0,\n      maximum: 0\n    })\n  };\n};\nlet warnedOnceInvalidSection = false;\nexport const validateSections = (sections, valueType) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceInvalidSection) {\n      const supportedSections = ['empty'];\n      if (['date', 'date-time'].includes(valueType)) {\n        supportedSections.push('weekDay', 'day', 'month', 'year');\n      }\n      if (['time', 'date-time'].includes(valueType)) {\n        supportedSections.push('hours', 'minutes', 'seconds', 'meridiem');\n      }\n      const invalidSection = sections.find(section => !supportedSections.includes(section.type));\n      if (invalidSection) {\n        console.warn(`MUI X: The field component you are using is not compatible with the \"${invalidSection.type}\" date section.`, `The supported date sections are [\"${supportedSections.join('\", \"')}\"]\\`.`);\n        warnedOnceInvalidSection = true;\n      }\n    }\n  }\n};\nconst transferDateSectionValue = (utils, section, dateToTransferFrom, dateToTransferTo) => {\n  switch (section.type) {\n    case 'year':\n      {\n        return utils.setYear(dateToTransferTo, utils.getYear(dateToTransferFrom));\n      }\n    case 'month':\n      {\n        return utils.setMonth(dateToTransferTo, utils.getMonth(dateToTransferFrom));\n      }\n    case 'weekDay':\n      {\n        const formattedDaysInWeek = getDaysInWeekStr(utils, section.format);\n        const dayInWeekStrOfActiveDate = utils.formatByString(dateToTransferFrom, section.format);\n        const dayInWeekOfActiveDate = formattedDaysInWeek.indexOf(dayInWeekStrOfActiveDate);\n        const dayInWeekOfNewSectionValue = formattedDaysInWeek.indexOf(section.value);\n        const diff = dayInWeekOfNewSectionValue - dayInWeekOfActiveDate;\n        return utils.addDays(dateToTransferFrom, diff);\n      }\n    case 'day':\n      {\n        return utils.setDate(dateToTransferTo, utils.getDate(dateToTransferFrom));\n      }\n    case 'meridiem':\n      {\n        const isAM = utils.getHours(dateToTransferFrom) < 12;\n        const mergedDateHours = utils.getHours(dateToTransferTo);\n        if (isAM && mergedDateHours >= 12) {\n          return utils.addHours(dateToTransferTo, -12);\n        }\n        if (!isAM && mergedDateHours < 12) {\n          return utils.addHours(dateToTransferTo, 12);\n        }\n        return dateToTransferTo;\n      }\n    case 'hours':\n      {\n        return utils.setHours(dateToTransferTo, utils.getHours(dateToTransferFrom));\n      }\n    case 'minutes':\n      {\n        return utils.setMinutes(dateToTransferTo, utils.getMinutes(dateToTransferFrom));\n      }\n    case 'seconds':\n      {\n        return utils.setSeconds(dateToTransferTo, utils.getSeconds(dateToTransferFrom));\n      }\n    default:\n      {\n        return dateToTransferTo;\n      }\n  }\n};\nconst reliableSectionModificationOrder = {\n  year: 1,\n  month: 2,\n  day: 3,\n  weekDay: 4,\n  hours: 5,\n  minutes: 6,\n  seconds: 7,\n  meridiem: 8,\n  empty: 9\n};\nexport const mergeDateIntoReferenceDate = (utils, dateToTransferFrom, sections, referenceDate, shouldLimitToEditedSections) =>\n// cloning sections before sort to avoid mutating it\n[...sections].sort((a, b) => reliableSectionModificationOrder[a.type] - reliableSectionModificationOrder[b.type]).reduce((mergedDate, section) => {\n  if (!shouldLimitToEditedSections || section.modified) {\n    return transferDateSectionValue(utils, section, dateToTransferFrom, mergedDate);\n  }\n  return mergedDate;\n}, referenceDate);\nexport const isAndroid = () => navigator.userAgent.toLowerCase().includes('android');\n\n// TODO v8: Remove if we drop the v6 TextField approach.\nexport const getSectionOrder = (sections, shouldApplyRTL) => {\n  const neighbors = {};\n  if (!shouldApplyRTL) {\n    sections.forEach((_, index) => {\n      const leftIndex = index === 0 ? null : index - 1;\n      const rightIndex = index === sections.length - 1 ? null : index + 1;\n      neighbors[index] = {\n        leftIndex,\n        rightIndex\n      };\n    });\n    return {\n      neighbors,\n      startIndex: 0,\n      endIndex: sections.length - 1\n    };\n  }\n  const rtl2ltr = {};\n  const ltr2rtl = {};\n  let groupedSectionsStart = 0;\n  let groupedSectionsEnd = 0;\n  let RTLIndex = sections.length - 1;\n  while (RTLIndex >= 0) {\n    groupedSectionsEnd = sections.findIndex(\n    // eslint-disable-next-line @typescript-eslint/no-loop-func\n    (section, index) => index >= groupedSectionsStart && section.endSeparator?.includes(' ') &&\n    // Special case where the spaces were not there in the initial input\n    section.endSeparator !== ' / ');\n    if (groupedSectionsEnd === -1) {\n      groupedSectionsEnd = sections.length - 1;\n    }\n    for (let i = groupedSectionsEnd; i >= groupedSectionsStart; i -= 1) {\n      ltr2rtl[i] = RTLIndex;\n      rtl2ltr[RTLIndex] = i;\n      RTLIndex -= 1;\n    }\n    groupedSectionsStart = groupedSectionsEnd + 1;\n  }\n  sections.forEach((_, index) => {\n    const rtlIndex = ltr2rtl[index];\n    const leftIndex = rtlIndex === 0 ? null : rtl2ltr[rtlIndex - 1];\n    const rightIndex = rtlIndex === sections.length - 1 ? null : rtl2ltr[rtlIndex + 1];\n    neighbors[index] = {\n      leftIndex,\n      rightIndex\n    };\n  });\n  return {\n    neighbors,\n    startIndex: rtl2ltr[0],\n    endIndex: rtl2ltr[sections.length - 1]\n  };\n};\nexport const parseSelectedSections = (selectedSections, sections) => {\n  if (selectedSections == null) {\n    return null;\n  }\n  if (selectedSections === 'all') {\n    return 'all';\n  }\n  if (typeof selectedSections === 'string') {\n    const index = sections.findIndex(section => section.type === selectedSections);\n    return index === -1 ? null : index;\n  }\n  return selectedSections;\n};\nexport const getSectionValueText = (section, utils) => {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return utils.format(utils.setMonth(utils.date(), Number(section.value) - 1), 'month');\n        }\n        const parsedDate = utils.parse(section.value, section.format);\n        return parsedDate ? utils.format(parsedDate, 'month') : undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit' ? utils.format(utils.setDate(utils.startOfYear(utils.date()), Number(section.value)), 'dayOfMonthFull') : section.value;\n    case 'weekDay':\n      // TODO: improve by providing the label of the week day\n      return undefined;\n    default:\n      return undefined;\n  }\n};\nexport const getSectionValueNow = (section, utils) => {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'weekDay':\n      {\n        if (section.contentType === 'letter') {\n          // TODO: improve by resolving the week day number from a letter week day\n          return undefined;\n        }\n        return Number(section.value);\n      }\n    case 'meridiem':\n      {\n        const parsedDate = utils.parse(`01:00 ${section.value}`, `${utils.formats.hours12h}:${utils.formats.minutes} ${section.format}`);\n        if (parsedDate) {\n          return utils.getHours(parsedDate) >= 12 ? 1 : 0;\n        }\n        return undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit-with-letter' ? parseInt(section.value, 10) : Number(section.value);\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return Number(section.value);\n        }\n        const parsedDate = utils.parse(section.value, section.format);\n        return parsedDate ? utils.getMonth(parsedDate) + 1 : undefined;\n      }\n    default:\n      return section.contentType !== 'letter' ? Number(section.value) : undefined;\n  }\n};", "map": {"version": 3, "names": ["getMonthsInYear", "getDateSectionConfigFromFormatToken", "utils", "formatToken", "config", "formatTokenMap", "Error", "join", "type", "contentType", "max<PERSON><PERSON><PERSON>", "undefined", "sectionType", "getDeltaFromKeyCode", "keyCode", "getDaysInWeekStr", "format", "elements", "now", "date", "startDate", "startOfWeek", "endDate", "endOfWeek", "current", "isBefore", "push", "addDays", "map", "weekDay", "formatByString", "getLetterEditingOptions", "timezone", "month", "startOfDay", "endOfDay", "FORMAT_SECONDS_NO_LEADING_ZEROS", "NON_LOCALIZED_DIGITS", "getLocalizedDigits", "today", "formattedZero", "setSeconds", "Array", "from", "length", "_", "index", "removeLocalizedDigits", "valueStr", "localizedDigits", "digits", "currentFormattedDigit", "i", "matchingDigitIndex", "indexOf", "toString", "applyLocalizedDigits", "split", "char", "Number", "isStringNumber", "nonLocalizedValueStr", "isNaN", "cleanLeadingZeros", "size", "cleanValueStr", "cleanDigitSectionValue", "value", "sectionBoundaries", "section", "process", "env", "NODE_ENV", "setDate", "longestMonth", "hasLeadingZerosInInput", "adjustSectionValue", "sectionsValueBoundaries", "activeDate", "stepsAttributes", "delta", "isStart", "isEnd", "shouldSetAbsolute", "adjustDigitSection", "currentDate", "getCleanValue", "step", "minutesStep", "currentSectionValue", "parseInt", "newSectionValueNumber", "minimum", "maximum", "adjustLetterSection", "options", "currentOptionIndex", "newOptionIndex", "clampedIndex", "getSectionVisibleValue", "target", "placeholder", "hasLeadingZeros", "hasLeadingZerosInFormat", "shouldAddInvisibleSpace", "includes", "changeSectionValueFormat", "currentFormat", "newFormat", "parse", "isFourDigitYearFormat", "doesSectionFormatHaveLeadingZeros", "formatted0001", "setYear", "formatted2001", "startOfYear", "startOfMonth", "setHours", "setMinutes", "getDateFromDateSections", "sections", "shouldSkipWeekDays", "some", "sectionFormats", "sectionValues", "shouldSkip", "formatWithoutSeparator", "dateWithoutSeparatorStr", "createDateStrForV7HiddenInputFromSections", "startSeparator", "endSeparator", "createDateStrForV6InputFromSections", "isRtl", "formattedSections", "dateValue", "dateStr", "getSectionsBoundaries", "endOfYear", "maxDaysInMonth", "reduce", "acc", "daysInMonth", "getDaysInMonth", "year", "getMonth", "day", "<PERSON><PERSON><PERSON><PERSON>", "daysInWeek", "Math", "min", "max", "hours", "lastHourInDay", "getHours", "hasMeridiem", "minutes", "getMinutes", "seconds", "getSeconds", "meridiem", "empty", "warnedOnceInvalidSection", "validateSections", "valueType", "supportedSections", "invalidSection", "find", "console", "warn", "transferDateSectionValue", "dateToTransferFrom", "dateToTransferTo", "getYear", "setMonth", "formattedDaysInWeek", "dayInWeekStrOfActiveDate", "dayInWeekOfActiveDate", "dayInWeekOfNewSectionValue", "diff", "getDate", "isAM", "mergedDateHours", "addHours", "reliableSectionModificationOrder", "mergeDateIntoReferenceDate", "referenceDate", "shouldLimitToEditedSections", "sort", "a", "b", "mergedDate", "modified", "isAndroid", "navigator", "userAgent", "toLowerCase", "getSectionOrder", "shouldApplyRTL", "neighbors", "for<PERSON>ach", "leftIndex", "rightIndex", "startIndex", "endIndex", "rtl2ltr", "ltr2rtl", "groupedSectionsStart", "groupedSectionsEnd", "RTLIndex", "findIndex", "rtlIndex", "parseSelectedSections", "selectedSections", "getSectionValueText", "parsedDate", "getSectionValueNow", "formats", "hours12h"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useField.utils.js"], "sourcesContent": ["import { getMonthsInYear } from \"../../utils/date-utils.js\";\nexport const getDateSectionConfigFromFormatToken = (utils, formatToken) => {\n  const config = utils.formatTokenMap[formatToken];\n  if (config == null) {\n    throw new Error([`MUI X: The token \"${formatToken}\" is not supported by the Date and Time Pickers.`, 'Please try using another token or open an issue on https://github.com/mui/mui-x/issues/new/choose if you think it should be supported.'].join('\\n'));\n  }\n  if (typeof config === 'string') {\n    return {\n      type: config,\n      contentType: config === 'meridiem' ? 'letter' : 'digit',\n      maxLength: undefined\n    };\n  }\n  return {\n    type: config.sectionType,\n    contentType: config.contentType,\n    maxLength: config.maxLength\n  };\n};\nconst getDeltaFromKeyCode = keyCode => {\n  switch (keyCode) {\n    case 'ArrowUp':\n      return 1;\n    case 'ArrowDown':\n      return -1;\n    case 'PageUp':\n      return 5;\n    case 'PageDown':\n      return -5;\n    default:\n      return 0;\n  }\n};\nexport const getDaysInWeekStr = (utils, format) => {\n  const elements = [];\n  const now = utils.date(undefined, 'default');\n  const startDate = utils.startOfWeek(now);\n  const endDate = utils.endOfWeek(now);\n  let current = startDate;\n  while (utils.isBefore(current, endDate)) {\n    elements.push(current);\n    current = utils.addDays(current, 1);\n  }\n  return elements.map(weekDay => utils.formatByString(weekDay, format));\n};\nexport const getLetterEditingOptions = (utils, timezone, sectionType, format) => {\n  switch (sectionType) {\n    case 'month':\n      {\n        return getMonthsInYear(utils, utils.date(undefined, timezone)).map(month => utils.formatByString(month, format));\n      }\n    case 'weekDay':\n      {\n        return getDaysInWeekStr(utils, format);\n      }\n    case 'meridiem':\n      {\n        const now = utils.date(undefined, timezone);\n        return [utils.startOfDay(now), utils.endOfDay(now)].map(date => utils.formatByString(date, format));\n      }\n    default:\n      {\n        return [];\n      }\n  }\n};\n\n// This format should be the same on all the adapters\n// If some adapter does not respect this convention, then we will need to hardcode the format on each adapter.\nexport const FORMAT_SECONDS_NO_LEADING_ZEROS = 's';\nconst NON_LOCALIZED_DIGITS = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];\nexport const getLocalizedDigits = utils => {\n  const today = utils.date(undefined);\n  const formattedZero = utils.formatByString(utils.setSeconds(today, 0), FORMAT_SECONDS_NO_LEADING_ZEROS);\n  if (formattedZero === '0') {\n    return NON_LOCALIZED_DIGITS;\n  }\n  return Array.from({\n    length: 10\n  }).map((_, index) => utils.formatByString(utils.setSeconds(today, index), FORMAT_SECONDS_NO_LEADING_ZEROS));\n};\nexport const removeLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  const digits = [];\n  let currentFormattedDigit = '';\n  for (let i = 0; i < valueStr.length; i += 1) {\n    currentFormattedDigit += valueStr[i];\n    const matchingDigitIndex = localizedDigits.indexOf(currentFormattedDigit);\n    if (matchingDigitIndex > -1) {\n      digits.push(matchingDigitIndex.toString());\n      currentFormattedDigit = '';\n    }\n  }\n  return digits.join('');\n};\nexport const applyLocalizedDigits = (valueStr, localizedDigits) => {\n  if (localizedDigits[0] === '0') {\n    return valueStr;\n  }\n  return valueStr.split('').map(char => localizedDigits[Number(char)]).join('');\n};\nexport const isStringNumber = (valueStr, localizedDigits) => {\n  const nonLocalizedValueStr = removeLocalizedDigits(valueStr, localizedDigits);\n  // `Number(' ')` returns `0` even if ' ' is not a valid number.\n  return nonLocalizedValueStr !== ' ' && !Number.isNaN(Number(nonLocalizedValueStr));\n};\n\n/**\n * Remove the leading zeroes to a digit section value.\n * E.g.: `03` => `3`\n * Warning: Should only be called with non-localized digits. Call `removeLocalizedDigits` with your value if needed.\n */\nexport const cleanLeadingZeros = (valueStr, size) => {\n  let cleanValueStr = valueStr;\n\n  // Remove the leading zeros\n  cleanValueStr = Number(cleanValueStr).toString();\n\n  // Add enough leading zeros to fill the section\n  while (cleanValueStr.length < size) {\n    cleanValueStr = `0${cleanValueStr}`;\n  }\n  return cleanValueStr;\n};\nexport const cleanDigitSectionValue = (utils, value, sectionBoundaries, localizedDigits, section) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (section.type !== 'day' && section.contentType === 'digit-with-letter') {\n      throw new Error([`MUI X: The token \"${section.format}\" is a digit format with letter in it.'\n             This type of format is only supported for 'day' sections`].join('\\n'));\n    }\n  }\n  if (section.type === 'day' && section.contentType === 'digit-with-letter') {\n    const date = utils.setDate(sectionBoundaries.longestMonth, value);\n    return utils.formatByString(date, section.format);\n  }\n\n  // queryValue without leading `0` (`01` => `1`)\n  let valueStr = value.toString();\n  if (section.hasLeadingZerosInInput) {\n    valueStr = cleanLeadingZeros(valueStr, section.maxLength);\n  }\n  return applyLocalizedDigits(valueStr, localizedDigits);\n};\nexport const adjustSectionValue = (utils, timezone, section, keyCode, sectionsValueBoundaries, localizedDigits, activeDate, stepsAttributes) => {\n  const delta = getDeltaFromKeyCode(keyCode);\n  const isStart = keyCode === 'Home';\n  const isEnd = keyCode === 'End';\n  const shouldSetAbsolute = section.value === '' || isStart || isEnd;\n  const adjustDigitSection = () => {\n    const sectionBoundaries = sectionsValueBoundaries[section.type]({\n      currentDate: activeDate,\n      format: section.format,\n      contentType: section.contentType\n    });\n    const getCleanValue = value => cleanDigitSectionValue(utils, value, sectionBoundaries, localizedDigits, section);\n    const step = section.type === 'minutes' && stepsAttributes?.minutesStep ? stepsAttributes.minutesStep : 1;\n    const currentSectionValue = parseInt(removeLocalizedDigits(section.value, localizedDigits), 10);\n    let newSectionValueNumber = currentSectionValue + delta * step;\n    if (shouldSetAbsolute) {\n      if (section.type === 'year' && !isEnd && !isStart) {\n        return utils.formatByString(utils.date(undefined, timezone), section.format);\n      }\n      if (delta > 0 || isStart) {\n        newSectionValueNumber = sectionBoundaries.minimum;\n      } else {\n        newSectionValueNumber = sectionBoundaries.maximum;\n      }\n    }\n    if (newSectionValueNumber % step !== 0) {\n      if (delta < 0 || isStart) {\n        newSectionValueNumber += step - (step + newSectionValueNumber) % step; // for JS -3 % 5 = -3 (should be 2)\n      }\n      if (delta > 0 || isEnd) {\n        newSectionValueNumber -= newSectionValueNumber % step;\n      }\n    }\n    if (newSectionValueNumber > sectionBoundaries.maximum) {\n      return getCleanValue(sectionBoundaries.minimum + (newSectionValueNumber - sectionBoundaries.maximum - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    if (newSectionValueNumber < sectionBoundaries.minimum) {\n      return getCleanValue(sectionBoundaries.maximum - (sectionBoundaries.minimum - newSectionValueNumber - 1) % (sectionBoundaries.maximum - sectionBoundaries.minimum + 1));\n    }\n    return getCleanValue(newSectionValueNumber);\n  };\n  const adjustLetterSection = () => {\n    const options = getLetterEditingOptions(utils, timezone, section.type, section.format);\n    if (options.length === 0) {\n      return section.value;\n    }\n    if (shouldSetAbsolute) {\n      if (delta > 0 || isStart) {\n        return options[0];\n      }\n      return options[options.length - 1];\n    }\n    const currentOptionIndex = options.indexOf(section.value);\n    const newOptionIndex = (currentOptionIndex + delta) % options.length;\n    const clampedIndex = (newOptionIndex + options.length) % options.length;\n    return options[clampedIndex];\n  };\n  if (section.contentType === 'digit' || section.contentType === 'digit-with-letter') {\n    return adjustDigitSection();\n  }\n  return adjustLetterSection();\n};\nexport const getSectionVisibleValue = (section, target, localizedDigits) => {\n  let value = section.value || section.placeholder;\n  const hasLeadingZeros = target === 'non-input' ? section.hasLeadingZerosInFormat : section.hasLeadingZerosInInput;\n  if (target === 'non-input' && section.hasLeadingZerosInInput && !section.hasLeadingZerosInFormat) {\n    value = Number(removeLocalizedDigits(value, localizedDigits)).toString();\n  }\n\n  // In the input, we add an empty character at the end of each section without leading zeros.\n  // This makes sure that `onChange` will always be fired.\n  // Otherwise, when your input value equals `1/dd/yyyy` (format `M/DD/YYYY` on DayJs),\n  // If you press `1`, on the first section, the new value is also `1/dd/yyyy`,\n  // So the browser will not fire the input `onChange`.\n  const shouldAddInvisibleSpace = ['input-rtl', 'input-ltr'].includes(target) && section.contentType === 'digit' && !hasLeadingZeros && value.length === 1;\n  if (shouldAddInvisibleSpace) {\n    value = `${value}\\u200e`;\n  }\n  if (target === 'input-rtl') {\n    value = `\\u2068${value}\\u2069`;\n  }\n  return value;\n};\nexport const changeSectionValueFormat = (utils, valueStr, currentFormat, newFormat) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (getDateSectionConfigFromFormatToken(utils, currentFormat).type === 'weekDay') {\n      throw new Error(\"changeSectionValueFormat doesn't support week day formats\");\n    }\n  }\n  return utils.formatByString(utils.parse(valueStr, currentFormat), newFormat);\n};\nconst isFourDigitYearFormat = (utils, format) => utils.formatByString(utils.date(undefined, 'system'), format).length === 4;\nexport const doesSectionFormatHaveLeadingZeros = (utils, contentType, sectionType, format) => {\n  if (contentType !== 'digit') {\n    return false;\n  }\n  const now = utils.date(undefined, 'default');\n  switch (sectionType) {\n    // We can't use `changeSectionValueFormat`, because  `utils.parse('1', 'YYYY')` returns `1971` instead of `1`.\n    case 'year':\n      {\n        if (isFourDigitYearFormat(utils, format)) {\n          const formatted0001 = utils.formatByString(utils.setYear(now, 1), format);\n          return formatted0001 === '0001';\n        }\n        const formatted2001 = utils.formatByString(utils.setYear(now, 2001), format);\n        return formatted2001 === '01';\n      }\n    case 'month':\n      {\n        return utils.formatByString(utils.startOfYear(now), format).length > 1;\n      }\n    case 'day':\n      {\n        return utils.formatByString(utils.startOfMonth(now), format).length > 1;\n      }\n    case 'weekDay':\n      {\n        return utils.formatByString(utils.startOfWeek(now), format).length > 1;\n      }\n    case 'hours':\n      {\n        return utils.formatByString(utils.setHours(now, 1), format).length > 1;\n      }\n    case 'minutes':\n      {\n        return utils.formatByString(utils.setMinutes(now, 1), format).length > 1;\n      }\n    case 'seconds':\n      {\n        return utils.formatByString(utils.setSeconds(now, 1), format).length > 1;\n      }\n    default:\n      {\n        throw new Error('Invalid section type');\n      }\n  }\n};\n\n/**\n * Some date libraries like `dayjs` don't support parsing from date with escaped characters.\n * To make sure that the parsing works, we are building a format and a date without any separator.\n */\nexport const getDateFromDateSections = (utils, sections, localizedDigits) => {\n  // If we have both a day and a weekDay section,\n  // Then we skip the weekDay in the parsing because libraries like dayjs can't parse complicated formats containing a weekDay.\n  // dayjs(dayjs().format('dddd MMMM D YYYY'), 'dddd MMMM D YYYY')) // returns `Invalid Date` even if the format is valid.\n  const shouldSkipWeekDays = sections.some(section => section.type === 'day');\n  const sectionFormats = [];\n  const sectionValues = [];\n  for (let i = 0; i < sections.length; i += 1) {\n    const section = sections[i];\n    const shouldSkip = shouldSkipWeekDays && section.type === 'weekDay';\n    if (!shouldSkip) {\n      sectionFormats.push(section.format);\n      sectionValues.push(getSectionVisibleValue(section, 'non-input', localizedDigits));\n    }\n  }\n  const formatWithoutSeparator = sectionFormats.join(' ');\n  const dateWithoutSeparatorStr = sectionValues.join(' ');\n  return utils.parse(dateWithoutSeparatorStr, formatWithoutSeparator);\n};\nexport const createDateStrForV7HiddenInputFromSections = sections => sections.map(section => {\n  return `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`;\n}).join('');\nexport const createDateStrForV6InputFromSections = (sections, localizedDigits, isRtl) => {\n  const formattedSections = sections.map(section => {\n    const dateValue = getSectionVisibleValue(section, isRtl ? 'input-rtl' : 'input-ltr', localizedDigits);\n    return `${section.startSeparator}${dateValue}${section.endSeparator}`;\n  });\n  const dateStr = formattedSections.join('');\n  if (!isRtl) {\n    return dateStr;\n  }\n\n  // \\u2066: start left-to-right isolation\n  // \\u2067: start right-to-left isolation\n  // \\u2068: start first strong character isolation\n  // \\u2069: pop isolation\n  // wrap into an isolated group such that separators can split the string in smaller ones by adding \\u2069\\u2068\n  return `\\u2066${dateStr}\\u2069`;\n};\nexport const getSectionsBoundaries = (utils, localizedDigits, timezone) => {\n  const today = utils.date(undefined, timezone);\n  const endOfYear = utils.endOfYear(today);\n  const endOfDay = utils.endOfDay(today);\n  const {\n    maxDaysInMonth,\n    longestMonth\n  } = getMonthsInYear(utils, today).reduce((acc, month) => {\n    const daysInMonth = utils.getDaysInMonth(month);\n    if (daysInMonth > acc.maxDaysInMonth) {\n      return {\n        maxDaysInMonth: daysInMonth,\n        longestMonth: month\n      };\n    }\n    return acc;\n  }, {\n    maxDaysInMonth: 0,\n    longestMonth: null\n  });\n  return {\n    year: ({\n      format\n    }) => ({\n      minimum: 0,\n      maximum: isFourDigitYearFormat(utils, format) ? 9999 : 99\n    }),\n    month: () => ({\n      minimum: 1,\n      // Assumption: All years have the same amount of months\n      maximum: utils.getMonth(endOfYear) + 1\n    }),\n    day: ({\n      currentDate\n    }) => ({\n      minimum: 1,\n      maximum: currentDate != null && utils.isValid(currentDate) ? utils.getDaysInMonth(currentDate) : maxDaysInMonth,\n      longestMonth: longestMonth\n    }),\n    weekDay: ({\n      format,\n      contentType\n    }) => {\n      if (contentType === 'digit') {\n        const daysInWeek = getDaysInWeekStr(utils, format).map(Number);\n        return {\n          minimum: Math.min(...daysInWeek),\n          maximum: Math.max(...daysInWeek)\n        };\n      }\n      return {\n        minimum: 1,\n        maximum: 7\n      };\n    },\n    hours: ({\n      format\n    }) => {\n      const lastHourInDay = utils.getHours(endOfDay);\n      const hasMeridiem = removeLocalizedDigits(utils.formatByString(utils.endOfDay(today), format), localizedDigits) !== lastHourInDay.toString();\n      if (hasMeridiem) {\n        return {\n          minimum: 1,\n          maximum: Number(removeLocalizedDigits(utils.formatByString(utils.startOfDay(today), format), localizedDigits))\n        };\n      }\n      return {\n        minimum: 0,\n        maximum: lastHourInDay\n      };\n    },\n    minutes: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of minutes\n      maximum: utils.getMinutes(endOfDay)\n    }),\n    seconds: () => ({\n      minimum: 0,\n      // Assumption: All years have the same amount of seconds\n      maximum: utils.getSeconds(endOfDay)\n    }),\n    meridiem: () => ({\n      minimum: 0,\n      maximum: 1\n    }),\n    empty: () => ({\n      minimum: 0,\n      maximum: 0\n    })\n  };\n};\nlet warnedOnceInvalidSection = false;\nexport const validateSections = (sections, valueType) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceInvalidSection) {\n      const supportedSections = ['empty'];\n      if (['date', 'date-time'].includes(valueType)) {\n        supportedSections.push('weekDay', 'day', 'month', 'year');\n      }\n      if (['time', 'date-time'].includes(valueType)) {\n        supportedSections.push('hours', 'minutes', 'seconds', 'meridiem');\n      }\n      const invalidSection = sections.find(section => !supportedSections.includes(section.type));\n      if (invalidSection) {\n        console.warn(`MUI X: The field component you are using is not compatible with the \"${invalidSection.type}\" date section.`, `The supported date sections are [\"${supportedSections.join('\", \"')}\"]\\`.`);\n        warnedOnceInvalidSection = true;\n      }\n    }\n  }\n};\nconst transferDateSectionValue = (utils, section, dateToTransferFrom, dateToTransferTo) => {\n  switch (section.type) {\n    case 'year':\n      {\n        return utils.setYear(dateToTransferTo, utils.getYear(dateToTransferFrom));\n      }\n    case 'month':\n      {\n        return utils.setMonth(dateToTransferTo, utils.getMonth(dateToTransferFrom));\n      }\n    case 'weekDay':\n      {\n        const formattedDaysInWeek = getDaysInWeekStr(utils, section.format);\n        const dayInWeekStrOfActiveDate = utils.formatByString(dateToTransferFrom, section.format);\n        const dayInWeekOfActiveDate = formattedDaysInWeek.indexOf(dayInWeekStrOfActiveDate);\n        const dayInWeekOfNewSectionValue = formattedDaysInWeek.indexOf(section.value);\n        const diff = dayInWeekOfNewSectionValue - dayInWeekOfActiveDate;\n        return utils.addDays(dateToTransferFrom, diff);\n      }\n    case 'day':\n      {\n        return utils.setDate(dateToTransferTo, utils.getDate(dateToTransferFrom));\n      }\n    case 'meridiem':\n      {\n        const isAM = utils.getHours(dateToTransferFrom) < 12;\n        const mergedDateHours = utils.getHours(dateToTransferTo);\n        if (isAM && mergedDateHours >= 12) {\n          return utils.addHours(dateToTransferTo, -12);\n        }\n        if (!isAM && mergedDateHours < 12) {\n          return utils.addHours(dateToTransferTo, 12);\n        }\n        return dateToTransferTo;\n      }\n    case 'hours':\n      {\n        return utils.setHours(dateToTransferTo, utils.getHours(dateToTransferFrom));\n      }\n    case 'minutes':\n      {\n        return utils.setMinutes(dateToTransferTo, utils.getMinutes(dateToTransferFrom));\n      }\n    case 'seconds':\n      {\n        return utils.setSeconds(dateToTransferTo, utils.getSeconds(dateToTransferFrom));\n      }\n    default:\n      {\n        return dateToTransferTo;\n      }\n  }\n};\nconst reliableSectionModificationOrder = {\n  year: 1,\n  month: 2,\n  day: 3,\n  weekDay: 4,\n  hours: 5,\n  minutes: 6,\n  seconds: 7,\n  meridiem: 8,\n  empty: 9\n};\nexport const mergeDateIntoReferenceDate = (utils, dateToTransferFrom, sections, referenceDate, shouldLimitToEditedSections) =>\n// cloning sections before sort to avoid mutating it\n[...sections].sort((a, b) => reliableSectionModificationOrder[a.type] - reliableSectionModificationOrder[b.type]).reduce((mergedDate, section) => {\n  if (!shouldLimitToEditedSections || section.modified) {\n    return transferDateSectionValue(utils, section, dateToTransferFrom, mergedDate);\n  }\n  return mergedDate;\n}, referenceDate);\nexport const isAndroid = () => navigator.userAgent.toLowerCase().includes('android');\n\n// TODO v8: Remove if we drop the v6 TextField approach.\nexport const getSectionOrder = (sections, shouldApplyRTL) => {\n  const neighbors = {};\n  if (!shouldApplyRTL) {\n    sections.forEach((_, index) => {\n      const leftIndex = index === 0 ? null : index - 1;\n      const rightIndex = index === sections.length - 1 ? null : index + 1;\n      neighbors[index] = {\n        leftIndex,\n        rightIndex\n      };\n    });\n    return {\n      neighbors,\n      startIndex: 0,\n      endIndex: sections.length - 1\n    };\n  }\n  const rtl2ltr = {};\n  const ltr2rtl = {};\n  let groupedSectionsStart = 0;\n  let groupedSectionsEnd = 0;\n  let RTLIndex = sections.length - 1;\n  while (RTLIndex >= 0) {\n    groupedSectionsEnd = sections.findIndex(\n    // eslint-disable-next-line @typescript-eslint/no-loop-func\n    (section, index) => index >= groupedSectionsStart && section.endSeparator?.includes(' ') &&\n    // Special case where the spaces were not there in the initial input\n    section.endSeparator !== ' / ');\n    if (groupedSectionsEnd === -1) {\n      groupedSectionsEnd = sections.length - 1;\n    }\n    for (let i = groupedSectionsEnd; i >= groupedSectionsStart; i -= 1) {\n      ltr2rtl[i] = RTLIndex;\n      rtl2ltr[RTLIndex] = i;\n      RTLIndex -= 1;\n    }\n    groupedSectionsStart = groupedSectionsEnd + 1;\n  }\n  sections.forEach((_, index) => {\n    const rtlIndex = ltr2rtl[index];\n    const leftIndex = rtlIndex === 0 ? null : rtl2ltr[rtlIndex - 1];\n    const rightIndex = rtlIndex === sections.length - 1 ? null : rtl2ltr[rtlIndex + 1];\n    neighbors[index] = {\n      leftIndex,\n      rightIndex\n    };\n  });\n  return {\n    neighbors,\n    startIndex: rtl2ltr[0],\n    endIndex: rtl2ltr[sections.length - 1]\n  };\n};\nexport const parseSelectedSections = (selectedSections, sections) => {\n  if (selectedSections == null) {\n    return null;\n  }\n  if (selectedSections === 'all') {\n    return 'all';\n  }\n  if (typeof selectedSections === 'string') {\n    const index = sections.findIndex(section => section.type === selectedSections);\n    return index === -1 ? null : index;\n  }\n  return selectedSections;\n};\nexport const getSectionValueText = (section, utils) => {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return utils.format(utils.setMonth(utils.date(), Number(section.value) - 1), 'month');\n        }\n        const parsedDate = utils.parse(section.value, section.format);\n        return parsedDate ? utils.format(parsedDate, 'month') : undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit' ? utils.format(utils.setDate(utils.startOfYear(utils.date()), Number(section.value)), 'dayOfMonthFull') : section.value;\n    case 'weekDay':\n      // TODO: improve by providing the label of the week day\n      return undefined;\n    default:\n      return undefined;\n  }\n};\nexport const getSectionValueNow = (section, utils) => {\n  if (!section.value) {\n    return undefined;\n  }\n  switch (section.type) {\n    case 'weekDay':\n      {\n        if (section.contentType === 'letter') {\n          // TODO: improve by resolving the week day number from a letter week day\n          return undefined;\n        }\n        return Number(section.value);\n      }\n    case 'meridiem':\n      {\n        const parsedDate = utils.parse(`01:00 ${section.value}`, `${utils.formats.hours12h}:${utils.formats.minutes} ${section.format}`);\n        if (parsedDate) {\n          return utils.getHours(parsedDate) >= 12 ? 1 : 0;\n        }\n        return undefined;\n      }\n    case 'day':\n      return section.contentType === 'digit-with-letter' ? parseInt(section.value, 10) : Number(section.value);\n    case 'month':\n      {\n        if (section.contentType === 'digit') {\n          return Number(section.value);\n        }\n        const parsedDate = utils.parse(section.value, section.format);\n        return parsedDate ? utils.getMonth(parsedDate) + 1 : undefined;\n      }\n    default:\n      return section.contentType !== 'letter' ? Number(section.value) : undefined;\n  }\n};"], "mappings": "AAAA,SAASA,eAAe,QAAQ,2BAA2B;AAC3D,OAAO,MAAMC,mCAAmC,GAAGA,CAACC,KAAK,EAAEC,WAAW,KAAK;EACzE,MAAMC,MAAM,GAAGF,KAAK,CAACG,cAAc,CAACF,WAAW,CAAC;EAChD,IAAIC,MAAM,IAAI,IAAI,EAAE;IAClB,MAAM,IAAIE,KAAK,CAAC,CAAC,qBAAqBH,WAAW,kDAAkD,EAAE,wIAAwI,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5P;EACA,IAAI,OAAOH,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO;MACLI,IAAI,EAAEJ,MAAM;MACZK,WAAW,EAAEL,MAAM,KAAK,UAAU,GAAG,QAAQ,GAAG,OAAO;MACvDM,SAAS,EAAEC;IACb,CAAC;EACH;EACA,OAAO;IACLH,IAAI,EAAEJ,MAAM,CAACQ,WAAW;IACxBH,WAAW,EAAEL,MAAM,CAACK,WAAW;IAC/BC,SAAS,EAAEN,MAAM,CAACM;EACpB,CAAC;AACH,CAAC;AACD,MAAMG,mBAAmB,GAAGC,OAAO,IAAI;EACrC,QAAQA,OAAO;IACb,KAAK,SAAS;MACZ,OAAO,CAAC;IACV,KAAK,WAAW;MACd,OAAO,CAAC,CAAC;IACX,KAAK,QAAQ;MACX,OAAO,CAAC;IACV,KAAK,UAAU;MACb,OAAO,CAAC,CAAC;IACX;MACE,OAAO,CAAC;EACZ;AACF,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGA,CAACb,KAAK,EAAEc,MAAM,KAAK;EACjD,MAAMC,QAAQ,GAAG,EAAE;EACnB,MAAMC,GAAG,GAAGhB,KAAK,CAACiB,IAAI,CAACR,SAAS,EAAE,SAAS,CAAC;EAC5C,MAAMS,SAAS,GAAGlB,KAAK,CAACmB,WAAW,CAACH,GAAG,CAAC;EACxC,MAAMI,OAAO,GAAGpB,KAAK,CAACqB,SAAS,CAACL,GAAG,CAAC;EACpC,IAAIM,OAAO,GAAGJ,SAAS;EACvB,OAAOlB,KAAK,CAACuB,QAAQ,CAACD,OAAO,EAAEF,OAAO,CAAC,EAAE;IACvCL,QAAQ,CAACS,IAAI,CAACF,OAAO,CAAC;IACtBA,OAAO,GAAGtB,KAAK,CAACyB,OAAO,CAACH,OAAO,EAAE,CAAC,CAAC;EACrC;EACA,OAAOP,QAAQ,CAACW,GAAG,CAACC,OAAO,IAAI3B,KAAK,CAAC4B,cAAc,CAACD,OAAO,EAAEb,MAAM,CAAC,CAAC;AACvE,CAAC;AACD,OAAO,MAAMe,uBAAuB,GAAGA,CAAC7B,KAAK,EAAE8B,QAAQ,EAAEpB,WAAW,EAAEI,MAAM,KAAK;EAC/E,QAAQJ,WAAW;IACjB,KAAK,OAAO;MACV;QACE,OAAOZ,eAAe,CAACE,KAAK,EAAEA,KAAK,CAACiB,IAAI,CAACR,SAAS,EAAEqB,QAAQ,CAAC,CAAC,CAACJ,GAAG,CAACK,KAAK,IAAI/B,KAAK,CAAC4B,cAAc,CAACG,KAAK,EAAEjB,MAAM,CAAC,CAAC;MAClH;IACF,KAAK,SAAS;MACZ;QACE,OAAOD,gBAAgB,CAACb,KAAK,EAAEc,MAAM,CAAC;MACxC;IACF,KAAK,UAAU;MACb;QACE,MAAME,GAAG,GAAGhB,KAAK,CAACiB,IAAI,CAACR,SAAS,EAAEqB,QAAQ,CAAC;QAC3C,OAAO,CAAC9B,KAAK,CAACgC,UAAU,CAAChB,GAAG,CAAC,EAAEhB,KAAK,CAACiC,QAAQ,CAACjB,GAAG,CAAC,CAAC,CAACU,GAAG,CAACT,IAAI,IAAIjB,KAAK,CAAC4B,cAAc,CAACX,IAAI,EAAEH,MAAM,CAAC,CAAC;MACrG;IACF;MACE;QACE,OAAO,EAAE;MACX;EACJ;AACF,CAAC;;AAED;AACA;AACA,OAAO,MAAMoB,+BAA+B,GAAG,GAAG;AAClD,MAAMC,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC/E,OAAO,MAAMC,kBAAkB,GAAGpC,KAAK,IAAI;EACzC,MAAMqC,KAAK,GAAGrC,KAAK,CAACiB,IAAI,CAACR,SAAS,CAAC;EACnC,MAAM6B,aAAa,GAAGtC,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACuC,UAAU,CAACF,KAAK,EAAE,CAAC,CAAC,EAAEH,+BAA+B,CAAC;EACvG,IAAII,aAAa,KAAK,GAAG,EAAE;IACzB,OAAOH,oBAAoB;EAC7B;EACA,OAAOK,KAAK,CAACC,IAAI,CAAC;IAChBC,MAAM,EAAE;EACV,CAAC,CAAC,CAAChB,GAAG,CAAC,CAACiB,CAAC,EAAEC,KAAK,KAAK5C,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACuC,UAAU,CAACF,KAAK,EAAEO,KAAK,CAAC,EAAEV,+BAA+B,CAAC,CAAC;AAC7G,CAAC;AACD,OAAO,MAAMW,qBAAqB,GAAGA,CAACC,QAAQ,EAAEC,eAAe,KAAK;EAClE,IAAIA,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC9B,OAAOD,QAAQ;EACjB;EACA,MAAME,MAAM,GAAG,EAAE;EACjB,IAAIC,qBAAqB,GAAG,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,CAACJ,MAAM,EAAEQ,CAAC,IAAI,CAAC,EAAE;IAC3CD,qBAAqB,IAAIH,QAAQ,CAACI,CAAC,CAAC;IACpC,MAAMC,kBAAkB,GAAGJ,eAAe,CAACK,OAAO,CAACH,qBAAqB,CAAC;IACzE,IAAIE,kBAAkB,GAAG,CAAC,CAAC,EAAE;MAC3BH,MAAM,CAACxB,IAAI,CAAC2B,kBAAkB,CAACE,QAAQ,CAAC,CAAC,CAAC;MAC1CJ,qBAAqB,GAAG,EAAE;IAC5B;EACF;EACA,OAAOD,MAAM,CAAC3C,IAAI,CAAC,EAAE,CAAC;AACxB,CAAC;AACD,OAAO,MAAMiD,oBAAoB,GAAGA,CAACR,QAAQ,EAAEC,eAAe,KAAK;EACjE,IAAIA,eAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC9B,OAAOD,QAAQ;EACjB;EACA,OAAOA,QAAQ,CAACS,KAAK,CAAC,EAAE,CAAC,CAAC7B,GAAG,CAAC8B,IAAI,IAAIT,eAAe,CAACU,MAAM,CAACD,IAAI,CAAC,CAAC,CAAC,CAACnD,IAAI,CAAC,EAAE,CAAC;AAC/E,CAAC;AACD,OAAO,MAAMqD,cAAc,GAAGA,CAACZ,QAAQ,EAAEC,eAAe,KAAK;EAC3D,MAAMY,oBAAoB,GAAGd,qBAAqB,CAACC,QAAQ,EAAEC,eAAe,CAAC;EAC7E;EACA,OAAOY,oBAAoB,KAAK,GAAG,IAAI,CAACF,MAAM,CAACG,KAAK,CAACH,MAAM,CAACE,oBAAoB,CAAC,CAAC;AACpF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,iBAAiB,GAAGA,CAACf,QAAQ,EAAEgB,IAAI,KAAK;EACnD,IAAIC,aAAa,GAAGjB,QAAQ;;EAE5B;EACAiB,aAAa,GAAGN,MAAM,CAACM,aAAa,CAAC,CAACV,QAAQ,CAAC,CAAC;;EAEhD;EACA,OAAOU,aAAa,CAACrB,MAAM,GAAGoB,IAAI,EAAE;IAClCC,aAAa,GAAG,IAAIA,aAAa,EAAE;EACrC;EACA,OAAOA,aAAa;AACtB,CAAC;AACD,OAAO,MAAMC,sBAAsB,GAAGA,CAAChE,KAAK,EAAEiE,KAAK,EAAEC,iBAAiB,EAAEnB,eAAe,EAAEoB,OAAO,KAAK;EACnG,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIH,OAAO,CAAC7D,IAAI,KAAK,KAAK,IAAI6D,OAAO,CAAC5D,WAAW,KAAK,mBAAmB,EAAE;MACzE,MAAM,IAAIH,KAAK,CAAC,CAAC,qBAAqB+D,OAAO,CAACrD,MAAM;AAC1D,sEAAsE,CAAC,CAACT,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/E;EACF;EACA,IAAI8D,OAAO,CAAC7D,IAAI,KAAK,KAAK,IAAI6D,OAAO,CAAC5D,WAAW,KAAK,mBAAmB,EAAE;IACzE,MAAMU,IAAI,GAAGjB,KAAK,CAACuE,OAAO,CAACL,iBAAiB,CAACM,YAAY,EAAEP,KAAK,CAAC;IACjE,OAAOjE,KAAK,CAAC4B,cAAc,CAACX,IAAI,EAAEkD,OAAO,CAACrD,MAAM,CAAC;EACnD;;EAEA;EACA,IAAIgC,QAAQ,GAAGmB,KAAK,CAACZ,QAAQ,CAAC,CAAC;EAC/B,IAAIc,OAAO,CAACM,sBAAsB,EAAE;IAClC3B,QAAQ,GAAGe,iBAAiB,CAACf,QAAQ,EAAEqB,OAAO,CAAC3D,SAAS,CAAC;EAC3D;EACA,OAAO8C,oBAAoB,CAACR,QAAQ,EAAEC,eAAe,CAAC;AACxD,CAAC;AACD,OAAO,MAAM2B,kBAAkB,GAAGA,CAAC1E,KAAK,EAAE8B,QAAQ,EAAEqC,OAAO,EAAEvD,OAAO,EAAE+D,uBAAuB,EAAE5B,eAAe,EAAE6B,UAAU,EAAEC,eAAe,KAAK;EAC9I,MAAMC,KAAK,GAAGnE,mBAAmB,CAACC,OAAO,CAAC;EAC1C,MAAMmE,OAAO,GAAGnE,OAAO,KAAK,MAAM;EAClC,MAAMoE,KAAK,GAAGpE,OAAO,KAAK,KAAK;EAC/B,MAAMqE,iBAAiB,GAAGd,OAAO,CAACF,KAAK,KAAK,EAAE,IAAIc,OAAO,IAAIC,KAAK;EAClE,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMhB,iBAAiB,GAAGS,uBAAuB,CAACR,OAAO,CAAC7D,IAAI,CAAC,CAAC;MAC9D6E,WAAW,EAAEP,UAAU;MACvB9D,MAAM,EAAEqD,OAAO,CAACrD,MAAM;MACtBP,WAAW,EAAE4D,OAAO,CAAC5D;IACvB,CAAC,CAAC;IACF,MAAM6E,aAAa,GAAGnB,KAAK,IAAID,sBAAsB,CAAChE,KAAK,EAAEiE,KAAK,EAAEC,iBAAiB,EAAEnB,eAAe,EAAEoB,OAAO,CAAC;IAChH,MAAMkB,IAAI,GAAGlB,OAAO,CAAC7D,IAAI,KAAK,SAAS,IAAIuE,eAAe,EAAES,WAAW,GAAGT,eAAe,CAACS,WAAW,GAAG,CAAC;IACzG,MAAMC,mBAAmB,GAAGC,QAAQ,CAAC3C,qBAAqB,CAACsB,OAAO,CAACF,KAAK,EAAElB,eAAe,CAAC,EAAE,EAAE,CAAC;IAC/F,IAAI0C,qBAAqB,GAAGF,mBAAmB,GAAGT,KAAK,GAAGO,IAAI;IAC9D,IAAIJ,iBAAiB,EAAE;MACrB,IAAId,OAAO,CAAC7D,IAAI,KAAK,MAAM,IAAI,CAAC0E,KAAK,IAAI,CAACD,OAAO,EAAE;QACjD,OAAO/E,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACiB,IAAI,CAACR,SAAS,EAAEqB,QAAQ,CAAC,EAAEqC,OAAO,CAACrD,MAAM,CAAC;MAC9E;MACA,IAAIgE,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxBU,qBAAqB,GAAGvB,iBAAiB,CAACwB,OAAO;MACnD,CAAC,MAAM;QACLD,qBAAqB,GAAGvB,iBAAiB,CAACyB,OAAO;MACnD;IACF;IACA,IAAIF,qBAAqB,GAAGJ,IAAI,KAAK,CAAC,EAAE;MACtC,IAAIP,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxBU,qBAAqB,IAAIJ,IAAI,GAAG,CAACA,IAAI,GAAGI,qBAAqB,IAAIJ,IAAI,CAAC,CAAC;MACzE;MACA,IAAIP,KAAK,GAAG,CAAC,IAAIE,KAAK,EAAE;QACtBS,qBAAqB,IAAIA,qBAAqB,GAAGJ,IAAI;MACvD;IACF;IACA,IAAII,qBAAqB,GAAGvB,iBAAiB,CAACyB,OAAO,EAAE;MACrD,OAAOP,aAAa,CAAClB,iBAAiB,CAACwB,OAAO,GAAG,CAACD,qBAAqB,GAAGvB,iBAAiB,CAACyB,OAAO,GAAG,CAAC,KAAKzB,iBAAiB,CAACyB,OAAO,GAAGzB,iBAAiB,CAACwB,OAAO,GAAG,CAAC,CAAC,CAAC;IACzK;IACA,IAAID,qBAAqB,GAAGvB,iBAAiB,CAACwB,OAAO,EAAE;MACrD,OAAON,aAAa,CAAClB,iBAAiB,CAACyB,OAAO,GAAG,CAACzB,iBAAiB,CAACwB,OAAO,GAAGD,qBAAqB,GAAG,CAAC,KAAKvB,iBAAiB,CAACyB,OAAO,GAAGzB,iBAAiB,CAACwB,OAAO,GAAG,CAAC,CAAC,CAAC;IACzK;IACA,OAAON,aAAa,CAACK,qBAAqB,CAAC;EAC7C,CAAC;EACD,MAAMG,mBAAmB,GAAGA,CAAA,KAAM;IAChC,MAAMC,OAAO,GAAGhE,uBAAuB,CAAC7B,KAAK,EAAE8B,QAAQ,EAAEqC,OAAO,CAAC7D,IAAI,EAAE6D,OAAO,CAACrD,MAAM,CAAC;IACtF,IAAI+E,OAAO,CAACnD,MAAM,KAAK,CAAC,EAAE;MACxB,OAAOyB,OAAO,CAACF,KAAK;IACtB;IACA,IAAIgB,iBAAiB,EAAE;MACrB,IAAIH,KAAK,GAAG,CAAC,IAAIC,OAAO,EAAE;QACxB,OAAOc,OAAO,CAAC,CAAC,CAAC;MACnB;MACA,OAAOA,OAAO,CAACA,OAAO,CAACnD,MAAM,GAAG,CAAC,CAAC;IACpC;IACA,MAAMoD,kBAAkB,GAAGD,OAAO,CAACzC,OAAO,CAACe,OAAO,CAACF,KAAK,CAAC;IACzD,MAAM8B,cAAc,GAAG,CAACD,kBAAkB,GAAGhB,KAAK,IAAIe,OAAO,CAACnD,MAAM;IACpE,MAAMsD,YAAY,GAAG,CAACD,cAAc,GAAGF,OAAO,CAACnD,MAAM,IAAImD,OAAO,CAACnD,MAAM;IACvE,OAAOmD,OAAO,CAACG,YAAY,CAAC;EAC9B,CAAC;EACD,IAAI7B,OAAO,CAAC5D,WAAW,KAAK,OAAO,IAAI4D,OAAO,CAAC5D,WAAW,KAAK,mBAAmB,EAAE;IAClF,OAAO2E,kBAAkB,CAAC,CAAC;EAC7B;EACA,OAAOU,mBAAmB,CAAC,CAAC;AAC9B,CAAC;AACD,OAAO,MAAMK,sBAAsB,GAAGA,CAAC9B,OAAO,EAAE+B,MAAM,EAAEnD,eAAe,KAAK;EAC1E,IAAIkB,KAAK,GAAGE,OAAO,CAACF,KAAK,IAAIE,OAAO,CAACgC,WAAW;EAChD,MAAMC,eAAe,GAAGF,MAAM,KAAK,WAAW,GAAG/B,OAAO,CAACkC,uBAAuB,GAAGlC,OAAO,CAACM,sBAAsB;EACjH,IAAIyB,MAAM,KAAK,WAAW,IAAI/B,OAAO,CAACM,sBAAsB,IAAI,CAACN,OAAO,CAACkC,uBAAuB,EAAE;IAChGpC,KAAK,GAAGR,MAAM,CAACZ,qBAAqB,CAACoB,KAAK,EAAElB,eAAe,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC;EAC1E;;EAEA;EACA;EACA;EACA;EACA;EACA,MAAMiD,uBAAuB,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC,CAACC,QAAQ,CAACL,MAAM,CAAC,IAAI/B,OAAO,CAAC5D,WAAW,KAAK,OAAO,IAAI,CAAC6F,eAAe,IAAInC,KAAK,CAACvB,MAAM,KAAK,CAAC;EACxJ,IAAI4D,uBAAuB,EAAE;IAC3BrC,KAAK,GAAG,GAAGA,KAAK,QAAQ;EAC1B;EACA,IAAIiC,MAAM,KAAK,WAAW,EAAE;IAC1BjC,KAAK,GAAG,SAASA,KAAK,QAAQ;EAChC;EACA,OAAOA,KAAK;AACd,CAAC;AACD,OAAO,MAAMuC,wBAAwB,GAAGA,CAACxG,KAAK,EAAE8C,QAAQ,EAAE2D,aAAa,EAAEC,SAAS,KAAK;EACrF,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIvE,mCAAmC,CAACC,KAAK,EAAEyG,aAAa,CAAC,CAACnG,IAAI,KAAK,SAAS,EAAE;MAChF,MAAM,IAAIF,KAAK,CAAC,2DAA2D,CAAC;IAC9E;EACF;EACA,OAAOJ,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAAC2G,KAAK,CAAC7D,QAAQ,EAAE2D,aAAa,CAAC,EAAEC,SAAS,CAAC;AAC9E,CAAC;AACD,MAAME,qBAAqB,GAAGA,CAAC5G,KAAK,EAAEc,MAAM,KAAKd,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACiB,IAAI,CAACR,SAAS,EAAE,QAAQ,CAAC,EAAEK,MAAM,CAAC,CAAC4B,MAAM,KAAK,CAAC;AAC3H,OAAO,MAAMmE,iCAAiC,GAAGA,CAAC7G,KAAK,EAAEO,WAAW,EAAEG,WAAW,EAAEI,MAAM,KAAK;EAC5F,IAAIP,WAAW,KAAK,OAAO,EAAE;IAC3B,OAAO,KAAK;EACd;EACA,MAAMS,GAAG,GAAGhB,KAAK,CAACiB,IAAI,CAACR,SAAS,EAAE,SAAS,CAAC;EAC5C,QAAQC,WAAW;IACjB;IACA,KAAK,MAAM;MACT;QACE,IAAIkG,qBAAqB,CAAC5G,KAAK,EAAEc,MAAM,CAAC,EAAE;UACxC,MAAMgG,aAAa,GAAG9G,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAAC+G,OAAO,CAAC/F,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC;UACzE,OAAOgG,aAAa,KAAK,MAAM;QACjC;QACA,MAAME,aAAa,GAAGhH,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAAC+G,OAAO,CAAC/F,GAAG,EAAE,IAAI,CAAC,EAAEF,MAAM,CAAC;QAC5E,OAAOkG,aAAa,KAAK,IAAI;MAC/B;IACF,KAAK,OAAO;MACV;QACE,OAAOhH,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACiH,WAAW,CAACjG,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,KAAK;MACR;QACE,OAAO1C,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACkH,YAAY,CAAClG,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MACzE;IACF,KAAK,SAAS;MACZ;QACE,OAAO1C,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACmB,WAAW,CAACH,GAAG,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,OAAO;MACV;QACE,OAAO1C,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACmH,QAAQ,CAACnG,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MACxE;IACF,KAAK,SAAS;MACZ;QACE,OAAO1C,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACoH,UAAU,CAACpG,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MAC1E;IACF,KAAK,SAAS;MACZ;QACE,OAAO1C,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACuC,UAAU,CAACvB,GAAG,EAAE,CAAC,CAAC,EAAEF,MAAM,CAAC,CAAC4B,MAAM,GAAG,CAAC;MAC1E;IACF;MACE;QACE,MAAM,IAAItC,KAAK,CAAC,sBAAsB,CAAC;MACzC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMiH,uBAAuB,GAAGA,CAACrH,KAAK,EAAEsH,QAAQ,EAAEvE,eAAe,KAAK;EAC3E;EACA;EACA;EACA,MAAMwE,kBAAkB,GAAGD,QAAQ,CAACE,IAAI,CAACrD,OAAO,IAAIA,OAAO,CAAC7D,IAAI,KAAK,KAAK,CAAC;EAC3E,MAAMmH,cAAc,GAAG,EAAE;EACzB,MAAMC,aAAa,GAAG,EAAE;EACxB,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoE,QAAQ,CAAC5E,MAAM,EAAEQ,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAMiB,OAAO,GAAGmD,QAAQ,CAACpE,CAAC,CAAC;IAC3B,MAAMyE,UAAU,GAAGJ,kBAAkB,IAAIpD,OAAO,CAAC7D,IAAI,KAAK,SAAS;IACnE,IAAI,CAACqH,UAAU,EAAE;MACfF,cAAc,CAACjG,IAAI,CAAC2C,OAAO,CAACrD,MAAM,CAAC;MACnC4G,aAAa,CAAClG,IAAI,CAACyE,sBAAsB,CAAC9B,OAAO,EAAE,WAAW,EAAEpB,eAAe,CAAC,CAAC;IACnF;EACF;EACA,MAAM6E,sBAAsB,GAAGH,cAAc,CAACpH,IAAI,CAAC,GAAG,CAAC;EACvD,MAAMwH,uBAAuB,GAAGH,aAAa,CAACrH,IAAI,CAAC,GAAG,CAAC;EACvD,OAAOL,KAAK,CAAC2G,KAAK,CAACkB,uBAAuB,EAAED,sBAAsB,CAAC;AACrE,CAAC;AACD,OAAO,MAAME,yCAAyC,GAAGR,QAAQ,IAAIA,QAAQ,CAAC5F,GAAG,CAACyC,OAAO,IAAI;EAC3F,OAAO,GAAGA,OAAO,CAAC4D,cAAc,GAAG5D,OAAO,CAACF,KAAK,IAAIE,OAAO,CAACgC,WAAW,GAAGhC,OAAO,CAAC6D,YAAY,EAAE;AAClG,CAAC,CAAC,CAAC3H,IAAI,CAAC,EAAE,CAAC;AACX,OAAO,MAAM4H,mCAAmC,GAAGA,CAACX,QAAQ,EAAEvE,eAAe,EAAEmF,KAAK,KAAK;EACvF,MAAMC,iBAAiB,GAAGb,QAAQ,CAAC5F,GAAG,CAACyC,OAAO,IAAI;IAChD,MAAMiE,SAAS,GAAGnC,sBAAsB,CAAC9B,OAAO,EAAE+D,KAAK,GAAG,WAAW,GAAG,WAAW,EAAEnF,eAAe,CAAC;IACrG,OAAO,GAAGoB,OAAO,CAAC4D,cAAc,GAAGK,SAAS,GAAGjE,OAAO,CAAC6D,YAAY,EAAE;EACvE,CAAC,CAAC;EACF,MAAMK,OAAO,GAAGF,iBAAiB,CAAC9H,IAAI,CAAC,EAAE,CAAC;EAC1C,IAAI,CAAC6H,KAAK,EAAE;IACV,OAAOG,OAAO;EAChB;;EAEA;EACA;EACA;EACA;EACA;EACA,OAAO,SAASA,OAAO,QAAQ;AACjC,CAAC;AACD,OAAO,MAAMC,qBAAqB,GAAGA,CAACtI,KAAK,EAAE+C,eAAe,EAAEjB,QAAQ,KAAK;EACzE,MAAMO,KAAK,GAAGrC,KAAK,CAACiB,IAAI,CAACR,SAAS,EAAEqB,QAAQ,CAAC;EAC7C,MAAMyG,SAAS,GAAGvI,KAAK,CAACuI,SAAS,CAAClG,KAAK,CAAC;EACxC,MAAMJ,QAAQ,GAAGjC,KAAK,CAACiC,QAAQ,CAACI,KAAK,CAAC;EACtC,MAAM;IACJmG,cAAc;IACdhE;EACF,CAAC,GAAG1E,eAAe,CAACE,KAAK,EAAEqC,KAAK,CAAC,CAACoG,MAAM,CAAC,CAACC,GAAG,EAAE3G,KAAK,KAAK;IACvD,MAAM4G,WAAW,GAAG3I,KAAK,CAAC4I,cAAc,CAAC7G,KAAK,CAAC;IAC/C,IAAI4G,WAAW,GAAGD,GAAG,CAACF,cAAc,EAAE;MACpC,OAAO;QACLA,cAAc,EAAEG,WAAW;QAC3BnE,YAAY,EAAEzC;MAChB,CAAC;IACH;IACA,OAAO2G,GAAG;EACZ,CAAC,EAAE;IACDF,cAAc,EAAE,CAAC;IACjBhE,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,OAAO;IACLqE,IAAI,EAAEA,CAAC;MACL/H;IACF,CAAC,MAAM;MACL4E,OAAO,EAAE,CAAC;MACVC,OAAO,EAAEiB,qBAAqB,CAAC5G,KAAK,EAAEc,MAAM,CAAC,GAAG,IAAI,GAAG;IACzD,CAAC,CAAC;IACFiB,KAAK,EAAEA,CAAA,MAAO;MACZ2D,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAE3F,KAAK,CAAC8I,QAAQ,CAACP,SAAS,CAAC,GAAG;IACvC,CAAC,CAAC;IACFQ,GAAG,EAAEA,CAAC;MACJ5D;IACF,CAAC,MAAM;MACLO,OAAO,EAAE,CAAC;MACVC,OAAO,EAAER,WAAW,IAAI,IAAI,IAAInF,KAAK,CAACgJ,OAAO,CAAC7D,WAAW,CAAC,GAAGnF,KAAK,CAAC4I,cAAc,CAACzD,WAAW,CAAC,GAAGqD,cAAc;MAC/GhE,YAAY,EAAEA;IAChB,CAAC,CAAC;IACF7C,OAAO,EAAEA,CAAC;MACRb,MAAM;MACNP;IACF,CAAC,KAAK;MACJ,IAAIA,WAAW,KAAK,OAAO,EAAE;QAC3B,MAAM0I,UAAU,GAAGpI,gBAAgB,CAACb,KAAK,EAAEc,MAAM,CAAC,CAACY,GAAG,CAAC+B,MAAM,CAAC;QAC9D,OAAO;UACLiC,OAAO,EAAEwD,IAAI,CAACC,GAAG,CAAC,GAAGF,UAAU,CAAC;UAChCtD,OAAO,EAAEuD,IAAI,CAACE,GAAG,CAAC,GAAGH,UAAU;QACjC,CAAC;MACH;MACA,OAAO;QACLvD,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH,CAAC;IACD0D,KAAK,EAAEA,CAAC;MACNvI;IACF,CAAC,KAAK;MACJ,MAAMwI,aAAa,GAAGtJ,KAAK,CAACuJ,QAAQ,CAACtH,QAAQ,CAAC;MAC9C,MAAMuH,WAAW,GAAG3G,qBAAqB,CAAC7C,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACiC,QAAQ,CAACI,KAAK,CAAC,EAAEvB,MAAM,CAAC,EAAEiC,eAAe,CAAC,KAAKuG,aAAa,CAACjG,QAAQ,CAAC,CAAC;MAC5I,IAAImG,WAAW,EAAE;QACf,OAAO;UACL9D,OAAO,EAAE,CAAC;UACVC,OAAO,EAAElC,MAAM,CAACZ,qBAAqB,CAAC7C,KAAK,CAAC4B,cAAc,CAAC5B,KAAK,CAACgC,UAAU,CAACK,KAAK,CAAC,EAAEvB,MAAM,CAAC,EAAEiC,eAAe,CAAC;QAC/G,CAAC;MACH;MACA,OAAO;QACL2C,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE2D;MACX,CAAC;IACH,CAAC;IACDG,OAAO,EAAEA,CAAA,MAAO;MACd/D,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAE3F,KAAK,CAAC0J,UAAU,CAACzH,QAAQ;IACpC,CAAC,CAAC;IACF0H,OAAO,EAAEA,CAAA,MAAO;MACdjE,OAAO,EAAE,CAAC;MACV;MACAC,OAAO,EAAE3F,KAAK,CAAC4J,UAAU,CAAC3H,QAAQ;IACpC,CAAC,CAAC;IACF4H,QAAQ,EAAEA,CAAA,MAAO;MACfnE,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC,CAAC;IACFmE,KAAK,EAAEA,CAAA,MAAO;MACZpE,OAAO,EAAE,CAAC;MACVC,OAAO,EAAE;IACX,CAAC;EACH,CAAC;AACH,CAAC;AACD,IAAIoE,wBAAwB,GAAG,KAAK;AACpC,OAAO,MAAMC,gBAAgB,GAAGA,CAAC1C,QAAQ,EAAE2C,SAAS,KAAK;EACvD,IAAI7F,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACyF,wBAAwB,EAAE;MAC7B,MAAMG,iBAAiB,GAAG,CAAC,OAAO,CAAC;MACnC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC3D,QAAQ,CAAC0D,SAAS,CAAC,EAAE;QAC7CC,iBAAiB,CAAC1I,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC;MAC3D;MACA,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC+E,QAAQ,CAAC0D,SAAS,CAAC,EAAE;QAC7CC,iBAAiB,CAAC1I,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;MACnE;MACA,MAAM2I,cAAc,GAAG7C,QAAQ,CAAC8C,IAAI,CAACjG,OAAO,IAAI,CAAC+F,iBAAiB,CAAC3D,QAAQ,CAACpC,OAAO,CAAC7D,IAAI,CAAC,CAAC;MAC1F,IAAI6J,cAAc,EAAE;QAClBE,OAAO,CAACC,IAAI,CAAC,wEAAwEH,cAAc,CAAC7J,IAAI,iBAAiB,EAAE,qCAAqC4J,iBAAiB,CAAC7J,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACtM0J,wBAAwB,GAAG,IAAI;MACjC;IACF;EACF;AACF,CAAC;AACD,MAAMQ,wBAAwB,GAAGA,CAACvK,KAAK,EAAEmE,OAAO,EAAEqG,kBAAkB,EAAEC,gBAAgB,KAAK;EACzF,QAAQtG,OAAO,CAAC7D,IAAI;IAClB,KAAK,MAAM;MACT;QACE,OAAON,KAAK,CAAC+G,OAAO,CAAC0D,gBAAgB,EAAEzK,KAAK,CAAC0K,OAAO,CAACF,kBAAkB,CAAC,CAAC;MAC3E;IACF,KAAK,OAAO;MACV;QACE,OAAOxK,KAAK,CAAC2K,QAAQ,CAACF,gBAAgB,EAAEzK,KAAK,CAAC8I,QAAQ,CAAC0B,kBAAkB,CAAC,CAAC;MAC7E;IACF,KAAK,SAAS;MACZ;QACE,MAAMI,mBAAmB,GAAG/J,gBAAgB,CAACb,KAAK,EAAEmE,OAAO,CAACrD,MAAM,CAAC;QACnE,MAAM+J,wBAAwB,GAAG7K,KAAK,CAAC4B,cAAc,CAAC4I,kBAAkB,EAAErG,OAAO,CAACrD,MAAM,CAAC;QACzF,MAAMgK,qBAAqB,GAAGF,mBAAmB,CAACxH,OAAO,CAACyH,wBAAwB,CAAC;QACnF,MAAME,0BAA0B,GAAGH,mBAAmB,CAACxH,OAAO,CAACe,OAAO,CAACF,KAAK,CAAC;QAC7E,MAAM+G,IAAI,GAAGD,0BAA0B,GAAGD,qBAAqB;QAC/D,OAAO9K,KAAK,CAACyB,OAAO,CAAC+I,kBAAkB,EAAEQ,IAAI,CAAC;MAChD;IACF,KAAK,KAAK;MACR;QACE,OAAOhL,KAAK,CAACuE,OAAO,CAACkG,gBAAgB,EAAEzK,KAAK,CAACiL,OAAO,CAACT,kBAAkB,CAAC,CAAC;MAC3E;IACF,KAAK,UAAU;MACb;QACE,MAAMU,IAAI,GAAGlL,KAAK,CAACuJ,QAAQ,CAACiB,kBAAkB,CAAC,GAAG,EAAE;QACpD,MAAMW,eAAe,GAAGnL,KAAK,CAACuJ,QAAQ,CAACkB,gBAAgB,CAAC;QACxD,IAAIS,IAAI,IAAIC,eAAe,IAAI,EAAE,EAAE;UACjC,OAAOnL,KAAK,CAACoL,QAAQ,CAACX,gBAAgB,EAAE,CAAC,EAAE,CAAC;QAC9C;QACA,IAAI,CAACS,IAAI,IAAIC,eAAe,GAAG,EAAE,EAAE;UACjC,OAAOnL,KAAK,CAACoL,QAAQ,CAACX,gBAAgB,EAAE,EAAE,CAAC;QAC7C;QACA,OAAOA,gBAAgB;MACzB;IACF,KAAK,OAAO;MACV;QACE,OAAOzK,KAAK,CAACmH,QAAQ,CAACsD,gBAAgB,EAAEzK,KAAK,CAACuJ,QAAQ,CAACiB,kBAAkB,CAAC,CAAC;MAC7E;IACF,KAAK,SAAS;MACZ;QACE,OAAOxK,KAAK,CAACoH,UAAU,CAACqD,gBAAgB,EAAEzK,KAAK,CAAC0J,UAAU,CAACc,kBAAkB,CAAC,CAAC;MACjF;IACF,KAAK,SAAS;MACZ;QACE,OAAOxK,KAAK,CAACuC,UAAU,CAACkI,gBAAgB,EAAEzK,KAAK,CAAC4J,UAAU,CAACY,kBAAkB,CAAC,CAAC;MACjF;IACF;MACE;QACE,OAAOC,gBAAgB;MACzB;EACJ;AACF,CAAC;AACD,MAAMY,gCAAgC,GAAG;EACvCxC,IAAI,EAAE,CAAC;EACP9G,KAAK,EAAE,CAAC;EACRgH,GAAG,EAAE,CAAC;EACNpH,OAAO,EAAE,CAAC;EACV0H,KAAK,EAAE,CAAC;EACRI,OAAO,EAAE,CAAC;EACVE,OAAO,EAAE,CAAC;EACVE,QAAQ,EAAE,CAAC;EACXC,KAAK,EAAE;AACT,CAAC;AACD,OAAO,MAAMwB,0BAA0B,GAAGA,CAACtL,KAAK,EAAEwK,kBAAkB,EAAElD,QAAQ,EAAEiE,aAAa,EAAEC,2BAA2B;AAC1H;AACA,CAAC,GAAGlE,QAAQ,CAAC,CAACmE,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKN,gCAAgC,CAACK,CAAC,CAACpL,IAAI,CAAC,GAAG+K,gCAAgC,CAACM,CAAC,CAACrL,IAAI,CAAC,CAAC,CAACmI,MAAM,CAAC,CAACmD,UAAU,EAAEzH,OAAO,KAAK;EAChJ,IAAI,CAACqH,2BAA2B,IAAIrH,OAAO,CAAC0H,QAAQ,EAAE;IACpD,OAAOtB,wBAAwB,CAACvK,KAAK,EAAEmE,OAAO,EAAEqG,kBAAkB,EAAEoB,UAAU,CAAC;EACjF;EACA,OAAOA,UAAU;AACnB,CAAC,EAAEL,aAAa,CAAC;AACjB,OAAO,MAAMO,SAAS,GAAGA,CAAA,KAAMC,SAAS,CAACC,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC1F,QAAQ,CAAC,SAAS,CAAC;;AAEpF;AACA,OAAO,MAAM2F,eAAe,GAAGA,CAAC5E,QAAQ,EAAE6E,cAAc,KAAK;EAC3D,MAAMC,SAAS,GAAG,CAAC,CAAC;EACpB,IAAI,CAACD,cAAc,EAAE;IACnB7E,QAAQ,CAAC+E,OAAO,CAAC,CAAC1J,CAAC,EAAEC,KAAK,KAAK;MAC7B,MAAM0J,SAAS,GAAG1J,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,KAAK,GAAG,CAAC;MAChD,MAAM2J,UAAU,GAAG3J,KAAK,KAAK0E,QAAQ,CAAC5E,MAAM,GAAG,CAAC,GAAG,IAAI,GAAGE,KAAK,GAAG,CAAC;MACnEwJ,SAAS,CAACxJ,KAAK,CAAC,GAAG;QACjB0J,SAAS;QACTC;MACF,CAAC;IACH,CAAC,CAAC;IACF,OAAO;MACLH,SAAS;MACTI,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAEnF,QAAQ,CAAC5E,MAAM,GAAG;IAC9B,CAAC;EACH;EACA,MAAMgK,OAAO,GAAG,CAAC,CAAC;EAClB,MAAMC,OAAO,GAAG,CAAC,CAAC;EAClB,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,IAAIC,QAAQ,GAAGxF,QAAQ,CAAC5E,MAAM,GAAG,CAAC;EAClC,OAAOoK,QAAQ,IAAI,CAAC,EAAE;IACpBD,kBAAkB,GAAGvF,QAAQ,CAACyF,SAAS;IACvC;IACA,CAAC5I,OAAO,EAAEvB,KAAK,KAAKA,KAAK,IAAIgK,oBAAoB,IAAIzI,OAAO,CAAC6D,YAAY,EAAEzB,QAAQ,CAAC,GAAG,CAAC;IACxF;IACApC,OAAO,CAAC6D,YAAY,KAAK,KAAK,CAAC;IAC/B,IAAI6E,kBAAkB,KAAK,CAAC,CAAC,EAAE;MAC7BA,kBAAkB,GAAGvF,QAAQ,CAAC5E,MAAM,GAAG,CAAC;IAC1C;IACA,KAAK,IAAIQ,CAAC,GAAG2J,kBAAkB,EAAE3J,CAAC,IAAI0J,oBAAoB,EAAE1J,CAAC,IAAI,CAAC,EAAE;MAClEyJ,OAAO,CAACzJ,CAAC,CAAC,GAAG4J,QAAQ;MACrBJ,OAAO,CAACI,QAAQ,CAAC,GAAG5J,CAAC;MACrB4J,QAAQ,IAAI,CAAC;IACf;IACAF,oBAAoB,GAAGC,kBAAkB,GAAG,CAAC;EAC/C;EACAvF,QAAQ,CAAC+E,OAAO,CAAC,CAAC1J,CAAC,EAAEC,KAAK,KAAK;IAC7B,MAAMoK,QAAQ,GAAGL,OAAO,CAAC/J,KAAK,CAAC;IAC/B,MAAM0J,SAAS,GAAGU,QAAQ,KAAK,CAAC,GAAG,IAAI,GAAGN,OAAO,CAACM,QAAQ,GAAG,CAAC,CAAC;IAC/D,MAAMT,UAAU,GAAGS,QAAQ,KAAK1F,QAAQ,CAAC5E,MAAM,GAAG,CAAC,GAAG,IAAI,GAAGgK,OAAO,CAACM,QAAQ,GAAG,CAAC,CAAC;IAClFZ,SAAS,CAACxJ,KAAK,CAAC,GAAG;MACjB0J,SAAS;MACTC;IACF,CAAC;EACH,CAAC,CAAC;EACF,OAAO;IACLH,SAAS;IACTI,UAAU,EAAEE,OAAO,CAAC,CAAC,CAAC;IACtBD,QAAQ,EAAEC,OAAO,CAACpF,QAAQ,CAAC5E,MAAM,GAAG,CAAC;EACvC,CAAC;AACH,CAAC;AACD,OAAO,MAAMuK,qBAAqB,GAAGA,CAACC,gBAAgB,EAAE5F,QAAQ,KAAK;EACnE,IAAI4F,gBAAgB,IAAI,IAAI,EAAE;IAC5B,OAAO,IAAI;EACb;EACA,IAAIA,gBAAgB,KAAK,KAAK,EAAE;IAC9B,OAAO,KAAK;EACd;EACA,IAAI,OAAOA,gBAAgB,KAAK,QAAQ,EAAE;IACxC,MAAMtK,KAAK,GAAG0E,QAAQ,CAACyF,SAAS,CAAC5I,OAAO,IAAIA,OAAO,CAAC7D,IAAI,KAAK4M,gBAAgB,CAAC;IAC9E,OAAOtK,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,GAAGA,KAAK;EACpC;EACA,OAAOsK,gBAAgB;AACzB,CAAC;AACD,OAAO,MAAMC,mBAAmB,GAAGA,CAAChJ,OAAO,EAAEnE,KAAK,KAAK;EACrD,IAAI,CAACmE,OAAO,CAACF,KAAK,EAAE;IAClB,OAAOxD,SAAS;EAClB;EACA,QAAQ0D,OAAO,CAAC7D,IAAI;IAClB,KAAK,OAAO;MACV;QACE,IAAI6D,OAAO,CAAC5D,WAAW,KAAK,OAAO,EAAE;UACnC,OAAOP,KAAK,CAACc,MAAM,CAACd,KAAK,CAAC2K,QAAQ,CAAC3K,KAAK,CAACiB,IAAI,CAAC,CAAC,EAAEwC,MAAM,CAACU,OAAO,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;QACvF;QACA,MAAMmJ,UAAU,GAAGpN,KAAK,CAAC2G,KAAK,CAACxC,OAAO,CAACF,KAAK,EAAEE,OAAO,CAACrD,MAAM,CAAC;QAC7D,OAAOsM,UAAU,GAAGpN,KAAK,CAACc,MAAM,CAACsM,UAAU,EAAE,OAAO,CAAC,GAAG3M,SAAS;MACnE;IACF,KAAK,KAAK;MACR,OAAO0D,OAAO,CAAC5D,WAAW,KAAK,OAAO,GAAGP,KAAK,CAACc,MAAM,CAACd,KAAK,CAACuE,OAAO,CAACvE,KAAK,CAACiH,WAAW,CAACjH,KAAK,CAACiB,IAAI,CAAC,CAAC,CAAC,EAAEwC,MAAM,CAACU,OAAO,CAACF,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,GAAGE,OAAO,CAACF,KAAK;IAChK,KAAK,SAAS;MACZ;MACA,OAAOxD,SAAS;IAClB;MACE,OAAOA,SAAS;EACpB;AACF,CAAC;AACD,OAAO,MAAM4M,kBAAkB,GAAGA,CAAClJ,OAAO,EAAEnE,KAAK,KAAK;EACpD,IAAI,CAACmE,OAAO,CAACF,KAAK,EAAE;IAClB,OAAOxD,SAAS;EAClB;EACA,QAAQ0D,OAAO,CAAC7D,IAAI;IAClB,KAAK,SAAS;MACZ;QACE,IAAI6D,OAAO,CAAC5D,WAAW,KAAK,QAAQ,EAAE;UACpC;UACA,OAAOE,SAAS;QAClB;QACA,OAAOgD,MAAM,CAACU,OAAO,CAACF,KAAK,CAAC;MAC9B;IACF,KAAK,UAAU;MACb;QACE,MAAMmJ,UAAU,GAAGpN,KAAK,CAAC2G,KAAK,CAAC,SAASxC,OAAO,CAACF,KAAK,EAAE,EAAE,GAAGjE,KAAK,CAACsN,OAAO,CAACC,QAAQ,IAAIvN,KAAK,CAACsN,OAAO,CAAC7D,OAAO,IAAItF,OAAO,CAACrD,MAAM,EAAE,CAAC;QAChI,IAAIsM,UAAU,EAAE;UACd,OAAOpN,KAAK,CAACuJ,QAAQ,CAAC6D,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC;QACjD;QACA,OAAO3M,SAAS;MAClB;IACF,KAAK,KAAK;MACR,OAAO0D,OAAO,CAAC5D,WAAW,KAAK,mBAAmB,GAAGiF,QAAQ,CAACrB,OAAO,CAACF,KAAK,EAAE,EAAE,CAAC,GAAGR,MAAM,CAACU,OAAO,CAACF,KAAK,CAAC;IAC1G,KAAK,OAAO;MACV;QACE,IAAIE,OAAO,CAAC5D,WAAW,KAAK,OAAO,EAAE;UACnC,OAAOkD,MAAM,CAACU,OAAO,CAACF,KAAK,CAAC;QAC9B;QACA,MAAMmJ,UAAU,GAAGpN,KAAK,CAAC2G,KAAK,CAACxC,OAAO,CAACF,KAAK,EAAEE,OAAO,CAACrD,MAAM,CAAC;QAC7D,OAAOsM,UAAU,GAAGpN,KAAK,CAAC8I,QAAQ,CAACsE,UAAU,CAAC,GAAG,CAAC,GAAG3M,SAAS;MAChE;IACF;MACE,OAAO0D,OAAO,CAAC5D,WAAW,KAAK,QAAQ,GAAGkD,MAAM,CAACU,OAAO,CAACF,KAAK,CAAC,GAAGxD,SAAS;EAC/E;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}