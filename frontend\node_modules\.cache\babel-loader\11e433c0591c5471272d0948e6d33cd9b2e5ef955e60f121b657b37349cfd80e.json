{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { DATE_TIME_VALIDATION_PROP_NAMES, DATE_VALIDATION_PROP_NAMES, TIME_VALIDATION_PROP_NAMES } from \"../validation/extractValidationProps.js\";\nconst SHARED_FIELD_INTERNAL_PROP_NAMES = ['value', 'defaultValue', 'referenceDate', 'format', 'formatDensity', 'onChange', 'timezone', 'onError', 'shouldRespectLeadingZeros', 'selectedSections', 'onSelectedSectionsChange', 'unstableFieldRef', 'enableAccessibleFieldDOMStructure', 'disabled', 'readOnly', 'dateSeparator'];\n/**\n * Split the props received by the field component into:\n * - `internalProps` which are used by the various hooks called by the field component.\n * - `forwardedProps` which are passed to the underlying component.\n * Note that some forwarded props might be used by the hooks as well.\n * For instance, hooks like `useDateField` need props like `autoFocus` to know how to behave.\n * @template TProps, TValueType\n * @param {TProps} props The props received by the field component.\n * @param {TValueType} valueType The type of the field value ('date', 'time', or 'date-time').\n */\nexport const useSplitFieldProps = (props, valueType) => {\n  return React.useMemo(() => {\n    const forwardedProps = _extends({}, props);\n    const internalProps = {};\n    const extractProp = propName => {\n      if (forwardedProps.hasOwnProperty(propName)) {\n        // @ts-ignore\n        internalProps[propName] = forwardedProps[propName];\n        delete forwardedProps[propName];\n      }\n    };\n    SHARED_FIELD_INTERNAL_PROP_NAMES.forEach(extractProp);\n    if (valueType === 'date') {\n      DATE_VALIDATION_PROP_NAMES.forEach(extractProp);\n    } else if (valueType === 'time') {\n      TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n    } else if (valueType === 'date-time') {\n      DATE_VALIDATION_PROP_NAMES.forEach(extractProp);\n      TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n      DATE_TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n    }\n    return {\n      forwardedProps,\n      internalProps\n    };\n  }, [props, valueType]);\n};", "map": {"version": 3, "names": ["_extends", "React", "DATE_TIME_VALIDATION_PROP_NAMES", "DATE_VALIDATION_PROP_NAMES", "TIME_VALIDATION_PROP_NAMES", "SHARED_FIELD_INTERNAL_PROP_NAMES", "useSplitFieldProps", "props", "valueType", "useMemo", "forwardedProps", "internalProps", "extractProp", "propName", "hasOwnProperty", "for<PERSON>ach"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/hooks/useSplitFieldProps.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { DATE_TIME_VALIDATION_PROP_NAMES, DATE_VALIDATION_PROP_NAMES, TIME_VALIDATION_PROP_NAMES } from \"../validation/extractValidationProps.js\";\nconst SHARED_FIELD_INTERNAL_PROP_NAMES = ['value', 'defaultValue', 'referenceDate', 'format', 'formatDensity', 'onChange', 'timezone', 'onError', 'shouldRespectLeadingZeros', 'selectedSections', 'onSelectedSectionsChange', 'unstableFieldRef', 'enableAccessibleFieldDOMStructure', 'disabled', 'readOnly', 'dateSeparator'];\n/**\n * Split the props received by the field component into:\n * - `internalProps` which are used by the various hooks called by the field component.\n * - `forwardedProps` which are passed to the underlying component.\n * Note that some forwarded props might be used by the hooks as well.\n * For instance, hooks like `useDateField` need props like `autoFocus` to know how to behave.\n * @template TProps, TValueType\n * @param {TProps} props The props received by the field component.\n * @param {TValueType} valueType The type of the field value ('date', 'time', or 'date-time').\n */\nexport const useSplitFieldProps = (props, valueType) => {\n  return React.useMemo(() => {\n    const forwardedProps = _extends({}, props);\n    const internalProps = {};\n    const extractProp = propName => {\n      if (forwardedProps.hasOwnProperty(propName)) {\n        // @ts-ignore\n        internalProps[propName] = forwardedProps[propName];\n        delete forwardedProps[propName];\n      }\n    };\n    SHARED_FIELD_INTERNAL_PROP_NAMES.forEach(extractProp);\n    if (valueType === 'date') {\n      DATE_VALIDATION_PROP_NAMES.forEach(extractProp);\n    } else if (valueType === 'time') {\n      TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n    } else if (valueType === 'date-time') {\n      DATE_VALIDATION_PROP_NAMES.forEach(extractProp);\n      TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n      DATE_TIME_VALIDATION_PROP_NAMES.forEach(extractProp);\n    }\n    return {\n      forwardedProps,\n      internalProps\n    };\n  }, [props, valueType]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,+BAA+B,EAAEC,0BAA0B,EAAEC,0BAA0B,QAAQ,yCAAyC;AACjJ,MAAMC,gCAAgC,GAAG,CAAC,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,2BAA2B,EAAE,kBAAkB,EAAE,0BAA0B,EAAE,kBAAkB,EAAE,mCAAmC,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC;AAChU;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;EACtD,OAAOP,KAAK,CAACQ,OAAO,CAAC,MAAM;IACzB,MAAMC,cAAc,GAAGV,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,CAAC;IAC1C,MAAMI,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,WAAW,GAAGC,QAAQ,IAAI;MAC9B,IAAIH,cAAc,CAACI,cAAc,CAACD,QAAQ,CAAC,EAAE;QAC3C;QACAF,aAAa,CAACE,QAAQ,CAAC,GAAGH,cAAc,CAACG,QAAQ,CAAC;QAClD,OAAOH,cAAc,CAACG,QAAQ,CAAC;MACjC;IACF,CAAC;IACDR,gCAAgC,CAACU,OAAO,CAACH,WAAW,CAAC;IACrD,IAAIJ,SAAS,KAAK,MAAM,EAAE;MACxBL,0BAA0B,CAACY,OAAO,CAACH,WAAW,CAAC;IACjD,CAAC,MAAM,IAAIJ,SAAS,KAAK,MAAM,EAAE;MAC/BJ,0BAA0B,CAACW,OAAO,CAACH,WAAW,CAAC;IACjD,CAAC,MAAM,IAAIJ,SAAS,KAAK,WAAW,EAAE;MACpCL,0BAA0B,CAACY,OAAO,CAACH,WAAW,CAAC;MAC/CR,0BAA0B,CAACW,OAAO,CAACH,WAAW,CAAC;MAC/CV,+BAA+B,CAACa,OAAO,CAACH,WAAW,CAAC;IACtD;IACA,OAAO;MACLF,cAAc;MACdC;IACF,CAAC;EACH,CAAC,EAAE,CAACJ,KAAK,EAAEC,SAAS,CAAC,CAAC;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}