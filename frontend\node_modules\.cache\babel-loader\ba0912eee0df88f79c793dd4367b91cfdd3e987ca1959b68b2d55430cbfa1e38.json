{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport DecodeHintType from '../DecodeHintType';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\n// import Reader from '../Reader';\nimport Result from '../Result';\n// import ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport OneDReader from './OneDReader';\n/**\n * <p>Decodes Code 128 barcodes.</p>\n *\n * <AUTHOR> Owen\n */\nvar Code128Reader = /** @class */function (_super) {\n  __extends(Code128Reader, _super);\n  function Code128Reader() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Code128Reader.findStartPattern = function (row) {\n    var width = row.getSize();\n    var rowOffset = row.getNextSet(0);\n    var counterPosition = 0;\n    var counters = Int32Array.from([0, 0, 0, 0, 0, 0]);\n    var patternStart = rowOffset;\n    var isWhite = false;\n    var patternLength = 6;\n    for (var i = rowOffset; i < width; i++) {\n      if (row.get(i) !== isWhite) {\n        counters[counterPosition]++;\n      } else {\n        if (counterPosition === patternLength - 1) {\n          var bestVariance = Code128Reader.MAX_AVG_VARIANCE;\n          var bestMatch = -1;\n          for (var startCode = Code128Reader.CODE_START_A; startCode <= Code128Reader.CODE_START_C; startCode++) {\n            var variance = OneDReader.patternMatchVariance(counters, Code128Reader.CODE_PATTERNS[startCode], Code128Reader.MAX_INDIVIDUAL_VARIANCE);\n            if (variance < bestVariance) {\n              bestVariance = variance;\n              bestMatch = startCode;\n            }\n          }\n          // Look for whitespace before start pattern, >= 50% of width of start pattern\n          if (bestMatch >= 0 && row.isRange(Math.max(0, patternStart - (i - patternStart) / 2), patternStart, false)) {\n            return Int32Array.from([patternStart, i, bestMatch]);\n          }\n          patternStart += counters[0] + counters[1];\n          counters = counters.slice(2, counters.length);\n          counters[counterPosition - 1] = 0;\n          counters[counterPosition] = 0;\n          counterPosition--;\n        } else {\n          counterPosition++;\n        }\n        counters[counterPosition] = 1;\n        isWhite = !isWhite;\n      }\n    }\n    throw new NotFoundException();\n  };\n  Code128Reader.decodeCode = function (row, counters, rowOffset) {\n    OneDReader.recordPattern(row, rowOffset, counters);\n    var bestVariance = Code128Reader.MAX_AVG_VARIANCE; // worst variance we'll accept\n    var bestMatch = -1;\n    for (var d = 0; d < Code128Reader.CODE_PATTERNS.length; d++) {\n      var pattern = Code128Reader.CODE_PATTERNS[d];\n      var variance = this.patternMatchVariance(counters, pattern, Code128Reader.MAX_INDIVIDUAL_VARIANCE);\n      if (variance < bestVariance) {\n        bestVariance = variance;\n        bestMatch = d;\n      }\n    }\n    // TODO We're overlooking the fact that the STOP pattern has 7 values, not 6.\n    if (bestMatch >= 0) {\n      return bestMatch;\n    } else {\n      throw new NotFoundException();\n    }\n  };\n  Code128Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n    var convertFNC1 = hints && hints.get(DecodeHintType.ASSUME_GS1) === true;\n    var startPatternInfo = Code128Reader.findStartPattern(row);\n    var startCode = startPatternInfo[2];\n    var currentRawCodesIndex = 0;\n    var rawCodes = new Uint8Array(20);\n    rawCodes[currentRawCodesIndex++] = startCode;\n    var codeSet;\n    switch (startCode) {\n      case Code128Reader.CODE_START_A:\n        codeSet = Code128Reader.CODE_CODE_A;\n        break;\n      case Code128Reader.CODE_START_B:\n        codeSet = Code128Reader.CODE_CODE_B;\n        break;\n      case Code128Reader.CODE_START_C:\n        codeSet = Code128Reader.CODE_CODE_C;\n        break;\n      default:\n        throw new FormatException();\n    }\n    var done = false;\n    var isNextShifted = false;\n    var result = '';\n    var lastStart = startPatternInfo[0];\n    var nextStart = startPatternInfo[1];\n    var counters = Int32Array.from([0, 0, 0, 0, 0, 0]);\n    var lastCode = 0;\n    var code = 0;\n    var checksumTotal = startCode;\n    var multiplier = 0;\n    var lastCharacterWasPrintable = true;\n    var upperMode = false;\n    var shiftUpperMode = false;\n    while (!done) {\n      var unshift = isNextShifted;\n      isNextShifted = false;\n      // Save off last code\n      lastCode = code;\n      // Decode another code from image\n      code = Code128Reader.decodeCode(row, counters, nextStart);\n      rawCodes[currentRawCodesIndex++] = code;\n      // Remember whether the last code was printable or not (excluding CODE_STOP)\n      if (code !== Code128Reader.CODE_STOP) {\n        lastCharacterWasPrintable = true;\n      }\n      // Add to checksum computation (if not CODE_STOP of course)\n      if (code !== Code128Reader.CODE_STOP) {\n        multiplier++;\n        checksumTotal += multiplier * code;\n      }\n      // Advance to where the next code will to start\n      lastStart = nextStart;\n      nextStart += counters.reduce(function (previous, current) {\n        return previous + current;\n      }, 0);\n      // Take care of illegal start codes\n      switch (code) {\n        case Code128Reader.CODE_START_A:\n        case Code128Reader.CODE_START_B:\n        case Code128Reader.CODE_START_C:\n          throw new FormatException();\n      }\n      switch (codeSet) {\n        case Code128Reader.CODE_CODE_A:\n          if (code < 64) {\n            if (shiftUpperMode === upperMode) {\n              result += String.fromCharCode(' '.charCodeAt(0) + code);\n            } else {\n              result += String.fromCharCode(' '.charCodeAt(0) + code + 128);\n            }\n            shiftUpperMode = false;\n          } else if (code < 96) {\n            if (shiftUpperMode === upperMode) {\n              result += String.fromCharCode(code - 64);\n            } else {\n              result += String.fromCharCode(code + 64);\n            }\n            shiftUpperMode = false;\n          } else {\n            // Don't let CODE_STOP, which always appears, affect whether whether we think the last\n            // code was printable or not.\n            if (code !== Code128Reader.CODE_STOP) {\n              lastCharacterWasPrintable = false;\n            }\n            switch (code) {\n              case Code128Reader.CODE_FNC_1:\n                if (convertFNC1) {\n                  if (result.length === 0) {\n                    // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                    // is FNC1 then this is GS1-128. We add the symbology identifier.\n                    result += ']C1';\n                  } else {\n                    // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                    result += String.fromCharCode(29);\n                  }\n                }\n                break;\n              case Code128Reader.CODE_FNC_2:\n              case Code128Reader.CODE_FNC_3:\n                // do nothing?\n                break;\n              case Code128Reader.CODE_FNC_4_A:\n                if (!upperMode && shiftUpperMode) {\n                  upperMode = true;\n                  shiftUpperMode = false;\n                } else if (upperMode && shiftUpperMode) {\n                  upperMode = false;\n                  shiftUpperMode = false;\n                } else {\n                  shiftUpperMode = true;\n                }\n                break;\n              case Code128Reader.CODE_SHIFT:\n                isNextShifted = true;\n                codeSet = Code128Reader.CODE_CODE_B;\n                break;\n              case Code128Reader.CODE_CODE_B:\n                codeSet = Code128Reader.CODE_CODE_B;\n                break;\n              case Code128Reader.CODE_CODE_C:\n                codeSet = Code128Reader.CODE_CODE_C;\n                break;\n              case Code128Reader.CODE_STOP:\n                done = true;\n                break;\n            }\n          }\n          break;\n        case Code128Reader.CODE_CODE_B:\n          if (code < 96) {\n            if (shiftUpperMode === upperMode) {\n              result += String.fromCharCode(' '.charCodeAt(0) + code);\n            } else {\n              result += String.fromCharCode(' '.charCodeAt(0) + code + 128);\n            }\n            shiftUpperMode = false;\n          } else {\n            if (code !== Code128Reader.CODE_STOP) {\n              lastCharacterWasPrintable = false;\n            }\n            switch (code) {\n              case Code128Reader.CODE_FNC_1:\n                if (convertFNC1) {\n                  if (result.length === 0) {\n                    // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                    // is FNC1 then this is GS1-128. We add the symbology identifier.\n                    result += ']C1';\n                  } else {\n                    // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                    result += String.fromCharCode(29);\n                  }\n                }\n                break;\n              case Code128Reader.CODE_FNC_2:\n              case Code128Reader.CODE_FNC_3:\n                // do nothing?\n                break;\n              case Code128Reader.CODE_FNC_4_B:\n                if (!upperMode && shiftUpperMode) {\n                  upperMode = true;\n                  shiftUpperMode = false;\n                } else if (upperMode && shiftUpperMode) {\n                  upperMode = false;\n                  shiftUpperMode = false;\n                } else {\n                  shiftUpperMode = true;\n                }\n                break;\n              case Code128Reader.CODE_SHIFT:\n                isNextShifted = true;\n                codeSet = Code128Reader.CODE_CODE_A;\n                break;\n              case Code128Reader.CODE_CODE_A:\n                codeSet = Code128Reader.CODE_CODE_A;\n                break;\n              case Code128Reader.CODE_CODE_C:\n                codeSet = Code128Reader.CODE_CODE_C;\n                break;\n              case Code128Reader.CODE_STOP:\n                done = true;\n                break;\n            }\n          }\n          break;\n        case Code128Reader.CODE_CODE_C:\n          if (code < 100) {\n            if (code < 10) {\n              result += '0';\n            }\n            result += code;\n          } else {\n            if (code !== Code128Reader.CODE_STOP) {\n              lastCharacterWasPrintable = false;\n            }\n            switch (code) {\n              case Code128Reader.CODE_FNC_1:\n                if (convertFNC1) {\n                  if (result.length === 0) {\n                    // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                    // is FNC1 then this is GS1-128. We add the symbology identifier.\n                    result += ']C1';\n                  } else {\n                    // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                    result += String.fromCharCode(29);\n                  }\n                }\n                break;\n              case Code128Reader.CODE_CODE_A:\n                codeSet = Code128Reader.CODE_CODE_A;\n                break;\n              case Code128Reader.CODE_CODE_B:\n                codeSet = Code128Reader.CODE_CODE_B;\n                break;\n              case Code128Reader.CODE_STOP:\n                done = true;\n                break;\n            }\n          }\n          break;\n      }\n      // Unshift back to another code set if we were shifted\n      if (unshift) {\n        codeSet = codeSet === Code128Reader.CODE_CODE_A ? Code128Reader.CODE_CODE_B : Code128Reader.CODE_CODE_A;\n      }\n    }\n    var lastPatternSize = nextStart - lastStart;\n    // Check for ample whitespace following pattern, but, to do this we first need to remember that\n    // we fudged decoding CODE_STOP since it actually has 7 bars, not 6. There is a black bar left\n    // to read off. Would be slightly better to properly read. Here we just skip it:\n    nextStart = row.getNextUnset(nextStart);\n    if (!row.isRange(nextStart, Math.min(row.getSize(), nextStart + (nextStart - lastStart) / 2), false)) {\n      throw new NotFoundException();\n    }\n    // Pull out from sum the value of the penultimate check code\n    checksumTotal -= multiplier * lastCode;\n    // lastCode is the checksum then:\n    if (checksumTotal % 103 !== lastCode) {\n      throw new ChecksumException();\n    }\n    // Need to pull out the check digits from string\n    var resultLength = result.length;\n    if (resultLength === 0) {\n      // false positive\n      throw new NotFoundException();\n    }\n    // Only bother if the result had at least one character, and if the checksum digit happened to\n    // be a printable character. If it was just interpreted as a control code, nothing to remove.\n    if (resultLength > 0 && lastCharacterWasPrintable) {\n      if (codeSet === Code128Reader.CODE_CODE_C) {\n        result = result.substring(0, resultLength - 2);\n      } else {\n        result = result.substring(0, resultLength - 1);\n      }\n    }\n    var left = (startPatternInfo[1] + startPatternInfo[0]) / 2.0;\n    var right = lastStart + lastPatternSize / 2.0;\n    var rawCodesSize = rawCodes.length;\n    var rawBytes = new Uint8Array(rawCodesSize);\n    for (var i = 0; i < rawCodesSize; i++) {\n      rawBytes[i] = rawCodes[i];\n    }\n    var points = [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)];\n    return new Result(result, rawBytes, 0, points, BarcodeFormat.CODE_128, new Date().getTime());\n  };\n  Code128Reader.CODE_PATTERNS = [Int32Array.from([2, 1, 2, 2, 2, 2]), Int32Array.from([2, 2, 2, 1, 2, 2]), Int32Array.from([2, 2, 2, 2, 2, 1]), Int32Array.from([1, 2, 1, 2, 2, 3]), Int32Array.from([1, 2, 1, 3, 2, 2]), Int32Array.from([1, 3, 1, 2, 2, 2]), Int32Array.from([1, 2, 2, 2, 1, 3]), Int32Array.from([1, 2, 2, 3, 1, 2]), Int32Array.from([1, 3, 2, 2, 1, 2]), Int32Array.from([2, 2, 1, 2, 1, 3]), Int32Array.from([2, 2, 1, 3, 1, 2]), Int32Array.from([2, 3, 1, 2, 1, 2]), Int32Array.from([1, 1, 2, 2, 3, 2]), Int32Array.from([1, 2, 2, 1, 3, 2]), Int32Array.from([1, 2, 2, 2, 3, 1]), Int32Array.from([1, 1, 3, 2, 2, 2]), Int32Array.from([1, 2, 3, 1, 2, 2]), Int32Array.from([1, 2, 3, 2, 2, 1]), Int32Array.from([2, 2, 3, 2, 1, 1]), Int32Array.from([2, 2, 1, 1, 3, 2]), Int32Array.from([2, 2, 1, 2, 3, 1]), Int32Array.from([2, 1, 3, 2, 1, 2]), Int32Array.from([2, 2, 3, 1, 1, 2]), Int32Array.from([3, 1, 2, 1, 3, 1]), Int32Array.from([3, 1, 1, 2, 2, 2]), Int32Array.from([3, 2, 1, 1, 2, 2]), Int32Array.from([3, 2, 1, 2, 2, 1]), Int32Array.from([3, 1, 2, 2, 1, 2]), Int32Array.from([3, 2, 2, 1, 1, 2]), Int32Array.from([3, 2, 2, 2, 1, 1]), Int32Array.from([2, 1, 2, 1, 2, 3]), Int32Array.from([2, 1, 2, 3, 2, 1]), Int32Array.from([2, 3, 2, 1, 2, 1]), Int32Array.from([1, 1, 1, 3, 2, 3]), Int32Array.from([1, 3, 1, 1, 2, 3]), Int32Array.from([1, 3, 1, 3, 2, 1]), Int32Array.from([1, 1, 2, 3, 1, 3]), Int32Array.from([1, 3, 2, 1, 1, 3]), Int32Array.from([1, 3, 2, 3, 1, 1]), Int32Array.from([2, 1, 1, 3, 1, 3]), Int32Array.from([2, 3, 1, 1, 1, 3]), Int32Array.from([2, 3, 1, 3, 1, 1]), Int32Array.from([1, 1, 2, 1, 3, 3]), Int32Array.from([1, 1, 2, 3, 3, 1]), Int32Array.from([1, 3, 2, 1, 3, 1]), Int32Array.from([1, 1, 3, 1, 2, 3]), Int32Array.from([1, 1, 3, 3, 2, 1]), Int32Array.from([1, 3, 3, 1, 2, 1]), Int32Array.from([3, 1, 3, 1, 2, 1]), Int32Array.from([2, 1, 1, 3, 3, 1]), Int32Array.from([2, 3, 1, 1, 3, 1]), Int32Array.from([2, 1, 3, 1, 1, 3]), Int32Array.from([2, 1, 3, 3, 1, 1]), Int32Array.from([2, 1, 3, 1, 3, 1]), Int32Array.from([3, 1, 1, 1, 2, 3]), Int32Array.from([3, 1, 1, 3, 2, 1]), Int32Array.from([3, 3, 1, 1, 2, 1]), Int32Array.from([3, 1, 2, 1, 1, 3]), Int32Array.from([3, 1, 2, 3, 1, 1]), Int32Array.from([3, 3, 2, 1, 1, 1]), Int32Array.from([3, 1, 4, 1, 1, 1]), Int32Array.from([2, 2, 1, 4, 1, 1]), Int32Array.from([4, 3, 1, 1, 1, 1]), Int32Array.from([1, 1, 1, 2, 2, 4]), Int32Array.from([1, 1, 1, 4, 2, 2]), Int32Array.from([1, 2, 1, 1, 2, 4]), Int32Array.from([1, 2, 1, 4, 2, 1]), Int32Array.from([1, 4, 1, 1, 2, 2]), Int32Array.from([1, 4, 1, 2, 2, 1]), Int32Array.from([1, 1, 2, 2, 1, 4]), Int32Array.from([1, 1, 2, 4, 1, 2]), Int32Array.from([1, 2, 2, 1, 1, 4]), Int32Array.from([1, 2, 2, 4, 1, 1]), Int32Array.from([1, 4, 2, 1, 1, 2]), Int32Array.from([1, 4, 2, 2, 1, 1]), Int32Array.from([2, 4, 1, 2, 1, 1]), Int32Array.from([2, 2, 1, 1, 1, 4]), Int32Array.from([4, 1, 3, 1, 1, 1]), Int32Array.from([2, 4, 1, 1, 1, 2]), Int32Array.from([1, 3, 4, 1, 1, 1]), Int32Array.from([1, 1, 1, 2, 4, 2]), Int32Array.from([1, 2, 1, 1, 4, 2]), Int32Array.from([1, 2, 1, 2, 4, 1]), Int32Array.from([1, 1, 4, 2, 1, 2]), Int32Array.from([1, 2, 4, 1, 1, 2]), Int32Array.from([1, 2, 4, 2, 1, 1]), Int32Array.from([4, 1, 1, 2, 1, 2]), Int32Array.from([4, 2, 1, 1, 1, 2]), Int32Array.from([4, 2, 1, 2, 1, 1]), Int32Array.from([2, 1, 2, 1, 4, 1]), Int32Array.from([2, 1, 4, 1, 2, 1]), Int32Array.from([4, 1, 2, 1, 2, 1]), Int32Array.from([1, 1, 1, 1, 4, 3]), Int32Array.from([1, 1, 1, 3, 4, 1]), Int32Array.from([1, 3, 1, 1, 4, 1]), Int32Array.from([1, 1, 4, 1, 1, 3]), Int32Array.from([1, 1, 4, 3, 1, 1]), Int32Array.from([4, 1, 1, 1, 1, 3]), Int32Array.from([4, 1, 1, 3, 1, 1]), Int32Array.from([1, 1, 3, 1, 4, 1]), Int32Array.from([1, 1, 4, 1, 3, 1]), Int32Array.from([3, 1, 1, 1, 4, 1]), Int32Array.from([4, 1, 1, 1, 3, 1]), Int32Array.from([2, 1, 1, 4, 1, 2]), Int32Array.from([2, 1, 1, 2, 1, 4]), Int32Array.from([2, 1, 1, 2, 3, 2]), Int32Array.from([2, 3, 3, 1, 1, 1, 2])];\n  Code128Reader.MAX_AVG_VARIANCE = 0.25;\n  Code128Reader.MAX_INDIVIDUAL_VARIANCE = 0.7;\n  Code128Reader.CODE_SHIFT = 98;\n  Code128Reader.CODE_CODE_C = 99;\n  Code128Reader.CODE_CODE_B = 100;\n  Code128Reader.CODE_CODE_A = 101;\n  Code128Reader.CODE_FNC_1 = 102;\n  Code128Reader.CODE_FNC_2 = 97;\n  Code128Reader.CODE_FNC_3 = 96;\n  Code128Reader.CODE_FNC_4_A = 101;\n  Code128Reader.CODE_FNC_4_B = 100;\n  Code128Reader.CODE_START_A = 103;\n  Code128Reader.CODE_START_B = 104;\n  Code128Reader.CODE_START_C = 105;\n  Code128Reader.CODE_STOP = 106;\n  return Code128Reader;\n}(OneDReader);\nexport default Code128Reader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "BarcodeFormat", "ChecksumException", "DecodeHintType", "FormatException", "NotFoundException", "Result", "ResultPoint", "OneDReader", "Code128Reader", "_super", "apply", "arguments", "findStartPattern", "row", "width", "getSize", "rowOffset", "getNextSet", "counterPosition", "counters", "Int32Array", "from", "patternStart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "i", "get", "bestVariance", "MAX_AVG_VARIANCE", "bestMatch", "startCode", "CODE_START_A", "CODE_START_C", "variance", "patternMatchVariance", "CODE_PATTERNS", "MAX_INDIVIDUAL_VARIANCE", "isRange", "Math", "max", "slice", "length", "decodeCode", "recordPattern", "pattern", "decodeRow", "rowNumber", "hints", "convertFNC1", "ASSUME_GS1", "startPatternInfo", "currentRawCodesIndex", "rawCodes", "Uint8Array", "codeSet", "CODE_CODE_A", "CODE_START_B", "CODE_CODE_B", "CODE_CODE_C", "done", "isNextShifted", "result", "lastStart", "nextStart", "lastCode", "code", "checksumTotal", "multiplier", "lastCharacterWasPrintable", "upperMode", "shiftUpperMode", "unshift", "CODE_STOP", "reduce", "previous", "current", "String", "fromCharCode", "charCodeAt", "CODE_FNC_1", "CODE_FNC_2", "CODE_FNC_3", "CODE_FNC_4_A", "CODE_SHIFT", "CODE_FNC_4_B", "lastPatternSize", "getNextUnset", "min", "result<PERSON><PERSON><PERSON>", "substring", "left", "right", "rawCodesSize", "rawBytes", "points", "CODE_128", "Date", "getTime"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/Code128Reader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport DecodeHintType from '../DecodeHintType';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\n// import Reader from '../Reader';\nimport Result from '../Result';\n// import ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport OneDReader from './OneDReader';\n/**\n * <p>Decodes Code 128 barcodes.</p>\n *\n * <AUTHOR> Owen\n */\nvar Code128Reader = /** @class */ (function (_super) {\n    __extends(Code128Reader, _super);\n    function Code128Reader() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Code128Reader.findStartPattern = function (row) {\n        var width = row.getSize();\n        var rowOffset = row.getNextSet(0);\n        var counterPosition = 0;\n        var counters = Int32Array.from([0, 0, 0, 0, 0, 0]);\n        var patternStart = rowOffset;\n        var isWhite = false;\n        var patternLength = 6;\n        for (var i = rowOffset; i < width; i++) {\n            if (row.get(i) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === (patternLength - 1)) {\n                    var bestVariance = Code128Reader.MAX_AVG_VARIANCE;\n                    var bestMatch = -1;\n                    for (var startCode = Code128Reader.CODE_START_A; startCode <= Code128Reader.CODE_START_C; startCode++) {\n                        var variance = OneDReader.patternMatchVariance(counters, Code128Reader.CODE_PATTERNS[startCode], Code128Reader.MAX_INDIVIDUAL_VARIANCE);\n                        if (variance < bestVariance) {\n                            bestVariance = variance;\n                            bestMatch = startCode;\n                        }\n                    }\n                    // Look for whitespace before start pattern, >= 50% of width of start pattern\n                    if (bestMatch >= 0 &&\n                        row.isRange(Math.max(0, patternStart - (i - patternStart) / 2), patternStart, false)) {\n                        return Int32Array.from([patternStart, i, bestMatch]);\n                    }\n                    patternStart += counters[0] + counters[1];\n                    counters = counters.slice(2, counters.length);\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    Code128Reader.decodeCode = function (row, counters, rowOffset) {\n        OneDReader.recordPattern(row, rowOffset, counters);\n        var bestVariance = Code128Reader.MAX_AVG_VARIANCE; // worst variance we'll accept\n        var bestMatch = -1;\n        for (var d = 0; d < Code128Reader.CODE_PATTERNS.length; d++) {\n            var pattern = Code128Reader.CODE_PATTERNS[d];\n            var variance = this.patternMatchVariance(counters, pattern, Code128Reader.MAX_INDIVIDUAL_VARIANCE);\n            if (variance < bestVariance) {\n                bestVariance = variance;\n                bestMatch = d;\n            }\n        }\n        // TODO We're overlooking the fact that the STOP pattern has 7 values, not 6.\n        if (bestMatch >= 0) {\n            return bestMatch;\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    Code128Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var convertFNC1 = hints && (hints.get(DecodeHintType.ASSUME_GS1) === true);\n        var startPatternInfo = Code128Reader.findStartPattern(row);\n        var startCode = startPatternInfo[2];\n        var currentRawCodesIndex = 0;\n        var rawCodes = new Uint8Array(20);\n        rawCodes[currentRawCodesIndex++] = startCode;\n        var codeSet;\n        switch (startCode) {\n            case Code128Reader.CODE_START_A:\n                codeSet = Code128Reader.CODE_CODE_A;\n                break;\n            case Code128Reader.CODE_START_B:\n                codeSet = Code128Reader.CODE_CODE_B;\n                break;\n            case Code128Reader.CODE_START_C:\n                codeSet = Code128Reader.CODE_CODE_C;\n                break;\n            default:\n                throw new FormatException();\n        }\n        var done = false;\n        var isNextShifted = false;\n        var result = '';\n        var lastStart = startPatternInfo[0];\n        var nextStart = startPatternInfo[1];\n        var counters = Int32Array.from([0, 0, 0, 0, 0, 0]);\n        var lastCode = 0;\n        var code = 0;\n        var checksumTotal = startCode;\n        var multiplier = 0;\n        var lastCharacterWasPrintable = true;\n        var upperMode = false;\n        var shiftUpperMode = false;\n        while (!done) {\n            var unshift = isNextShifted;\n            isNextShifted = false;\n            // Save off last code\n            lastCode = code;\n            // Decode another code from image\n            code = Code128Reader.decodeCode(row, counters, nextStart);\n            rawCodes[currentRawCodesIndex++] = code;\n            // Remember whether the last code was printable or not (excluding CODE_STOP)\n            if (code !== Code128Reader.CODE_STOP) {\n                lastCharacterWasPrintable = true;\n            }\n            // Add to checksum computation (if not CODE_STOP of course)\n            if (code !== Code128Reader.CODE_STOP) {\n                multiplier++;\n                checksumTotal += multiplier * code;\n            }\n            // Advance to where the next code will to start\n            lastStart = nextStart;\n            nextStart += counters.reduce(function (previous, current) { return previous + current; }, 0);\n            // Take care of illegal start codes\n            switch (code) {\n                case Code128Reader.CODE_START_A:\n                case Code128Reader.CODE_START_B:\n                case Code128Reader.CODE_START_C:\n                    throw new FormatException();\n            }\n            switch (codeSet) {\n                case Code128Reader.CODE_CODE_A:\n                    if (code < 64) {\n                        if (shiftUpperMode === upperMode) {\n                            result += String.fromCharCode((' '.charCodeAt(0) + code));\n                        }\n                        else {\n                            result += String.fromCharCode((' '.charCodeAt(0) + code + 128));\n                        }\n                        shiftUpperMode = false;\n                    }\n                    else if (code < 96) {\n                        if (shiftUpperMode === upperMode) {\n                            result += String.fromCharCode((code - 64));\n                        }\n                        else {\n                            result += String.fromCharCode((code + 64));\n                        }\n                        shiftUpperMode = false;\n                    }\n                    else {\n                        // Don't let CODE_STOP, which always appears, affect whether whether we think the last\n                        // code was printable or not.\n                        if (code !== Code128Reader.CODE_STOP) {\n                            lastCharacterWasPrintable = false;\n                        }\n                        switch (code) {\n                            case Code128Reader.CODE_FNC_1:\n                                if (convertFNC1) {\n                                    if (result.length === 0) {\n                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                                        // is FNC1 then this is GS1-128. We add the symbology identifier.\n                                        result += ']C1';\n                                    }\n                                    else {\n                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                                        result += String.fromCharCode(29);\n                                    }\n                                }\n                                break;\n                            case Code128Reader.CODE_FNC_2:\n                            case Code128Reader.CODE_FNC_3:\n                                // do nothing?\n                                break;\n                            case Code128Reader.CODE_FNC_4_A:\n                                if (!upperMode && shiftUpperMode) {\n                                    upperMode = true;\n                                    shiftUpperMode = false;\n                                }\n                                else if (upperMode && shiftUpperMode) {\n                                    upperMode = false;\n                                    shiftUpperMode = false;\n                                }\n                                else {\n                                    shiftUpperMode = true;\n                                }\n                                break;\n                            case Code128Reader.CODE_SHIFT:\n                                isNextShifted = true;\n                                codeSet = Code128Reader.CODE_CODE_B;\n                                break;\n                            case Code128Reader.CODE_CODE_B:\n                                codeSet = Code128Reader.CODE_CODE_B;\n                                break;\n                            case Code128Reader.CODE_CODE_C:\n                                codeSet = Code128Reader.CODE_CODE_C;\n                                break;\n                            case Code128Reader.CODE_STOP:\n                                done = true;\n                                break;\n                        }\n                    }\n                    break;\n                case Code128Reader.CODE_CODE_B:\n                    if (code < 96) {\n                        if (shiftUpperMode === upperMode) {\n                            result += String.fromCharCode((' '.charCodeAt(0) + code));\n                        }\n                        else {\n                            result += String.fromCharCode((' '.charCodeAt(0) + code + 128));\n                        }\n                        shiftUpperMode = false;\n                    }\n                    else {\n                        if (code !== Code128Reader.CODE_STOP) {\n                            lastCharacterWasPrintable = false;\n                        }\n                        switch (code) {\n                            case Code128Reader.CODE_FNC_1:\n                                if (convertFNC1) {\n                                    if (result.length === 0) {\n                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                                        // is FNC1 then this is GS1-128. We add the symbology identifier.\n                                        result += ']C1';\n                                    }\n                                    else {\n                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                                        result += String.fromCharCode(29);\n                                    }\n                                }\n                                break;\n                            case Code128Reader.CODE_FNC_2:\n                            case Code128Reader.CODE_FNC_3:\n                                // do nothing?\n                                break;\n                            case Code128Reader.CODE_FNC_4_B:\n                                if (!upperMode && shiftUpperMode) {\n                                    upperMode = true;\n                                    shiftUpperMode = false;\n                                }\n                                else if (upperMode && shiftUpperMode) {\n                                    upperMode = false;\n                                    shiftUpperMode = false;\n                                }\n                                else {\n                                    shiftUpperMode = true;\n                                }\n                                break;\n                            case Code128Reader.CODE_SHIFT:\n                                isNextShifted = true;\n                                codeSet = Code128Reader.CODE_CODE_A;\n                                break;\n                            case Code128Reader.CODE_CODE_A:\n                                codeSet = Code128Reader.CODE_CODE_A;\n                                break;\n                            case Code128Reader.CODE_CODE_C:\n                                codeSet = Code128Reader.CODE_CODE_C;\n                                break;\n                            case Code128Reader.CODE_STOP:\n                                done = true;\n                                break;\n                        }\n                    }\n                    break;\n                case Code128Reader.CODE_CODE_C:\n                    if (code < 100) {\n                        if (code < 10) {\n                            result += '0';\n                        }\n                        result += code;\n                    }\n                    else {\n                        if (code !== Code128Reader.CODE_STOP) {\n                            lastCharacterWasPrintable = false;\n                        }\n                        switch (code) {\n                            case Code128Reader.CODE_FNC_1:\n                                if (convertFNC1) {\n                                    if (result.length === 0) {\n                                        // GS1 specification 5.4.3.7. and 5.4.6.4. If the first char after the start code\n                                        // is FNC1 then this is GS1-128. We add the symbology identifier.\n                                        result += ']C1';\n                                    }\n                                    else {\n                                        // GS1 specification 5.4.7.5. Every subsequent FNC1 is returned as ASCII 29 (GS)\n                                        result += String.fromCharCode(29);\n                                    }\n                                }\n                                break;\n                            case Code128Reader.CODE_CODE_A:\n                                codeSet = Code128Reader.CODE_CODE_A;\n                                break;\n                            case Code128Reader.CODE_CODE_B:\n                                codeSet = Code128Reader.CODE_CODE_B;\n                                break;\n                            case Code128Reader.CODE_STOP:\n                                done = true;\n                                break;\n                        }\n                    }\n                    break;\n            }\n            // Unshift back to another code set if we were shifted\n            if (unshift) {\n                codeSet = codeSet === Code128Reader.CODE_CODE_A ? Code128Reader.CODE_CODE_B : Code128Reader.CODE_CODE_A;\n            }\n        }\n        var lastPatternSize = nextStart - lastStart;\n        // Check for ample whitespace following pattern, but, to do this we first need to remember that\n        // we fudged decoding CODE_STOP since it actually has 7 bars, not 6. There is a black bar left\n        // to read off. Would be slightly better to properly read. Here we just skip it:\n        nextStart = row.getNextUnset(nextStart);\n        if (!row.isRange(nextStart, Math.min(row.getSize(), nextStart + (nextStart - lastStart) / 2), false)) {\n            throw new NotFoundException();\n        }\n        // Pull out from sum the value of the penultimate check code\n        checksumTotal -= multiplier * lastCode;\n        // lastCode is the checksum then:\n        if (checksumTotal % 103 !== lastCode) {\n            throw new ChecksumException();\n        }\n        // Need to pull out the check digits from string\n        var resultLength = result.length;\n        if (resultLength === 0) {\n            // false positive\n            throw new NotFoundException();\n        }\n        // Only bother if the result had at least one character, and if the checksum digit happened to\n        // be a printable character. If it was just interpreted as a control code, nothing to remove.\n        if (resultLength > 0 && lastCharacterWasPrintable) {\n            if (codeSet === Code128Reader.CODE_CODE_C) {\n                result = result.substring(0, resultLength - 2);\n            }\n            else {\n                result = result.substring(0, resultLength - 1);\n            }\n        }\n        var left = (startPatternInfo[1] + startPatternInfo[0]) / 2.0;\n        var right = lastStart + lastPatternSize / 2.0;\n        var rawCodesSize = rawCodes.length;\n        var rawBytes = new Uint8Array(rawCodesSize);\n        for (var i = 0; i < rawCodesSize; i++) {\n            rawBytes[i] = rawCodes[i];\n        }\n        var points = [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)];\n        return new Result(result, rawBytes, 0, points, BarcodeFormat.CODE_128, new Date().getTime());\n    };\n    Code128Reader.CODE_PATTERNS = [\n        Int32Array.from([2, 1, 2, 2, 2, 2]),\n        Int32Array.from([2, 2, 2, 1, 2, 2]),\n        Int32Array.from([2, 2, 2, 2, 2, 1]),\n        Int32Array.from([1, 2, 1, 2, 2, 3]),\n        Int32Array.from([1, 2, 1, 3, 2, 2]),\n        Int32Array.from([1, 3, 1, 2, 2, 2]),\n        Int32Array.from([1, 2, 2, 2, 1, 3]),\n        Int32Array.from([1, 2, 2, 3, 1, 2]),\n        Int32Array.from([1, 3, 2, 2, 1, 2]),\n        Int32Array.from([2, 2, 1, 2, 1, 3]),\n        Int32Array.from([2, 2, 1, 3, 1, 2]),\n        Int32Array.from([2, 3, 1, 2, 1, 2]),\n        Int32Array.from([1, 1, 2, 2, 3, 2]),\n        Int32Array.from([1, 2, 2, 1, 3, 2]),\n        Int32Array.from([1, 2, 2, 2, 3, 1]),\n        Int32Array.from([1, 1, 3, 2, 2, 2]),\n        Int32Array.from([1, 2, 3, 1, 2, 2]),\n        Int32Array.from([1, 2, 3, 2, 2, 1]),\n        Int32Array.from([2, 2, 3, 2, 1, 1]),\n        Int32Array.from([2, 2, 1, 1, 3, 2]),\n        Int32Array.from([2, 2, 1, 2, 3, 1]),\n        Int32Array.from([2, 1, 3, 2, 1, 2]),\n        Int32Array.from([2, 2, 3, 1, 1, 2]),\n        Int32Array.from([3, 1, 2, 1, 3, 1]),\n        Int32Array.from([3, 1, 1, 2, 2, 2]),\n        Int32Array.from([3, 2, 1, 1, 2, 2]),\n        Int32Array.from([3, 2, 1, 2, 2, 1]),\n        Int32Array.from([3, 1, 2, 2, 1, 2]),\n        Int32Array.from([3, 2, 2, 1, 1, 2]),\n        Int32Array.from([3, 2, 2, 2, 1, 1]),\n        Int32Array.from([2, 1, 2, 1, 2, 3]),\n        Int32Array.from([2, 1, 2, 3, 2, 1]),\n        Int32Array.from([2, 3, 2, 1, 2, 1]),\n        Int32Array.from([1, 1, 1, 3, 2, 3]),\n        Int32Array.from([1, 3, 1, 1, 2, 3]),\n        Int32Array.from([1, 3, 1, 3, 2, 1]),\n        Int32Array.from([1, 1, 2, 3, 1, 3]),\n        Int32Array.from([1, 3, 2, 1, 1, 3]),\n        Int32Array.from([1, 3, 2, 3, 1, 1]),\n        Int32Array.from([2, 1, 1, 3, 1, 3]),\n        Int32Array.from([2, 3, 1, 1, 1, 3]),\n        Int32Array.from([2, 3, 1, 3, 1, 1]),\n        Int32Array.from([1, 1, 2, 1, 3, 3]),\n        Int32Array.from([1, 1, 2, 3, 3, 1]),\n        Int32Array.from([1, 3, 2, 1, 3, 1]),\n        Int32Array.from([1, 1, 3, 1, 2, 3]),\n        Int32Array.from([1, 1, 3, 3, 2, 1]),\n        Int32Array.from([1, 3, 3, 1, 2, 1]),\n        Int32Array.from([3, 1, 3, 1, 2, 1]),\n        Int32Array.from([2, 1, 1, 3, 3, 1]),\n        Int32Array.from([2, 3, 1, 1, 3, 1]),\n        Int32Array.from([2, 1, 3, 1, 1, 3]),\n        Int32Array.from([2, 1, 3, 3, 1, 1]),\n        Int32Array.from([2, 1, 3, 1, 3, 1]),\n        Int32Array.from([3, 1, 1, 1, 2, 3]),\n        Int32Array.from([3, 1, 1, 3, 2, 1]),\n        Int32Array.from([3, 3, 1, 1, 2, 1]),\n        Int32Array.from([3, 1, 2, 1, 1, 3]),\n        Int32Array.from([3, 1, 2, 3, 1, 1]),\n        Int32Array.from([3, 3, 2, 1, 1, 1]),\n        Int32Array.from([3, 1, 4, 1, 1, 1]),\n        Int32Array.from([2, 2, 1, 4, 1, 1]),\n        Int32Array.from([4, 3, 1, 1, 1, 1]),\n        Int32Array.from([1, 1, 1, 2, 2, 4]),\n        Int32Array.from([1, 1, 1, 4, 2, 2]),\n        Int32Array.from([1, 2, 1, 1, 2, 4]),\n        Int32Array.from([1, 2, 1, 4, 2, 1]),\n        Int32Array.from([1, 4, 1, 1, 2, 2]),\n        Int32Array.from([1, 4, 1, 2, 2, 1]),\n        Int32Array.from([1, 1, 2, 2, 1, 4]),\n        Int32Array.from([1, 1, 2, 4, 1, 2]),\n        Int32Array.from([1, 2, 2, 1, 1, 4]),\n        Int32Array.from([1, 2, 2, 4, 1, 1]),\n        Int32Array.from([1, 4, 2, 1, 1, 2]),\n        Int32Array.from([1, 4, 2, 2, 1, 1]),\n        Int32Array.from([2, 4, 1, 2, 1, 1]),\n        Int32Array.from([2, 2, 1, 1, 1, 4]),\n        Int32Array.from([4, 1, 3, 1, 1, 1]),\n        Int32Array.from([2, 4, 1, 1, 1, 2]),\n        Int32Array.from([1, 3, 4, 1, 1, 1]),\n        Int32Array.from([1, 1, 1, 2, 4, 2]),\n        Int32Array.from([1, 2, 1, 1, 4, 2]),\n        Int32Array.from([1, 2, 1, 2, 4, 1]),\n        Int32Array.from([1, 1, 4, 2, 1, 2]),\n        Int32Array.from([1, 2, 4, 1, 1, 2]),\n        Int32Array.from([1, 2, 4, 2, 1, 1]),\n        Int32Array.from([4, 1, 1, 2, 1, 2]),\n        Int32Array.from([4, 2, 1, 1, 1, 2]),\n        Int32Array.from([4, 2, 1, 2, 1, 1]),\n        Int32Array.from([2, 1, 2, 1, 4, 1]),\n        Int32Array.from([2, 1, 4, 1, 2, 1]),\n        Int32Array.from([4, 1, 2, 1, 2, 1]),\n        Int32Array.from([1, 1, 1, 1, 4, 3]),\n        Int32Array.from([1, 1, 1, 3, 4, 1]),\n        Int32Array.from([1, 3, 1, 1, 4, 1]),\n        Int32Array.from([1, 1, 4, 1, 1, 3]),\n        Int32Array.from([1, 1, 4, 3, 1, 1]),\n        Int32Array.from([4, 1, 1, 1, 1, 3]),\n        Int32Array.from([4, 1, 1, 3, 1, 1]),\n        Int32Array.from([1, 1, 3, 1, 4, 1]),\n        Int32Array.from([1, 1, 4, 1, 3, 1]),\n        Int32Array.from([3, 1, 1, 1, 4, 1]),\n        Int32Array.from([4, 1, 1, 1, 3, 1]),\n        Int32Array.from([2, 1, 1, 4, 1, 2]),\n        Int32Array.from([2, 1, 1, 2, 1, 4]),\n        Int32Array.from([2, 1, 1, 2, 3, 2]),\n        Int32Array.from([2, 3, 3, 1, 1, 1, 2]),\n    ];\n    Code128Reader.MAX_AVG_VARIANCE = 0.25;\n    Code128Reader.MAX_INDIVIDUAL_VARIANCE = 0.7;\n    Code128Reader.CODE_SHIFT = 98;\n    Code128Reader.CODE_CODE_C = 99;\n    Code128Reader.CODE_CODE_B = 100;\n    Code128Reader.CODE_CODE_A = 101;\n    Code128Reader.CODE_FNC_1 = 102;\n    Code128Reader.CODE_FNC_2 = 97;\n    Code128Reader.CODE_FNC_3 = 96;\n    Code128Reader.CODE_FNC_4_A = 101;\n    Code128Reader.CODE_FNC_4_B = 100;\n    Code128Reader.CODE_START_A = 103;\n    Code128Reader.CODE_START_B = 104;\n    Code128Reader.CODE_START_C = 105;\n    Code128Reader.CODE_STOP = 106;\n    return Code128Reader;\n}(OneDReader));\nexport default Code128Reader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA,OAAOC,MAAM,MAAM,WAAW;AAC9B;AACA,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,UAAU,MAAM,cAAc;AACrC;AACA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,aAAe,UAAUC,MAAM,EAAE;EACjDvB,SAAS,CAACsB,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACrB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACAH,aAAa,CAACI,gBAAgB,GAAG,UAAUC,GAAG,EAAE;IAC5C,IAAIC,KAAK,GAAGD,GAAG,CAACE,OAAO,CAAC,CAAC;IACzB,IAAIC,SAAS,GAAGH,GAAG,CAACI,UAAU,CAAC,CAAC,CAAC;IACjC,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIC,QAAQ,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAClD,IAAIC,YAAY,GAAGN,SAAS;IAC5B,IAAIO,OAAO,GAAG,KAAK;IACnB,IAAIC,aAAa,GAAG,CAAC;IACrB,KAAK,IAAIC,CAAC,GAAGT,SAAS,EAAES,CAAC,GAAGX,KAAK,EAAEW,CAAC,EAAE,EAAE;MACpC,IAAIZ,GAAG,CAACa,GAAG,CAACD,CAAC,CAAC,KAAKF,OAAO,EAAE;QACxBJ,QAAQ,CAACD,eAAe,CAAC,EAAE;MAC/B,CAAC,MACI;QACD,IAAIA,eAAe,KAAMM,aAAa,GAAG,CAAE,EAAE;UACzC,IAAIG,YAAY,GAAGnB,aAAa,CAACoB,gBAAgB;UACjD,IAAIC,SAAS,GAAG,CAAC,CAAC;UAClB,KAAK,IAAIC,SAAS,GAAGtB,aAAa,CAACuB,YAAY,EAAED,SAAS,IAAItB,aAAa,CAACwB,YAAY,EAAEF,SAAS,EAAE,EAAE;YACnG,IAAIG,QAAQ,GAAG1B,UAAU,CAAC2B,oBAAoB,CAACf,QAAQ,EAAEX,aAAa,CAAC2B,aAAa,CAACL,SAAS,CAAC,EAAEtB,aAAa,CAAC4B,uBAAuB,CAAC;YACvI,IAAIH,QAAQ,GAAGN,YAAY,EAAE;cACzBA,YAAY,GAAGM,QAAQ;cACvBJ,SAAS,GAAGC,SAAS;YACzB;UACJ;UACA;UACA,IAAID,SAAS,IAAI,CAAC,IACdhB,GAAG,CAACwB,OAAO,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjB,YAAY,GAAG,CAACG,CAAC,GAAGH,YAAY,IAAI,CAAC,CAAC,EAAEA,YAAY,EAAE,KAAK,CAAC,EAAE;YACtF,OAAOF,UAAU,CAACC,IAAI,CAAC,CAACC,YAAY,EAAEG,CAAC,EAAEI,SAAS,CAAC,CAAC;UACxD;UACAP,YAAY,IAAIH,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzCA,QAAQ,GAAGA,QAAQ,CAACqB,KAAK,CAAC,CAAC,EAAErB,QAAQ,CAACsB,MAAM,CAAC;UAC7CtB,QAAQ,CAACD,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;UACjCC,QAAQ,CAACD,eAAe,CAAC,GAAG,CAAC;UAC7BA,eAAe,EAAE;QACrB,CAAC,MACI;UACDA,eAAe,EAAE;QACrB;QACAC,QAAQ,CAACD,eAAe,CAAC,GAAG,CAAC;QAC7BK,OAAO,GAAG,CAACA,OAAO;MACtB;IACJ;IACA,MAAM,IAAInB,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACDI,aAAa,CAACkC,UAAU,GAAG,UAAU7B,GAAG,EAAEM,QAAQ,EAAEH,SAAS,EAAE;IAC3DT,UAAU,CAACoC,aAAa,CAAC9B,GAAG,EAAEG,SAAS,EAAEG,QAAQ,CAAC;IAClD,IAAIQ,YAAY,GAAGnB,aAAa,CAACoB,gBAAgB,CAAC,CAAC;IACnD,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,aAAa,CAAC2B,aAAa,CAACM,MAAM,EAAErD,CAAC,EAAE,EAAE;MACzD,IAAIwD,OAAO,GAAGpC,aAAa,CAAC2B,aAAa,CAAC/C,CAAC,CAAC;MAC5C,IAAI6C,QAAQ,GAAG,IAAI,CAACC,oBAAoB,CAACf,QAAQ,EAAEyB,OAAO,EAAEpC,aAAa,CAAC4B,uBAAuB,CAAC;MAClG,IAAIH,QAAQ,GAAGN,YAAY,EAAE;QACzBA,YAAY,GAAGM,QAAQ;QACvBJ,SAAS,GAAGzC,CAAC;MACjB;IACJ;IACA;IACA,IAAIyC,SAAS,IAAI,CAAC,EAAE;MAChB,OAAOA,SAAS;IACpB,CAAC,MACI;MACD,MAAM,IAAIzB,iBAAiB,CAAC,CAAC;IACjC;EACJ,CAAC;EACDI,aAAa,CAACV,SAAS,CAAC+C,SAAS,GAAG,UAAUC,SAAS,EAAEjC,GAAG,EAAEkC,KAAK,EAAE;IACjE,IAAIC,WAAW,GAAGD,KAAK,IAAKA,KAAK,CAACrB,GAAG,CAACxB,cAAc,CAAC+C,UAAU,CAAC,KAAK,IAAK;IAC1E,IAAIC,gBAAgB,GAAG1C,aAAa,CAACI,gBAAgB,CAACC,GAAG,CAAC;IAC1D,IAAIiB,SAAS,GAAGoB,gBAAgB,CAAC,CAAC,CAAC;IACnC,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,IAAIC,QAAQ,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;IACjCD,QAAQ,CAACD,oBAAoB,EAAE,CAAC,GAAGrB,SAAS;IAC5C,IAAIwB,OAAO;IACX,QAAQxB,SAAS;MACb,KAAKtB,aAAa,CAACuB,YAAY;QAC3BuB,OAAO,GAAG9C,aAAa,CAAC+C,WAAW;QACnC;MACJ,KAAK/C,aAAa,CAACgD,YAAY;QAC3BF,OAAO,GAAG9C,aAAa,CAACiD,WAAW;QACnC;MACJ,KAAKjD,aAAa,CAACwB,YAAY;QAC3BsB,OAAO,GAAG9C,aAAa,CAACkD,WAAW;QACnC;MACJ;QACI,MAAM,IAAIvD,eAAe,CAAC,CAAC;IACnC;IACA,IAAIwD,IAAI,GAAG,KAAK;IAChB,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAIC,MAAM,GAAG,EAAE;IACf,IAAIC,SAAS,GAAGZ,gBAAgB,CAAC,CAAC,CAAC;IACnC,IAAIa,SAAS,GAAGb,gBAAgB,CAAC,CAAC,CAAC;IACnC,IAAI/B,QAAQ,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAClD,IAAI2C,QAAQ,GAAG,CAAC;IAChB,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAIC,aAAa,GAAGpC,SAAS;IAC7B,IAAIqC,UAAU,GAAG,CAAC;IAClB,IAAIC,yBAAyB,GAAG,IAAI;IACpC,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,cAAc,GAAG,KAAK;IAC1B,OAAO,CAACX,IAAI,EAAE;MACV,IAAIY,OAAO,GAAGX,aAAa;MAC3BA,aAAa,GAAG,KAAK;MACrB;MACAI,QAAQ,GAAGC,IAAI;MACf;MACAA,IAAI,GAAGzD,aAAa,CAACkC,UAAU,CAAC7B,GAAG,EAAEM,QAAQ,EAAE4C,SAAS,CAAC;MACzDX,QAAQ,CAACD,oBAAoB,EAAE,CAAC,GAAGc,IAAI;MACvC;MACA,IAAIA,IAAI,KAAKzD,aAAa,CAACgE,SAAS,EAAE;QAClCJ,yBAAyB,GAAG,IAAI;MACpC;MACA;MACA,IAAIH,IAAI,KAAKzD,aAAa,CAACgE,SAAS,EAAE;QAClCL,UAAU,EAAE;QACZD,aAAa,IAAIC,UAAU,GAAGF,IAAI;MACtC;MACA;MACAH,SAAS,GAAGC,SAAS;MACrBA,SAAS,IAAI5C,QAAQ,CAACsD,MAAM,CAAC,UAAUC,QAAQ,EAAEC,OAAO,EAAE;QAAE,OAAOD,QAAQ,GAAGC,OAAO;MAAE,CAAC,EAAE,CAAC,CAAC;MAC5F;MACA,QAAQV,IAAI;QACR,KAAKzD,aAAa,CAACuB,YAAY;QAC/B,KAAKvB,aAAa,CAACgD,YAAY;QAC/B,KAAKhD,aAAa,CAACwB,YAAY;UAC3B,MAAM,IAAI7B,eAAe,CAAC,CAAC;MACnC;MACA,QAAQmD,OAAO;QACX,KAAK9C,aAAa,CAAC+C,WAAW;UAC1B,IAAIU,IAAI,GAAG,EAAE,EAAE;YACX,IAAIK,cAAc,KAAKD,SAAS,EAAE;cAC9BR,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGb,IAAK,CAAC;YAC7D,CAAC,MACI;cACDJ,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGb,IAAI,GAAG,GAAI,CAAC;YACnE;YACAK,cAAc,GAAG,KAAK;UAC1B,CAAC,MACI,IAAIL,IAAI,GAAG,EAAE,EAAE;YAChB,IAAIK,cAAc,KAAKD,SAAS,EAAE;cAC9BR,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAEZ,IAAI,GAAG,EAAG,CAAC;YAC9C,CAAC,MACI;cACDJ,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAEZ,IAAI,GAAG,EAAG,CAAC;YAC9C;YACAK,cAAc,GAAG,KAAK;UAC1B,CAAC,MACI;YACD;YACA;YACA,IAAIL,IAAI,KAAKzD,aAAa,CAACgE,SAAS,EAAE;cAClCJ,yBAAyB,GAAG,KAAK;YACrC;YACA,QAAQH,IAAI;cACR,KAAKzD,aAAa,CAACuE,UAAU;gBACzB,IAAI/B,WAAW,EAAE;kBACb,IAAIa,MAAM,CAACpB,MAAM,KAAK,CAAC,EAAE;oBACrB;oBACA;oBACAoB,MAAM,IAAI,KAAK;kBACnB,CAAC,MACI;oBACD;oBACAA,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC;kBACrC;gBACJ;gBACA;cACJ,KAAKrE,aAAa,CAACwE,UAAU;cAC7B,KAAKxE,aAAa,CAACyE,UAAU;gBACzB;gBACA;cACJ,KAAKzE,aAAa,CAAC0E,YAAY;gBAC3B,IAAI,CAACb,SAAS,IAAIC,cAAc,EAAE;kBAC9BD,SAAS,GAAG,IAAI;kBAChBC,cAAc,GAAG,KAAK;gBAC1B,CAAC,MACI,IAAID,SAAS,IAAIC,cAAc,EAAE;kBAClCD,SAAS,GAAG,KAAK;kBACjBC,cAAc,GAAG,KAAK;gBAC1B,CAAC,MACI;kBACDA,cAAc,GAAG,IAAI;gBACzB;gBACA;cACJ,KAAK9D,aAAa,CAAC2E,UAAU;gBACzBvB,aAAa,GAAG,IAAI;gBACpBN,OAAO,GAAG9C,aAAa,CAACiD,WAAW;gBACnC;cACJ,KAAKjD,aAAa,CAACiD,WAAW;gBAC1BH,OAAO,GAAG9C,aAAa,CAACiD,WAAW;gBACnC;cACJ,KAAKjD,aAAa,CAACkD,WAAW;gBAC1BJ,OAAO,GAAG9C,aAAa,CAACkD,WAAW;gBACnC;cACJ,KAAKlD,aAAa,CAACgE,SAAS;gBACxBb,IAAI,GAAG,IAAI;gBACX;YACR;UACJ;UACA;QACJ,KAAKnD,aAAa,CAACiD,WAAW;UAC1B,IAAIQ,IAAI,GAAG,EAAE,EAAE;YACX,IAAIK,cAAc,KAAKD,SAAS,EAAE;cAC9BR,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGb,IAAK,CAAC;YAC7D,CAAC,MACI;cACDJ,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGb,IAAI,GAAG,GAAI,CAAC;YACnE;YACAK,cAAc,GAAG,KAAK;UAC1B,CAAC,MACI;YACD,IAAIL,IAAI,KAAKzD,aAAa,CAACgE,SAAS,EAAE;cAClCJ,yBAAyB,GAAG,KAAK;YACrC;YACA,QAAQH,IAAI;cACR,KAAKzD,aAAa,CAACuE,UAAU;gBACzB,IAAI/B,WAAW,EAAE;kBACb,IAAIa,MAAM,CAACpB,MAAM,KAAK,CAAC,EAAE;oBACrB;oBACA;oBACAoB,MAAM,IAAI,KAAK;kBACnB,CAAC,MACI;oBACD;oBACAA,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC;kBACrC;gBACJ;gBACA;cACJ,KAAKrE,aAAa,CAACwE,UAAU;cAC7B,KAAKxE,aAAa,CAACyE,UAAU;gBACzB;gBACA;cACJ,KAAKzE,aAAa,CAAC4E,YAAY;gBAC3B,IAAI,CAACf,SAAS,IAAIC,cAAc,EAAE;kBAC9BD,SAAS,GAAG,IAAI;kBAChBC,cAAc,GAAG,KAAK;gBAC1B,CAAC,MACI,IAAID,SAAS,IAAIC,cAAc,EAAE;kBAClCD,SAAS,GAAG,KAAK;kBACjBC,cAAc,GAAG,KAAK;gBAC1B,CAAC,MACI;kBACDA,cAAc,GAAG,IAAI;gBACzB;gBACA;cACJ,KAAK9D,aAAa,CAAC2E,UAAU;gBACzBvB,aAAa,GAAG,IAAI;gBACpBN,OAAO,GAAG9C,aAAa,CAAC+C,WAAW;gBACnC;cACJ,KAAK/C,aAAa,CAAC+C,WAAW;gBAC1BD,OAAO,GAAG9C,aAAa,CAAC+C,WAAW;gBACnC;cACJ,KAAK/C,aAAa,CAACkD,WAAW;gBAC1BJ,OAAO,GAAG9C,aAAa,CAACkD,WAAW;gBACnC;cACJ,KAAKlD,aAAa,CAACgE,SAAS;gBACxBb,IAAI,GAAG,IAAI;gBACX;YACR;UACJ;UACA;QACJ,KAAKnD,aAAa,CAACkD,WAAW;UAC1B,IAAIO,IAAI,GAAG,GAAG,EAAE;YACZ,IAAIA,IAAI,GAAG,EAAE,EAAE;cACXJ,MAAM,IAAI,GAAG;YACjB;YACAA,MAAM,IAAII,IAAI;UAClB,CAAC,MACI;YACD,IAAIA,IAAI,KAAKzD,aAAa,CAACgE,SAAS,EAAE;cAClCJ,yBAAyB,GAAG,KAAK;YACrC;YACA,QAAQH,IAAI;cACR,KAAKzD,aAAa,CAACuE,UAAU;gBACzB,IAAI/B,WAAW,EAAE;kBACb,IAAIa,MAAM,CAACpB,MAAM,KAAK,CAAC,EAAE;oBACrB;oBACA;oBACAoB,MAAM,IAAI,KAAK;kBACnB,CAAC,MACI;oBACD;oBACAA,MAAM,IAAIe,MAAM,CAACC,YAAY,CAAC,EAAE,CAAC;kBACrC;gBACJ;gBACA;cACJ,KAAKrE,aAAa,CAAC+C,WAAW;gBAC1BD,OAAO,GAAG9C,aAAa,CAAC+C,WAAW;gBACnC;cACJ,KAAK/C,aAAa,CAACiD,WAAW;gBAC1BH,OAAO,GAAG9C,aAAa,CAACiD,WAAW;gBACnC;cACJ,KAAKjD,aAAa,CAACgE,SAAS;gBACxBb,IAAI,GAAG,IAAI;gBACX;YACR;UACJ;UACA;MACR;MACA;MACA,IAAIY,OAAO,EAAE;QACTjB,OAAO,GAAGA,OAAO,KAAK9C,aAAa,CAAC+C,WAAW,GAAG/C,aAAa,CAACiD,WAAW,GAAGjD,aAAa,CAAC+C,WAAW;MAC3G;IACJ;IACA,IAAI8B,eAAe,GAAGtB,SAAS,GAAGD,SAAS;IAC3C;IACA;IACA;IACAC,SAAS,GAAGlD,GAAG,CAACyE,YAAY,CAACvB,SAAS,CAAC;IACvC,IAAI,CAAClD,GAAG,CAACwB,OAAO,CAAC0B,SAAS,EAAEzB,IAAI,CAACiD,GAAG,CAAC1E,GAAG,CAACE,OAAO,CAAC,CAAC,EAAEgD,SAAS,GAAG,CAACA,SAAS,GAAGD,SAAS,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE;MAClG,MAAM,IAAI1D,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA8D,aAAa,IAAIC,UAAU,GAAGH,QAAQ;IACtC;IACA,IAAIE,aAAa,GAAG,GAAG,KAAKF,QAAQ,EAAE;MAClC,MAAM,IAAI/D,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA,IAAIuF,YAAY,GAAG3B,MAAM,CAACpB,MAAM;IAChC,IAAI+C,YAAY,KAAK,CAAC,EAAE;MACpB;MACA,MAAM,IAAIpF,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA;IACA,IAAIoF,YAAY,GAAG,CAAC,IAAIpB,yBAAyB,EAAE;MAC/C,IAAId,OAAO,KAAK9C,aAAa,CAACkD,WAAW,EAAE;QACvCG,MAAM,GAAGA,MAAM,CAAC4B,SAAS,CAAC,CAAC,EAAED,YAAY,GAAG,CAAC,CAAC;MAClD,CAAC,MACI;QACD3B,MAAM,GAAGA,MAAM,CAAC4B,SAAS,CAAC,CAAC,EAAED,YAAY,GAAG,CAAC,CAAC;MAClD;IACJ;IACA,IAAIE,IAAI,GAAG,CAACxC,gBAAgB,CAAC,CAAC,CAAC,GAAGA,gBAAgB,CAAC,CAAC,CAAC,IAAI,GAAG;IAC5D,IAAIyC,KAAK,GAAG7B,SAAS,GAAGuB,eAAe,GAAG,GAAG;IAC7C,IAAIO,YAAY,GAAGxC,QAAQ,CAACX,MAAM;IAClC,IAAIoD,QAAQ,GAAG,IAAIxC,UAAU,CAACuC,YAAY,CAAC;IAC3C,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmE,YAAY,EAAEnE,CAAC,EAAE,EAAE;MACnCoE,QAAQ,CAACpE,CAAC,CAAC,GAAG2B,QAAQ,CAAC3B,CAAC,CAAC;IAC7B;IACA,IAAIqE,MAAM,GAAG,CAAC,IAAIxF,WAAW,CAACoF,IAAI,EAAE5C,SAAS,CAAC,EAAE,IAAIxC,WAAW,CAACqF,KAAK,EAAE7C,SAAS,CAAC,CAAC;IAClF,OAAO,IAAIzC,MAAM,CAACwD,MAAM,EAAEgC,QAAQ,EAAE,CAAC,EAAEC,MAAM,EAAE9F,aAAa,CAAC+F,QAAQ,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAChG,CAAC;EACDzF,aAAa,CAAC2B,aAAa,GAAG,CAC1Bf,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACnCD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CACzC;EACDb,aAAa,CAACoB,gBAAgB,GAAG,IAAI;EACrCpB,aAAa,CAAC4B,uBAAuB,GAAG,GAAG;EAC3C5B,aAAa,CAAC2E,UAAU,GAAG,EAAE;EAC7B3E,aAAa,CAACkD,WAAW,GAAG,EAAE;EAC9BlD,aAAa,CAACiD,WAAW,GAAG,GAAG;EAC/BjD,aAAa,CAAC+C,WAAW,GAAG,GAAG;EAC/B/C,aAAa,CAACuE,UAAU,GAAG,GAAG;EAC9BvE,aAAa,CAACwE,UAAU,GAAG,EAAE;EAC7BxE,aAAa,CAACyE,UAAU,GAAG,EAAE;EAC7BzE,aAAa,CAAC0E,YAAY,GAAG,GAAG;EAChC1E,aAAa,CAAC4E,YAAY,GAAG,GAAG;EAChC5E,aAAa,CAACuB,YAAY,GAAG,GAAG;EAChCvB,aAAa,CAACgD,YAAY,GAAG,GAAG;EAChChD,aAAa,CAACwB,YAAY,GAAG,GAAG;EAChCxB,aAAa,CAACgE,SAAS,GAAG,GAAG;EAC7B,OAAOhE,aAAa;AACxB,CAAC,CAACD,UAAU,CAAE;AACd,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}