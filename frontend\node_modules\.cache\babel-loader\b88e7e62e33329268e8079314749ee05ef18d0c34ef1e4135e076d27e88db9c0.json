{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getTotalWidthOfEncodings = exports.calculateEncodingAttributes = exports.getBarcodePadding = exports.getEncodingHeight = exports.getMaximumHeightOfEncodings = undefined;\nvar _merge = require(\"../help/merge.js\");\nvar _merge2 = _interopRequireDefault(_merge);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction getEncodingHeight(encoding, options) {\n  return options.height + (options.displayValue && encoding.text.length > 0 ? options.fontSize + options.textMargin : 0) + options.marginTop + options.marginBottom;\n}\nfunction getBarcodePadding(textWidth, barcodeWidth, options) {\n  if (options.displayValue && barcodeWidth < textWidth) {\n    if (options.textAlign == \"center\") {\n      return Math.floor((textWidth - barcodeWidth) / 2);\n    } else if (options.textAlign == \"left\") {\n      return 0;\n    } else if (options.textAlign == \"right\") {\n      return Math.floor(textWidth - barcodeWidth);\n    }\n  }\n  return 0;\n}\nfunction calculateEncodingAttributes(encodings, barcodeOptions, context) {\n  for (var i = 0; i < encodings.length; i++) {\n    var encoding = encodings[i];\n    var options = (0, _merge2.default)(barcodeOptions, encoding.options);\n\n    // Calculate the width of the encoding\n    var textWidth;\n    if (options.displayValue) {\n      textWidth = messureText(encoding.text, options, context);\n    } else {\n      textWidth = 0;\n    }\n    var barcodeWidth = encoding.data.length * options.width;\n    encoding.width = Math.ceil(Math.max(textWidth, barcodeWidth));\n    encoding.height = getEncodingHeight(encoding, options);\n    encoding.barcodePadding = getBarcodePadding(textWidth, barcodeWidth, options);\n  }\n}\nfunction getTotalWidthOfEncodings(encodings) {\n  var totalWidth = 0;\n  for (var i = 0; i < encodings.length; i++) {\n    totalWidth += encodings[i].width;\n  }\n  return totalWidth;\n}\nfunction getMaximumHeightOfEncodings(encodings) {\n  var maxHeight = 0;\n  for (var i = 0; i < encodings.length; i++) {\n    if (encodings[i].height > maxHeight) {\n      maxHeight = encodings[i].height;\n    }\n  }\n  return maxHeight;\n}\nfunction messureText(string, options, context) {\n  var ctx;\n  if (context) {\n    ctx = context;\n  } else if (typeof document !== \"undefined\") {\n    ctx = document.createElement(\"canvas\").getContext(\"2d\");\n  } else {\n    // If the text cannot be messured we will return 0.\n    // This will make some barcode with big text render incorrectly\n    return 0;\n  }\n  ctx.font = options.fontOptions + \" \" + options.fontSize + \"px \" + options.font;\n\n  // Calculate the width of the encoding\n  var measureTextResult = ctx.measureText(string);\n  if (!measureTextResult) {\n    // Some implementations don't implement measureText and return undefined.\n    // If the text cannot be measured we will return 0.\n    // This will make some barcode with big text render incorrectly\n    return 0;\n  }\n  var size = measureTextResult.width;\n  return size;\n}\nexports.getMaximumHeightOfEncodings = getMaximumHeightOfEncodings;\nexports.getEncodingHeight = getEncodingHeight;\nexports.getBarcodePadding = getBarcodePadding;\nexports.calculateEncodingAttributes = calculateEncodingAttributes;\nexports.getTotalWidthOfEncodings = getTotalWidthOfEncodings;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "getTotalWidthOfEncodings", "calculateEncodingAttributes", "getBarcodePadding", "getEncodingHeight", "getMaximumHeightOfEncodings", "undefined", "_merge", "require", "_merge2", "_interopRequireDefault", "obj", "__esModule", "default", "encoding", "options", "height", "displayValue", "text", "length", "fontSize", "textMargin", "marginTop", "marginBottom", "textWidth", "barcodeWidth", "textAlign", "Math", "floor", "encodings", "barcodeOptions", "context", "i", "messureText", "data", "width", "ceil", "max", "barcodePadding", "totalWidth", "maxHeight", "string", "ctx", "document", "createElement", "getContext", "font", "fontOptions", "measureTextResult", "measureText", "size"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/renderers/shared.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.getTotalWidthOfEncodings = exports.calculateEncodingAttributes = exports.getBarcodePadding = exports.getEncodingHeight = exports.getMaximumHeightOfEncodings = undefined;\n\nvar _merge = require(\"../help/merge.js\");\n\nvar _merge2 = _interopRequireDefault(_merge);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction getEncodingHeight(encoding, options) {\n\treturn options.height + (options.displayValue && encoding.text.length > 0 ? options.fontSize + options.textMargin : 0) + options.marginTop + options.marginBottom;\n}\n\nfunction getBarcodePadding(textWidth, barcodeWidth, options) {\n\tif (options.displayValue && barcodeWidth < textWidth) {\n\t\tif (options.textAlign == \"center\") {\n\t\t\treturn Math.floor((textWidth - barcodeWidth) / 2);\n\t\t} else if (options.textAlign == \"left\") {\n\t\t\treturn 0;\n\t\t} else if (options.textAlign == \"right\") {\n\t\t\treturn Math.floor(textWidth - barcodeWidth);\n\t\t}\n\t}\n\treturn 0;\n}\n\nfunction calculateEncodingAttributes(encodings, barcodeOptions, context) {\n\tfor (var i = 0; i < encodings.length; i++) {\n\t\tvar encoding = encodings[i];\n\t\tvar options = (0, _merge2.default)(barcodeOptions, encoding.options);\n\n\t\t// Calculate the width of the encoding\n\t\tvar textWidth;\n\t\tif (options.displayValue) {\n\t\t\ttextWidth = messureText(encoding.text, options, context);\n\t\t} else {\n\t\t\ttextWidth = 0;\n\t\t}\n\n\t\tvar barcodeWidth = encoding.data.length * options.width;\n\t\tencoding.width = Math.ceil(Math.max(textWidth, barcodeWidth));\n\n\t\tencoding.height = getEncodingHeight(encoding, options);\n\n\t\tencoding.barcodePadding = getBarcodePadding(textWidth, barcodeWidth, options);\n\t}\n}\n\nfunction getTotalWidthOfEncodings(encodings) {\n\tvar totalWidth = 0;\n\tfor (var i = 0; i < encodings.length; i++) {\n\t\ttotalWidth += encodings[i].width;\n\t}\n\treturn totalWidth;\n}\n\nfunction getMaximumHeightOfEncodings(encodings) {\n\tvar maxHeight = 0;\n\tfor (var i = 0; i < encodings.length; i++) {\n\t\tif (encodings[i].height > maxHeight) {\n\t\t\tmaxHeight = encodings[i].height;\n\t\t}\n\t}\n\treturn maxHeight;\n}\n\nfunction messureText(string, options, context) {\n\tvar ctx;\n\n\tif (context) {\n\t\tctx = context;\n\t} else if (typeof document !== \"undefined\") {\n\t\tctx = document.createElement(\"canvas\").getContext(\"2d\");\n\t} else {\n\t\t// If the text cannot be messured we will return 0.\n\t\t// This will make some barcode with big text render incorrectly\n\t\treturn 0;\n\t}\n\tctx.font = options.fontOptions + \" \" + options.fontSize + \"px \" + options.font;\n\n\t// Calculate the width of the encoding\n\tvar measureTextResult = ctx.measureText(string);\n\tif (!measureTextResult) {\n\t\t// Some implementations don't implement measureText and return undefined.\n\t\t// If the text cannot be measured we will return 0.\n\t\t// This will make some barcode with big text render incorrectly\n\t\treturn 0;\n\t}\n\tvar size = measureTextResult.width;\n\treturn size;\n}\n\nexports.getMaximumHeightOfEncodings = getMaximumHeightOfEncodings;\nexports.getEncodingHeight = getEncodingHeight;\nexports.getBarcodePadding = getBarcodePadding;\nexports.calculateEncodingAttributes = calculateEncodingAttributes;\nexports.getTotalWidthOfEncodings = getTotalWidthOfEncodings;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AACFD,OAAO,CAACE,wBAAwB,GAAGF,OAAO,CAACG,2BAA2B,GAAGH,OAAO,CAACI,iBAAiB,GAAGJ,OAAO,CAACK,iBAAiB,GAAGL,OAAO,CAACM,2BAA2B,GAAGC,SAAS;AAEhL,IAAIC,MAAM,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAExC,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASP,iBAAiBA,CAACU,QAAQ,EAAEC,OAAO,EAAE;EAC7C,OAAOA,OAAO,CAACC,MAAM,IAAID,OAAO,CAACE,YAAY,IAAIH,QAAQ,CAACI,IAAI,CAACC,MAAM,GAAG,CAAC,GAAGJ,OAAO,CAACK,QAAQ,GAAGL,OAAO,CAACM,UAAU,GAAG,CAAC,CAAC,GAAGN,OAAO,CAACO,SAAS,GAAGP,OAAO,CAACQ,YAAY;AAClK;AAEA,SAASpB,iBAAiBA,CAACqB,SAAS,EAAEC,YAAY,EAAEV,OAAO,EAAE;EAC5D,IAAIA,OAAO,CAACE,YAAY,IAAIQ,YAAY,GAAGD,SAAS,EAAE;IACrD,IAAIT,OAAO,CAACW,SAAS,IAAI,QAAQ,EAAE;MAClC,OAAOC,IAAI,CAACC,KAAK,CAAC,CAACJ,SAAS,GAAGC,YAAY,IAAI,CAAC,CAAC;IAClD,CAAC,MAAM,IAAIV,OAAO,CAACW,SAAS,IAAI,MAAM,EAAE;MACvC,OAAO,CAAC;IACT,CAAC,MAAM,IAAIX,OAAO,CAACW,SAAS,IAAI,OAAO,EAAE;MACxC,OAAOC,IAAI,CAACC,KAAK,CAACJ,SAAS,GAAGC,YAAY,CAAC;IAC5C;EACD;EACA,OAAO,CAAC;AACT;AAEA,SAASvB,2BAA2BA,CAAC2B,SAAS,EAAEC,cAAc,EAAEC,OAAO,EAAE;EACxE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACV,MAAM,EAAEa,CAAC,EAAE,EAAE;IAC1C,IAAIlB,QAAQ,GAAGe,SAAS,CAACG,CAAC,CAAC;IAC3B,IAAIjB,OAAO,GAAG,CAAC,CAAC,EAAEN,OAAO,CAACI,OAAO,EAAEiB,cAAc,EAAEhB,QAAQ,CAACC,OAAO,CAAC;;IAEpE;IACA,IAAIS,SAAS;IACb,IAAIT,OAAO,CAACE,YAAY,EAAE;MACzBO,SAAS,GAAGS,WAAW,CAACnB,QAAQ,CAACI,IAAI,EAAEH,OAAO,EAAEgB,OAAO,CAAC;IACzD,CAAC,MAAM;MACNP,SAAS,GAAG,CAAC;IACd;IAEA,IAAIC,YAAY,GAAGX,QAAQ,CAACoB,IAAI,CAACf,MAAM,GAAGJ,OAAO,CAACoB,KAAK;IACvDrB,QAAQ,CAACqB,KAAK,GAAGR,IAAI,CAACS,IAAI,CAACT,IAAI,CAACU,GAAG,CAACb,SAAS,EAAEC,YAAY,CAAC,CAAC;IAE7DX,QAAQ,CAACE,MAAM,GAAGZ,iBAAiB,CAACU,QAAQ,EAAEC,OAAO,CAAC;IAEtDD,QAAQ,CAACwB,cAAc,GAAGnC,iBAAiB,CAACqB,SAAS,EAAEC,YAAY,EAAEV,OAAO,CAAC;EAC9E;AACD;AAEA,SAASd,wBAAwBA,CAAC4B,SAAS,EAAE;EAC5C,IAAIU,UAAU,GAAG,CAAC;EAClB,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACV,MAAM,EAAEa,CAAC,EAAE,EAAE;IAC1CO,UAAU,IAAIV,SAAS,CAACG,CAAC,CAAC,CAACG,KAAK;EACjC;EACA,OAAOI,UAAU;AAClB;AAEA,SAASlC,2BAA2BA,CAACwB,SAAS,EAAE;EAC/C,IAAIW,SAAS,GAAG,CAAC;EACjB,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACV,MAAM,EAAEa,CAAC,EAAE,EAAE;IAC1C,IAAIH,SAAS,CAACG,CAAC,CAAC,CAAChB,MAAM,GAAGwB,SAAS,EAAE;MACpCA,SAAS,GAAGX,SAAS,CAACG,CAAC,CAAC,CAAChB,MAAM;IAChC;EACD;EACA,OAAOwB,SAAS;AACjB;AAEA,SAASP,WAAWA,CAACQ,MAAM,EAAE1B,OAAO,EAAEgB,OAAO,EAAE;EAC9C,IAAIW,GAAG;EAEP,IAAIX,OAAO,EAAE;IACZW,GAAG,GAAGX,OAAO;EACd,CAAC,MAAM,IAAI,OAAOY,QAAQ,KAAK,WAAW,EAAE;IAC3CD,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC,CAACC,UAAU,CAAC,IAAI,CAAC;EACxD,CAAC,MAAM;IACN;IACA;IACA,OAAO,CAAC;EACT;EACAH,GAAG,CAACI,IAAI,GAAG/B,OAAO,CAACgC,WAAW,GAAG,GAAG,GAAGhC,OAAO,CAACK,QAAQ,GAAG,KAAK,GAAGL,OAAO,CAAC+B,IAAI;;EAE9E;EACA,IAAIE,iBAAiB,GAAGN,GAAG,CAACO,WAAW,CAACR,MAAM,CAAC;EAC/C,IAAI,CAACO,iBAAiB,EAAE;IACvB;IACA;IACA;IACA,OAAO,CAAC;EACT;EACA,IAAIE,IAAI,GAAGF,iBAAiB,CAACb,KAAK;EAClC,OAAOe,IAAI;AACZ;AAEAnD,OAAO,CAACM,2BAA2B,GAAGA,2BAA2B;AACjEN,OAAO,CAACK,iBAAiB,GAAGA,iBAAiB;AAC7CL,OAAO,CAACI,iBAAiB,GAAGA,iBAAiB;AAC7CJ,OAAO,CAACG,2BAA2B,GAAGA,2BAA2B;AACjEH,OAAO,CAACE,wBAAwB,GAAGA,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}