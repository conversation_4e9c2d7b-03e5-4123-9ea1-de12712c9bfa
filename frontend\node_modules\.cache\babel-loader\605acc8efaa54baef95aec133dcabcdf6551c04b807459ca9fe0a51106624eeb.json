{"ast": null, "code": "/*\n * Copyright 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.detector {*/\n/**\n * General math-related and numeric utility functions.\n */\nvar MathUtils = /** @class */function () {\n  function MathUtils() {}\n  /**\n   * Ends up being a bit faster than {@link Math#round(float)}. This merely rounds its\n   * argument to the nearest int, where x.5 rounds up to x+1. Semantics of this shortcut\n   * differ slightly from {@link Math#round(float)} in that half rounds down for negative\n   * values. -2.5 rounds to -3, not -2. For purposes here it makes no difference.\n   *\n   * @param d real value to round\n   * @return nearest {@code int}\n   */\n  MathUtils.round = function (d /*float*/) {\n    if (isNaN(d)) return 0;\n    if (d <= Number.MIN_SAFE_INTEGER) return Number.MIN_SAFE_INTEGER;\n    if (d >= Number.MAX_SAFE_INTEGER) return Number.MAX_SAFE_INTEGER;\n    return /*(int) */d + (d < 0.0 ? -0.5 : 0.5) | 0;\n  };\n  // TYPESCRIPTPORT: maybe remove round method and call directly Math.round, it looks like it doesn't make sense for js\n  /**\n   * @param aX point A x coordinate\n   * @param aY point A y coordinate\n   * @param bX point B x coordinate\n   * @param bY point B y coordinate\n   * @return Euclidean distance between points A and B\n   */\n  MathUtils.distance = function (aX /*float|int*/, aY /*float|int*/, bX /*float|int*/, bY /*float|int*/) {\n    var xDiff = aX - bX;\n    var yDiff = aY - bY;\n    return /*(float) */Math.sqrt(xDiff * xDiff + yDiff * yDiff);\n  };\n  /**\n   * @param aX point A x coordinate\n   * @param aY point A y coordinate\n   * @param bX point B x coordinate\n   * @param bY point B y coordinate\n   * @return Euclidean distance between points A and B\n   */\n  // public static distance(aX: number /*int*/, aY: number /*int*/, bX: number /*int*/, bY: number /*int*/): float {\n  //   const xDiff = aX - bX\n  //   const yDiff = aY - bY\n  //   return (float) Math.sqrt(xDiff * xDiff + yDiff * yDiff);\n  // }\n  /**\n   * @param array values to sum\n   * @return sum of values in array\n   */\n  MathUtils.sum = function (array) {\n    var count = 0;\n    for (var i = 0, length_1 = array.length; i !== length_1; i++) {\n      var a = array[i];\n      count += a;\n    }\n    return count;\n  };\n  return MathUtils;\n}();\nexport default MathUtils;", "map": {"version": 3, "names": ["MathUtils", "round", "d", "isNaN", "Number", "MIN_SAFE_INTEGER", "MAX_SAFE_INTEGER", "distance", "aX", "aY", "bX", "bY", "xDiff", "yDiff", "Math", "sqrt", "sum", "array", "count", "i", "length_1", "length", "a"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/detector/MathUtils.js"], "sourcesContent": ["/*\n * Copyright 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.detector {*/\n/**\n * General math-related and numeric utility functions.\n */\nvar MathUtils = /** @class */ (function () {\n    function MathUtils() {\n    }\n    /**\n     * Ends up being a bit faster than {@link Math#round(float)}. This merely rounds its\n     * argument to the nearest int, where x.5 rounds up to x+1. Semantics of this shortcut\n     * differ slightly from {@link Math#round(float)} in that half rounds down for negative\n     * values. -2.5 rounds to -3, not -2. For purposes here it makes no difference.\n     *\n     * @param d real value to round\n     * @return nearest {@code int}\n     */\n    MathUtils.round = function (d /*float*/) {\n        if (isNaN(d))\n            return 0;\n        if (d <= Number.MIN_SAFE_INTEGER)\n            return Number.MIN_SAFE_INTEGER;\n        if (d >= Number.MAX_SAFE_INTEGER)\n            return Number.MAX_SAFE_INTEGER;\n        return /*(int) */ (d + (d < 0.0 ? -0.5 : 0.5)) | 0;\n    };\n    // TYPESCRIPTPORT: maybe remove round method and call directly Math.round, it looks like it doesn't make sense for js\n    /**\n     * @param aX point A x coordinate\n     * @param aY point A y coordinate\n     * @param bX point B x coordinate\n     * @param bY point B y coordinate\n     * @return Euclidean distance between points A and B\n     */\n    MathUtils.distance = function (aX /*float|int*/, aY /*float|int*/, bX /*float|int*/, bY /*float|int*/) {\n        var xDiff = aX - bX;\n        var yDiff = aY - bY;\n        return /*(float) */ Math.sqrt(xDiff * xDiff + yDiff * yDiff);\n    };\n    /**\n     * @param aX point A x coordinate\n     * @param aY point A y coordinate\n     * @param bX point B x coordinate\n     * @param bY point B y coordinate\n     * @return Euclidean distance between points A and B\n     */\n    // public static distance(aX: number /*int*/, aY: number /*int*/, bX: number /*int*/, bY: number /*int*/): float {\n    //   const xDiff = aX - bX\n    //   const yDiff = aY - bY\n    //   return (float) Math.sqrt(xDiff * xDiff + yDiff * yDiff);\n    // }\n    /**\n     * @param array values to sum\n     * @return sum of values in array\n     */\n    MathUtils.sum = function (array) {\n        var count = 0;\n        for (var i = 0, length_1 = array.length; i !== length_1; i++) {\n            var a = array[i];\n            count += a;\n        }\n        return count;\n    };\n    return MathUtils;\n}());\nexport default MathUtils;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAAA,EAAG,CACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,SAAS,CAACC,KAAK,GAAG,UAAUC,CAAC,CAAC,WAAW;IACrC,IAAIC,KAAK,CAACD,CAAC,CAAC,EACR,OAAO,CAAC;IACZ,IAAIA,CAAC,IAAIE,MAAM,CAACC,gBAAgB,EAC5B,OAAOD,MAAM,CAACC,gBAAgB;IAClC,IAAIH,CAAC,IAAIE,MAAM,CAACE,gBAAgB,EAC5B,OAAOF,MAAM,CAACE,gBAAgB;IAClC,OAAO,UAAYJ,CAAC,IAAIA,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAI,CAAC;EACtD,CAAC;EACD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIF,SAAS,CAACO,QAAQ,GAAG,UAAUC,EAAE,CAAC,eAAeC,EAAE,CAAC,eAAeC,EAAE,CAAC,eAAeC,EAAE,CAAC,eAAe;IACnG,IAAIC,KAAK,GAAGJ,EAAE,GAAGE,EAAE;IACnB,IAAIG,KAAK,GAAGJ,EAAE,GAAGE,EAAE;IACnB,OAAO,YAAaG,IAAI,CAACC,IAAI,CAACH,KAAK,GAAGA,KAAK,GAAGC,KAAK,GAAGA,KAAK,CAAC;EAChE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;EACIb,SAAS,CAACgB,GAAG,GAAG,UAAUC,KAAK,EAAE;IAC7B,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,QAAQ,GAAGH,KAAK,CAACI,MAAM,EAAEF,CAAC,KAAKC,QAAQ,EAAED,CAAC,EAAE,EAAE;MAC1D,IAAIG,CAAC,GAAGL,KAAK,CAACE,CAAC,CAAC;MAChBD,KAAK,IAAII,CAAC;IACd;IACA,OAAOJ,KAAK;EAChB,CAAC;EACD,OAAOlB,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}