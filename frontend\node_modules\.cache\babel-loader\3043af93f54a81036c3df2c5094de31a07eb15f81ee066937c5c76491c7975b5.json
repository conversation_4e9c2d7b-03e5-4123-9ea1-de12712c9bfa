{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.common {*/\nimport FormatException from '../FormatException';\n/*import java.util.HashMap;*/\n/*import java.util.Map;*/\nexport var CharacterSetValueIdentifiers;\n(function (CharacterSetValueIdentifiers) {\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp437\"] = 0] = \"Cp437\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_1\"] = 1] = \"ISO8859_1\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_2\"] = 2] = \"ISO8859_2\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_3\"] = 3] = \"ISO8859_3\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_4\"] = 4] = \"ISO8859_4\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_5\"] = 5] = \"ISO8859_5\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_6\"] = 6] = \"ISO8859_6\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_7\"] = 7] = \"ISO8859_7\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_8\"] = 8] = \"ISO8859_8\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_9\"] = 9] = \"ISO8859_9\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_10\"] = 10] = \"ISO8859_10\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_11\"] = 11] = \"ISO8859_11\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_13\"] = 12] = \"ISO8859_13\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_14\"] = 13] = \"ISO8859_14\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_15\"] = 14] = \"ISO8859_15\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_16\"] = 15] = \"ISO8859_16\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"SJIS\"] = 16] = \"SJIS\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1250\"] = 17] = \"Cp1250\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1251\"] = 18] = \"Cp1251\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1252\"] = 19] = \"Cp1252\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1256\"] = 20] = \"Cp1256\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"UnicodeBigUnmarked\"] = 21] = \"UnicodeBigUnmarked\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"UTF8\"] = 22] = \"UTF8\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ASCII\"] = 23] = \"ASCII\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Big5\"] = 24] = \"Big5\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"GB18030\"] = 25] = \"GB18030\";\n  CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"EUC_KR\"] = 26] = \"EUC_KR\";\n})(CharacterSetValueIdentifiers || (CharacterSetValueIdentifiers = {}));\n/**\n * Encapsulates a Character Set ECI, according to \"Extended Channel Interpretations\" 5.3.1.1\n * of ISO 18004.\n *\n * <AUTHOR> Owen\n */\nvar CharacterSetECI = /** @class */function () {\n  function CharacterSetECI(valueIdentifier, valuesParam, name) {\n    var e_1, _a;\n    var otherEncodingNames = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n      otherEncodingNames[_i - 3] = arguments[_i];\n    }\n    this.valueIdentifier = valueIdentifier;\n    this.name = name;\n    if (typeof valuesParam === 'number') {\n      this.values = Int32Array.from([valuesParam]);\n    } else {\n      this.values = valuesParam;\n    }\n    this.otherEncodingNames = otherEncodingNames;\n    CharacterSetECI.VALUE_IDENTIFIER_TO_ECI.set(valueIdentifier, this);\n    CharacterSetECI.NAME_TO_ECI.set(name, this);\n    var values = this.values;\n    for (var i = 0, length_1 = values.length; i !== length_1; i++) {\n      var v = values[i];\n      CharacterSetECI.VALUES_TO_ECI.set(v, this);\n    }\n    try {\n      for (var otherEncodingNames_1 = __values(otherEncodingNames), otherEncodingNames_1_1 = otherEncodingNames_1.next(); !otherEncodingNames_1_1.done; otherEncodingNames_1_1 = otherEncodingNames_1.next()) {\n        var otherName = otherEncodingNames_1_1.value;\n        CharacterSetECI.NAME_TO_ECI.set(otherName, this);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (otherEncodingNames_1_1 && !otherEncodingNames_1_1.done && (_a = otherEncodingNames_1.return)) _a.call(otherEncodingNames_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n  }\n  // CharacterSetECI(value: number /*int*/) {\n  //   this(new Int32Array {value})\n  // }\n  // CharacterSetECI(value: number /*int*/, String... otherEncodingNames) {\n  //   this.values = new Int32Array {value}\n  //   this.otherEncodingNames = otherEncodingNames\n  // }\n  // CharacterSetECI(values: Int32Array, String... otherEncodingNames) {\n  //   this.values = values\n  //   this.otherEncodingNames = otherEncodingNames\n  // }\n  CharacterSetECI.prototype.getValueIdentifier = function () {\n    return this.valueIdentifier;\n  };\n  CharacterSetECI.prototype.getName = function () {\n    return this.name;\n  };\n  CharacterSetECI.prototype.getValue = function () {\n    return this.values[0];\n  };\n  /**\n   * @param value character set ECI value\n   * @return {@code CharacterSetECI} representing ECI of given value, or null if it is legal but\n   *   unsupported\n   * @throws FormatException if ECI value is invalid\n   */\n  CharacterSetECI.getCharacterSetECIByValue = function (value /*int*/) {\n    if (value < 0 || value >= 900) {\n      throw new FormatException('incorect value');\n    }\n    var characterSet = CharacterSetECI.VALUES_TO_ECI.get(value);\n    if (undefined === characterSet) {\n      throw new FormatException('incorect value');\n    }\n    return characterSet;\n  };\n  /**\n   * @param name character set ECI encoding name\n   * @return CharacterSetECI representing ECI for character encoding, or null if it is legal\n   *   but unsupported\n   */\n  CharacterSetECI.getCharacterSetECIByName = function (name) {\n    var characterSet = CharacterSetECI.NAME_TO_ECI.get(name);\n    if (undefined === characterSet) {\n      throw new FormatException('incorect value');\n    }\n    return characterSet;\n  };\n  CharacterSetECI.prototype.equals = function (o) {\n    if (!(o instanceof CharacterSetECI)) {\n      return false;\n    }\n    var other = o;\n    return this.getName() === other.getName();\n  };\n  CharacterSetECI.VALUE_IDENTIFIER_TO_ECI = new Map();\n  CharacterSetECI.VALUES_TO_ECI = new Map();\n  CharacterSetECI.NAME_TO_ECI = new Map();\n  // Enum name is a Java encoding valid for java.lang and java.io\n  // TYPESCRIPTPORT: changed the main label for ISO as the TextEncoder did not recognized them in the form from java\n  // (eg ISO8859_1 must be ISO88591 or ISO8859-1 or ISO-8859-1)\n  // later on: well, except 16 wich does not work with ISO885916 so used ISO-8859-1 form for default\n  CharacterSetECI.Cp437 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp437, Int32Array.from([0, 2]), 'Cp437');\n  CharacterSetECI.ISO8859_1 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_1, Int32Array.from([1, 3]), 'ISO-8859-1', 'ISO88591', 'ISO8859_1');\n  CharacterSetECI.ISO8859_2 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_2, 4, 'ISO-8859-2', 'ISO88592', 'ISO8859_2');\n  CharacterSetECI.ISO8859_3 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_3, 5, 'ISO-8859-3', 'ISO88593', 'ISO8859_3');\n  CharacterSetECI.ISO8859_4 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_4, 6, 'ISO-8859-4', 'ISO88594', 'ISO8859_4');\n  CharacterSetECI.ISO8859_5 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_5, 7, 'ISO-8859-5', 'ISO88595', 'ISO8859_5');\n  CharacterSetECI.ISO8859_6 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_6, 8, 'ISO-8859-6', 'ISO88596', 'ISO8859_6');\n  CharacterSetECI.ISO8859_7 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_7, 9, 'ISO-8859-7', 'ISO88597', 'ISO8859_7');\n  CharacterSetECI.ISO8859_8 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_8, 10, 'ISO-8859-8', 'ISO88598', 'ISO8859_8');\n  CharacterSetECI.ISO8859_9 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_9, 11, 'ISO-8859-9', 'ISO88599', 'ISO8859_9');\n  CharacterSetECI.ISO8859_10 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_10, 12, 'ISO-8859-10', 'ISO885910', 'ISO8859_10');\n  CharacterSetECI.ISO8859_11 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_11, 13, 'ISO-8859-11', 'ISO885911', 'ISO8859_11');\n  CharacterSetECI.ISO8859_13 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_13, 15, 'ISO-8859-13', 'ISO885913', 'ISO8859_13');\n  CharacterSetECI.ISO8859_14 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_14, 16, 'ISO-8859-14', 'ISO885914', 'ISO8859_14');\n  CharacterSetECI.ISO8859_15 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_15, 17, 'ISO-8859-15', 'ISO885915', 'ISO8859_15');\n  CharacterSetECI.ISO8859_16 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_16, 18, 'ISO-8859-16', 'ISO885916', 'ISO8859_16');\n  CharacterSetECI.SJIS = new CharacterSetECI(CharacterSetValueIdentifiers.SJIS, 20, 'SJIS', 'Shift_JIS');\n  CharacterSetECI.Cp1250 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1250, 21, 'Cp1250', 'windows-1250');\n  CharacterSetECI.Cp1251 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1251, 22, 'Cp1251', 'windows-1251');\n  CharacterSetECI.Cp1252 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1252, 23, 'Cp1252', 'windows-1252');\n  CharacterSetECI.Cp1256 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1256, 24, 'Cp1256', 'windows-1256');\n  CharacterSetECI.UnicodeBigUnmarked = new CharacterSetECI(CharacterSetValueIdentifiers.UnicodeBigUnmarked, 25, 'UnicodeBigUnmarked', 'UTF-16BE', 'UnicodeBig');\n  CharacterSetECI.UTF8 = new CharacterSetECI(CharacterSetValueIdentifiers.UTF8, 26, 'UTF8', 'UTF-8');\n  CharacterSetECI.ASCII = new CharacterSetECI(CharacterSetValueIdentifiers.ASCII, Int32Array.from([27, 170]), 'ASCII', 'US-ASCII');\n  CharacterSetECI.Big5 = new CharacterSetECI(CharacterSetValueIdentifiers.Big5, 28, 'Big5');\n  CharacterSetECI.GB18030 = new CharacterSetECI(CharacterSetValueIdentifiers.GB18030, 29, 'GB18030', 'GB2312', 'EUC_CN', 'GBK');\n  CharacterSetECI.EUC_KR = new CharacterSetECI(CharacterSetValueIdentifiers.EUC_KR, 30, 'EUC_KR', 'EUC-KR');\n  return CharacterSetECI;\n}();\nexport default CharacterSetECI;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "FormatException", "CharacterSetValueIdentifiers", "CharacterSetECI", "valueIdentifier", "valuesParam", "name", "e_1", "_a", "otherEncodingNames", "_i", "arguments", "values", "Int32Array", "from", "VALUE_IDENTIFIER_TO_ECI", "set", "NAME_TO_ECI", "length_1", "v", "VALUES_TO_ECI", "otherEncodingNames_1", "otherEncodingNames_1_1", "otherName", "e_1_1", "error", "return", "prototype", "getValueIdentifier", "getName", "getValue", "getCharacterSetECIByValue", "characterSet", "get", "undefined", "getCharacterSetECIByName", "equals", "other", "Map", "Cp437", "ISO8859_1", "ISO8859_2", "ISO8859_3", "ISO8859_4", "ISO8859_5", "ISO8859_6", "ISO8859_7", "ISO8859_8", "ISO8859_9", "ISO8859_10", "ISO8859_11", "ISO8859_13", "ISO8859_14", "ISO8859_15", "ISO8859_16", "SJIS", "Cp1250", "Cp1251", "Cp1252", "Cp1256", "UnicodeBigUnmarked", "UTF8", "ASCII", "Big5", "GB18030", "EUC_KR"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/CharacterSetECI.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.common {*/\nimport FormatException from '../FormatException';\n/*import java.util.HashMap;*/\n/*import java.util.Map;*/\nexport var CharacterSetValueIdentifiers;\n(function (CharacterSetValueIdentifiers) {\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp437\"] = 0] = \"Cp437\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_1\"] = 1] = \"ISO8859_1\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_2\"] = 2] = \"ISO8859_2\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_3\"] = 3] = \"ISO8859_3\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_4\"] = 4] = \"ISO8859_4\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_5\"] = 5] = \"ISO8859_5\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_6\"] = 6] = \"ISO8859_6\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_7\"] = 7] = \"ISO8859_7\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_8\"] = 8] = \"ISO8859_8\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_9\"] = 9] = \"ISO8859_9\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_10\"] = 10] = \"ISO8859_10\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_11\"] = 11] = \"ISO8859_11\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_13\"] = 12] = \"ISO8859_13\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_14\"] = 13] = \"ISO8859_14\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_15\"] = 14] = \"ISO8859_15\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ISO8859_16\"] = 15] = \"ISO8859_16\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"SJIS\"] = 16] = \"SJIS\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1250\"] = 17] = \"Cp1250\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1251\"] = 18] = \"Cp1251\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1252\"] = 19] = \"Cp1252\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Cp1256\"] = 20] = \"Cp1256\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"UnicodeBigUnmarked\"] = 21] = \"UnicodeBigUnmarked\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"UTF8\"] = 22] = \"UTF8\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"ASCII\"] = 23] = \"ASCII\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"Big5\"] = 24] = \"Big5\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"GB18030\"] = 25] = \"GB18030\";\n    CharacterSetValueIdentifiers[CharacterSetValueIdentifiers[\"EUC_KR\"] = 26] = \"EUC_KR\";\n})(CharacterSetValueIdentifiers || (CharacterSetValueIdentifiers = {}));\n/**\n * Encapsulates a Character Set ECI, according to \"Extended Channel Interpretations\" 5.3.1.1\n * of ISO 18004.\n *\n * <AUTHOR> Owen\n */\nvar CharacterSetECI = /** @class */ (function () {\n    function CharacterSetECI(valueIdentifier, valuesParam, name) {\n        var e_1, _a;\n        var otherEncodingNames = [];\n        for (var _i = 3; _i < arguments.length; _i++) {\n            otherEncodingNames[_i - 3] = arguments[_i];\n        }\n        this.valueIdentifier = valueIdentifier;\n        this.name = name;\n        if (typeof valuesParam === 'number') {\n            this.values = Int32Array.from([valuesParam]);\n        }\n        else {\n            this.values = valuesParam;\n        }\n        this.otherEncodingNames = otherEncodingNames;\n        CharacterSetECI.VALUE_IDENTIFIER_TO_ECI.set(valueIdentifier, this);\n        CharacterSetECI.NAME_TO_ECI.set(name, this);\n        var values = this.values;\n        for (var i = 0, length_1 = values.length; i !== length_1; i++) {\n            var v = values[i];\n            CharacterSetECI.VALUES_TO_ECI.set(v, this);\n        }\n        try {\n            for (var otherEncodingNames_1 = __values(otherEncodingNames), otherEncodingNames_1_1 = otherEncodingNames_1.next(); !otherEncodingNames_1_1.done; otherEncodingNames_1_1 = otherEncodingNames_1.next()) {\n                var otherName = otherEncodingNames_1_1.value;\n                CharacterSetECI.NAME_TO_ECI.set(otherName, this);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (otherEncodingNames_1_1 && !otherEncodingNames_1_1.done && (_a = otherEncodingNames_1.return)) _a.call(otherEncodingNames_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    }\n    // CharacterSetECI(value: number /*int*/) {\n    //   this(new Int32Array {value})\n    // }\n    // CharacterSetECI(value: number /*int*/, String... otherEncodingNames) {\n    //   this.values = new Int32Array {value}\n    //   this.otherEncodingNames = otherEncodingNames\n    // }\n    // CharacterSetECI(values: Int32Array, String... otherEncodingNames) {\n    //   this.values = values\n    //   this.otherEncodingNames = otherEncodingNames\n    // }\n    CharacterSetECI.prototype.getValueIdentifier = function () {\n        return this.valueIdentifier;\n    };\n    CharacterSetECI.prototype.getName = function () {\n        return this.name;\n    };\n    CharacterSetECI.prototype.getValue = function () {\n        return this.values[0];\n    };\n    /**\n     * @param value character set ECI value\n     * @return {@code CharacterSetECI} representing ECI of given value, or null if it is legal but\n     *   unsupported\n     * @throws FormatException if ECI value is invalid\n     */\n    CharacterSetECI.getCharacterSetECIByValue = function (value /*int*/) {\n        if (value < 0 || value >= 900) {\n            throw new FormatException('incorect value');\n        }\n        var characterSet = CharacterSetECI.VALUES_TO_ECI.get(value);\n        if (undefined === characterSet) {\n            throw new FormatException('incorect value');\n        }\n        return characterSet;\n    };\n    /**\n     * @param name character set ECI encoding name\n     * @return CharacterSetECI representing ECI for character encoding, or null if it is legal\n     *   but unsupported\n     */\n    CharacterSetECI.getCharacterSetECIByName = function (name) {\n        var characterSet = CharacterSetECI.NAME_TO_ECI.get(name);\n        if (undefined === characterSet) {\n            throw new FormatException('incorect value');\n        }\n        return characterSet;\n    };\n    CharacterSetECI.prototype.equals = function (o) {\n        if (!(o instanceof CharacterSetECI)) {\n            return false;\n        }\n        var other = o;\n        return this.getName() === other.getName();\n    };\n    CharacterSetECI.VALUE_IDENTIFIER_TO_ECI = new Map();\n    CharacterSetECI.VALUES_TO_ECI = new Map();\n    CharacterSetECI.NAME_TO_ECI = new Map();\n    // Enum name is a Java encoding valid for java.lang and java.io\n    // TYPESCRIPTPORT: changed the main label for ISO as the TextEncoder did not recognized them in the form from java\n    // (eg ISO8859_1 must be ISO88591 or ISO8859-1 or ISO-8859-1)\n    // later on: well, except 16 wich does not work with ISO885916 so used ISO-8859-1 form for default\n    CharacterSetECI.Cp437 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp437, Int32Array.from([0, 2]), 'Cp437');\n    CharacterSetECI.ISO8859_1 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_1, Int32Array.from([1, 3]), 'ISO-8859-1', 'ISO88591', 'ISO8859_1');\n    CharacterSetECI.ISO8859_2 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_2, 4, 'ISO-8859-2', 'ISO88592', 'ISO8859_2');\n    CharacterSetECI.ISO8859_3 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_3, 5, 'ISO-8859-3', 'ISO88593', 'ISO8859_3');\n    CharacterSetECI.ISO8859_4 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_4, 6, 'ISO-8859-4', 'ISO88594', 'ISO8859_4');\n    CharacterSetECI.ISO8859_5 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_5, 7, 'ISO-8859-5', 'ISO88595', 'ISO8859_5');\n    CharacterSetECI.ISO8859_6 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_6, 8, 'ISO-8859-6', 'ISO88596', 'ISO8859_6');\n    CharacterSetECI.ISO8859_7 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_7, 9, 'ISO-8859-7', 'ISO88597', 'ISO8859_7');\n    CharacterSetECI.ISO8859_8 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_8, 10, 'ISO-8859-8', 'ISO88598', 'ISO8859_8');\n    CharacterSetECI.ISO8859_9 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_9, 11, 'ISO-8859-9', 'ISO88599', 'ISO8859_9');\n    CharacterSetECI.ISO8859_10 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_10, 12, 'ISO-8859-10', 'ISO885910', 'ISO8859_10');\n    CharacterSetECI.ISO8859_11 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_11, 13, 'ISO-8859-11', 'ISO885911', 'ISO8859_11');\n    CharacterSetECI.ISO8859_13 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_13, 15, 'ISO-8859-13', 'ISO885913', 'ISO8859_13');\n    CharacterSetECI.ISO8859_14 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_14, 16, 'ISO-8859-14', 'ISO885914', 'ISO8859_14');\n    CharacterSetECI.ISO8859_15 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_15, 17, 'ISO-8859-15', 'ISO885915', 'ISO8859_15');\n    CharacterSetECI.ISO8859_16 = new CharacterSetECI(CharacterSetValueIdentifiers.ISO8859_16, 18, 'ISO-8859-16', 'ISO885916', 'ISO8859_16');\n    CharacterSetECI.SJIS = new CharacterSetECI(CharacterSetValueIdentifiers.SJIS, 20, 'SJIS', 'Shift_JIS');\n    CharacterSetECI.Cp1250 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1250, 21, 'Cp1250', 'windows-1250');\n    CharacterSetECI.Cp1251 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1251, 22, 'Cp1251', 'windows-1251');\n    CharacterSetECI.Cp1252 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1252, 23, 'Cp1252', 'windows-1252');\n    CharacterSetECI.Cp1256 = new CharacterSetECI(CharacterSetValueIdentifiers.Cp1256, 24, 'Cp1256', 'windows-1256');\n    CharacterSetECI.UnicodeBigUnmarked = new CharacterSetECI(CharacterSetValueIdentifiers.UnicodeBigUnmarked, 25, 'UnicodeBigUnmarked', 'UTF-16BE', 'UnicodeBig');\n    CharacterSetECI.UTF8 = new CharacterSetECI(CharacterSetValueIdentifiers.UTF8, 26, 'UTF8', 'UTF-8');\n    CharacterSetECI.ASCII = new CharacterSetECI(CharacterSetValueIdentifiers.ASCII, Int32Array.from([27, 170]), 'ASCII', 'US-ASCII');\n    CharacterSetECI.Big5 = new CharacterSetECI(CharacterSetValueIdentifiers.Big5, 28, 'Big5');\n    CharacterSetECI.GB18030 = new CharacterSetECI(CharacterSetValueIdentifiers.GB18030, 29, 'GB18030', 'GB2312', 'EUC_CN', 'GBK');\n    CharacterSetECI.EUC_KR = new CharacterSetECI(CharacterSetValueIdentifiers.EUC_KR, 30, 'EUC_KR', 'EUC-KR');\n    return CharacterSetECI;\n}());\nexport default CharacterSetECI;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,eAAe,MAAM,oBAAoB;AAChD;AACA;AACA,OAAO,IAAIC,4BAA4B;AACvC,CAAC,UAAUA,4BAA4B,EAAE;EACrCA,4BAA4B,CAACA,4BAA4B,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACjFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzFA,4BAA4B,CAACA,4BAA4B,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAC5FA,4BAA4B,CAACA,4BAA4B,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAC5FA,4BAA4B,CAACA,4BAA4B,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAC5FA,4BAA4B,CAACA,4BAA4B,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAC5FA,4BAA4B,CAACA,4BAA4B,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAC5FA,4BAA4B,CAACA,4BAA4B,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAC5FA,4BAA4B,CAACA,4BAA4B,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM;EAChFA,4BAA4B,CAACA,4BAA4B,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ;EACpFA,4BAA4B,CAACA,4BAA4B,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ;EACpFA,4BAA4B,CAACA,4BAA4B,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ;EACpFA,4BAA4B,CAACA,4BAA4B,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ;EACpFA,4BAA4B,CAACA,4BAA4B,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,GAAG,oBAAoB;EAC5GA,4BAA4B,CAACA,4BAA4B,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM;EAChFA,4BAA4B,CAACA,4BAA4B,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EAClFA,4BAA4B,CAACA,4BAA4B,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM;EAChFA,4BAA4B,CAACA,4BAA4B,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS;EACtFA,4BAA4B,CAACA,4BAA4B,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ;AACxF,CAAC,EAAEA,4BAA4B,KAAKA,4BAA4B,GAAG,CAAC,CAAC,CAAC,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C,SAASA,eAAeA,CAACC,eAAe,EAAEC,WAAW,EAAEC,IAAI,EAAE;IACzD,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIC,kBAAkB,GAAG,EAAE;IAC3B,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACf,MAAM,EAAEc,EAAE,EAAE,EAAE;MAC1CD,kBAAkB,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC9C;IACA,IAAI,CAACN,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACE,IAAI,GAAGA,IAAI;IAChB,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;MACjC,IAAI,CAACO,MAAM,GAAGC,UAAU,CAACC,IAAI,CAAC,CAACT,WAAW,CAAC,CAAC;IAChD,CAAC,MACI;MACD,IAAI,CAACO,MAAM,GAAGP,WAAW;IAC7B;IACA,IAAI,CAACI,kBAAkB,GAAGA,kBAAkB;IAC5CN,eAAe,CAACY,uBAAuB,CAACC,GAAG,CAACZ,eAAe,EAAE,IAAI,CAAC;IAClED,eAAe,CAACc,WAAW,CAACD,GAAG,CAACV,IAAI,EAAE,IAAI,CAAC;IAC3C,IAAIM,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEwB,QAAQ,GAAGN,MAAM,CAAChB,MAAM,EAAEF,CAAC,KAAKwB,QAAQ,EAAExB,CAAC,EAAE,EAAE;MAC3D,IAAIyB,CAAC,GAAGP,MAAM,CAAClB,CAAC,CAAC;MACjBS,eAAe,CAACiB,aAAa,CAACJ,GAAG,CAACG,CAAC,EAAE,IAAI,CAAC;IAC9C;IACA,IAAI;MACA,KAAK,IAAIE,oBAAoB,GAAGjC,QAAQ,CAACqB,kBAAkB,CAAC,EAAEa,sBAAsB,GAAGD,oBAAoB,CAACxB,IAAI,CAAC,CAAC,EAAE,CAACyB,sBAAsB,CAACvB,IAAI,EAAEuB,sBAAsB,GAAGD,oBAAoB,CAACxB,IAAI,CAAC,CAAC,EAAE;QACpM,IAAI0B,SAAS,GAAGD,sBAAsB,CAACxB,KAAK;QAC5CK,eAAe,CAACc,WAAW,CAACD,GAAG,CAACO,SAAS,EAAE,IAAI,CAAC;MACpD;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEjB,GAAG,GAAG;QAAEkB,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,sBAAsB,IAAI,CAACA,sBAAsB,CAACvB,IAAI,KAAKS,EAAE,GAAGa,oBAAoB,CAACK,MAAM,CAAC,EAAElB,EAAE,CAACb,IAAI,CAAC0B,oBAAoB,CAAC;MACnI,CAAC,SACO;QAAE,IAAId,GAAG,EAAE,MAAMA,GAAG,CAACkB,KAAK;MAAE;IACxC;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAtB,eAAe,CAACwB,SAAS,CAACC,kBAAkB,GAAG,YAAY;IACvD,OAAO,IAAI,CAACxB,eAAe;EAC/B,CAAC;EACDD,eAAe,CAACwB,SAAS,CAACE,OAAO,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACvB,IAAI;EACpB,CAAC;EACDH,eAAe,CAACwB,SAAS,CAACG,QAAQ,GAAG,YAAY;IAC7C,OAAO,IAAI,CAAClB,MAAM,CAAC,CAAC,CAAC;EACzB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIT,eAAe,CAAC4B,yBAAyB,GAAG,UAAUjC,KAAK,CAAC,SAAS;IACjE,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,GAAG,EAAE;MAC3B,MAAM,IAAIG,eAAe,CAAC,gBAAgB,CAAC;IAC/C;IACA,IAAI+B,YAAY,GAAG7B,eAAe,CAACiB,aAAa,CAACa,GAAG,CAACnC,KAAK,CAAC;IAC3D,IAAIoC,SAAS,KAAKF,YAAY,EAAE;MAC5B,MAAM,IAAI/B,eAAe,CAAC,gBAAgB,CAAC;IAC/C;IACA,OAAO+B,YAAY;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI7B,eAAe,CAACgC,wBAAwB,GAAG,UAAU7B,IAAI,EAAE;IACvD,IAAI0B,YAAY,GAAG7B,eAAe,CAACc,WAAW,CAACgB,GAAG,CAAC3B,IAAI,CAAC;IACxD,IAAI4B,SAAS,KAAKF,YAAY,EAAE;MAC5B,MAAM,IAAI/B,eAAe,CAAC,gBAAgB,CAAC;IAC/C;IACA,OAAO+B,YAAY;EACvB,CAAC;EACD7B,eAAe,CAACwB,SAAS,CAACS,MAAM,GAAG,UAAU/C,CAAC,EAAE;IAC5C,IAAI,EAAEA,CAAC,YAAYc,eAAe,CAAC,EAAE;MACjC,OAAO,KAAK;IAChB;IACA,IAAIkC,KAAK,GAAGhD,CAAC;IACb,OAAO,IAAI,CAACwC,OAAO,CAAC,CAAC,KAAKQ,KAAK,CAACR,OAAO,CAAC,CAAC;EAC7C,CAAC;EACD1B,eAAe,CAACY,uBAAuB,GAAG,IAAIuB,GAAG,CAAC,CAAC;EACnDnC,eAAe,CAACiB,aAAa,GAAG,IAAIkB,GAAG,CAAC,CAAC;EACzCnC,eAAe,CAACc,WAAW,GAAG,IAAIqB,GAAG,CAAC,CAAC;EACvC;EACA;EACA;EACA;EACAnC,eAAe,CAACoC,KAAK,GAAG,IAAIpC,eAAe,CAACD,4BAA4B,CAACqC,KAAK,EAAE1B,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;EACjHX,eAAe,CAACqC,SAAS,GAAG,IAAIrC,eAAe,CAACD,4BAA4B,CAACsC,SAAS,EAAE3B,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EACvJX,eAAe,CAACsC,SAAS,GAAG,IAAItC,eAAe,CAACD,4BAA4B,CAACuC,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EACjItC,eAAe,CAACuC,SAAS,GAAG,IAAIvC,eAAe,CAACD,4BAA4B,CAACwC,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EACjIvC,eAAe,CAACwC,SAAS,GAAG,IAAIxC,eAAe,CAACD,4BAA4B,CAACyC,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EACjIxC,eAAe,CAACyC,SAAS,GAAG,IAAIzC,eAAe,CAACD,4BAA4B,CAAC0C,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EACjIzC,eAAe,CAAC0C,SAAS,GAAG,IAAI1C,eAAe,CAACD,4BAA4B,CAAC2C,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EACjI1C,eAAe,CAAC2C,SAAS,GAAG,IAAI3C,eAAe,CAACD,4BAA4B,CAAC4C,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EACjI3C,eAAe,CAAC4C,SAAS,GAAG,IAAI5C,eAAe,CAACD,4BAA4B,CAAC6C,SAAS,EAAE,EAAE,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EAClI5C,eAAe,CAAC6C,SAAS,GAAG,IAAI7C,eAAe,CAACD,4BAA4B,CAAC8C,SAAS,EAAE,EAAE,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;EAClI7C,eAAe,CAAC8C,UAAU,GAAG,IAAI9C,eAAe,CAACD,4BAA4B,CAAC+C,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC;EACvI9C,eAAe,CAAC+C,UAAU,GAAG,IAAI/C,eAAe,CAACD,4BAA4B,CAACgD,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC;EACvI/C,eAAe,CAACgD,UAAU,GAAG,IAAIhD,eAAe,CAACD,4BAA4B,CAACiD,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC;EACvIhD,eAAe,CAACiD,UAAU,GAAG,IAAIjD,eAAe,CAACD,4BAA4B,CAACkD,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC;EACvIjD,eAAe,CAACkD,UAAU,GAAG,IAAIlD,eAAe,CAACD,4BAA4B,CAACmD,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC;EACvIlD,eAAe,CAACmD,UAAU,GAAG,IAAInD,eAAe,CAACD,4BAA4B,CAACoD,UAAU,EAAE,EAAE,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC;EACvInD,eAAe,CAACoD,IAAI,GAAG,IAAIpD,eAAe,CAACD,4BAA4B,CAACqD,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,WAAW,CAAC;EACtGpD,eAAe,CAACqD,MAAM,GAAG,IAAIrD,eAAe,CAACD,4BAA4B,CAACsD,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC;EAC/GrD,eAAe,CAACsD,MAAM,GAAG,IAAItD,eAAe,CAACD,4BAA4B,CAACuD,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC;EAC/GtD,eAAe,CAACuD,MAAM,GAAG,IAAIvD,eAAe,CAACD,4BAA4B,CAACwD,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC;EAC/GvD,eAAe,CAACwD,MAAM,GAAG,IAAIxD,eAAe,CAACD,4BAA4B,CAACyD,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC;EAC/GxD,eAAe,CAACyD,kBAAkB,GAAG,IAAIzD,eAAe,CAACD,4BAA4B,CAAC0D,kBAAkB,EAAE,EAAE,EAAE,oBAAoB,EAAE,UAAU,EAAE,YAAY,CAAC;EAC7JzD,eAAe,CAAC0D,IAAI,GAAG,IAAI1D,eAAe,CAACD,4BAA4B,CAAC2D,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC;EAClG1D,eAAe,CAAC2D,KAAK,GAAG,IAAI3D,eAAe,CAACD,4BAA4B,CAAC4D,KAAK,EAAEjD,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,UAAU,CAAC;EAChIX,eAAe,CAAC4D,IAAI,GAAG,IAAI5D,eAAe,CAACD,4BAA4B,CAAC6D,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC;EACzF5D,eAAe,CAAC6D,OAAO,GAAG,IAAI7D,eAAe,CAACD,4BAA4B,CAAC8D,OAAO,EAAE,EAAE,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC;EAC7H7D,eAAe,CAAC8D,MAAM,GAAG,IAAI9D,eAAe,CAACD,4BAA4B,CAAC+D,MAAM,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACzG,OAAO9D,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}