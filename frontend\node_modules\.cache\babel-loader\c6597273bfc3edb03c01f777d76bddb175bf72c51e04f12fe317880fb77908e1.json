{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.encoder {*/\nimport EncodeHintType from '../../EncodeHintType';\nimport BitArray from '../../common/BitArray';\nimport CharacterSetECI from '../../common/CharacterSetECI';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonEncoder from '../../common/reedsolomon/ReedSolomonEncoder';\nimport Mode from '../decoder/Mode';\nimport Version from '../decoder/Version';\nimport MaskUtil from './MaskUtil';\nimport ByteMatrix from './ByteMatrix';\nimport QRCode from './QRCode';\nimport MatrixUtil from './MatrixUtil';\nimport StringEncoding from '../../util/StringEncoding';\nimport BlockPair from './BlockPair';\nimport WriterException from '../../WriterException';\n/*import java.io.UnsupportedEncodingException;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collection;*/\n/*import java.util.Map;*/\n/**\n * <AUTHOR> (Satoru Takabayashi) - creator\n * <AUTHOR> (Daniel Switkin) - ported from C++\n */\nvar Encoder = /** @class */function () {\n  // TYPESCRIPTPORT: changed to UTF8, the default for js\n  function Encoder() {}\n  // The mask penalty calculation is complicated.  See Table 21 of JISX0510:2004 (p.45) for details.\n  // Basically it applies four rules and summate all penalties.\n  Encoder.calculateMaskPenalty = function (matrix) {\n    return MaskUtil.applyMaskPenaltyRule1(matrix) + MaskUtil.applyMaskPenaltyRule2(matrix) + MaskUtil.applyMaskPenaltyRule3(matrix) + MaskUtil.applyMaskPenaltyRule4(matrix);\n  };\n  /**\n   * @param content text to encode\n   * @param ecLevel error correction level to use\n   * @return {@link QRCode} representing the encoded QR code\n   * @throws WriterException if encoding can't succeed, because of for example invalid content\n   *   or configuration\n   */\n  // public static encode(content: string, ecLevel: ErrorCorrectionLevel): QRCode /*throws WriterException*/ {\n  //   return encode(content, ecLevel, null)\n  // }\n  Encoder.encode = function (content, ecLevel, hints) {\n    if (hints === void 0) {\n      hints = null;\n    }\n    // Determine what character encoding has been specified by the caller, if any\n    var encoding = Encoder.DEFAULT_BYTE_MODE_ENCODING;\n    var hasEncodingHint = hints !== null && undefined !== hints.get(EncodeHintType.CHARACTER_SET);\n    if (hasEncodingHint) {\n      encoding = hints.get(EncodeHintType.CHARACTER_SET).toString();\n    }\n    // Pick an encoding mode appropriate for the content. Note that this will not attempt to use\n    // multiple modes / segments even if that were more efficient. Twould be nice.\n    var mode = this.chooseMode(content, encoding);\n    // This will store the header information, like mode and\n    // length, as well as \"header\" segments like an ECI segment.\n    var headerBits = new BitArray();\n    // Append ECI segment if applicable\n    if (mode === Mode.BYTE && (hasEncodingHint || Encoder.DEFAULT_BYTE_MODE_ENCODING !== encoding)) {\n      var eci = CharacterSetECI.getCharacterSetECIByName(encoding);\n      if (eci !== undefined) {\n        this.appendECI(eci, headerBits);\n      }\n    }\n    // (With ECI in place,) Write the mode marker\n    this.appendModeInfo(mode, headerBits);\n    // Collect data within the main segment, separately, to count its size if needed. Don't add it to\n    // main payload yet.\n    var dataBits = new BitArray();\n    this.appendBytes(content, mode, dataBits, encoding);\n    var version;\n    if (hints !== null && undefined !== hints.get(EncodeHintType.QR_VERSION)) {\n      var versionNumber = Number.parseInt(hints.get(EncodeHintType.QR_VERSION).toString(), 10);\n      version = Version.getVersionForNumber(versionNumber);\n      var bitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, version);\n      if (!this.willFit(bitsNeeded, version, ecLevel)) {\n        throw new WriterException('Data too big for requested version');\n      }\n    } else {\n      version = this.recommendVersion(ecLevel, mode, headerBits, dataBits);\n    }\n    var headerAndDataBits = new BitArray();\n    headerAndDataBits.appendBitArray(headerBits);\n    // Find \"length\" of main segment and write it\n    var numLetters = mode === Mode.BYTE ? dataBits.getSizeInBytes() : content.length;\n    this.appendLengthInfo(numLetters, version, mode, headerAndDataBits);\n    // Put data together into the overall payload\n    headerAndDataBits.appendBitArray(dataBits);\n    var ecBlocks = version.getECBlocksForLevel(ecLevel);\n    var numDataBytes = version.getTotalCodewords() - ecBlocks.getTotalECCodewords();\n    // Terminate the bits properly.\n    this.terminateBits(numDataBytes, headerAndDataBits);\n    // Interleave data bits with error correction code.\n    var finalBits = this.interleaveWithECBytes(headerAndDataBits, version.getTotalCodewords(), numDataBytes, ecBlocks.getNumBlocks());\n    var qrCode = new QRCode();\n    qrCode.setECLevel(ecLevel);\n    qrCode.setMode(mode);\n    qrCode.setVersion(version);\n    //  Choose the mask pattern and set to \"qrCode\".\n    var dimension = version.getDimensionForVersion();\n    var matrix = new ByteMatrix(dimension, dimension);\n    var maskPattern = this.chooseMaskPattern(finalBits, ecLevel, version, matrix);\n    qrCode.setMaskPattern(maskPattern);\n    // Build the matrix and set it to \"qrCode\".\n    MatrixUtil.buildMatrix(finalBits, ecLevel, version, maskPattern, matrix);\n    qrCode.setMatrix(matrix);\n    return qrCode;\n  };\n  /**\n   * Decides the smallest version of QR code that will contain all of the provided data.\n   *\n   * @throws WriterException if the data cannot fit in any version\n   */\n  Encoder.recommendVersion = function (ecLevel, mode, headerBits, dataBits) {\n    // Hard part: need to know version to know how many bits length takes. But need to know how many\n    // bits it takes to know version. First we take a guess at version by assuming version will be\n    // the minimum, 1:\n    var provisionalBitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, Version.getVersionForNumber(1));\n    var provisionalVersion = this.chooseVersion(provisionalBitsNeeded, ecLevel);\n    // Use that guess to calculate the right version. I am still not sure this works in 100% of cases.\n    var bitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, provisionalVersion);\n    return this.chooseVersion(bitsNeeded, ecLevel);\n  };\n  Encoder.calculateBitsNeeded = function (mode, headerBits, dataBits, version) {\n    return headerBits.getSize() + mode.getCharacterCountBits(version) + dataBits.getSize();\n  };\n  /**\n   * @return the code point of the table used in alphanumeric mode or\n   *  -1 if there is no corresponding code in the table.\n   */\n  Encoder.getAlphanumericCode = function (code /*int*/) {\n    if (code < Encoder.ALPHANUMERIC_TABLE.length) {\n      return Encoder.ALPHANUMERIC_TABLE[code];\n    }\n    return -1;\n  };\n  // public static chooseMode(content: string): Mode {\n  //   return chooseMode(content, null);\n  // }\n  /**\n   * Choose the best mode by examining the content. Note that 'encoding' is used as a hint;\n   * if it is Shift_JIS, and the input is only double-byte Kanji, then we return {@link Mode#KANJI}.\n   */\n  Encoder.chooseMode = function (content, encoding) {\n    if (encoding === void 0) {\n      encoding = null;\n    }\n    if (CharacterSetECI.SJIS.getName() === encoding && this.isOnlyDoubleByteKanji(content)) {\n      // Choose Kanji mode if all input are double-byte characters\n      return Mode.KANJI;\n    }\n    var hasNumeric = false;\n    var hasAlphanumeric = false;\n    for (var i = 0, length_1 = content.length; i < length_1; ++i) {\n      var c = content.charAt(i);\n      if (Encoder.isDigit(c)) {\n        hasNumeric = true;\n      } else if (this.getAlphanumericCode(c.charCodeAt(0)) !== -1) {\n        hasAlphanumeric = true;\n      } else {\n        return Mode.BYTE;\n      }\n    }\n    if (hasAlphanumeric) {\n      return Mode.ALPHANUMERIC;\n    }\n    if (hasNumeric) {\n      return Mode.NUMERIC;\n    }\n    return Mode.BYTE;\n  };\n  Encoder.isOnlyDoubleByteKanji = function (content) {\n    var bytes;\n    try {\n      bytes = StringEncoding.encode(content, CharacterSetECI.SJIS); // content.getBytes(\"Shift_JIS\"))\n    } catch (ignored /*: UnsupportedEncodingException*/) {\n      return false;\n    }\n    var length = bytes.length;\n    if (length % 2 !== 0) {\n      return false;\n    }\n    for (var i = 0; i < length; i += 2) {\n      var byte1 = bytes[i] & 0xFF;\n      if ((byte1 < 0x81 || byte1 > 0x9F) && (byte1 < 0xE0 || byte1 > 0xEB)) {\n        return false;\n      }\n    }\n    return true;\n  };\n  Encoder.chooseMaskPattern = function (bits, ecLevel, version, matrix) {\n    var minPenalty = Number.MAX_SAFE_INTEGER; // Lower penalty is better.\n    var bestMaskPattern = -1;\n    // We try all mask patterns to choose the best one.\n    for (var maskPattern = 0; maskPattern < QRCode.NUM_MASK_PATTERNS; maskPattern++) {\n      MatrixUtil.buildMatrix(bits, ecLevel, version, maskPattern, matrix);\n      var penalty = this.calculateMaskPenalty(matrix);\n      if (penalty < minPenalty) {\n        minPenalty = penalty;\n        bestMaskPattern = maskPattern;\n      }\n    }\n    return bestMaskPattern;\n  };\n  Encoder.chooseVersion = function (numInputBits /*int*/, ecLevel) {\n    for (var versionNum = 1; versionNum <= 40; versionNum++) {\n      var version = Version.getVersionForNumber(versionNum);\n      if (Encoder.willFit(numInputBits, version, ecLevel)) {\n        return version;\n      }\n    }\n    throw new WriterException('Data too big');\n  };\n  /**\n   * @return true if the number of input bits will fit in a code with the specified version and\n   * error correction level.\n   */\n  Encoder.willFit = function (numInputBits /*int*/, version, ecLevel) {\n    // In the following comments, we use numbers of Version 7-H.\n    // numBytes = 196\n    var numBytes = version.getTotalCodewords();\n    // getNumECBytes = 130\n    var ecBlocks = version.getECBlocksForLevel(ecLevel);\n    var numEcBytes = ecBlocks.getTotalECCodewords();\n    // getNumDataBytes = 196 - 130 = 66\n    var numDataBytes = numBytes - numEcBytes;\n    var totalInputBytes = (numInputBits + 7) / 8;\n    return numDataBytes >= totalInputBytes;\n  };\n  /**\n   * Terminate bits as described in 8.4.8 and 8.4.9 of JISX0510:2004 (p.24).\n   */\n  Encoder.terminateBits = function (numDataBytes /*int*/, bits) {\n    var capacity = numDataBytes * 8;\n    if (bits.getSize() > capacity) {\n      throw new WriterException('data bits cannot fit in the QR Code' + bits.getSize() + ' > ' + capacity);\n    }\n    for (var i = 0; i < 4 && bits.getSize() < capacity; ++i) {\n      bits.appendBit(false);\n    }\n    // Append termination bits. See 8.4.8 of JISX0510:2004 (p.24) for details.\n    // If the last byte isn't 8-bit aligned, we'll add padding bits.\n    var numBitsInLastByte = bits.getSize() & 0x07;\n    if (numBitsInLastByte > 0) {\n      for (var i = numBitsInLastByte; i < 8; i++) {\n        bits.appendBit(false);\n      }\n    }\n    // If we have more space, we'll fill the space with padding patterns defined in 8.4.9 (p.24).\n    var numPaddingBytes = numDataBytes - bits.getSizeInBytes();\n    for (var i = 0; i < numPaddingBytes; ++i) {\n      bits.appendBits((i & 0x01) === 0 ? 0xEC : 0x11, 8);\n    }\n    if (bits.getSize() !== capacity) {\n      throw new WriterException('Bits size does not equal capacity');\n    }\n  };\n  /**\n   * Get number of data bytes and number of error correction bytes for block id \"blockID\". Store\n   * the result in \"numDataBytesInBlock\", and \"numECBytesInBlock\". See table 12 in 8.5.1 of\n   * JISX0510:2004 (p.30)\n   */\n  Encoder.getNumDataBytesAndNumECBytesForBlockID = function (numTotalBytes /*int*/, numDataBytes /*int*/, numRSBlocks /*int*/, blockID /*int*/, numDataBytesInBlock, numECBytesInBlock) {\n    if (blockID >= numRSBlocks) {\n      throw new WriterException('Block ID too large');\n    }\n    // numRsBlocksInGroup2 = 196 % 5 = 1\n    var numRsBlocksInGroup2 = numTotalBytes % numRSBlocks;\n    // numRsBlocksInGroup1 = 5 - 1 = 4\n    var numRsBlocksInGroup1 = numRSBlocks - numRsBlocksInGroup2;\n    // numTotalBytesInGroup1 = 196 / 5 = 39\n    var numTotalBytesInGroup1 = Math.floor(numTotalBytes / numRSBlocks);\n    // numTotalBytesInGroup2 = 39 + 1 = 40\n    var numTotalBytesInGroup2 = numTotalBytesInGroup1 + 1;\n    // numDataBytesInGroup1 = 66 / 5 = 13\n    var numDataBytesInGroup1 = Math.floor(numDataBytes / numRSBlocks);\n    // numDataBytesInGroup2 = 13 + 1 = 14\n    var numDataBytesInGroup2 = numDataBytesInGroup1 + 1;\n    // numEcBytesInGroup1 = 39 - 13 = 26\n    var numEcBytesInGroup1 = numTotalBytesInGroup1 - numDataBytesInGroup1;\n    // numEcBytesInGroup2 = 40 - 14 = 26\n    var numEcBytesInGroup2 = numTotalBytesInGroup2 - numDataBytesInGroup2;\n    // Sanity checks.\n    // 26 = 26\n    if (numEcBytesInGroup1 !== numEcBytesInGroup2) {\n      throw new WriterException('EC bytes mismatch');\n    }\n    // 5 = 4 + 1.\n    if (numRSBlocks !== numRsBlocksInGroup1 + numRsBlocksInGroup2) {\n      throw new WriterException('RS blocks mismatch');\n    }\n    // 196 = (13 + 26) * 4 + (14 + 26) * 1\n    if (numTotalBytes !== (numDataBytesInGroup1 + numEcBytesInGroup1) * numRsBlocksInGroup1 + (numDataBytesInGroup2 + numEcBytesInGroup2) * numRsBlocksInGroup2) {\n      throw new WriterException('Total bytes mismatch');\n    }\n    if (blockID < numRsBlocksInGroup1) {\n      numDataBytesInBlock[0] = numDataBytesInGroup1;\n      numECBytesInBlock[0] = numEcBytesInGroup1;\n    } else {\n      numDataBytesInBlock[0] = numDataBytesInGroup2;\n      numECBytesInBlock[0] = numEcBytesInGroup2;\n    }\n  };\n  /**\n   * Interleave \"bits\" with corresponding error correction bytes. On success, store the result in\n   * \"result\". The interleave rule is complicated. See 8.6 of JISX0510:2004 (p.37) for details.\n   */\n  Encoder.interleaveWithECBytes = function (bits, numTotalBytes /*int*/, numDataBytes /*int*/, numRSBlocks /*int*/) {\n    var e_1, _a, e_2, _b;\n    // \"bits\" must have \"getNumDataBytes\" bytes of data.\n    if (bits.getSizeInBytes() !== numDataBytes) {\n      throw new WriterException('Number of bits and data bytes does not match');\n    }\n    // Step 1.  Divide data bytes into blocks and generate error correction bytes for them. We'll\n    // store the divided data bytes blocks and error correction bytes blocks into \"blocks\".\n    var dataBytesOffset = 0;\n    var maxNumDataBytes = 0;\n    var maxNumEcBytes = 0;\n    // Since, we know the number of reedsolmon blocks, we can initialize the vector with the number.\n    var blocks = new Array(); // new Array<BlockPair>(numRSBlocks)\n    for (var i = 0; i < numRSBlocks; ++i) {\n      var numDataBytesInBlock = new Int32Array(1);\n      var numEcBytesInBlock = new Int32Array(1);\n      Encoder.getNumDataBytesAndNumECBytesForBlockID(numTotalBytes, numDataBytes, numRSBlocks, i, numDataBytesInBlock, numEcBytesInBlock);\n      var size = numDataBytesInBlock[0];\n      var dataBytes = new Uint8Array(size);\n      bits.toBytes(8 * dataBytesOffset, dataBytes, 0, size);\n      var ecBytes = Encoder.generateECBytes(dataBytes, numEcBytesInBlock[0]);\n      blocks.push(new BlockPair(dataBytes, ecBytes));\n      maxNumDataBytes = Math.max(maxNumDataBytes, size);\n      maxNumEcBytes = Math.max(maxNumEcBytes, ecBytes.length);\n      dataBytesOffset += numDataBytesInBlock[0];\n    }\n    if (numDataBytes !== dataBytesOffset) {\n      throw new WriterException('Data bytes does not match offset');\n    }\n    var result = new BitArray();\n    // First, place data blocks.\n    for (var i = 0; i < maxNumDataBytes; ++i) {\n      try {\n        for (var blocks_1 = (e_1 = void 0, __values(blocks)), blocks_1_1 = blocks_1.next(); !blocks_1_1.done; blocks_1_1 = blocks_1.next()) {\n          var block = blocks_1_1.value;\n          var dataBytes = block.getDataBytes();\n          if (i < dataBytes.length) {\n            result.appendBits(dataBytes[i], 8);\n          }\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (blocks_1_1 && !blocks_1_1.done && (_a = blocks_1.return)) _a.call(blocks_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }\n    // Then, place error correction blocks.\n    for (var i = 0; i < maxNumEcBytes; ++i) {\n      try {\n        for (var blocks_2 = (e_2 = void 0, __values(blocks)), blocks_2_1 = blocks_2.next(); !blocks_2_1.done; blocks_2_1 = blocks_2.next()) {\n          var block = blocks_2_1.value;\n          var ecBytes = block.getErrorCorrectionBytes();\n          if (i < ecBytes.length) {\n            result.appendBits(ecBytes[i], 8);\n          }\n        }\n      } catch (e_2_1) {\n        e_2 = {\n          error: e_2_1\n        };\n      } finally {\n        try {\n          if (blocks_2_1 && !blocks_2_1.done && (_b = blocks_2.return)) _b.call(blocks_2);\n        } finally {\n          if (e_2) throw e_2.error;\n        }\n      }\n    }\n    if (numTotalBytes !== result.getSizeInBytes()) {\n      // Should be same.\n      throw new WriterException('Interleaving error: ' + numTotalBytes + ' and ' + result.getSizeInBytes() + ' differ.');\n    }\n    return result;\n  };\n  Encoder.generateECBytes = function (dataBytes, numEcBytesInBlock /*int*/) {\n    var numDataBytes = dataBytes.length;\n    var toEncode = new Int32Array(numDataBytes + numEcBytesInBlock); // int[numDataBytes + numEcBytesInBlock]\n    for (var i = 0; i < numDataBytes; i++) {\n      toEncode[i] = dataBytes[i] & 0xFF;\n    }\n    new ReedSolomonEncoder(GenericGF.QR_CODE_FIELD_256).encode(toEncode, numEcBytesInBlock);\n    var ecBytes = new Uint8Array(numEcBytesInBlock);\n    for (var i = 0; i < numEcBytesInBlock; i++) {\n      ecBytes[i] = /*(byte) */toEncode[numDataBytes + i];\n    }\n    return ecBytes;\n  };\n  /**\n   * Append mode info. On success, store the result in \"bits\".\n   */\n  Encoder.appendModeInfo = function (mode, bits) {\n    bits.appendBits(mode.getBits(), 4);\n  };\n  /**\n   * Append length info. On success, store the result in \"bits\".\n   */\n  Encoder.appendLengthInfo = function (numLetters /*int*/, version, mode, bits) {\n    var numBits = mode.getCharacterCountBits(version);\n    if (numLetters >= 1 << numBits) {\n      throw new WriterException(numLetters + ' is bigger than ' + ((1 << numBits) - 1));\n    }\n    bits.appendBits(numLetters, numBits);\n  };\n  /**\n   * Append \"bytes\" in \"mode\" mode (encoding) into \"bits\". On success, store the result in \"bits\".\n   */\n  Encoder.appendBytes = function (content, mode, bits, encoding) {\n    switch (mode) {\n      case Mode.NUMERIC:\n        Encoder.appendNumericBytes(content, bits);\n        break;\n      case Mode.ALPHANUMERIC:\n        Encoder.appendAlphanumericBytes(content, bits);\n        break;\n      case Mode.BYTE:\n        Encoder.append8BitBytes(content, bits, encoding);\n        break;\n      case Mode.KANJI:\n        Encoder.appendKanjiBytes(content, bits);\n        break;\n      default:\n        throw new WriterException('Invalid mode: ' + mode);\n    }\n  };\n  Encoder.getDigit = function (singleCharacter) {\n    return singleCharacter.charCodeAt(0) - 48;\n  };\n  Encoder.isDigit = function (singleCharacter) {\n    var cn = Encoder.getDigit(singleCharacter);\n    return cn >= 0 && cn <= 9;\n  };\n  Encoder.appendNumericBytes = function (content, bits) {\n    var length = content.length;\n    var i = 0;\n    while (i < length) {\n      var num1 = Encoder.getDigit(content.charAt(i));\n      if (i + 2 < length) {\n        // Encode three numeric letters in ten bits.\n        var num2 = Encoder.getDigit(content.charAt(i + 1));\n        var num3 = Encoder.getDigit(content.charAt(i + 2));\n        bits.appendBits(num1 * 100 + num2 * 10 + num3, 10);\n        i += 3;\n      } else if (i + 1 < length) {\n        // Encode two numeric letters in seven bits.\n        var num2 = Encoder.getDigit(content.charAt(i + 1));\n        bits.appendBits(num1 * 10 + num2, 7);\n        i += 2;\n      } else {\n        // Encode one numeric letter in four bits.\n        bits.appendBits(num1, 4);\n        i++;\n      }\n    }\n  };\n  Encoder.appendAlphanumericBytes = function (content, bits) {\n    var length = content.length;\n    var i = 0;\n    while (i < length) {\n      var code1 = Encoder.getAlphanumericCode(content.charCodeAt(i));\n      if (code1 === -1) {\n        throw new WriterException();\n      }\n      if (i + 1 < length) {\n        var code2 = Encoder.getAlphanumericCode(content.charCodeAt(i + 1));\n        if (code2 === -1) {\n          throw new WriterException();\n        }\n        // Encode two alphanumeric letters in 11 bits.\n        bits.appendBits(code1 * 45 + code2, 11);\n        i += 2;\n      } else {\n        // Encode one alphanumeric letter in six bits.\n        bits.appendBits(code1, 6);\n        i++;\n      }\n    }\n  };\n  Encoder.append8BitBytes = function (content, bits, encoding) {\n    var bytes;\n    try {\n      bytes = StringEncoding.encode(content, encoding);\n    } catch (uee /*: UnsupportedEncodingException*/) {\n      throw new WriterException(uee);\n    }\n    for (var i = 0, length_2 = bytes.length; i !== length_2; i++) {\n      var b = bytes[i];\n      bits.appendBits(b, 8);\n    }\n  };\n  /**\n   * @throws WriterException\n   */\n  Encoder.appendKanjiBytes = function (content, bits) {\n    var bytes;\n    try {\n      bytes = StringEncoding.encode(content, CharacterSetECI.SJIS);\n    } catch (uee /*: UnsupportedEncodingException*/) {\n      throw new WriterException(uee);\n    }\n    var length = bytes.length;\n    for (var i = 0; i < length; i += 2) {\n      var byte1 = bytes[i] & 0xFF;\n      var byte2 = bytes[i + 1] & 0xFF;\n      var code = byte1 << 8 & 0xFFFFFFFF | byte2;\n      var subtracted = -1;\n      if (code >= 0x8140 && code <= 0x9ffc) {\n        subtracted = code - 0x8140;\n      } else if (code >= 0xe040 && code <= 0xebbf) {\n        subtracted = code - 0xc140;\n      }\n      if (subtracted === -1) {\n        throw new WriterException('Invalid byte sequence');\n      }\n      var encoded = (subtracted >> 8) * 0xc0 + (subtracted & 0xff);\n      bits.appendBits(encoded, 13);\n    }\n  };\n  Encoder.appendECI = function (eci, bits) {\n    bits.appendBits(Mode.ECI.getBits(), 4);\n    // This is correct for values up to 127, which is all we need now.\n    bits.appendBits(eci.getValue(), 8);\n  };\n  // The original table is defined in the table 5 of JISX0510:2004 (p.19).\n  Encoder.ALPHANUMERIC_TABLE = Int32Array.from([-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 36, -1, -1, -1, 37, 38, -1, -1, -1, -1, 39, 40, -1, 41, 42, 43, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 44, -1, -1, -1, -1, -1, -1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, -1, -1, -1, -1, -1]);\n  Encoder.DEFAULT_BYTE_MODE_ENCODING = CharacterSetECI.UTF8.getName(); // \"ISO-8859-1\"\n  return Encoder;\n}();\nexport default Encoder;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "EncodeHintType", "BitArray", "CharacterSetECI", "GenericGF", "ReedSolomonEncoder", "Mode", "Version", "<PERSON><PERSON><PERSON>", "ByteMatrix", "QRCode", "MatrixUtil", "StringEncoding", "BlockPair", "WriterException", "Encoder", "calculateMaskPenalty", "matrix", "applyMaskPenaltyRule1", "applyMaskPenaltyRule2", "applyMaskPenaltyRule3", "applyMaskPenaltyRule4", "encode", "content", "ecLevel", "hints", "encoding", "DEFAULT_BYTE_MODE_ENCODING", "hasEncodingHint", "undefined", "get", "CHARACTER_SET", "toString", "mode", "chooseMode", "headerBits", "BYTE", "eci", "getCharacterSetECIByName", "appendECI", "appendModeInfo", "dataBits", "appendBytes", "version", "QR_VERSION", "versionNumber", "Number", "parseInt", "getVersionForNumber", "bitsNeeded", "calculateBitsNeeded", "willFit", "recommendVersion", "headerAndDataBits", "appendBitArray", "numLetters", "getSizeInBytes", "appendLengthInfo", "ecBlocks", "getECBlocksForLevel", "numDataBytes", "getTotalCodewords", "getTotalECCodewords", "terminateBits", "finalBits", "interleaveWithECBytes", "getNumBlocks", "qrCode", "setECLevel", "setMode", "setVersion", "dimension", "getDimensionForVersion", "maskPattern", "chooseMaskPattern", "setMaskPattern", "buildMatrix", "setMatrix", "provisionalBitsNeeded", "provisionalVersion", "chooseVersion", "getSize", "getCharacterCountBits", "getAlphanumericCode", "code", "ALPHANUMERIC_TABLE", "SJIS", "getName", "isOnlyDoubleByteKanji", "KANJI", "hasNumeric", "hasAlphanumeric", "length_1", "c", "char<PERSON>t", "isDigit", "charCodeAt", "ALPHANUMERIC", "NUMERIC", "bytes", "ignored", "byte1", "bits", "min<PERSON><PERSON><PERSON><PERSON>", "MAX_SAFE_INTEGER", "bestMaskPattern", "NUM_MASK_PATTERNS", "penalty", "numInputBits", "versionNum", "numBytes", "numEcBytes", "totalInputBytes", "capacity", "appendBit", "numBitsInLastByte", "numPaddingBytes", "appendBits", "getNumDataBytesAndNumECBytesForBlockID", "numTotalBytes", "numRSBlocks", "blockID", "numDataBytesInBlock", "numECBytesInBlock", "numRsBlocksInGroup2", "numRsBlocksInGroup1", "numTotalBytesInGroup1", "Math", "floor", "numTotalBytesInGroup2", "numDataBytesInGroup1", "numDataBytesInGroup2", "numEcBytesInGroup1", "numEcBytesInGroup2", "e_1", "_a", "e_2", "_b", "dataBytesOffset", "maxNumDataBytes", "maxNumEcBytes", "blocks", "Array", "Int32Array", "numEcBytesInBlock", "size", "dataBytes", "Uint8Array", "toBytes", "ecBytes", "generateECBytes", "push", "max", "result", "blocks_1", "blocks_1_1", "block", "getDataBytes", "e_1_1", "error", "return", "blocks_2", "blocks_2_1", "getErrorCorrectionBytes", "e_2_1", "toEncode", "QR_CODE_FIELD_256", "getBits", "numBits", "appendNumericBytes", "appendAlphanumericBytes", "append8BitBytes", "appendKanjiBytes", "getDigit", "singleCharacter", "cn", "num1", "num2", "num3", "code1", "code2", "uee", "length_2", "b", "byte2", "subtracted", "encoded", "ECI", "getValue", "from", "UTF8"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/encoder/Encoder.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.encoder {*/\nimport EncodeHintType from '../../EncodeHintType';\nimport BitArray from '../../common/BitArray';\nimport CharacterSetECI from '../../common/CharacterSetECI';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonEncoder from '../../common/reedsolomon/ReedSolomonEncoder';\nimport Mode from '../decoder/Mode';\nimport Version from '../decoder/Version';\nimport MaskUtil from './MaskUtil';\nimport ByteMatrix from './ByteMatrix';\nimport QRCode from './QRCode';\nimport MatrixUtil from './MatrixUtil';\nimport StringEncoding from '../../util/StringEncoding';\nimport BlockPair from './BlockPair';\nimport WriterException from '../../WriterException';\n/*import java.io.UnsupportedEncodingException;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collection;*/\n/*import java.util.Map;*/\n/**\n * <AUTHOR> (Satoru Takabayashi) - creator\n * <AUTHOR> (Daniel Switkin) - ported from C++\n */\nvar Encoder = /** @class */ (function () {\n    // TYPESCRIPTPORT: changed to UTF8, the default for js\n    function Encoder() {\n    }\n    // The mask penalty calculation is complicated.  See Table 21 of JISX0510:2004 (p.45) for details.\n    // Basically it applies four rules and summate all penalties.\n    Encoder.calculateMaskPenalty = function (matrix) {\n        return MaskUtil.applyMaskPenaltyRule1(matrix)\n            + MaskUtil.applyMaskPenaltyRule2(matrix)\n            + MaskUtil.applyMaskPenaltyRule3(matrix)\n            + MaskUtil.applyMaskPenaltyRule4(matrix);\n    };\n    /**\n     * @param content text to encode\n     * @param ecLevel error correction level to use\n     * @return {@link QRCode} representing the encoded QR code\n     * @throws WriterException if encoding can't succeed, because of for example invalid content\n     *   or configuration\n     */\n    // public static encode(content: string, ecLevel: ErrorCorrectionLevel): QRCode /*throws WriterException*/ {\n    //   return encode(content, ecLevel, null)\n    // }\n    Encoder.encode = function (content, ecLevel, hints) {\n        if (hints === void 0) { hints = null; }\n        // Determine what character encoding has been specified by the caller, if any\n        var encoding = Encoder.DEFAULT_BYTE_MODE_ENCODING;\n        var hasEncodingHint = hints !== null && undefined !== hints.get(EncodeHintType.CHARACTER_SET);\n        if (hasEncodingHint) {\n            encoding = hints.get(EncodeHintType.CHARACTER_SET).toString();\n        }\n        // Pick an encoding mode appropriate for the content. Note that this will not attempt to use\n        // multiple modes / segments even if that were more efficient. Twould be nice.\n        var mode = this.chooseMode(content, encoding);\n        // This will store the header information, like mode and\n        // length, as well as \"header\" segments like an ECI segment.\n        var headerBits = new BitArray();\n        // Append ECI segment if applicable\n        if (mode === Mode.BYTE && (hasEncodingHint || Encoder.DEFAULT_BYTE_MODE_ENCODING !== encoding)) {\n            var eci = CharacterSetECI.getCharacterSetECIByName(encoding);\n            if (eci !== undefined) {\n                this.appendECI(eci, headerBits);\n            }\n        }\n        // (With ECI in place,) Write the mode marker\n        this.appendModeInfo(mode, headerBits);\n        // Collect data within the main segment, separately, to count its size if needed. Don't add it to\n        // main payload yet.\n        var dataBits = new BitArray();\n        this.appendBytes(content, mode, dataBits, encoding);\n        var version;\n        if (hints !== null && undefined !== hints.get(EncodeHintType.QR_VERSION)) {\n            var versionNumber = Number.parseInt(hints.get(EncodeHintType.QR_VERSION).toString(), 10);\n            version = Version.getVersionForNumber(versionNumber);\n            var bitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, version);\n            if (!this.willFit(bitsNeeded, version, ecLevel)) {\n                throw new WriterException('Data too big for requested version');\n            }\n        }\n        else {\n            version = this.recommendVersion(ecLevel, mode, headerBits, dataBits);\n        }\n        var headerAndDataBits = new BitArray();\n        headerAndDataBits.appendBitArray(headerBits);\n        // Find \"length\" of main segment and write it\n        var numLetters = mode === Mode.BYTE ? dataBits.getSizeInBytes() : content.length;\n        this.appendLengthInfo(numLetters, version, mode, headerAndDataBits);\n        // Put data together into the overall payload\n        headerAndDataBits.appendBitArray(dataBits);\n        var ecBlocks = version.getECBlocksForLevel(ecLevel);\n        var numDataBytes = version.getTotalCodewords() - ecBlocks.getTotalECCodewords();\n        // Terminate the bits properly.\n        this.terminateBits(numDataBytes, headerAndDataBits);\n        // Interleave data bits with error correction code.\n        var finalBits = this.interleaveWithECBytes(headerAndDataBits, version.getTotalCodewords(), numDataBytes, ecBlocks.getNumBlocks());\n        var qrCode = new QRCode();\n        qrCode.setECLevel(ecLevel);\n        qrCode.setMode(mode);\n        qrCode.setVersion(version);\n        //  Choose the mask pattern and set to \"qrCode\".\n        var dimension = version.getDimensionForVersion();\n        var matrix = new ByteMatrix(dimension, dimension);\n        var maskPattern = this.chooseMaskPattern(finalBits, ecLevel, version, matrix);\n        qrCode.setMaskPattern(maskPattern);\n        // Build the matrix and set it to \"qrCode\".\n        MatrixUtil.buildMatrix(finalBits, ecLevel, version, maskPattern, matrix);\n        qrCode.setMatrix(matrix);\n        return qrCode;\n    };\n    /**\n     * Decides the smallest version of QR code that will contain all of the provided data.\n     *\n     * @throws WriterException if the data cannot fit in any version\n     */\n    Encoder.recommendVersion = function (ecLevel, mode, headerBits, dataBits) {\n        // Hard part: need to know version to know how many bits length takes. But need to know how many\n        // bits it takes to know version. First we take a guess at version by assuming version will be\n        // the minimum, 1:\n        var provisionalBitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, Version.getVersionForNumber(1));\n        var provisionalVersion = this.chooseVersion(provisionalBitsNeeded, ecLevel);\n        // Use that guess to calculate the right version. I am still not sure this works in 100% of cases.\n        var bitsNeeded = this.calculateBitsNeeded(mode, headerBits, dataBits, provisionalVersion);\n        return this.chooseVersion(bitsNeeded, ecLevel);\n    };\n    Encoder.calculateBitsNeeded = function (mode, headerBits, dataBits, version) {\n        return headerBits.getSize() + mode.getCharacterCountBits(version) + dataBits.getSize();\n    };\n    /**\n     * @return the code point of the table used in alphanumeric mode or\n     *  -1 if there is no corresponding code in the table.\n     */\n    Encoder.getAlphanumericCode = function (code /*int*/) {\n        if (code < Encoder.ALPHANUMERIC_TABLE.length) {\n            return Encoder.ALPHANUMERIC_TABLE[code];\n        }\n        return -1;\n    };\n    // public static chooseMode(content: string): Mode {\n    //   return chooseMode(content, null);\n    // }\n    /**\n     * Choose the best mode by examining the content. Note that 'encoding' is used as a hint;\n     * if it is Shift_JIS, and the input is only double-byte Kanji, then we return {@link Mode#KANJI}.\n     */\n    Encoder.chooseMode = function (content, encoding) {\n        if (encoding === void 0) { encoding = null; }\n        if (CharacterSetECI.SJIS.getName() === encoding && this.isOnlyDoubleByteKanji(content)) {\n            // Choose Kanji mode if all input are double-byte characters\n            return Mode.KANJI;\n        }\n        var hasNumeric = false;\n        var hasAlphanumeric = false;\n        for (var i = 0, length_1 = content.length; i < length_1; ++i) {\n            var c = content.charAt(i);\n            if (Encoder.isDigit(c)) {\n                hasNumeric = true;\n            }\n            else if (this.getAlphanumericCode(c.charCodeAt(0)) !== -1) {\n                hasAlphanumeric = true;\n            }\n            else {\n                return Mode.BYTE;\n            }\n        }\n        if (hasAlphanumeric) {\n            return Mode.ALPHANUMERIC;\n        }\n        if (hasNumeric) {\n            return Mode.NUMERIC;\n        }\n        return Mode.BYTE;\n    };\n    Encoder.isOnlyDoubleByteKanji = function (content) {\n        var bytes;\n        try {\n            bytes = StringEncoding.encode(content, CharacterSetECI.SJIS); // content.getBytes(\"Shift_JIS\"))\n        }\n        catch (ignored /*: UnsupportedEncodingException*/) {\n            return false;\n        }\n        var length = bytes.length;\n        if (length % 2 !== 0) {\n            return false;\n        }\n        for (var i = 0; i < length; i += 2) {\n            var byte1 = bytes[i] & 0xFF;\n            if ((byte1 < 0x81 || byte1 > 0x9F) && (byte1 < 0xE0 || byte1 > 0xEB)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Encoder.chooseMaskPattern = function (bits, ecLevel, version, matrix) {\n        var minPenalty = Number.MAX_SAFE_INTEGER; // Lower penalty is better.\n        var bestMaskPattern = -1;\n        // We try all mask patterns to choose the best one.\n        for (var maskPattern = 0; maskPattern < QRCode.NUM_MASK_PATTERNS; maskPattern++) {\n            MatrixUtil.buildMatrix(bits, ecLevel, version, maskPattern, matrix);\n            var penalty = this.calculateMaskPenalty(matrix);\n            if (penalty < minPenalty) {\n                minPenalty = penalty;\n                bestMaskPattern = maskPattern;\n            }\n        }\n        return bestMaskPattern;\n    };\n    Encoder.chooseVersion = function (numInputBits /*int*/, ecLevel) {\n        for (var versionNum = 1; versionNum <= 40; versionNum++) {\n            var version = Version.getVersionForNumber(versionNum);\n            if (Encoder.willFit(numInputBits, version, ecLevel)) {\n                return version;\n            }\n        }\n        throw new WriterException('Data too big');\n    };\n    /**\n     * @return true if the number of input bits will fit in a code with the specified version and\n     * error correction level.\n     */\n    Encoder.willFit = function (numInputBits /*int*/, version, ecLevel) {\n        // In the following comments, we use numbers of Version 7-H.\n        // numBytes = 196\n        var numBytes = version.getTotalCodewords();\n        // getNumECBytes = 130\n        var ecBlocks = version.getECBlocksForLevel(ecLevel);\n        var numEcBytes = ecBlocks.getTotalECCodewords();\n        // getNumDataBytes = 196 - 130 = 66\n        var numDataBytes = numBytes - numEcBytes;\n        var totalInputBytes = (numInputBits + 7) / 8;\n        return numDataBytes >= totalInputBytes;\n    };\n    /**\n     * Terminate bits as described in 8.4.8 and 8.4.9 of JISX0510:2004 (p.24).\n     */\n    Encoder.terminateBits = function (numDataBytes /*int*/, bits) {\n        var capacity = numDataBytes * 8;\n        if (bits.getSize() > capacity) {\n            throw new WriterException('data bits cannot fit in the QR Code' + bits.getSize() + ' > ' +\n                capacity);\n        }\n        for (var i = 0; i < 4 && bits.getSize() < capacity; ++i) {\n            bits.appendBit(false);\n        }\n        // Append termination bits. See 8.4.8 of JISX0510:2004 (p.24) for details.\n        // If the last byte isn't 8-bit aligned, we'll add padding bits.\n        var numBitsInLastByte = bits.getSize() & 0x07;\n        if (numBitsInLastByte > 0) {\n            for (var i = numBitsInLastByte; i < 8; i++) {\n                bits.appendBit(false);\n            }\n        }\n        // If we have more space, we'll fill the space with padding patterns defined in 8.4.9 (p.24).\n        var numPaddingBytes = numDataBytes - bits.getSizeInBytes();\n        for (var i = 0; i < numPaddingBytes; ++i) {\n            bits.appendBits((i & 0x01) === 0 ? 0xEC : 0x11, 8);\n        }\n        if (bits.getSize() !== capacity) {\n            throw new WriterException('Bits size does not equal capacity');\n        }\n    };\n    /**\n     * Get number of data bytes and number of error correction bytes for block id \"blockID\". Store\n     * the result in \"numDataBytesInBlock\", and \"numECBytesInBlock\". See table 12 in 8.5.1 of\n     * JISX0510:2004 (p.30)\n     */\n    Encoder.getNumDataBytesAndNumECBytesForBlockID = function (numTotalBytes /*int*/, numDataBytes /*int*/, numRSBlocks /*int*/, blockID /*int*/, numDataBytesInBlock, numECBytesInBlock) {\n        if (blockID >= numRSBlocks) {\n            throw new WriterException('Block ID too large');\n        }\n        // numRsBlocksInGroup2 = 196 % 5 = 1\n        var numRsBlocksInGroup2 = numTotalBytes % numRSBlocks;\n        // numRsBlocksInGroup1 = 5 - 1 = 4\n        var numRsBlocksInGroup1 = numRSBlocks - numRsBlocksInGroup2;\n        // numTotalBytesInGroup1 = 196 / 5 = 39\n        var numTotalBytesInGroup1 = Math.floor(numTotalBytes / numRSBlocks);\n        // numTotalBytesInGroup2 = 39 + 1 = 40\n        var numTotalBytesInGroup2 = numTotalBytesInGroup1 + 1;\n        // numDataBytesInGroup1 = 66 / 5 = 13\n        var numDataBytesInGroup1 = Math.floor(numDataBytes / numRSBlocks);\n        // numDataBytesInGroup2 = 13 + 1 = 14\n        var numDataBytesInGroup2 = numDataBytesInGroup1 + 1;\n        // numEcBytesInGroup1 = 39 - 13 = 26\n        var numEcBytesInGroup1 = numTotalBytesInGroup1 - numDataBytesInGroup1;\n        // numEcBytesInGroup2 = 40 - 14 = 26\n        var numEcBytesInGroup2 = numTotalBytesInGroup2 - numDataBytesInGroup2;\n        // Sanity checks.\n        // 26 = 26\n        if (numEcBytesInGroup1 !== numEcBytesInGroup2) {\n            throw new WriterException('EC bytes mismatch');\n        }\n        // 5 = 4 + 1.\n        if (numRSBlocks !== numRsBlocksInGroup1 + numRsBlocksInGroup2) {\n            throw new WriterException('RS blocks mismatch');\n        }\n        // 196 = (13 + 26) * 4 + (14 + 26) * 1\n        if (numTotalBytes !==\n            ((numDataBytesInGroup1 + numEcBytesInGroup1) *\n                numRsBlocksInGroup1) +\n                ((numDataBytesInGroup2 + numEcBytesInGroup2) *\n                    numRsBlocksInGroup2)) {\n            throw new WriterException('Total bytes mismatch');\n        }\n        if (blockID < numRsBlocksInGroup1) {\n            numDataBytesInBlock[0] = numDataBytesInGroup1;\n            numECBytesInBlock[0] = numEcBytesInGroup1;\n        }\n        else {\n            numDataBytesInBlock[0] = numDataBytesInGroup2;\n            numECBytesInBlock[0] = numEcBytesInGroup2;\n        }\n    };\n    /**\n     * Interleave \"bits\" with corresponding error correction bytes. On success, store the result in\n     * \"result\". The interleave rule is complicated. See 8.6 of JISX0510:2004 (p.37) for details.\n     */\n    Encoder.interleaveWithECBytes = function (bits, numTotalBytes /*int*/, numDataBytes /*int*/, numRSBlocks /*int*/) {\n        var e_1, _a, e_2, _b;\n        // \"bits\" must have \"getNumDataBytes\" bytes of data.\n        if (bits.getSizeInBytes() !== numDataBytes) {\n            throw new WriterException('Number of bits and data bytes does not match');\n        }\n        // Step 1.  Divide data bytes into blocks and generate error correction bytes for them. We'll\n        // store the divided data bytes blocks and error correction bytes blocks into \"blocks\".\n        var dataBytesOffset = 0;\n        var maxNumDataBytes = 0;\n        var maxNumEcBytes = 0;\n        // Since, we know the number of reedsolmon blocks, we can initialize the vector with the number.\n        var blocks = new Array(); // new Array<BlockPair>(numRSBlocks)\n        for (var i = 0; i < numRSBlocks; ++i) {\n            var numDataBytesInBlock = new Int32Array(1);\n            var numEcBytesInBlock = new Int32Array(1);\n            Encoder.getNumDataBytesAndNumECBytesForBlockID(numTotalBytes, numDataBytes, numRSBlocks, i, numDataBytesInBlock, numEcBytesInBlock);\n            var size = numDataBytesInBlock[0];\n            var dataBytes = new Uint8Array(size);\n            bits.toBytes(8 * dataBytesOffset, dataBytes, 0, size);\n            var ecBytes = Encoder.generateECBytes(dataBytes, numEcBytesInBlock[0]);\n            blocks.push(new BlockPair(dataBytes, ecBytes));\n            maxNumDataBytes = Math.max(maxNumDataBytes, size);\n            maxNumEcBytes = Math.max(maxNumEcBytes, ecBytes.length);\n            dataBytesOffset += numDataBytesInBlock[0];\n        }\n        if (numDataBytes !== dataBytesOffset) {\n            throw new WriterException('Data bytes does not match offset');\n        }\n        var result = new BitArray();\n        // First, place data blocks.\n        for (var i = 0; i < maxNumDataBytes; ++i) {\n            try {\n                for (var blocks_1 = (e_1 = void 0, __values(blocks)), blocks_1_1 = blocks_1.next(); !blocks_1_1.done; blocks_1_1 = blocks_1.next()) {\n                    var block = blocks_1_1.value;\n                    var dataBytes = block.getDataBytes();\n                    if (i < dataBytes.length) {\n                        result.appendBits(dataBytes[i], 8);\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (blocks_1_1 && !blocks_1_1.done && (_a = blocks_1.return)) _a.call(blocks_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n        // Then, place error correction blocks.\n        for (var i = 0; i < maxNumEcBytes; ++i) {\n            try {\n                for (var blocks_2 = (e_2 = void 0, __values(blocks)), blocks_2_1 = blocks_2.next(); !blocks_2_1.done; blocks_2_1 = blocks_2.next()) {\n                    var block = blocks_2_1.value;\n                    var ecBytes = block.getErrorCorrectionBytes();\n                    if (i < ecBytes.length) {\n                        result.appendBits(ecBytes[i], 8);\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (blocks_2_1 && !blocks_2_1.done && (_b = blocks_2.return)) _b.call(blocks_2);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        if (numTotalBytes !== result.getSizeInBytes()) { // Should be same.\n            throw new WriterException('Interleaving error: ' + numTotalBytes + ' and ' +\n                result.getSizeInBytes() + ' differ.');\n        }\n        return result;\n    };\n    Encoder.generateECBytes = function (dataBytes, numEcBytesInBlock /*int*/) {\n        var numDataBytes = dataBytes.length;\n        var toEncode = new Int32Array(numDataBytes + numEcBytesInBlock); // int[numDataBytes + numEcBytesInBlock]\n        for (var i = 0; i < numDataBytes; i++) {\n            toEncode[i] = dataBytes[i] & 0xFF;\n        }\n        new ReedSolomonEncoder(GenericGF.QR_CODE_FIELD_256).encode(toEncode, numEcBytesInBlock);\n        var ecBytes = new Uint8Array(numEcBytesInBlock);\n        for (var i = 0; i < numEcBytesInBlock; i++) {\n            ecBytes[i] = /*(byte) */ toEncode[numDataBytes + i];\n        }\n        return ecBytes;\n    };\n    /**\n     * Append mode info. On success, store the result in \"bits\".\n     */\n    Encoder.appendModeInfo = function (mode, bits) {\n        bits.appendBits(mode.getBits(), 4);\n    };\n    /**\n     * Append length info. On success, store the result in \"bits\".\n     */\n    Encoder.appendLengthInfo = function (numLetters /*int*/, version, mode, bits) {\n        var numBits = mode.getCharacterCountBits(version);\n        if (numLetters >= (1 << numBits)) {\n            throw new WriterException(numLetters + ' is bigger than ' + ((1 << numBits) - 1));\n        }\n        bits.appendBits(numLetters, numBits);\n    };\n    /**\n     * Append \"bytes\" in \"mode\" mode (encoding) into \"bits\". On success, store the result in \"bits\".\n     */\n    Encoder.appendBytes = function (content, mode, bits, encoding) {\n        switch (mode) {\n            case Mode.NUMERIC:\n                Encoder.appendNumericBytes(content, bits);\n                break;\n            case Mode.ALPHANUMERIC:\n                Encoder.appendAlphanumericBytes(content, bits);\n                break;\n            case Mode.BYTE:\n                Encoder.append8BitBytes(content, bits, encoding);\n                break;\n            case Mode.KANJI:\n                Encoder.appendKanjiBytes(content, bits);\n                break;\n            default:\n                throw new WriterException('Invalid mode: ' + mode);\n        }\n    };\n    Encoder.getDigit = function (singleCharacter) {\n        return singleCharacter.charCodeAt(0) - 48;\n    };\n    Encoder.isDigit = function (singleCharacter) {\n        var cn = Encoder.getDigit(singleCharacter);\n        return cn >= 0 && cn <= 9;\n    };\n    Encoder.appendNumericBytes = function (content, bits) {\n        var length = content.length;\n        var i = 0;\n        while (i < length) {\n            var num1 = Encoder.getDigit(content.charAt(i));\n            if (i + 2 < length) {\n                // Encode three numeric letters in ten bits.\n                var num2 = Encoder.getDigit(content.charAt(i + 1));\n                var num3 = Encoder.getDigit(content.charAt(i + 2));\n                bits.appendBits(num1 * 100 + num2 * 10 + num3, 10);\n                i += 3;\n            }\n            else if (i + 1 < length) {\n                // Encode two numeric letters in seven bits.\n                var num2 = Encoder.getDigit(content.charAt(i + 1));\n                bits.appendBits(num1 * 10 + num2, 7);\n                i += 2;\n            }\n            else {\n                // Encode one numeric letter in four bits.\n                bits.appendBits(num1, 4);\n                i++;\n            }\n        }\n    };\n    Encoder.appendAlphanumericBytes = function (content, bits) {\n        var length = content.length;\n        var i = 0;\n        while (i < length) {\n            var code1 = Encoder.getAlphanumericCode(content.charCodeAt(i));\n            if (code1 === -1) {\n                throw new WriterException();\n            }\n            if (i + 1 < length) {\n                var code2 = Encoder.getAlphanumericCode(content.charCodeAt(i + 1));\n                if (code2 === -1) {\n                    throw new WriterException();\n                }\n                // Encode two alphanumeric letters in 11 bits.\n                bits.appendBits(code1 * 45 + code2, 11);\n                i += 2;\n            }\n            else {\n                // Encode one alphanumeric letter in six bits.\n                bits.appendBits(code1, 6);\n                i++;\n            }\n        }\n    };\n    Encoder.append8BitBytes = function (content, bits, encoding) {\n        var bytes;\n        try {\n            bytes = StringEncoding.encode(content, encoding);\n        }\n        catch (uee /*: UnsupportedEncodingException*/) {\n            throw new WriterException(uee);\n        }\n        for (var i = 0, length_2 = bytes.length; i !== length_2; i++) {\n            var b = bytes[i];\n            bits.appendBits(b, 8);\n        }\n    };\n    /**\n     * @throws WriterException\n     */\n    Encoder.appendKanjiBytes = function (content, bits) {\n        var bytes;\n        try {\n            bytes = StringEncoding.encode(content, CharacterSetECI.SJIS);\n        }\n        catch (uee /*: UnsupportedEncodingException*/) {\n            throw new WriterException(uee);\n        }\n        var length = bytes.length;\n        for (var i = 0; i < length; i += 2) {\n            var byte1 = bytes[i] & 0xFF;\n            var byte2 = bytes[i + 1] & 0xFF;\n            var code = ((byte1 << 8) & 0xFFFFFFFF) | byte2;\n            var subtracted = -1;\n            if (code >= 0x8140 && code <= 0x9ffc) {\n                subtracted = code - 0x8140;\n            }\n            else if (code >= 0xe040 && code <= 0xebbf) {\n                subtracted = code - 0xc140;\n            }\n            if (subtracted === -1) {\n                throw new WriterException('Invalid byte sequence');\n            }\n            var encoded = ((subtracted >> 8) * 0xc0) + (subtracted & 0xff);\n            bits.appendBits(encoded, 13);\n        }\n    };\n    Encoder.appendECI = function (eci, bits) {\n        bits.appendBits(Mode.ECI.getBits(), 4);\n        // This is correct for values up to 127, which is all we need now.\n        bits.appendBits(eci.getValue(), 8);\n    };\n    // The original table is defined in the table 5 of JISX0510:2004 (p.19).\n    Encoder.ALPHANUMERIC_TABLE = Int32Array.from([\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n        -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n        36, -1, -1, -1, 37, 38, -1, -1, -1, -1, 39, 40, -1, 41, 42, 43,\n        0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 44, -1, -1, -1, -1, -1,\n        -1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,\n        25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, -1, -1, -1, -1, -1,\n    ]);\n    Encoder.DEFAULT_BYTE_MODE_ENCODING = CharacterSetECI.UTF8.getName(); // \"ISO-8859-1\"\n    return Encoder;\n}());\nexport default Encoder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,IAAI,MAAM,iBAAiB;AAClC,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,eAAe,MAAM,uBAAuB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAG,aAAe,YAAY;EACrC;EACA,SAASA,OAAOA,CAAA,EAAG,CACnB;EACA;EACA;EACAA,OAAO,CAACC,oBAAoB,GAAG,UAAUC,MAAM,EAAE;IAC7C,OAAOT,QAAQ,CAACU,qBAAqB,CAACD,MAAM,CAAC,GACvCT,QAAQ,CAACW,qBAAqB,CAACF,MAAM,CAAC,GACtCT,QAAQ,CAACY,qBAAqB,CAACH,MAAM,CAAC,GACtCT,QAAQ,CAACa,qBAAqB,CAACJ,MAAM,CAAC;EAChD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACAF,OAAO,CAACO,MAAM,GAAG,UAAUC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAE;IAChD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC;IACA,IAAIC,QAAQ,GAAGX,OAAO,CAACY,0BAA0B;IACjD,IAAIC,eAAe,GAAGH,KAAK,KAAK,IAAI,IAAII,SAAS,KAAKJ,KAAK,CAACK,GAAG,CAAC7B,cAAc,CAAC8B,aAAa,CAAC;IAC7F,IAAIH,eAAe,EAAE;MACjBF,QAAQ,GAAGD,KAAK,CAACK,GAAG,CAAC7B,cAAc,CAAC8B,aAAa,CAAC,CAACC,QAAQ,CAAC,CAAC;IACjE;IACA;IACA;IACA,IAAIC,IAAI,GAAG,IAAI,CAACC,UAAU,CAACX,OAAO,EAAEG,QAAQ,CAAC;IAC7C;IACA;IACA,IAAIS,UAAU,GAAG,IAAIjC,QAAQ,CAAC,CAAC;IAC/B;IACA,IAAI+B,IAAI,KAAK3B,IAAI,CAAC8B,IAAI,KAAKR,eAAe,IAAIb,OAAO,CAACY,0BAA0B,KAAKD,QAAQ,CAAC,EAAE;MAC5F,IAAIW,GAAG,GAAGlC,eAAe,CAACmC,wBAAwB,CAACZ,QAAQ,CAAC;MAC5D,IAAIW,GAAG,KAAKR,SAAS,EAAE;QACnB,IAAI,CAACU,SAAS,CAACF,GAAG,EAAEF,UAAU,CAAC;MACnC;IACJ;IACA;IACA,IAAI,CAACK,cAAc,CAACP,IAAI,EAAEE,UAAU,CAAC;IACrC;IACA;IACA,IAAIM,QAAQ,GAAG,IAAIvC,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACwC,WAAW,CAACnB,OAAO,EAAEU,IAAI,EAAEQ,QAAQ,EAAEf,QAAQ,CAAC;IACnD,IAAIiB,OAAO;IACX,IAAIlB,KAAK,KAAK,IAAI,IAAII,SAAS,KAAKJ,KAAK,CAACK,GAAG,CAAC7B,cAAc,CAAC2C,UAAU,CAAC,EAAE;MACtE,IAAIC,aAAa,GAAGC,MAAM,CAACC,QAAQ,CAACtB,KAAK,CAACK,GAAG,CAAC7B,cAAc,CAAC2C,UAAU,CAAC,CAACZ,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;MACxFW,OAAO,GAAGpC,OAAO,CAACyC,mBAAmB,CAACH,aAAa,CAAC;MACpD,IAAII,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAACjB,IAAI,EAAEE,UAAU,EAAEM,QAAQ,EAAEE,OAAO,CAAC;MAC9E,IAAI,CAAC,IAAI,CAACQ,OAAO,CAACF,UAAU,EAAEN,OAAO,EAAEnB,OAAO,CAAC,EAAE;QAC7C,MAAM,IAAIV,eAAe,CAAC,oCAAoC,CAAC;MACnE;IACJ,CAAC,MACI;MACD6B,OAAO,GAAG,IAAI,CAACS,gBAAgB,CAAC5B,OAAO,EAAES,IAAI,EAAEE,UAAU,EAAEM,QAAQ,CAAC;IACxE;IACA,IAAIY,iBAAiB,GAAG,IAAInD,QAAQ,CAAC,CAAC;IACtCmD,iBAAiB,CAACC,cAAc,CAACnB,UAAU,CAAC;IAC5C;IACA,IAAIoB,UAAU,GAAGtB,IAAI,KAAK3B,IAAI,CAAC8B,IAAI,GAAGK,QAAQ,CAACe,cAAc,CAAC,CAAC,GAAGjC,OAAO,CAAC3B,MAAM;IAChF,IAAI,CAAC6D,gBAAgB,CAACF,UAAU,EAAEZ,OAAO,EAAEV,IAAI,EAAEoB,iBAAiB,CAAC;IACnE;IACAA,iBAAiB,CAACC,cAAc,CAACb,QAAQ,CAAC;IAC1C,IAAIiB,QAAQ,GAAGf,OAAO,CAACgB,mBAAmB,CAACnC,OAAO,CAAC;IACnD,IAAIoC,YAAY,GAAGjB,OAAO,CAACkB,iBAAiB,CAAC,CAAC,GAAGH,QAAQ,CAACI,mBAAmB,CAAC,CAAC;IAC/E;IACA,IAAI,CAACC,aAAa,CAACH,YAAY,EAAEP,iBAAiB,CAAC;IACnD;IACA,IAAIW,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAACZ,iBAAiB,EAAEV,OAAO,CAACkB,iBAAiB,CAAC,CAAC,EAAED,YAAY,EAAEF,QAAQ,CAACQ,YAAY,CAAC,CAAC,CAAC;IACjI,IAAIC,MAAM,GAAG,IAAIzD,MAAM,CAAC,CAAC;IACzByD,MAAM,CAACC,UAAU,CAAC5C,OAAO,CAAC;IAC1B2C,MAAM,CAACE,OAAO,CAACpC,IAAI,CAAC;IACpBkC,MAAM,CAACG,UAAU,CAAC3B,OAAO,CAAC;IAC1B;IACA,IAAI4B,SAAS,GAAG5B,OAAO,CAAC6B,sBAAsB,CAAC,CAAC;IAChD,IAAIvD,MAAM,GAAG,IAAIR,UAAU,CAAC8D,SAAS,EAAEA,SAAS,CAAC;IACjD,IAAIE,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAACV,SAAS,EAAExC,OAAO,EAAEmB,OAAO,EAAE1B,MAAM,CAAC;IAC7EkD,MAAM,CAACQ,cAAc,CAACF,WAAW,CAAC;IAClC;IACA9D,UAAU,CAACiE,WAAW,CAACZ,SAAS,EAAExC,OAAO,EAAEmB,OAAO,EAAE8B,WAAW,EAAExD,MAAM,CAAC;IACxEkD,MAAM,CAACU,SAAS,CAAC5D,MAAM,CAAC;IACxB,OAAOkD,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIpD,OAAO,CAACqC,gBAAgB,GAAG,UAAU5B,OAAO,EAAES,IAAI,EAAEE,UAAU,EAAEM,QAAQ,EAAE;IACtE;IACA;IACA;IACA,IAAIqC,qBAAqB,GAAG,IAAI,CAAC5B,mBAAmB,CAACjB,IAAI,EAAEE,UAAU,EAAEM,QAAQ,EAAElC,OAAO,CAACyC,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAChH,IAAI+B,kBAAkB,GAAG,IAAI,CAACC,aAAa,CAACF,qBAAqB,EAAEtD,OAAO,CAAC;IAC3E;IACA,IAAIyB,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAACjB,IAAI,EAAEE,UAAU,EAAEM,QAAQ,EAAEsC,kBAAkB,CAAC;IACzF,OAAO,IAAI,CAACC,aAAa,CAAC/B,UAAU,EAAEzB,OAAO,CAAC;EAClD,CAAC;EACDT,OAAO,CAACmC,mBAAmB,GAAG,UAAUjB,IAAI,EAAEE,UAAU,EAAEM,QAAQ,EAAEE,OAAO,EAAE;IACzE,OAAOR,UAAU,CAAC8C,OAAO,CAAC,CAAC,GAAGhD,IAAI,CAACiD,qBAAqB,CAACvC,OAAO,CAAC,GAAGF,QAAQ,CAACwC,OAAO,CAAC,CAAC;EAC1F,CAAC;EACD;AACJ;AACA;AACA;EACIlE,OAAO,CAACoE,mBAAmB,GAAG,UAAUC,IAAI,CAAC,SAAS;IAClD,IAAIA,IAAI,GAAGrE,OAAO,CAACsE,kBAAkB,CAACzF,MAAM,EAAE;MAC1C,OAAOmB,OAAO,CAACsE,kBAAkB,CAACD,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC,CAAC;EACb,CAAC;EACD;EACA;EACA;EACA;AACJ;AACA;AACA;EACIrE,OAAO,CAACmB,UAAU,GAAG,UAAUX,OAAO,EAAEG,QAAQ,EAAE;IAC9C,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAG,IAAI;IAAE;IAC5C,IAAIvB,eAAe,CAACmF,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK7D,QAAQ,IAAI,IAAI,CAAC8D,qBAAqB,CAACjE,OAAO,CAAC,EAAE;MACpF;MACA,OAAOjB,IAAI,CAACmF,KAAK;IACrB;IACA,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,eAAe,GAAG,KAAK;IAC3B,KAAK,IAAIjG,CAAC,GAAG,CAAC,EAAEkG,QAAQ,GAAGrE,OAAO,CAAC3B,MAAM,EAAEF,CAAC,GAAGkG,QAAQ,EAAE,EAAElG,CAAC,EAAE;MAC1D,IAAImG,CAAC,GAAGtE,OAAO,CAACuE,MAAM,CAACpG,CAAC,CAAC;MACzB,IAAIqB,OAAO,CAACgF,OAAO,CAACF,CAAC,CAAC,EAAE;QACpBH,UAAU,GAAG,IAAI;MACrB,CAAC,MACI,IAAI,IAAI,CAACP,mBAAmB,CAACU,CAAC,CAACG,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACvDL,eAAe,GAAG,IAAI;MAC1B,CAAC,MACI;QACD,OAAOrF,IAAI,CAAC8B,IAAI;MACpB;IACJ;IACA,IAAIuD,eAAe,EAAE;MACjB,OAAOrF,IAAI,CAAC2F,YAAY;IAC5B;IACA,IAAIP,UAAU,EAAE;MACZ,OAAOpF,IAAI,CAAC4F,OAAO;IACvB;IACA,OAAO5F,IAAI,CAAC8B,IAAI;EACpB,CAAC;EACDrB,OAAO,CAACyE,qBAAqB,GAAG,UAAUjE,OAAO,EAAE;IAC/C,IAAI4E,KAAK;IACT,IAAI;MACAA,KAAK,GAAGvF,cAAc,CAACU,MAAM,CAACC,OAAO,EAAEpB,eAAe,CAACmF,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC,CACD,OAAOc,OAAO,CAAC,oCAAoC;MAC/C,OAAO,KAAK;IAChB;IACA,IAAIxG,MAAM,GAAGuG,KAAK,CAACvG,MAAM;IACzB,IAAIA,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,KAAK;IAChB;IACA,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,EAAEF,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI2G,KAAK,GAAGF,KAAK,CAACzG,CAAC,CAAC,GAAG,IAAI;MAC3B,IAAI,CAAC2G,KAAK,GAAG,IAAI,IAAIA,KAAK,GAAG,IAAI,MAAMA,KAAK,GAAG,IAAI,IAAIA,KAAK,GAAG,IAAI,CAAC,EAAE;QAClE,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDtF,OAAO,CAAC2D,iBAAiB,GAAG,UAAU4B,IAAI,EAAE9E,OAAO,EAAEmB,OAAO,EAAE1B,MAAM,EAAE;IAClE,IAAIsF,UAAU,GAAGzD,MAAM,CAAC0D,gBAAgB,CAAC,CAAC;IAC1C,IAAIC,eAAe,GAAG,CAAC,CAAC;IACxB;IACA,KAAK,IAAIhC,WAAW,GAAG,CAAC,EAAEA,WAAW,GAAG/D,MAAM,CAACgG,iBAAiB,EAAEjC,WAAW,EAAE,EAAE;MAC7E9D,UAAU,CAACiE,WAAW,CAAC0B,IAAI,EAAE9E,OAAO,EAAEmB,OAAO,EAAE8B,WAAW,EAAExD,MAAM,CAAC;MACnE,IAAI0F,OAAO,GAAG,IAAI,CAAC3F,oBAAoB,CAACC,MAAM,CAAC;MAC/C,IAAI0F,OAAO,GAAGJ,UAAU,EAAE;QACtBA,UAAU,GAAGI,OAAO;QACpBF,eAAe,GAAGhC,WAAW;MACjC;IACJ;IACA,OAAOgC,eAAe;EAC1B,CAAC;EACD1F,OAAO,CAACiE,aAAa,GAAG,UAAU4B,YAAY,CAAC,SAASpF,OAAO,EAAE;IAC7D,KAAK,IAAIqF,UAAU,GAAG,CAAC,EAAEA,UAAU,IAAI,EAAE,EAAEA,UAAU,EAAE,EAAE;MACrD,IAAIlE,OAAO,GAAGpC,OAAO,CAACyC,mBAAmB,CAAC6D,UAAU,CAAC;MACrD,IAAI9F,OAAO,CAACoC,OAAO,CAACyD,YAAY,EAAEjE,OAAO,EAAEnB,OAAO,CAAC,EAAE;QACjD,OAAOmB,OAAO;MAClB;IACJ;IACA,MAAM,IAAI7B,eAAe,CAAC,cAAc,CAAC;EAC7C,CAAC;EACD;AACJ;AACA;AACA;EACIC,OAAO,CAACoC,OAAO,GAAG,UAAUyD,YAAY,CAAC,SAASjE,OAAO,EAAEnB,OAAO,EAAE;IAChE;IACA;IACA,IAAIsF,QAAQ,GAAGnE,OAAO,CAACkB,iBAAiB,CAAC,CAAC;IAC1C;IACA,IAAIH,QAAQ,GAAGf,OAAO,CAACgB,mBAAmB,CAACnC,OAAO,CAAC;IACnD,IAAIuF,UAAU,GAAGrD,QAAQ,CAACI,mBAAmB,CAAC,CAAC;IAC/C;IACA,IAAIF,YAAY,GAAGkD,QAAQ,GAAGC,UAAU;IACxC,IAAIC,eAAe,GAAG,CAACJ,YAAY,GAAG,CAAC,IAAI,CAAC;IAC5C,OAAOhD,YAAY,IAAIoD,eAAe;EAC1C,CAAC;EACD;AACJ;AACA;EACIjG,OAAO,CAACgD,aAAa,GAAG,UAAUH,YAAY,CAAC,SAAS0C,IAAI,EAAE;IAC1D,IAAIW,QAAQ,GAAGrD,YAAY,GAAG,CAAC;IAC/B,IAAI0C,IAAI,CAACrB,OAAO,CAAC,CAAC,GAAGgC,QAAQ,EAAE;MAC3B,MAAM,IAAInG,eAAe,CAAC,qCAAqC,GAAGwF,IAAI,CAACrB,OAAO,CAAC,CAAC,GAAG,KAAK,GACpFgC,QAAQ,CAAC;IACjB;IACA,KAAK,IAAIvH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAI4G,IAAI,CAACrB,OAAO,CAAC,CAAC,GAAGgC,QAAQ,EAAE,EAAEvH,CAAC,EAAE;MACrD4G,IAAI,CAACY,SAAS,CAAC,KAAK,CAAC;IACzB;IACA;IACA;IACA,IAAIC,iBAAiB,GAAGb,IAAI,CAACrB,OAAO,CAAC,CAAC,GAAG,IAAI;IAC7C,IAAIkC,iBAAiB,GAAG,CAAC,EAAE;MACvB,KAAK,IAAIzH,CAAC,GAAGyH,iBAAiB,EAAEzH,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxC4G,IAAI,CAACY,SAAS,CAAC,KAAK,CAAC;MACzB;IACJ;IACA;IACA,IAAIE,eAAe,GAAGxD,YAAY,GAAG0C,IAAI,CAAC9C,cAAc,CAAC,CAAC;IAC1D,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0H,eAAe,EAAE,EAAE1H,CAAC,EAAE;MACtC4G,IAAI,CAACe,UAAU,CAAC,CAAC3H,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC,CAAC;IACtD;IACA,IAAI4G,IAAI,CAACrB,OAAO,CAAC,CAAC,KAAKgC,QAAQ,EAAE;MAC7B,MAAM,IAAInG,eAAe,CAAC,mCAAmC,CAAC;IAClE;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIC,OAAO,CAACuG,sCAAsC,GAAG,UAAUC,aAAa,CAAC,SAAS3D,YAAY,CAAC,SAAS4D,WAAW,CAAC,SAASC,OAAO,CAAC,SAASC,mBAAmB,EAAEC,iBAAiB,EAAE;IAClL,IAAIF,OAAO,IAAID,WAAW,EAAE;MACxB,MAAM,IAAI1G,eAAe,CAAC,oBAAoB,CAAC;IACnD;IACA;IACA,IAAI8G,mBAAmB,GAAGL,aAAa,GAAGC,WAAW;IACrD;IACA,IAAIK,mBAAmB,GAAGL,WAAW,GAAGI,mBAAmB;IAC3D;IACA,IAAIE,qBAAqB,GAAGC,IAAI,CAACC,KAAK,CAACT,aAAa,GAAGC,WAAW,CAAC;IACnE;IACA,IAAIS,qBAAqB,GAAGH,qBAAqB,GAAG,CAAC;IACrD;IACA,IAAII,oBAAoB,GAAGH,IAAI,CAACC,KAAK,CAACpE,YAAY,GAAG4D,WAAW,CAAC;IACjE;IACA,IAAIW,oBAAoB,GAAGD,oBAAoB,GAAG,CAAC;IACnD;IACA,IAAIE,kBAAkB,GAAGN,qBAAqB,GAAGI,oBAAoB;IACrE;IACA,IAAIG,kBAAkB,GAAGJ,qBAAqB,GAAGE,oBAAoB;IACrE;IACA;IACA,IAAIC,kBAAkB,KAAKC,kBAAkB,EAAE;MAC3C,MAAM,IAAIvH,eAAe,CAAC,mBAAmB,CAAC;IAClD;IACA;IACA,IAAI0G,WAAW,KAAKK,mBAAmB,GAAGD,mBAAmB,EAAE;MAC3D,MAAM,IAAI9G,eAAe,CAAC,oBAAoB,CAAC;IACnD;IACA;IACA,IAAIyG,aAAa,KACZ,CAACW,oBAAoB,GAAGE,kBAAkB,IACvCP,mBAAmB,GAClB,CAACM,oBAAoB,GAAGE,kBAAkB,IACvCT,mBAAoB,EAAE;MAC9B,MAAM,IAAI9G,eAAe,CAAC,sBAAsB,CAAC;IACrD;IACA,IAAI2G,OAAO,GAAGI,mBAAmB,EAAE;MAC/BH,mBAAmB,CAAC,CAAC,CAAC,GAAGQ,oBAAoB;MAC7CP,iBAAiB,CAAC,CAAC,CAAC,GAAGS,kBAAkB;IAC7C,CAAC,MACI;MACDV,mBAAmB,CAAC,CAAC,CAAC,GAAGS,oBAAoB;MAC7CR,iBAAiB,CAAC,CAAC,CAAC,GAAGU,kBAAkB;IAC7C;EACJ,CAAC;EACD;AACJ;AACA;AACA;EACItH,OAAO,CAACkD,qBAAqB,GAAG,UAAUqC,IAAI,EAAEiB,aAAa,CAAC,SAAS3D,YAAY,CAAC,SAAS4D,WAAW,CAAC,SAAS;IAC9G,IAAIc,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB;IACA,IAAInC,IAAI,CAAC9C,cAAc,CAAC,CAAC,KAAKI,YAAY,EAAE;MACxC,MAAM,IAAI9C,eAAe,CAAC,8CAA8C,CAAC;IAC7E;IACA;IACA;IACA,IAAI4H,eAAe,GAAG,CAAC;IACvB,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIC,aAAa,GAAG,CAAC;IACrB;IACA,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAIpJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8H,WAAW,EAAE,EAAE9H,CAAC,EAAE;MAClC,IAAIgI,mBAAmB,GAAG,IAAIqB,UAAU,CAAC,CAAC,CAAC;MAC3C,IAAIC,iBAAiB,GAAG,IAAID,UAAU,CAAC,CAAC,CAAC;MACzChI,OAAO,CAACuG,sCAAsC,CAACC,aAAa,EAAE3D,YAAY,EAAE4D,WAAW,EAAE9H,CAAC,EAAEgI,mBAAmB,EAAEsB,iBAAiB,CAAC;MACnI,IAAIC,IAAI,GAAGvB,mBAAmB,CAAC,CAAC,CAAC;MACjC,IAAIwB,SAAS,GAAG,IAAIC,UAAU,CAACF,IAAI,CAAC;MACpC3C,IAAI,CAAC8C,OAAO,CAAC,CAAC,GAAGV,eAAe,EAAEQ,SAAS,EAAE,CAAC,EAAED,IAAI,CAAC;MACrD,IAAII,OAAO,GAAGtI,OAAO,CAACuI,eAAe,CAACJ,SAAS,EAAEF,iBAAiB,CAAC,CAAC,CAAC,CAAC;MACtEH,MAAM,CAACU,IAAI,CAAC,IAAI1I,SAAS,CAACqI,SAAS,EAAEG,OAAO,CAAC,CAAC;MAC9CV,eAAe,GAAGZ,IAAI,CAACyB,GAAG,CAACb,eAAe,EAAEM,IAAI,CAAC;MACjDL,aAAa,GAAGb,IAAI,CAACyB,GAAG,CAACZ,aAAa,EAAES,OAAO,CAACzJ,MAAM,CAAC;MACvD8I,eAAe,IAAIhB,mBAAmB,CAAC,CAAC,CAAC;IAC7C;IACA,IAAI9D,YAAY,KAAK8E,eAAe,EAAE;MAClC,MAAM,IAAI5H,eAAe,CAAC,kCAAkC,CAAC;IACjE;IACA,IAAI2I,MAAM,GAAG,IAAIvJ,QAAQ,CAAC,CAAC;IAC3B;IACA,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiJ,eAAe,EAAE,EAAEjJ,CAAC,EAAE;MACtC,IAAI;QACA,KAAK,IAAIgK,QAAQ,IAAIpB,GAAG,GAAG,KAAK,CAAC,EAAElJ,QAAQ,CAACyJ,MAAM,CAAC,CAAC,EAAEc,UAAU,GAAGD,QAAQ,CAAC7J,IAAI,CAAC,CAAC,EAAE,CAAC8J,UAAU,CAAC5J,IAAI,EAAE4J,UAAU,GAAGD,QAAQ,CAAC7J,IAAI,CAAC,CAAC,EAAE;UAChI,IAAI+J,KAAK,GAAGD,UAAU,CAAC7J,KAAK;UAC5B,IAAIoJ,SAAS,GAAGU,KAAK,CAACC,YAAY,CAAC,CAAC;UACpC,IAAInK,CAAC,GAAGwJ,SAAS,CAACtJ,MAAM,EAAE;YACtB6J,MAAM,CAACpC,UAAU,CAAC6B,SAAS,CAACxJ,CAAC,CAAC,EAAE,CAAC,CAAC;UACtC;QACJ;MACJ,CAAC,CACD,OAAOoK,KAAK,EAAE;QAAExB,GAAG,GAAG;UAAEyB,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIH,UAAU,IAAI,CAACA,UAAU,CAAC5J,IAAI,KAAKwI,EAAE,GAAGmB,QAAQ,CAACM,MAAM,CAAC,EAAEzB,EAAE,CAAC5I,IAAI,CAAC+J,QAAQ,CAAC;QACnF,CAAC,SACO;UAAE,IAAIpB,GAAG,EAAE,MAAMA,GAAG,CAACyB,KAAK;QAAE;MACxC;IACJ;IACA;IACA,KAAK,IAAIrK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkJ,aAAa,EAAE,EAAElJ,CAAC,EAAE;MACpC,IAAI;QACA,KAAK,IAAIuK,QAAQ,IAAIzB,GAAG,GAAG,KAAK,CAAC,EAAEpJ,QAAQ,CAACyJ,MAAM,CAAC,CAAC,EAAEqB,UAAU,GAAGD,QAAQ,CAACpK,IAAI,CAAC,CAAC,EAAE,CAACqK,UAAU,CAACnK,IAAI,EAAEmK,UAAU,GAAGD,QAAQ,CAACpK,IAAI,CAAC,CAAC,EAAE;UAChI,IAAI+J,KAAK,GAAGM,UAAU,CAACpK,KAAK;UAC5B,IAAIuJ,OAAO,GAAGO,KAAK,CAACO,uBAAuB,CAAC,CAAC;UAC7C,IAAIzK,CAAC,GAAG2J,OAAO,CAACzJ,MAAM,EAAE;YACpB6J,MAAM,CAACpC,UAAU,CAACgC,OAAO,CAAC3J,CAAC,CAAC,EAAE,CAAC,CAAC;UACpC;QACJ;MACJ,CAAC,CACD,OAAO0K,KAAK,EAAE;QAAE5B,GAAG,GAAG;UAAEuB,KAAK,EAAEK;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,UAAU,IAAI,CAACA,UAAU,CAACnK,IAAI,KAAK0I,EAAE,GAAGwB,QAAQ,CAACD,MAAM,CAAC,EAAEvB,EAAE,CAAC9I,IAAI,CAACsK,QAAQ,CAAC;QACnF,CAAC,SACO;UAAE,IAAIzB,GAAG,EAAE,MAAMA,GAAG,CAACuB,KAAK;QAAE;MACxC;IACJ;IACA,IAAIxC,aAAa,KAAKkC,MAAM,CAACjG,cAAc,CAAC,CAAC,EAAE;MAAE;MAC7C,MAAM,IAAI1C,eAAe,CAAC,sBAAsB,GAAGyG,aAAa,GAAG,OAAO,GACtEkC,MAAM,CAACjG,cAAc,CAAC,CAAC,GAAG,UAAU,CAAC;IAC7C;IACA,OAAOiG,MAAM;EACjB,CAAC;EACD1I,OAAO,CAACuI,eAAe,GAAG,UAAUJ,SAAS,EAAEF,iBAAiB,CAAC,SAAS;IACtE,IAAIpF,YAAY,GAAGsF,SAAS,CAACtJ,MAAM;IACnC,IAAIyK,QAAQ,GAAG,IAAItB,UAAU,CAACnF,YAAY,GAAGoF,iBAAiB,CAAC,CAAC,CAAC;IACjE,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkE,YAAY,EAAElE,CAAC,EAAE,EAAE;MACnC2K,QAAQ,CAAC3K,CAAC,CAAC,GAAGwJ,SAAS,CAACxJ,CAAC,CAAC,GAAG,IAAI;IACrC;IACA,IAAIW,kBAAkB,CAACD,SAAS,CAACkK,iBAAiB,CAAC,CAAChJ,MAAM,CAAC+I,QAAQ,EAAErB,iBAAiB,CAAC;IACvF,IAAIK,OAAO,GAAG,IAAIF,UAAU,CAACH,iBAAiB,CAAC;IAC/C,KAAK,IAAItJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsJ,iBAAiB,EAAEtJ,CAAC,EAAE,EAAE;MACxC2J,OAAO,CAAC3J,CAAC,CAAC,GAAG,WAAY2K,QAAQ,CAACzG,YAAY,GAAGlE,CAAC,CAAC;IACvD;IACA,OAAO2J,OAAO;EAClB,CAAC;EACD;AACJ;AACA;EACItI,OAAO,CAACyB,cAAc,GAAG,UAAUP,IAAI,EAAEqE,IAAI,EAAE;IAC3CA,IAAI,CAACe,UAAU,CAACpF,IAAI,CAACsI,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACtC,CAAC;EACD;AACJ;AACA;EACIxJ,OAAO,CAAC0C,gBAAgB,GAAG,UAAUF,UAAU,CAAC,SAASZ,OAAO,EAAEV,IAAI,EAAEqE,IAAI,EAAE;IAC1E,IAAIkE,OAAO,GAAGvI,IAAI,CAACiD,qBAAqB,CAACvC,OAAO,CAAC;IACjD,IAAIY,UAAU,IAAK,CAAC,IAAIiH,OAAQ,EAAE;MAC9B,MAAM,IAAI1J,eAAe,CAACyC,UAAU,GAAG,kBAAkB,IAAI,CAAC,CAAC,IAAIiH,OAAO,IAAI,CAAC,CAAC,CAAC;IACrF;IACAlE,IAAI,CAACe,UAAU,CAAC9D,UAAU,EAAEiH,OAAO,CAAC;EACxC,CAAC;EACD;AACJ;AACA;EACIzJ,OAAO,CAAC2B,WAAW,GAAG,UAAUnB,OAAO,EAAEU,IAAI,EAAEqE,IAAI,EAAE5E,QAAQ,EAAE;IAC3D,QAAQO,IAAI;MACR,KAAK3B,IAAI,CAAC4F,OAAO;QACbnF,OAAO,CAAC0J,kBAAkB,CAAClJ,OAAO,EAAE+E,IAAI,CAAC;QACzC;MACJ,KAAKhG,IAAI,CAAC2F,YAAY;QAClBlF,OAAO,CAAC2J,uBAAuB,CAACnJ,OAAO,EAAE+E,IAAI,CAAC;QAC9C;MACJ,KAAKhG,IAAI,CAAC8B,IAAI;QACVrB,OAAO,CAAC4J,eAAe,CAACpJ,OAAO,EAAE+E,IAAI,EAAE5E,QAAQ,CAAC;QAChD;MACJ,KAAKpB,IAAI,CAACmF,KAAK;QACX1E,OAAO,CAAC6J,gBAAgB,CAACrJ,OAAO,EAAE+E,IAAI,CAAC;QACvC;MACJ;QACI,MAAM,IAAIxF,eAAe,CAAC,gBAAgB,GAAGmB,IAAI,CAAC;IAC1D;EACJ,CAAC;EACDlB,OAAO,CAAC8J,QAAQ,GAAG,UAAUC,eAAe,EAAE;IAC1C,OAAOA,eAAe,CAAC9E,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE;EAC7C,CAAC;EACDjF,OAAO,CAACgF,OAAO,GAAG,UAAU+E,eAAe,EAAE;IACzC,IAAIC,EAAE,GAAGhK,OAAO,CAAC8J,QAAQ,CAACC,eAAe,CAAC;IAC1C,OAAOC,EAAE,IAAI,CAAC,IAAIA,EAAE,IAAI,CAAC;EAC7B,CAAC;EACDhK,OAAO,CAAC0J,kBAAkB,GAAG,UAAUlJ,OAAO,EAAE+E,IAAI,EAAE;IAClD,IAAI1G,MAAM,GAAG2B,OAAO,CAAC3B,MAAM;IAC3B,IAAIF,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGE,MAAM,EAAE;MACf,IAAIoL,IAAI,GAAGjK,OAAO,CAAC8J,QAAQ,CAACtJ,OAAO,CAACuE,MAAM,CAACpG,CAAC,CAAC,CAAC;MAC9C,IAAIA,CAAC,GAAG,CAAC,GAAGE,MAAM,EAAE;QAChB;QACA,IAAIqL,IAAI,GAAGlK,OAAO,CAAC8J,QAAQ,CAACtJ,OAAO,CAACuE,MAAM,CAACpG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,IAAIwL,IAAI,GAAGnK,OAAO,CAAC8J,QAAQ,CAACtJ,OAAO,CAACuE,MAAM,CAACpG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD4G,IAAI,CAACe,UAAU,CAAC2D,IAAI,GAAG,GAAG,GAAGC,IAAI,GAAG,EAAE,GAAGC,IAAI,EAAE,EAAE,CAAC;QAClDxL,CAAC,IAAI,CAAC;MACV,CAAC,MACI,IAAIA,CAAC,GAAG,CAAC,GAAGE,MAAM,EAAE;QACrB;QACA,IAAIqL,IAAI,GAAGlK,OAAO,CAAC8J,QAAQ,CAACtJ,OAAO,CAACuE,MAAM,CAACpG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD4G,IAAI,CAACe,UAAU,CAAC2D,IAAI,GAAG,EAAE,GAAGC,IAAI,EAAE,CAAC,CAAC;QACpCvL,CAAC,IAAI,CAAC;MACV,CAAC,MACI;QACD;QACA4G,IAAI,CAACe,UAAU,CAAC2D,IAAI,EAAE,CAAC,CAAC;QACxBtL,CAAC,EAAE;MACP;IACJ;EACJ,CAAC;EACDqB,OAAO,CAAC2J,uBAAuB,GAAG,UAAUnJ,OAAO,EAAE+E,IAAI,EAAE;IACvD,IAAI1G,MAAM,GAAG2B,OAAO,CAAC3B,MAAM;IAC3B,IAAIF,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAGE,MAAM,EAAE;MACf,IAAIuL,KAAK,GAAGpK,OAAO,CAACoE,mBAAmB,CAAC5D,OAAO,CAACyE,UAAU,CAACtG,CAAC,CAAC,CAAC;MAC9D,IAAIyL,KAAK,KAAK,CAAC,CAAC,EAAE;QACd,MAAM,IAAIrK,eAAe,CAAC,CAAC;MAC/B;MACA,IAAIpB,CAAC,GAAG,CAAC,GAAGE,MAAM,EAAE;QAChB,IAAIwL,KAAK,GAAGrK,OAAO,CAACoE,mBAAmB,CAAC5D,OAAO,CAACyE,UAAU,CAACtG,CAAC,GAAG,CAAC,CAAC,CAAC;QAClE,IAAI0L,KAAK,KAAK,CAAC,CAAC,EAAE;UACd,MAAM,IAAItK,eAAe,CAAC,CAAC;QAC/B;QACA;QACAwF,IAAI,CAACe,UAAU,CAAC8D,KAAK,GAAG,EAAE,GAAGC,KAAK,EAAE,EAAE,CAAC;QACvC1L,CAAC,IAAI,CAAC;MACV,CAAC,MACI;QACD;QACA4G,IAAI,CAACe,UAAU,CAAC8D,KAAK,EAAE,CAAC,CAAC;QACzBzL,CAAC,EAAE;MACP;IACJ;EACJ,CAAC;EACDqB,OAAO,CAAC4J,eAAe,GAAG,UAAUpJ,OAAO,EAAE+E,IAAI,EAAE5E,QAAQ,EAAE;IACzD,IAAIyE,KAAK;IACT,IAAI;MACAA,KAAK,GAAGvF,cAAc,CAACU,MAAM,CAACC,OAAO,EAAEG,QAAQ,CAAC;IACpD,CAAC,CACD,OAAO2J,GAAG,CAAC,oCAAoC;MAC3C,MAAM,IAAIvK,eAAe,CAACuK,GAAG,CAAC;IAClC;IACA,KAAK,IAAI3L,CAAC,GAAG,CAAC,EAAE4L,QAAQ,GAAGnF,KAAK,CAACvG,MAAM,EAAEF,CAAC,KAAK4L,QAAQ,EAAE5L,CAAC,EAAE,EAAE;MAC1D,IAAI6L,CAAC,GAAGpF,KAAK,CAACzG,CAAC,CAAC;MAChB4G,IAAI,CAACe,UAAU,CAACkE,CAAC,EAAE,CAAC,CAAC;IACzB;EACJ,CAAC;EACD;AACJ;AACA;EACIxK,OAAO,CAAC6J,gBAAgB,GAAG,UAAUrJ,OAAO,EAAE+E,IAAI,EAAE;IAChD,IAAIH,KAAK;IACT,IAAI;MACAA,KAAK,GAAGvF,cAAc,CAACU,MAAM,CAACC,OAAO,EAAEpB,eAAe,CAACmF,IAAI,CAAC;IAChE,CAAC,CACD,OAAO+F,GAAG,CAAC,oCAAoC;MAC3C,MAAM,IAAIvK,eAAe,CAACuK,GAAG,CAAC;IAClC;IACA,IAAIzL,MAAM,GAAGuG,KAAK,CAACvG,MAAM;IACzB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,EAAEF,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI2G,KAAK,GAAGF,KAAK,CAACzG,CAAC,CAAC,GAAG,IAAI;MAC3B,IAAI8L,KAAK,GAAGrF,KAAK,CAACzG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;MAC/B,IAAI0F,IAAI,GAAKiB,KAAK,IAAI,CAAC,GAAI,UAAU,GAAImF,KAAK;MAC9C,IAAIC,UAAU,GAAG,CAAC,CAAC;MACnB,IAAIrG,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;QAClCqG,UAAU,GAAGrG,IAAI,GAAG,MAAM;MAC9B,CAAC,MACI,IAAIA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;QACvCqG,UAAU,GAAGrG,IAAI,GAAG,MAAM;MAC9B;MACA,IAAIqG,UAAU,KAAK,CAAC,CAAC,EAAE;QACnB,MAAM,IAAI3K,eAAe,CAAC,uBAAuB,CAAC;MACtD;MACA,IAAI4K,OAAO,GAAI,CAACD,UAAU,IAAI,CAAC,IAAI,IAAI,IAAKA,UAAU,GAAG,IAAI,CAAC;MAC9DnF,IAAI,CAACe,UAAU,CAACqE,OAAO,EAAE,EAAE,CAAC;IAChC;EACJ,CAAC;EACD3K,OAAO,CAACwB,SAAS,GAAG,UAAUF,GAAG,EAAEiE,IAAI,EAAE;IACrCA,IAAI,CAACe,UAAU,CAAC/G,IAAI,CAACqL,GAAG,CAACpB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;IACtC;IACAjE,IAAI,CAACe,UAAU,CAAChF,GAAG,CAACuJ,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACtC,CAAC;EACD;EACA7K,OAAO,CAACsE,kBAAkB,GAAG0D,UAAU,CAAC8C,IAAI,CAAC,CACzC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAC9D,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAC9D,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC9D,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EACpD,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAC9D,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CACjE,CAAC;EACF9K,OAAO,CAACY,0BAA0B,GAAGxB,eAAe,CAAC2L,IAAI,CAACvG,OAAO,CAAC,CAAC,CAAC,CAAC;EACrE,OAAOxE,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}