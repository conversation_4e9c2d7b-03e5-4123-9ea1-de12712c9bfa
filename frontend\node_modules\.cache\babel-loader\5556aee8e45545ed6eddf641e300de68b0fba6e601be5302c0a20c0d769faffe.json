{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\nimport UPCEANReader from './UPCEANReader';\n/**\n * <p>Implements decoding of the EAN-8 format.</p>\n *\n * <AUTHOR> Owen\n */\nvar EAN8Reader = /** @class */function (_super) {\n  __extends(EAN8Reader, _super);\n  function EAN8Reader() {\n    var _this = _super.call(this) || this;\n    _this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n    return _this;\n  }\n  EAN8Reader.prototype.decodeMiddle = function (row, startRange, resultString) {\n    var e_1, _a, e_2, _b;\n    var counters = this.decodeMiddleCounters;\n    counters[0] = 0;\n    counters[1] = 0;\n    counters[2] = 0;\n    counters[3] = 0;\n    var end = row.getSize();\n    var rowOffset = startRange[1];\n    for (var x = 0; x < 4 && rowOffset < end; x++) {\n      var bestMatch = UPCEANReader.decodeDigit(row, counters, rowOffset, UPCEANReader.L_PATTERNS);\n      resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch);\n      try {\n        for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n          var counter = counters_1_1.value;\n          rowOffset += counter;\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }\n    var middleRange = UPCEANReader.findGuardPattern(row, rowOffset, true, UPCEANReader.MIDDLE_PATTERN, new Int32Array(UPCEANReader.MIDDLE_PATTERN.length).fill(0));\n    rowOffset = middleRange[1];\n    for (var x = 0; x < 4 && rowOffset < end; x++) {\n      var bestMatch = UPCEANReader.decodeDigit(row, counters, rowOffset, UPCEANReader.L_PATTERNS);\n      resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch);\n      try {\n        for (var counters_2 = (e_2 = void 0, __values(counters)), counters_2_1 = counters_2.next(); !counters_2_1.done; counters_2_1 = counters_2.next()) {\n          var counter = counters_2_1.value;\n          rowOffset += counter;\n        }\n      } catch (e_2_1) {\n        e_2 = {\n          error: e_2_1\n        };\n      } finally {\n        try {\n          if (counters_2_1 && !counters_2_1.done && (_b = counters_2.return)) _b.call(counters_2);\n        } finally {\n          if (e_2) throw e_2.error;\n        }\n      }\n    }\n    return {\n      rowOffset: rowOffset,\n      resultString: resultString\n    };\n  };\n  EAN8Reader.prototype.getBarcodeFormat = function () {\n    return BarcodeFormat.EAN_8;\n  };\n  return EAN8Reader;\n}(UPCEANReader);\nexport default EAN8Reader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BarcodeFormat", "UPCEANReader", "EAN8Reader", "_super", "_this", "decodeMiddleCounters", "Int32Array", "from", "decodeMiddle", "row", "startRange", "resultString", "e_1", "_a", "e_2", "_b", "counters", "end", "getSize", "rowOffset", "x", "bestMatch", "decodeDigit", "L_PATTERNS", "String", "fromCharCode", "charCodeAt", "counters_1", "counters_1_1", "counter", "e_1_1", "error", "return", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>att<PERSON>", "MIDDLE_PATTERN", "fill", "counters_2", "counters_2_1", "e_2_1", "getBarcodeFormat", "EAN_8"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/EAN8Reader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\nimport UPCEANReader from './UPCEANReader';\n/**\n * <p>Implements decoding of the EAN-8 format.</p>\n *\n * <AUTHOR> Owen\n */\nvar EAN8Reader = /** @class */ (function (_super) {\n    __extends(EAN8Reader, _super);\n    function EAN8Reader() {\n        var _this = _super.call(this) || this;\n        _this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n        return _this;\n    }\n    EAN8Reader.prototype.decodeMiddle = function (row, startRange, resultString) {\n        var e_1, _a, e_2, _b;\n        var counters = this.decodeMiddleCounters;\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        for (var x = 0; x < 4 && rowOffset < end; x++) {\n            var bestMatch = UPCEANReader.decodeDigit(row, counters, rowOffset, UPCEANReader.L_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n        var middleRange = UPCEANReader.findGuardPattern(row, rowOffset, true, UPCEANReader.MIDDLE_PATTERN, new Int32Array(UPCEANReader.MIDDLE_PATTERN.length).fill(0));\n        rowOffset = middleRange[1];\n        for (var x = 0; x < 4 && rowOffset < end; x++) {\n            var bestMatch = UPCEANReader.decodeDigit(row, counters, rowOffset, UPCEANReader.L_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch));\n            try {\n                for (var counters_2 = (e_2 = void 0, __values(counters)), counters_2_1 = counters_2.next(); !counters_2_1.done; counters_2_1 = counters_2.next()) {\n                    var counter = counters_2_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (counters_2_1 && !counters_2_1.done && (_b = counters_2.return)) _b.call(counters_2);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n        }\n        return { rowOffset: rowOffset, resultString: resultString };\n    };\n    EAN8Reader.prototype.getBarcodeFormat = function () {\n        return BarcodeFormat.EAN_8;\n    };\n    return EAN8Reader;\n}(UPCEANReader));\nexport default EAN8Reader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,YAAY,MAAM,gBAAgB;AACzC;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC9C9B,SAAS,CAAC6B,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IAClB,IAAIE,KAAK,GAAGD,MAAM,CAACT,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCU,KAAK,CAACC,oBAAoB,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1D,OAAOH,KAAK;EAChB;EACAF,UAAU,CAACjB,SAAS,CAACuB,YAAY,GAAG,UAAUC,GAAG,EAAEC,UAAU,EAAEC,YAAY,EAAE;IACzE,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB,IAAIC,QAAQ,GAAG,IAAI,CAACX,oBAAoB;IACxCW,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACf,IAAIC,GAAG,GAAGR,GAAG,CAACS,OAAO,CAAC,CAAC;IACvB,IAAIC,SAAS,GAAGT,UAAU,CAAC,CAAC,CAAC;IAC7B,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAID,SAAS,GAAGF,GAAG,EAAEG,CAAC,EAAE,EAAE;MAC3C,IAAIC,SAAS,GAAGpB,YAAY,CAACqB,WAAW,CAACb,GAAG,EAAEO,QAAQ,EAAEG,SAAS,EAAElB,YAAY,CAACsB,UAAU,CAAC;MAC3FZ,YAAY,IAAIa,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGL,SAAU,CAAC;MACpE,IAAI;QACA,KAAK,IAAIM,UAAU,IAAIf,GAAG,GAAG,KAAK,CAAC,EAAEzB,QAAQ,CAAC6B,QAAQ,CAAC,CAAC,EAAEY,YAAY,GAAGD,UAAU,CAAC/B,IAAI,CAAC,CAAC,EAAE,CAACgC,YAAY,CAAC9B,IAAI,EAAE8B,YAAY,GAAGD,UAAU,CAAC/B,IAAI,CAAC,CAAC,EAAE;UAC9I,IAAIiC,OAAO,GAAGD,YAAY,CAAC/B,KAAK;UAChCsB,SAAS,IAAIU,OAAO;QACxB;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAElB,GAAG,GAAG;UAAEmB,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,YAAY,IAAI,CAACA,YAAY,CAAC9B,IAAI,KAAKe,EAAE,GAAGc,UAAU,CAACK,MAAM,CAAC,EAAEnB,EAAE,CAACnB,IAAI,CAACiC,UAAU,CAAC;QAC3F,CAAC,SACO;UAAE,IAAIf,GAAG,EAAE,MAAMA,GAAG,CAACmB,KAAK;QAAE;MACxC;IACJ;IACA,IAAIE,WAAW,GAAGhC,YAAY,CAACiC,gBAAgB,CAACzB,GAAG,EAAEU,SAAS,EAAE,IAAI,EAAElB,YAAY,CAACkC,cAAc,EAAE,IAAI7B,UAAU,CAACL,YAAY,CAACkC,cAAc,CAACxC,MAAM,CAAC,CAACyC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9JjB,SAAS,GAAGc,WAAW,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAID,SAAS,GAAGF,GAAG,EAAEG,CAAC,EAAE,EAAE;MAC3C,IAAIC,SAAS,GAAGpB,YAAY,CAACqB,WAAW,CAACb,GAAG,EAAEO,QAAQ,EAAEG,SAAS,EAAElB,YAAY,CAACsB,UAAU,CAAC;MAC3FZ,YAAY,IAAIa,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGL,SAAU,CAAC;MACpE,IAAI;QACA,KAAK,IAAIgB,UAAU,IAAIvB,GAAG,GAAG,KAAK,CAAC,EAAE3B,QAAQ,CAAC6B,QAAQ,CAAC,CAAC,EAAEsB,YAAY,GAAGD,UAAU,CAACzC,IAAI,CAAC,CAAC,EAAE,CAAC0C,YAAY,CAACxC,IAAI,EAAEwC,YAAY,GAAGD,UAAU,CAACzC,IAAI,CAAC,CAAC,EAAE;UAC9I,IAAIiC,OAAO,GAAGS,YAAY,CAACzC,KAAK;UAChCsB,SAAS,IAAIU,OAAO;QACxB;MACJ,CAAC,CACD,OAAOU,KAAK,EAAE;QAAEzB,GAAG,GAAG;UAAEiB,KAAK,EAAEQ;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAID,YAAY,IAAI,CAACA,YAAY,CAACxC,IAAI,KAAKiB,EAAE,GAAGsB,UAAU,CAACL,MAAM,CAAC,EAAEjB,EAAE,CAACrB,IAAI,CAAC2C,UAAU,CAAC;QAC3F,CAAC,SACO;UAAE,IAAIvB,GAAG,EAAE,MAAMA,GAAG,CAACiB,KAAK;QAAE;MACxC;IACJ;IACA,OAAO;MAAEZ,SAAS,EAAEA,SAAS;MAAER,YAAY,EAAEA;IAAa,CAAC;EAC/D,CAAC;EACDT,UAAU,CAACjB,SAAS,CAACuD,gBAAgB,GAAG,YAAY;IAChD,OAAOxC,aAAa,CAACyC,KAAK;EAC9B,CAAC;EACD,OAAOvC,UAAU;AACrB,CAAC,CAACD,YAAY,CAAE;AAChB,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}