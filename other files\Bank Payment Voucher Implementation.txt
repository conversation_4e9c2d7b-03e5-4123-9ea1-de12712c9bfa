Implementation Payment Transactions – Bank Payment Module

We are now starting the implementation of **Payment Transactions** in the Accounting & Finance module.

The following transaction types are already available on the landing page:

1. **Bank Payments**
2. **Bank Receipts**
3. **Cash Payments**
4. **Cash Receipts**

Let’s begin with **Bank Payments**.

---

### ✅ Objective: Bank Payment Voucher

The **Bank Payment Voucher** will be used to record outgoing payments made through bank accounts. These payments may include:

* Payment to **vendors** (against purchase invoices)
* Refunds to **customers** (e.g., due to sales returns)
* **Other expenses** such as:

  * Utility bills
  * Shop rent
  * Employee salaries
  * Sales Tax payable
  * Withholding Tax payable, etc.

---

### 🔁 Workflow

1. **User selects a bank account** from a dropdown.

   * This bank account will be recorded on the **credit side** of the transaction.

2. **User selects the debit account** from a dropdown (e.g., vendor payable, utility expense, etc.).

   * These accounts will be fetched from the **Chart of Accounts**.

3. If the **debit account is a vendor**, the user must select a **Purchase Invoice** from a dropdown list:

   * The system should **ensure the payment applies to the selected invoice**.
   * If the invoice has an associated **purchase return** with Approved status not Pending, the system should:

     * Automatically adjust the payable amount after deducting returns.
     * Prevent overpayment or duplicate payment.
   * **Partial payments** must be supported:

     * If the invoice total is PKR 100,000 and the user pays PKR 50,000, the remaining PKR 50,000 remains outstanding.
     * The same invoice should **remain available** in the dropdown for future payments until fully settled.

---

### 📋 Fields in Bank Payment Voucher

| Field                | Description                                                                                |
| -------------------- | ------------------------------------------------------------------------------------------ |
| **Voucher No.**      | Auto-generated unique ID (e.g., BP-1001)                                                   |
| **Voucher Date**     | Displayed in `dd/mmm/yyyy` format (e.g., 17/Jun/2025)                                      |
| **Transaction Date** | Date the payment was actually made; `dd/mmm/yyyy` format                                   |
| **Transaction ID**   | Optional field for manually entering an internal reference or payment ID                   |
| **Bank**             | Dropdown to select the bank (this becomes the credit account)                              |
| **Narration**        | A text field to describe the payment purpose                                               |
| **Title**            | Dropdown to select the **debit account** (from Chart of Accounts)                          |
| **Purchase Invoice** | (Conditional) Dropdown to select the relevant Purchase Invoice (if payment is to a vendor) |
| **Amount**           | The payment amount being made (entered manually by the user)                               |

---

### 🧠 Notes & Validation Rules

* **Voucher number** must be unique and auto-incremented.
* If a **Purchase Invoice** is selected, its associated **Vendor name** should auto-populate in the **Title** field.
* The system must track:

  * Total amount paid against each invoice.
  * Outstanding balance (in case of partial payment).
* The **Purchase Invoice dropdown** must show only **unpaid or partially paid invoices**.
* Vendor and invoice linkage should be strict — a user shouldn't pay a different vendor using an unrelated invoice.
