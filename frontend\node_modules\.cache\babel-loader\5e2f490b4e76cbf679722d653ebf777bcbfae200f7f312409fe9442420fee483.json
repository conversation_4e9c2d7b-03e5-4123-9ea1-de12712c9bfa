{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\n// import DetectorResult from '../common/DetectorResult';\nimport Decoder from './decoder/Decoder';\nimport QRCodeDecoderMetaData from './decoder/QRCodeDecoderMetaData';\nimport Detector from './detector/Detector';\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * This implementation can detect and decode QR Codes in an image.\n *\n * <AUTHOR> Owen\n */\nvar QRCodeReader = /** @class */function () {\n  function QRCodeReader() {\n    this.decoder = new Decoder();\n  }\n  QRCodeReader.prototype.getDecoder = function () {\n    return this.decoder;\n  };\n  /**\n   * Locates and decodes a QR code in an image.\n   *\n   * @return a representing: string the content encoded by the QR code\n   * @throws NotFoundException if a QR code cannot be found\n   * @throws FormatException if a QR code cannot be decoded\n   * @throws ChecksumException if error correction fails\n   */\n  /*@Override*/\n  // public decode(image: BinaryBitmap): Result /*throws NotFoundException, ChecksumException, FormatException */ {\n  //   return this.decode(image, null)\n  // }\n  /*@Override*/\n  QRCodeReader.prototype.decode = function (image, hints) {\n    var decoderResult;\n    var points;\n    if (hints !== undefined && hints !== null && undefined !== hints.get(DecodeHintType.PURE_BARCODE)) {\n      var bits = QRCodeReader.extractPureBits(image.getBlackMatrix());\n      decoderResult = this.decoder.decodeBitMatrix(bits, hints);\n      points = QRCodeReader.NO_POINTS;\n    } else {\n      var detectorResult = new Detector(image.getBlackMatrix()).detect(hints);\n      decoderResult = this.decoder.decodeBitMatrix(detectorResult.getBits(), hints);\n      points = detectorResult.getPoints();\n    }\n    // If the code was mirrored: swap the bottom-left and the top-right points.\n    if (decoderResult.getOther() instanceof QRCodeDecoderMetaData) {\n      decoderResult.getOther().applyMirroredCorrection(points);\n    }\n    var result = new Result(decoderResult.getText(), decoderResult.getRawBytes(), undefined, points, BarcodeFormat.QR_CODE, undefined);\n    var byteSegments = decoderResult.getByteSegments();\n    if (byteSegments !== null) {\n      result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n    }\n    var ecLevel = decoderResult.getECLevel();\n    if (ecLevel !== null) {\n      result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n    }\n    if (decoderResult.hasStructuredAppend()) {\n      result.putMetadata(ResultMetadataType.STRUCTURED_APPEND_SEQUENCE, decoderResult.getStructuredAppendSequenceNumber());\n      result.putMetadata(ResultMetadataType.STRUCTURED_APPEND_PARITY, decoderResult.getStructuredAppendParity());\n    }\n    return result;\n  };\n  /*@Override*/\n  QRCodeReader.prototype.reset = function () {\n    // do nothing\n  };\n  /**\n   * This method detects a code in a \"pure\" image -- that is, pure monochrome image\n   * which contains only an unrotated, unskewed, image of a code, with some white border\n   * around it. This is a specialized method that works exceptionally fast in this special\n   * case.\n   *\n   * @see com.google.zxing.datamatrix.DataMatrixReader#extractPureBits(BitMatrix)\n   */\n  QRCodeReader.extractPureBits = function (image) {\n    var leftTopBlack = image.getTopLeftOnBit();\n    var rightBottomBlack = image.getBottomRightOnBit();\n    if (leftTopBlack === null || rightBottomBlack === null) {\n      throw new NotFoundException();\n    }\n    var moduleSize = this.moduleSize(leftTopBlack, image);\n    var top = leftTopBlack[1];\n    var bottom = rightBottomBlack[1];\n    var left = leftTopBlack[0];\n    var right = rightBottomBlack[0];\n    // Sanity check!\n    if (left >= right || top >= bottom) {\n      throw new NotFoundException();\n    }\n    if (bottom - top !== right - left) {\n      // Special case, where bottom-right module wasn't black so we found something else in the last row\n      // Assume it's a square, so use height as the width\n      right = left + (bottom - top);\n      if (right >= image.getWidth()) {\n        // Abort if that would not make sense -- off image\n        throw new NotFoundException();\n      }\n    }\n    var matrixWidth = Math.round((right - left + 1) / moduleSize);\n    var matrixHeight = Math.round((bottom - top + 1) / moduleSize);\n    if (matrixWidth <= 0 || matrixHeight <= 0) {\n      throw new NotFoundException();\n    }\n    if (matrixHeight !== matrixWidth) {\n      // Only possibly decode square regions\n      throw new NotFoundException();\n    }\n    // Push in the \"border\" by half the module width so that we start\n    // sampling in the middle of the module. Just in case the image is a\n    // little off, this will help recover.\n    var nudge = /*(int) */Math.floor(moduleSize / 2.0);\n    top += nudge;\n    left += nudge;\n    // But careful that this does not sample off the edge\n    // \"right\" is the farthest-right valid pixel location -- right+1 is not necessarily\n    // This is positive by how much the inner x loop below would be too large\n    var nudgedTooFarRight = left + /*(int) */Math.floor((matrixWidth - 1) * moduleSize) - right;\n    if (nudgedTooFarRight > 0) {\n      if (nudgedTooFarRight > nudge) {\n        // Neither way fits; abort\n        throw new NotFoundException();\n      }\n      left -= nudgedTooFarRight;\n    }\n    // See logic above\n    var nudgedTooFarDown = top + /*(int) */Math.floor((matrixHeight - 1) * moduleSize) - bottom;\n    if (nudgedTooFarDown > 0) {\n      if (nudgedTooFarDown > nudge) {\n        // Neither way fits; abort\n        throw new NotFoundException();\n      }\n      top -= nudgedTooFarDown;\n    }\n    // Now just read off the bits\n    var bits = new BitMatrix(matrixWidth, matrixHeight);\n    for (var y = 0; y < matrixHeight; y++) {\n      var iOffset = top + /*(int) */Math.floor(y * moduleSize);\n      for (var x = 0; x < matrixWidth; x++) {\n        if (image.get(left + /*(int) */Math.floor(x * moduleSize), iOffset)) {\n          bits.set(x, y);\n        }\n      }\n    }\n    return bits;\n  };\n  QRCodeReader.moduleSize = function (leftTopBlack, image) {\n    var height = image.getHeight();\n    var width = image.getWidth();\n    var x = leftTopBlack[0];\n    var y = leftTopBlack[1];\n    var inBlack = true;\n    var transitions = 0;\n    while (x < width && y < height) {\n      if (inBlack !== image.get(x, y)) {\n        if (++transitions === 5) {\n          break;\n        }\n        inBlack = !inBlack;\n      }\n      x++;\n      y++;\n    }\n    if (x === width || y === height) {\n      throw new NotFoundException();\n    }\n    return (x - leftTopBlack[0]) / 7.0;\n  };\n  QRCodeReader.NO_POINTS = new Array();\n  return QRCodeReader;\n}();\nexport default QRCodeReader;", "map": {"version": 3, "names": ["BarcodeFormat", "BitMatrix", "DecodeHintType", "NotFoundException", "Result", "ResultMetadataType", "Decoder", "QRCodeDecoderMetaData", "Detector", "QRCodeReader", "decoder", "prototype", "getDecoder", "decode", "image", "hints", "decoderResult", "points", "undefined", "get", "PURE_BARCODE", "bits", "extractPureBits", "getBlackMatrix", "decodeBitMatrix", "NO_POINTS", "detectorResult", "detect", "getBits", "getPoints", "getOther", "applyMirroredCorrection", "result", "getText", "getRawBytes", "QR_CODE", "byteSegments", "getByteSegments", "putMetadata", "BYTE_SEGMENTS", "ecLevel", "getECLevel", "ERROR_CORRECTION_LEVEL", "hasStructuredAppend", "STRUCTURED_APPEND_SEQUENCE", "getStructuredAppendSequenceNumber", "STRUCTURED_APPEND_PARITY", "getStructuredAppendParity", "reset", "leftTopBlack", "getTopLeftOnBit", "rightBottomBlack", "getBottomRightOnBit", "moduleSize", "top", "bottom", "left", "right", "getWidth", "matrixWidth", "Math", "round", "matrixHeight", "nudge", "floor", "nudgedTooFarRight", "nudgedTooFarDown", "y", "iOffset", "x", "set", "height", "getHeight", "width", "inBlack", "transitions", "Array"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/QRCodeReader.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\n// import DetectorResult from '../common/DetectorResult';\nimport Decoder from './decoder/Decoder';\nimport QRCodeDecoderMetaData from './decoder/QRCodeDecoderMetaData';\nimport Detector from './detector/Detector';\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * This implementation can detect and decode QR Codes in an image.\n *\n * <AUTHOR> Owen\n */\nvar QRCodeReader = /** @class */ (function () {\n    function QRCodeReader() {\n        this.decoder = new Decoder();\n    }\n    QRCodeReader.prototype.getDecoder = function () {\n        return this.decoder;\n    };\n    /**\n     * Locates and decodes a QR code in an image.\n     *\n     * @return a representing: string the content encoded by the QR code\n     * @throws NotFoundException if a QR code cannot be found\n     * @throws FormatException if a QR code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    /*@Override*/\n    // public decode(image: BinaryBitmap): Result /*throws NotFoundException, ChecksumException, FormatException */ {\n    //   return this.decode(image, null)\n    // }\n    /*@Override*/\n    QRCodeReader.prototype.decode = function (image, hints) {\n        var decoderResult;\n        var points;\n        if (hints !== undefined && hints !== null && undefined !== hints.get(DecodeHintType.PURE_BARCODE)) {\n            var bits = QRCodeReader.extractPureBits(image.getBlackMatrix());\n            decoderResult = this.decoder.decodeBitMatrix(bits, hints);\n            points = QRCodeReader.NO_POINTS;\n        }\n        else {\n            var detectorResult = new Detector(image.getBlackMatrix()).detect(hints);\n            decoderResult = this.decoder.decodeBitMatrix(detectorResult.getBits(), hints);\n            points = detectorResult.getPoints();\n        }\n        // If the code was mirrored: swap the bottom-left and the top-right points.\n        if (decoderResult.getOther() instanceof QRCodeDecoderMetaData) {\n            decoderResult.getOther().applyMirroredCorrection(points);\n        }\n        var result = new Result(decoderResult.getText(), decoderResult.getRawBytes(), undefined, points, BarcodeFormat.QR_CODE, undefined);\n        var byteSegments = decoderResult.getByteSegments();\n        if (byteSegments !== null) {\n            result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n        }\n        var ecLevel = decoderResult.getECLevel();\n        if (ecLevel !== null) {\n            result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n        }\n        if (decoderResult.hasStructuredAppend()) {\n            result.putMetadata(ResultMetadataType.STRUCTURED_APPEND_SEQUENCE, decoderResult.getStructuredAppendSequenceNumber());\n            result.putMetadata(ResultMetadataType.STRUCTURED_APPEND_PARITY, decoderResult.getStructuredAppendParity());\n        }\n        return result;\n    };\n    /*@Override*/\n    QRCodeReader.prototype.reset = function () {\n        // do nothing\n    };\n    /**\n     * This method detects a code in a \"pure\" image -- that is, pure monochrome image\n     * which contains only an unrotated, unskewed, image of a code, with some white border\n     * around it. This is a specialized method that works exceptionally fast in this special\n     * case.\n     *\n     * @see com.google.zxing.datamatrix.DataMatrixReader#extractPureBits(BitMatrix)\n     */\n    QRCodeReader.extractPureBits = function (image) {\n        var leftTopBlack = image.getTopLeftOnBit();\n        var rightBottomBlack = image.getBottomRightOnBit();\n        if (leftTopBlack === null || rightBottomBlack === null) {\n            throw new NotFoundException();\n        }\n        var moduleSize = this.moduleSize(leftTopBlack, image);\n        var top = leftTopBlack[1];\n        var bottom = rightBottomBlack[1];\n        var left = leftTopBlack[0];\n        var right = rightBottomBlack[0];\n        // Sanity check!\n        if (left >= right || top >= bottom) {\n            throw new NotFoundException();\n        }\n        if (bottom - top !== right - left) {\n            // Special case, where bottom-right module wasn't black so we found something else in the last row\n            // Assume it's a square, so use height as the width\n            right = left + (bottom - top);\n            if (right >= image.getWidth()) {\n                // Abort if that would not make sense -- off image\n                throw new NotFoundException();\n            }\n        }\n        var matrixWidth = Math.round((right - left + 1) / moduleSize);\n        var matrixHeight = Math.round((bottom - top + 1) / moduleSize);\n        if (matrixWidth <= 0 || matrixHeight <= 0) {\n            throw new NotFoundException();\n        }\n        if (matrixHeight !== matrixWidth) {\n            // Only possibly decode square regions\n            throw new NotFoundException();\n        }\n        // Push in the \"border\" by half the module width so that we start\n        // sampling in the middle of the module. Just in case the image is a\n        // little off, this will help recover.\n        var nudge = /*(int) */ Math.floor(moduleSize / 2.0);\n        top += nudge;\n        left += nudge;\n        // But careful that this does not sample off the edge\n        // \"right\" is the farthest-right valid pixel location -- right+1 is not necessarily\n        // This is positive by how much the inner x loop below would be too large\n        var nudgedTooFarRight = left + /*(int) */ Math.floor((matrixWidth - 1) * moduleSize) - right;\n        if (nudgedTooFarRight > 0) {\n            if (nudgedTooFarRight > nudge) {\n                // Neither way fits; abort\n                throw new NotFoundException();\n            }\n            left -= nudgedTooFarRight;\n        }\n        // See logic above\n        var nudgedTooFarDown = top + /*(int) */ Math.floor((matrixHeight - 1) * moduleSize) - bottom;\n        if (nudgedTooFarDown > 0) {\n            if (nudgedTooFarDown > nudge) {\n                // Neither way fits; abort\n                throw new NotFoundException();\n            }\n            top -= nudgedTooFarDown;\n        }\n        // Now just read off the bits\n        var bits = new BitMatrix(matrixWidth, matrixHeight);\n        for (var y = 0; y < matrixHeight; y++) {\n            var iOffset = top + /*(int) */ Math.floor(y * moduleSize);\n            for (var x = 0; x < matrixWidth; x++) {\n                if (image.get(left + /*(int) */ Math.floor(x * moduleSize), iOffset)) {\n                    bits.set(x, y);\n                }\n            }\n        }\n        return bits;\n    };\n    QRCodeReader.moduleSize = function (leftTopBlack, image) {\n        var height = image.getHeight();\n        var width = image.getWidth();\n        var x = leftTopBlack[0];\n        var y = leftTopBlack[1];\n        var inBlack = true;\n        var transitions = 0;\n        while (x < width && y < height) {\n            if (inBlack !== image.get(x, y)) {\n                if (++transitions === 5) {\n                    break;\n                }\n                inBlack = !inBlack;\n            }\n            x++;\n            y++;\n        }\n        if (x === width || y === height) {\n            throw new NotFoundException();\n        }\n        return (x - leftTopBlack[0]) / 7.0;\n    };\n    QRCodeReader.NO_POINTS = new Array();\n    return QRCodeReader;\n}());\nexport default QRCodeReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD;AACA,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,qBAAqB,MAAM,iCAAiC;AACnE,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG;IACpB,IAAI,CAACC,OAAO,GAAG,IAAIJ,OAAO,CAAC,CAAC;EAChC;EACAG,YAAY,CAACE,SAAS,CAACC,UAAU,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACF,OAAO;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACAD,YAAY,CAACE,SAAS,CAACE,MAAM,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACpD,IAAIC,aAAa;IACjB,IAAIC,MAAM;IACV,IAAIF,KAAK,KAAKG,SAAS,IAAIH,KAAK,KAAK,IAAI,IAAIG,SAAS,KAAKH,KAAK,CAACI,GAAG,CAACjB,cAAc,CAACkB,YAAY,CAAC,EAAE;MAC/F,IAAIC,IAAI,GAAGZ,YAAY,CAACa,eAAe,CAACR,KAAK,CAACS,cAAc,CAAC,CAAC,CAAC;MAC/DP,aAAa,GAAG,IAAI,CAACN,OAAO,CAACc,eAAe,CAACH,IAAI,EAAEN,KAAK,CAAC;MACzDE,MAAM,GAAGR,YAAY,CAACgB,SAAS;IACnC,CAAC,MACI;MACD,IAAIC,cAAc,GAAG,IAAIlB,QAAQ,CAACM,KAAK,CAACS,cAAc,CAAC,CAAC,CAAC,CAACI,MAAM,CAACZ,KAAK,CAAC;MACvEC,aAAa,GAAG,IAAI,CAACN,OAAO,CAACc,eAAe,CAACE,cAAc,CAACE,OAAO,CAAC,CAAC,EAAEb,KAAK,CAAC;MAC7EE,MAAM,GAAGS,cAAc,CAACG,SAAS,CAAC,CAAC;IACvC;IACA;IACA,IAAIb,aAAa,CAACc,QAAQ,CAAC,CAAC,YAAYvB,qBAAqB,EAAE;MAC3DS,aAAa,CAACc,QAAQ,CAAC,CAAC,CAACC,uBAAuB,CAACd,MAAM,CAAC;IAC5D;IACA,IAAIe,MAAM,GAAG,IAAI5B,MAAM,CAACY,aAAa,CAACiB,OAAO,CAAC,CAAC,EAAEjB,aAAa,CAACkB,WAAW,CAAC,CAAC,EAAEhB,SAAS,EAAED,MAAM,EAAEjB,aAAa,CAACmC,OAAO,EAAEjB,SAAS,CAAC;IAClI,IAAIkB,YAAY,GAAGpB,aAAa,CAACqB,eAAe,CAAC,CAAC;IAClD,IAAID,YAAY,KAAK,IAAI,EAAE;MACvBJ,MAAM,CAACM,WAAW,CAACjC,kBAAkB,CAACkC,aAAa,EAAEH,YAAY,CAAC;IACtE;IACA,IAAII,OAAO,GAAGxB,aAAa,CAACyB,UAAU,CAAC,CAAC;IACxC,IAAID,OAAO,KAAK,IAAI,EAAE;MAClBR,MAAM,CAACM,WAAW,CAACjC,kBAAkB,CAACqC,sBAAsB,EAAEF,OAAO,CAAC;IAC1E;IACA,IAAIxB,aAAa,CAAC2B,mBAAmB,CAAC,CAAC,EAAE;MACrCX,MAAM,CAACM,WAAW,CAACjC,kBAAkB,CAACuC,0BAA0B,EAAE5B,aAAa,CAAC6B,iCAAiC,CAAC,CAAC,CAAC;MACpHb,MAAM,CAACM,WAAW,CAACjC,kBAAkB,CAACyC,wBAAwB,EAAE9B,aAAa,CAAC+B,yBAAyB,CAAC,CAAC,CAAC;IAC9G;IACA,OAAOf,MAAM;EACjB,CAAC;EACD;EACAvB,YAAY,CAACE,SAAS,CAACqC,KAAK,GAAG,YAAY;IACvC;EAAA,CACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIvC,YAAY,CAACa,eAAe,GAAG,UAAUR,KAAK,EAAE;IAC5C,IAAImC,YAAY,GAAGnC,KAAK,CAACoC,eAAe,CAAC,CAAC;IAC1C,IAAIC,gBAAgB,GAAGrC,KAAK,CAACsC,mBAAmB,CAAC,CAAC;IAClD,IAAIH,YAAY,KAAK,IAAI,IAAIE,gBAAgB,KAAK,IAAI,EAAE;MACpD,MAAM,IAAIhD,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAIkD,UAAU,GAAG,IAAI,CAACA,UAAU,CAACJ,YAAY,EAAEnC,KAAK,CAAC;IACrD,IAAIwC,GAAG,GAAGL,YAAY,CAAC,CAAC,CAAC;IACzB,IAAIM,MAAM,GAAGJ,gBAAgB,CAAC,CAAC,CAAC;IAChC,IAAIK,IAAI,GAAGP,YAAY,CAAC,CAAC,CAAC;IAC1B,IAAIQ,KAAK,GAAGN,gBAAgB,CAAC,CAAC,CAAC;IAC/B;IACA,IAAIK,IAAI,IAAIC,KAAK,IAAIH,GAAG,IAAIC,MAAM,EAAE;MAChC,MAAM,IAAIpD,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAIoD,MAAM,GAAGD,GAAG,KAAKG,KAAK,GAAGD,IAAI,EAAE;MAC/B;MACA;MACAC,KAAK,GAAGD,IAAI,IAAID,MAAM,GAAGD,GAAG,CAAC;MAC7B,IAAIG,KAAK,IAAI3C,KAAK,CAAC4C,QAAQ,CAAC,CAAC,EAAE;QAC3B;QACA,MAAM,IAAIvD,iBAAiB,CAAC,CAAC;MACjC;IACJ;IACA,IAAIwD,WAAW,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,KAAK,GAAGD,IAAI,GAAG,CAAC,IAAIH,UAAU,CAAC;IAC7D,IAAIS,YAAY,GAAGF,IAAI,CAACC,KAAK,CAAC,CAACN,MAAM,GAAGD,GAAG,GAAG,CAAC,IAAID,UAAU,CAAC;IAC9D,IAAIM,WAAW,IAAI,CAAC,IAAIG,YAAY,IAAI,CAAC,EAAE;MACvC,MAAM,IAAI3D,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI2D,YAAY,KAAKH,WAAW,EAAE;MAC9B;MACA,MAAM,IAAIxD,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA,IAAI4D,KAAK,GAAG,UAAWH,IAAI,CAACI,KAAK,CAACX,UAAU,GAAG,GAAG,CAAC;IACnDC,GAAG,IAAIS,KAAK;IACZP,IAAI,IAAIO,KAAK;IACb;IACA;IACA;IACA,IAAIE,iBAAiB,GAAGT,IAAI,GAAG,UAAWI,IAAI,CAACI,KAAK,CAAC,CAACL,WAAW,GAAG,CAAC,IAAIN,UAAU,CAAC,GAAGI,KAAK;IAC5F,IAAIQ,iBAAiB,GAAG,CAAC,EAAE;MACvB,IAAIA,iBAAiB,GAAGF,KAAK,EAAE;QAC3B;QACA,MAAM,IAAI5D,iBAAiB,CAAC,CAAC;MACjC;MACAqD,IAAI,IAAIS,iBAAiB;IAC7B;IACA;IACA,IAAIC,gBAAgB,GAAGZ,GAAG,GAAG,UAAWM,IAAI,CAACI,KAAK,CAAC,CAACF,YAAY,GAAG,CAAC,IAAIT,UAAU,CAAC,GAAGE,MAAM;IAC5F,IAAIW,gBAAgB,GAAG,CAAC,EAAE;MACtB,IAAIA,gBAAgB,GAAGH,KAAK,EAAE;QAC1B;QACA,MAAM,IAAI5D,iBAAiB,CAAC,CAAC;MACjC;MACAmD,GAAG,IAAIY,gBAAgB;IAC3B;IACA;IACA,IAAI7C,IAAI,GAAG,IAAIpB,SAAS,CAAC0D,WAAW,EAAEG,YAAY,CAAC;IACnD,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,YAAY,EAAEK,CAAC,EAAE,EAAE;MACnC,IAAIC,OAAO,GAAGd,GAAG,GAAG,UAAWM,IAAI,CAACI,KAAK,CAACG,CAAC,GAAGd,UAAU,CAAC;MACzD,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,WAAW,EAAEU,CAAC,EAAE,EAAE;QAClC,IAAIvD,KAAK,CAACK,GAAG,CAACqC,IAAI,GAAG,UAAWI,IAAI,CAACI,KAAK,CAACK,CAAC,GAAGhB,UAAU,CAAC,EAAEe,OAAO,CAAC,EAAE;UAClE/C,IAAI,CAACiD,GAAG,CAACD,CAAC,EAAEF,CAAC,CAAC;QAClB;MACJ;IACJ;IACA,OAAO9C,IAAI;EACf,CAAC;EACDZ,YAAY,CAAC4C,UAAU,GAAG,UAAUJ,YAAY,EAAEnC,KAAK,EAAE;IACrD,IAAIyD,MAAM,GAAGzD,KAAK,CAAC0D,SAAS,CAAC,CAAC;IAC9B,IAAIC,KAAK,GAAG3D,KAAK,CAAC4C,QAAQ,CAAC,CAAC;IAC5B,IAAIW,CAAC,GAAGpB,YAAY,CAAC,CAAC,CAAC;IACvB,IAAIkB,CAAC,GAAGlB,YAAY,CAAC,CAAC,CAAC;IACvB,IAAIyB,OAAO,GAAG,IAAI;IAClB,IAAIC,WAAW,GAAG,CAAC;IACnB,OAAON,CAAC,GAAGI,KAAK,IAAIN,CAAC,GAAGI,MAAM,EAAE;MAC5B,IAAIG,OAAO,KAAK5D,KAAK,CAACK,GAAG,CAACkD,CAAC,EAAEF,CAAC,CAAC,EAAE;QAC7B,IAAI,EAAEQ,WAAW,KAAK,CAAC,EAAE;UACrB;QACJ;QACAD,OAAO,GAAG,CAACA,OAAO;MACtB;MACAL,CAAC,EAAE;MACHF,CAAC,EAAE;IACP;IACA,IAAIE,CAAC,KAAKI,KAAK,IAAIN,CAAC,KAAKI,MAAM,EAAE;MAC7B,MAAM,IAAIpE,iBAAiB,CAAC,CAAC;IACjC;IACA,OAAO,CAACkE,CAAC,GAAGpB,YAAY,CAAC,CAAC,CAAC,IAAI,GAAG;EACtC,CAAC;EACDxC,YAAY,CAACgB,SAAS,GAAG,IAAImD,KAAK,CAAC,CAAC;EACpC,OAAOnE,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}