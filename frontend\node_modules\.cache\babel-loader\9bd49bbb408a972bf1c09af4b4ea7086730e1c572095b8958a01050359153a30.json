{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _constants = require('./constants');\n\n// Match Set functions\nvar matchSetALength = function matchSetALength(string) {\n  return string.match(new RegExp('^' + _constants.A_CHARS + '*'))[0].length;\n};\nvar matchSetBLength = function matchSetBLength(string) {\n  return string.match(new RegExp('^' + _constants.B_CHARS + '*'))[0].length;\n};\nvar matchSetC = function matchSetC(string) {\n  return string.match(new RegExp('^' + _constants.C_CHARS + '*'))[0];\n};\n\n// CODE128A or CODE128B\nfunction autoSelectFromAB(string, isA) {\n  var ranges = isA ? _constants.A_CHARS : _constants.B_CHARS;\n  var untilC = string.match(new RegExp('^(' + ranges + '+?)(([0-9]{2}){2,})([^0-9]|$)'));\n  if (untilC) {\n    return untilC[1] + String.fromCharCode(204) + autoSelectFromC(string.substring(untilC[1].length));\n  }\n  var chars = string.match(new RegExp('^' + ranges + '+'))[0];\n  if (chars.length === string.length) {\n    return string;\n  }\n  return chars + String.fromCharCode(isA ? 205 : 206) + autoSelectFromAB(string.substring(chars.length), !isA);\n}\n\n// CODE128C\nfunction autoSelectFromC(string) {\n  var cMatch = matchSetC(string);\n  var length = cMatch.length;\n  if (length === string.length) {\n    return string;\n  }\n  string = string.substring(length);\n\n  // Select A/B depending on the longest match\n  var isA = matchSetALength(string) >= matchSetBLength(string);\n  return cMatch + String.fromCharCode(isA ? 206 : 205) + autoSelectFromAB(string, isA);\n}\n\n// Detect Code Set (A, B or C) and format the string\n\nexports.default = function (string) {\n  var newString = void 0;\n  var cLength = matchSetC(string).length;\n\n  // Select 128C if the string start with enough digits\n  if (cLength >= 2) {\n    newString = _constants.C_START_CHAR + autoSelectFromC(string);\n  } else {\n    // Select A/B depending on the longest match\n    var isA = matchSetALength(string) > matchSetBLength(string);\n    newString = (isA ? _constants.A_START_CHAR : _constants.B_START_CHAR) + autoSelectFromAB(string, isA);\n  }\n  return newString.replace(/[\\xCD\\xCE]([^])[\\xCD\\xCE]/,\n  // Any sequence between 205 and 206 characters\n  function (match, char) {\n    return String.fromCharCode(203) + char;\n  });\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_constants", "require", "matchSetALength", "string", "match", "RegExp", "A_CHARS", "length", "matchSetBLength", "B_CHARS", "matchSetC", "C_CHARS", "autoSelectFromAB", "isA", "ranges", "untilC", "String", "fromCharCode", "autoSelectFromC", "substring", "chars", "cMatch", "default", "newString", "c<PERSON><PERSON>th", "C_START_CHAR", "A_START_CHAR", "B_START_CHAR", "replace", "char"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/CODE128/auto.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _constants = require('./constants');\n\n// Match Set functions\nvar matchSetALength = function matchSetALength(string) {\n\treturn string.match(new RegExp('^' + _constants.A_CHARS + '*'))[0].length;\n};\nvar matchSetBLength = function matchSetBLength(string) {\n\treturn string.match(new RegExp('^' + _constants.B_CHARS + '*'))[0].length;\n};\nvar matchSetC = function matchSetC(string) {\n\treturn string.match(new RegExp('^' + _constants.C_CHARS + '*'))[0];\n};\n\n// CODE128A or CODE128B\nfunction autoSelectFromAB(string, isA) {\n\tvar ranges = isA ? _constants.A_CHARS : _constants.B_CHARS;\n\tvar untilC = string.match(new RegExp('^(' + ranges + '+?)(([0-9]{2}){2,})([^0-9]|$)'));\n\n\tif (untilC) {\n\t\treturn untilC[1] + String.fromCharCode(204) + autoSelectFromC(string.substring(untilC[1].length));\n\t}\n\n\tvar chars = string.match(new RegExp('^' + ranges + '+'))[0];\n\n\tif (chars.length === string.length) {\n\t\treturn string;\n\t}\n\n\treturn chars + String.fromCharCode(isA ? 205 : 206) + autoSelectFromAB(string.substring(chars.length), !isA);\n}\n\n// CODE128C\nfunction autoSelectFromC(string) {\n\tvar cMatch = matchSetC(string);\n\tvar length = cMatch.length;\n\n\tif (length === string.length) {\n\t\treturn string;\n\t}\n\n\tstring = string.substring(length);\n\n\t// Select A/B depending on the longest match\n\tvar isA = matchSetALength(string) >= matchSetBLength(string);\n\treturn cMatch + String.fromCharCode(isA ? 206 : 205) + autoSelectFromAB(string, isA);\n}\n\n// Detect Code Set (A, B or C) and format the string\n\nexports.default = function (string) {\n\tvar newString = void 0;\n\tvar cLength = matchSetC(string).length;\n\n\t// Select 128C if the string start with enough digits\n\tif (cLength >= 2) {\n\t\tnewString = _constants.C_START_CHAR + autoSelectFromC(string);\n\t} else {\n\t\t// Select A/B depending on the longest match\n\t\tvar isA = matchSetALength(string) > matchSetBLength(string);\n\t\tnewString = (isA ? _constants.A_START_CHAR : _constants.B_START_CHAR) + autoSelectFromAB(string, isA);\n\t}\n\n\treturn newString.replace(/[\\xCD\\xCE]([^])[\\xCD\\xCE]/, // Any sequence between 205 and 206 characters\n\tfunction (match, char) {\n\t\treturn String.fromCharCode(203) + char;\n\t});\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAEvC;AACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,MAAM,EAAE;EACtD,OAAOA,MAAM,CAACC,KAAK,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGL,UAAU,CAACM,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACC,MAAM;AAC1E,CAAC;AACD,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACL,MAAM,EAAE;EACtD,OAAOA,MAAM,CAACC,KAAK,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGL,UAAU,CAACS,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACF,MAAM;AAC1E,CAAC;AACD,IAAIG,SAAS,GAAG,SAASA,SAASA,CAACP,MAAM,EAAE;EAC1C,OAAOA,MAAM,CAACC,KAAK,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGL,UAAU,CAACW,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,CAAC;;AAED;AACA,SAASC,gBAAgBA,CAACT,MAAM,EAAEU,GAAG,EAAE;EACtC,IAAIC,MAAM,GAAGD,GAAG,GAAGb,UAAU,CAACM,OAAO,GAAGN,UAAU,CAACS,OAAO;EAC1D,IAAIM,MAAM,GAAGZ,MAAM,CAACC,KAAK,CAAC,IAAIC,MAAM,CAAC,IAAI,GAAGS,MAAM,GAAG,+BAA+B,CAAC,CAAC;EAEtF,IAAIC,MAAM,EAAE;IACX,OAAOA,MAAM,CAAC,CAAC,CAAC,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,GAAGC,eAAe,CAACf,MAAM,CAACgB,SAAS,CAACJ,MAAM,CAAC,CAAC,CAAC,CAACR,MAAM,CAAC,CAAC;EAClG;EAEA,IAAIa,KAAK,GAAGjB,MAAM,CAACC,KAAK,CAAC,IAAIC,MAAM,CAAC,GAAG,GAAGS,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAE3D,IAAIM,KAAK,CAACb,MAAM,KAAKJ,MAAM,CAACI,MAAM,EAAE;IACnC,OAAOJ,MAAM;EACd;EAEA,OAAOiB,KAAK,GAAGJ,MAAM,CAACC,YAAY,CAACJ,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGD,gBAAgB,CAACT,MAAM,CAACgB,SAAS,CAACC,KAAK,CAACb,MAAM,CAAC,EAAE,CAACM,GAAG,CAAC;AAC7G;;AAEA;AACA,SAASK,eAAeA,CAACf,MAAM,EAAE;EAChC,IAAIkB,MAAM,GAAGX,SAAS,CAACP,MAAM,CAAC;EAC9B,IAAII,MAAM,GAAGc,MAAM,CAACd,MAAM;EAE1B,IAAIA,MAAM,KAAKJ,MAAM,CAACI,MAAM,EAAE;IAC7B,OAAOJ,MAAM;EACd;EAEAA,MAAM,GAAGA,MAAM,CAACgB,SAAS,CAACZ,MAAM,CAAC;;EAEjC;EACA,IAAIM,GAAG,GAAGX,eAAe,CAACC,MAAM,CAAC,IAAIK,eAAe,CAACL,MAAM,CAAC;EAC5D,OAAOkB,MAAM,GAAGL,MAAM,CAACC,YAAY,CAACJ,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,GAAGD,gBAAgB,CAACT,MAAM,EAAEU,GAAG,CAAC;AACrF;;AAEA;;AAEAf,OAAO,CAACwB,OAAO,GAAG,UAAUnB,MAAM,EAAE;EACnC,IAAIoB,SAAS,GAAG,KAAK,CAAC;EACtB,IAAIC,OAAO,GAAGd,SAAS,CAACP,MAAM,CAAC,CAACI,MAAM;;EAEtC;EACA,IAAIiB,OAAO,IAAI,CAAC,EAAE;IACjBD,SAAS,GAAGvB,UAAU,CAACyB,YAAY,GAAGP,eAAe,CAACf,MAAM,CAAC;EAC9D,CAAC,MAAM;IACN;IACA,IAAIU,GAAG,GAAGX,eAAe,CAACC,MAAM,CAAC,GAAGK,eAAe,CAACL,MAAM,CAAC;IAC3DoB,SAAS,GAAG,CAACV,GAAG,GAAGb,UAAU,CAAC0B,YAAY,GAAG1B,UAAU,CAAC2B,YAAY,IAAIf,gBAAgB,CAACT,MAAM,EAAEU,GAAG,CAAC;EACtG;EAEA,OAAOU,SAAS,CAACK,OAAO,CAAC,2BAA2B;EAAE;EACtD,UAAUxB,KAAK,EAAEyB,IAAI,EAAE;IACtB,OAAOb,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,GAAGY,IAAI;EACvC,CAAC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}