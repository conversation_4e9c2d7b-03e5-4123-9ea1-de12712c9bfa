{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"slots\", \"slotProps\", \"currentMonth\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onMonthChange\", \"onViewChange\", \"view\", \"reduceAnimations\", \"views\", \"labelId\", \"className\", \"timezone\", \"format\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Fade from '@mui/material/Fade';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport IconButton from '@mui/material/IconButton';\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { PickersFadeTransitionGroup } from \"../DateCalendar/PickersFadeTransitionGroup.js\";\nimport { ArrowDropDownIcon } from \"../icons/index.js\";\nimport { PickersArrowSwitcher } from \"../internals/components/PickersArrowSwitcher/index.js\";\nimport { usePreviousMonthDisabled, useNextMonthDisabled } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { getPickersCalendarHeaderUtilityClass, pickersCalendarHeaderClasses } from \"./pickersCalendarHeaderClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    labelContainer: ['labelContainer'],\n    label: ['label'],\n    switchViewButton: ['switchViewButton'],\n    switchViewIcon: ['switchViewIcon']\n  };\n  return composeClasses(slots, getPickersCalendarHeaderUtilityClass, classes);\n};\nconst PickersCalendarHeaderRoot = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginTop: 12,\n  marginBottom: 4,\n  paddingLeft: 24,\n  paddingRight: 12,\n  // prevent jumping in safari\n  maxHeight: 40,\n  minHeight: 40\n});\nconst PickersCalendarHeaderLabelContainer = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'LabelContainer',\n  overridesResolver: (_, styles) => styles.labelContainer\n})(({\n  theme\n}) => _extends({\n  display: 'flex',\n  overflow: 'hidden',\n  alignItems: 'center',\n  cursor: 'pointer',\n  marginRight: 'auto'\n}, theme.typography.body1, {\n  fontWeight: theme.typography.fontWeightMedium\n}));\nconst PickersCalendarHeaderLabel = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'Label',\n  overridesResolver: (_, styles) => styles.label\n})({\n  marginRight: 6\n});\nconst PickersCalendarHeaderSwitchViewButton = styled(IconButton, {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'SwitchViewButton',\n  overridesResolver: (_, styles) => styles.switchViewButton\n})({\n  marginRight: 'auto',\n  variants: [{\n    props: {\n      view: 'year'\n    },\n    style: {\n      [`.${pickersCalendarHeaderClasses.switchViewIcon}`]: {\n        transform: 'rotate(180deg)'\n      }\n    }\n  }]\n});\nconst PickersCalendarHeaderSwitchViewIcon = styled(ArrowDropDownIcon, {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'SwitchViewIcon',\n  overridesResolver: (_, styles) => styles.switchViewIcon\n})(({\n  theme\n}) => ({\n  willChange: 'transform',\n  transition: theme.transitions.create('transform'),\n  transform: 'rotate(0deg)'\n}));\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n * - [DateRangeCalendar](https://mui.com/x/react-date-pickers/date-range-calendar/)\n * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)\n *\n * API:\n *\n * - [PickersCalendarHeader API](https://mui.com/x/api/date-pickers/pickers-calendar-header/)\n */\nconst PickersCalendarHeader = /*#__PURE__*/React.forwardRef(function PickersCalendarHeader(inProps, ref) {\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersCalendarHeader'\n  });\n  const {\n      slots,\n      slotProps,\n      currentMonth: month,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onMonthChange,\n      onViewChange,\n      view,\n      reduceAnimations,\n      views,\n      labelId,\n      className,\n      timezone,\n      format = `${utils.formats.month} ${utils.formats.year}`\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(props);\n  const SwitchViewButton = slots?.switchViewButton ?? PickersCalendarHeaderSwitchViewButton;\n  const switchViewButtonProps = useSlotProps({\n    elementType: SwitchViewButton,\n    externalSlotProps: slotProps?.switchViewButton,\n    additionalProps: {\n      size: 'small',\n      'aria-label': translations.calendarViewSwitchingButtonAriaLabel(view)\n    },\n    ownerState,\n    className: classes.switchViewButton\n  });\n  const SwitchViewIcon = slots?.switchViewIcon ?? PickersCalendarHeaderSwitchViewIcon;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: SwitchViewIcon,\n      externalSlotProps: slotProps?.switchViewIcon,\n      ownerState,\n      className: classes.switchViewIcon\n    }),\n    switchViewIconProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const selectNextMonth = () => onMonthChange(utils.addMonths(month, 1), 'left');\n  const selectPreviousMonth = () => onMonthChange(utils.addMonths(month, -1), 'right');\n  const isNextMonthDisabled = useNextMonthDisabled(month, {\n    disableFuture,\n    maxDate,\n    timezone\n  });\n  const isPreviousMonthDisabled = usePreviousMonthDisabled(month, {\n    disablePast,\n    minDate,\n    timezone\n  });\n  const handleToggleView = () => {\n    if (views.length === 1 || !onViewChange || disabled) {\n      return;\n    }\n    if (views.length === 2) {\n      onViewChange(views.find(el => el !== view) || views[0]);\n    } else {\n      // switching only between first 2\n      const nextIndexToOpen = views.indexOf(view) !== 0 ? 0 : 1;\n      onViewChange(views[nextIndexToOpen]);\n    }\n  };\n\n  // No need to display more information\n  if (views.length === 1 && views[0] === 'year') {\n    return null;\n  }\n  const label = utils.formatByString(month, format);\n  return /*#__PURE__*/_jsxs(PickersCalendarHeaderRoot, _extends({}, other, {\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    children: [/*#__PURE__*/_jsxs(PickersCalendarHeaderLabelContainer, {\n      role: \"presentation\",\n      onClick: handleToggleView,\n      ownerState: ownerState\n      // putting this on the label item element below breaks when using transition\n      ,\n\n      \"aria-live\": \"polite\",\n      className: classes.labelContainer,\n      children: [/*#__PURE__*/_jsx(PickersFadeTransitionGroup, {\n        reduceAnimations: reduceAnimations,\n        transKey: label,\n        children: /*#__PURE__*/_jsx(PickersCalendarHeaderLabel, {\n          id: labelId,\n          ownerState: ownerState,\n          className: classes.label,\n          children: label\n        })\n      }), views.length > 1 && !disabled && /*#__PURE__*/_jsx(SwitchViewButton, _extends({}, switchViewButtonProps, {\n        children: /*#__PURE__*/_jsx(SwitchViewIcon, _extends({}, switchViewIconProps))\n      }))]\n    }), /*#__PURE__*/_jsx(Fade, {\n      in: view === 'day',\n      appear: !reduceAnimations,\n      enter: !reduceAnimations,\n      children: /*#__PURE__*/_jsx(PickersArrowSwitcher, {\n        slots: slots,\n        slotProps: slotProps,\n        onGoToPrevious: selectPreviousMonth,\n        isPreviousDisabled: isPreviousMonthDisabled,\n        previousLabel: translations.previousMonth,\n        onGoToNext: selectNextMonth,\n        isNextDisabled: isNextMonthDisabled,\n        nextLabel: translations.nextMonth\n      })\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? PickersCalendarHeader.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  currentMonth: PropTypes.object.isRequired,\n  disabled: PropTypes.bool,\n  disableFuture: PropTypes.bool,\n  disablePast: PropTypes.bool,\n  /**\n   * Format used to display the date.\n   * @default `${adapter.formats.month} ${adapter.formats.year}`\n   */\n  format: PropTypes.string,\n  /**\n   * Id of the calendar text element.\n   * It is used to establish an `aria-labelledby` relationship with the calendar `grid` element.\n   */\n  labelId: PropTypes.string,\n  maxDate: PropTypes.object.isRequired,\n  minDate: PropTypes.object.isRequired,\n  onMonthChange: PropTypes.func.isRequired,\n  onViewChange: PropTypes.func,\n  reduceAnimations: PropTypes.bool.isRequired,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  timezone: PropTypes.string.isRequired,\n  view: PropTypes.oneOf(['day', 'month', 'year']).isRequired,\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired).isRequired\n} : void 0;\nexport { PickersCalendarHeader };", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "PropTypes", "clsx", "Fade", "styled", "useThemeProps", "useSlotProps", "composeClasses", "IconButton", "usePickersTranslations", "useUtils", "PickersFadeTransitionGroup", "ArrowDropDownIcon", "PickersArrowSwitcher", "usePreviousMonthDisabled", "useNextMonthDisabled", "getPickersCalendarHeaderUtilityClass", "pickersCalendarHeaderClasses", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "classes", "slots", "root", "labelContainer", "label", "switchViewButton", "switchViewIcon", "PickersCalendarHeaderRoot", "name", "slot", "overridesResolver", "_", "styles", "display", "alignItems", "marginTop", "marginBottom", "paddingLeft", "paddingRight", "maxHeight", "minHeight", "PickersCalendarHeaderLabelContainer", "theme", "overflow", "cursor", "marginRight", "typography", "body1", "fontWeight", "fontWeightMedium", "PickersCalendarHeaderLabel", "PickersCalendarHeaderSwitchViewButton", "variants", "props", "view", "style", "transform", "PickersCalendarHeaderSwitchViewIcon", "<PERSON><PERSON><PERSON><PERSON>", "transition", "transitions", "create", "Pickers<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "inProps", "ref", "translations", "utils", "slotProps", "currentMonth", "month", "disabled", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "onViewChange", "reduceAnimations", "views", "labelId", "className", "timezone", "format", "formats", "year", "other", "SwitchViewButton", "switchViewButtonProps", "elementType", "externalSlotProps", "additionalProps", "size", "calendarViewSwitchingButtonAriaLabel", "SwitchViewIcon", "_useSlotProps", "switchViewIconProps", "selectNextMonth", "addMonths", "selectPreviousMonth", "isNextMonthDisabled", "isPreviousMonthDisabled", "handleToggleView", "length", "find", "el", "nextIndexToOpen", "indexOf", "formatByString", "children", "role", "onClick", "transKey", "id", "in", "appear", "enter", "onGoToPrevious", "isPreviousDisabled", "previousLabel", "previousMonth", "onGoToNext", "isNextDisabled", "next<PERSON><PERSON><PERSON>", "nextMonth", "process", "env", "NODE_ENV", "propTypes", "object", "string", "isRequired", "bool", "func", "sx", "oneOfType", "arrayOf", "oneOf"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/PickersCalendarHeader/PickersCalendarHeader.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"slots\", \"slotProps\", \"currentMonth\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onMonthChange\", \"onViewChange\", \"view\", \"reduceAnimations\", \"views\", \"labelId\", \"className\", \"timezone\", \"format\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport Fade from '@mui/material/Fade';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport IconButton from '@mui/material/IconButton';\nimport { usePickersTranslations } from \"../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { PickersFadeTransitionGroup } from \"../DateCalendar/PickersFadeTransitionGroup.js\";\nimport { ArrowDropDownIcon } from \"../icons/index.js\";\nimport { PickersArrowSwitcher } from \"../internals/components/PickersArrowSwitcher/index.js\";\nimport { usePreviousMonthDisabled, useNextMonthDisabled } from \"../internals/hooks/date-helpers-hooks.js\";\nimport { getPickersCalendarHeaderUtilityClass, pickersCalendarHeaderClasses } from \"./pickersCalendarHeaderClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    labelContainer: ['labelContainer'],\n    label: ['label'],\n    switchViewButton: ['switchViewButton'],\n    switchViewIcon: ['switchViewIcon']\n  };\n  return composeClasses(slots, getPickersCalendarHeaderUtilityClass, classes);\n};\nconst PickersCalendarHeaderRoot = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})({\n  display: 'flex',\n  alignItems: 'center',\n  marginTop: 12,\n  marginBottom: 4,\n  paddingLeft: 24,\n  paddingRight: 12,\n  // prevent jumping in safari\n  maxHeight: 40,\n  minHeight: 40\n});\nconst PickersCalendarHeaderLabelContainer = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'LabelContainer',\n  overridesResolver: (_, styles) => styles.labelContainer\n})(({\n  theme\n}) => _extends({\n  display: 'flex',\n  overflow: 'hidden',\n  alignItems: 'center',\n  cursor: 'pointer',\n  marginRight: 'auto'\n}, theme.typography.body1, {\n  fontWeight: theme.typography.fontWeightMedium\n}));\nconst PickersCalendarHeaderLabel = styled('div', {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'Label',\n  overridesResolver: (_, styles) => styles.label\n})({\n  marginRight: 6\n});\nconst PickersCalendarHeaderSwitchViewButton = styled(IconButton, {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'SwitchViewButton',\n  overridesResolver: (_, styles) => styles.switchViewButton\n})({\n  marginRight: 'auto',\n  variants: [{\n    props: {\n      view: 'year'\n    },\n    style: {\n      [`.${pickersCalendarHeaderClasses.switchViewIcon}`]: {\n        transform: 'rotate(180deg)'\n      }\n    }\n  }]\n});\nconst PickersCalendarHeaderSwitchViewIcon = styled(ArrowDropDownIcon, {\n  name: 'MuiPickersCalendarHeader',\n  slot: 'SwitchViewIcon',\n  overridesResolver: (_, styles) => styles.switchViewIcon\n})(({\n  theme\n}) => ({\n  willChange: 'transform',\n  transition: theme.transitions.create('transform'),\n  transform: 'rotate(0deg)'\n}));\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n * - [DateRangeCalendar](https://mui.com/x/react-date-pickers/date-range-calendar/)\n * - [Custom slots and subcomponents](https://mui.com/x/react-date-pickers/custom-components/)\n *\n * API:\n *\n * - [PickersCalendarHeader API](https://mui.com/x/api/date-pickers/pickers-calendar-header/)\n */\nconst PickersCalendarHeader = /*#__PURE__*/React.forwardRef(function PickersCalendarHeader(inProps, ref) {\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersCalendarHeader'\n  });\n  const {\n      slots,\n      slotProps,\n      currentMonth: month,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onMonthChange,\n      onViewChange,\n      view,\n      reduceAnimations,\n      views,\n      labelId,\n      className,\n      timezone,\n      format = `${utils.formats.month} ${utils.formats.year}`\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  const classes = useUtilityClasses(props);\n  const SwitchViewButton = slots?.switchViewButton ?? PickersCalendarHeaderSwitchViewButton;\n  const switchViewButtonProps = useSlotProps({\n    elementType: SwitchViewButton,\n    externalSlotProps: slotProps?.switchViewButton,\n    additionalProps: {\n      size: 'small',\n      'aria-label': translations.calendarViewSwitchingButtonAriaLabel(view)\n    },\n    ownerState,\n    className: classes.switchViewButton\n  });\n  const SwitchViewIcon = slots?.switchViewIcon ?? PickersCalendarHeaderSwitchViewIcon;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: SwitchViewIcon,\n      externalSlotProps: slotProps?.switchViewIcon,\n      ownerState,\n      className: classes.switchViewIcon\n    }),\n    switchViewIconProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const selectNextMonth = () => onMonthChange(utils.addMonths(month, 1), 'left');\n  const selectPreviousMonth = () => onMonthChange(utils.addMonths(month, -1), 'right');\n  const isNextMonthDisabled = useNextMonthDisabled(month, {\n    disableFuture,\n    maxDate,\n    timezone\n  });\n  const isPreviousMonthDisabled = usePreviousMonthDisabled(month, {\n    disablePast,\n    minDate,\n    timezone\n  });\n  const handleToggleView = () => {\n    if (views.length === 1 || !onViewChange || disabled) {\n      return;\n    }\n    if (views.length === 2) {\n      onViewChange(views.find(el => el !== view) || views[0]);\n    } else {\n      // switching only between first 2\n      const nextIndexToOpen = views.indexOf(view) !== 0 ? 0 : 1;\n      onViewChange(views[nextIndexToOpen]);\n    }\n  };\n\n  // No need to display more information\n  if (views.length === 1 && views[0] === 'year') {\n    return null;\n  }\n  const label = utils.formatByString(month, format);\n  return /*#__PURE__*/_jsxs(PickersCalendarHeaderRoot, _extends({}, other, {\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    children: [/*#__PURE__*/_jsxs(PickersCalendarHeaderLabelContainer, {\n      role: \"presentation\",\n      onClick: handleToggleView,\n      ownerState: ownerState\n      // putting this on the label item element below breaks when using transition\n      ,\n      \"aria-live\": \"polite\",\n      className: classes.labelContainer,\n      children: [/*#__PURE__*/_jsx(PickersFadeTransitionGroup, {\n        reduceAnimations: reduceAnimations,\n        transKey: label,\n        children: /*#__PURE__*/_jsx(PickersCalendarHeaderLabel, {\n          id: labelId,\n          ownerState: ownerState,\n          className: classes.label,\n          children: label\n        })\n      }), views.length > 1 && !disabled && /*#__PURE__*/_jsx(SwitchViewButton, _extends({}, switchViewButtonProps, {\n        children: /*#__PURE__*/_jsx(SwitchViewIcon, _extends({}, switchViewIconProps))\n      }))]\n    }), /*#__PURE__*/_jsx(Fade, {\n      in: view === 'day',\n      appear: !reduceAnimations,\n      enter: !reduceAnimations,\n      children: /*#__PURE__*/_jsx(PickersArrowSwitcher, {\n        slots: slots,\n        slotProps: slotProps,\n        onGoToPrevious: selectPreviousMonth,\n        isPreviousDisabled: isPreviousMonthDisabled,\n        previousLabel: translations.previousMonth,\n        onGoToNext: selectNextMonth,\n        isNextDisabled: isNextMonthDisabled,\n        nextLabel: translations.nextMonth\n      })\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? PickersCalendarHeader.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  currentMonth: PropTypes.object.isRequired,\n  disabled: PropTypes.bool,\n  disableFuture: PropTypes.bool,\n  disablePast: PropTypes.bool,\n  /**\n   * Format used to display the date.\n   * @default `${adapter.formats.month} ${adapter.formats.year}`\n   */\n  format: PropTypes.string,\n  /**\n   * Id of the calendar text element.\n   * It is used to establish an `aria-labelledby` relationship with the calendar `grid` element.\n   */\n  labelId: PropTypes.string,\n  maxDate: PropTypes.object.isRequired,\n  minDate: PropTypes.object.isRequired,\n  onMonthChange: PropTypes.func.isRequired,\n  onViewChange: PropTypes.func,\n  reduceAnimations: PropTypes.bool.isRequired,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  timezone: PropTypes.string.isRequired,\n  view: PropTypes.oneOf(['day', 'month', 'year']).isRequired,\n  views: PropTypes.arrayOf(PropTypes.oneOf(['day', 'month', 'year']).isRequired).isRequired\n} : void 0;\nexport { PickersCalendarHeader };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,EAAE,kBAAkB,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,CAAC;EAC5OC,UAAU,GAAG,CAAC,YAAY,CAAC;AAC7B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,UAAU,MAAM,0BAA0B;AACjD,SAASC,sBAAsB,QAAQ,oCAAoC;AAC3E,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,0BAA0B,QAAQ,+CAA+C;AAC1F,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,oBAAoB,QAAQ,uDAAuD;AAC5F,SAASC,wBAAwB,EAAEC,oBAAoB,QAAQ,0CAA0C;AACzG,SAASC,oCAAoC,EAAEC,4BAA4B,QAAQ,mCAAmC;AACtH,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,gBAAgB,EAAE,CAAC,kBAAkB,CAAC;IACtCC,cAAc,EAAE,CAAC,gBAAgB;EACnC,CAAC;EACD,OAAOvB,cAAc,CAACkB,KAAK,EAAET,oCAAoC,EAAEQ,OAAO,CAAC;AAC7E,CAAC;AACD,MAAMO,yBAAyB,GAAG3B,MAAM,CAAC,KAAK,EAAE;EAC9C4B,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACV;AAC3C,CAAC,CAAC,CAAC;EACDW,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,CAAC;EACfC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChB;EACAC,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,mCAAmC,GAAGzC,MAAM,CAAC,KAAK,EAAE;EACxD4B,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC3C,CAAC,CAAC,CAAC,CAAC;EACFmB;AACF,CAAC,KAAKjD,QAAQ,CAAC;EACbwC,OAAO,EAAE,MAAM;EACfU,QAAQ,EAAE,QAAQ;EAClBT,UAAU,EAAE,QAAQ;EACpBU,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE;AACf,CAAC,EAAEH,KAAK,CAACI,UAAU,CAACC,KAAK,EAAE;EACzBC,UAAU,EAAEN,KAAK,CAACI,UAAU,CAACG;AAC/B,CAAC,CAAC,CAAC;AACH,MAAMC,0BAA0B,GAAGlD,MAAM,CAAC,KAAK,EAAE;EAC/C4B,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC3C,CAAC,CAAC,CAAC;EACDqB,WAAW,EAAE;AACf,CAAC,CAAC;AACF,MAAMM,qCAAqC,GAAGnD,MAAM,CAACI,UAAU,EAAE;EAC/DwB,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,kBAAkB;EACxBC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC3C,CAAC,CAAC,CAAC;EACDoB,WAAW,EAAE,MAAM;EACnBO,QAAQ,EAAE,CAAC;IACTC,KAAK,EAAE;MACLC,IAAI,EAAE;IACR,CAAC;IACDC,KAAK,EAAE;MACL,CAAC,IAAI1C,4BAA4B,CAACa,cAAc,EAAE,GAAG;QACnD8B,SAAS,EAAE;MACb;IACF;EACF,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,mCAAmC,GAAGzD,MAAM,CAACQ,iBAAiB,EAAE;EACpEoB,IAAI,EAAE,0BAA0B;EAChCC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC3C,CAAC,CAAC,CAAC,CAAC;EACFgB;AACF,CAAC,MAAM;EACLgB,UAAU,EAAE,WAAW;EACvBC,UAAU,EAAEjB,KAAK,CAACkB,WAAW,CAACC,MAAM,CAAC,WAAW,CAAC;EACjDL,SAAS,EAAE;AACb,CAAC,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,qBAAqB,GAAG,aAAalE,KAAK,CAACmE,UAAU,CAAC,SAASD,qBAAqBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvG,MAAMC,YAAY,GAAG7D,sBAAsB,CAAC,CAAC;EAC7C,MAAM8D,KAAK,GAAG7D,QAAQ,CAAC,CAAC;EACxB,MAAM+C,KAAK,GAAGpD,aAAa,CAAC;IAC1BoD,KAAK,EAAEW,OAAO;IACdpC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFP,KAAK;MACL+C,SAAS;MACTC,YAAY,EAAEC,KAAK;MACnBC,QAAQ;MACRC,aAAa;MACbC,WAAW;MACXC,OAAO;MACPC,OAAO;MACPC,aAAa;MACbC,YAAY;MACZvB,IAAI;MACJwB,gBAAgB;MAChBC,KAAK;MACLC,OAAO;MACPC,SAAS;MACTC,QAAQ;MACRC,MAAM,GAAG,GAAGhB,KAAK,CAACiB,OAAO,CAACd,KAAK,IAAIH,KAAK,CAACiB,OAAO,CAACC,IAAI;IACvD,CAAC,GAAGhC,KAAK;IACTiC,KAAK,GAAG9F,6BAA6B,CAAC6D,KAAK,EAAE3D,SAAS,CAAC;EACzD,MAAMyB,UAAU,GAAGkC,KAAK;EACxB,MAAMjC,OAAO,GAAGF,iBAAiB,CAACmC,KAAK,CAAC;EACxC,MAAMkC,gBAAgB,GAAGlE,KAAK,EAAEI,gBAAgB,IAAI0B,qCAAqC;EACzF,MAAMqC,qBAAqB,GAAGtF,YAAY,CAAC;IACzCuF,WAAW,EAAEF,gBAAgB;IAC7BG,iBAAiB,EAAEtB,SAAS,EAAE3C,gBAAgB;IAC9CkE,eAAe,EAAE;MACfC,IAAI,EAAE,OAAO;MACb,YAAY,EAAE1B,YAAY,CAAC2B,oCAAoC,CAACvC,IAAI;IACtE,CAAC;IACDnC,UAAU;IACV8D,SAAS,EAAE7D,OAAO,CAACK;EACrB,CAAC,CAAC;EACF,MAAMqE,cAAc,GAAGzE,KAAK,EAAEK,cAAc,IAAI+B,mCAAmC;EACnF;EACA,MAAMsC,aAAa,GAAG7F,YAAY,CAAC;MAC/BuF,WAAW,EAAEK,cAAc;MAC3BJ,iBAAiB,EAAEtB,SAAS,EAAE1C,cAAc;MAC5CP,UAAU;MACV8D,SAAS,EAAE7D,OAAO,CAACM;IACrB,CAAC,CAAC;IACFsE,mBAAmB,GAAGxG,6BAA6B,CAACuG,aAAa,EAAEpG,UAAU,CAAC;EAChF,MAAMsG,eAAe,GAAGA,CAAA,KAAMrB,aAAa,CAACT,KAAK,CAAC+B,SAAS,CAAC5B,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;EAC9E,MAAM6B,mBAAmB,GAAGA,CAAA,KAAMvB,aAAa,CAACT,KAAK,CAAC+B,SAAS,CAAC5B,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;EACpF,MAAM8B,mBAAmB,GAAGzF,oBAAoB,CAAC2D,KAAK,EAAE;IACtDE,aAAa;IACbE,OAAO;IACPQ;EACF,CAAC,CAAC;EACF,MAAMmB,uBAAuB,GAAG3F,wBAAwB,CAAC4D,KAAK,EAAE;IAC9DG,WAAW;IACXE,OAAO;IACPO;EACF,CAAC,CAAC;EACF,MAAMoB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIvB,KAAK,CAACwB,MAAM,KAAK,CAAC,IAAI,CAAC1B,YAAY,IAAIN,QAAQ,EAAE;MACnD;IACF;IACA,IAAIQ,KAAK,CAACwB,MAAM,KAAK,CAAC,EAAE;MACtB1B,YAAY,CAACE,KAAK,CAACyB,IAAI,CAACC,EAAE,IAAIA,EAAE,KAAKnD,IAAI,CAAC,IAAIyB,KAAK,CAAC,CAAC,CAAC,CAAC;IACzD,CAAC,MAAM;MACL;MACA,MAAM2B,eAAe,GAAG3B,KAAK,CAAC4B,OAAO,CAACrD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MACzDuB,YAAY,CAACE,KAAK,CAAC2B,eAAe,CAAC,CAAC;IACtC;EACF,CAAC;;EAED;EACA,IAAI3B,KAAK,CAACwB,MAAM,KAAK,CAAC,IAAIxB,KAAK,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;IAC7C,OAAO,IAAI;EACb;EACA,MAAMvD,KAAK,GAAG2C,KAAK,CAACyC,cAAc,CAACtC,KAAK,EAAEa,MAAM,CAAC;EACjD,OAAO,aAAalE,KAAK,CAACU,yBAAyB,EAAElC,QAAQ,CAAC,CAAC,CAAC,EAAE6F,KAAK,EAAE;IACvEnE,UAAU,EAAEA,UAAU;IACtB8D,SAAS,EAAEnF,IAAI,CAACsB,OAAO,CAACE,IAAI,EAAE2D,SAAS,CAAC;IACxChB,GAAG,EAAEA,GAAG;IACR4C,QAAQ,EAAE,CAAC,aAAa5F,KAAK,CAACwB,mCAAmC,EAAE;MACjEqE,IAAI,EAAE,cAAc;MACpBC,OAAO,EAAET,gBAAgB;MACzBnF,UAAU,EAAEA;MACZ;MAAA;;MAEA,WAAW,EAAE,QAAQ;MACrB8D,SAAS,EAAE7D,OAAO,CAACG,cAAc;MACjCsF,QAAQ,EAAE,CAAC,aAAa9F,IAAI,CAACR,0BAA0B,EAAE;QACvDuE,gBAAgB,EAAEA,gBAAgB;QAClCkC,QAAQ,EAAExF,KAAK;QACfqF,QAAQ,EAAE,aAAa9F,IAAI,CAACmC,0BAA0B,EAAE;UACtD+D,EAAE,EAAEjC,OAAO;UACX7D,UAAU,EAAEA,UAAU;UACtB8D,SAAS,EAAE7D,OAAO,CAACI,KAAK;UACxBqF,QAAQ,EAAErF;QACZ,CAAC;MACH,CAAC,CAAC,EAAEuD,KAAK,CAACwB,MAAM,GAAG,CAAC,IAAI,CAAChC,QAAQ,IAAI,aAAaxD,IAAI,CAACwE,gBAAgB,EAAE9F,QAAQ,CAAC,CAAC,CAAC,EAAE+F,qBAAqB,EAAE;QAC3GqB,QAAQ,EAAE,aAAa9F,IAAI,CAAC+E,cAAc,EAAErG,QAAQ,CAAC,CAAC,CAAC,EAAEuG,mBAAmB,CAAC;MAC/E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,EAAE,aAAajF,IAAI,CAAChB,IAAI,EAAE;MAC1BmH,EAAE,EAAE5D,IAAI,KAAK,KAAK;MAClB6D,MAAM,EAAE,CAACrC,gBAAgB;MACzBsC,KAAK,EAAE,CAACtC,gBAAgB;MACxB+B,QAAQ,EAAE,aAAa9F,IAAI,CAACN,oBAAoB,EAAE;QAChDY,KAAK,EAAEA,KAAK;QACZ+C,SAAS,EAAEA,SAAS;QACpBiD,cAAc,EAAElB,mBAAmB;QACnCmB,kBAAkB,EAAEjB,uBAAuB;QAC3CkB,aAAa,EAAErD,YAAY,CAACsD,aAAa;QACzCC,UAAU,EAAExB,eAAe;QAC3ByB,cAAc,EAAEtB,mBAAmB;QACnCuB,SAAS,EAAEzD,YAAY,CAAC0D;MAC1B,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjE,qBAAqB,CAACkE,SAAS,GAAG;EACxE;EACA;EACA;EACA;EACA;AACF;AACA;EACE5G,OAAO,EAAEvB,SAAS,CAACoI,MAAM;EACzBhD,SAAS,EAAEpF,SAAS,CAACqI,MAAM;EAC3B7D,YAAY,EAAExE,SAAS,CAACoI,MAAM,CAACE,UAAU;EACzC5D,QAAQ,EAAE1E,SAAS,CAACuI,IAAI;EACxB5D,aAAa,EAAE3E,SAAS,CAACuI,IAAI;EAC7B3D,WAAW,EAAE5E,SAAS,CAACuI,IAAI;EAC3B;AACF;AACA;AACA;EACEjD,MAAM,EAAEtF,SAAS,CAACqI,MAAM;EACxB;AACF;AACA;AACA;EACElD,OAAO,EAAEnF,SAAS,CAACqI,MAAM;EACzBxD,OAAO,EAAE7E,SAAS,CAACoI,MAAM,CAACE,UAAU;EACpCxD,OAAO,EAAE9E,SAAS,CAACoI,MAAM,CAACE,UAAU;EACpCvD,aAAa,EAAE/E,SAAS,CAACwI,IAAI,CAACF,UAAU;EACxCtD,YAAY,EAAEhF,SAAS,CAACwI,IAAI;EAC5BvD,gBAAgB,EAAEjF,SAAS,CAACuI,IAAI,CAACD,UAAU;EAC3C;AACF;AACA;AACA;EACE/D,SAAS,EAAEvE,SAAS,CAACoI,MAAM;EAC3B;AACF;AACA;AACA;EACE5G,KAAK,EAAExB,SAAS,CAACoI,MAAM;EACvB;AACF;AACA;EACEK,EAAE,EAAEzI,SAAS,CAAC0I,SAAS,CAAC,CAAC1I,SAAS,CAAC2I,OAAO,CAAC3I,SAAS,CAAC0I,SAAS,CAAC,CAAC1I,SAAS,CAACwI,IAAI,EAAExI,SAAS,CAACoI,MAAM,EAAEpI,SAAS,CAACuI,IAAI,CAAC,CAAC,CAAC,EAAEvI,SAAS,CAACwI,IAAI,EAAExI,SAAS,CAACoI,MAAM,CAAC,CAAC;EACvJ/C,QAAQ,EAAErF,SAAS,CAACqI,MAAM,CAACC,UAAU;EACrC7E,IAAI,EAAEzD,SAAS,CAAC4I,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAACN,UAAU;EAC1DpD,KAAK,EAAElF,SAAS,CAAC2I,OAAO,CAAC3I,SAAS,CAAC4I,KAAK,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAACN,UAAU,CAAC,CAACA;AACjF,CAAC,GAAG,KAAK,CAAC;AACV,SAASrE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}