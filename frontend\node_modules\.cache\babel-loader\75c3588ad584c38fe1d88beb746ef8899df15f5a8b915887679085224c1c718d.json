{"ast": null, "code": "/**\n * Ponyfill for Java's Long class.\n */\nvar Long = /** @class */function () {\n  function Long() {}\n  /**\n   * Parses a string to a number, since JS has no really Int64.\n   *\n   * @param num Numeric string.\n   * @param radix Destination radix.\n   */\n  Long.parseLong = function (num, radix) {\n    if (radix === void 0) {\n      radix = undefined;\n    }\n    return parseInt(num, radix);\n  };\n  return Long;\n}();\nexport default Long;", "map": {"version": 3, "names": ["<PERSON>", "parseLong", "num", "radix", "undefined", "parseInt"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/Long.js"], "sourcesContent": ["/**\n * Ponyfill for Java's Long class.\n */\nvar Long = /** @class */ (function () {\n    function Long() {\n    }\n    /**\n     * Parses a string to a number, since JS has no really Int64.\n     *\n     * @param num Numeric string.\n     * @param radix Destination radix.\n     */\n    Long.parseLong = function (num, radix) {\n        if (radix === void 0) { radix = undefined; }\n        return parseInt(num, radix);\n    };\n    return Long;\n}());\nexport default Long;\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAAA,EAAG,CAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIA,IAAI,CAACC,SAAS,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACnC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAGC,SAAS;IAAE;IAC3C,OAAOC,QAAQ,CAACH,GAAG,EAAEC,KAAK,CAAC;EAC/B,CAAC;EACD,OAAOH,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,eAAeA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}