{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport AztecReader from '../core/aztec/AztecReader';\n/**\n * Aztec Code reader to use from browser.\n *\n * @class BrowserAztecCodeReader\n * @extends {BrowserCodeReader}\n */\nvar BrowserAztecCodeReader = /** @class */function (_super) {\n  __extends(BrowserAztec<PERSON>odeReader, _super);\n  /**\n   * Creates an instance of BrowserAztecCodeReader.\n   * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries\n   *\n   * @memberOf BrowserAztecCodeReader\n   */\n  function BrowserAztecCodeReader(timeBetweenScansMillis) {\n    if (timeBetweenScansMillis === void 0) {\n      timeBetweenScansMillis = 500;\n    }\n    return _super.call(this, new AztecReader(), timeBetweenScansMillis) || this;\n  }\n  return BrowserAztecCodeReader;\n}(BrowserCodeReader);\nexport { BrowserAztecCodeReader };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "AztecReader", "BrowserAztecCodeReader", "_super", "timeBetweenScansMillis", "call"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/browser/BrowserAztecCodeReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport AztecReader from '../core/aztec/AztecReader';\n/**\n * Aztec Code reader to use from browser.\n *\n * @class BrowserAztecCodeReader\n * @extends {BrowserCodeReader}\n */\nvar BrowserAztecCodeReader = /** @class */ (function (_super) {\n    __extends(BrowserAztecCodeReader, _super);\n    /**\n     * Creates an instance of BrowserAztecCodeReader.\n     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent decode tries\n     *\n     * @memberOf BrowserAztecCodeReader\n     */\n    function BrowserAztecCodeReader(timeBetweenScansMillis) {\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        return _super.call(this, new AztecReader(), timeBetweenScansMillis) || this;\n    }\n    return BrowserAztecCodeReader;\n}(BrowserCodeReader));\nexport { BrowserAztecCodeReader };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,SAASI,iBAAiB,QAAQ,qBAAqB;AACvD,OAAOC,WAAW,MAAM,2BAA2B;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC1DjB,SAAS,CAACgB,sBAAsB,EAAEC,MAAM,CAAC;EACzC;AACJ;AACA;AACA;AACA;AACA;EACI,SAASD,sBAAsBA,CAACE,sBAAsB,EAAE;IACpD,IAAIA,sBAAsB,KAAK,KAAK,CAAC,EAAE;MAAEA,sBAAsB,GAAG,GAAG;IAAE;IACvE,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAE,IAAIJ,WAAW,CAAC,CAAC,EAAEG,sBAAsB,CAAC,IAAI,IAAI;EAC/E;EACA,OAAOF,sBAAsB;AACjC,CAAC,CAACF,iBAAiB,CAAE;AACrB,SAASE,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}