{"ast": null, "code": "import useMediaQuery from '@mui/material/useMediaQuery';\nconst PREFERS_REDUCED_MOTION = '@media (prefers-reduced-motion: reduce)';\n\n// detect if user agent has Android version < 10 or iOS version < 13\nconst mobileVersionMatches = typeof navigator !== 'undefined' && navigator.userAgent.match(/android\\s(\\d+)|OS\\s(\\d+)/i);\nconst androidVersion = mobileVersionMatches && mobileVersionMatches[1] ? parseInt(mobileVersionMatches[1], 10) : null;\nconst iOSVersion = mobileVersionMatches && mobileVersionMatches[2] ? parseInt(mobileVersionMatches[2], 10) : null;\nexport const slowAnimationDevices = androidVersion && androidVersion < 10 || iOSVersion && iOSVersion < 13 || false;\nexport const useDefaultReduceAnimations = () => {\n  const prefersReduced = useMediaQuery(PREFERS_REDUCED_MOTION, {\n    defaultMatches: false\n  });\n  return prefersReduced || slowAnimationDevices;\n};", "map": {"version": 3, "names": ["useMediaQuery", "PREFERS_REDUCED_MOTION", "mobileVersionMatches", "navigator", "userAgent", "match", "androidVersion", "parseInt", "iOSVersion", "slowAnimationDevices", "useDefaultReduceAnimations", "prefersReduced", "defaultMatches"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useDefaultReduceAnimations.js"], "sourcesContent": ["import useMediaQuery from '@mui/material/useMediaQuery';\nconst PREFERS_REDUCED_MOTION = '@media (prefers-reduced-motion: reduce)';\n\n// detect if user agent has Android version < 10 or iOS version < 13\nconst mobileVersionMatches = typeof navigator !== 'undefined' && navigator.userAgent.match(/android\\s(\\d+)|OS\\s(\\d+)/i);\nconst androidVersion = mobileVersionMatches && mobileVersionMatches[1] ? parseInt(mobileVersionMatches[1], 10) : null;\nconst iOSVersion = mobileVersionMatches && mobileVersionMatches[2] ? parseInt(mobileVersionMatches[2], 10) : null;\nexport const slowAnimationDevices = androidVersion && androidVersion < 10 || iOSVersion && iOSVersion < 13 || false;\nexport const useDefaultReduceAnimations = () => {\n  const prefersReduced = useMediaQuery(PREFERS_REDUCED_MOTION, {\n    defaultMatches: false\n  });\n  return prefersReduced || slowAnimationDevices;\n};"], "mappings": "AAAA,OAAOA,aAAa,MAAM,6BAA6B;AACvD,MAAMC,sBAAsB,GAAG,yCAAyC;;AAExE;AACA,MAAMC,oBAAoB,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,2BAA2B,CAAC;AACvH,MAAMC,cAAc,GAAGJ,oBAAoB,IAAIA,oBAAoB,CAAC,CAAC,CAAC,GAAGK,QAAQ,CAACL,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;AACrH,MAAMM,UAAU,GAAGN,oBAAoB,IAAIA,oBAAoB,CAAC,CAAC,CAAC,GAAGK,QAAQ,CAACL,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;AACjH,OAAO,MAAMO,oBAAoB,GAAGH,cAAc,IAAIA,cAAc,GAAG,EAAE,IAAIE,UAAU,IAAIA,UAAU,GAAG,EAAE,IAAI,KAAK;AACnH,OAAO,MAAME,0BAA0B,GAAGA,CAAA,KAAM;EAC9C,MAAMC,cAAc,GAAGX,aAAa,CAACC,sBAAsB,EAAE;IAC3DW,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,OAAOD,cAAc,IAAIF,oBAAoB;AAC/C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}