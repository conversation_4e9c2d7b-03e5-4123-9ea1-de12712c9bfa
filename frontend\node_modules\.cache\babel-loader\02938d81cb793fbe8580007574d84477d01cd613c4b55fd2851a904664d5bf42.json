{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _constants = require('./constants');\nvar _Barcode2 = require('../Barcode.js');\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n} // Encoding documentation:\n// https://en.wikipedia.org/wiki/Code_93#Detailed_outline\n\nvar CODE93 = function (_Barcode) {\n  _inherits(CODE93, _Barcode);\n  function CODE93(data, options) {\n    _classCallCheck(this, CODE93);\n    return _possibleConstructorReturn(this, (CODE93.__proto__ || Object.getPrototypeOf(CODE93)).call(this, data, options));\n  }\n  _createClass(CODE93, [{\n    key: 'valid',\n    value: function valid() {\n      return /^[0-9A-Z\\-. $/+%]+$/.test(this.data);\n    }\n  }, {\n    key: 'encode',\n    value: function encode() {\n      var symbols = this.data.split('').flatMap(function (c) {\n        return _constants.MULTI_SYMBOLS[c] || c;\n      });\n      var encoded = symbols.map(function (s) {\n        return CODE93.getEncoding(s);\n      }).join('');\n\n      // Compute checksum symbols\n      var csumC = CODE93.checksum(symbols, 20);\n      var csumK = CODE93.checksum(symbols.concat(csumC), 15);\n      return {\n        text: this.text,\n        data:\n        // Add the start bits\n        CODE93.getEncoding('\\xff') +\n        // Add the encoded bits\n        encoded +\n        // Add the checksum\n        CODE93.getEncoding(csumC) + CODE93.getEncoding(csumK) +\n        // Add the stop bits\n        CODE93.getEncoding('\\xff') +\n        // Add the termination bit\n        '1'\n      };\n    }\n\n    // Get the binary encoding of a symbol\n  }], [{\n    key: 'getEncoding',\n    value: function getEncoding(symbol) {\n      return _constants.BINARIES[CODE93.symbolValue(symbol)];\n    }\n\n    // Get the symbol for a symbol value\n  }, {\n    key: 'getSymbol',\n    value: function getSymbol(symbolValue) {\n      return _constants.SYMBOLS[symbolValue];\n    }\n\n    // Get the symbol value of a symbol\n  }, {\n    key: 'symbolValue',\n    value: function symbolValue(symbol) {\n      return _constants.SYMBOLS.indexOf(symbol);\n    }\n\n    // Calculate a checksum symbol\n  }, {\n    key: 'checksum',\n    value: function checksum(symbols, maxWeight) {\n      var csum = symbols.slice().reverse().reduce(function (sum, symbol, idx) {\n        var weight = idx % maxWeight + 1;\n        return sum + CODE93.symbolValue(symbol) * weight;\n      }, 0);\n      return CODE93.getSymbol(csum % 47);\n    }\n  }]);\n  return CODE93;\n}(_Barcode3.default);\nexports.default = CODE93;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_constants", "require", "_Barcode2", "_Barcode3", "_interopRequireDefault", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "call", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "CODE93", "_Barcode", "data", "options", "getPrototypeOf", "valid", "test", "encode", "symbols", "split", "flatMap", "c", "MULTI_SYMBOLS", "encoded", "map", "s", "getEncoding", "join", "csumC", "checksum", "csumK", "concat", "text", "symbol", "BINARIES", "symbolValue", "getSymbol", "SYMBOLS", "indexOf", "maxWeight", "csum", "slice", "reverse", "reduce", "sum", "idx", "weight"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/CODE93/CODE93.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _constants = require('./constants');\n\nvar _Barcode2 = require('../Barcode.js');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// https://en.wikipedia.org/wiki/Code_93#Detailed_outline\n\nvar CODE93 = function (_Barcode) {\n\t_inherits(CODE93, _Barcode);\n\n\tfunction CODE93(data, options) {\n\t\t_classCallCheck(this, CODE93);\n\n\t\treturn _possibleConstructorReturn(this, (CODE93.__proto__ || Object.getPrototypeOf(CODE93)).call(this, data, options));\n\t}\n\n\t_createClass(CODE93, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn (/^[0-9A-Z\\-. $/+%]+$/.test(this.data)\n\t\t\t);\n\t\t}\n\t}, {\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\tvar symbols = this.data.split('').flatMap(function (c) {\n\t\t\t\treturn _constants.MULTI_SYMBOLS[c] || c;\n\t\t\t});\n\t\t\tvar encoded = symbols.map(function (s) {\n\t\t\t\treturn CODE93.getEncoding(s);\n\t\t\t}).join('');\n\n\t\t\t// Compute checksum symbols\n\t\t\tvar csumC = CODE93.checksum(symbols, 20);\n\t\t\tvar csumK = CODE93.checksum(symbols.concat(csumC), 15);\n\n\t\t\treturn {\n\t\t\t\ttext: this.text,\n\t\t\t\tdata:\n\t\t\t\t// Add the start bits\n\t\t\t\tCODE93.getEncoding('\\xff') +\n\t\t\t\t// Add the encoded bits\n\t\t\t\tencoded +\n\t\t\t\t// Add the checksum\n\t\t\t\tCODE93.getEncoding(csumC) + CODE93.getEncoding(csumK) +\n\t\t\t\t// Add the stop bits\n\t\t\t\tCODE93.getEncoding('\\xff') +\n\t\t\t\t// Add the termination bit\n\t\t\t\t'1'\n\t\t\t};\n\t\t}\n\n\t\t// Get the binary encoding of a symbol\n\n\t}], [{\n\t\tkey: 'getEncoding',\n\t\tvalue: function getEncoding(symbol) {\n\t\t\treturn _constants.BINARIES[CODE93.symbolValue(symbol)];\n\t\t}\n\n\t\t// Get the symbol for a symbol value\n\n\t}, {\n\t\tkey: 'getSymbol',\n\t\tvalue: function getSymbol(symbolValue) {\n\t\t\treturn _constants.SYMBOLS[symbolValue];\n\t\t}\n\n\t\t// Get the symbol value of a symbol\n\n\t}, {\n\t\tkey: 'symbolValue',\n\t\tvalue: function symbolValue(symbol) {\n\t\t\treturn _constants.SYMBOLS.indexOf(symbol);\n\t\t}\n\n\t\t// Calculate a checksum symbol\n\n\t}, {\n\t\tkey: 'checksum',\n\t\tvalue: function checksum(symbols, maxWeight) {\n\t\t\tvar csum = symbols.slice().reverse().reduce(function (sum, symbol, idx) {\n\t\t\t\tvar weight = idx % maxWeight + 1;\n\t\t\t\treturn sum + CODE93.symbolValue(symbol) * weight;\n\t\t\t}, 0);\n\n\t\t\treturn CODE93.getSymbol(csum % 47);\n\t\t}\n\t}]);\n\n\treturn CODE93;\n}(_Barcode3.default);\n\nexports.default = CODE93;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEvC,IAAIC,SAAS,GAAGD,OAAO,CAAC,eAAe,CAAC;AAExC,IAAIE,SAAS,GAAGC,sBAAsB,CAACF,SAAS,CAAC;AAEjD,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEb,WAAW,EAAE;EAAE,IAAI,EAAEa,QAAQ,YAAYb,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIc,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAI,CAACD,IAAI,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGD,IAAI;AAAE;AAE/O,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIP,SAAS,CAAC,0DAA0D,GAAG,OAAOO,UAAU,CAAC;EAAE;EAAED,QAAQ,CAACjB,SAAS,GAAGlB,MAAM,CAACqC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAClB,SAAS,EAAE;IAAEoB,WAAW,EAAE;MAAEnC,KAAK,EAAEgC,QAAQ;MAAExB,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIwB,UAAU,EAAEpC,MAAM,CAACuC,cAAc,GAAGvC,MAAM,CAACuC,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AAAE,CAAC,CAAC;AAC/e;;AAEA,IAAIK,MAAM,GAAG,UAAUC,QAAQ,EAAE;EAChCR,SAAS,CAACO,MAAM,EAAEC,QAAQ,CAAC;EAE3B,SAASD,MAAMA,CAACE,IAAI,EAAEC,OAAO,EAAE;IAC9BjB,eAAe,CAAC,IAAI,EAAEc,MAAM,CAAC;IAE7B,OAAOX,0BAA0B,CAAC,IAAI,EAAE,CAACW,MAAM,CAACD,SAAS,IAAIxC,MAAM,CAAC6C,cAAc,CAACJ,MAAM,CAAC,EAAET,IAAI,CAAC,IAAI,EAAEW,IAAI,EAAEC,OAAO,CAAC,CAAC;EACvH;EAEAxC,YAAY,CAACqC,MAAM,EAAE,CAAC;IACrB3B,GAAG,EAAE,OAAO;IACZX,KAAK,EAAE,SAAS2C,KAAKA,CAAA,EAAG;MACvB,OAAQ,qBAAqB,CAACC,IAAI,CAAC,IAAI,CAACJ,IAAI,CAAC;IAE9C;EACD,CAAC,EAAE;IACF7B,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAAS6C,MAAMA,CAAA,EAAG;MACxB,IAAIC,OAAO,GAAG,IAAI,CAACN,IAAI,CAACO,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,UAAUC,CAAC,EAAE;QACtD,OAAOjC,UAAU,CAACkC,aAAa,CAACD,CAAC,CAAC,IAAIA,CAAC;MACxC,CAAC,CAAC;MACF,IAAIE,OAAO,GAAGL,OAAO,CAACM,GAAG,CAAC,UAAUC,CAAC,EAAE;QACtC,OAAOf,MAAM,CAACgB,WAAW,CAACD,CAAC,CAAC;MAC7B,CAAC,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;;MAEX;MACA,IAAIC,KAAK,GAAGlB,MAAM,CAACmB,QAAQ,CAACX,OAAO,EAAE,EAAE,CAAC;MACxC,IAAIY,KAAK,GAAGpB,MAAM,CAACmB,QAAQ,CAACX,OAAO,CAACa,MAAM,CAACH,KAAK,CAAC,EAAE,EAAE,CAAC;MAEtD,OAAO;QACNI,IAAI,EAAE,IAAI,CAACA,IAAI;QACfpB,IAAI;QACJ;QACAF,MAAM,CAACgB,WAAW,CAAC,MAAM,CAAC;QAC1B;QACAH,OAAO;QACP;QACAb,MAAM,CAACgB,WAAW,CAACE,KAAK,CAAC,GAAGlB,MAAM,CAACgB,WAAW,CAACI,KAAK,CAAC;QACrD;QACApB,MAAM,CAACgB,WAAW,CAAC,MAAM,CAAC;QAC1B;QACA;MACD,CAAC;IACF;;IAEA;EAED,CAAC,CAAC,EAAE,CAAC;IACJ3C,GAAG,EAAE,aAAa;IAClBX,KAAK,EAAE,SAASsD,WAAWA,CAACO,MAAM,EAAE;MACnC,OAAO7C,UAAU,CAAC8C,QAAQ,CAACxB,MAAM,CAACyB,WAAW,CAACF,MAAM,CAAC,CAAC;IACvD;;IAEA;EAED,CAAC,EAAE;IACFlD,GAAG,EAAE,WAAW;IAChBX,KAAK,EAAE,SAASgE,SAASA,CAACD,WAAW,EAAE;MACtC,OAAO/C,UAAU,CAACiD,OAAO,CAACF,WAAW,CAAC;IACvC;;IAEA;EAED,CAAC,EAAE;IACFpD,GAAG,EAAE,aAAa;IAClBX,KAAK,EAAE,SAAS+D,WAAWA,CAACF,MAAM,EAAE;MACnC,OAAO7C,UAAU,CAACiD,OAAO,CAACC,OAAO,CAACL,MAAM,CAAC;IAC1C;;IAEA;EAED,CAAC,EAAE;IACFlD,GAAG,EAAE,UAAU;IACfX,KAAK,EAAE,SAASyD,QAAQA,CAACX,OAAO,EAAEqB,SAAS,EAAE;MAC5C,IAAIC,IAAI,GAAGtB,OAAO,CAACuB,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAEX,MAAM,EAAEY,GAAG,EAAE;QACvE,IAAIC,MAAM,GAAGD,GAAG,GAAGN,SAAS,GAAG,CAAC;QAChC,OAAOK,GAAG,GAAGlC,MAAM,CAACyB,WAAW,CAACF,MAAM,CAAC,GAAGa,MAAM;MACjD,CAAC,EAAE,CAAC,CAAC;MAEL,OAAOpC,MAAM,CAAC0B,SAAS,CAACI,IAAI,GAAG,EAAE,CAAC;IACnC;EACD,CAAC,CAAC,CAAC;EAEH,OAAO9B,MAAM;AACd,CAAC,CAACnB,SAAS,CAACI,OAAO,CAAC;AAEpBxB,OAAO,CAACwB,OAAO,GAAGe,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}