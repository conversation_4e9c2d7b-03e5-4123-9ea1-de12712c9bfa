{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport BitArray from '../common/BitArray';\nimport DecodeHintType from '../DecodeHintType';\nimport ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport NotFoundException from '../NotFoundException';\n/**\n * Encapsulates functionality and implementation that is common to all families\n * of one-dimensional barcodes.\n *\n * <AUTHOR> (<PERSON>)\n * <AUTHOR>\n */\nvar OneDReader = /** @class */function () {\n  function OneDReader() {}\n  /*\n  @Override\n  public Result decode(BinaryBitmap image) throws NotFoundException, FormatException {\n    return decode(image, null);\n  }\n  */\n  // Note that we don't try rotation without the try harder flag, even if rotation was supported.\n  // @Override\n  OneDReader.prototype.decode = function (image, hints) {\n    try {\n      return this.doDecode(image, hints);\n    } catch (nfe) {\n      var tryHarder = hints && hints.get(DecodeHintType.TRY_HARDER) === true;\n      if (tryHarder && image.isRotateSupported()) {\n        var rotatedImage = image.rotateCounterClockwise();\n        var result = this.doDecode(rotatedImage, hints);\n        // Record that we found it rotated 90 degrees CCW / 270 degrees CW\n        var metadata = result.getResultMetadata();\n        var orientation_1 = 270;\n        if (metadata !== null && metadata.get(ResultMetadataType.ORIENTATION) === true) {\n          // But if we found it reversed in doDecode(), add in that result here:\n          orientation_1 = orientation_1 + metadata.get(ResultMetadataType.ORIENTATION) % 360;\n        }\n        result.putMetadata(ResultMetadataType.ORIENTATION, orientation_1);\n        // Update result points\n        var points = result.getResultPoints();\n        if (points !== null) {\n          var height = rotatedImage.getHeight();\n          for (var i = 0; i < points.length; i++) {\n            points[i] = new ResultPoint(height - points[i].getY() - 1, points[i].getX());\n          }\n        }\n        return result;\n      } else {\n        throw new NotFoundException();\n      }\n    }\n  };\n  // @Override\n  OneDReader.prototype.reset = function () {\n    // do nothing\n  };\n  /**\n   * We're going to examine rows from the middle outward, searching alternately above and below the\n   * middle, and farther out each time. rowStep is the number of rows between each successive\n   * attempt above and below the middle. So we'd scan row middle, then middle - rowStep, then\n   * middle + rowStep, then middle - (2 * rowStep), etc.\n   * rowStep is bigger as the image is taller, but is always at least 1. We've somewhat arbitrarily\n   * decided that moving up and down by about 1/16 of the image is pretty good; we try more of the\n   * image if \"trying harder\".\n   *\n   * @param image The image to decode\n   * @param hints Any hints that were requested\n   * @return The contents of the decoded barcode\n   * @throws NotFoundException Any spontaneous errors which occur\n   */\n  OneDReader.prototype.doDecode = function (image, hints) {\n    var width = image.getWidth();\n    var height = image.getHeight();\n    var row = new BitArray(width);\n    var tryHarder = hints && hints.get(DecodeHintType.TRY_HARDER) === true;\n    var rowStep = Math.max(1, height >> (tryHarder ? 8 : 5));\n    var maxLines;\n    if (tryHarder) {\n      maxLines = height; // Look at the whole image, not just the center\n    } else {\n      maxLines = 15; // 15 rows spaced 1/32 apart is roughly the middle half of the image\n    }\n    var middle = Math.trunc(height / 2);\n    for (var x = 0; x < maxLines; x++) {\n      // Scanning from the middle out. Determine which row we're looking at next:\n      var rowStepsAboveOrBelow = Math.trunc((x + 1) / 2);\n      var isAbove = (x & 0x01) === 0; // i.e. is x even?\n      var rowNumber = middle + rowStep * (isAbove ? rowStepsAboveOrBelow : -rowStepsAboveOrBelow);\n      if (rowNumber < 0 || rowNumber >= height) {\n        // Oops, if we run off the top or bottom, stop\n        break;\n      }\n      // Estimate black point for this row and load it:\n      try {\n        row = image.getBlackRow(rowNumber, row);\n      } catch (ignored) {\n        continue;\n      }\n      var _loop_1 = function (attempt) {\n        if (attempt === 1) {\n          // trying again?\n          row.reverse(); // reverse the row and continue\n          // This means we will only ever draw result points *once* in the life of this method\n          // since we want to avoid drawing the wrong points after flipping the row, and,\n          // don't want to clutter with noise from every single row scan -- just the scans\n          // that start on the center line.\n          if (hints && hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK) === true) {\n            var newHints_1 = new Map();\n            hints.forEach(function (hint, key) {\n              return newHints_1.set(key, hint);\n            });\n            newHints_1.delete(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n            hints = newHints_1;\n          }\n        }\n        try {\n          // Look for a barcode\n          var result = this_1.decodeRow(rowNumber, row, hints);\n          // We found our barcode\n          if (attempt === 1) {\n            // But it was upside down, so note that\n            result.putMetadata(ResultMetadataType.ORIENTATION, 180);\n            // And remember to flip the result points horizontally.\n            var points = result.getResultPoints();\n            if (points !== null) {\n              points[0] = new ResultPoint(width - points[0].getX() - 1, points[0].getY());\n              points[1] = new ResultPoint(width - points[1].getX() - 1, points[1].getY());\n            }\n          }\n          return {\n            value: result\n          };\n        } catch (re) {\n          // continue -- just couldn't decode this row\n        }\n      };\n      var this_1 = this;\n      // While we have the image data in a BitArray, it's fairly cheap to reverse it in place to\n      // handle decoding upside down barcodes.\n      for (var attempt = 0; attempt < 2; attempt++) {\n        var state_1 = _loop_1(attempt);\n        if (typeof state_1 === \"object\") return state_1.value;\n      }\n    }\n    throw new NotFoundException();\n  };\n  /**\n   * Records the size of successive runs of white and black pixels in a row, starting at a given point.\n   * The values are recorded in the given array, and the number of runs recorded is equal to the size\n   * of the array. If the row starts on a white pixel at the given start point, then the first count\n   * recorded is the run of white pixels starting from that point; likewise it is the count of a run\n   * of black pixels if the row begin on a black pixels at that point.\n   *\n   * @param row row to count from\n   * @param start offset into row to start at\n   * @param counters array into which to record counts\n   * @throws NotFoundException if counters cannot be filled entirely from row before running out\n   *  of pixels\n   */\n  OneDReader.recordPattern = function (row, start, counters) {\n    var numCounters = counters.length;\n    for (var index = 0; index < numCounters; index++) counters[index] = 0;\n    var end = row.getSize();\n    if (start >= end) {\n      throw new NotFoundException();\n    }\n    var isWhite = !row.get(start);\n    var counterPosition = 0;\n    var i = start;\n    while (i < end) {\n      if (row.get(i) !== isWhite) {\n        counters[counterPosition]++;\n      } else {\n        if (++counterPosition === numCounters) {\n          break;\n        } else {\n          counters[counterPosition] = 1;\n          isWhite = !isWhite;\n        }\n      }\n      i++;\n    }\n    // If we read fully the last section of pixels and filled up our counters -- or filled\n    // the last counter but ran off the side of the image, OK. Otherwise, a problem.\n    if (!(counterPosition === numCounters || counterPosition === numCounters - 1 && i === end)) {\n      throw new NotFoundException();\n    }\n  };\n  OneDReader.recordPatternInReverse = function (row, start, counters) {\n    // This could be more efficient I guess\n    var numTransitionsLeft = counters.length;\n    var last = row.get(start);\n    while (start > 0 && numTransitionsLeft >= 0) {\n      if (row.get(--start) !== last) {\n        numTransitionsLeft--;\n        last = !last;\n      }\n    }\n    if (numTransitionsLeft >= 0) {\n      throw new NotFoundException();\n    }\n    OneDReader.recordPattern(row, start + 1, counters);\n  };\n  /**\n   * Determines how closely a set of observed counts of runs of black/white values matches a given\n   * target pattern. This is reported as the ratio of the total variance from the expected pattern\n   * proportions across all pattern elements, to the length of the pattern.\n   *\n   * @param counters observed counters\n   * @param pattern expected pattern\n   * @param maxIndividualVariance The most any counter can differ before we give up\n   * @return ratio of total variance between counters and pattern compared to total pattern size\n   */\n  OneDReader.patternMatchVariance = function (counters, pattern, maxIndividualVariance) {\n    var numCounters = counters.length;\n    var total = 0;\n    var patternLength = 0;\n    for (var i = 0; i < numCounters; i++) {\n      total += counters[i];\n      patternLength += pattern[i];\n    }\n    if (total < patternLength) {\n      // If we don't even have one pixel per unit of bar width, assume this is too small\n      // to reliably match, so fail:\n      return Number.POSITIVE_INFINITY;\n    }\n    var unitBarWidth = total / patternLength;\n    maxIndividualVariance *= unitBarWidth;\n    var totalVariance = 0.0;\n    for (var x = 0; x < numCounters; x++) {\n      var counter = counters[x];\n      var scaledPattern = pattern[x] * unitBarWidth;\n      var variance = counter > scaledPattern ? counter - scaledPattern : scaledPattern - counter;\n      if (variance > maxIndividualVariance) {\n        return Number.POSITIVE_INFINITY;\n      }\n      totalVariance += variance;\n    }\n    return totalVariance / total;\n  };\n  return OneDReader;\n}();\nexport default OneDReader;", "map": {"version": 3, "names": ["BitArray", "DecodeHintType", "ResultMetadataType", "ResultPoint", "NotFoundException", "OneDReader", "prototype", "decode", "image", "hints", "doDecode", "nfe", "<PERSON><PERSON><PERSON><PERSON>", "get", "TRY_HARDER", "isRotateSupported", "rotatedImage", "rotateCounterClockwise", "result", "metadata", "getResultMetadata", "orientation_1", "ORIENTATION", "putMetadata", "points", "getResultPoints", "height", "getHeight", "i", "length", "getY", "getX", "reset", "width", "getWidth", "row", "rowStep", "Math", "max", "maxLines", "middle", "trunc", "x", "rowStepsAboveOrBelow", "isAbove", "rowNumber", "getBlackRow", "ignored", "_loop_1", "attempt", "reverse", "NEED_RESULT_POINT_CALLBACK", "newHints_1", "Map", "for<PERSON>ach", "hint", "key", "set", "delete", "this_1", "decodeRow", "value", "re", "state_1", "recordPattern", "start", "counters", "numCounters", "index", "end", "getSize", "<PERSON><PERSON><PERSON><PERSON>", "counterPosition", "recordPatternInReverse", "numTransitionsLeft", "last", "patternMatchVariance", "pattern", "maxIndividualV<PERSON>ce", "total", "<PERSON><PERSON><PERSON><PERSON>", "Number", "POSITIVE_INFINITY", "unitBarWidth", "totalVariance", "counter", "scaledPattern", "variance"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/OneDReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport BitArray from '../common/BitArray';\nimport DecodeHintType from '../DecodeHintType';\nimport ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport NotFoundException from '../NotFoundException';\n/**\n * Encapsulates functionality and implementation that is common to all families\n * of one-dimensional barcodes.\n *\n * <AUTHOR> (<PERSON>)\n * <AUTHOR>\n */\nvar OneDReader = /** @class */ (function () {\n    function OneDReader() {\n    }\n    /*\n    @Override\n    public Result decode(BinaryBitmap image) throws NotFoundException, FormatException {\n      return decode(image, null);\n    }\n    */\n    // Note that we don't try rotation without the try harder flag, even if rotation was supported.\n    // @Override\n    OneDReader.prototype.decode = function (image, hints) {\n        try {\n            return this.doDecode(image, hints);\n        }\n        catch (nfe) {\n            var tryHarder = hints && (hints.get(DecodeHintType.TRY_HARDER) === true);\n            if (tryHarder && image.isRotateSupported()) {\n                var rotatedImage = image.rotateCounterClockwise();\n                var result = this.doDecode(rotatedImage, hints);\n                // Record that we found it rotated 90 degrees CCW / 270 degrees CW\n                var metadata = result.getResultMetadata();\n                var orientation_1 = 270;\n                if (metadata !== null && (metadata.get(ResultMetadataType.ORIENTATION) === true)) {\n                    // But if we found it reversed in doDecode(), add in that result here:\n                    orientation_1 = (orientation_1 + metadata.get(ResultMetadataType.ORIENTATION) % 360);\n                }\n                result.putMetadata(ResultMetadataType.ORIENTATION, orientation_1);\n                // Update result points\n                var points = result.getResultPoints();\n                if (points !== null) {\n                    var height = rotatedImage.getHeight();\n                    for (var i = 0; i < points.length; i++) {\n                        points[i] = new ResultPoint(height - points[i].getY() - 1, points[i].getX());\n                    }\n                }\n                return result;\n            }\n            else {\n                throw new NotFoundException();\n            }\n        }\n    };\n    // @Override\n    OneDReader.prototype.reset = function () {\n        // do nothing\n    };\n    /**\n     * We're going to examine rows from the middle outward, searching alternately above and below the\n     * middle, and farther out each time. rowStep is the number of rows between each successive\n     * attempt above and below the middle. So we'd scan row middle, then middle - rowStep, then\n     * middle + rowStep, then middle - (2 * rowStep), etc.\n     * rowStep is bigger as the image is taller, but is always at least 1. We've somewhat arbitrarily\n     * decided that moving up and down by about 1/16 of the image is pretty good; we try more of the\n     * image if \"trying harder\".\n     *\n     * @param image The image to decode\n     * @param hints Any hints that were requested\n     * @return The contents of the decoded barcode\n     * @throws NotFoundException Any spontaneous errors which occur\n     */\n    OneDReader.prototype.doDecode = function (image, hints) {\n        var width = image.getWidth();\n        var height = image.getHeight();\n        var row = new BitArray(width);\n        var tryHarder = hints && (hints.get(DecodeHintType.TRY_HARDER) === true);\n        var rowStep = Math.max(1, height >> (tryHarder ? 8 : 5));\n        var maxLines;\n        if (tryHarder) {\n            maxLines = height; // Look at the whole image, not just the center\n        }\n        else {\n            maxLines = 15; // 15 rows spaced 1/32 apart is roughly the middle half of the image\n        }\n        var middle = Math.trunc(height / 2);\n        for (var x = 0; x < maxLines; x++) {\n            // Scanning from the middle out. Determine which row we're looking at next:\n            var rowStepsAboveOrBelow = Math.trunc((x + 1) / 2);\n            var isAbove = (x & 0x01) === 0; // i.e. is x even?\n            var rowNumber = middle + rowStep * (isAbove ? rowStepsAboveOrBelow : -rowStepsAboveOrBelow);\n            if (rowNumber < 0 || rowNumber >= height) {\n                // Oops, if we run off the top or bottom, stop\n                break;\n            }\n            // Estimate black point for this row and load it:\n            try {\n                row = image.getBlackRow(rowNumber, row);\n            }\n            catch (ignored) {\n                continue;\n            }\n            var _loop_1 = function (attempt) {\n                if (attempt === 1) { // trying again?\n                    row.reverse(); // reverse the row and continue\n                    // This means we will only ever draw result points *once* in the life of this method\n                    // since we want to avoid drawing the wrong points after flipping the row, and,\n                    // don't want to clutter with noise from every single row scan -- just the scans\n                    // that start on the center line.\n                    if (hints && (hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK) === true)) {\n                        var newHints_1 = new Map();\n                        hints.forEach(function (hint, key) { return newHints_1.set(key, hint); });\n                        newHints_1.delete(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n                        hints = newHints_1;\n                    }\n                }\n                try {\n                    // Look for a barcode\n                    var result = this_1.decodeRow(rowNumber, row, hints);\n                    // We found our barcode\n                    if (attempt === 1) {\n                        // But it was upside down, so note that\n                        result.putMetadata(ResultMetadataType.ORIENTATION, 180);\n                        // And remember to flip the result points horizontally.\n                        var points = result.getResultPoints();\n                        if (points !== null) {\n                            points[0] = new ResultPoint(width - points[0].getX() - 1, points[0].getY());\n                            points[1] = new ResultPoint(width - points[1].getX() - 1, points[1].getY());\n                        }\n                    }\n                    return { value: result };\n                }\n                catch (re) {\n                    // continue -- just couldn't decode this row\n                }\n            };\n            var this_1 = this;\n            // While we have the image data in a BitArray, it's fairly cheap to reverse it in place to\n            // handle decoding upside down barcodes.\n            for (var attempt = 0; attempt < 2; attempt++) {\n                var state_1 = _loop_1(attempt);\n                if (typeof state_1 === \"object\")\n                    return state_1.value;\n            }\n        }\n        throw new NotFoundException();\n    };\n    /**\n     * Records the size of successive runs of white and black pixels in a row, starting at a given point.\n     * The values are recorded in the given array, and the number of runs recorded is equal to the size\n     * of the array. If the row starts on a white pixel at the given start point, then the first count\n     * recorded is the run of white pixels starting from that point; likewise it is the count of a run\n     * of black pixels if the row begin on a black pixels at that point.\n     *\n     * @param row row to count from\n     * @param start offset into row to start at\n     * @param counters array into which to record counts\n     * @throws NotFoundException if counters cannot be filled entirely from row before running out\n     *  of pixels\n     */\n    OneDReader.recordPattern = function (row, start, counters) {\n        var numCounters = counters.length;\n        for (var index = 0; index < numCounters; index++)\n            counters[index] = 0;\n        var end = row.getSize();\n        if (start >= end) {\n            throw new NotFoundException();\n        }\n        var isWhite = !row.get(start);\n        var counterPosition = 0;\n        var i = start;\n        while (i < end) {\n            if (row.get(i) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (++counterPosition === numCounters) {\n                    break;\n                }\n                else {\n                    counters[counterPosition] = 1;\n                    isWhite = !isWhite;\n                }\n            }\n            i++;\n        }\n        // If we read fully the last section of pixels and filled up our counters -- or filled\n        // the last counter but ran off the side of the image, OK. Otherwise, a problem.\n        if (!(counterPosition === numCounters || (counterPosition === numCounters - 1 && i === end))) {\n            throw new NotFoundException();\n        }\n    };\n    OneDReader.recordPatternInReverse = function (row, start, counters) {\n        // This could be more efficient I guess\n        var numTransitionsLeft = counters.length;\n        var last = row.get(start);\n        while (start > 0 && numTransitionsLeft >= 0) {\n            if (row.get(--start) !== last) {\n                numTransitionsLeft--;\n                last = !last;\n            }\n        }\n        if (numTransitionsLeft >= 0) {\n            throw new NotFoundException();\n        }\n        OneDReader.recordPattern(row, start + 1, counters);\n    };\n    /**\n     * Determines how closely a set of observed counts of runs of black/white values matches a given\n     * target pattern. This is reported as the ratio of the total variance from the expected pattern\n     * proportions across all pattern elements, to the length of the pattern.\n     *\n     * @param counters observed counters\n     * @param pattern expected pattern\n     * @param maxIndividualVariance The most any counter can differ before we give up\n     * @return ratio of total variance between counters and pattern compared to total pattern size\n     */\n    OneDReader.patternMatchVariance = function (counters, pattern, maxIndividualVariance) {\n        var numCounters = counters.length;\n        var total = 0;\n        var patternLength = 0;\n        for (var i = 0; i < numCounters; i++) {\n            total += counters[i];\n            patternLength += pattern[i];\n        }\n        if (total < patternLength) {\n            // If we don't even have one pixel per unit of bar width, assume this is too small\n            // to reliably match, so fail:\n            return Number.POSITIVE_INFINITY;\n        }\n        var unitBarWidth = total / patternLength;\n        maxIndividualVariance *= unitBarWidth;\n        var totalVariance = 0.0;\n        for (var x = 0; x < numCounters; x++) {\n            var counter = counters[x];\n            var scaledPattern = pattern[x] * unitBarWidth;\n            var variance = counter > scaledPattern ? counter - scaledPattern : scaledPattern - counter;\n            if (variance > maxIndividualVariance) {\n                return Number.POSITIVE_INFINITY;\n            }\n            totalVariance += variance;\n        }\n        return totalVariance / total;\n    };\n    return OneDReader;\n}());\nexport default OneDReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAAA,EAAG,CACtB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI;EACA;EACAA,UAAU,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAClD,IAAI;MACA,OAAO,IAAI,CAACC,QAAQ,CAACF,KAAK,EAAEC,KAAK,CAAC;IACtC,CAAC,CACD,OAAOE,GAAG,EAAE;MACR,IAAIC,SAAS,GAAGH,KAAK,IAAKA,KAAK,CAACI,GAAG,CAACZ,cAAc,CAACa,UAAU,CAAC,KAAK,IAAK;MACxE,IAAIF,SAAS,IAAIJ,KAAK,CAACO,iBAAiB,CAAC,CAAC,EAAE;QACxC,IAAIC,YAAY,GAAGR,KAAK,CAACS,sBAAsB,CAAC,CAAC;QACjD,IAAIC,MAAM,GAAG,IAAI,CAACR,QAAQ,CAACM,YAAY,EAAEP,KAAK,CAAC;QAC/C;QACA,IAAIU,QAAQ,GAAGD,MAAM,CAACE,iBAAiB,CAAC,CAAC;QACzC,IAAIC,aAAa,GAAG,GAAG;QACvB,IAAIF,QAAQ,KAAK,IAAI,IAAKA,QAAQ,CAACN,GAAG,CAACX,kBAAkB,CAACoB,WAAW,CAAC,KAAK,IAAK,EAAE;UAC9E;UACAD,aAAa,GAAIA,aAAa,GAAGF,QAAQ,CAACN,GAAG,CAACX,kBAAkB,CAACoB,WAAW,CAAC,GAAG,GAAI;QACxF;QACAJ,MAAM,CAACK,WAAW,CAACrB,kBAAkB,CAACoB,WAAW,EAAED,aAAa,CAAC;QACjE;QACA,IAAIG,MAAM,GAAGN,MAAM,CAACO,eAAe,CAAC,CAAC;QACrC,IAAID,MAAM,KAAK,IAAI,EAAE;UACjB,IAAIE,MAAM,GAAGV,YAAY,CAACW,SAAS,CAAC,CAAC;UACrC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;YACpCJ,MAAM,CAACI,CAAC,CAAC,GAAG,IAAIzB,WAAW,CAACuB,MAAM,GAAGF,MAAM,CAACI,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAEN,MAAM,CAACI,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;UAChF;QACJ;QACA,OAAOb,MAAM;MACjB,CAAC,MACI;QACD,MAAM,IAAId,iBAAiB,CAAC,CAAC;MACjC;IACJ;EACJ,CAAC;EACD;EACAC,UAAU,CAACC,SAAS,CAAC0B,KAAK,GAAG,YAAY;IACrC;EAAA,CACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3B,UAAU,CAACC,SAAS,CAACI,QAAQ,GAAG,UAAUF,KAAK,EAAEC,KAAK,EAAE;IACpD,IAAIwB,KAAK,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,CAAC;IAC5B,IAAIR,MAAM,GAAGlB,KAAK,CAACmB,SAAS,CAAC,CAAC;IAC9B,IAAIQ,GAAG,GAAG,IAAInC,QAAQ,CAACiC,KAAK,CAAC;IAC7B,IAAIrB,SAAS,GAAGH,KAAK,IAAKA,KAAK,CAACI,GAAG,CAACZ,cAAc,CAACa,UAAU,CAAC,KAAK,IAAK;IACxE,IAAIsB,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEZ,MAAM,KAAKd,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxD,IAAI2B,QAAQ;IACZ,IAAI3B,SAAS,EAAE;MACX2B,QAAQ,GAAGb,MAAM,CAAC,CAAC;IACvB,CAAC,MACI;MACDa,QAAQ,GAAG,EAAE,CAAC,CAAC;IACnB;IACA,IAAIC,MAAM,GAAGH,IAAI,CAACI,KAAK,CAACf,MAAM,GAAG,CAAC,CAAC;IACnC,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,EAAEG,CAAC,EAAE,EAAE;MAC/B;MACA,IAAIC,oBAAoB,GAAGN,IAAI,CAACI,KAAK,CAAC,CAACC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;MAClD,IAAIE,OAAO,GAAG,CAACF,CAAC,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC;MAChC,IAAIG,SAAS,GAAGL,MAAM,GAAGJ,OAAO,IAAIQ,OAAO,GAAGD,oBAAoB,GAAG,CAACA,oBAAoB,CAAC;MAC3F,IAAIE,SAAS,GAAG,CAAC,IAAIA,SAAS,IAAInB,MAAM,EAAE;QACtC;QACA;MACJ;MACA;MACA,IAAI;QACAS,GAAG,GAAG3B,KAAK,CAACsC,WAAW,CAACD,SAAS,EAAEV,GAAG,CAAC;MAC3C,CAAC,CACD,OAAOY,OAAO,EAAE;QACZ;MACJ;MACA,IAAIC,OAAO,GAAG,SAAAA,CAAUC,OAAO,EAAE;QAC7B,IAAIA,OAAO,KAAK,CAAC,EAAE;UAAE;UACjBd,GAAG,CAACe,OAAO,CAAC,CAAC,CAAC,CAAC;UACf;UACA;UACA;UACA;UACA,IAAIzC,KAAK,IAAKA,KAAK,CAACI,GAAG,CAACZ,cAAc,CAACkD,0BAA0B,CAAC,KAAK,IAAK,EAAE;YAC1E,IAAIC,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;YAC1B5C,KAAK,CAAC6C,OAAO,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;cAAE,OAAOJ,UAAU,CAACK,GAAG,CAACD,GAAG,EAAED,IAAI,CAAC;YAAE,CAAC,CAAC;YACzEH,UAAU,CAACM,MAAM,CAACzD,cAAc,CAACkD,0BAA0B,CAAC;YAC5D1C,KAAK,GAAG2C,UAAU;UACtB;QACJ;QACA,IAAI;UACA;UACA,IAAIlC,MAAM,GAAGyC,MAAM,CAACC,SAAS,CAACf,SAAS,EAAEV,GAAG,EAAE1B,KAAK,CAAC;UACpD;UACA,IAAIwC,OAAO,KAAK,CAAC,EAAE;YACf;YACA/B,MAAM,CAACK,WAAW,CAACrB,kBAAkB,CAACoB,WAAW,EAAE,GAAG,CAAC;YACvD;YACA,IAAIE,MAAM,GAAGN,MAAM,CAACO,eAAe,CAAC,CAAC;YACrC,IAAID,MAAM,KAAK,IAAI,EAAE;cACjBA,MAAM,CAAC,CAAC,CAAC,GAAG,IAAIrB,WAAW,CAAC8B,KAAK,GAAGT,MAAM,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC,GAAG,CAAC,EAAEP,MAAM,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;cAC3EN,MAAM,CAAC,CAAC,CAAC,GAAG,IAAIrB,WAAW,CAAC8B,KAAK,GAAGT,MAAM,CAAC,CAAC,CAAC,CAACO,IAAI,CAAC,CAAC,GAAG,CAAC,EAAEP,MAAM,CAAC,CAAC,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;YAC/E;UACJ;UACA,OAAO;YAAE+B,KAAK,EAAE3C;UAAO,CAAC;QAC5B,CAAC,CACD,OAAO4C,EAAE,EAAE;UACP;QAAA;MAER,CAAC;MACD,IAAIH,MAAM,GAAG,IAAI;MACjB;MACA;MACA,KAAK,IAAIV,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG,CAAC,EAAEA,OAAO,EAAE,EAAE;QAC1C,IAAIc,OAAO,GAAGf,OAAO,CAACC,OAAO,CAAC;QAC9B,IAAI,OAAOc,OAAO,KAAK,QAAQ,EAC3B,OAAOA,OAAO,CAACF,KAAK;MAC5B;IACJ;IACA,MAAM,IAAIzD,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,UAAU,CAAC2D,aAAa,GAAG,UAAU7B,GAAG,EAAE8B,KAAK,EAAEC,QAAQ,EAAE;IACvD,IAAIC,WAAW,GAAGD,QAAQ,CAACrC,MAAM;IACjC,KAAK,IAAIuC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,WAAW,EAAEC,KAAK,EAAE,EAC5CF,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;IACvB,IAAIC,GAAG,GAAGlC,GAAG,CAACmC,OAAO,CAAC,CAAC;IACvB,IAAIL,KAAK,IAAII,GAAG,EAAE;MACd,MAAM,IAAIjE,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAImE,OAAO,GAAG,CAACpC,GAAG,CAACtB,GAAG,CAACoD,KAAK,CAAC;IAC7B,IAAIO,eAAe,GAAG,CAAC;IACvB,IAAI5C,CAAC,GAAGqC,KAAK;IACb,OAAOrC,CAAC,GAAGyC,GAAG,EAAE;MACZ,IAAIlC,GAAG,CAACtB,GAAG,CAACe,CAAC,CAAC,KAAK2C,OAAO,EAAE;QACxBL,QAAQ,CAACM,eAAe,CAAC,EAAE;MAC/B,CAAC,MACI;QACD,IAAI,EAAEA,eAAe,KAAKL,WAAW,EAAE;UACnC;QACJ,CAAC,MACI;UACDD,QAAQ,CAACM,eAAe,CAAC,GAAG,CAAC;UAC7BD,OAAO,GAAG,CAACA,OAAO;QACtB;MACJ;MACA3C,CAAC,EAAE;IACP;IACA;IACA;IACA,IAAI,EAAE4C,eAAe,KAAKL,WAAW,IAAKK,eAAe,KAAKL,WAAW,GAAG,CAAC,IAAIvC,CAAC,KAAKyC,GAAI,CAAC,EAAE;MAC1F,MAAM,IAAIjE,iBAAiB,CAAC,CAAC;IACjC;EACJ,CAAC;EACDC,UAAU,CAACoE,sBAAsB,GAAG,UAAUtC,GAAG,EAAE8B,KAAK,EAAEC,QAAQ,EAAE;IAChE;IACA,IAAIQ,kBAAkB,GAAGR,QAAQ,CAACrC,MAAM;IACxC,IAAI8C,IAAI,GAAGxC,GAAG,CAACtB,GAAG,CAACoD,KAAK,CAAC;IACzB,OAAOA,KAAK,GAAG,CAAC,IAAIS,kBAAkB,IAAI,CAAC,EAAE;MACzC,IAAIvC,GAAG,CAACtB,GAAG,CAAC,EAAEoD,KAAK,CAAC,KAAKU,IAAI,EAAE;QAC3BD,kBAAkB,EAAE;QACpBC,IAAI,GAAG,CAACA,IAAI;MAChB;IACJ;IACA,IAAID,kBAAkB,IAAI,CAAC,EAAE;MACzB,MAAM,IAAItE,iBAAiB,CAAC,CAAC;IACjC;IACAC,UAAU,CAAC2D,aAAa,CAAC7B,GAAG,EAAE8B,KAAK,GAAG,CAAC,EAAEC,QAAQ,CAAC;EACtD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI7D,UAAU,CAACuE,oBAAoB,GAAG,UAAUV,QAAQ,EAAEW,OAAO,EAAEC,qBAAqB,EAAE;IAClF,IAAIX,WAAW,GAAGD,QAAQ,CAACrC,MAAM;IACjC,IAAIkD,KAAK,GAAG,CAAC;IACb,IAAIC,aAAa,GAAG,CAAC;IACrB,KAAK,IAAIpD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuC,WAAW,EAAEvC,CAAC,EAAE,EAAE;MAClCmD,KAAK,IAAIb,QAAQ,CAACtC,CAAC,CAAC;MACpBoD,aAAa,IAAIH,OAAO,CAACjD,CAAC,CAAC;IAC/B;IACA,IAAImD,KAAK,GAAGC,aAAa,EAAE;MACvB;MACA;MACA,OAAOC,MAAM,CAACC,iBAAiB;IACnC;IACA,IAAIC,YAAY,GAAGJ,KAAK,GAAGC,aAAa;IACxCF,qBAAqB,IAAIK,YAAY;IACrC,IAAIC,aAAa,GAAG,GAAG;IACvB,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,WAAW,EAAEzB,CAAC,EAAE,EAAE;MAClC,IAAI2C,OAAO,GAAGnB,QAAQ,CAACxB,CAAC,CAAC;MACzB,IAAI4C,aAAa,GAAGT,OAAO,CAACnC,CAAC,CAAC,GAAGyC,YAAY;MAC7C,IAAII,QAAQ,GAAGF,OAAO,GAAGC,aAAa,GAAGD,OAAO,GAAGC,aAAa,GAAGA,aAAa,GAAGD,OAAO;MAC1F,IAAIE,QAAQ,GAAGT,qBAAqB,EAAE;QAClC,OAAOG,MAAM,CAACC,iBAAiB;MACnC;MACAE,aAAa,IAAIG,QAAQ;IAC7B;IACA,OAAOH,aAAa,GAAGL,KAAK;EAChC,CAAC;EACD,OAAO1E,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}