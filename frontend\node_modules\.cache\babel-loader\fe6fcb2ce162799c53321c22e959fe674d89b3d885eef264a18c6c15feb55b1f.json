{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport AI01decoder from './AI01decoder';\nimport NotFoundException from '../../../../NotFoundException';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI01392xDecoder = /** @class */function (_super) {\n  __extends(AI01392xDecoder, _super);\n  function AI01392xDecoder(information) {\n    return _super.call(this, information) || this;\n  }\n  AI01392xDecoder.prototype.parseInformation = function () {\n    if (this.getInformation().getSize() < AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE) {\n      throw new NotFoundException();\n    }\n    var buf = new StringBuilder();\n    this.encodeCompressedGtin(buf, AI01392xDecoder.HEADER_SIZE);\n    var lastAIdigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE, AI01392xDecoder.LAST_DIGIT_SIZE);\n    buf.append('(392');\n    buf.append(lastAIdigit);\n    buf.append(')');\n    var decodedInformation = this.getGeneralDecoder().decodeGeneralPurposeField(AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE + AI01392xDecoder.LAST_DIGIT_SIZE, null);\n    buf.append(decodedInformation.getNewString());\n    return buf.toString();\n  };\n  AI01392xDecoder.HEADER_SIZE = 5 + 1 + 2;\n  AI01392xDecoder.LAST_DIGIT_SIZE = 2;\n  return AI01392xDecoder;\n}(AI01decoder);\nexport default AI01392xDecoder;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "AI01decoder", "NotFoundException", "StringBuilder", "AI01392xDecoder", "_super", "information", "call", "parseInformation", "getInformation", "getSize", "HEADER_SIZE", "GTIN_SIZE", "buf", "encodeCompressedGtin", "lastAIdigit", "getGeneralDecoder", "extractNumericValueFromBitArray", "LAST_DIGIT_SIZE", "append", "decodedInformation", "decodeGeneralPurposeField", "getNewString", "toString"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01392xDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01decoder from './AI01decoder';\nimport NotFoundException from '../../../../NotFoundException';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI01392xDecoder = /** @class */ (function (_super) {\n    __extends(AI01392xDecoder, _super);\n    function AI01392xDecoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01392xDecoder.prototype.parseInformation = function () {\n        if (this.getInformation().getSize() < AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE) {\n            throw new NotFoundException();\n        }\n        var buf = new StringBuilder();\n        this.encodeCompressedGtin(buf, AI01392xDecoder.HEADER_SIZE);\n        var lastAIdigit = this.getGeneralDecoder().extractNumericValueFromBitArray(AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE, AI01392xDecoder.LAST_DIGIT_SIZE);\n        buf.append('(392');\n        buf.append(lastAIdigit);\n        buf.append(')');\n        var decodedInformation = this.getGeneralDecoder().decodeGeneralPurposeField(AI01392xDecoder.HEADER_SIZE + AI01decoder.GTIN_SIZE + AI01392xDecoder.LAST_DIGIT_SIZE, null);\n        buf.append(decodedInformation.getNewString());\n        return buf.toString();\n    };\n    AI01392xDecoder.HEADER_SIZE = 5 + 1 + 2;\n    AI01392xDecoder.LAST_DIGIT_SIZE = 2;\n    return AI01392xDecoder;\n}(AI01decoder));\nexport default AI01392xDecoder;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,WAAW,MAAM,eAAe;AACvC,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,IAAIC,eAAe,GAAG,aAAe,UAAUC,MAAM,EAAE;EACnDlB,SAAS,CAACiB,eAAe,EAAEC,MAAM,CAAC;EAClC,SAASD,eAAeA,CAACE,WAAW,EAAE;IAClC,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,WAAW,CAAC,IAAI,IAAI;EACjD;EACAF,eAAe,CAACL,SAAS,CAACS,gBAAgB,GAAG,YAAY;IACrD,IAAI,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,GAAGN,eAAe,CAACO,WAAW,GAAGV,WAAW,CAACW,SAAS,EAAE;MACvF,MAAM,IAAIV,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAIW,GAAG,GAAG,IAAIV,aAAa,CAAC,CAAC;IAC7B,IAAI,CAACW,oBAAoB,CAACD,GAAG,EAAET,eAAe,CAACO,WAAW,CAAC;IAC3D,IAAII,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAACC,+BAA+B,CAACb,eAAe,CAACO,WAAW,GAAGV,WAAW,CAACW,SAAS,EAAER,eAAe,CAACc,eAAe,CAAC;IAChKL,GAAG,CAACM,MAAM,CAAC,MAAM,CAAC;IAClBN,GAAG,CAACM,MAAM,CAACJ,WAAW,CAAC;IACvBF,GAAG,CAACM,MAAM,CAAC,GAAG,CAAC;IACf,IAAIC,kBAAkB,GAAG,IAAI,CAACJ,iBAAiB,CAAC,CAAC,CAACK,yBAAyB,CAACjB,eAAe,CAACO,WAAW,GAAGV,WAAW,CAACW,SAAS,GAAGR,eAAe,CAACc,eAAe,EAAE,IAAI,CAAC;IACxKL,GAAG,CAACM,MAAM,CAACC,kBAAkB,CAACE,YAAY,CAAC,CAAC,CAAC;IAC7C,OAAOT,GAAG,CAACU,QAAQ,CAAC,CAAC;EACzB,CAAC;EACDnB,eAAe,CAACO,WAAW,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;EACvCP,eAAe,CAACc,eAAe,GAAG,CAAC;EACnC,OAAOd,eAAe;AAC1B,CAAC,CAACH,WAAW,CAAE;AACf,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}