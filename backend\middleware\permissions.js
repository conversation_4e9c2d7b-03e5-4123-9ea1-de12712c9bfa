/**
 * Permission checking middleware
 * Ensures users have the required permissions to access specific routes
 */

/**
 * Check if user has a specific permission
 * @param {string} permissionName - The permission to check (e.g., 'customers.create')
 * @returns {Function} Express middleware function
 */
const requirePermission = (permissionName) => {
  return (req, res, next) => {
    try {
      console.log(`🔐 Checking permission: ${permissionName}`);

      // Check if user is authenticated
      if (!req.user) {
        console.log('❌ No user in request');
        return res.status(401).json({
          message: 'Authentication required'
        });
      }

      console.log(`👤 User: ${req.user.username}, Role: ${req.user.role?.name}`);

      // Developer role has all permissions
      if (req.user.role?.name === 'developer') {
        console.log('✅ Developer role - access granted');
        return next();
      }

      // Check if user has the required permission
      const userPermissions = req.user.role?.permissions || [];
      console.log(`🔑 User permissions: ${userPermissions.map(p => p.name).join(', ')}`);

      const hasPermission = userPermissions.some(permission =>
        permission.name === permissionName
      );

      if (!hasPermission) {
        console.log(`❌ Permission denied: ${permissionName}`);
        return res.status(403).json({
          message: `Access denied. Required permission: ${permissionName}`,
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      console.log(`✅ Permission granted: ${permissionName}`);
      next();

    } catch (error) {
      console.error('❌ Permission check error:', error);
      res.status(500).json({
        message: 'Internal server error'
      });
    }
  };
};

/**
 * Check if user has any of the specified permissions
 * @param {string[]} permissionNames - Array of permissions to check
 * @returns {Function} Express middleware function
 */
const requireAnyPermission = (permissionNames) => {
  return (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        return res.status(401).json({ 
          message: 'Authentication required' 
        });
      }

      // Developer role has all permissions
      if (req.user.role?.name === 'developer') {
        return next();
      }

      // Check if user has any of the required permissions
      const userPermissions = req.user.role?.permissions || [];
      const hasAnyPermission = permissionNames.some(permissionName =>
        userPermissions.some(permission => permission.name === permissionName)
      );

      if (!hasAnyPermission) {
        return res.status(403).json({ 
          message: `Access denied. Required permissions: ${permissionNames.join(' OR ')}`,
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      next();
      
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({ 
        message: 'Internal server error' 
      });
    }
  };
};

/**
 * Check if user has all of the specified permissions
 * @param {string[]} permissionNames - Array of permissions to check
 * @returns {Function} Express middleware function
 */
const requireAllPermissions = (permissionNames) => {
  return (req, res, next) => {
    try {
      // Check if user is authenticated
      if (!req.user) {
        return res.status(401).json({ 
          message: 'Authentication required' 
        });
      }

      // Developer role has all permissions
      if (req.user.role?.name === 'developer') {
        return next();
      }

      // Check if user has all required permissions
      const userPermissions = req.user.role?.permissions || [];
      const hasAllPermissions = permissionNames.every(permissionName =>
        userPermissions.some(permission => permission.name === permissionName)
      );

      if (!hasAllPermissions) {
        return res.status(403).json({ 
          message: `Access denied. Required permissions: ${permissionNames.join(' AND ')}`,
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      next();
      
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({ 
        message: 'Internal server error' 
      });
    }
  };
};

/**
 * Helper function to get user permissions (for debugging)
 */
const getUserPermissions = (req) => {
  if (!req.user || !req.user.role) return [];
  return req.user.role.permissions?.map(p => p.name) || [];
};

module.exports = {
  requirePermission,
  requireAnyPermission,
  requireAllPermissions,
  getUserPermissions
};
