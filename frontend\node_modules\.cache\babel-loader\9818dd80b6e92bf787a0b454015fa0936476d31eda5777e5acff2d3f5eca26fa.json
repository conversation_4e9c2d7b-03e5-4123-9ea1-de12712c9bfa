{"ast": null, "code": "/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.aztec;\n// import com.google.zxing.BarcodeFormat;\nimport BarcodeFormat from '../BarcodeFormat';\n// import com.google.zxing.EncodeHintType;\nimport EncodeHintType from '../EncodeHintType';\n// import com.google.zxing.aztec.encoder.Encoder;\nimport Encoder from './encoder/Encoder';\n// import com.google.zxing.common.BitMatrix;\nimport BitMatrix from '../common/BitMatrix';\n// import java.nio.charset.Charset;\nimport Charset from '../util/Charset';\n// import java.nio.charset.StandardCharsets;\nimport StandardCharsets from '../util/StandardCharsets';\n// import java.util.Map;\nimport Integer from '../util/Integer';\nimport IllegalStateException from '../IllegalStateException';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport StringUtils from '../common/StringUtils';\n/**\n * Renders an Aztec code as a {@link BitMatrix}.\n */\nvar AztecWriter = /** @class */function () {\n  function AztecWriter() {}\n  // @Override\n  AztecWriter.prototype.encode = function (contents, format, width, height) {\n    return this.encodeWithHints(contents, format, width, height, null);\n  };\n  // @Override\n  AztecWriter.prototype.encodeWithHints = function (contents, format, width, height, hints) {\n    var charset = StandardCharsets.ISO_8859_1;\n    var eccPercent = Encoder.DEFAULT_EC_PERCENT;\n    var layers = Encoder.DEFAULT_AZTEC_LAYERS;\n    if (hints != null) {\n      if (hints.has(EncodeHintType.CHARACTER_SET)) {\n        charset = Charset.forName(hints.get(EncodeHintType.CHARACTER_SET).toString());\n      }\n      if (hints.has(EncodeHintType.ERROR_CORRECTION)) {\n        eccPercent = Integer.parseInt(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n      }\n      if (hints.has(EncodeHintType.AZTEC_LAYERS)) {\n        layers = Integer.parseInt(hints.get(EncodeHintType.AZTEC_LAYERS).toString());\n      }\n    }\n    return AztecWriter.encodeLayers(contents, format, width, height, charset, eccPercent, layers);\n  };\n  AztecWriter.encodeLayers = function (contents, format, width, height, charset, eccPercent, layers) {\n    if (format !== BarcodeFormat.AZTEC) {\n      throw new IllegalArgumentException('Can only encode AZTEC, but got ' + format);\n    }\n    var aztec = Encoder.encode(StringUtils.getBytes(contents, charset), eccPercent, layers);\n    return AztecWriter.renderResult(aztec, width, height);\n  };\n  AztecWriter.renderResult = function (code, width, height) {\n    var input = code.getMatrix();\n    if (input == null) {\n      throw new IllegalStateException();\n    }\n    var inputWidth = input.getWidth();\n    var inputHeight = input.getHeight();\n    var outputWidth = Math.max(width, inputWidth);\n    var outputHeight = Math.max(height, inputHeight);\n    var multiple = Math.min(outputWidth / inputWidth, outputHeight / inputHeight);\n    var leftPadding = (outputWidth - inputWidth * multiple) / 2;\n    var topPadding = (outputHeight - inputHeight * multiple) / 2;\n    var output = new BitMatrix(outputWidth, outputHeight);\n    for (var inputY /*int*/ = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n      // Write the contents of this row of the barcode\n      for (var inputX /*int*/ = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n        if (input.get(inputX, inputY)) {\n          output.setRegion(outputX, outputY, multiple, multiple);\n        }\n      }\n    }\n    return output;\n  };\n  return AztecWriter;\n}();\nexport default AztecWriter;", "map": {"version": 3, "names": ["BarcodeFormat", "EncodeHintType", "Encoder", "BitMatrix", "Charset", "StandardCharsets", "Integer", "IllegalStateException", "IllegalArgumentException", "StringUtils", "AztecWriter", "prototype", "encode", "contents", "format", "width", "height", "encodeWithHints", "hints", "charset", "ISO_8859_1", "eccPercent", "DEFAULT_EC_PERCENT", "layers", "DEFAULT_AZTEC_LAYERS", "has", "CHARACTER_SET", "forName", "get", "toString", "ERROR_CORRECTION", "parseInt", "AZTEC_LAYERS", "encodeLayers", "AZTEC", "aztec", "getBytes", "renderResult", "code", "input", "getMatrix", "inputWidth", "getWidth", "inputHeight", "getHeight", "outputWidth", "Math", "max", "outputHeight", "multiple", "min", "leftPadding", "topPadding", "output", "inputY", "outputY", "inputX", "outputX", "setRegion"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/AztecWriter.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.aztec;\n// import com.google.zxing.BarcodeFormat;\nimport BarcodeFormat from '../BarcodeFormat';\n// import com.google.zxing.EncodeHintType;\nimport EncodeHintType from '../EncodeHintType';\n// import com.google.zxing.aztec.encoder.Encoder;\nimport Encoder from './encoder/Encoder';\n// import com.google.zxing.common.BitMatrix;\nimport BitMatrix from '../common/BitMatrix';\n// import java.nio.charset.Charset;\nimport Charset from '../util/Charset';\n// import java.nio.charset.StandardCharsets;\nimport StandardCharsets from '../util/StandardCharsets';\n// import java.util.Map;\nimport Integer from '../util/Integer';\nimport IllegalStateException from '../IllegalStateException';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport StringUtils from '../common/StringUtils';\n/**\n * Renders an Aztec code as a {@link BitMatrix}.\n */\nvar AztecWriter = /** @class */ (function () {\n    function AztecWriter() {\n    }\n    // @Override\n    AztecWriter.prototype.encode = function (contents, format, width, height) {\n        return this.encodeWithHints(contents, format, width, height, null);\n    };\n    // @Override\n    AztecWriter.prototype.encodeWithHints = function (contents, format, width, height, hints) {\n        var charset = StandardCharsets.ISO_8859_1;\n        var eccPercent = Encoder.DEFAULT_EC_PERCENT;\n        var layers = Encoder.DEFAULT_AZTEC_LAYERS;\n        if (hints != null) {\n            if (hints.has(EncodeHintType.CHARACTER_SET)) {\n                charset = Charset.forName(hints.get(EncodeHintType.CHARACTER_SET).toString());\n            }\n            if (hints.has(EncodeHintType.ERROR_CORRECTION)) {\n                eccPercent = Integer.parseInt(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n            }\n            if (hints.has(EncodeHintType.AZTEC_LAYERS)) {\n                layers = Integer.parseInt(hints.get(EncodeHintType.AZTEC_LAYERS).toString());\n            }\n        }\n        return AztecWriter.encodeLayers(contents, format, width, height, charset, eccPercent, layers);\n    };\n    AztecWriter.encodeLayers = function (contents, format, width, height, charset, eccPercent, layers) {\n        if (format !== BarcodeFormat.AZTEC) {\n            throw new IllegalArgumentException('Can only encode AZTEC, but got ' + format);\n        }\n        var aztec = Encoder.encode(StringUtils.getBytes(contents, charset), eccPercent, layers);\n        return AztecWriter.renderResult(aztec, width, height);\n    };\n    AztecWriter.renderResult = function (code, width, height) {\n        var input = code.getMatrix();\n        if (input == null) {\n            throw new IllegalStateException();\n        }\n        var inputWidth = input.getWidth();\n        var inputHeight = input.getHeight();\n        var outputWidth = Math.max(width, inputWidth);\n        var outputHeight = Math.max(height, inputHeight);\n        var multiple = Math.min(outputWidth / inputWidth, outputHeight / inputHeight);\n        var leftPadding = (outputWidth - (inputWidth * multiple)) / 2;\n        var topPadding = (outputHeight - (inputHeight * multiple)) / 2;\n        var output = new BitMatrix(outputWidth, outputHeight);\n        for (var inputY /*int*/ = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n            // Write the contents of this row of the barcode\n            for (var inputX /*int*/ = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n                if (input.get(inputX, inputY)) {\n                    output.setRegion(outputX, outputY, multiple, multiple);\n                }\n            }\n        }\n        return output;\n    };\n    return AztecWriter;\n}());\nexport default AztecWriter;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C;AACA,OAAOC,cAAc,MAAM,mBAAmB;AAC9C;AACA,OAAOC,OAAO,MAAM,mBAAmB;AACvC;AACA,OAAOC,SAAS,MAAM,qBAAqB;AAC3C;AACA,OAAOC,OAAO,MAAM,iBAAiB;AACrC;AACA,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD;AACA,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,qBAAqB,MAAM,0BAA0B;AAC5D,OAAOC,wBAAwB,MAAM,6BAA6B;AAClE,OAAOC,WAAW,MAAM,uBAAuB;AAC/C;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG,CACvB;EACA;EACAA,WAAW,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE;IACtE,OAAO,IAAI,CAACC,eAAe,CAACJ,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAE,IAAI,CAAC;EACtE,CAAC;EACD;EACAN,WAAW,CAACC,SAAS,CAACM,eAAe,GAAG,UAAUJ,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEE,KAAK,EAAE;IACtF,IAAIC,OAAO,GAAGd,gBAAgB,CAACe,UAAU;IACzC,IAAIC,UAAU,GAAGnB,OAAO,CAACoB,kBAAkB;IAC3C,IAAIC,MAAM,GAAGrB,OAAO,CAACsB,oBAAoB;IACzC,IAAIN,KAAK,IAAI,IAAI,EAAE;MACf,IAAIA,KAAK,CAACO,GAAG,CAACxB,cAAc,CAACyB,aAAa,CAAC,EAAE;QACzCP,OAAO,GAAGf,OAAO,CAACuB,OAAO,CAACT,KAAK,CAACU,GAAG,CAAC3B,cAAc,CAACyB,aAAa,CAAC,CAACG,QAAQ,CAAC,CAAC,CAAC;MACjF;MACA,IAAIX,KAAK,CAACO,GAAG,CAACxB,cAAc,CAAC6B,gBAAgB,CAAC,EAAE;QAC5CT,UAAU,GAAGf,OAAO,CAACyB,QAAQ,CAACb,KAAK,CAACU,GAAG,CAAC3B,cAAc,CAAC6B,gBAAgB,CAAC,CAACD,QAAQ,CAAC,CAAC,CAAC;MACxF;MACA,IAAIX,KAAK,CAACO,GAAG,CAACxB,cAAc,CAAC+B,YAAY,CAAC,EAAE;QACxCT,MAAM,GAAGjB,OAAO,CAACyB,QAAQ,CAACb,KAAK,CAACU,GAAG,CAAC3B,cAAc,CAAC+B,YAAY,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAC;MAChF;IACJ;IACA,OAAOnB,WAAW,CAACuB,YAAY,CAACpB,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEG,OAAO,EAAEE,UAAU,EAAEE,MAAM,CAAC;EACjG,CAAC;EACDb,WAAW,CAACuB,YAAY,GAAG,UAAUpB,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEG,OAAO,EAAEE,UAAU,EAAEE,MAAM,EAAE;IAC/F,IAAIT,MAAM,KAAKd,aAAa,CAACkC,KAAK,EAAE;MAChC,MAAM,IAAI1B,wBAAwB,CAAC,iCAAiC,GAAGM,MAAM,CAAC;IAClF;IACA,IAAIqB,KAAK,GAAGjC,OAAO,CAACU,MAAM,CAACH,WAAW,CAAC2B,QAAQ,CAACvB,QAAQ,EAAEM,OAAO,CAAC,EAAEE,UAAU,EAAEE,MAAM,CAAC;IACvF,OAAOb,WAAW,CAAC2B,YAAY,CAACF,KAAK,EAAEpB,KAAK,EAAEC,MAAM,CAAC;EACzD,CAAC;EACDN,WAAW,CAAC2B,YAAY,GAAG,UAAUC,IAAI,EAAEvB,KAAK,EAAEC,MAAM,EAAE;IACtD,IAAIuB,KAAK,GAAGD,IAAI,CAACE,SAAS,CAAC,CAAC;IAC5B,IAAID,KAAK,IAAI,IAAI,EAAE;MACf,MAAM,IAAIhC,qBAAqB,CAAC,CAAC;IACrC;IACA,IAAIkC,UAAU,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC;IACjC,IAAIC,WAAW,GAAGJ,KAAK,CAACK,SAAS,CAAC,CAAC;IACnC,IAAIC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAChC,KAAK,EAAE0B,UAAU,CAAC;IAC7C,IAAIO,YAAY,GAAGF,IAAI,CAACC,GAAG,CAAC/B,MAAM,EAAE2B,WAAW,CAAC;IAChD,IAAIM,QAAQ,GAAGH,IAAI,CAACI,GAAG,CAACL,WAAW,GAAGJ,UAAU,EAAEO,YAAY,GAAGL,WAAW,CAAC;IAC7E,IAAIQ,WAAW,GAAG,CAACN,WAAW,GAAIJ,UAAU,GAAGQ,QAAS,IAAI,CAAC;IAC7D,IAAIG,UAAU,GAAG,CAACJ,YAAY,GAAIL,WAAW,GAAGM,QAAS,IAAI,CAAC;IAC9D,IAAII,MAAM,GAAG,IAAIlD,SAAS,CAAC0C,WAAW,EAAEG,YAAY,CAAC;IACrD,KAAK,IAAIM,MAAM,CAAC,UAAU,CAAC,EAAEC,OAAO,GAAGH,UAAU,EAAEE,MAAM,GAAGX,WAAW,EAAEW,MAAM,EAAE,EAAEC,OAAO,IAAIN,QAAQ,EAAE;MACpG;MACA,KAAK,IAAIO,MAAM,CAAC,UAAU,CAAC,EAAEC,OAAO,GAAGN,WAAW,EAAEK,MAAM,GAAGf,UAAU,EAAEe,MAAM,EAAE,EAAEC,OAAO,IAAIR,QAAQ,EAAE;QACpG,IAAIV,KAAK,CAACX,GAAG,CAAC4B,MAAM,EAAEF,MAAM,CAAC,EAAE;UAC3BD,MAAM,CAACK,SAAS,CAACD,OAAO,EAAEF,OAAO,EAAEN,QAAQ,EAAEA,QAAQ,CAAC;QAC1D;MACJ;IACJ;IACA,OAAOI,MAAM;EACjB,CAAC;EACD,OAAO3C,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}