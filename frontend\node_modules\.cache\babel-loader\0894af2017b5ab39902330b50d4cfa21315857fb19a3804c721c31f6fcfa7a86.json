{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport DecodeHintType from './DecodeHintType';\nimport BarcodeFormat from './BarcodeFormat';\nimport QRCodeReader from './qrcode/QRCodeReader';\nimport AztecReader from './aztec/AztecReader';\nimport MultiFormatOneDReader from './oned/MultiFormatOneDReader';\nimport DataMatrixReader from './datamatrix/DataMatrixReader';\nimport NotFoundException from './NotFoundException';\nimport PDF417Reader from './pdf417/PDF417Reader';\nimport ReaderException from './ReaderException';\n/*namespace com.google.zxing {*/\n/**\n * MultiFormatReader is a convenience class and the main entry point into the library for most uses.\n * By default it attempts to decode all barcode formats that the library supports. Optionally, you\n * can provide a hints object to request different behavior, for example only decoding QR codes.\n *\n * <AUTHOR> Owen\n * <AUTHOR> (Daniel Switkin)\n */\nvar MultiFormatReader = /** @class */function () {\n  function MultiFormatReader() {}\n  /**\n   * This version of decode honors the intent of Reader.decode(BinaryBitmap) in that it\n   * passes null as a hint to the decoders. However, that makes it inefficient to call repeatedly.\n   * Use setHints() followed by decodeWithState() for continuous scan applications.\n   *\n   * @param image The pixel data to decode\n   * @return The contents of the image\n   *\n   * @throws NotFoundException Any errors which occurred\n   */\n  /*@Override*/\n  // public decode(image: BinaryBitmap): Result {\n  //   setHints(null)\n  //   return decodeInternal(image)\n  // }\n  /**\n   * Decode an image using the hints provided. Does not honor existing state.\n   *\n   * @param image The pixel data to decode\n   * @param hints The hints to use, clearing the previous state.\n   * @return The contents of the image\n   *\n   * @throws NotFoundException Any errors which occurred\n   */\n  /*@Override*/\n  MultiFormatReader.prototype.decode = function (image, hints) {\n    this.setHints(hints);\n    return this.decodeInternal(image);\n  };\n  /**\n   * Decode an image using the state set up by calling setHints() previously. Continuous scan\n   * clients will get a <b>large</b> speed increase by using this instead of decode().\n   *\n   * @param image The pixel data to decode\n   * @return The contents of the image\n   *\n   * @throws NotFoundException Any errors which occurred\n   */\n  MultiFormatReader.prototype.decodeWithState = function (image) {\n    // Make sure to set up the default state so we don't crash\n    if (this.readers === null || this.readers === undefined) {\n      this.setHints(null);\n    }\n    return this.decodeInternal(image);\n  };\n  /**\n   * This method adds state to the MultiFormatReader. By setting the hints once, subsequent calls\n   * to decodeWithState(image) can reuse the same set of readers without reallocating memory. This\n   * is important for performance in continuous scan clients.\n   *\n   * @param hints The set of hints to use for subsequent calls to decode(image)\n   */\n  MultiFormatReader.prototype.setHints = function (hints) {\n    this.hints = hints;\n    var tryHarder = hints !== null && hints !== undefined && undefined !== hints.get(DecodeHintType.TRY_HARDER);\n    /*@SuppressWarnings(\"unchecked\")*/\n    var formats = hints === null || hints === undefined ? null : hints.get(DecodeHintType.POSSIBLE_FORMATS);\n    var readers = new Array();\n    if (formats !== null && formats !== undefined) {\n      var addOneDReader = formats.some(function (f) {\n        return f === BarcodeFormat.UPC_A || f === BarcodeFormat.UPC_E || f === BarcodeFormat.EAN_13 || f === BarcodeFormat.EAN_8 || f === BarcodeFormat.CODABAR || f === BarcodeFormat.CODE_39 || f === BarcodeFormat.CODE_93 || f === BarcodeFormat.CODE_128 || f === BarcodeFormat.ITF || f === BarcodeFormat.RSS_14 || f === BarcodeFormat.RSS_EXPANDED;\n      });\n      // Put 1D readers upfront in \"normal\" mode\n      // TYPESCRIPTPORT: TODO: uncomment below as they are ported\n      if (addOneDReader && !tryHarder) {\n        readers.push(new MultiFormatOneDReader(hints));\n      }\n      if (formats.includes(BarcodeFormat.QR_CODE)) {\n        readers.push(new QRCodeReader());\n      }\n      if (formats.includes(BarcodeFormat.DATA_MATRIX)) {\n        readers.push(new DataMatrixReader());\n      }\n      if (formats.includes(BarcodeFormat.AZTEC)) {\n        readers.push(new AztecReader());\n      }\n      if (formats.includes(BarcodeFormat.PDF_417)) {\n        readers.push(new PDF417Reader());\n      }\n      // if (formats.includes(BarcodeFormat.MAXICODE)) {\n      //    readers.push(new MaxiCodeReader())\n      // }\n      // At end in \"try harder\" mode\n      if (addOneDReader && tryHarder) {\n        readers.push(new MultiFormatOneDReader(hints));\n      }\n    }\n    if (readers.length === 0) {\n      if (!tryHarder) {\n        readers.push(new MultiFormatOneDReader(hints));\n      }\n      readers.push(new QRCodeReader());\n      readers.push(new DataMatrixReader());\n      readers.push(new AztecReader());\n      readers.push(new PDF417Reader());\n      // readers.push(new MaxiCodeReader())\n      if (tryHarder) {\n        readers.push(new MultiFormatOneDReader(hints));\n      }\n    }\n    this.readers = readers; // .toArray(new Reader[readers.size()])\n  };\n  /*@Override*/\n  MultiFormatReader.prototype.reset = function () {\n    var e_1, _a;\n    if (this.readers !== null) {\n      try {\n        for (var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var reader = _c.value;\n          reader.reset();\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }\n  };\n  /**\n   * @throws NotFoundException\n   */\n  MultiFormatReader.prototype.decodeInternal = function (image) {\n    var e_2, _a;\n    if (this.readers === null) {\n      throw new ReaderException('No readers where selected, nothing can be read.');\n    }\n    try {\n      for (var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var reader = _c.value;\n        // Trying to decode with ${reader} reader.\n        try {\n          return reader.decode(image, this.hints);\n        } catch (ex) {\n          if (ex instanceof ReaderException) {\n            continue;\n          }\n          // Bad Exception.\n        }\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    throw new NotFoundException('No MultiFormat Readers were able to detect the code.');\n  };\n  return MultiFormatReader;\n}();\nexport default MultiFormatReader;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "DecodeHintType", "BarcodeFormat", "QRCodeReader", "AztecReader", "MultiFormatOneDReader", "DataMatrixReader", "NotFoundException", "PDF417<PERSON><PERSON><PERSON>", "ReaderException", "MultiFormatReader", "prototype", "decode", "image", "hints", "setHints", "decodeInternal", "decodeWithState", "readers", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "get", "TRY_HARDER", "formats", "POSSIBLE_FORMATS", "Array", "addOneDReader", "some", "f", "UPC_A", "UPC_E", "EAN_13", "EAN_8", "CODABAR", "CODE_39", "CODE_93", "CODE_128", "ITF", "RSS_14", "RSS_EXPANDED", "push", "includes", "QR_CODE", "DATA_MATRIX", "AZTEC", "PDF_417", "reset", "e_1", "_a", "_b", "_c", "reader", "e_1_1", "error", "return", "e_2", "ex", "e_2_1"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/MultiFormatReader.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport DecodeHintType from './DecodeHintType';\nimport BarcodeFormat from './BarcodeFormat';\nimport QRCodeReader from './qrcode/QRCodeReader';\nimport AztecReader from './aztec/AztecReader';\nimport MultiFormatOneDReader from './oned/MultiFormatOneDReader';\nimport DataMatrixReader from './datamatrix/DataMatrixReader';\nimport NotFoundException from './NotFoundException';\nimport PDF417Reader from './pdf417/PDF417Reader';\nimport ReaderException from './ReaderException';\n/*namespace com.google.zxing {*/\n/**\n * MultiFormatReader is a convenience class and the main entry point into the library for most uses.\n * By default it attempts to decode all barcode formats that the library supports. Optionally, you\n * can provide a hints object to request different behavior, for example only decoding QR codes.\n *\n * <AUTHOR> Owen\n * <AUTHOR> (Daniel Switkin)\n */\nvar MultiFormatReader = /** @class */ (function () {\n    function MultiFormatReader() {\n    }\n    /**\n     * This version of decode honors the intent of Reader.decode(BinaryBitmap) in that it\n     * passes null as a hint to the decoders. However, that makes it inefficient to call repeatedly.\n     * Use setHints() followed by decodeWithState() for continuous scan applications.\n     *\n     * @param image The pixel data to decode\n     * @return The contents of the image\n     *\n     * @throws NotFoundException Any errors which occurred\n     */\n    /*@Override*/\n    // public decode(image: BinaryBitmap): Result {\n    //   setHints(null)\n    //   return decodeInternal(image)\n    // }\n    /**\n     * Decode an image using the hints provided. Does not honor existing state.\n     *\n     * @param image The pixel data to decode\n     * @param hints The hints to use, clearing the previous state.\n     * @return The contents of the image\n     *\n     * @throws NotFoundException Any errors which occurred\n     */\n    /*@Override*/\n    MultiFormatReader.prototype.decode = function (image, hints) {\n        this.setHints(hints);\n        return this.decodeInternal(image);\n    };\n    /**\n     * Decode an image using the state set up by calling setHints() previously. Continuous scan\n     * clients will get a <b>large</b> speed increase by using this instead of decode().\n     *\n     * @param image The pixel data to decode\n     * @return The contents of the image\n     *\n     * @throws NotFoundException Any errors which occurred\n     */\n    MultiFormatReader.prototype.decodeWithState = function (image) {\n        // Make sure to set up the default state so we don't crash\n        if (this.readers === null || this.readers === undefined) {\n            this.setHints(null);\n        }\n        return this.decodeInternal(image);\n    };\n    /**\n     * This method adds state to the MultiFormatReader. By setting the hints once, subsequent calls\n     * to decodeWithState(image) can reuse the same set of readers without reallocating memory. This\n     * is important for performance in continuous scan clients.\n     *\n     * @param hints The set of hints to use for subsequent calls to decode(image)\n     */\n    MultiFormatReader.prototype.setHints = function (hints) {\n        this.hints = hints;\n        var tryHarder = hints !== null && hints !== undefined && undefined !== hints.get(DecodeHintType.TRY_HARDER);\n        /*@SuppressWarnings(\"unchecked\")*/\n        var formats = hints === null || hints === undefined ? null : hints.get(DecodeHintType.POSSIBLE_FORMATS);\n        var readers = new Array();\n        if (formats !== null && formats !== undefined) {\n            var addOneDReader = formats.some(function (f) {\n                return f === BarcodeFormat.UPC_A ||\n                    f === BarcodeFormat.UPC_E ||\n                    f === BarcodeFormat.EAN_13 ||\n                    f === BarcodeFormat.EAN_8 ||\n                    f === BarcodeFormat.CODABAR ||\n                    f === BarcodeFormat.CODE_39 ||\n                    f === BarcodeFormat.CODE_93 ||\n                    f === BarcodeFormat.CODE_128 ||\n                    f === BarcodeFormat.ITF ||\n                    f === BarcodeFormat.RSS_14 ||\n                    f === BarcodeFormat.RSS_EXPANDED;\n            });\n            // Put 1D readers upfront in \"normal\" mode\n            // TYPESCRIPTPORT: TODO: uncomment below as they are ported\n            if (addOneDReader && !tryHarder) {\n                readers.push(new MultiFormatOneDReader(hints));\n            }\n            if (formats.includes(BarcodeFormat.QR_CODE)) {\n                readers.push(new QRCodeReader());\n            }\n            if (formats.includes(BarcodeFormat.DATA_MATRIX)) {\n                readers.push(new DataMatrixReader());\n            }\n            if (formats.includes(BarcodeFormat.AZTEC)) {\n                readers.push(new AztecReader());\n            }\n            if (formats.includes(BarcodeFormat.PDF_417)) {\n                readers.push(new PDF417Reader());\n            }\n            // if (formats.includes(BarcodeFormat.MAXICODE)) {\n            //    readers.push(new MaxiCodeReader())\n            // }\n            // At end in \"try harder\" mode\n            if (addOneDReader && tryHarder) {\n                readers.push(new MultiFormatOneDReader(hints));\n            }\n        }\n        if (readers.length === 0) {\n            if (!tryHarder) {\n                readers.push(new MultiFormatOneDReader(hints));\n            }\n            readers.push(new QRCodeReader());\n            readers.push(new DataMatrixReader());\n            readers.push(new AztecReader());\n            readers.push(new PDF417Reader());\n            // readers.push(new MaxiCodeReader())\n            if (tryHarder) {\n                readers.push(new MultiFormatOneDReader(hints));\n            }\n        }\n        this.readers = readers; // .toArray(new Reader[readers.size()])\n    };\n    /*@Override*/\n    MultiFormatReader.prototype.reset = function () {\n        var e_1, _a;\n        if (this.readers !== null) {\n            try {\n                for (var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var reader = _c.value;\n                    reader.reset();\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n    };\n    /**\n     * @throws NotFoundException\n     */\n    MultiFormatReader.prototype.decodeInternal = function (image) {\n        var e_2, _a;\n        if (this.readers === null) {\n            throw new ReaderException('No readers where selected, nothing can be read.');\n        }\n        try {\n            for (var _b = __values(this.readers), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var reader = _c.value;\n                // Trying to decode with ${reader} reader.\n                try {\n                    return reader.decode(image, this.hints);\n                }\n                catch (ex) {\n                    if (ex instanceof ReaderException) {\n                        continue;\n                    }\n                    // Bad Exception.\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        throw new NotFoundException('No MultiFormat Readers were able to detect the code.');\n    };\n    return MultiFormatReader;\n}());\nexport default MultiFormatReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,qBAAqB,MAAM,8BAA8B;AAChE,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,aAAe,YAAY;EAC/C,SAASA,iBAAiBA,CAAA,EAAG,CAC7B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACAA,iBAAiB,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACzD,IAAI,CAACC,QAAQ,CAACD,KAAK,CAAC;IACpB,OAAO,IAAI,CAACE,cAAc,CAACH,KAAK,CAAC;EACrC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,iBAAiB,CAACC,SAAS,CAACM,eAAe,GAAG,UAAUJ,KAAK,EAAE;IAC3D;IACA,IAAI,IAAI,CAACK,OAAO,KAAK,IAAI,IAAI,IAAI,CAACA,OAAO,KAAKC,SAAS,EAAE;MACrD,IAAI,CAACJ,QAAQ,CAAC,IAAI,CAAC;IACvB;IACA,OAAO,IAAI,CAACC,cAAc,CAACH,KAAK,CAAC;EACrC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIH,iBAAiB,CAACC,SAAS,CAACI,QAAQ,GAAG,UAAUD,KAAK,EAAE;IACpD,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAIM,SAAS,GAAGN,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKK,SAAS,IAAIA,SAAS,KAAKL,KAAK,CAACO,GAAG,CAACpB,cAAc,CAACqB,UAAU,CAAC;IAC3G;IACA,IAAIC,OAAO,GAAGT,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKK,SAAS,GAAG,IAAI,GAAGL,KAAK,CAACO,GAAG,CAACpB,cAAc,CAACuB,gBAAgB,CAAC;IACvG,IAAIN,OAAO,GAAG,IAAIO,KAAK,CAAC,CAAC;IACzB,IAAIF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAKJ,SAAS,EAAE;MAC3C,IAAIO,aAAa,GAAGH,OAAO,CAACI,IAAI,CAAC,UAAUC,CAAC,EAAE;QAC1C,OAAOA,CAAC,KAAK1B,aAAa,CAAC2B,KAAK,IAC5BD,CAAC,KAAK1B,aAAa,CAAC4B,KAAK,IACzBF,CAAC,KAAK1B,aAAa,CAAC6B,MAAM,IAC1BH,CAAC,KAAK1B,aAAa,CAAC8B,KAAK,IACzBJ,CAAC,KAAK1B,aAAa,CAAC+B,OAAO,IAC3BL,CAAC,KAAK1B,aAAa,CAACgC,OAAO,IAC3BN,CAAC,KAAK1B,aAAa,CAACiC,OAAO,IAC3BP,CAAC,KAAK1B,aAAa,CAACkC,QAAQ,IAC5BR,CAAC,KAAK1B,aAAa,CAACmC,GAAG,IACvBT,CAAC,KAAK1B,aAAa,CAACoC,MAAM,IAC1BV,CAAC,KAAK1B,aAAa,CAACqC,YAAY;MACxC,CAAC,CAAC;MACF;MACA;MACA,IAAIb,aAAa,IAAI,CAACN,SAAS,EAAE;QAC7BF,OAAO,CAACsB,IAAI,CAAC,IAAInC,qBAAqB,CAACS,KAAK,CAAC,CAAC;MAClD;MACA,IAAIS,OAAO,CAACkB,QAAQ,CAACvC,aAAa,CAACwC,OAAO,CAAC,EAAE;QACzCxB,OAAO,CAACsB,IAAI,CAAC,IAAIrC,YAAY,CAAC,CAAC,CAAC;MACpC;MACA,IAAIoB,OAAO,CAACkB,QAAQ,CAACvC,aAAa,CAACyC,WAAW,CAAC,EAAE;QAC7CzB,OAAO,CAACsB,IAAI,CAAC,IAAIlC,gBAAgB,CAAC,CAAC,CAAC;MACxC;MACA,IAAIiB,OAAO,CAACkB,QAAQ,CAACvC,aAAa,CAAC0C,KAAK,CAAC,EAAE;QACvC1B,OAAO,CAACsB,IAAI,CAAC,IAAIpC,WAAW,CAAC,CAAC,CAAC;MACnC;MACA,IAAImB,OAAO,CAACkB,QAAQ,CAACvC,aAAa,CAAC2C,OAAO,CAAC,EAAE;QACzC3B,OAAO,CAACsB,IAAI,CAAC,IAAIhC,YAAY,CAAC,CAAC,CAAC;MACpC;MACA;MACA;MACA;MACA;MACA,IAAIkB,aAAa,IAAIN,SAAS,EAAE;QAC5BF,OAAO,CAACsB,IAAI,CAAC,IAAInC,qBAAqB,CAACS,KAAK,CAAC,CAAC;MAClD;IACJ;IACA,IAAII,OAAO,CAACtB,MAAM,KAAK,CAAC,EAAE;MACtB,IAAI,CAACwB,SAAS,EAAE;QACZF,OAAO,CAACsB,IAAI,CAAC,IAAInC,qBAAqB,CAACS,KAAK,CAAC,CAAC;MAClD;MACAI,OAAO,CAACsB,IAAI,CAAC,IAAIrC,YAAY,CAAC,CAAC,CAAC;MAChCe,OAAO,CAACsB,IAAI,CAAC,IAAIlC,gBAAgB,CAAC,CAAC,CAAC;MACpCY,OAAO,CAACsB,IAAI,CAAC,IAAIpC,WAAW,CAAC,CAAC,CAAC;MAC/Bc,OAAO,CAACsB,IAAI,CAAC,IAAIhC,YAAY,CAAC,CAAC,CAAC;MAChC;MACA,IAAIY,SAAS,EAAE;QACXF,OAAO,CAACsB,IAAI,CAAC,IAAInC,qBAAqB,CAACS,KAAK,CAAC,CAAC;MAClD;IACJ;IACA,IAAI,CAACI,OAAO,GAAGA,OAAO,CAAC,CAAC;EAC5B,CAAC;EACD;EACAR,iBAAiB,CAACC,SAAS,CAACmC,KAAK,GAAG,YAAY;IAC5C,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAI,IAAI,CAAC9B,OAAO,KAAK,IAAI,EAAE;MACvB,IAAI;QACA,KAAK,IAAI+B,EAAE,GAAG7D,QAAQ,CAAC,IAAI,CAAC8B,OAAO,CAAC,EAAEgC,EAAE,GAAGD,EAAE,CAACpD,IAAI,CAAC,CAAC,EAAE,CAACqD,EAAE,CAACnD,IAAI,EAAEmD,EAAE,GAAGD,EAAE,CAACpD,IAAI,CAAC,CAAC,EAAE;UAC5E,IAAIsD,MAAM,GAAGD,EAAE,CAACpD,KAAK;UACrBqD,MAAM,CAACL,KAAK,CAAC,CAAC;QAClB;MACJ,CAAC,CACD,OAAOM,KAAK,EAAE;QAAEL,GAAG,GAAG;UAAEM,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,EAAE,IAAI,CAACA,EAAE,CAACnD,IAAI,KAAKiD,EAAE,GAAGC,EAAE,CAACK,MAAM,CAAC,EAAEN,EAAE,CAACrD,IAAI,CAACsD,EAAE,CAAC;QACvD,CAAC,SACO;UAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACM,KAAK;QAAE;MACxC;IACJ;EACJ,CAAC;EACD;AACJ;AACA;EACI3C,iBAAiB,CAACC,SAAS,CAACK,cAAc,GAAG,UAAUH,KAAK,EAAE;IAC1D,IAAI0C,GAAG,EAAEP,EAAE;IACX,IAAI,IAAI,CAAC9B,OAAO,KAAK,IAAI,EAAE;MACvB,MAAM,IAAIT,eAAe,CAAC,iDAAiD,CAAC;IAChF;IACA,IAAI;MACA,KAAK,IAAIwC,EAAE,GAAG7D,QAAQ,CAAC,IAAI,CAAC8B,OAAO,CAAC,EAAEgC,EAAE,GAAGD,EAAE,CAACpD,IAAI,CAAC,CAAC,EAAE,CAACqD,EAAE,CAACnD,IAAI,EAAEmD,EAAE,GAAGD,EAAE,CAACpD,IAAI,CAAC,CAAC,EAAE;QAC5E,IAAIsD,MAAM,GAAGD,EAAE,CAACpD,KAAK;QACrB;QACA,IAAI;UACA,OAAOqD,MAAM,CAACvC,MAAM,CAACC,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;QAC3C,CAAC,CACD,OAAO0C,EAAE,EAAE;UACP,IAAIA,EAAE,YAAY/C,eAAe,EAAE;YAC/B;UACJ;UACA;QACJ;MACJ;IACJ,CAAC,CACD,OAAOgD,KAAK,EAAE;MAAEF,GAAG,GAAG;QAAEF,KAAK,EAAEI;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIP,EAAE,IAAI,CAACA,EAAE,CAACnD,IAAI,KAAKiD,EAAE,GAAGC,EAAE,CAACK,MAAM,CAAC,EAAEN,EAAE,CAACrD,IAAI,CAACsD,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIM,GAAG,EAAE,MAAMA,GAAG,CAACF,KAAK;MAAE;IACxC;IACA,MAAM,IAAI9C,iBAAiB,CAAC,sDAAsD,CAAC;EACvF,CAAC;EACD,OAAOG,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AACJ,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}