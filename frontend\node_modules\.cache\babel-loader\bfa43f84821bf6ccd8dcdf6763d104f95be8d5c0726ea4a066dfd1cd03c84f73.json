{"ast": null, "code": "import BitMatrix from '../../common/BitMatrix';\nimport Version from './Version';\nimport FormatException from '../../FormatException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <AUTHOR> (<PERSON>)\n */\nvar BitMatrixParser = /** @class */function () {\n  /**\n   * @param bitMatrix {@link BitMatrix} to parse\n   * @throws FormatException if dimension is < 8 or > 144 or not 0 mod 2\n   */\n  function BitMatrixParser(bitMatrix) {\n    var dimension = bitMatrix.getHeight();\n    if (dimension < 8 || dimension > 144 || (dimension & 0x01) !== 0) {\n      throw new FormatException();\n    }\n    this.version = BitMatrixParser.readVersion(bitMatrix);\n    this.mappingBitMatrix = this.extractDataRegion(bitMatrix);\n    this.readMappingMatrix = new BitMatrix(this.mappingBitMatrix.getWidth(), this.mappingBitMatrix.getHeight());\n  }\n  BitMatrixParser.prototype.getVersion = function () {\n    return this.version;\n  };\n  /**\n   * <p>Creates the version object based on the dimension of the original bit matrix from\n   * the datamatrix code.</p>\n   *\n   * <p>See ISO 16022:2006 Table 7 - ECC 200 symbol attributes</p>\n   *\n   * @param bitMatrix Original {@link BitMatrix} including alignment patterns\n   * @return {@link Version} encapsulating the Data Matrix Code's \"version\"\n   * @throws FormatException if the dimensions of the mapping matrix are not valid\n   * Data Matrix dimensions.\n   */\n  BitMatrixParser.readVersion = function (bitMatrix) {\n    var numRows = bitMatrix.getHeight();\n    var numColumns = bitMatrix.getWidth();\n    return Version.getVersionForDimensions(numRows, numColumns);\n  };\n  /**\n   * <p>Reads the bits in the {@link BitMatrix} representing the mapping matrix (No alignment patterns)\n   * in the correct order in order to reconstitute the codewords bytes contained within the\n   * Data Matrix Code.</p>\n   *\n   * @return bytes encoded within the Data Matrix Code\n   * @throws FormatException if the exact number of bytes expected is not read\n   */\n  BitMatrixParser.prototype.readCodewords = function () {\n    var result = new Int8Array(this.version.getTotalCodewords());\n    var resultOffset = 0;\n    var row = 4;\n    var column = 0;\n    var numRows = this.mappingBitMatrix.getHeight();\n    var numColumns = this.mappingBitMatrix.getWidth();\n    var corner1Read = false;\n    var corner2Read = false;\n    var corner3Read = false;\n    var corner4Read = false;\n    // Read all of the codewords\n    do {\n      // Check the four corner cases\n      if (row === numRows && column === 0 && !corner1Read) {\n        result[resultOffset++] = this.readCorner1(numRows, numColumns) & 0xff;\n        row -= 2;\n        column += 2;\n        corner1Read = true;\n      } else if (row === numRows - 2 && column === 0 && (numColumns & 0x03) !== 0 && !corner2Read) {\n        result[resultOffset++] = this.readCorner2(numRows, numColumns) & 0xff;\n        row -= 2;\n        column += 2;\n        corner2Read = true;\n      } else if (row === numRows + 4 && column === 2 && (numColumns & 0x07) === 0 && !corner3Read) {\n        result[resultOffset++] = this.readCorner3(numRows, numColumns) & 0xff;\n        row -= 2;\n        column += 2;\n        corner3Read = true;\n      } else if (row === numRows - 2 && column === 0 && (numColumns & 0x07) === 4 && !corner4Read) {\n        result[resultOffset++] = this.readCorner4(numRows, numColumns) & 0xff;\n        row -= 2;\n        column += 2;\n        corner4Read = true;\n      } else {\n        // Sweep upward diagonally to the right\n        do {\n          if (row < numRows && column >= 0 && !this.readMappingMatrix.get(column, row)) {\n            result[resultOffset++] = this.readUtah(row, column, numRows, numColumns) & 0xff;\n          }\n          row -= 2;\n          column += 2;\n        } while (row >= 0 && column < numColumns);\n        row += 1;\n        column += 3;\n        // Sweep downward diagonally to the left\n        do {\n          if (row >= 0 && column < numColumns && !this.readMappingMatrix.get(column, row)) {\n            result[resultOffset++] = this.readUtah(row, column, numRows, numColumns) & 0xff;\n          }\n          row += 2;\n          column -= 2;\n        } while (row < numRows && column >= 0);\n        row += 3;\n        column += 1;\n      }\n    } while (row < numRows || column < numColumns);\n    if (resultOffset !== this.version.getTotalCodewords()) {\n      throw new FormatException();\n    }\n    return result;\n  };\n  /**\n   * <p>Reads a bit of the mapping matrix accounting for boundary wrapping.</p>\n   *\n   * @param row Row to read in the mapping matrix\n   * @param column Column to read in the mapping matrix\n   * @param numRows Number of rows in the mapping matrix\n   * @param numColumns Number of columns in the mapping matrix\n   * @return value of the given bit in the mapping matrix\n   */\n  BitMatrixParser.prototype.readModule = function (row, column, numRows, numColumns) {\n    // Adjust the row and column indices based on boundary wrapping\n    if (row < 0) {\n      row += numRows;\n      column += 4 - (numRows + 4 & 0x07);\n    }\n    if (column < 0) {\n      column += numColumns;\n      row += 4 - (numColumns + 4 & 0x07);\n    }\n    this.readMappingMatrix.set(column, row);\n    return this.mappingBitMatrix.get(column, row);\n  };\n  /**\n   * <p>Reads the 8 bits of the standard Utah-shaped pattern.</p>\n   *\n   * <p>See ISO 16022:2006, 5.8.1 Figure 6</p>\n   *\n   * @param row Current row in the mapping matrix, anchored at the 8th bit (LSB) of the pattern\n   * @param column Current column in the mapping matrix, anchored at the 8th bit (LSB) of the pattern\n   * @param numRows Number of rows in the mapping matrix\n   * @param numColumns Number of columns in the mapping matrix\n   * @return byte from the utah shape\n   */\n  BitMatrixParser.prototype.readUtah = function (row, column, numRows, numColumns) {\n    var currentByte = 0;\n    if (this.readModule(row - 2, column - 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(row - 2, column - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(row - 1, column - 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(row - 1, column - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(row - 1, column, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(row, column - 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(row, column - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(row, column, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    return currentByte;\n  };\n  /**\n   * <p>Reads the 8 bits of the special corner condition 1.</p>\n   *\n   * <p>See ISO 16022:2006, Figure F.3</p>\n   *\n   * @param numRows Number of rows in the mapping matrix\n   * @param numColumns Number of columns in the mapping matrix\n   * @return byte from the Corner condition 1\n   */\n  BitMatrixParser.prototype.readCorner1 = function (numRows, numColumns) {\n    var currentByte = 0;\n    if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(numRows - 1, 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(numRows - 1, 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(2, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(3, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    return currentByte;\n  };\n  /**\n   * <p>Reads the 8 bits of the special corner condition 2.</p>\n   *\n   * <p>See ISO 16022:2006, Figure F.4</p>\n   *\n   * @param numRows Number of rows in the mapping matrix\n   * @param numColumns Number of columns in the mapping matrix\n   * @return byte from the Corner condition 2\n   */\n  BitMatrixParser.prototype.readCorner2 = function (numRows, numColumns) {\n    var currentByte = 0;\n    if (this.readModule(numRows - 3, 0, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(numRows - 2, 0, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 4, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 3, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    return currentByte;\n  };\n  /**\n   * <p>Reads the 8 bits of the special corner condition 3.</p>\n   *\n   * <p>See ISO 16022:2006, Figure F.5</p>\n   *\n   * @param numRows Number of rows in the mapping matrix\n   * @param numColumns Number of columns in the mapping matrix\n   * @return byte from the Corner condition 3\n   */\n  BitMatrixParser.prototype.readCorner3 = function (numRows, numColumns) {\n    var currentByte = 0;\n    if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(numRows - 1, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 3, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(1, numColumns - 3, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(1, numColumns - 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    return currentByte;\n  };\n  /**\n   * <p>Reads the 8 bits of the special corner condition 4.</p>\n   *\n   * <p>See ISO 16022:2006, Figure F.6</p>\n   *\n   * @param numRows Number of rows in the mapping matrix\n   * @param numColumns Number of columns in the mapping matrix\n   * @return byte from the Corner condition 4\n   */\n  BitMatrixParser.prototype.readCorner4 = function (numRows, numColumns) {\n    var currentByte = 0;\n    if (this.readModule(numRows - 3, 0, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(numRows - 2, 0, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(2, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    currentByte <<= 1;\n    if (this.readModule(3, numColumns - 1, numRows, numColumns)) {\n      currentByte |= 1;\n    }\n    return currentByte;\n  };\n  /**\n   * <p>Extracts the data region from a {@link BitMatrix} that contains\n   * alignment patterns.</p>\n   *\n   * @param bitMatrix Original {@link BitMatrix} with alignment patterns\n   * @return BitMatrix that has the alignment patterns removed\n   */\n  BitMatrixParser.prototype.extractDataRegion = function (bitMatrix) {\n    var symbolSizeRows = this.version.getSymbolSizeRows();\n    var symbolSizeColumns = this.version.getSymbolSizeColumns();\n    if (bitMatrix.getHeight() !== symbolSizeRows) {\n      throw new IllegalArgumentException('Dimension of bitMatrix must match the version size');\n    }\n    var dataRegionSizeRows = this.version.getDataRegionSizeRows();\n    var dataRegionSizeColumns = this.version.getDataRegionSizeColumns();\n    var numDataRegionsRow = symbolSizeRows / dataRegionSizeRows | 0;\n    var numDataRegionsColumn = symbolSizeColumns / dataRegionSizeColumns | 0;\n    var sizeDataRegionRow = numDataRegionsRow * dataRegionSizeRows;\n    var sizeDataRegionColumn = numDataRegionsColumn * dataRegionSizeColumns;\n    var bitMatrixWithoutAlignment = new BitMatrix(sizeDataRegionColumn, sizeDataRegionRow);\n    for (var dataRegionRow = 0; dataRegionRow < numDataRegionsRow; ++dataRegionRow) {\n      var dataRegionRowOffset = dataRegionRow * dataRegionSizeRows;\n      for (var dataRegionColumn = 0; dataRegionColumn < numDataRegionsColumn; ++dataRegionColumn) {\n        var dataRegionColumnOffset = dataRegionColumn * dataRegionSizeColumns;\n        for (var i = 0; i < dataRegionSizeRows; ++i) {\n          var readRowOffset = dataRegionRow * (dataRegionSizeRows + 2) + 1 + i;\n          var writeRowOffset = dataRegionRowOffset + i;\n          for (var j = 0; j < dataRegionSizeColumns; ++j) {\n            var readColumnOffset = dataRegionColumn * (dataRegionSizeColumns + 2) + 1 + j;\n            if (bitMatrix.get(readColumnOffset, readRowOffset)) {\n              var writeColumnOffset = dataRegionColumnOffset + j;\n              bitMatrixWithoutAlignment.set(writeColumnOffset, writeRowOffset);\n            }\n          }\n        }\n      }\n    }\n    return bitMatrixWithoutAlignment;\n  };\n  return BitMatrixParser;\n}();\nexport default BitMatrixParser;", "map": {"version": 3, "names": ["BitMatrix", "Version", "FormatException", "IllegalArgumentException", "BitMatrixParser", "bitMatrix", "dimension", "getHeight", "version", "readVersion", "mappingBitMatrix", "extractDataRegion", "readMappingMatrix", "getWidth", "prototype", "getVersion", "numRows", "numColumns", "getVersionForDimensions", "readCodewords", "result", "Int8Array", "getTotalCodewords", "resultOffset", "row", "column", "corner1Read", "corner2Read", "corner3Read", "corner4Read", "readCorner1", "readCorner2", "readCorner3", "readCorner4", "get", "readUtah", "readModule", "set", "currentByte", "symbolSizeRows", "getSymbolSizeRows", "symbolSizeColumns", "getSymbolSizeColumns", "dataRegionSizeRows", "getDataRegionSizeRows", "dataRegionSizeColumns", "getDataRegionSizeColumns", "numDataRegionsRow", "numDataRegionsColumn", "sizeDataRegionRow", "sizeDataRegionColumn", "bitMatrixWithoutAlignment", "dataRegionRow", "dataRegionRowOffset", "dataRegionColumn", "dataRegionColumnOffset", "i", "readRowOffset", "writeRowOffset", "j", "readColumnOffset", "writeColumnOffset"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/decoder/BitMatrixParser.js"], "sourcesContent": ["import BitMatrix from '../../common/BitMatrix';\nimport Version from './Version';\nimport FormatException from '../../FormatException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <AUTHOR> (<PERSON>)\n */\nvar BitMatrixParser = /** @class */ (function () {\n    /**\n     * @param bitMatrix {@link BitMatrix} to parse\n     * @throws FormatException if dimension is < 8 or > 144 or not 0 mod 2\n     */\n    function BitMatrixParser(bitMatrix) {\n        var dimension = bitMatrix.getHeight();\n        if (dimension < 8 || dimension > 144 || (dimension & 0x01) !== 0) {\n            throw new FormatException();\n        }\n        this.version = BitMatrixParser.readVersion(bitMatrix);\n        this.mappingBitMatrix = this.extractDataRegion(bitMatrix);\n        this.readMappingMatrix = new BitMatrix(this.mappingBitMatrix.getWidth(), this.mappingBitMatrix.getHeight());\n    }\n    BitMatrixParser.prototype.getVersion = function () {\n        return this.version;\n    };\n    /**\n     * <p>Creates the version object based on the dimension of the original bit matrix from\n     * the datamatrix code.</p>\n     *\n     * <p>See ISO 16022:2006 Table 7 - ECC 200 symbol attributes</p>\n     *\n     * @param bitMatrix Original {@link BitMatrix} including alignment patterns\n     * @return {@link Version} encapsulating the Data Matrix Code's \"version\"\n     * @throws FormatException if the dimensions of the mapping matrix are not valid\n     * Data Matrix dimensions.\n     */\n    BitMatrixParser.readVersion = function (bitMatrix) {\n        var numRows = bitMatrix.getHeight();\n        var numColumns = bitMatrix.getWidth();\n        return Version.getVersionForDimensions(numRows, numColumns);\n    };\n    /**\n     * <p>Reads the bits in the {@link BitMatrix} representing the mapping matrix (No alignment patterns)\n     * in the correct order in order to reconstitute the codewords bytes contained within the\n     * Data Matrix Code.</p>\n     *\n     * @return bytes encoded within the Data Matrix Code\n     * @throws FormatException if the exact number of bytes expected is not read\n     */\n    BitMatrixParser.prototype.readCodewords = function () {\n        var result = new Int8Array(this.version.getTotalCodewords());\n        var resultOffset = 0;\n        var row = 4;\n        var column = 0;\n        var numRows = this.mappingBitMatrix.getHeight();\n        var numColumns = this.mappingBitMatrix.getWidth();\n        var corner1Read = false;\n        var corner2Read = false;\n        var corner3Read = false;\n        var corner4Read = false;\n        // Read all of the codewords\n        do {\n            // Check the four corner cases\n            if ((row === numRows) && (column === 0) && !corner1Read) {\n                result[resultOffset++] = this.readCorner1(numRows, numColumns) & 0xff;\n                row -= 2;\n                column += 2;\n                corner1Read = true;\n            }\n            else if ((row === numRows - 2) && (column === 0) && ((numColumns & 0x03) !== 0) && !corner2Read) {\n                result[resultOffset++] = this.readCorner2(numRows, numColumns) & 0xff;\n                row -= 2;\n                column += 2;\n                corner2Read = true;\n            }\n            else if ((row === numRows + 4) && (column === 2) && ((numColumns & 0x07) === 0) && !corner3Read) {\n                result[resultOffset++] = this.readCorner3(numRows, numColumns) & 0xff;\n                row -= 2;\n                column += 2;\n                corner3Read = true;\n            }\n            else if ((row === numRows - 2) && (column === 0) && ((numColumns & 0x07) === 4) && !corner4Read) {\n                result[resultOffset++] = this.readCorner4(numRows, numColumns) & 0xff;\n                row -= 2;\n                column += 2;\n                corner4Read = true;\n            }\n            else {\n                // Sweep upward diagonally to the right\n                do {\n                    if ((row < numRows) && (column >= 0) && !this.readMappingMatrix.get(column, row)) {\n                        result[resultOffset++] = this.readUtah(row, column, numRows, numColumns) & 0xff;\n                    }\n                    row -= 2;\n                    column += 2;\n                } while ((row >= 0) && (column < numColumns));\n                row += 1;\n                column += 3;\n                // Sweep downward diagonally to the left\n                do {\n                    if ((row >= 0) && (column < numColumns) && !this.readMappingMatrix.get(column, row)) {\n                        result[resultOffset++] = this.readUtah(row, column, numRows, numColumns) & 0xff;\n                    }\n                    row += 2;\n                    column -= 2;\n                } while ((row < numRows) && (column >= 0));\n                row += 3;\n                column += 1;\n            }\n        } while ((row < numRows) || (column < numColumns));\n        if (resultOffset !== this.version.getTotalCodewords()) {\n            throw new FormatException();\n        }\n        return result;\n    };\n    /**\n     * <p>Reads a bit of the mapping matrix accounting for boundary wrapping.</p>\n     *\n     * @param row Row to read in the mapping matrix\n     * @param column Column to read in the mapping matrix\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return value of the given bit in the mapping matrix\n     */\n    BitMatrixParser.prototype.readModule = function (row, column, numRows, numColumns) {\n        // Adjust the row and column indices based on boundary wrapping\n        if (row < 0) {\n            row += numRows;\n            column += 4 - ((numRows + 4) & 0x07);\n        }\n        if (column < 0) {\n            column += numColumns;\n            row += 4 - ((numColumns + 4) & 0x07);\n        }\n        this.readMappingMatrix.set(column, row);\n        return this.mappingBitMatrix.get(column, row);\n    };\n    /**\n     * <p>Reads the 8 bits of the standard Utah-shaped pattern.</p>\n     *\n     * <p>See ISO 16022:2006, 5.8.1 Figure 6</p>\n     *\n     * @param row Current row in the mapping matrix, anchored at the 8th bit (LSB) of the pattern\n     * @param column Current column in the mapping matrix, anchored at the 8th bit (LSB) of the pattern\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the utah shape\n     */\n    BitMatrixParser.prototype.readUtah = function (row, column, numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(row - 2, column - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row - 2, column - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row - 1, column - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row - 1, column - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row - 1, column, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row, column - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row, column - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(row, column, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Reads the 8 bits of the special corner condition 1.</p>\n     *\n     * <p>See ISO 16022:2006, Figure F.3</p>\n     *\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the Corner condition 1\n     */\n    BitMatrixParser.prototype.readCorner1 = function (numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(2, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(3, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Reads the 8 bits of the special corner condition 2.</p>\n     *\n     * <p>See ISO 16022:2006, Figure F.4</p>\n     *\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the Corner condition 2\n     */\n    BitMatrixParser.prototype.readCorner2 = function (numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(numRows - 3, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 2, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 4, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 3, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Reads the 8 bits of the special corner condition 3.</p>\n     *\n     * <p>See ISO 16022:2006, Figure F.5</p>\n     *\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the Corner condition 3\n     */\n    BitMatrixParser.prototype.readCorner3 = function (numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 3, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 3, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Reads the 8 bits of the special corner condition 4.</p>\n     *\n     * <p>See ISO 16022:2006, Figure F.6</p>\n     *\n     * @param numRows Number of rows in the mapping matrix\n     * @param numColumns Number of columns in the mapping matrix\n     * @return byte from the Corner condition 4\n     */\n    BitMatrixParser.prototype.readCorner4 = function (numRows, numColumns) {\n        var currentByte = 0;\n        if (this.readModule(numRows - 3, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 2, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(numRows - 1, 0, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 2, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(0, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(1, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(2, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        currentByte <<= 1;\n        if (this.readModule(3, numColumns - 1, numRows, numColumns)) {\n            currentByte |= 1;\n        }\n        return currentByte;\n    };\n    /**\n     * <p>Extracts the data region from a {@link BitMatrix} that contains\n     * alignment patterns.</p>\n     *\n     * @param bitMatrix Original {@link BitMatrix} with alignment patterns\n     * @return BitMatrix that has the alignment patterns removed\n     */\n    BitMatrixParser.prototype.extractDataRegion = function (bitMatrix) {\n        var symbolSizeRows = this.version.getSymbolSizeRows();\n        var symbolSizeColumns = this.version.getSymbolSizeColumns();\n        if (bitMatrix.getHeight() !== symbolSizeRows) {\n            throw new IllegalArgumentException('Dimension of bitMatrix must match the version size');\n        }\n        var dataRegionSizeRows = this.version.getDataRegionSizeRows();\n        var dataRegionSizeColumns = this.version.getDataRegionSizeColumns();\n        var numDataRegionsRow = symbolSizeRows / dataRegionSizeRows | 0;\n        var numDataRegionsColumn = symbolSizeColumns / dataRegionSizeColumns | 0;\n        var sizeDataRegionRow = numDataRegionsRow * dataRegionSizeRows;\n        var sizeDataRegionColumn = numDataRegionsColumn * dataRegionSizeColumns;\n        var bitMatrixWithoutAlignment = new BitMatrix(sizeDataRegionColumn, sizeDataRegionRow);\n        for (var dataRegionRow = 0; dataRegionRow < numDataRegionsRow; ++dataRegionRow) {\n            var dataRegionRowOffset = dataRegionRow * dataRegionSizeRows;\n            for (var dataRegionColumn = 0; dataRegionColumn < numDataRegionsColumn; ++dataRegionColumn) {\n                var dataRegionColumnOffset = dataRegionColumn * dataRegionSizeColumns;\n                for (var i = 0; i < dataRegionSizeRows; ++i) {\n                    var readRowOffset = dataRegionRow * (dataRegionSizeRows + 2) + 1 + i;\n                    var writeRowOffset = dataRegionRowOffset + i;\n                    for (var j = 0; j < dataRegionSizeColumns; ++j) {\n                        var readColumnOffset = dataRegionColumn * (dataRegionSizeColumns + 2) + 1 + j;\n                        if (bitMatrix.get(readColumnOffset, readRowOffset)) {\n                            var writeColumnOffset = dataRegionColumnOffset + j;\n                            bitMatrixWithoutAlignment.set(writeColumnOffset, writeRowOffset);\n                        }\n                    }\n                }\n            }\n        }\n        return bitMatrixWithoutAlignment;\n    };\n    return BitMatrixParser;\n}());\nexport default BitMatrixParser;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C;AACJ;AACA;AACA;EACI,SAASA,eAAeA,CAACC,SAAS,EAAE;IAChC,IAAIC,SAAS,GAAGD,SAAS,CAACE,SAAS,CAAC,CAAC;IACrC,IAAID,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAG,GAAG,IAAI,CAACA,SAAS,GAAG,IAAI,MAAM,CAAC,EAAE;MAC9D,MAAM,IAAIJ,eAAe,CAAC,CAAC;IAC/B;IACA,IAAI,CAACM,OAAO,GAAGJ,eAAe,CAACK,WAAW,CAACJ,SAAS,CAAC;IACrD,IAAI,CAACK,gBAAgB,GAAG,IAAI,CAACC,iBAAiB,CAACN,SAAS,CAAC;IACzD,IAAI,CAACO,iBAAiB,GAAG,IAAIZ,SAAS,CAAC,IAAI,CAACU,gBAAgB,CAACG,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACH,gBAAgB,CAACH,SAAS,CAAC,CAAC,CAAC;EAC/G;EACAH,eAAe,CAACU,SAAS,CAACC,UAAU,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACP,OAAO;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIJ,eAAe,CAACK,WAAW,GAAG,UAAUJ,SAAS,EAAE;IAC/C,IAAIW,OAAO,GAAGX,SAAS,CAACE,SAAS,CAAC,CAAC;IACnC,IAAIU,UAAU,GAAGZ,SAAS,CAACQ,QAAQ,CAAC,CAAC;IACrC,OAAOZ,OAAO,CAACiB,uBAAuB,CAACF,OAAO,EAAEC,UAAU,CAAC;EAC/D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIb,eAAe,CAACU,SAAS,CAACK,aAAa,GAAG,YAAY;IAClD,IAAIC,MAAM,GAAG,IAAIC,SAAS,CAAC,IAAI,CAACb,OAAO,CAACc,iBAAiB,CAAC,CAAC,CAAC;IAC5D,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIT,OAAO,GAAG,IAAI,CAACN,gBAAgB,CAACH,SAAS,CAAC,CAAC;IAC/C,IAAIU,UAAU,GAAG,IAAI,CAACP,gBAAgB,CAACG,QAAQ,CAAC,CAAC;IACjD,IAAIa,WAAW,GAAG,KAAK;IACvB,IAAIC,WAAW,GAAG,KAAK;IACvB,IAAIC,WAAW,GAAG,KAAK;IACvB,IAAIC,WAAW,GAAG,KAAK;IACvB;IACA,GAAG;MACC;MACA,IAAKL,GAAG,KAAKR,OAAO,IAAMS,MAAM,KAAK,CAAE,IAAI,CAACC,WAAW,EAAE;QACrDN,MAAM,CAACG,YAAY,EAAE,CAAC,GAAG,IAAI,CAACO,WAAW,CAACd,OAAO,EAAEC,UAAU,CAAC,GAAG,IAAI;QACrEO,GAAG,IAAI,CAAC;QACRC,MAAM,IAAI,CAAC;QACXC,WAAW,GAAG,IAAI;MACtB,CAAC,MACI,IAAKF,GAAG,KAAKR,OAAO,GAAG,CAAC,IAAMS,MAAM,KAAK,CAAE,IAAK,CAACR,UAAU,GAAG,IAAI,MAAM,CAAE,IAAI,CAACU,WAAW,EAAE;QAC7FP,MAAM,CAACG,YAAY,EAAE,CAAC,GAAG,IAAI,CAACQ,WAAW,CAACf,OAAO,EAAEC,UAAU,CAAC,GAAG,IAAI;QACrEO,GAAG,IAAI,CAAC;QACRC,MAAM,IAAI,CAAC;QACXE,WAAW,GAAG,IAAI;MACtB,CAAC,MACI,IAAKH,GAAG,KAAKR,OAAO,GAAG,CAAC,IAAMS,MAAM,KAAK,CAAE,IAAK,CAACR,UAAU,GAAG,IAAI,MAAM,CAAE,IAAI,CAACW,WAAW,EAAE;QAC7FR,MAAM,CAACG,YAAY,EAAE,CAAC,GAAG,IAAI,CAACS,WAAW,CAAChB,OAAO,EAAEC,UAAU,CAAC,GAAG,IAAI;QACrEO,GAAG,IAAI,CAAC;QACRC,MAAM,IAAI,CAAC;QACXG,WAAW,GAAG,IAAI;MACtB,CAAC,MACI,IAAKJ,GAAG,KAAKR,OAAO,GAAG,CAAC,IAAMS,MAAM,KAAK,CAAE,IAAK,CAACR,UAAU,GAAG,IAAI,MAAM,CAAE,IAAI,CAACY,WAAW,EAAE;QAC7FT,MAAM,CAACG,YAAY,EAAE,CAAC,GAAG,IAAI,CAACU,WAAW,CAACjB,OAAO,EAAEC,UAAU,CAAC,GAAG,IAAI;QACrEO,GAAG,IAAI,CAAC;QACRC,MAAM,IAAI,CAAC;QACXI,WAAW,GAAG,IAAI;MACtB,CAAC,MACI;QACD;QACA,GAAG;UACC,IAAKL,GAAG,GAAGR,OAAO,IAAMS,MAAM,IAAI,CAAE,IAAI,CAAC,IAAI,CAACb,iBAAiB,CAACsB,GAAG,CAACT,MAAM,EAAED,GAAG,CAAC,EAAE;YAC9EJ,MAAM,CAACG,YAAY,EAAE,CAAC,GAAG,IAAI,CAACY,QAAQ,CAACX,GAAG,EAAEC,MAAM,EAAET,OAAO,EAAEC,UAAU,CAAC,GAAG,IAAI;UACnF;UACAO,GAAG,IAAI,CAAC;UACRC,MAAM,IAAI,CAAC;QACf,CAAC,QAASD,GAAG,IAAI,CAAC,IAAMC,MAAM,GAAGR,UAAW;QAC5CO,GAAG,IAAI,CAAC;QACRC,MAAM,IAAI,CAAC;QACX;QACA,GAAG;UACC,IAAKD,GAAG,IAAI,CAAC,IAAMC,MAAM,GAAGR,UAAW,IAAI,CAAC,IAAI,CAACL,iBAAiB,CAACsB,GAAG,CAACT,MAAM,EAAED,GAAG,CAAC,EAAE;YACjFJ,MAAM,CAACG,YAAY,EAAE,CAAC,GAAG,IAAI,CAACY,QAAQ,CAACX,GAAG,EAAEC,MAAM,EAAET,OAAO,EAAEC,UAAU,CAAC,GAAG,IAAI;UACnF;UACAO,GAAG,IAAI,CAAC;UACRC,MAAM,IAAI,CAAC;QACf,CAAC,QAASD,GAAG,GAAGR,OAAO,IAAMS,MAAM,IAAI,CAAE;QACzCD,GAAG,IAAI,CAAC;QACRC,MAAM,IAAI,CAAC;MACf;IACJ,CAAC,QAASD,GAAG,GAAGR,OAAO,IAAMS,MAAM,GAAGR,UAAW;IACjD,IAAIM,YAAY,KAAK,IAAI,CAACf,OAAO,CAACc,iBAAiB,CAAC,CAAC,EAAE;MACnD,MAAM,IAAIpB,eAAe,CAAC,CAAC;IAC/B;IACA,OAAOkB,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhB,eAAe,CAACU,SAAS,CAACsB,UAAU,GAAG,UAAUZ,GAAG,EAAEC,MAAM,EAAET,OAAO,EAAEC,UAAU,EAAE;IAC/E;IACA,IAAIO,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,IAAIR,OAAO;MACdS,MAAM,IAAI,CAAC,IAAKT,OAAO,GAAG,CAAC,GAAI,IAAI,CAAC;IACxC;IACA,IAAIS,MAAM,GAAG,CAAC,EAAE;MACZA,MAAM,IAAIR,UAAU;MACpBO,GAAG,IAAI,CAAC,IAAKP,UAAU,GAAG,CAAC,GAAI,IAAI,CAAC;IACxC;IACA,IAAI,CAACL,iBAAiB,CAACyB,GAAG,CAACZ,MAAM,EAAED,GAAG,CAAC;IACvC,OAAO,IAAI,CAACd,gBAAgB,CAACwB,GAAG,CAACT,MAAM,EAAED,GAAG,CAAC;EACjD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpB,eAAe,CAACU,SAAS,CAACqB,QAAQ,GAAG,UAAUX,GAAG,EAAEC,MAAM,EAAET,OAAO,EAAEC,UAAU,EAAE;IAC7E,IAAIqB,WAAW,GAAG,CAAC;IACnB,IAAI,IAAI,CAACF,UAAU,CAACZ,GAAG,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAET,OAAO,EAAEC,UAAU,CAAC,EAAE;MAC3DqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACZ,GAAG,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAET,OAAO,EAAEC,UAAU,CAAC,EAAE;MAC3DqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACZ,GAAG,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAET,OAAO,EAAEC,UAAU,CAAC,EAAE;MAC3DqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACZ,GAAG,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAET,OAAO,EAAEC,UAAU,CAAC,EAAE;MAC3DqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACZ,GAAG,GAAG,CAAC,EAAEC,MAAM,EAAET,OAAO,EAAEC,UAAU,CAAC,EAAE;MACvDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACZ,GAAG,EAAEC,MAAM,GAAG,CAAC,EAAET,OAAO,EAAEC,UAAU,CAAC,EAAE;MACvDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACZ,GAAG,EAAEC,MAAM,GAAG,CAAC,EAAET,OAAO,EAAEC,UAAU,CAAC,EAAE;MACvDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACZ,GAAG,EAAEC,MAAM,EAAET,OAAO,EAAEC,UAAU,CAAC,EAAE;MACnDqB,WAAW,IAAI,CAAC;IACpB;IACA,OAAOA,WAAW;EACtB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlC,eAAe,CAACU,SAAS,CAACgB,WAAW,GAAG,UAAUd,OAAO,EAAEC,UAAU,EAAE;IACnE,IAAIqB,WAAW,GAAG,CAAC;IACnB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACA,OAAOA,WAAW;EACtB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlC,eAAe,CAACU,SAAS,CAACiB,WAAW,GAAG,UAAUf,OAAO,EAAEC,UAAU,EAAE;IACnE,IAAIqB,WAAW,GAAG,CAAC;IACnB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACA,OAAOA,WAAW;EACtB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlC,eAAe,CAACU,SAAS,CAACkB,WAAW,GAAG,UAAUhB,OAAO,EAAEC,UAAU,EAAE;IACnE,IAAIqB,WAAW,GAAG,CAAC;IACnB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAEC,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACnEqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACA,OAAOA,WAAW;EACtB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlC,eAAe,CAACU,SAAS,CAACmB,WAAW,GAAG,UAAUjB,OAAO,EAAEC,UAAU,EAAE;IACnE,IAAIqB,WAAW,GAAG,CAAC;IACnB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAACpB,OAAO,GAAG,CAAC,EAAE,CAAC,EAAEA,OAAO,EAAEC,UAAU,CAAC,EAAE;MACtDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACAA,WAAW,KAAK,CAAC;IACjB,IAAI,IAAI,CAACF,UAAU,CAAC,CAAC,EAAEnB,UAAU,GAAG,CAAC,EAAED,OAAO,EAAEC,UAAU,CAAC,EAAE;MACzDqB,WAAW,IAAI,CAAC;IACpB;IACA,OAAOA,WAAW;EACtB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlC,eAAe,CAACU,SAAS,CAACH,iBAAiB,GAAG,UAAUN,SAAS,EAAE;IAC/D,IAAIkC,cAAc,GAAG,IAAI,CAAC/B,OAAO,CAACgC,iBAAiB,CAAC,CAAC;IACrD,IAAIC,iBAAiB,GAAG,IAAI,CAACjC,OAAO,CAACkC,oBAAoB,CAAC,CAAC;IAC3D,IAAIrC,SAAS,CAACE,SAAS,CAAC,CAAC,KAAKgC,cAAc,EAAE;MAC1C,MAAM,IAAIpC,wBAAwB,CAAC,oDAAoD,CAAC;IAC5F;IACA,IAAIwC,kBAAkB,GAAG,IAAI,CAACnC,OAAO,CAACoC,qBAAqB,CAAC,CAAC;IAC7D,IAAIC,qBAAqB,GAAG,IAAI,CAACrC,OAAO,CAACsC,wBAAwB,CAAC,CAAC;IACnE,IAAIC,iBAAiB,GAAGR,cAAc,GAAGI,kBAAkB,GAAG,CAAC;IAC/D,IAAIK,oBAAoB,GAAGP,iBAAiB,GAAGI,qBAAqB,GAAG,CAAC;IACxE,IAAII,iBAAiB,GAAGF,iBAAiB,GAAGJ,kBAAkB;IAC9D,IAAIO,oBAAoB,GAAGF,oBAAoB,GAAGH,qBAAqB;IACvE,IAAIM,yBAAyB,GAAG,IAAInD,SAAS,CAACkD,oBAAoB,EAAED,iBAAiB,CAAC;IACtF,KAAK,IAAIG,aAAa,GAAG,CAAC,EAAEA,aAAa,GAAGL,iBAAiB,EAAE,EAAEK,aAAa,EAAE;MAC5E,IAAIC,mBAAmB,GAAGD,aAAa,GAAGT,kBAAkB;MAC5D,KAAK,IAAIW,gBAAgB,GAAG,CAAC,EAAEA,gBAAgB,GAAGN,oBAAoB,EAAE,EAAEM,gBAAgB,EAAE;QACxF,IAAIC,sBAAsB,GAAGD,gBAAgB,GAAGT,qBAAqB;QACrE,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,kBAAkB,EAAE,EAAEa,CAAC,EAAE;UACzC,IAAIC,aAAa,GAAGL,aAAa,IAAIT,kBAAkB,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGa,CAAC;UACpE,IAAIE,cAAc,GAAGL,mBAAmB,GAAGG,CAAC;UAC5C,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,qBAAqB,EAAE,EAAEc,CAAC,EAAE;YAC5C,IAAIC,gBAAgB,GAAGN,gBAAgB,IAAIT,qBAAqB,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGc,CAAC;YAC7E,IAAItD,SAAS,CAAC6B,GAAG,CAAC0B,gBAAgB,EAAEH,aAAa,CAAC,EAAE;cAChD,IAAII,iBAAiB,GAAGN,sBAAsB,GAAGI,CAAC;cAClDR,yBAAyB,CAACd,GAAG,CAACwB,iBAAiB,EAAEH,cAAc,CAAC;YACpE;UACJ;QACJ;MACJ;IACJ;IACA,OAAOP,yBAAyB;EACpC,CAAC;EACD,OAAO/C,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}