{"ast": null, "code": "/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.detector {*/\nimport ResultPoint from '../../ResultPoint';\nimport MathUtils from './MathUtils';\nimport NotFoundException from '../../NotFoundException';\n/**\n * <p>\n * Detects a candidate barcode-like rectangular region within an image. It\n * starts around the center of the image, increases the size of the candidate\n * region until it finds a white rectangular region. By keeping track of the\n * last black points it encountered, it determines the corners of the barcode.\n * </p>\n *\n * <AUTHOR>\n */\nvar WhiteRectangleDetector = /** @class */function () {\n  // public constructor(private image: BitMatrix) /*throws NotFoundException*/ {\n  //   this(image, INIT_SIZE, image.getWidth() / 2, image.getHeight() / 2)\n  // }\n  /**\n   * @param image barcode image to find a rectangle in\n   * @param initSize initial size of search area around center\n   * @param x x position of search center\n   * @param y y position of search center\n   * @throws NotFoundException if image is too small to accommodate {@code initSize}\n   */\n  function WhiteRectangleDetector(image, initSize /*int*/, x /*int*/, y /*int*/) {\n    this.image = image;\n    this.height = image.getHeight();\n    this.width = image.getWidth();\n    if (undefined === initSize || null === initSize) {\n      initSize = WhiteRectangleDetector.INIT_SIZE;\n    }\n    if (undefined === x || null === x) {\n      x = image.getWidth() / 2 | 0;\n    }\n    if (undefined === y || null === y) {\n      y = image.getHeight() / 2 | 0;\n    }\n    var halfsize = initSize / 2 | 0;\n    this.leftInit = x - halfsize;\n    this.rightInit = x + halfsize;\n    this.upInit = y - halfsize;\n    this.downInit = y + halfsize;\n    if (this.upInit < 0 || this.leftInit < 0 || this.downInit >= this.height || this.rightInit >= this.width) {\n      throw new NotFoundException();\n    }\n  }\n  /**\n   * <p>\n   * Detects a candidate barcode-like rectangular region within an image. It\n   * starts around the center of the image, increases the size of the candidate\n   * region until it finds a white rectangular region.\n   * </p>\n   *\n   * @return {@link ResultPoint}[] describing the corners of the rectangular\n   *         region. The first and last points are opposed on the diagonal, as\n   *         are the second and third. The first point will be the topmost\n   *         point and the last, the bottommost. The second point will be\n   *         leftmost and the third, the rightmost\n   * @throws NotFoundException if no Data Matrix Code can be found\n   */\n  WhiteRectangleDetector.prototype.detect = function () {\n    var left = this.leftInit;\n    var right = this.rightInit;\n    var up = this.upInit;\n    var down = this.downInit;\n    var sizeExceeded = false;\n    var aBlackPointFoundOnBorder = true;\n    var atLeastOneBlackPointFoundOnBorder = false;\n    var atLeastOneBlackPointFoundOnRight = false;\n    var atLeastOneBlackPointFoundOnBottom = false;\n    var atLeastOneBlackPointFoundOnLeft = false;\n    var atLeastOneBlackPointFoundOnTop = false;\n    var width = this.width;\n    var height = this.height;\n    while (aBlackPointFoundOnBorder) {\n      aBlackPointFoundOnBorder = false;\n      // .....\n      // .   |\n      // .....\n      var rightBorderNotWhite = true;\n      while ((rightBorderNotWhite || !atLeastOneBlackPointFoundOnRight) && right < width) {\n        rightBorderNotWhite = this.containsBlackPoint(up, down, right, false);\n        if (rightBorderNotWhite) {\n          right++;\n          aBlackPointFoundOnBorder = true;\n          atLeastOneBlackPointFoundOnRight = true;\n        } else if (!atLeastOneBlackPointFoundOnRight) {\n          right++;\n        }\n      }\n      if (right >= width) {\n        sizeExceeded = true;\n        break;\n      }\n      // .....\n      // .   .\n      // .___.\n      var bottomBorderNotWhite = true;\n      while ((bottomBorderNotWhite || !atLeastOneBlackPointFoundOnBottom) && down < height) {\n        bottomBorderNotWhite = this.containsBlackPoint(left, right, down, true);\n        if (bottomBorderNotWhite) {\n          down++;\n          aBlackPointFoundOnBorder = true;\n          atLeastOneBlackPointFoundOnBottom = true;\n        } else if (!atLeastOneBlackPointFoundOnBottom) {\n          down++;\n        }\n      }\n      if (down >= height) {\n        sizeExceeded = true;\n        break;\n      }\n      // .....\n      // |   .\n      // .....\n      var leftBorderNotWhite = true;\n      while ((leftBorderNotWhite || !atLeastOneBlackPointFoundOnLeft) && left >= 0) {\n        leftBorderNotWhite = this.containsBlackPoint(up, down, left, false);\n        if (leftBorderNotWhite) {\n          left--;\n          aBlackPointFoundOnBorder = true;\n          atLeastOneBlackPointFoundOnLeft = true;\n        } else if (!atLeastOneBlackPointFoundOnLeft) {\n          left--;\n        }\n      }\n      if (left < 0) {\n        sizeExceeded = true;\n        break;\n      }\n      // .___.\n      // .   .\n      // .....\n      var topBorderNotWhite = true;\n      while ((topBorderNotWhite || !atLeastOneBlackPointFoundOnTop) && up >= 0) {\n        topBorderNotWhite = this.containsBlackPoint(left, right, up, true);\n        if (topBorderNotWhite) {\n          up--;\n          aBlackPointFoundOnBorder = true;\n          atLeastOneBlackPointFoundOnTop = true;\n        } else if (!atLeastOneBlackPointFoundOnTop) {\n          up--;\n        }\n      }\n      if (up < 0) {\n        sizeExceeded = true;\n        break;\n      }\n      if (aBlackPointFoundOnBorder) {\n        atLeastOneBlackPointFoundOnBorder = true;\n      }\n    }\n    if (!sizeExceeded && atLeastOneBlackPointFoundOnBorder) {\n      var maxSize = right - left;\n      var z = null;\n      for (var i = 1; z === null && i < maxSize; i++) {\n        z = this.getBlackPointOnSegment(left, down - i, left + i, down);\n      }\n      if (z == null) {\n        throw new NotFoundException();\n      }\n      var t = null;\n      // go down right\n      for (var i = 1; t === null && i < maxSize; i++) {\n        t = this.getBlackPointOnSegment(left, up + i, left + i, up);\n      }\n      if (t == null) {\n        throw new NotFoundException();\n      }\n      var x = null;\n      // go down left\n      for (var i = 1; x === null && i < maxSize; i++) {\n        x = this.getBlackPointOnSegment(right, up + i, right - i, up);\n      }\n      if (x == null) {\n        throw new NotFoundException();\n      }\n      var y = null;\n      // go up left\n      for (var i = 1; y === null && i < maxSize; i++) {\n        y = this.getBlackPointOnSegment(right, down - i, right - i, down);\n      }\n      if (y == null) {\n        throw new NotFoundException();\n      }\n      return this.centerEdges(y, z, x, t);\n    } else {\n      throw new NotFoundException();\n    }\n  };\n  WhiteRectangleDetector.prototype.getBlackPointOnSegment = function (aX /*float*/, aY /*float*/, bX /*float*/, bY /*float*/) {\n    var dist = MathUtils.round(MathUtils.distance(aX, aY, bX, bY));\n    var xStep = (bX - aX) / dist;\n    var yStep = (bY - aY) / dist;\n    var image = this.image;\n    for (var i = 0; i < dist; i++) {\n      var x = MathUtils.round(aX + i * xStep);\n      var y = MathUtils.round(aY + i * yStep);\n      if (image.get(x, y)) {\n        return new ResultPoint(x, y);\n      }\n    }\n    return null;\n  };\n  /**\n   * recenters the points of a constant distance towards the center\n   *\n   * @param y bottom most point\n   * @param z left most point\n   * @param x right most point\n   * @param t top most point\n   * @return {@link ResultPoint}[] describing the corners of the rectangular\n   *         region. The first and last points are opposed on the diagonal, as\n   *         are the second and third. The first point will be the topmost\n   *         point and the last, the bottommost. The second point will be\n   *         leftmost and the third, the rightmost\n   */\n  WhiteRectangleDetector.prototype.centerEdges = function (y, z, x, t) {\n    //\n    //       t            t\n    //  z                      x\n    //        x    OR    z\n    //   y                    y\n    //\n    var yi = y.getX();\n    var yj = y.getY();\n    var zi = z.getX();\n    var zj = z.getY();\n    var xi = x.getX();\n    var xj = x.getY();\n    var ti = t.getX();\n    var tj = t.getY();\n    var CORR = WhiteRectangleDetector.CORR;\n    if (yi < this.width / 2.0) {\n      return [new ResultPoint(ti - CORR, tj + CORR), new ResultPoint(zi + CORR, zj + CORR), new ResultPoint(xi - CORR, xj - CORR), new ResultPoint(yi + CORR, yj - CORR)];\n    } else {\n      return [new ResultPoint(ti + CORR, tj + CORR), new ResultPoint(zi + CORR, zj - CORR), new ResultPoint(xi - CORR, xj + CORR), new ResultPoint(yi - CORR, yj - CORR)];\n    }\n  };\n  /**\n   * Determines whether a segment contains a black point\n   *\n   * @param a          min value of the scanned coordinate\n   * @param b          max value of the scanned coordinate\n   * @param fixed      value of fixed coordinate\n   * @param horizontal set to true if scan must be horizontal, false if vertical\n   * @return true if a black point has been found, else false.\n   */\n  WhiteRectangleDetector.prototype.containsBlackPoint = function (a /*int*/, b /*int*/, fixed /*int*/, horizontal) {\n    var image = this.image;\n    if (horizontal) {\n      for (var x = a; x <= b; x++) {\n        if (image.get(x, fixed)) {\n          return true;\n        }\n      }\n    } else {\n      for (var y = a; y <= b; y++) {\n        if (image.get(fixed, y)) {\n          return true;\n        }\n      }\n    }\n    return false;\n  };\n  WhiteRectangleDetector.INIT_SIZE = 10;\n  WhiteRectangleDetector.CORR = 1;\n  return WhiteRectangleDetector;\n}();\nexport default WhiteRectangleDetector;", "map": {"version": 3, "names": ["ResultPoint", "MathUtils", "NotFoundException", "WhiteRectangleDetector", "image", "initSize", "x", "y", "height", "getHeight", "width", "getWidth", "undefined", "INIT_SIZE", "halfsize", "leftInit", "rightInit", "upInit", "downInit", "prototype", "detect", "left", "right", "up", "down", "sizeExceeded", "aBlackPointFoundOnBorder", "atLeastOneBlackPointFoundOnBorder", "atLeastOneBlackPointFoundOnRight", "atLeastOneBlackPointFoundOnBottom", "atLeastOneBlackPointFoundOnLeft", "atLeastOneBlackPointFoundOnTop", "rightBorderNotWhite", "containsBlackPoint", "bottomBorderNotWhite", "leftBorderNotWhite", "topBorderNotWhite", "maxSize", "z", "i", "getBlackPointOnSegment", "t", "centerEdges", "aX", "aY", "bX", "bY", "dist", "round", "distance", "xStep", "yStep", "get", "yi", "getX", "yj", "getY", "zi", "zj", "xi", "xj", "ti", "tj", "CORR", "a", "b", "fixed", "horizontal"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/detector/WhiteRectangleDetector.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.detector {*/\nimport ResultPoint from '../../ResultPoint';\nimport MathUtils from './MathUtils';\nimport NotFoundException from '../../NotFoundException';\n/**\n * <p>\n * Detects a candidate barcode-like rectangular region within an image. It\n * starts around the center of the image, increases the size of the candidate\n * region until it finds a white rectangular region. By keeping track of the\n * last black points it encountered, it determines the corners of the barcode.\n * </p>\n *\n * <AUTHOR>\n */\nvar WhiteRectangleDetector = /** @class */ (function () {\n    // public constructor(private image: BitMatrix) /*throws NotFoundException*/ {\n    //   this(image, INIT_SIZE, image.getWidth() / 2, image.getHeight() / 2)\n    // }\n    /**\n     * @param image barcode image to find a rectangle in\n     * @param initSize initial size of search area around center\n     * @param x x position of search center\n     * @param y y position of search center\n     * @throws NotFoundException if image is too small to accommodate {@code initSize}\n     */\n    function WhiteRectangleDetector(image, initSize /*int*/, x /*int*/, y /*int*/) {\n        this.image = image;\n        this.height = image.getHeight();\n        this.width = image.getWidth();\n        if (undefined === initSize || null === initSize) {\n            initSize = WhiteRectangleDetector.INIT_SIZE;\n        }\n        if (undefined === x || null === x) {\n            x = image.getWidth() / 2 | 0;\n        }\n        if (undefined === y || null === y) {\n            y = image.getHeight() / 2 | 0;\n        }\n        var halfsize = initSize / 2 | 0;\n        this.leftInit = x - halfsize;\n        this.rightInit = x + halfsize;\n        this.upInit = y - halfsize;\n        this.downInit = y + halfsize;\n        if (this.upInit < 0 || this.leftInit < 0 || this.downInit >= this.height || this.rightInit >= this.width) {\n            throw new NotFoundException();\n        }\n    }\n    /**\n     * <p>\n     * Detects a candidate barcode-like rectangular region within an image. It\n     * starts around the center of the image, increases the size of the candidate\n     * region until it finds a white rectangular region.\n     * </p>\n     *\n     * @return {@link ResultPoint}[] describing the corners of the rectangular\n     *         region. The first and last points are opposed on the diagonal, as\n     *         are the second and third. The first point will be the topmost\n     *         point and the last, the bottommost. The second point will be\n     *         leftmost and the third, the rightmost\n     * @throws NotFoundException if no Data Matrix Code can be found\n     */\n    WhiteRectangleDetector.prototype.detect = function () {\n        var left = this.leftInit;\n        var right = this.rightInit;\n        var up = this.upInit;\n        var down = this.downInit;\n        var sizeExceeded = false;\n        var aBlackPointFoundOnBorder = true;\n        var atLeastOneBlackPointFoundOnBorder = false;\n        var atLeastOneBlackPointFoundOnRight = false;\n        var atLeastOneBlackPointFoundOnBottom = false;\n        var atLeastOneBlackPointFoundOnLeft = false;\n        var atLeastOneBlackPointFoundOnTop = false;\n        var width = this.width;\n        var height = this.height;\n        while (aBlackPointFoundOnBorder) {\n            aBlackPointFoundOnBorder = false;\n            // .....\n            // .   |\n            // .....\n            var rightBorderNotWhite = true;\n            while ((rightBorderNotWhite || !atLeastOneBlackPointFoundOnRight) && right < width) {\n                rightBorderNotWhite = this.containsBlackPoint(up, down, right, false);\n                if (rightBorderNotWhite) {\n                    right++;\n                    aBlackPointFoundOnBorder = true;\n                    atLeastOneBlackPointFoundOnRight = true;\n                }\n                else if (!atLeastOneBlackPointFoundOnRight) {\n                    right++;\n                }\n            }\n            if (right >= width) {\n                sizeExceeded = true;\n                break;\n            }\n            // .....\n            // .   .\n            // .___.\n            var bottomBorderNotWhite = true;\n            while ((bottomBorderNotWhite || !atLeastOneBlackPointFoundOnBottom) && down < height) {\n                bottomBorderNotWhite = this.containsBlackPoint(left, right, down, true);\n                if (bottomBorderNotWhite) {\n                    down++;\n                    aBlackPointFoundOnBorder = true;\n                    atLeastOneBlackPointFoundOnBottom = true;\n                }\n                else if (!atLeastOneBlackPointFoundOnBottom) {\n                    down++;\n                }\n            }\n            if (down >= height) {\n                sizeExceeded = true;\n                break;\n            }\n            // .....\n            // |   .\n            // .....\n            var leftBorderNotWhite = true;\n            while ((leftBorderNotWhite || !atLeastOneBlackPointFoundOnLeft) && left >= 0) {\n                leftBorderNotWhite = this.containsBlackPoint(up, down, left, false);\n                if (leftBorderNotWhite) {\n                    left--;\n                    aBlackPointFoundOnBorder = true;\n                    atLeastOneBlackPointFoundOnLeft = true;\n                }\n                else if (!atLeastOneBlackPointFoundOnLeft) {\n                    left--;\n                }\n            }\n            if (left < 0) {\n                sizeExceeded = true;\n                break;\n            }\n            // .___.\n            // .   .\n            // .....\n            var topBorderNotWhite = true;\n            while ((topBorderNotWhite || !atLeastOneBlackPointFoundOnTop) && up >= 0) {\n                topBorderNotWhite = this.containsBlackPoint(left, right, up, true);\n                if (topBorderNotWhite) {\n                    up--;\n                    aBlackPointFoundOnBorder = true;\n                    atLeastOneBlackPointFoundOnTop = true;\n                }\n                else if (!atLeastOneBlackPointFoundOnTop) {\n                    up--;\n                }\n            }\n            if (up < 0) {\n                sizeExceeded = true;\n                break;\n            }\n            if (aBlackPointFoundOnBorder) {\n                atLeastOneBlackPointFoundOnBorder = true;\n            }\n        }\n        if (!sizeExceeded && atLeastOneBlackPointFoundOnBorder) {\n            var maxSize = right - left;\n            var z = null;\n            for (var i = 1; z === null && i < maxSize; i++) {\n                z = this.getBlackPointOnSegment(left, down - i, left + i, down);\n            }\n            if (z == null) {\n                throw new NotFoundException();\n            }\n            var t = null;\n            // go down right\n            for (var i = 1; t === null && i < maxSize; i++) {\n                t = this.getBlackPointOnSegment(left, up + i, left + i, up);\n            }\n            if (t == null) {\n                throw new NotFoundException();\n            }\n            var x = null;\n            // go down left\n            for (var i = 1; x === null && i < maxSize; i++) {\n                x = this.getBlackPointOnSegment(right, up + i, right - i, up);\n            }\n            if (x == null) {\n                throw new NotFoundException();\n            }\n            var y = null;\n            // go up left\n            for (var i = 1; y === null && i < maxSize; i++) {\n                y = this.getBlackPointOnSegment(right, down - i, right - i, down);\n            }\n            if (y == null) {\n                throw new NotFoundException();\n            }\n            return this.centerEdges(y, z, x, t);\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    WhiteRectangleDetector.prototype.getBlackPointOnSegment = function (aX /*float*/, aY /*float*/, bX /*float*/, bY /*float*/) {\n        var dist = MathUtils.round(MathUtils.distance(aX, aY, bX, bY));\n        var xStep = (bX - aX) / dist;\n        var yStep = (bY - aY) / dist;\n        var image = this.image;\n        for (var i = 0; i < dist; i++) {\n            var x = MathUtils.round(aX + i * xStep);\n            var y = MathUtils.round(aY + i * yStep);\n            if (image.get(x, y)) {\n                return new ResultPoint(x, y);\n            }\n        }\n        return null;\n    };\n    /**\n     * recenters the points of a constant distance towards the center\n     *\n     * @param y bottom most point\n     * @param z left most point\n     * @param x right most point\n     * @param t top most point\n     * @return {@link ResultPoint}[] describing the corners of the rectangular\n     *         region. The first and last points are opposed on the diagonal, as\n     *         are the second and third. The first point will be the topmost\n     *         point and the last, the bottommost. The second point will be\n     *         leftmost and the third, the rightmost\n     */\n    WhiteRectangleDetector.prototype.centerEdges = function (y, z, x, t) {\n        //\n        //       t            t\n        //  z                      x\n        //        x    OR    z\n        //   y                    y\n        //\n        var yi = y.getX();\n        var yj = y.getY();\n        var zi = z.getX();\n        var zj = z.getY();\n        var xi = x.getX();\n        var xj = x.getY();\n        var ti = t.getX();\n        var tj = t.getY();\n        var CORR = WhiteRectangleDetector.CORR;\n        if (yi < this.width / 2.0) {\n            return [\n                new ResultPoint(ti - CORR, tj + CORR),\n                new ResultPoint(zi + CORR, zj + CORR),\n                new ResultPoint(xi - CORR, xj - CORR),\n                new ResultPoint(yi + CORR, yj - CORR)\n            ];\n        }\n        else {\n            return [\n                new ResultPoint(ti + CORR, tj + CORR),\n                new ResultPoint(zi + CORR, zj - CORR),\n                new ResultPoint(xi - CORR, xj + CORR),\n                new ResultPoint(yi - CORR, yj - CORR)\n            ];\n        }\n    };\n    /**\n     * Determines whether a segment contains a black point\n     *\n     * @param a          min value of the scanned coordinate\n     * @param b          max value of the scanned coordinate\n     * @param fixed      value of fixed coordinate\n     * @param horizontal set to true if scan must be horizontal, false if vertical\n     * @return true if a black point has been found, else false.\n     */\n    WhiteRectangleDetector.prototype.containsBlackPoint = function (a /*int*/, b /*int*/, fixed /*int*/, horizontal) {\n        var image = this.image;\n        if (horizontal) {\n            for (var x = a; x <= b; x++) {\n                if (image.get(x, fixed)) {\n                    return true;\n                }\n            }\n        }\n        else {\n            for (var y = a; y <= b; y++) {\n                if (image.get(fixed, y)) {\n                    return true;\n                }\n            }\n        }\n        return false;\n    };\n    WhiteRectangleDetector.INIT_SIZE = 10;\n    WhiteRectangleDetector.CORR = 1;\n    return WhiteRectangleDetector;\n}());\nexport default WhiteRectangleDetector;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,aAAe,YAAY;EACpD;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,sBAAsBA,CAACC,KAAK,EAAEC,QAAQ,CAAC,SAASC,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAC3E,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACI,MAAM,GAAGJ,KAAK,CAACK,SAAS,CAAC,CAAC;IAC/B,IAAI,CAACC,KAAK,GAAGN,KAAK,CAACO,QAAQ,CAAC,CAAC;IAC7B,IAAIC,SAAS,KAAKP,QAAQ,IAAI,IAAI,KAAKA,QAAQ,EAAE;MAC7CA,QAAQ,GAAGF,sBAAsB,CAACU,SAAS;IAC/C;IACA,IAAID,SAAS,KAAKN,CAAC,IAAI,IAAI,KAAKA,CAAC,EAAE;MAC/BA,CAAC,GAAGF,KAAK,CAACO,QAAQ,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAChC;IACA,IAAIC,SAAS,KAAKL,CAAC,IAAI,IAAI,KAAKA,CAAC,EAAE;MAC/BA,CAAC,GAAGH,KAAK,CAACK,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACjC;IACA,IAAIK,QAAQ,GAAGT,QAAQ,GAAG,CAAC,GAAG,CAAC;IAC/B,IAAI,CAACU,QAAQ,GAAGT,CAAC,GAAGQ,QAAQ;IAC5B,IAAI,CAACE,SAAS,GAAGV,CAAC,GAAGQ,QAAQ;IAC7B,IAAI,CAACG,MAAM,GAAGV,CAAC,GAAGO,QAAQ;IAC1B,IAAI,CAACI,QAAQ,GAAGX,CAAC,GAAGO,QAAQ;IAC5B,IAAI,IAAI,CAACG,MAAM,GAAG,CAAC,IAAI,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,IAAI,CAACG,QAAQ,IAAI,IAAI,CAACV,MAAM,IAAI,IAAI,CAACQ,SAAS,IAAI,IAAI,CAACN,KAAK,EAAE;MACtG,MAAM,IAAIR,iBAAiB,CAAC,CAAC;IACjC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,sBAAsB,CAACgB,SAAS,CAACC,MAAM,GAAG,YAAY;IAClD,IAAIC,IAAI,GAAG,IAAI,CAACN,QAAQ;IACxB,IAAIO,KAAK,GAAG,IAAI,CAACN,SAAS;IAC1B,IAAIO,EAAE,GAAG,IAAI,CAACN,MAAM;IACpB,IAAIO,IAAI,GAAG,IAAI,CAACN,QAAQ;IACxB,IAAIO,YAAY,GAAG,KAAK;IACxB,IAAIC,wBAAwB,GAAG,IAAI;IACnC,IAAIC,iCAAiC,GAAG,KAAK;IAC7C,IAAIC,gCAAgC,GAAG,KAAK;IAC5C,IAAIC,iCAAiC,GAAG,KAAK;IAC7C,IAAIC,+BAA+B,GAAG,KAAK;IAC3C,IAAIC,8BAA8B,GAAG,KAAK;IAC1C,IAAIrB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIF,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,OAAOkB,wBAAwB,EAAE;MAC7BA,wBAAwB,GAAG,KAAK;MAChC;MACA;MACA;MACA,IAAIM,mBAAmB,GAAG,IAAI;MAC9B,OAAO,CAACA,mBAAmB,IAAI,CAACJ,gCAAgC,KAAKN,KAAK,GAAGZ,KAAK,EAAE;QAChFsB,mBAAmB,GAAG,IAAI,CAACC,kBAAkB,CAACV,EAAE,EAAEC,IAAI,EAAEF,KAAK,EAAE,KAAK,CAAC;QACrE,IAAIU,mBAAmB,EAAE;UACrBV,KAAK,EAAE;UACPI,wBAAwB,GAAG,IAAI;UAC/BE,gCAAgC,GAAG,IAAI;QAC3C,CAAC,MACI,IAAI,CAACA,gCAAgC,EAAE;UACxCN,KAAK,EAAE;QACX;MACJ;MACA,IAAIA,KAAK,IAAIZ,KAAK,EAAE;QAChBe,YAAY,GAAG,IAAI;QACnB;MACJ;MACA;MACA;MACA;MACA,IAAIS,oBAAoB,GAAG,IAAI;MAC/B,OAAO,CAACA,oBAAoB,IAAI,CAACL,iCAAiC,KAAKL,IAAI,GAAGhB,MAAM,EAAE;QAClF0B,oBAAoB,GAAG,IAAI,CAACD,kBAAkB,CAACZ,IAAI,EAAEC,KAAK,EAAEE,IAAI,EAAE,IAAI,CAAC;QACvE,IAAIU,oBAAoB,EAAE;UACtBV,IAAI,EAAE;UACNE,wBAAwB,GAAG,IAAI;UAC/BG,iCAAiC,GAAG,IAAI;QAC5C,CAAC,MACI,IAAI,CAACA,iCAAiC,EAAE;UACzCL,IAAI,EAAE;QACV;MACJ;MACA,IAAIA,IAAI,IAAIhB,MAAM,EAAE;QAChBiB,YAAY,GAAG,IAAI;QACnB;MACJ;MACA;MACA;MACA;MACA,IAAIU,kBAAkB,GAAG,IAAI;MAC7B,OAAO,CAACA,kBAAkB,IAAI,CAACL,+BAA+B,KAAKT,IAAI,IAAI,CAAC,EAAE;QAC1Ec,kBAAkB,GAAG,IAAI,CAACF,kBAAkB,CAACV,EAAE,EAAEC,IAAI,EAAEH,IAAI,EAAE,KAAK,CAAC;QACnE,IAAIc,kBAAkB,EAAE;UACpBd,IAAI,EAAE;UACNK,wBAAwB,GAAG,IAAI;UAC/BI,+BAA+B,GAAG,IAAI;QAC1C,CAAC,MACI,IAAI,CAACA,+BAA+B,EAAE;UACvCT,IAAI,EAAE;QACV;MACJ;MACA,IAAIA,IAAI,GAAG,CAAC,EAAE;QACVI,YAAY,GAAG,IAAI;QACnB;MACJ;MACA;MACA;MACA;MACA,IAAIW,iBAAiB,GAAG,IAAI;MAC5B,OAAO,CAACA,iBAAiB,IAAI,CAACL,8BAA8B,KAAKR,EAAE,IAAI,CAAC,EAAE;QACtEa,iBAAiB,GAAG,IAAI,CAACH,kBAAkB,CAACZ,IAAI,EAAEC,KAAK,EAAEC,EAAE,EAAE,IAAI,CAAC;QAClE,IAAIa,iBAAiB,EAAE;UACnBb,EAAE,EAAE;UACJG,wBAAwB,GAAG,IAAI;UAC/BK,8BAA8B,GAAG,IAAI;QACzC,CAAC,MACI,IAAI,CAACA,8BAA8B,EAAE;UACtCR,EAAE,EAAE;QACR;MACJ;MACA,IAAIA,EAAE,GAAG,CAAC,EAAE;QACRE,YAAY,GAAG,IAAI;QACnB;MACJ;MACA,IAAIC,wBAAwB,EAAE;QAC1BC,iCAAiC,GAAG,IAAI;MAC5C;IACJ;IACA,IAAI,CAACF,YAAY,IAAIE,iCAAiC,EAAE;MACpD,IAAIU,OAAO,GAAGf,KAAK,GAAGD,IAAI;MAC1B,IAAIiB,CAAC,GAAG,IAAI;MACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAED,CAAC,KAAK,IAAI,IAAIC,CAAC,GAAGF,OAAO,EAAEE,CAAC,EAAE,EAAE;QAC5CD,CAAC,GAAG,IAAI,CAACE,sBAAsB,CAACnB,IAAI,EAAEG,IAAI,GAAGe,CAAC,EAAElB,IAAI,GAAGkB,CAAC,EAAEf,IAAI,CAAC;MACnE;MACA,IAAIc,CAAC,IAAI,IAAI,EAAE;QACX,MAAM,IAAIpC,iBAAiB,CAAC,CAAC;MACjC;MACA,IAAIuC,CAAC,GAAG,IAAI;MACZ;MACA,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEE,CAAC,KAAK,IAAI,IAAIF,CAAC,GAAGF,OAAO,EAAEE,CAAC,EAAE,EAAE;QAC5CE,CAAC,GAAG,IAAI,CAACD,sBAAsB,CAACnB,IAAI,EAAEE,EAAE,GAAGgB,CAAC,EAAElB,IAAI,GAAGkB,CAAC,EAAEhB,EAAE,CAAC;MAC/D;MACA,IAAIkB,CAAC,IAAI,IAAI,EAAE;QACX,MAAM,IAAIvC,iBAAiB,CAAC,CAAC;MACjC;MACA,IAAII,CAAC,GAAG,IAAI;MACZ;MACA,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEjC,CAAC,KAAK,IAAI,IAAIiC,CAAC,GAAGF,OAAO,EAAEE,CAAC,EAAE,EAAE;QAC5CjC,CAAC,GAAG,IAAI,CAACkC,sBAAsB,CAAClB,KAAK,EAAEC,EAAE,GAAGgB,CAAC,EAAEjB,KAAK,GAAGiB,CAAC,EAAEhB,EAAE,CAAC;MACjE;MACA,IAAIjB,CAAC,IAAI,IAAI,EAAE;QACX,MAAM,IAAIJ,iBAAiB,CAAC,CAAC;MACjC;MACA,IAAIK,CAAC,GAAG,IAAI;MACZ;MACA,KAAK,IAAIgC,CAAC,GAAG,CAAC,EAAEhC,CAAC,KAAK,IAAI,IAAIgC,CAAC,GAAGF,OAAO,EAAEE,CAAC,EAAE,EAAE;QAC5ChC,CAAC,GAAG,IAAI,CAACiC,sBAAsB,CAAClB,KAAK,EAAEE,IAAI,GAAGe,CAAC,EAAEjB,KAAK,GAAGiB,CAAC,EAAEf,IAAI,CAAC;MACrE;MACA,IAAIjB,CAAC,IAAI,IAAI,EAAE;QACX,MAAM,IAAIL,iBAAiB,CAAC,CAAC;MACjC;MACA,OAAO,IAAI,CAACwC,WAAW,CAACnC,CAAC,EAAE+B,CAAC,EAAEhC,CAAC,EAAEmC,CAAC,CAAC;IACvC,CAAC,MACI;MACD,MAAM,IAAIvC,iBAAiB,CAAC,CAAC;IACjC;EACJ,CAAC;EACDC,sBAAsB,CAACgB,SAAS,CAACqB,sBAAsB,GAAG,UAAUG,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAWC,EAAE,CAAC,WAAW;IACxH,IAAIC,IAAI,GAAG9C,SAAS,CAAC+C,KAAK,CAAC/C,SAAS,CAACgD,QAAQ,CAACN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;IAC9D,IAAII,KAAK,GAAG,CAACL,EAAE,GAAGF,EAAE,IAAII,IAAI;IAC5B,IAAII,KAAK,GAAG,CAACL,EAAE,GAAGF,EAAE,IAAIG,IAAI;IAC5B,IAAI3C,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,IAAI,EAAER,CAAC,EAAE,EAAE;MAC3B,IAAIjC,CAAC,GAAGL,SAAS,CAAC+C,KAAK,CAACL,EAAE,GAAGJ,CAAC,GAAGW,KAAK,CAAC;MACvC,IAAI3C,CAAC,GAAGN,SAAS,CAAC+C,KAAK,CAACJ,EAAE,GAAGL,CAAC,GAAGY,KAAK,CAAC;MACvC,IAAI/C,KAAK,CAACgD,GAAG,CAAC9C,CAAC,EAAEC,CAAC,CAAC,EAAE;QACjB,OAAO,IAAIP,WAAW,CAACM,CAAC,EAAEC,CAAC,CAAC;MAChC;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIJ,sBAAsB,CAACgB,SAAS,CAACuB,WAAW,GAAG,UAAUnC,CAAC,EAAE+B,CAAC,EAAEhC,CAAC,EAAEmC,CAAC,EAAE;IACjE;IACA;IACA;IACA;IACA;IACA;IACA,IAAIY,EAAE,GAAG9C,CAAC,CAAC+C,IAAI,CAAC,CAAC;IACjB,IAAIC,EAAE,GAAGhD,CAAC,CAACiD,IAAI,CAAC,CAAC;IACjB,IAAIC,EAAE,GAAGnB,CAAC,CAACgB,IAAI,CAAC,CAAC;IACjB,IAAII,EAAE,GAAGpB,CAAC,CAACkB,IAAI,CAAC,CAAC;IACjB,IAAIG,EAAE,GAAGrD,CAAC,CAACgD,IAAI,CAAC,CAAC;IACjB,IAAIM,EAAE,GAAGtD,CAAC,CAACkD,IAAI,CAAC,CAAC;IACjB,IAAIK,EAAE,GAAGpB,CAAC,CAACa,IAAI,CAAC,CAAC;IACjB,IAAIQ,EAAE,GAAGrB,CAAC,CAACe,IAAI,CAAC,CAAC;IACjB,IAAIO,IAAI,GAAG5D,sBAAsB,CAAC4D,IAAI;IACtC,IAAIV,EAAE,GAAG,IAAI,CAAC3C,KAAK,GAAG,GAAG,EAAE;MACvB,OAAO,CACH,IAAIV,WAAW,CAAC6D,EAAE,GAAGE,IAAI,EAAED,EAAE,GAAGC,IAAI,CAAC,EACrC,IAAI/D,WAAW,CAACyD,EAAE,GAAGM,IAAI,EAAEL,EAAE,GAAGK,IAAI,CAAC,EACrC,IAAI/D,WAAW,CAAC2D,EAAE,GAAGI,IAAI,EAAEH,EAAE,GAAGG,IAAI,CAAC,EACrC,IAAI/D,WAAW,CAACqD,EAAE,GAAGU,IAAI,EAAER,EAAE,GAAGQ,IAAI,CAAC,CACxC;IACL,CAAC,MACI;MACD,OAAO,CACH,IAAI/D,WAAW,CAAC6D,EAAE,GAAGE,IAAI,EAAED,EAAE,GAAGC,IAAI,CAAC,EACrC,IAAI/D,WAAW,CAACyD,EAAE,GAAGM,IAAI,EAAEL,EAAE,GAAGK,IAAI,CAAC,EACrC,IAAI/D,WAAW,CAAC2D,EAAE,GAAGI,IAAI,EAAEH,EAAE,GAAGG,IAAI,CAAC,EACrC,IAAI/D,WAAW,CAACqD,EAAE,GAAGU,IAAI,EAAER,EAAE,GAAGQ,IAAI,CAAC,CACxC;IACL;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5D,sBAAsB,CAACgB,SAAS,CAACc,kBAAkB,GAAG,UAAU+B,CAAC,CAAC,SAASC,CAAC,CAAC,SAASC,KAAK,CAAC,SAASC,UAAU,EAAE;IAC7G,IAAI/D,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI+D,UAAU,EAAE;MACZ,KAAK,IAAI7D,CAAC,GAAG0D,CAAC,EAAE1D,CAAC,IAAI2D,CAAC,EAAE3D,CAAC,EAAE,EAAE;QACzB,IAAIF,KAAK,CAACgD,GAAG,CAAC9C,CAAC,EAAE4D,KAAK,CAAC,EAAE;UACrB,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,MACI;MACD,KAAK,IAAI3D,CAAC,GAAGyD,CAAC,EAAEzD,CAAC,IAAI0D,CAAC,EAAE1D,CAAC,EAAE,EAAE;QACzB,IAAIH,KAAK,CAACgD,GAAG,CAACc,KAAK,EAAE3D,CAAC,CAAC,EAAE;UACrB,OAAO,IAAI;QACf;MACJ;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACDJ,sBAAsB,CAACU,SAAS,GAAG,EAAE;EACrCV,sBAAsB,CAAC4D,IAAI,GAAG,CAAC;EAC/B,OAAO5D,sBAAsB;AACjC,CAAC,CAAC,CAAE;AACJ,eAAeA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}