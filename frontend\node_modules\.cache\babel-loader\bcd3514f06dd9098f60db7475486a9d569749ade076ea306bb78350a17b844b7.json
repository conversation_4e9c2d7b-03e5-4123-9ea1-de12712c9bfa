{"ast": null, "code": "/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport Result from '../Result';\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport ResultMetadataType from '../ResultMetadataType';\nimport System from '../util/System';\nimport Decoder from './decoder/Decoder';\nimport Detector from './detector/Detector';\n// import java.util.List;\n// import java.util.Map;\n/**\n * This implementation can detect and decode Aztec codes in an image.\n *\n * <AUTHOR>\n */\nvar AztecReader = /** @class */function () {\n  function AztecReader() {}\n  /**\n   * Locates and decodes a Data Matrix code in an image.\n   *\n   * @return a String representing the content encoded by the Data Matrix code\n   * @throws NotFoundException if a Data Matrix code cannot be found\n   * @throws FormatException if a Data Matrix code cannot be decoded\n   */\n  AztecReader.prototype.decode = function (image, hints) {\n    if (hints === void 0) {\n      hints = null;\n    }\n    var exception = null;\n    var detector = new Detector(image.getBlackMatrix());\n    var points = null;\n    var decoderResult = null;\n    try {\n      var detectorResult = detector.detectMirror(false);\n      points = detectorResult.getPoints();\n      this.reportFoundResultPoints(hints, points);\n      decoderResult = new Decoder().decode(detectorResult);\n    } catch (e) {\n      exception = e;\n    }\n    if (decoderResult == null) {\n      try {\n        var detectorResult = detector.detectMirror(true);\n        points = detectorResult.getPoints();\n        this.reportFoundResultPoints(hints, points);\n        decoderResult = new Decoder().decode(detectorResult);\n      } catch (e) {\n        if (exception != null) {\n          throw exception;\n        }\n        throw e;\n      }\n    }\n    var result = new Result(decoderResult.getText(), decoderResult.getRawBytes(), decoderResult.getNumBits(), points, BarcodeFormat.AZTEC, System.currentTimeMillis());\n    var byteSegments = decoderResult.getByteSegments();\n    if (byteSegments != null) {\n      result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n    }\n    var ecLevel = decoderResult.getECLevel();\n    if (ecLevel != null) {\n      result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n    }\n    return result;\n  };\n  AztecReader.prototype.reportFoundResultPoints = function (hints, points) {\n    if (hints != null) {\n      var rpcb_1 = hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n      if (rpcb_1 != null) {\n        points.forEach(function (point, idx, arr) {\n          rpcb_1.foundPossibleResultPoint(point);\n        });\n      }\n    }\n  };\n  // @Override\n  AztecReader.prototype.reset = function () {\n    // do nothing\n  };\n  return AztecReader;\n}();\nexport default AztecReader;", "map": {"version": 3, "names": ["Result", "BarcodeFormat", "DecodeHintType", "ResultMetadataType", "System", "Decoder", "Detector", "AztecReader", "prototype", "decode", "image", "hints", "exception", "detector", "getBlackMatrix", "points", "decoderResult", "detectorResult", "detectMirror", "getPoints", "reportFoundResultPoints", "e", "result", "getText", "getRawBytes", "getNumBits", "AZTEC", "currentTimeMillis", "byteSegments", "getByteSegments", "putMetadata", "BYTE_SEGMENTS", "ecLevel", "getECLevel", "ERROR_CORRECTION_LEVEL", "rpcb_1", "get", "NEED_RESULT_POINT_CALLBACK", "for<PERSON>ach", "point", "idx", "arr", "foundPossibleResultPoint", "reset"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/AztecReader.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport Result from '../Result';\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport ResultMetadataType from '../ResultMetadataType';\nimport System from '../util/System';\nimport Decoder from './decoder/Decoder';\nimport Detector from './detector/Detector';\n// import java.util.List;\n// import java.util.Map;\n/**\n * This implementation can detect and decode Aztec codes in an image.\n *\n * <AUTHOR>\n */\nvar AztecReader = /** @class */ (function () {\n    function AztecReader() {\n    }\n    /**\n     * Locates and decodes a Data Matrix code in an image.\n     *\n     * @return a String representing the content encoded by the Data Matrix code\n     * @throws NotFoundException if a Data Matrix code cannot be found\n     * @throws FormatException if a Data Matrix code cannot be decoded\n     */\n    AztecReader.prototype.decode = function (image, hints) {\n        if (hints === void 0) { hints = null; }\n        var exception = null;\n        var detector = new Detector(image.getBlackMatrix());\n        var points = null;\n        var decoderResult = null;\n        try {\n            var detectorResult = detector.detectMirror(false);\n            points = detectorResult.getPoints();\n            this.reportFoundResultPoints(hints, points);\n            decoderResult = new Decoder().decode(detectorResult);\n        }\n        catch (e) {\n            exception = e;\n        }\n        if (decoderResult == null) {\n            try {\n                var detectorResult = detector.detectMirror(true);\n                points = detectorResult.getPoints();\n                this.reportFoundResultPoints(hints, points);\n                decoderResult = new Decoder().decode(detectorResult);\n            }\n            catch (e) {\n                if (exception != null) {\n                    throw exception;\n                }\n                throw e;\n            }\n        }\n        var result = new Result(decoderResult.getText(), decoderResult.getRawBytes(), decoderResult.getNumBits(), points, BarcodeFormat.AZTEC, System.currentTimeMillis());\n        var byteSegments = decoderResult.getByteSegments();\n        if (byteSegments != null) {\n            result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n        }\n        var ecLevel = decoderResult.getECLevel();\n        if (ecLevel != null) {\n            result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n        }\n        return result;\n    };\n    AztecReader.prototype.reportFoundResultPoints = function (hints, points) {\n        if (hints != null) {\n            var rpcb_1 = hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n            if (rpcb_1 != null) {\n                points.forEach(function (point, idx, arr) {\n                    rpcb_1.foundPossibleResultPoint(point);\n                });\n            }\n        }\n    };\n    // @Override\n    AztecReader.prototype.reset = function () {\n        // do nothing\n    };\n    return AztecReader;\n}());\nexport default AztecReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG,CACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,WAAW,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACnD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,QAAQ,GAAG,IAAIP,QAAQ,CAACI,KAAK,CAACI,cAAc,CAAC,CAAC,CAAC;IACnD,IAAIC,MAAM,GAAG,IAAI;IACjB,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAI;MACA,IAAIC,cAAc,GAAGJ,QAAQ,CAACK,YAAY,CAAC,KAAK,CAAC;MACjDH,MAAM,GAAGE,cAAc,CAACE,SAAS,CAAC,CAAC;MACnC,IAAI,CAACC,uBAAuB,CAACT,KAAK,EAAEI,MAAM,CAAC;MAC3CC,aAAa,GAAG,IAAIX,OAAO,CAAC,CAAC,CAACI,MAAM,CAACQ,cAAc,CAAC;IACxD,CAAC,CACD,OAAOI,CAAC,EAAE;MACNT,SAAS,GAAGS,CAAC;IACjB;IACA,IAAIL,aAAa,IAAI,IAAI,EAAE;MACvB,IAAI;QACA,IAAIC,cAAc,GAAGJ,QAAQ,CAACK,YAAY,CAAC,IAAI,CAAC;QAChDH,MAAM,GAAGE,cAAc,CAACE,SAAS,CAAC,CAAC;QACnC,IAAI,CAACC,uBAAuB,CAACT,KAAK,EAAEI,MAAM,CAAC;QAC3CC,aAAa,GAAG,IAAIX,OAAO,CAAC,CAAC,CAACI,MAAM,CAACQ,cAAc,CAAC;MACxD,CAAC,CACD,OAAOI,CAAC,EAAE;QACN,IAAIT,SAAS,IAAI,IAAI,EAAE;UACnB,MAAMA,SAAS;QACnB;QACA,MAAMS,CAAC;MACX;IACJ;IACA,IAAIC,MAAM,GAAG,IAAItB,MAAM,CAACgB,aAAa,CAACO,OAAO,CAAC,CAAC,EAAEP,aAAa,CAACQ,WAAW,CAAC,CAAC,EAAER,aAAa,CAACS,UAAU,CAAC,CAAC,EAAEV,MAAM,EAAEd,aAAa,CAACyB,KAAK,EAAEtB,MAAM,CAACuB,iBAAiB,CAAC,CAAC,CAAC;IAClK,IAAIC,YAAY,GAAGZ,aAAa,CAACa,eAAe,CAAC,CAAC;IAClD,IAAID,YAAY,IAAI,IAAI,EAAE;MACtBN,MAAM,CAACQ,WAAW,CAAC3B,kBAAkB,CAAC4B,aAAa,EAAEH,YAAY,CAAC;IACtE;IACA,IAAII,OAAO,GAAGhB,aAAa,CAACiB,UAAU,CAAC,CAAC;IACxC,IAAID,OAAO,IAAI,IAAI,EAAE;MACjBV,MAAM,CAACQ,WAAW,CAAC3B,kBAAkB,CAAC+B,sBAAsB,EAAEF,OAAO,CAAC;IAC1E;IACA,OAAOV,MAAM;EACjB,CAAC;EACDf,WAAW,CAACC,SAAS,CAACY,uBAAuB,GAAG,UAAUT,KAAK,EAAEI,MAAM,EAAE;IACrE,IAAIJ,KAAK,IAAI,IAAI,EAAE;MACf,IAAIwB,MAAM,GAAGxB,KAAK,CAACyB,GAAG,CAAClC,cAAc,CAACmC,0BAA0B,CAAC;MACjE,IAAIF,MAAM,IAAI,IAAI,EAAE;QAChBpB,MAAM,CAACuB,OAAO,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAE;UACtCN,MAAM,CAACO,wBAAwB,CAACH,KAAK,CAAC;QAC1C,CAAC,CAAC;MACN;IACJ;EACJ,CAAC;EACD;EACAhC,WAAW,CAACC,SAAS,CAACmC,KAAK,GAAG,YAAY;IACtC;EAAA,CACH;EACD,OAAOpC,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}