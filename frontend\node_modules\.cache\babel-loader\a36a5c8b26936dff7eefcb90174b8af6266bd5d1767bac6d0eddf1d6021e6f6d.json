{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"clearable\", \"onClear\", \"InputProps\", \"sx\", \"slots\", \"slotProps\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport MuiIconButton from '@mui/material/IconButton';\nimport InputAdornment from '@mui/material/InputAdornment';\nimport { ClearIcon } from \"../icons/index.js\";\nimport { usePickersTranslations } from \"./usePickersTranslations.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const useClearableField = props => {\n  const translations = usePickersTranslations();\n  const {\n      clearable,\n      onClear,\n      InputProps,\n      sx,\n      slots,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const IconButton = slots?.clearButton ?? MuiIconButton;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: IconButton,\n      externalSlotProps: slotProps?.clearButton,\n      ownerState: {},\n      className: 'clearButton',\n      additionalProps: {\n        title: translations.fieldClearLabel\n      }\n    }),\n    iconButtonProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const EndClearIcon = slots?.clearIcon ?? ClearIcon;\n  const endClearIconProps = useSlotProps({\n    elementType: EndClearIcon,\n    externalSlotProps: slotProps?.clearIcon,\n    ownerState: {}\n  });\n  return _extends({}, other, {\n    InputProps: _extends({}, InputProps, {\n      endAdornment: /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [clearable && /*#__PURE__*/_jsx(InputAdornment, {\n          position: \"end\",\n          sx: {\n            marginRight: InputProps?.endAdornment ? -1 : -1.5\n          },\n          children: /*#__PURE__*/_jsx(IconButton, _extends({}, iconButtonProps, {\n            onClick: onClear,\n            children: /*#__PURE__*/_jsx(EndClearIcon, _extends({\n              fontSize: \"small\"\n            }, endClearIconProps))\n          }))\n        }), InputProps?.endAdornment]\n      })\n    }),\n    sx: [{\n      '& .clearButton': {\n        opacity: 1\n      },\n      '@media (pointer: fine)': {\n        '& .clearButton': {\n          opacity: 0\n        },\n        '&:hover, &:focus-within': {\n          '.clearButton': {\n            opacity: 1\n          }\n        }\n      }\n    }, ...(Array.isArray(sx) ? sx : [sx])]\n  });\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "React", "useSlotProps", "MuiIconButton", "InputAdornment", "ClearIcon", "usePickersTranslations", "jsx", "_jsx", "jsxs", "_jsxs", "useClearableField", "props", "translations", "clearable", "onClear", "InputProps", "sx", "slots", "slotProps", "other", "IconButton", "clearButton", "_useSlotProps", "elementType", "externalSlotProps", "ownerState", "className", "additionalProps", "title", "fieldClearLabel", "iconButtonProps", "EndClearIcon", "clearIcon", "endClearIconProps", "endAdornment", "Fragment", "children", "position", "marginRight", "onClick", "fontSize", "opacity", "Array", "isArray"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/hooks/useClearableField.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"clearable\", \"onClear\", \"InputProps\", \"sx\", \"slots\", \"slotProps\"],\n  _excluded2 = [\"ownerState\"];\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport MuiIconButton from '@mui/material/IconButton';\nimport InputAdornment from '@mui/material/InputAdornment';\nimport { ClearIcon } from \"../icons/index.js\";\nimport { usePickersTranslations } from \"./usePickersTranslations.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const useClearableField = props => {\n  const translations = usePickersTranslations();\n  const {\n      clearable,\n      onClear,\n      InputProps,\n      sx,\n      slots,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const IconButton = slots?.clearButton ?? MuiIconButton;\n  // The spread is here to avoid this bug mui/material-ui#34056\n  const _useSlotProps = useSlotProps({\n      elementType: IconButton,\n      externalSlotProps: slotProps?.clearButton,\n      ownerState: {},\n      className: 'clearButton',\n      additionalProps: {\n        title: translations.fieldClearLabel\n      }\n    }),\n    iconButtonProps = _objectWithoutPropertiesLoose(_useSlotProps, _excluded2);\n  const EndClearIcon = slots?.clearIcon ?? ClearIcon;\n  const endClearIconProps = useSlotProps({\n    elementType: EndClearIcon,\n    externalSlotProps: slotProps?.clearIcon,\n    ownerState: {}\n  });\n  return _extends({}, other, {\n    InputProps: _extends({}, InputProps, {\n      endAdornment: /*#__PURE__*/_jsxs(React.Fragment, {\n        children: [clearable && /*#__PURE__*/_jsx(InputAdornment, {\n          position: \"end\",\n          sx: {\n            marginRight: InputProps?.endAdornment ? -1 : -1.5\n          },\n          children: /*#__PURE__*/_jsx(IconButton, _extends({}, iconButtonProps, {\n            onClick: onClear,\n            children: /*#__PURE__*/_jsx(EndClearIcon, _extends({\n              fontSize: \"small\"\n            }, endClearIconProps))\n          }))\n        }), InputProps?.endAdornment]\n      })\n    }),\n    sx: [{\n      '& .clearButton': {\n        opacity: 1\n      },\n      '@media (pointer: fine)': {\n        '& .clearButton': {\n          opacity: 0\n        },\n        '&:hover, &:focus-within': {\n          '.clearButton': {\n            opacity: 1\n          }\n        }\n      }\n    }, ...(Array.isArray(sx) ? sx : [sx])]\n  });\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;EAClFC,UAAU,GAAG,CAAC,YAAY,CAAC;AAC7B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,OAAO,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;EACxC,MAAMC,YAAY,GAAGP,sBAAsB,CAAC,CAAC;EAC7C,MAAM;MACFQ,SAAS;MACTC,OAAO;MACPC,UAAU;MACVC,EAAE;MACFC,KAAK;MACLC;IACF,CAAC,GAAGP,KAAK;IACTQ,KAAK,GAAGtB,6BAA6B,CAACc,KAAK,EAAEb,SAAS,CAAC;EACzD,MAAMsB,UAAU,GAAGH,KAAK,EAAEI,WAAW,IAAInB,aAAa;EACtD;EACA,MAAMoB,aAAa,GAAGrB,YAAY,CAAC;MAC/BsB,WAAW,EAAEH,UAAU;MACvBI,iBAAiB,EAAEN,SAAS,EAAEG,WAAW;MACzCI,UAAU,EAAE,CAAC,CAAC;MACdC,SAAS,EAAE,aAAa;MACxBC,eAAe,EAAE;QACfC,KAAK,EAAEhB,YAAY,CAACiB;MACtB;IACF,CAAC,CAAC;IACFC,eAAe,GAAGjC,6BAA6B,CAACyB,aAAa,EAAEvB,UAAU,CAAC;EAC5E,MAAMgC,YAAY,GAAGd,KAAK,EAAEe,SAAS,IAAI5B,SAAS;EAClD,MAAM6B,iBAAiB,GAAGhC,YAAY,CAAC;IACrCsB,WAAW,EAAEQ,YAAY;IACzBP,iBAAiB,EAAEN,SAAS,EAAEc,SAAS;IACvCP,UAAU,EAAE,CAAC;EACf,CAAC,CAAC;EACF,OAAO7B,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACzBJ,UAAU,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAEmB,UAAU,EAAE;MACnCmB,YAAY,EAAE,aAAazB,KAAK,CAACT,KAAK,CAACmC,QAAQ,EAAE;QAC/CC,QAAQ,EAAE,CAACvB,SAAS,IAAI,aAAaN,IAAI,CAACJ,cAAc,EAAE;UACxDkC,QAAQ,EAAE,KAAK;UACfrB,EAAE,EAAE;YACFsB,WAAW,EAAEvB,UAAU,EAAEmB,YAAY,GAAG,CAAC,CAAC,GAAG,CAAC;UAChD,CAAC;UACDE,QAAQ,EAAE,aAAa7B,IAAI,CAACa,UAAU,EAAExB,QAAQ,CAAC,CAAC,CAAC,EAAEkC,eAAe,EAAE;YACpES,OAAO,EAAEzB,OAAO;YAChBsB,QAAQ,EAAE,aAAa7B,IAAI,CAACwB,YAAY,EAAEnC,QAAQ,CAAC;cACjD4C,QAAQ,EAAE;YACZ,CAAC,EAAEP,iBAAiB,CAAC;UACvB,CAAC,CAAC;QACJ,CAAC,CAAC,EAAElB,UAAU,EAAEmB,YAAY;MAC9B,CAAC;IACH,CAAC,CAAC;IACFlB,EAAE,EAAE,CAAC;MACH,gBAAgB,EAAE;QAChByB,OAAO,EAAE;MACX,CAAC;MACD,wBAAwB,EAAE;QACxB,gBAAgB,EAAE;UAChBA,OAAO,EAAE;QACX,CAAC;QACD,yBAAyB,EAAE;UACzB,cAAc,EAAE;YACdA,OAAO,EAAE;UACX;QACF;MACF;IACF,CAAC,EAAE,IAAIC,KAAK,CAACC,OAAO,CAAC3B,EAAE,CAAC,GAAGA,EAAE,GAAG,CAACA,EAAE,CAAC,CAAC;EACvC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}