{"ast": null, "code": "import { ASCII_ENCODATION, BASE256_ENCODATION, C40_ENCODATION, EDIFACT_ENCODATION, LATCH_TO_ANSIX12, LATCH_TO_BASE256, LATCH_TO_C40, LATCH_TO_EDIFACT, LATCH_TO_TEXT, TEXT_ENCODATION, UPPER_SHIFT, X12_ENCODATION } from './constants';\n// tslint:disable-next-line:no-circular-imports\nimport HighLevelEncoder from './HighLevelEncoder';\nvar ASCIIEncoder = /** @class */function () {\n  function ASCIIEncoder() {}\n  ASCIIEncoder.prototype.getEncodingMode = function () {\n    return ASCII_ENCODATION;\n  };\n  ASCIIEncoder.prototype.encode = function (context) {\n    // step B\n    var n = HighLevelEncoder.determineConsecutiveDigitCount(context.getMessage(), context.pos);\n    if (n >= 2) {\n      context.writeCodeword(this.encodeASCIIDigits(context.getMessage().charCodeAt(context.pos), context.getMessage().charCodeAt(context.pos + 1)));\n      context.pos += 2;\n    } else {\n      var c = context.getCurrentChar();\n      var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n      if (newMode !== this.getEncodingMode()) {\n        switch (newMode) {\n          case BASE256_ENCODATION:\n            context.writeCodeword(LATCH_TO_BASE256);\n            context.signalEncoderChange(BASE256_ENCODATION);\n            return;\n          case C40_ENCODATION:\n            context.writeCodeword(LATCH_TO_C40);\n            context.signalEncoderChange(C40_ENCODATION);\n            return;\n          case X12_ENCODATION:\n            context.writeCodeword(LATCH_TO_ANSIX12);\n            context.signalEncoderChange(X12_ENCODATION);\n            break;\n          case TEXT_ENCODATION:\n            context.writeCodeword(LATCH_TO_TEXT);\n            context.signalEncoderChange(TEXT_ENCODATION);\n            break;\n          case EDIFACT_ENCODATION:\n            context.writeCodeword(LATCH_TO_EDIFACT);\n            context.signalEncoderChange(EDIFACT_ENCODATION);\n            break;\n          default:\n            throw new Error('Illegal mode: ' + newMode);\n        }\n      } else if (HighLevelEncoder.isExtendedASCII(c)) {\n        context.writeCodeword(UPPER_SHIFT);\n        context.writeCodeword(c - 128 + 1);\n        context.pos++;\n      } else {\n        context.writeCodeword(c + 1);\n        context.pos++;\n      }\n    }\n  };\n  ASCIIEncoder.prototype.encodeASCIIDigits = function (digit1, digit2) {\n    if (HighLevelEncoder.isDigit(digit1) && HighLevelEncoder.isDigit(digit2)) {\n      var num = (digit1 - 48) * 10 + (digit2 - 48);\n      return num + 130;\n    }\n    throw new Error('not digits: ' + digit1 + digit2);\n  };\n  return ASCIIEncoder;\n}();\nexport { ASCIIEncoder };", "map": {"version": 3, "names": ["ASCII_ENCODATION", "BASE256_ENCODATION", "C40_ENCODATION", "EDIFACT_ENCODATION", "LATCH_TO_ANSIX12", "LATCH_TO_BASE256", "LATCH_TO_C40", "LATCH_TO_EDIFACT", "LATCH_TO_TEXT", "TEXT_ENCODATION", "UPPER_SHIFT", "X12_ENCODATION", "HighLevelEncoder", "ASCIIEncoder", "prototype", "getEncodingMode", "encode", "context", "n", "determineConsecutiveDigitCount", "getMessage", "pos", "writeCodeword", "encodeASCIIDigits", "charCodeAt", "c", "getCurrentChar", "newMode", "lookAheadTest", "signalEncoderChange", "Error", "isExtendedASCII", "digit1", "digit2", "isDigit", "num"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/ASCIIEncoder.js"], "sourcesContent": ["import { ASCII_ENCODATION, BASE256_ENCODATION, C40_ENCODATION, EDIFACT_ENCODATION, LATCH_TO_ANSIX12, LATCH_TO_BASE256, LATCH_TO_C40, LATCH_TO_EDIFACT, LATCH_TO_TEXT, TEXT_ENCODATION, UPPER_SHIFT, X12_ENCODATION, } from './constants';\n// tslint:disable-next-line:no-circular-imports\nimport HighLevelEncoder from './HighLevelEncoder';\nvar ASCIIEncoder = /** @class */ (function () {\n    function ASCIIEncoder() {\n    }\n    ASCIIEncoder.prototype.getEncodingMode = function () {\n        return ASCII_ENCODATION;\n    };\n    ASCIIEncoder.prototype.encode = function (context) {\n        // step B\n        var n = HighLevelEncoder.determineConsecutiveDigitCount(context.getMessage(), context.pos);\n        if (n >= 2) {\n            context.writeCodeword(this.encodeASCIIDigits(context.getMessage().charCodeAt(context.pos), context.getMessage().charCodeAt(context.pos + 1)));\n            context.pos += 2;\n        }\n        else {\n            var c = context.getCurrentChar();\n            var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n            if (newMode !== this.getEncodingMode()) {\n                switch (newMode) {\n                    case BASE256_ENCODATION:\n                        context.writeCodeword(LATCH_TO_BASE256);\n                        context.signalEncoderChange(BASE256_ENCODATION);\n                        return;\n                    case C40_ENCODATION:\n                        context.writeCodeword(LATCH_TO_C40);\n                        context.signalEncoderChange(C40_ENCODATION);\n                        return;\n                    case X12_ENCODATION:\n                        context.writeCodeword(LATCH_TO_ANSIX12);\n                        context.signalEncoderChange(X12_ENCODATION);\n                        break;\n                    case TEXT_ENCODATION:\n                        context.writeCodeword(LATCH_TO_TEXT);\n                        context.signalEncoderChange(TEXT_ENCODATION);\n                        break;\n                    case EDIFACT_ENCODATION:\n                        context.writeCodeword(LATCH_TO_EDIFACT);\n                        context.signalEncoderChange(EDIFACT_ENCODATION);\n                        break;\n                    default:\n                        throw new Error('Illegal mode: ' + newMode);\n                }\n            }\n            else if (HighLevelEncoder.isExtendedASCII(c)) {\n                context.writeCodeword(UPPER_SHIFT);\n                context.writeCodeword(c - 128 + 1);\n                context.pos++;\n            }\n            else {\n                context.writeCodeword(c + 1);\n                context.pos++;\n            }\n        }\n    };\n    ASCIIEncoder.prototype.encodeASCIIDigits = function (digit1, digit2) {\n        if (HighLevelEncoder.isDigit(digit1) && HighLevelEncoder.isDigit(digit2)) {\n            var num = (digit1 - 48) * 10 + (digit2 - 48);\n            return num + 130;\n        }\n        throw new Error('not digits: ' + digit1 + digit2);\n    };\n    return ASCIIEncoder;\n}());\nexport { ASCIIEncoder };\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,eAAe,EAAEC,WAAW,EAAEC,cAAc,QAAS,aAAa;AACxO;AACA,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG,CACxB;EACAA,YAAY,CAACC,SAAS,CAACC,eAAe,GAAG,YAAY;IACjD,OAAOf,gBAAgB;EAC3B,CAAC;EACDa,YAAY,CAACC,SAAS,CAACE,MAAM,GAAG,UAAUC,OAAO,EAAE;IAC/C;IACA,IAAIC,CAAC,GAAGN,gBAAgB,CAACO,8BAA8B,CAACF,OAAO,CAACG,UAAU,CAAC,CAAC,EAAEH,OAAO,CAACI,GAAG,CAAC;IAC1F,IAAIH,CAAC,IAAI,CAAC,EAAE;MACRD,OAAO,CAACK,aAAa,CAAC,IAAI,CAACC,iBAAiB,CAACN,OAAO,CAACG,UAAU,CAAC,CAAC,CAACI,UAAU,CAACP,OAAO,CAACI,GAAG,CAAC,EAAEJ,OAAO,CAACG,UAAU,CAAC,CAAC,CAACI,UAAU,CAACP,OAAO,CAACI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7IJ,OAAO,CAACI,GAAG,IAAI,CAAC;IACpB,CAAC,MACI;MACD,IAAII,CAAC,GAAGR,OAAO,CAACS,cAAc,CAAC,CAAC;MAChC,IAAIC,OAAO,GAAGf,gBAAgB,CAACgB,aAAa,CAACX,OAAO,CAACG,UAAU,CAAC,CAAC,EAAEH,OAAO,CAACI,GAAG,EAAE,IAAI,CAACN,eAAe,CAAC,CAAC,CAAC;MACvG,IAAIY,OAAO,KAAK,IAAI,CAACZ,eAAe,CAAC,CAAC,EAAE;QACpC,QAAQY,OAAO;UACX,KAAK1B,kBAAkB;YACnBgB,OAAO,CAACK,aAAa,CAACjB,gBAAgB,CAAC;YACvCY,OAAO,CAACY,mBAAmB,CAAC5B,kBAAkB,CAAC;YAC/C;UACJ,KAAKC,cAAc;YACfe,OAAO,CAACK,aAAa,CAAChB,YAAY,CAAC;YACnCW,OAAO,CAACY,mBAAmB,CAAC3B,cAAc,CAAC;YAC3C;UACJ,KAAKS,cAAc;YACfM,OAAO,CAACK,aAAa,CAAClB,gBAAgB,CAAC;YACvCa,OAAO,CAACY,mBAAmB,CAAClB,cAAc,CAAC;YAC3C;UACJ,KAAKF,eAAe;YAChBQ,OAAO,CAACK,aAAa,CAACd,aAAa,CAAC;YACpCS,OAAO,CAACY,mBAAmB,CAACpB,eAAe,CAAC;YAC5C;UACJ,KAAKN,kBAAkB;YACnBc,OAAO,CAACK,aAAa,CAACf,gBAAgB,CAAC;YACvCU,OAAO,CAACY,mBAAmB,CAAC1B,kBAAkB,CAAC;YAC/C;UACJ;YACI,MAAM,IAAI2B,KAAK,CAAC,gBAAgB,GAAGH,OAAO,CAAC;QACnD;MACJ,CAAC,MACI,IAAIf,gBAAgB,CAACmB,eAAe,CAACN,CAAC,CAAC,EAAE;QAC1CR,OAAO,CAACK,aAAa,CAACZ,WAAW,CAAC;QAClCO,OAAO,CAACK,aAAa,CAACG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;QAClCR,OAAO,CAACI,GAAG,EAAE;MACjB,CAAC,MACI;QACDJ,OAAO,CAACK,aAAa,CAACG,CAAC,GAAG,CAAC,CAAC;QAC5BR,OAAO,CAACI,GAAG,EAAE;MACjB;IACJ;EACJ,CAAC;EACDR,YAAY,CAACC,SAAS,CAACS,iBAAiB,GAAG,UAAUS,MAAM,EAAEC,MAAM,EAAE;IACjE,IAAIrB,gBAAgB,CAACsB,OAAO,CAACF,MAAM,CAAC,IAAIpB,gBAAgB,CAACsB,OAAO,CAACD,MAAM,CAAC,EAAE;MACtE,IAAIE,GAAG,GAAG,CAACH,MAAM,GAAG,EAAE,IAAI,EAAE,IAAIC,MAAM,GAAG,EAAE,CAAC;MAC5C,OAAOE,GAAG,GAAG,GAAG;IACpB;IACA,MAAM,IAAIL,KAAK,CAAC,cAAc,GAAGE,MAAM,GAAGC,MAAM,CAAC;EACrD,CAAC;EACD,OAAOpB,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,SAASA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}