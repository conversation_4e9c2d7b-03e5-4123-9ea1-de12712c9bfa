{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _ITF2 = require('./ITF');\nvar _ITF3 = _interopRequireDefault(_ITF2);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\n\n// Calculate the checksum digit\nvar checksum = function checksum(data) {\n  var res = data.substr(0, 13).split('').map(function (num) {\n    return parseInt(num, 10);\n  }).reduce(function (sum, n, idx) {\n    return sum + n * (3 - idx % 2 * 2);\n  }, 0);\n  return Math.ceil(res / 10) * 10 - res;\n};\nvar ITF14 = function (_ITF) {\n  _inherits(ITF14, _ITF);\n  function ITF14(data, options) {\n    _classCallCheck(this, ITF14);\n\n    // Add checksum if it does not exist\n    if (data.search(/^[0-9]{13}$/) !== -1) {\n      data += checksum(data);\n    }\n    return _possibleConstructorReturn(this, (ITF14.__proto__ || Object.getPrototypeOf(ITF14)).call(this, data, options));\n  }\n  _createClass(ITF14, [{\n    key: 'valid',\n    value: function valid() {\n      return this.data.search(/^[0-9]{14}$/) !== -1 && +this.data[13] === checksum(this.data);\n    }\n  }]);\n  return ITF14;\n}(_ITF3.default);\nexports.default = ITF14;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_ITF2", "require", "_ITF3", "_interopRequireDefault", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "call", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "checksum", "data", "res", "substr", "split", "map", "num", "parseInt", "reduce", "sum", "n", "idx", "Math", "ceil", "ITF14", "_ITF", "options", "search", "getPrototypeOf", "valid"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/ITF/ITF14.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _ITF2 = require('./ITF');\n\nvar _ITF3 = _interopRequireDefault(_ITF2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n// Calculate the checksum digit\nvar checksum = function checksum(data) {\n\tvar res = data.substr(0, 13).split('').map(function (num) {\n\t\treturn parseInt(num, 10);\n\t}).reduce(function (sum, n, idx) {\n\t\treturn sum + n * (3 - idx % 2 * 2);\n\t}, 0);\n\n\treturn Math.ceil(res / 10) * 10 - res;\n};\n\nvar ITF14 = function (_ITF) {\n\t_inherits(ITF14, _ITF);\n\n\tfunction ITF14(data, options) {\n\t\t_classCallCheck(this, ITF14);\n\n\t\t// Add checksum if it does not exist\n\t\tif (data.search(/^[0-9]{13}$/) !== -1) {\n\t\t\tdata += checksum(data);\n\t\t}\n\t\treturn _possibleConstructorReturn(this, (ITF14.__proto__ || Object.getPrototypeOf(ITF14)).call(this, data, options));\n\t}\n\n\t_createClass(ITF14, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{14}$/) !== -1 && +this.data[13] === checksum(this.data);\n\t\t}\n\t}]);\n\n\treturn ITF14;\n}(_ITF3.default);\n\nexports.default = ITF14;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,KAAK,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE5B,IAAIC,KAAK,GAAGC,sBAAsB,CAACH,KAAK,CAAC;AAEzC,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEZ,WAAW,EAAE;EAAE,IAAI,EAAEY,QAAQ,YAAYZ,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIa,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAI,CAACD,IAAI,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGD,IAAI;AAAE;AAE/O,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIP,SAAS,CAAC,0DAA0D,GAAG,OAAOO,UAAU,CAAC;EAAE;EAAED,QAAQ,CAAChB,SAAS,GAAGlB,MAAM,CAACoC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACjB,SAAS,EAAE;IAAEmB,WAAW,EAAE;MAAElC,KAAK,EAAE+B,QAAQ;MAAEvB,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIuB,UAAU,EAAEnC,MAAM,CAACsC,cAAc,GAAGtC,MAAM,CAACsC,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AAAE;;AAE7e;AACA,IAAIK,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtC,IAAIC,GAAG,GAAGD,IAAI,CAACE,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;IACzD,OAAOC,QAAQ,CAACD,GAAG,EAAE,EAAE,CAAC;EACzB,CAAC,CAAC,CAACE,MAAM,CAAC,UAAUC,GAAG,EAAEC,CAAC,EAAEC,GAAG,EAAE;IAChC,OAAOF,GAAG,GAAGC,CAAC,IAAI,CAAC,GAAGC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;EACnC,CAAC,EAAE,CAAC,CAAC;EAEL,OAAOC,IAAI,CAACC,IAAI,CAACX,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAGA,GAAG;AACtC,CAAC;AAED,IAAIY,KAAK,GAAG,UAAUC,IAAI,EAAE;EAC3BtB,SAAS,CAACqB,KAAK,EAAEC,IAAI,CAAC;EAEtB,SAASD,KAAKA,CAACb,IAAI,EAAEe,OAAO,EAAE;IAC7B9B,eAAe,CAAC,IAAI,EAAE4B,KAAK,CAAC;;IAE5B;IACA,IAAIb,IAAI,CAACgB,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE;MACtChB,IAAI,IAAID,QAAQ,CAACC,IAAI,CAAC;IACvB;IACA,OAAOZ,0BAA0B,CAAC,IAAI,EAAE,CAACyB,KAAK,CAACf,SAAS,IAAIvC,MAAM,CAAC0D,cAAc,CAACJ,KAAK,CAAC,EAAEvB,IAAI,CAAC,IAAI,EAAEU,IAAI,EAAEe,OAAO,CAAC,CAAC;EACrH;EAEApD,YAAY,CAACkD,KAAK,EAAE,CAAC;IACpBxC,GAAG,EAAE,OAAO;IACZX,KAAK,EAAE,SAASwD,KAAKA,CAAA,EAAG;MACvB,OAAO,IAAI,CAAClB,IAAI,CAACgB,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAChB,IAAI,CAAC,EAAE,CAAC,KAAKD,QAAQ,CAAC,IAAI,CAACC,IAAI,CAAC;IACxF;EACD,CAAC,CAAC,CAAC;EAEH,OAAOa,KAAK;AACb,CAAC,CAACjC,KAAK,CAACI,OAAO,CAAC;AAEhBvB,OAAO,CAACuB,OAAO,GAAG6B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}