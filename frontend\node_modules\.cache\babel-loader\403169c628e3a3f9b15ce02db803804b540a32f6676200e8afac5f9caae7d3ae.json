{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import java.util.Collection;\n// import java.util.Collections;\nimport Collections from '../../util/Collections';\n// import java.util.Comparator;\n// import java.util.Iterator;\n// import java.util.LinkedList;\nimport State from './State';\nimport * as C from './EncoderConstants';\nimport * as CharMap from './CharMap';\nimport * as ShiftTable from './ShiftTable';\nimport StringUtils from '../../common/StringUtils';\n/**\n * This produces nearly optimal encodings of text into the first-level of\n * encoding used by Aztec code.\n *\n * It uses a dynamic algorithm.  For each prefix of the string, it determines\n * a set of encodings that could lead to this prefix.  We repeatedly add a\n * character and generate a new set of optimal encodings until we have read\n * through the entire input.\n *\n * <AUTHOR> Yellin\n * <AUTHOR> Abdullaev\n */\nvar HighLevelEncoder = /** @class */function () {\n  function HighLevelEncoder(text) {\n    this.text = text;\n  }\n  /**\n   * @return text represented by this encoder encoded as a {@link BitArray}\n   */\n  HighLevelEncoder.prototype.encode = function () {\n    var spaceCharCode = StringUtils.getCharCode(' ');\n    var lineBreakCharCode = StringUtils.getCharCode('\\n');\n    var states = Collections.singletonList(State.INITIAL_STATE);\n    for (var index = 0; index < this.text.length; index++) {\n      var pairCode = void 0;\n      var nextChar = index + 1 < this.text.length ? this.text[index + 1] : 0;\n      switch (this.text[index]) {\n        case StringUtils.getCharCode('\\r'):\n          pairCode = nextChar === lineBreakCharCode ? 2 : 0;\n          break;\n        case StringUtils.getCharCode('.'):\n          pairCode = nextChar === spaceCharCode ? 3 : 0;\n          break;\n        case StringUtils.getCharCode(','):\n          pairCode = nextChar === spaceCharCode ? 4 : 0;\n          break;\n        case StringUtils.getCharCode(':'):\n          pairCode = nextChar === spaceCharCode ? 5 : 0;\n          break;\n        default:\n          pairCode = 0;\n      }\n      if (pairCode > 0) {\n        // We have one of the four special PUNCT pairs.  Treat them specially.\n        // Get a new set of states for the two new characters.\n        states = HighLevelEncoder.updateStateListForPair(states, index, pairCode);\n        index++;\n      } else {\n        // Get a new set of states for the new character.\n        states = this.updateStateListForChar(states, index);\n      }\n    }\n    // We are left with a set of states.  Find the shortest one.\n    var minState = Collections.min(states, function (a, b) {\n      return a.getBitCount() - b.getBitCount();\n    });\n    // Convert it to a bit array, and return.\n    return minState.toBitArray(this.text);\n  };\n  // We update a set of states for a new character by updating each state\n  // for the new character, merging the results, and then removing the\n  // non-optimal states.\n  HighLevelEncoder.prototype.updateStateListForChar = function (states, index) {\n    var e_1, _a;\n    var result = [];\n    try {\n      for (var states_1 = __values(states), states_1_1 = states_1.next(); !states_1_1.done; states_1_1 = states_1.next()) {\n        var state = states_1_1.value /*State*/;\n        this.updateStateForChar(state, index, result);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (states_1_1 && !states_1_1.done && (_a = states_1.return)) _a.call(states_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    return HighLevelEncoder.simplifyStates(result);\n  };\n  // Return a set of states that represent the possible ways of updating this\n  // state for the next character.  The resulting set of states are added to\n  // the \"result\" list.\n  HighLevelEncoder.prototype.updateStateForChar = function (state, index, result) {\n    var ch = this.text[index] & 0xff;\n    var charInCurrentTable = CharMap.CHAR_MAP[state.getMode()][ch] > 0;\n    var stateNoBinary = null;\n    for (var mode /*int*/ = 0; mode <= C.MODE_PUNCT; mode++) {\n      var charInMode = CharMap.CHAR_MAP[mode][ch];\n      if (charInMode > 0) {\n        if (stateNoBinary == null) {\n          // Only create stateNoBinary the first time it's required.\n          stateNoBinary = state.endBinaryShift(index);\n        }\n        // Try generating the character by latching to its mode\n        if (!charInCurrentTable || mode === state.getMode() || mode === C.MODE_DIGIT) {\n          // If the character is in the current table, we don't want to latch to\n          // any other mode except possibly digit (which uses only 4 bits).  Any\n          // other latch would be equally successful *after* this character, and\n          // so wouldn't save any bits.\n          var latchState = stateNoBinary.latchAndAppend(mode, charInMode);\n          result.push(latchState);\n        }\n        // Try generating the character by switching to its mode.\n        if (!charInCurrentTable && ShiftTable.SHIFT_TABLE[state.getMode()][mode] >= 0) {\n          // It never makes sense to temporarily shift to another mode if the\n          // character exists in the current mode.  That can never save bits.\n          var shiftState = stateNoBinary.shiftAndAppend(mode, charInMode);\n          result.push(shiftState);\n        }\n      }\n    }\n    if (state.getBinaryShiftByteCount() > 0 || CharMap.CHAR_MAP[state.getMode()][ch] === 0) {\n      // It's never worthwhile to go into binary shift mode if you're not already\n      // in binary shift mode, and the character exists in your current mode.\n      // That can never save bits over just outputting the char in the current mode.\n      var binaryState = state.addBinaryShiftChar(index);\n      result.push(binaryState);\n    }\n  };\n  HighLevelEncoder.updateStateListForPair = function (states, index, pairCode) {\n    var e_2, _a;\n    var result = [];\n    try {\n      for (var states_2 = __values(states), states_2_1 = states_2.next(); !states_2_1.done; states_2_1 = states_2.next()) {\n        var state = states_2_1.value /*State*/;\n        this.updateStateForPair(state, index, pairCode, result);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (states_2_1 && !states_2_1.done && (_a = states_2.return)) _a.call(states_2);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return this.simplifyStates(result);\n  };\n  HighLevelEncoder.updateStateForPair = function (state, index, pairCode, result) {\n    var stateNoBinary = state.endBinaryShift(index);\n    // Possibility 1.  Latch to C.MODE_PUNCT, and then append this code\n    result.push(stateNoBinary.latchAndAppend(C.MODE_PUNCT, pairCode));\n    if (state.getMode() !== C.MODE_PUNCT) {\n      // Possibility 2.  Shift to C.MODE_PUNCT, and then append this code.\n      // Every state except C.MODE_PUNCT (handled above) can shift\n      result.push(stateNoBinary.shiftAndAppend(C.MODE_PUNCT, pairCode));\n    }\n    if (pairCode === 3 || pairCode === 4) {\n      // both characters are in DIGITS.  Sometimes better to just add two digits\n      var digitState = stateNoBinary.latchAndAppend(C.MODE_DIGIT, 16 - pairCode) // period or comma in DIGIT\n      .latchAndAppend(C.MODE_DIGIT, 1); // space in DIGIT\n      result.push(digitState);\n    }\n    if (state.getBinaryShiftByteCount() > 0) {\n      // It only makes sense to do the characters as binary if we're already\n      // in binary mode.\n      var binaryState = state.addBinaryShiftChar(index).addBinaryShiftChar(index + 1);\n      result.push(binaryState);\n    }\n  };\n  HighLevelEncoder.simplifyStates = function (states) {\n    var e_3, _a, e_4, _b;\n    var result = [];\n    try {\n      for (var states_3 = __values(states), states_3_1 = states_3.next(); !states_3_1.done; states_3_1 = states_3.next()) {\n        var newState = states_3_1.value;\n        var add = true;\n        var _loop_1 = function (oldState) {\n          if (oldState.isBetterThanOrEqualTo(newState)) {\n            add = false;\n            return \"break\";\n          }\n          if (newState.isBetterThanOrEqualTo(oldState)) {\n            // iterator.remove();\n            result = result.filter(function (x) {\n              return x !== oldState;\n            }); // remove old state\n          }\n        };\n        try {\n          for (var result_1 = (e_4 = void 0, __values(result)), result_1_1 = result_1.next(); !result_1_1.done; result_1_1 = result_1.next()) {\n            var oldState = result_1_1.value;\n            var state_1 = _loop_1(oldState);\n            if (state_1 === \"break\") break;\n          }\n        } catch (e_4_1) {\n          e_4 = {\n            error: e_4_1\n          };\n        } finally {\n          try {\n            if (result_1_1 && !result_1_1.done && (_b = result_1.return)) _b.call(result_1);\n          } finally {\n            if (e_4) throw e_4.error;\n          }\n        }\n        if (add) {\n          result.push(newState);\n        }\n      }\n    } catch (e_3_1) {\n      e_3 = {\n        error: e_3_1\n      };\n    } finally {\n      try {\n        if (states_3_1 && !states_3_1.done && (_a = states_3.return)) _a.call(states_3);\n      } finally {\n        if (e_3) throw e_3.error;\n      }\n    }\n    return result;\n  };\n  return HighLevelEncoder;\n}();\nexport default HighLevelEncoder;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "Collections", "State", "C", "CharMap", "ShiftTable", "StringUtils", "HighLevelEncoder", "text", "prototype", "encode", "spaceCharCode", "getCharCode", "lineBreakCharCode", "states", "singletonList", "INITIAL_STATE", "index", "pairCode", "nextChar", "updateStateListForPair", "updateStateListForChar", "minState", "min", "a", "b", "getBitCount", "toBitArray", "e_1", "_a", "result", "states_1", "states_1_1", "state", "updateStateForChar", "e_1_1", "error", "return", "simplifyStates", "ch", "charInCurrentTable", "CHAR_MAP", "getMode", "stateNoBinary", "mode", "MODE_PUNCT", "charInMode", "endBinaryShift", "MODE_DIGIT", "latchState", "latchAndAppend", "push", "SHIFT_TABLE", "shiftState", "shiftAndAppend", "getBinaryShiftByteCount", "binaryState", "addBinaryShiftChar", "e_2", "states_2", "states_2_1", "updateStateForPair", "e_2_1", "digitState", "e_3", "e_4", "_b", "states_3", "states_3_1", "newState", "add", "_loop_1", "oldState", "isBetterThanOrEqualTo", "filter", "x", "result_1", "result_1_1", "state_1", "e_4_1", "e_3_1"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/HighLevelEncoder.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import java.util.Collection;\n// import java.util.Collections;\nimport Collections from '../../util/Collections';\n// import java.util.Comparator;\n// import java.util.Iterator;\n// import java.util.LinkedList;\nimport State from './State';\nimport * as C from './EncoderConstants';\nimport * as CharMap from './CharMap';\nimport * as ShiftTable from './ShiftTable';\nimport StringUtils from '../../common/StringUtils';\n/**\n * This produces nearly optimal encodings of text into the first-level of\n * encoding used by Aztec code.\n *\n * It uses a dynamic algorithm.  For each prefix of the string, it determines\n * a set of encodings that could lead to this prefix.  We repeatedly add a\n * character and generate a new set of optimal encodings until we have read\n * through the entire input.\n *\n * <AUTHOR> Yellin\n * <AUTHOR> Abdullaev\n */\nvar HighLevelEncoder = /** @class */ (function () {\n    function HighLevelEncoder(text) {\n        this.text = text;\n    }\n    /**\n     * @return text represented by this encoder encoded as a {@link BitArray}\n     */\n    HighLevelEncoder.prototype.encode = function () {\n        var spaceCharCode = StringUtils.getCharCode(' ');\n        var lineBreakCharCode = StringUtils.getCharCode('\\n');\n        var states = Collections.singletonList(State.INITIAL_STATE);\n        for (var index = 0; index < this.text.length; index++) {\n            var pairCode = void 0;\n            var nextChar = index + 1 < this.text.length ? this.text[index + 1] : 0;\n            switch (this.text[index]) {\n                case StringUtils.getCharCode('\\r'):\n                    pairCode = nextChar === lineBreakCharCode ? 2 : 0;\n                    break;\n                case StringUtils.getCharCode('.'):\n                    pairCode = nextChar === spaceCharCode ? 3 : 0;\n                    break;\n                case StringUtils.getCharCode(','):\n                    pairCode = nextChar === spaceCharCode ? 4 : 0;\n                    break;\n                case StringUtils.getCharCode(':'):\n                    pairCode = nextChar === spaceCharCode ? 5 : 0;\n                    break;\n                default:\n                    pairCode = 0;\n            }\n            if (pairCode > 0) {\n                // We have one of the four special PUNCT pairs.  Treat them specially.\n                // Get a new set of states for the two new characters.\n                states = HighLevelEncoder.updateStateListForPair(states, index, pairCode);\n                index++;\n            }\n            else {\n                // Get a new set of states for the new character.\n                states = this.updateStateListForChar(states, index);\n            }\n        }\n        // We are left with a set of states.  Find the shortest one.\n        var minState = Collections.min(states, function (a, b) {\n            return a.getBitCount() - b.getBitCount();\n        });\n        // Convert it to a bit array, and return.\n        return minState.toBitArray(this.text);\n    };\n    // We update a set of states for a new character by updating each state\n    // for the new character, merging the results, and then removing the\n    // non-optimal states.\n    HighLevelEncoder.prototype.updateStateListForChar = function (states, index) {\n        var e_1, _a;\n        var result = [];\n        try {\n            for (var states_1 = __values(states), states_1_1 = states_1.next(); !states_1_1.done; states_1_1 = states_1.next()) {\n                var state = states_1_1.value /*State*/;\n                this.updateStateForChar(state, index, result);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (states_1_1 && !states_1_1.done && (_a = states_1.return)) _a.call(states_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return HighLevelEncoder.simplifyStates(result);\n    };\n    // Return a set of states that represent the possible ways of updating this\n    // state for the next character.  The resulting set of states are added to\n    // the \"result\" list.\n    HighLevelEncoder.prototype.updateStateForChar = function (state, index, result) {\n        var ch = (this.text[index] & 0xff);\n        var charInCurrentTable = CharMap.CHAR_MAP[state.getMode()][ch] > 0;\n        var stateNoBinary = null;\n        for (var mode /*int*/ = 0; mode <= C.MODE_PUNCT; mode++) {\n            var charInMode = CharMap.CHAR_MAP[mode][ch];\n            if (charInMode > 0) {\n                if (stateNoBinary == null) {\n                    // Only create stateNoBinary the first time it's required.\n                    stateNoBinary = state.endBinaryShift(index);\n                }\n                // Try generating the character by latching to its mode\n                if (!charInCurrentTable ||\n                    mode === state.getMode() ||\n                    mode === C.MODE_DIGIT) {\n                    // If the character is in the current table, we don't want to latch to\n                    // any other mode except possibly digit (which uses only 4 bits).  Any\n                    // other latch would be equally successful *after* this character, and\n                    // so wouldn't save any bits.\n                    var latchState = stateNoBinary.latchAndAppend(mode, charInMode);\n                    result.push(latchState);\n                }\n                // Try generating the character by switching to its mode.\n                if (!charInCurrentTable &&\n                    ShiftTable.SHIFT_TABLE[state.getMode()][mode] >= 0) {\n                    // It never makes sense to temporarily shift to another mode if the\n                    // character exists in the current mode.  That can never save bits.\n                    var shiftState = stateNoBinary.shiftAndAppend(mode, charInMode);\n                    result.push(shiftState);\n                }\n            }\n        }\n        if (state.getBinaryShiftByteCount() > 0 ||\n            CharMap.CHAR_MAP[state.getMode()][ch] === 0) {\n            // It's never worthwhile to go into binary shift mode if you're not already\n            // in binary shift mode, and the character exists in your current mode.\n            // That can never save bits over just outputting the char in the current mode.\n            var binaryState = state.addBinaryShiftChar(index);\n            result.push(binaryState);\n        }\n    };\n    HighLevelEncoder.updateStateListForPair = function (states, index, pairCode) {\n        var e_2, _a;\n        var result = [];\n        try {\n            for (var states_2 = __values(states), states_2_1 = states_2.next(); !states_2_1.done; states_2_1 = states_2.next()) {\n                var state = states_2_1.value /*State*/;\n                this.updateStateForPair(state, index, pairCode, result);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (states_2_1 && !states_2_1.done && (_a = states_2.return)) _a.call(states_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return this.simplifyStates(result);\n    };\n    HighLevelEncoder.updateStateForPair = function (state, index, pairCode, result) {\n        var stateNoBinary = state.endBinaryShift(index);\n        // Possibility 1.  Latch to C.MODE_PUNCT, and then append this code\n        result.push(stateNoBinary.latchAndAppend(C.MODE_PUNCT, pairCode));\n        if (state.getMode() !== C.MODE_PUNCT) {\n            // Possibility 2.  Shift to C.MODE_PUNCT, and then append this code.\n            // Every state except C.MODE_PUNCT (handled above) can shift\n            result.push(stateNoBinary.shiftAndAppend(C.MODE_PUNCT, pairCode));\n        }\n        if (pairCode === 3 || pairCode === 4) {\n            // both characters are in DIGITS.  Sometimes better to just add two digits\n            var digitState = stateNoBinary\n                .latchAndAppend(C.MODE_DIGIT, 16 - pairCode) // period or comma in DIGIT\n                .latchAndAppend(C.MODE_DIGIT, 1); // space in DIGIT\n            result.push(digitState);\n        }\n        if (state.getBinaryShiftByteCount() > 0) {\n            // It only makes sense to do the characters as binary if we're already\n            // in binary mode.\n            var binaryState = state\n                .addBinaryShiftChar(index)\n                .addBinaryShiftChar(index + 1);\n            result.push(binaryState);\n        }\n    };\n    HighLevelEncoder.simplifyStates = function (states) {\n        var e_3, _a, e_4, _b;\n        var result = [];\n        try {\n            for (var states_3 = __values(states), states_3_1 = states_3.next(); !states_3_1.done; states_3_1 = states_3.next()) {\n                var newState = states_3_1.value;\n                var add = true;\n                var _loop_1 = function (oldState) {\n                    if (oldState.isBetterThanOrEqualTo(newState)) {\n                        add = false;\n                        return \"break\";\n                    }\n                    if (newState.isBetterThanOrEqualTo(oldState)) {\n                        // iterator.remove();\n                        result = result.filter(function (x) { return x !== oldState; }); // remove old state\n                    }\n                };\n                try {\n                    for (var result_1 = (e_4 = void 0, __values(result)), result_1_1 = result_1.next(); !result_1_1.done; result_1_1 = result_1.next()) {\n                        var oldState = result_1_1.value;\n                        var state_1 = _loop_1(oldState);\n                        if (state_1 === \"break\")\n                            break;\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (result_1_1 && !result_1_1.done && (_b = result_1.return)) _b.call(result_1);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n                if (add) {\n                    result.push(newState);\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (states_3_1 && !states_3_1.done && (_a = states_3.return)) _a.call(states_3);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return result;\n    };\n    return HighLevelEncoder;\n}());\nexport default HighLevelEncoder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA,OAAOW,WAAW,MAAM,wBAAwB;AAChD;AACA;AACA;AACA,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAO,KAAKC,CAAC,MAAM,oBAAoB;AACvC,OAAO,KAAKC,OAAO,MAAM,WAAW;AACpC,OAAO,KAAKC,UAAU,MAAM,cAAc;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAe,YAAY;EAC9C,SAASA,gBAAgBA,CAACC,IAAI,EAAE;IAC5B,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACID,gBAAgB,CAACE,SAAS,CAACC,MAAM,GAAG,YAAY;IAC5C,IAAIC,aAAa,GAAGL,WAAW,CAACM,WAAW,CAAC,GAAG,CAAC;IAChD,IAAIC,iBAAiB,GAAGP,WAAW,CAACM,WAAW,CAAC,IAAI,CAAC;IACrD,IAAIE,MAAM,GAAGb,WAAW,CAACc,aAAa,CAACb,KAAK,CAACc,aAAa,CAAC;IAC3D,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACT,IAAI,CAACZ,MAAM,EAAEqB,KAAK,EAAE,EAAE;MACnD,IAAIC,QAAQ,GAAG,KAAK,CAAC;MACrB,IAAIC,QAAQ,GAAGF,KAAK,GAAG,CAAC,GAAG,IAAI,CAACT,IAAI,CAACZ,MAAM,GAAG,IAAI,CAACY,IAAI,CAACS,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MACtE,QAAQ,IAAI,CAACT,IAAI,CAACS,KAAK,CAAC;QACpB,KAAKX,WAAW,CAACM,WAAW,CAAC,IAAI,CAAC;UAC9BM,QAAQ,GAAGC,QAAQ,KAAKN,iBAAiB,GAAG,CAAC,GAAG,CAAC;UACjD;QACJ,KAAKP,WAAW,CAACM,WAAW,CAAC,GAAG,CAAC;UAC7BM,QAAQ,GAAGC,QAAQ,KAAKR,aAAa,GAAG,CAAC,GAAG,CAAC;UAC7C;QACJ,KAAKL,WAAW,CAACM,WAAW,CAAC,GAAG,CAAC;UAC7BM,QAAQ,GAAGC,QAAQ,KAAKR,aAAa,GAAG,CAAC,GAAG,CAAC;UAC7C;QACJ,KAAKL,WAAW,CAACM,WAAW,CAAC,GAAG,CAAC;UAC7BM,QAAQ,GAAGC,QAAQ,KAAKR,aAAa,GAAG,CAAC,GAAG,CAAC;UAC7C;QACJ;UACIO,QAAQ,GAAG,CAAC;MACpB;MACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;QACd;QACA;QACAJ,MAAM,GAAGP,gBAAgB,CAACa,sBAAsB,CAACN,MAAM,EAAEG,KAAK,EAAEC,QAAQ,CAAC;QACzED,KAAK,EAAE;MACX,CAAC,MACI;QACD;QACAH,MAAM,GAAG,IAAI,CAACO,sBAAsB,CAACP,MAAM,EAAEG,KAAK,CAAC;MACvD;IACJ;IACA;IACA,IAAIK,QAAQ,GAAGrB,WAAW,CAACsB,GAAG,CAACT,MAAM,EAAE,UAAUU,CAAC,EAAEC,CAAC,EAAE;MACnD,OAAOD,CAAC,CAACE,WAAW,CAAC,CAAC,GAAGD,CAAC,CAACC,WAAW,CAAC,CAAC;IAC5C,CAAC,CAAC;IACF;IACA,OAAOJ,QAAQ,CAACK,UAAU,CAAC,IAAI,CAACnB,IAAI,CAAC;EACzC,CAAC;EACD;EACA;EACA;EACAD,gBAAgB,CAACE,SAAS,CAACY,sBAAsB,GAAG,UAAUP,MAAM,EAAEG,KAAK,EAAE;IACzE,IAAIW,GAAG,EAAEC,EAAE;IACX,IAAIC,MAAM,GAAG,EAAE;IACf,IAAI;MACA,KAAK,IAAIC,QAAQ,GAAG3C,QAAQ,CAAC0B,MAAM,CAAC,EAAEkB,UAAU,GAAGD,QAAQ,CAAClC,IAAI,CAAC,CAAC,EAAE,CAACmC,UAAU,CAACjC,IAAI,EAAEiC,UAAU,GAAGD,QAAQ,CAAClC,IAAI,CAAC,CAAC,EAAE;QAChH,IAAIoC,KAAK,GAAGD,UAAU,CAAClC,KAAK,CAAC;QAC7B,IAAI,CAACoC,kBAAkB,CAACD,KAAK,EAAEhB,KAAK,EAAEa,MAAM,CAAC;MACjD;IACJ,CAAC,CACD,OAAOK,KAAK,EAAE;MAAEP,GAAG,GAAG;QAAEQ,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,UAAU,IAAI,CAACA,UAAU,CAACjC,IAAI,KAAK8B,EAAE,GAAGE,QAAQ,CAACM,MAAM,CAAC,EAAER,EAAE,CAAClC,IAAI,CAACoC,QAAQ,CAAC;MACnF,CAAC,SACO;QAAE,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAACQ,KAAK;MAAE;IACxC;IACA,OAAO7B,gBAAgB,CAAC+B,cAAc,CAACR,MAAM,CAAC;EAClD,CAAC;EACD;EACA;EACA;EACAvB,gBAAgB,CAACE,SAAS,CAACyB,kBAAkB,GAAG,UAAUD,KAAK,EAAEhB,KAAK,EAAEa,MAAM,EAAE;IAC5E,IAAIS,EAAE,GAAI,IAAI,CAAC/B,IAAI,CAACS,KAAK,CAAC,GAAG,IAAK;IAClC,IAAIuB,kBAAkB,GAAGpC,OAAO,CAACqC,QAAQ,CAACR,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,EAAE,CAAC,GAAG,CAAC;IAClE,IAAII,aAAa,GAAG,IAAI;IACxB,KAAK,IAAIC,IAAI,CAAC,UAAU,CAAC,EAAEA,IAAI,IAAIzC,CAAC,CAAC0C,UAAU,EAAED,IAAI,EAAE,EAAE;MACrD,IAAIE,UAAU,GAAG1C,OAAO,CAACqC,QAAQ,CAACG,IAAI,CAAC,CAACL,EAAE,CAAC;MAC3C,IAAIO,UAAU,GAAG,CAAC,EAAE;QAChB,IAAIH,aAAa,IAAI,IAAI,EAAE;UACvB;UACAA,aAAa,GAAGV,KAAK,CAACc,cAAc,CAAC9B,KAAK,CAAC;QAC/C;QACA;QACA,IAAI,CAACuB,kBAAkB,IACnBI,IAAI,KAAKX,KAAK,CAACS,OAAO,CAAC,CAAC,IACxBE,IAAI,KAAKzC,CAAC,CAAC6C,UAAU,EAAE;UACvB;UACA;UACA;UACA;UACA,IAAIC,UAAU,GAAGN,aAAa,CAACO,cAAc,CAACN,IAAI,EAAEE,UAAU,CAAC;UAC/DhB,MAAM,CAACqB,IAAI,CAACF,UAAU,CAAC;QAC3B;QACA;QACA,IAAI,CAACT,kBAAkB,IACnBnC,UAAU,CAAC+C,WAAW,CAACnB,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE;UACpD;UACA;UACA,IAAIS,UAAU,GAAGV,aAAa,CAACW,cAAc,CAACV,IAAI,EAAEE,UAAU,CAAC;UAC/DhB,MAAM,CAACqB,IAAI,CAACE,UAAU,CAAC;QAC3B;MACJ;IACJ;IACA,IAAIpB,KAAK,CAACsB,uBAAuB,CAAC,CAAC,GAAG,CAAC,IACnCnD,OAAO,CAACqC,QAAQ,CAACR,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC,CAACH,EAAE,CAAC,KAAK,CAAC,EAAE;MAC7C;MACA;MACA;MACA,IAAIiB,WAAW,GAAGvB,KAAK,CAACwB,kBAAkB,CAACxC,KAAK,CAAC;MACjDa,MAAM,CAACqB,IAAI,CAACK,WAAW,CAAC;IAC5B;EACJ,CAAC;EACDjD,gBAAgB,CAACa,sBAAsB,GAAG,UAAUN,MAAM,EAAEG,KAAK,EAAEC,QAAQ,EAAE;IACzE,IAAIwC,GAAG,EAAE7B,EAAE;IACX,IAAIC,MAAM,GAAG,EAAE;IACf,IAAI;MACA,KAAK,IAAI6B,QAAQ,GAAGvE,QAAQ,CAAC0B,MAAM,CAAC,EAAE8C,UAAU,GAAGD,QAAQ,CAAC9D,IAAI,CAAC,CAAC,EAAE,CAAC+D,UAAU,CAAC7D,IAAI,EAAE6D,UAAU,GAAGD,QAAQ,CAAC9D,IAAI,CAAC,CAAC,EAAE;QAChH,IAAIoC,KAAK,GAAG2B,UAAU,CAAC9D,KAAK,CAAC;QAC7B,IAAI,CAAC+D,kBAAkB,CAAC5B,KAAK,EAAEhB,KAAK,EAAEC,QAAQ,EAAEY,MAAM,CAAC;MAC3D;IACJ,CAAC,CACD,OAAOgC,KAAK,EAAE;MAAEJ,GAAG,GAAG;QAAEtB,KAAK,EAAE0B;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,UAAU,IAAI,CAACA,UAAU,CAAC7D,IAAI,KAAK8B,EAAE,GAAG8B,QAAQ,CAACtB,MAAM,CAAC,EAAER,EAAE,CAAClC,IAAI,CAACgE,QAAQ,CAAC;MACnF,CAAC,SACO;QAAE,IAAID,GAAG,EAAE,MAAMA,GAAG,CAACtB,KAAK;MAAE;IACxC;IACA,OAAO,IAAI,CAACE,cAAc,CAACR,MAAM,CAAC;EACtC,CAAC;EACDvB,gBAAgB,CAACsD,kBAAkB,GAAG,UAAU5B,KAAK,EAAEhB,KAAK,EAAEC,QAAQ,EAAEY,MAAM,EAAE;IAC5E,IAAIa,aAAa,GAAGV,KAAK,CAACc,cAAc,CAAC9B,KAAK,CAAC;IAC/C;IACAa,MAAM,CAACqB,IAAI,CAACR,aAAa,CAACO,cAAc,CAAC/C,CAAC,CAAC0C,UAAU,EAAE3B,QAAQ,CAAC,CAAC;IACjE,IAAIe,KAAK,CAACS,OAAO,CAAC,CAAC,KAAKvC,CAAC,CAAC0C,UAAU,EAAE;MAClC;MACA;MACAf,MAAM,CAACqB,IAAI,CAACR,aAAa,CAACW,cAAc,CAACnD,CAAC,CAAC0C,UAAU,EAAE3B,QAAQ,CAAC,CAAC;IACrE;IACA,IAAIA,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClC;MACA,IAAI6C,UAAU,GAAGpB,aAAa,CACzBO,cAAc,CAAC/C,CAAC,CAAC6C,UAAU,EAAE,EAAE,GAAG9B,QAAQ,CAAC,CAAC;MAAA,CAC5CgC,cAAc,CAAC/C,CAAC,CAAC6C,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;MACtClB,MAAM,CAACqB,IAAI,CAACY,UAAU,CAAC;IAC3B;IACA,IAAI9B,KAAK,CAACsB,uBAAuB,CAAC,CAAC,GAAG,CAAC,EAAE;MACrC;MACA;MACA,IAAIC,WAAW,GAAGvB,KAAK,CAClBwB,kBAAkB,CAACxC,KAAK,CAAC,CACzBwC,kBAAkB,CAACxC,KAAK,GAAG,CAAC,CAAC;MAClCa,MAAM,CAACqB,IAAI,CAACK,WAAW,CAAC;IAC5B;EACJ,CAAC;EACDjD,gBAAgB,CAAC+B,cAAc,GAAG,UAAUxB,MAAM,EAAE;IAChD,IAAIkD,GAAG,EAAEnC,EAAE,EAAEoC,GAAG,EAAEC,EAAE;IACpB,IAAIpC,MAAM,GAAG,EAAE;IACf,IAAI;MACA,KAAK,IAAIqC,QAAQ,GAAG/E,QAAQ,CAAC0B,MAAM,CAAC,EAAEsD,UAAU,GAAGD,QAAQ,CAACtE,IAAI,CAAC,CAAC,EAAE,CAACuE,UAAU,CAACrE,IAAI,EAAEqE,UAAU,GAAGD,QAAQ,CAACtE,IAAI,CAAC,CAAC,EAAE;QAChH,IAAIwE,QAAQ,GAAGD,UAAU,CAACtE,KAAK;QAC/B,IAAIwE,GAAG,GAAG,IAAI;QACd,IAAIC,OAAO,GAAG,SAAAA,CAAUC,QAAQ,EAAE;UAC9B,IAAIA,QAAQ,CAACC,qBAAqB,CAACJ,QAAQ,CAAC,EAAE;YAC1CC,GAAG,GAAG,KAAK;YACX,OAAO,OAAO;UAClB;UACA,IAAID,QAAQ,CAACI,qBAAqB,CAACD,QAAQ,CAAC,EAAE;YAC1C;YACA1C,MAAM,GAAGA,MAAM,CAAC4C,MAAM,CAAC,UAAUC,CAAC,EAAE;cAAE,OAAOA,CAAC,KAAKH,QAAQ;YAAE,CAAC,CAAC,CAAC,CAAC;UACrE;QACJ,CAAC;QACD,IAAI;UACA,KAAK,IAAII,QAAQ,IAAIX,GAAG,GAAG,KAAK,CAAC,EAAE7E,QAAQ,CAAC0C,MAAM,CAAC,CAAC,EAAE+C,UAAU,GAAGD,QAAQ,CAAC/E,IAAI,CAAC,CAAC,EAAE,CAACgF,UAAU,CAAC9E,IAAI,EAAE8E,UAAU,GAAGD,QAAQ,CAAC/E,IAAI,CAAC,CAAC,EAAE;YAChI,IAAI2E,QAAQ,GAAGK,UAAU,CAAC/E,KAAK;YAC/B,IAAIgF,OAAO,GAAGP,OAAO,CAACC,QAAQ,CAAC;YAC/B,IAAIM,OAAO,KAAK,OAAO,EACnB;UACR;QACJ,CAAC,CACD,OAAOC,KAAK,EAAE;UAAEd,GAAG,GAAG;YAAE7B,KAAK,EAAE2C;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAIF,UAAU,IAAI,CAACA,UAAU,CAAC9E,IAAI,KAAKmE,EAAE,GAAGU,QAAQ,CAACvC,MAAM,CAAC,EAAE6B,EAAE,CAACvE,IAAI,CAACiF,QAAQ,CAAC;UACnF,CAAC,SACO;YAAE,IAAIX,GAAG,EAAE,MAAMA,GAAG,CAAC7B,KAAK;UAAE;QACxC;QACA,IAAIkC,GAAG,EAAE;UACLxC,MAAM,CAACqB,IAAI,CAACkB,QAAQ,CAAC;QACzB;MACJ;IACJ,CAAC,CACD,OAAOW,KAAK,EAAE;MAAEhB,GAAG,GAAG;QAAE5B,KAAK,EAAE4C;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIZ,UAAU,IAAI,CAACA,UAAU,CAACrE,IAAI,KAAK8B,EAAE,GAAGsC,QAAQ,CAAC9B,MAAM,CAAC,EAAER,EAAE,CAAClC,IAAI,CAACwE,QAAQ,CAAC;MACnF,CAAC,SACO;QAAE,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAAC5B,KAAK;MAAE;IACxC;IACA,OAAON,MAAM;EACjB,CAAC;EACD,OAAOvB,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}