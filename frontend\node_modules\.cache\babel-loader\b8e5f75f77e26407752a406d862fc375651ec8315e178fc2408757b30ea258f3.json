{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport DecodedObject from './DecodedObject';\nvar DecodedInformation = /** @class */function (_super) {\n  __extends(DecodedInformation, _super);\n  function DecodedInformation(newPosition, newString, remainingValue) {\n    var _this = _super.call(this, newPosition) || this;\n    if (remainingValue) {\n      _this.remaining = true;\n      _this.remainingValue = _this.remainingValue;\n    } else {\n      _this.remaining = false;\n      _this.remainingValue = 0;\n    }\n    _this.newString = newString;\n    return _this;\n  }\n  DecodedInformation.prototype.getNewString = function () {\n    return this.newString;\n  };\n  DecodedInformation.prototype.isRemaining = function () {\n    return this.remaining;\n  };\n  DecodedInformation.prototype.getRemainingValue = function () {\n    return this.remainingValue;\n  };\n  return DecodedInformation;\n}(DecodedObject);\nexport default DecodedInformation;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "DecodedObject", "DecodedInformation", "_super", "newPosition", "newString", "remainingValue", "_this", "call", "remaining", "getNewString", "isRemaining", "getRemainingValue"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedInformation.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport DecodedObject from './DecodedObject';\nvar DecodedInformation = /** @class */ (function (_super) {\n    __extends(DecodedInformation, _super);\n    function DecodedInformation(newPosition, newString, remainingValue) {\n        var _this = _super.call(this, newPosition) || this;\n        if (remainingValue) {\n            _this.remaining = true;\n            _this.remainingValue = _this.remainingValue;\n        }\n        else {\n            _this.remaining = false;\n            _this.remainingValue = 0;\n        }\n        _this.newString = newString;\n        return _this;\n    }\n    DecodedInformation.prototype.getNewString = function () {\n        return this.newString;\n    };\n    DecodedInformation.prototype.isRemaining = function () {\n        return this.remaining;\n    };\n    DecodedInformation.prototype.getRemainingValue = function () {\n        return this.remainingValue;\n    };\n    return DecodedInformation;\n}(DecodedObject));\nexport default DecodedInformation;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,aAAa,MAAM,iBAAiB;AAC3C,IAAIC,kBAAkB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACtDhB,SAAS,CAACe,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAACE,WAAW,EAAEC,SAAS,EAAEC,cAAc,EAAE;IAChE,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,WAAW,CAAC,IAAI,IAAI;IAClD,IAAIE,cAAc,EAAE;MAChBC,KAAK,CAACE,SAAS,GAAG,IAAI;MACtBF,KAAK,CAACD,cAAc,GAAGC,KAAK,CAACD,cAAc;IAC/C,CAAC,MACI;MACDC,KAAK,CAACE,SAAS,GAAG,KAAK;MACvBF,KAAK,CAACD,cAAc,GAAG,CAAC;IAC5B;IACAC,KAAK,CAACF,SAAS,GAAGA,SAAS;IAC3B,OAAOE,KAAK;EAChB;EACAL,kBAAkB,CAACH,SAAS,CAACW,YAAY,GAAG,YAAY;IACpD,OAAO,IAAI,CAACL,SAAS;EACzB,CAAC;EACDH,kBAAkB,CAACH,SAAS,CAACY,WAAW,GAAG,YAAY;IACnD,OAAO,IAAI,CAACF,SAAS;EACzB,CAAC;EACDP,kBAAkB,CAACH,SAAS,CAACa,iBAAiB,GAAG,YAAY;IACzD,OAAO,IAAI,CAACN,cAAc;EAC9B,CAAC;EACD,OAAOJ,kBAAkB;AAC7B,CAAC,CAACD,aAAa,CAAE;AACjB,eAAeC,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}