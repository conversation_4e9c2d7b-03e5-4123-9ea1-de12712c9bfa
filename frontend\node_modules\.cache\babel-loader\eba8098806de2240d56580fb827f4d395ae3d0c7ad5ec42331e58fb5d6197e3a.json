{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates information about finder patterns in an image, including the location of\n * the three finder patterns, and their estimated module size.</p>\n *\n * <AUTHOR>\n */\nvar FinderPatternInfo = /** @class */function () {\n  function FinderPatternInfo(patternCenters) {\n    this.bottomLeft = patternCenters[0];\n    this.topLeft = patternCenters[1];\n    this.topRight = patternCenters[2];\n  }\n  FinderPatternInfo.prototype.getBottomLeft = function () {\n    return this.bottomLeft;\n  };\n  FinderPatternInfo.prototype.getTopLeft = function () {\n    return this.topLeft;\n  };\n  FinderPatternInfo.prototype.getTopRight = function () {\n    return this.topRight;\n  };\n  return FinderPatternInfo;\n}();\nexport default FinderPatternInfo;", "map": {"version": 3, "names": ["FinderPatternInfo", "patternCenters", "bottomLeft", "topLeft", "topRight", "prototype", "getBottomLeft", "getTopLeft", "getTopRight"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPatternInfo.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates information about finder patterns in an image, including the location of\n * the three finder patterns, and their estimated module size.</p>\n *\n * <AUTHOR>\n */\nvar FinderPatternInfo = /** @class */ (function () {\n    function FinderPatternInfo(patternCenters) {\n        this.bottomLeft = patternCenters[0];\n        this.topLeft = patternCenters[1];\n        this.topRight = patternCenters[2];\n    }\n    FinderPatternInfo.prototype.getBottomLeft = function () {\n        return this.bottomLeft;\n    };\n    FinderPatternInfo.prototype.getTopLeft = function () {\n        return this.topLeft;\n    };\n    FinderPatternInfo.prototype.getTopRight = function () {\n        return this.topRight;\n    };\n    return FinderPatternInfo;\n}());\nexport default FinderPatternInfo;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,iBAAiB,GAAG,aAAe,YAAY;EAC/C,SAASA,iBAAiBA,CAACC,cAAc,EAAE;IACvC,IAAI,CAACC,UAAU,GAAGD,cAAc,CAAC,CAAC,CAAC;IACnC,IAAI,CAACE,OAAO,GAAGF,cAAc,CAAC,CAAC,CAAC;IAChC,IAAI,CAACG,QAAQ,GAAGH,cAAc,CAAC,CAAC,CAAC;EACrC;EACAD,iBAAiB,CAACK,SAAS,CAACC,aAAa,GAAG,YAAY;IACpD,OAAO,IAAI,CAACJ,UAAU;EAC1B,CAAC;EACDF,iBAAiB,CAACK,SAAS,CAACE,UAAU,GAAG,YAAY;IACjD,OAAO,IAAI,CAACJ,OAAO;EACvB,CAAC;EACDH,iBAAiB,CAACK,SAAS,CAACG,WAAW,GAAG,YAAY;IAClD,OAAO,IAAI,CAACJ,QAAQ;EACxB,CAAC;EACD,OAAOJ,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AACJ,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}