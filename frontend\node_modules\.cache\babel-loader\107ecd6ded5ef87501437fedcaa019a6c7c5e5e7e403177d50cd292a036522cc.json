{"ast": null, "code": "// tslint:disable-next-line:no-circular-imports\nimport { ASCIIEncoder } from './ASCIIEncoder';\n// tslint:disable-next-line:no-circular-imports\nimport { Base256Encoder } from './Base256Encoder';\n// tslint:disable-next-line:no-circular-imports\nimport { C40Encoder } from './C40Encoder';\nimport { ASCII_ENCODATION, BASE256_ENCODATION, C40_ENCODATION, EDIFACT_ENCODATION, MACRO_05, MACRO_05_HEADER, MACRO_06, MACRO_06_HEADER, MACRO_TRAILER, PAD, TEXT_ENCODATION, X12_ENCODATION } from './constants';\n// tslint:disable-next-line:no-circular-imports\nimport { EdifactEncoder } from './EdifactEncoder';\nimport { EncoderContext } from './EncoderContext';\n// tslint:disable-next-line:no-circular-imports\nimport { X12Encoder } from './X12Encoder';\n// tslint:disable-next-line:no-circular-imports\nimport { TextEncoder } from './TextEncoder';\nimport Arrays from '../../util/Arrays';\nimport Integer from '../../util/Integer';\n/**\n * DataMatrix ECC 200 data encoder following the algorithm described in ISO/IEC 16022:200(E) in\n * annex S.\n */\nvar HighLevelEncoder = /** @class */function () {\n  function HighLevelEncoder() {}\n  HighLevelEncoder.randomize253State = function (codewordPosition) {\n    var pseudoRandom = 149 * codewordPosition % 253 + 1;\n    var tempVariable = PAD + pseudoRandom;\n    return tempVariable <= 254 ? tempVariable : tempVariable - 254;\n  };\n  /**\n   * Performs message encoding of a DataMatrix message using the algorithm described in annex P\n   * of ISO/IEC 16022:2000(E).\n   *\n   * @param msg     the message\n   * @param shape   requested shape. May be {@code SymbolShapeHint.FORCE_NONE},\n   *                {@code SymbolShapeHint.FORCE_SQUARE} or {@code SymbolShapeHint.FORCE_RECTANGLE}.\n   * @param minSize the minimum symbol size constraint or null for no constraint\n   * @param maxSize the maximum symbol size constraint or null for no constraint\n   * @param forceC40 enforce C40 encoding\n   * @return the encoded message (the char values range from 0 to 255)\n   */\n  HighLevelEncoder.encodeHighLevel = function (msg, shape, minSize, maxSize, forceC40) {\n    if (shape === void 0) {\n      shape = 0 /* FORCE_NONE */;\n    }\n    if (minSize === void 0) {\n      minSize = null;\n    }\n    if (maxSize === void 0) {\n      maxSize = null;\n    }\n    if (forceC40 === void 0) {\n      forceC40 = false;\n    }\n    // the codewords 0..255 are encoded as Unicode characters\n    var c40Encoder = new C40Encoder();\n    var encoders = [new ASCIIEncoder(), c40Encoder, new TextEncoder(), new X12Encoder(), new EdifactEncoder(), new Base256Encoder()];\n    var context = new EncoderContext(msg);\n    context.setSymbolShape(shape);\n    context.setSizeConstraints(minSize, maxSize);\n    if (msg.startsWith(MACRO_05_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n      context.writeCodeword(MACRO_05);\n      context.setSkipAtEnd(2);\n      context.pos += MACRO_05_HEADER.length;\n    } else if (msg.startsWith(MACRO_06_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n      context.writeCodeword(MACRO_06);\n      context.setSkipAtEnd(2);\n      context.pos += MACRO_06_HEADER.length;\n    }\n    var encodingMode = ASCII_ENCODATION; // Default mode\n    if (forceC40) {\n      c40Encoder.encodeMaximal(context);\n      encodingMode = context.getNewEncoding();\n      context.resetEncoderSignal();\n    }\n    while (context.hasMoreCharacters()) {\n      encoders[encodingMode].encode(context);\n      if (context.getNewEncoding() >= 0) {\n        encodingMode = context.getNewEncoding();\n        context.resetEncoderSignal();\n      }\n    }\n    var len = context.getCodewordCount();\n    context.updateSymbolInfo();\n    var capacity = context.getSymbolInfo().getDataCapacity();\n    if (len < capacity && encodingMode !== ASCII_ENCODATION && encodingMode !== BASE256_ENCODATION && encodingMode !== EDIFACT_ENCODATION) {\n      context.writeCodeword('\\u00fe'); // Unlatch (254)\n    }\n    // Padding\n    var codewords = context.getCodewords();\n    if (codewords.length() < capacity) {\n      codewords.append(PAD);\n    }\n    while (codewords.length() < capacity) {\n      codewords.append(this.randomize253State(codewords.length() + 1));\n    }\n    return context.getCodewords().toString();\n  };\n  HighLevelEncoder.lookAheadTest = function (msg, startpos, currentMode) {\n    var newMode = this.lookAheadTestIntern(msg, startpos, currentMode);\n    if (currentMode === X12_ENCODATION && newMode === X12_ENCODATION) {\n      var endpos = Math.min(startpos + 3, msg.length);\n      for (var i = startpos; i < endpos; i++) {\n        if (!this.isNativeX12(msg.charCodeAt(i))) {\n          return ASCII_ENCODATION;\n        }\n      }\n    } else if (currentMode === EDIFACT_ENCODATION && newMode === EDIFACT_ENCODATION) {\n      var endpos = Math.min(startpos + 4, msg.length);\n      for (var i = startpos; i < endpos; i++) {\n        if (!this.isNativeEDIFACT(msg.charCodeAt(i))) {\n          return ASCII_ENCODATION;\n        }\n      }\n    }\n    return newMode;\n  };\n  HighLevelEncoder.lookAheadTestIntern = function (msg, startpos, currentMode) {\n    if (startpos >= msg.length) {\n      return currentMode;\n    }\n    var charCounts;\n    // step J\n    if (currentMode === ASCII_ENCODATION) {\n      charCounts = [0, 1, 1, 1, 1, 1.25];\n    } else {\n      charCounts = [1, 2, 2, 2, 2, 2.25];\n      charCounts[currentMode] = 0;\n    }\n    var charsProcessed = 0;\n    var mins = new Uint8Array(6);\n    var intCharCounts = [];\n    while (true) {\n      // step K\n      if (startpos + charsProcessed === msg.length) {\n        Arrays.fill(mins, 0);\n        Arrays.fill(intCharCounts, 0);\n        var min = this.findMinimums(charCounts, intCharCounts, Integer.MAX_VALUE, mins);\n        var minCount = this.getMinimumCount(mins);\n        if (intCharCounts[ASCII_ENCODATION] === min) {\n          return ASCII_ENCODATION;\n        }\n        if (minCount === 1) {\n          if (mins[BASE256_ENCODATION] > 0) {\n            return BASE256_ENCODATION;\n          }\n          if (mins[EDIFACT_ENCODATION] > 0) {\n            return EDIFACT_ENCODATION;\n          }\n          if (mins[TEXT_ENCODATION] > 0) {\n            return TEXT_ENCODATION;\n          }\n          if (mins[X12_ENCODATION] > 0) {\n            return X12_ENCODATION;\n          }\n        }\n        return C40_ENCODATION;\n      }\n      var c = msg.charCodeAt(startpos + charsProcessed);\n      charsProcessed++;\n      // step L\n      if (this.isDigit(c)) {\n        charCounts[ASCII_ENCODATION] += 0.5;\n      } else if (this.isExtendedASCII(c)) {\n        charCounts[ASCII_ENCODATION] = Math.ceil(charCounts[ASCII_ENCODATION]);\n        charCounts[ASCII_ENCODATION] += 2.0;\n      } else {\n        charCounts[ASCII_ENCODATION] = Math.ceil(charCounts[ASCII_ENCODATION]);\n        charCounts[ASCII_ENCODATION]++;\n      }\n      // step M\n      if (this.isNativeC40(c)) {\n        charCounts[C40_ENCODATION] += 2.0 / 3.0;\n      } else if (this.isExtendedASCII(c)) {\n        charCounts[C40_ENCODATION] += 8.0 / 3.0;\n      } else {\n        charCounts[C40_ENCODATION] += 4.0 / 3.0;\n      }\n      // step N\n      if (this.isNativeText(c)) {\n        charCounts[TEXT_ENCODATION] += 2.0 / 3.0;\n      } else if (this.isExtendedASCII(c)) {\n        charCounts[TEXT_ENCODATION] += 8.0 / 3.0;\n      } else {\n        charCounts[TEXT_ENCODATION] += 4.0 / 3.0;\n      }\n      // step O\n      if (this.isNativeX12(c)) {\n        charCounts[X12_ENCODATION] += 2.0 / 3.0;\n      } else if (this.isExtendedASCII(c)) {\n        charCounts[X12_ENCODATION] += 13.0 / 3.0;\n      } else {\n        charCounts[X12_ENCODATION] += 10.0 / 3.0;\n      }\n      // step P\n      if (this.isNativeEDIFACT(c)) {\n        charCounts[EDIFACT_ENCODATION] += 3.0 / 4.0;\n      } else if (this.isExtendedASCII(c)) {\n        charCounts[EDIFACT_ENCODATION] += 17.0 / 4.0;\n      } else {\n        charCounts[EDIFACT_ENCODATION] += 13.0 / 4.0;\n      }\n      // step Q\n      if (this.isSpecialB256(c)) {\n        charCounts[BASE256_ENCODATION] += 4.0;\n      } else {\n        charCounts[BASE256_ENCODATION]++;\n      }\n      // step R\n      if (charsProcessed >= 4) {\n        Arrays.fill(mins, 0);\n        Arrays.fill(intCharCounts, 0);\n        this.findMinimums(charCounts, intCharCounts, Integer.MAX_VALUE, mins);\n        if (intCharCounts[ASCII_ENCODATION] < this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[EDIFACT_ENCODATION])) {\n          return ASCII_ENCODATION;\n        }\n        if (intCharCounts[BASE256_ENCODATION] < intCharCounts[ASCII_ENCODATION] || intCharCounts[BASE256_ENCODATION] + 1 < this.min(intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[EDIFACT_ENCODATION])) {\n          return BASE256_ENCODATION;\n        }\n        if (intCharCounts[EDIFACT_ENCODATION] + 1 < this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n          return EDIFACT_ENCODATION;\n        }\n        if (intCharCounts[TEXT_ENCODATION] + 1 < this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n          return TEXT_ENCODATION;\n        }\n        if (intCharCounts[X12_ENCODATION] + 1 < this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n          return X12_ENCODATION;\n        }\n        if (intCharCounts[C40_ENCODATION] + 1 < this.min(intCharCounts[ASCII_ENCODATION], intCharCounts[BASE256_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[TEXT_ENCODATION])) {\n          if (intCharCounts[C40_ENCODATION] < intCharCounts[X12_ENCODATION]) {\n            return C40_ENCODATION;\n          }\n          if (intCharCounts[C40_ENCODATION] === intCharCounts[X12_ENCODATION]) {\n            var p = startpos + charsProcessed + 1;\n            while (p < msg.length) {\n              var tc = msg.charCodeAt(p);\n              if (this.isX12TermSep(tc)) {\n                return X12_ENCODATION;\n              }\n              if (!this.isNativeX12(tc)) {\n                break;\n              }\n              p++;\n            }\n            return C40_ENCODATION;\n          }\n        }\n      }\n    }\n  };\n  HighLevelEncoder.min = function (f1, f2, f3, f4, f5) {\n    var val = Math.min(f1, Math.min(f2, Math.min(f3, f4)));\n    if (f5 === undefined) {\n      return val;\n    } else {\n      return Math.min(val, f5);\n    }\n  };\n  HighLevelEncoder.findMinimums = function (charCounts, intCharCounts, min, mins) {\n    for (var i = 0; i < 6; i++) {\n      var current = intCharCounts[i] = Math.ceil(charCounts[i]);\n      if (min > current) {\n        min = current;\n        Arrays.fill(mins, 0);\n      }\n      if (min === current) {\n        mins[i] = mins[i] + 1;\n      }\n    }\n    return min;\n  };\n  HighLevelEncoder.getMinimumCount = function (mins) {\n    var minCount = 0;\n    for (var i = 0; i < 6; i++) {\n      minCount += mins[i];\n    }\n    return minCount || 0;\n  };\n  HighLevelEncoder.isDigit = function (ch) {\n    return ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0);\n  };\n  HighLevelEncoder.isExtendedASCII = function (ch) {\n    return ch >= 128 && ch <= 255;\n  };\n  HighLevelEncoder.isNativeC40 = function (ch) {\n    return ch === ' '.charCodeAt(0) || ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0) || ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0);\n  };\n  HighLevelEncoder.isNativeText = function (ch) {\n    return ch === ' '.charCodeAt(0) || ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0) || ch >= 'a'.charCodeAt(0) && ch <= 'z'.charCodeAt(0);\n  };\n  HighLevelEncoder.isNativeX12 = function (ch) {\n    return this.isX12TermSep(ch) || ch === ' '.charCodeAt(0) || ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0) || ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0);\n  };\n  HighLevelEncoder.isX12TermSep = function (ch) {\n    return ch === 13 ||\n    // CR\n    ch === '*'.charCodeAt(0) || ch === '>'.charCodeAt(0);\n  };\n  HighLevelEncoder.isNativeEDIFACT = function (ch) {\n    return ch >= ' '.charCodeAt(0) && ch <= '^'.charCodeAt(0);\n  };\n  HighLevelEncoder.isSpecialB256 = function (ch) {\n    return false; // TODO NOT IMPLEMENTED YET!!!\n  };\n  /**\n   * Determines the number of consecutive characters that are encodable using numeric compaction.\n   *\n   * @param msg      the message\n   * @param startpos the start position within the message\n   * @return the requested character count\n   */\n  HighLevelEncoder.determineConsecutiveDigitCount = function (msg, startpos) {\n    if (startpos === void 0) {\n      startpos = 0;\n    }\n    var len = msg.length;\n    var idx = startpos;\n    while (idx < len && this.isDigit(msg.charCodeAt(idx))) {\n      idx++;\n    }\n    return idx - startpos;\n  };\n  HighLevelEncoder.illegalCharacter = function (singleCharacter) {\n    var hex = Integer.toHexString(singleCharacter.charCodeAt(0));\n    hex = '0000'.substring(0, 4 - hex.length) + hex;\n    throw new Error('Illegal character: ' + singleCharacter + ' (0x' + hex + ')');\n  };\n  return HighLevelEncoder;\n}();\nexport default HighLevelEncoder;", "map": {"version": 3, "names": ["ASCIIEncoder", "Base256Encoder", "C40Encoder", "ASCII_ENCODATION", "BASE256_ENCODATION", "C40_ENCODATION", "EDIFACT_ENCODATION", "MACRO_05", "MACRO_05_HEADER", "MACRO_06", "MACRO_06_HEADER", "MACRO_TRAILER", "PAD", "TEXT_ENCODATION", "X12_ENCODATION", "EdifactEncoder", "EncoderContext", "X12Encoder", "TextEncoder", "<PERSON><PERSON><PERSON>", "Integer", "HighLevelEncoder", "randomize253State", "codewordPosition", "pseudoRandom", "tempVariable", "encodeHighLevel", "msg", "shape", "minSize", "maxSize", "forceC40", "c40<PERSON><PERSON>der", "encoders", "context", "setSymbolShape", "setSizeConstraints", "startsWith", "endsWith", "writeCodeword", "setSkipAtEnd", "pos", "length", "encodingMode", "encodeMaximal", "getNewEncoding", "resetEncoderSignal", "hasMoreCharacters", "encode", "len", "getCodewordCount", "updateSymbolInfo", "capacity", "getSymbolInfo", "getDataCapacity", "codewords", "getCodewords", "append", "toString", "lookAheadTest", "startpos", "currentMode", "newMode", "lookAheadTestIntern", "endpos", "Math", "min", "i", "isNativeX12", "charCodeAt", "isNativeEDIFACT", "charCounts", "charsProcessed", "mins", "Uint8Array", "intCharCounts", "fill", "findMinimums", "MAX_VALUE", "minCount", "getMinimumCount", "c", "isDigit", "isExtendedASCII", "ceil", "isNativeC40", "isNativeText", "isSpecialB256", "p", "tc", "isX12TermSep", "f1", "f2", "f3", "f4", "f5", "val", "undefined", "current", "ch", "determineConsecutiveDigitCount", "idx", "illegalCharacter", "singleCharacter", "hex", "toHexString", "substring", "Error"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/HighLevelEncoder.js"], "sourcesContent": ["// tslint:disable-next-line:no-circular-imports\nimport { ASCIIEncoder } from './ASCIIEncoder';\n// tslint:disable-next-line:no-circular-imports\nimport { Base256Encoder } from './Base256Encoder';\n// tslint:disable-next-line:no-circular-imports\nimport { C40Encoder } from './C40Encoder';\nimport { ASCII_ENCODATION, BASE256_ENCODATION, C40_ENCODATION, EDIFACT_ENCODATION, MACRO_05, MACRO_05_HEADER, MACRO_06, MACRO_06_HEADER, MACRO_TRAILER, PAD, TEXT_ENCODATION, X12_ENCODATION, } from './constants';\n// tslint:disable-next-line:no-circular-imports\nimport { EdifactEncoder } from './EdifactEncoder';\nimport { EncoderContext } from './EncoderContext';\n// tslint:disable-next-line:no-circular-imports\nimport { X12Encoder } from './X12Encoder';\n// tslint:disable-next-line:no-circular-imports\nimport { TextEncoder } from './TextEncoder';\nimport Arrays from '../../util/Arrays';\nimport Integer from '../../util/Integer';\n/**\n * DataMatrix ECC 200 data encoder following the algorithm described in ISO/IEC 16022:200(E) in\n * annex S.\n */\nvar HighLevelEncoder = /** @class */ (function () {\n    function HighLevelEncoder() {\n    }\n    HighLevelEncoder.randomize253State = function (codewordPosition) {\n        var pseudoRandom = ((149 * codewordPosition) % 253) + 1;\n        var tempVariable = PAD + pseudoRandom;\n        return tempVariable <= 254 ? tempVariable : tempVariable - 254;\n    };\n    /**\n     * Performs message encoding of a DataMatrix message using the algorithm described in annex P\n     * of ISO/IEC 16022:2000(E).\n     *\n     * @param msg     the message\n     * @param shape   requested shape. May be {@code SymbolShapeHint.FORCE_NONE},\n     *                {@code SymbolShapeHint.FORCE_SQUARE} or {@code SymbolShapeHint.FORCE_RECTANGLE}.\n     * @param minSize the minimum symbol size constraint or null for no constraint\n     * @param maxSize the maximum symbol size constraint or null for no constraint\n     * @param forceC40 enforce C40 encoding\n     * @return the encoded message (the char values range from 0 to 255)\n     */\n    HighLevelEncoder.encodeHighLevel = function (msg, shape, minSize, maxSize, forceC40) {\n        if (shape === void 0) { shape = 0 /* FORCE_NONE */; }\n        if (minSize === void 0) { minSize = null; }\n        if (maxSize === void 0) { maxSize = null; }\n        if (forceC40 === void 0) { forceC40 = false; }\n        // the codewords 0..255 are encoded as Unicode characters\n        var c40Encoder = new C40Encoder();\n        var encoders = [\n            new ASCIIEncoder(),\n            c40Encoder,\n            new TextEncoder(),\n            new X12Encoder(),\n            new EdifactEncoder(),\n            new Base256Encoder(),\n        ];\n        var context = new EncoderContext(msg);\n        context.setSymbolShape(shape);\n        context.setSizeConstraints(minSize, maxSize);\n        if (msg.startsWith(MACRO_05_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n            context.writeCodeword(MACRO_05);\n            context.setSkipAtEnd(2);\n            context.pos += MACRO_05_HEADER.length;\n        }\n        else if (msg.startsWith(MACRO_06_HEADER) && msg.endsWith(MACRO_TRAILER)) {\n            context.writeCodeword(MACRO_06);\n            context.setSkipAtEnd(2);\n            context.pos += MACRO_06_HEADER.length;\n        }\n        var encodingMode = ASCII_ENCODATION; // Default mode\n        if (forceC40) {\n            c40Encoder.encodeMaximal(context);\n            encodingMode = context.getNewEncoding();\n            context.resetEncoderSignal();\n        }\n        while (context.hasMoreCharacters()) {\n            encoders[encodingMode].encode(context);\n            if (context.getNewEncoding() >= 0) {\n                encodingMode = context.getNewEncoding();\n                context.resetEncoderSignal();\n            }\n        }\n        var len = context.getCodewordCount();\n        context.updateSymbolInfo();\n        var capacity = context.getSymbolInfo().getDataCapacity();\n        if (len < capacity &&\n            encodingMode !== ASCII_ENCODATION &&\n            encodingMode !== BASE256_ENCODATION &&\n            encodingMode !== EDIFACT_ENCODATION) {\n            context.writeCodeword('\\u00fe'); // Unlatch (254)\n        }\n        // Padding\n        var codewords = context.getCodewords();\n        if (codewords.length() < capacity) {\n            codewords.append(PAD);\n        }\n        while (codewords.length() < capacity) {\n            codewords.append(this.randomize253State(codewords.length() + 1));\n        }\n        return context.getCodewords().toString();\n    };\n    HighLevelEncoder.lookAheadTest = function (msg, startpos, currentMode) {\n        var newMode = this.lookAheadTestIntern(msg, startpos, currentMode);\n        if (currentMode === X12_ENCODATION && newMode === X12_ENCODATION) {\n            var endpos = Math.min(startpos + 3, msg.length);\n            for (var i = startpos; i < endpos; i++) {\n                if (!this.isNativeX12(msg.charCodeAt(i))) {\n                    return ASCII_ENCODATION;\n                }\n            }\n        }\n        else if (currentMode === EDIFACT_ENCODATION &&\n            newMode === EDIFACT_ENCODATION) {\n            var endpos = Math.min(startpos + 4, msg.length);\n            for (var i = startpos; i < endpos; i++) {\n                if (!this.isNativeEDIFACT(msg.charCodeAt(i))) {\n                    return ASCII_ENCODATION;\n                }\n            }\n        }\n        return newMode;\n    };\n    HighLevelEncoder.lookAheadTestIntern = function (msg, startpos, currentMode) {\n        if (startpos >= msg.length) {\n            return currentMode;\n        }\n        var charCounts;\n        // step J\n        if (currentMode === ASCII_ENCODATION) {\n            charCounts = [0, 1, 1, 1, 1, 1.25];\n        }\n        else {\n            charCounts = [1, 2, 2, 2, 2, 2.25];\n            charCounts[currentMode] = 0;\n        }\n        var charsProcessed = 0;\n        var mins = new Uint8Array(6);\n        var intCharCounts = [];\n        while (true) {\n            // step K\n            if (startpos + charsProcessed === msg.length) {\n                Arrays.fill(mins, 0);\n                Arrays.fill(intCharCounts, 0);\n                var min = this.findMinimums(charCounts, intCharCounts, Integer.MAX_VALUE, mins);\n                var minCount = this.getMinimumCount(mins);\n                if (intCharCounts[ASCII_ENCODATION] === min) {\n                    return ASCII_ENCODATION;\n                }\n                if (minCount === 1) {\n                    if (mins[BASE256_ENCODATION] > 0) {\n                        return BASE256_ENCODATION;\n                    }\n                    if (mins[EDIFACT_ENCODATION] > 0) {\n                        return EDIFACT_ENCODATION;\n                    }\n                    if (mins[TEXT_ENCODATION] > 0) {\n                        return TEXT_ENCODATION;\n                    }\n                    if (mins[X12_ENCODATION] > 0) {\n                        return X12_ENCODATION;\n                    }\n                }\n                return C40_ENCODATION;\n            }\n            var c = msg.charCodeAt(startpos + charsProcessed);\n            charsProcessed++;\n            // step L\n            if (this.isDigit(c)) {\n                charCounts[ASCII_ENCODATION] += 0.5;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[ASCII_ENCODATION] = Math.ceil(charCounts[ASCII_ENCODATION]);\n                charCounts[ASCII_ENCODATION] += 2.0;\n            }\n            else {\n                charCounts[ASCII_ENCODATION] = Math.ceil(charCounts[ASCII_ENCODATION]);\n                charCounts[ASCII_ENCODATION]++;\n            }\n            // step M\n            if (this.isNativeC40(c)) {\n                charCounts[C40_ENCODATION] += 2.0 / 3.0;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[C40_ENCODATION] += 8.0 / 3.0;\n            }\n            else {\n                charCounts[C40_ENCODATION] += 4.0 / 3.0;\n            }\n            // step N\n            if (this.isNativeText(c)) {\n                charCounts[TEXT_ENCODATION] += 2.0 / 3.0;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[TEXT_ENCODATION] += 8.0 / 3.0;\n            }\n            else {\n                charCounts[TEXT_ENCODATION] += 4.0 / 3.0;\n            }\n            // step O\n            if (this.isNativeX12(c)) {\n                charCounts[X12_ENCODATION] += 2.0 / 3.0;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[X12_ENCODATION] += 13.0 / 3.0;\n            }\n            else {\n                charCounts[X12_ENCODATION] += 10.0 / 3.0;\n            }\n            // step P\n            if (this.isNativeEDIFACT(c)) {\n                charCounts[EDIFACT_ENCODATION] += 3.0 / 4.0;\n            }\n            else if (this.isExtendedASCII(c)) {\n                charCounts[EDIFACT_ENCODATION] += 17.0 / 4.0;\n            }\n            else {\n                charCounts[EDIFACT_ENCODATION] += 13.0 / 4.0;\n            }\n            // step Q\n            if (this.isSpecialB256(c)) {\n                charCounts[BASE256_ENCODATION] += 4.0;\n            }\n            else {\n                charCounts[BASE256_ENCODATION]++;\n            }\n            // step R\n            if (charsProcessed >= 4) {\n                Arrays.fill(mins, 0);\n                Arrays.fill(intCharCounts, 0);\n                this.findMinimums(charCounts, intCharCounts, Integer.MAX_VALUE, mins);\n                if (intCharCounts[ASCII_ENCODATION] <\n                    this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[EDIFACT_ENCODATION])) {\n                    return ASCII_ENCODATION;\n                }\n                if (intCharCounts[BASE256_ENCODATION] < intCharCounts[ASCII_ENCODATION] ||\n                    intCharCounts[BASE256_ENCODATION] + 1 <\n                        this.min(intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[EDIFACT_ENCODATION])) {\n                    return BASE256_ENCODATION;\n                }\n                if (intCharCounts[EDIFACT_ENCODATION] + 1 <\n                    this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n                    return EDIFACT_ENCODATION;\n                }\n                if (intCharCounts[TEXT_ENCODATION] + 1 <\n                    this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[X12_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n                    return TEXT_ENCODATION;\n                }\n                if (intCharCounts[X12_ENCODATION] + 1 <\n                    this.min(intCharCounts[BASE256_ENCODATION], intCharCounts[C40_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[TEXT_ENCODATION], intCharCounts[ASCII_ENCODATION])) {\n                    return X12_ENCODATION;\n                }\n                if (intCharCounts[C40_ENCODATION] + 1 <\n                    this.min(intCharCounts[ASCII_ENCODATION], intCharCounts[BASE256_ENCODATION], intCharCounts[EDIFACT_ENCODATION], intCharCounts[TEXT_ENCODATION])) {\n                    if (intCharCounts[C40_ENCODATION] < intCharCounts[X12_ENCODATION]) {\n                        return C40_ENCODATION;\n                    }\n                    if (intCharCounts[C40_ENCODATION] === intCharCounts[X12_ENCODATION]) {\n                        var p = startpos + charsProcessed + 1;\n                        while (p < msg.length) {\n                            var tc = msg.charCodeAt(p);\n                            if (this.isX12TermSep(tc)) {\n                                return X12_ENCODATION;\n                            }\n                            if (!this.isNativeX12(tc)) {\n                                break;\n                            }\n                            p++;\n                        }\n                        return C40_ENCODATION;\n                    }\n                }\n            }\n        }\n    };\n    HighLevelEncoder.min = function (f1, f2, f3, f4, f5) {\n        var val = Math.min(f1, Math.min(f2, Math.min(f3, f4)));\n        if (f5 === undefined) {\n            return val;\n        }\n        else {\n            return Math.min(val, f5);\n        }\n    };\n    HighLevelEncoder.findMinimums = function (charCounts, intCharCounts, min, mins) {\n        for (var i = 0; i < 6; i++) {\n            var current = (intCharCounts[i] = Math.ceil(charCounts[i]));\n            if (min > current) {\n                min = current;\n                Arrays.fill(mins, 0);\n            }\n            if (min === current) {\n                mins[i] = mins[i] + 1;\n            }\n        }\n        return min;\n    };\n    HighLevelEncoder.getMinimumCount = function (mins) {\n        var minCount = 0;\n        for (var i = 0; i < 6; i++) {\n            minCount += mins[i];\n        }\n        return minCount || 0;\n    };\n    HighLevelEncoder.isDigit = function (ch) {\n        return ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0);\n    };\n    HighLevelEncoder.isExtendedASCII = function (ch) {\n        return ch >= 128 && ch <= 255;\n    };\n    HighLevelEncoder.isNativeC40 = function (ch) {\n        return (ch === ' '.charCodeAt(0) ||\n            (ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0)) ||\n            (ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0)));\n    };\n    HighLevelEncoder.isNativeText = function (ch) {\n        return (ch === ' '.charCodeAt(0) ||\n            (ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0)) ||\n            (ch >= 'a'.charCodeAt(0) && ch <= 'z'.charCodeAt(0)));\n    };\n    HighLevelEncoder.isNativeX12 = function (ch) {\n        return (this.isX12TermSep(ch) ||\n            ch === ' '.charCodeAt(0) ||\n            (ch >= '0'.charCodeAt(0) && ch <= '9'.charCodeAt(0)) ||\n            (ch >= 'A'.charCodeAt(0) && ch <= 'Z'.charCodeAt(0)));\n    };\n    HighLevelEncoder.isX12TermSep = function (ch) {\n        return (ch === 13 || // CR\n            ch === '*'.charCodeAt(0) ||\n            ch === '>'.charCodeAt(0));\n    };\n    HighLevelEncoder.isNativeEDIFACT = function (ch) {\n        return ch >= ' '.charCodeAt(0) && ch <= '^'.charCodeAt(0);\n    };\n    HighLevelEncoder.isSpecialB256 = function (ch) {\n        return false; // TODO NOT IMPLEMENTED YET!!!\n    };\n    /**\n     * Determines the number of consecutive characters that are encodable using numeric compaction.\n     *\n     * @param msg      the message\n     * @param startpos the start position within the message\n     * @return the requested character count\n     */\n    HighLevelEncoder.determineConsecutiveDigitCount = function (msg, startpos) {\n        if (startpos === void 0) { startpos = 0; }\n        var len = msg.length;\n        var idx = startpos;\n        while (idx < len && this.isDigit(msg.charCodeAt(idx))) {\n            idx++;\n        }\n        return idx - startpos;\n    };\n    HighLevelEncoder.illegalCharacter = function (singleCharacter) {\n        var hex = Integer.toHexString(singleCharacter.charCodeAt(0));\n        hex = '0000'.substring(0, 4 - hex.length) + hex;\n        throw new Error('Illegal character: ' + singleCharacter + ' (0x' + hex + ')');\n    };\n    return HighLevelEncoder;\n}());\nexport default HighLevelEncoder;\n"], "mappings": "AAAA;AACA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C;AACA,SAASC,cAAc,QAAQ,kBAAkB;AACjD;AACA,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,gBAAgB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,aAAa,EAAEC,GAAG,EAAEC,eAAe,EAAEC,cAAc,QAAS,aAAa;AAClN;AACA,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD;AACA,SAASC,UAAU,QAAQ,cAAc;AACzC;AACA,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,OAAO,MAAM,oBAAoB;AACxC;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAe,YAAY;EAC9C,SAASA,gBAAgBA,CAAA,EAAG,CAC5B;EACAA,gBAAgB,CAACC,iBAAiB,GAAG,UAAUC,gBAAgB,EAAE;IAC7D,IAAIC,YAAY,GAAK,GAAG,GAAGD,gBAAgB,GAAI,GAAG,GAAI,CAAC;IACvD,IAAIE,YAAY,GAAGb,GAAG,GAAGY,YAAY;IACrC,OAAOC,YAAY,IAAI,GAAG,GAAGA,YAAY,GAAGA,YAAY,GAAG,GAAG;EAClE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIJ,gBAAgB,CAACK,eAAe,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAE;IACjF,IAAIH,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC,CAAC;IAAkB;IACpD,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,IAAI;IAAE;IAC1C,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,IAAI;IAAE;IAC1C,IAAIC,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAG,KAAK;IAAE;IAC7C;IACA,IAAIC,UAAU,GAAG,IAAI9B,UAAU,CAAC,CAAC;IACjC,IAAI+B,QAAQ,GAAG,CACX,IAAIjC,YAAY,CAAC,CAAC,EAClBgC,UAAU,EACV,IAAId,WAAW,CAAC,CAAC,EACjB,IAAID,UAAU,CAAC,CAAC,EAChB,IAAIF,cAAc,CAAC,CAAC,EACpB,IAAId,cAAc,CAAC,CAAC,CACvB;IACD,IAAIiC,OAAO,GAAG,IAAIlB,cAAc,CAACW,GAAG,CAAC;IACrCO,OAAO,CAACC,cAAc,CAACP,KAAK,CAAC;IAC7BM,OAAO,CAACE,kBAAkB,CAACP,OAAO,EAAEC,OAAO,CAAC;IAC5C,IAAIH,GAAG,CAACU,UAAU,CAAC7B,eAAe,CAAC,IAAImB,GAAG,CAACW,QAAQ,CAAC3B,aAAa,CAAC,EAAE;MAChEuB,OAAO,CAACK,aAAa,CAAChC,QAAQ,CAAC;MAC/B2B,OAAO,CAACM,YAAY,CAAC,CAAC,CAAC;MACvBN,OAAO,CAACO,GAAG,IAAIjC,eAAe,CAACkC,MAAM;IACzC,CAAC,MACI,IAAIf,GAAG,CAACU,UAAU,CAAC3B,eAAe,CAAC,IAAIiB,GAAG,CAACW,QAAQ,CAAC3B,aAAa,CAAC,EAAE;MACrEuB,OAAO,CAACK,aAAa,CAAC9B,QAAQ,CAAC;MAC/ByB,OAAO,CAACM,YAAY,CAAC,CAAC,CAAC;MACvBN,OAAO,CAACO,GAAG,IAAI/B,eAAe,CAACgC,MAAM;IACzC;IACA,IAAIC,YAAY,GAAGxC,gBAAgB,CAAC,CAAC;IACrC,IAAI4B,QAAQ,EAAE;MACVC,UAAU,CAACY,aAAa,CAACV,OAAO,CAAC;MACjCS,YAAY,GAAGT,OAAO,CAACW,cAAc,CAAC,CAAC;MACvCX,OAAO,CAACY,kBAAkB,CAAC,CAAC;IAChC;IACA,OAAOZ,OAAO,CAACa,iBAAiB,CAAC,CAAC,EAAE;MAChCd,QAAQ,CAACU,YAAY,CAAC,CAACK,MAAM,CAACd,OAAO,CAAC;MACtC,IAAIA,OAAO,CAACW,cAAc,CAAC,CAAC,IAAI,CAAC,EAAE;QAC/BF,YAAY,GAAGT,OAAO,CAACW,cAAc,CAAC,CAAC;QACvCX,OAAO,CAACY,kBAAkB,CAAC,CAAC;MAChC;IACJ;IACA,IAAIG,GAAG,GAAGf,OAAO,CAACgB,gBAAgB,CAAC,CAAC;IACpChB,OAAO,CAACiB,gBAAgB,CAAC,CAAC;IAC1B,IAAIC,QAAQ,GAAGlB,OAAO,CAACmB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC;IACxD,IAAIL,GAAG,GAAGG,QAAQ,IACdT,YAAY,KAAKxC,gBAAgB,IACjCwC,YAAY,KAAKvC,kBAAkB,IACnCuC,YAAY,KAAKrC,kBAAkB,EAAE;MACrC4B,OAAO,CAACK,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC;IACrC;IACA;IACA,IAAIgB,SAAS,GAAGrB,OAAO,CAACsB,YAAY,CAAC,CAAC;IACtC,IAAID,SAAS,CAACb,MAAM,CAAC,CAAC,GAAGU,QAAQ,EAAE;MAC/BG,SAAS,CAACE,MAAM,CAAC7C,GAAG,CAAC;IACzB;IACA,OAAO2C,SAAS,CAACb,MAAM,CAAC,CAAC,GAAGU,QAAQ,EAAE;MAClCG,SAAS,CAACE,MAAM,CAAC,IAAI,CAACnC,iBAAiB,CAACiC,SAAS,CAACb,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACpE;IACA,OAAOR,OAAO,CAACsB,YAAY,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC;EAC5C,CAAC;EACDrC,gBAAgB,CAACsC,aAAa,GAAG,UAAUhC,GAAG,EAAEiC,QAAQ,EAAEC,WAAW,EAAE;IACnE,IAAIC,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAACpC,GAAG,EAAEiC,QAAQ,EAAEC,WAAW,CAAC;IAClE,IAAIA,WAAW,KAAK/C,cAAc,IAAIgD,OAAO,KAAKhD,cAAc,EAAE;MAC9D,IAAIkD,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACN,QAAQ,GAAG,CAAC,EAAEjC,GAAG,CAACe,MAAM,CAAC;MAC/C,KAAK,IAAIyB,CAAC,GAAGP,QAAQ,EAAEO,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC,IAAI,CAACC,WAAW,CAACzC,GAAG,CAAC0C,UAAU,CAACF,CAAC,CAAC,CAAC,EAAE;UACtC,OAAOhE,gBAAgB;QAC3B;MACJ;IACJ,CAAC,MACI,IAAI0D,WAAW,KAAKvD,kBAAkB,IACvCwD,OAAO,KAAKxD,kBAAkB,EAAE;MAChC,IAAI0D,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACN,QAAQ,GAAG,CAAC,EAAEjC,GAAG,CAACe,MAAM,CAAC;MAC/C,KAAK,IAAIyB,CAAC,GAAGP,QAAQ,EAAEO,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC,IAAI,CAACG,eAAe,CAAC3C,GAAG,CAAC0C,UAAU,CAACF,CAAC,CAAC,CAAC,EAAE;UAC1C,OAAOhE,gBAAgB;QAC3B;MACJ;IACJ;IACA,OAAO2D,OAAO;EAClB,CAAC;EACDzC,gBAAgB,CAAC0C,mBAAmB,GAAG,UAAUpC,GAAG,EAAEiC,QAAQ,EAAEC,WAAW,EAAE;IACzE,IAAID,QAAQ,IAAIjC,GAAG,CAACe,MAAM,EAAE;MACxB,OAAOmB,WAAW;IACtB;IACA,IAAIU,UAAU;IACd;IACA,IAAIV,WAAW,KAAK1D,gBAAgB,EAAE;MAClCoE,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACtC,CAAC,MACI;MACDA,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAClCA,UAAU,CAACV,WAAW,CAAC,GAAG,CAAC;IAC/B;IACA,IAAIW,cAAc,GAAG,CAAC;IACtB,IAAIC,IAAI,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;IAC5B,IAAIC,aAAa,GAAG,EAAE;IACtB,OAAO,IAAI,EAAE;MACT;MACA,IAAIf,QAAQ,GAAGY,cAAc,KAAK7C,GAAG,CAACe,MAAM,EAAE;QAC1CvB,MAAM,CAACyD,IAAI,CAACH,IAAI,EAAE,CAAC,CAAC;QACpBtD,MAAM,CAACyD,IAAI,CAACD,aAAa,EAAE,CAAC,CAAC;QAC7B,IAAIT,GAAG,GAAG,IAAI,CAACW,YAAY,CAACN,UAAU,EAAEI,aAAa,EAAEvD,OAAO,CAAC0D,SAAS,EAAEL,IAAI,CAAC;QAC/E,IAAIM,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACP,IAAI,CAAC;QACzC,IAAIE,aAAa,CAACxE,gBAAgB,CAAC,KAAK+D,GAAG,EAAE;UACzC,OAAO/D,gBAAgB;QAC3B;QACA,IAAI4E,QAAQ,KAAK,CAAC,EAAE;UAChB,IAAIN,IAAI,CAACrE,kBAAkB,CAAC,GAAG,CAAC,EAAE;YAC9B,OAAOA,kBAAkB;UAC7B;UACA,IAAIqE,IAAI,CAACnE,kBAAkB,CAAC,GAAG,CAAC,EAAE;YAC9B,OAAOA,kBAAkB;UAC7B;UACA,IAAImE,IAAI,CAAC5D,eAAe,CAAC,GAAG,CAAC,EAAE;YAC3B,OAAOA,eAAe;UAC1B;UACA,IAAI4D,IAAI,CAAC3D,cAAc,CAAC,GAAG,CAAC,EAAE;YAC1B,OAAOA,cAAc;UACzB;QACJ;QACA,OAAOT,cAAc;MACzB;MACA,IAAI4E,CAAC,GAAGtD,GAAG,CAAC0C,UAAU,CAACT,QAAQ,GAAGY,cAAc,CAAC;MACjDA,cAAc,EAAE;MAChB;MACA,IAAI,IAAI,CAACU,OAAO,CAACD,CAAC,CAAC,EAAE;QACjBV,UAAU,CAACpE,gBAAgB,CAAC,IAAI,GAAG;MACvC,CAAC,MACI,IAAI,IAAI,CAACgF,eAAe,CAACF,CAAC,CAAC,EAAE;QAC9BV,UAAU,CAACpE,gBAAgB,CAAC,GAAG8D,IAAI,CAACmB,IAAI,CAACb,UAAU,CAACpE,gBAAgB,CAAC,CAAC;QACtEoE,UAAU,CAACpE,gBAAgB,CAAC,IAAI,GAAG;MACvC,CAAC,MACI;QACDoE,UAAU,CAACpE,gBAAgB,CAAC,GAAG8D,IAAI,CAACmB,IAAI,CAACb,UAAU,CAACpE,gBAAgB,CAAC,CAAC;QACtEoE,UAAU,CAACpE,gBAAgB,CAAC,EAAE;MAClC;MACA;MACA,IAAI,IAAI,CAACkF,WAAW,CAACJ,CAAC,CAAC,EAAE;QACrBV,UAAU,CAAClE,cAAc,CAAC,IAAI,GAAG,GAAG,GAAG;MAC3C,CAAC,MACI,IAAI,IAAI,CAAC8E,eAAe,CAACF,CAAC,CAAC,EAAE;QAC9BV,UAAU,CAAClE,cAAc,CAAC,IAAI,GAAG,GAAG,GAAG;MAC3C,CAAC,MACI;QACDkE,UAAU,CAAClE,cAAc,CAAC,IAAI,GAAG,GAAG,GAAG;MAC3C;MACA;MACA,IAAI,IAAI,CAACiF,YAAY,CAACL,CAAC,CAAC,EAAE;QACtBV,UAAU,CAAC1D,eAAe,CAAC,IAAI,GAAG,GAAG,GAAG;MAC5C,CAAC,MACI,IAAI,IAAI,CAACsE,eAAe,CAACF,CAAC,CAAC,EAAE;QAC9BV,UAAU,CAAC1D,eAAe,CAAC,IAAI,GAAG,GAAG,GAAG;MAC5C,CAAC,MACI;QACD0D,UAAU,CAAC1D,eAAe,CAAC,IAAI,GAAG,GAAG,GAAG;MAC5C;MACA;MACA,IAAI,IAAI,CAACuD,WAAW,CAACa,CAAC,CAAC,EAAE;QACrBV,UAAU,CAACzD,cAAc,CAAC,IAAI,GAAG,GAAG,GAAG;MAC3C,CAAC,MACI,IAAI,IAAI,CAACqE,eAAe,CAACF,CAAC,CAAC,EAAE;QAC9BV,UAAU,CAACzD,cAAc,CAAC,IAAI,IAAI,GAAG,GAAG;MAC5C,CAAC,MACI;QACDyD,UAAU,CAACzD,cAAc,CAAC,IAAI,IAAI,GAAG,GAAG;MAC5C;MACA;MACA,IAAI,IAAI,CAACwD,eAAe,CAACW,CAAC,CAAC,EAAE;QACzBV,UAAU,CAACjE,kBAAkB,CAAC,IAAI,GAAG,GAAG,GAAG;MAC/C,CAAC,MACI,IAAI,IAAI,CAAC6E,eAAe,CAACF,CAAC,CAAC,EAAE;QAC9BV,UAAU,CAACjE,kBAAkB,CAAC,IAAI,IAAI,GAAG,GAAG;MAChD,CAAC,MACI;QACDiE,UAAU,CAACjE,kBAAkB,CAAC,IAAI,IAAI,GAAG,GAAG;MAChD;MACA;MACA,IAAI,IAAI,CAACiF,aAAa,CAACN,CAAC,CAAC,EAAE;QACvBV,UAAU,CAACnE,kBAAkB,CAAC,IAAI,GAAG;MACzC,CAAC,MACI;QACDmE,UAAU,CAACnE,kBAAkB,CAAC,EAAE;MACpC;MACA;MACA,IAAIoE,cAAc,IAAI,CAAC,EAAE;QACrBrD,MAAM,CAACyD,IAAI,CAACH,IAAI,EAAE,CAAC,CAAC;QACpBtD,MAAM,CAACyD,IAAI,CAACD,aAAa,EAAE,CAAC,CAAC;QAC7B,IAAI,CAACE,YAAY,CAACN,UAAU,EAAEI,aAAa,EAAEvD,OAAO,CAAC0D,SAAS,EAAEL,IAAI,CAAC;QACrE,IAAIE,aAAa,CAACxE,gBAAgB,CAAC,GAC/B,IAAI,CAAC+D,GAAG,CAACS,aAAa,CAACvE,kBAAkB,CAAC,EAAEuE,aAAa,CAACtE,cAAc,CAAC,EAAEsE,aAAa,CAAC9D,eAAe,CAAC,EAAE8D,aAAa,CAAC7D,cAAc,CAAC,EAAE6D,aAAa,CAACrE,kBAAkB,CAAC,CAAC,EAAE;UAC9K,OAAOH,gBAAgB;QAC3B;QACA,IAAIwE,aAAa,CAACvE,kBAAkB,CAAC,GAAGuE,aAAa,CAACxE,gBAAgB,CAAC,IACnEwE,aAAa,CAACvE,kBAAkB,CAAC,GAAG,CAAC,GACjC,IAAI,CAAC8D,GAAG,CAACS,aAAa,CAACtE,cAAc,CAAC,EAAEsE,aAAa,CAAC9D,eAAe,CAAC,EAAE8D,aAAa,CAAC7D,cAAc,CAAC,EAAE6D,aAAa,CAACrE,kBAAkB,CAAC,CAAC,EAAE;UAC/I,OAAOF,kBAAkB;QAC7B;QACA,IAAIuE,aAAa,CAACrE,kBAAkB,CAAC,GAAG,CAAC,GACrC,IAAI,CAAC4D,GAAG,CAACS,aAAa,CAACvE,kBAAkB,CAAC,EAAEuE,aAAa,CAACtE,cAAc,CAAC,EAAEsE,aAAa,CAAC9D,eAAe,CAAC,EAAE8D,aAAa,CAAC7D,cAAc,CAAC,EAAE6D,aAAa,CAACxE,gBAAgB,CAAC,CAAC,EAAE;UAC5K,OAAOG,kBAAkB;QAC7B;QACA,IAAIqE,aAAa,CAAC9D,eAAe,CAAC,GAAG,CAAC,GAClC,IAAI,CAACqD,GAAG,CAACS,aAAa,CAACvE,kBAAkB,CAAC,EAAEuE,aAAa,CAACtE,cAAc,CAAC,EAAEsE,aAAa,CAACrE,kBAAkB,CAAC,EAAEqE,aAAa,CAAC7D,cAAc,CAAC,EAAE6D,aAAa,CAACxE,gBAAgB,CAAC,CAAC,EAAE;UAC/K,OAAOU,eAAe;QAC1B;QACA,IAAI8D,aAAa,CAAC7D,cAAc,CAAC,GAAG,CAAC,GACjC,IAAI,CAACoD,GAAG,CAACS,aAAa,CAACvE,kBAAkB,CAAC,EAAEuE,aAAa,CAACtE,cAAc,CAAC,EAAEsE,aAAa,CAACrE,kBAAkB,CAAC,EAAEqE,aAAa,CAAC9D,eAAe,CAAC,EAAE8D,aAAa,CAACxE,gBAAgB,CAAC,CAAC,EAAE;UAChL,OAAOW,cAAc;QACzB;QACA,IAAI6D,aAAa,CAACtE,cAAc,CAAC,GAAG,CAAC,GACjC,IAAI,CAAC6D,GAAG,CAACS,aAAa,CAACxE,gBAAgB,CAAC,EAAEwE,aAAa,CAACvE,kBAAkB,CAAC,EAAEuE,aAAa,CAACrE,kBAAkB,CAAC,EAAEqE,aAAa,CAAC9D,eAAe,CAAC,CAAC,EAAE;UACjJ,IAAI8D,aAAa,CAACtE,cAAc,CAAC,GAAGsE,aAAa,CAAC7D,cAAc,CAAC,EAAE;YAC/D,OAAOT,cAAc;UACzB;UACA,IAAIsE,aAAa,CAACtE,cAAc,CAAC,KAAKsE,aAAa,CAAC7D,cAAc,CAAC,EAAE;YACjE,IAAI0E,CAAC,GAAG5B,QAAQ,GAAGY,cAAc,GAAG,CAAC;YACrC,OAAOgB,CAAC,GAAG7D,GAAG,CAACe,MAAM,EAAE;cACnB,IAAI+C,EAAE,GAAG9D,GAAG,CAAC0C,UAAU,CAACmB,CAAC,CAAC;cAC1B,IAAI,IAAI,CAACE,YAAY,CAACD,EAAE,CAAC,EAAE;gBACvB,OAAO3E,cAAc;cACzB;cACA,IAAI,CAAC,IAAI,CAACsD,WAAW,CAACqB,EAAE,CAAC,EAAE;gBACvB;cACJ;cACAD,CAAC,EAAE;YACP;YACA,OAAOnF,cAAc;UACzB;QACJ;MACJ;IACJ;EACJ,CAAC;EACDgB,gBAAgB,CAAC6C,GAAG,GAAG,UAAUyB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACjD,IAAIC,GAAG,GAAG/B,IAAI,CAACC,GAAG,CAACyB,EAAE,EAAE1B,IAAI,CAACC,GAAG,CAAC0B,EAAE,EAAE3B,IAAI,CAACC,GAAG,CAAC2B,EAAE,EAAEC,EAAE,CAAC,CAAC,CAAC;IACtD,IAAIC,EAAE,KAAKE,SAAS,EAAE;MAClB,OAAOD,GAAG;IACd,CAAC,MACI;MACD,OAAO/B,IAAI,CAACC,GAAG,CAAC8B,GAAG,EAAED,EAAE,CAAC;IAC5B;EACJ,CAAC;EACD1E,gBAAgB,CAACwD,YAAY,GAAG,UAAUN,UAAU,EAAEI,aAAa,EAAET,GAAG,EAAEO,IAAI,EAAE;IAC5E,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAI+B,OAAO,GAAIvB,aAAa,CAACR,CAAC,CAAC,GAAGF,IAAI,CAACmB,IAAI,CAACb,UAAU,CAACJ,CAAC,CAAC,CAAE;MAC3D,IAAID,GAAG,GAAGgC,OAAO,EAAE;QACfhC,GAAG,GAAGgC,OAAO;QACb/E,MAAM,CAACyD,IAAI,CAACH,IAAI,EAAE,CAAC,CAAC;MACxB;MACA,IAAIP,GAAG,KAAKgC,OAAO,EAAE;QACjBzB,IAAI,CAACN,CAAC,CAAC,GAAGM,IAAI,CAACN,CAAC,CAAC,GAAG,CAAC;MACzB;IACJ;IACA,OAAOD,GAAG;EACd,CAAC;EACD7C,gBAAgB,CAAC2D,eAAe,GAAG,UAAUP,IAAI,EAAE;IAC/C,IAAIM,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxBY,QAAQ,IAAIN,IAAI,CAACN,CAAC,CAAC;IACvB;IACA,OAAOY,QAAQ,IAAI,CAAC;EACxB,CAAC;EACD1D,gBAAgB,CAAC6D,OAAO,GAAG,UAAUiB,EAAE,EAAE;IACrC,OAAOA,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAAI8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC;EAC7D,CAAC;EACDhD,gBAAgB,CAAC8D,eAAe,GAAG,UAAUgB,EAAE,EAAE;IAC7C,OAAOA,EAAE,IAAI,GAAG,IAAIA,EAAE,IAAI,GAAG;EACjC,CAAC;EACD9E,gBAAgB,CAACgE,WAAW,GAAG,UAAUc,EAAE,EAAE;IACzC,OAAQA,EAAE,KAAK,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAC3B8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAAI8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAE,IACnD8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAAI8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAE;EAC5D,CAAC;EACDhD,gBAAgB,CAACiE,YAAY,GAAG,UAAUa,EAAE,EAAE;IAC1C,OAAQA,EAAE,KAAK,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAC3B8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAAI8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAE,IACnD8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAAI8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAE;EAC5D,CAAC;EACDhD,gBAAgB,CAAC+C,WAAW,GAAG,UAAU+B,EAAE,EAAE;IACzC,OAAQ,IAAI,CAACT,YAAY,CAACS,EAAE,CAAC,IACzBA,EAAE,KAAK,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IACvB8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAAI8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAE,IACnD8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAAI8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAE;EAC5D,CAAC;EACDhD,gBAAgB,CAACqE,YAAY,GAAG,UAAUS,EAAE,EAAE;IAC1C,OAAQA,EAAE,KAAK,EAAE;IAAI;IACjBA,EAAE,KAAK,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IACxB8B,EAAE,KAAK,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC;EAChC,CAAC;EACDhD,gBAAgB,CAACiD,eAAe,GAAG,UAAU6B,EAAE,EAAE;IAC7C,OAAOA,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC,IAAI8B,EAAE,IAAI,GAAG,CAAC9B,UAAU,CAAC,CAAC,CAAC;EAC7D,CAAC;EACDhD,gBAAgB,CAACkE,aAAa,GAAG,UAAUY,EAAE,EAAE;IAC3C,OAAO,KAAK,CAAC,CAAC;EAClB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI9E,gBAAgB,CAAC+E,8BAA8B,GAAG,UAAUzE,GAAG,EAAEiC,QAAQ,EAAE;IACvE,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAG,CAAC;IAAE;IACzC,IAAIX,GAAG,GAAGtB,GAAG,CAACe,MAAM;IACpB,IAAI2D,GAAG,GAAGzC,QAAQ;IAClB,OAAOyC,GAAG,GAAGpD,GAAG,IAAI,IAAI,CAACiC,OAAO,CAACvD,GAAG,CAAC0C,UAAU,CAACgC,GAAG,CAAC,CAAC,EAAE;MACnDA,GAAG,EAAE;IACT;IACA,OAAOA,GAAG,GAAGzC,QAAQ;EACzB,CAAC;EACDvC,gBAAgB,CAACiF,gBAAgB,GAAG,UAAUC,eAAe,EAAE;IAC3D,IAAIC,GAAG,GAAGpF,OAAO,CAACqF,WAAW,CAACF,eAAe,CAAClC,UAAU,CAAC,CAAC,CAAC,CAAC;IAC5DmC,GAAG,GAAG,MAAM,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,GAAGF,GAAG,CAAC9D,MAAM,CAAC,GAAG8D,GAAG;IAC/C,MAAM,IAAIG,KAAK,CAAC,qBAAqB,GAAGJ,eAAe,GAAG,MAAM,GAAGC,GAAG,GAAG,GAAG,CAAC;EACjF,CAAC;EACD,OAAOnF,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}