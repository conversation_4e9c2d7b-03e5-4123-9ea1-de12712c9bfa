{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport FormatException from '../../FormatException';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will\n * use blocks of differing sizes within one version, so, this encapsulates the parameters for\n * each set of blocks. It also holds the number of error-correction codewords per block since it\n * will be the same across all blocks within one version.</p>\n */\nvar ECBlocks = /** @class */function () {\n  function ECBlocks(ecCodewords, ecBlocks1, ecBlocks2) {\n    this.ecCodewords = ecCodewords;\n    this.ecBlocks = [ecBlocks1];\n    ecBlocks2 && this.ecBlocks.push(ecBlocks2);\n  }\n  ECBlocks.prototype.getECCodewords = function () {\n    return this.ecCodewords;\n  };\n  ECBlocks.prototype.getECBlocks = function () {\n    return this.ecBlocks;\n  };\n  return ECBlocks;\n}();\nexport { ECBlocks };\n/**\n * <p>Encapsulates the parameters for one error-correction block in one symbol version.\n * This includes the number of data codewords, and the number of times a block with these\n * parameters is used consecutively in the Data Matrix code version's format.</p>\n */\nvar ECB = /** @class */function () {\n  function ECB(count, dataCodewords) {\n    this.count = count;\n    this.dataCodewords = dataCodewords;\n  }\n  ECB.prototype.getCount = function () {\n    return this.count;\n  };\n  ECB.prototype.getDataCodewords = function () {\n    return this.dataCodewords;\n  };\n  return ECB;\n}();\nexport { ECB };\n/**\n * The Version object encapsulates attributes about a particular\n * size Data Matrix Code.\n *\n * <AUTHOR> (Brian Brown)\n */\nvar Version = /** @class */function () {\n  function Version(versionNumber, symbolSizeRows, symbolSizeColumns, dataRegionSizeRows, dataRegionSizeColumns, ecBlocks) {\n    var e_1, _a;\n    this.versionNumber = versionNumber;\n    this.symbolSizeRows = symbolSizeRows;\n    this.symbolSizeColumns = symbolSizeColumns;\n    this.dataRegionSizeRows = dataRegionSizeRows;\n    this.dataRegionSizeColumns = dataRegionSizeColumns;\n    this.ecBlocks = ecBlocks;\n    // Calculate the total number of codewords\n    var total = 0;\n    var ecCodewords = ecBlocks.getECCodewords();\n    var ecbArray = ecBlocks.getECBlocks();\n    try {\n      for (var ecbArray_1 = __values(ecbArray), ecbArray_1_1 = ecbArray_1.next(); !ecbArray_1_1.done; ecbArray_1_1 = ecbArray_1.next()) {\n        var ecBlock = ecbArray_1_1.value;\n        total += ecBlock.getCount() * (ecBlock.getDataCodewords() + ecCodewords);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (ecbArray_1_1 && !ecbArray_1_1.done && (_a = ecbArray_1.return)) _a.call(ecbArray_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    this.totalCodewords = total;\n  }\n  Version.prototype.getVersionNumber = function () {\n    return this.versionNumber;\n  };\n  Version.prototype.getSymbolSizeRows = function () {\n    return this.symbolSizeRows;\n  };\n  Version.prototype.getSymbolSizeColumns = function () {\n    return this.symbolSizeColumns;\n  };\n  Version.prototype.getDataRegionSizeRows = function () {\n    return this.dataRegionSizeRows;\n  };\n  Version.prototype.getDataRegionSizeColumns = function () {\n    return this.dataRegionSizeColumns;\n  };\n  Version.prototype.getTotalCodewords = function () {\n    return this.totalCodewords;\n  };\n  Version.prototype.getECBlocks = function () {\n    return this.ecBlocks;\n  };\n  /**\n   * <p>Deduces version information from Data Matrix dimensions.</p>\n   *\n   * @param numRows Number of rows in modules\n   * @param numColumns Number of columns in modules\n   * @return Version for a Data Matrix Code of those dimensions\n   * @throws FormatException if dimensions do correspond to a valid Data Matrix size\n   */\n  Version.getVersionForDimensions = function (numRows, numColumns) {\n    var e_2, _a;\n    if ((numRows & 0x01) !== 0 || (numColumns & 0x01) !== 0) {\n      throw new FormatException();\n    }\n    try {\n      for (var _b = __values(Version.VERSIONS), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var version = _c.value;\n        if (version.symbolSizeRows === numRows && version.symbolSizeColumns === numColumns) {\n          return version;\n        }\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    throw new FormatException();\n  };\n  //  @Override\n  Version.prototype.toString = function () {\n    return '' + this.versionNumber;\n  };\n  /**\n   * See ISO 16022:2006 5.5.1 Table 7\n   */\n  Version.buildVersions = function () {\n    return [new Version(1, 10, 10, 8, 8, new ECBlocks(5, new ECB(1, 3))), new Version(2, 12, 12, 10, 10, new ECBlocks(7, new ECB(1, 5))), new Version(3, 14, 14, 12, 12, new ECBlocks(10, new ECB(1, 8))), new Version(4, 16, 16, 14, 14, new ECBlocks(12, new ECB(1, 12))), new Version(5, 18, 18, 16, 16, new ECBlocks(14, new ECB(1, 18))), new Version(6, 20, 20, 18, 18, new ECBlocks(18, new ECB(1, 22))), new Version(7, 22, 22, 20, 20, new ECBlocks(20, new ECB(1, 30))), new Version(8, 24, 24, 22, 22, new ECBlocks(24, new ECB(1, 36))), new Version(9, 26, 26, 24, 24, new ECBlocks(28, new ECB(1, 44))), new Version(10, 32, 32, 14, 14, new ECBlocks(36, new ECB(1, 62))), new Version(11, 36, 36, 16, 16, new ECBlocks(42, new ECB(1, 86))), new Version(12, 40, 40, 18, 18, new ECBlocks(48, new ECB(1, 114))), new Version(13, 44, 44, 20, 20, new ECBlocks(56, new ECB(1, 144))), new Version(14, 48, 48, 22, 22, new ECBlocks(68, new ECB(1, 174))), new Version(15, 52, 52, 24, 24, new ECBlocks(42, new ECB(2, 102))), new Version(16, 64, 64, 14, 14, new ECBlocks(56, new ECB(2, 140))), new Version(17, 72, 72, 16, 16, new ECBlocks(36, new ECB(4, 92))), new Version(18, 80, 80, 18, 18, new ECBlocks(48, new ECB(4, 114))), new Version(19, 88, 88, 20, 20, new ECBlocks(56, new ECB(4, 144))), new Version(20, 96, 96, 22, 22, new ECBlocks(68, new ECB(4, 174))), new Version(21, 104, 104, 24, 24, new ECBlocks(56, new ECB(6, 136))), new Version(22, 120, 120, 18, 18, new ECBlocks(68, new ECB(6, 175))), new Version(23, 132, 132, 20, 20, new ECBlocks(62, new ECB(8, 163))), new Version(24, 144, 144, 22, 22, new ECBlocks(62, new ECB(8, 156), new ECB(2, 155))), new Version(25, 8, 18, 6, 16, new ECBlocks(7, new ECB(1, 5))), new Version(26, 8, 32, 6, 14, new ECBlocks(11, new ECB(1, 10))), new Version(27, 12, 26, 10, 24, new ECBlocks(14, new ECB(1, 16))), new Version(28, 12, 36, 10, 16, new ECBlocks(18, new ECB(1, 22))), new Version(29, 16, 36, 14, 16, new ECBlocks(24, new ECB(1, 32))), new Version(30, 16, 48, 14, 22, new ECBlocks(28, new ECB(1, 49)))];\n  };\n  Version.VERSIONS = Version.buildVersions();\n  return Version;\n}();\nexport default Version;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "FormatException", "ECBlocks", "ecCodewords", "ecBlocks1", "ecBlocks2", "ecBlocks", "push", "prototype", "getECCodewords", "getECBlocks", "ECB", "count", "dataCodewords", "getCount", "getDataCodewords", "Version", "versionNumber", "symbolSizeRows", "symbolSizeColumns", "dataRegionSizeRows", "dataRegionSizeColumns", "e_1", "_a", "total", "ecbArray", "ecbArray_1", "ecbArray_1_1", "ecBlock", "e_1_1", "error", "return", "totalCodewords", "getVersionNumber", "getSymbolSizeRows", "getSymbolSizeColumns", "getDataRegionSizeRows", "getDataRegionSizeColumns", "getTotalCodewords", "getVersionForDimensions", "numRows", "numColumns", "e_2", "_b", "VERSIONS", "_c", "version", "e_2_1", "toString", "buildVersions"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/decoder/Version.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport FormatException from '../../FormatException';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates a set of error-correction blocks in one symbol version. Most versions will\n * use blocks of differing sizes within one version, so, this encapsulates the parameters for\n * each set of blocks. It also holds the number of error-correction codewords per block since it\n * will be the same across all blocks within one version.</p>\n */\nvar ECBlocks = /** @class */ (function () {\n    function ECBlocks(ecCodewords, ecBlocks1, ecBlocks2) {\n        this.ecCodewords = ecCodewords;\n        this.ecBlocks = [ecBlocks1];\n        ecBlocks2 && this.ecBlocks.push(ecBlocks2);\n    }\n    ECBlocks.prototype.getECCodewords = function () {\n        return this.ecCodewords;\n    };\n    ECBlocks.prototype.getECBlocks = function () {\n        return this.ecBlocks;\n    };\n    return ECBlocks;\n}());\nexport { ECBlocks };\n/**\n * <p>Encapsulates the parameters for one error-correction block in one symbol version.\n * This includes the number of data codewords, and the number of times a block with these\n * parameters is used consecutively in the Data Matrix code version's format.</p>\n */\nvar ECB = /** @class */ (function () {\n    function ECB(count, dataCodewords) {\n        this.count = count;\n        this.dataCodewords = dataCodewords;\n    }\n    ECB.prototype.getCount = function () {\n        return this.count;\n    };\n    ECB.prototype.getDataCodewords = function () {\n        return this.dataCodewords;\n    };\n    return ECB;\n}());\nexport { ECB };\n/**\n * The Version object encapsulates attributes about a particular\n * size Data Matrix Code.\n *\n * <AUTHOR> (Brian Brown)\n */\nvar Version = /** @class */ (function () {\n    function Version(versionNumber, symbolSizeRows, symbolSizeColumns, dataRegionSizeRows, dataRegionSizeColumns, ecBlocks) {\n        var e_1, _a;\n        this.versionNumber = versionNumber;\n        this.symbolSizeRows = symbolSizeRows;\n        this.symbolSizeColumns = symbolSizeColumns;\n        this.dataRegionSizeRows = dataRegionSizeRows;\n        this.dataRegionSizeColumns = dataRegionSizeColumns;\n        this.ecBlocks = ecBlocks;\n        // Calculate the total number of codewords\n        var total = 0;\n        var ecCodewords = ecBlocks.getECCodewords();\n        var ecbArray = ecBlocks.getECBlocks();\n        try {\n            for (var ecbArray_1 = __values(ecbArray), ecbArray_1_1 = ecbArray_1.next(); !ecbArray_1_1.done; ecbArray_1_1 = ecbArray_1.next()) {\n                var ecBlock = ecbArray_1_1.value;\n                total += ecBlock.getCount() * (ecBlock.getDataCodewords() + ecCodewords);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecbArray_1_1 && !ecbArray_1_1.done && (_a = ecbArray_1.return)) _a.call(ecbArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        this.totalCodewords = total;\n    }\n    Version.prototype.getVersionNumber = function () {\n        return this.versionNumber;\n    };\n    Version.prototype.getSymbolSizeRows = function () {\n        return this.symbolSizeRows;\n    };\n    Version.prototype.getSymbolSizeColumns = function () {\n        return this.symbolSizeColumns;\n    };\n    Version.prototype.getDataRegionSizeRows = function () {\n        return this.dataRegionSizeRows;\n    };\n    Version.prototype.getDataRegionSizeColumns = function () {\n        return this.dataRegionSizeColumns;\n    };\n    Version.prototype.getTotalCodewords = function () {\n        return this.totalCodewords;\n    };\n    Version.prototype.getECBlocks = function () {\n        return this.ecBlocks;\n    };\n    /**\n     * <p>Deduces version information from Data Matrix dimensions.</p>\n     *\n     * @param numRows Number of rows in modules\n     * @param numColumns Number of columns in modules\n     * @return Version for a Data Matrix Code of those dimensions\n     * @throws FormatException if dimensions do correspond to a valid Data Matrix size\n     */\n    Version.getVersionForDimensions = function (numRows, numColumns) {\n        var e_2, _a;\n        if ((numRows & 0x01) !== 0 || (numColumns & 0x01) !== 0) {\n            throw new FormatException();\n        }\n        try {\n            for (var _b = __values(Version.VERSIONS), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var version = _c.value;\n                if (version.symbolSizeRows === numRows && version.symbolSizeColumns === numColumns) {\n                    return version;\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        throw new FormatException();\n    };\n    //  @Override\n    Version.prototype.toString = function () {\n        return '' + this.versionNumber;\n    };\n    /**\n     * See ISO 16022:2006 5.5.1 Table 7\n     */\n    Version.buildVersions = function () {\n        return [\n            new Version(1, 10, 10, 8, 8, new ECBlocks(5, new ECB(1, 3))),\n            new Version(2, 12, 12, 10, 10, new ECBlocks(7, new ECB(1, 5))),\n            new Version(3, 14, 14, 12, 12, new ECBlocks(10, new ECB(1, 8))),\n            new Version(4, 16, 16, 14, 14, new ECBlocks(12, new ECB(1, 12))),\n            new Version(5, 18, 18, 16, 16, new ECBlocks(14, new ECB(1, 18))),\n            new Version(6, 20, 20, 18, 18, new ECBlocks(18, new ECB(1, 22))),\n            new Version(7, 22, 22, 20, 20, new ECBlocks(20, new ECB(1, 30))),\n            new Version(8, 24, 24, 22, 22, new ECBlocks(24, new ECB(1, 36))),\n            new Version(9, 26, 26, 24, 24, new ECBlocks(28, new ECB(1, 44))),\n            new Version(10, 32, 32, 14, 14, new ECBlocks(36, new ECB(1, 62))),\n            new Version(11, 36, 36, 16, 16, new ECBlocks(42, new ECB(1, 86))),\n            new Version(12, 40, 40, 18, 18, new ECBlocks(48, new ECB(1, 114))),\n            new Version(13, 44, 44, 20, 20, new ECBlocks(56, new ECB(1, 144))),\n            new Version(14, 48, 48, 22, 22, new ECBlocks(68, new ECB(1, 174))),\n            new Version(15, 52, 52, 24, 24, new ECBlocks(42, new ECB(2, 102))),\n            new Version(16, 64, 64, 14, 14, new ECBlocks(56, new ECB(2, 140))),\n            new Version(17, 72, 72, 16, 16, new ECBlocks(36, new ECB(4, 92))),\n            new Version(18, 80, 80, 18, 18, new ECBlocks(48, new ECB(4, 114))),\n            new Version(19, 88, 88, 20, 20, new ECBlocks(56, new ECB(4, 144))),\n            new Version(20, 96, 96, 22, 22, new ECBlocks(68, new ECB(4, 174))),\n            new Version(21, 104, 104, 24, 24, new ECBlocks(56, new ECB(6, 136))),\n            new Version(22, 120, 120, 18, 18, new ECBlocks(68, new ECB(6, 175))),\n            new Version(23, 132, 132, 20, 20, new ECBlocks(62, new ECB(8, 163))),\n            new Version(24, 144, 144, 22, 22, new ECBlocks(62, new ECB(8, 156), new ECB(2, 155))),\n            new Version(25, 8, 18, 6, 16, new ECBlocks(7, new ECB(1, 5))),\n            new Version(26, 8, 32, 6, 14, new ECBlocks(11, new ECB(1, 10))),\n            new Version(27, 12, 26, 10, 24, new ECBlocks(14, new ECB(1, 16))),\n            new Version(28, 12, 36, 10, 16, new ECBlocks(18, new ECB(1, 22))),\n            new Version(29, 16, 36, 14, 16, new ECBlocks(24, new ECB(1, 32))),\n            new Version(30, 16, 48, 14, 22, new ECBlocks(28, new ECB(1, 49)))\n        ];\n    };\n    Version.VERSIONS = Version.buildVersions();\n    return Version;\n}());\nexport default Version;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,eAAe,MAAM,uBAAuB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACC,WAAW,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACjD,IAAI,CAACF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACG,QAAQ,GAAG,CAACF,SAAS,CAAC;IAC3BC,SAAS,IAAI,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACF,SAAS,CAAC;EAC9C;EACAH,QAAQ,CAACM,SAAS,CAACC,cAAc,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACN,WAAW;EAC3B,CAAC;EACDD,QAAQ,CAACM,SAAS,CAACE,WAAW,GAAG,YAAY;IACzC,OAAO,IAAI,CAACJ,QAAQ;EACxB,CAAC;EACD,OAAOJ,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,SAASA,QAAQ;AACjB;AACA;AACA;AACA;AACA;AACA,IAAIS,GAAG,GAAG,aAAe,YAAY;EACjC,SAASA,GAAGA,CAACC,KAAK,EAAEC,aAAa,EAAE;IAC/B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACAF,GAAG,CAACH,SAAS,CAACM,QAAQ,GAAG,YAAY;IACjC,OAAO,IAAI,CAACF,KAAK;EACrB,CAAC;EACDD,GAAG,CAACH,SAAS,CAACO,gBAAgB,GAAG,YAAY;IACzC,OAAO,IAAI,CAACF,aAAa;EAC7B,CAAC;EACD,OAAOF,GAAG;AACd,CAAC,CAAC,CAAE;AACJ,SAASA,GAAG;AACZ;AACA;AACA;AACA;AACA;AACA;AACA,IAAIK,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAACC,aAAa,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEf,QAAQ,EAAE;IACpH,IAAIgB,GAAG,EAAEC,EAAE;IACX,IAAI,CAACN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACf,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAIkB,KAAK,GAAG,CAAC;IACb,IAAIrB,WAAW,GAAGG,QAAQ,CAACG,cAAc,CAAC,CAAC;IAC3C,IAAIgB,QAAQ,GAAGnB,QAAQ,CAACI,WAAW,CAAC,CAAC;IACrC,IAAI;MACA,KAAK,IAAIgB,UAAU,GAAGtC,QAAQ,CAACqC,QAAQ,CAAC,EAAEE,YAAY,GAAGD,UAAU,CAAC7B,IAAI,CAAC,CAAC,EAAE,CAAC8B,YAAY,CAAC5B,IAAI,EAAE4B,YAAY,GAAGD,UAAU,CAAC7B,IAAI,CAAC,CAAC,EAAE;QAC9H,IAAI+B,OAAO,GAAGD,YAAY,CAAC7B,KAAK;QAChC0B,KAAK,IAAII,OAAO,CAACd,QAAQ,CAAC,CAAC,IAAIc,OAAO,CAACb,gBAAgB,CAAC,CAAC,GAAGZ,WAAW,CAAC;MAC5E;IACJ,CAAC,CACD,OAAO0B,KAAK,EAAE;MAAEP,GAAG,GAAG;QAAEQ,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,YAAY,IAAI,CAACA,YAAY,CAAC5B,IAAI,KAAKwB,EAAE,GAAGG,UAAU,CAACK,MAAM,CAAC,EAAER,EAAE,CAAC5B,IAAI,CAAC+B,UAAU,CAAC;MAC3F,CAAC,SACO;QAAE,IAAIJ,GAAG,EAAE,MAAMA,GAAG,CAACQ,KAAK;MAAE;IACxC;IACA,IAAI,CAACE,cAAc,GAAGR,KAAK;EAC/B;EACAR,OAAO,CAACR,SAAS,CAACyB,gBAAgB,GAAG,YAAY;IAC7C,OAAO,IAAI,CAAChB,aAAa;EAC7B,CAAC;EACDD,OAAO,CAACR,SAAS,CAAC0B,iBAAiB,GAAG,YAAY;IAC9C,OAAO,IAAI,CAAChB,cAAc;EAC9B,CAAC;EACDF,OAAO,CAACR,SAAS,CAAC2B,oBAAoB,GAAG,YAAY;IACjD,OAAO,IAAI,CAAChB,iBAAiB;EACjC,CAAC;EACDH,OAAO,CAACR,SAAS,CAAC4B,qBAAqB,GAAG,YAAY;IAClD,OAAO,IAAI,CAAChB,kBAAkB;EAClC,CAAC;EACDJ,OAAO,CAACR,SAAS,CAAC6B,wBAAwB,GAAG,YAAY;IACrD,OAAO,IAAI,CAAChB,qBAAqB;EACrC,CAAC;EACDL,OAAO,CAACR,SAAS,CAAC8B,iBAAiB,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACN,cAAc;EAC9B,CAAC;EACDhB,OAAO,CAACR,SAAS,CAACE,WAAW,GAAG,YAAY;IACxC,OAAO,IAAI,CAACJ,QAAQ;EACxB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIU,OAAO,CAACuB,uBAAuB,GAAG,UAAUC,OAAO,EAAEC,UAAU,EAAE;IAC7D,IAAIC,GAAG,EAAEnB,EAAE;IACX,IAAI,CAACiB,OAAO,GAAG,IAAI,MAAM,CAAC,IAAI,CAACC,UAAU,GAAG,IAAI,MAAM,CAAC,EAAE;MACrD,MAAM,IAAIxC,eAAe,CAAC,CAAC;IAC/B;IACA,IAAI;MACA,KAAK,IAAI0C,EAAE,GAAGvD,QAAQ,CAAC4B,OAAO,CAAC4B,QAAQ,CAAC,EAAEC,EAAE,GAAGF,EAAE,CAAC9C,IAAI,CAAC,CAAC,EAAE,CAACgD,EAAE,CAAC9C,IAAI,EAAE8C,EAAE,GAAGF,EAAE,CAAC9C,IAAI,CAAC,CAAC,EAAE;QAChF,IAAIiD,OAAO,GAAGD,EAAE,CAAC/C,KAAK;QACtB,IAAIgD,OAAO,CAAC5B,cAAc,KAAKsB,OAAO,IAAIM,OAAO,CAAC3B,iBAAiB,KAAKsB,UAAU,EAAE;UAChF,OAAOK,OAAO;QAClB;MACJ;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEL,GAAG,GAAG;QAAEZ,KAAK,EAAEiB;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,EAAE,IAAI,CAACA,EAAE,CAAC9C,IAAI,KAAKwB,EAAE,GAAGoB,EAAE,CAACZ,MAAM,CAAC,EAAER,EAAE,CAAC5B,IAAI,CAACgD,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAID,GAAG,EAAE,MAAMA,GAAG,CAACZ,KAAK;MAAE;IACxC;IACA,MAAM,IAAI7B,eAAe,CAAC,CAAC;EAC/B,CAAC;EACD;EACAe,OAAO,CAACR,SAAS,CAACwC,QAAQ,GAAG,YAAY;IACrC,OAAO,EAAE,GAAG,IAAI,CAAC/B,aAAa;EAClC,CAAC;EACD;AACJ;AACA;EACID,OAAO,CAACiC,aAAa,GAAG,YAAY;IAChC,OAAO,CACH,IAAIjC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,IAAId,QAAQ,CAAC,CAAC,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5D,IAAIK,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,CAAC,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC9D,IAAIK,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC/D,IAAIK,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChE,IAAIK,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChE,IAAIK,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChE,IAAIK,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChE,IAAIK,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChE,IAAIK,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACjE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACjE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACjE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAClE,IAAIK,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EACpE,IAAIK,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EACpE,IAAIK,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EACpE,IAAIK,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EACrF,IAAIK,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,CAAC,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7D,IAAIK,OAAO,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAC/D,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACjE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACjE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACjE,IAAIK,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAId,QAAQ,CAAC,EAAE,EAAE,IAAIS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CACpE;EACL,CAAC;EACDK,OAAO,CAAC4B,QAAQ,GAAG5B,OAAO,CAACiC,aAAa,CAAC,CAAC;EAC1C,OAAOjC,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}