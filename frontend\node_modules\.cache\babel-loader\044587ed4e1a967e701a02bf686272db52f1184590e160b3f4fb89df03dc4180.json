{"ast": null, "code": "import getUTCISO<PERSON>eekYear from \"../getUTCISOWeekYear/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function startOfUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getUTCISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setUTCFullYear(year, 0, 4);\n  fourthOfJanuary.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCISOWeek(fourthOfJanuary);\n  return date;\n}", "map": {"version": 3, "names": ["getUTCISOWeekYear", "startOfUTCISOWeek", "requiredArgs", "startOfUTCISOWeekYear", "dirtyDate", "arguments", "year", "fourthOfJanuary", "Date", "setUTCFullYear", "setUTCHours", "date"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/node_modules/date-fns/esm/_lib/startOfUTCISOWeekYear/index.js"], "sourcesContent": ["import getUTCISO<PERSON>eekYear from \"../getUTCISOWeekYear/index.js\";\nimport startOfUTCISOWeek from \"../startOfUTCISOWeek/index.js\";\nimport requiredArgs from \"../requiredArgs/index.js\";\nexport default function startOfUTCISOWeekYear(dirtyDate) {\n  requiredArgs(1, arguments);\n  var year = getUTCISOWeekYear(dirtyDate);\n  var fourthOfJanuary = new Date(0);\n  fourthOfJanuary.setUTCFullYear(year, 0, 4);\n  fourthOfJanuary.setUTCHours(0, 0, 0, 0);\n  var date = startOfUTCISOWeek(fourthOfJanuary);\n  return date;\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,YAAY,MAAM,0BAA0B;AACnD,eAAe,SAASC,qBAAqBA,CAACC,SAAS,EAAE;EACvDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,iBAAiB,CAACI,SAAS,CAAC;EACvC,IAAIG,eAAe,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EACjCD,eAAe,CAACE,cAAc,CAACH,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1CC,eAAe,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACvC,IAAIC,IAAI,GAAGV,iBAAiB,CAACM,eAAe,CAAC;EAC7C,OAAOI,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}