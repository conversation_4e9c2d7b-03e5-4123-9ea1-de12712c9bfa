{"ast": null, "code": "import StringUtils from '../common/StringUtils';\nvar StringBuilder = /** @class */function () {\n  function StringBuilder(value) {\n    if (value === void 0) {\n      value = '';\n    }\n    this.value = value;\n  }\n  StringBuilder.prototype.enableDecoding = function (encoding) {\n    this.encoding = encoding;\n    return this;\n  };\n  StringBuilder.prototype.append = function (s) {\n    if (typeof s === 'string') {\n      this.value += s.toString();\n    } else if (this.encoding) {\n      // use passed format (fromCharCode will return UTF8 encoding)\n      this.value += StringUtils.castAsNonUtf8Char(s, this.encoding);\n    } else {\n      // correctly converts from UTF-8, but not other encodings\n      this.value += String.fromCharCode(s);\n    }\n    return this;\n  };\n  StringBuilder.prototype.appendChars = function (str, offset, len) {\n    for (var i = offset; offset < offset + len; i++) {\n      this.append(str[i]);\n    }\n    return this;\n  };\n  StringBuilder.prototype.length = function () {\n    return this.value.length;\n  };\n  StringBuilder.prototype.charAt = function (n) {\n    return this.value.charAt(n);\n  };\n  StringBuilder.prototype.deleteCharAt = function (n) {\n    this.value = this.value.substr(0, n) + this.value.substring(n + 1);\n  };\n  StringBuilder.prototype.setCharAt = function (n, c) {\n    this.value = this.value.substr(0, n) + c + this.value.substr(n + 1);\n  };\n  StringBuilder.prototype.substring = function (start, end) {\n    return this.value.substring(start, end);\n  };\n  /**\n   * @note helper method for RSS Expanded\n   */\n  StringBuilder.prototype.setLengthToZero = function () {\n    this.value = '';\n  };\n  StringBuilder.prototype.toString = function () {\n    return this.value;\n  };\n  StringBuilder.prototype.insert = function (n, c) {\n    this.value = this.value.substring(0, n) + c + this.value.substring(n);\n  };\n  return StringBuilder;\n}();\nexport default StringBuilder;", "map": {"version": 3, "names": ["StringUtils", "StringBuilder", "value", "prototype", "enableDecoding", "encoding", "append", "s", "toString", "castAsNonUtf8Char", "String", "fromCharCode", "appendChars", "str", "offset", "len", "i", "length", "char<PERSON>t", "n", "deleteCharAt", "substr", "substring", "setCharAt", "c", "start", "end", "setLengthToZero", "insert"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/StringBuilder.js"], "sourcesContent": ["import StringUtils from '../common/StringUtils';\nvar StringBuilder = /** @class */ (function () {\n    function StringBuilder(value) {\n        if (value === void 0) { value = ''; }\n        this.value = value;\n    }\n    StringBuilder.prototype.enableDecoding = function (encoding) {\n        this.encoding = encoding;\n        return this;\n    };\n    StringBuilder.prototype.append = function (s) {\n        if (typeof s === 'string') {\n            this.value += s.toString();\n        }\n        else if (this.encoding) {\n            // use passed format (fromCharCode will return UTF8 encoding)\n            this.value += StringUtils.castAsNonUtf8Char(s, this.encoding);\n        }\n        else {\n            // correctly converts from UTF-8, but not other encodings\n            this.value += String.fromCharCode(s);\n        }\n        return this;\n    };\n    StringBuilder.prototype.appendChars = function (str, offset, len) {\n        for (var i = offset; offset < offset + len; i++) {\n            this.append(str[i]);\n        }\n        return this;\n    };\n    StringBuilder.prototype.length = function () {\n        return this.value.length;\n    };\n    StringBuilder.prototype.charAt = function (n) {\n        return this.value.charAt(n);\n    };\n    StringBuilder.prototype.deleteCharAt = function (n) {\n        this.value = this.value.substr(0, n) + this.value.substring(n + 1);\n    };\n    StringBuilder.prototype.setCharAt = function (n, c) {\n        this.value = this.value.substr(0, n) + c + this.value.substr(n + 1);\n    };\n    StringBuilder.prototype.substring = function (start, end) {\n        return this.value.substring(start, end);\n    };\n    /**\n     * @note helper method for RSS Expanded\n     */\n    StringBuilder.prototype.setLengthToZero = function () {\n        this.value = '';\n    };\n    StringBuilder.prototype.toString = function () {\n        return this.value;\n    };\n    StringBuilder.prototype.insert = function (n, c) {\n        this.value = this.value.substring(0, n) + c + this.value.substring(n);\n    };\n    return StringBuilder;\n}());\nexport default StringBuilder;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,uBAAuB;AAC/C,IAAIC,aAAa,GAAG,aAAe,YAAY;EAC3C,SAASA,aAAaA,CAACC,KAAK,EAAE;IAC1B,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,EAAE;IAAE;IACpC,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACAD,aAAa,CAACE,SAAS,CAACC,cAAc,GAAG,UAAUC,QAAQ,EAAE;IACzD,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,OAAO,IAAI;EACf,CAAC;EACDJ,aAAa,CAACE,SAAS,CAACG,MAAM,GAAG,UAAUC,CAAC,EAAE;IAC1C,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACvB,IAAI,CAACL,KAAK,IAAIK,CAAC,CAACC,QAAQ,CAAC,CAAC;IAC9B,CAAC,MACI,IAAI,IAAI,CAACH,QAAQ,EAAE;MACpB;MACA,IAAI,CAACH,KAAK,IAAIF,WAAW,CAACS,iBAAiB,CAACF,CAAC,EAAE,IAAI,CAACF,QAAQ,CAAC;IACjE,CAAC,MACI;MACD;MACA,IAAI,CAACH,KAAK,IAAIQ,MAAM,CAACC,YAAY,CAACJ,CAAC,CAAC;IACxC;IACA,OAAO,IAAI;EACf,CAAC;EACDN,aAAa,CAACE,SAAS,CAACS,WAAW,GAAG,UAAUC,GAAG,EAAEC,MAAM,EAAEC,GAAG,EAAE;IAC9D,KAAK,IAAIC,CAAC,GAAGF,MAAM,EAAEA,MAAM,GAAGA,MAAM,GAAGC,GAAG,EAAEC,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACV,MAAM,CAACO,GAAG,CAACG,CAAC,CAAC,CAAC;IACvB;IACA,OAAO,IAAI;EACf,CAAC;EACDf,aAAa,CAACE,SAAS,CAACc,MAAM,GAAG,YAAY;IACzC,OAAO,IAAI,CAACf,KAAK,CAACe,MAAM;EAC5B,CAAC;EACDhB,aAAa,CAACE,SAAS,CAACe,MAAM,GAAG,UAAUC,CAAC,EAAE;IAC1C,OAAO,IAAI,CAACjB,KAAK,CAACgB,MAAM,CAACC,CAAC,CAAC;EAC/B,CAAC;EACDlB,aAAa,CAACE,SAAS,CAACiB,YAAY,GAAG,UAAUD,CAAC,EAAE;IAChD,IAAI,CAACjB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACmB,MAAM,CAAC,CAAC,EAAEF,CAAC,CAAC,GAAG,IAAI,CAACjB,KAAK,CAACoB,SAAS,CAACH,CAAC,GAAG,CAAC,CAAC;EACtE,CAAC;EACDlB,aAAa,CAACE,SAAS,CAACoB,SAAS,GAAG,UAAUJ,CAAC,EAAEK,CAAC,EAAE;IAChD,IAAI,CAACtB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACmB,MAAM,CAAC,CAAC,EAAEF,CAAC,CAAC,GAAGK,CAAC,GAAG,IAAI,CAACtB,KAAK,CAACmB,MAAM,CAACF,CAAC,GAAG,CAAC,CAAC;EACvE,CAAC;EACDlB,aAAa,CAACE,SAAS,CAACmB,SAAS,GAAG,UAAUG,KAAK,EAAEC,GAAG,EAAE;IACtD,OAAO,IAAI,CAACxB,KAAK,CAACoB,SAAS,CAACG,KAAK,EAAEC,GAAG,CAAC;EAC3C,CAAC;EACD;AACJ;AACA;EACIzB,aAAa,CAACE,SAAS,CAACwB,eAAe,GAAG,YAAY;IAClD,IAAI,CAACzB,KAAK,GAAG,EAAE;EACnB,CAAC;EACDD,aAAa,CAACE,SAAS,CAACK,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACN,KAAK;EACrB,CAAC;EACDD,aAAa,CAACE,SAAS,CAACyB,MAAM,GAAG,UAAUT,CAAC,EAAEK,CAAC,EAAE;IAC7C,IAAI,CAACtB,KAAK,GAAG,IAAI,CAACA,KAAK,CAACoB,SAAS,CAAC,CAAC,EAAEH,CAAC,CAAC,GAAGK,CAAC,GAAG,IAAI,CAACtB,KAAK,CAACoB,SAAS,CAACH,CAAC,CAAC;EACzE,CAAC;EACD,OAAOlB,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}