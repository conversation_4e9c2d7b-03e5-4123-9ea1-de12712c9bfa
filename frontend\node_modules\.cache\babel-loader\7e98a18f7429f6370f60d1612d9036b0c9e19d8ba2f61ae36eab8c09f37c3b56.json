{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Learning Projects\\\\uni-core-business-suite_v3\\\\frontend\\\\src\\\\pages\\\\BankPaymentsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Card, CardContent, Typography, Button, Grid, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination, Chip, IconButton, Tooltip } from \"@mui/material\";\nimport { Add as AddIcon, Edit as EditIcon, Visibility as ViewIcon, Delete as DeleteIcon, Check as ApproveIcon, PostAdd as PostIcon } from \"@mui/icons-material\";\nimport { useNavigate } from \"react-router-dom\";\nimport axios from \"../utils/axiosConfig\";\nimport { formatCurrency } from \"../utils/numberUtils\";\nimport BankPaymentVoucherForm from \"../components/BankPaymentVoucherForm\";\nimport Breadcrumb from \"../components/Breadcrumb\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BankPaymentsPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [vouchers, setVouchers] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(10);\n  const [totalVouchers, setTotalVouchers] = useState(0);\n  useEffect(() => {\n    fetchVouchers();\n  }, [page, rowsPerPage]);\n  const fetchVouchers = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/payment-vouchers?voucherType=BP&page=${page + 1}&limit=${rowsPerPage}`);\n      setVouchers(response.data.vouchers);\n      setTotalVouchers(response.data.pagination.totalVouchers);\n    } catch (error) {\n      console.error(\"Error fetching bank payment vouchers:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChangePage = (event, newPage) => {\n    setPage(newPage);\n  };\n  const handleChangeRowsPerPage = event => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n  const handleDeleteVoucher = async voucherId => {\n    if (window.confirm(\"Are you sure you want to delete this voucher?\")) {\n      try {\n        await axios.delete(`/api/payment-vouchers/${voucherId}`);\n        fetchVouchers();\n      } catch (error) {\n        console.error(\"Error deleting voucher:\", error);\n        alert(\"Failed to delete voucher\");\n      }\n    }\n  };\n  const handleApproveVoucher = async voucherId => {\n    try {\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\n      fetchVouchers();\n    } catch (error) {\n      console.error(\"Error approving voucher:\", error);\n      alert(\"Failed to approve voucher\");\n    }\n  };\n  const handlePostVoucher = async voucherId => {\n    try {\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\n      fetchVouchers();\n    } catch (error) {\n      console.error(\"Error posting voucher:\", error);\n      alert(\"Failed to post voucher\");\n    }\n  };\n  const getStatusChip = status => {\n    const statusConfig = {\n      Draft: {\n        color: 'default',\n        label: 'Draft'\n      },\n      Approved: {\n        color: 'warning',\n        label: 'Approved'\n      },\n      Posted: {\n        color: 'success',\n        label: 'Posted'\n      },\n      Cancelled: {\n        color: 'error',\n        label: 'Cancelled'\n      }\n    };\n    const config = statusConfig[status] || {\n      color: 'default',\n      label: status\n    };\n    return /*#__PURE__*/_jsxDEV(Chip, {\n      size: \"small\",\n      color: config.color,\n      label: config.label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 12\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Breadcrumb, {\n      items: [{\n        label: \"Home\",\n        link: \"/\"\n      }, {\n        label: \"Accounting\",\n        link: \"/accounting\"\n      }, {\n        label: \"Bank Payments\",\n        link: \"/accounting/bank-payments\"\n      }]\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              gutterBottom: true,\n              children: \"Bank Payments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              color: \"text.secondary\",\n              children: \"Record payments made through bank accounts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 26\n            }, this),\n            onClick: () => navigate('/manage-bank-payment'),\n            children: \"New Bank Payment\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Voucher No.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Bank Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Paid To\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"right\",\n                  children: \"Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 161,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: loading ? /*#__PURE__*/_jsxDEV(TableRow, {\n                children: /*#__PURE__*/_jsxDEV(TableCell, {\n                  colSpan: 7,\n                  align: \"center\",\n                  children: \"Loading...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 19\n              }, this) : vouchers.length === 0 ? /*#__PURE__*/_jsxDEV(TableRow, {\n                children: /*#__PURE__*/_jsxDEV(TableCell, {\n                  colSpan: 7,\n                  align: \"center\",\n                  children: \"No bank payment vouchers found\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 170,\n                columnNumber: 19\n              }, this) : vouchers.map(voucher => {\n                var _voucher$fromAccountI, _voucher$toAccountId;\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: voucher.voucherNumber\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: new Date(voucher.transactionDate).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: (_voucher$fromAccountI = voucher.fromAccountId) === null || _voucher$fromAccountI === void 0 ? void 0 : _voucher$fromAccountI.accountName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: (_voucher$toAccountId = voucher.toAccountId) === null || _voucher$toAccountId === void 0 ? void 0 : _voucher$toAccountId.accountName\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"right\",\n                    children: formatCurrency(voucher.amount)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 180,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    children: getStatusChip(voucher.status)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"View\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"primary\",\n                        onClick: () => navigate(`/manage-bank-payment?id=${voucher._id}&view=true`),\n                        children: /*#__PURE__*/_jsxDEV(ViewIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 189,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 25\n                    }, this), voucher.status === 'Draft' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                        title: \"Edit\",\n                        children: /*#__PURE__*/_jsxDEV(IconButton, {\n                          size: \"small\",\n                          color: \"primary\",\n                          onClick: () => navigate(`/manage-bank-payment?id=${voucher._id}`),\n                          children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 201,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 196,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 195,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                        title: \"Delete\",\n                        children: /*#__PURE__*/_jsxDEV(IconButton, {\n                          size: \"small\",\n                          color: \"error\",\n                          onClick: () => handleDeleteVoucher(voucher._id),\n                          children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 211,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 206,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 205,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                        title: \"Approve\",\n                        children: /*#__PURE__*/_jsxDEV(IconButton, {\n                          size: \"small\",\n                          color: \"warning\",\n                          onClick: () => handleApproveVoucher(voucher._id),\n                          children: /*#__PURE__*/_jsxDEV(ApproveIcon, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 221,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 216,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 215,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true), voucher.status === 'Approved' && /*#__PURE__*/_jsxDEV(Tooltip, {\n                      title: \"Post to Ledger\",\n                      children: /*#__PURE__*/_jsxDEV(IconButton, {\n                        size: \"small\",\n                        color: \"success\",\n                        onClick: () => handlePostVoucher(voucher._id),\n                        children: /*#__PURE__*/_jsxDEV(PostIcon, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 234,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 228,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this)]\n                }, voucher._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TablePagination, {\n          rowsPerPageOptions: [5, 10, 25],\n          component: \"div\",\n          count: totalVouchers,\n          rowsPerPage: rowsPerPage,\n          page: page,\n          onPageChange: handleChangePage,\n          onRowsPerPageChange: handleChangeRowsPerPage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(BankPaymentsPage, \"cmcrIsqBfgX3/bXa9OWDY8vFYTg=\", false, function () {\n  return [useNavigate];\n});\n_c = BankPaymentsPage;\nexport default BankPaymentsPage;\nvar _c;\n$RefreshReg$(_c, \"BankPaymentsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Grid", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "TablePagination", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "Add", "AddIcon", "Edit", "EditIcon", "Visibility", "ViewIcon", "Delete", "DeleteIcon", "Check", "ApproveIcon", "PostAdd", "PostIcon", "useNavigate", "axios", "formatCurrency", "BankPaymentVoucherForm", "Breadcrumb", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BankPaymentsPage", "_s", "navigate", "vouchers", "setVouchers", "loading", "setLoading", "page", "setPage", "rowsPerPage", "setRowsPerPage", "totalVouchers", "setTotalVouchers", "fetchVouchers", "response", "get", "data", "pagination", "error", "console", "handleChangePage", "event", "newPage", "handleChangeRowsPerPage", "parseInt", "target", "value", "handleDeleteVoucher", "voucherId", "window", "confirm", "delete", "alert", "handleApproveVoucher", "put", "handlePostVoucher", "getStatusChip", "status", "statusConfig", "Draft", "color", "label", "Approved", "Posted", "Cancelled", "config", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "items", "link", "sx", "mb", "display", "justifyContent", "alignItems", "variant", "gutterBottom", "startIcon", "onClick", "component", "align", "colSpan", "length", "map", "voucher", "_voucher$fromAccountI", "_voucher$toAccountId", "voucherNumber", "Date", "transactionDate", "toLocaleDateString", "fromAccountId", "accountName", "toAccountId", "amount", "title", "_id", "rowsPerPageOptions", "count", "onPageChange", "onRowsPerPageChange", "_c", "$RefreshReg$"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/pages/BankPaymentsPage.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  Button,\r\n  Grid,\r\n  Paper,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  TablePagination,\r\n  Chip,\r\n  IconButton,\r\n  Tooltip\r\n} from \"@mui/material\";\r\nimport {\r\n  Add as AddIcon,\r\n  Edit as EditIcon,\r\n  Visibility as ViewIcon,\r\n  Delete as DeleteIcon,\r\n  Check as ApproveIcon,\r\n  PostAdd as PostIcon\r\n} from \"@mui/icons-material\";\r\nimport { useNavigate } from \"react-router-dom\";\r\nimport axios from \"../utils/axiosConfig\";\r\nimport { formatCurrency } from \"../utils/numberUtils\";\r\nimport BankPaymentVoucherForm from \"../components/BankPaymentVoucherForm\";\r\nimport Breadcrumb from \"../components/Breadcrumb\";\r\n\r\nconst BankPaymentsPage = () => {\r\n  const navigate = useNavigate();\r\n  const [vouchers, setVouchers] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [page, setPage] = useState(0);\r\n  const [rowsPerPage, setRowsPerPage] = useState(10);\r\n  const [totalVouchers, setTotalVouchers] = useState(0);\r\n\r\n  useEffect(() => {\r\n    fetchVouchers();\r\n  }, [page, rowsPerPage]);\r\n\r\n  const fetchVouchers = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await axios.get(`/api/payment-vouchers?voucherType=BP&page=${page + 1}&limit=${rowsPerPage}`);\r\n      setVouchers(response.data.vouchers);\r\n      setTotalVouchers(response.data.pagination.totalVouchers);\r\n    } catch (error) {\r\n      console.error(\"Error fetching bank payment vouchers:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChangePage = (event, newPage) => {\r\n    setPage(newPage);\r\n  };\r\n\r\n  const handleChangeRowsPerPage = (event) => {\r\n    setRowsPerPage(parseInt(event.target.value, 10));\r\n    setPage(0);\r\n  };\r\n\r\n\r\n\r\n  const handleDeleteVoucher = async (voucherId) => {\r\n    if (window.confirm(\"Are you sure you want to delete this voucher?\")) {\r\n      try {\r\n        await axios.delete(`/api/payment-vouchers/${voucherId}`);\r\n        fetchVouchers();\r\n      } catch (error) {\r\n        console.error(\"Error deleting voucher:\", error);\r\n        alert(\"Failed to delete voucher\");\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleApproveVoucher = async (voucherId) => {\r\n    try {\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\r\n      fetchVouchers();\r\n    } catch (error) {\r\n      console.error(\"Error approving voucher:\", error);\r\n      alert(\"Failed to approve voucher\");\r\n    }\r\n  };\r\n\r\n  const handlePostVoucher = async (voucherId) => {\r\n    try {\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\r\n      fetchVouchers();\r\n    } catch (error) {\r\n      console.error(\"Error posting voucher:\", error);\r\n      alert(\"Failed to post voucher\");\r\n    }\r\n  };\r\n\r\n  const getStatusChip = (status) => {\r\n    const statusConfig = {\r\n      Draft: { color: 'default', label: 'Draft' },\r\n      Approved: { color: 'warning', label: 'Approved' },\r\n      Posted: { color: 'success', label: 'Posted' },\r\n      Cancelled: { color: 'error', label: 'Cancelled' }\r\n    };\r\n    \r\n    const config = statusConfig[status] || { color: 'default', label: status };\r\n    return <Chip size=\"small\" color={config.color} label={config.label} />;\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <Breadcrumb\r\n        items={[\r\n          { label: \"Home\", link: \"/\" },\r\n          { label: \"Accounting\", link: \"/accounting\" },\r\n          { label: \"Bank Payments\", link: \"/accounting/bank-payments\" }\r\n        ]}\r\n      />\r\n      \r\n      {/* Header */}\r\n      <Card sx={{ mb: 3 }}>\r\n        <CardContent>\r\n          <Box sx={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"center\" }}>\r\n            <Box>\r\n              <Typography variant=\"h4\" gutterBottom>\r\n                Bank Payments\r\n              </Typography>\r\n              <Typography variant=\"body1\" color=\"text.secondary\">\r\n                Record payments made through bank accounts\r\n              </Typography>\r\n            </Box>\r\n            <Button\r\n              variant=\"contained\"\r\n              startIcon={<AddIcon />}\r\n              onClick={() => navigate('/manage-bank-payment')}\r\n            >\r\n              New Bank Payment\r\n            </Button>\r\n          </Box>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {/* Voucher List */}\r\n      <Card>\r\n        <CardContent>\r\n          <TableContainer component={Paper}>\r\n            <Table>\r\n              <TableHead>\r\n                <TableRow>\r\n                  <TableCell>Voucher No.</TableCell>\r\n                  <TableCell>Date</TableCell>\r\n                  <TableCell>Bank Account</TableCell>\r\n                  <TableCell>Paid To</TableCell>\r\n                  <TableCell align=\"right\">Amount</TableCell>\r\n                  <TableCell>Status</TableCell>\r\n                  <TableCell align=\"center\">Actions</TableCell>\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                {loading ? (\r\n                  <TableRow>\r\n                    <TableCell colSpan={7} align=\"center\">Loading...</TableCell>\r\n                  </TableRow>\r\n                ) : vouchers.length === 0 ? (\r\n                  <TableRow>\r\n                    <TableCell colSpan={7} align=\"center\">No bank payment vouchers found</TableCell>\r\n                  </TableRow>\r\n                ) : (\r\n                  vouchers.map((voucher) => (\r\n                    <TableRow key={voucher._id}>\r\n                      <TableCell>{voucher.voucherNumber}</TableCell>\r\n                      <TableCell>{new Date(voucher.transactionDate).toLocaleDateString()}</TableCell>\r\n                      <TableCell>{voucher.fromAccountId?.accountName}</TableCell>\r\n                      <TableCell>{voucher.toAccountId?.accountName}</TableCell>\r\n                      <TableCell align=\"right\">{formatCurrency(voucher.amount)}</TableCell>\r\n                      <TableCell>{getStatusChip(voucher.status)}</TableCell>\r\n                      <TableCell align=\"center\">\r\n                        <Tooltip title=\"View\">\r\n                          <IconButton\r\n                            size=\"small\"\r\n                            color=\"primary\"\r\n                            onClick={() => navigate(`/manage-bank-payment?id=${voucher._id}&view=true`)}\r\n                          >\r\n                            <ViewIcon />\r\n                          </IconButton>\r\n                        </Tooltip>\r\n                        \r\n                        {voucher.status === 'Draft' && (\r\n                          <>\r\n                            <Tooltip title=\"Edit\">\r\n                              <IconButton\r\n                                size=\"small\"\r\n                                color=\"primary\"\r\n                                onClick={() => navigate(`/manage-bank-payment?id=${voucher._id}`)}\r\n                              >\r\n                                <EditIcon />\r\n                              </IconButton>\r\n                            </Tooltip>\r\n                            \r\n                            <Tooltip title=\"Delete\">\r\n                              <IconButton\r\n                                size=\"small\"\r\n                                color=\"error\"\r\n                                onClick={() => handleDeleteVoucher(voucher._id)}\r\n                              >\r\n                                <DeleteIcon />\r\n                              </IconButton>\r\n                            </Tooltip>\r\n                            \r\n                            <Tooltip title=\"Approve\">\r\n                              <IconButton\r\n                                size=\"small\"\r\n                                color=\"warning\"\r\n                                onClick={() => handleApproveVoucher(voucher._id)}\r\n                              >\r\n                                <ApproveIcon />\r\n                              </IconButton>\r\n                            </Tooltip>\r\n                          </>\r\n                        )}\r\n                        \r\n                        {voucher.status === 'Approved' && (\r\n                          <Tooltip title=\"Post to Ledger\">\r\n                            <IconButton\r\n                              size=\"small\"\r\n                              color=\"success\"\r\n                              onClick={() => handlePostVoucher(voucher._id)}\r\n                            >\r\n                              <PostIcon />\r\n                            </IconButton>\r\n                          </Tooltip>\r\n                        )}\r\n                      </TableCell>\r\n                    </TableRow>\r\n                  ))\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n          \r\n          <TablePagination\r\n            rowsPerPageOptions={[5, 10, 25]}\r\n            component=\"div\"\r\n            count={totalVouchers}\r\n            rowsPerPage={rowsPerPage}\r\n            page={page}\r\n            onPageChange={handleChangePage}\r\n            onRowsPerPageChange={handleChangeRowsPerPage}\r\n          />\r\n        </CardContent>\r\n      </Card>\r\n\r\n\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default BankPaymentsPage; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,eAAe,EACfC,IAAI,EACJC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,IAAIC,OAAO,EACdC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,QAAQ,EACtBC,MAAM,IAAIC,UAAU,EACpBC,KAAK,IAAIC,WAAW,EACpBC,OAAO,IAAIC,QAAQ,QACd,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,UAAU,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6C,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC+C,IAAI,EAAEC,OAAO,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAC,CAAC,CAAC;EAErDC,SAAS,CAAC,MAAM;IACdoD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACN,IAAI,EAAEE,WAAW,CAAC,CAAC;EAEvB,MAAMI,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAMtB,KAAK,CAACuB,GAAG,CAAC,6CAA6CR,IAAI,GAAG,CAAC,UAAUE,WAAW,EAAE,CAAC;MAC9GL,WAAW,CAACU,QAAQ,CAACE,IAAI,CAACb,QAAQ,CAAC;MACnCS,gBAAgB,CAACE,QAAQ,CAACE,IAAI,CAACC,UAAU,CAACN,aAAa,CAAC;IAC1D,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAC/D,CAAC,SAAS;MACRZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,OAAO,KAAK;IAC3Cd,OAAO,CAACc,OAAO,CAAC;EAClB,CAAC;EAED,MAAMC,uBAAuB,GAAIF,KAAK,IAAK;IACzCX,cAAc,CAACc,QAAQ,CAACH,KAAK,CAACI,MAAM,CAACC,KAAK,EAAE,EAAE,CAAC,CAAC;IAChDlB,OAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EAID,MAAMmB,mBAAmB,GAAG,MAAOC,SAAS,IAAK;IAC/C,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnE,IAAI;QACF,MAAMtC,KAAK,CAACuC,MAAM,CAAC,yBAAyBH,SAAS,EAAE,CAAC;QACxDf,aAAa,CAAC,CAAC;MACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/Cc,KAAK,CAAC,0BAA0B,CAAC;MACnC;IACF;EACF,CAAC;EAED,MAAMC,oBAAoB,GAAG,MAAOL,SAAS,IAAK;IAChD,IAAI;MACF,MAAMpC,KAAK,CAAC0C,GAAG,CAAC,yBAAyBN,SAAS,UAAU,CAAC;MAC7Df,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDc,KAAK,CAAC,2BAA2B,CAAC;IACpC;EACF,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAOP,SAAS,IAAK;IAC7C,IAAI;MACF,MAAMpC,KAAK,CAAC0C,GAAG,CAAC,yBAAyBN,SAAS,OAAO,CAAC;MAC1Df,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9Cc,KAAK,CAAC,wBAAwB,CAAC;IACjC;EACF,CAAC;EAED,MAAMI,aAAa,GAAIC,MAAM,IAAK;IAChC,MAAMC,YAAY,GAAG;MACnBC,KAAK,EAAE;QAAEC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAQ,CAAC;MAC3CC,QAAQ,EAAE;QAAEF,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAW,CAAC;MACjDE,MAAM,EAAE;QAAEH,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAS,CAAC;MAC7CG,SAAS,EAAE;QAAEJ,KAAK,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAY;IAClD,CAAC;IAED,MAAMI,MAAM,GAAGP,YAAY,CAACD,MAAM,CAAC,IAAI;MAAEG,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAEJ;IAAO,CAAC;IAC1E,oBAAOxC,OAAA,CAACrB,IAAI;MAACsE,IAAI,EAAC,OAAO;MAACN,KAAK,EAAEK,MAAM,CAACL,KAAM;MAACC,KAAK,EAAEI,MAAM,CAACJ;IAAM;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxE,CAAC;EAED,oBACErD,OAAA,CAACnC,GAAG;IAAAyF,QAAA,gBACFtD,OAAA,CAACF,UAAU;MACTyD,KAAK,EAAE,CACL;QAAEX,KAAK,EAAE,MAAM;QAAEY,IAAI,EAAE;MAAI,CAAC,EAC5B;QAAEZ,KAAK,EAAE,YAAY;QAAEY,IAAI,EAAE;MAAc,CAAC,EAC5C;QAAEZ,KAAK,EAAE,eAAe;QAAEY,IAAI,EAAE;MAA4B,CAAC;IAC7D;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGFrD,OAAA,CAAClC,IAAI;MAAC2F,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,eAClBtD,OAAA,CAACjC,WAAW;QAAAuF,QAAA,eACVtD,OAAA,CAACnC,GAAG;UAAC4F,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAP,QAAA,gBAClFtD,OAAA,CAACnC,GAAG;YAAAyF,QAAA,gBACFtD,OAAA,CAAChC,UAAU;cAAC8F,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAT,QAAA,EAAC;YAEtC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbrD,OAAA,CAAChC,UAAU;cAAC8F,OAAO,EAAC,OAAO;cAACnB,KAAK,EAAC,gBAAgB;cAAAW,QAAA,EAAC;YAEnD;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNrD,OAAA,CAAC/B,MAAM;YACL6F,OAAO,EAAC,WAAW;YACnBE,SAAS,eAAEhE,OAAA,CAACjB,OAAO;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBY,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,sBAAsB,CAAE;YAAAiD,QAAA,EACjD;UAED;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPrD,OAAA,CAAClC,IAAI;MAAAwF,QAAA,eACHtD,OAAA,CAACjC,WAAW;QAAAuF,QAAA,gBACVtD,OAAA,CAACzB,cAAc;UAAC2F,SAAS,EAAE/F,KAAM;UAAAmF,QAAA,eAC/BtD,OAAA,CAAC5B,KAAK;YAAAkF,QAAA,gBACJtD,OAAA,CAACxB,SAAS;cAAA8E,QAAA,eACRtD,OAAA,CAACvB,QAAQ;gBAAA6E,QAAA,gBACPtD,OAAA,CAAC1B,SAAS;kBAAAgF,QAAA,EAAC;gBAAW;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClCrD,OAAA,CAAC1B,SAAS;kBAAAgF,QAAA,EAAC;gBAAI;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC3BrD,OAAA,CAAC1B,SAAS;kBAAAgF,QAAA,EAAC;gBAAY;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnCrD,OAAA,CAAC1B,SAAS;kBAAAgF,QAAA,EAAC;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC9BrD,OAAA,CAAC1B,SAAS;kBAAC6F,KAAK,EAAC,OAAO;kBAAAb,QAAA,EAAC;gBAAM;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC3CrD,OAAA,CAAC1B,SAAS;kBAAAgF,QAAA,EAAC;gBAAM;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC7BrD,OAAA,CAAC1B,SAAS;kBAAC6F,KAAK,EAAC,QAAQ;kBAAAb,QAAA,EAAC;gBAAO;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZrD,OAAA,CAAC3B,SAAS;cAAAiF,QAAA,EACP9C,OAAO,gBACNR,OAAA,CAACvB,QAAQ;gBAAA6E,QAAA,eACPtD,OAAA,CAAC1B,SAAS;kBAAC8F,OAAO,EAAE,CAAE;kBAACD,KAAK,EAAC,QAAQ;kBAAAb,QAAA,EAAC;gBAAU;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,GACT/C,QAAQ,CAAC+D,MAAM,KAAK,CAAC,gBACvBrE,OAAA,CAACvB,QAAQ;gBAAA6E,QAAA,eACPtD,OAAA,CAAC1B,SAAS;kBAAC8F,OAAO,EAAE,CAAE;kBAACD,KAAK,EAAC,QAAQ;kBAAAb,QAAA,EAAC;gBAA8B;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,GAEX/C,QAAQ,CAACgE,GAAG,CAAEC,OAAO;gBAAA,IAAAC,qBAAA,EAAAC,oBAAA;gBAAA,oBACnBzE,OAAA,CAACvB,QAAQ;kBAAA6E,QAAA,gBACPtD,OAAA,CAAC1B,SAAS;oBAAAgF,QAAA,EAAEiB,OAAO,CAACG;kBAAa;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC9CrD,OAAA,CAAC1B,SAAS;oBAAAgF,QAAA,EAAE,IAAIqB,IAAI,CAACJ,OAAO,CAACK,eAAe,CAAC,CAACC,kBAAkB,CAAC;kBAAC;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC/ErD,OAAA,CAAC1B,SAAS;oBAAAgF,QAAA,GAAAkB,qBAAA,GAAED,OAAO,CAACO,aAAa,cAAAN,qBAAA,uBAArBA,qBAAA,CAAuBO;kBAAW;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAC3DrD,OAAA,CAAC1B,SAAS;oBAAAgF,QAAA,GAAAmB,oBAAA,GAAEF,OAAO,CAACS,WAAW,cAAAP,oBAAA,uBAAnBA,oBAAA,CAAqBM;kBAAW;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACzDrD,OAAA,CAAC1B,SAAS;oBAAC6F,KAAK,EAAC,OAAO;oBAAAb,QAAA,EAAE1D,cAAc,CAAC2E,OAAO,CAACU,MAAM;kBAAC;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACrErD,OAAA,CAAC1B,SAAS;oBAAAgF,QAAA,EAAEf,aAAa,CAACgC,OAAO,CAAC/B,MAAM;kBAAC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACtDrD,OAAA,CAAC1B,SAAS;oBAAC6F,KAAK,EAAC,QAAQ;oBAAAb,QAAA,gBACvBtD,OAAA,CAACnB,OAAO;sBAACqG,KAAK,EAAC,MAAM;sBAAA5B,QAAA,eACnBtD,OAAA,CAACpB,UAAU;wBACTqE,IAAI,EAAC,OAAO;wBACZN,KAAK,EAAC,SAAS;wBACfsB,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,2BAA2BkE,OAAO,CAACY,GAAG,YAAY,CAAE;wBAAA7B,QAAA,eAE5EtD,OAAA,CAACb,QAAQ;0BAAA+D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,EAETkB,OAAO,CAAC/B,MAAM,KAAK,OAAO,iBACzBxC,OAAA,CAAAE,SAAA;sBAAAoD,QAAA,gBACEtD,OAAA,CAACnB,OAAO;wBAACqG,KAAK,EAAC,MAAM;wBAAA5B,QAAA,eACnBtD,OAAA,CAACpB,UAAU;0BACTqE,IAAI,EAAC,OAAO;0BACZN,KAAK,EAAC,SAAS;0BACfsB,OAAO,EAAEA,CAAA,KAAM5D,QAAQ,CAAC,2BAA2BkE,OAAO,CAACY,GAAG,EAAE,CAAE;0BAAA7B,QAAA,eAElEtD,OAAA,CAACf,QAAQ;4BAAAiE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACF;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eAEVrD,OAAA,CAACnB,OAAO;wBAACqG,KAAK,EAAC,QAAQ;wBAAA5B,QAAA,eACrBtD,OAAA,CAACpB,UAAU;0BACTqE,IAAI,EAAC,OAAO;0BACZN,KAAK,EAAC,OAAO;0BACbsB,OAAO,EAAEA,CAAA,KAAMnC,mBAAmB,CAACyC,OAAO,CAACY,GAAG,CAAE;0BAAA7B,QAAA,eAEhDtD,OAAA,CAACX,UAAU;4BAAA6D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eAEVrD,OAAA,CAACnB,OAAO;wBAACqG,KAAK,EAAC,SAAS;wBAAA5B,QAAA,eACtBtD,OAAA,CAACpB,UAAU;0BACTqE,IAAI,EAAC,OAAO;0BACZN,KAAK,EAAC,SAAS;0BACfsB,OAAO,EAAEA,CAAA,KAAM7B,oBAAoB,CAACmC,OAAO,CAACY,GAAG,CAAE;0BAAA7B,QAAA,eAEjDtD,OAAA,CAACT,WAAW;4BAAA2D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA,eACV,CACH,EAEAkB,OAAO,CAAC/B,MAAM,KAAK,UAAU,iBAC5BxC,OAAA,CAACnB,OAAO;sBAACqG,KAAK,EAAC,gBAAgB;sBAAA5B,QAAA,eAC7BtD,OAAA,CAACpB,UAAU;wBACTqE,IAAI,EAAC,OAAO;wBACZN,KAAK,EAAC,SAAS;wBACfsB,OAAO,EAAEA,CAAA,KAAM3B,iBAAiB,CAACiC,OAAO,CAACY,GAAG,CAAE;wBAAA7B,QAAA,eAE9CtD,OAAA,CAACP,QAAQ;0BAAAyD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CACV;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ,CAAC;gBAAA,GA/DCkB,OAAO,CAACY,GAAG;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgEhB,CAAC;cAAA,CACZ;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC,eAEjBrD,OAAA,CAACtB,eAAe;UACd0G,kBAAkB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAE;UAChClB,SAAS,EAAC,KAAK;UACfmB,KAAK,EAAEvE,aAAc;UACrBF,WAAW,EAAEA,WAAY;UACzBF,IAAI,EAAEA,IAAK;UACX4E,YAAY,EAAE/D,gBAAiB;UAC/BgE,mBAAmB,EAAE7D;QAAwB;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAGJ,CAAC;AAEV,CAAC;AAACjD,EAAA,CAlOID,gBAAgB;EAAA,QACHT,WAAW;AAAA;AAAA8F,EAAA,GADxBrF,gBAAgB;AAoOtB,eAAeA,gBAAgB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}