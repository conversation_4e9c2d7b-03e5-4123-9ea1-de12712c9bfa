{"ast": null, "code": "import BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\nimport System from '../util/System';\nimport Decoder from './decoder/Decoder';\nimport Detector from './detector/Detector';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * This implementation can detect and decode Data Matrix codes in an image.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar DataMatrixReader = /** @class */function () {\n  function DataMatrixReader() {\n    this.decoder = new Decoder();\n  }\n  /**\n   * Locates and decodes a Data Matrix code in an image.\n   *\n   * @return a String representing the content encoded by the Data Matrix code\n   * @throws NotFoundException if a Data Matrix code cannot be found\n   * @throws FormatException if a Data Matrix code cannot be decoded\n   * @throws ChecksumException if error correction fails\n   */\n  // @Override\n  // public Result decode(BinaryBitmap image) throws NotFoundException, ChecksumException, FormatException {\n  //   return decode(image, null);\n  // }\n  // @Override\n  DataMatrixReader.prototype.decode = function (image, hints) {\n    if (hints === void 0) {\n      hints = null;\n    }\n    var decoderResult;\n    var points;\n    if (hints != null && hints.has(DecodeHintType.PURE_BARCODE)) {\n      var bits = DataMatrixReader.extractPureBits(image.getBlackMatrix());\n      decoderResult = this.decoder.decode(bits);\n      points = DataMatrixReader.NO_POINTS;\n    } else {\n      var detectorResult = new Detector(image.getBlackMatrix()).detect();\n      decoderResult = this.decoder.decode(detectorResult.getBits());\n      points = detectorResult.getPoints();\n    }\n    var rawBytes = decoderResult.getRawBytes();\n    var result = new Result(decoderResult.getText(), rawBytes, 8 * rawBytes.length, points, BarcodeFormat.DATA_MATRIX, System.currentTimeMillis());\n    var byteSegments = decoderResult.getByteSegments();\n    if (byteSegments != null) {\n      result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n    }\n    var ecLevel = decoderResult.getECLevel();\n    if (ecLevel != null) {\n      result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n    }\n    return result;\n  };\n  // @Override\n  DataMatrixReader.prototype.reset = function () {\n    // do nothing\n  };\n  /**\n   * This method detects a code in a \"pure\" image -- that is, pure monochrome image\n   * which contains only an unrotated, unskewed, image of a code, with some white border\n   * around it. This is a specialized method that works exceptionally fast in this special\n   * case.\n   *\n   * @see com.google.zxing.qrcode.QRCodeReader#extractPureBits(BitMatrix)\n   */\n  DataMatrixReader.extractPureBits = function (image) {\n    var leftTopBlack = image.getTopLeftOnBit();\n    var rightBottomBlack = image.getBottomRightOnBit();\n    if (leftTopBlack == null || rightBottomBlack == null) {\n      throw new NotFoundException();\n    }\n    var moduleSize = this.moduleSize(leftTopBlack, image);\n    var top = leftTopBlack[1];\n    var bottom = rightBottomBlack[1];\n    var left = leftTopBlack[0];\n    var right = rightBottomBlack[0];\n    var matrixWidth = (right - left + 1) / moduleSize;\n    var matrixHeight = (bottom - top + 1) / moduleSize;\n    if (matrixWidth <= 0 || matrixHeight <= 0) {\n      throw new NotFoundException();\n    }\n    // Push in the \"border\" by half the module width so that we start\n    // sampling in the middle of the module. Just in case the image is a\n    // little off, this will help recover.\n    var nudge = moduleSize / 2;\n    top += nudge;\n    left += nudge;\n    // Now just read off the bits\n    var bits = new BitMatrix(matrixWidth, matrixHeight);\n    for (var y = 0; y < matrixHeight; y++) {\n      var iOffset = top + y * moduleSize;\n      for (var x = 0; x < matrixWidth; x++) {\n        if (image.get(left + x * moduleSize, iOffset)) {\n          bits.set(x, y);\n        }\n      }\n    }\n    return bits;\n  };\n  DataMatrixReader.moduleSize = function (leftTopBlack, image) {\n    var width = image.getWidth();\n    var x = leftTopBlack[0];\n    var y = leftTopBlack[1];\n    while (x < width && image.get(x, y)) {\n      x++;\n    }\n    if (x === width) {\n      throw new NotFoundException();\n    }\n    var moduleSize = x - leftTopBlack[0];\n    if (moduleSize === 0) {\n      throw new NotFoundException();\n    }\n    return moduleSize;\n  };\n  DataMatrixReader.NO_POINTS = [];\n  return DataMatrixReader;\n}();\nexport default DataMatrixReader;", "map": {"version": 3, "names": ["BarcodeFormat", "BitMatrix", "DecodeHintType", "NotFoundException", "Result", "ResultMetadataType", "System", "Decoder", "Detector", "DataMatrixReader", "decoder", "prototype", "decode", "image", "hints", "decoderResult", "points", "has", "PURE_BARCODE", "bits", "extractPureBits", "getBlackMatrix", "NO_POINTS", "detectorResult", "detect", "getBits", "getPoints", "rawBytes", "getRawBytes", "result", "getText", "length", "DATA_MATRIX", "currentTimeMillis", "byteSegments", "getByteSegments", "putMetadata", "BYTE_SEGMENTS", "ecLevel", "getECLevel", "ERROR_CORRECTION_LEVEL", "reset", "leftTopBlack", "getTopLeftOnBit", "rightBottomBlack", "getBottomRightOnBit", "moduleSize", "top", "bottom", "left", "right", "matrixWidth", "matrixHeight", "nudge", "y", "iOffset", "x", "get", "set", "width", "getWidth"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixReader.js"], "sourcesContent": ["import BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\nimport System from '../util/System';\nimport Decoder from './decoder/Decoder';\nimport Detector from './detector/Detector';\n/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * This implementation can detect and decode Data Matrix codes in an image.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar DataMatrixReader = /** @class */ (function () {\n    function DataMatrixReader() {\n        this.decoder = new Decoder();\n    }\n    /**\n     * Locates and decodes a Data Matrix code in an image.\n     *\n     * @return a String representing the content encoded by the Data Matrix code\n     * @throws NotFoundException if a Data Matrix code cannot be found\n     * @throws FormatException if a Data Matrix code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    // @Override\n    // public Result decode(BinaryBitmap image) throws NotFoundException, ChecksumException, FormatException {\n    //   return decode(image, null);\n    // }\n    // @Override\n    DataMatrixReader.prototype.decode = function (image, hints) {\n        if (hints === void 0) { hints = null; }\n        var decoderResult;\n        var points;\n        if (hints != null && hints.has(DecodeHintType.PURE_BARCODE)) {\n            var bits = DataMatrixReader.extractPureBits(image.getBlackMatrix());\n            decoderResult = this.decoder.decode(bits);\n            points = DataMatrixReader.NO_POINTS;\n        }\n        else {\n            var detectorResult = new Detector(image.getBlackMatrix()).detect();\n            decoderResult = this.decoder.decode(detectorResult.getBits());\n            points = detectorResult.getPoints();\n        }\n        var rawBytes = decoderResult.getRawBytes();\n        var result = new Result(decoderResult.getText(), rawBytes, 8 * rawBytes.length, points, BarcodeFormat.DATA_MATRIX, System.currentTimeMillis());\n        var byteSegments = decoderResult.getByteSegments();\n        if (byteSegments != null) {\n            result.putMetadata(ResultMetadataType.BYTE_SEGMENTS, byteSegments);\n        }\n        var ecLevel = decoderResult.getECLevel();\n        if (ecLevel != null) {\n            result.putMetadata(ResultMetadataType.ERROR_CORRECTION_LEVEL, ecLevel);\n        }\n        return result;\n    };\n    // @Override\n    DataMatrixReader.prototype.reset = function () {\n        // do nothing\n    };\n    /**\n     * This method detects a code in a \"pure\" image -- that is, pure monochrome image\n     * which contains only an unrotated, unskewed, image of a code, with some white border\n     * around it. This is a specialized method that works exceptionally fast in this special\n     * case.\n     *\n     * @see com.google.zxing.qrcode.QRCodeReader#extractPureBits(BitMatrix)\n     */\n    DataMatrixReader.extractPureBits = function (image) {\n        var leftTopBlack = image.getTopLeftOnBit();\n        var rightBottomBlack = image.getBottomRightOnBit();\n        if (leftTopBlack == null || rightBottomBlack == null) {\n            throw new NotFoundException();\n        }\n        var moduleSize = this.moduleSize(leftTopBlack, image);\n        var top = leftTopBlack[1];\n        var bottom = rightBottomBlack[1];\n        var left = leftTopBlack[0];\n        var right = rightBottomBlack[0];\n        var matrixWidth = (right - left + 1) / moduleSize;\n        var matrixHeight = (bottom - top + 1) / moduleSize;\n        if (matrixWidth <= 0 || matrixHeight <= 0) {\n            throw new NotFoundException();\n        }\n        // Push in the \"border\" by half the module width so that we start\n        // sampling in the middle of the module. Just in case the image is a\n        // little off, this will help recover.\n        var nudge = moduleSize / 2;\n        top += nudge;\n        left += nudge;\n        // Now just read off the bits\n        var bits = new BitMatrix(matrixWidth, matrixHeight);\n        for (var y = 0; y < matrixHeight; y++) {\n            var iOffset = top + y * moduleSize;\n            for (var x = 0; x < matrixWidth; x++) {\n                if (image.get(left + x * moduleSize, iOffset)) {\n                    bits.set(x, y);\n                }\n            }\n        }\n        return bits;\n    };\n    DataMatrixReader.moduleSize = function (leftTopBlack, image) {\n        var width = image.getWidth();\n        var x = leftTopBlack[0];\n        var y = leftTopBlack[1];\n        while (x < width && image.get(x, y)) {\n            x++;\n        }\n        if (x === width) {\n            throw new NotFoundException();\n        }\n        var moduleSize = x - leftTopBlack[0];\n        if (moduleSize === 0) {\n            throw new NotFoundException();\n        }\n        return moduleSize;\n    };\n    DataMatrixReader.NO_POINTS = [];\n    return DataMatrixReader;\n}());\nexport default DataMatrixReader;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAe,YAAY;EAC9C,SAASA,gBAAgBA,CAAA,EAAG;IACxB,IAAI,CAACC,OAAO,GAAG,IAAIH,OAAO,CAAC,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACAE,gBAAgB,CAACE,SAAS,CAACC,MAAM,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IACxD,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC,IAAIC,aAAa;IACjB,IAAIC,MAAM;IACV,IAAIF,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACG,GAAG,CAACf,cAAc,CAACgB,YAAY,CAAC,EAAE;MACzD,IAAIC,IAAI,GAAGV,gBAAgB,CAACW,eAAe,CAACP,KAAK,CAACQ,cAAc,CAAC,CAAC,CAAC;MACnEN,aAAa,GAAG,IAAI,CAACL,OAAO,CAACE,MAAM,CAACO,IAAI,CAAC;MACzCH,MAAM,GAAGP,gBAAgB,CAACa,SAAS;IACvC,CAAC,MACI;MACD,IAAIC,cAAc,GAAG,IAAIf,QAAQ,CAACK,KAAK,CAACQ,cAAc,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC,CAAC;MAClET,aAAa,GAAG,IAAI,CAACL,OAAO,CAACE,MAAM,CAACW,cAAc,CAACE,OAAO,CAAC,CAAC,CAAC;MAC7DT,MAAM,GAAGO,cAAc,CAACG,SAAS,CAAC,CAAC;IACvC;IACA,IAAIC,QAAQ,GAAGZ,aAAa,CAACa,WAAW,CAAC,CAAC;IAC1C,IAAIC,MAAM,GAAG,IAAIzB,MAAM,CAACW,aAAa,CAACe,OAAO,CAAC,CAAC,EAAEH,QAAQ,EAAE,CAAC,GAAGA,QAAQ,CAACI,MAAM,EAAEf,MAAM,EAAEhB,aAAa,CAACgC,WAAW,EAAE1B,MAAM,CAAC2B,iBAAiB,CAAC,CAAC,CAAC;IAC9I,IAAIC,YAAY,GAAGnB,aAAa,CAACoB,eAAe,CAAC,CAAC;IAClD,IAAID,YAAY,IAAI,IAAI,EAAE;MACtBL,MAAM,CAACO,WAAW,CAAC/B,kBAAkB,CAACgC,aAAa,EAAEH,YAAY,CAAC;IACtE;IACA,IAAII,OAAO,GAAGvB,aAAa,CAACwB,UAAU,CAAC,CAAC;IACxC,IAAID,OAAO,IAAI,IAAI,EAAE;MACjBT,MAAM,CAACO,WAAW,CAAC/B,kBAAkB,CAACmC,sBAAsB,EAAEF,OAAO,CAAC;IAC1E;IACA,OAAOT,MAAM;EACjB,CAAC;EACD;EACApB,gBAAgB,CAACE,SAAS,CAAC8B,KAAK,GAAG,YAAY;IAC3C;EAAA,CACH;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIhC,gBAAgB,CAACW,eAAe,GAAG,UAAUP,KAAK,EAAE;IAChD,IAAI6B,YAAY,GAAG7B,KAAK,CAAC8B,eAAe,CAAC,CAAC;IAC1C,IAAIC,gBAAgB,GAAG/B,KAAK,CAACgC,mBAAmB,CAAC,CAAC;IAClD,IAAIH,YAAY,IAAI,IAAI,IAAIE,gBAAgB,IAAI,IAAI,EAAE;MAClD,MAAM,IAAIzC,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI2C,UAAU,GAAG,IAAI,CAACA,UAAU,CAACJ,YAAY,EAAE7B,KAAK,CAAC;IACrD,IAAIkC,GAAG,GAAGL,YAAY,CAAC,CAAC,CAAC;IACzB,IAAIM,MAAM,GAAGJ,gBAAgB,CAAC,CAAC,CAAC;IAChC,IAAIK,IAAI,GAAGP,YAAY,CAAC,CAAC,CAAC;IAC1B,IAAIQ,KAAK,GAAGN,gBAAgB,CAAC,CAAC,CAAC;IAC/B,IAAIO,WAAW,GAAG,CAACD,KAAK,GAAGD,IAAI,GAAG,CAAC,IAAIH,UAAU;IACjD,IAAIM,YAAY,GAAG,CAACJ,MAAM,GAAGD,GAAG,GAAG,CAAC,IAAID,UAAU;IAClD,IAAIK,WAAW,IAAI,CAAC,IAAIC,YAAY,IAAI,CAAC,EAAE;MACvC,MAAM,IAAIjD,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA,IAAIkD,KAAK,GAAGP,UAAU,GAAG,CAAC;IAC1BC,GAAG,IAAIM,KAAK;IACZJ,IAAI,IAAII,KAAK;IACb;IACA,IAAIlC,IAAI,GAAG,IAAIlB,SAAS,CAACkD,WAAW,EAAEC,YAAY,CAAC;IACnD,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,YAAY,EAAEE,CAAC,EAAE,EAAE;MACnC,IAAIC,OAAO,GAAGR,GAAG,GAAGO,CAAC,GAAGR,UAAU;MAClC,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,EAAEK,CAAC,EAAE,EAAE;QAClC,IAAI3C,KAAK,CAAC4C,GAAG,CAACR,IAAI,GAAGO,CAAC,GAAGV,UAAU,EAAES,OAAO,CAAC,EAAE;UAC3CpC,IAAI,CAACuC,GAAG,CAACF,CAAC,EAAEF,CAAC,CAAC;QAClB;MACJ;IACJ;IACA,OAAOnC,IAAI;EACf,CAAC;EACDV,gBAAgB,CAACqC,UAAU,GAAG,UAAUJ,YAAY,EAAE7B,KAAK,EAAE;IACzD,IAAI8C,KAAK,GAAG9C,KAAK,CAAC+C,QAAQ,CAAC,CAAC;IAC5B,IAAIJ,CAAC,GAAGd,YAAY,CAAC,CAAC,CAAC;IACvB,IAAIY,CAAC,GAAGZ,YAAY,CAAC,CAAC,CAAC;IACvB,OAAOc,CAAC,GAAGG,KAAK,IAAI9C,KAAK,CAAC4C,GAAG,CAACD,CAAC,EAAEF,CAAC,CAAC,EAAE;MACjCE,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,KAAKG,KAAK,EAAE;MACb,MAAM,IAAIxD,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI2C,UAAU,GAAGU,CAAC,GAAGd,YAAY,CAAC,CAAC,CAAC;IACpC,IAAII,UAAU,KAAK,CAAC,EAAE;MAClB,MAAM,IAAI3C,iBAAiB,CAAC,CAAC;IACjC;IACA,OAAO2C,UAAU;EACrB,CAAC;EACDrC,gBAAgB,CAACa,SAAS,GAAG,EAAE;EAC/B,OAAOb,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}