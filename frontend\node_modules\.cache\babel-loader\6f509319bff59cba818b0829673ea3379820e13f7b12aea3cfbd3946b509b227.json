{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.Arrays;*/\nimport BitArray from './BitArray';\nimport System from '../util/System';\nimport Arrays from '../util/Arrays';\nimport StringBuilder from '../util/StringBuilder';\nimport IllegalArgumentException from '../IllegalArgumentException';\n/**\n * <p>Represents a 2D matrix of bits. In function arguments below, and throughout the common\n * module, x is the column position, and y is the row position. The ordering is always x, y.\n * The origin is at the top-left.</p>\n *\n * <p>Internally the bits are represented in a 1-D array of 32-bit ints. However, each row begins\n * with a new int. This is done intentionally so that we can copy out a row into a BitArray very\n * efficiently.</p>\n *\n * <p>The ordering of bits is row-major. Within each int, the least significant bits are used first,\n * meaning they represent lower x values. This is compatible with BitArray's implementation.</p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> (Daniel Switkin)\n */\nvar BitMatrix /*implements Cloneable*/ = /** @class */function () {\n  /**\n   * Creates an empty square {@link BitMatrix}.\n   *\n   * @param dimension height and width\n   */\n  // public constructor(dimension: number /*int*/) {\n  //   this(dimension, dimension)\n  // }\n  /**\n   * Creates an empty {@link BitMatrix}.\n   *\n   * @param width bit matrix width\n   * @param height bit matrix height\n   */\n  // public constructor(width: number /*int*/, height: number /*int*/) {\n  //   if (width < 1 || height < 1) {\n  //     throw new IllegalArgumentException(\"Both dimensions must be greater than 0\")\n  //   }\n  //   this.width = width\n  //   this.height = height\n  //   this.rowSize = (width + 31) / 32\n  //   bits = new int[rowSize * height];\n  // }\n  function BitMatrix(width /*int*/, height /*int*/, rowSize /*int*/, bits) {\n    this.width = width;\n    this.height = height;\n    this.rowSize = rowSize;\n    this.bits = bits;\n    if (undefined === height || null === height) {\n      height = width;\n    }\n    this.height = height;\n    if (width < 1 || height < 1) {\n      throw new IllegalArgumentException('Both dimensions must be greater than 0');\n    }\n    if (undefined === rowSize || null === rowSize) {\n      rowSize = Math.floor((width + 31) / 32);\n    }\n    this.rowSize = rowSize;\n    if (undefined === bits || null === bits) {\n      this.bits = new Int32Array(this.rowSize * this.height);\n    }\n  }\n  /**\n   * Interprets a 2D array of booleans as a {@link BitMatrix}, where \"true\" means an \"on\" bit.\n   *\n   * @function parse\n   * @param image bits of the image, as a row-major 2D array. Elements are arrays representing rows\n   * @return {@link BitMatrix} representation of image\n   */\n  BitMatrix.parseFromBooleanArray = function (image) {\n    var height = image.length;\n    var width = image[0].length;\n    var bits = new BitMatrix(width, height);\n    for (var i = 0; i < height; i++) {\n      var imageI = image[i];\n      for (var j = 0; j < width; j++) {\n        if (imageI[j]) {\n          bits.set(j, i);\n        }\n      }\n    }\n    return bits;\n  };\n  /**\n   *\n   * @function parse\n   * @param stringRepresentation\n   * @param setString\n   * @param unsetString\n   */\n  BitMatrix.parseFromString = function (stringRepresentation, setString, unsetString) {\n    if (stringRepresentation === null) {\n      throw new IllegalArgumentException('stringRepresentation cannot be null');\n    }\n    var bits = new Array(stringRepresentation.length);\n    var bitsPos = 0;\n    var rowStartPos = 0;\n    var rowLength = -1;\n    var nRows = 0;\n    var pos = 0;\n    while (pos < stringRepresentation.length) {\n      if (stringRepresentation.charAt(pos) === '\\n' || stringRepresentation.charAt(pos) === '\\r') {\n        if (bitsPos > rowStartPos) {\n          if (rowLength === -1) {\n            rowLength = bitsPos - rowStartPos;\n          } else if (bitsPos - rowStartPos !== rowLength) {\n            throw new IllegalArgumentException('row lengths do not match');\n          }\n          rowStartPos = bitsPos;\n          nRows++;\n        }\n        pos++;\n      } else if (stringRepresentation.substring(pos, pos + setString.length) === setString) {\n        pos += setString.length;\n        bits[bitsPos] = true;\n        bitsPos++;\n      } else if (stringRepresentation.substring(pos, pos + unsetString.length) === unsetString) {\n        pos += unsetString.length;\n        bits[bitsPos] = false;\n        bitsPos++;\n      } else {\n        throw new IllegalArgumentException('illegal character encountered: ' + stringRepresentation.substring(pos));\n      }\n    }\n    // no EOL at end?\n    if (bitsPos > rowStartPos) {\n      if (rowLength === -1) {\n        rowLength = bitsPos - rowStartPos;\n      } else if (bitsPos - rowStartPos !== rowLength) {\n        throw new IllegalArgumentException('row lengths do not match');\n      }\n      nRows++;\n    }\n    var matrix = new BitMatrix(rowLength, nRows);\n    for (var i = 0; i < bitsPos; i++) {\n      if (bits[i]) {\n        matrix.set(Math.floor(i % rowLength), Math.floor(i / rowLength));\n      }\n    }\n    return matrix;\n  };\n  /**\n   * <p>Gets the requested bit, where true means black.</p>\n   *\n   * @param x The horizontal component (i.e. which column)\n   * @param y The vertical component (i.e. which row)\n   * @return value of given bit in matrix\n   */\n  BitMatrix.prototype.get = function (x /*int*/, y /*int*/) {\n    var offset = y * this.rowSize + Math.floor(x / 32);\n    return (this.bits[offset] >>> (x & 0x1f) & 1) !== 0;\n  };\n  /**\n   * <p>Sets the given bit to true.</p>\n   *\n   * @param x The horizontal component (i.e. which column)\n   * @param y The vertical component (i.e. which row)\n   */\n  BitMatrix.prototype.set = function (x /*int*/, y /*int*/) {\n    var offset = y * this.rowSize + Math.floor(x / 32);\n    this.bits[offset] |= 1 << (x & 0x1f) & 0xFFFFFFFF;\n  };\n  BitMatrix.prototype.unset = function (x /*int*/, y /*int*/) {\n    var offset = y * this.rowSize + Math.floor(x / 32);\n    this.bits[offset] &= ~(1 << (x & 0x1f) & 0xFFFFFFFF);\n  };\n  /**\n   * <p>Flips the given bit.</p>\n   *\n   * @param x The horizontal component (i.e. which column)\n   * @param y The vertical component (i.e. which row)\n   */\n  BitMatrix.prototype.flip = function (x /*int*/, y /*int*/) {\n    var offset = y * this.rowSize + Math.floor(x / 32);\n    this.bits[offset] ^= 1 << (x & 0x1f) & 0xFFFFFFFF;\n  };\n  /**\n   * Exclusive-or (XOR): Flip the bit in this {@code BitMatrix} if the corresponding\n   * mask bit is set.\n   *\n   * @param mask XOR mask\n   */\n  BitMatrix.prototype.xor = function (mask) {\n    if (this.width !== mask.getWidth() || this.height !== mask.getHeight() || this.rowSize !== mask.getRowSize()) {\n      throw new IllegalArgumentException('input matrix dimensions do not match');\n    }\n    var rowArray = new BitArray(Math.floor(this.width / 32) + 1);\n    var rowSize = this.rowSize;\n    var bits = this.bits;\n    for (var y = 0, height = this.height; y < height; y++) {\n      var offset = y * rowSize;\n      var row = mask.getRow(y, rowArray).getBitArray();\n      for (var x = 0; x < rowSize; x++) {\n        bits[offset + x] ^= row[x];\n      }\n    }\n  };\n  /**\n   * Clears all bits (sets to false).\n   */\n  BitMatrix.prototype.clear = function () {\n    var bits = this.bits;\n    var max = bits.length;\n    for (var i = 0; i < max; i++) {\n      bits[i] = 0;\n    }\n  };\n  /**\n   * <p>Sets a square region of the bit matrix to true.</p>\n   *\n   * @param left The horizontal position to begin at (inclusive)\n   * @param top The vertical position to begin at (inclusive)\n   * @param width The width of the region\n   * @param height The height of the region\n   */\n  BitMatrix.prototype.setRegion = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n    if (top < 0 || left < 0) {\n      throw new IllegalArgumentException('Left and top must be nonnegative');\n    }\n    if (height < 1 || width < 1) {\n      throw new IllegalArgumentException('Height and width must be at least 1');\n    }\n    var right = left + width;\n    var bottom = top + height;\n    if (bottom > this.height || right > this.width) {\n      throw new IllegalArgumentException('The region must fit inside the matrix');\n    }\n    var rowSize = this.rowSize;\n    var bits = this.bits;\n    for (var y = top; y < bottom; y++) {\n      var offset = y * rowSize;\n      for (var x = left; x < right; x++) {\n        bits[offset + Math.floor(x / 32)] |= 1 << (x & 0x1f) & 0xFFFFFFFF;\n      }\n    }\n  };\n  /**\n   * A fast method to retrieve one row of data from the matrix as a BitArray.\n   *\n   * @param y The row to retrieve\n   * @param row An optional caller-allocated BitArray, will be allocated if null or too small\n   * @return The resulting BitArray - this reference should always be used even when passing\n   *         your own row\n   */\n  BitMatrix.prototype.getRow = function (y /*int*/, row) {\n    if (row === null || row === undefined || row.getSize() < this.width) {\n      row = new BitArray(this.width);\n    } else {\n      row.clear();\n    }\n    var rowSize = this.rowSize;\n    var bits = this.bits;\n    var offset = y * rowSize;\n    for (var x = 0; x < rowSize; x++) {\n      row.setBulk(x * 32, bits[offset + x]);\n    }\n    return row;\n  };\n  /**\n   * @param y row to set\n   * @param row {@link BitArray} to copy from\n   */\n  BitMatrix.prototype.setRow = function (y /*int*/, row) {\n    System.arraycopy(row.getBitArray(), 0, this.bits, y * this.rowSize, this.rowSize);\n  };\n  /**\n   * Modifies this {@code BitMatrix} to represent the same but rotated 180 degrees\n   */\n  BitMatrix.prototype.rotate180 = function () {\n    var width = this.getWidth();\n    var height = this.getHeight();\n    var topRow = new BitArray(width);\n    var bottomRow = new BitArray(width);\n    for (var i = 0, length_1 = Math.floor((height + 1) / 2); i < length_1; i++) {\n      topRow = this.getRow(i, topRow);\n      bottomRow = this.getRow(height - 1 - i, bottomRow);\n      topRow.reverse();\n      bottomRow.reverse();\n      this.setRow(i, bottomRow);\n      this.setRow(height - 1 - i, topRow);\n    }\n  };\n  /**\n   * This is useful in detecting the enclosing rectangle of a 'pure' barcode.\n   *\n   * @return {@code left,top,width,height} enclosing rectangle of all 1 bits, or null if it is all white\n   */\n  BitMatrix.prototype.getEnclosingRectangle = function () {\n    var width = this.width;\n    var height = this.height;\n    var rowSize = this.rowSize;\n    var bits = this.bits;\n    var left = width;\n    var top = height;\n    var right = -1;\n    var bottom = -1;\n    for (var y = 0; y < height; y++) {\n      for (var x32 = 0; x32 < rowSize; x32++) {\n        var theBits = bits[y * rowSize + x32];\n        if (theBits !== 0) {\n          if (y < top) {\n            top = y;\n          }\n          if (y > bottom) {\n            bottom = y;\n          }\n          if (x32 * 32 < left) {\n            var bit = 0;\n            while ((theBits << 31 - bit & 0xFFFFFFFF) === 0) {\n              bit++;\n            }\n            if (x32 * 32 + bit < left) {\n              left = x32 * 32 + bit;\n            }\n          }\n          if (x32 * 32 + 31 > right) {\n            var bit = 31;\n            while (theBits >>> bit === 0) {\n              bit--;\n            }\n            if (x32 * 32 + bit > right) {\n              right = x32 * 32 + bit;\n            }\n          }\n        }\n      }\n    }\n    if (right < left || bottom < top) {\n      return null;\n    }\n    return Int32Array.from([left, top, right - left + 1, bottom - top + 1]);\n  };\n  /**\n   * This is useful in detecting a corner of a 'pure' barcode.\n   *\n   * @return {@code x,y} coordinate of top-left-most 1 bit, or null if it is all white\n   */\n  BitMatrix.prototype.getTopLeftOnBit = function () {\n    var rowSize = this.rowSize;\n    var bits = this.bits;\n    var bitsOffset = 0;\n    while (bitsOffset < bits.length && bits[bitsOffset] === 0) {\n      bitsOffset++;\n    }\n    if (bitsOffset === bits.length) {\n      return null;\n    }\n    var y = bitsOffset / rowSize;\n    var x = bitsOffset % rowSize * 32;\n    var theBits = bits[bitsOffset];\n    var bit = 0;\n    while ((theBits << 31 - bit & 0xFFFFFFFF) === 0) {\n      bit++;\n    }\n    x += bit;\n    return Int32Array.from([x, y]);\n  };\n  BitMatrix.prototype.getBottomRightOnBit = function () {\n    var rowSize = this.rowSize;\n    var bits = this.bits;\n    var bitsOffset = bits.length - 1;\n    while (bitsOffset >= 0 && bits[bitsOffset] === 0) {\n      bitsOffset--;\n    }\n    if (bitsOffset < 0) {\n      return null;\n    }\n    var y = Math.floor(bitsOffset / rowSize);\n    var x = Math.floor(bitsOffset % rowSize) * 32;\n    var theBits = bits[bitsOffset];\n    var bit = 31;\n    while (theBits >>> bit === 0) {\n      bit--;\n    }\n    x += bit;\n    return Int32Array.from([x, y]);\n  };\n  /**\n   * @return The width of the matrix\n   */\n  BitMatrix.prototype.getWidth = function () {\n    return this.width;\n  };\n  /**\n   * @return The height of the matrix\n   */\n  BitMatrix.prototype.getHeight = function () {\n    return this.height;\n  };\n  /**\n   * @return The row size of the matrix\n   */\n  BitMatrix.prototype.getRowSize = function () {\n    return this.rowSize;\n  };\n  /*@Override*/\n  BitMatrix.prototype.equals = function (o) {\n    if (!(o instanceof BitMatrix)) {\n      return false;\n    }\n    var other = o;\n    return this.width === other.width && this.height === other.height && this.rowSize === other.rowSize && Arrays.equals(this.bits, other.bits);\n  };\n  /*@Override*/\n  BitMatrix.prototype.hashCode = function () {\n    var hash = this.width;\n    hash = 31 * hash + this.width;\n    hash = 31 * hash + this.height;\n    hash = 31 * hash + this.rowSize;\n    hash = 31 * hash + Arrays.hashCode(this.bits);\n    return hash;\n  };\n  /**\n   * @return string representation using \"X\" for set and \" \" for unset bits\n   */\n  /*@Override*/\n  // public toString(): string {\n  //   return toString(\": \"X, \"  \")\n  // }\n  /**\n   * @param setString representation of a set bit\n   * @param unsetString representation of an unset bit\n   * @return string representation of entire matrix utilizing given strings\n   */\n  // public toString(setString: string = \"X \", unsetString: string = \"  \"): string {\n  //   return this.buildToString(setString, unsetString, \"\\n\")\n  // }\n  /**\n   * @param setString representation of a set bit\n   * @param unsetString representation of an unset bit\n   * @param lineSeparator newline character in string representation\n   * @return string representation of entire matrix utilizing given strings and line separator\n   * @deprecated call {@link #toString(String,String)} only, which uses \\n line separator always\n   */\n  // @Deprecated\n  BitMatrix.prototype.toString = function (setString, unsetString, lineSeparator) {\n    if (setString === void 0) {\n      setString = 'X ';\n    }\n    if (unsetString === void 0) {\n      unsetString = '  ';\n    }\n    if (lineSeparator === void 0) {\n      lineSeparator = '\\n';\n    }\n    return this.buildToString(setString, unsetString, lineSeparator);\n  };\n  BitMatrix.prototype.buildToString = function (setString, unsetString, lineSeparator) {\n    var result = new StringBuilder();\n    // result.append(lineSeparator);\n    for (var y = 0, height = this.height; y < height; y++) {\n      for (var x = 0, width = this.width; x < width; x++) {\n        result.append(this.get(x, y) ? setString : unsetString);\n      }\n      result.append(lineSeparator);\n    }\n    return result.toString();\n  };\n  /*@Override*/\n  BitMatrix.prototype.clone = function () {\n    return new BitMatrix(this.width, this.height, this.rowSize, this.bits.slice());\n  };\n  return BitMatrix;\n}();\nexport default BitMatrix;", "map": {"version": 3, "names": ["BitArray", "System", "<PERSON><PERSON><PERSON>", "StringBuilder", "IllegalArgumentException", "BitMatrix", "width", "height", "rowSize", "bits", "undefined", "Math", "floor", "Int32Array", "parseFromBooleanArray", "image", "length", "i", "imageI", "j", "set", "parseFromString", "stringRepresentation", "setString", "unsetString", "Array", "bitsPos", "rowStartPos", "<PERSON><PERSON><PERSON><PERSON>", "nRows", "pos", "char<PERSON>t", "substring", "matrix", "prototype", "get", "x", "y", "offset", "unset", "flip", "xor", "mask", "getWidth", "getHeight", "getRowSize", "rowArray", "row", "getRow", "getBitArray", "clear", "max", "setRegion", "left", "top", "right", "bottom", "getSize", "setBulk", "setRow", "arraycopy", "rotate180", "topRow", "bottomRow", "length_1", "reverse", "getEnclosingRectangle", "x32", "theBits", "bit", "from", "getTopLeftOnBit", "bitsOffset", "getBottomRightOnBit", "equals", "o", "other", "hashCode", "hash", "toString", "lineSeparator", "buildToString", "result", "append", "clone", "slice"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/BitMatrix.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.Arrays;*/\nimport BitArray from './BitArray';\nimport System from '../util/System';\nimport Arrays from '../util/Arrays';\nimport StringBuilder from '../util/StringBuilder';\nimport IllegalArgumentException from '../IllegalArgumentException';\n/**\n * <p>Represents a 2D matrix of bits. In function arguments below, and throughout the common\n * module, x is the column position, and y is the row position. The ordering is always x, y.\n * The origin is at the top-left.</p>\n *\n * <p>Internally the bits are represented in a 1-D array of 32-bit ints. However, each row begins\n * with a new int. This is done intentionally so that we can copy out a row into a BitArray very\n * efficiently.</p>\n *\n * <p>The ordering of bits is row-major. Within each int, the least significant bits are used first,\n * meaning they represent lower x values. This is compatible with BitArray's implementation.</p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> (Daniel Switkin)\n */\nvar BitMatrix /*implements Cloneable*/ = /** @class */ (function () {\n    /**\n     * Creates an empty square {@link BitMatrix}.\n     *\n     * @param dimension height and width\n     */\n    // public constructor(dimension: number /*int*/) {\n    //   this(dimension, dimension)\n    // }\n    /**\n     * Creates an empty {@link BitMatrix}.\n     *\n     * @param width bit matrix width\n     * @param height bit matrix height\n     */\n    // public constructor(width: number /*int*/, height: number /*int*/) {\n    //   if (width < 1 || height < 1) {\n    //     throw new IllegalArgumentException(\"Both dimensions must be greater than 0\")\n    //   }\n    //   this.width = width\n    //   this.height = height\n    //   this.rowSize = (width + 31) / 32\n    //   bits = new int[rowSize * height];\n    // }\n    function BitMatrix(width /*int*/, height /*int*/, rowSize /*int*/, bits) {\n        this.width = width;\n        this.height = height;\n        this.rowSize = rowSize;\n        this.bits = bits;\n        if (undefined === height || null === height) {\n            height = width;\n        }\n        this.height = height;\n        if (width < 1 || height < 1) {\n            throw new IllegalArgumentException('Both dimensions must be greater than 0');\n        }\n        if (undefined === rowSize || null === rowSize) {\n            rowSize = Math.floor((width + 31) / 32);\n        }\n        this.rowSize = rowSize;\n        if (undefined === bits || null === bits) {\n            this.bits = new Int32Array(this.rowSize * this.height);\n        }\n    }\n    /**\n     * Interprets a 2D array of booleans as a {@link BitMatrix}, where \"true\" means an \"on\" bit.\n     *\n     * @function parse\n     * @param image bits of the image, as a row-major 2D array. Elements are arrays representing rows\n     * @return {@link BitMatrix} representation of image\n     */\n    BitMatrix.parseFromBooleanArray = function (image) {\n        var height = image.length;\n        var width = image[0].length;\n        var bits = new BitMatrix(width, height);\n        for (var i = 0; i < height; i++) {\n            var imageI = image[i];\n            for (var j = 0; j < width; j++) {\n                if (imageI[j]) {\n                    bits.set(j, i);\n                }\n            }\n        }\n        return bits;\n    };\n    /**\n     *\n     * @function parse\n     * @param stringRepresentation\n     * @param setString\n     * @param unsetString\n     */\n    BitMatrix.parseFromString = function (stringRepresentation, setString, unsetString) {\n        if (stringRepresentation === null) {\n            throw new IllegalArgumentException('stringRepresentation cannot be null');\n        }\n        var bits = new Array(stringRepresentation.length);\n        var bitsPos = 0;\n        var rowStartPos = 0;\n        var rowLength = -1;\n        var nRows = 0;\n        var pos = 0;\n        while (pos < stringRepresentation.length) {\n            if (stringRepresentation.charAt(pos) === '\\n' ||\n                stringRepresentation.charAt(pos) === '\\r') {\n                if (bitsPos > rowStartPos) {\n                    if (rowLength === -1) {\n                        rowLength = bitsPos - rowStartPos;\n                    }\n                    else if (bitsPos - rowStartPos !== rowLength) {\n                        throw new IllegalArgumentException('row lengths do not match');\n                    }\n                    rowStartPos = bitsPos;\n                    nRows++;\n                }\n                pos++;\n            }\n            else if (stringRepresentation.substring(pos, pos + setString.length) === setString) {\n                pos += setString.length;\n                bits[bitsPos] = true;\n                bitsPos++;\n            }\n            else if (stringRepresentation.substring(pos, pos + unsetString.length) === unsetString) {\n                pos += unsetString.length;\n                bits[bitsPos] = false;\n                bitsPos++;\n            }\n            else {\n                throw new IllegalArgumentException('illegal character encountered: ' + stringRepresentation.substring(pos));\n            }\n        }\n        // no EOL at end?\n        if (bitsPos > rowStartPos) {\n            if (rowLength === -1) {\n                rowLength = bitsPos - rowStartPos;\n            }\n            else if (bitsPos - rowStartPos !== rowLength) {\n                throw new IllegalArgumentException('row lengths do not match');\n            }\n            nRows++;\n        }\n        var matrix = new BitMatrix(rowLength, nRows);\n        for (var i = 0; i < bitsPos; i++) {\n            if (bits[i]) {\n                matrix.set(Math.floor(i % rowLength), Math.floor(i / rowLength));\n            }\n        }\n        return matrix;\n    };\n    /**\n     * <p>Gets the requested bit, where true means black.</p>\n     *\n     * @param x The horizontal component (i.e. which column)\n     * @param y The vertical component (i.e. which row)\n     * @return value of given bit in matrix\n     */\n    BitMatrix.prototype.get = function (x /*int*/, y /*int*/) {\n        var offset = y * this.rowSize + Math.floor(x / 32);\n        return ((this.bits[offset] >>> (x & 0x1f)) & 1) !== 0;\n    };\n    /**\n     * <p>Sets the given bit to true.</p>\n     *\n     * @param x The horizontal component (i.e. which column)\n     * @param y The vertical component (i.e. which row)\n     */\n    BitMatrix.prototype.set = function (x /*int*/, y /*int*/) {\n        var offset = y * this.rowSize + Math.floor(x / 32);\n        this.bits[offset] |= (1 << (x & 0x1f)) & 0xFFFFFFFF;\n    };\n    BitMatrix.prototype.unset = function (x /*int*/, y /*int*/) {\n        var offset = y * this.rowSize + Math.floor(x / 32);\n        this.bits[offset] &= ~((1 << (x & 0x1f)) & 0xFFFFFFFF);\n    };\n    /**\n     * <p>Flips the given bit.</p>\n     *\n     * @param x The horizontal component (i.e. which column)\n     * @param y The vertical component (i.e. which row)\n     */\n    BitMatrix.prototype.flip = function (x /*int*/, y /*int*/) {\n        var offset = y * this.rowSize + Math.floor(x / 32);\n        this.bits[offset] ^= ((1 << (x & 0x1f)) & 0xFFFFFFFF);\n    };\n    /**\n     * Exclusive-or (XOR): Flip the bit in this {@code BitMatrix} if the corresponding\n     * mask bit is set.\n     *\n     * @param mask XOR mask\n     */\n    BitMatrix.prototype.xor = function (mask) {\n        if (this.width !== mask.getWidth() || this.height !== mask.getHeight()\n            || this.rowSize !== mask.getRowSize()) {\n            throw new IllegalArgumentException('input matrix dimensions do not match');\n        }\n        var rowArray = new BitArray(Math.floor(this.width / 32) + 1);\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        for (var y = 0, height = this.height; y < height; y++) {\n            var offset = y * rowSize;\n            var row = mask.getRow(y, rowArray).getBitArray();\n            for (var x = 0; x < rowSize; x++) {\n                bits[offset + x] ^= row[x];\n            }\n        }\n    };\n    /**\n     * Clears all bits (sets to false).\n     */\n    BitMatrix.prototype.clear = function () {\n        var bits = this.bits;\n        var max = bits.length;\n        for (var i = 0; i < max; i++) {\n            bits[i] = 0;\n        }\n    };\n    /**\n     * <p>Sets a square region of the bit matrix to true.</p>\n     *\n     * @param left The horizontal position to begin at (inclusive)\n     * @param top The vertical position to begin at (inclusive)\n     * @param width The width of the region\n     * @param height The height of the region\n     */\n    BitMatrix.prototype.setRegion = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        if (top < 0 || left < 0) {\n            throw new IllegalArgumentException('Left and top must be nonnegative');\n        }\n        if (height < 1 || width < 1) {\n            throw new IllegalArgumentException('Height and width must be at least 1');\n        }\n        var right = left + width;\n        var bottom = top + height;\n        if (bottom > this.height || right > this.width) {\n            throw new IllegalArgumentException('The region must fit inside the matrix');\n        }\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        for (var y = top; y < bottom; y++) {\n            var offset = y * rowSize;\n            for (var x = left; x < right; x++) {\n                bits[offset + Math.floor(x / 32)] |= ((1 << (x & 0x1f)) & 0xFFFFFFFF);\n            }\n        }\n    };\n    /**\n     * A fast method to retrieve one row of data from the matrix as a BitArray.\n     *\n     * @param y The row to retrieve\n     * @param row An optional caller-allocated BitArray, will be allocated if null or too small\n     * @return The resulting BitArray - this reference should always be used even when passing\n     *         your own row\n     */\n    BitMatrix.prototype.getRow = function (y /*int*/, row) {\n        if (row === null || row === undefined || row.getSize() < this.width) {\n            row = new BitArray(this.width);\n        }\n        else {\n            row.clear();\n        }\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        var offset = y * rowSize;\n        for (var x = 0; x < rowSize; x++) {\n            row.setBulk(x * 32, bits[offset + x]);\n        }\n        return row;\n    };\n    /**\n     * @param y row to set\n     * @param row {@link BitArray} to copy from\n     */\n    BitMatrix.prototype.setRow = function (y /*int*/, row) {\n        System.arraycopy(row.getBitArray(), 0, this.bits, y * this.rowSize, this.rowSize);\n    };\n    /**\n     * Modifies this {@code BitMatrix} to represent the same but rotated 180 degrees\n     */\n    BitMatrix.prototype.rotate180 = function () {\n        var width = this.getWidth();\n        var height = this.getHeight();\n        var topRow = new BitArray(width);\n        var bottomRow = new BitArray(width);\n        for (var i = 0, length_1 = Math.floor((height + 1) / 2); i < length_1; i++) {\n            topRow = this.getRow(i, topRow);\n            bottomRow = this.getRow(height - 1 - i, bottomRow);\n            topRow.reverse();\n            bottomRow.reverse();\n            this.setRow(i, bottomRow);\n            this.setRow(height - 1 - i, topRow);\n        }\n    };\n    /**\n     * This is useful in detecting the enclosing rectangle of a 'pure' barcode.\n     *\n     * @return {@code left,top,width,height} enclosing rectangle of all 1 bits, or null if it is all white\n     */\n    BitMatrix.prototype.getEnclosingRectangle = function () {\n        var width = this.width;\n        var height = this.height;\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        var left = width;\n        var top = height;\n        var right = -1;\n        var bottom = -1;\n        for (var y = 0; y < height; y++) {\n            for (var x32 = 0; x32 < rowSize; x32++) {\n                var theBits = bits[y * rowSize + x32];\n                if (theBits !== 0) {\n                    if (y < top) {\n                        top = y;\n                    }\n                    if (y > bottom) {\n                        bottom = y;\n                    }\n                    if (x32 * 32 < left) {\n                        var bit = 0;\n                        while (((theBits << (31 - bit)) & 0xFFFFFFFF) === 0) {\n                            bit++;\n                        }\n                        if ((x32 * 32 + bit) < left) {\n                            left = x32 * 32 + bit;\n                        }\n                    }\n                    if (x32 * 32 + 31 > right) {\n                        var bit = 31;\n                        while ((theBits >>> bit) === 0) {\n                            bit--;\n                        }\n                        if ((x32 * 32 + bit) > right) {\n                            right = x32 * 32 + bit;\n                        }\n                    }\n                }\n            }\n        }\n        if (right < left || bottom < top) {\n            return null;\n        }\n        return Int32Array.from([left, top, right - left + 1, bottom - top + 1]);\n    };\n    /**\n     * This is useful in detecting a corner of a 'pure' barcode.\n     *\n     * @return {@code x,y} coordinate of top-left-most 1 bit, or null if it is all white\n     */\n    BitMatrix.prototype.getTopLeftOnBit = function () {\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        var bitsOffset = 0;\n        while (bitsOffset < bits.length && bits[bitsOffset] === 0) {\n            bitsOffset++;\n        }\n        if (bitsOffset === bits.length) {\n            return null;\n        }\n        var y = bitsOffset / rowSize;\n        var x = (bitsOffset % rowSize) * 32;\n        var theBits = bits[bitsOffset];\n        var bit = 0;\n        while (((theBits << (31 - bit)) & 0xFFFFFFFF) === 0) {\n            bit++;\n        }\n        x += bit;\n        return Int32Array.from([x, y]);\n    };\n    BitMatrix.prototype.getBottomRightOnBit = function () {\n        var rowSize = this.rowSize;\n        var bits = this.bits;\n        var bitsOffset = bits.length - 1;\n        while (bitsOffset >= 0 && bits[bitsOffset] === 0) {\n            bitsOffset--;\n        }\n        if (bitsOffset < 0) {\n            return null;\n        }\n        var y = Math.floor(bitsOffset / rowSize);\n        var x = Math.floor(bitsOffset % rowSize) * 32;\n        var theBits = bits[bitsOffset];\n        var bit = 31;\n        while ((theBits >>> bit) === 0) {\n            bit--;\n        }\n        x += bit;\n        return Int32Array.from([x, y]);\n    };\n    /**\n     * @return The width of the matrix\n     */\n    BitMatrix.prototype.getWidth = function () {\n        return this.width;\n    };\n    /**\n     * @return The height of the matrix\n     */\n    BitMatrix.prototype.getHeight = function () {\n        return this.height;\n    };\n    /**\n     * @return The row size of the matrix\n     */\n    BitMatrix.prototype.getRowSize = function () {\n        return this.rowSize;\n    };\n    /*@Override*/\n    BitMatrix.prototype.equals = function (o) {\n        if (!(o instanceof BitMatrix)) {\n            return false;\n        }\n        var other = o;\n        return this.width === other.width && this.height === other.height && this.rowSize === other.rowSize &&\n            Arrays.equals(this.bits, other.bits);\n    };\n    /*@Override*/\n    BitMatrix.prototype.hashCode = function () {\n        var hash = this.width;\n        hash = 31 * hash + this.width;\n        hash = 31 * hash + this.height;\n        hash = 31 * hash + this.rowSize;\n        hash = 31 * hash + Arrays.hashCode(this.bits);\n        return hash;\n    };\n    /**\n     * @return string representation using \"X\" for set and \" \" for unset bits\n     */\n    /*@Override*/\n    // public toString(): string {\n    //   return toString(\": \"X, \"  \")\n    // }\n    /**\n     * @param setString representation of a set bit\n     * @param unsetString representation of an unset bit\n     * @return string representation of entire matrix utilizing given strings\n     */\n    // public toString(setString: string = \"X \", unsetString: string = \"  \"): string {\n    //   return this.buildToString(setString, unsetString, \"\\n\")\n    // }\n    /**\n     * @param setString representation of a set bit\n     * @param unsetString representation of an unset bit\n     * @param lineSeparator newline character in string representation\n     * @return string representation of entire matrix utilizing given strings and line separator\n     * @deprecated call {@link #toString(String,String)} only, which uses \\n line separator always\n     */\n    // @Deprecated\n    BitMatrix.prototype.toString = function (setString, unsetString, lineSeparator) {\n        if (setString === void 0) { setString = 'X '; }\n        if (unsetString === void 0) { unsetString = '  '; }\n        if (lineSeparator === void 0) { lineSeparator = '\\n'; }\n        return this.buildToString(setString, unsetString, lineSeparator);\n    };\n    BitMatrix.prototype.buildToString = function (setString, unsetString, lineSeparator) {\n        var result = new StringBuilder();\n        // result.append(lineSeparator);\n        for (var y = 0, height = this.height; y < height; y++) {\n            for (var x = 0, width = this.width; x < width; x++) {\n                result.append(this.get(x, y) ? setString : unsetString);\n            }\n            result.append(lineSeparator);\n        }\n        return result.toString();\n    };\n    /*@Override*/\n    BitMatrix.prototype.clone = function () {\n        return new BitMatrix(this.width, this.height, this.rowSize, this.bits.slice());\n    };\n    return BitMatrix;\n}());\nexport default BitMatrix;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,wBAAwB,MAAM,6BAA6B;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,CAAC,2BAA2B,aAAe,YAAY;EAChE;AACJ;AACA;AACA;AACA;EACI;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASA,SAASA,CAACC,KAAK,CAAC,SAASC,MAAM,CAAC,SAASC,OAAO,CAAC,SAASC,IAAI,EAAE;IACrE,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAIC,SAAS,KAAKH,MAAM,IAAI,IAAI,KAAKA,MAAM,EAAE;MACzCA,MAAM,GAAGD,KAAK;IAClB;IACA,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAID,KAAK,GAAG,CAAC,IAAIC,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIH,wBAAwB,CAAC,wCAAwC,CAAC;IAChF;IACA,IAAIM,SAAS,KAAKF,OAAO,IAAI,IAAI,KAAKA,OAAO,EAAE;MAC3CA,OAAO,GAAGG,IAAI,CAACC,KAAK,CAAC,CAACN,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC;IAC3C;IACA,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAIE,SAAS,KAAKD,IAAI,IAAI,IAAI,KAAKA,IAAI,EAAE;MACrC,IAAI,CAACA,IAAI,GAAG,IAAII,UAAU,CAAC,IAAI,CAACL,OAAO,GAAG,IAAI,CAACD,MAAM,CAAC;IAC1D;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIF,SAAS,CAACS,qBAAqB,GAAG,UAAUC,KAAK,EAAE;IAC/C,IAAIR,MAAM,GAAGQ,KAAK,CAACC,MAAM;IACzB,IAAIV,KAAK,GAAGS,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM;IAC3B,IAAIP,IAAI,GAAG,IAAIJ,SAAS,CAACC,KAAK,EAAEC,MAAM,CAAC;IACvC,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,MAAM,EAAEU,CAAC,EAAE,EAAE;MAC7B,IAAIC,MAAM,GAAGH,KAAK,CAACE,CAAC,CAAC;MACrB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,KAAK,EAAEa,CAAC,EAAE,EAAE;QAC5B,IAAID,MAAM,CAACC,CAAC,CAAC,EAAE;UACXV,IAAI,CAACW,GAAG,CAACD,CAAC,EAAEF,CAAC,CAAC;QAClB;MACJ;IACJ;IACA,OAAOR,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIJ,SAAS,CAACgB,eAAe,GAAG,UAAUC,oBAAoB,EAAEC,SAAS,EAAEC,WAAW,EAAE;IAChF,IAAIF,oBAAoB,KAAK,IAAI,EAAE;MAC/B,MAAM,IAAIlB,wBAAwB,CAAC,qCAAqC,CAAC;IAC7E;IACA,IAAIK,IAAI,GAAG,IAAIgB,KAAK,CAACH,oBAAoB,CAACN,MAAM,CAAC;IACjD,IAAIU,OAAO,GAAG,CAAC;IACf,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAG,CAAC;IACX,OAAOA,GAAG,GAAGR,oBAAoB,CAACN,MAAM,EAAE;MACtC,IAAIM,oBAAoB,CAACS,MAAM,CAACD,GAAG,CAAC,KAAK,IAAI,IACzCR,oBAAoB,CAACS,MAAM,CAACD,GAAG,CAAC,KAAK,IAAI,EAAE;QAC3C,IAAIJ,OAAO,GAAGC,WAAW,EAAE;UACvB,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;YAClBA,SAAS,GAAGF,OAAO,GAAGC,WAAW;UACrC,CAAC,MACI,IAAID,OAAO,GAAGC,WAAW,KAAKC,SAAS,EAAE;YAC1C,MAAM,IAAIxB,wBAAwB,CAAC,0BAA0B,CAAC;UAClE;UACAuB,WAAW,GAAGD,OAAO;UACrBG,KAAK,EAAE;QACX;QACAC,GAAG,EAAE;MACT,CAAC,MACI,IAAIR,oBAAoB,CAACU,SAAS,CAACF,GAAG,EAAEA,GAAG,GAAGP,SAAS,CAACP,MAAM,CAAC,KAAKO,SAAS,EAAE;QAChFO,GAAG,IAAIP,SAAS,CAACP,MAAM;QACvBP,IAAI,CAACiB,OAAO,CAAC,GAAG,IAAI;QACpBA,OAAO,EAAE;MACb,CAAC,MACI,IAAIJ,oBAAoB,CAACU,SAAS,CAACF,GAAG,EAAEA,GAAG,GAAGN,WAAW,CAACR,MAAM,CAAC,KAAKQ,WAAW,EAAE;QACpFM,GAAG,IAAIN,WAAW,CAACR,MAAM;QACzBP,IAAI,CAACiB,OAAO,CAAC,GAAG,KAAK;QACrBA,OAAO,EAAE;MACb,CAAC,MACI;QACD,MAAM,IAAItB,wBAAwB,CAAC,iCAAiC,GAAGkB,oBAAoB,CAACU,SAAS,CAACF,GAAG,CAAC,CAAC;MAC/G;IACJ;IACA;IACA,IAAIJ,OAAO,GAAGC,WAAW,EAAE;MACvB,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;QAClBA,SAAS,GAAGF,OAAO,GAAGC,WAAW;MACrC,CAAC,MACI,IAAID,OAAO,GAAGC,WAAW,KAAKC,SAAS,EAAE;QAC1C,MAAM,IAAIxB,wBAAwB,CAAC,0BAA0B,CAAC;MAClE;MACAyB,KAAK,EAAE;IACX;IACA,IAAII,MAAM,GAAG,IAAI5B,SAAS,CAACuB,SAAS,EAAEC,KAAK,CAAC;IAC5C,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,OAAO,EAAET,CAAC,EAAE,EAAE;MAC9B,IAAIR,IAAI,CAACQ,CAAC,CAAC,EAAE;QACTgB,MAAM,CAACb,GAAG,CAACT,IAAI,CAACC,KAAK,CAACK,CAAC,GAAGW,SAAS,CAAC,EAAEjB,IAAI,CAACC,KAAK,CAACK,CAAC,GAAGW,SAAS,CAAC,CAAC;MACpE;IACJ;IACA,OAAOK,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI5B,SAAS,CAAC6B,SAAS,CAACC,GAAG,GAAG,UAAUC,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IACtD,IAAIC,MAAM,GAAGD,CAAC,GAAG,IAAI,CAAC7B,OAAO,GAAGG,IAAI,CAACC,KAAK,CAACwB,CAAC,GAAG,EAAE,CAAC;IAClD,OAAO,CAAE,IAAI,CAAC3B,IAAI,CAAC6B,MAAM,CAAC,MAAMF,CAAC,GAAG,IAAI,CAAC,GAAI,CAAC,MAAM,CAAC;EACzD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI/B,SAAS,CAAC6B,SAAS,CAACd,GAAG,GAAG,UAAUgB,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IACtD,IAAIC,MAAM,GAAGD,CAAC,GAAG,IAAI,CAAC7B,OAAO,GAAGG,IAAI,CAACC,KAAK,CAACwB,CAAC,GAAG,EAAE,CAAC;IAClD,IAAI,CAAC3B,IAAI,CAAC6B,MAAM,CAAC,IAAK,CAAC,KAAKF,CAAC,GAAG,IAAI,CAAC,GAAI,UAAU;EACvD,CAAC;EACD/B,SAAS,CAAC6B,SAAS,CAACK,KAAK,GAAG,UAAUH,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IACxD,IAAIC,MAAM,GAAGD,CAAC,GAAG,IAAI,CAAC7B,OAAO,GAAGG,IAAI,CAACC,KAAK,CAACwB,CAAC,GAAG,EAAE,CAAC;IAClD,IAAI,CAAC3B,IAAI,CAAC6B,MAAM,CAAC,IAAI,EAAG,CAAC,KAAKF,CAAC,GAAG,IAAI,CAAC,GAAI,UAAU,CAAC;EAC1D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI/B,SAAS,CAAC6B,SAAS,CAACM,IAAI,GAAG,UAAUJ,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IACvD,IAAIC,MAAM,GAAGD,CAAC,GAAG,IAAI,CAAC7B,OAAO,GAAGG,IAAI,CAACC,KAAK,CAACwB,CAAC,GAAG,EAAE,CAAC;IAClD,IAAI,CAAC3B,IAAI,CAAC6B,MAAM,CAAC,IAAM,CAAC,KAAKF,CAAC,GAAG,IAAI,CAAC,GAAI,UAAW;EACzD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI/B,SAAS,CAAC6B,SAAS,CAACO,GAAG,GAAG,UAAUC,IAAI,EAAE;IACtC,IAAI,IAAI,CAACpC,KAAK,KAAKoC,IAAI,CAACC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAACpC,MAAM,KAAKmC,IAAI,CAACE,SAAS,CAAC,CAAC,IAC/D,IAAI,CAACpC,OAAO,KAAKkC,IAAI,CAACG,UAAU,CAAC,CAAC,EAAE;MACvC,MAAM,IAAIzC,wBAAwB,CAAC,sCAAsC,CAAC;IAC9E;IACA,IAAI0C,QAAQ,GAAG,IAAI9C,QAAQ,CAACW,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IAC5D,IAAIE,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAE9B,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE8B,CAAC,GAAG9B,MAAM,EAAE8B,CAAC,EAAE,EAAE;MACnD,IAAIC,MAAM,GAAGD,CAAC,GAAG7B,OAAO;MACxB,IAAIuC,GAAG,GAAGL,IAAI,CAACM,MAAM,CAACX,CAAC,EAAES,QAAQ,CAAC,CAACG,WAAW,CAAC,CAAC;MAChD,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,OAAO,EAAE4B,CAAC,EAAE,EAAE;QAC9B3B,IAAI,CAAC6B,MAAM,GAAGF,CAAC,CAAC,IAAIW,GAAG,CAACX,CAAC,CAAC;MAC9B;IACJ;EACJ,CAAC;EACD;AACJ;AACA;EACI/B,SAAS,CAAC6B,SAAS,CAACgB,KAAK,GAAG,YAAY;IACpC,IAAIzC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI0C,GAAG,GAAG1C,IAAI,CAACO,MAAM;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,GAAG,EAAElC,CAAC,EAAE,EAAE;MAC1BR,IAAI,CAACQ,CAAC,CAAC,GAAG,CAAC;IACf;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIZ,SAAS,CAAC6B,SAAS,CAACkB,SAAS,GAAG,UAAUC,IAAI,CAAC,SAASC,GAAG,CAAC,SAAShD,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IAChG,IAAI+C,GAAG,GAAG,CAAC,IAAID,IAAI,GAAG,CAAC,EAAE;MACrB,MAAM,IAAIjD,wBAAwB,CAAC,kCAAkC,CAAC;IAC1E;IACA,IAAIG,MAAM,GAAG,CAAC,IAAID,KAAK,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIF,wBAAwB,CAAC,qCAAqC,CAAC;IAC7E;IACA,IAAImD,KAAK,GAAGF,IAAI,GAAG/C,KAAK;IACxB,IAAIkD,MAAM,GAAGF,GAAG,GAAG/C,MAAM;IACzB,IAAIiD,MAAM,GAAG,IAAI,CAACjD,MAAM,IAAIgD,KAAK,GAAG,IAAI,CAACjD,KAAK,EAAE;MAC5C,MAAM,IAAIF,wBAAwB,CAAC,uCAAuC,CAAC;IAC/E;IACA,IAAII,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,KAAK,IAAI4B,CAAC,GAAGiB,GAAG,EAAEjB,CAAC,GAAGmB,MAAM,EAAEnB,CAAC,EAAE,EAAE;MAC/B,IAAIC,MAAM,GAAGD,CAAC,GAAG7B,OAAO;MACxB,KAAK,IAAI4B,CAAC,GAAGiB,IAAI,EAAEjB,CAAC,GAAGmB,KAAK,EAAEnB,CAAC,EAAE,EAAE;QAC/B3B,IAAI,CAAC6B,MAAM,GAAG3B,IAAI,CAACC,KAAK,CAACwB,CAAC,GAAG,EAAE,CAAC,CAAC,IAAM,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAC,GAAI,UAAW;MACzE;IACJ;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI/B,SAAS,CAAC6B,SAAS,CAACc,MAAM,GAAG,UAAUX,CAAC,CAAC,SAASU,GAAG,EAAE;IACnD,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKrC,SAAS,IAAIqC,GAAG,CAACU,OAAO,CAAC,CAAC,GAAG,IAAI,CAACnD,KAAK,EAAE;MACjEyC,GAAG,GAAG,IAAI/C,QAAQ,CAAC,IAAI,CAACM,KAAK,CAAC;IAClC,CAAC,MACI;MACDyC,GAAG,CAACG,KAAK,CAAC,CAAC;IACf;IACA,IAAI1C,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI6B,MAAM,GAAGD,CAAC,GAAG7B,OAAO;IACxB,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,OAAO,EAAE4B,CAAC,EAAE,EAAE;MAC9BW,GAAG,CAACW,OAAO,CAACtB,CAAC,GAAG,EAAE,EAAE3B,IAAI,CAAC6B,MAAM,GAAGF,CAAC,CAAC,CAAC;IACzC;IACA,OAAOW,GAAG;EACd,CAAC;EACD;AACJ;AACA;AACA;EACI1C,SAAS,CAAC6B,SAAS,CAACyB,MAAM,GAAG,UAAUtB,CAAC,CAAC,SAASU,GAAG,EAAE;IACnD9C,MAAM,CAAC2D,SAAS,CAACb,GAAG,CAACE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACxC,IAAI,EAAE4B,CAAC,GAAG,IAAI,CAAC7B,OAAO,EAAE,IAAI,CAACA,OAAO,CAAC;EACrF,CAAC;EACD;AACJ;AACA;EACIH,SAAS,CAAC6B,SAAS,CAAC2B,SAAS,GAAG,YAAY;IACxC,IAAIvD,KAAK,GAAG,IAAI,CAACqC,QAAQ,CAAC,CAAC;IAC3B,IAAIpC,MAAM,GAAG,IAAI,CAACqC,SAAS,CAAC,CAAC;IAC7B,IAAIkB,MAAM,GAAG,IAAI9D,QAAQ,CAACM,KAAK,CAAC;IAChC,IAAIyD,SAAS,GAAG,IAAI/D,QAAQ,CAACM,KAAK,CAAC;IACnC,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAE+C,QAAQ,GAAGrD,IAAI,CAACC,KAAK,CAAC,CAACL,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,EAAEU,CAAC,GAAG+C,QAAQ,EAAE/C,CAAC,EAAE,EAAE;MACxE6C,MAAM,GAAG,IAAI,CAACd,MAAM,CAAC/B,CAAC,EAAE6C,MAAM,CAAC;MAC/BC,SAAS,GAAG,IAAI,CAACf,MAAM,CAACzC,MAAM,GAAG,CAAC,GAAGU,CAAC,EAAE8C,SAAS,CAAC;MAClDD,MAAM,CAACG,OAAO,CAAC,CAAC;MAChBF,SAAS,CAACE,OAAO,CAAC,CAAC;MACnB,IAAI,CAACN,MAAM,CAAC1C,CAAC,EAAE8C,SAAS,CAAC;MACzB,IAAI,CAACJ,MAAM,CAACpD,MAAM,GAAG,CAAC,GAAGU,CAAC,EAAE6C,MAAM,CAAC;IACvC;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIzD,SAAS,CAAC6B,SAAS,CAACgC,qBAAqB,GAAG,YAAY;IACpD,IAAI5D,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI4C,IAAI,GAAG/C,KAAK;IAChB,IAAIgD,GAAG,GAAG/C,MAAM;IAChB,IAAIgD,KAAK,GAAG,CAAC,CAAC;IACd,IAAIC,MAAM,GAAG,CAAC,CAAC;IACf,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG9B,MAAM,EAAE8B,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAI8B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG3D,OAAO,EAAE2D,GAAG,EAAE,EAAE;QACpC,IAAIC,OAAO,GAAG3D,IAAI,CAAC4B,CAAC,GAAG7B,OAAO,GAAG2D,GAAG,CAAC;QACrC,IAAIC,OAAO,KAAK,CAAC,EAAE;UACf,IAAI/B,CAAC,GAAGiB,GAAG,EAAE;YACTA,GAAG,GAAGjB,CAAC;UACX;UACA,IAAIA,CAAC,GAAGmB,MAAM,EAAE;YACZA,MAAM,GAAGnB,CAAC;UACd;UACA,IAAI8B,GAAG,GAAG,EAAE,GAAGd,IAAI,EAAE;YACjB,IAAIgB,GAAG,GAAG,CAAC;YACX,OAAO,CAAED,OAAO,IAAK,EAAE,GAAGC,GAAI,GAAI,UAAU,MAAM,CAAC,EAAE;cACjDA,GAAG,EAAE;YACT;YACA,IAAKF,GAAG,GAAG,EAAE,GAAGE,GAAG,GAAIhB,IAAI,EAAE;cACzBA,IAAI,GAAGc,GAAG,GAAG,EAAE,GAAGE,GAAG;YACzB;UACJ;UACA,IAAIF,GAAG,GAAG,EAAE,GAAG,EAAE,GAAGZ,KAAK,EAAE;YACvB,IAAIc,GAAG,GAAG,EAAE;YACZ,OAAQD,OAAO,KAAKC,GAAG,KAAM,CAAC,EAAE;cAC5BA,GAAG,EAAE;YACT;YACA,IAAKF,GAAG,GAAG,EAAE,GAAGE,GAAG,GAAId,KAAK,EAAE;cAC1BA,KAAK,GAAGY,GAAG,GAAG,EAAE,GAAGE,GAAG;YAC1B;UACJ;QACJ;MACJ;IACJ;IACA,IAAId,KAAK,GAAGF,IAAI,IAAIG,MAAM,GAAGF,GAAG,EAAE;MAC9B,OAAO,IAAI;IACf;IACA,OAAOzC,UAAU,CAACyD,IAAI,CAAC,CAACjB,IAAI,EAAEC,GAAG,EAAEC,KAAK,GAAGF,IAAI,GAAG,CAAC,EAAEG,MAAM,GAAGF,GAAG,GAAG,CAAC,CAAC,CAAC;EAC3E,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIjD,SAAS,CAAC6B,SAAS,CAACqC,eAAe,GAAG,YAAY;IAC9C,IAAI/D,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI+D,UAAU,GAAG,CAAC;IAClB,OAAOA,UAAU,GAAG/D,IAAI,CAACO,MAAM,IAAIP,IAAI,CAAC+D,UAAU,CAAC,KAAK,CAAC,EAAE;MACvDA,UAAU,EAAE;IAChB;IACA,IAAIA,UAAU,KAAK/D,IAAI,CAACO,MAAM,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,IAAIqB,CAAC,GAAGmC,UAAU,GAAGhE,OAAO;IAC5B,IAAI4B,CAAC,GAAIoC,UAAU,GAAGhE,OAAO,GAAI,EAAE;IACnC,IAAI4D,OAAO,GAAG3D,IAAI,CAAC+D,UAAU,CAAC;IAC9B,IAAIH,GAAG,GAAG,CAAC;IACX,OAAO,CAAED,OAAO,IAAK,EAAE,GAAGC,GAAI,GAAI,UAAU,MAAM,CAAC,EAAE;MACjDA,GAAG,EAAE;IACT;IACAjC,CAAC,IAAIiC,GAAG;IACR,OAAOxD,UAAU,CAACyD,IAAI,CAAC,CAAClC,CAAC,EAAEC,CAAC,CAAC,CAAC;EAClC,CAAC;EACDhC,SAAS,CAAC6B,SAAS,CAACuC,mBAAmB,GAAG,YAAY;IAClD,IAAIjE,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAI+D,UAAU,GAAG/D,IAAI,CAACO,MAAM,GAAG,CAAC;IAChC,OAAOwD,UAAU,IAAI,CAAC,IAAI/D,IAAI,CAAC+D,UAAU,CAAC,KAAK,CAAC,EAAE;MAC9CA,UAAU,EAAE;IAChB;IACA,IAAIA,UAAU,GAAG,CAAC,EAAE;MAChB,OAAO,IAAI;IACf;IACA,IAAInC,CAAC,GAAG1B,IAAI,CAACC,KAAK,CAAC4D,UAAU,GAAGhE,OAAO,CAAC;IACxC,IAAI4B,CAAC,GAAGzB,IAAI,CAACC,KAAK,CAAC4D,UAAU,GAAGhE,OAAO,CAAC,GAAG,EAAE;IAC7C,IAAI4D,OAAO,GAAG3D,IAAI,CAAC+D,UAAU,CAAC;IAC9B,IAAIH,GAAG,GAAG,EAAE;IACZ,OAAQD,OAAO,KAAKC,GAAG,KAAM,CAAC,EAAE;MAC5BA,GAAG,EAAE;IACT;IACAjC,CAAC,IAAIiC,GAAG;IACR,OAAOxD,UAAU,CAACyD,IAAI,CAAC,CAAClC,CAAC,EAAEC,CAAC,CAAC,CAAC;EAClC,CAAC;EACD;AACJ;AACA;EACIhC,SAAS,CAAC6B,SAAS,CAACS,QAAQ,GAAG,YAAY;IACvC,OAAO,IAAI,CAACrC,KAAK;EACrB,CAAC;EACD;AACJ;AACA;EACID,SAAS,CAAC6B,SAAS,CAACU,SAAS,GAAG,YAAY;IACxC,OAAO,IAAI,CAACrC,MAAM;EACtB,CAAC;EACD;AACJ;AACA;EACIF,SAAS,CAAC6B,SAAS,CAACW,UAAU,GAAG,YAAY;IACzC,OAAO,IAAI,CAACrC,OAAO;EACvB,CAAC;EACD;EACAH,SAAS,CAAC6B,SAAS,CAACwC,MAAM,GAAG,UAAUC,CAAC,EAAE;IACtC,IAAI,EAAEA,CAAC,YAAYtE,SAAS,CAAC,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA,IAAIuE,KAAK,GAAGD,CAAC;IACb,OAAO,IAAI,CAACrE,KAAK,KAAKsE,KAAK,CAACtE,KAAK,IAAI,IAAI,CAACC,MAAM,KAAKqE,KAAK,CAACrE,MAAM,IAAI,IAAI,CAACC,OAAO,KAAKoE,KAAK,CAACpE,OAAO,IAC/FN,MAAM,CAACwE,MAAM,CAAC,IAAI,CAACjE,IAAI,EAAEmE,KAAK,CAACnE,IAAI,CAAC;EAC5C,CAAC;EACD;EACAJ,SAAS,CAAC6B,SAAS,CAAC2C,QAAQ,GAAG,YAAY;IACvC,IAAIC,IAAI,GAAG,IAAI,CAACxE,KAAK;IACrBwE,IAAI,GAAG,EAAE,GAAGA,IAAI,GAAG,IAAI,CAACxE,KAAK;IAC7BwE,IAAI,GAAG,EAAE,GAAGA,IAAI,GAAG,IAAI,CAACvE,MAAM;IAC9BuE,IAAI,GAAG,EAAE,GAAGA,IAAI,GAAG,IAAI,CAACtE,OAAO;IAC/BsE,IAAI,GAAG,EAAE,GAAGA,IAAI,GAAG5E,MAAM,CAAC2E,QAAQ,CAAC,IAAI,CAACpE,IAAI,CAAC;IAC7C,OAAOqE,IAAI;EACf,CAAC;EACD;AACJ;AACA;EACI;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;EACI;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACAzE,SAAS,CAAC6B,SAAS,CAAC6C,QAAQ,GAAG,UAAUxD,SAAS,EAAEC,WAAW,EAAEwD,aAAa,EAAE;IAC5E,IAAIzD,SAAS,KAAK,KAAK,CAAC,EAAE;MAAEA,SAAS,GAAG,IAAI;IAAE;IAC9C,IAAIC,WAAW,KAAK,KAAK,CAAC,EAAE;MAAEA,WAAW,GAAG,IAAI;IAAE;IAClD,IAAIwD,aAAa,KAAK,KAAK,CAAC,EAAE;MAAEA,aAAa,GAAG,IAAI;IAAE;IACtD,OAAO,IAAI,CAACC,aAAa,CAAC1D,SAAS,EAAEC,WAAW,EAAEwD,aAAa,CAAC;EACpE,CAAC;EACD3E,SAAS,CAAC6B,SAAS,CAAC+C,aAAa,GAAG,UAAU1D,SAAS,EAAEC,WAAW,EAAEwD,aAAa,EAAE;IACjF,IAAIE,MAAM,GAAG,IAAI/E,aAAa,CAAC,CAAC;IAChC;IACA,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAE9B,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE8B,CAAC,GAAG9B,MAAM,EAAE8B,CAAC,EAAE,EAAE;MACnD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAE9B,KAAK,GAAG,IAAI,CAACA,KAAK,EAAE8B,CAAC,GAAG9B,KAAK,EAAE8B,CAAC,EAAE,EAAE;QAChD8C,MAAM,CAACC,MAAM,CAAC,IAAI,CAAChD,GAAG,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGd,SAAS,GAAGC,WAAW,CAAC;MAC3D;MACA0D,MAAM,CAACC,MAAM,CAACH,aAAa,CAAC;IAChC;IACA,OAAOE,MAAM,CAACH,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACD;EACA1E,SAAS,CAAC6B,SAAS,CAACkD,KAAK,GAAG,YAAY;IACpC,OAAO,IAAI/E,SAAS,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAACC,IAAI,CAAC4E,KAAK,CAAC,CAAC,CAAC;EAClF,CAAC;EACD,OAAOhF,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}