{"ast": null, "code": "/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\n//import com.google.zxing.ResultMetadataType;\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes Code 93 barcodes.</p>\n *\n * <AUTHOR> Owen\n * @see Code39Reader\n */\nvar Code93Reader = /** @class */function (_super) {\n  __extends(Code93Reader, _super);\n  //public Code93Reader() {\n  //  decodeRowResult = new StringBuilder(20);\n  //  counters = new int[6];\n  //}\n  function Code93Reader() {\n    var _this = _super.call(this) || this;\n    _this.decodeRowResult = '';\n    _this.counters = new Int32Array(6);\n    return _this;\n  }\n  Code93Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n    var e_1, _a, e_2, _b;\n    var start = this.findAsteriskPattern(row);\n    // Read off white space\n    var nextStart = row.getNextSet(start[1]);\n    var end = row.getSize();\n    var theCounters = this.counters;\n    theCounters.fill(0);\n    this.decodeRowResult = '';\n    var decodedChar;\n    var lastStart;\n    do {\n      Code93Reader.recordPattern(row, nextStart, theCounters);\n      var pattern = this.toPattern(theCounters);\n      if (pattern < 0) {\n        throw new NotFoundException();\n      }\n      decodedChar = this.patternToChar(pattern);\n      this.decodeRowResult += decodedChar;\n      lastStart = nextStart;\n      try {\n        for (var theCounters_1 = (e_1 = void 0, __values(theCounters)), theCounters_1_1 = theCounters_1.next(); !theCounters_1_1.done; theCounters_1_1 = theCounters_1.next()) {\n          var counter = theCounters_1_1.value;\n          nextStart += counter;\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (theCounters_1_1 && !theCounters_1_1.done && (_a = theCounters_1.return)) _a.call(theCounters_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      // Read off white space\n      nextStart = row.getNextSet(nextStart);\n    } while (decodedChar !== '*');\n    this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 1); // remove asterisk\n    var lastPatternSize = 0;\n    try {\n      for (var theCounters_2 = __values(theCounters), theCounters_2_1 = theCounters_2.next(); !theCounters_2_1.done; theCounters_2_1 = theCounters_2.next()) {\n        var counter = theCounters_2_1.value;\n        lastPatternSize += counter;\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (theCounters_2_1 && !theCounters_2_1.done && (_b = theCounters_2.return)) _b.call(theCounters_2);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    // Should be at least one more black module\n    if (nextStart === end || !row.get(nextStart)) {\n      throw new NotFoundException();\n    }\n    if (this.decodeRowResult.length < 2) {\n      // false positive -- need at least 2 checksum digits\n      throw new NotFoundException();\n    }\n    this.checkChecksums(this.decodeRowResult);\n    // Remove checksum digits\n    this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 2);\n    var resultString = this.decodeExtended(this.decodeRowResult);\n    var left = (start[1] + start[0]) / 2.0;\n    var right = lastStart + lastPatternSize / 2.0;\n    return new Result(resultString, null, 0, [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)], BarcodeFormat.CODE_93, new Date().getTime());\n  };\n  Code93Reader.prototype.findAsteriskPattern = function (row) {\n    var width = row.getSize();\n    var rowOffset = row.getNextSet(0);\n    this.counters.fill(0);\n    var theCounters = this.counters;\n    var patternStart = rowOffset;\n    var isWhite = false;\n    var patternLength = theCounters.length;\n    var counterPosition = 0;\n    for (var i = rowOffset; i < width; i++) {\n      if (row.get(i) !== isWhite) {\n        theCounters[counterPosition]++;\n      } else {\n        if (counterPosition === patternLength - 1) {\n          if (this.toPattern(theCounters) === Code93Reader.ASTERISK_ENCODING) {\n            return new Int32Array([patternStart, i]);\n          }\n          patternStart += theCounters[0] + theCounters[1];\n          theCounters.copyWithin(0, 2, 2 + counterPosition - 1);\n          theCounters[counterPosition - 1] = 0;\n          theCounters[counterPosition] = 0;\n          counterPosition--;\n        } else {\n          counterPosition++;\n        }\n        theCounters[counterPosition] = 1;\n        isWhite = !isWhite;\n      }\n    }\n    throw new NotFoundException();\n  };\n  Code93Reader.prototype.toPattern = function (counters) {\n    var e_3, _a;\n    var sum = 0;\n    try {\n      for (var counters_1 = __values(counters), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n        var counter = counters_1_1.value;\n        sum += counter;\n      }\n    } catch (e_3_1) {\n      e_3 = {\n        error: e_3_1\n      };\n    } finally {\n      try {\n        if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n      } finally {\n        if (e_3) throw e_3.error;\n      }\n    }\n    var pattern = 0;\n    var max = counters.length;\n    for (var i = 0; i < max; i++) {\n      var scaled = Math.round(counters[i] * 9.0 / sum);\n      if (scaled < 1 || scaled > 4) {\n        return -1;\n      }\n      if ((i & 0x01) === 0) {\n        for (var j = 0; j < scaled; j++) {\n          pattern = pattern << 1 | 0x01;\n        }\n      } else {\n        pattern <<= scaled;\n      }\n    }\n    return pattern;\n  };\n  Code93Reader.prototype.patternToChar = function (pattern) {\n    for (var i = 0; i < Code93Reader.CHARACTER_ENCODINGS.length; i++) {\n      if (Code93Reader.CHARACTER_ENCODINGS[i] === pattern) {\n        return Code93Reader.ALPHABET_STRING.charAt(i);\n      }\n    }\n    throw new NotFoundException();\n  };\n  Code93Reader.prototype.decodeExtended = function (encoded) {\n    var length = encoded.length;\n    var decoded = '';\n    for (var i = 0; i < length; i++) {\n      var c = encoded.charAt(i);\n      if (c >= 'a' && c <= 'd') {\n        if (i >= length - 1) {\n          throw new FormatException();\n        }\n        var next = encoded.charAt(i + 1);\n        var decodedChar = '\\0';\n        switch (c) {\n          case 'd':\n            // +A to +Z map to a to z\n            if (next >= 'A' && next <= 'Z') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) + 32);\n            } else {\n              throw new FormatException();\n            }\n            break;\n          case 'a':\n            // $A to $Z map to control codes SH to SB\n            if (next >= 'A' && next <= 'Z') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) - 64);\n            } else {\n              throw new FormatException();\n            }\n            break;\n          case 'b':\n            if (next >= 'A' && next <= 'E') {\n              // %A to %E map to control codes ESC to USep\n              decodedChar = String.fromCharCode(next.charCodeAt(0) - 38);\n            } else if (next >= 'F' && next <= 'J') {\n              // %F to %J map to ; < = > ?\n              decodedChar = String.fromCharCode(next.charCodeAt(0) - 11);\n            } else if (next >= 'K' && next <= 'O') {\n              // %K to %O map to [ \\ ] ^ _\n              decodedChar = String.fromCharCode(next.charCodeAt(0) + 16);\n            } else if (next >= 'P' && next <= 'T') {\n              // %P to %T map to { | } ~ DEL\n              decodedChar = String.fromCharCode(next.charCodeAt(0) + 43);\n            } else if (next === 'U') {\n              // %U map to NUL\n              decodedChar = '\\0';\n            } else if (next === 'V') {\n              // %V map to @\n              decodedChar = '@';\n            } else if (next === 'W') {\n              // %W map to `\n              decodedChar = '`';\n            } else if (next >= 'X' && next <= 'Z') {\n              // %X to %Z all map to DEL (127)\n              decodedChar = String.fromCharCode(127);\n            } else {\n              throw new FormatException();\n            }\n            break;\n          case 'c':\n            // /A to /O map to ! to , and /Z maps to :\n            if (next >= 'A' && next <= 'O') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) - 32);\n            } else if (next === 'Z') {\n              decodedChar = ':';\n            } else {\n              throw new FormatException();\n            }\n            break;\n        }\n        decoded += decodedChar;\n        // bump up i again since we read two characters\n        i++;\n      } else {\n        decoded += c;\n      }\n    }\n    return decoded;\n  };\n  Code93Reader.prototype.checkChecksums = function (result) {\n    var length = result.length;\n    this.checkOneChecksum(result, length - 2, 20);\n    this.checkOneChecksum(result, length - 1, 15);\n  };\n  Code93Reader.prototype.checkOneChecksum = function (result, checkPosition, weightMax) {\n    var weight = 1;\n    var total = 0;\n    for (var i = checkPosition - 1; i >= 0; i--) {\n      total += weight * Code93Reader.ALPHABET_STRING.indexOf(result.charAt(i));\n      if (++weight > weightMax) {\n        weight = 1;\n      }\n    }\n    if (result.charAt(checkPosition) !== Code93Reader.ALPHABET_STRING[total % 47]) {\n      throw new ChecksumException();\n    }\n  };\n  // Note that 'abcd' are dummy characters in place of control characters.\n  Code93Reader.ALPHABET_STRING = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*\";\n  /**\n   * These represent the encodings of characters, as patterns of wide and narrow bars.\n   * The 9 least-significant bits of each int correspond to the pattern of wide and narrow.\n   */\n  Code93Reader.CHARACTER_ENCODINGS = [0x114, 0x148, 0x144, 0x142, 0x128, 0x124, 0x122, 0x150, 0x112, 0x10A, 0x1A8, 0x1A4, 0x1A2, 0x194, 0x192, 0x18A, 0x168, 0x164, 0x162, 0x134, 0x11A, 0x158, 0x14C, 0x146, 0x12C, 0x116, 0x1B4, 0x1B2, 0x1AC, 0x1A6, 0x196, 0x19A, 0x16C, 0x166, 0x136, 0x13A, 0x12E, 0x1D4, 0x1D2, 0x1CA, 0x16E, 0x176, 0x1AE, 0x126, 0x1DA, 0x1D6, 0x132, 0x15E];\n  Code93Reader.ASTERISK_ENCODING = Code93Reader.CHARACTER_ENCODINGS[47];\n  return Code93Reader;\n}(OneDReader);\nexport default Code93Reader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BarcodeFormat", "ChecksumException", "FormatException", "NotFoundException", "OneDReader", "Result", "ResultPoint", "Code93<PERSON><PERSON>er", "_super", "_this", "decodeRowResult", "counters", "Int32Array", "decodeRow", "rowNumber", "row", "hints", "e_1", "_a", "e_2", "_b", "start", "findAsteriskPattern", "nextStart", "getNextSet", "end", "getSize", "theCounters", "fill", "decodedChar", "lastStart", "recordPattern", "pattern", "toPattern", "patternToChar", "theCounters_1", "theCounters_1_1", "counter", "e_1_1", "error", "return", "substring", "lastPatternSize", "theCounters_2", "theCounters_2_1", "e_2_1", "get", "checkChecksums", "resultString", "decodeExtended", "left", "right", "CODE_93", "Date", "getTime", "width", "rowOffset", "patternStart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "counterPosition", "ASTERISK_ENCODING", "copyWithin", "e_3", "sum", "counters_1", "counters_1_1", "e_3_1", "max", "scaled", "Math", "round", "j", "CHARACTER_ENCODINGS", "ALPHABET_STRING", "char<PERSON>t", "encoded", "decoded", "c", "String", "fromCharCode", "charCodeAt", "result", "checkOneChecksum", "checkPosition", "weightMax", "weight", "total", "indexOf"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/Code93Reader.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\n//import com.google.zxing.ResultMetadataType;\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes Code 93 barcodes.</p>\n *\n * <AUTHOR> Owen\n * @see Code39Reader\n */\nvar Code93Reader = /** @class */ (function (_super) {\n    __extends(Code93Reader, _super);\n    //public Code93Reader() {\n    //  decodeRowResult = new StringBuilder(20);\n    //  counters = new int[6];\n    //}\n    function Code93Reader() {\n        var _this = _super.call(this) || this;\n        _this.decodeRowResult = '';\n        _this.counters = new Int32Array(6);\n        return _this;\n    }\n    Code93Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a, e_2, _b;\n        var start = this.findAsteriskPattern(row);\n        // Read off white space\n        var nextStart = row.getNextSet(start[1]);\n        var end = row.getSize();\n        var theCounters = this.counters;\n        theCounters.fill(0);\n        this.decodeRowResult = '';\n        var decodedChar;\n        var lastStart;\n        do {\n            Code93Reader.recordPattern(row, nextStart, theCounters);\n            var pattern = this.toPattern(theCounters);\n            if (pattern < 0) {\n                throw new NotFoundException();\n            }\n            decodedChar = this.patternToChar(pattern);\n            this.decodeRowResult += decodedChar;\n            lastStart = nextStart;\n            try {\n                for (var theCounters_1 = (e_1 = void 0, __values(theCounters)), theCounters_1_1 = theCounters_1.next(); !theCounters_1_1.done; theCounters_1_1 = theCounters_1.next()) {\n                    var counter = theCounters_1_1.value;\n                    nextStart += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (theCounters_1_1 && !theCounters_1_1.done && (_a = theCounters_1.return)) _a.call(theCounters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            // Read off white space\n            nextStart = row.getNextSet(nextStart);\n        } while (decodedChar !== '*');\n        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 1); // remove asterisk\n        var lastPatternSize = 0;\n        try {\n            for (var theCounters_2 = __values(theCounters), theCounters_2_1 = theCounters_2.next(); !theCounters_2_1.done; theCounters_2_1 = theCounters_2.next()) {\n                var counter = theCounters_2_1.value;\n                lastPatternSize += counter;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (theCounters_2_1 && !theCounters_2_1.done && (_b = theCounters_2.return)) _b.call(theCounters_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        // Should be at least one more black module\n        if (nextStart === end || !row.get(nextStart)) {\n            throw new NotFoundException();\n        }\n        if (this.decodeRowResult.length < 2) {\n            // false positive -- need at least 2 checksum digits\n            throw new NotFoundException();\n        }\n        this.checkChecksums(this.decodeRowResult);\n        // Remove checksum digits\n        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 2);\n        var resultString = this.decodeExtended(this.decodeRowResult);\n        var left = (start[1] + start[0]) / 2.0;\n        var right = lastStart + lastPatternSize / 2.0;\n        return new Result(resultString, null, 0, [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)], BarcodeFormat.CODE_93, new Date().getTime());\n    };\n    Code93Reader.prototype.findAsteriskPattern = function (row) {\n        var width = row.getSize();\n        var rowOffset = row.getNextSet(0);\n        this.counters.fill(0);\n        var theCounters = this.counters;\n        var patternStart = rowOffset;\n        var isWhite = false;\n        var patternLength = theCounters.length;\n        var counterPosition = 0;\n        for (var i = rowOffset; i < width; i++) {\n            if (row.get(i) !== isWhite) {\n                theCounters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    if (this.toPattern(theCounters) === Code93Reader.ASTERISK_ENCODING) {\n                        return new Int32Array([patternStart, i]);\n                    }\n                    patternStart += theCounters[0] + theCounters[1];\n                    theCounters.copyWithin(0, 2, 2 + counterPosition - 1);\n                    theCounters[counterPosition - 1] = 0;\n                    theCounters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                theCounters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException;\n    };\n    Code93Reader.prototype.toPattern = function (counters) {\n        var e_3, _a;\n        var sum = 0;\n        try {\n            for (var counters_1 = __values(counters), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                var counter = counters_1_1.value;\n                sum += counter;\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        var pattern = 0;\n        var max = counters.length;\n        for (var i = 0; i < max; i++) {\n            var scaled = Math.round(counters[i] * 9.0 / sum);\n            if (scaled < 1 || scaled > 4) {\n                return -1;\n            }\n            if ((i & 0x01) === 0) {\n                for (var j = 0; j < scaled; j++) {\n                    pattern = (pattern << 1) | 0x01;\n                }\n            }\n            else {\n                pattern <<= scaled;\n            }\n        }\n        return pattern;\n    };\n    Code93Reader.prototype.patternToChar = function (pattern) {\n        for (var i = 0; i < Code93Reader.CHARACTER_ENCODINGS.length; i++) {\n            if (Code93Reader.CHARACTER_ENCODINGS[i] === pattern) {\n                return Code93Reader.ALPHABET_STRING.charAt(i);\n            }\n        }\n        throw new NotFoundException();\n    };\n    Code93Reader.prototype.decodeExtended = function (encoded) {\n        var length = encoded.length;\n        var decoded = '';\n        for (var i = 0; i < length; i++) {\n            var c = encoded.charAt(i);\n            if (c >= 'a' && c <= 'd') {\n                if (i >= length - 1) {\n                    throw new FormatException();\n                }\n                var next = encoded.charAt(i + 1);\n                var decodedChar = '\\0';\n                switch (c) {\n                    case 'd':\n                        // +A to +Z map to a to z\n                        if (next >= 'A' && next <= 'Z') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 32);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 'a':\n                        // $A to $Z map to control codes SH to SB\n                        if (next >= 'A' && next <= 'Z') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 64);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 'b':\n                        if (next >= 'A' && next <= 'E') {\n                            // %A to %E map to control codes ESC to USep\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 38);\n                        }\n                        else if (next >= 'F' && next <= 'J') {\n                            // %F to %J map to ; < = > ?\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 11);\n                        }\n                        else if (next >= 'K' && next <= 'O') {\n                            // %K to %O map to [ \\ ] ^ _\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 16);\n                        }\n                        else if (next >= 'P' && next <= 'T') {\n                            // %P to %T map to { | } ~ DEL\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 43);\n                        }\n                        else if (next === 'U') {\n                            // %U map to NUL\n                            decodedChar = '\\0';\n                        }\n                        else if (next === 'V') {\n                            // %V map to @\n                            decodedChar = '@';\n                        }\n                        else if (next === 'W') {\n                            // %W map to `\n                            decodedChar = '`';\n                        }\n                        else if (next >= 'X' && next <= 'Z') {\n                            // %X to %Z all map to DEL (127)\n                            decodedChar = String.fromCharCode(127);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case 'c':\n                        // /A to /O map to ! to , and /Z maps to :\n                        if (next >= 'A' && next <= 'O') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 32);\n                        }\n                        else if (next === 'Z') {\n                            decodedChar = ':';\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                }\n                decoded += decodedChar;\n                // bump up i again since we read two characters\n                i++;\n            }\n            else {\n                decoded += c;\n            }\n        }\n        return decoded;\n    };\n    Code93Reader.prototype.checkChecksums = function (result) {\n        var length = result.length;\n        this.checkOneChecksum(result, length - 2, 20);\n        this.checkOneChecksum(result, length - 1, 15);\n    };\n    Code93Reader.prototype.checkOneChecksum = function (result, checkPosition, weightMax) {\n        var weight = 1;\n        var total = 0;\n        for (var i = checkPosition - 1; i >= 0; i--) {\n            total += weight * Code93Reader.ALPHABET_STRING.indexOf(result.charAt(i));\n            if (++weight > weightMax) {\n                weight = 1;\n            }\n        }\n        if (result.charAt(checkPosition) !== Code93Reader.ALPHABET_STRING[total % 47]) {\n            throw new ChecksumException;\n        }\n    };\n    // Note that 'abcd' are dummy characters in place of control characters.\n    Code93Reader.ALPHABET_STRING = \"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%abcd*\";\n    /**\n     * These represent the encodings of characters, as patterns of wide and narrow bars.\n     * The 9 least-significant bits of each int correspond to the pattern of wide and narrow.\n     */\n    Code93Reader.CHARACTER_ENCODINGS = [\n        0x114, 0x148, 0x144, 0x142, 0x128, 0x124, 0x122, 0x150, 0x112, 0x10A,\n        0x1A8, 0x1A4, 0x1A2, 0x194, 0x192, 0x18A, 0x168, 0x164, 0x162, 0x134,\n        0x11A, 0x158, 0x14C, 0x146, 0x12C, 0x116, 0x1B4, 0x1B2, 0x1AC, 0x1A6,\n        0x196, 0x19A, 0x16C, 0x166, 0x136, 0x13A,\n        0x12E, 0x1D4, 0x1D2, 0x1CA, 0x16E, 0x176, 0x1AE,\n        0x126, 0x1DA, 0x1D6, 0x132, 0x15E,\n    ];\n    Code93Reader.ASTERISK_ENCODING = Code93Reader.CHARACTER_ENCODINGS[47];\n    return Code93Reader;\n}(OneDReader));\nexport default Code93Reader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,WAAW;AAC9B;AACA,OAAOC,WAAW,MAAM,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAe,UAAUC,MAAM,EAAE;EAChDnC,SAAS,CAACkC,YAAY,EAAEC,MAAM,CAAC;EAC/B;EACA;EACA;EACA;EACA,SAASD,YAAYA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,CAACd,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCe,KAAK,CAACC,eAAe,GAAG,EAAE;IAC1BD,KAAK,CAACE,QAAQ,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;IAClC,OAAOH,KAAK;EAChB;EACAF,YAAY,CAACtB,SAAS,CAAC4B,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAE;IAChE,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB,IAAIC,KAAK,GAAG,IAAI,CAACC,mBAAmB,CAACP,GAAG,CAAC;IACzC;IACA,IAAIQ,SAAS,GAAGR,GAAG,CAACS,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IACxC,IAAII,GAAG,GAAGV,GAAG,CAACW,OAAO,CAAC,CAAC;IACvB,IAAIC,WAAW,GAAG,IAAI,CAAChB,QAAQ;IAC/BgB,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC;IACnB,IAAI,CAAClB,eAAe,GAAG,EAAE;IACzB,IAAImB,WAAW;IACf,IAAIC,SAAS;IACb,GAAG;MACCvB,YAAY,CAACwB,aAAa,CAAChB,GAAG,EAAEQ,SAAS,EAAEI,WAAW,CAAC;MACvD,IAAIK,OAAO,GAAG,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC;MACzC,IAAIK,OAAO,GAAG,CAAC,EAAE;QACb,MAAM,IAAI7B,iBAAiB,CAAC,CAAC;MACjC;MACA0B,WAAW,GAAG,IAAI,CAACK,aAAa,CAACF,OAAO,CAAC;MACzC,IAAI,CAACtB,eAAe,IAAImB,WAAW;MACnCC,SAAS,GAAGP,SAAS;MACrB,IAAI;QACA,KAAK,IAAIY,aAAa,IAAIlB,GAAG,GAAG,KAAK,CAAC,EAAE9B,QAAQ,CAACwC,WAAW,CAAC,CAAC,EAAES,eAAe,GAAGD,aAAa,CAACvC,IAAI,CAAC,CAAC,EAAE,CAACwC,eAAe,CAACtC,IAAI,EAAEsC,eAAe,GAAGD,aAAa,CAACvC,IAAI,CAAC,CAAC,EAAE;UACnK,IAAIyC,OAAO,GAAGD,eAAe,CAACvC,KAAK;UACnC0B,SAAS,IAAIc,OAAO;QACxB;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAErB,GAAG,GAAG;UAAEsB,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,eAAe,IAAI,CAACA,eAAe,CAACtC,IAAI,KAAKoB,EAAE,GAAGiB,aAAa,CAACK,MAAM,CAAC,EAAEtB,EAAE,CAACxB,IAAI,CAACyC,aAAa,CAAC;QACvG,CAAC,SACO;UAAE,IAAIlB,GAAG,EAAE,MAAMA,GAAG,CAACsB,KAAK;QAAE;MACxC;MACA;MACAhB,SAAS,GAAGR,GAAG,CAACS,UAAU,CAACD,SAAS,CAAC;IACzC,CAAC,QAAQM,WAAW,KAAK,GAAG;IAC5B,IAAI,CAACnB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC+B,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC/B,eAAe,CAACf,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3F,IAAI+C,eAAe,GAAG,CAAC;IACvB,IAAI;MACA,KAAK,IAAIC,aAAa,GAAGxD,QAAQ,CAACwC,WAAW,CAAC,EAAEiB,eAAe,GAAGD,aAAa,CAAC/C,IAAI,CAAC,CAAC,EAAE,CAACgD,eAAe,CAAC9C,IAAI,EAAE8C,eAAe,GAAGD,aAAa,CAAC/C,IAAI,CAAC,CAAC,EAAE;QACnJ,IAAIyC,OAAO,GAAGO,eAAe,CAAC/C,KAAK;QACnC6C,eAAe,IAAIL,OAAO;MAC9B;IACJ,CAAC,CACD,OAAOQ,KAAK,EAAE;MAAE1B,GAAG,GAAG;QAAEoB,KAAK,EAAEM;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAID,eAAe,IAAI,CAACA,eAAe,CAAC9C,IAAI,KAAKsB,EAAE,GAAGuB,aAAa,CAACH,MAAM,CAAC,EAAEpB,EAAE,CAAC1B,IAAI,CAACiD,aAAa,CAAC;MACvG,CAAC,SACO;QAAE,IAAIxB,GAAG,EAAE,MAAMA,GAAG,CAACoB,KAAK;MAAE;IACxC;IACA;IACA,IAAIhB,SAAS,KAAKE,GAAG,IAAI,CAACV,GAAG,CAAC+B,GAAG,CAACvB,SAAS,CAAC,EAAE;MAC1C,MAAM,IAAIpB,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI,IAAI,CAACO,eAAe,CAACf,MAAM,GAAG,CAAC,EAAE;MACjC;MACA,MAAM,IAAIQ,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI,CAAC4C,cAAc,CAAC,IAAI,CAACrC,eAAe,CAAC;IACzC;IACA,IAAI,CAACA,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC+B,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC/B,eAAe,CAACf,MAAM,GAAG,CAAC,CAAC;IACzF,IAAIqD,YAAY,GAAG,IAAI,CAACC,cAAc,CAAC,IAAI,CAACvC,eAAe,CAAC;IAC5D,IAAIwC,IAAI,GAAG,CAAC7B,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;IACtC,IAAI8B,KAAK,GAAGrB,SAAS,GAAGY,eAAe,GAAG,GAAG;IAC7C,OAAO,IAAIrC,MAAM,CAAC2C,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI1C,WAAW,CAAC4C,IAAI,EAAEpC,SAAS,CAAC,EAAE,IAAIR,WAAW,CAAC6C,KAAK,EAAErC,SAAS,CAAC,CAAC,EAAEd,aAAa,CAACoD,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAChK,CAAC;EACD/C,YAAY,CAACtB,SAAS,CAACqC,mBAAmB,GAAG,UAAUP,GAAG,EAAE;IACxD,IAAIwC,KAAK,GAAGxC,GAAG,CAACW,OAAO,CAAC,CAAC;IACzB,IAAI8B,SAAS,GAAGzC,GAAG,CAACS,UAAU,CAAC,CAAC,CAAC;IACjC,IAAI,CAACb,QAAQ,CAACiB,IAAI,CAAC,CAAC,CAAC;IACrB,IAAID,WAAW,GAAG,IAAI,CAAChB,QAAQ;IAC/B,IAAI8C,YAAY,GAAGD,SAAS;IAC5B,IAAIE,OAAO,GAAG,KAAK;IACnB,IAAIC,aAAa,GAAGhC,WAAW,CAAChC,MAAM;IACtC,IAAIiE,eAAe,GAAG,CAAC;IACvB,KAAK,IAAInE,CAAC,GAAG+D,SAAS,EAAE/D,CAAC,GAAG8D,KAAK,EAAE9D,CAAC,EAAE,EAAE;MACpC,IAAIsB,GAAG,CAAC+B,GAAG,CAACrD,CAAC,CAAC,KAAKiE,OAAO,EAAE;QACxB/B,WAAW,CAACiC,eAAe,CAAC,EAAE;MAClC,CAAC,MACI;QACD,IAAIA,eAAe,KAAKD,aAAa,GAAG,CAAC,EAAE;UACvC,IAAI,IAAI,CAAC1B,SAAS,CAACN,WAAW,CAAC,KAAKpB,YAAY,CAACsD,iBAAiB,EAAE;YAChE,OAAO,IAAIjD,UAAU,CAAC,CAAC6C,YAAY,EAAEhE,CAAC,CAAC,CAAC;UAC5C;UACAgE,YAAY,IAAI9B,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC;UAC/CA,WAAW,CAACmC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGF,eAAe,GAAG,CAAC,CAAC;UACrDjC,WAAW,CAACiC,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;UACpCjC,WAAW,CAACiC,eAAe,CAAC,GAAG,CAAC;UAChCA,eAAe,EAAE;QACrB,CAAC,MACI;UACDA,eAAe,EAAE;QACrB;QACAjC,WAAW,CAACiC,eAAe,CAAC,GAAG,CAAC;QAChCF,OAAO,GAAG,CAACA,OAAO;MACtB;IACJ;IACA,MAAM,IAAIvD,iBAAiB,CAAD,CAAC;EAC/B,CAAC;EACDI,YAAY,CAACtB,SAAS,CAACgD,SAAS,GAAG,UAAUtB,QAAQ,EAAE;IACnD,IAAIoD,GAAG,EAAE7C,EAAE;IACX,IAAI8C,GAAG,GAAG,CAAC;IACX,IAAI;MACA,KAAK,IAAIC,UAAU,GAAG9E,QAAQ,CAACwB,QAAQ,CAAC,EAAEuD,YAAY,GAAGD,UAAU,CAACrE,IAAI,CAAC,CAAC,EAAE,CAACsE,YAAY,CAACpE,IAAI,EAAEoE,YAAY,GAAGD,UAAU,CAACrE,IAAI,CAAC,CAAC,EAAE;QAC9H,IAAIyC,OAAO,GAAG6B,YAAY,CAACrE,KAAK;QAChCmE,GAAG,IAAI3B,OAAO;MAClB;IACJ,CAAC,CACD,OAAO8B,KAAK,EAAE;MAAEJ,GAAG,GAAG;QAAExB,KAAK,EAAE4B;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAID,YAAY,IAAI,CAACA,YAAY,CAACpE,IAAI,KAAKoB,EAAE,GAAG+C,UAAU,CAACzB,MAAM,CAAC,EAAEtB,EAAE,CAACxB,IAAI,CAACuE,UAAU,CAAC;MAC3F,CAAC,SACO;QAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACxB,KAAK;MAAE;IACxC;IACA,IAAIP,OAAO,GAAG,CAAC;IACf,IAAIoC,GAAG,GAAGzD,QAAQ,CAAChB,MAAM;IACzB,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2E,GAAG,EAAE3E,CAAC,EAAE,EAAE;MAC1B,IAAI4E,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC5D,QAAQ,CAAClB,CAAC,CAAC,GAAG,GAAG,GAAGuE,GAAG,CAAC;MAChD,IAAIK,MAAM,GAAG,CAAC,IAAIA,MAAM,GAAG,CAAC,EAAE;QAC1B,OAAO,CAAC,CAAC;MACb;MACA,IAAI,CAAC5E,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE;QAClB,KAAK,IAAI+E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;UAC7BxC,OAAO,GAAIA,OAAO,IAAI,CAAC,GAAI,IAAI;QACnC;MACJ,CAAC,MACI;QACDA,OAAO,KAAKqC,MAAM;MACtB;IACJ;IACA,OAAOrC,OAAO;EAClB,CAAC;EACDzB,YAAY,CAACtB,SAAS,CAACiD,aAAa,GAAG,UAAUF,OAAO,EAAE;IACtD,KAAK,IAAIvC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,YAAY,CAACkE,mBAAmB,CAAC9E,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC9D,IAAIc,YAAY,CAACkE,mBAAmB,CAAChF,CAAC,CAAC,KAAKuC,OAAO,EAAE;QACjD,OAAOzB,YAAY,CAACmE,eAAe,CAACC,MAAM,CAAClF,CAAC,CAAC;MACjD;IACJ;IACA,MAAM,IAAIU,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACDI,YAAY,CAACtB,SAAS,CAACgE,cAAc,GAAG,UAAU2B,OAAO,EAAE;IACvD,IAAIjF,MAAM,GAAGiF,OAAO,CAACjF,MAAM;IAC3B,IAAIkF,OAAO,GAAG,EAAE;IAChB,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC7B,IAAIqF,CAAC,GAAGF,OAAO,CAACD,MAAM,CAAClF,CAAC,CAAC;MACzB,IAAIqF,CAAC,IAAI,GAAG,IAAIA,CAAC,IAAI,GAAG,EAAE;QACtB,IAAIrF,CAAC,IAAIE,MAAM,GAAG,CAAC,EAAE;UACjB,MAAM,IAAIO,eAAe,CAAC,CAAC;QAC/B;QACA,IAAIN,IAAI,GAAGgF,OAAO,CAACD,MAAM,CAAClF,CAAC,GAAG,CAAC,CAAC;QAChC,IAAIoC,WAAW,GAAG,IAAI;QACtB,QAAQiD,CAAC;UACL,KAAK,GAAG;YACJ;YACA,IAAIlF,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cAC5BiC,WAAW,GAAGkD,MAAM,CAACC,YAAY,CAACpF,IAAI,CAACqF,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI;cACD,MAAM,IAAI/E,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAK,GAAG;YACJ;YACA,IAAIN,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cAC5BiC,WAAW,GAAGkD,MAAM,CAACC,YAAY,CAACpF,IAAI,CAACqF,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI;cACD,MAAM,IAAI/E,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAK,GAAG;YACJ,IAAIN,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cAC5B;cACAiC,WAAW,GAAGkD,MAAM,CAACC,YAAY,CAACpF,IAAI,CAACqF,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAIrF,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cACjC;cACAiC,WAAW,GAAGkD,MAAM,CAACC,YAAY,CAACpF,IAAI,CAACqF,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAIrF,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cACjC;cACAiC,WAAW,GAAGkD,MAAM,CAACC,YAAY,CAACpF,IAAI,CAACqF,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAIrF,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cACjC;cACAiC,WAAW,GAAGkD,MAAM,CAACC,YAAY,CAACpF,IAAI,CAACqF,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAIrF,IAAI,KAAK,GAAG,EAAE;cACnB;cACAiC,WAAW,GAAG,IAAI;YACtB,CAAC,MACI,IAAIjC,IAAI,KAAK,GAAG,EAAE;cACnB;cACAiC,WAAW,GAAG,GAAG;YACrB,CAAC,MACI,IAAIjC,IAAI,KAAK,GAAG,EAAE;cACnB;cACAiC,WAAW,GAAG,GAAG;YACrB,CAAC,MACI,IAAIjC,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cACjC;cACAiC,WAAW,GAAGkD,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC;YAC1C,CAAC,MACI;cACD,MAAM,IAAI9E,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAK,GAAG;YACJ;YACA,IAAIN,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cAC5BiC,WAAW,GAAGkD,MAAM,CAACC,YAAY,CAACpF,IAAI,CAACqF,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAIrF,IAAI,KAAK,GAAG,EAAE;cACnBiC,WAAW,GAAG,GAAG;YACrB,CAAC,MACI;cACD,MAAM,IAAI3B,eAAe,CAAC,CAAC;YAC/B;YACA;QACR;QACA2E,OAAO,IAAIhD,WAAW;QACtB;QACApC,CAAC,EAAE;MACP,CAAC,MACI;QACDoF,OAAO,IAAIC,CAAC;MAChB;IACJ;IACA,OAAOD,OAAO;EAClB,CAAC;EACDtE,YAAY,CAACtB,SAAS,CAAC8D,cAAc,GAAG,UAAUmC,MAAM,EAAE;IACtD,IAAIvF,MAAM,GAAGuF,MAAM,CAACvF,MAAM;IAC1B,IAAI,CAACwF,gBAAgB,CAACD,MAAM,EAAEvF,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC;IAC7C,IAAI,CAACwF,gBAAgB,CAACD,MAAM,EAAEvF,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC;EACjD,CAAC;EACDY,YAAY,CAACtB,SAAS,CAACkG,gBAAgB,GAAG,UAAUD,MAAM,EAAEE,aAAa,EAAEC,SAAS,EAAE;IAClF,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAI9F,CAAC,GAAG2F,aAAa,GAAG,CAAC,EAAE3F,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC8F,KAAK,IAAID,MAAM,GAAG/E,YAAY,CAACmE,eAAe,CAACc,OAAO,CAACN,MAAM,CAACP,MAAM,CAAClF,CAAC,CAAC,CAAC;MACxE,IAAI,EAAE6F,MAAM,GAAGD,SAAS,EAAE;QACtBC,MAAM,GAAG,CAAC;MACd;IACJ;IACA,IAAIJ,MAAM,CAACP,MAAM,CAACS,aAAa,CAAC,KAAK7E,YAAY,CAACmE,eAAe,CAACa,KAAK,GAAG,EAAE,CAAC,EAAE;MAC3E,MAAM,IAAItF,iBAAiB,CAAD,CAAC;IAC/B;EACJ,CAAC;EACD;EACAM,YAAY,CAACmE,eAAe,GAAG,kDAAkD;EACjF;AACJ;AACA;AACA;EACInE,YAAY,CAACkE,mBAAmB,GAAG,CAC/B,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACxC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAC/C,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CACpC;EACDlE,YAAY,CAACsD,iBAAiB,GAAGtD,YAAY,CAACkE,mBAAmB,CAAC,EAAE,CAAC;EACrE,OAAOlE,YAAY;AACvB,CAAC,CAACH,UAAU,CAAE;AACd,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}