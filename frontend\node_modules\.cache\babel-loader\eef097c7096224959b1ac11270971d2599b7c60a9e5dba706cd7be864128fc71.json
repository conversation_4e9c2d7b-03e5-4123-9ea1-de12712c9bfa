{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport BitMatrix from '../../common/BitMatrix';\nimport FormatInformation from './FormatInformation';\nimport ECBlocks from './ECBlocks';\nimport ECB from './ECB';\nimport FormatException from '../../FormatException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * See ISO 18004:2006 Annex D\n *\n * <AUTHOR> Owen\n */\nvar Version = /** @class */function () {\n  function Version(versionNumber /*int*/, alignmentPatternCenters) {\n    var e_1, _a;\n    var ecBlocks = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n      ecBlocks[_i - 2] = arguments[_i];\n    }\n    this.versionNumber = versionNumber;\n    this.alignmentPatternCenters = alignmentPatternCenters;\n    this.ecBlocks = ecBlocks;\n    var total = 0;\n    var ecCodewords = ecBlocks[0].getECCodewordsPerBlock();\n    var ecbArray = ecBlocks[0].getECBlocks();\n    try {\n      for (var ecbArray_1 = __values(ecbArray), ecbArray_1_1 = ecbArray_1.next(); !ecbArray_1_1.done; ecbArray_1_1 = ecbArray_1.next()) {\n        var ecBlock = ecbArray_1_1.value;\n        total += ecBlock.getCount() * (ecBlock.getDataCodewords() + ecCodewords);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (ecbArray_1_1 && !ecbArray_1_1.done && (_a = ecbArray_1.return)) _a.call(ecbArray_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    this.totalCodewords = total;\n  }\n  Version.prototype.getVersionNumber = function () {\n    return this.versionNumber;\n  };\n  Version.prototype.getAlignmentPatternCenters = function () {\n    return this.alignmentPatternCenters;\n  };\n  Version.prototype.getTotalCodewords = function () {\n    return this.totalCodewords;\n  };\n  Version.prototype.getDimensionForVersion = function () {\n    return 17 + 4 * this.versionNumber;\n  };\n  Version.prototype.getECBlocksForLevel = function (ecLevel) {\n    return this.ecBlocks[ecLevel.getValue()];\n    // TYPESCRIPTPORT: original was using ordinal, and using the order of levels as defined in ErrorCorrectionLevel enum (LMQH)\n    // I will use the direct value from ErrorCorrectionLevelValues enum which in typescript goes to a number\n  };\n  /**\n   * <p>Deduces version information purely from QR Code dimensions.</p>\n   *\n   * @param dimension dimension in modules\n   * @return Version for a QR Code of that dimension\n   * @throws FormatException if dimension is not 1 mod 4\n   */\n  Version.getProvisionalVersionForDimension = function (dimension /*int*/) {\n    if (dimension % 4 !== 1) {\n      throw new FormatException();\n    }\n    try {\n      return this.getVersionForNumber((dimension - 17) / 4);\n    } catch (ignored /*: IllegalArgumentException*/) {\n      throw new FormatException();\n    }\n  };\n  Version.getVersionForNumber = function (versionNumber /*int*/) {\n    if (versionNumber < 1 || versionNumber > 40) {\n      throw new IllegalArgumentException();\n    }\n    return Version.VERSIONS[versionNumber - 1];\n  };\n  Version.decodeVersionInformation = function (versionBits /*int*/) {\n    var bestDifference = Number.MAX_SAFE_INTEGER;\n    var bestVersion = 0;\n    for (var i = 0; i < Version.VERSION_DECODE_INFO.length; i++) {\n      var targetVersion = Version.VERSION_DECODE_INFO[i];\n      // Do the version info bits match exactly? done.\n      if (targetVersion === versionBits) {\n        return Version.getVersionForNumber(i + 7);\n      }\n      // Otherwise see if this is the closest to a real version info bit string\n      // we have seen so far\n      var bitsDifference = FormatInformation.numBitsDiffering(versionBits, targetVersion);\n      if (bitsDifference < bestDifference) {\n        bestVersion = i + 7;\n        bestDifference = bitsDifference;\n      }\n    }\n    // We can tolerate up to 3 bits of error since no two version info codewords will\n    // differ in less than 8 bits.\n    if (bestDifference <= 3) {\n      return Version.getVersionForNumber(bestVersion);\n    }\n    // If we didn't find a close enough match, fail\n    return null;\n  };\n  /**\n   * See ISO 18004:2006 Annex E\n   */\n  Version.prototype.buildFunctionPattern = function () {\n    var dimension = this.getDimensionForVersion();\n    var bitMatrix = new BitMatrix(dimension);\n    // Top left finder pattern + separator + format\n    bitMatrix.setRegion(0, 0, 9, 9);\n    // Top right finder pattern + separator + format\n    bitMatrix.setRegion(dimension - 8, 0, 8, 9);\n    // Bottom left finder pattern + separator + format\n    bitMatrix.setRegion(0, dimension - 8, 9, 8);\n    // Alignment patterns\n    var max = this.alignmentPatternCenters.length;\n    for (var x = 0; x < max; x++) {\n      var i = this.alignmentPatternCenters[x] - 2;\n      for (var y = 0; y < max; y++) {\n        if (x === 0 && (y === 0 || y === max - 1) || x === max - 1 && y === 0) {\n          // No alignment patterns near the three finder patterns\n          continue;\n        }\n        bitMatrix.setRegion(this.alignmentPatternCenters[y] - 2, i, 5, 5);\n      }\n    }\n    // Vertical timing pattern\n    bitMatrix.setRegion(6, 9, 1, dimension - 17);\n    // Horizontal timing pattern\n    bitMatrix.setRegion(9, 6, dimension - 17, 1);\n    if (this.versionNumber > 6) {\n      // Version info, top right\n      bitMatrix.setRegion(dimension - 11, 0, 3, 6);\n      // Version info, bottom left\n      bitMatrix.setRegion(0, dimension - 11, 6, 3);\n    }\n    return bitMatrix;\n  };\n  /*@Override*/\n  Version.prototype.toString = function () {\n    return '' + this.versionNumber;\n  };\n  /**\n     * See ISO 18004:2006 Annex D.\n     * Element i represents the raw version bits that specify version i + 7\n     */\n  Version.VERSION_DECODE_INFO = Int32Array.from([0x07C94, 0x085BC, 0x09A99, 0x0A4D3, 0x0BBF6, 0x0C762, 0x0D847, 0x0E60D, 0x0F928, 0x10B78, 0x1145D, 0x12A17, 0x13532, 0x149A6, 0x15683, 0x168C9, 0x177EC, 0x18EC4, 0x191E1, 0x1AFAB, 0x1B08E, 0x1CC1A, 0x1D33F, 0x1ED75, 0x1F250, 0x209D5, 0x216F0, 0x228BA, 0x2379F, 0x24B0B, 0x2542E, 0x26A64, 0x27541, 0x28C69]);\n  /**\n     * See ISO 18004:2006 6.5.1 Table 9\n     */\n  Version.VERSIONS = [new Version(1, new Int32Array(0), new ECBlocks(7, new ECB(1, 19)), new ECBlocks(10, new ECB(1, 16)), new ECBlocks(13, new ECB(1, 13)), new ECBlocks(17, new ECB(1, 9))), new Version(2, Int32Array.from([6, 18]), new ECBlocks(10, new ECB(1, 34)), new ECBlocks(16, new ECB(1, 28)), new ECBlocks(22, new ECB(1, 22)), new ECBlocks(28, new ECB(1, 16))), new Version(3, Int32Array.from([6, 22]), new ECBlocks(15, new ECB(1, 55)), new ECBlocks(26, new ECB(1, 44)), new ECBlocks(18, new ECB(2, 17)), new ECBlocks(22, new ECB(2, 13))), new Version(4, Int32Array.from([6, 26]), new ECBlocks(20, new ECB(1, 80)), new ECBlocks(18, new ECB(2, 32)), new ECBlocks(26, new ECB(2, 24)), new ECBlocks(16, new ECB(4, 9))), new Version(5, Int32Array.from([6, 30]), new ECBlocks(26, new ECB(1, 108)), new ECBlocks(24, new ECB(2, 43)), new ECBlocks(18, new ECB(2, 15), new ECB(2, 16)), new ECBlocks(22, new ECB(2, 11), new ECB(2, 12))), new Version(6, Int32Array.from([6, 34]), new ECBlocks(18, new ECB(2, 68)), new ECBlocks(16, new ECB(4, 27)), new ECBlocks(24, new ECB(4, 19)), new ECBlocks(28, new ECB(4, 15))), new Version(7, Int32Array.from([6, 22, 38]), new ECBlocks(20, new ECB(2, 78)), new ECBlocks(18, new ECB(4, 31)), new ECBlocks(18, new ECB(2, 14), new ECB(4, 15)), new ECBlocks(26, new ECB(4, 13), new ECB(1, 14))), new Version(8, Int32Array.from([6, 24, 42]), new ECBlocks(24, new ECB(2, 97)), new ECBlocks(22, new ECB(2, 38), new ECB(2, 39)), new ECBlocks(22, new ECB(4, 18), new ECB(2, 19)), new ECBlocks(26, new ECB(4, 14), new ECB(2, 15))), new Version(9, Int32Array.from([6, 26, 46]), new ECBlocks(30, new ECB(2, 116)), new ECBlocks(22, new ECB(3, 36), new ECB(2, 37)), new ECBlocks(20, new ECB(4, 16), new ECB(4, 17)), new ECBlocks(24, new ECB(4, 12), new ECB(4, 13))), new Version(10, Int32Array.from([6, 28, 50]), new ECBlocks(18, new ECB(2, 68), new ECB(2, 69)), new ECBlocks(26, new ECB(4, 43), new ECB(1, 44)), new ECBlocks(24, new ECB(6, 19), new ECB(2, 20)), new ECBlocks(28, new ECB(6, 15), new ECB(2, 16))), new Version(11, Int32Array.from([6, 30, 54]), new ECBlocks(20, new ECB(4, 81)), new ECBlocks(30, new ECB(1, 50), new ECB(4, 51)), new ECBlocks(28, new ECB(4, 22), new ECB(4, 23)), new ECBlocks(24, new ECB(3, 12), new ECB(8, 13))), new Version(12, Int32Array.from([6, 32, 58]), new ECBlocks(24, new ECB(2, 92), new ECB(2, 93)), new ECBlocks(22, new ECB(6, 36), new ECB(2, 37)), new ECBlocks(26, new ECB(4, 20), new ECB(6, 21)), new ECBlocks(28, new ECB(7, 14), new ECB(4, 15))), new Version(13, Int32Array.from([6, 34, 62]), new ECBlocks(26, new ECB(4, 107)), new ECBlocks(22, new ECB(8, 37), new ECB(1, 38)), new ECBlocks(24, new ECB(8, 20), new ECB(4, 21)), new ECBlocks(22, new ECB(12, 11), new ECB(4, 12))), new Version(14, Int32Array.from([6, 26, 46, 66]), new ECBlocks(30, new ECB(3, 115), new ECB(1, 116)), new ECBlocks(24, new ECB(4, 40), new ECB(5, 41)), new ECBlocks(20, new ECB(11, 16), new ECB(5, 17)), new ECBlocks(24, new ECB(11, 12), new ECB(5, 13))), new Version(15, Int32Array.from([6, 26, 48, 70]), new ECBlocks(22, new ECB(5, 87), new ECB(1, 88)), new ECBlocks(24, new ECB(5, 41), new ECB(5, 42)), new ECBlocks(30, new ECB(5, 24), new ECB(7, 25)), new ECBlocks(24, new ECB(11, 12), new ECB(7, 13))), new Version(16, Int32Array.from([6, 26, 50, 74]), new ECBlocks(24, new ECB(5, 98), new ECB(1, 99)), new ECBlocks(28, new ECB(7, 45), new ECB(3, 46)), new ECBlocks(24, new ECB(15, 19), new ECB(2, 20)), new ECBlocks(30, new ECB(3, 15), new ECB(13, 16))), new Version(17, Int32Array.from([6, 30, 54, 78]), new ECBlocks(28, new ECB(1, 107), new ECB(5, 108)), new ECBlocks(28, new ECB(10, 46), new ECB(1, 47)), new ECBlocks(28, new ECB(1, 22), new ECB(15, 23)), new ECBlocks(28, new ECB(2, 14), new ECB(17, 15))), new Version(18, Int32Array.from([6, 30, 56, 82]), new ECBlocks(30, new ECB(5, 120), new ECB(1, 121)), new ECBlocks(26, new ECB(9, 43), new ECB(4, 44)), new ECBlocks(28, new ECB(17, 22), new ECB(1, 23)), new ECBlocks(28, new ECB(2, 14), new ECB(19, 15))), new Version(19, Int32Array.from([6, 30, 58, 86]), new ECBlocks(28, new ECB(3, 113), new ECB(4, 114)), new ECBlocks(26, new ECB(3, 44), new ECB(11, 45)), new ECBlocks(26, new ECB(17, 21), new ECB(4, 22)), new ECBlocks(26, new ECB(9, 13), new ECB(16, 14))), new Version(20, Int32Array.from([6, 34, 62, 90]), new ECBlocks(28, new ECB(3, 107), new ECB(5, 108)), new ECBlocks(26, new ECB(3, 41), new ECB(13, 42)), new ECBlocks(30, new ECB(15, 24), new ECB(5, 25)), new ECBlocks(28, new ECB(15, 15), new ECB(10, 16))), new Version(21, Int32Array.from([6, 28, 50, 72, 94]), new ECBlocks(28, new ECB(4, 116), new ECB(4, 117)), new ECBlocks(26, new ECB(17, 42)), new ECBlocks(28, new ECB(17, 22), new ECB(6, 23)), new ECBlocks(30, new ECB(19, 16), new ECB(6, 17))), new Version(22, Int32Array.from([6, 26, 50, 74, 98]), new ECBlocks(28, new ECB(2, 111), new ECB(7, 112)), new ECBlocks(28, new ECB(17, 46)), new ECBlocks(30, new ECB(7, 24), new ECB(16, 25)), new ECBlocks(24, new ECB(34, 13))), new Version(23, Int32Array.from([6, 30, 54, 78, 102]), new ECBlocks(30, new ECB(4, 121), new ECB(5, 122)), new ECBlocks(28, new ECB(4, 47), new ECB(14, 48)), new ECBlocks(30, new ECB(11, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(16, 15), new ECB(14, 16))), new Version(24, Int32Array.from([6, 28, 54, 80, 106]), new ECBlocks(30, new ECB(6, 117), new ECB(4, 118)), new ECBlocks(28, new ECB(6, 45), new ECB(14, 46)), new ECBlocks(30, new ECB(11, 24), new ECB(16, 25)), new ECBlocks(30, new ECB(30, 16), new ECB(2, 17))), new Version(25, Int32Array.from([6, 32, 58, 84, 110]), new ECBlocks(26, new ECB(8, 106), new ECB(4, 107)), new ECBlocks(28, new ECB(8, 47), new ECB(13, 48)), new ECBlocks(30, new ECB(7, 24), new ECB(22, 25)), new ECBlocks(30, new ECB(22, 15), new ECB(13, 16))), new Version(26, Int32Array.from([6, 30, 58, 86, 114]), new ECBlocks(28, new ECB(10, 114), new ECB(2, 115)), new ECBlocks(28, new ECB(19, 46), new ECB(4, 47)), new ECBlocks(28, new ECB(28, 22), new ECB(6, 23)), new ECBlocks(30, new ECB(33, 16), new ECB(4, 17))), new Version(27, Int32Array.from([6, 34, 62, 90, 118]), new ECBlocks(30, new ECB(8, 122), new ECB(4, 123)), new ECBlocks(28, new ECB(22, 45), new ECB(3, 46)), new ECBlocks(30, new ECB(8, 23), new ECB(26, 24)), new ECBlocks(30, new ECB(12, 15), new ECB(28, 16))), new Version(28, Int32Array.from([6, 26, 50, 74, 98, 122]), new ECBlocks(30, new ECB(3, 117), new ECB(10, 118)), new ECBlocks(28, new ECB(3, 45), new ECB(23, 46)), new ECBlocks(30, new ECB(4, 24), new ECB(31, 25)), new ECBlocks(30, new ECB(11, 15), new ECB(31, 16))), new Version(29, Int32Array.from([6, 30, 54, 78, 102, 126]), new ECBlocks(30, new ECB(7, 116), new ECB(7, 117)), new ECBlocks(28, new ECB(21, 45), new ECB(7, 46)), new ECBlocks(30, new ECB(1, 23), new ECB(37, 24)), new ECBlocks(30, new ECB(19, 15), new ECB(26, 16))), new Version(30, Int32Array.from([6, 26, 52, 78, 104, 130]), new ECBlocks(30, new ECB(5, 115), new ECB(10, 116)), new ECBlocks(28, new ECB(19, 47), new ECB(10, 48)), new ECBlocks(30, new ECB(15, 24), new ECB(25, 25)), new ECBlocks(30, new ECB(23, 15), new ECB(25, 16))), new Version(31, Int32Array.from([6, 30, 56, 82, 108, 134]), new ECBlocks(30, new ECB(13, 115), new ECB(3, 116)), new ECBlocks(28, new ECB(2, 46), new ECB(29, 47)), new ECBlocks(30, new ECB(42, 24), new ECB(1, 25)), new ECBlocks(30, new ECB(23, 15), new ECB(28, 16))), new Version(32, Int32Array.from([6, 34, 60, 86, 112, 138]), new ECBlocks(30, new ECB(17, 115)), new ECBlocks(28, new ECB(10, 46), new ECB(23, 47)), new ECBlocks(30, new ECB(10, 24), new ECB(35, 25)), new ECBlocks(30, new ECB(19, 15), new ECB(35, 16))), new Version(33, Int32Array.from([6, 30, 58, 86, 114, 142]), new ECBlocks(30, new ECB(17, 115), new ECB(1, 116)), new ECBlocks(28, new ECB(14, 46), new ECB(21, 47)), new ECBlocks(30, new ECB(29, 24), new ECB(19, 25)), new ECBlocks(30, new ECB(11, 15), new ECB(46, 16))), new Version(34, Int32Array.from([6, 34, 62, 90, 118, 146]), new ECBlocks(30, new ECB(13, 115), new ECB(6, 116)), new ECBlocks(28, new ECB(14, 46), new ECB(23, 47)), new ECBlocks(30, new ECB(44, 24), new ECB(7, 25)), new ECBlocks(30, new ECB(59, 16), new ECB(1, 17))), new Version(35, Int32Array.from([6, 30, 54, 78, 102, 126, 150]), new ECBlocks(30, new ECB(12, 121), new ECB(7, 122)), new ECBlocks(28, new ECB(12, 47), new ECB(26, 48)), new ECBlocks(30, new ECB(39, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(22, 15), new ECB(41, 16))), new Version(36, Int32Array.from([6, 24, 50, 76, 102, 128, 154]), new ECBlocks(30, new ECB(6, 121), new ECB(14, 122)), new ECBlocks(28, new ECB(6, 47), new ECB(34, 48)), new ECBlocks(30, new ECB(46, 24), new ECB(10, 25)), new ECBlocks(30, new ECB(2, 15), new ECB(64, 16))), new Version(37, Int32Array.from([6, 28, 54, 80, 106, 132, 158]), new ECBlocks(30, new ECB(17, 122), new ECB(4, 123)), new ECBlocks(28, new ECB(29, 46), new ECB(14, 47)), new ECBlocks(30, new ECB(49, 24), new ECB(10, 25)), new ECBlocks(30, new ECB(24, 15), new ECB(46, 16))), new Version(38, Int32Array.from([6, 32, 58, 84, 110, 136, 162]), new ECBlocks(30, new ECB(4, 122), new ECB(18, 123)), new ECBlocks(28, new ECB(13, 46), new ECB(32, 47)), new ECBlocks(30, new ECB(48, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(42, 15), new ECB(32, 16))), new Version(39, Int32Array.from([6, 26, 54, 82, 110, 138, 166]), new ECBlocks(30, new ECB(20, 117), new ECB(4, 118)), new ECBlocks(28, new ECB(40, 47), new ECB(7, 48)), new ECBlocks(30, new ECB(43, 24), new ECB(22, 25)), new ECBlocks(30, new ECB(10, 15), new ECB(67, 16))), new Version(40, Int32Array.from([6, 30, 58, 86, 114, 142, 170]), new ECBlocks(30, new ECB(19, 118), new ECB(6, 119)), new ECBlocks(28, new ECB(18, 47), new ECB(31, 48)), new ECBlocks(30, new ECB(34, 24), new ECB(34, 25)), new ECBlocks(30, new ECB(20, 15), new ECB(61, 16)))];\n  return Version;\n}();\nexport default Version;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BitMatrix", "FormatInformation", "ECBlocks", "ECB", "FormatException", "IllegalArgumentException", "Version", "versionNumber", "alignmentPatternCenters", "e_1", "_a", "ecBlocks", "_i", "arguments", "total", "ecCodewords", "getECCodewordsPerBlock", "ecbArray", "getECBlocks", "ecbArray_1", "ecbArray_1_1", "ecBlock", "getCount", "getDataCodewords", "e_1_1", "error", "return", "totalCodewords", "prototype", "getVersionNumber", "getAlignmentPatternCenters", "getTotalCodewords", "getDimensionForVersion", "getECBlocksForLevel", "ecLevel", "getValue", "getProvisionalVersionForDimension", "dimension", "getVersionForNumber", "ignored", "VERSIONS", "decodeVersionInformation", "versionBits", "bestDifference", "Number", "MAX_SAFE_INTEGER", "bestVersion", "VERSION_DECODE_INFO", "targetVersion", "bitsDifference", "numBits<PERSON><PERSON>ering", "buildFunctionPattern", "bitMatrix", "setRegion", "max", "x", "y", "toString", "Int32Array", "from"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/Version.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport BitMatrix from '../../common/BitMatrix';\nimport FormatInformation from './FormatInformation';\nimport ECBlocks from './ECBlocks';\nimport ECB from './ECB';\nimport FormatException from '../../FormatException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * See ISO 18004:2006 Annex D\n *\n * <AUTHOR> Owen\n */\nvar Version = /** @class */ (function () {\n    function Version(versionNumber /*int*/, alignmentPatternCenters) {\n        var e_1, _a;\n        var ecBlocks = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            ecBlocks[_i - 2] = arguments[_i];\n        }\n        this.versionNumber = versionNumber;\n        this.alignmentPatternCenters = alignmentPatternCenters;\n        this.ecBlocks = ecBlocks;\n        var total = 0;\n        var ecCodewords = ecBlocks[0].getECCodewordsPerBlock();\n        var ecbArray = ecBlocks[0].getECBlocks();\n        try {\n            for (var ecbArray_1 = __values(ecbArray), ecbArray_1_1 = ecbArray_1.next(); !ecbArray_1_1.done; ecbArray_1_1 = ecbArray_1.next()) {\n                var ecBlock = ecbArray_1_1.value;\n                total += ecBlock.getCount() * (ecBlock.getDataCodewords() + ecCodewords);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecbArray_1_1 && !ecbArray_1_1.done && (_a = ecbArray_1.return)) _a.call(ecbArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        this.totalCodewords = total;\n    }\n    Version.prototype.getVersionNumber = function () {\n        return this.versionNumber;\n    };\n    Version.prototype.getAlignmentPatternCenters = function () {\n        return this.alignmentPatternCenters;\n    };\n    Version.prototype.getTotalCodewords = function () {\n        return this.totalCodewords;\n    };\n    Version.prototype.getDimensionForVersion = function () {\n        return 17 + 4 * this.versionNumber;\n    };\n    Version.prototype.getECBlocksForLevel = function (ecLevel) {\n        return this.ecBlocks[ecLevel.getValue()];\n        // TYPESCRIPTPORT: original was using ordinal, and using the order of levels as defined in ErrorCorrectionLevel enum (LMQH)\n        // I will use the direct value from ErrorCorrectionLevelValues enum which in typescript goes to a number\n    };\n    /**\n     * <p>Deduces version information purely from QR Code dimensions.</p>\n     *\n     * @param dimension dimension in modules\n     * @return Version for a QR Code of that dimension\n     * @throws FormatException if dimension is not 1 mod 4\n     */\n    Version.getProvisionalVersionForDimension = function (dimension /*int*/) {\n        if (dimension % 4 !== 1) {\n            throw new FormatException();\n        }\n        try {\n            return this.getVersionForNumber((dimension - 17) / 4);\n        }\n        catch (ignored /*: IllegalArgumentException*/) {\n            throw new FormatException();\n        }\n    };\n    Version.getVersionForNumber = function (versionNumber /*int*/) {\n        if (versionNumber < 1 || versionNumber > 40) {\n            throw new IllegalArgumentException();\n        }\n        return Version.VERSIONS[versionNumber - 1];\n    };\n    Version.decodeVersionInformation = function (versionBits /*int*/) {\n        var bestDifference = Number.MAX_SAFE_INTEGER;\n        var bestVersion = 0;\n        for (var i = 0; i < Version.VERSION_DECODE_INFO.length; i++) {\n            var targetVersion = Version.VERSION_DECODE_INFO[i];\n            // Do the version info bits match exactly? done.\n            if (targetVersion === versionBits) {\n                return Version.getVersionForNumber(i + 7);\n            }\n            // Otherwise see if this is the closest to a real version info bit string\n            // we have seen so far\n            var bitsDifference = FormatInformation.numBitsDiffering(versionBits, targetVersion);\n            if (bitsDifference < bestDifference) {\n                bestVersion = i + 7;\n                bestDifference = bitsDifference;\n            }\n        }\n        // We can tolerate up to 3 bits of error since no two version info codewords will\n        // differ in less than 8 bits.\n        if (bestDifference <= 3) {\n            return Version.getVersionForNumber(bestVersion);\n        }\n        // If we didn't find a close enough match, fail\n        return null;\n    };\n    /**\n     * See ISO 18004:2006 Annex E\n     */\n    Version.prototype.buildFunctionPattern = function () {\n        var dimension = this.getDimensionForVersion();\n        var bitMatrix = new BitMatrix(dimension);\n        // Top left finder pattern + separator + format\n        bitMatrix.setRegion(0, 0, 9, 9);\n        // Top right finder pattern + separator + format\n        bitMatrix.setRegion(dimension - 8, 0, 8, 9);\n        // Bottom left finder pattern + separator + format\n        bitMatrix.setRegion(0, dimension - 8, 9, 8);\n        // Alignment patterns\n        var max = this.alignmentPatternCenters.length;\n        for (var x = 0; x < max; x++) {\n            var i = this.alignmentPatternCenters[x] - 2;\n            for (var y = 0; y < max; y++) {\n                if ((x === 0 && (y === 0 || y === max - 1)) || (x === max - 1 && y === 0)) {\n                    // No alignment patterns near the three finder patterns\n                    continue;\n                }\n                bitMatrix.setRegion(this.alignmentPatternCenters[y] - 2, i, 5, 5);\n            }\n        }\n        // Vertical timing pattern\n        bitMatrix.setRegion(6, 9, 1, dimension - 17);\n        // Horizontal timing pattern\n        bitMatrix.setRegion(9, 6, dimension - 17, 1);\n        if (this.versionNumber > 6) {\n            // Version info, top right\n            bitMatrix.setRegion(dimension - 11, 0, 3, 6);\n            // Version info, bottom left\n            bitMatrix.setRegion(0, dimension - 11, 6, 3);\n        }\n        return bitMatrix;\n    };\n    /*@Override*/\n    Version.prototype.toString = function () {\n        return '' + this.versionNumber;\n    };\n    /**\n       * See ISO 18004:2006 Annex D.\n       * Element i represents the raw version bits that specify version i + 7\n       */\n    Version.VERSION_DECODE_INFO = Int32Array.from([\n        0x07C94, 0x085BC, 0x09A99, 0x0A4D3, 0x0BBF6,\n        0x0C762, 0x0D847, 0x0E60D, 0x0F928, 0x10B78,\n        0x1145D, 0x12A17, 0x13532, 0x149A6, 0x15683,\n        0x168C9, 0x177EC, 0x18EC4, 0x191E1, 0x1AFAB,\n        0x1B08E, 0x1CC1A, 0x1D33F, 0x1ED75, 0x1F250,\n        0x209D5, 0x216F0, 0x228BA, 0x2379F, 0x24B0B,\n        0x2542E, 0x26A64, 0x27541, 0x28C69\n    ]);\n    /**\n       * See ISO 18004:2006 6.5.1 Table 9\n       */\n    Version.VERSIONS = [\n        new Version(1, new Int32Array(0), new ECBlocks(7, new ECB(1, 19)), new ECBlocks(10, new ECB(1, 16)), new ECBlocks(13, new ECB(1, 13)), new ECBlocks(17, new ECB(1, 9))),\n        new Version(2, Int32Array.from([6, 18]), new ECBlocks(10, new ECB(1, 34)), new ECBlocks(16, new ECB(1, 28)), new ECBlocks(22, new ECB(1, 22)), new ECBlocks(28, new ECB(1, 16))),\n        new Version(3, Int32Array.from([6, 22]), new ECBlocks(15, new ECB(1, 55)), new ECBlocks(26, new ECB(1, 44)), new ECBlocks(18, new ECB(2, 17)), new ECBlocks(22, new ECB(2, 13))),\n        new Version(4, Int32Array.from([6, 26]), new ECBlocks(20, new ECB(1, 80)), new ECBlocks(18, new ECB(2, 32)), new ECBlocks(26, new ECB(2, 24)), new ECBlocks(16, new ECB(4, 9))),\n        new Version(5, Int32Array.from([6, 30]), new ECBlocks(26, new ECB(1, 108)), new ECBlocks(24, new ECB(2, 43)), new ECBlocks(18, new ECB(2, 15), new ECB(2, 16)), new ECBlocks(22, new ECB(2, 11), new ECB(2, 12))),\n        new Version(6, Int32Array.from([6, 34]), new ECBlocks(18, new ECB(2, 68)), new ECBlocks(16, new ECB(4, 27)), new ECBlocks(24, new ECB(4, 19)), new ECBlocks(28, new ECB(4, 15))),\n        new Version(7, Int32Array.from([6, 22, 38]), new ECBlocks(20, new ECB(2, 78)), new ECBlocks(18, new ECB(4, 31)), new ECBlocks(18, new ECB(2, 14), new ECB(4, 15)), new ECBlocks(26, new ECB(4, 13), new ECB(1, 14))),\n        new Version(8, Int32Array.from([6, 24, 42]), new ECBlocks(24, new ECB(2, 97)), new ECBlocks(22, new ECB(2, 38), new ECB(2, 39)), new ECBlocks(22, new ECB(4, 18), new ECB(2, 19)), new ECBlocks(26, new ECB(4, 14), new ECB(2, 15))),\n        new Version(9, Int32Array.from([6, 26, 46]), new ECBlocks(30, new ECB(2, 116)), new ECBlocks(22, new ECB(3, 36), new ECB(2, 37)), new ECBlocks(20, new ECB(4, 16), new ECB(4, 17)), new ECBlocks(24, new ECB(4, 12), new ECB(4, 13))),\n        new Version(10, Int32Array.from([6, 28, 50]), new ECBlocks(18, new ECB(2, 68), new ECB(2, 69)), new ECBlocks(26, new ECB(4, 43), new ECB(1, 44)), new ECBlocks(24, new ECB(6, 19), new ECB(2, 20)), new ECBlocks(28, new ECB(6, 15), new ECB(2, 16))),\n        new Version(11, Int32Array.from([6, 30, 54]), new ECBlocks(20, new ECB(4, 81)), new ECBlocks(30, new ECB(1, 50), new ECB(4, 51)), new ECBlocks(28, new ECB(4, 22), new ECB(4, 23)), new ECBlocks(24, new ECB(3, 12), new ECB(8, 13))),\n        new Version(12, Int32Array.from([6, 32, 58]), new ECBlocks(24, new ECB(2, 92), new ECB(2, 93)), new ECBlocks(22, new ECB(6, 36), new ECB(2, 37)), new ECBlocks(26, new ECB(4, 20), new ECB(6, 21)), new ECBlocks(28, new ECB(7, 14), new ECB(4, 15))),\n        new Version(13, Int32Array.from([6, 34, 62]), new ECBlocks(26, new ECB(4, 107)), new ECBlocks(22, new ECB(8, 37), new ECB(1, 38)), new ECBlocks(24, new ECB(8, 20), new ECB(4, 21)), new ECBlocks(22, new ECB(12, 11), new ECB(4, 12))),\n        new Version(14, Int32Array.from([6, 26, 46, 66]), new ECBlocks(30, new ECB(3, 115), new ECB(1, 116)), new ECBlocks(24, new ECB(4, 40), new ECB(5, 41)), new ECBlocks(20, new ECB(11, 16), new ECB(5, 17)), new ECBlocks(24, new ECB(11, 12), new ECB(5, 13))),\n        new Version(15, Int32Array.from([6, 26, 48, 70]), new ECBlocks(22, new ECB(5, 87), new ECB(1, 88)), new ECBlocks(24, new ECB(5, 41), new ECB(5, 42)), new ECBlocks(30, new ECB(5, 24), new ECB(7, 25)), new ECBlocks(24, new ECB(11, 12), new ECB(7, 13))),\n        new Version(16, Int32Array.from([6, 26, 50, 74]), new ECBlocks(24, new ECB(5, 98), new ECB(1, 99)), new ECBlocks(28, new ECB(7, 45), new ECB(3, 46)), new ECBlocks(24, new ECB(15, 19), new ECB(2, 20)), new ECBlocks(30, new ECB(3, 15), new ECB(13, 16))),\n        new Version(17, Int32Array.from([6, 30, 54, 78]), new ECBlocks(28, new ECB(1, 107), new ECB(5, 108)), new ECBlocks(28, new ECB(10, 46), new ECB(1, 47)), new ECBlocks(28, new ECB(1, 22), new ECB(15, 23)), new ECBlocks(28, new ECB(2, 14), new ECB(17, 15))),\n        new Version(18, Int32Array.from([6, 30, 56, 82]), new ECBlocks(30, new ECB(5, 120), new ECB(1, 121)), new ECBlocks(26, new ECB(9, 43), new ECB(4, 44)), new ECBlocks(28, new ECB(17, 22), new ECB(1, 23)), new ECBlocks(28, new ECB(2, 14), new ECB(19, 15))),\n        new Version(19, Int32Array.from([6, 30, 58, 86]), new ECBlocks(28, new ECB(3, 113), new ECB(4, 114)), new ECBlocks(26, new ECB(3, 44), new ECB(11, 45)), new ECBlocks(26, new ECB(17, 21), new ECB(4, 22)), new ECBlocks(26, new ECB(9, 13), new ECB(16, 14))),\n        new Version(20, Int32Array.from([6, 34, 62, 90]), new ECBlocks(28, new ECB(3, 107), new ECB(5, 108)), new ECBlocks(26, new ECB(3, 41), new ECB(13, 42)), new ECBlocks(30, new ECB(15, 24), new ECB(5, 25)), new ECBlocks(28, new ECB(15, 15), new ECB(10, 16))),\n        new Version(21, Int32Array.from([6, 28, 50, 72, 94]), new ECBlocks(28, new ECB(4, 116), new ECB(4, 117)), new ECBlocks(26, new ECB(17, 42)), new ECBlocks(28, new ECB(17, 22), new ECB(6, 23)), new ECBlocks(30, new ECB(19, 16), new ECB(6, 17))),\n        new Version(22, Int32Array.from([6, 26, 50, 74, 98]), new ECBlocks(28, new ECB(2, 111), new ECB(7, 112)), new ECBlocks(28, new ECB(17, 46)), new ECBlocks(30, new ECB(7, 24), new ECB(16, 25)), new ECBlocks(24, new ECB(34, 13))),\n        new Version(23, Int32Array.from([6, 30, 54, 78, 102]), new ECBlocks(30, new ECB(4, 121), new ECB(5, 122)), new ECBlocks(28, new ECB(4, 47), new ECB(14, 48)), new ECBlocks(30, new ECB(11, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(16, 15), new ECB(14, 16))),\n        new Version(24, Int32Array.from([6, 28, 54, 80, 106]), new ECBlocks(30, new ECB(6, 117), new ECB(4, 118)), new ECBlocks(28, new ECB(6, 45), new ECB(14, 46)), new ECBlocks(30, new ECB(11, 24), new ECB(16, 25)), new ECBlocks(30, new ECB(30, 16), new ECB(2, 17))),\n        new Version(25, Int32Array.from([6, 32, 58, 84, 110]), new ECBlocks(26, new ECB(8, 106), new ECB(4, 107)), new ECBlocks(28, new ECB(8, 47), new ECB(13, 48)), new ECBlocks(30, new ECB(7, 24), new ECB(22, 25)), new ECBlocks(30, new ECB(22, 15), new ECB(13, 16))),\n        new Version(26, Int32Array.from([6, 30, 58, 86, 114]), new ECBlocks(28, new ECB(10, 114), new ECB(2, 115)), new ECBlocks(28, new ECB(19, 46), new ECB(4, 47)), new ECBlocks(28, new ECB(28, 22), new ECB(6, 23)), new ECBlocks(30, new ECB(33, 16), new ECB(4, 17))),\n        new Version(27, Int32Array.from([6, 34, 62, 90, 118]), new ECBlocks(30, new ECB(8, 122), new ECB(4, 123)), new ECBlocks(28, new ECB(22, 45), new ECB(3, 46)), new ECBlocks(30, new ECB(8, 23), new ECB(26, 24)), new ECBlocks(30, new ECB(12, 15), new ECB(28, 16))),\n        new Version(28, Int32Array.from([6, 26, 50, 74, 98, 122]), new ECBlocks(30, new ECB(3, 117), new ECB(10, 118)), new ECBlocks(28, new ECB(3, 45), new ECB(23, 46)), new ECBlocks(30, new ECB(4, 24), new ECB(31, 25)), new ECBlocks(30, new ECB(11, 15), new ECB(31, 16))),\n        new Version(29, Int32Array.from([6, 30, 54, 78, 102, 126]), new ECBlocks(30, new ECB(7, 116), new ECB(7, 117)), new ECBlocks(28, new ECB(21, 45), new ECB(7, 46)), new ECBlocks(30, new ECB(1, 23), new ECB(37, 24)), new ECBlocks(30, new ECB(19, 15), new ECB(26, 16))),\n        new Version(30, Int32Array.from([6, 26, 52, 78, 104, 130]), new ECBlocks(30, new ECB(5, 115), new ECB(10, 116)), new ECBlocks(28, new ECB(19, 47), new ECB(10, 48)), new ECBlocks(30, new ECB(15, 24), new ECB(25, 25)), new ECBlocks(30, new ECB(23, 15), new ECB(25, 16))),\n        new Version(31, Int32Array.from([6, 30, 56, 82, 108, 134]), new ECBlocks(30, new ECB(13, 115), new ECB(3, 116)), new ECBlocks(28, new ECB(2, 46), new ECB(29, 47)), new ECBlocks(30, new ECB(42, 24), new ECB(1, 25)), new ECBlocks(30, new ECB(23, 15), new ECB(28, 16))),\n        new Version(32, Int32Array.from([6, 34, 60, 86, 112, 138]), new ECBlocks(30, new ECB(17, 115)), new ECBlocks(28, new ECB(10, 46), new ECB(23, 47)), new ECBlocks(30, new ECB(10, 24), new ECB(35, 25)), new ECBlocks(30, new ECB(19, 15), new ECB(35, 16))),\n        new Version(33, Int32Array.from([6, 30, 58, 86, 114, 142]), new ECBlocks(30, new ECB(17, 115), new ECB(1, 116)), new ECBlocks(28, new ECB(14, 46), new ECB(21, 47)), new ECBlocks(30, new ECB(29, 24), new ECB(19, 25)), new ECBlocks(30, new ECB(11, 15), new ECB(46, 16))),\n        new Version(34, Int32Array.from([6, 34, 62, 90, 118, 146]), new ECBlocks(30, new ECB(13, 115), new ECB(6, 116)), new ECBlocks(28, new ECB(14, 46), new ECB(23, 47)), new ECBlocks(30, new ECB(44, 24), new ECB(7, 25)), new ECBlocks(30, new ECB(59, 16), new ECB(1, 17))),\n        new Version(35, Int32Array.from([6, 30, 54, 78, 102, 126, 150]), new ECBlocks(30, new ECB(12, 121), new ECB(7, 122)), new ECBlocks(28, new ECB(12, 47), new ECB(26, 48)), new ECBlocks(30, new ECB(39, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(22, 15), new ECB(41, 16))),\n        new Version(36, Int32Array.from([6, 24, 50, 76, 102, 128, 154]), new ECBlocks(30, new ECB(6, 121), new ECB(14, 122)), new ECBlocks(28, new ECB(6, 47), new ECB(34, 48)), new ECBlocks(30, new ECB(46, 24), new ECB(10, 25)), new ECBlocks(30, new ECB(2, 15), new ECB(64, 16))),\n        new Version(37, Int32Array.from([6, 28, 54, 80, 106, 132, 158]), new ECBlocks(30, new ECB(17, 122), new ECB(4, 123)), new ECBlocks(28, new ECB(29, 46), new ECB(14, 47)), new ECBlocks(30, new ECB(49, 24), new ECB(10, 25)), new ECBlocks(30, new ECB(24, 15), new ECB(46, 16))),\n        new Version(38, Int32Array.from([6, 32, 58, 84, 110, 136, 162]), new ECBlocks(30, new ECB(4, 122), new ECB(18, 123)), new ECBlocks(28, new ECB(13, 46), new ECB(32, 47)), new ECBlocks(30, new ECB(48, 24), new ECB(14, 25)), new ECBlocks(30, new ECB(42, 15), new ECB(32, 16))),\n        new Version(39, Int32Array.from([6, 26, 54, 82, 110, 138, 166]), new ECBlocks(30, new ECB(20, 117), new ECB(4, 118)), new ECBlocks(28, new ECB(40, 47), new ECB(7, 48)), new ECBlocks(30, new ECB(43, 24), new ECB(22, 25)), new ECBlocks(30, new ECB(10, 15), new ECB(67, 16))),\n        new Version(40, Int32Array.from([6, 30, 58, 86, 114, 142, 170]), new ECBlocks(30, new ECB(19, 118), new ECB(6, 119)), new ECBlocks(28, new ECB(18, 47), new ECB(31, 48)), new ECBlocks(30, new ECB(34, 24), new ECB(34, 25)), new ECBlocks(30, new ECB(20, 15), new ECB(61, 16)))\n    ];\n    return Version;\n}());\nexport default Version;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAACC,aAAa,CAAC,SAASC,uBAAuB,EAAE;IAC7D,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIC,QAAQ,GAAG,EAAE;IACjB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAAClB,MAAM,EAAEiB,EAAE,EAAE,EAAE;MAC1CD,QAAQ,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IACpC;IACA,IAAI,CAACL,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;IACtD,IAAI,CAACG,QAAQ,GAAGA,QAAQ;IACxB,IAAIG,KAAK,GAAG,CAAC;IACb,IAAIC,WAAW,GAAGJ,QAAQ,CAAC,CAAC,CAAC,CAACK,sBAAsB,CAAC,CAAC;IACtD,IAAIC,QAAQ,GAAGN,QAAQ,CAAC,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC;IACxC,IAAI;MACA,KAAK,IAAIC,UAAU,GAAGhC,QAAQ,CAAC8B,QAAQ,CAAC,EAAEG,YAAY,GAAGD,UAAU,CAACvB,IAAI,CAAC,CAAC,EAAE,CAACwB,YAAY,CAACtB,IAAI,EAAEsB,YAAY,GAAGD,UAAU,CAACvB,IAAI,CAAC,CAAC,EAAE;QAC9H,IAAIyB,OAAO,GAAGD,YAAY,CAACvB,KAAK;QAChCiB,KAAK,IAAIO,OAAO,CAACC,QAAQ,CAAC,CAAC,IAAID,OAAO,CAACE,gBAAgB,CAAC,CAAC,GAAGR,WAAW,CAAC;MAC5E;IACJ,CAAC,CACD,OAAOS,KAAK,EAAE;MAAEf,GAAG,GAAG;QAAEgB,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIJ,YAAY,IAAI,CAACA,YAAY,CAACtB,IAAI,KAAKY,EAAE,GAAGS,UAAU,CAACO,MAAM,CAAC,EAAEhB,EAAE,CAAChB,IAAI,CAACyB,UAAU,CAAC;MAC3F,CAAC,SACO;QAAE,IAAIV,GAAG,EAAE,MAAMA,GAAG,CAACgB,KAAK;MAAE;IACxC;IACA,IAAI,CAACE,cAAc,GAAGb,KAAK;EAC/B;EACAR,OAAO,CAACsB,SAAS,CAACC,gBAAgB,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACtB,aAAa;EAC7B,CAAC;EACDD,OAAO,CAACsB,SAAS,CAACE,0BAA0B,GAAG,YAAY;IACvD,OAAO,IAAI,CAACtB,uBAAuB;EACvC,CAAC;EACDF,OAAO,CAACsB,SAAS,CAACG,iBAAiB,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACJ,cAAc;EAC9B,CAAC;EACDrB,OAAO,CAACsB,SAAS,CAACI,sBAAsB,GAAG,YAAY;IACnD,OAAO,EAAE,GAAG,CAAC,GAAG,IAAI,CAACzB,aAAa;EACtC,CAAC;EACDD,OAAO,CAACsB,SAAS,CAACK,mBAAmB,GAAG,UAAUC,OAAO,EAAE;IACvD,OAAO,IAAI,CAACvB,QAAQ,CAACuB,OAAO,CAACC,QAAQ,CAAC,CAAC,CAAC;IACxC;IACA;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACI7B,OAAO,CAAC8B,iCAAiC,GAAG,UAAUC,SAAS,CAAC,SAAS;IACrE,IAAIA,SAAS,GAAG,CAAC,KAAK,CAAC,EAAE;MACrB,MAAM,IAAIjC,eAAe,CAAC,CAAC;IAC/B;IACA,IAAI;MACA,OAAO,IAAI,CAACkC,mBAAmB,CAAC,CAACD,SAAS,GAAG,EAAE,IAAI,CAAC,CAAC;IACzD,CAAC,CACD,OAAOE,OAAO,CAAC,gCAAgC;MAC3C,MAAM,IAAInC,eAAe,CAAC,CAAC;IAC/B;EACJ,CAAC;EACDE,OAAO,CAACgC,mBAAmB,GAAG,UAAU/B,aAAa,CAAC,SAAS;IAC3D,IAAIA,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,EAAE,EAAE;MACzC,MAAM,IAAIF,wBAAwB,CAAC,CAAC;IACxC;IACA,OAAOC,OAAO,CAACkC,QAAQ,CAACjC,aAAa,GAAG,CAAC,CAAC;EAC9C,CAAC;EACDD,OAAO,CAACmC,wBAAwB,GAAG,UAAUC,WAAW,CAAC,SAAS;IAC9D,IAAIC,cAAc,GAAGC,MAAM,CAACC,gBAAgB;IAC5C,IAAIC,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,OAAO,CAACyC,mBAAmB,CAACpD,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzD,IAAIuD,aAAa,GAAG1C,OAAO,CAACyC,mBAAmB,CAACtD,CAAC,CAAC;MAClD;MACA,IAAIuD,aAAa,KAAKN,WAAW,EAAE;QAC/B,OAAOpC,OAAO,CAACgC,mBAAmB,CAAC7C,CAAC,GAAG,CAAC,CAAC;MAC7C;MACA;MACA;MACA,IAAIwD,cAAc,GAAGhD,iBAAiB,CAACiD,gBAAgB,CAACR,WAAW,EAAEM,aAAa,CAAC;MACnF,IAAIC,cAAc,GAAGN,cAAc,EAAE;QACjCG,WAAW,GAAGrD,CAAC,GAAG,CAAC;QACnBkD,cAAc,GAAGM,cAAc;MACnC;IACJ;IACA;IACA;IACA,IAAIN,cAAc,IAAI,CAAC,EAAE;MACrB,OAAOrC,OAAO,CAACgC,mBAAmB,CAACQ,WAAW,CAAC;IACnD;IACA;IACA,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;EACIxC,OAAO,CAACsB,SAAS,CAACuB,oBAAoB,GAAG,YAAY;IACjD,IAAId,SAAS,GAAG,IAAI,CAACL,sBAAsB,CAAC,CAAC;IAC7C,IAAIoB,SAAS,GAAG,IAAIpD,SAAS,CAACqC,SAAS,CAAC;IACxC;IACAe,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/B;IACAD,SAAS,CAACC,SAAS,CAAChB,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3C;IACAe,SAAS,CAACC,SAAS,CAAC,CAAC,EAAEhB,SAAS,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC3C;IACA,IAAIiB,GAAG,GAAG,IAAI,CAAC9C,uBAAuB,CAACb,MAAM;IAC7C,KAAK,IAAI4D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;MAC1B,IAAI9D,CAAC,GAAG,IAAI,CAACe,uBAAuB,CAAC+C,CAAC,CAAC,GAAG,CAAC;MAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAC1B,IAAKD,CAAC,KAAK,CAAC,KAAKC,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAKF,GAAG,GAAG,CAAC,CAAC,IAAMC,CAAC,KAAKD,GAAG,GAAG,CAAC,IAAIE,CAAC,KAAK,CAAE,EAAE;UACvE;UACA;QACJ;QACAJ,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC7C,uBAAuB,CAACgD,CAAC,CAAC,GAAG,CAAC,EAAE/D,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrE;IACJ;IACA;IACA2D,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEhB,SAAS,GAAG,EAAE,CAAC;IAC5C;IACAe,SAAS,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEhB,SAAS,GAAG,EAAE,EAAE,CAAC,CAAC;IAC5C,IAAI,IAAI,CAAC9B,aAAa,GAAG,CAAC,EAAE;MACxB;MACA6C,SAAS,CAACC,SAAS,CAAChB,SAAS,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC5C;MACAe,SAAS,CAACC,SAAS,CAAC,CAAC,EAAEhB,SAAS,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IAChD;IACA,OAAOe,SAAS;EACpB,CAAC;EACD;EACA9C,OAAO,CAACsB,SAAS,CAAC6B,QAAQ,GAAG,YAAY;IACrC,OAAO,EAAE,GAAG,IAAI,CAAClD,aAAa;EAClC,CAAC;EACD;AACJ;AACA;AACA;EACID,OAAO,CAACyC,mBAAmB,GAAGW,UAAU,CAACC,IAAI,CAAC,CAC1C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAC3C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAC3C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAC3C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAC3C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAC3C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAC3C,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CACrC,CAAC;EACF;AACJ;AACA;EACIrD,OAAO,CAACkC,QAAQ,GAAG,CACf,IAAIlC,OAAO,CAAC,CAAC,EAAE,IAAIoD,UAAU,CAAC,CAAC,CAAC,EAAE,IAAIxD,QAAQ,CAAC,CAAC,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EACvK,IAAIG,OAAO,CAAC,CAAC,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChL,IAAIG,OAAO,CAAC,CAAC,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChL,IAAIG,OAAO,CAAC,CAAC,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC/K,IAAIG,OAAO,CAAC,CAAC,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACjN,IAAIG,OAAO,CAAC,CAAC,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAChL,IAAIG,OAAO,CAAC,CAAC,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACpN,IAAIG,OAAO,CAAC,CAAC,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACpO,IAAIG,OAAO,CAAC,CAAC,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACrO,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACrP,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACrO,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACrP,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACvO,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAC7P,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAC1P,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC3P,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC9P,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC7P,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC9P,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC/P,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAClP,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAClO,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EACrQ,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACpQ,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EACpQ,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACpQ,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EACpQ,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EACzQ,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EACzQ,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC5Q,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC1Q,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC3P,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC5Q,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAC1Q,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EACjR,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAC/Q,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EACjR,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EACjR,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAChR,IAAIG,OAAO,CAAC,EAAE,EAAEoD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAIzD,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,IAAIA,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAID,QAAQ,CAAC,EAAE,EAAE,IAAIC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,IAAIA,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CACpR;EACD,OAAOG,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}