{"ast": null, "code": "var ExpandedRow = /** @class */function () {\n  function ExpandedRow(pairs, rowNumber, wasReversed) {\n    this.pairs = pairs;\n    this.rowNumber = rowNumber;\n    this.wasReversed = wasReversed;\n  }\n  ExpandedRow.prototype.getPairs = function () {\n    return this.pairs;\n  };\n  ExpandedRow.prototype.getRowNumber = function () {\n    return this.rowNumber;\n  };\n  ExpandedRow.prototype.isReversed = function () {\n    return this.wasReversed;\n  };\n  // check implementation\n  ExpandedRow.prototype.isEquivalent = function (otherPairs) {\n    return this.checkEqualitity(this, otherPairs);\n  };\n  // @Override\n  ExpandedRow.prototype.toString = function () {\n    return '{ ' + this.pairs + ' }';\n  };\n  /**\n   * Two rows are equal if they contain the same pairs in the same order.\n   */\n  // @Override\n  // check implementation\n  ExpandedRow.prototype.equals = function (o1, o2) {\n    if (!(o1 instanceof ExpandedRow)) {\n      return false;\n    }\n    return this.checkEqualitity(o1, o2) && o1.wasReversed === o2.wasReversed;\n  };\n  ExpandedRow.prototype.checkEqualitity = function (pair1, pair2) {\n    if (!pair1 || !pair2) return;\n    var result;\n    pair1.forEach(function (e1, i) {\n      pair2.forEach(function (e2) {\n        if (e1.getLeftChar().getValue() === e2.getLeftChar().getValue() && e1.getRightChar().getValue() === e2.getRightChar().getValue() && e1.getFinderPatter().getValue() === e2.getFinderPatter().getValue()) {\n          result = true;\n        }\n      });\n    });\n    return result;\n  };\n  return ExpandedRow;\n}();\nexport default ExpandedRow;", "map": {"version": 3, "names": ["ExpandedRow", "pairs", "rowNumber", "wasReversed", "prototype", "getPairs", "getRowNumber", "isReversed", "isEquivalent", "otherPairs", "checkEqualitity", "toString", "equals", "o1", "o2", "pair1", "pair2", "result", "for<PERSON>ach", "e1", "i", "e2", "getLeftChar", "getValue", "getRightChar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/ExpandedRow.js"], "sourcesContent": ["var ExpandedRow = /** @class */ (function () {\n    function ExpandedRow(pairs, rowNumber, wasReversed) {\n        this.pairs = pairs;\n        this.rowNumber = rowNumber;\n        this.wasReversed = wasReversed;\n    }\n    ExpandedRow.prototype.getPairs = function () {\n        return this.pairs;\n    };\n    ExpandedRow.prototype.getRowNumber = function () {\n        return this.rowNumber;\n    };\n    ExpandedRow.prototype.isReversed = function () {\n        return this.wasReversed;\n    };\n    // check implementation\n    ExpandedRow.prototype.isEquivalent = function (otherPairs) {\n        return this.checkEqualitity(this, otherPairs);\n    };\n    // @Override\n    ExpandedRow.prototype.toString = function () {\n        return '{ ' + this.pairs + ' }';\n    };\n    /**\n     * Two rows are equal if they contain the same pairs in the same order.\n     */\n    // @Override\n    // check implementation\n    ExpandedRow.prototype.equals = function (o1, o2) {\n        if (!(o1 instanceof ExpandedRow)) {\n            return false;\n        }\n        return this.checkEqualitity(o1, o2) && o1.wasReversed === o2.wasReversed;\n    };\n    ExpandedRow.prototype.checkEqualitity = function (pair1, pair2) {\n        if (!pair1 || !pair2)\n            return;\n        var result;\n        pair1.forEach(function (e1, i) {\n            pair2.forEach(function (e2) {\n                if (e1.getLeftChar().getValue() === e2.getLeftChar().getValue() && e1.getRightChar().getValue() === e2.getRightChar().getValue() && e1.getFinderPatter().getValue() === e2.getFinderPatter().getValue()) {\n                    result = true;\n                }\n            });\n        });\n        return result;\n    };\n    return ExpandedRow;\n}());\nexport default ExpandedRow;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAACC,KAAK,EAAEC,SAAS,EAAEC,WAAW,EAAE;IAChD,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;EACAH,WAAW,CAACI,SAAS,CAACC,QAAQ,GAAG,YAAY;IACzC,OAAO,IAAI,CAACJ,KAAK;EACrB,CAAC;EACDD,WAAW,CAACI,SAAS,CAACE,YAAY,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACJ,SAAS;EACzB,CAAC;EACDF,WAAW,CAACI,SAAS,CAACG,UAAU,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACJ,WAAW;EAC3B,CAAC;EACD;EACAH,WAAW,CAACI,SAAS,CAACI,YAAY,GAAG,UAAUC,UAAU,EAAE;IACvD,OAAO,IAAI,CAACC,eAAe,CAAC,IAAI,EAAED,UAAU,CAAC;EACjD,CAAC;EACD;EACAT,WAAW,CAACI,SAAS,CAACO,QAAQ,GAAG,YAAY;IACzC,OAAO,IAAI,GAAG,IAAI,CAACV,KAAK,GAAG,IAAI;EACnC,CAAC;EACD;AACJ;AACA;EACI;EACA;EACAD,WAAW,CAACI,SAAS,CAACQ,MAAM,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAE;IAC7C,IAAI,EAAED,EAAE,YAAYb,WAAW,CAAC,EAAE;MAC9B,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACU,eAAe,CAACG,EAAE,EAAEC,EAAE,CAAC,IAAID,EAAE,CAACV,WAAW,KAAKW,EAAE,CAACX,WAAW;EAC5E,CAAC;EACDH,WAAW,CAACI,SAAS,CAACM,eAAe,GAAG,UAAUK,KAAK,EAAEC,KAAK,EAAE;IAC5D,IAAI,CAACD,KAAK,IAAI,CAACC,KAAK,EAChB;IACJ,IAAIC,MAAM;IACVF,KAAK,CAACG,OAAO,CAAC,UAAUC,EAAE,EAAEC,CAAC,EAAE;MAC3BJ,KAAK,CAACE,OAAO,CAAC,UAAUG,EAAE,EAAE;QACxB,IAAIF,EAAE,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,KAAKF,EAAE,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,IAAIJ,EAAE,CAACK,YAAY,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,KAAKF,EAAE,CAACG,YAAY,CAAC,CAAC,CAACD,QAAQ,CAAC,CAAC,IAAIJ,EAAE,CAACM,eAAe,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,KAAKF,EAAE,CAACI,eAAe,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE;UACrMN,MAAM,GAAG,IAAI;QACjB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOA,MAAM;EACjB,CAAC;EACD,OAAOjB,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}