{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useIsDateDisabled } from \"./useIsDateDisabled.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nexport const createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {\n  switch (action.type) {\n    case 'changeMonth':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.newMonth,\n        isMonthSwitchingAnimating: !reduceAnimations\n      });\n    case 'changeMonthTimezone':\n      {\n        const newTimezone = action.newTimezone;\n        if (utils.getTimezone(state.currentMonth) === newTimezone) {\n          return state;\n        }\n        let newCurrentMonth = utils.setTimezone(state.currentMonth, newTimezone);\n        if (utils.getMonth(newCurrentMonth) !== utils.getMonth(state.currentMonth)) {\n          newCurrentMonth = utils.setMonth(newCurrentMonth, utils.getMonth(state.currentMonth));\n        }\n        return _extends({}, state, {\n          currentMonth: newCurrentMonth\n        });\n      }\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    case 'changeFocusedDay':\n      {\n        if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {\n          return state;\n        }\n        const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);\n        return _extends({}, state, {\n          focusedDay: action.focusedDay,\n          isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,\n          currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,\n          slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? 'left' : 'right'\n        });\n      }\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = params => {\n  const {\n    value,\n    referenceDate: referenceDateProp,\n    disableFuture,\n    disablePast,\n    disableSwitchToMonthOnDayFocus = false,\n    maxDate,\n    minDate,\n    onMonthChange,\n    reduceAnimations,\n    shouldDisableDate,\n    timezone\n  } = params;\n  const utils = useUtils();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;\n  const referenceDate = React.useMemo(() => {\n    return singleItemValueManager.getInitialReferenceValue({\n      value,\n      utils,\n      timezone,\n      props: params,\n      referenceDate: referenceDateProp,\n      granularity: SECTION_TYPE_GRANULARITY.day\n    });\n  },\n  // We want the `referenceDate` to update on prop and `timezone` change (https://github.com/mui/mui-x/issues/10804)\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [referenceDateProp, timezone]);\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: referenceDate,\n    currentMonth: utils.startOfMonth(referenceDate),\n    slideDirection: 'left'\n  });\n\n  // Ensure that `calendarState.currentMonth` timezone is updated when `referenceDate` (or timezone changes)\n  // https://github.com/mui/mui-x/issues/10804\n  React.useEffect(() => {\n    dispatch({\n      type: 'changeMonthTimezone',\n      newTimezone: utils.getTimezone(referenceDate)\n    });\n  }, [referenceDate, utils]);\n  const handleChangeMonth = React.useCallback(payload => {\n    dispatch(_extends({\n      type: 'changeMonth'\n    }, payload));\n    if (onMonthChange) {\n      onMonthChange(payload.newMonth);\n    }\n  }, [onMonthChange]);\n  const changeMonth = React.useCallback(newDate => {\n    const newDateRequested = newDate;\n    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {\n      return;\n    }\n    handleChangeMonth({\n      newMonth: utils.startOfMonth(newDateRequested),\n      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? 'left' : 'right'\n    });\n  }, [calendarState.currentMonth, handleChangeMonth, utils]);\n  const isDateDisabled = useIsDateDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast,\n    timezone\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  const changeFocusedDay = useEventCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {\n    if (!isDateDisabled(newFocusedDate)) {\n      dispatch({\n        type: 'changeFocusedDay',\n        focusedDay: newFocusedDate,\n        withoutMonthSwitchingAnimation\n      });\n    }\n  });\n  return {\n    referenceDate,\n    calendarState,\n    changeMonth,\n    changeFocusedDay,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd,\n    handleChangeMonth\n  };\n};", "map": {"version": 3, "names": ["_extends", "React", "useEventCallback", "useIsDateDisabled", "useUtils", "singleItemValueManager", "SECTION_TYPE_GRANULARITY", "createCalendarStateReducer", "reduceAnimations", "disableSwitchToMonthOnDayFocus", "utils", "state", "action", "type", "slideDirection", "direction", "currentMonth", "newMonth", "isMonthSwitchingAnimating", "newTimezone", "getTimezone", "newCurrentMonth", "setTimezone", "getMonth", "setMonth", "focusedDay", "isSameDay", "needMonthSwitch", "isSameMonth", "withoutMonthSwitchingAnimation", "startOfMonth", "isAfterDay", "Error", "useCalendarState", "params", "value", "referenceDate", "referenceDateProp", "disableFuture", "disablePast", "maxDate", "minDate", "onMonthChange", "shouldDisableDate", "timezone", "reducerFn", "useRef", "Boolean", "current", "useMemo", "getInitialReferenceValue", "props", "granularity", "day", "calendarState", "dispatch", "useReducer", "useEffect", "handleChangeMonth", "useCallback", "payload", "changeMonth", "newDate", "newDateRequested", "isDateDisabled", "onMonthSwitchingAnimationEnd", "changeFocusedDay", "newFocusedDate"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/DateCalendar/useCalendarState.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useIsDateDisabled } from \"./useIsDateDisabled.js\";\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nexport const createCalendarStateReducer = (reduceAnimations, disableSwitchToMonthOnDayFocus, utils) => (state, action) => {\n  switch (action.type) {\n    case 'changeMonth':\n      return _extends({}, state, {\n        slideDirection: action.direction,\n        currentMonth: action.newMonth,\n        isMonthSwitchingAnimating: !reduceAnimations\n      });\n    case 'changeMonthTimezone':\n      {\n        const newTimezone = action.newTimezone;\n        if (utils.getTimezone(state.currentMonth) === newTimezone) {\n          return state;\n        }\n        let newCurrentMonth = utils.setTimezone(state.currentMonth, newTimezone);\n        if (utils.getMonth(newCurrentMonth) !== utils.getMonth(state.currentMonth)) {\n          newCurrentMonth = utils.setMonth(newCurrentMonth, utils.getMonth(state.currentMonth));\n        }\n        return _extends({}, state, {\n          currentMonth: newCurrentMonth\n        });\n      }\n    case 'finishMonthSwitchingAnimation':\n      return _extends({}, state, {\n        isMonthSwitchingAnimating: false\n      });\n    case 'changeFocusedDay':\n      {\n        if (state.focusedDay != null && action.focusedDay != null && utils.isSameDay(action.focusedDay, state.focusedDay)) {\n          return state;\n        }\n        const needMonthSwitch = action.focusedDay != null && !disableSwitchToMonthOnDayFocus && !utils.isSameMonth(state.currentMonth, action.focusedDay);\n        return _extends({}, state, {\n          focusedDay: action.focusedDay,\n          isMonthSwitchingAnimating: needMonthSwitch && !reduceAnimations && !action.withoutMonthSwitchingAnimation,\n          currentMonth: needMonthSwitch ? utils.startOfMonth(action.focusedDay) : state.currentMonth,\n          slideDirection: action.focusedDay != null && utils.isAfterDay(action.focusedDay, state.currentMonth) ? 'left' : 'right'\n        });\n      }\n    default:\n      throw new Error('missing support');\n  }\n};\nexport const useCalendarState = params => {\n  const {\n    value,\n    referenceDate: referenceDateProp,\n    disableFuture,\n    disablePast,\n    disableSwitchToMonthOnDayFocus = false,\n    maxDate,\n    minDate,\n    onMonthChange,\n    reduceAnimations,\n    shouldDisableDate,\n    timezone\n  } = params;\n  const utils = useUtils();\n  const reducerFn = React.useRef(createCalendarStateReducer(Boolean(reduceAnimations), disableSwitchToMonthOnDayFocus, utils)).current;\n  const referenceDate = React.useMemo(() => {\n    return singleItemValueManager.getInitialReferenceValue({\n      value,\n      utils,\n      timezone,\n      props: params,\n      referenceDate: referenceDateProp,\n      granularity: SECTION_TYPE_GRANULARITY.day\n    });\n  },\n  // We want the `referenceDate` to update on prop and `timezone` change (https://github.com/mui/mui-x/issues/10804)\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [referenceDateProp, timezone]);\n  const [calendarState, dispatch] = React.useReducer(reducerFn, {\n    isMonthSwitchingAnimating: false,\n    focusedDay: referenceDate,\n    currentMonth: utils.startOfMonth(referenceDate),\n    slideDirection: 'left'\n  });\n\n  // Ensure that `calendarState.currentMonth` timezone is updated when `referenceDate` (or timezone changes)\n  // https://github.com/mui/mui-x/issues/10804\n  React.useEffect(() => {\n    dispatch({\n      type: 'changeMonthTimezone',\n      newTimezone: utils.getTimezone(referenceDate)\n    });\n  }, [referenceDate, utils]);\n  const handleChangeMonth = React.useCallback(payload => {\n    dispatch(_extends({\n      type: 'changeMonth'\n    }, payload));\n    if (onMonthChange) {\n      onMonthChange(payload.newMonth);\n    }\n  }, [onMonthChange]);\n  const changeMonth = React.useCallback(newDate => {\n    const newDateRequested = newDate;\n    if (utils.isSameMonth(newDateRequested, calendarState.currentMonth)) {\n      return;\n    }\n    handleChangeMonth({\n      newMonth: utils.startOfMonth(newDateRequested),\n      direction: utils.isAfterDay(newDateRequested, calendarState.currentMonth) ? 'left' : 'right'\n    });\n  }, [calendarState.currentMonth, handleChangeMonth, utils]);\n  const isDateDisabled = useIsDateDisabled({\n    shouldDisableDate,\n    minDate,\n    maxDate,\n    disableFuture,\n    disablePast,\n    timezone\n  });\n  const onMonthSwitchingAnimationEnd = React.useCallback(() => {\n    dispatch({\n      type: 'finishMonthSwitchingAnimation'\n    });\n  }, []);\n  const changeFocusedDay = useEventCallback((newFocusedDate, withoutMonthSwitchingAnimation) => {\n    if (!isDateDisabled(newFocusedDate)) {\n      dispatch({\n        type: 'changeFocusedDay',\n        focusedDay: newFocusedDate,\n        withoutMonthSwitchingAnimation\n      });\n    }\n  });\n  return {\n    referenceDate,\n    calendarState,\n    changeMonth,\n    changeFocusedDay,\n    isDateDisabled,\n    onMonthSwitchingAnimationEnd,\n    handleChangeMonth\n  };\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,iBAAiB,QAAQ,wBAAwB;AAC1D,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,gBAAgB,EAAEC,8BAA8B,EAAEC,KAAK,KAAK,CAACC,KAAK,EAAEC,MAAM,KAAK;EACxH,QAAQA,MAAM,CAACC,IAAI;IACjB,KAAK,aAAa;MAChB,OAAOb,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;QACzBG,cAAc,EAAEF,MAAM,CAACG,SAAS;QAChCC,YAAY,EAAEJ,MAAM,CAACK,QAAQ;QAC7BC,yBAAyB,EAAE,CAACV;MAC9B,CAAC,CAAC;IACJ,KAAK,qBAAqB;MACxB;QACE,MAAMW,WAAW,GAAGP,MAAM,CAACO,WAAW;QACtC,IAAIT,KAAK,CAACU,WAAW,CAACT,KAAK,CAACK,YAAY,CAAC,KAAKG,WAAW,EAAE;UACzD,OAAOR,KAAK;QACd;QACA,IAAIU,eAAe,GAAGX,KAAK,CAACY,WAAW,CAACX,KAAK,CAACK,YAAY,EAAEG,WAAW,CAAC;QACxE,IAAIT,KAAK,CAACa,QAAQ,CAACF,eAAe,CAAC,KAAKX,KAAK,CAACa,QAAQ,CAACZ,KAAK,CAACK,YAAY,CAAC,EAAE;UAC1EK,eAAe,GAAGX,KAAK,CAACc,QAAQ,CAACH,eAAe,EAAEX,KAAK,CAACa,QAAQ,CAACZ,KAAK,CAACK,YAAY,CAAC,CAAC;QACvF;QACA,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;UACzBK,YAAY,EAAEK;QAChB,CAAC,CAAC;MACJ;IACF,KAAK,+BAA+B;MAClC,OAAOrB,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;QACzBO,yBAAyB,EAAE;MAC7B,CAAC,CAAC;IACJ,KAAK,kBAAkB;MACrB;QACE,IAAIP,KAAK,CAACc,UAAU,IAAI,IAAI,IAAIb,MAAM,CAACa,UAAU,IAAI,IAAI,IAAIf,KAAK,CAACgB,SAAS,CAACd,MAAM,CAACa,UAAU,EAAEd,KAAK,CAACc,UAAU,CAAC,EAAE;UACjH,OAAOd,KAAK;QACd;QACA,MAAMgB,eAAe,GAAGf,MAAM,CAACa,UAAU,IAAI,IAAI,IAAI,CAAChB,8BAA8B,IAAI,CAACC,KAAK,CAACkB,WAAW,CAACjB,KAAK,CAACK,YAAY,EAAEJ,MAAM,CAACa,UAAU,CAAC;QACjJ,OAAOzB,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;UACzBc,UAAU,EAAEb,MAAM,CAACa,UAAU;UAC7BP,yBAAyB,EAAES,eAAe,IAAI,CAACnB,gBAAgB,IAAI,CAACI,MAAM,CAACiB,8BAA8B;UACzGb,YAAY,EAAEW,eAAe,GAAGjB,KAAK,CAACoB,YAAY,CAAClB,MAAM,CAACa,UAAU,CAAC,GAAGd,KAAK,CAACK,YAAY;UAC1FF,cAAc,EAAEF,MAAM,CAACa,UAAU,IAAI,IAAI,IAAIf,KAAK,CAACqB,UAAU,CAACnB,MAAM,CAACa,UAAU,EAAEd,KAAK,CAACK,YAAY,CAAC,GAAG,MAAM,GAAG;QAClH,CAAC,CAAC;MACJ;IACF;MACE,MAAM,IAAIgB,KAAK,CAAC,iBAAiB,CAAC;EACtC;AACF,CAAC;AACD,OAAO,MAAMC,gBAAgB,GAAGC,MAAM,IAAI;EACxC,MAAM;IACJC,KAAK;IACLC,aAAa,EAAEC,iBAAiB;IAChCC,aAAa;IACbC,WAAW;IACX9B,8BAA8B,GAAG,KAAK;IACtC+B,OAAO;IACPC,OAAO;IACPC,aAAa;IACblC,gBAAgB;IAChBmC,iBAAiB;IACjBC;EACF,CAAC,GAAGV,MAAM;EACV,MAAMxB,KAAK,GAAGN,QAAQ,CAAC,CAAC;EACxB,MAAMyC,SAAS,GAAG5C,KAAK,CAAC6C,MAAM,CAACvC,0BAA0B,CAACwC,OAAO,CAACvC,gBAAgB,CAAC,EAAEC,8BAA8B,EAAEC,KAAK,CAAC,CAAC,CAACsC,OAAO;EACpI,MAAMZ,aAAa,GAAGnC,KAAK,CAACgD,OAAO,CAAC,MAAM;IACxC,OAAO5C,sBAAsB,CAAC6C,wBAAwB,CAAC;MACrDf,KAAK;MACLzB,KAAK;MACLkC,QAAQ;MACRO,KAAK,EAAEjB,MAAM;MACbE,aAAa,EAAEC,iBAAiB;MAChCe,WAAW,EAAE9C,wBAAwB,CAAC+C;IACxC,CAAC,CAAC;EACJ,CAAC;EACD;EACA;EACA,CAAChB,iBAAiB,EAAEO,QAAQ,CAAC,CAAC;EAC9B,MAAM,CAACU,aAAa,EAAEC,QAAQ,CAAC,GAAGtD,KAAK,CAACuD,UAAU,CAACX,SAAS,EAAE;IAC5D3B,yBAAyB,EAAE,KAAK;IAChCO,UAAU,EAAEW,aAAa;IACzBpB,YAAY,EAAEN,KAAK,CAACoB,YAAY,CAACM,aAAa,CAAC;IAC/CtB,cAAc,EAAE;EAClB,CAAC,CAAC;;EAEF;EACA;EACAb,KAAK,CAACwD,SAAS,CAAC,MAAM;IACpBF,QAAQ,CAAC;MACP1C,IAAI,EAAE,qBAAqB;MAC3BM,WAAW,EAAET,KAAK,CAACU,WAAW,CAACgB,aAAa;IAC9C,CAAC,CAAC;EACJ,CAAC,EAAE,CAACA,aAAa,EAAE1B,KAAK,CAAC,CAAC;EAC1B,MAAMgD,iBAAiB,GAAGzD,KAAK,CAAC0D,WAAW,CAACC,OAAO,IAAI;IACrDL,QAAQ,CAACvD,QAAQ,CAAC;MAChBa,IAAI,EAAE;IACR,CAAC,EAAE+C,OAAO,CAAC,CAAC;IACZ,IAAIlB,aAAa,EAAE;MACjBA,aAAa,CAACkB,OAAO,CAAC3C,QAAQ,CAAC;IACjC;EACF,CAAC,EAAE,CAACyB,aAAa,CAAC,CAAC;EACnB,MAAMmB,WAAW,GAAG5D,KAAK,CAAC0D,WAAW,CAACG,OAAO,IAAI;IAC/C,MAAMC,gBAAgB,GAAGD,OAAO;IAChC,IAAIpD,KAAK,CAACkB,WAAW,CAACmC,gBAAgB,EAAET,aAAa,CAACtC,YAAY,CAAC,EAAE;MACnE;IACF;IACA0C,iBAAiB,CAAC;MAChBzC,QAAQ,EAAEP,KAAK,CAACoB,YAAY,CAACiC,gBAAgB,CAAC;MAC9ChD,SAAS,EAAEL,KAAK,CAACqB,UAAU,CAACgC,gBAAgB,EAAET,aAAa,CAACtC,YAAY,CAAC,GAAG,MAAM,GAAG;IACvF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACsC,aAAa,CAACtC,YAAY,EAAE0C,iBAAiB,EAAEhD,KAAK,CAAC,CAAC;EAC1D,MAAMsD,cAAc,GAAG7D,iBAAiB,CAAC;IACvCwC,iBAAiB;IACjBF,OAAO;IACPD,OAAO;IACPF,aAAa;IACbC,WAAW;IACXK;EACF,CAAC,CAAC;EACF,MAAMqB,4BAA4B,GAAGhE,KAAK,CAAC0D,WAAW,CAAC,MAAM;IAC3DJ,QAAQ,CAAC;MACP1C,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,MAAMqD,gBAAgB,GAAGhE,gBAAgB,CAAC,CAACiE,cAAc,EAAEtC,8BAA8B,KAAK;IAC5F,IAAI,CAACmC,cAAc,CAACG,cAAc,CAAC,EAAE;MACnCZ,QAAQ,CAAC;QACP1C,IAAI,EAAE,kBAAkB;QACxBY,UAAU,EAAE0C,cAAc;QAC1BtC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAO;IACLO,aAAa;IACbkB,aAAa;IACbO,WAAW;IACXK,gBAAgB;IAChBF,cAAc;IACdC,4BAA4B;IAC5BP;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}