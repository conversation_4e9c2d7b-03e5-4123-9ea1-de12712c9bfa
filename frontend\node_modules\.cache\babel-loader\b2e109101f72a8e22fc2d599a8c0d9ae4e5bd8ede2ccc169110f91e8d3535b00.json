{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport DialogContent from '@mui/material/DialogContent';\nimport Fade from '@mui/material/Fade';\nimport MuiDialog, { dialogClasses } from '@mui/material/Dialog';\nimport { styled } from '@mui/material/styles';\nimport { DIALOG_WIDTH } from \"../constants/dimensions.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PickersModalDialogRoot = styled(MuiDialog)({\n  [`& .${dialogClasses.container}`]: {\n    outline: 0\n  },\n  [`& .${dialogClasses.paper}`]: {\n    outline: 0,\n    minWidth: DIALOG_WIDTH\n  }\n});\nconst PickersModalDialogContent = styled(DialogContent)({\n  '&:first-of-type': {\n    padding: 0\n  }\n});\nexport function PickersModalDialog(props) {\n  const {\n    children,\n    onDismiss,\n    open,\n    slots,\n    slotProps\n  } = props;\n  const Dialog = slots?.dialog ?? PickersModalDialogRoot;\n  const Transition = slots?.mobileTransition ?? Fade;\n  return /*#__PURE__*/_jsx(Dialog, _extends({\n    open: open,\n    onClose: onDismiss\n  }, slotProps?.dialog, {\n    TransitionComponent: Transition,\n    TransitionProps: slotProps?.mobileTransition,\n    PaperComponent: slots?.mobilePaper,\n    PaperProps: slotProps?.mobilePaper,\n    children: /*#__PURE__*/_jsx(PickersModalDialogContent, {\n      children: children\n    })\n  }));\n}", "map": {"version": 3, "names": ["_extends", "React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fade", "MuiDialog", "dialogClasses", "styled", "DIALOG_WIDTH", "jsx", "_jsx", "PickersModalDialogRoot", "container", "outline", "paper", "min<PERSON><PERSON><PERSON>", "PickersModalDialogContent", "padding", "PickersModalDialog", "props", "children", "on<PERSON><PERSON><PERSON>", "open", "slots", "slotProps", "Dialog", "dialog", "Transition", "mobileTransition", "onClose", "TransitionComponent", "TransitionProps", "PaperComponent", "mobilePaper", "PaperProps"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/components/PickersModalDialog.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport DialogContent from '@mui/material/DialogContent';\nimport Fade from '@mui/material/Fade';\nimport MuiDialog, { dialogClasses } from '@mui/material/Dialog';\nimport { styled } from '@mui/material/styles';\nimport { DIALOG_WIDTH } from \"../constants/dimensions.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst PickersModalDialogRoot = styled(MuiDialog)({\n  [`& .${dialogClasses.container}`]: {\n    outline: 0\n  },\n  [`& .${dialogClasses.paper}`]: {\n    outline: 0,\n    minWidth: DIALOG_WIDTH\n  }\n});\nconst PickersModalDialogContent = styled(DialogContent)({\n  '&:first-of-type': {\n    padding: 0\n  }\n});\nexport function PickersModalDialog(props) {\n  const {\n    children,\n    onDismiss,\n    open,\n    slots,\n    slotProps\n  } = props;\n  const Dialog = slots?.dialog ?? PickersModalDialogRoot;\n  const Transition = slots?.mobileTransition ?? Fade;\n  return /*#__PURE__*/_jsx(Dialog, _extends({\n    open: open,\n    onClose: onDismiss\n  }, slotProps?.dialog, {\n    TransitionComponent: Transition,\n    TransitionProps: slotProps?.mobileTransition,\n    PaperComponent: slots?.mobilePaper,\n    PaperProps: slotProps?.mobilePaper,\n    children: /*#__PURE__*/_jsx(PickersModalDialogContent, {\n      children: children\n    })\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,6BAA6B;AACvD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,SAAS,IAAIC,aAAa,QAAQ,sBAAsB;AAC/D,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,sBAAsB,GAAGJ,MAAM,CAACF,SAAS,CAAC,CAAC;EAC/C,CAAC,MAAMC,aAAa,CAACM,SAAS,EAAE,GAAG;IACjCC,OAAO,EAAE;EACX,CAAC;EACD,CAAC,MAAMP,aAAa,CAACQ,KAAK,EAAE,GAAG;IAC7BD,OAAO,EAAE,CAAC;IACVE,QAAQ,EAAEP;EACZ;AACF,CAAC,CAAC;AACF,MAAMQ,yBAAyB,GAAGT,MAAM,CAACJ,aAAa,CAAC,CAAC;EACtD,iBAAiB,EAAE;IACjBc,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,OAAO,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACxC,MAAM;IACJC,QAAQ;IACRC,SAAS;IACTC,IAAI;IACJC,KAAK;IACLC;EACF,CAAC,GAAGL,KAAK;EACT,MAAMM,MAAM,GAAGF,KAAK,EAAEG,MAAM,IAAIf,sBAAsB;EACtD,MAAMgB,UAAU,GAAGJ,KAAK,EAAEK,gBAAgB,IAAIxB,IAAI;EAClD,OAAO,aAAaM,IAAI,CAACe,MAAM,EAAExB,QAAQ,CAAC;IACxCqB,IAAI,EAAEA,IAAI;IACVO,OAAO,EAAER;EACX,CAAC,EAAEG,SAAS,EAAEE,MAAM,EAAE;IACpBI,mBAAmB,EAAEH,UAAU;IAC/BI,eAAe,EAAEP,SAAS,EAAEI,gBAAgB;IAC5CI,cAAc,EAAET,KAAK,EAAEU,WAAW;IAClCC,UAAU,EAAEV,SAAS,EAAES,WAAW;IAClCb,QAAQ,EAAE,aAAaV,IAAI,CAACM,yBAAyB,EAAE;MACrDI,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}