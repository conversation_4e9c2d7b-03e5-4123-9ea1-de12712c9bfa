{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport Arrays from '../../util/Arrays';\nimport * as C from './EncoderConstants';\nexport function static_SHIFT_TABLE(SHIFT_TABLE) {\n  var e_1, _a;\n  try {\n    for (var SHIFT_TABLE_1 = __values(SHIFT_TABLE), SHIFT_TABLE_1_1 = SHIFT_TABLE_1.next(); !SHIFT_TABLE_1_1.done; SHIFT_TABLE_1_1 = SHIFT_TABLE_1.next()) {\n      var table = SHIFT_TABLE_1_1.value /*Int32Array*/;\n      Arrays.fill(table, -1);\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (SHIFT_TABLE_1_1 && !SHIFT_TABLE_1_1.done && (_a = SHIFT_TABLE_1.return)) _a.call(SHIFT_TABLE_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  SHIFT_TABLE[C.MODE_UPPER][C.MODE_PUNCT] = 0;\n  SHIFT_TABLE[C.MODE_LOWER][C.MODE_PUNCT] = 0;\n  SHIFT_TABLE[C.MODE_LOWER][C.MODE_UPPER] = 28;\n  SHIFT_TABLE[C.MODE_MIXED][C.MODE_PUNCT] = 0;\n  SHIFT_TABLE[C.MODE_DIGIT][C.MODE_PUNCT] = 0;\n  SHIFT_TABLE[C.MODE_DIGIT][C.MODE_UPPER] = 15;\n  return SHIFT_TABLE;\n}\nexport var /*final*/SHIFT_TABLE = static_SHIFT_TABLE(Arrays.createInt32Array(6, 6)); // mode shift codes, per table", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "<PERSON><PERSON><PERSON>", "C", "static_SHIFT_TABLE", "SHIFT_TABLE", "e_1", "_a", "SHIFT_TABLE_1", "SHIFT_TABLE_1_1", "table", "fill", "e_1_1", "error", "return", "MODE_UPPER", "MODE_PUNCT", "MODE_LOWER", "MODE_MIXED", "MODE_DIGIT", "createInt32Array"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/ShiftTable.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport Arrays from '../../util/Arrays';\nimport * as C from './EncoderConstants';\nexport function static_SHIFT_TABLE(SHIFT_TABLE) {\n    var e_1, _a;\n    try {\n        for (var SHIFT_TABLE_1 = __values(SHIFT_TABLE), SHIFT_TABLE_1_1 = SHIFT_TABLE_1.next(); !SHIFT_TABLE_1_1.done; SHIFT_TABLE_1_1 = SHIFT_TABLE_1.next()) {\n            var table = SHIFT_TABLE_1_1.value /*Int32Array*/;\n            Arrays.fill(table, -1);\n        }\n    }\n    catch (e_1_1) { e_1 = { error: e_1_1 }; }\n    finally {\n        try {\n            if (SHIFT_TABLE_1_1 && !SHIFT_TABLE_1_1.done && (_a = SHIFT_TABLE_1.return)) _a.call(SHIFT_TABLE_1);\n        }\n        finally { if (e_1) throw e_1.error; }\n    }\n    SHIFT_TABLE[C.MODE_UPPER][C.MODE_PUNCT] = 0;\n    SHIFT_TABLE[C.MODE_LOWER][C.MODE_PUNCT] = 0;\n    SHIFT_TABLE[C.MODE_LOWER][C.MODE_UPPER] = 28;\n    SHIFT_TABLE[C.MODE_MIXED][C.MODE_PUNCT] = 0;\n    SHIFT_TABLE[C.MODE_DIGIT][C.MODE_PUNCT] = 0;\n    SHIFT_TABLE[C.MODE_DIGIT][C.MODE_UPPER] = 15;\n    return SHIFT_TABLE;\n}\nexport var /*final*/ SHIFT_TABLE = static_SHIFT_TABLE(Arrays.createInt32Array(6, 6)); // mode shift codes, per table\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,MAAM,MAAM,mBAAmB;AACtC,OAAO,KAAKC,CAAC,MAAM,oBAAoB;AACvC,OAAO,SAASC,kBAAkBA,CAACC,WAAW,EAAE;EAC5C,IAAIC,GAAG,EAAEC,EAAE;EACX,IAAI;IACA,KAAK,IAAIC,aAAa,GAAGnB,QAAQ,CAACgB,WAAW,CAAC,EAAEI,eAAe,GAAGD,aAAa,CAACV,IAAI,CAAC,CAAC,EAAE,CAACW,eAAe,CAACT,IAAI,EAAES,eAAe,GAAGD,aAAa,CAACV,IAAI,CAAC,CAAC,EAAE;MACnJ,IAAIY,KAAK,GAAGD,eAAe,CAACV,KAAK,CAAC;MAClCG,MAAM,CAACS,IAAI,CAACD,KAAK,EAAE,CAAC,CAAC,CAAC;IAC1B;EACJ,CAAC,CACD,OAAOE,KAAK,EAAE;IAAEN,GAAG,GAAG;MAAEO,KAAK,EAAED;IAAM,CAAC;EAAE,CAAC,SACjC;IACJ,IAAI;MACA,IAAIH,eAAe,IAAI,CAACA,eAAe,CAACT,IAAI,KAAKO,EAAE,GAAGC,aAAa,CAACM,MAAM,CAAC,EAAEP,EAAE,CAACX,IAAI,CAACY,aAAa,CAAC;IACvG,CAAC,SACO;MAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACO,KAAK;IAAE;EACxC;EACAR,WAAW,CAACF,CAAC,CAACY,UAAU,CAAC,CAACZ,CAAC,CAACa,UAAU,CAAC,GAAG,CAAC;EAC3CX,WAAW,CAACF,CAAC,CAACc,UAAU,CAAC,CAACd,CAAC,CAACa,UAAU,CAAC,GAAG,CAAC;EAC3CX,WAAW,CAACF,CAAC,CAACc,UAAU,CAAC,CAACd,CAAC,CAACY,UAAU,CAAC,GAAG,EAAE;EAC5CV,WAAW,CAACF,CAAC,CAACe,UAAU,CAAC,CAACf,CAAC,CAACa,UAAU,CAAC,GAAG,CAAC;EAC3CX,WAAW,CAACF,CAAC,CAACgB,UAAU,CAAC,CAAChB,CAAC,CAACa,UAAU,CAAC,GAAG,CAAC;EAC3CX,WAAW,CAACF,CAAC,CAACgB,UAAU,CAAC,CAAChB,CAAC,CAACY,UAAU,CAAC,GAAG,EAAE;EAC5C,OAAOV,WAAW;AACtB;AACA,OAAO,IAAI,SAAUA,WAAW,GAAGD,kBAAkB,CAACF,MAAM,CAACkB,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}