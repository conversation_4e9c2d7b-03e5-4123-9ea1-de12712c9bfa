{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.List;*/\n/**\n * <p>Encapsulates the result of decoding a matrix of bits. This typically\n * applies to 2D barcode formats. For now it contains the raw bytes obtained,\n * as well as a String interpretation of those bytes, if applicable.</p>\n *\n * <AUTHOR>\n */\nvar DecoderResult = /** @class */function () {\n  // public constructor(rawBytes: Uint8Array,\n  //                      text: string,\n  //                      List<Uint8Array> byteSegments,\n  //                      String ecLevel) {\n  //   this(rawBytes, text, byteSegments, ecLevel, -1, -1)\n  // }\n  function DecoderResult(rawBytes, text, byteSegments, ecLevel, structuredAppendSequenceNumber, structuredAppendParity) {\n    if (structuredAppendSequenceNumber === void 0) {\n      structuredAppendSequenceNumber = -1;\n    }\n    if (structuredAppendParity === void 0) {\n      structuredAppendParity = -1;\n    }\n    this.rawBytes = rawBytes;\n    this.text = text;\n    this.byteSegments = byteSegments;\n    this.ecLevel = ecLevel;\n    this.structuredAppendSequenceNumber = structuredAppendSequenceNumber;\n    this.structuredAppendParity = structuredAppendParity;\n    this.numBits = rawBytes === undefined || rawBytes === null ? 0 : 8 * rawBytes.length;\n  }\n  /**\n   * @return raw bytes representing the result, or {@code null} if not applicable\n   */\n  DecoderResult.prototype.getRawBytes = function () {\n    return this.rawBytes;\n  };\n  /**\n   * @return how many bits of {@link #getRawBytes()} are valid; typically 8 times its length\n   * @since 3.3.0\n   */\n  DecoderResult.prototype.getNumBits = function () {\n    return this.numBits;\n  };\n  /**\n   * @param numBits overrides the number of bits that are valid in {@link #getRawBytes()}\n   * @since 3.3.0\n   */\n  DecoderResult.prototype.setNumBits = function (numBits /*int*/) {\n    this.numBits = numBits;\n  };\n  /**\n   * @return text representation of the result\n   */\n  DecoderResult.prototype.getText = function () {\n    return this.text;\n  };\n  /**\n   * @return list of byte segments in the result, or {@code null} if not applicable\n   */\n  DecoderResult.prototype.getByteSegments = function () {\n    return this.byteSegments;\n  };\n  /**\n   * @return name of error correction level used, or {@code null} if not applicable\n   */\n  DecoderResult.prototype.getECLevel = function () {\n    return this.ecLevel;\n  };\n  /**\n   * @return number of errors corrected, or {@code null} if not applicable\n   */\n  DecoderResult.prototype.getErrorsCorrected = function () {\n    return this.errorsCorrected;\n  };\n  DecoderResult.prototype.setErrorsCorrected = function (errorsCorrected /*Integer*/) {\n    this.errorsCorrected = errorsCorrected;\n  };\n  /**\n   * @return number of erasures corrected, or {@code null} if not applicable\n   */\n  DecoderResult.prototype.getErasures = function () {\n    return this.erasures;\n  };\n  DecoderResult.prototype.setErasures = function (erasures /*Integer*/) {\n    this.erasures = erasures;\n  };\n  /**\n   * @return arbitrary additional metadata\n   */\n  DecoderResult.prototype.getOther = function () {\n    return this.other;\n  };\n  DecoderResult.prototype.setOther = function (other) {\n    this.other = other;\n  };\n  DecoderResult.prototype.hasStructuredAppend = function () {\n    return this.structuredAppendParity >= 0 && this.structuredAppendSequenceNumber >= 0;\n  };\n  DecoderResult.prototype.getStructuredAppendParity = function () {\n    return this.structuredAppendParity;\n  };\n  DecoderResult.prototype.getStructuredAppendSequenceNumber = function () {\n    return this.structuredAppendSequenceNumber;\n  };\n  return DecoderResult;\n}();\nexport default DecoderResult;", "map": {"version": 3, "names": ["DecoderResult", "rawBytes", "text", "byteSegments", "ecLevel", "structuredAppendSequenceNumber", "structuredAppendParity", "numBits", "undefined", "length", "prototype", "getRawBytes", "getNumBits", "setNumBits", "getText", "getByteSegments", "getECLevel", "getErrorsCorrected", "errorsCorrected", "setErrorsCorrected", "getErasures", "erasures", "setErasures", "getOther", "other", "setOther", "hasStructuredAppend", "getStructuredAppendParity", "getStructuredAppendSequenceNumber"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/DecoderResult.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.List;*/\n/**\n * <p>Encapsulates the result of decoding a matrix of bits. This typically\n * applies to 2D barcode formats. For now it contains the raw bytes obtained,\n * as well as a String interpretation of those bytes, if applicable.</p>\n *\n * <AUTHOR>\n */\nvar DecoderResult = /** @class */ (function () {\n    // public constructor(rawBytes: Uint8Array,\n    //                      text: string,\n    //                      List<Uint8Array> byteSegments,\n    //                      String ecLevel) {\n    //   this(rawBytes, text, byteSegments, ecLevel, -1, -1)\n    // }\n    function DecoderResult(rawBytes, text, byteSegments, ecLevel, structuredAppendSequenceNumber, structuredAppendParity) {\n        if (structuredAppendSequenceNumber === void 0) { structuredAppendSequenceNumber = -1; }\n        if (structuredAppendParity === void 0) { structuredAppendParity = -1; }\n        this.rawBytes = rawBytes;\n        this.text = text;\n        this.byteSegments = byteSegments;\n        this.ecLevel = ecLevel;\n        this.structuredAppendSequenceNumber = structuredAppendSequenceNumber;\n        this.structuredAppendParity = structuredAppendParity;\n        this.numBits = (rawBytes === undefined || rawBytes === null) ? 0 : 8 * rawBytes.length;\n    }\n    /**\n     * @return raw bytes representing the result, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getRawBytes = function () {\n        return this.rawBytes;\n    };\n    /**\n     * @return how many bits of {@link #getRawBytes()} are valid; typically 8 times its length\n     * @since 3.3.0\n     */\n    DecoderResult.prototype.getNumBits = function () {\n        return this.numBits;\n    };\n    /**\n     * @param numBits overrides the number of bits that are valid in {@link #getRawBytes()}\n     * @since 3.3.0\n     */\n    DecoderResult.prototype.setNumBits = function (numBits /*int*/) {\n        this.numBits = numBits;\n    };\n    /**\n     * @return text representation of the result\n     */\n    DecoderResult.prototype.getText = function () {\n        return this.text;\n    };\n    /**\n     * @return list of byte segments in the result, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getByteSegments = function () {\n        return this.byteSegments;\n    };\n    /**\n     * @return name of error correction level used, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getECLevel = function () {\n        return this.ecLevel;\n    };\n    /**\n     * @return number of errors corrected, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getErrorsCorrected = function () {\n        return this.errorsCorrected;\n    };\n    DecoderResult.prototype.setErrorsCorrected = function (errorsCorrected /*Integer*/) {\n        this.errorsCorrected = errorsCorrected;\n    };\n    /**\n     * @return number of erasures corrected, or {@code null} if not applicable\n     */\n    DecoderResult.prototype.getErasures = function () {\n        return this.erasures;\n    };\n    DecoderResult.prototype.setErasures = function (erasures /*Integer*/) {\n        this.erasures = erasures;\n    };\n    /**\n     * @return arbitrary additional metadata\n     */\n    DecoderResult.prototype.getOther = function () {\n        return this.other;\n    };\n    DecoderResult.prototype.setOther = function (other) {\n        this.other = other;\n    };\n    DecoderResult.prototype.hasStructuredAppend = function () {\n        return this.structuredAppendParity >= 0 && this.structuredAppendSequenceNumber >= 0;\n    };\n    DecoderResult.prototype.getStructuredAppendParity = function () {\n        return this.structuredAppendParity;\n    };\n    DecoderResult.prototype.getStructuredAppendSequenceNumber = function () {\n        return this.structuredAppendSequenceNumber;\n    };\n    return DecoderResult;\n}());\nexport default DecoderResult;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,aAAa,GAAG,aAAe,YAAY;EAC3C;EACA;EACA;EACA;EACA;EACA;EACA,SAASA,aAAaA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,YAAY,EAAEC,OAAO,EAAEC,8BAA8B,EAAEC,sBAAsB,EAAE;IAClH,IAAID,8BAA8B,KAAK,KAAK,CAAC,EAAE;MAAEA,8BAA8B,GAAG,CAAC,CAAC;IAAE;IACtF,IAAIC,sBAAsB,KAAK,KAAK,CAAC,EAAE;MAAEA,sBAAsB,GAAG,CAAC,CAAC;IAAE;IACtE,IAAI,CAACL,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,8BAA8B,GAAGA,8BAA8B;IACpE,IAAI,CAACC,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,OAAO,GAAIN,QAAQ,KAAKO,SAAS,IAAIP,QAAQ,KAAK,IAAI,GAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,CAACQ,MAAM;EAC1F;EACA;AACJ;AACA;EACIT,aAAa,CAACU,SAAS,CAACC,WAAW,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACV,QAAQ;EACxB,CAAC;EACD;AACJ;AACA;AACA;EACID,aAAa,CAACU,SAAS,CAACE,UAAU,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACL,OAAO;EACvB,CAAC;EACD;AACJ;AACA;AACA;EACIP,aAAa,CAACU,SAAS,CAACG,UAAU,GAAG,UAAUN,OAAO,CAAC,SAAS;IAC5D,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B,CAAC;EACD;AACJ;AACA;EACIP,aAAa,CAACU,SAAS,CAACI,OAAO,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACZ,IAAI;EACpB,CAAC;EACD;AACJ;AACA;EACIF,aAAa,CAACU,SAAS,CAACK,eAAe,GAAG,YAAY;IAClD,OAAO,IAAI,CAACZ,YAAY;EAC5B,CAAC;EACD;AACJ;AACA;EACIH,aAAa,CAACU,SAAS,CAACM,UAAU,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACZ,OAAO;EACvB,CAAC;EACD;AACJ;AACA;EACIJ,aAAa,CAACU,SAAS,CAACO,kBAAkB,GAAG,YAAY;IACrD,OAAO,IAAI,CAACC,eAAe;EAC/B,CAAC;EACDlB,aAAa,CAACU,SAAS,CAACS,kBAAkB,GAAG,UAAUD,eAAe,CAAC,aAAa;IAChF,IAAI,CAACA,eAAe,GAAGA,eAAe;EAC1C,CAAC;EACD;AACJ;AACA;EACIlB,aAAa,CAACU,SAAS,CAACU,WAAW,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACC,QAAQ;EACxB,CAAC;EACDrB,aAAa,CAACU,SAAS,CAACY,WAAW,GAAG,UAAUD,QAAQ,CAAC,aAAa;IAClE,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B,CAAC;EACD;AACJ;AACA;EACIrB,aAAa,CAACU,SAAS,CAACa,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACC,KAAK;EACrB,CAAC;EACDxB,aAAa,CAACU,SAAS,CAACe,QAAQ,GAAG,UAAUD,KAAK,EAAE;IAChD,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB,CAAC;EACDxB,aAAa,CAACU,SAAS,CAACgB,mBAAmB,GAAG,YAAY;IACtD,OAAO,IAAI,CAACpB,sBAAsB,IAAI,CAAC,IAAI,IAAI,CAACD,8BAA8B,IAAI,CAAC;EACvF,CAAC;EACDL,aAAa,CAACU,SAAS,CAACiB,yBAAyB,GAAG,YAAY;IAC5D,OAAO,IAAI,CAACrB,sBAAsB;EACtC,CAAC;EACDN,aAAa,CAACU,SAAS,CAACkB,iCAAiC,GAAG,YAAY;IACpE,OAAO,IAAI,CAACvB,8BAA8B;EAC9C,CAAC;EACD,OAAOL,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}