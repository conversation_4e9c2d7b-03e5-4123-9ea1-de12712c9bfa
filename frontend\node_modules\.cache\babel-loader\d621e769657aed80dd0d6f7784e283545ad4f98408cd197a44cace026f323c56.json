{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = this && this.__read || function (o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o),\n    r,\n    ar = [],\n    e;\n  try {\n    while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  } catch (error) {\n    e = {\n      error: error\n    };\n  } finally {\n    try {\n      if (r && !r.done && (m = i[\"return\"])) m.call(i);\n    } finally {\n      if (e) throw e.error;\n    }\n  }\n  return ar;\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\n// import java.util.ArrayList;\n// import java.util.Collection;\n// import java.util.HashMap;\n// import java.util.Map;\n// import java.util.Map.Entry;\n/**\n * <AUTHOR> Grau\n */\nvar BarcodeValue = /** @class */function () {\n  function BarcodeValue() {\n    this.values = new Map();\n  }\n  /**\n   * Add an occurrence of a value\n   */\n  BarcodeValue.prototype.setValue = function (value) {\n    value = Math.trunc(value);\n    var confidence = this.values.get(value);\n    if (confidence == null) {\n      confidence = 0;\n    }\n    confidence++;\n    this.values.set(value, confidence);\n  };\n  /**\n   * Determines the maximum occurrence of a set value and returns all values which were set with this occurrence.\n   * @return an array of int, containing the values with the highest occurrence, or null, if no value was set\n   */\n  BarcodeValue.prototype.getValue = function () {\n    var e_1, _a;\n    var maxConfidence = -1;\n    var result = new Array();\n    var _loop_1 = function (key, value) {\n      var entry = {\n        getKey: function () {\n          return key;\n        },\n        getValue: function () {\n          return value;\n        }\n      };\n      if (entry.getValue() > maxConfidence) {\n        maxConfidence = entry.getValue();\n        result = [];\n        result.push(entry.getKey());\n      } else if (entry.getValue() === maxConfidence) {\n        result.push(entry.getKey());\n      }\n    };\n    try {\n      for (var _b = __values(this.values.entries()), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var _d = __read(_c.value, 2),\n          key = _d[0],\n          value = _d[1];\n        _loop_1(key, value);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    return PDF417Common.toIntArray(result);\n  };\n  BarcodeValue.prototype.getConfidence = function (value) {\n    return this.values.get(value);\n  };\n  return BarcodeValue;\n}();\nexport default BarcodeValue;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "__read", "n", "r", "ar", "e", "push", "error", "PDF417<PERSON><PERSON><PERSON>", "BarcodeValue", "values", "Map", "prototype", "setValue", "Math", "trunc", "confidence", "get", "set", "getValue", "e_1", "_a", "maxConfidence", "result", "Array", "_loop_1", "key", "entry", "<PERSON><PERSON><PERSON>", "_b", "entries", "_c", "_d", "e_1_1", "return", "toIntArray", "getConfidence"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/BarcodeValue.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\n// import java.util.ArrayList;\n// import java.util.Collection;\n// import java.util.HashMap;\n// import java.util.Map;\n// import java.util.Map.Entry;\n/**\n * <AUTHOR> Grau\n */\nvar BarcodeValue = /** @class */ (function () {\n    function BarcodeValue() {\n        this.values = new Map();\n    }\n    /**\n     * Add an occurrence of a value\n     */\n    BarcodeValue.prototype.setValue = function (value) {\n        value = Math.trunc(value);\n        var confidence = this.values.get(value);\n        if (confidence == null) {\n            confidence = 0;\n        }\n        confidence++;\n        this.values.set(value, confidence);\n    };\n    /**\n     * Determines the maximum occurrence of a set value and returns all values which were set with this occurrence.\n     * @return an array of int, containing the values with the highest occurrence, or null, if no value was set\n     */\n    BarcodeValue.prototype.getValue = function () {\n        var e_1, _a;\n        var maxConfidence = -1;\n        var result = new Array();\n        var _loop_1 = function (key, value) {\n            var entry = {\n                getKey: function () { return key; },\n                getValue: function () { return value; },\n            };\n            if (entry.getValue() > maxConfidence) {\n                maxConfidence = entry.getValue();\n                result = [];\n                result.push(entry.getKey());\n            }\n            else if (entry.getValue() === maxConfidence) {\n                result.push(entry.getKey());\n            }\n        };\n        try {\n            for (var _b = __values(this.values.entries()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var _d = __read(_c.value, 2), key = _d[0], value = _d[1];\n                _loop_1(key, value);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return PDF417Common.toIntArray(result);\n    };\n    BarcodeValue.prototype.getConfidence = function (value) {\n        return this.values.get(value);\n    };\n    return BarcodeValue;\n}());\nexport default BarcodeValue;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,IAAIW,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUZ,CAAC,EAAEa,CAAC,EAAE;EAClD,IAAIT,CAAC,GAAG,OAAOF,MAAM,KAAK,UAAU,IAAIF,CAAC,CAACE,MAAM,CAACC,QAAQ,CAAC;EAC1D,IAAI,CAACC,CAAC,EAAE,OAAOJ,CAAC;EAChB,IAAIK,CAAC,GAAGD,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;IAAEc,CAAC;IAAEC,EAAE,GAAG,EAAE;IAAEC,CAAC;EAChC,IAAI;IACA,OAAO,CAACH,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAACC,CAAC,GAAGT,CAAC,CAACG,IAAI,CAAC,CAAC,EAAEE,IAAI,EAAEK,EAAE,CAACE,IAAI,CAACH,CAAC,CAACL,KAAK,CAAC;EAC9E,CAAC,CACD,OAAOS,KAAK,EAAE;IAAEF,CAAC,GAAG;MAAEE,KAAK,EAAEA;IAAM,CAAC;EAAE,CAAC,SAC/B;IACJ,IAAI;MACA,IAAIJ,CAAC,IAAI,CAACA,CAAC,CAACJ,IAAI,KAAKN,CAAC,GAAGC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAED,CAAC,CAACE,IAAI,CAACD,CAAC,CAAC;IACpD,CAAC,SACO;MAAE,IAAIW,CAAC,EAAE,MAAMA,CAAC,CAACE,KAAK;IAAE;EACpC;EACA,OAAOH,EAAE;AACb,CAAC;AACD;AACA;AACA,OAAOI,YAAY,MAAM,iBAAiB;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG;IACpB,IAAI,CAACC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC3B;EACA;AACJ;AACA;EACIF,YAAY,CAACG,SAAS,CAACC,QAAQ,GAAG,UAAUf,KAAK,EAAE;IAC/CA,KAAK,GAAGgB,IAAI,CAACC,KAAK,CAACjB,KAAK,CAAC;IACzB,IAAIkB,UAAU,GAAG,IAAI,CAACN,MAAM,CAACO,GAAG,CAACnB,KAAK,CAAC;IACvC,IAAIkB,UAAU,IAAI,IAAI,EAAE;MACpBA,UAAU,GAAG,CAAC;IAClB;IACAA,UAAU,EAAE;IACZ,IAAI,CAACN,MAAM,CAACQ,GAAG,CAACpB,KAAK,EAAEkB,UAAU,CAAC;EACtC,CAAC;EACD;AACJ;AACA;AACA;EACIP,YAAY,CAACG,SAAS,CAACO,QAAQ,GAAG,YAAY;IAC1C,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIC,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC;IACxB,IAAIC,OAAO,GAAG,SAAAA,CAAUC,GAAG,EAAE5B,KAAK,EAAE;MAChC,IAAI6B,KAAK,GAAG;QACRC,MAAM,EAAE,SAAAA,CAAA,EAAY;UAAE,OAAOF,GAAG;QAAE,CAAC;QACnCP,QAAQ,EAAE,SAAAA,CAAA,EAAY;UAAE,OAAOrB,KAAK;QAAE;MAC1C,CAAC;MACD,IAAI6B,KAAK,CAACR,QAAQ,CAAC,CAAC,GAAGG,aAAa,EAAE;QAClCA,aAAa,GAAGK,KAAK,CAACR,QAAQ,CAAC,CAAC;QAChCI,MAAM,GAAG,EAAE;QACXA,MAAM,CAACjB,IAAI,CAACqB,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC;MAC/B,CAAC,MACI,IAAID,KAAK,CAACR,QAAQ,CAAC,CAAC,KAAKG,aAAa,EAAE;QACzCC,MAAM,CAACjB,IAAI,CAACqB,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC;MAC/B;IACJ,CAAC;IACD,IAAI;MACA,KAAK,IAAIC,EAAE,GAAGzC,QAAQ,CAAC,IAAI,CAACsB,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC,EAAEC,EAAE,GAAGF,EAAE,CAAChC,IAAI,CAAC,CAAC,EAAE,CAACkC,EAAE,CAAChC,IAAI,EAAEgC,EAAE,GAAGF,EAAE,CAAChC,IAAI,CAAC,CAAC,EAAE;QACrF,IAAImC,EAAE,GAAG/B,MAAM,CAAC8B,EAAE,CAACjC,KAAK,EAAE,CAAC,CAAC;UAAE4B,GAAG,GAAGM,EAAE,CAAC,CAAC,CAAC;UAAElC,KAAK,GAAGkC,EAAE,CAAC,CAAC,CAAC;QACxDP,OAAO,CAACC,GAAG,EAAE5B,KAAK,CAAC;MACvB;IACJ,CAAC,CACD,OAAOmC,KAAK,EAAE;MAAEb,GAAG,GAAG;QAAEb,KAAK,EAAE0B;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,EAAE,IAAI,CAACA,EAAE,CAAChC,IAAI,KAAKsB,EAAE,GAAGQ,EAAE,CAACK,MAAM,CAAC,EAAEb,EAAE,CAAC1B,IAAI,CAACkC,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIT,GAAG,EAAE,MAAMA,GAAG,CAACb,KAAK;MAAE;IACxC;IACA,OAAOC,YAAY,CAAC2B,UAAU,CAACZ,MAAM,CAAC;EAC1C,CAAC;EACDd,YAAY,CAACG,SAAS,CAACwB,aAAa,GAAG,UAAUtC,KAAK,EAAE;IACpD,OAAO,IAAI,CAACY,MAAM,CAACO,GAAG,CAACnB,KAAK,CAAC;EACjC,CAAC;EACD,OAAOW,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}