{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _constants = require('./constants');\nvar _encoder = require('./encoder');\nvar _encoder2 = _interopRequireDefault(_encoder);\nvar _Barcode2 = require('../Barcode');\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n} // Encoding documentation:\n// https://en.wikipedia.org/wiki/EAN_2#Encoding\n\nvar EAN2 = function (_Barcode) {\n  _inherits(EAN2, _Barcode);\n  function EAN2(data, options) {\n    _classCallCheck(this, EAN2);\n    return _possibleConstructorReturn(this, (EAN2.__proto__ || Object.getPrototypeOf(EAN2)).call(this, data, options));\n  }\n  _createClass(EAN2, [{\n    key: 'valid',\n    value: function valid() {\n      return this.data.search(/^[0-9]{2}$/) !== -1;\n    }\n  }, {\n    key: 'encode',\n    value: function encode() {\n      // Choose the structure based on the number mod 4\n      var structure = _constants.EAN2_STRUCTURE[parseInt(this.data) % 4];\n      return {\n        // Start bits + Encode the two digits with 01 in between\n        data: '1011' + (0, _encoder2.default)(this.data, structure, '01'),\n        text: this.text\n      };\n    }\n  }]);\n  return EAN2;\n}(_Barcode3.default);\nexports.default = EAN2;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_constants", "require", "_encoder", "_encoder2", "_interopRequireDefault", "_Barcode2", "_Barcode3", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "call", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "EAN2", "_Barcode", "data", "options", "getPrototypeOf", "valid", "search", "encode", "structure", "EAN2_STRUCTURE", "parseInt", "text"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN2.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _constants = require('./constants');\n\nvar _encoder = require('./encoder');\n\nvar _encoder2 = _interopRequireDefault(_encoder);\n\nvar _Barcode2 = require('../Barcode');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation:\n// https://en.wikipedia.org/wiki/EAN_2#Encoding\n\nvar EAN2 = function (_Barcode) {\n\t_inherits(EAN2, _Barcode);\n\n\tfunction EAN2(data, options) {\n\t\t_classCallCheck(this, EAN2);\n\n\t\treturn _possibleConstructorReturn(this, (EAN2.__proto__ || Object.getPrototypeOf(EAN2)).call(this, data, options));\n\t}\n\n\t_createClass(EAN2, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]{2}$/) !== -1;\n\t\t}\n\t}, {\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\t// Choose the structure based on the number mod 4\n\t\t\tvar structure = _constants.EAN2_STRUCTURE[parseInt(this.data) % 4];\n\t\t\treturn {\n\t\t\t\t// Start bits + Encode the two digits with 01 in between\n\t\t\t\tdata: '1011' + (0, _encoder2.default)(this.data, structure, '01'),\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}]);\n\n\treturn EAN2;\n}(_Barcode3.default);\n\nexports.default = EAN2;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEvC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEnC,IAAIE,SAAS,GAAGC,sBAAsB,CAACF,QAAQ,CAAC;AAEhD,IAAIG,SAAS,GAAGJ,OAAO,CAAC,YAAY,CAAC;AAErC,IAAIK,SAAS,GAAGF,sBAAsB,CAACC,SAAS,CAAC;AAEjD,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEf,WAAW,EAAE;EAAE,IAAI,EAAEe,QAAQ,YAAYf,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIgB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAI,CAACD,IAAI,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGD,IAAI;AAAE;AAE/O,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIP,SAAS,CAAC,0DAA0D,GAAG,OAAOO,UAAU,CAAC;EAAE;EAAED,QAAQ,CAACnB,SAAS,GAAGlB,MAAM,CAACuC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACpB,SAAS,EAAE;IAAEsB,WAAW,EAAE;MAAErC,KAAK,EAAEkC,QAAQ;MAAE1B,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAI0B,UAAU,EAAEtC,MAAM,CAACyC,cAAc,GAAGzC,MAAM,CAACyC,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AAAE,CAAC,CAAC;AAC/e;;AAEA,IAAIK,IAAI,GAAG,UAAUC,QAAQ,EAAE;EAC9BR,SAAS,CAACO,IAAI,EAAEC,QAAQ,CAAC;EAEzB,SAASD,IAAIA,CAACE,IAAI,EAAEC,OAAO,EAAE;IAC5BjB,eAAe,CAAC,IAAI,EAAEc,IAAI,CAAC;IAE3B,OAAOX,0BAA0B,CAAC,IAAI,EAAE,CAACW,IAAI,CAACD,SAAS,IAAI1C,MAAM,CAAC+C,cAAc,CAACJ,IAAI,CAAC,EAAET,IAAI,CAAC,IAAI,EAAEW,IAAI,EAAEC,OAAO,CAAC,CAAC;EACnH;EAEA1C,YAAY,CAACuC,IAAI,EAAE,CAAC;IACnB7B,GAAG,EAAE,OAAO;IACZX,KAAK,EAAE,SAAS6C,KAAKA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACH,IAAI,CAACI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IAC7C;EACD,CAAC,EAAE;IACFnC,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAAS+C,MAAMA,CAAA,EAAG;MACxB;MACA,IAAIC,SAAS,GAAGhC,UAAU,CAACiC,cAAc,CAACC,QAAQ,CAAC,IAAI,CAACR,IAAI,CAAC,GAAG,CAAC,CAAC;MAClE,OAAO;QACN;QACAA,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,EAAEvB,SAAS,CAACM,OAAO,EAAE,IAAI,CAACiB,IAAI,EAAEM,SAAS,EAAE,IAAI,CAAC;QACjEG,IAAI,EAAE,IAAI,CAACA;MACZ,CAAC;IACF;EACD,CAAC,CAAC,CAAC;EAEH,OAAOX,IAAI;AACZ,CAAC,CAAClB,SAAS,CAACG,OAAO,CAAC;AAEpB1B,OAAO,CAAC0B,OAAO,GAAGe,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}