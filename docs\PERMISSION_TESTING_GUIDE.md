# Permission Testing Guide

## Overview
This guide helps you test the newly implemented permission restrictions for Barcode Generator and Accounting Management features.

## Test Setup

### 1. Create Test User Without Permissions
1. Go to `/roles` and create a new role called "Limited User"
2. **DO NOT** assign the following permissions:
   - `barcode.view`
   - `accounting.view`
   - `chart_of_accounts.view`
   - `ledger.view`
   - `subsidiary_ledger.view`
3. Go to `/users` and create a test user with the "Limited User" role
4. Logout and login with the test user

### 2. Create Test User With Partial Permissions
1. Create another role called "Partial Access User"
2. Assign only `barcode.view` permission (but not accounting permissions)
3. Create a test user with this role

## Test Scenarios

### ✅ Test 1: Navigation Menu Restrictions

#### Expected Behavior for Users Without Permissions:
1. **Hover over "Barcode Generator" menu item**:
   - Should show tooltip: "You don't have permission to view this information. Please contact your administrator if you need this access."
   - Menu item should have lock icon and reduced opacity

2. **Click on "Barcode Generator" menu item**:
   - Should show snackbar notification with permission message
   - Should NOT navigate to the barcode generator page

3. **Hover over "Accounting" menu item**:
   - Should show same tooltip message
   - Should have lock icon and disabled styling

4. **Click on "Accounting" menu item**:
   - Should show snackbar notification
   - Should NOT navigate to accounting page

5. **Hover over Accounting sub-menu items** (Chart of Accounts, General Ledger, etc.):
   - All should show tooltips and lock icons
   - All should be visually disabled

### ✅ Test 2: Direct URL Access

#### Test URLs to try:
- `http://localhost:3000/barcode-generator`
- `http://localhost:3000/accounting`
- `http://localhost:3000/accounting/chart-of-accounts`
- `http://localhost:3000/accounting/general-ledger`
- `http://localhost:3000/accounting/subsidiary-ledgers`

#### Expected Behavior:
- Should show professional access denied page with:
  - Lock icon
  - Clear message about restricted access
  - "Go Back" and "Go to Dashboard" buttons
  - Information about required permission

### ✅ Test 3: API Protection

#### Test API Endpoints:
Open browser developer tools and try these API calls:

```javascript
// Should return 403 Forbidden
fetch('/api/barcode/products')
fetch('/api/accounts')
fetch('/api/ledger')
```

#### Expected Behavior:
- Should return 403 status code
- Should show "Access denied" message
- Backend should log permission denial

### ✅ Test 4: Partial Permissions

#### For User with Only Barcode Permissions:
1. **Barcode Generator**: Should work normally
2. **Accounting Features**: Should be restricted
3. **Mixed Menu**: Some items accessible, others restricted

### ✅ Test 5: Visual Indicators

#### Check for Proper Styling:
1. **Restricted Items**:
   - Lock icons instead of normal icons
   - Reduced opacity (60%)
   - "not-allowed" cursor on hover
   - No hover background color change

2. **Accessible Items**:
   - Normal icons
   - Full opacity
   - Normal hover effects
   - Clickable with proper navigation

## Verification Checklist

### Navigation Menu
- [ ] Tooltips appear on hover for restricted items
- [ ] Lock icons show for restricted items
- [ ] Reduced opacity for restricted items
- [ ] Snackbar notifications on click
- [ ] No navigation occurs for restricted items
- [ ] Normal behavior for accessible items

### Direct URL Access
- [ ] Access denied page shows for restricted routes
- [ ] Professional messaging and styling
- [ ] Navigation buttons work correctly
- [ ] Required permission information displayed

### API Protection
- [ ] 403 status codes for restricted API calls
- [ ] Proper error messages returned
- [ ] Backend permission middleware working

### User Experience
- [ ] Consistent messaging across all restrictions
- [ ] Professional and polite tone
- [ ] Clear guidance to contact administrator
- [ ] No technical jargon or intimidating language

## Common Issues to Check

### 1. Permission Not Working
- Verify user role has correct permissions assigned
- Check permission names match exactly (case-sensitive)
- Ensure user has logged out and back in after role changes

### 2. Tooltips Not Showing
- Check browser console for JavaScript errors
- Verify Material-UI Tooltip component is working
- Test with different browsers

### 3. API Still Accessible
- Check backend middleware is properly applied
- Verify permission names in backend match frontend
- Test with network tab in developer tools

### 4. Visual Styling Issues
- Check CSS is loading correctly
- Verify Material-UI theme is applied
- Test responsive behavior on different screen sizes

## Success Criteria

### ✅ Complete Success When:
1. **All restricted menu items** show tooltips and lock icons
2. **All restricted clicks** show notifications instead of navigating
3. **All direct URL access** shows professional access denied pages
4. **All API calls** are properly blocked by backend middleware
5. **Visual indicators** are consistent and professional
6. **User experience** is polite and helpful
7. **No console errors** during testing
8. **Responsive design** works on mobile and desktop

## Developer Testing Commands

### Check Current User Permissions
```javascript
// In browser console
console.log('Current User:', JSON.parse(localStorage.getItem('currentUser')));
console.log('User Permissions:', JSON.parse(localStorage.getItem('currentUser')).role.permissions);
```

### Test Permission Functions
```javascript
// In browser console
import { hasPermission } from './src/utils/permissions';
console.log('Has barcode.view:', hasPermission('barcode.view'));
console.log('Has accounting.view:', hasPermission('accounting.view'));
```

### Monitor Network Requests
1. Open Developer Tools → Network tab
2. Try accessing restricted features
3. Look for 403 status codes on API calls
4. Verify proper error responses

## Troubleshooting

### If Permissions Aren't Working:
1. Clear browser cache and localStorage
2. Restart frontend development server
3. Check backend server is running
4. Verify database has correct permissions
5. Check user role assignments in database

### If Styling Issues Occur:
1. Check Material-UI version compatibility
2. Verify CSS imports are correct
3. Test with browser developer tools
4. Check for conflicting styles

This comprehensive testing ensures the permission system works correctly and provides a professional user experience.
