🔄 Let’s re-create the Chart of Accounts algorithm:
Our Chart of Accounts will consist of 4 main types of accounts:
________________________________________
Type 1: Auto-Created from Other Screens
These accounts are created indirectly via other entity forms:
•	Product Items (not Services)
•	Customers
•	Vendors
•	Bank Accounts
🧾 Their accounts are created automatically in the CoA when they are added from their respective forms/screens.
•	✅ Their opening balances are set via their forms, not directly within the Chart of Accounts screen.
•	❌ These accounts should not be added manually through the Chart of Accounts module.
________________________________________
Type 2: System-Generated from Transaction Forms (Invoices)
These accounts are fetched dynamically based on data coming from invoices:
📦 Purchase Invoice
•	Inventory / Purchase Expense
•	Vendor (Accounts Payable – Individual Vendor)
•	Sales Tax Receivable
•	Withholding Tax Payable
•	Purchase Discount (Income)
💰 Sales Order / Sales Invoice
•	Accounts Receivable – Individual Customer
•	Withholding Tax Receivable
•	Sales Revenue
•	Sales Discount Allowed (Expense)
•	Sales Tax Payable
🔁 Purchase Return
•	Accounts Payable – Vendor
•	Inventory / Purchase Expense (reversed)
•	Sales Tax Receivable (reversed)
•	Withholding Tax Payable (reversed)
•	Vendor Deduction Expense
🔁 Sales Return
•	Sales Returns / Revenue Reversal
•	Sales Tax Payable (reversed)
•	Withholding Tax Receivable (reversed)
•	Accounts Receivable – Customer (reversed)
•	Restocking Fee Income
•	Other Deduction Income
🔗 Notes:
•	Accounts like Vendor and Customer should be created per entity name.
E.g., Accounts Payable – ABC Traders, Accounts Receivable – XYZ Pvt Ltd
•	Sales Tax Receivable, Sales Tax Payable, Withholding Tax Payable, and Withholding Tax Receivable are shared/common accounts.
o	All transactions across Purchase, Sales, and Returns should affect these shared tax accounts.
•	✅ These common tax accounts should be editable for opening balances via the Chart of Accounts screen.
________________________________________
Type 3: Manually Added by User (Custom Accounts)
These are accounts added manually by the user through the Chart of Accounts screen.
Examples:
•	Owner’s Capital
•	Shop Rent
•	Utility Bills
•	Employee Salaries
✅ These accounts should support editing and setting the opening balance manually.
________________________________________
Type 4: Built-In System Accounts
These are default accounts required by every accounting system.
Examples:
•	Cash In Hand
•	Retained Earnings (if applicable)
✅ These accounts are mandatory, and the opening balance should also be editable through the Chart of Accounts screen.
________________________________________
✅ Summary of Rules
Type	Source	Addable by User	Editable Opening Balance
1	Forms (Item, Customer, Vendor, Bank)	❌ No	✅ Yes (via form only)
2	Invoices (System-generated)	❌ No	✅ For tax-related accounts
3	User-defined (via CoA screen)	✅ Yes	✅ Yes
4	System default accounts	❌ No	✅ Yes
________________________________________
✅ Final Suggestions
•	You could optionally tag each account with its type (1–4) in your DB schema for better filtering and management.
•	For reporting clarity, consider grouping Type 1 and Type 2 accounts under "System Accounts" in the Chart of Accounts UI.

