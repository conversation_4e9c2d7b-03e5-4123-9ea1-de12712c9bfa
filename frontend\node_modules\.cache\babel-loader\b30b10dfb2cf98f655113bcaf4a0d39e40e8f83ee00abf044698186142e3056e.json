{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Encapsulates a block of data within a QR Code. QR Codes may split their data into\n * multiple blocks, each of which is a unit of data and error-correction codewords. Each\n * is represented by an instance of this class.</p>\n *\n * <AUTHOR> Owen\n */\nvar DataBlock = /** @class */function () {\n  function DataBlock(numDataCodewords /*int*/, codewords) {\n    this.numDataCodewords = numDataCodewords;\n    this.codewords = codewords;\n  }\n  /**\n   * <p>When QR Codes use multiple data blocks, they are actually interleaved.\n   * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This\n   * method will separate the data into original blocks.</p>\n   *\n   * @param rawCodewords bytes as read directly from the QR Code\n   * @param version version of the QR Code\n   * @param ecLevel error-correction level of the QR Code\n   * @return DataBlocks containing original bytes, \"de-interleaved\" from representation in the\n   *         QR Code\n   */\n  DataBlock.getDataBlocks = function (rawCodewords, version, ecLevel) {\n    var e_1, _a, e_2, _b;\n    if (rawCodewords.length !== version.getTotalCodewords()) {\n      throw new IllegalArgumentException();\n    }\n    // Figure out the number and size of data blocks used by this version and\n    // error correction level\n    var ecBlocks = version.getECBlocksForLevel(ecLevel);\n    // First count the total number of data blocks\n    var totalBlocks = 0;\n    var ecBlockArray = ecBlocks.getECBlocks();\n    try {\n      for (var ecBlockArray_1 = __values(ecBlockArray), ecBlockArray_1_1 = ecBlockArray_1.next(); !ecBlockArray_1_1.done; ecBlockArray_1_1 = ecBlockArray_1.next()) {\n        var ecBlock = ecBlockArray_1_1.value;\n        totalBlocks += ecBlock.getCount();\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (ecBlockArray_1_1 && !ecBlockArray_1_1.done && (_a = ecBlockArray_1.return)) _a.call(ecBlockArray_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    // Now establish DataBlocks of the appropriate size and number of data codewords\n    var result = new Array(totalBlocks);\n    var numResultBlocks = 0;\n    try {\n      for (var ecBlockArray_2 = __values(ecBlockArray), ecBlockArray_2_1 = ecBlockArray_2.next(); !ecBlockArray_2_1.done; ecBlockArray_2_1 = ecBlockArray_2.next()) {\n        var ecBlock = ecBlockArray_2_1.value;\n        for (var i = 0; i < ecBlock.getCount(); i++) {\n          var numDataCodewords = ecBlock.getDataCodewords();\n          var numBlockCodewords = ecBlocks.getECCodewordsPerBlock() + numDataCodewords;\n          result[numResultBlocks++] = new DataBlock(numDataCodewords, new Uint8Array(numBlockCodewords));\n        }\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (ecBlockArray_2_1 && !ecBlockArray_2_1.done && (_b = ecBlockArray_2.return)) _b.call(ecBlockArray_2);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    // All blocks have the same amount of data, except that the last n\n    // (where n may be 0) have 1 more byte. Figure out where these start.\n    var shorterBlocksTotalCodewords = result[0].codewords.length;\n    var longerBlocksStartAt = result.length - 1;\n    // TYPESCRIPTPORT: check length is correct here\n    while (longerBlocksStartAt >= 0) {\n      var numCodewords = result[longerBlocksStartAt].codewords.length;\n      if (numCodewords === shorterBlocksTotalCodewords) {\n        break;\n      }\n      longerBlocksStartAt--;\n    }\n    longerBlocksStartAt++;\n    var shorterBlocksNumDataCodewords = shorterBlocksTotalCodewords - ecBlocks.getECCodewordsPerBlock();\n    // The last elements of result may be 1 element longer\n    // first fill out as many elements as all of them have\n    var rawCodewordsOffset = 0;\n    for (var i = 0; i < shorterBlocksNumDataCodewords; i++) {\n      for (var j = 0; j < numResultBlocks; j++) {\n        result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];\n      }\n    }\n    // Fill out the last data block in the longer ones\n    for (var j = longerBlocksStartAt; j < numResultBlocks; j++) {\n      result[j].codewords[shorterBlocksNumDataCodewords] = rawCodewords[rawCodewordsOffset++];\n    }\n    // Now add in error correction blocks\n    var max = result[0].codewords.length;\n    for (var i = shorterBlocksNumDataCodewords; i < max; i++) {\n      for (var j = 0; j < numResultBlocks; j++) {\n        var iOffset = j < longerBlocksStartAt ? i : i + 1;\n        result[j].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];\n      }\n    }\n    return result;\n  };\n  DataBlock.prototype.getNumDataCodewords = function () {\n    return this.numDataCodewords;\n  };\n  DataBlock.prototype.getCodewords = function () {\n    return this.codewords;\n  };\n  return DataBlock;\n}();\nexport default DataBlock;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "IllegalArgumentException", "DataBlock", "numDataCodewords", "codewords", "getDataBlocks", "rawCodewords", "version", "ecLevel", "e_1", "_a", "e_2", "_b", "getTotalCodewords", "ecBlocks", "getECBlocksForLevel", "totalBlocks", "ecBlockArray", "getECBlocks", "ecBlockArray_1", "ecBlockArray_1_1", "ecBlock", "getCount", "e_1_1", "error", "return", "result", "Array", "numResultBlocks", "ecBlockArray_2", "ecBlockArray_2_1", "getDataCodewords", "numBlockCodewords", "getECCodewordsPerBlock", "Uint8Array", "e_2_1", "shorterBlocksTotalCodewords", "longerBlocksStartAt", "numCodewords", "shorterBlocksNumDataCodewords", "rawCodewordsOffset", "j", "max", "iOffset", "prototype", "getNumDataCodewords", "getCodewords"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/DataBlock.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Encapsulates a block of data within a QR Code. QR Codes may split their data into\n * multiple blocks, each of which is a unit of data and error-correction codewords. Each\n * is represented by an instance of this class.</p>\n *\n * <AUTHOR> Owen\n */\nvar DataBlock = /** @class */ (function () {\n    function DataBlock(numDataCodewords /*int*/, codewords) {\n        this.numDataCodewords = numDataCodewords;\n        this.codewords = codewords;\n    }\n    /**\n     * <p>When QR Codes use multiple data blocks, they are actually interleaved.\n     * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This\n     * method will separate the data into original blocks.</p>\n     *\n     * @param rawCodewords bytes as read directly from the QR Code\n     * @param version version of the QR Code\n     * @param ecLevel error-correction level of the QR Code\n     * @return DataBlocks containing original bytes, \"de-interleaved\" from representation in the\n     *         QR Code\n     */\n    DataBlock.getDataBlocks = function (rawCodewords, version, ecLevel) {\n        var e_1, _a, e_2, _b;\n        if (rawCodewords.length !== version.getTotalCodewords()) {\n            throw new IllegalArgumentException();\n        }\n        // Figure out the number and size of data blocks used by this version and\n        // error correction level\n        var ecBlocks = version.getECBlocksForLevel(ecLevel);\n        // First count the total number of data blocks\n        var totalBlocks = 0;\n        var ecBlockArray = ecBlocks.getECBlocks();\n        try {\n            for (var ecBlockArray_1 = __values(ecBlockArray), ecBlockArray_1_1 = ecBlockArray_1.next(); !ecBlockArray_1_1.done; ecBlockArray_1_1 = ecBlockArray_1.next()) {\n                var ecBlock = ecBlockArray_1_1.value;\n                totalBlocks += ecBlock.getCount();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecBlockArray_1_1 && !ecBlockArray_1_1.done && (_a = ecBlockArray_1.return)) _a.call(ecBlockArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        // Now establish DataBlocks of the appropriate size and number of data codewords\n        var result = new Array(totalBlocks);\n        var numResultBlocks = 0;\n        try {\n            for (var ecBlockArray_2 = __values(ecBlockArray), ecBlockArray_2_1 = ecBlockArray_2.next(); !ecBlockArray_2_1.done; ecBlockArray_2_1 = ecBlockArray_2.next()) {\n                var ecBlock = ecBlockArray_2_1.value;\n                for (var i = 0; i < ecBlock.getCount(); i++) {\n                    var numDataCodewords = ecBlock.getDataCodewords();\n                    var numBlockCodewords = ecBlocks.getECCodewordsPerBlock() + numDataCodewords;\n                    result[numResultBlocks++] = new DataBlock(numDataCodewords, new Uint8Array(numBlockCodewords));\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (ecBlockArray_2_1 && !ecBlockArray_2_1.done && (_b = ecBlockArray_2.return)) _b.call(ecBlockArray_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        // All blocks have the same amount of data, except that the last n\n        // (where n may be 0) have 1 more byte. Figure out where these start.\n        var shorterBlocksTotalCodewords = result[0].codewords.length;\n        var longerBlocksStartAt = result.length - 1;\n        // TYPESCRIPTPORT: check length is correct here\n        while (longerBlocksStartAt >= 0) {\n            var numCodewords = result[longerBlocksStartAt].codewords.length;\n            if (numCodewords === shorterBlocksTotalCodewords) {\n                break;\n            }\n            longerBlocksStartAt--;\n        }\n        longerBlocksStartAt++;\n        var shorterBlocksNumDataCodewords = shorterBlocksTotalCodewords - ecBlocks.getECCodewordsPerBlock();\n        // The last elements of result may be 1 element longer\n        // first fill out as many elements as all of them have\n        var rawCodewordsOffset = 0;\n        for (var i = 0; i < shorterBlocksNumDataCodewords; i++) {\n            for (var j = 0; j < numResultBlocks; j++) {\n                result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];\n            }\n        }\n        // Fill out the last data block in the longer ones\n        for (var j = longerBlocksStartAt; j < numResultBlocks; j++) {\n            result[j].codewords[shorterBlocksNumDataCodewords] = rawCodewords[rawCodewordsOffset++];\n        }\n        // Now add in error correction blocks\n        var max = result[0].codewords.length;\n        for (var i = shorterBlocksNumDataCodewords; i < max; i++) {\n            for (var j = 0; j < numResultBlocks; j++) {\n                var iOffset = j < longerBlocksStartAt ? i : i + 1;\n                result[j].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];\n            }\n        }\n        return result;\n    };\n    DataBlock.prototype.getNumDataCodewords = function () {\n        return this.numDataCodewords;\n    };\n    DataBlock.prototype.getCodewords = function () {\n        return this.codewords;\n    };\n    return DataBlock;\n}());\nexport default DataBlock;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACC,gBAAgB,CAAC,SAASC,SAAS,EAAE;IACpD,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,SAAS,CAACG,aAAa,GAAG,UAAUC,YAAY,EAAEC,OAAO,EAAEC,OAAO,EAAE;IAChE,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB,IAAIN,YAAY,CAACV,MAAM,KAAKW,OAAO,CAACM,iBAAiB,CAAC,CAAC,EAAE;MACrD,MAAM,IAAIZ,wBAAwB,CAAC,CAAC;IACxC;IACA;IACA;IACA,IAAIa,QAAQ,GAAGP,OAAO,CAACQ,mBAAmB,CAACP,OAAO,CAAC;IACnD;IACA,IAAIQ,WAAW,GAAG,CAAC;IACnB,IAAIC,YAAY,GAAGH,QAAQ,CAACI,WAAW,CAAC,CAAC;IACzC,IAAI;MACA,KAAK,IAAIC,cAAc,GAAG/B,QAAQ,CAAC6B,YAAY,CAAC,EAAEG,gBAAgB,GAAGD,cAAc,CAACtB,IAAI,CAAC,CAAC,EAAE,CAACuB,gBAAgB,CAACrB,IAAI,EAAEqB,gBAAgB,GAAGD,cAAc,CAACtB,IAAI,CAAC,CAAC,EAAE;QAC1J,IAAIwB,OAAO,GAAGD,gBAAgB,CAACtB,KAAK;QACpCkB,WAAW,IAAIK,OAAO,CAACC,QAAQ,CAAC,CAAC;MACrC;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEd,GAAG,GAAG;QAAEe,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,gBAAgB,IAAI,CAACA,gBAAgB,CAACrB,IAAI,KAAKW,EAAE,GAAGS,cAAc,CAACM,MAAM,CAAC,EAAEf,EAAE,CAACf,IAAI,CAACwB,cAAc,CAAC;MAC3G,CAAC,SACO;QAAE,IAAIV,GAAG,EAAE,MAAMA,GAAG,CAACe,KAAK;MAAE;IACxC;IACA;IACA,IAAIE,MAAM,GAAG,IAAIC,KAAK,CAACX,WAAW,CAAC;IACnC,IAAIY,eAAe,GAAG,CAAC;IACvB,IAAI;MACA,KAAK,IAAIC,cAAc,GAAGzC,QAAQ,CAAC6B,YAAY,CAAC,EAAEa,gBAAgB,GAAGD,cAAc,CAAChC,IAAI,CAAC,CAAC,EAAE,CAACiC,gBAAgB,CAAC/B,IAAI,EAAE+B,gBAAgB,GAAGD,cAAc,CAAChC,IAAI,CAAC,CAAC,EAAE;QAC1J,IAAIwB,OAAO,GAAGS,gBAAgB,CAAChC,KAAK;QACpC,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,OAAO,CAACC,QAAQ,CAAC,CAAC,EAAE5B,CAAC,EAAE,EAAE;UACzC,IAAIS,gBAAgB,GAAGkB,OAAO,CAACU,gBAAgB,CAAC,CAAC;UACjD,IAAIC,iBAAiB,GAAGlB,QAAQ,CAACmB,sBAAsB,CAAC,CAAC,GAAG9B,gBAAgB;UAC5EuB,MAAM,CAACE,eAAe,EAAE,CAAC,GAAG,IAAI1B,SAAS,CAACC,gBAAgB,EAAE,IAAI+B,UAAU,CAACF,iBAAiB,CAAC,CAAC;QAClG;MACJ;IACJ,CAAC,CACD,OAAOG,KAAK,EAAE;MAAExB,GAAG,GAAG;QAAEa,KAAK,EAAEW;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIL,gBAAgB,IAAI,CAACA,gBAAgB,CAAC/B,IAAI,KAAKa,EAAE,GAAGiB,cAAc,CAACJ,MAAM,CAAC,EAAEb,EAAE,CAACjB,IAAI,CAACkC,cAAc,CAAC;MAC3G,CAAC,SACO;QAAE,IAAIlB,GAAG,EAAE,MAAMA,GAAG,CAACa,KAAK;MAAE;IACxC;IACA;IACA;IACA,IAAIY,2BAA2B,GAAGV,MAAM,CAAC,CAAC,CAAC,CAACtB,SAAS,CAACR,MAAM;IAC5D,IAAIyC,mBAAmB,GAAGX,MAAM,CAAC9B,MAAM,GAAG,CAAC;IAC3C;IACA,OAAOyC,mBAAmB,IAAI,CAAC,EAAE;MAC7B,IAAIC,YAAY,GAAGZ,MAAM,CAACW,mBAAmB,CAAC,CAACjC,SAAS,CAACR,MAAM;MAC/D,IAAI0C,YAAY,KAAKF,2BAA2B,EAAE;QAC9C;MACJ;MACAC,mBAAmB,EAAE;IACzB;IACAA,mBAAmB,EAAE;IACrB,IAAIE,6BAA6B,GAAGH,2BAA2B,GAAGtB,QAAQ,CAACmB,sBAAsB,CAAC,CAAC;IACnG;IACA;IACA,IAAIO,kBAAkB,GAAG,CAAC;IAC1B,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,6BAA6B,EAAE7C,CAAC,EAAE,EAAE;MACpD,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,eAAe,EAAEa,CAAC,EAAE,EAAE;QACtCf,MAAM,CAACe,CAAC,CAAC,CAACrC,SAAS,CAACV,CAAC,CAAC,GAAGY,YAAY,CAACkC,kBAAkB,EAAE,CAAC;MAC/D;IACJ;IACA;IACA,KAAK,IAAIC,CAAC,GAAGJ,mBAAmB,EAAEI,CAAC,GAAGb,eAAe,EAAEa,CAAC,EAAE,EAAE;MACxDf,MAAM,CAACe,CAAC,CAAC,CAACrC,SAAS,CAACmC,6BAA6B,CAAC,GAAGjC,YAAY,CAACkC,kBAAkB,EAAE,CAAC;IAC3F;IACA;IACA,IAAIE,GAAG,GAAGhB,MAAM,CAAC,CAAC,CAAC,CAACtB,SAAS,CAACR,MAAM;IACpC,KAAK,IAAIF,CAAC,GAAG6C,6BAA6B,EAAE7C,CAAC,GAAGgD,GAAG,EAAEhD,CAAC,EAAE,EAAE;MACtD,KAAK,IAAI+C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,eAAe,EAAEa,CAAC,EAAE,EAAE;QACtC,IAAIE,OAAO,GAAGF,CAAC,GAAGJ,mBAAmB,GAAG3C,CAAC,GAAGA,CAAC,GAAG,CAAC;QACjDgC,MAAM,CAACe,CAAC,CAAC,CAACrC,SAAS,CAACuC,OAAO,CAAC,GAAGrC,YAAY,CAACkC,kBAAkB,EAAE,CAAC;MACrE;IACJ;IACA,OAAOd,MAAM;EACjB,CAAC;EACDxB,SAAS,CAAC0C,SAAS,CAACC,mBAAmB,GAAG,YAAY;IAClD,OAAO,IAAI,CAAC1C,gBAAgB;EAChC,CAAC;EACDD,SAAS,CAAC0C,SAAS,CAACE,YAAY,GAAG,YAAY;IAC3C,OAAO,IAAI,CAAC1C,SAAS;EACzB,CAAC;EACD,OAAOF,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}