{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport AbstractRSSReader from './AbstractRSSReader';\nimport Pair from './Pair';\nimport Result from '../../Result';\nimport DecodeHintType from '../../DecodeHintType';\nimport NotFoundException from '../../NotFoundException';\nimport StringBuilder from '../../util/StringBuilder';\nimport BarcodeFormat from '../../BarcodeFormat';\nimport ResultPoint from '../../ResultPoint';\nimport FinderPattern from './FinderPattern';\nimport DataCharacter from './DataCharacter';\nimport MathUtils from '../../common/detector/MathUtils';\nimport RSSUtils from './RSSUtils';\nimport System from '../../util/System';\nimport OneDReader from '../OneDReader';\nvar RSS14Reader = /** @class */function (_super) {\n  __extends(RSS14Reader, _super);\n  function RSS14Reader() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.possibleLeftPairs = [];\n    _this.possibleRightPairs = [];\n    return _this;\n  }\n  RSS14Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n    var e_1, _a, e_2, _b;\n    var leftPair = this.decodePair(row, false, rowNumber, hints);\n    RSS14Reader.addOrTally(this.possibleLeftPairs, leftPair);\n    row.reverse();\n    var rightPair = this.decodePair(row, true, rowNumber, hints);\n    RSS14Reader.addOrTally(this.possibleRightPairs, rightPair);\n    row.reverse();\n    try {\n      for (var _c = __values(this.possibleLeftPairs), _d = _c.next(); !_d.done; _d = _c.next()) {\n        var left = _d.value;\n        if (left.getCount() > 1) {\n          try {\n            for (var _e = (e_2 = void 0, __values(this.possibleRightPairs)), _f = _e.next(); !_f.done; _f = _e.next()) {\n              var right = _f.value;\n              if (right.getCount() > 1 && RSS14Reader.checkChecksum(left, right)) {\n                return RSS14Reader.constructResult(left, right);\n              }\n            }\n          } catch (e_2_1) {\n            e_2 = {\n              error: e_2_1\n            };\n          } finally {\n            try {\n              if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            } finally {\n              if (e_2) throw e_2.error;\n            }\n          }\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    throw new NotFoundException();\n  };\n  RSS14Reader.addOrTally = function (possiblePairs, pair) {\n    var e_3, _a;\n    if (pair == null) {\n      return;\n    }\n    var found = false;\n    try {\n      for (var possiblePairs_1 = __values(possiblePairs), possiblePairs_1_1 = possiblePairs_1.next(); !possiblePairs_1_1.done; possiblePairs_1_1 = possiblePairs_1.next()) {\n        var other = possiblePairs_1_1.value;\n        if (other.getValue() === pair.getValue()) {\n          other.incrementCount();\n          found = true;\n          break;\n        }\n      }\n    } catch (e_3_1) {\n      e_3 = {\n        error: e_3_1\n      };\n    } finally {\n      try {\n        if (possiblePairs_1_1 && !possiblePairs_1_1.done && (_a = possiblePairs_1.return)) _a.call(possiblePairs_1);\n      } finally {\n        if (e_3) throw e_3.error;\n      }\n    }\n    if (!found) {\n      possiblePairs.push(pair);\n    }\n  };\n  RSS14Reader.prototype.reset = function () {\n    this.possibleLeftPairs.length = 0;\n    this.possibleRightPairs.length = 0;\n  };\n  RSS14Reader.constructResult = function (leftPair, rightPair) {\n    var symbolValue = 4537077 * leftPair.getValue() + rightPair.getValue();\n    var text = new String(symbolValue).toString();\n    var buffer = new StringBuilder();\n    for (var i = 13 - text.length; i > 0; i--) {\n      buffer.append('0');\n    }\n    buffer.append(text);\n    var checkDigit = 0;\n    for (var i = 0; i < 13; i++) {\n      var digit = buffer.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n      checkDigit += (i & 0x01) === 0 ? 3 * digit : digit;\n    }\n    checkDigit = 10 - checkDigit % 10;\n    if (checkDigit === 10) {\n      checkDigit = 0;\n    }\n    buffer.append(checkDigit.toString());\n    var leftPoints = leftPair.getFinderPattern().getResultPoints();\n    var rightPoints = rightPair.getFinderPattern().getResultPoints();\n    return new Result(buffer.toString(), null, 0, [leftPoints[0], leftPoints[1], rightPoints[0], rightPoints[1]], BarcodeFormat.RSS_14, new Date().getTime());\n  };\n  RSS14Reader.checkChecksum = function (leftPair, rightPair) {\n    var checkValue = (leftPair.getChecksumPortion() + 16 * rightPair.getChecksumPortion()) % 79;\n    var targetCheckValue = 9 * leftPair.getFinderPattern().getValue() + rightPair.getFinderPattern().getValue();\n    if (targetCheckValue > 72) {\n      targetCheckValue--;\n    }\n    if (targetCheckValue > 8) {\n      targetCheckValue--;\n    }\n    return checkValue === targetCheckValue;\n  };\n  RSS14Reader.prototype.decodePair = function (row, right, rowNumber, hints) {\n    try {\n      var startEnd = this.findFinderPattern(row, right);\n      var pattern = this.parseFoundFinderPattern(row, rowNumber, right, startEnd);\n      var resultPointCallback = hints == null ? null : hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n      if (resultPointCallback != null) {\n        var center = (startEnd[0] + startEnd[1]) / 2.0;\n        if (right) {\n          // row is actually reversed\n          center = row.getSize() - 1 - center;\n        }\n        resultPointCallback.foundPossibleResultPoint(new ResultPoint(center, rowNumber));\n      }\n      var outside = this.decodeDataCharacter(row, pattern, true);\n      var inside = this.decodeDataCharacter(row, pattern, false);\n      return new Pair(1597 * outside.getValue() + inside.getValue(), outside.getChecksumPortion() + 4 * inside.getChecksumPortion(), pattern);\n    } catch (err) {\n      return null;\n    }\n  };\n  RSS14Reader.prototype.decodeDataCharacter = function (row, pattern, outsideChar) {\n    var counters = this.getDataCharacterCounters();\n    for (var x = 0; x < counters.length; x++) {\n      counters[x] = 0;\n    }\n    if (outsideChar) {\n      OneDReader.recordPatternInReverse(row, pattern.getStartEnd()[0], counters);\n    } else {\n      OneDReader.recordPattern(row, pattern.getStartEnd()[1] + 1, counters);\n      // reverse it\n      for (var i = 0, j = counters.length - 1; i < j; i++, j--) {\n        var temp = counters[i];\n        counters[i] = counters[j];\n        counters[j] = temp;\n      }\n    }\n    var numModules = outsideChar ? 16 : 15;\n    var elementWidth = MathUtils.sum(new Int32Array(counters)) / numModules;\n    var oddCounts = this.getOddCounts();\n    var evenCounts = this.getEvenCounts();\n    var oddRoundingErrors = this.getOddRoundingErrors();\n    var evenRoundingErrors = this.getEvenRoundingErrors();\n    for (var i = 0; i < counters.length; i++) {\n      var value = counters[i] / elementWidth;\n      var count = Math.floor(value + 0.5);\n      if (count < 1) {\n        count = 1;\n      } else if (count > 8) {\n        count = 8;\n      }\n      var offset = Math.floor(i / 2);\n      if ((i & 0x01) === 0) {\n        oddCounts[offset] = count;\n        oddRoundingErrors[offset] = value - count;\n      } else {\n        evenCounts[offset] = count;\n        evenRoundingErrors[offset] = value - count;\n      }\n    }\n    this.adjustOddEvenCounts(outsideChar, numModules);\n    var oddSum = 0;\n    var oddChecksumPortion = 0;\n    for (var i = oddCounts.length - 1; i >= 0; i--) {\n      oddChecksumPortion *= 9;\n      oddChecksumPortion += oddCounts[i];\n      oddSum += oddCounts[i];\n    }\n    var evenChecksumPortion = 0;\n    var evenSum = 0;\n    for (var i = evenCounts.length - 1; i >= 0; i--) {\n      evenChecksumPortion *= 9;\n      evenChecksumPortion += evenCounts[i];\n      evenSum += evenCounts[i];\n    }\n    var checksumPortion = oddChecksumPortion + 3 * evenChecksumPortion;\n    if (outsideChar) {\n      if ((oddSum & 0x01) !== 0 || oddSum > 12 || oddSum < 4) {\n        throw new NotFoundException();\n      }\n      var group = (12 - oddSum) / 2;\n      var oddWidest = RSS14Reader.OUTSIDE_ODD_WIDEST[group];\n      var evenWidest = 9 - oddWidest;\n      var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, false);\n      var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, true);\n      var tEven = RSS14Reader.OUTSIDE_EVEN_TOTAL_SUBSET[group];\n      var gSum = RSS14Reader.OUTSIDE_GSUM[group];\n      return new DataCharacter(vOdd * tEven + vEven + gSum, checksumPortion);\n    } else {\n      if ((evenSum & 0x01) !== 0 || evenSum > 10 || evenSum < 4) {\n        throw new NotFoundException();\n      }\n      var group = (10 - evenSum) / 2;\n      var oddWidest = RSS14Reader.INSIDE_ODD_WIDEST[group];\n      var evenWidest = 9 - oddWidest;\n      var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, true);\n      var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, false);\n      var tOdd = RSS14Reader.INSIDE_ODD_TOTAL_SUBSET[group];\n      var gSum = RSS14Reader.INSIDE_GSUM[group];\n      return new DataCharacter(vEven * tOdd + vOdd + gSum, checksumPortion);\n    }\n  };\n  RSS14Reader.prototype.findFinderPattern = function (row, rightFinderPattern) {\n    var counters = this.getDecodeFinderCounters();\n    counters[0] = 0;\n    counters[1] = 0;\n    counters[2] = 0;\n    counters[3] = 0;\n    var width = row.getSize();\n    var isWhite = false;\n    var rowOffset = 0;\n    while (rowOffset < width) {\n      isWhite = !row.get(rowOffset);\n      if (rightFinderPattern === isWhite) {\n        // Will encounter white first when searching for right finder pattern\n        break;\n      }\n      rowOffset++;\n    }\n    var counterPosition = 0;\n    var patternStart = rowOffset;\n    for (var x = rowOffset; x < width; x++) {\n      if (row.get(x) !== isWhite) {\n        counters[counterPosition]++;\n      } else {\n        if (counterPosition === 3) {\n          if (AbstractRSSReader.isFinderPattern(counters)) {\n            return [patternStart, x];\n          }\n          patternStart += counters[0] + counters[1];\n          counters[0] = counters[2];\n          counters[1] = counters[3];\n          counters[2] = 0;\n          counters[3] = 0;\n          counterPosition--;\n        } else {\n          counterPosition++;\n        }\n        counters[counterPosition] = 1;\n        isWhite = !isWhite;\n      }\n    }\n    throw new NotFoundException();\n  };\n  RSS14Reader.prototype.parseFoundFinderPattern = function (row, rowNumber, right, startEnd) {\n    // Actually we found elements 2-5\n    var firstIsBlack = row.get(startEnd[0]);\n    var firstElementStart = startEnd[0] - 1;\n    // Locate element 1\n    while (firstElementStart >= 0 && firstIsBlack !== row.get(firstElementStart)) {\n      firstElementStart--;\n    }\n    firstElementStart++;\n    var firstCounter = startEnd[0] - firstElementStart;\n    // Make 'counters' hold 1-4\n    var counters = this.getDecodeFinderCounters();\n    var copy = new Int32Array(counters.length);\n    System.arraycopy(counters, 0, copy, 1, counters.length - 1);\n    copy[0] = firstCounter;\n    var value = this.parseFinderValue(copy, RSS14Reader.FINDER_PATTERNS);\n    var start = firstElementStart;\n    var end = startEnd[1];\n    if (right) {\n      // row is actually reversed\n      start = row.getSize() - 1 - start;\n      end = row.getSize() - 1 - end;\n    }\n    return new FinderPattern(value, [firstElementStart, startEnd[1]], start, end, rowNumber);\n  };\n  RSS14Reader.prototype.adjustOddEvenCounts = function (outsideChar, numModules) {\n    var oddSum = MathUtils.sum(new Int32Array(this.getOddCounts()));\n    var evenSum = MathUtils.sum(new Int32Array(this.getEvenCounts()));\n    var incrementOdd = false;\n    var decrementOdd = false;\n    var incrementEven = false;\n    var decrementEven = false;\n    if (outsideChar) {\n      if (oddSum > 12) {\n        decrementOdd = true;\n      } else if (oddSum < 4) {\n        incrementOdd = true;\n      }\n      if (evenSum > 12) {\n        decrementEven = true;\n      } else if (evenSum < 4) {\n        incrementEven = true;\n      }\n    } else {\n      if (oddSum > 11) {\n        decrementOdd = true;\n      } else if (oddSum < 5) {\n        incrementOdd = true;\n      }\n      if (evenSum > 10) {\n        decrementEven = true;\n      } else if (evenSum < 4) {\n        incrementEven = true;\n      }\n    }\n    var mismatch = oddSum + evenSum - numModules;\n    var oddParityBad = (oddSum & 0x01) === (outsideChar ? 1 : 0);\n    var evenParityBad = (evenSum & 0x01) === 1;\n    if (mismatch === 1) {\n      if (oddParityBad) {\n        if (evenParityBad) {\n          throw new NotFoundException();\n        }\n        decrementOdd = true;\n      } else {\n        if (!evenParityBad) {\n          throw new NotFoundException();\n        }\n        decrementEven = true;\n      }\n    } else if (mismatch === -1) {\n      if (oddParityBad) {\n        if (evenParityBad) {\n          throw new NotFoundException();\n        }\n        incrementOdd = true;\n      } else {\n        if (!evenParityBad) {\n          throw new NotFoundException();\n        }\n        incrementEven = true;\n      }\n    } else if (mismatch === 0) {\n      if (oddParityBad) {\n        if (!evenParityBad) {\n          throw new NotFoundException();\n        }\n        // Both bad\n        if (oddSum < evenSum) {\n          incrementOdd = true;\n          decrementEven = true;\n        } else {\n          decrementOdd = true;\n          incrementEven = true;\n        }\n      } else {\n        if (evenParityBad) {\n          throw new NotFoundException();\n        }\n        // Nothing to do!\n      }\n    } else {\n      throw new NotFoundException();\n    }\n    if (incrementOdd) {\n      if (decrementOdd) {\n        throw new NotFoundException();\n      }\n      AbstractRSSReader.increment(this.getOddCounts(), this.getOddRoundingErrors());\n    }\n    if (decrementOdd) {\n      AbstractRSSReader.decrement(this.getOddCounts(), this.getOddRoundingErrors());\n    }\n    if (incrementEven) {\n      if (decrementEven) {\n        throw new NotFoundException();\n      }\n      AbstractRSSReader.increment(this.getEvenCounts(), this.getOddRoundingErrors());\n    }\n    if (decrementEven) {\n      AbstractRSSReader.decrement(this.getEvenCounts(), this.getEvenRoundingErrors());\n    }\n  };\n  RSS14Reader.OUTSIDE_EVEN_TOTAL_SUBSET = [1, 10, 34, 70, 126];\n  RSS14Reader.INSIDE_ODD_TOTAL_SUBSET = [4, 20, 48, 81];\n  RSS14Reader.OUTSIDE_GSUM = [0, 161, 961, 2015, 2715];\n  RSS14Reader.INSIDE_GSUM = [0, 336, 1036, 1516];\n  RSS14Reader.OUTSIDE_ODD_WIDEST = [8, 6, 4, 3, 1];\n  RSS14Reader.INSIDE_ODD_WIDEST = [2, 4, 6, 8];\n  RSS14Reader.FINDER_PATTERNS = [Int32Array.from([3, 8, 2, 1]), Int32Array.from([3, 5, 5, 1]), Int32Array.from([3, 3, 7, 1]), Int32Array.from([3, 1, 9, 1]), Int32Array.from([2, 7, 4, 1]), Int32Array.from([2, 5, 6, 1]), Int32Array.from([2, 3, 8, 1]), Int32Array.from([1, 5, 7, 1]), Int32Array.from([1, 3, 9, 1])];\n  return RSS14Reader;\n}(AbstractRSSReader);\nexport default RSS14Reader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "AbstractRSSReader", "Pair", "Result", "DecodeHintType", "NotFoundException", "StringBuilder", "BarcodeFormat", "ResultPoint", "FinderPattern", "DataCharacter", "MathUtils", "RSSUtils", "System", "OneDReader", "RSS14Reader", "_super", "_this", "apply", "arguments", "possibleLeftPairs", "possibleRightPairs", "decodeRow", "rowNumber", "row", "hints", "e_1", "_a", "e_2", "_b", "leftPair", "decodePair", "addOrTally", "reverse", "rightPair", "_c", "_d", "left", "getCount", "_e", "_f", "right", "checkChecksum", "constructResult", "e_2_1", "error", "return", "e_1_1", "possiblePairs", "pair", "e_3", "found", "possiblePairs_1", "possiblePairs_1_1", "other", "getValue", "incrementCount", "e_3_1", "push", "reset", "symbolValue", "text", "String", "toString", "buffer", "append", "checkDigit", "digit", "char<PERSON>t", "charCodeAt", "leftPoints", "getFinderPattern", "getResultPoints", "rightPoints", "RSS_14", "Date", "getTime", "checkValue", "getChecksumPortion", "targetCheckValue", "startEnd", "findFinderPattern", "pattern", "parseFoundFinderPattern", "resultPointCallback", "get", "NEED_RESULT_POINT_CALLBACK", "center", "getSize", "foundPossibleResultPoint", "outside", "decodeDataCharacter", "inside", "err", "outsideChar", "counters", "getDataCharacterCounters", "x", "recordPatternInReverse", "getStartEnd", "recordPattern", "j", "temp", "numModules", "elementWidth", "sum", "Int32Array", "oddCounts", "getOddCounts", "evenCounts", "getEvenCounts", "oddRoundingErrors", "getOddRoundingErrors", "evenRoundingErrors", "getEvenRoundingErrors", "count", "Math", "floor", "offset", "adjustOddEvenCounts", "oddSum", "oddChecksumPortion", "evenChecksumPortion", "evenSum", "checksumPortion", "group", "oddWidest", "OUTSIDE_ODD_WIDEST", "evenWidest", "vOdd", "getRSSvalue", "vEven", "tEven", "OUTSIDE_EVEN_TOTAL_SUBSET", "gSum", "OUTSIDE_GSUM", "INSIDE_ODD_WIDEST", "tOdd", "INSIDE_ODD_TOTAL_SUBSET", "INSIDE_GSUM", "rightFinderPattern", "getDecodeFinderCounters", "width", "<PERSON><PERSON><PERSON><PERSON>", "rowOffset", "counterPosition", "patternStart", "isFinderPattern", "firstIsBlack", "firstElementStart", "firstCounter", "copy", "arraycopy", "parseFinderValue", "FINDER_PATTERNS", "start", "end", "incrementOdd", "decrementOdd", "incrementEven", "decrementEven", "mismatch", "oddParityBad", "evenParityBad", "increment", "decrement", "from"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/RSS14Reader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport AbstractRSSReader from './AbstractRSSReader';\nimport Pair from './Pair';\nimport Result from '../../Result';\nimport DecodeHintType from '../../DecodeHintType';\nimport NotFoundException from '../../NotFoundException';\nimport StringBuilder from '../../util/StringBuilder';\nimport BarcodeFormat from '../../BarcodeFormat';\nimport ResultPoint from '../../ResultPoint';\nimport FinderPattern from './FinderPattern';\nimport DataCharacter from './DataCharacter';\nimport MathUtils from '../../common/detector/MathUtils';\nimport RSSUtils from './RSSUtils';\nimport System from '../../util/System';\nimport OneDReader from '../OneDReader';\nvar RSS14Reader = /** @class */ (function (_super) {\n    __extends(RSS14Reader, _super);\n    function RSS14Reader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.possibleLeftPairs = [];\n        _this.possibleRightPairs = [];\n        return _this;\n    }\n    RSS14Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a, e_2, _b;\n        var leftPair = this.decodePair(row, false, rowNumber, hints);\n        RSS14Reader.addOrTally(this.possibleLeftPairs, leftPair);\n        row.reverse();\n        var rightPair = this.decodePair(row, true, rowNumber, hints);\n        RSS14Reader.addOrTally(this.possibleRightPairs, rightPair);\n        row.reverse();\n        try {\n            for (var _c = __values(this.possibleLeftPairs), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var left = _d.value;\n                if (left.getCount() > 1) {\n                    try {\n                        for (var _e = (e_2 = void 0, __values(this.possibleRightPairs)), _f = _e.next(); !_f.done; _f = _e.next()) {\n                            var right = _f.value;\n                            if (right.getCount() > 1 && RSS14Reader.checkChecksum(left, right)) {\n                                return RSS14Reader.constructResult(left, right);\n                            }\n                        }\n                    }\n                    catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                    finally {\n                        try {\n                            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                        }\n                        finally { if (e_2) throw e_2.error; }\n                    }\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        throw new NotFoundException();\n    };\n    RSS14Reader.addOrTally = function (possiblePairs, pair) {\n        var e_3, _a;\n        if (pair == null) {\n            return;\n        }\n        var found = false;\n        try {\n            for (var possiblePairs_1 = __values(possiblePairs), possiblePairs_1_1 = possiblePairs_1.next(); !possiblePairs_1_1.done; possiblePairs_1_1 = possiblePairs_1.next()) {\n                var other = possiblePairs_1_1.value;\n                if (other.getValue() === pair.getValue()) {\n                    other.incrementCount();\n                    found = true;\n                    break;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (possiblePairs_1_1 && !possiblePairs_1_1.done && (_a = possiblePairs_1.return)) _a.call(possiblePairs_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        if (!found) {\n            possiblePairs.push(pair);\n        }\n    };\n    RSS14Reader.prototype.reset = function () {\n        this.possibleLeftPairs.length = 0;\n        this.possibleRightPairs.length = 0;\n    };\n    RSS14Reader.constructResult = function (leftPair, rightPair) {\n        var symbolValue = 4537077 * leftPair.getValue() + rightPair.getValue();\n        var text = new String(symbolValue).toString();\n        var buffer = new StringBuilder();\n        for (var i = 13 - text.length; i > 0; i--) {\n            buffer.append('0');\n        }\n        buffer.append(text);\n        var checkDigit = 0;\n        for (var i = 0; i < 13; i++) {\n            var digit = buffer.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            checkDigit += ((i & 0x01) === 0) ? 3 * digit : digit;\n        }\n        checkDigit = 10 - (checkDigit % 10);\n        if (checkDigit === 10) {\n            checkDigit = 0;\n        }\n        buffer.append(checkDigit.toString());\n        var leftPoints = leftPair.getFinderPattern().getResultPoints();\n        var rightPoints = rightPair.getFinderPattern().getResultPoints();\n        return new Result(buffer.toString(), null, 0, [leftPoints[0], leftPoints[1], rightPoints[0], rightPoints[1]], BarcodeFormat.RSS_14, new Date().getTime());\n    };\n    RSS14Reader.checkChecksum = function (leftPair, rightPair) {\n        var checkValue = (leftPair.getChecksumPortion() + 16 * rightPair.getChecksumPortion()) % 79;\n        var targetCheckValue = 9 * leftPair.getFinderPattern().getValue() + rightPair.getFinderPattern().getValue();\n        if (targetCheckValue > 72) {\n            targetCheckValue--;\n        }\n        if (targetCheckValue > 8) {\n            targetCheckValue--;\n        }\n        return checkValue === targetCheckValue;\n    };\n    RSS14Reader.prototype.decodePair = function (row, right, rowNumber, hints) {\n        try {\n            var startEnd = this.findFinderPattern(row, right);\n            var pattern = this.parseFoundFinderPattern(row, rowNumber, right, startEnd);\n            var resultPointCallback = hints == null ? null : hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n            if (resultPointCallback != null) {\n                var center = (startEnd[0] + startEnd[1]) / 2.0;\n                if (right) {\n                    // row is actually reversed\n                    center = row.getSize() - 1 - center;\n                }\n                resultPointCallback.foundPossibleResultPoint(new ResultPoint(center, rowNumber));\n            }\n            var outside = this.decodeDataCharacter(row, pattern, true);\n            var inside = this.decodeDataCharacter(row, pattern, false);\n            return new Pair(1597 * outside.getValue() + inside.getValue(), outside.getChecksumPortion() + 4 * inside.getChecksumPortion(), pattern);\n        }\n        catch (err) {\n            return null;\n        }\n    };\n    RSS14Reader.prototype.decodeDataCharacter = function (row, pattern, outsideChar) {\n        var counters = this.getDataCharacterCounters();\n        for (var x = 0; x < counters.length; x++) {\n            counters[x] = 0;\n        }\n        if (outsideChar) {\n            OneDReader.recordPatternInReverse(row, pattern.getStartEnd()[0], counters);\n        }\n        else {\n            OneDReader.recordPattern(row, pattern.getStartEnd()[1] + 1, counters);\n            // reverse it\n            for (var i = 0, j = counters.length - 1; i < j; i++, j--) {\n                var temp = counters[i];\n                counters[i] = counters[j];\n                counters[j] = temp;\n            }\n        }\n        var numModules = outsideChar ? 16 : 15;\n        var elementWidth = MathUtils.sum(new Int32Array(counters)) / numModules;\n        var oddCounts = this.getOddCounts();\n        var evenCounts = this.getEvenCounts();\n        var oddRoundingErrors = this.getOddRoundingErrors();\n        var evenRoundingErrors = this.getEvenRoundingErrors();\n        for (var i = 0; i < counters.length; i++) {\n            var value = counters[i] / elementWidth;\n            var count = Math.floor(value + 0.5);\n            if (count < 1) {\n                count = 1;\n            }\n            else if (count > 8) {\n                count = 8;\n            }\n            var offset = Math.floor(i / 2);\n            if ((i & 0x01) === 0) {\n                oddCounts[offset] = count;\n                oddRoundingErrors[offset] = value - count;\n            }\n            else {\n                evenCounts[offset] = count;\n                evenRoundingErrors[offset] = value - count;\n            }\n        }\n        this.adjustOddEvenCounts(outsideChar, numModules);\n        var oddSum = 0;\n        var oddChecksumPortion = 0;\n        for (var i = oddCounts.length - 1; i >= 0; i--) {\n            oddChecksumPortion *= 9;\n            oddChecksumPortion += oddCounts[i];\n            oddSum += oddCounts[i];\n        }\n        var evenChecksumPortion = 0;\n        var evenSum = 0;\n        for (var i = evenCounts.length - 1; i >= 0; i--) {\n            evenChecksumPortion *= 9;\n            evenChecksumPortion += evenCounts[i];\n            evenSum += evenCounts[i];\n        }\n        var checksumPortion = oddChecksumPortion + 3 * evenChecksumPortion;\n        if (outsideChar) {\n            if ((oddSum & 0x01) !== 0 || oddSum > 12 || oddSum < 4) {\n                throw new NotFoundException();\n            }\n            var group = (12 - oddSum) / 2;\n            var oddWidest = RSS14Reader.OUTSIDE_ODD_WIDEST[group];\n            var evenWidest = 9 - oddWidest;\n            var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, false);\n            var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, true);\n            var tEven = RSS14Reader.OUTSIDE_EVEN_TOTAL_SUBSET[group];\n            var gSum = RSS14Reader.OUTSIDE_GSUM[group];\n            return new DataCharacter(vOdd * tEven + vEven + gSum, checksumPortion);\n        }\n        else {\n            if ((evenSum & 0x01) !== 0 || evenSum > 10 || evenSum < 4) {\n                throw new NotFoundException();\n            }\n            var group = (10 - evenSum) / 2;\n            var oddWidest = RSS14Reader.INSIDE_ODD_WIDEST[group];\n            var evenWidest = 9 - oddWidest;\n            var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, true);\n            var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, false);\n            var tOdd = RSS14Reader.INSIDE_ODD_TOTAL_SUBSET[group];\n            var gSum = RSS14Reader.INSIDE_GSUM[group];\n            return new DataCharacter(vEven * tOdd + vOdd + gSum, checksumPortion);\n        }\n    };\n    RSS14Reader.prototype.findFinderPattern = function (row, rightFinderPattern) {\n        var counters = this.getDecodeFinderCounters();\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var width = row.getSize();\n        var isWhite = false;\n        var rowOffset = 0;\n        while (rowOffset < width) {\n            isWhite = !row.get(rowOffset);\n            if (rightFinderPattern === isWhite) {\n                // Will encounter white first when searching for right finder pattern\n                break;\n            }\n            rowOffset++;\n        }\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        for (var x = rowOffset; x < width; x++) {\n            if (row.get(x) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === 3) {\n                    if (AbstractRSSReader.isFinderPattern(counters)) {\n                        return [patternStart, x];\n                    }\n                    patternStart += counters[0] + counters[1];\n                    counters[0] = counters[2];\n                    counters[1] = counters[3];\n                    counters[2] = 0;\n                    counters[3] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    RSS14Reader.prototype.parseFoundFinderPattern = function (row, rowNumber, right, startEnd) {\n        // Actually we found elements 2-5\n        var firstIsBlack = row.get(startEnd[0]);\n        var firstElementStart = startEnd[0] - 1;\n        // Locate element 1\n        while (firstElementStart >= 0 && firstIsBlack !== row.get(firstElementStart)) {\n            firstElementStart--;\n        }\n        firstElementStart++;\n        var firstCounter = startEnd[0] - firstElementStart;\n        // Make 'counters' hold 1-4\n        var counters = this.getDecodeFinderCounters();\n        var copy = new Int32Array(counters.length);\n        System.arraycopy(counters, 0, copy, 1, counters.length - 1);\n        copy[0] = firstCounter;\n        var value = this.parseFinderValue(copy, RSS14Reader.FINDER_PATTERNS);\n        var start = firstElementStart;\n        var end = startEnd[1];\n        if (right) {\n            // row is actually reversed\n            start = row.getSize() - 1 - start;\n            end = row.getSize() - 1 - end;\n        }\n        return new FinderPattern(value, [firstElementStart, startEnd[1]], start, end, rowNumber);\n    };\n    RSS14Reader.prototype.adjustOddEvenCounts = function (outsideChar, numModules) {\n        var oddSum = MathUtils.sum(new Int32Array(this.getOddCounts()));\n        var evenSum = MathUtils.sum(new Int32Array(this.getEvenCounts()));\n        var incrementOdd = false;\n        var decrementOdd = false;\n        var incrementEven = false;\n        var decrementEven = false;\n        if (outsideChar) {\n            if (oddSum > 12) {\n                decrementOdd = true;\n            }\n            else if (oddSum < 4) {\n                incrementOdd = true;\n            }\n            if (evenSum > 12) {\n                decrementEven = true;\n            }\n            else if (evenSum < 4) {\n                incrementEven = true;\n            }\n        }\n        else {\n            if (oddSum > 11) {\n                decrementOdd = true;\n            }\n            else if (oddSum < 5) {\n                incrementOdd = true;\n            }\n            if (evenSum > 10) {\n                decrementEven = true;\n            }\n            else if (evenSum < 4) {\n                incrementEven = true;\n            }\n        }\n        var mismatch = oddSum + evenSum - numModules;\n        var oddParityBad = (oddSum & 0x01) === (outsideChar ? 1 : 0);\n        var evenParityBad = (evenSum & 0x01) === 1;\n        if (mismatch === 1) {\n            if (oddParityBad) {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                decrementOdd = true;\n            }\n            else {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                decrementEven = true;\n            }\n        }\n        else if (mismatch === -1) {\n            if (oddParityBad) {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                incrementOdd = true;\n            }\n            else {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                incrementEven = true;\n            }\n        }\n        else if (mismatch === 0) {\n            if (oddParityBad) {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                // Both bad\n                if (oddSum < evenSum) {\n                    incrementOdd = true;\n                    decrementEven = true;\n                }\n                else {\n                    decrementOdd = true;\n                    incrementEven = true;\n                }\n            }\n            else {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                // Nothing to do!\n            }\n        }\n        else {\n            throw new NotFoundException();\n        }\n        if (incrementOdd) {\n            if (decrementOdd) {\n                throw new NotFoundException();\n            }\n            AbstractRSSReader.increment(this.getOddCounts(), this.getOddRoundingErrors());\n        }\n        if (decrementOdd) {\n            AbstractRSSReader.decrement(this.getOddCounts(), this.getOddRoundingErrors());\n        }\n        if (incrementEven) {\n            if (decrementEven) {\n                throw new NotFoundException();\n            }\n            AbstractRSSReader.increment(this.getEvenCounts(), this.getOddRoundingErrors());\n        }\n        if (decrementEven) {\n            AbstractRSSReader.decrement(this.getEvenCounts(), this.getEvenRoundingErrors());\n        }\n    };\n    RSS14Reader.OUTSIDE_EVEN_TOTAL_SUBSET = [1, 10, 34, 70, 126];\n    RSS14Reader.INSIDE_ODD_TOTAL_SUBSET = [4, 20, 48, 81];\n    RSS14Reader.OUTSIDE_GSUM = [0, 161, 961, 2015, 2715];\n    RSS14Reader.INSIDE_GSUM = [0, 336, 1036, 1516];\n    RSS14Reader.OUTSIDE_ODD_WIDEST = [8, 6, 4, 3, 1];\n    RSS14Reader.INSIDE_ODD_WIDEST = [2, 4, 6, 8];\n    RSS14Reader.FINDER_PATTERNS = [\n        Int32Array.from([3, 8, 2, 1]),\n        Int32Array.from([3, 5, 5, 1]),\n        Int32Array.from([3, 3, 7, 1]),\n        Int32Array.from([3, 1, 9, 1]),\n        Int32Array.from([2, 7, 4, 1]),\n        Int32Array.from([2, 5, 6, 1]),\n        Int32Array.from([2, 3, 8, 1]),\n        Int32Array.from([1, 5, 7, 1]),\n        Int32Array.from([1, 3, 9, 1]),\n    ];\n    return RSS14Reader;\n}(AbstractRSSReader));\nexport default RSS14Reader;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,SAAS,MAAM,iCAAiC;AACvD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,UAAU,MAAM,eAAe;AACtC,IAAIC,WAAW,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC/C1C,SAAS,CAACyC,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACnB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,iBAAiB,GAAG,EAAE;IAC5BH,KAAK,CAACI,kBAAkB,GAAG,EAAE;IAC7B,OAAOJ,KAAK;EAChB;EACAF,WAAW,CAAC7B,SAAS,CAACoC,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAE;IAC/D,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB,IAAIC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACP,GAAG,EAAE,KAAK,EAAED,SAAS,EAAEE,KAAK,CAAC;IAC5DV,WAAW,CAACiB,UAAU,CAAC,IAAI,CAACZ,iBAAiB,EAAEU,QAAQ,CAAC;IACxDN,GAAG,CAACS,OAAO,CAAC,CAAC;IACb,IAAIC,SAAS,GAAG,IAAI,CAACH,UAAU,CAACP,GAAG,EAAE,IAAI,EAAED,SAAS,EAAEE,KAAK,CAAC;IAC5DV,WAAW,CAACiB,UAAU,CAAC,IAAI,CAACX,kBAAkB,EAAEa,SAAS,CAAC;IAC1DV,GAAG,CAACS,OAAO,CAAC,CAAC;IACb,IAAI;MACA,KAAK,IAAIE,EAAE,GAAG/C,QAAQ,CAAC,IAAI,CAACgC,iBAAiB,CAAC,EAAEgB,EAAE,GAAGD,EAAE,CAACtC,IAAI,CAAC,CAAC,EAAE,CAACuC,EAAE,CAACrC,IAAI,EAAEqC,EAAE,GAAGD,EAAE,CAACtC,IAAI,CAAC,CAAC,EAAE;QACtF,IAAIwC,IAAI,GAAGD,EAAE,CAACtC,KAAK;QACnB,IAAIuC,IAAI,CAACC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE;UACrB,IAAI;YACA,KAAK,IAAIC,EAAE,IAAIX,GAAG,GAAG,KAAK,CAAC,EAAExC,QAAQ,CAAC,IAAI,CAACiC,kBAAkB,CAAC,CAAC,EAAEmB,EAAE,GAAGD,EAAE,CAAC1C,IAAI,CAAC,CAAC,EAAE,CAAC2C,EAAE,CAACzC,IAAI,EAAEyC,EAAE,GAAGD,EAAE,CAAC1C,IAAI,CAAC,CAAC,EAAE;cACvG,IAAI4C,KAAK,GAAGD,EAAE,CAAC1C,KAAK;cACpB,IAAI2C,KAAK,CAACH,QAAQ,CAAC,CAAC,GAAG,CAAC,IAAIvB,WAAW,CAAC2B,aAAa,CAACL,IAAI,EAAEI,KAAK,CAAC,EAAE;gBAChE,OAAO1B,WAAW,CAAC4B,eAAe,CAACN,IAAI,EAAEI,KAAK,CAAC;cACnD;YACJ;UACJ,CAAC,CACD,OAAOG,KAAK,EAAE;YAAEhB,GAAG,GAAG;cAAEiB,KAAK,EAAED;YAAM,CAAC;UAAE,CAAC,SACjC;YACJ,IAAI;cACA,IAAIJ,EAAE,IAAI,CAACA,EAAE,CAACzC,IAAI,KAAK8B,EAAE,GAAGU,EAAE,CAACO,MAAM,CAAC,EAAEjB,EAAE,CAAClC,IAAI,CAAC4C,EAAE,CAAC;YACvD,CAAC,SACO;cAAE,IAAIX,GAAG,EAAE,MAAMA,GAAG,CAACiB,KAAK;YAAE;UACxC;QACJ;MACJ;IACJ,CAAC,CACD,OAAOE,KAAK,EAAE;MAAErB,GAAG,GAAG;QAAEmB,KAAK,EAAEE;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIX,EAAE,IAAI,CAACA,EAAE,CAACrC,IAAI,KAAK4B,EAAE,GAAGQ,EAAE,CAACW,MAAM,CAAC,EAAEnB,EAAE,CAAChC,IAAI,CAACwC,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIT,GAAG,EAAE,MAAMA,GAAG,CAACmB,KAAK;MAAE;IACxC;IACA,MAAM,IAAIxC,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACDU,WAAW,CAACiB,UAAU,GAAG,UAAUgB,aAAa,EAAEC,IAAI,EAAE;IACpD,IAAIC,GAAG,EAAEvB,EAAE;IACX,IAAIsB,IAAI,IAAI,IAAI,EAAE;MACd;IACJ;IACA,IAAIE,KAAK,GAAG,KAAK;IACjB,IAAI;MACA,KAAK,IAAIC,eAAe,GAAGhE,QAAQ,CAAC4D,aAAa,CAAC,EAAEK,iBAAiB,GAAGD,eAAe,CAACvD,IAAI,CAAC,CAAC,EAAE,CAACwD,iBAAiB,CAACtD,IAAI,EAAEsD,iBAAiB,GAAGD,eAAe,CAACvD,IAAI,CAAC,CAAC,EAAE;QACjK,IAAIyD,KAAK,GAAGD,iBAAiB,CAACvD,KAAK;QACnC,IAAIwD,KAAK,CAACC,QAAQ,CAAC,CAAC,KAAKN,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAE;UACtCD,KAAK,CAACE,cAAc,CAAC,CAAC;UACtBL,KAAK,GAAG,IAAI;UACZ;QACJ;MACJ;IACJ,CAAC,CACD,OAAOM,KAAK,EAAE;MAAEP,GAAG,GAAG;QAAEL,KAAK,EAAEY;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIJ,iBAAiB,IAAI,CAACA,iBAAiB,CAACtD,IAAI,KAAK4B,EAAE,GAAGyB,eAAe,CAACN,MAAM,CAAC,EAAEnB,EAAE,CAAChC,IAAI,CAACyD,eAAe,CAAC;MAC/G,CAAC,SACO;QAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACL,KAAK;MAAE;IACxC;IACA,IAAI,CAACM,KAAK,EAAE;MACRH,aAAa,CAACU,IAAI,CAACT,IAAI,CAAC;IAC5B;EACJ,CAAC;EACDlC,WAAW,CAAC7B,SAAS,CAACyE,KAAK,GAAG,YAAY;IACtC,IAAI,CAACvC,iBAAiB,CAACxB,MAAM,GAAG,CAAC;IACjC,IAAI,CAACyB,kBAAkB,CAACzB,MAAM,GAAG,CAAC;EACtC,CAAC;EACDmB,WAAW,CAAC4B,eAAe,GAAG,UAAUb,QAAQ,EAAEI,SAAS,EAAE;IACzD,IAAI0B,WAAW,GAAG,OAAO,GAAG9B,QAAQ,CAACyB,QAAQ,CAAC,CAAC,GAAGrB,SAAS,CAACqB,QAAQ,CAAC,CAAC;IACtE,IAAIM,IAAI,GAAG,IAAIC,MAAM,CAACF,WAAW,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC7C,IAAIC,MAAM,GAAG,IAAI1D,aAAa,CAAC,CAAC;IAChC,KAAK,IAAIZ,CAAC,GAAG,EAAE,GAAGmE,IAAI,CAACjE,MAAM,EAAEF,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACvCsE,MAAM,CAACC,MAAM,CAAC,GAAG,CAAC;IACtB;IACAD,MAAM,CAACC,MAAM,CAACJ,IAAI,CAAC;IACnB,IAAIK,UAAU,GAAG,CAAC;IAClB,KAAK,IAAIxE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzB,IAAIyE,KAAK,GAAGH,MAAM,CAACI,MAAM,CAAC1E,CAAC,CAAC,CAAC2E,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;MAC9DH,UAAU,IAAK,CAACxE,CAAC,GAAG,IAAI,MAAM,CAAC,GAAI,CAAC,GAAGyE,KAAK,GAAGA,KAAK;IACxD;IACAD,UAAU,GAAG,EAAE,GAAIA,UAAU,GAAG,EAAG;IACnC,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnBA,UAAU,GAAG,CAAC;IAClB;IACAF,MAAM,CAACC,MAAM,CAACC,UAAU,CAACH,QAAQ,CAAC,CAAC,CAAC;IACpC,IAAIO,UAAU,GAAGxC,QAAQ,CAACyC,gBAAgB,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC;IAC9D,IAAIC,WAAW,GAAGvC,SAAS,CAACqC,gBAAgB,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC;IAChE,OAAO,IAAIrE,MAAM,CAAC6D,MAAM,CAACD,QAAQ,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAACO,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,EAAEG,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC,EAAElE,aAAa,CAACmE,MAAM,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAC7J,CAAC;EACD7D,WAAW,CAAC2B,aAAa,GAAG,UAAUZ,QAAQ,EAAEI,SAAS,EAAE;IACvD,IAAI2C,UAAU,GAAG,CAAC/C,QAAQ,CAACgD,kBAAkB,CAAC,CAAC,GAAG,EAAE,GAAG5C,SAAS,CAAC4C,kBAAkB,CAAC,CAAC,IAAI,EAAE;IAC3F,IAAIC,gBAAgB,GAAG,CAAC,GAAGjD,QAAQ,CAACyC,gBAAgB,CAAC,CAAC,CAAChB,QAAQ,CAAC,CAAC,GAAGrB,SAAS,CAACqC,gBAAgB,CAAC,CAAC,CAAChB,QAAQ,CAAC,CAAC;IAC3G,IAAIwB,gBAAgB,GAAG,EAAE,EAAE;MACvBA,gBAAgB,EAAE;IACtB;IACA,IAAIA,gBAAgB,GAAG,CAAC,EAAE;MACtBA,gBAAgB,EAAE;IACtB;IACA,OAAOF,UAAU,KAAKE,gBAAgB;EAC1C,CAAC;EACDhE,WAAW,CAAC7B,SAAS,CAAC6C,UAAU,GAAG,UAAUP,GAAG,EAAEiB,KAAK,EAAElB,SAAS,EAAEE,KAAK,EAAE;IACvE,IAAI;MACA,IAAIuD,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAACzD,GAAG,EAAEiB,KAAK,CAAC;MACjD,IAAIyC,OAAO,GAAG,IAAI,CAACC,uBAAuB,CAAC3D,GAAG,EAAED,SAAS,EAAEkB,KAAK,EAAEuC,QAAQ,CAAC;MAC3E,IAAII,mBAAmB,GAAG3D,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGA,KAAK,CAAC4D,GAAG,CAACjF,cAAc,CAACkF,0BAA0B,CAAC;MACrG,IAAIF,mBAAmB,IAAI,IAAI,EAAE;QAC7B,IAAIG,MAAM,GAAG,CAACP,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG;QAC9C,IAAIvC,KAAK,EAAE;UACP;UACA8C,MAAM,GAAG/D,GAAG,CAACgE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGD,MAAM;QACvC;QACAH,mBAAmB,CAACK,wBAAwB,CAAC,IAAIjF,WAAW,CAAC+E,MAAM,EAAEhE,SAAS,CAAC,CAAC;MACpF;MACA,IAAImE,OAAO,GAAG,IAAI,CAACC,mBAAmB,CAACnE,GAAG,EAAE0D,OAAO,EAAE,IAAI,CAAC;MAC1D,IAAIU,MAAM,GAAG,IAAI,CAACD,mBAAmB,CAACnE,GAAG,EAAE0D,OAAO,EAAE,KAAK,CAAC;MAC1D,OAAO,IAAIhF,IAAI,CAAC,IAAI,GAAGwF,OAAO,CAACnC,QAAQ,CAAC,CAAC,GAAGqC,MAAM,CAACrC,QAAQ,CAAC,CAAC,EAAEmC,OAAO,CAACZ,kBAAkB,CAAC,CAAC,GAAG,CAAC,GAAGc,MAAM,CAACd,kBAAkB,CAAC,CAAC,EAAEI,OAAO,CAAC;IAC3I,CAAC,CACD,OAAOW,GAAG,EAAE;MACR,OAAO,IAAI;IACf;EACJ,CAAC;EACD9E,WAAW,CAAC7B,SAAS,CAACyG,mBAAmB,GAAG,UAAUnE,GAAG,EAAE0D,OAAO,EAAEY,WAAW,EAAE;IAC7E,IAAIC,QAAQ,GAAG,IAAI,CAACC,wBAAwB,CAAC,CAAC;IAC9C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACnG,MAAM,EAAEqG,CAAC,EAAE,EAAE;MACtCF,QAAQ,CAACE,CAAC,CAAC,GAAG,CAAC;IACnB;IACA,IAAIH,WAAW,EAAE;MACbhF,UAAU,CAACoF,sBAAsB,CAAC1E,GAAG,EAAE0D,OAAO,CAACiB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEJ,QAAQ,CAAC;IAC9E,CAAC,MACI;MACDjF,UAAU,CAACsF,aAAa,CAAC5E,GAAG,EAAE0D,OAAO,CAACiB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEJ,QAAQ,CAAC;MACrE;MACA,KAAK,IAAIrG,CAAC,GAAG,CAAC,EAAE2G,CAAC,GAAGN,QAAQ,CAACnG,MAAM,GAAG,CAAC,EAAEF,CAAC,GAAG2G,CAAC,EAAE3G,CAAC,EAAE,EAAE2G,CAAC,EAAE,EAAE;QACtD,IAAIC,IAAI,GAAGP,QAAQ,CAACrG,CAAC,CAAC;QACtBqG,QAAQ,CAACrG,CAAC,CAAC,GAAGqG,QAAQ,CAACM,CAAC,CAAC;QACzBN,QAAQ,CAACM,CAAC,CAAC,GAAGC,IAAI;MACtB;IACJ;IACA,IAAIC,UAAU,GAAGT,WAAW,GAAG,EAAE,GAAG,EAAE;IACtC,IAAIU,YAAY,GAAG7F,SAAS,CAAC8F,GAAG,CAAC,IAAIC,UAAU,CAACX,QAAQ,CAAC,CAAC,GAAGQ,UAAU;IACvE,IAAII,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACnC,IAAIC,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACrC,IAAIC,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACnD,IAAIC,kBAAkB,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACrD,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqG,QAAQ,CAACnG,MAAM,EAAEF,CAAC,EAAE,EAAE;MACtC,IAAII,KAAK,GAAGiG,QAAQ,CAACrG,CAAC,CAAC,GAAG8G,YAAY;MACtC,IAAIW,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACvH,KAAK,GAAG,GAAG,CAAC;MACnC,IAAIqH,KAAK,GAAG,CAAC,EAAE;QACXA,KAAK,GAAG,CAAC;MACb,CAAC,MACI,IAAIA,KAAK,GAAG,CAAC,EAAE;QAChBA,KAAK,GAAG,CAAC;MACb;MACA,IAAIG,MAAM,GAAGF,IAAI,CAACC,KAAK,CAAC3H,CAAC,GAAG,CAAC,CAAC;MAC9B,IAAI,CAACA,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE;QAClBiH,SAAS,CAACW,MAAM,CAAC,GAAGH,KAAK;QACzBJ,iBAAiB,CAACO,MAAM,CAAC,GAAGxH,KAAK,GAAGqH,KAAK;MAC7C,CAAC,MACI;QACDN,UAAU,CAACS,MAAM,CAAC,GAAGH,KAAK;QAC1BF,kBAAkB,CAACK,MAAM,CAAC,GAAGxH,KAAK,GAAGqH,KAAK;MAC9C;IACJ;IACA,IAAI,CAACI,mBAAmB,CAACzB,WAAW,EAAES,UAAU,CAAC;IACjD,IAAIiB,MAAM,GAAG,CAAC;IACd,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,KAAK,IAAI/H,CAAC,GAAGiH,SAAS,CAAC/G,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5C+H,kBAAkB,IAAI,CAAC;MACvBA,kBAAkB,IAAId,SAAS,CAACjH,CAAC,CAAC;MAClC8H,MAAM,IAAIb,SAAS,CAACjH,CAAC,CAAC;IAC1B;IACA,IAAIgI,mBAAmB,GAAG,CAAC;IAC3B,IAAIC,OAAO,GAAG,CAAC;IACf,KAAK,IAAIjI,CAAC,GAAGmH,UAAU,CAACjH,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC7CgI,mBAAmB,IAAI,CAAC;MACxBA,mBAAmB,IAAIb,UAAU,CAACnH,CAAC,CAAC;MACpCiI,OAAO,IAAId,UAAU,CAACnH,CAAC,CAAC;IAC5B;IACA,IAAIkI,eAAe,GAAGH,kBAAkB,GAAG,CAAC,GAAGC,mBAAmB;IAClE,IAAI5B,WAAW,EAAE;MACb,IAAI,CAAC0B,MAAM,GAAG,IAAI,MAAM,CAAC,IAAIA,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,CAAC,EAAE;QACpD,MAAM,IAAInH,iBAAiB,CAAC,CAAC;MACjC;MACA,IAAIwH,KAAK,GAAG,CAAC,EAAE,GAAGL,MAAM,IAAI,CAAC;MAC7B,IAAIM,SAAS,GAAG/G,WAAW,CAACgH,kBAAkB,CAACF,KAAK,CAAC;MACrD,IAAIG,UAAU,GAAG,CAAC,GAAGF,SAAS;MAC9B,IAAIG,IAAI,GAAGrH,QAAQ,CAACsH,WAAW,CAACvB,SAAS,EAAEmB,SAAS,EAAE,KAAK,CAAC;MAC5D,IAAIK,KAAK,GAAGvH,QAAQ,CAACsH,WAAW,CAACrB,UAAU,EAAEmB,UAAU,EAAE,IAAI,CAAC;MAC9D,IAAII,KAAK,GAAGrH,WAAW,CAACsH,yBAAyB,CAACR,KAAK,CAAC;MACxD,IAAIS,IAAI,GAAGvH,WAAW,CAACwH,YAAY,CAACV,KAAK,CAAC;MAC1C,OAAO,IAAInH,aAAa,CAACuH,IAAI,GAAGG,KAAK,GAAGD,KAAK,GAAGG,IAAI,EAAEV,eAAe,CAAC;IAC1E,CAAC,MACI;MACD,IAAI,CAACD,OAAO,GAAG,IAAI,MAAM,CAAC,IAAIA,OAAO,GAAG,EAAE,IAAIA,OAAO,GAAG,CAAC,EAAE;QACvD,MAAM,IAAItH,iBAAiB,CAAC,CAAC;MACjC;MACA,IAAIwH,KAAK,GAAG,CAAC,EAAE,GAAGF,OAAO,IAAI,CAAC;MAC9B,IAAIG,SAAS,GAAG/G,WAAW,CAACyH,iBAAiB,CAACX,KAAK,CAAC;MACpD,IAAIG,UAAU,GAAG,CAAC,GAAGF,SAAS;MAC9B,IAAIG,IAAI,GAAGrH,QAAQ,CAACsH,WAAW,CAACvB,SAAS,EAAEmB,SAAS,EAAE,IAAI,CAAC;MAC3D,IAAIK,KAAK,GAAGvH,QAAQ,CAACsH,WAAW,CAACrB,UAAU,EAAEmB,UAAU,EAAE,KAAK,CAAC;MAC/D,IAAIS,IAAI,GAAG1H,WAAW,CAAC2H,uBAAuB,CAACb,KAAK,CAAC;MACrD,IAAIS,IAAI,GAAGvH,WAAW,CAAC4H,WAAW,CAACd,KAAK,CAAC;MACzC,OAAO,IAAInH,aAAa,CAACyH,KAAK,GAAGM,IAAI,GAAGR,IAAI,GAAGK,IAAI,EAAEV,eAAe,CAAC;IACzE;EACJ,CAAC;EACD7G,WAAW,CAAC7B,SAAS,CAAC+F,iBAAiB,GAAG,UAAUzD,GAAG,EAAEoH,kBAAkB,EAAE;IACzE,IAAI7C,QAAQ,GAAG,IAAI,CAAC8C,uBAAuB,CAAC,CAAC;IAC7C9C,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACf,IAAI+C,KAAK,GAAGtH,GAAG,CAACgE,OAAO,CAAC,CAAC;IACzB,IAAIuD,OAAO,GAAG,KAAK;IACnB,IAAIC,SAAS,GAAG,CAAC;IACjB,OAAOA,SAAS,GAAGF,KAAK,EAAE;MACtBC,OAAO,GAAG,CAACvH,GAAG,CAAC6D,GAAG,CAAC2D,SAAS,CAAC;MAC7B,IAAIJ,kBAAkB,KAAKG,OAAO,EAAE;QAChC;QACA;MACJ;MACAC,SAAS,EAAE;IACf;IACA,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIC,YAAY,GAAGF,SAAS;IAC5B,KAAK,IAAI/C,CAAC,GAAG+C,SAAS,EAAE/C,CAAC,GAAG6C,KAAK,EAAE7C,CAAC,EAAE,EAAE;MACpC,IAAIzE,GAAG,CAAC6D,GAAG,CAACY,CAAC,CAAC,KAAK8C,OAAO,EAAE;QACxBhD,QAAQ,CAACkD,eAAe,CAAC,EAAE;MAC/B,CAAC,MACI;QACD,IAAIA,eAAe,KAAK,CAAC,EAAE;UACvB,IAAIhJ,iBAAiB,CAACkJ,eAAe,CAACpD,QAAQ,CAAC,EAAE;YAC7C,OAAO,CAACmD,YAAY,EAAEjD,CAAC,CAAC;UAC5B;UACAiD,YAAY,IAAInD,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzCA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzBA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzBA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;UACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;UACfkD,eAAe,EAAE;QACrB,CAAC,MACI;UACDA,eAAe,EAAE;QACrB;QACAlD,QAAQ,CAACkD,eAAe,CAAC,GAAG,CAAC;QAC7BF,OAAO,GAAG,CAACA,OAAO;MACtB;IACJ;IACA,MAAM,IAAI1I,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACDU,WAAW,CAAC7B,SAAS,CAACiG,uBAAuB,GAAG,UAAU3D,GAAG,EAAED,SAAS,EAAEkB,KAAK,EAAEuC,QAAQ,EAAE;IACvF;IACA,IAAIoE,YAAY,GAAG5H,GAAG,CAAC6D,GAAG,CAACL,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvC,IAAIqE,iBAAiB,GAAGrE,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACvC;IACA,OAAOqE,iBAAiB,IAAI,CAAC,IAAID,YAAY,KAAK5H,GAAG,CAAC6D,GAAG,CAACgE,iBAAiB,CAAC,EAAE;MAC1EA,iBAAiB,EAAE;IACvB;IACAA,iBAAiB,EAAE;IACnB,IAAIC,YAAY,GAAGtE,QAAQ,CAAC,CAAC,CAAC,GAAGqE,iBAAiB;IAClD;IACA,IAAItD,QAAQ,GAAG,IAAI,CAAC8C,uBAAuB,CAAC,CAAC;IAC7C,IAAIU,IAAI,GAAG,IAAI7C,UAAU,CAACX,QAAQ,CAACnG,MAAM,CAAC;IAC1CiB,MAAM,CAAC2I,SAAS,CAACzD,QAAQ,EAAE,CAAC,EAAEwD,IAAI,EAAE,CAAC,EAAExD,QAAQ,CAACnG,MAAM,GAAG,CAAC,CAAC;IAC3D2J,IAAI,CAAC,CAAC,CAAC,GAAGD,YAAY;IACtB,IAAIxJ,KAAK,GAAG,IAAI,CAAC2J,gBAAgB,CAACF,IAAI,EAAExI,WAAW,CAAC2I,eAAe,CAAC;IACpE,IAAIC,KAAK,GAAGN,iBAAiB;IAC7B,IAAIO,GAAG,GAAG5E,QAAQ,CAAC,CAAC,CAAC;IACrB,IAAIvC,KAAK,EAAE;MACP;MACAkH,KAAK,GAAGnI,GAAG,CAACgE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGmE,KAAK;MACjCC,GAAG,GAAGpI,GAAG,CAACgE,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGoE,GAAG;IACjC;IACA,OAAO,IAAInJ,aAAa,CAACX,KAAK,EAAE,CAACuJ,iBAAiB,EAAErE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE2E,KAAK,EAAEC,GAAG,EAAErI,SAAS,CAAC;EAC5F,CAAC;EACDR,WAAW,CAAC7B,SAAS,CAACqI,mBAAmB,GAAG,UAAUzB,WAAW,EAAES,UAAU,EAAE;IAC3E,IAAIiB,MAAM,GAAG7G,SAAS,CAAC8F,GAAG,CAAC,IAAIC,UAAU,CAAC,IAAI,CAACE,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/D,IAAIe,OAAO,GAAGhH,SAAS,CAAC8F,GAAG,CAAC,IAAIC,UAAU,CAAC,IAAI,CAACI,aAAa,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI+C,YAAY,GAAG,KAAK;IACxB,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAIlE,WAAW,EAAE;MACb,IAAI0B,MAAM,GAAG,EAAE,EAAE;QACbsC,YAAY,GAAG,IAAI;MACvB,CAAC,MACI,IAAItC,MAAM,GAAG,CAAC,EAAE;QACjBqC,YAAY,GAAG,IAAI;MACvB;MACA,IAAIlC,OAAO,GAAG,EAAE,EAAE;QACdqC,aAAa,GAAG,IAAI;MACxB,CAAC,MACI,IAAIrC,OAAO,GAAG,CAAC,EAAE;QAClBoC,aAAa,GAAG,IAAI;MACxB;IACJ,CAAC,MACI;MACD,IAAIvC,MAAM,GAAG,EAAE,EAAE;QACbsC,YAAY,GAAG,IAAI;MACvB,CAAC,MACI,IAAItC,MAAM,GAAG,CAAC,EAAE;QACjBqC,YAAY,GAAG,IAAI;MACvB;MACA,IAAIlC,OAAO,GAAG,EAAE,EAAE;QACdqC,aAAa,GAAG,IAAI;MACxB,CAAC,MACI,IAAIrC,OAAO,GAAG,CAAC,EAAE;QAClBoC,aAAa,GAAG,IAAI;MACxB;IACJ;IACA,IAAIE,QAAQ,GAAGzC,MAAM,GAAGG,OAAO,GAAGpB,UAAU;IAC5C,IAAI2D,YAAY,GAAG,CAAC1C,MAAM,GAAG,IAAI,OAAO1B,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5D,IAAIqE,aAAa,GAAG,CAACxC,OAAO,GAAG,IAAI,MAAM,CAAC;IAC1C,IAAIsC,QAAQ,KAAK,CAAC,EAAE;MAChB,IAAIC,YAAY,EAAE;QACd,IAAIC,aAAa,EAAE;UACf,MAAM,IAAI9J,iBAAiB,CAAC,CAAC;QACjC;QACAyJ,YAAY,GAAG,IAAI;MACvB,CAAC,MACI;QACD,IAAI,CAACK,aAAa,EAAE;UAChB,MAAM,IAAI9J,iBAAiB,CAAC,CAAC;QACjC;QACA2J,aAAa,GAAG,IAAI;MACxB;IACJ,CAAC,MACI,IAAIC,QAAQ,KAAK,CAAC,CAAC,EAAE;MACtB,IAAIC,YAAY,EAAE;QACd,IAAIC,aAAa,EAAE;UACf,MAAM,IAAI9J,iBAAiB,CAAC,CAAC;QACjC;QACAwJ,YAAY,GAAG,IAAI;MACvB,CAAC,MACI;QACD,IAAI,CAACM,aAAa,EAAE;UAChB,MAAM,IAAI9J,iBAAiB,CAAC,CAAC;QACjC;QACA0J,aAAa,GAAG,IAAI;MACxB;IACJ,CAAC,MACI,IAAIE,QAAQ,KAAK,CAAC,EAAE;MACrB,IAAIC,YAAY,EAAE;QACd,IAAI,CAACC,aAAa,EAAE;UAChB,MAAM,IAAI9J,iBAAiB,CAAC,CAAC;QACjC;QACA;QACA,IAAImH,MAAM,GAAGG,OAAO,EAAE;UAClBkC,YAAY,GAAG,IAAI;UACnBG,aAAa,GAAG,IAAI;QACxB,CAAC,MACI;UACDF,YAAY,GAAG,IAAI;UACnBC,aAAa,GAAG,IAAI;QACxB;MACJ,CAAC,MACI;QACD,IAAII,aAAa,EAAE;UACf,MAAM,IAAI9J,iBAAiB,CAAC,CAAC;QACjC;QACA;MACJ;IACJ,CAAC,MACI;MACD,MAAM,IAAIA,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAIwJ,YAAY,EAAE;MACd,IAAIC,YAAY,EAAE;QACd,MAAM,IAAIzJ,iBAAiB,CAAC,CAAC;MACjC;MACAJ,iBAAiB,CAACmK,SAAS,CAAC,IAAI,CAACxD,YAAY,CAAC,CAAC,EAAE,IAAI,CAACI,oBAAoB,CAAC,CAAC,CAAC;IACjF;IACA,IAAI8C,YAAY,EAAE;MACd7J,iBAAiB,CAACoK,SAAS,CAAC,IAAI,CAACzD,YAAY,CAAC,CAAC,EAAE,IAAI,CAACI,oBAAoB,CAAC,CAAC,CAAC;IACjF;IACA,IAAI+C,aAAa,EAAE;MACf,IAAIC,aAAa,EAAE;QACf,MAAM,IAAI3J,iBAAiB,CAAC,CAAC;MACjC;MACAJ,iBAAiB,CAACmK,SAAS,CAAC,IAAI,CAACtD,aAAa,CAAC,CAAC,EAAE,IAAI,CAACE,oBAAoB,CAAC,CAAC,CAAC;IAClF;IACA,IAAIgD,aAAa,EAAE;MACf/J,iBAAiB,CAACoK,SAAS,CAAC,IAAI,CAACvD,aAAa,CAAC,CAAC,EAAE,IAAI,CAACI,qBAAqB,CAAC,CAAC,CAAC;IACnF;EACJ,CAAC;EACDnG,WAAW,CAACsH,yBAAyB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC5DtH,WAAW,CAAC2H,uBAAuB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACrD3H,WAAW,CAACwH,YAAY,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;EACpDxH,WAAW,CAAC4H,WAAW,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9C5H,WAAW,CAACgH,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChDhH,WAAW,CAACyH,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC5CzH,WAAW,CAAC2I,eAAe,GAAG,CAC1BhD,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B5D,UAAU,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAChC;EACD,OAAOvJ,WAAW;AACtB,CAAC,CAACd,iBAAiB,CAAE;AACrB,eAAec,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}