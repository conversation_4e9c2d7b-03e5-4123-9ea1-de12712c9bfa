# Developer User & Role Management Guide

## 🔒 Security Overview

The Developer user and Developer role are **hidden from the standard User Management interface** for security reasons. This prevents unauthorized access to system-level privileges and protects against accidental modification or deletion.

## 🚫 Restrictions Implemented

### 1. **User Interface Restrictions**
- ✅ Developer role is **filtered out** from role selection dropdowns
- ✅ Developer user is **hidden** from user lists and management pages
- ✅ Developer role is **hidden** from role management pages
- ✅ Backend validation prevents Developer role assignment

### 2. **Backend Protection**
- ✅ API endpoints reject attempts to assign Developer role to users
- ✅ Built-in users (including Developer) cannot be modified via standard routes
- ✅ Built-in roles (including <PERSON><PERSON><PERSON>) cannot be modified via standard routes

## 🛠️ How to Update Developer Information

### **Method 1: Direct Database Access (Recommended)**

#### **Update Developer User Information:**
```javascript
// Connect to MongoDB and run these commands
use unicore_business_suite

// Update Developer user details
db.users.updateOne(
  { username: "developer" },
  {
    $set: {
      email: "<EMAIL>",
      firstName: "New First Name",
      lastName: "New Last Name",
      updatedAt: new Date()
    }
  }
)

// Update Developer password (will be hashed on next login)
const bcrypt = require('bcrypt');
const newPassword = await bcrypt.hash('NewPassword@123', 12);
db.users.updateOne(
  { username: "developer" },
  {
    $set: {
      password: newPassword,
      updatedAt: new Date()
    }
  }
)
```

#### **Update Developer Role Information:**
```javascript
// Update Developer role details
db.roles.updateOne(
  { name: "developer" },
  {
    $set: {
      displayName: "System Developer",
      description: "Full system access for development team",
      updatedAt: new Date()
    }
  }
)
```

### **Method 2: Temporary API Endpoint (Advanced)**

Create a temporary, secured endpoint for Developer management:

#### **Step 1: Create Temporary Route**
```javascript
// backend/routes/developerRoutes.js
const express = require('express');
const router = express.Router();
const User = require('../models/User');
const Role = require('../models/Role');
const auth = require('../middleware/auth');

// Middleware to ensure only current Developer can access
const developerOnlyAuth = async (req, res, next) => {
  if (req.user.username !== 'developer') {
    return res.status(403).json({ message: 'Access denied' });
  }
  next();
};

// Update Developer user
router.put('/user', auth, developerOnlyAuth, async (req, res) => {
  try {
    const { email, firstName, lastName } = req.body;
    
    await User.findOneAndUpdate(
      { username: 'developer' },
      { email, firstName, lastName, updatedAt: new Date() }
    );
    
    res.json({ message: 'Developer user updated successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error updating developer user' });
  }
});

// Update Developer role
router.put('/role', auth, developerOnlyAuth, async (req, res) => {
  try {
    const { displayName, description } = req.body;
    
    await Role.findOneAndUpdate(
      { name: 'developer' },
      { displayName, description, updatedAt: new Date() }
    );
    
    res.json({ message: 'Developer role updated successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error updating developer role' });
  }
});

module.exports = router;
```

#### **Step 2: Add Route to Server**
```javascript
// backend/server.js
app.use('/api/developer', require('./routes/developerRoutes'));
```

#### **Step 3: Use the Endpoint**
```bash
# Update Developer user
curl -X PUT http://localhost:5000/api/developer/user \
  -H "Authorization: Bearer YOUR_DEVELOPER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "firstName": "New Name",
    "lastName": "New Surname"
  }'

# Update Developer role
curl -X PUT http://localhost:5000/api/developer/role \
  -H "Authorization: Bearer YOUR_DEVELOPER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "displayName": "System Developer",
    "description": "Full system access for development team"
  }'
```

#### **Step 4: Remove Route After Use**
**Important:** Remove the temporary route after making changes for security.

### **Method 3: Seeder Script Modification**

#### **Update the Seeder File:**
```javascript
// backend/seeders/userRoleSeeder.js
// Modify the Developer user creation section:

const developerUser = await User.create({
  username: 'developer',
  email: '<EMAIL>',  // Update this
  password: 'YourNewPassword@123',       // Update this
  firstName: 'Your',                     // Update this
  lastName: 'Name',                      // Update this
  role: developerRole._id,
  isBuiltIn: true
});
```

#### **Re-run the Seeder:**
```bash
# Clear existing data and re-seed (WARNING: This will reset all data)
# Only use this in development environment
node -e "
const { seedUserRoleSystem } = require('./backend/seeders/userRoleSeeder');
const mongoose = require('mongoose');
mongoose.connect('your-mongodb-connection-string');
seedUserRoleSystem().then(() => process.exit());
"
```

## ⚠️ Important Security Notes

1. **Database Access**: Ensure only authorized personnel have direct database access
2. **Temporary Routes**: Always remove temporary API endpoints after use
3. **Password Security**: Use strong passwords and consider regular rotation
4. **Audit Trail**: Keep records of any Developer account modifications
5. **Backup**: Always backup the database before making direct modifications

## 🔍 Verification

After making changes, verify the updates:

```javascript
// Check Developer user
db.users.findOne({ username: "developer" })

// Check Developer role
db.roles.findOne({ name: "developer" })
```

## 📞 Support

If you need assistance with Developer account management:
1. Ensure you have proper database access credentials
2. Test changes in a development environment first
3. Keep backups of original data
4. Document all changes made

---

**Remember**: The Developer account has full system access. Handle with extreme care and follow your organization's security protocols.
