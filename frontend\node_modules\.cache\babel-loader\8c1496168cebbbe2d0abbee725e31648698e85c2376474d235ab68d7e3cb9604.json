{"ast": null, "code": "/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar Token = /** @class */function () {\n  function Token(previous) {\n    this.previous = previous;\n  }\n  Token.prototype.getPrevious = function () {\n    return this.previous;\n  };\n  return Token;\n}();\nexport default Token;", "map": {"version": 3, "names": ["Token", "previous", "prototype", "getPrevious"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/Token.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar Token = /** @class */ (function () {\n    function Token(previous) {\n        this.previous = previous;\n    }\n    Token.prototype.getPrevious = function () {\n        return this.previous;\n    };\n    return Token;\n}());\nexport default Token;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,KAAK,GAAG,aAAe,YAAY;EACnC,SAASA,KAAKA,CAACC,QAAQ,EAAE;IACrB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAD,KAAK,CAACE,SAAS,CAACC,WAAW,GAAG,YAAY;IACtC,OAAO,IAAI,CAACF,QAAQ;EACxB,CAAC;EACD,OAAOD,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}