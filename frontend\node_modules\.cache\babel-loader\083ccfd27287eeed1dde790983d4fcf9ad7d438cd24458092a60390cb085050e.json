{"ast": null, "code": "/**\n * Set of CharsetEncoders for a given input string\n *\n * Invariants:\n * - The list contains only encoders from CharacterSetECI (list is shorter then the list of encoders available on\n *   the platform for which ECI values are defined).\n * - The list contains encoders at least one encoder for every character in the input.\n * - The first encoder in the list is always the ISO-8859-1 encoder even of no character in the input can be encoded\n *       by it.\n * - If the input contains a character that is not in ISO-8859-1 then the last two entries in the list will be the\n *   UTF-8 encoder and the UTF-16BE encoder.\n *\n * <AUTHOR>\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport Charset from '../util/Charset';\nimport StandardCharsets from '../util/StandardCharsets';\nimport StringEncoding from '../util/StringEncoding';\nimport StringUtils from './StringUtils';\nvar CharsetEncoder = /** @class */function () {\n  function CharsetEncoder(charset) {\n    this.charset = charset;\n    this.name = charset.name;\n  }\n  CharsetEncoder.prototype.canEncode = function (c) {\n    try {\n      return StringEncoding.encode(c, this.charset) != null;\n    } catch (ex) {\n      return false;\n    }\n  };\n  return CharsetEncoder;\n}();\nvar ECIEncoderSet = /** @class */function () {\n  /**\n   * Constructs an encoder set\n   *\n   * @param stringToEncode the string that needs to be encoded\n   * @param priorityCharset The preferred {@link Charset} or null.\n   * @param fnc1 fnc1 denotes the character in the input that represents the FNC1 character or -1 for a non-GS1 bar\n   * code. When specified, it is considered an error to pass it as argument to the methods canEncode() or encode().\n   */\n  function ECIEncoderSet(stringToEncode, priorityCharset, fnc1) {\n    var e_1, _a, e_2, _b, e_3, _c;\n    this.ENCODERS = ['IBM437', 'ISO-8859-2', 'ISO-8859-3', 'ISO-8859-4', 'ISO-8859-5', 'ISO-8859-6', 'ISO-8859-7', 'ISO-8859-8', 'ISO-8859-9', 'ISO-8859-10', 'ISO-8859-11', 'ISO-8859-13', 'ISO-8859-14', 'ISO-8859-15', 'ISO-8859-16', 'windows-1250', 'windows-1251', 'windows-1252', 'windows-1256', 'Shift_JIS'].map(function (name) {\n      return new CharsetEncoder(Charset.forName(name));\n    });\n    this.encoders = [];\n    var neededEncoders = [];\n    // we always need the ISO-8859-1 encoder. It is the default encoding\n    neededEncoders.push(new CharsetEncoder(StandardCharsets.ISO_8859_1));\n    var needUnicodeEncoder = priorityCharset != null && priorityCharset.name.startsWith('UTF');\n    // Walk over the input string and see if all characters can be encoded with the list of encoders\n    for (var i = 0; i < stringToEncode.length; i++) {\n      var canEncode = false;\n      try {\n        for (var neededEncoders_1 = (e_1 = void 0, __values(neededEncoders)), neededEncoders_1_1 = neededEncoders_1.next(); !neededEncoders_1_1.done; neededEncoders_1_1 = neededEncoders_1.next()) {\n          var encoder = neededEncoders_1_1.value;\n          var singleCharacter = stringToEncode.charAt(i);\n          var c = singleCharacter.charCodeAt(0);\n          if (c === fnc1 || encoder.canEncode(singleCharacter)) {\n            canEncode = true;\n            break;\n          }\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (neededEncoders_1_1 && !neededEncoders_1_1.done && (_a = neededEncoders_1.return)) _a.call(neededEncoders_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      if (!canEncode) {\n        try {\n          // for the character at position i we don't yet have an encoder in the list\n          for (var _d = (e_2 = void 0, __values(this.ENCODERS)), _e = _d.next(); !_e.done; _e = _d.next()) {\n            var encoder = _e.value;\n            if (encoder.canEncode(stringToEncode.charAt(i))) {\n              // Good, we found an encoder that can encode the character. We add him to the list and continue scanning\n              // the input\n              neededEncoders.push(encoder);\n              canEncode = true;\n              break;\n            }\n          }\n        } catch (e_2_1) {\n          e_2 = {\n            error: e_2_1\n          };\n        } finally {\n          try {\n            if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n          } finally {\n            if (e_2) throw e_2.error;\n          }\n        }\n      }\n      if (!canEncode) {\n        // The character is not encodeable by any of the single byte encoders so we remember that we will need a\n        // Unicode encoder.\n        needUnicodeEncoder = true;\n      }\n    }\n    if (neededEncoders.length === 1 && !needUnicodeEncoder) {\n      // the entire input can be encoded by the ISO-8859-1 encoder\n      this.encoders = [neededEncoders[0]];\n    } else {\n      // we need more than one single byte encoder or we need a Unicode encoder.\n      // In this case we append a UTF-8 and UTF-16 encoder to the list\n      this.encoders = [];\n      var index = 0;\n      try {\n        for (var neededEncoders_2 = __values(neededEncoders), neededEncoders_2_1 = neededEncoders_2.next(); !neededEncoders_2_1.done; neededEncoders_2_1 = neededEncoders_2.next()) {\n          var encoder = neededEncoders_2_1.value;\n          this.encoders[index++] = encoder;\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (neededEncoders_2_1 && !neededEncoders_2_1.done && (_c = neededEncoders_2.return)) _c.call(neededEncoders_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n      // this.encoders[index] = new CharsetEncoder(StandardCharsets.UTF_8);\n      // this.encoders[index + 1] = new CharsetEncoder(StandardCharsets.UTF_16BE);\n    }\n    // Compute priorityEncoderIndex by looking up priorityCharset in encoders\n    var priorityEncoderIndexValue = -1;\n    if (priorityCharset != null) {\n      for (var i = 0; i < this.encoders.length; i++) {\n        if (this.encoders[i] != null && priorityCharset.name === this.encoders[i].name) {\n          priorityEncoderIndexValue = i;\n          break;\n        }\n      }\n    }\n    this.priorityEncoderIndex = priorityEncoderIndexValue;\n    // invariants\n    // if(this?.encoders?.[0].name !== StandardCharsets.ISO_8859_1)){\n    // throw new Error(\"ISO-8859-1 must be the first encoder\");\n    // }\n  }\n  ECIEncoderSet.prototype.length = function () {\n    return this.encoders.length;\n  };\n  ECIEncoderSet.prototype.getCharsetName = function (index) {\n    if (!(index < this.length())) {\n      throw new Error('index must be less than length');\n    }\n    return this.encoders[index].name;\n  };\n  ECIEncoderSet.prototype.getCharset = function (index) {\n    if (!(index < this.length())) {\n      throw new Error('index must be less than length');\n    }\n    return this.encoders[index].charset;\n  };\n  ECIEncoderSet.prototype.getECIValue = function (encoderIndex) {\n    return this.encoders[encoderIndex].charset.getValueIdentifier();\n  };\n  /*\n   *  returns -1 if no priority charset was defined\n   */\n  ECIEncoderSet.prototype.getPriorityEncoderIndex = function () {\n    return this.priorityEncoderIndex;\n  };\n  ECIEncoderSet.prototype.canEncode = function (c, encoderIndex) {\n    if (!(encoderIndex < this.length())) {\n      throw new Error('index must be less than length');\n    }\n    return true;\n  };\n  ECIEncoderSet.prototype.encode = function (c, encoderIndex) {\n    if (!(encoderIndex < this.length())) {\n      throw new Error('index must be less than length');\n    }\n    return StringEncoding.encode(StringUtils.getCharAt(c), this.encoders[encoderIndex].name);\n  };\n  return ECIEncoderSet;\n}();\nexport { ECIEncoderSet };", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "Charset", "StandardCharsets", "StringEncoding", "StringUtils", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "charset", "name", "prototype", "canEncode", "c", "encode", "ex", "ECIEncoderSet", "stringToEncode", "priorityCharset", "fnc1", "e_1", "_a", "e_2", "_b", "e_3", "_c", "ENCODERS", "map", "forName", "encoders", "neededEncoders", "push", "ISO_8859_1", "needUnicodeEncoder", "startsWith", "neededEncoders_1", "neededEncoders_1_1", "encoder", "singleCharacter", "char<PERSON>t", "charCodeAt", "e_1_1", "error", "return", "_d", "_e", "e_2_1", "index", "neededEncoders_2", "neededEncoders_2_1", "e_3_1", "priorityEncoderIndexValue", "priorityEncoderIndex", "getCharsetName", "Error", "getCharset", "getECIValue", "encoderIndex", "getValueIdentifier", "getPriorityEncoderIndex", "getCharAt"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/ECIEncoderSet.js"], "sourcesContent": ["/**\n * Set of CharsetEncoders for a given input string\n *\n * Invariants:\n * - The list contains only encoders from CharacterSetECI (list is shorter then the list of encoders available on\n *   the platform for which ECI values are defined).\n * - The list contains encoders at least one encoder for every character in the input.\n * - The first encoder in the list is always the ISO-8859-1 encoder even of no character in the input can be encoded\n *       by it.\n * - If the input contains a character that is not in ISO-8859-1 then the last two entries in the list will be the\n *   UTF-8 encoder and the UTF-16BE encoder.\n *\n * <AUTHOR>\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport Charset from '../util/Charset';\nimport StandardCharsets from '../util/StandardCharsets';\nimport StringEncoding from '../util/StringEncoding';\nimport StringUtils from './StringUtils';\nvar CharsetEncoder = /** @class */ (function () {\n    function CharsetEncoder(charset) {\n        this.charset = charset;\n        this.name = charset.name;\n    }\n    CharsetEncoder.prototype.canEncode = function (c) {\n        try {\n            return StringEncoding.encode(c, this.charset) != null;\n        }\n        catch (ex) {\n            return false;\n        }\n    };\n    return CharsetEncoder;\n}());\nvar ECIEncoderSet = /** @class */ (function () {\n    /**\n     * Constructs an encoder set\n     *\n     * @param stringToEncode the string that needs to be encoded\n     * @param priorityCharset The preferred {@link Charset} or null.\n     * @param fnc1 fnc1 denotes the character in the input that represents the FNC1 character or -1 for a non-GS1 bar\n     * code. When specified, it is considered an error to pass it as argument to the methods canEncode() or encode().\n     */\n    function ECIEncoderSet(stringToEncode, priorityCharset, fnc1) {\n        var e_1, _a, e_2, _b, e_3, _c;\n        this.ENCODERS = [\n            'IBM437',\n            'ISO-8859-2',\n            'ISO-8859-3',\n            'ISO-8859-4',\n            'ISO-8859-5',\n            'ISO-8859-6',\n            'ISO-8859-7',\n            'ISO-8859-8',\n            'ISO-8859-9',\n            'ISO-8859-10',\n            'ISO-8859-11',\n            'ISO-8859-13',\n            'ISO-8859-14',\n            'ISO-8859-15',\n            'ISO-8859-16',\n            'windows-1250',\n            'windows-1251',\n            'windows-1252',\n            'windows-1256',\n            'Shift_JIS',\n        ].map(function (name) { return new CharsetEncoder(Charset.forName(name)); });\n        this.encoders = [];\n        var neededEncoders = [];\n        // we always need the ISO-8859-1 encoder. It is the default encoding\n        neededEncoders.push(new CharsetEncoder(StandardCharsets.ISO_8859_1));\n        var needUnicodeEncoder = priorityCharset != null && priorityCharset.name.startsWith('UTF');\n        // Walk over the input string and see if all characters can be encoded with the list of encoders\n        for (var i = 0; i < stringToEncode.length; i++) {\n            var canEncode = false;\n            try {\n                for (var neededEncoders_1 = (e_1 = void 0, __values(neededEncoders)), neededEncoders_1_1 = neededEncoders_1.next(); !neededEncoders_1_1.done; neededEncoders_1_1 = neededEncoders_1.next()) {\n                    var encoder = neededEncoders_1_1.value;\n                    var singleCharacter = stringToEncode.charAt(i);\n                    var c = singleCharacter.charCodeAt(0);\n                    if (c === fnc1 || encoder.canEncode(singleCharacter)) {\n                        canEncode = true;\n                        break;\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (neededEncoders_1_1 && !neededEncoders_1_1.done && (_a = neededEncoders_1.return)) _a.call(neededEncoders_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (!canEncode) {\n                try {\n                    // for the character at position i we don't yet have an encoder in the list\n                    for (var _d = (e_2 = void 0, __values(this.ENCODERS)), _e = _d.next(); !_e.done; _e = _d.next()) {\n                        var encoder = _e.value;\n                        if (encoder.canEncode(stringToEncode.charAt(i))) {\n                            // Good, we found an encoder that can encode the character. We add him to the list and continue scanning\n                            // the input\n                            neededEncoders.push(encoder);\n                            canEncode = true;\n                            break;\n                        }\n                    }\n                }\n                catch (e_2_1) { e_2 = { error: e_2_1 }; }\n                finally {\n                    try {\n                        if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n                    }\n                    finally { if (e_2) throw e_2.error; }\n                }\n            }\n            if (!canEncode) {\n                // The character is not encodeable by any of the single byte encoders so we remember that we will need a\n                // Unicode encoder.\n                needUnicodeEncoder = true;\n            }\n        }\n        if (neededEncoders.length === 1 && !needUnicodeEncoder) {\n            // the entire input can be encoded by the ISO-8859-1 encoder\n            this.encoders = [neededEncoders[0]];\n        }\n        else {\n            // we need more than one single byte encoder or we need a Unicode encoder.\n            // In this case we append a UTF-8 and UTF-16 encoder to the list\n            this.encoders = [];\n            var index = 0;\n            try {\n                for (var neededEncoders_2 = __values(neededEncoders), neededEncoders_2_1 = neededEncoders_2.next(); !neededEncoders_2_1.done; neededEncoders_2_1 = neededEncoders_2.next()) {\n                    var encoder = neededEncoders_2_1.value;\n                    this.encoders[index++] = encoder;\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (neededEncoders_2_1 && !neededEncoders_2_1.done && (_c = neededEncoders_2.return)) _c.call(neededEncoders_2);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n            // this.encoders[index] = new CharsetEncoder(StandardCharsets.UTF_8);\n            // this.encoders[index + 1] = new CharsetEncoder(StandardCharsets.UTF_16BE);\n        }\n        // Compute priorityEncoderIndex by looking up priorityCharset in encoders\n        var priorityEncoderIndexValue = -1;\n        if (priorityCharset != null) {\n            for (var i = 0; i < this.encoders.length; i++) {\n                if (this.encoders[i] != null &&\n                    priorityCharset.name === this.encoders[i].name) {\n                    priorityEncoderIndexValue = i;\n                    break;\n                }\n            }\n        }\n        this.priorityEncoderIndex = priorityEncoderIndexValue;\n        // invariants\n        // if(this?.encoders?.[0].name !== StandardCharsets.ISO_8859_1)){\n        // throw new Error(\"ISO-8859-1 must be the first encoder\");\n        // }\n    }\n    ECIEncoderSet.prototype.length = function () {\n        return this.encoders.length;\n    };\n    ECIEncoderSet.prototype.getCharsetName = function (index) {\n        if (!(index < this.length())) {\n            throw new Error('index must be less than length');\n        }\n        return this.encoders[index].name;\n    };\n    ECIEncoderSet.prototype.getCharset = function (index) {\n        if (!(index < this.length())) {\n            throw new Error('index must be less than length');\n        }\n        return this.encoders[index].charset;\n    };\n    ECIEncoderSet.prototype.getECIValue = function (encoderIndex) {\n        return this.encoders[encoderIndex].charset.getValueIdentifier();\n    };\n    /*\n     *  returns -1 if no priority charset was defined\n     */\n    ECIEncoderSet.prototype.getPriorityEncoderIndex = function () {\n        return this.priorityEncoderIndex;\n    };\n    ECIEncoderSet.prototype.canEncode = function (c, encoderIndex) {\n        if (!(encoderIndex < this.length())) {\n            throw new Error('index must be less than length');\n        }\n        return true;\n    };\n    ECIEncoderSet.prototype.encode = function (c, encoderIndex) {\n        if (!(encoderIndex < this.length())) {\n            throw new Error('index must be less than length');\n        }\n        return StringEncoding.encode(StringUtils.getCharAt(c), this.encoders[encoderIndex].name);\n    };\n    return ECIEncoderSet;\n}());\nexport { ECIEncoderSet };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,OAAO,MAAM,iBAAiB;AACrC,OAAOC,gBAAgB,MAAM,0BAA0B;AACvD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,WAAW,MAAM,eAAe;AACvC,IAAIC,cAAc,GAAG,aAAe,YAAY;EAC5C,SAASA,cAAcA,CAACC,OAAO,EAAE;IAC7B,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGD,OAAO,CAACC,IAAI;EAC5B;EACAF,cAAc,CAACG,SAAS,CAACC,SAAS,GAAG,UAAUC,CAAC,EAAE;IAC9C,IAAI;MACA,OAAOP,cAAc,CAACQ,MAAM,CAACD,CAAC,EAAE,IAAI,CAACJ,OAAO,CAAC,IAAI,IAAI;IACzD,CAAC,CACD,OAAOM,EAAE,EAAE;MACP,OAAO,KAAK;IAChB;EACJ,CAAC;EACD,OAAOP,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,IAAIQ,aAAa,GAAG,aAAe,YAAY;EAC3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,aAAaA,CAACC,cAAc,EAAEC,eAAe,EAAEC,IAAI,EAAE;IAC1D,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IAC7B,IAAI,CAACC,QAAQ,GAAG,CACZ,QAAQ,EACR,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,aAAa,EACb,aAAa,EACb,aAAa,EACb,aAAa,EACb,aAAa,EACb,cAAc,EACd,cAAc,EACd,cAAc,EACd,cAAc,EACd,WAAW,CACd,CAACC,GAAG,CAAC,UAAUjB,IAAI,EAAE;MAAE,OAAO,IAAIF,cAAc,CAACJ,OAAO,CAACwB,OAAO,CAAClB,IAAI,CAAC,CAAC;IAAE,CAAC,CAAC;IAC5E,IAAI,CAACmB,QAAQ,GAAG,EAAE;IAClB,IAAIC,cAAc,GAAG,EAAE;IACvB;IACAA,cAAc,CAACC,IAAI,CAAC,IAAIvB,cAAc,CAACH,gBAAgB,CAAC2B,UAAU,CAAC,CAAC;IACpE,IAAIC,kBAAkB,GAAGf,eAAe,IAAI,IAAI,IAAIA,eAAe,CAACR,IAAI,CAACwB,UAAU,CAAC,KAAK,CAAC;IAC1F;IACA,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,cAAc,CAAClB,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5C,IAAIe,SAAS,GAAG,KAAK;MACrB,IAAI;QACA,KAAK,IAAIuB,gBAAgB,IAAIf,GAAG,GAAG,KAAK,CAAC,EAAE7B,QAAQ,CAACuC,cAAc,CAAC,CAAC,EAAEM,kBAAkB,GAAGD,gBAAgB,CAACnC,IAAI,CAAC,CAAC,EAAE,CAACoC,kBAAkB,CAAClC,IAAI,EAAEkC,kBAAkB,GAAGD,gBAAgB,CAACnC,IAAI,CAAC,CAAC,EAAE;UACxL,IAAIqC,OAAO,GAAGD,kBAAkB,CAACnC,KAAK;UACtC,IAAIqC,eAAe,GAAGrB,cAAc,CAACsB,MAAM,CAAC1C,CAAC,CAAC;UAC9C,IAAIgB,CAAC,GAAGyB,eAAe,CAACE,UAAU,CAAC,CAAC,CAAC;UACrC,IAAI3B,CAAC,KAAKM,IAAI,IAAIkB,OAAO,CAACzB,SAAS,CAAC0B,eAAe,CAAC,EAAE;YAClD1B,SAAS,GAAG,IAAI;YAChB;UACJ;QACJ;MACJ,CAAC,CACD,OAAO6B,KAAK,EAAE;QAAErB,GAAG,GAAG;UAAEsB,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIL,kBAAkB,IAAI,CAACA,kBAAkB,CAAClC,IAAI,KAAKmB,EAAE,GAAGc,gBAAgB,CAACQ,MAAM,CAAC,EAAEtB,EAAE,CAACvB,IAAI,CAACqC,gBAAgB,CAAC;QACnH,CAAC,SACO;UAAE,IAAIf,GAAG,EAAE,MAAMA,GAAG,CAACsB,KAAK;QAAE;MACxC;MACA,IAAI,CAAC9B,SAAS,EAAE;QACZ,IAAI;UACA;UACA,KAAK,IAAIgC,EAAE,IAAItB,GAAG,GAAG,KAAK,CAAC,EAAE/B,QAAQ,CAAC,IAAI,CAACmC,QAAQ,CAAC,CAAC,EAAEmB,EAAE,GAAGD,EAAE,CAAC5C,IAAI,CAAC,CAAC,EAAE,CAAC6C,EAAE,CAAC3C,IAAI,EAAE2C,EAAE,GAAGD,EAAE,CAAC5C,IAAI,CAAC,CAAC,EAAE;YAC7F,IAAIqC,OAAO,GAAGQ,EAAE,CAAC5C,KAAK;YACtB,IAAIoC,OAAO,CAACzB,SAAS,CAACK,cAAc,CAACsB,MAAM,CAAC1C,CAAC,CAAC,CAAC,EAAE;cAC7C;cACA;cACAiC,cAAc,CAACC,IAAI,CAACM,OAAO,CAAC;cAC5BzB,SAAS,GAAG,IAAI;cAChB;YACJ;UACJ;QACJ,CAAC,CACD,OAAOkC,KAAK,EAAE;UAAExB,GAAG,GAAG;YAAEoB,KAAK,EAAEI;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAID,EAAE,IAAI,CAACA,EAAE,CAAC3C,IAAI,KAAKqB,EAAE,GAAGqB,EAAE,CAACD,MAAM,CAAC,EAAEpB,EAAE,CAACzB,IAAI,CAAC8C,EAAE,CAAC;UACvD,CAAC,SACO;YAAE,IAAItB,GAAG,EAAE,MAAMA,GAAG,CAACoB,KAAK;UAAE;QACxC;MACJ;MACA,IAAI,CAAC9B,SAAS,EAAE;QACZ;QACA;QACAqB,kBAAkB,GAAG,IAAI;MAC7B;IACJ;IACA,IAAIH,cAAc,CAAC/B,MAAM,KAAK,CAAC,IAAI,CAACkC,kBAAkB,EAAE;MACpD;MACA,IAAI,CAACJ,QAAQ,GAAG,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,MACI;MACD;MACA;MACA,IAAI,CAACD,QAAQ,GAAG,EAAE;MAClB,IAAIkB,KAAK,GAAG,CAAC;MACb,IAAI;QACA,KAAK,IAAIC,gBAAgB,GAAGzD,QAAQ,CAACuC,cAAc,CAAC,EAAEmB,kBAAkB,GAAGD,gBAAgB,CAAChD,IAAI,CAAC,CAAC,EAAE,CAACiD,kBAAkB,CAAC/C,IAAI,EAAE+C,kBAAkB,GAAGD,gBAAgB,CAAChD,IAAI,CAAC,CAAC,EAAE;UACxK,IAAIqC,OAAO,GAAGY,kBAAkB,CAAChD,KAAK;UACtC,IAAI,CAAC4B,QAAQ,CAACkB,KAAK,EAAE,CAAC,GAAGV,OAAO;QACpC;MACJ,CAAC,CACD,OAAOa,KAAK,EAAE;QAAE1B,GAAG,GAAG;UAAEkB,KAAK,EAAEQ;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAID,kBAAkB,IAAI,CAACA,kBAAkB,CAAC/C,IAAI,KAAKuB,EAAE,GAAGuB,gBAAgB,CAACL,MAAM,CAAC,EAAElB,EAAE,CAAC3B,IAAI,CAACkD,gBAAgB,CAAC;QACnH,CAAC,SACO;UAAE,IAAIxB,GAAG,EAAE,MAAMA,GAAG,CAACkB,KAAK;QAAE;MACxC;MACA;MACA;IACJ;IACA;IACA,IAAIS,yBAAyB,GAAG,CAAC,CAAC;IAClC,IAAIjC,eAAe,IAAI,IAAI,EAAE;MACzB,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACgC,QAAQ,CAAC9B,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC3C,IAAI,IAAI,CAACgC,QAAQ,CAAChC,CAAC,CAAC,IAAI,IAAI,IACxBqB,eAAe,CAACR,IAAI,KAAK,IAAI,CAACmB,QAAQ,CAAChC,CAAC,CAAC,CAACa,IAAI,EAAE;UAChDyC,yBAAyB,GAAGtD,CAAC;UAC7B;QACJ;MACJ;IACJ;IACA,IAAI,CAACuD,oBAAoB,GAAGD,yBAAyB;IACrD;IACA;IACA;IACA;EACJ;EACAnC,aAAa,CAACL,SAAS,CAACZ,MAAM,GAAG,YAAY;IACzC,OAAO,IAAI,CAAC8B,QAAQ,CAAC9B,MAAM;EAC/B,CAAC;EACDiB,aAAa,CAACL,SAAS,CAAC0C,cAAc,GAAG,UAAUN,KAAK,EAAE;IACtD,IAAI,EAAEA,KAAK,GAAG,IAAI,CAAChD,MAAM,CAAC,CAAC,CAAC,EAAE;MAC1B,MAAM,IAAIuD,KAAK,CAAC,gCAAgC,CAAC;IACrD;IACA,OAAO,IAAI,CAACzB,QAAQ,CAACkB,KAAK,CAAC,CAACrC,IAAI;EACpC,CAAC;EACDM,aAAa,CAACL,SAAS,CAAC4C,UAAU,GAAG,UAAUR,KAAK,EAAE;IAClD,IAAI,EAAEA,KAAK,GAAG,IAAI,CAAChD,MAAM,CAAC,CAAC,CAAC,EAAE;MAC1B,MAAM,IAAIuD,KAAK,CAAC,gCAAgC,CAAC;IACrD;IACA,OAAO,IAAI,CAACzB,QAAQ,CAACkB,KAAK,CAAC,CAACtC,OAAO;EACvC,CAAC;EACDO,aAAa,CAACL,SAAS,CAAC6C,WAAW,GAAG,UAAUC,YAAY,EAAE;IAC1D,OAAO,IAAI,CAAC5B,QAAQ,CAAC4B,YAAY,CAAC,CAAChD,OAAO,CAACiD,kBAAkB,CAAC,CAAC;EACnE,CAAC;EACD;AACJ;AACA;EACI1C,aAAa,CAACL,SAAS,CAACgD,uBAAuB,GAAG,YAAY;IAC1D,OAAO,IAAI,CAACP,oBAAoB;EACpC,CAAC;EACDpC,aAAa,CAACL,SAAS,CAACC,SAAS,GAAG,UAAUC,CAAC,EAAE4C,YAAY,EAAE;IAC3D,IAAI,EAAEA,YAAY,GAAG,IAAI,CAAC1D,MAAM,CAAC,CAAC,CAAC,EAAE;MACjC,MAAM,IAAIuD,KAAK,CAAC,gCAAgC,CAAC;IACrD;IACA,OAAO,IAAI;EACf,CAAC;EACDtC,aAAa,CAACL,SAAS,CAACG,MAAM,GAAG,UAAUD,CAAC,EAAE4C,YAAY,EAAE;IACxD,IAAI,EAAEA,YAAY,GAAG,IAAI,CAAC1D,MAAM,CAAC,CAAC,CAAC,EAAE;MACjC,MAAM,IAAIuD,KAAK,CAAC,gCAAgC,CAAC;IACrD;IACA,OAAOhD,cAAc,CAACQ,MAAM,CAACP,WAAW,CAACqD,SAAS,CAAC/C,CAAC,CAAC,EAAE,IAAI,CAACgB,QAAQ,CAAC4B,YAAY,CAAC,CAAC/C,IAAI,CAAC;EAC5F,CAAC;EACD,OAAOM,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,SAASA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}