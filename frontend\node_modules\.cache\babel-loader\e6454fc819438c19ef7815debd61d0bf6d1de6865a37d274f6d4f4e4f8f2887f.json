{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useIsLandscape } from \"../useIsLandscape.js\";\n\n/**\n * Props used to create the layout of the views.\n * Those props are exposed on all the pickers.\n */\n\n/**\n * Prepare the props for the view layout (managed by `PickersLayout`)\n */\nexport const usePickerLayoutProps = ({\n  props,\n  propsFromPickerValue,\n  propsFromPickerViews,\n  wrapperVariant\n}) => {\n  const {\n    orientation\n  } = props;\n  const isLandscape = useIsLandscape(propsFromPickerViews.views, orientation);\n  const isRtl = useRtl();\n  const layoutProps = _extends({}, propsFromPickerViews, propsFromPickerValue, {\n    isLandscape,\n    isRtl,\n    wrapperVariant,\n    disabled: props.disabled,\n    readOnly: props.readOnly\n  });\n  return {\n    layoutProps\n  };\n};", "map": {"version": 3, "names": ["_extends", "useRtl", "useIsLandscape", "usePickerLayoutProps", "props", "propsFromPickerValue", "propsFromPickerViews", "wrapperVariant", "orientation", "isLandscape", "views", "isRtl", "layoutProps", "disabled", "readOnly"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePickerLayoutProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useIsLandscape } from \"../useIsLandscape.js\";\n\n/**\n * Props used to create the layout of the views.\n * Those props are exposed on all the pickers.\n */\n\n/**\n * Prepare the props for the view layout (managed by `PickersLayout`)\n */\nexport const usePickerLayoutProps = ({\n  props,\n  propsFromPickerValue,\n  propsFromPickerViews,\n  wrapperVariant\n}) => {\n  const {\n    orientation\n  } = props;\n  const isLandscape = useIsLandscape(propsFromPickerViews.views, orientation);\n  const isRtl = useRtl();\n  const layoutProps = _extends({}, propsFromPickerViews, propsFromPickerValue, {\n    isLandscape,\n    isRtl,\n    wrapperVariant,\n    disabled: props.disabled,\n    readOnly: props.readOnly\n  });\n  return {\n    layoutProps\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,cAAc,QAAQ,sBAAsB;;AAErD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAAC;EACnCC,KAAK;EACLC,oBAAoB;EACpBC,oBAAoB;EACpBC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMK,WAAW,GAAGP,cAAc,CAACI,oBAAoB,CAACI,KAAK,EAAEF,WAAW,CAAC;EAC3E,MAAMG,KAAK,GAAGV,MAAM,CAAC,CAAC;EACtB,MAAMW,WAAW,GAAGZ,QAAQ,CAAC,CAAC,CAAC,EAAEM,oBAAoB,EAAED,oBAAoB,EAAE;IAC3EI,WAAW;IACXE,KAAK;IACLJ,cAAc;IACdM,QAAQ,EAAET,KAAK,CAACS,QAAQ;IACxBC,QAAQ,EAAEV,KAAK,CAACU;EAClB,CAAC,CAAC;EACF,OAAO;IACLF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}