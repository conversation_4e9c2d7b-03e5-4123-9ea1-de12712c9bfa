{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.encoder {*/\nimport BitArray from '../../common/BitArray';\nimport Integer from '../../util/Integer';\nimport QRCode from './QRCode';\nimport <PERSON>Util from './MaskUtil';\nimport WriterException from '../../WriterException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <AUTHOR> (<PERSON><PERSON>) - creator\n * <AUTHOR> (<PERSON>) - ported from C++\n */\nvar MatrixUtil = /** @class */function () {\n  function MatrixUtil() {\n    // do nothing\n  }\n  // Set all cells to -1 (TYPESCRIPTPORT: 255).  -1 (TYPESCRIPTPORT: 255) means that the cell is empty (not set yet).\n  //\n  // JAVAPORT: We shouldn't need to do this at all. The code should be rewritten to begin encoding\n  // with the ByteMatrix initialized all to zero.\n  MatrixUtil.clearMatrix = function (matrix) {\n    // TYPESCRIPTPORT: we use UintArray se changed here from -1 to 255\n    matrix.clear(/*(byte) */ /*-1*/255);\n  };\n  // Build 2D matrix of QR Code from \"dataBits\" with \"ecLevel\", \"version\" and \"getMaskPattern\". On\n  // success, store the result in \"matrix\" and return true.\n  MatrixUtil.buildMatrix = function (dataBits, ecLevel, version, maskPattern /*int*/, matrix) {\n    MatrixUtil.clearMatrix(matrix);\n    MatrixUtil.embedBasicPatterns(version, matrix);\n    // Type information appear with any version.\n    MatrixUtil.embedTypeInfo(ecLevel, maskPattern, matrix);\n    // Version info appear if version >= 7.\n    MatrixUtil.maybeEmbedVersionInfo(version, matrix);\n    // Data should be embedded at end.\n    MatrixUtil.embedDataBits(dataBits, maskPattern, matrix);\n  };\n  // Embed basic patterns. On success, modify the matrix and return true.\n  // The basic patterns are:\n  // - Position detection patterns\n  // - Timing patterns\n  // - Dark dot at the left bottom corner\n  // - Position adjustment patterns, if need be\n  MatrixUtil.embedBasicPatterns = function (version, matrix) {\n    // Let's get started with embedding big squares at corners.\n    MatrixUtil.embedPositionDetectionPatternsAndSeparators(matrix);\n    // Then, embed the dark dot at the left bottom corner.\n    MatrixUtil.embedDarkDotAtLeftBottomCorner(matrix);\n    // Position adjustment patterns appear if version >= 2.\n    MatrixUtil.maybeEmbedPositionAdjustmentPatterns(version, matrix);\n    // Timing patterns should be embedded after position adj. patterns.\n    MatrixUtil.embedTimingPatterns(matrix);\n  };\n  // Embed type information. On success, modify the matrix.\n  MatrixUtil.embedTypeInfo = function (ecLevel, maskPattern /*int*/, matrix) {\n    var typeInfoBits = new BitArray();\n    MatrixUtil.makeTypeInfoBits(ecLevel, maskPattern, typeInfoBits);\n    for (var i = 0, size = typeInfoBits.getSize(); i < size; ++i) {\n      // Place bits in LSB to MSB order.  LSB (least significant bit) is the last value in\n      // \"typeInfoBits\".\n      var bit = typeInfoBits.get(typeInfoBits.getSize() - 1 - i);\n      // Type info bits at the left top corner. See 8.9 of JISX0510:2004 (p.46).\n      var coordinates = MatrixUtil.TYPE_INFO_COORDINATES[i];\n      var x1 = coordinates[0];\n      var y1 = coordinates[1];\n      matrix.setBoolean(x1, y1, bit);\n      if (i < 8) {\n        // Right top corner.\n        var x2 = matrix.getWidth() - i - 1;\n        var y2 = 8;\n        matrix.setBoolean(x2, y2, bit);\n      } else {\n        // Left bottom corner.\n        var x2 = 8;\n        var y2 = matrix.getHeight() - 7 + (i - 8);\n        matrix.setBoolean(x2, y2, bit);\n      }\n    }\n  };\n  // Embed version information if need be. On success, modify the matrix and return true.\n  // See 8.10 of JISX0510:2004 (p.47) for how to embed version information.\n  MatrixUtil.maybeEmbedVersionInfo = function (version, matrix) {\n    if (version.getVersionNumber() < 7) {\n      // Version info is necessary if version >= 7.\n      return; // Don't need version info.\n    }\n    var versionInfoBits = new BitArray();\n    MatrixUtil.makeVersionInfoBits(version, versionInfoBits);\n    var bitIndex = 6 * 3 - 1; // It will decrease from 17 to 0.\n    for (var i = 0; i < 6; ++i) {\n      for (var j = 0; j < 3; ++j) {\n        // Place bits in LSB (least significant bit) to MSB order.\n        var bit = versionInfoBits.get(bitIndex);\n        bitIndex--;\n        // Left bottom corner.\n        matrix.setBoolean(i, matrix.getHeight() - 11 + j, bit);\n        // Right bottom corner.\n        matrix.setBoolean(matrix.getHeight() - 11 + j, i, bit);\n      }\n    }\n  };\n  // Embed \"dataBits\" using \"getMaskPattern\". On success, modify the matrix and return true.\n  // For debugging purposes, it skips masking process if \"getMaskPattern\" is -1(TYPESCRIPTPORT: 255).\n  // See 8.7 of JISX0510:2004 (p.38) for how to embed data bits.\n  MatrixUtil.embedDataBits = function (dataBits, maskPattern /*int*/, matrix) {\n    var bitIndex = 0;\n    var direction = -1;\n    // Start from the right bottom cell.\n    var x = matrix.getWidth() - 1;\n    var y = matrix.getHeight() - 1;\n    while (x > 0) {\n      // Skip the vertical timing pattern.\n      if (x === 6) {\n        x -= 1;\n      }\n      while (y >= 0 && y < matrix.getHeight()) {\n        for (var i = 0; i < 2; ++i) {\n          var xx = x - i;\n          // Skip the cell if it's not empty.\n          if (!MatrixUtil.isEmpty(matrix.get(xx, y))) {\n            continue;\n          }\n          var bit = void 0;\n          if (bitIndex < dataBits.getSize()) {\n            bit = dataBits.get(bitIndex);\n            ++bitIndex;\n          } else {\n            // Padding bit. If there is no bit left, we'll fill the left cells with 0, as described\n            // in 8.4.9 of JISX0510:2004 (p. 24).\n            bit = false;\n          }\n          // Skip masking if mask_pattern is -1 (TYPESCRIPTPORT: 255).\n          if (maskPattern !== 255 && MaskUtil.getDataMaskBit(maskPattern, xx, y)) {\n            bit = !bit;\n          }\n          matrix.setBoolean(xx, y, bit);\n        }\n        y += direction;\n      }\n      direction = -direction; // Reverse the direction.\n      y += direction;\n      x -= 2; // Move to the left.\n    }\n    // All bits should be consumed.\n    if (bitIndex !== dataBits.getSize()) {\n      throw new WriterException('Not all bits consumed: ' + bitIndex + '/' + dataBits.getSize());\n    }\n  };\n  // Return the position of the most significant bit set (one: to) in the \"value\". The most\n  // significant bit is position 32. If there is no bit set, return 0. Examples:\n  // - findMSBSet(0) => 0\n  // - findMSBSet(1) => 1\n  // - findMSBSet(255) => 8\n  MatrixUtil.findMSBSet = function (value /*int*/) {\n    return 32 - Integer.numberOfLeadingZeros(value);\n  };\n  // Calculate BCH (Bose-Chaudhuri-Hocquenghem) code for \"value\" using polynomial \"poly\". The BCH\n  // code is used for encoding type information and version information.\n  // Example: Calculation of version information of 7.\n  // f(x) is created from 7.\n  //   - 7 = 000111 in 6 bits\n  //   - f(x) = x^2 + x^1 + x^0\n  // g(x) is given by the standard (p. 67)\n  //   - g(x) = x^12 + x^11 + x^10 + x^9 + x^8 + x^5 + x^2 + 1\n  // Multiply f(x) by x^(18 - 6)\n  //   - f'(x) = f(x) * x^(18 - 6)\n  //   - f'(x) = x^14 + x^13 + x^12\n  // Calculate the remainder of f'(x) / g(x)\n  //         x^2\n  //         __________________________________________________\n  //   g(x) )x^14 + x^13 + x^12\n  //         x^14 + x^13 + x^12 + x^11 + x^10 + x^7 + x^4 + x^2\n  //         --------------------------------------------------\n  //                              x^11 + x^10 + x^7 + x^4 + x^2\n  //\n  // The remainder is x^11 + x^10 + x^7 + x^4 + x^2\n  // Encode it in binary: 110010010100\n  // The return value is 0xc94 (1100 1001 0100)\n  //\n  // Since all coefficients in the polynomials are 1 or 0, we can do the calculation by bit\n  // operations. We don't care if coefficients are positive or negative.\n  MatrixUtil.calculateBCHCode = function (value /*int*/, poly /*int*/) {\n    if (poly === 0) {\n      throw new IllegalArgumentException('0 polynomial');\n    }\n    // If poly is \"1 1111 0010 0101\" (version info poly), msbSetInPoly is 13. We'll subtract 1\n    // from 13 to make it 12.\n    var msbSetInPoly = MatrixUtil.findMSBSet(poly);\n    value <<= msbSetInPoly - 1;\n    // Do the division business using exclusive-or operations.\n    while (MatrixUtil.findMSBSet(value) >= msbSetInPoly) {\n      value ^= poly << MatrixUtil.findMSBSet(value) - msbSetInPoly;\n    }\n    // Now the \"value\" is the remainder (i.e. the BCH code)\n    return value;\n  };\n  // Make bit vector of type information. On success, store the result in \"bits\" and return true.\n  // Encode error correction level and mask pattern. See 8.9 of\n  // JISX0510:2004 (p.45) for details.\n  MatrixUtil.makeTypeInfoBits = function (ecLevel, maskPattern /*int*/, bits) {\n    if (!QRCode.isValidMaskPattern(maskPattern)) {\n      throw new WriterException('Invalid mask pattern');\n    }\n    var typeInfo = ecLevel.getBits() << 3 | maskPattern;\n    bits.appendBits(typeInfo, 5);\n    var bchCode = MatrixUtil.calculateBCHCode(typeInfo, MatrixUtil.TYPE_INFO_POLY);\n    bits.appendBits(bchCode, 10);\n    var maskBits = new BitArray();\n    maskBits.appendBits(MatrixUtil.TYPE_INFO_MASK_PATTERN, 15);\n    bits.xor(maskBits);\n    if (bits.getSize() !== 15) {\n      // Just in case.\n      throw new WriterException('should not happen but we got: ' + bits.getSize());\n    }\n  };\n  // Make bit vector of version information. On success, store the result in \"bits\" and return true.\n  // See 8.10 of JISX0510:2004 (p.45) for details.\n  MatrixUtil.makeVersionInfoBits = function (version, bits) {\n    bits.appendBits(version.getVersionNumber(), 6);\n    var bchCode = MatrixUtil.calculateBCHCode(version.getVersionNumber(), MatrixUtil.VERSION_INFO_POLY);\n    bits.appendBits(bchCode, 12);\n    if (bits.getSize() !== 18) {\n      // Just in case.\n      throw new WriterException('should not happen but we got: ' + bits.getSize());\n    }\n  };\n  // Check if \"value\" is empty.\n  MatrixUtil.isEmpty = function (value /*int*/) {\n    return value === 255; // -1\n  };\n  MatrixUtil.embedTimingPatterns = function (matrix) {\n    // -8 is for skipping position detection patterns (7: size), and two horizontal/vertical\n    // separation patterns (1: size). Thus, 8 = 7 + 1.\n    for (var i = 8; i < matrix.getWidth() - 8; ++i) {\n      var bit = (i + 1) % 2;\n      // Horizontal line.\n      if (MatrixUtil.isEmpty(matrix.get(i, 6))) {\n        matrix.setNumber(i, 6, bit);\n      }\n      // Vertical line.\n      if (MatrixUtil.isEmpty(matrix.get(6, i))) {\n        matrix.setNumber(6, i, bit);\n      }\n    }\n  };\n  // Embed the lonely dark dot at left bottom corner. JISX0510:2004 (p.46)\n  MatrixUtil.embedDarkDotAtLeftBottomCorner = function (matrix) {\n    if (matrix.get(8, matrix.getHeight() - 8) === 0) {\n      throw new WriterException();\n    }\n    matrix.setNumber(8, matrix.getHeight() - 8, 1);\n  };\n  MatrixUtil.embedHorizontalSeparationPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n    for (var x = 0; x < 8; ++x) {\n      if (!MatrixUtil.isEmpty(matrix.get(xStart + x, yStart))) {\n        throw new WriterException();\n      }\n      matrix.setNumber(xStart + x, yStart, 0);\n    }\n  };\n  MatrixUtil.embedVerticalSeparationPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n    for (var y = 0; y < 7; ++y) {\n      if (!MatrixUtil.isEmpty(matrix.get(xStart, yStart + y))) {\n        throw new WriterException();\n      }\n      matrix.setNumber(xStart, yStart + y, 0);\n    }\n  };\n  MatrixUtil.embedPositionAdjustmentPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n    for (var y = 0; y < 5; ++y) {\n      var patternY = MatrixUtil.POSITION_ADJUSTMENT_PATTERN[y];\n      for (var x = 0; x < 5; ++x) {\n        matrix.setNumber(xStart + x, yStart + y, patternY[x]);\n      }\n    }\n  };\n  MatrixUtil.embedPositionDetectionPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n    for (var y = 0; y < 7; ++y) {\n      var patternY = MatrixUtil.POSITION_DETECTION_PATTERN[y];\n      for (var x = 0; x < 7; ++x) {\n        matrix.setNumber(xStart + x, yStart + y, patternY[x]);\n      }\n    }\n  };\n  // Embed position detection patterns and surrounding vertical/horizontal separators.\n  MatrixUtil.embedPositionDetectionPatternsAndSeparators = function (matrix) {\n    // Embed three big squares at corners.\n    var pdpWidth = MatrixUtil.POSITION_DETECTION_PATTERN[0].length;\n    // Left top corner.\n    MatrixUtil.embedPositionDetectionPattern(0, 0, matrix);\n    // Right top corner.\n    MatrixUtil.embedPositionDetectionPattern(matrix.getWidth() - pdpWidth, 0, matrix);\n    // Left bottom corner.\n    MatrixUtil.embedPositionDetectionPattern(0, matrix.getWidth() - pdpWidth, matrix);\n    // Embed horizontal separation patterns around the squares.\n    var hspWidth = 8;\n    // Left top corner.\n    MatrixUtil.embedHorizontalSeparationPattern(0, hspWidth - 1, matrix);\n    // Right top corner.\n    MatrixUtil.embedHorizontalSeparationPattern(matrix.getWidth() - hspWidth, hspWidth - 1, matrix);\n    // Left bottom corner.\n    MatrixUtil.embedHorizontalSeparationPattern(0, matrix.getWidth() - hspWidth, matrix);\n    // Embed vertical separation patterns around the squares.\n    var vspSize = 7;\n    // Left top corner.\n    MatrixUtil.embedVerticalSeparationPattern(vspSize, 0, matrix);\n    // Right top corner.\n    MatrixUtil.embedVerticalSeparationPattern(matrix.getHeight() - vspSize - 1, 0, matrix);\n    // Left bottom corner.\n    MatrixUtil.embedVerticalSeparationPattern(vspSize, matrix.getHeight() - vspSize, matrix);\n  };\n  // Embed position adjustment patterns if need be.\n  MatrixUtil.maybeEmbedPositionAdjustmentPatterns = function (version, matrix) {\n    if (version.getVersionNumber() < 2) {\n      // The patterns appear if version >= 2\n      return;\n    }\n    var index = version.getVersionNumber() - 1;\n    var coordinates = MatrixUtil.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[index];\n    for (var i = 0, length_1 = coordinates.length; i !== length_1; i++) {\n      var y = coordinates[i];\n      if (y >= 0) {\n        for (var j = 0; j !== length_1; j++) {\n          var x = coordinates[j];\n          if (x >= 0 && MatrixUtil.isEmpty(matrix.get(x, y))) {\n            // If the cell is unset, we embed the position adjustment pattern here.\n            // -2 is necessary since the x/y coordinates point to the center of the pattern, not the\n            // left top corner.\n            MatrixUtil.embedPositionAdjustmentPattern(x - 2, y - 2, matrix);\n          }\n        }\n      }\n    }\n  };\n  MatrixUtil.POSITION_DETECTION_PATTERN = Array.from([Int32Array.from([1, 1, 1, 1, 1, 1, 1]), Int32Array.from([1, 0, 0, 0, 0, 0, 1]), Int32Array.from([1, 0, 1, 1, 1, 0, 1]), Int32Array.from([1, 0, 1, 1, 1, 0, 1]), Int32Array.from([1, 0, 1, 1, 1, 0, 1]), Int32Array.from([1, 0, 0, 0, 0, 0, 1]), Int32Array.from([1, 1, 1, 1, 1, 1, 1])]);\n  MatrixUtil.POSITION_ADJUSTMENT_PATTERN = Array.from([Int32Array.from([1, 1, 1, 1, 1]), Int32Array.from([1, 0, 0, 0, 1]), Int32Array.from([1, 0, 1, 0, 1]), Int32Array.from([1, 0, 0, 0, 1]), Int32Array.from([1, 1, 1, 1, 1])]);\n  // From Appendix E. Table 1, JIS0510X:2004 (71: p). The table was double-checked by komatsu.\n  MatrixUtil.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE = Array.from([Int32Array.from([-1, -1, -1, -1, -1, -1, -1]), Int32Array.from([6, 18, -1, -1, -1, -1, -1]), Int32Array.from([6, 22, -1, -1, -1, -1, -1]), Int32Array.from([6, 26, -1, -1, -1, -1, -1]), Int32Array.from([6, 30, -1, -1, -1, -1, -1]), Int32Array.from([6, 34, -1, -1, -1, -1, -1]), Int32Array.from([6, 22, 38, -1, -1, -1, -1]), Int32Array.from([6, 24, 42, -1, -1, -1, -1]), Int32Array.from([6, 26, 46, -1, -1, -1, -1]), Int32Array.from([6, 28, 50, -1, -1, -1, -1]), Int32Array.from([6, 30, 54, -1, -1, -1, -1]), Int32Array.from([6, 32, 58, -1, -1, -1, -1]), Int32Array.from([6, 34, 62, -1, -1, -1, -1]), Int32Array.from([6, 26, 46, 66, -1, -1, -1]), Int32Array.from([6, 26, 48, 70, -1, -1, -1]), Int32Array.from([6, 26, 50, 74, -1, -1, -1]), Int32Array.from([6, 30, 54, 78, -1, -1, -1]), Int32Array.from([6, 30, 56, 82, -1, -1, -1]), Int32Array.from([6, 30, 58, 86, -1, -1, -1]), Int32Array.from([6, 34, 62, 90, -1, -1, -1]), Int32Array.from([6, 28, 50, 72, 94, -1, -1]), Int32Array.from([6, 26, 50, 74, 98, -1, -1]), Int32Array.from([6, 30, 54, 78, 102, -1, -1]), Int32Array.from([6, 28, 54, 80, 106, -1, -1]), Int32Array.from([6, 32, 58, 84, 110, -1, -1]), Int32Array.from([6, 30, 58, 86, 114, -1, -1]), Int32Array.from([6, 34, 62, 90, 118, -1, -1]), Int32Array.from([6, 26, 50, 74, 98, 122, -1]), Int32Array.from([6, 30, 54, 78, 102, 126, -1]), Int32Array.from([6, 26, 52, 78, 104, 130, -1]), Int32Array.from([6, 30, 56, 82, 108, 134, -1]), Int32Array.from([6, 34, 60, 86, 112, 138, -1]), Int32Array.from([6, 30, 58, 86, 114, 142, -1]), Int32Array.from([6, 34, 62, 90, 118, 146, -1]), Int32Array.from([6, 30, 54, 78, 102, 126, 150]), Int32Array.from([6, 24, 50, 76, 102, 128, 154]), Int32Array.from([6, 28, 54, 80, 106, 132, 158]), Int32Array.from([6, 32, 58, 84, 110, 136, 162]), Int32Array.from([6, 26, 54, 82, 110, 138, 166]), Int32Array.from([6, 30, 58, 86, 114, 142, 170])]);\n  // Type info cells at the left top corner.\n  MatrixUtil.TYPE_INFO_COORDINATES = Array.from([Int32Array.from([8, 0]), Int32Array.from([8, 1]), Int32Array.from([8, 2]), Int32Array.from([8, 3]), Int32Array.from([8, 4]), Int32Array.from([8, 5]), Int32Array.from([8, 7]), Int32Array.from([8, 8]), Int32Array.from([7, 8]), Int32Array.from([5, 8]), Int32Array.from([4, 8]), Int32Array.from([3, 8]), Int32Array.from([2, 8]), Int32Array.from([1, 8]), Int32Array.from([0, 8])]);\n  // From Appendix D in JISX0510:2004 (p. 67)\n  MatrixUtil.VERSION_INFO_POLY = 0x1f25; // 1 1111 0010 0101\n  // From Appendix C in JISX0510:2004 (p.65).\n  MatrixUtil.TYPE_INFO_POLY = 0x537;\n  MatrixUtil.TYPE_INFO_MASK_PATTERN = 0x5412;\n  return MatrixUtil;\n}();\nexport default MatrixUtil;", "map": {"version": 3, "names": ["BitArray", "Integer", "QRCode", "<PERSON><PERSON><PERSON>", "WriterException", "IllegalArgumentException", "MatrixUtil", "clearMatrix", "matrix", "clear", "buildMatrix", "dataBits", "ecLevel", "version", "maskPattern", "embedBasicPatterns", "embedTypeInfo", "maybeEmbedVersionInfo", "embedDataBits", "embedPositionDetectionPatternsAndSeparators", "embedDarkDotAtLeftBottomCorner", "maybeEmbedPositionAdjustmentPatterns", "embedTimingPatterns", "typeInfoBits", "makeTypeInfoBits", "i", "size", "getSize", "bit", "get", "coordinates", "TYPE_INFO_COORDINATES", "x1", "y1", "setBoolean", "x2", "getWidth", "y2", "getHeight", "getVersionNumber", "versionInfoBits", "makeVersionInfoBits", "bitIndex", "j", "direction", "x", "y", "xx", "isEmpty", "getDataMaskBit", "findMSBSet", "value", "numberOfLeadingZeros", "calculateBCHCode", "poly", "msbSetInPoly", "bits", "isValidMaskPattern", "typeInfo", "getBits", "appendBits", "bchCode", "TYPE_INFO_POLY", "maskBits", "TYPE_INFO_MASK_PATTERN", "xor", "VERSION_INFO_POLY", "setNumber", "embedHorizontalSeparationPattern", "xStart", "yStart", "embedVerticalSeparationPattern", "embedPositionAdjustmentPattern", "patternY", "POSITION_ADJUSTMENT_PATTERN", "embedPositionDetectionPattern", "POSITION_DETECTION_PATTERN", "pdpWidth", "length", "hspWidth", "vspSize", "index", "POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE", "length_1", "Array", "from", "Int32Array"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/encoder/MatrixUtil.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.encoder {*/\nimport BitArray from '../../common/BitArray';\nimport Integer from '../../util/Integer';\nimport QRCode from './QRCode';\nimport <PERSON>Util from './MaskUtil';\nimport WriterException from '../../WriterException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <AUTHOR> (<PERSON><PERSON>) - creator\n * <AUTHOR> (<PERSON>) - ported from C++\n */\nvar MatrixUtil = /** @class */ (function () {\n    function MatrixUtil() {\n        // do nothing\n    }\n    // Set all cells to -1 (TYPESCRIPTPORT: 255).  -1 (TYPESCRIPTPORT: 255) means that the cell is empty (not set yet).\n    //\n    // JAVAPORT: We shouldn't need to do this at all. The code should be rewritten to begin encoding\n    // with the ByteMatrix initialized all to zero.\n    MatrixUtil.clearMatrix = function (matrix) {\n        // TYPESCRIPTPORT: we use UintArray se changed here from -1 to 255\n        matrix.clear(/*(byte) */ /*-1*/ 255);\n    };\n    // Build 2D matrix of QR Code from \"dataBits\" with \"ecLevel\", \"version\" and \"getMaskPattern\". On\n    // success, store the result in \"matrix\" and return true.\n    MatrixUtil.buildMatrix = function (dataBits, ecLevel, version, maskPattern /*int*/, matrix) {\n        MatrixUtil.clearMatrix(matrix);\n        MatrixUtil.embedBasicPatterns(version, matrix);\n        // Type information appear with any version.\n        MatrixUtil.embedTypeInfo(ecLevel, maskPattern, matrix);\n        // Version info appear if version >= 7.\n        MatrixUtil.maybeEmbedVersionInfo(version, matrix);\n        // Data should be embedded at end.\n        MatrixUtil.embedDataBits(dataBits, maskPattern, matrix);\n    };\n    // Embed basic patterns. On success, modify the matrix and return true.\n    // The basic patterns are:\n    // - Position detection patterns\n    // - Timing patterns\n    // - Dark dot at the left bottom corner\n    // - Position adjustment patterns, if need be\n    MatrixUtil.embedBasicPatterns = function (version, matrix) {\n        // Let's get started with embedding big squares at corners.\n        MatrixUtil.embedPositionDetectionPatternsAndSeparators(matrix);\n        // Then, embed the dark dot at the left bottom corner.\n        MatrixUtil.embedDarkDotAtLeftBottomCorner(matrix);\n        // Position adjustment patterns appear if version >= 2.\n        MatrixUtil.maybeEmbedPositionAdjustmentPatterns(version, matrix);\n        // Timing patterns should be embedded after position adj. patterns.\n        MatrixUtil.embedTimingPatterns(matrix);\n    };\n    // Embed type information. On success, modify the matrix.\n    MatrixUtil.embedTypeInfo = function (ecLevel, maskPattern /*int*/, matrix) {\n        var typeInfoBits = new BitArray();\n        MatrixUtil.makeTypeInfoBits(ecLevel, maskPattern, typeInfoBits);\n        for (var i = 0, size = typeInfoBits.getSize(); i < size; ++i) {\n            // Place bits in LSB to MSB order.  LSB (least significant bit) is the last value in\n            // \"typeInfoBits\".\n            var bit = typeInfoBits.get(typeInfoBits.getSize() - 1 - i);\n            // Type info bits at the left top corner. See 8.9 of JISX0510:2004 (p.46).\n            var coordinates = MatrixUtil.TYPE_INFO_COORDINATES[i];\n            var x1 = coordinates[0];\n            var y1 = coordinates[1];\n            matrix.setBoolean(x1, y1, bit);\n            if (i < 8) {\n                // Right top corner.\n                var x2 = matrix.getWidth() - i - 1;\n                var y2 = 8;\n                matrix.setBoolean(x2, y2, bit);\n            }\n            else {\n                // Left bottom corner.\n                var x2 = 8;\n                var y2 = matrix.getHeight() - 7 + (i - 8);\n                matrix.setBoolean(x2, y2, bit);\n            }\n        }\n    };\n    // Embed version information if need be. On success, modify the matrix and return true.\n    // See 8.10 of JISX0510:2004 (p.47) for how to embed version information.\n    MatrixUtil.maybeEmbedVersionInfo = function (version, matrix) {\n        if (version.getVersionNumber() < 7) { // Version info is necessary if version >= 7.\n            return; // Don't need version info.\n        }\n        var versionInfoBits = new BitArray();\n        MatrixUtil.makeVersionInfoBits(version, versionInfoBits);\n        var bitIndex = 6 * 3 - 1; // It will decrease from 17 to 0.\n        for (var i = 0; i < 6; ++i) {\n            for (var j = 0; j < 3; ++j) {\n                // Place bits in LSB (least significant bit) to MSB order.\n                var bit = versionInfoBits.get(bitIndex);\n                bitIndex--;\n                // Left bottom corner.\n                matrix.setBoolean(i, matrix.getHeight() - 11 + j, bit);\n                // Right bottom corner.\n                matrix.setBoolean(matrix.getHeight() - 11 + j, i, bit);\n            }\n        }\n    };\n    // Embed \"dataBits\" using \"getMaskPattern\". On success, modify the matrix and return true.\n    // For debugging purposes, it skips masking process if \"getMaskPattern\" is -1(TYPESCRIPTPORT: 255).\n    // See 8.7 of JISX0510:2004 (p.38) for how to embed data bits.\n    MatrixUtil.embedDataBits = function (dataBits, maskPattern /*int*/, matrix) {\n        var bitIndex = 0;\n        var direction = -1;\n        // Start from the right bottom cell.\n        var x = matrix.getWidth() - 1;\n        var y = matrix.getHeight() - 1;\n        while (x > 0) {\n            // Skip the vertical timing pattern.\n            if (x === 6) {\n                x -= 1;\n            }\n            while (y >= 0 && y < matrix.getHeight()) {\n                for (var i = 0; i < 2; ++i) {\n                    var xx = x - i;\n                    // Skip the cell if it's not empty.\n                    if (!MatrixUtil.isEmpty(matrix.get(xx, y))) {\n                        continue;\n                    }\n                    var bit = void 0;\n                    if (bitIndex < dataBits.getSize()) {\n                        bit = dataBits.get(bitIndex);\n                        ++bitIndex;\n                    }\n                    else {\n                        // Padding bit. If there is no bit left, we'll fill the left cells with 0, as described\n                        // in 8.4.9 of JISX0510:2004 (p. 24).\n                        bit = false;\n                    }\n                    // Skip masking if mask_pattern is -1 (TYPESCRIPTPORT: 255).\n                    if (maskPattern !== 255 && MaskUtil.getDataMaskBit(maskPattern, xx, y)) {\n                        bit = !bit;\n                    }\n                    matrix.setBoolean(xx, y, bit);\n                }\n                y += direction;\n            }\n            direction = -direction; // Reverse the direction.\n            y += direction;\n            x -= 2; // Move to the left.\n        }\n        // All bits should be consumed.\n        if (bitIndex !== dataBits.getSize()) {\n            throw new WriterException('Not all bits consumed: ' + bitIndex + '/' + dataBits.getSize());\n        }\n    };\n    // Return the position of the most significant bit set (one: to) in the \"value\". The most\n    // significant bit is position 32. If there is no bit set, return 0. Examples:\n    // - findMSBSet(0) => 0\n    // - findMSBSet(1) => 1\n    // - findMSBSet(255) => 8\n    MatrixUtil.findMSBSet = function (value /*int*/) {\n        return 32 - Integer.numberOfLeadingZeros(value);\n    };\n    // Calculate BCH (Bose-Chaudhuri-Hocquenghem) code for \"value\" using polynomial \"poly\". The BCH\n    // code is used for encoding type information and version information.\n    // Example: Calculation of version information of 7.\n    // f(x) is created from 7.\n    //   - 7 = 000111 in 6 bits\n    //   - f(x) = x^2 + x^1 + x^0\n    // g(x) is given by the standard (p. 67)\n    //   - g(x) = x^12 + x^11 + x^10 + x^9 + x^8 + x^5 + x^2 + 1\n    // Multiply f(x) by x^(18 - 6)\n    //   - f'(x) = f(x) * x^(18 - 6)\n    //   - f'(x) = x^14 + x^13 + x^12\n    // Calculate the remainder of f'(x) / g(x)\n    //         x^2\n    //         __________________________________________________\n    //   g(x) )x^14 + x^13 + x^12\n    //         x^14 + x^13 + x^12 + x^11 + x^10 + x^7 + x^4 + x^2\n    //         --------------------------------------------------\n    //                              x^11 + x^10 + x^7 + x^4 + x^2\n    //\n    // The remainder is x^11 + x^10 + x^7 + x^4 + x^2\n    // Encode it in binary: 110010010100\n    // The return value is 0xc94 (1100 1001 0100)\n    //\n    // Since all coefficients in the polynomials are 1 or 0, we can do the calculation by bit\n    // operations. We don't care if coefficients are positive or negative.\n    MatrixUtil.calculateBCHCode = function (value /*int*/, poly /*int*/) {\n        if (poly === 0) {\n            throw new IllegalArgumentException('0 polynomial');\n        }\n        // If poly is \"1 1111 0010 0101\" (version info poly), msbSetInPoly is 13. We'll subtract 1\n        // from 13 to make it 12.\n        var msbSetInPoly = MatrixUtil.findMSBSet(poly);\n        value <<= msbSetInPoly - 1;\n        // Do the division business using exclusive-or operations.\n        while (MatrixUtil.findMSBSet(value) >= msbSetInPoly) {\n            value ^= poly << (MatrixUtil.findMSBSet(value) - msbSetInPoly);\n        }\n        // Now the \"value\" is the remainder (i.e. the BCH code)\n        return value;\n    };\n    // Make bit vector of type information. On success, store the result in \"bits\" and return true.\n    // Encode error correction level and mask pattern. See 8.9 of\n    // JISX0510:2004 (p.45) for details.\n    MatrixUtil.makeTypeInfoBits = function (ecLevel, maskPattern /*int*/, bits) {\n        if (!QRCode.isValidMaskPattern(maskPattern)) {\n            throw new WriterException('Invalid mask pattern');\n        }\n        var typeInfo = (ecLevel.getBits() << 3) | maskPattern;\n        bits.appendBits(typeInfo, 5);\n        var bchCode = MatrixUtil.calculateBCHCode(typeInfo, MatrixUtil.TYPE_INFO_POLY);\n        bits.appendBits(bchCode, 10);\n        var maskBits = new BitArray();\n        maskBits.appendBits(MatrixUtil.TYPE_INFO_MASK_PATTERN, 15);\n        bits.xor(maskBits);\n        if (bits.getSize() !== 15) { // Just in case.\n            throw new WriterException('should not happen but we got: ' + bits.getSize());\n        }\n    };\n    // Make bit vector of version information. On success, store the result in \"bits\" and return true.\n    // See 8.10 of JISX0510:2004 (p.45) for details.\n    MatrixUtil.makeVersionInfoBits = function (version, bits) {\n        bits.appendBits(version.getVersionNumber(), 6);\n        var bchCode = MatrixUtil.calculateBCHCode(version.getVersionNumber(), MatrixUtil.VERSION_INFO_POLY);\n        bits.appendBits(bchCode, 12);\n        if (bits.getSize() !== 18) { // Just in case.\n            throw new WriterException('should not happen but we got: ' + bits.getSize());\n        }\n    };\n    // Check if \"value\" is empty.\n    MatrixUtil.isEmpty = function (value /*int*/) {\n        return value === 255; // -1\n    };\n    MatrixUtil.embedTimingPatterns = function (matrix) {\n        // -8 is for skipping position detection patterns (7: size), and two horizontal/vertical\n        // separation patterns (1: size). Thus, 8 = 7 + 1.\n        for (var i = 8; i < matrix.getWidth() - 8; ++i) {\n            var bit = (i + 1) % 2;\n            // Horizontal line.\n            if (MatrixUtil.isEmpty(matrix.get(i, 6))) {\n                matrix.setNumber(i, 6, bit);\n            }\n            // Vertical line.\n            if (MatrixUtil.isEmpty(matrix.get(6, i))) {\n                matrix.setNumber(6, i, bit);\n            }\n        }\n    };\n    // Embed the lonely dark dot at left bottom corner. JISX0510:2004 (p.46)\n    MatrixUtil.embedDarkDotAtLeftBottomCorner = function (matrix) {\n        if (matrix.get(8, matrix.getHeight() - 8) === 0) {\n            throw new WriterException();\n        }\n        matrix.setNumber(8, matrix.getHeight() - 8, 1);\n    };\n    MatrixUtil.embedHorizontalSeparationPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n        for (var x = 0; x < 8; ++x) {\n            if (!MatrixUtil.isEmpty(matrix.get(xStart + x, yStart))) {\n                throw new WriterException();\n            }\n            matrix.setNumber(xStart + x, yStart, 0);\n        }\n    };\n    MatrixUtil.embedVerticalSeparationPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n        for (var y = 0; y < 7; ++y) {\n            if (!MatrixUtil.isEmpty(matrix.get(xStart, yStart + y))) {\n                throw new WriterException();\n            }\n            matrix.setNumber(xStart, yStart + y, 0);\n        }\n    };\n    MatrixUtil.embedPositionAdjustmentPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n        for (var y = 0; y < 5; ++y) {\n            var patternY = MatrixUtil.POSITION_ADJUSTMENT_PATTERN[y];\n            for (var x = 0; x < 5; ++x) {\n                matrix.setNumber(xStart + x, yStart + y, patternY[x]);\n            }\n        }\n    };\n    MatrixUtil.embedPositionDetectionPattern = function (xStart /*int*/, yStart /*int*/, matrix) {\n        for (var y = 0; y < 7; ++y) {\n            var patternY = MatrixUtil.POSITION_DETECTION_PATTERN[y];\n            for (var x = 0; x < 7; ++x) {\n                matrix.setNumber(xStart + x, yStart + y, patternY[x]);\n            }\n        }\n    };\n    // Embed position detection patterns and surrounding vertical/horizontal separators.\n    MatrixUtil.embedPositionDetectionPatternsAndSeparators = function (matrix) {\n        // Embed three big squares at corners.\n        var pdpWidth = MatrixUtil.POSITION_DETECTION_PATTERN[0].length;\n        // Left top corner.\n        MatrixUtil.embedPositionDetectionPattern(0, 0, matrix);\n        // Right top corner.\n        MatrixUtil.embedPositionDetectionPattern(matrix.getWidth() - pdpWidth, 0, matrix);\n        // Left bottom corner.\n        MatrixUtil.embedPositionDetectionPattern(0, matrix.getWidth() - pdpWidth, matrix);\n        // Embed horizontal separation patterns around the squares.\n        var hspWidth = 8;\n        // Left top corner.\n        MatrixUtil.embedHorizontalSeparationPattern(0, hspWidth - 1, matrix);\n        // Right top corner.\n        MatrixUtil.embedHorizontalSeparationPattern(matrix.getWidth() - hspWidth, hspWidth - 1, matrix);\n        // Left bottom corner.\n        MatrixUtil.embedHorizontalSeparationPattern(0, matrix.getWidth() - hspWidth, matrix);\n        // Embed vertical separation patterns around the squares.\n        var vspSize = 7;\n        // Left top corner.\n        MatrixUtil.embedVerticalSeparationPattern(vspSize, 0, matrix);\n        // Right top corner.\n        MatrixUtil.embedVerticalSeparationPattern(matrix.getHeight() - vspSize - 1, 0, matrix);\n        // Left bottom corner.\n        MatrixUtil.embedVerticalSeparationPattern(vspSize, matrix.getHeight() - vspSize, matrix);\n    };\n    // Embed position adjustment patterns if need be.\n    MatrixUtil.maybeEmbedPositionAdjustmentPatterns = function (version, matrix) {\n        if (version.getVersionNumber() < 2) { // The patterns appear if version >= 2\n            return;\n        }\n        var index = version.getVersionNumber() - 1;\n        var coordinates = MatrixUtil.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE[index];\n        for (var i = 0, length_1 = coordinates.length; i !== length_1; i++) {\n            var y = coordinates[i];\n            if (y >= 0) {\n                for (var j = 0; j !== length_1; j++) {\n                    var x = coordinates[j];\n                    if (x >= 0 && MatrixUtil.isEmpty(matrix.get(x, y))) {\n                        // If the cell is unset, we embed the position adjustment pattern here.\n                        // -2 is necessary since the x/y coordinates point to the center of the pattern, not the\n                        // left top corner.\n                        MatrixUtil.embedPositionAdjustmentPattern(x - 2, y - 2, matrix);\n                    }\n                }\n            }\n        }\n    };\n    MatrixUtil.POSITION_DETECTION_PATTERN = Array.from([\n        Int32Array.from([1, 1, 1, 1, 1, 1, 1]),\n        Int32Array.from([1, 0, 0, 0, 0, 0, 1]),\n        Int32Array.from([1, 0, 1, 1, 1, 0, 1]),\n        Int32Array.from([1, 0, 1, 1, 1, 0, 1]),\n        Int32Array.from([1, 0, 1, 1, 1, 0, 1]),\n        Int32Array.from([1, 0, 0, 0, 0, 0, 1]),\n        Int32Array.from([1, 1, 1, 1, 1, 1, 1]),\n    ]);\n    MatrixUtil.POSITION_ADJUSTMENT_PATTERN = Array.from([\n        Int32Array.from([1, 1, 1, 1, 1]),\n        Int32Array.from([1, 0, 0, 0, 1]),\n        Int32Array.from([1, 0, 1, 0, 1]),\n        Int32Array.from([1, 0, 0, 0, 1]),\n        Int32Array.from([1, 1, 1, 1, 1]),\n    ]);\n    // From Appendix E. Table 1, JIS0510X:2004 (71: p). The table was double-checked by komatsu.\n    MatrixUtil.POSITION_ADJUSTMENT_PATTERN_COORDINATE_TABLE = Array.from([\n        Int32Array.from([-1, -1, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 18, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 22, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 26, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 30, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 34, -1, -1, -1, -1, -1]),\n        Int32Array.from([6, 22, 38, -1, -1, -1, -1]),\n        Int32Array.from([6, 24, 42, -1, -1, -1, -1]),\n        Int32Array.from([6, 26, 46, -1, -1, -1, -1]),\n        Int32Array.from([6, 28, 50, -1, -1, -1, -1]),\n        Int32Array.from([6, 30, 54, -1, -1, -1, -1]),\n        Int32Array.from([6, 32, 58, -1, -1, -1, -1]),\n        Int32Array.from([6, 34, 62, -1, -1, -1, -1]),\n        Int32Array.from([6, 26, 46, 66, -1, -1, -1]),\n        Int32Array.from([6, 26, 48, 70, -1, -1, -1]),\n        Int32Array.from([6, 26, 50, 74, -1, -1, -1]),\n        Int32Array.from([6, 30, 54, 78, -1, -1, -1]),\n        Int32Array.from([6, 30, 56, 82, -1, -1, -1]),\n        Int32Array.from([6, 30, 58, 86, -1, -1, -1]),\n        Int32Array.from([6, 34, 62, 90, -1, -1, -1]),\n        Int32Array.from([6, 28, 50, 72, 94, -1, -1]),\n        Int32Array.from([6, 26, 50, 74, 98, -1, -1]),\n        Int32Array.from([6, 30, 54, 78, 102, -1, -1]),\n        Int32Array.from([6, 28, 54, 80, 106, -1, -1]),\n        Int32Array.from([6, 32, 58, 84, 110, -1, -1]),\n        Int32Array.from([6, 30, 58, 86, 114, -1, -1]),\n        Int32Array.from([6, 34, 62, 90, 118, -1, -1]),\n        Int32Array.from([6, 26, 50, 74, 98, 122, -1]),\n        Int32Array.from([6, 30, 54, 78, 102, 126, -1]),\n        Int32Array.from([6, 26, 52, 78, 104, 130, -1]),\n        Int32Array.from([6, 30, 56, 82, 108, 134, -1]),\n        Int32Array.from([6, 34, 60, 86, 112, 138, -1]),\n        Int32Array.from([6, 30, 58, 86, 114, 142, -1]),\n        Int32Array.from([6, 34, 62, 90, 118, 146, -1]),\n        Int32Array.from([6, 30, 54, 78, 102, 126, 150]),\n        Int32Array.from([6, 24, 50, 76, 102, 128, 154]),\n        Int32Array.from([6, 28, 54, 80, 106, 132, 158]),\n        Int32Array.from([6, 32, 58, 84, 110, 136, 162]),\n        Int32Array.from([6, 26, 54, 82, 110, 138, 166]),\n        Int32Array.from([6, 30, 58, 86, 114, 142, 170]),\n    ]);\n    // Type info cells at the left top corner.\n    MatrixUtil.TYPE_INFO_COORDINATES = Array.from([\n        Int32Array.from([8, 0]),\n        Int32Array.from([8, 1]),\n        Int32Array.from([8, 2]),\n        Int32Array.from([8, 3]),\n        Int32Array.from([8, 4]),\n        Int32Array.from([8, 5]),\n        Int32Array.from([8, 7]),\n        Int32Array.from([8, 8]),\n        Int32Array.from([7, 8]),\n        Int32Array.from([5, 8]),\n        Int32Array.from([4, 8]),\n        Int32Array.from([3, 8]),\n        Int32Array.from([2, 8]),\n        Int32Array.from([1, 8]),\n        Int32Array.from([0, 8]),\n    ]);\n    // From Appendix D in JISX0510:2004 (p. 67)\n    MatrixUtil.VERSION_INFO_POLY = 0x1f25; // 1 1111 0010 0101\n    // From Appendix C in JISX0510:2004 (p.65).\n    MatrixUtil.TYPE_INFO_POLY = 0x537;\n    MatrixUtil.TYPE_INFO_MASK_PATTERN = 0x5412;\n    return MatrixUtil;\n}());\nexport default MatrixUtil;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAAA,EAAG;IAClB;EAAA;EAEJ;EACA;EACA;EACA;EACAA,UAAU,CAACC,WAAW,GAAG,UAAUC,MAAM,EAAE;IACvC;IACAA,MAAM,CAACC,KAAK,CAAC,YAAY,MAAO,GAAG,CAAC;EACxC,CAAC;EACD;EACA;EACAH,UAAU,CAACI,WAAW,GAAG,UAAUC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,CAAC,SAASN,MAAM,EAAE;IACxFF,UAAU,CAACC,WAAW,CAACC,MAAM,CAAC;IAC9BF,UAAU,CAACS,kBAAkB,CAACF,OAAO,EAAEL,MAAM,CAAC;IAC9C;IACAF,UAAU,CAACU,aAAa,CAACJ,OAAO,EAAEE,WAAW,EAAEN,MAAM,CAAC;IACtD;IACAF,UAAU,CAACW,qBAAqB,CAACJ,OAAO,EAAEL,MAAM,CAAC;IACjD;IACAF,UAAU,CAACY,aAAa,CAACP,QAAQ,EAAEG,WAAW,EAAEN,MAAM,CAAC;EAC3D,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACAF,UAAU,CAACS,kBAAkB,GAAG,UAAUF,OAAO,EAAEL,MAAM,EAAE;IACvD;IACAF,UAAU,CAACa,2CAA2C,CAACX,MAAM,CAAC;IAC9D;IACAF,UAAU,CAACc,8BAA8B,CAACZ,MAAM,CAAC;IACjD;IACAF,UAAU,CAACe,oCAAoC,CAACR,OAAO,EAAEL,MAAM,CAAC;IAChE;IACAF,UAAU,CAACgB,mBAAmB,CAACd,MAAM,CAAC;EAC1C,CAAC;EACD;EACAF,UAAU,CAACU,aAAa,GAAG,UAAUJ,OAAO,EAAEE,WAAW,CAAC,SAASN,MAAM,EAAE;IACvE,IAAIe,YAAY,GAAG,IAAIvB,QAAQ,CAAC,CAAC;IACjCM,UAAU,CAACkB,gBAAgB,CAACZ,OAAO,EAAEE,WAAW,EAAES,YAAY,CAAC;IAC/D,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGH,YAAY,CAACI,OAAO,CAAC,CAAC,EAAEF,CAAC,GAAGC,IAAI,EAAE,EAAED,CAAC,EAAE;MAC1D;MACA;MACA,IAAIG,GAAG,GAAGL,YAAY,CAACM,GAAG,CAACN,YAAY,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,GAAGF,CAAC,CAAC;MAC1D;MACA,IAAIK,WAAW,GAAGxB,UAAU,CAACyB,qBAAqB,CAACN,CAAC,CAAC;MACrD,IAAIO,EAAE,GAAGF,WAAW,CAAC,CAAC,CAAC;MACvB,IAAIG,EAAE,GAAGH,WAAW,CAAC,CAAC,CAAC;MACvBtB,MAAM,CAAC0B,UAAU,CAACF,EAAE,EAAEC,EAAE,EAAEL,GAAG,CAAC;MAC9B,IAAIH,CAAC,GAAG,CAAC,EAAE;QACP;QACA,IAAIU,EAAE,GAAG3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,GAAGX,CAAC,GAAG,CAAC;QAClC,IAAIY,EAAE,GAAG,CAAC;QACV7B,MAAM,CAAC0B,UAAU,CAACC,EAAE,EAAEE,EAAE,EAAET,GAAG,CAAC;MAClC,CAAC,MACI;QACD;QACA,IAAIO,EAAE,GAAG,CAAC;QACV,IAAIE,EAAE,GAAG7B,MAAM,CAAC8B,SAAS,CAAC,CAAC,GAAG,CAAC,IAAIb,CAAC,GAAG,CAAC,CAAC;QACzCjB,MAAM,CAAC0B,UAAU,CAACC,EAAE,EAAEE,EAAE,EAAET,GAAG,CAAC;MAClC;IACJ;EACJ,CAAC;EACD;EACA;EACAtB,UAAU,CAACW,qBAAqB,GAAG,UAAUJ,OAAO,EAAEL,MAAM,EAAE;IAC1D,IAAIK,OAAO,CAAC0B,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE;MAAE;MAClC,OAAO,CAAC;IACZ;IACA,IAAIC,eAAe,GAAG,IAAIxC,QAAQ,CAAC,CAAC;IACpCM,UAAU,CAACmC,mBAAmB,CAAC5B,OAAO,EAAE2B,eAAe,CAAC;IACxD,IAAIE,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACxB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;QACxB;QACA,IAAIf,GAAG,GAAGY,eAAe,CAACX,GAAG,CAACa,QAAQ,CAAC;QACvCA,QAAQ,EAAE;QACV;QACAlC,MAAM,CAAC0B,UAAU,CAACT,CAAC,EAAEjB,MAAM,CAAC8B,SAAS,CAAC,CAAC,GAAG,EAAE,GAAGK,CAAC,EAAEf,GAAG,CAAC;QACtD;QACApB,MAAM,CAAC0B,UAAU,CAAC1B,MAAM,CAAC8B,SAAS,CAAC,CAAC,GAAG,EAAE,GAAGK,CAAC,EAAElB,CAAC,EAAEG,GAAG,CAAC;MAC1D;IACJ;EACJ,CAAC;EACD;EACA;EACA;EACAtB,UAAU,CAACY,aAAa,GAAG,UAAUP,QAAQ,EAAEG,WAAW,CAAC,SAASN,MAAM,EAAE;IACxE,IAAIkC,QAAQ,GAAG,CAAC;IAChB,IAAIE,SAAS,GAAG,CAAC,CAAC;IAClB;IACA,IAAIC,CAAC,GAAGrC,MAAM,CAAC4B,QAAQ,CAAC,CAAC,GAAG,CAAC;IAC7B,IAAIU,CAAC,GAAGtC,MAAM,CAAC8B,SAAS,CAAC,CAAC,GAAG,CAAC;IAC9B,OAAOO,CAAC,GAAG,CAAC,EAAE;MACV;MACA,IAAIA,CAAC,KAAK,CAAC,EAAE;QACTA,CAAC,IAAI,CAAC;MACV;MACA,OAAOC,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAGtC,MAAM,CAAC8B,SAAS,CAAC,CAAC,EAAE;QACrC,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;UACxB,IAAIsB,EAAE,GAAGF,CAAC,GAAGpB,CAAC;UACd;UACA,IAAI,CAACnB,UAAU,CAAC0C,OAAO,CAACxC,MAAM,CAACqB,GAAG,CAACkB,EAAE,EAAED,CAAC,CAAC,CAAC,EAAE;YACxC;UACJ;UACA,IAAIlB,GAAG,GAAG,KAAK,CAAC;UAChB,IAAIc,QAAQ,GAAG/B,QAAQ,CAACgB,OAAO,CAAC,CAAC,EAAE;YAC/BC,GAAG,GAAGjB,QAAQ,CAACkB,GAAG,CAACa,QAAQ,CAAC;YAC5B,EAAEA,QAAQ;UACd,CAAC,MACI;YACD;YACA;YACAd,GAAG,GAAG,KAAK;UACf;UACA;UACA,IAAId,WAAW,KAAK,GAAG,IAAIX,QAAQ,CAAC8C,cAAc,CAACnC,WAAW,EAAEiC,EAAE,EAAED,CAAC,CAAC,EAAE;YACpElB,GAAG,GAAG,CAACA,GAAG;UACd;UACApB,MAAM,CAAC0B,UAAU,CAACa,EAAE,EAAED,CAAC,EAAElB,GAAG,CAAC;QACjC;QACAkB,CAAC,IAAIF,SAAS;MAClB;MACAA,SAAS,GAAG,CAACA,SAAS,CAAC,CAAC;MACxBE,CAAC,IAAIF,SAAS;MACdC,CAAC,IAAI,CAAC,CAAC,CAAC;IACZ;IACA;IACA,IAAIH,QAAQ,KAAK/B,QAAQ,CAACgB,OAAO,CAAC,CAAC,EAAE;MACjC,MAAM,IAAIvB,eAAe,CAAC,yBAAyB,GAAGsC,QAAQ,GAAG,GAAG,GAAG/B,QAAQ,CAACgB,OAAO,CAAC,CAAC,CAAC;IAC9F;EACJ,CAAC;EACD;EACA;EACA;EACA;EACA;EACArB,UAAU,CAAC4C,UAAU,GAAG,UAAUC,KAAK,CAAC,SAAS;IAC7C,OAAO,EAAE,GAAGlD,OAAO,CAACmD,oBAAoB,CAACD,KAAK,CAAC;EACnD,CAAC;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA7C,UAAU,CAAC+C,gBAAgB,GAAG,UAAUF,KAAK,CAAC,SAASG,IAAI,CAAC,SAAS;IACjE,IAAIA,IAAI,KAAK,CAAC,EAAE;MACZ,MAAM,IAAIjD,wBAAwB,CAAC,cAAc,CAAC;IACtD;IACA;IACA;IACA,IAAIkD,YAAY,GAAGjD,UAAU,CAAC4C,UAAU,CAACI,IAAI,CAAC;IAC9CH,KAAK,KAAKI,YAAY,GAAG,CAAC;IAC1B;IACA,OAAOjD,UAAU,CAAC4C,UAAU,CAACC,KAAK,CAAC,IAAII,YAAY,EAAE;MACjDJ,KAAK,IAAIG,IAAI,IAAKhD,UAAU,CAAC4C,UAAU,CAACC,KAAK,CAAC,GAAGI,YAAa;IAClE;IACA;IACA,OAAOJ,KAAK;EAChB,CAAC;EACD;EACA;EACA;EACA7C,UAAU,CAACkB,gBAAgB,GAAG,UAAUZ,OAAO,EAAEE,WAAW,CAAC,SAAS0C,IAAI,EAAE;IACxE,IAAI,CAACtD,MAAM,CAACuD,kBAAkB,CAAC3C,WAAW,CAAC,EAAE;MACzC,MAAM,IAAIV,eAAe,CAAC,sBAAsB,CAAC;IACrD;IACA,IAAIsD,QAAQ,GAAI9C,OAAO,CAAC+C,OAAO,CAAC,CAAC,IAAI,CAAC,GAAI7C,WAAW;IACrD0C,IAAI,CAACI,UAAU,CAACF,QAAQ,EAAE,CAAC,CAAC;IAC5B,IAAIG,OAAO,GAAGvD,UAAU,CAAC+C,gBAAgB,CAACK,QAAQ,EAAEpD,UAAU,CAACwD,cAAc,CAAC;IAC9EN,IAAI,CAACI,UAAU,CAACC,OAAO,EAAE,EAAE,CAAC;IAC5B,IAAIE,QAAQ,GAAG,IAAI/D,QAAQ,CAAC,CAAC;IAC7B+D,QAAQ,CAACH,UAAU,CAACtD,UAAU,CAAC0D,sBAAsB,EAAE,EAAE,CAAC;IAC1DR,IAAI,CAACS,GAAG,CAACF,QAAQ,CAAC;IAClB,IAAIP,IAAI,CAAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;MAAE;MACzB,MAAM,IAAIvB,eAAe,CAAC,gCAAgC,GAAGoD,IAAI,CAAC7B,OAAO,CAAC,CAAC,CAAC;IAChF;EACJ,CAAC;EACD;EACA;EACArB,UAAU,CAACmC,mBAAmB,GAAG,UAAU5B,OAAO,EAAE2C,IAAI,EAAE;IACtDA,IAAI,CAACI,UAAU,CAAC/C,OAAO,CAAC0B,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9C,IAAIsB,OAAO,GAAGvD,UAAU,CAAC+C,gBAAgB,CAACxC,OAAO,CAAC0B,gBAAgB,CAAC,CAAC,EAAEjC,UAAU,CAAC4D,iBAAiB,CAAC;IACnGV,IAAI,CAACI,UAAU,CAACC,OAAO,EAAE,EAAE,CAAC;IAC5B,IAAIL,IAAI,CAAC7B,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;MAAE;MACzB,MAAM,IAAIvB,eAAe,CAAC,gCAAgC,GAAGoD,IAAI,CAAC7B,OAAO,CAAC,CAAC,CAAC;IAChF;EACJ,CAAC;EACD;EACArB,UAAU,CAAC0C,OAAO,GAAG,UAAUG,KAAK,CAAC,SAAS;IAC1C,OAAOA,KAAK,KAAK,GAAG,CAAC,CAAC;EAC1B,CAAC;EACD7C,UAAU,CAACgB,mBAAmB,GAAG,UAAUd,MAAM,EAAE;IAC/C;IACA;IACA,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,MAAM,CAAC4B,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,EAAEX,CAAC,EAAE;MAC5C,IAAIG,GAAG,GAAG,CAACH,CAAC,GAAG,CAAC,IAAI,CAAC;MACrB;MACA,IAAInB,UAAU,CAAC0C,OAAO,CAACxC,MAAM,CAACqB,GAAG,CAACJ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;QACtCjB,MAAM,CAAC2D,SAAS,CAAC1C,CAAC,EAAE,CAAC,EAAEG,GAAG,CAAC;MAC/B;MACA;MACA,IAAItB,UAAU,CAAC0C,OAAO,CAACxC,MAAM,CAACqB,GAAG,CAAC,CAAC,EAAEJ,CAAC,CAAC,CAAC,EAAE;QACtCjB,MAAM,CAAC2D,SAAS,CAAC,CAAC,EAAE1C,CAAC,EAAEG,GAAG,CAAC;MAC/B;IACJ;EACJ,CAAC;EACD;EACAtB,UAAU,CAACc,8BAA8B,GAAG,UAAUZ,MAAM,EAAE;IAC1D,IAAIA,MAAM,CAACqB,GAAG,CAAC,CAAC,EAAErB,MAAM,CAAC8B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MAC7C,MAAM,IAAIlC,eAAe,CAAC,CAAC;IAC/B;IACAI,MAAM,CAAC2D,SAAS,CAAC,CAAC,EAAE3D,MAAM,CAAC8B,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;EAClD,CAAC;EACDhC,UAAU,CAAC8D,gCAAgC,GAAG,UAAUC,MAAM,CAAC,SAASC,MAAM,CAAC,SAAS9D,MAAM,EAAE;IAC5F,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACxB,IAAI,CAACvC,UAAU,CAAC0C,OAAO,CAACxC,MAAM,CAACqB,GAAG,CAACwC,MAAM,GAAGxB,CAAC,EAAEyB,MAAM,CAAC,CAAC,EAAE;QACrD,MAAM,IAAIlE,eAAe,CAAC,CAAC;MAC/B;MACAI,MAAM,CAAC2D,SAAS,CAACE,MAAM,GAAGxB,CAAC,EAAEyB,MAAM,EAAE,CAAC,CAAC;IAC3C;EACJ,CAAC;EACDhE,UAAU,CAACiE,8BAA8B,GAAG,UAAUF,MAAM,CAAC,SAASC,MAAM,CAAC,SAAS9D,MAAM,EAAE;IAC1F,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACxB,IAAI,CAACxC,UAAU,CAAC0C,OAAO,CAACxC,MAAM,CAACqB,GAAG,CAACwC,MAAM,EAAEC,MAAM,GAAGxB,CAAC,CAAC,CAAC,EAAE;QACrD,MAAM,IAAI1C,eAAe,CAAC,CAAC;MAC/B;MACAI,MAAM,CAAC2D,SAAS,CAACE,MAAM,EAAEC,MAAM,GAAGxB,CAAC,EAAE,CAAC,CAAC;IAC3C;EACJ,CAAC;EACDxC,UAAU,CAACkE,8BAA8B,GAAG,UAAUH,MAAM,CAAC,SAASC,MAAM,CAAC,SAAS9D,MAAM,EAAE;IAC1F,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACxB,IAAI2B,QAAQ,GAAGnE,UAAU,CAACoE,2BAA2B,CAAC5B,CAAC,CAAC;MACxD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;QACxBrC,MAAM,CAAC2D,SAAS,CAACE,MAAM,GAAGxB,CAAC,EAAEyB,MAAM,GAAGxB,CAAC,EAAE2B,QAAQ,CAAC5B,CAAC,CAAC,CAAC;MACzD;IACJ;EACJ,CAAC;EACDvC,UAAU,CAACqE,6BAA6B,GAAG,UAAUN,MAAM,CAAC,SAASC,MAAM,CAAC,SAAS9D,MAAM,EAAE;IACzF,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACxB,IAAI2B,QAAQ,GAAGnE,UAAU,CAACsE,0BAA0B,CAAC9B,CAAC,CAAC;MACvD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;QACxBrC,MAAM,CAAC2D,SAAS,CAACE,MAAM,GAAGxB,CAAC,EAAEyB,MAAM,GAAGxB,CAAC,EAAE2B,QAAQ,CAAC5B,CAAC,CAAC,CAAC;MACzD;IACJ;EACJ,CAAC;EACD;EACAvC,UAAU,CAACa,2CAA2C,GAAG,UAAUX,MAAM,EAAE;IACvE;IACA,IAAIqE,QAAQ,GAAGvE,UAAU,CAACsE,0BAA0B,CAAC,CAAC,CAAC,CAACE,MAAM;IAC9D;IACAxE,UAAU,CAACqE,6BAA6B,CAAC,CAAC,EAAE,CAAC,EAAEnE,MAAM,CAAC;IACtD;IACAF,UAAU,CAACqE,6BAA6B,CAACnE,MAAM,CAAC4B,QAAQ,CAAC,CAAC,GAAGyC,QAAQ,EAAE,CAAC,EAAErE,MAAM,CAAC;IACjF;IACAF,UAAU,CAACqE,6BAA6B,CAAC,CAAC,EAAEnE,MAAM,CAAC4B,QAAQ,CAAC,CAAC,GAAGyC,QAAQ,EAAErE,MAAM,CAAC;IACjF;IACA,IAAIuE,QAAQ,GAAG,CAAC;IAChB;IACAzE,UAAU,CAAC8D,gCAAgC,CAAC,CAAC,EAAEW,QAAQ,GAAG,CAAC,EAAEvE,MAAM,CAAC;IACpE;IACAF,UAAU,CAAC8D,gCAAgC,CAAC5D,MAAM,CAAC4B,QAAQ,CAAC,CAAC,GAAG2C,QAAQ,EAAEA,QAAQ,GAAG,CAAC,EAAEvE,MAAM,CAAC;IAC/F;IACAF,UAAU,CAAC8D,gCAAgC,CAAC,CAAC,EAAE5D,MAAM,CAAC4B,QAAQ,CAAC,CAAC,GAAG2C,QAAQ,EAAEvE,MAAM,CAAC;IACpF;IACA,IAAIwE,OAAO,GAAG,CAAC;IACf;IACA1E,UAAU,CAACiE,8BAA8B,CAACS,OAAO,EAAE,CAAC,EAAExE,MAAM,CAAC;IAC7D;IACAF,UAAU,CAACiE,8BAA8B,CAAC/D,MAAM,CAAC8B,SAAS,CAAC,CAAC,GAAG0C,OAAO,GAAG,CAAC,EAAE,CAAC,EAAExE,MAAM,CAAC;IACtF;IACAF,UAAU,CAACiE,8BAA8B,CAACS,OAAO,EAAExE,MAAM,CAAC8B,SAAS,CAAC,CAAC,GAAG0C,OAAO,EAAExE,MAAM,CAAC;EAC5F,CAAC;EACD;EACAF,UAAU,CAACe,oCAAoC,GAAG,UAAUR,OAAO,EAAEL,MAAM,EAAE;IACzE,IAAIK,OAAO,CAAC0B,gBAAgB,CAAC,CAAC,GAAG,CAAC,EAAE;MAAE;MAClC;IACJ;IACA,IAAI0C,KAAK,GAAGpE,OAAO,CAAC0B,gBAAgB,CAAC,CAAC,GAAG,CAAC;IAC1C,IAAIT,WAAW,GAAGxB,UAAU,CAAC4E,4CAA4C,CAACD,KAAK,CAAC;IAChF,KAAK,IAAIxD,CAAC,GAAG,CAAC,EAAE0D,QAAQ,GAAGrD,WAAW,CAACgD,MAAM,EAAErD,CAAC,KAAK0D,QAAQ,EAAE1D,CAAC,EAAE,EAAE;MAChE,IAAIqB,CAAC,GAAGhB,WAAW,CAACL,CAAC,CAAC;MACtB,IAAIqB,CAAC,IAAI,CAAC,EAAE;QACR,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAKwC,QAAQ,EAAExC,CAAC,EAAE,EAAE;UACjC,IAAIE,CAAC,GAAGf,WAAW,CAACa,CAAC,CAAC;UACtB,IAAIE,CAAC,IAAI,CAAC,IAAIvC,UAAU,CAAC0C,OAAO,CAACxC,MAAM,CAACqB,GAAG,CAACgB,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAE;YAChD;YACA;YACA;YACAxC,UAAU,CAACkE,8BAA8B,CAAC3B,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEtC,MAAM,CAAC;UACnE;QACJ;MACJ;IACJ;EACJ,CAAC;EACDF,UAAU,CAACsE,0BAA0B,GAAGQ,KAAK,CAACC,IAAI,CAAC,CAC/CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACtCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACtCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACtCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACtCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACtCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EACtCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CACzC,CAAC;EACF/E,UAAU,CAACoE,2BAA2B,GAAGU,KAAK,CAACC,IAAI,CAAC,CAChDC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAChCC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CACnC,CAAC;EACF;EACA/E,UAAU,CAAC4E,4CAA4C,GAAGE,KAAK,CAACC,IAAI,CAAC,CACjEC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC5CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAC7CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAC9CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAC9CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAC9CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAC9CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAC9CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,EAC9CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAC/CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAC/CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAC/CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAC/CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAC/CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAClD,CAAC;EACF;EACA/E,UAAU,CAACyB,qBAAqB,GAAGqD,KAAK,CAACC,IAAI,CAAC,CAC1CC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACvBC,UAAU,CAACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAC1B,CAAC;EACF;EACA/E,UAAU,CAAC4D,iBAAiB,GAAG,MAAM,CAAC,CAAC;EACvC;EACA5D,UAAU,CAACwD,cAAc,GAAG,KAAK;EACjCxD,UAAU,CAAC0D,sBAAsB,GAAG,MAAM;EAC1C,OAAO1D,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}