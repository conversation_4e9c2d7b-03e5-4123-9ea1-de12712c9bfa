{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates a block of data within a Data Matrix Code. Data Matrix Codes may split their data into\n * multiple blocks, each of which is a unit of data and error-correction codewords. Each\n * is represented by an instance of this class.</p>\n *\n * <AUTHOR> (Brian Brown)\n */\nvar DataBlock = /** @class */function () {\n  function DataBlock(numDataCodewords, codewords) {\n    this.numDataCodewords = numDataCodewords;\n    this.codewords = codewords;\n  }\n  /**\n   * <p>When Data Matrix Codes use multiple data blocks, they actually interleave the bytes of each of them.\n   * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This\n   * method will separate the data into original blocks.</p>\n   *\n   * @param rawCodewords bytes as read directly from the Data Matrix Code\n   * @param version version of the Data Matrix Code\n   * @return DataBlocks containing original bytes, \"de-interleaved\" from representation in the\n   *         Data Matrix Code\n   */\n  DataBlock.getDataBlocks = function (rawCodewords, version) {\n    var e_1, _a, e_2, _b;\n    // Figure out the number and size of data blocks used by this version\n    var ecBlocks = version.getECBlocks();\n    // First count the total number of data blocks\n    var totalBlocks = 0;\n    var ecBlockArray = ecBlocks.getECBlocks();\n    try {\n      for (var ecBlockArray_1 = __values(ecBlockArray), ecBlockArray_1_1 = ecBlockArray_1.next(); !ecBlockArray_1_1.done; ecBlockArray_1_1 = ecBlockArray_1.next()) {\n        var ecBlock = ecBlockArray_1_1.value;\n        totalBlocks += ecBlock.getCount();\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (ecBlockArray_1_1 && !ecBlockArray_1_1.done && (_a = ecBlockArray_1.return)) _a.call(ecBlockArray_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    // Now establish DataBlocks of the appropriate size and number of data codewords\n    var result = new Array(totalBlocks);\n    var numResultBlocks = 0;\n    try {\n      for (var ecBlockArray_2 = __values(ecBlockArray), ecBlockArray_2_1 = ecBlockArray_2.next(); !ecBlockArray_2_1.done; ecBlockArray_2_1 = ecBlockArray_2.next()) {\n        var ecBlock = ecBlockArray_2_1.value;\n        for (var i = 0; i < ecBlock.getCount(); i++) {\n          var numDataCodewords = ecBlock.getDataCodewords();\n          var numBlockCodewords = ecBlocks.getECCodewords() + numDataCodewords;\n          result[numResultBlocks++] = new DataBlock(numDataCodewords, new Uint8Array(numBlockCodewords));\n        }\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (ecBlockArray_2_1 && !ecBlockArray_2_1.done && (_b = ecBlockArray_2.return)) _b.call(ecBlockArray_2);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    // All blocks have the same amount of data, except that the last n\n    // (where n may be 0) have 1 less byte. Figure out where these start.\n    // TODO(bbrown): There is only one case where there is a difference for Data Matrix for size 144\n    var longerBlocksTotalCodewords = result[0].codewords.length;\n    // int shorterBlocksTotalCodewords = longerBlocksTotalCodewords - 1;\n    var longerBlocksNumDataCodewords = longerBlocksTotalCodewords - ecBlocks.getECCodewords();\n    var shorterBlocksNumDataCodewords = longerBlocksNumDataCodewords - 1;\n    // The last elements of result may be 1 element shorter for 144 matrix\n    // first fill out as many elements as all of them have minus 1\n    var rawCodewordsOffset = 0;\n    for (var i = 0; i < shorterBlocksNumDataCodewords; i++) {\n      for (var j = 0; j < numResultBlocks; j++) {\n        result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];\n      }\n    }\n    // Fill out the last data block in the longer ones\n    var specialVersion = version.getVersionNumber() === 24;\n    var numLongerBlocks = specialVersion ? 8 : numResultBlocks;\n    for (var j = 0; j < numLongerBlocks; j++) {\n      result[j].codewords[longerBlocksNumDataCodewords - 1] = rawCodewords[rawCodewordsOffset++];\n    }\n    // Now add in error correction blocks\n    var max = result[0].codewords.length;\n    for (var i = longerBlocksNumDataCodewords; i < max; i++) {\n      for (var j = 0; j < numResultBlocks; j++) {\n        var jOffset = specialVersion ? (j + 8) % numResultBlocks : j;\n        var iOffset = specialVersion && jOffset > 7 ? i - 1 : i;\n        result[jOffset].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];\n      }\n    }\n    if (rawCodewordsOffset !== rawCodewords.length) {\n      throw new IllegalArgumentException();\n    }\n    return result;\n  };\n  DataBlock.prototype.getNumDataCodewords = function () {\n    return this.numDataCodewords;\n  };\n  DataBlock.prototype.getCodewords = function () {\n    return this.codewords;\n  };\n  return DataBlock;\n}();\nexport default DataBlock;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "IllegalArgumentException", "DataBlock", "numDataCodewords", "codewords", "getDataBlocks", "rawCodewords", "version", "e_1", "_a", "e_2", "_b", "ecBlocks", "getECBlocks", "totalBlocks", "ecBlockArray", "ecBlockArray_1", "ecBlockArray_1_1", "ecBlock", "getCount", "e_1_1", "error", "return", "result", "Array", "numResultBlocks", "ecBlockArray_2", "ecBlockArray_2_1", "getDataCodewords", "numBlockCodewords", "getECCodewords", "Uint8Array", "e_2_1", "longerBlocksTotalCodewords", "longerBlocksNumDataCodewords", "shorterBlocksNumDataCodewords", "rawCodewordsOffset", "j", "specialVersion", "getVersionNumber", "numLongerBlocks", "max", "jOffset", "iOffset", "prototype", "getNumDataCodewords", "getCodewords"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/decoder/DataBlock.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates a block of data within a Data Matrix Code. Data Matrix Codes may split their data into\n * multiple blocks, each of which is a unit of data and error-correction codewords. Each\n * is represented by an instance of this class.</p>\n *\n * <AUTHOR> (Brian Brown)\n */\nvar DataBlock = /** @class */ (function () {\n    function DataBlock(numDataCodewords, codewords) {\n        this.numDataCodewords = numDataCodewords;\n        this.codewords = codewords;\n    }\n    /**\n     * <p>When Data Matrix Codes use multiple data blocks, they actually interleave the bytes of each of them.\n     * That is, the first byte of data block 1 to n is written, then the second bytes, and so on. This\n     * method will separate the data into original blocks.</p>\n     *\n     * @param rawCodewords bytes as read directly from the Data Matrix Code\n     * @param version version of the Data Matrix Code\n     * @return DataBlocks containing original bytes, \"de-interleaved\" from representation in the\n     *         Data Matrix Code\n     */\n    DataBlock.getDataBlocks = function (rawCodewords, version) {\n        var e_1, _a, e_2, _b;\n        // Figure out the number and size of data blocks used by this version\n        var ecBlocks = version.getECBlocks();\n        // First count the total number of data blocks\n        var totalBlocks = 0;\n        var ecBlockArray = ecBlocks.getECBlocks();\n        try {\n            for (var ecBlockArray_1 = __values(ecBlockArray), ecBlockArray_1_1 = ecBlockArray_1.next(); !ecBlockArray_1_1.done; ecBlockArray_1_1 = ecBlockArray_1.next()) {\n                var ecBlock = ecBlockArray_1_1.value;\n                totalBlocks += ecBlock.getCount();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (ecBlockArray_1_1 && !ecBlockArray_1_1.done && (_a = ecBlockArray_1.return)) _a.call(ecBlockArray_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        // Now establish DataBlocks of the appropriate size and number of data codewords\n        var result = new Array(totalBlocks);\n        var numResultBlocks = 0;\n        try {\n            for (var ecBlockArray_2 = __values(ecBlockArray), ecBlockArray_2_1 = ecBlockArray_2.next(); !ecBlockArray_2_1.done; ecBlockArray_2_1 = ecBlockArray_2.next()) {\n                var ecBlock = ecBlockArray_2_1.value;\n                for (var i = 0; i < ecBlock.getCount(); i++) {\n                    var numDataCodewords = ecBlock.getDataCodewords();\n                    var numBlockCodewords = ecBlocks.getECCodewords() + numDataCodewords;\n                    result[numResultBlocks++] = new DataBlock(numDataCodewords, new Uint8Array(numBlockCodewords));\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (ecBlockArray_2_1 && !ecBlockArray_2_1.done && (_b = ecBlockArray_2.return)) _b.call(ecBlockArray_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        // All blocks have the same amount of data, except that the last n\n        // (where n may be 0) have 1 less byte. Figure out where these start.\n        // TODO(bbrown): There is only one case where there is a difference for Data Matrix for size 144\n        var longerBlocksTotalCodewords = result[0].codewords.length;\n        // int shorterBlocksTotalCodewords = longerBlocksTotalCodewords - 1;\n        var longerBlocksNumDataCodewords = longerBlocksTotalCodewords - ecBlocks.getECCodewords();\n        var shorterBlocksNumDataCodewords = longerBlocksNumDataCodewords - 1;\n        // The last elements of result may be 1 element shorter for 144 matrix\n        // first fill out as many elements as all of them have minus 1\n        var rawCodewordsOffset = 0;\n        for (var i = 0; i < shorterBlocksNumDataCodewords; i++) {\n            for (var j = 0; j < numResultBlocks; j++) {\n                result[j].codewords[i] = rawCodewords[rawCodewordsOffset++];\n            }\n        }\n        // Fill out the last data block in the longer ones\n        var specialVersion = version.getVersionNumber() === 24;\n        var numLongerBlocks = specialVersion ? 8 : numResultBlocks;\n        for (var j = 0; j < numLongerBlocks; j++) {\n            result[j].codewords[longerBlocksNumDataCodewords - 1] = rawCodewords[rawCodewordsOffset++];\n        }\n        // Now add in error correction blocks\n        var max = result[0].codewords.length;\n        for (var i = longerBlocksNumDataCodewords; i < max; i++) {\n            for (var j = 0; j < numResultBlocks; j++) {\n                var jOffset = specialVersion ? (j + 8) % numResultBlocks : j;\n                var iOffset = specialVersion && jOffset > 7 ? i - 1 : i;\n                result[jOffset].codewords[iOffset] = rawCodewords[rawCodewordsOffset++];\n            }\n        }\n        if (rawCodewordsOffset !== rawCodewords.length) {\n            throw new IllegalArgumentException();\n        }\n        return result;\n    };\n    DataBlock.prototype.getNumDataCodewords = function () {\n        return this.numDataCodewords;\n    };\n    DataBlock.prototype.getCodewords = function () {\n        return this.codewords;\n    };\n    return DataBlock;\n}());\nexport default DataBlock;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACC,gBAAgB,EAAEC,SAAS,EAAE;IAC5C,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,SAAS,CAACG,aAAa,GAAG,UAAUC,YAAY,EAAEC,OAAO,EAAE;IACvD,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB;IACA,IAAIC,QAAQ,GAAGL,OAAO,CAACM,WAAW,CAAC,CAAC;IACpC;IACA,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,YAAY,GAAGH,QAAQ,CAACC,WAAW,CAAC,CAAC;IACzC,IAAI;MACA,KAAK,IAAIG,cAAc,GAAG5B,QAAQ,CAAC2B,YAAY,CAAC,EAAEE,gBAAgB,GAAGD,cAAc,CAACnB,IAAI,CAAC,CAAC,EAAE,CAACoB,gBAAgB,CAAClB,IAAI,EAAEkB,gBAAgB,GAAGD,cAAc,CAACnB,IAAI,CAAC,CAAC,EAAE;QAC1J,IAAIqB,OAAO,GAAGD,gBAAgB,CAACnB,KAAK;QACpCgB,WAAW,IAAII,OAAO,CAACC,QAAQ,CAAC,CAAC;MACrC;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEZ,GAAG,GAAG;QAAEa,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,gBAAgB,IAAI,CAACA,gBAAgB,CAAClB,IAAI,KAAKU,EAAE,GAAGO,cAAc,CAACM,MAAM,CAAC,EAAEb,EAAE,CAACd,IAAI,CAACqB,cAAc,CAAC;MAC3G,CAAC,SACO;QAAE,IAAIR,GAAG,EAAE,MAAMA,GAAG,CAACa,KAAK;MAAE;IACxC;IACA;IACA,IAAIE,MAAM,GAAG,IAAIC,KAAK,CAACV,WAAW,CAAC;IACnC,IAAIW,eAAe,GAAG,CAAC;IACvB,IAAI;MACA,KAAK,IAAIC,cAAc,GAAGtC,QAAQ,CAAC2B,YAAY,CAAC,EAAEY,gBAAgB,GAAGD,cAAc,CAAC7B,IAAI,CAAC,CAAC,EAAE,CAAC8B,gBAAgB,CAAC5B,IAAI,EAAE4B,gBAAgB,GAAGD,cAAc,CAAC7B,IAAI,CAAC,CAAC,EAAE;QAC1J,IAAIqB,OAAO,GAAGS,gBAAgB,CAAC7B,KAAK;QACpC,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,OAAO,CAACC,QAAQ,CAAC,CAAC,EAAEzB,CAAC,EAAE,EAAE;UACzC,IAAIS,gBAAgB,GAAGe,OAAO,CAACU,gBAAgB,CAAC,CAAC;UACjD,IAAIC,iBAAiB,GAAGjB,QAAQ,CAACkB,cAAc,CAAC,CAAC,GAAG3B,gBAAgB;UACpEoB,MAAM,CAACE,eAAe,EAAE,CAAC,GAAG,IAAIvB,SAAS,CAACC,gBAAgB,EAAE,IAAI4B,UAAU,CAACF,iBAAiB,CAAC,CAAC;QAClG;MACJ;IACJ,CAAC,CACD,OAAOG,KAAK,EAAE;MAAEtB,GAAG,GAAG;QAAEW,KAAK,EAAEW;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIL,gBAAgB,IAAI,CAACA,gBAAgB,CAAC5B,IAAI,KAAKY,EAAE,GAAGe,cAAc,CAACJ,MAAM,CAAC,EAAEX,EAAE,CAAChB,IAAI,CAAC+B,cAAc,CAAC;MAC3G,CAAC,SACO;QAAE,IAAIhB,GAAG,EAAE,MAAMA,GAAG,CAACW,KAAK;MAAE;IACxC;IACA;IACA;IACA;IACA,IAAIY,0BAA0B,GAAGV,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAACR,MAAM;IAC3D;IACA,IAAIsC,4BAA4B,GAAGD,0BAA0B,GAAGrB,QAAQ,CAACkB,cAAc,CAAC,CAAC;IACzF,IAAIK,6BAA6B,GAAGD,4BAA4B,GAAG,CAAC;IACpE;IACA;IACA,IAAIE,kBAAkB,GAAG,CAAC;IAC1B,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyC,6BAA6B,EAAEzC,CAAC,EAAE,EAAE;MACpD,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,eAAe,EAAEY,CAAC,EAAE,EAAE;QACtCd,MAAM,CAACc,CAAC,CAAC,CAACjC,SAAS,CAACV,CAAC,CAAC,GAAGY,YAAY,CAAC8B,kBAAkB,EAAE,CAAC;MAC/D;IACJ;IACA;IACA,IAAIE,cAAc,GAAG/B,OAAO,CAACgC,gBAAgB,CAAC,CAAC,KAAK,EAAE;IACtD,IAAIC,eAAe,GAAGF,cAAc,GAAG,CAAC,GAAGb,eAAe;IAC1D,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,eAAe,EAAEH,CAAC,EAAE,EAAE;MACtCd,MAAM,CAACc,CAAC,CAAC,CAACjC,SAAS,CAAC8B,4BAA4B,GAAG,CAAC,CAAC,GAAG5B,YAAY,CAAC8B,kBAAkB,EAAE,CAAC;IAC9F;IACA;IACA,IAAIK,GAAG,GAAGlB,MAAM,CAAC,CAAC,CAAC,CAACnB,SAAS,CAACR,MAAM;IACpC,KAAK,IAAIF,CAAC,GAAGwC,4BAA4B,EAAExC,CAAC,GAAG+C,GAAG,EAAE/C,CAAC,EAAE,EAAE;MACrD,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,eAAe,EAAEY,CAAC,EAAE,EAAE;QACtC,IAAIK,OAAO,GAAGJ,cAAc,GAAG,CAACD,CAAC,GAAG,CAAC,IAAIZ,eAAe,GAAGY,CAAC;QAC5D,IAAIM,OAAO,GAAGL,cAAc,IAAII,OAAO,GAAG,CAAC,GAAGhD,CAAC,GAAG,CAAC,GAAGA,CAAC;QACvD6B,MAAM,CAACmB,OAAO,CAAC,CAACtC,SAAS,CAACuC,OAAO,CAAC,GAAGrC,YAAY,CAAC8B,kBAAkB,EAAE,CAAC;MAC3E;IACJ;IACA,IAAIA,kBAAkB,KAAK9B,YAAY,CAACV,MAAM,EAAE;MAC5C,MAAM,IAAIK,wBAAwB,CAAC,CAAC;IACxC;IACA,OAAOsB,MAAM;EACjB,CAAC;EACDrB,SAAS,CAAC0C,SAAS,CAACC,mBAAmB,GAAG,YAAY;IAClD,OAAO,IAAI,CAAC1C,gBAAgB;EAChC,CAAC;EACDD,SAAS,CAAC0C,SAAS,CAACE,YAAY,GAAG,YAAY;IAC3C,OAAO,IAAI,CAAC1C,SAAS;EACzB,CAAC;EACD,OAAOF,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}