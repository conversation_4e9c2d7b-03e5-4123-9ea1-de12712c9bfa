{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _merge = require(\"../help/merge.js\");\nvar _merge2 = _interopRequireDefault(_merge);\nvar _shared = require(\"./shared.js\");\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nvar svgns = \"http://www.w3.org/2000/svg\";\nvar SVGRenderer = function () {\n  function SVGRenderer(svg, encodings, options) {\n    _classCallCheck(this, SVGRenderer);\n    this.svg = svg;\n    this.encodings = encodings;\n    this.options = options;\n    this.document = options.xmlDocument || document;\n  }\n  _createClass(SVGRenderer, [{\n    key: \"render\",\n    value: function render() {\n      var currentX = this.options.marginLeft;\n      this.prepareSVG();\n      for (var i = 0; i < this.encodings.length; i++) {\n        var encoding = this.encodings[i];\n        var encodingOptions = (0, _merge2.default)(this.options, encoding.options);\n        var group = this.createGroup(currentX, encodingOptions.marginTop, this.svg);\n        this.setGroupOptions(group, encodingOptions);\n        this.drawSvgBarcode(group, encodingOptions, encoding);\n        this.drawSVGText(group, encodingOptions, encoding);\n        currentX += encoding.width;\n      }\n    }\n  }, {\n    key: \"prepareSVG\",\n    value: function prepareSVG() {\n      // Clear the SVG\n      while (this.svg.firstChild) {\n        this.svg.removeChild(this.svg.firstChild);\n      }\n      (0, _shared.calculateEncodingAttributes)(this.encodings, this.options);\n      var totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);\n      var maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);\n      var width = totalWidth + this.options.marginLeft + this.options.marginRight;\n      this.setSvgAttributes(width, maxHeight);\n      if (this.options.background) {\n        this.drawRect(0, 0, width, maxHeight, this.svg).setAttribute(\"style\", \"fill:\" + this.options.background + \";\");\n      }\n    }\n  }, {\n    key: \"drawSvgBarcode\",\n    value: function drawSvgBarcode(parent, options, encoding) {\n      var binary = encoding.data;\n\n      // Creates the barcode out of the encoded binary\n      var yFrom;\n      if (options.textPosition == \"top\") {\n        yFrom = options.fontSize + options.textMargin;\n      } else {\n        yFrom = 0;\n      }\n      var barWidth = 0;\n      var x = 0;\n      for (var b = 0; b < binary.length; b++) {\n        x = b * options.width + encoding.barcodePadding;\n        if (binary[b] === \"1\") {\n          barWidth++;\n        } else if (barWidth > 0) {\n          this.drawRect(x - options.width * barWidth, yFrom, options.width * barWidth, options.height, parent);\n          barWidth = 0;\n        }\n      }\n\n      // Last draw is needed since the barcode ends with 1\n      if (barWidth > 0) {\n        this.drawRect(x - options.width * (barWidth - 1), yFrom, options.width * barWidth, options.height, parent);\n      }\n    }\n  }, {\n    key: \"drawSVGText\",\n    value: function drawSVGText(parent, options, encoding) {\n      var textElem = this.document.createElementNS(svgns, 'text');\n\n      // Draw the text if displayValue is set\n      if (options.displayValue) {\n        var x, y;\n        textElem.setAttribute(\"style\", \"font:\" + options.fontOptions + \" \" + options.fontSize + \"px \" + options.font);\n        if (options.textPosition == \"top\") {\n          y = options.fontSize - options.textMargin;\n        } else {\n          y = options.height + options.textMargin + options.fontSize;\n        }\n\n        // Draw the text in the correct X depending on the textAlign option\n        if (options.textAlign == \"left\" || encoding.barcodePadding > 0) {\n          x = 0;\n          textElem.setAttribute(\"text-anchor\", \"start\");\n        } else if (options.textAlign == \"right\") {\n          x = encoding.width - 1;\n          textElem.setAttribute(\"text-anchor\", \"end\");\n        }\n        // In all other cases, center the text\n        else {\n          x = encoding.width / 2;\n          textElem.setAttribute(\"text-anchor\", \"middle\");\n        }\n        textElem.setAttribute(\"x\", x);\n        textElem.setAttribute(\"y\", y);\n        textElem.appendChild(this.document.createTextNode(encoding.text));\n        parent.appendChild(textElem);\n      }\n    }\n  }, {\n    key: \"setSvgAttributes\",\n    value: function setSvgAttributes(width, height) {\n      var svg = this.svg;\n      svg.setAttribute(\"width\", width + \"px\");\n      svg.setAttribute(\"height\", height + \"px\");\n      svg.setAttribute(\"x\", \"0px\");\n      svg.setAttribute(\"y\", \"0px\");\n      svg.setAttribute(\"viewBox\", \"0 0 \" + width + \" \" + height);\n      svg.setAttribute(\"xmlns\", svgns);\n      svg.setAttribute(\"version\", \"1.1\");\n      svg.setAttribute(\"style\", \"transform: translate(0,0)\");\n    }\n  }, {\n    key: \"createGroup\",\n    value: function createGroup(x, y, parent) {\n      var group = this.document.createElementNS(svgns, 'g');\n      group.setAttribute(\"transform\", \"translate(\" + x + \", \" + y + \")\");\n      parent.appendChild(group);\n      return group;\n    }\n  }, {\n    key: \"setGroupOptions\",\n    value: function setGroupOptions(group, options) {\n      group.setAttribute(\"style\", \"fill:\" + options.lineColor + \";\");\n    }\n  }, {\n    key: \"drawRect\",\n    value: function drawRect(x, y, width, height, parent) {\n      var rect = this.document.createElementNS(svgns, 'rect');\n      rect.setAttribute(\"x\", x);\n      rect.setAttribute(\"y\", y);\n      rect.setAttribute(\"width\", width);\n      rect.setAttribute(\"height\", height);\n      parent.appendChild(rect);\n      return rect;\n    }\n  }]);\n  return SVGRenderer;\n}();\nexports.default = SVGRenderer;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_merge", "require", "_merge2", "_interopRequireDefault", "_shared", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "svgns", "<PERSON><PERSON><PERSON><PERSON>", "svg", "encodings", "options", "document", "xmlDocument", "render", "currentX", "marginLeft", "prepareSVG", "encoding", "encodingOptions", "group", "createGroup", "marginTop", "setGroupOptions", "drawSvgBarcode", "drawSVGText", "width", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "calculateEncodingAttributes", "totalWidth", "getTotalWidthOfEncodings", "maxHeight", "getMaximumHeightOfEncodings", "marginRight", "setSvgAttributes", "background", "drawRect", "setAttribute", "parent", "binary", "data", "yFrom", "textPosition", "fontSize", "textMargin", "<PERSON><PERSON><PERSON><PERSON>", "x", "b", "barcodePadding", "height", "textElem", "createElementNS", "displayValue", "y", "fontOptions", "font", "textAlign", "append<PERSON><PERSON><PERSON>", "createTextNode", "text", "lineColor", "rect"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/renderers/svg.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _merge = require(\"../help/merge.js\");\n\nvar _merge2 = _interopRequireDefault(_merge);\n\nvar _shared = require(\"./shared.js\");\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar svgns = \"http://www.w3.org/2000/svg\";\n\nvar SVGRenderer = function () {\n\tfunction SVGRenderer(svg, encodings, options) {\n\t\t_classCallCheck(this, SVGRenderer);\n\n\t\tthis.svg = svg;\n\t\tthis.encodings = encodings;\n\t\tthis.options = options;\n\t\tthis.document = options.xmlDocument || document;\n\t}\n\n\t_createClass(SVGRenderer, [{\n\t\tkey: \"render\",\n\t\tvalue: function render() {\n\t\t\tvar currentX = this.options.marginLeft;\n\n\t\t\tthis.prepareSVG();\n\t\t\tfor (var i = 0; i < this.encodings.length; i++) {\n\t\t\t\tvar encoding = this.encodings[i];\n\t\t\t\tvar encodingOptions = (0, _merge2.default)(this.options, encoding.options);\n\n\t\t\t\tvar group = this.createGroup(currentX, encodingOptions.marginTop, this.svg);\n\n\t\t\t\tthis.setGroupOptions(group, encodingOptions);\n\n\t\t\t\tthis.drawSvgBarcode(group, encodingOptions, encoding);\n\t\t\t\tthis.drawSVGText(group, encodingOptions, encoding);\n\n\t\t\t\tcurrentX += encoding.width;\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"prepareSVG\",\n\t\tvalue: function prepareSVG() {\n\t\t\t// Clear the SVG\n\t\t\twhile (this.svg.firstChild) {\n\t\t\t\tthis.svg.removeChild(this.svg.firstChild);\n\t\t\t}\n\n\t\t\t(0, _shared.calculateEncodingAttributes)(this.encodings, this.options);\n\t\t\tvar totalWidth = (0, _shared.getTotalWidthOfEncodings)(this.encodings);\n\t\t\tvar maxHeight = (0, _shared.getMaximumHeightOfEncodings)(this.encodings);\n\n\t\t\tvar width = totalWidth + this.options.marginLeft + this.options.marginRight;\n\t\t\tthis.setSvgAttributes(width, maxHeight);\n\n\t\t\tif (this.options.background) {\n\t\t\t\tthis.drawRect(0, 0, width, maxHeight, this.svg).setAttribute(\"style\", \"fill:\" + this.options.background + \";\");\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"drawSvgBarcode\",\n\t\tvalue: function drawSvgBarcode(parent, options, encoding) {\n\t\t\tvar binary = encoding.data;\n\n\t\t\t// Creates the barcode out of the encoded binary\n\t\t\tvar yFrom;\n\t\t\tif (options.textPosition == \"top\") {\n\t\t\t\tyFrom = options.fontSize + options.textMargin;\n\t\t\t} else {\n\t\t\t\tyFrom = 0;\n\t\t\t}\n\n\t\t\tvar barWidth = 0;\n\t\t\tvar x = 0;\n\t\t\tfor (var b = 0; b < binary.length; b++) {\n\t\t\t\tx = b * options.width + encoding.barcodePadding;\n\n\t\t\t\tif (binary[b] === \"1\") {\n\t\t\t\t\tbarWidth++;\n\t\t\t\t} else if (barWidth > 0) {\n\t\t\t\t\tthis.drawRect(x - options.width * barWidth, yFrom, options.width * barWidth, options.height, parent);\n\t\t\t\t\tbarWidth = 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Last draw is needed since the barcode ends with 1\n\t\t\tif (barWidth > 0) {\n\t\t\t\tthis.drawRect(x - options.width * (barWidth - 1), yFrom, options.width * barWidth, options.height, parent);\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"drawSVGText\",\n\t\tvalue: function drawSVGText(parent, options, encoding) {\n\t\t\tvar textElem = this.document.createElementNS(svgns, 'text');\n\n\t\t\t// Draw the text if displayValue is set\n\t\t\tif (options.displayValue) {\n\t\t\t\tvar x, y;\n\n\t\t\t\ttextElem.setAttribute(\"style\", \"font:\" + options.fontOptions + \" \" + options.fontSize + \"px \" + options.font);\n\n\t\t\t\tif (options.textPosition == \"top\") {\n\t\t\t\t\ty = options.fontSize - options.textMargin;\n\t\t\t\t} else {\n\t\t\t\t\ty = options.height + options.textMargin + options.fontSize;\n\t\t\t\t}\n\n\t\t\t\t// Draw the text in the correct X depending on the textAlign option\n\t\t\t\tif (options.textAlign == \"left\" || encoding.barcodePadding > 0) {\n\t\t\t\t\tx = 0;\n\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"start\");\n\t\t\t\t} else if (options.textAlign == \"right\") {\n\t\t\t\t\tx = encoding.width - 1;\n\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"end\");\n\t\t\t\t}\n\t\t\t\t// In all other cases, center the text\n\t\t\t\telse {\n\t\t\t\t\t\tx = encoding.width / 2;\n\t\t\t\t\t\ttextElem.setAttribute(\"text-anchor\", \"middle\");\n\t\t\t\t\t}\n\n\t\t\t\ttextElem.setAttribute(\"x\", x);\n\t\t\t\ttextElem.setAttribute(\"y\", y);\n\n\t\t\t\ttextElem.appendChild(this.document.createTextNode(encoding.text));\n\n\t\t\t\tparent.appendChild(textElem);\n\t\t\t}\n\t\t}\n\t}, {\n\t\tkey: \"setSvgAttributes\",\n\t\tvalue: function setSvgAttributes(width, height) {\n\t\t\tvar svg = this.svg;\n\t\t\tsvg.setAttribute(\"width\", width + \"px\");\n\t\t\tsvg.setAttribute(\"height\", height + \"px\");\n\t\t\tsvg.setAttribute(\"x\", \"0px\");\n\t\t\tsvg.setAttribute(\"y\", \"0px\");\n\t\t\tsvg.setAttribute(\"viewBox\", \"0 0 \" + width + \" \" + height);\n\n\t\t\tsvg.setAttribute(\"xmlns\", svgns);\n\t\t\tsvg.setAttribute(\"version\", \"1.1\");\n\n\t\t\tsvg.setAttribute(\"style\", \"transform: translate(0,0)\");\n\t\t}\n\t}, {\n\t\tkey: \"createGroup\",\n\t\tvalue: function createGroup(x, y, parent) {\n\t\t\tvar group = this.document.createElementNS(svgns, 'g');\n\t\t\tgroup.setAttribute(\"transform\", \"translate(\" + x + \", \" + y + \")\");\n\n\t\t\tparent.appendChild(group);\n\n\t\t\treturn group;\n\t\t}\n\t}, {\n\t\tkey: \"setGroupOptions\",\n\t\tvalue: function setGroupOptions(group, options) {\n\t\t\tgroup.setAttribute(\"style\", \"fill:\" + options.lineColor + \";\");\n\t\t}\n\t}, {\n\t\tkey: \"drawRect\",\n\t\tvalue: function drawRect(x, y, width, height, parent) {\n\t\t\tvar rect = this.document.createElementNS(svgns, 'rect');\n\n\t\t\trect.setAttribute(\"x\", x);\n\t\t\trect.setAttribute(\"y\", y);\n\t\t\trect.setAttribute(\"width\", width);\n\t\t\trect.setAttribute(\"height\", height);\n\n\t\t\tparent.appendChild(rect);\n\n\t\t\treturn rect;\n\t\t}\n\t}]);\n\n\treturn SVGRenderer;\n}();\n\nexports.default = SVGRenderer;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,MAAM,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAExC,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,IAAII,OAAO,GAAGH,OAAO,CAAC,aAAa,CAAC;AAEpC,SAASE,sBAAsBA,CAACE,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEb,WAAW,EAAE;EAAE,IAAI,EAAEa,QAAQ,YAAYb,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIc,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,IAAIC,KAAK,GAAG,4BAA4B;AAExC,IAAIC,WAAW,GAAG,YAAY;EAC7B,SAASA,WAAWA,CAACC,GAAG,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC7CP,eAAe,CAAC,IAAI,EAAEI,WAAW,CAAC;IAElC,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,QAAQ,GAAGD,OAAO,CAACE,WAAW,IAAID,QAAQ;EAChD;EAEA/B,YAAY,CAAC2B,WAAW,EAAE,CAAC;IAC1BjB,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAASkC,MAAMA,CAAA,EAAG;MACxB,IAAIC,QAAQ,GAAG,IAAI,CAACJ,OAAO,CAACK,UAAU;MAEtC,IAAI,CAACC,UAAU,CAAC,CAAC;MACjB,KAAK,IAAIhC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyB,SAAS,CAACxB,MAAM,EAAED,CAAC,EAAE,EAAE;QAC/C,IAAIiC,QAAQ,GAAG,IAAI,CAACR,SAAS,CAACzB,CAAC,CAAC;QAChC,IAAIkC,eAAe,GAAG,CAAC,CAAC,EAAErB,OAAO,CAACK,OAAO,EAAE,IAAI,CAACQ,OAAO,EAAEO,QAAQ,CAACP,OAAO,CAAC;QAE1E,IAAIS,KAAK,GAAG,IAAI,CAACC,WAAW,CAACN,QAAQ,EAAEI,eAAe,CAACG,SAAS,EAAE,IAAI,CAACb,GAAG,CAAC;QAE3E,IAAI,CAACc,eAAe,CAACH,KAAK,EAAED,eAAe,CAAC;QAE5C,IAAI,CAACK,cAAc,CAACJ,KAAK,EAAED,eAAe,EAAED,QAAQ,CAAC;QACrD,IAAI,CAACO,WAAW,CAACL,KAAK,EAAED,eAAe,EAAED,QAAQ,CAAC;QAElDH,QAAQ,IAAIG,QAAQ,CAACQ,KAAK;MAC3B;IACD;EACD,CAAC,EAAE;IACFnC,GAAG,EAAE,YAAY;IACjBX,KAAK,EAAE,SAASqC,UAAUA,CAAA,EAAG;MAC5B;MACA,OAAO,IAAI,CAACR,GAAG,CAACkB,UAAU,EAAE;QAC3B,IAAI,CAAClB,GAAG,CAACmB,WAAW,CAAC,IAAI,CAACnB,GAAG,CAACkB,UAAU,CAAC;MAC1C;MAEA,CAAC,CAAC,EAAE3B,OAAO,CAAC6B,2BAA2B,EAAE,IAAI,CAACnB,SAAS,EAAE,IAAI,CAACC,OAAO,CAAC;MACtE,IAAImB,UAAU,GAAG,CAAC,CAAC,EAAE9B,OAAO,CAAC+B,wBAAwB,EAAE,IAAI,CAACrB,SAAS,CAAC;MACtE,IAAIsB,SAAS,GAAG,CAAC,CAAC,EAAEhC,OAAO,CAACiC,2BAA2B,EAAE,IAAI,CAACvB,SAAS,CAAC;MAExE,IAAIgB,KAAK,GAAGI,UAAU,GAAG,IAAI,CAACnB,OAAO,CAACK,UAAU,GAAG,IAAI,CAACL,OAAO,CAACuB,WAAW;MAC3E,IAAI,CAACC,gBAAgB,CAACT,KAAK,EAAEM,SAAS,CAAC;MAEvC,IAAI,IAAI,CAACrB,OAAO,CAACyB,UAAU,EAAE;QAC5B,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAEX,KAAK,EAAEM,SAAS,EAAE,IAAI,CAACvB,GAAG,CAAC,CAAC6B,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC3B,OAAO,CAACyB,UAAU,GAAG,GAAG,CAAC;MAC/G;IACD;EACD,CAAC,EAAE;IACF7C,GAAG,EAAE,gBAAgB;IACrBX,KAAK,EAAE,SAAS4C,cAAcA,CAACe,MAAM,EAAE5B,OAAO,EAAEO,QAAQ,EAAE;MACzD,IAAIsB,MAAM,GAAGtB,QAAQ,CAACuB,IAAI;;MAE1B;MACA,IAAIC,KAAK;MACT,IAAI/B,OAAO,CAACgC,YAAY,IAAI,KAAK,EAAE;QAClCD,KAAK,GAAG/B,OAAO,CAACiC,QAAQ,GAAGjC,OAAO,CAACkC,UAAU;MAC9C,CAAC,MAAM;QACNH,KAAK,GAAG,CAAC;MACV;MAEA,IAAII,QAAQ,GAAG,CAAC;MAChB,IAAIC,CAAC,GAAG,CAAC;MACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACtD,MAAM,EAAE8D,CAAC,EAAE,EAAE;QACvCD,CAAC,GAAGC,CAAC,GAAGrC,OAAO,CAACe,KAAK,GAAGR,QAAQ,CAAC+B,cAAc;QAE/C,IAAIT,MAAM,CAACQ,CAAC,CAAC,KAAK,GAAG,EAAE;UACtBF,QAAQ,EAAE;QACX,CAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;UACxB,IAAI,CAACT,QAAQ,CAACU,CAAC,GAAGpC,OAAO,CAACe,KAAK,GAAGoB,QAAQ,EAAEJ,KAAK,EAAE/B,OAAO,CAACe,KAAK,GAAGoB,QAAQ,EAAEnC,OAAO,CAACuC,MAAM,EAAEX,MAAM,CAAC;UACpGO,QAAQ,GAAG,CAAC;QACb;MACD;;MAEA;MACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;QACjB,IAAI,CAACT,QAAQ,CAACU,CAAC,GAAGpC,OAAO,CAACe,KAAK,IAAIoB,QAAQ,GAAG,CAAC,CAAC,EAAEJ,KAAK,EAAE/B,OAAO,CAACe,KAAK,GAAGoB,QAAQ,EAAEnC,OAAO,CAACuC,MAAM,EAAEX,MAAM,CAAC;MAC3G;IACD;EACD,CAAC,EAAE;IACFhD,GAAG,EAAE,aAAa;IAClBX,KAAK,EAAE,SAAS6C,WAAWA,CAACc,MAAM,EAAE5B,OAAO,EAAEO,QAAQ,EAAE;MACtD,IAAIiC,QAAQ,GAAG,IAAI,CAACvC,QAAQ,CAACwC,eAAe,CAAC7C,KAAK,EAAE,MAAM,CAAC;;MAE3D;MACA,IAAII,OAAO,CAAC0C,YAAY,EAAE;QACzB,IAAIN,CAAC,EAAEO,CAAC;QAERH,QAAQ,CAACb,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG3B,OAAO,CAAC4C,WAAW,GAAG,GAAG,GAAG5C,OAAO,CAACiC,QAAQ,GAAG,KAAK,GAAGjC,OAAO,CAAC6C,IAAI,CAAC;QAE7G,IAAI7C,OAAO,CAACgC,YAAY,IAAI,KAAK,EAAE;UAClCW,CAAC,GAAG3C,OAAO,CAACiC,QAAQ,GAAGjC,OAAO,CAACkC,UAAU;QAC1C,CAAC,MAAM;UACNS,CAAC,GAAG3C,OAAO,CAACuC,MAAM,GAAGvC,OAAO,CAACkC,UAAU,GAAGlC,OAAO,CAACiC,QAAQ;QAC3D;;QAEA;QACA,IAAIjC,OAAO,CAAC8C,SAAS,IAAI,MAAM,IAAIvC,QAAQ,CAAC+B,cAAc,GAAG,CAAC,EAAE;UAC/DF,CAAC,GAAG,CAAC;UACLI,QAAQ,CAACb,YAAY,CAAC,aAAa,EAAE,OAAO,CAAC;QAC9C,CAAC,MAAM,IAAI3B,OAAO,CAAC8C,SAAS,IAAI,OAAO,EAAE;UACxCV,CAAC,GAAG7B,QAAQ,CAACQ,KAAK,GAAG,CAAC;UACtByB,QAAQ,CAACb,YAAY,CAAC,aAAa,EAAE,KAAK,CAAC;QAC5C;QACA;QAAA,KACK;UACHS,CAAC,GAAG7B,QAAQ,CAACQ,KAAK,GAAG,CAAC;UACtByB,QAAQ,CAACb,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC;QAC/C;QAEDa,QAAQ,CAACb,YAAY,CAAC,GAAG,EAAES,CAAC,CAAC;QAC7BI,QAAQ,CAACb,YAAY,CAAC,GAAG,EAAEgB,CAAC,CAAC;QAE7BH,QAAQ,CAACO,WAAW,CAAC,IAAI,CAAC9C,QAAQ,CAAC+C,cAAc,CAACzC,QAAQ,CAAC0C,IAAI,CAAC,CAAC;QAEjErB,MAAM,CAACmB,WAAW,CAACP,QAAQ,CAAC;MAC7B;IACD;EACD,CAAC,EAAE;IACF5D,GAAG,EAAE,kBAAkB;IACvBX,KAAK,EAAE,SAASuD,gBAAgBA,CAACT,KAAK,EAAEwB,MAAM,EAAE;MAC/C,IAAIzC,GAAG,GAAG,IAAI,CAACA,GAAG;MAClBA,GAAG,CAAC6B,YAAY,CAAC,OAAO,EAAEZ,KAAK,GAAG,IAAI,CAAC;MACvCjB,GAAG,CAAC6B,YAAY,CAAC,QAAQ,EAAEY,MAAM,GAAG,IAAI,CAAC;MACzCzC,GAAG,CAAC6B,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;MAC5B7B,GAAG,CAAC6B,YAAY,CAAC,GAAG,EAAE,KAAK,CAAC;MAC5B7B,GAAG,CAAC6B,YAAY,CAAC,SAAS,EAAE,MAAM,GAAGZ,KAAK,GAAG,GAAG,GAAGwB,MAAM,CAAC;MAE1DzC,GAAG,CAAC6B,YAAY,CAAC,OAAO,EAAE/B,KAAK,CAAC;MAChCE,GAAG,CAAC6B,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC;MAElC7B,GAAG,CAAC6B,YAAY,CAAC,OAAO,EAAE,2BAA2B,CAAC;IACvD;EACD,CAAC,EAAE;IACF/C,GAAG,EAAE,aAAa;IAClBX,KAAK,EAAE,SAASyC,WAAWA,CAAC0B,CAAC,EAAEO,CAAC,EAAEf,MAAM,EAAE;MACzC,IAAInB,KAAK,GAAG,IAAI,CAACR,QAAQ,CAACwC,eAAe,CAAC7C,KAAK,EAAE,GAAG,CAAC;MACrDa,KAAK,CAACkB,YAAY,CAAC,WAAW,EAAE,YAAY,GAAGS,CAAC,GAAG,IAAI,GAAGO,CAAC,GAAG,GAAG,CAAC;MAElEf,MAAM,CAACmB,WAAW,CAACtC,KAAK,CAAC;MAEzB,OAAOA,KAAK;IACb;EACD,CAAC,EAAE;IACF7B,GAAG,EAAE,iBAAiB;IACtBX,KAAK,EAAE,SAAS2C,eAAeA,CAACH,KAAK,EAAET,OAAO,EAAE;MAC/CS,KAAK,CAACkB,YAAY,CAAC,OAAO,EAAE,OAAO,GAAG3B,OAAO,CAACkD,SAAS,GAAG,GAAG,CAAC;IAC/D;EACD,CAAC,EAAE;IACFtE,GAAG,EAAE,UAAU;IACfX,KAAK,EAAE,SAASyD,QAAQA,CAACU,CAAC,EAAEO,CAAC,EAAE5B,KAAK,EAAEwB,MAAM,EAAEX,MAAM,EAAE;MACrD,IAAIuB,IAAI,GAAG,IAAI,CAAClD,QAAQ,CAACwC,eAAe,CAAC7C,KAAK,EAAE,MAAM,CAAC;MAEvDuD,IAAI,CAACxB,YAAY,CAAC,GAAG,EAAES,CAAC,CAAC;MACzBe,IAAI,CAACxB,YAAY,CAAC,GAAG,EAAEgB,CAAC,CAAC;MACzBQ,IAAI,CAACxB,YAAY,CAAC,OAAO,EAAEZ,KAAK,CAAC;MACjCoC,IAAI,CAACxB,YAAY,CAAC,QAAQ,EAAEY,MAAM,CAAC;MAEnCX,MAAM,CAACmB,WAAW,CAACI,IAAI,CAAC;MAExB,OAAOA,IAAI;IACZ;EACD,CAAC,CAAC,CAAC;EAEH,OAAOtD,WAAW;AACnB,CAAC,CAAC,CAAC;AAEH7B,OAAO,CAACwB,OAAO,GAAGK,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}