{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n/**\n * <AUTHOR> Grau\n */\nvar Codeword = /** @class */function () {\n  function Codeword(startX, endX, bucket, value) {\n    this.rowNumber = Codeword.BARCODE_ROW_UNKNOWN;\n    this.startX = Math.trunc(startX);\n    this.endX = Math.trunc(endX);\n    this.bucket = Math.trunc(bucket);\n    this.value = Math.trunc(value);\n  }\n  Codeword.prototype.hasValidRowNumber = function () {\n    return this.isValidRowNumber(this.rowNumber);\n  };\n  Codeword.prototype.isValidRowNumber = function (rowNumber) {\n    return rowNumber !== Codeword.BARCODE_ROW_UNKNOWN && this.bucket === rowNumber % 3 * 3;\n  };\n  Codeword.prototype.setRowNumberAsRowIndicatorColumn = function () {\n    this.rowNumber = Math.trunc(Math.trunc(this.value / 30) * 3 + Math.trunc(this.bucket / 3));\n  };\n  Codeword.prototype.getWidth = function () {\n    return this.endX - this.startX;\n  };\n  Codeword.prototype.getStartX = function () {\n    return this.startX;\n  };\n  Codeword.prototype.getEndX = function () {\n    return this.endX;\n  };\n  Codeword.prototype.getBucket = function () {\n    return this.bucket;\n  };\n  Codeword.prototype.getValue = function () {\n    return this.value;\n  };\n  Codeword.prototype.getRowNumber = function () {\n    return this.rowNumber;\n  };\n  Codeword.prototype.setRowNumber = function (rowNumber) {\n    this.rowNumber = rowNumber;\n  };\n  //   @Override\n  Codeword.prototype.toString = function () {\n    return this.rowNumber + '|' + this.value;\n  };\n  Codeword.BARCODE_ROW_UNKNOWN = -1;\n  return Codeword;\n}();\nexport default Codeword;", "map": {"version": 3, "names": ["Codeword", "startX", "endX", "bucket", "value", "rowNumber", "BARCODE_ROW_UNKNOWN", "Math", "trunc", "prototype", "hasValidRowNumber", "isValidRowNumber", "setRowNumberAsRowIndicatorColumn", "getWidth", "getStartX", "getEndX", "getBucket", "getValue", "getRowNumber", "setRowNumber", "toString"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/Codeword.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n/**\n * <AUTHOR> Grau\n */\nvar Codeword = /** @class */ (function () {\n    function Codeword(startX, endX, bucket, value) {\n        this.rowNumber = Codeword.BARCODE_ROW_UNKNOWN;\n        this.startX = Math.trunc(startX);\n        this.endX = Math.trunc(endX);\n        this.bucket = Math.trunc(bucket);\n        this.value = Math.trunc(value);\n    }\n    Codeword.prototype.hasValidRowNumber = function () {\n        return this.isValidRowNumber(this.rowNumber);\n    };\n    Codeword.prototype.isValidRowNumber = function (rowNumber) {\n        return rowNumber !== Codeword.BARCODE_ROW_UNKNOWN && this.bucket === (rowNumber % 3) * 3;\n    };\n    Codeword.prototype.setRowNumberAsRowIndicatorColumn = function () {\n        this.rowNumber = Math.trunc((Math.trunc(this.value / 30)) * 3 + Math.trunc(this.bucket / 3));\n    };\n    Codeword.prototype.getWidth = function () {\n        return this.endX - this.startX;\n    };\n    Codeword.prototype.getStartX = function () {\n        return this.startX;\n    };\n    Codeword.prototype.getEndX = function () {\n        return this.endX;\n    };\n    Codeword.prototype.getBucket = function () {\n        return this.bucket;\n    };\n    Codeword.prototype.getValue = function () {\n        return this.value;\n    };\n    Codeword.prototype.getRowNumber = function () {\n        return this.rowNumber;\n    };\n    Codeword.prototype.setRowNumber = function (rowNumber) {\n        this.rowNumber = rowNumber;\n    };\n    //   @Override\n    Codeword.prototype.toString = function () {\n        return this.rowNumber + '|' + this.value;\n    };\n    Codeword.BARCODE_ROW_UNKNOWN = -1;\n    return Codeword;\n}());\nexport default Codeword;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAC3C,IAAI,CAACC,SAAS,GAAGL,QAAQ,CAACM,mBAAmB;IAC7C,IAAI,CAACL,MAAM,GAAGM,IAAI,CAACC,KAAK,CAACP,MAAM,CAAC;IAChC,IAAI,CAACC,IAAI,GAAGK,IAAI,CAACC,KAAK,CAACN,IAAI,CAAC;IAC5B,IAAI,CAACC,MAAM,GAAGI,IAAI,CAACC,KAAK,CAACL,MAAM,CAAC;IAChC,IAAI,CAACC,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC;EAClC;EACAJ,QAAQ,CAACS,SAAS,CAACC,iBAAiB,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACN,SAAS,CAAC;EAChD,CAAC;EACDL,QAAQ,CAACS,SAAS,CAACE,gBAAgB,GAAG,UAAUN,SAAS,EAAE;IACvD,OAAOA,SAAS,KAAKL,QAAQ,CAACM,mBAAmB,IAAI,IAAI,CAACH,MAAM,KAAME,SAAS,GAAG,CAAC,GAAI,CAAC;EAC5F,CAAC;EACDL,QAAQ,CAACS,SAAS,CAACG,gCAAgC,GAAG,YAAY;IAC9D,IAAI,CAACP,SAAS,GAAGE,IAAI,CAACC,KAAK,CAAED,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,KAAK,GAAG,EAAE,CAAC,GAAI,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,MAAM,GAAG,CAAC,CAAC,CAAC;EAChG,CAAC;EACDH,QAAQ,CAACS,SAAS,CAACI,QAAQ,GAAG,YAAY;IACtC,OAAO,IAAI,CAACX,IAAI,GAAG,IAAI,CAACD,MAAM;EAClC,CAAC;EACDD,QAAQ,CAACS,SAAS,CAACK,SAAS,GAAG,YAAY;IACvC,OAAO,IAAI,CAACb,MAAM;EACtB,CAAC;EACDD,QAAQ,CAACS,SAAS,CAACM,OAAO,GAAG,YAAY;IACrC,OAAO,IAAI,CAACb,IAAI;EACpB,CAAC;EACDF,QAAQ,CAACS,SAAS,CAACO,SAAS,GAAG,YAAY;IACvC,OAAO,IAAI,CAACb,MAAM;EACtB,CAAC;EACDH,QAAQ,CAACS,SAAS,CAACQ,QAAQ,GAAG,YAAY;IACtC,OAAO,IAAI,CAACb,KAAK;EACrB,CAAC;EACDJ,QAAQ,CAACS,SAAS,CAACS,YAAY,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACb,SAAS;EACzB,CAAC;EACDL,QAAQ,CAACS,SAAS,CAACU,YAAY,GAAG,UAAUd,SAAS,EAAE;IACnD,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B,CAAC;EACD;EACAL,QAAQ,CAACS,SAAS,CAACW,QAAQ,GAAG,YAAY;IACtC,OAAO,IAAI,CAACf,SAAS,GAAG,GAAG,GAAG,IAAI,CAACD,KAAK;EAC5C,CAAC;EACDJ,QAAQ,CAACM,mBAAmB,GAAG,CAAC,CAAC;EACjC,OAAON,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}