{"ast": null, "code": "/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.ChecksumException;\nimport ChecksumException from '../../ChecksumException';\n// import com.google.zxing.FormatException;\nimport FormatException from '../../FormatException';\n// import com.google.zxing.NotFoundException;\nimport NotFoundException from '../../NotFoundException';\n// import com.google.zxing.common.detector.MathUtils;\nimport MathUtils from '../../common/detector/MathUtils';\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\n// import com.google.zxing.pdf417.decoder.ec.ErrorCorrection;\nimport ErrorCorrection from './ec/ErrorCorrection';\n// local\nimport BoundingBox from './BoundingBox';\nimport DetectionResultRowIndicatorColumn from './DetectionResultRowIndicatorColumn';\nimport DetectionResult from './DetectionResult';\nimport DetectionResultColumn from './DetectionResultColumn';\nimport Codeword from './Codeword';\nimport BarcodeValue from './BarcodeValue';\nimport PDF417CodewordDecoder from './PDF417CodewordDecoder';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\n// utils\nimport Formatter from '../../util/Formatter';\n// import java.util.ArrayList;\n// import java.util.Collection;\n// import java.util.Formatter;\n// import java.util.List;\n/**\n * <AUTHOR> Grau\n */\nvar PDF417ScanningDecoder = /** @class */function () {\n  function PDF417ScanningDecoder() {}\n  /**\n   * @TODO don't pass in minCodewordWidth and maxCodewordWidth, pass in barcode columns for start and stop pattern\n   *\n   * columns. That way width can be deducted from the pattern column.\n   * This approach also allows to detect more details about the barcode, e.g. if a bar type (white or black) is wider\n   * than it should be. This can happen if the scanner used a bad blackpoint.\n   *\n   * @param BitMatrix\n   * @param image\n   * @param ResultPoint\n   * @param imageTopLeft\n   * @param ResultPoint\n   * @param imageBottomLeft\n   * @param ResultPoint\n   * @param imageTopRight\n   * @param ResultPoint\n   * @param imageBottomRight\n   * @param int\n   * @param minCodewordWidth\n   * @param int\n   * @param maxCodewordWidth\n   *\n   * @throws NotFoundException\n   * @throws FormatException\n   * @throws ChecksumException\n   */\n  PDF417ScanningDecoder.decode = function (image, imageTopLeft, imageBottomLeft, imageTopRight, imageBottomRight, minCodewordWidth, maxCodewordWidth) {\n    var boundingBox = new BoundingBox(image, imageTopLeft, imageBottomLeft, imageTopRight, imageBottomRight);\n    var leftRowIndicatorColumn = null;\n    var rightRowIndicatorColumn = null;\n    var detectionResult;\n    for (var firstPass /*boolean*/ = true;; firstPass = false) {\n      if (imageTopLeft != null) {\n        leftRowIndicatorColumn = PDF417ScanningDecoder.getRowIndicatorColumn(image, boundingBox, imageTopLeft, true, minCodewordWidth, maxCodewordWidth);\n      }\n      if (imageTopRight != null) {\n        rightRowIndicatorColumn = PDF417ScanningDecoder.getRowIndicatorColumn(image, boundingBox, imageTopRight, false, minCodewordWidth, maxCodewordWidth);\n      }\n      detectionResult = PDF417ScanningDecoder.merge(leftRowIndicatorColumn, rightRowIndicatorColumn);\n      if (detectionResult == null) {\n        throw NotFoundException.getNotFoundInstance();\n      }\n      var resultBox = detectionResult.getBoundingBox();\n      if (firstPass && resultBox != null && (resultBox.getMinY() < boundingBox.getMinY() || resultBox.getMaxY() > boundingBox.getMaxY())) {\n        boundingBox = resultBox;\n      } else {\n        break;\n      }\n    }\n    detectionResult.setBoundingBox(boundingBox);\n    var maxBarcodeColumn = detectionResult.getBarcodeColumnCount() + 1;\n    detectionResult.setDetectionResultColumn(0, leftRowIndicatorColumn);\n    detectionResult.setDetectionResultColumn(maxBarcodeColumn, rightRowIndicatorColumn);\n    var leftToRight = leftRowIndicatorColumn != null;\n    for (var barcodeColumnCount /*int*/ = 1; barcodeColumnCount <= maxBarcodeColumn; barcodeColumnCount++) {\n      var barcodeColumn = leftToRight ? barcodeColumnCount : maxBarcodeColumn - barcodeColumnCount;\n      if (detectionResult.getDetectionResultColumn(barcodeColumn) !== /* null */undefined) {\n        // This will be the case for the opposite row indicator column, which doesn't need to be decoded again.\n        continue;\n      }\n      var detectionResultColumn = void 0;\n      if (barcodeColumn === 0 || barcodeColumn === maxBarcodeColumn) {\n        detectionResultColumn = new DetectionResultRowIndicatorColumn(boundingBox, barcodeColumn === 0);\n      } else {\n        detectionResultColumn = new DetectionResultColumn(boundingBox);\n      }\n      detectionResult.setDetectionResultColumn(barcodeColumn, detectionResultColumn);\n      var startColumn = -1;\n      var previousStartColumn = startColumn;\n      // TODO start at a row for which we know the start position, then detect upwards and downwards from there.\n      for (var imageRow /*int*/ = boundingBox.getMinY(); imageRow <= boundingBox.getMaxY(); imageRow++) {\n        startColumn = PDF417ScanningDecoder.getStartColumn(detectionResult, barcodeColumn, imageRow, leftToRight);\n        if (startColumn < 0 || startColumn > boundingBox.getMaxX()) {\n          if (previousStartColumn === -1) {\n            continue;\n          }\n          startColumn = previousStartColumn;\n        }\n        var codeword = PDF417ScanningDecoder.detectCodeword(image, boundingBox.getMinX(), boundingBox.getMaxX(), leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth);\n        if (codeword != null) {\n          detectionResultColumn.setCodeword(imageRow, codeword);\n          previousStartColumn = startColumn;\n          minCodewordWidth = Math.min(minCodewordWidth, codeword.getWidth());\n          maxCodewordWidth = Math.max(maxCodewordWidth, codeword.getWidth());\n        }\n      }\n    }\n    return PDF417ScanningDecoder.createDecoderResult(detectionResult);\n  };\n  /**\n   *\n   * @param leftRowIndicatorColumn\n   * @param rightRowIndicatorColumn\n   *\n   * @throws NotFoundException\n   */\n  PDF417ScanningDecoder.merge = function (leftRowIndicatorColumn, rightRowIndicatorColumn) {\n    if (leftRowIndicatorColumn == null && rightRowIndicatorColumn == null) {\n      return null;\n    }\n    var barcodeMetadata = PDF417ScanningDecoder.getBarcodeMetadata(leftRowIndicatorColumn, rightRowIndicatorColumn);\n    if (barcodeMetadata == null) {\n      return null;\n    }\n    var boundingBox = BoundingBox.merge(PDF417ScanningDecoder.adjustBoundingBox(leftRowIndicatorColumn), PDF417ScanningDecoder.adjustBoundingBox(rightRowIndicatorColumn));\n    return new DetectionResult(barcodeMetadata, boundingBox);\n  };\n  /**\n   *\n   * @param rowIndicatorColumn\n   *\n   * @throws NotFoundException\n   */\n  PDF417ScanningDecoder.adjustBoundingBox = function (rowIndicatorColumn) {\n    var e_1, _a;\n    if (rowIndicatorColumn == null) {\n      return null;\n    }\n    var rowHeights = rowIndicatorColumn.getRowHeights();\n    if (rowHeights == null) {\n      return null;\n    }\n    var maxRowHeight = PDF417ScanningDecoder.getMax(rowHeights);\n    var missingStartRows = 0;\n    try {\n      for (var rowHeights_1 = __values(rowHeights), rowHeights_1_1 = rowHeights_1.next(); !rowHeights_1_1.done; rowHeights_1_1 = rowHeights_1.next()) {\n        var rowHeight = rowHeights_1_1.value /*int*/;\n        missingStartRows += maxRowHeight - rowHeight;\n        if (rowHeight > 0) {\n          break;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (rowHeights_1_1 && !rowHeights_1_1.done && (_a = rowHeights_1.return)) _a.call(rowHeights_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    var codewords = rowIndicatorColumn.getCodewords();\n    for (var row /*int*/ = 0; missingStartRows > 0 && codewords[row] == null; row++) {\n      missingStartRows--;\n    }\n    var missingEndRows = 0;\n    for (var row /*int*/ = rowHeights.length - 1; row >= 0; row--) {\n      missingEndRows += maxRowHeight - rowHeights[row];\n      if (rowHeights[row] > 0) {\n        break;\n      }\n    }\n    for (var row /*int*/ = codewords.length - 1; missingEndRows > 0 && codewords[row] == null; row--) {\n      missingEndRows--;\n    }\n    return rowIndicatorColumn.getBoundingBox().addMissingRows(missingStartRows, missingEndRows, rowIndicatorColumn.isLeft());\n  };\n  PDF417ScanningDecoder.getMax = function (values) {\n    var e_2, _a;\n    var maxValue = -1;\n    try {\n      for (var values_1 = __values(values), values_1_1 = values_1.next(); !values_1_1.done; values_1_1 = values_1.next()) {\n        var value = values_1_1.value /*int*/;\n        maxValue = Math.max(maxValue, value);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (values_1_1 && !values_1_1.done && (_a = values_1.return)) _a.call(values_1);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return maxValue;\n  };\n  PDF417ScanningDecoder.getBarcodeMetadata = function (leftRowIndicatorColumn, rightRowIndicatorColumn) {\n    var leftBarcodeMetadata;\n    if (leftRowIndicatorColumn == null || (leftBarcodeMetadata = leftRowIndicatorColumn.getBarcodeMetadata()) == null) {\n      return rightRowIndicatorColumn == null ? null : rightRowIndicatorColumn.getBarcodeMetadata();\n    }\n    var rightBarcodeMetadata;\n    if (rightRowIndicatorColumn == null || (rightBarcodeMetadata = rightRowIndicatorColumn.getBarcodeMetadata()) == null) {\n      return leftBarcodeMetadata;\n    }\n    if (leftBarcodeMetadata.getColumnCount() !== rightBarcodeMetadata.getColumnCount() && leftBarcodeMetadata.getErrorCorrectionLevel() !== rightBarcodeMetadata.getErrorCorrectionLevel() && leftBarcodeMetadata.getRowCount() !== rightBarcodeMetadata.getRowCount()) {\n      return null;\n    }\n    return leftBarcodeMetadata;\n  };\n  PDF417ScanningDecoder.getRowIndicatorColumn = function (image, boundingBox, startPoint, leftToRight, minCodewordWidth, maxCodewordWidth) {\n    var rowIndicatorColumn = new DetectionResultRowIndicatorColumn(boundingBox, leftToRight);\n    for (var i /*int*/ = 0; i < 2; i++) {\n      var increment = i === 0 ? 1 : -1;\n      var startColumn = Math.trunc(Math.trunc(startPoint.getX()));\n      for (var imageRow /*int*/ = Math.trunc(Math.trunc(startPoint.getY())); imageRow <= boundingBox.getMaxY() && imageRow >= boundingBox.getMinY(); imageRow += increment) {\n        var codeword = PDF417ScanningDecoder.detectCodeword(image, 0, image.getWidth(), leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth);\n        if (codeword != null) {\n          rowIndicatorColumn.setCodeword(imageRow, codeword);\n          if (leftToRight) {\n            startColumn = codeword.getStartX();\n          } else {\n            startColumn = codeword.getEndX();\n          }\n        }\n      }\n    }\n    return rowIndicatorColumn;\n  };\n  /**\n   *\n   * @param detectionResult\n   * @param BarcodeValue\n   * @param param2\n   * @param param3\n   * @param barcodeMatrix\n   *\n   * @throws NotFoundException\n   */\n  PDF417ScanningDecoder.adjustCodewordCount = function (detectionResult, barcodeMatrix) {\n    var barcodeMatrix01 = barcodeMatrix[0][1];\n    var numberOfCodewords = barcodeMatrix01.getValue();\n    var calculatedNumberOfCodewords = detectionResult.getBarcodeColumnCount() * detectionResult.getBarcodeRowCount() - PDF417ScanningDecoder.getNumberOfECCodeWords(detectionResult.getBarcodeECLevel());\n    if (numberOfCodewords.length === 0) {\n      if (calculatedNumberOfCodewords < 1 || calculatedNumberOfCodewords > PDF417Common.MAX_CODEWORDS_IN_BARCODE) {\n        throw NotFoundException.getNotFoundInstance();\n      }\n      barcodeMatrix01.setValue(calculatedNumberOfCodewords);\n    } else if (numberOfCodewords[0] !== calculatedNumberOfCodewords) {\n      // The calculated one is more reliable as it is derived from the row indicator columns\n      barcodeMatrix01.setValue(calculatedNumberOfCodewords);\n    }\n  };\n  /**\n   *\n   * @param detectionResult\n   *\n   * @throws FormatException\n   * @throws ChecksumException\n   * @throws NotFoundException\n   */\n  PDF417ScanningDecoder.createDecoderResult = function (detectionResult) {\n    var barcodeMatrix = PDF417ScanningDecoder.createBarcodeMatrix(detectionResult);\n    PDF417ScanningDecoder.adjustCodewordCount(detectionResult, barcodeMatrix);\n    var erasures /*Collection<Integer>*/ = new Array();\n    var codewords = new Int32Array(detectionResult.getBarcodeRowCount() * detectionResult.getBarcodeColumnCount());\n    var ambiguousIndexValuesList = /*List<int[]>*/[];\n    var ambiguousIndexesList = /*Collection<Integer>*/new Array();\n    for (var row /*int*/ = 0; row < detectionResult.getBarcodeRowCount(); row++) {\n      for (var column /*int*/ = 0; column < detectionResult.getBarcodeColumnCount(); column++) {\n        var values = barcodeMatrix[row][column + 1].getValue();\n        var codewordIndex = row * detectionResult.getBarcodeColumnCount() + column;\n        if (values.length === 0) {\n          erasures.push(codewordIndex);\n        } else if (values.length === 1) {\n          codewords[codewordIndex] = values[0];\n        } else {\n          ambiguousIndexesList.push(codewordIndex);\n          ambiguousIndexValuesList.push(values);\n        }\n      }\n    }\n    var ambiguousIndexValues = new Array(ambiguousIndexValuesList.length);\n    for (var i /*int*/ = 0; i < ambiguousIndexValues.length; i++) {\n      ambiguousIndexValues[i] = ambiguousIndexValuesList[i];\n    }\n    return PDF417ScanningDecoder.createDecoderResultFromAmbiguousValues(detectionResult.getBarcodeECLevel(), codewords, PDF417Common.toIntArray(erasures), PDF417Common.toIntArray(ambiguousIndexesList), ambiguousIndexValues);\n  };\n  /**\n   * This method deals with the fact, that the decoding process doesn't always yield a single most likely value. The\n   * current error correction implementation doesn't deal with erasures very well, so it's better to provide a value\n   * for these ambiguous codewords instead of treating it as an erasure. The problem is that we don't know which of\n   * the ambiguous values to choose. We try decode using the first value, and if that fails, we use another of the\n   * ambiguous values and try to decode again. This usually only happens on very hard to read and decode barcodes,\n   * so decoding the normal barcodes is not affected by this.\n   *\n   * @param erasureArray contains the indexes of erasures\n   * @param ambiguousIndexes array with the indexes that have more than one most likely value\n   * @param ambiguousIndexValues two dimensional array that contains the ambiguous values. The first dimension must\n   * be the same length as the ambiguousIndexes array\n   *\n   * @throws FormatException\n   * @throws ChecksumException\n   */\n  PDF417ScanningDecoder.createDecoderResultFromAmbiguousValues = function (ecLevel, codewords, erasureArray, ambiguousIndexes, ambiguousIndexValues) {\n    var ambiguousIndexCount = new Int32Array(ambiguousIndexes.length);\n    var tries = 100;\n    while (tries-- > 0) {\n      for (var i /*int*/ = 0; i < ambiguousIndexCount.length; i++) {\n        codewords[ambiguousIndexes[i]] = ambiguousIndexValues[i][ambiguousIndexCount[i]];\n      }\n      try {\n        return PDF417ScanningDecoder.decodeCodewords(codewords, ecLevel, erasureArray);\n      } catch (err) {\n        var ignored = err instanceof ChecksumException;\n        if (!ignored) {\n          throw err;\n        }\n      }\n      if (ambiguousIndexCount.length === 0) {\n        throw ChecksumException.getChecksumInstance();\n      }\n      for (var i /*int*/ = 0; i < ambiguousIndexCount.length; i++) {\n        if (ambiguousIndexCount[i] < ambiguousIndexValues[i].length - 1) {\n          ambiguousIndexCount[i]++;\n          break;\n        } else {\n          ambiguousIndexCount[i] = 0;\n          if (i === ambiguousIndexCount.length - 1) {\n            throw ChecksumException.getChecksumInstance();\n          }\n        }\n      }\n    }\n    throw ChecksumException.getChecksumInstance();\n  };\n  PDF417ScanningDecoder.createBarcodeMatrix = function (detectionResult) {\n    var e_3, _a, e_4, _b;\n    // let barcodeMatrix: BarcodeValue[][] =\n    // new BarcodeValue[detectionResult.getBarcodeRowCount()][detectionResult.getBarcodeColumnCount() + 2];\n    var barcodeMatrix = Array.from({\n      length: detectionResult.getBarcodeRowCount()\n    }, function () {\n      return new Array(detectionResult.getBarcodeColumnCount() + 2);\n    });\n    for (var row /*int*/ = 0; row < barcodeMatrix.length; row++) {\n      for (var column_1 /*int*/ = 0; column_1 < barcodeMatrix[row].length; column_1++) {\n        barcodeMatrix[row][column_1] = new BarcodeValue();\n      }\n    }\n    var column = 0;\n    try {\n      for (var _c = __values(detectionResult.getDetectionResultColumns()), _d = _c.next(); !_d.done; _d = _c.next()) {\n        var detectionResultColumn = _d.value /*DetectionResultColumn*/;\n        if (detectionResultColumn != null) {\n          try {\n            for (var _e = (e_4 = void 0, __values(detectionResultColumn.getCodewords())), _f = _e.next(); !_f.done; _f = _e.next()) {\n              var codeword = _f.value /*Codeword*/;\n              if (codeword != null) {\n                var rowNumber = codeword.getRowNumber();\n                if (rowNumber >= 0) {\n                  if (rowNumber >= barcodeMatrix.length) {\n                    // We have more rows than the barcode metadata allows for, ignore them.\n                    continue;\n                  }\n                  barcodeMatrix[rowNumber][column].setValue(codeword.getValue());\n                }\n              }\n            }\n          } catch (e_4_1) {\n            e_4 = {\n              error: e_4_1\n            };\n          } finally {\n            try {\n              if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            } finally {\n              if (e_4) throw e_4.error;\n            }\n          }\n        }\n        column++;\n      }\n    } catch (e_3_1) {\n      e_3 = {\n        error: e_3_1\n      };\n    } finally {\n      try {\n        if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n      } finally {\n        if (e_3) throw e_3.error;\n      }\n    }\n    return barcodeMatrix;\n  };\n  PDF417ScanningDecoder.isValidBarcodeColumn = function (detectionResult, barcodeColumn) {\n    return barcodeColumn >= 0 && barcodeColumn <= detectionResult.getBarcodeColumnCount() + 1;\n  };\n  PDF417ScanningDecoder.getStartColumn = function (detectionResult, barcodeColumn, imageRow, leftToRight) {\n    var e_5, _a;\n    var offset = leftToRight ? 1 : -1;\n    var codeword = null;\n    if (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n      codeword = detectionResult.getDetectionResultColumn(barcodeColumn - offset).getCodeword(imageRow);\n    }\n    if (codeword != null) {\n      return leftToRight ? codeword.getEndX() : codeword.getStartX();\n    }\n    codeword = detectionResult.getDetectionResultColumn(barcodeColumn).getCodewordNearby(imageRow);\n    if (codeword != null) {\n      return leftToRight ? codeword.getStartX() : codeword.getEndX();\n    }\n    if (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n      codeword = detectionResult.getDetectionResultColumn(barcodeColumn - offset).getCodewordNearby(imageRow);\n    }\n    if (codeword != null) {\n      return leftToRight ? codeword.getEndX() : codeword.getStartX();\n    }\n    var skippedColumns = 0;\n    while (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n      barcodeColumn -= offset;\n      try {\n        for (var _b = (e_5 = void 0, __values(detectionResult.getDetectionResultColumn(barcodeColumn).getCodewords())), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var previousRowCodeword = _c.value /*Codeword*/;\n          if (previousRowCodeword != null) {\n            return (leftToRight ? previousRowCodeword.getEndX() : previousRowCodeword.getStartX()) + offset * skippedColumns * (previousRowCodeword.getEndX() - previousRowCodeword.getStartX());\n          }\n        }\n      } catch (e_5_1) {\n        e_5 = {\n          error: e_5_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        } finally {\n          if (e_5) throw e_5.error;\n        }\n      }\n      skippedColumns++;\n    }\n    return leftToRight ? detectionResult.getBoundingBox().getMinX() : detectionResult.getBoundingBox().getMaxX();\n  };\n  PDF417ScanningDecoder.detectCodeword = function (image, minColumn, maxColumn, leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth) {\n    startColumn = PDF417ScanningDecoder.adjustCodewordStartColumn(image, minColumn, maxColumn, leftToRight, startColumn, imageRow);\n    // we usually know fairly exact now how long a codeword is. We should provide minimum and maximum expected length\n    // and try to adjust the read pixels, e.g. remove single pixel errors or try to cut off exceeding pixels.\n    // min and maxCodewordWidth should not be used as they are calculated for the whole barcode an can be inaccurate\n    // for the current position\n    var moduleBitCount = PDF417ScanningDecoder.getModuleBitCount(image, minColumn, maxColumn, leftToRight, startColumn, imageRow);\n    if (moduleBitCount == null) {\n      return null;\n    }\n    var endColumn;\n    var codewordBitCount = MathUtils.sum(moduleBitCount);\n    if (leftToRight) {\n      endColumn = startColumn + codewordBitCount;\n    } else {\n      for (var i /*int*/ = 0; i < moduleBitCount.length / 2; i++) {\n        var tmpCount = moduleBitCount[i];\n        moduleBitCount[i] = moduleBitCount[moduleBitCount.length - 1 - i];\n        moduleBitCount[moduleBitCount.length - 1 - i] = tmpCount;\n      }\n      endColumn = startColumn;\n      startColumn = endColumn - codewordBitCount;\n    }\n    // TODO implement check for width and correction of black and white bars\n    // use start (and maybe stop pattern) to determine if black bars are wider than white bars. If so, adjust.\n    // should probably done only for codewords with a lot more than 17 bits.\n    // The following fixes 10-1.png, which has wide black bars and small white bars\n    //    for (let i /*int*/ = 0; i < moduleBitCount.length; i++) {\n    //      if (i % 2 === 0) {\n    //        moduleBitCount[i]--;\n    //      } else {\n    //        moduleBitCount[i]++;\n    //      }\n    //    }\n    // We could also use the width of surrounding codewords for more accurate results, but this seems\n    // sufficient for now\n    if (!PDF417ScanningDecoder.checkCodewordSkew(codewordBitCount, minCodewordWidth, maxCodewordWidth)) {\n      // We could try to use the startX and endX position of the codeword in the same column in the previous row,\n      // create the bit count from it and normalize it to 8. This would help with single pixel errors.\n      return null;\n    }\n    var decodedValue = PDF417CodewordDecoder.getDecodedValue(moduleBitCount);\n    var codeword = PDF417Common.getCodeword(decodedValue);\n    if (codeword === -1) {\n      return null;\n    }\n    return new Codeword(startColumn, endColumn, PDF417ScanningDecoder.getCodewordBucketNumber(decodedValue), codeword);\n  };\n  PDF417ScanningDecoder.getModuleBitCount = function (image, minColumn, maxColumn, leftToRight, startColumn, imageRow) {\n    var imageColumn = startColumn;\n    var moduleBitCount = new Int32Array(8);\n    var moduleNumber = 0;\n    var increment = leftToRight ? 1 : -1;\n    var previousPixelValue = leftToRight;\n    while ((leftToRight ? imageColumn < maxColumn : imageColumn >= minColumn) && moduleNumber < moduleBitCount.length) {\n      if (image.get(imageColumn, imageRow) === previousPixelValue) {\n        moduleBitCount[moduleNumber]++;\n        imageColumn += increment;\n      } else {\n        moduleNumber++;\n        previousPixelValue = !previousPixelValue;\n      }\n    }\n    if (moduleNumber === moduleBitCount.length || imageColumn === (leftToRight ? maxColumn : minColumn) && moduleNumber === moduleBitCount.length - 1) {\n      return moduleBitCount;\n    }\n    return null;\n  };\n  PDF417ScanningDecoder.getNumberOfECCodeWords = function (barcodeECLevel) {\n    return 2 << barcodeECLevel;\n  };\n  PDF417ScanningDecoder.adjustCodewordStartColumn = function (image, minColumn, maxColumn, leftToRight, codewordStartColumn, imageRow) {\n    var correctedStartColumn = codewordStartColumn;\n    var increment = leftToRight ? -1 : 1;\n    // there should be no black pixels before the start column. If there are, then we need to start earlier.\n    for (var i /*int*/ = 0; i < 2; i++) {\n      while ((leftToRight ? correctedStartColumn >= minColumn : correctedStartColumn < maxColumn) && leftToRight === image.get(correctedStartColumn, imageRow)) {\n        if (Math.abs(codewordStartColumn - correctedStartColumn) > PDF417ScanningDecoder.CODEWORD_SKEW_SIZE) {\n          return codewordStartColumn;\n        }\n        correctedStartColumn += increment;\n      }\n      increment = -increment;\n      leftToRight = !leftToRight;\n    }\n    return correctedStartColumn;\n  };\n  PDF417ScanningDecoder.checkCodewordSkew = function (codewordSize, minCodewordWidth, maxCodewordWidth) {\n    return minCodewordWidth - PDF417ScanningDecoder.CODEWORD_SKEW_SIZE <= codewordSize && codewordSize <= maxCodewordWidth + PDF417ScanningDecoder.CODEWORD_SKEW_SIZE;\n  };\n  /**\n   * @throws FormatException,\n   * @throws ChecksumException\n   */\n  PDF417ScanningDecoder.decodeCodewords = function (codewords, ecLevel, erasures) {\n    if (codewords.length === 0) {\n      throw FormatException.getFormatInstance();\n    }\n    var numECCodewords = 1 << ecLevel + 1;\n    var correctedErrorsCount = PDF417ScanningDecoder.correctErrors(codewords, erasures, numECCodewords);\n    PDF417ScanningDecoder.verifyCodewordCount(codewords, numECCodewords);\n    // Decode the codewords\n    var decoderResult = DecodedBitStreamParser.decode(codewords, '' + ecLevel);\n    decoderResult.setErrorsCorrected(correctedErrorsCount);\n    decoderResult.setErasures(erasures.length);\n    return decoderResult;\n  };\n  /**\n   * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n   * correct the errors in-place.</p>\n   *\n   * @param codewords   data and error correction codewords\n   * @param erasures positions of any known erasures\n   * @param numECCodewords number of error correction codewords that are available in codewords\n   * @throws ChecksumException if error correction fails\n   */\n  PDF417ScanningDecoder.correctErrors = function (codewords, erasures, numECCodewords) {\n    if (erasures != null && erasures.length > numECCodewords / 2 + PDF417ScanningDecoder.MAX_ERRORS || numECCodewords < 0 || numECCodewords > PDF417ScanningDecoder.MAX_EC_CODEWORDS) {\n      // Too many errors or EC Codewords is corrupted\n      throw ChecksumException.getChecksumInstance();\n    }\n    return PDF417ScanningDecoder.errorCorrection.decode(codewords, numECCodewords, erasures);\n  };\n  /**\n   * Verify that all is OK with the codeword array.\n   * @throws FormatException\n   */\n  PDF417ScanningDecoder.verifyCodewordCount = function (codewords, numECCodewords) {\n    if (codewords.length < 4) {\n      // Codeword array size should be at least 4 allowing for\n      // Count CW, At least one Data CW, Error Correction CW, Error Correction CW\n      throw FormatException.getFormatInstance();\n    }\n    // The first codeword, the Symbol Length Descriptor, shall always encode the total number of data\n    // codewords in the symbol, including the Symbol Length Descriptor itself, data codewords and pad\n    // codewords, but excluding the number of error correction codewords.\n    var numberOfCodewords = codewords[0];\n    if (numberOfCodewords > codewords.length) {\n      throw FormatException.getFormatInstance();\n    }\n    if (numberOfCodewords === 0) {\n      // Reset to the length of the array - 8 (Allow for at least level 3 Error Correction (8 Error Codewords)\n      if (numECCodewords < codewords.length) {\n        codewords[0] = codewords.length - numECCodewords;\n      } else {\n        throw FormatException.getFormatInstance();\n      }\n    }\n  };\n  PDF417ScanningDecoder.getBitCountForCodeword = function (codeword) {\n    var result = new Int32Array(8);\n    var previousValue = 0;\n    var i = result.length - 1;\n    while (true) {\n      if ((codeword & 0x1) !== previousValue) {\n        previousValue = codeword & 0x1;\n        i--;\n        if (i < 0) {\n          break;\n        }\n      }\n      result[i]++;\n      codeword >>= 1;\n    }\n    return result;\n  };\n  PDF417ScanningDecoder.getCodewordBucketNumber = function (codeword) {\n    if (codeword instanceof Int32Array) {\n      return this.getCodewordBucketNumber_Int32Array(codeword);\n    }\n    return this.getCodewordBucketNumber_number(codeword);\n  };\n  PDF417ScanningDecoder.getCodewordBucketNumber_number = function (codeword) {\n    return PDF417ScanningDecoder.getCodewordBucketNumber(PDF417ScanningDecoder.getBitCountForCodeword(codeword));\n  };\n  PDF417ScanningDecoder.getCodewordBucketNumber_Int32Array = function (moduleBitCount) {\n    return (moduleBitCount[0] - moduleBitCount[2] + moduleBitCount[4] - moduleBitCount[6] + 9) % 9;\n  };\n  PDF417ScanningDecoder.toString = function (barcodeMatrix) {\n    var formatter = new Formatter();\n    // try (let formatter = new Formatter()) {\n    for (var row /*int*/ = 0; row < barcodeMatrix.length; row++) {\n      formatter.format('Row %2d: ', row);\n      for (var column /*int*/ = 0; column < barcodeMatrix[row].length; column++) {\n        var barcodeValue = barcodeMatrix[row][column];\n        if (barcodeValue.getValue().length === 0) {\n          formatter.format('        ', null);\n        } else {\n          formatter.format('%4d(%2d)', barcodeValue.getValue()[0], barcodeValue.getConfidence(barcodeValue.getValue()[0]));\n        }\n      }\n      formatter.format('%n');\n    }\n    return formatter.toString();\n    // }\n  };\n  /*final*/\n  PDF417ScanningDecoder.CODEWORD_SKEW_SIZE = 2;\n  /*final*/\n  PDF417ScanningDecoder.MAX_ERRORS = 3;\n  /*final*/\n  PDF417ScanningDecoder.MAX_EC_CODEWORDS = 512;\n  /*final*/\n  PDF417ScanningDecoder.errorCorrection = new ErrorCorrection();\n  return PDF417ScanningDecoder;\n}();\nexport default PDF417ScanningDecoder;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "ChecksumException", "FormatException", "NotFoundException", "MathUtils", "PDF417<PERSON><PERSON><PERSON>", "ErrorCorrection", "BoundingBox", "DetectionResultRowIndicatorColumn", "DetectionResult", "DetectionResultColumn", "Codeword", "BarcodeValue", "PDF417CodewordDecoder", "DecodedBitStreamParser", "<PERSON><PERSON><PERSON>", "PDF417ScanningDecoder", "decode", "image", "imageTopLeft", "imageBottomLeft", "imageTopRight", "imageBottomRight", "minCodewordWidth", "maxCodeword<PERSON>idth", "boundingBox", "leftRowIndicatorColumn", "rightRowIndicatorColumn", "detectionResult", "firstPass", "getRowIndicatorColumn", "merge", "getNotFoundInstance", "resultBox", "getBoundingBox", "getMinY", "getMaxY", "setBoundingBox", "maxBarcodeColumn", "getBarcodeColumnCount", "setDetectionResultColumn", "leftToRight", "barcodeColumnCount", "barcodeColumn", "getDetectionResultColumn", "undefined", "detectionResultColumn", "startColumn", "previousStartColumn", "imageRow", "getStartColumn", "getMaxX", "codeword", "detectCodeword", "getMinX", "setCodeword", "Math", "min", "getWidth", "max", "createDecoderResult", "barcodeMetadata", "getBarcodeMetadata", "adjustBoundingBox", "rowIndicatorColumn", "e_1", "_a", "rowHeights", "getRowHeights", "maxRowHeight", "getMax", "missingStartRows", "rowHeights_1", "rowHeights_1_1", "rowHeight", "e_1_1", "error", "return", "codewords", "getCodewords", "row", "missingEndRows", "addMissingRows", "isLeft", "values", "e_2", "maxValue", "values_1", "values_1_1", "e_2_1", "leftBarcodeMetadata", "rightBarcodeMetadata", "getColumnCount", "getErrorCorrectionLevel", "getRowCount", "startPoint", "increment", "trunc", "getX", "getY", "getStartX", "getEndX", "adjustCodewordCount", "barcodeMatrix", "barcodeMatrix01", "numberOfCodewords", "getValue", "calculatedNumberOfCodewords", "getBarcodeRowCount", "getNumberOfECCodeWords", "getBarcodeECLevel", "MAX_CODEWORDS_IN_BARCODE", "setValue", "createBarcodeMatrix", "erasures", "Array", "Int32Array", "ambiguousIndexValuesList", "ambiguousIndexesList", "column", "codewordIndex", "push", "ambiguousIndexValues", "createDecoderResultFromAmbiguousValues", "toIntArray", "ecLevel", "erasureArray", "ambiguousIndexes", "ambiguousIndexCount", "tries", "decodeCodewords", "err", "ignored", "getChecksumInstance", "e_3", "e_4", "_b", "from", "column_1", "_c", "getDetectionResultColumns", "_d", "_e", "_f", "rowNumber", "getRowNumber", "e_4_1", "e_3_1", "isValidBarcodeColumn", "e_5", "offset", "getCodeword", "getCodewordNearby", "skippedColumns", "previousRowCodeword", "e_5_1", "minColumn", "maxColumn", "adjustCodewordStartColumn", "moduleBitCount", "getModuleBitCount", "endColumn", "codewordBitCount", "sum", "tmpCount", "checkCodewordSkew", "decodedValue", "getDecodedValue", "getCodewordBucketNumber", "imageColumn", "moduleNumber", "previousPixelValue", "get", "barcodeECLevel", "codewordStartColumn", "correctedStartColumn", "abs", "CODEWORD_SKEW_SIZE", "codewordSize", "getFormatInstance", "numECCodewords", "correctedErrorsCount", "correctErrors", "verifyCodewordCount", "decoderResult", "setErrorsCorrected", "setErasures", "MAX_ERRORS", "MAX_EC_CODEWORDS", "errorCorrection", "getBitCountForCodeword", "result", "previousValue", "getCodewordBucketNumber_Int32Array", "getCodewordBucketNumber_number", "toString", "formatter", "format", "barcodeValue", "getConfidence"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/PDF417ScanningDecoder.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.ChecksumException;\nimport ChecksumException from '../../ChecksumException';\n// import com.google.zxing.FormatException;\nimport FormatException from '../../FormatException';\n// import com.google.zxing.NotFoundException;\nimport NotFoundException from '../../NotFoundException';\n// import com.google.zxing.common.detector.MathUtils;\nimport MathUtils from '../../common/detector/MathUtils';\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\n// import com.google.zxing.pdf417.decoder.ec.ErrorCorrection;\nimport ErrorCorrection from './ec/ErrorCorrection';\n// local\nimport BoundingBox from './BoundingBox';\nimport DetectionResultRowIndicatorColumn from './DetectionResultRowIndicatorColumn';\nimport DetectionResult from './DetectionResult';\nimport DetectionResultColumn from './DetectionResultColumn';\nimport Codeword from './Codeword';\nimport BarcodeValue from './BarcodeValue';\nimport PDF417CodewordDecoder from './PDF417CodewordDecoder';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\n// utils\nimport Formatter from '../../util/Formatter';\n// import java.util.ArrayList;\n// import java.util.Collection;\n// import java.util.Formatter;\n// import java.util.List;\n/**\n * <AUTHOR> Grau\n */\nvar PDF417ScanningDecoder = /** @class */ (function () {\n    function PDF417ScanningDecoder() {\n    }\n    /**\n     * @TODO don't pass in minCodewordWidth and maxCodewordWidth, pass in barcode columns for start and stop pattern\n     *\n     * columns. That way width can be deducted from the pattern column.\n     * This approach also allows to detect more details about the barcode, e.g. if a bar type (white or black) is wider\n     * than it should be. This can happen if the scanner used a bad blackpoint.\n     *\n     * @param BitMatrix\n     * @param image\n     * @param ResultPoint\n     * @param imageTopLeft\n     * @param ResultPoint\n     * @param imageBottomLeft\n     * @param ResultPoint\n     * @param imageTopRight\n     * @param ResultPoint\n     * @param imageBottomRight\n     * @param int\n     * @param minCodewordWidth\n     * @param int\n     * @param maxCodewordWidth\n     *\n     * @throws NotFoundException\n     * @throws FormatException\n     * @throws ChecksumException\n     */\n    PDF417ScanningDecoder.decode = function (image, imageTopLeft, imageBottomLeft, imageTopRight, imageBottomRight, minCodewordWidth, maxCodewordWidth) {\n        var boundingBox = new BoundingBox(image, imageTopLeft, imageBottomLeft, imageTopRight, imageBottomRight);\n        var leftRowIndicatorColumn = null;\n        var rightRowIndicatorColumn = null;\n        var detectionResult;\n        for (var firstPass /*boolean*/ = true;; firstPass = false) {\n            if (imageTopLeft != null) {\n                leftRowIndicatorColumn = PDF417ScanningDecoder.getRowIndicatorColumn(image, boundingBox, imageTopLeft, true, minCodewordWidth, maxCodewordWidth);\n            }\n            if (imageTopRight != null) {\n                rightRowIndicatorColumn = PDF417ScanningDecoder.getRowIndicatorColumn(image, boundingBox, imageTopRight, false, minCodewordWidth, maxCodewordWidth);\n            }\n            detectionResult = PDF417ScanningDecoder.merge(leftRowIndicatorColumn, rightRowIndicatorColumn);\n            if (detectionResult == null) {\n                throw NotFoundException.getNotFoundInstance();\n            }\n            var resultBox = detectionResult.getBoundingBox();\n            if (firstPass && resultBox != null &&\n                (resultBox.getMinY() < boundingBox.getMinY() || resultBox.getMaxY() > boundingBox.getMaxY())) {\n                boundingBox = resultBox;\n            }\n            else {\n                break;\n            }\n        }\n        detectionResult.setBoundingBox(boundingBox);\n        var maxBarcodeColumn = detectionResult.getBarcodeColumnCount() + 1;\n        detectionResult.setDetectionResultColumn(0, leftRowIndicatorColumn);\n        detectionResult.setDetectionResultColumn(maxBarcodeColumn, rightRowIndicatorColumn);\n        var leftToRight = leftRowIndicatorColumn != null;\n        for (var barcodeColumnCount /*int*/ = 1; barcodeColumnCount <= maxBarcodeColumn; barcodeColumnCount++) {\n            var barcodeColumn = leftToRight ? barcodeColumnCount : maxBarcodeColumn - barcodeColumnCount;\n            if (detectionResult.getDetectionResultColumn(barcodeColumn) !== /* null */ undefined) {\n                // This will be the case for the opposite row indicator column, which doesn't need to be decoded again.\n                continue;\n            }\n            var detectionResultColumn = void 0;\n            if (barcodeColumn === 0 || barcodeColumn === maxBarcodeColumn) {\n                detectionResultColumn = new DetectionResultRowIndicatorColumn(boundingBox, barcodeColumn === 0);\n            }\n            else {\n                detectionResultColumn = new DetectionResultColumn(boundingBox);\n            }\n            detectionResult.setDetectionResultColumn(barcodeColumn, detectionResultColumn);\n            var startColumn = -1;\n            var previousStartColumn = startColumn;\n            // TODO start at a row for which we know the start position, then detect upwards and downwards from there.\n            for (var imageRow /*int*/ = boundingBox.getMinY(); imageRow <= boundingBox.getMaxY(); imageRow++) {\n                startColumn = PDF417ScanningDecoder.getStartColumn(detectionResult, barcodeColumn, imageRow, leftToRight);\n                if (startColumn < 0 || startColumn > boundingBox.getMaxX()) {\n                    if (previousStartColumn === -1) {\n                        continue;\n                    }\n                    startColumn = previousStartColumn;\n                }\n                var codeword = PDF417ScanningDecoder.detectCodeword(image, boundingBox.getMinX(), boundingBox.getMaxX(), leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth);\n                if (codeword != null) {\n                    detectionResultColumn.setCodeword(imageRow, codeword);\n                    previousStartColumn = startColumn;\n                    minCodewordWidth = Math.min(minCodewordWidth, codeword.getWidth());\n                    maxCodewordWidth = Math.max(maxCodewordWidth, codeword.getWidth());\n                }\n            }\n        }\n        return PDF417ScanningDecoder.createDecoderResult(detectionResult);\n    };\n    /**\n     *\n     * @param leftRowIndicatorColumn\n     * @param rightRowIndicatorColumn\n     *\n     * @throws NotFoundException\n     */\n    PDF417ScanningDecoder.merge = function (leftRowIndicatorColumn, rightRowIndicatorColumn) {\n        if (leftRowIndicatorColumn == null && rightRowIndicatorColumn == null) {\n            return null;\n        }\n        var barcodeMetadata = PDF417ScanningDecoder.getBarcodeMetadata(leftRowIndicatorColumn, rightRowIndicatorColumn);\n        if (barcodeMetadata == null) {\n            return null;\n        }\n        var boundingBox = BoundingBox.merge(PDF417ScanningDecoder.adjustBoundingBox(leftRowIndicatorColumn), PDF417ScanningDecoder.adjustBoundingBox(rightRowIndicatorColumn));\n        return new DetectionResult(barcodeMetadata, boundingBox);\n    };\n    /**\n     *\n     * @param rowIndicatorColumn\n     *\n     * @throws NotFoundException\n     */\n    PDF417ScanningDecoder.adjustBoundingBox = function (rowIndicatorColumn) {\n        var e_1, _a;\n        if (rowIndicatorColumn == null) {\n            return null;\n        }\n        var rowHeights = rowIndicatorColumn.getRowHeights();\n        if (rowHeights == null) {\n            return null;\n        }\n        var maxRowHeight = PDF417ScanningDecoder.getMax(rowHeights);\n        var missingStartRows = 0;\n        try {\n            for (var rowHeights_1 = __values(rowHeights), rowHeights_1_1 = rowHeights_1.next(); !rowHeights_1_1.done; rowHeights_1_1 = rowHeights_1.next()) {\n                var rowHeight = rowHeights_1_1.value /*int*/;\n                missingStartRows += maxRowHeight - rowHeight;\n                if (rowHeight > 0) {\n                    break;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (rowHeights_1_1 && !rowHeights_1_1.done && (_a = rowHeights_1.return)) _a.call(rowHeights_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var codewords = rowIndicatorColumn.getCodewords();\n        for (var row /*int*/ = 0; missingStartRows > 0 && codewords[row] == null; row++) {\n            missingStartRows--;\n        }\n        var missingEndRows = 0;\n        for (var row /*int*/ = rowHeights.length - 1; row >= 0; row--) {\n            missingEndRows += maxRowHeight - rowHeights[row];\n            if (rowHeights[row] > 0) {\n                break;\n            }\n        }\n        for (var row /*int*/ = codewords.length - 1; missingEndRows > 0 && codewords[row] == null; row--) {\n            missingEndRows--;\n        }\n        return rowIndicatorColumn.getBoundingBox().addMissingRows(missingStartRows, missingEndRows, rowIndicatorColumn.isLeft());\n    };\n    PDF417ScanningDecoder.getMax = function (values) {\n        var e_2, _a;\n        var maxValue = -1;\n        try {\n            for (var values_1 = __values(values), values_1_1 = values_1.next(); !values_1_1.done; values_1_1 = values_1.next()) {\n                var value = values_1_1.value /*int*/;\n                maxValue = Math.max(maxValue, value);\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (values_1_1 && !values_1_1.done && (_a = values_1.return)) _a.call(values_1);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return maxValue;\n    };\n    PDF417ScanningDecoder.getBarcodeMetadata = function (leftRowIndicatorColumn, rightRowIndicatorColumn) {\n        var leftBarcodeMetadata;\n        if (leftRowIndicatorColumn == null ||\n            (leftBarcodeMetadata = leftRowIndicatorColumn.getBarcodeMetadata()) == null) {\n            return rightRowIndicatorColumn == null ? null : rightRowIndicatorColumn.getBarcodeMetadata();\n        }\n        var rightBarcodeMetadata;\n        if (rightRowIndicatorColumn == null ||\n            (rightBarcodeMetadata = rightRowIndicatorColumn.getBarcodeMetadata()) == null) {\n            return leftBarcodeMetadata;\n        }\n        if (leftBarcodeMetadata.getColumnCount() !== rightBarcodeMetadata.getColumnCount() &&\n            leftBarcodeMetadata.getErrorCorrectionLevel() !== rightBarcodeMetadata.getErrorCorrectionLevel() &&\n            leftBarcodeMetadata.getRowCount() !== rightBarcodeMetadata.getRowCount()) {\n            return null;\n        }\n        return leftBarcodeMetadata;\n    };\n    PDF417ScanningDecoder.getRowIndicatorColumn = function (image, boundingBox, startPoint, leftToRight, minCodewordWidth, maxCodewordWidth) {\n        var rowIndicatorColumn = new DetectionResultRowIndicatorColumn(boundingBox, leftToRight);\n        for (var i /*int*/ = 0; i < 2; i++) {\n            var increment = i === 0 ? 1 : -1;\n            var startColumn = Math.trunc(Math.trunc(startPoint.getX()));\n            for (var imageRow /*int*/ = Math.trunc(Math.trunc(startPoint.getY())); imageRow <= boundingBox.getMaxY() &&\n                imageRow >= boundingBox.getMinY(); imageRow += increment) {\n                var codeword = PDF417ScanningDecoder.detectCodeword(image, 0, image.getWidth(), leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth);\n                if (codeword != null) {\n                    rowIndicatorColumn.setCodeword(imageRow, codeword);\n                    if (leftToRight) {\n                        startColumn = codeword.getStartX();\n                    }\n                    else {\n                        startColumn = codeword.getEndX();\n                    }\n                }\n            }\n        }\n        return rowIndicatorColumn;\n    };\n    /**\n     *\n     * @param detectionResult\n     * @param BarcodeValue\n     * @param param2\n     * @param param3\n     * @param barcodeMatrix\n     *\n     * @throws NotFoundException\n     */\n    PDF417ScanningDecoder.adjustCodewordCount = function (detectionResult, barcodeMatrix) {\n        var barcodeMatrix01 = barcodeMatrix[0][1];\n        var numberOfCodewords = barcodeMatrix01.getValue();\n        var calculatedNumberOfCodewords = detectionResult.getBarcodeColumnCount() *\n            detectionResult.getBarcodeRowCount() -\n            PDF417ScanningDecoder.getNumberOfECCodeWords(detectionResult.getBarcodeECLevel());\n        if (numberOfCodewords.length === 0) {\n            if (calculatedNumberOfCodewords < 1 || calculatedNumberOfCodewords > PDF417Common.MAX_CODEWORDS_IN_BARCODE) {\n                throw NotFoundException.getNotFoundInstance();\n            }\n            barcodeMatrix01.setValue(calculatedNumberOfCodewords);\n        }\n        else if (numberOfCodewords[0] !== calculatedNumberOfCodewords) {\n            // The calculated one is more reliable as it is derived from the row indicator columns\n            barcodeMatrix01.setValue(calculatedNumberOfCodewords);\n        }\n    };\n    /**\n     *\n     * @param detectionResult\n     *\n     * @throws FormatException\n     * @throws ChecksumException\n     * @throws NotFoundException\n     */\n    PDF417ScanningDecoder.createDecoderResult = function (detectionResult) {\n        var barcodeMatrix = PDF417ScanningDecoder.createBarcodeMatrix(detectionResult);\n        PDF417ScanningDecoder.adjustCodewordCount(detectionResult, barcodeMatrix);\n        var erasures /*Collection<Integer>*/ = new Array();\n        var codewords = new Int32Array(detectionResult.getBarcodeRowCount() * detectionResult.getBarcodeColumnCount());\n        var ambiguousIndexValuesList = /*List<int[]>*/ [];\n        var ambiguousIndexesList = /*Collection<Integer>*/ new Array();\n        for (var row /*int*/ = 0; row < detectionResult.getBarcodeRowCount(); row++) {\n            for (var column /*int*/ = 0; column < detectionResult.getBarcodeColumnCount(); column++) {\n                var values = barcodeMatrix[row][column + 1].getValue();\n                var codewordIndex = row * detectionResult.getBarcodeColumnCount() + column;\n                if (values.length === 0) {\n                    erasures.push(codewordIndex);\n                }\n                else if (values.length === 1) {\n                    codewords[codewordIndex] = values[0];\n                }\n                else {\n                    ambiguousIndexesList.push(codewordIndex);\n                    ambiguousIndexValuesList.push(values);\n                }\n            }\n        }\n        var ambiguousIndexValues = new Array(ambiguousIndexValuesList.length);\n        for (var i /*int*/ = 0; i < ambiguousIndexValues.length; i++) {\n            ambiguousIndexValues[i] = ambiguousIndexValuesList[i];\n        }\n        return PDF417ScanningDecoder.createDecoderResultFromAmbiguousValues(detectionResult.getBarcodeECLevel(), codewords, PDF417Common.toIntArray(erasures), PDF417Common.toIntArray(ambiguousIndexesList), ambiguousIndexValues);\n    };\n    /**\n     * This method deals with the fact, that the decoding process doesn't always yield a single most likely value. The\n     * current error correction implementation doesn't deal with erasures very well, so it's better to provide a value\n     * for these ambiguous codewords instead of treating it as an erasure. The problem is that we don't know which of\n     * the ambiguous values to choose. We try decode using the first value, and if that fails, we use another of the\n     * ambiguous values and try to decode again. This usually only happens on very hard to read and decode barcodes,\n     * so decoding the normal barcodes is not affected by this.\n     *\n     * @param erasureArray contains the indexes of erasures\n     * @param ambiguousIndexes array with the indexes that have more than one most likely value\n     * @param ambiguousIndexValues two dimensional array that contains the ambiguous values. The first dimension must\n     * be the same length as the ambiguousIndexes array\n     *\n     * @throws FormatException\n     * @throws ChecksumException\n     */\n    PDF417ScanningDecoder.createDecoderResultFromAmbiguousValues = function (ecLevel, codewords, erasureArray, ambiguousIndexes, ambiguousIndexValues) {\n        var ambiguousIndexCount = new Int32Array(ambiguousIndexes.length);\n        var tries = 100;\n        while (tries-- > 0) {\n            for (var i /*int*/ = 0; i < ambiguousIndexCount.length; i++) {\n                codewords[ambiguousIndexes[i]] = ambiguousIndexValues[i][ambiguousIndexCount[i]];\n            }\n            try {\n                return PDF417ScanningDecoder.decodeCodewords(codewords, ecLevel, erasureArray);\n            }\n            catch (err) {\n                var ignored = err instanceof ChecksumException;\n                if (!ignored) {\n                    throw err;\n                }\n            }\n            if (ambiguousIndexCount.length === 0) {\n                throw ChecksumException.getChecksumInstance();\n            }\n            for (var i /*int*/ = 0; i < ambiguousIndexCount.length; i++) {\n                if (ambiguousIndexCount[i] < ambiguousIndexValues[i].length - 1) {\n                    ambiguousIndexCount[i]++;\n                    break;\n                }\n                else {\n                    ambiguousIndexCount[i] = 0;\n                    if (i === ambiguousIndexCount.length - 1) {\n                        throw ChecksumException.getChecksumInstance();\n                    }\n                }\n            }\n        }\n        throw ChecksumException.getChecksumInstance();\n    };\n    PDF417ScanningDecoder.createBarcodeMatrix = function (detectionResult) {\n        var e_3, _a, e_4, _b;\n        // let barcodeMatrix: BarcodeValue[][] =\n        // new BarcodeValue[detectionResult.getBarcodeRowCount()][detectionResult.getBarcodeColumnCount() + 2];\n        var barcodeMatrix = Array.from({ length: detectionResult.getBarcodeRowCount() }, function () { return new Array(detectionResult.getBarcodeColumnCount() + 2); });\n        for (var row /*int*/ = 0; row < barcodeMatrix.length; row++) {\n            for (var column_1 /*int*/ = 0; column_1 < barcodeMatrix[row].length; column_1++) {\n                barcodeMatrix[row][column_1] = new BarcodeValue();\n            }\n        }\n        var column = 0;\n        try {\n            for (var _c = __values(detectionResult.getDetectionResultColumns()), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var detectionResultColumn = _d.value /*DetectionResultColumn*/;\n                if (detectionResultColumn != null) {\n                    try {\n                        for (var _e = (e_4 = void 0, __values(detectionResultColumn.getCodewords())), _f = _e.next(); !_f.done; _f = _e.next()) {\n                            var codeword = _f.value /*Codeword*/;\n                            if (codeword != null) {\n                                var rowNumber = codeword.getRowNumber();\n                                if (rowNumber >= 0) {\n                                    if (rowNumber >= barcodeMatrix.length) {\n                                        // We have more rows than the barcode metadata allows for, ignore them.\n                                        continue;\n                                    }\n                                    barcodeMatrix[rowNumber][column].setValue(codeword.getValue());\n                                }\n                            }\n                        }\n                    }\n                    catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                    finally {\n                        try {\n                            if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n                        }\n                        finally { if (e_4) throw e_4.error; }\n                    }\n                }\n                column++;\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return barcodeMatrix;\n    };\n    PDF417ScanningDecoder.isValidBarcodeColumn = function (detectionResult, barcodeColumn) {\n        return barcodeColumn >= 0 && barcodeColumn <= detectionResult.getBarcodeColumnCount() + 1;\n    };\n    PDF417ScanningDecoder.getStartColumn = function (detectionResult, barcodeColumn, imageRow, leftToRight) {\n        var e_5, _a;\n        var offset = leftToRight ? 1 : -1;\n        var codeword = null;\n        if (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n            codeword = detectionResult.getDetectionResultColumn(barcodeColumn - offset).getCodeword(imageRow);\n        }\n        if (codeword != null) {\n            return leftToRight ? codeword.getEndX() : codeword.getStartX();\n        }\n        codeword = detectionResult.getDetectionResultColumn(barcodeColumn).getCodewordNearby(imageRow);\n        if (codeword != null) {\n            return leftToRight ? codeword.getStartX() : codeword.getEndX();\n        }\n        if (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n            codeword = detectionResult.getDetectionResultColumn(barcodeColumn - offset).getCodewordNearby(imageRow);\n        }\n        if (codeword != null) {\n            return leftToRight ? codeword.getEndX() : codeword.getStartX();\n        }\n        var skippedColumns = 0;\n        while (PDF417ScanningDecoder.isValidBarcodeColumn(detectionResult, barcodeColumn - offset)) {\n            barcodeColumn -= offset;\n            try {\n                for (var _b = (e_5 = void 0, __values(detectionResult.getDetectionResultColumn(barcodeColumn).getCodewords())), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var previousRowCodeword = _c.value /*Codeword*/;\n                    if (previousRowCodeword != null) {\n                        return (leftToRight ? previousRowCodeword.getEndX() : previousRowCodeword.getStartX()) +\n                            offset *\n                                skippedColumns *\n                                (previousRowCodeword.getEndX() - previousRowCodeword.getStartX());\n                    }\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            skippedColumns++;\n        }\n        return leftToRight ? detectionResult.getBoundingBox().getMinX() : detectionResult.getBoundingBox().getMaxX();\n    };\n    PDF417ScanningDecoder.detectCodeword = function (image, minColumn, maxColumn, leftToRight, startColumn, imageRow, minCodewordWidth, maxCodewordWidth) {\n        startColumn = PDF417ScanningDecoder.adjustCodewordStartColumn(image, minColumn, maxColumn, leftToRight, startColumn, imageRow);\n        // we usually know fairly exact now how long a codeword is. We should provide minimum and maximum expected length\n        // and try to adjust the read pixels, e.g. remove single pixel errors or try to cut off exceeding pixels.\n        // min and maxCodewordWidth should not be used as they are calculated for the whole barcode an can be inaccurate\n        // for the current position\n        var moduleBitCount = PDF417ScanningDecoder.getModuleBitCount(image, minColumn, maxColumn, leftToRight, startColumn, imageRow);\n        if (moduleBitCount == null) {\n            return null;\n        }\n        var endColumn;\n        var codewordBitCount = MathUtils.sum(moduleBitCount);\n        if (leftToRight) {\n            endColumn = startColumn + codewordBitCount;\n        }\n        else {\n            for (var i /*int*/ = 0; i < moduleBitCount.length / 2; i++) {\n                var tmpCount = moduleBitCount[i];\n                moduleBitCount[i] = moduleBitCount[moduleBitCount.length - 1 - i];\n                moduleBitCount[moduleBitCount.length - 1 - i] = tmpCount;\n            }\n            endColumn = startColumn;\n            startColumn = endColumn - codewordBitCount;\n        }\n        // TODO implement check for width and correction of black and white bars\n        // use start (and maybe stop pattern) to determine if black bars are wider than white bars. If so, adjust.\n        // should probably done only for codewords with a lot more than 17 bits.\n        // The following fixes 10-1.png, which has wide black bars and small white bars\n        //    for (let i /*int*/ = 0; i < moduleBitCount.length; i++) {\n        //      if (i % 2 === 0) {\n        //        moduleBitCount[i]--;\n        //      } else {\n        //        moduleBitCount[i]++;\n        //      }\n        //    }\n        // We could also use the width of surrounding codewords for more accurate results, but this seems\n        // sufficient for now\n        if (!PDF417ScanningDecoder.checkCodewordSkew(codewordBitCount, minCodewordWidth, maxCodewordWidth)) {\n            // We could try to use the startX and endX position of the codeword in the same column in the previous row,\n            // create the bit count from it and normalize it to 8. This would help with single pixel errors.\n            return null;\n        }\n        var decodedValue = PDF417CodewordDecoder.getDecodedValue(moduleBitCount);\n        var codeword = PDF417Common.getCodeword(decodedValue);\n        if (codeword === -1) {\n            return null;\n        }\n        return new Codeword(startColumn, endColumn, PDF417ScanningDecoder.getCodewordBucketNumber(decodedValue), codeword);\n    };\n    PDF417ScanningDecoder.getModuleBitCount = function (image, minColumn, maxColumn, leftToRight, startColumn, imageRow) {\n        var imageColumn = startColumn;\n        var moduleBitCount = new Int32Array(8);\n        var moduleNumber = 0;\n        var increment = leftToRight ? 1 : -1;\n        var previousPixelValue = leftToRight;\n        while ((leftToRight ? imageColumn < maxColumn : imageColumn >= minColumn) &&\n            moduleNumber < moduleBitCount.length) {\n            if (image.get(imageColumn, imageRow) === previousPixelValue) {\n                moduleBitCount[moduleNumber]++;\n                imageColumn += increment;\n            }\n            else {\n                moduleNumber++;\n                previousPixelValue = !previousPixelValue;\n            }\n        }\n        if (moduleNumber === moduleBitCount.length ||\n            ((imageColumn === (leftToRight ? maxColumn : minColumn)) &&\n                moduleNumber === moduleBitCount.length - 1)) {\n            return moduleBitCount;\n        }\n        return null;\n    };\n    PDF417ScanningDecoder.getNumberOfECCodeWords = function (barcodeECLevel) {\n        return 2 << barcodeECLevel;\n    };\n    PDF417ScanningDecoder.adjustCodewordStartColumn = function (image, minColumn, maxColumn, leftToRight, codewordStartColumn, imageRow) {\n        var correctedStartColumn = codewordStartColumn;\n        var increment = leftToRight ? -1 : 1;\n        // there should be no black pixels before the start column. If there are, then we need to start earlier.\n        for (var i /*int*/ = 0; i < 2; i++) {\n            while ((leftToRight ? correctedStartColumn >= minColumn : correctedStartColumn < maxColumn) &&\n                leftToRight === image.get(correctedStartColumn, imageRow)) {\n                if (Math.abs(codewordStartColumn - correctedStartColumn) > PDF417ScanningDecoder.CODEWORD_SKEW_SIZE) {\n                    return codewordStartColumn;\n                }\n                correctedStartColumn += increment;\n            }\n            increment = -increment;\n            leftToRight = !leftToRight;\n        }\n        return correctedStartColumn;\n    };\n    PDF417ScanningDecoder.checkCodewordSkew = function (codewordSize, minCodewordWidth, maxCodewordWidth) {\n        return minCodewordWidth - PDF417ScanningDecoder.CODEWORD_SKEW_SIZE <= codewordSize &&\n            codewordSize <= maxCodewordWidth + PDF417ScanningDecoder.CODEWORD_SKEW_SIZE;\n    };\n    /**\n     * @throws FormatException,\n     * @throws ChecksumException\n     */\n    PDF417ScanningDecoder.decodeCodewords = function (codewords, ecLevel, erasures) {\n        if (codewords.length === 0) {\n            throw FormatException.getFormatInstance();\n        }\n        var numECCodewords = 1 << (ecLevel + 1);\n        var correctedErrorsCount = PDF417ScanningDecoder.correctErrors(codewords, erasures, numECCodewords);\n        PDF417ScanningDecoder.verifyCodewordCount(codewords, numECCodewords);\n        // Decode the codewords\n        var decoderResult = DecodedBitStreamParser.decode(codewords, '' + ecLevel);\n        decoderResult.setErrorsCorrected(correctedErrorsCount);\n        decoderResult.setErasures(erasures.length);\n        return decoderResult;\n    };\n    /**\n     * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n     * correct the errors in-place.</p>\n     *\n     * @param codewords   data and error correction codewords\n     * @param erasures positions of any known erasures\n     * @param numECCodewords number of error correction codewords that are available in codewords\n     * @throws ChecksumException if error correction fails\n     */\n    PDF417ScanningDecoder.correctErrors = function (codewords, erasures, numECCodewords) {\n        if (erasures != null &&\n            erasures.length > numECCodewords / 2 + PDF417ScanningDecoder.MAX_ERRORS ||\n            numECCodewords < 0 ||\n            numECCodewords > PDF417ScanningDecoder.MAX_EC_CODEWORDS) {\n            // Too many errors or EC Codewords is corrupted\n            throw ChecksumException.getChecksumInstance();\n        }\n        return PDF417ScanningDecoder.errorCorrection.decode(codewords, numECCodewords, erasures);\n    };\n    /**\n     * Verify that all is OK with the codeword array.\n     * @throws FormatException\n     */\n    PDF417ScanningDecoder.verifyCodewordCount = function (codewords, numECCodewords) {\n        if (codewords.length < 4) {\n            // Codeword array size should be at least 4 allowing for\n            // Count CW, At least one Data CW, Error Correction CW, Error Correction CW\n            throw FormatException.getFormatInstance();\n        }\n        // The first codeword, the Symbol Length Descriptor, shall always encode the total number of data\n        // codewords in the symbol, including the Symbol Length Descriptor itself, data codewords and pad\n        // codewords, but excluding the number of error correction codewords.\n        var numberOfCodewords = codewords[0];\n        if (numberOfCodewords > codewords.length) {\n            throw FormatException.getFormatInstance();\n        }\n        if (numberOfCodewords === 0) {\n            // Reset to the length of the array - 8 (Allow for at least level 3 Error Correction (8 Error Codewords)\n            if (numECCodewords < codewords.length) {\n                codewords[0] = codewords.length - numECCodewords;\n            }\n            else {\n                throw FormatException.getFormatInstance();\n            }\n        }\n    };\n    PDF417ScanningDecoder.getBitCountForCodeword = function (codeword) {\n        var result = new Int32Array(8);\n        var previousValue = 0;\n        var i = result.length - 1;\n        while (true) {\n            if ((codeword & 0x1) !== previousValue) {\n                previousValue = codeword & 0x1;\n                i--;\n                if (i < 0) {\n                    break;\n                }\n            }\n            result[i]++;\n            codeword >>= 1;\n        }\n        return result;\n    };\n    PDF417ScanningDecoder.getCodewordBucketNumber = function (codeword) {\n        if (codeword instanceof Int32Array) {\n            return this.getCodewordBucketNumber_Int32Array(codeword);\n        }\n        return this.getCodewordBucketNumber_number(codeword);\n    };\n    PDF417ScanningDecoder.getCodewordBucketNumber_number = function (codeword) {\n        return PDF417ScanningDecoder.getCodewordBucketNumber(PDF417ScanningDecoder.getBitCountForCodeword(codeword));\n    };\n    PDF417ScanningDecoder.getCodewordBucketNumber_Int32Array = function (moduleBitCount) {\n        return (moduleBitCount[0] - moduleBitCount[2] + moduleBitCount[4] - moduleBitCount[6] + 9) % 9;\n    };\n    PDF417ScanningDecoder.toString = function (barcodeMatrix) {\n        var formatter = new Formatter();\n        // try (let formatter = new Formatter()) {\n        for (var row /*int*/ = 0; row < barcodeMatrix.length; row++) {\n            formatter.format('Row %2d: ', row);\n            for (var column /*int*/ = 0; column < barcodeMatrix[row].length; column++) {\n                var barcodeValue = barcodeMatrix[row][column];\n                if (barcodeValue.getValue().length === 0) {\n                    formatter.format('        ', null);\n                }\n                else {\n                    formatter.format('%4d(%2d)', barcodeValue.getValue()[0], barcodeValue.getConfidence(barcodeValue.getValue()[0]));\n                }\n            }\n            formatter.format('%n');\n        }\n        return formatter.toString();\n        // }\n    };\n    /*final*/ PDF417ScanningDecoder.CODEWORD_SKEW_SIZE = 2;\n    /*final*/ PDF417ScanningDecoder.MAX_ERRORS = 3;\n    /*final*/ PDF417ScanningDecoder.MAX_EC_CODEWORDS = 512;\n    /*final*/ PDF417ScanningDecoder.errorCorrection = new ErrorCorrection();\n    return PDF417ScanningDecoder;\n}());\nexport default PDF417ScanningDecoder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA,OAAOW,iBAAiB,MAAM,yBAAyB;AACvD;AACA,OAAOC,eAAe,MAAM,uBAAuB;AACnD;AACA,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD;AACA,OAAOC,SAAS,MAAM,iCAAiC;AACvD;AACA,OAAOC,YAAY,MAAM,iBAAiB;AAC1C;AACA,OAAOC,eAAe,MAAM,sBAAsB;AAClD;AACA,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,iCAAiC,MAAM,qCAAqC;AACnF,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D;AACA,OAAOC,SAAS,MAAM,sBAAsB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,qBAAqB,GAAG,aAAe,YAAY;EACnD,SAASA,qBAAqBA,CAAA,EAAG,CACjC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,qBAAqB,CAACC,MAAM,GAAG,UAAUC,KAAK,EAAEC,YAAY,EAAEC,eAAe,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAE;IAChJ,IAAIC,WAAW,GAAG,IAAIlB,WAAW,CAACW,KAAK,EAAEC,YAAY,EAAEC,eAAe,EAAEC,aAAa,EAAEC,gBAAgB,CAAC;IACxG,IAAII,sBAAsB,GAAG,IAAI;IACjC,IAAIC,uBAAuB,GAAG,IAAI;IAClC,IAAIC,eAAe;IACnB,KAAK,IAAIC,SAAS,CAAC,cAAc,IAAI,GAAGA,SAAS,GAAG,KAAK,EAAE;MACvD,IAAIV,YAAY,IAAI,IAAI,EAAE;QACtBO,sBAAsB,GAAGV,qBAAqB,CAACc,qBAAqB,CAACZ,KAAK,EAAEO,WAAW,EAAEN,YAAY,EAAE,IAAI,EAAEI,gBAAgB,EAAEC,gBAAgB,CAAC;MACpJ;MACA,IAAIH,aAAa,IAAI,IAAI,EAAE;QACvBM,uBAAuB,GAAGX,qBAAqB,CAACc,qBAAqB,CAACZ,KAAK,EAAEO,WAAW,EAAEJ,aAAa,EAAE,KAAK,EAAEE,gBAAgB,EAAEC,gBAAgB,CAAC;MACvJ;MACAI,eAAe,GAAGZ,qBAAqB,CAACe,KAAK,CAACL,sBAAsB,EAAEC,uBAAuB,CAAC;MAC9F,IAAIC,eAAe,IAAI,IAAI,EAAE;QACzB,MAAMzB,iBAAiB,CAAC6B,mBAAmB,CAAC,CAAC;MACjD;MACA,IAAIC,SAAS,GAAGL,eAAe,CAACM,cAAc,CAAC,CAAC;MAChD,IAAIL,SAAS,IAAII,SAAS,IAAI,IAAI,KAC7BA,SAAS,CAACE,OAAO,CAAC,CAAC,GAAGV,WAAW,CAACU,OAAO,CAAC,CAAC,IAAIF,SAAS,CAACG,OAAO,CAAC,CAAC,GAAGX,WAAW,CAACW,OAAO,CAAC,CAAC,CAAC,EAAE;QAC9FX,WAAW,GAAGQ,SAAS;MAC3B,CAAC,MACI;QACD;MACJ;IACJ;IACAL,eAAe,CAACS,cAAc,CAACZ,WAAW,CAAC;IAC3C,IAAIa,gBAAgB,GAAGV,eAAe,CAACW,qBAAqB,CAAC,CAAC,GAAG,CAAC;IAClEX,eAAe,CAACY,wBAAwB,CAAC,CAAC,EAAEd,sBAAsB,CAAC;IACnEE,eAAe,CAACY,wBAAwB,CAACF,gBAAgB,EAAEX,uBAAuB,CAAC;IACnF,IAAIc,WAAW,GAAGf,sBAAsB,IAAI,IAAI;IAChD,KAAK,IAAIgB,kBAAkB,CAAC,UAAU,CAAC,EAAEA,kBAAkB,IAAIJ,gBAAgB,EAAEI,kBAAkB,EAAE,EAAE;MACnG,IAAIC,aAAa,GAAGF,WAAW,GAAGC,kBAAkB,GAAGJ,gBAAgB,GAAGI,kBAAkB;MAC5F,IAAId,eAAe,CAACgB,wBAAwB,CAACD,aAAa,CAAC,KAAK,UAAWE,SAAS,EAAE;QAClF;QACA;MACJ;MACA,IAAIC,qBAAqB,GAAG,KAAK,CAAC;MAClC,IAAIH,aAAa,KAAK,CAAC,IAAIA,aAAa,KAAKL,gBAAgB,EAAE;QAC3DQ,qBAAqB,GAAG,IAAItC,iCAAiC,CAACiB,WAAW,EAAEkB,aAAa,KAAK,CAAC,CAAC;MACnG,CAAC,MACI;QACDG,qBAAqB,GAAG,IAAIpC,qBAAqB,CAACe,WAAW,CAAC;MAClE;MACAG,eAAe,CAACY,wBAAwB,CAACG,aAAa,EAAEG,qBAAqB,CAAC;MAC9E,IAAIC,WAAW,GAAG,CAAC,CAAC;MACpB,IAAIC,mBAAmB,GAAGD,WAAW;MACrC;MACA,KAAK,IAAIE,QAAQ,CAAC,UAAUxB,WAAW,CAACU,OAAO,CAAC,CAAC,EAAEc,QAAQ,IAAIxB,WAAW,CAACW,OAAO,CAAC,CAAC,EAAEa,QAAQ,EAAE,EAAE;QAC9FF,WAAW,GAAG/B,qBAAqB,CAACkC,cAAc,CAACtB,eAAe,EAAEe,aAAa,EAAEM,QAAQ,EAAER,WAAW,CAAC;QACzG,IAAIM,WAAW,GAAG,CAAC,IAAIA,WAAW,GAAGtB,WAAW,CAAC0B,OAAO,CAAC,CAAC,EAAE;UACxD,IAAIH,mBAAmB,KAAK,CAAC,CAAC,EAAE;YAC5B;UACJ;UACAD,WAAW,GAAGC,mBAAmB;QACrC;QACA,IAAII,QAAQ,GAAGpC,qBAAqB,CAACqC,cAAc,CAACnC,KAAK,EAAEO,WAAW,CAAC6B,OAAO,CAAC,CAAC,EAAE7B,WAAW,CAAC0B,OAAO,CAAC,CAAC,EAAEV,WAAW,EAAEM,WAAW,EAAEE,QAAQ,EAAE1B,gBAAgB,EAAEC,gBAAgB,CAAC;QAChL,IAAI4B,QAAQ,IAAI,IAAI,EAAE;UAClBN,qBAAqB,CAACS,WAAW,CAACN,QAAQ,EAAEG,QAAQ,CAAC;UACrDJ,mBAAmB,GAAGD,WAAW;UACjCxB,gBAAgB,GAAGiC,IAAI,CAACC,GAAG,CAAClC,gBAAgB,EAAE6B,QAAQ,CAACM,QAAQ,CAAC,CAAC,CAAC;UAClElC,gBAAgB,GAAGgC,IAAI,CAACG,GAAG,CAACnC,gBAAgB,EAAE4B,QAAQ,CAACM,QAAQ,CAAC,CAAC,CAAC;QACtE;MACJ;IACJ;IACA,OAAO1C,qBAAqB,CAAC4C,mBAAmB,CAAChC,eAAe,CAAC;EACrE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIZ,qBAAqB,CAACe,KAAK,GAAG,UAAUL,sBAAsB,EAAEC,uBAAuB,EAAE;IACrF,IAAID,sBAAsB,IAAI,IAAI,IAAIC,uBAAuB,IAAI,IAAI,EAAE;MACnE,OAAO,IAAI;IACf;IACA,IAAIkC,eAAe,GAAG7C,qBAAqB,CAAC8C,kBAAkB,CAACpC,sBAAsB,EAAEC,uBAAuB,CAAC;IAC/G,IAAIkC,eAAe,IAAI,IAAI,EAAE;MACzB,OAAO,IAAI;IACf;IACA,IAAIpC,WAAW,GAAGlB,WAAW,CAACwB,KAAK,CAACf,qBAAqB,CAAC+C,iBAAiB,CAACrC,sBAAsB,CAAC,EAAEV,qBAAqB,CAAC+C,iBAAiB,CAACpC,uBAAuB,CAAC,CAAC;IACtK,OAAO,IAAIlB,eAAe,CAACoD,eAAe,EAAEpC,WAAW,CAAC;EAC5D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIT,qBAAqB,CAAC+C,iBAAiB,GAAG,UAAUC,kBAAkB,EAAE;IACpE,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIF,kBAAkB,IAAI,IAAI,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,IAAIG,UAAU,GAAGH,kBAAkB,CAACI,aAAa,CAAC,CAAC;IACnD,IAAID,UAAU,IAAI,IAAI,EAAE;MACpB,OAAO,IAAI;IACf;IACA,IAAIE,YAAY,GAAGrD,qBAAqB,CAACsD,MAAM,CAACH,UAAU,CAAC;IAC3D,IAAII,gBAAgB,GAAG,CAAC;IACxB,IAAI;MACA,KAAK,IAAIC,YAAY,GAAGpF,QAAQ,CAAC+E,UAAU,CAAC,EAAEM,cAAc,GAAGD,YAAY,CAAC3E,IAAI,CAAC,CAAC,EAAE,CAAC4E,cAAc,CAAC1E,IAAI,EAAE0E,cAAc,GAAGD,YAAY,CAAC3E,IAAI,CAAC,CAAC,EAAE;QAC5I,IAAI6E,SAAS,GAAGD,cAAc,CAAC3E,KAAK,CAAC;QACrCyE,gBAAgB,IAAIF,YAAY,GAAGK,SAAS;QAC5C,IAAIA,SAAS,GAAG,CAAC,EAAE;UACf;QACJ;MACJ;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEV,GAAG,GAAG;QAAEW,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,cAAc,IAAI,CAACA,cAAc,CAAC1E,IAAI,KAAKmE,EAAE,GAAGM,YAAY,CAACK,MAAM,CAAC,EAAEX,EAAE,CAACvE,IAAI,CAAC6E,YAAY,CAAC;MACnG,CAAC,SACO;QAAE,IAAIP,GAAG,EAAE,MAAMA,GAAG,CAACW,KAAK;MAAE;IACxC;IACA,IAAIE,SAAS,GAAGd,kBAAkB,CAACe,YAAY,CAAC,CAAC;IACjD,KAAK,IAAIC,GAAG,CAAC,UAAU,CAAC,EAAET,gBAAgB,GAAG,CAAC,IAAIO,SAAS,CAACE,GAAG,CAAC,IAAI,IAAI,EAAEA,GAAG,EAAE,EAAE;MAC7ET,gBAAgB,EAAE;IACtB;IACA,IAAIU,cAAc,GAAG,CAAC;IACtB,KAAK,IAAID,GAAG,CAAC,UAAUb,UAAU,CAACvE,MAAM,GAAG,CAAC,EAAEoF,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;MAC3DC,cAAc,IAAIZ,YAAY,GAAGF,UAAU,CAACa,GAAG,CAAC;MAChD,IAAIb,UAAU,CAACa,GAAG,CAAC,GAAG,CAAC,EAAE;QACrB;MACJ;IACJ;IACA,KAAK,IAAIA,GAAG,CAAC,UAAUF,SAAS,CAAClF,MAAM,GAAG,CAAC,EAAEqF,cAAc,GAAG,CAAC,IAAIH,SAAS,CAACE,GAAG,CAAC,IAAI,IAAI,EAAEA,GAAG,EAAE,EAAE;MAC9FC,cAAc,EAAE;IACpB;IACA,OAAOjB,kBAAkB,CAAC9B,cAAc,CAAC,CAAC,CAACgD,cAAc,CAACX,gBAAgB,EAAEU,cAAc,EAAEjB,kBAAkB,CAACmB,MAAM,CAAC,CAAC,CAAC;EAC5H,CAAC;EACDnE,qBAAqB,CAACsD,MAAM,GAAG,UAAUc,MAAM,EAAE;IAC7C,IAAIC,GAAG,EAAEnB,EAAE;IACX,IAAIoB,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI;MACA,KAAK,IAAIC,QAAQ,GAAGnG,QAAQ,CAACgG,MAAM,CAAC,EAAEI,UAAU,GAAGD,QAAQ,CAAC1F,IAAI,CAAC,CAAC,EAAE,CAAC2F,UAAU,CAACzF,IAAI,EAAEyF,UAAU,GAAGD,QAAQ,CAAC1F,IAAI,CAAC,CAAC,EAAE;QAChH,IAAIC,KAAK,GAAG0F,UAAU,CAAC1F,KAAK,CAAC;QAC7BwF,QAAQ,GAAG9B,IAAI,CAACG,GAAG,CAAC2B,QAAQ,EAAExF,KAAK,CAAC;MACxC;IACJ,CAAC,CACD,OAAO2F,KAAK,EAAE;MAAEJ,GAAG,GAAG;QAAET,KAAK,EAAEa;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAID,UAAU,IAAI,CAACA,UAAU,CAACzF,IAAI,KAAKmE,EAAE,GAAGqB,QAAQ,CAACV,MAAM,CAAC,EAAEX,EAAE,CAACvE,IAAI,CAAC4F,QAAQ,CAAC;MACnF,CAAC,SACO;QAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACT,KAAK;MAAE;IACxC;IACA,OAAOU,QAAQ;EACnB,CAAC;EACDtE,qBAAqB,CAAC8C,kBAAkB,GAAG,UAAUpC,sBAAsB,EAAEC,uBAAuB,EAAE;IAClG,IAAI+D,mBAAmB;IACvB,IAAIhE,sBAAsB,IAAI,IAAI,IAC9B,CAACgE,mBAAmB,GAAGhE,sBAAsB,CAACoC,kBAAkB,CAAC,CAAC,KAAK,IAAI,EAAE;MAC7E,OAAOnC,uBAAuB,IAAI,IAAI,GAAG,IAAI,GAAGA,uBAAuB,CAACmC,kBAAkB,CAAC,CAAC;IAChG;IACA,IAAI6B,oBAAoB;IACxB,IAAIhE,uBAAuB,IAAI,IAAI,IAC/B,CAACgE,oBAAoB,GAAGhE,uBAAuB,CAACmC,kBAAkB,CAAC,CAAC,KAAK,IAAI,EAAE;MAC/E,OAAO4B,mBAAmB;IAC9B;IACA,IAAIA,mBAAmB,CAACE,cAAc,CAAC,CAAC,KAAKD,oBAAoB,CAACC,cAAc,CAAC,CAAC,IAC9EF,mBAAmB,CAACG,uBAAuB,CAAC,CAAC,KAAKF,oBAAoB,CAACE,uBAAuB,CAAC,CAAC,IAChGH,mBAAmB,CAACI,WAAW,CAAC,CAAC,KAAKH,oBAAoB,CAACG,WAAW,CAAC,CAAC,EAAE;MAC1E,OAAO,IAAI;IACf;IACA,OAAOJ,mBAAmB;EAC9B,CAAC;EACD1E,qBAAqB,CAACc,qBAAqB,GAAG,UAAUZ,KAAK,EAAEO,WAAW,EAAEsE,UAAU,EAAEtD,WAAW,EAAElB,gBAAgB,EAAEC,gBAAgB,EAAE;IACrI,IAAIwC,kBAAkB,GAAG,IAAIxD,iCAAiC,CAACiB,WAAW,EAAEgB,WAAW,CAAC;IACxF,KAAK,IAAI/C,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAChC,IAAIsG,SAAS,GAAGtG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAChC,IAAIqD,WAAW,GAAGS,IAAI,CAACyC,KAAK,CAACzC,IAAI,CAACyC,KAAK,CAACF,UAAU,CAACG,IAAI,CAAC,CAAC,CAAC,CAAC;MAC3D,KAAK,IAAIjD,QAAQ,CAAC,UAAUO,IAAI,CAACyC,KAAK,CAACzC,IAAI,CAACyC,KAAK,CAACF,UAAU,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAElD,QAAQ,IAAIxB,WAAW,CAACW,OAAO,CAAC,CAAC,IACpGa,QAAQ,IAAIxB,WAAW,CAACU,OAAO,CAAC,CAAC,EAAEc,QAAQ,IAAI+C,SAAS,EAAE;QAC1D,IAAI5C,QAAQ,GAAGpC,qBAAqB,CAACqC,cAAc,CAACnC,KAAK,EAAE,CAAC,EAAEA,KAAK,CAACwC,QAAQ,CAAC,CAAC,EAAEjB,WAAW,EAAEM,WAAW,EAAEE,QAAQ,EAAE1B,gBAAgB,EAAEC,gBAAgB,CAAC;QACvJ,IAAI4B,QAAQ,IAAI,IAAI,EAAE;UAClBY,kBAAkB,CAACT,WAAW,CAACN,QAAQ,EAAEG,QAAQ,CAAC;UAClD,IAAIX,WAAW,EAAE;YACbM,WAAW,GAAGK,QAAQ,CAACgD,SAAS,CAAC,CAAC;UACtC,CAAC,MACI;YACDrD,WAAW,GAAGK,QAAQ,CAACiD,OAAO,CAAC,CAAC;UACpC;QACJ;MACJ;IACJ;IACA,OAAOrC,kBAAkB;EAC7B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhD,qBAAqB,CAACsF,mBAAmB,GAAG,UAAU1E,eAAe,EAAE2E,aAAa,EAAE;IAClF,IAAIC,eAAe,GAAGD,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,IAAIE,iBAAiB,GAAGD,eAAe,CAACE,QAAQ,CAAC,CAAC;IAClD,IAAIC,2BAA2B,GAAG/E,eAAe,CAACW,qBAAqB,CAAC,CAAC,GACrEX,eAAe,CAACgF,kBAAkB,CAAC,CAAC,GACpC5F,qBAAqB,CAAC6F,sBAAsB,CAACjF,eAAe,CAACkF,iBAAiB,CAAC,CAAC,CAAC;IACrF,IAAIL,iBAAiB,CAAC7G,MAAM,KAAK,CAAC,EAAE;MAChC,IAAI+G,2BAA2B,GAAG,CAAC,IAAIA,2BAA2B,GAAGtG,YAAY,CAAC0G,wBAAwB,EAAE;QACxG,MAAM5G,iBAAiB,CAAC6B,mBAAmB,CAAC,CAAC;MACjD;MACAwE,eAAe,CAACQ,QAAQ,CAACL,2BAA2B,CAAC;IACzD,CAAC,MACI,IAAIF,iBAAiB,CAAC,CAAC,CAAC,KAAKE,2BAA2B,EAAE;MAC3D;MACAH,eAAe,CAACQ,QAAQ,CAACL,2BAA2B,CAAC;IACzD;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI3F,qBAAqB,CAAC4C,mBAAmB,GAAG,UAAUhC,eAAe,EAAE;IACnE,IAAI2E,aAAa,GAAGvF,qBAAqB,CAACiG,mBAAmB,CAACrF,eAAe,CAAC;IAC9EZ,qBAAqB,CAACsF,mBAAmB,CAAC1E,eAAe,EAAE2E,aAAa,CAAC;IACzE,IAAIW,QAAQ,CAAC,0BAA0B,IAAIC,KAAK,CAAC,CAAC;IAClD,IAAIrC,SAAS,GAAG,IAAIsC,UAAU,CAACxF,eAAe,CAACgF,kBAAkB,CAAC,CAAC,GAAGhF,eAAe,CAACW,qBAAqB,CAAC,CAAC,CAAC;IAC9G,IAAI8E,wBAAwB,GAAG,eAAgB,EAAE;IACjD,IAAIC,oBAAoB,GAAG,uBAAwB,IAAIH,KAAK,CAAC,CAAC;IAC9D,KAAK,IAAInC,GAAG,CAAC,UAAU,CAAC,EAAEA,GAAG,GAAGpD,eAAe,CAACgF,kBAAkB,CAAC,CAAC,EAAE5B,GAAG,EAAE,EAAE;MACzE,KAAK,IAAIuC,MAAM,CAAC,UAAU,CAAC,EAAEA,MAAM,GAAG3F,eAAe,CAACW,qBAAqB,CAAC,CAAC,EAAEgF,MAAM,EAAE,EAAE;QACrF,IAAInC,MAAM,GAAGmB,aAAa,CAACvB,GAAG,CAAC,CAACuC,MAAM,GAAG,CAAC,CAAC,CAACb,QAAQ,CAAC,CAAC;QACtD,IAAIc,aAAa,GAAGxC,GAAG,GAAGpD,eAAe,CAACW,qBAAqB,CAAC,CAAC,GAAGgF,MAAM;QAC1E,IAAInC,MAAM,CAACxF,MAAM,KAAK,CAAC,EAAE;UACrBsH,QAAQ,CAACO,IAAI,CAACD,aAAa,CAAC;QAChC,CAAC,MACI,IAAIpC,MAAM,CAACxF,MAAM,KAAK,CAAC,EAAE;UAC1BkF,SAAS,CAAC0C,aAAa,CAAC,GAAGpC,MAAM,CAAC,CAAC,CAAC;QACxC,CAAC,MACI;UACDkC,oBAAoB,CAACG,IAAI,CAACD,aAAa,CAAC;UACxCH,wBAAwB,CAACI,IAAI,CAACrC,MAAM,CAAC;QACzC;MACJ;IACJ;IACA,IAAIsC,oBAAoB,GAAG,IAAIP,KAAK,CAACE,wBAAwB,CAACzH,MAAM,CAAC;IACrE,KAAK,IAAIF,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGgI,oBAAoB,CAAC9H,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC1DgI,oBAAoB,CAAChI,CAAC,CAAC,GAAG2H,wBAAwB,CAAC3H,CAAC,CAAC;IACzD;IACA,OAAOsB,qBAAqB,CAAC2G,sCAAsC,CAAC/F,eAAe,CAACkF,iBAAiB,CAAC,CAAC,EAAEhC,SAAS,EAAEzE,YAAY,CAACuH,UAAU,CAACV,QAAQ,CAAC,EAAE7G,YAAY,CAACuH,UAAU,CAACN,oBAAoB,CAAC,EAAEI,oBAAoB,CAAC;EAC/N,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI1G,qBAAqB,CAAC2G,sCAAsC,GAAG,UAAUE,OAAO,EAAE/C,SAAS,EAAEgD,YAAY,EAAEC,gBAAgB,EAAEL,oBAAoB,EAAE;IAC/I,IAAIM,mBAAmB,GAAG,IAAIZ,UAAU,CAACW,gBAAgB,CAACnI,MAAM,CAAC;IACjE,IAAIqI,KAAK,GAAG,GAAG;IACf,OAAOA,KAAK,EAAE,GAAG,CAAC,EAAE;MAChB,KAAK,IAAIvI,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGsI,mBAAmB,CAACpI,MAAM,EAAEF,CAAC,EAAE,EAAE;QACzDoF,SAAS,CAACiD,gBAAgB,CAACrI,CAAC,CAAC,CAAC,GAAGgI,oBAAoB,CAAChI,CAAC,CAAC,CAACsI,mBAAmB,CAACtI,CAAC,CAAC,CAAC;MACpF;MACA,IAAI;QACA,OAAOsB,qBAAqB,CAACkH,eAAe,CAACpD,SAAS,EAAE+C,OAAO,EAAEC,YAAY,CAAC;MAClF,CAAC,CACD,OAAOK,GAAG,EAAE;QACR,IAAIC,OAAO,GAAGD,GAAG,YAAYlI,iBAAiB;QAC9C,IAAI,CAACmI,OAAO,EAAE;UACV,MAAMD,GAAG;QACb;MACJ;MACA,IAAIH,mBAAmB,CAACpI,MAAM,KAAK,CAAC,EAAE;QAClC,MAAMK,iBAAiB,CAACoI,mBAAmB,CAAC,CAAC;MACjD;MACA,KAAK,IAAI3I,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGsI,mBAAmB,CAACpI,MAAM,EAAEF,CAAC,EAAE,EAAE;QACzD,IAAIsI,mBAAmB,CAACtI,CAAC,CAAC,GAAGgI,oBAAoB,CAAChI,CAAC,CAAC,CAACE,MAAM,GAAG,CAAC,EAAE;UAC7DoI,mBAAmB,CAACtI,CAAC,CAAC,EAAE;UACxB;QACJ,CAAC,MACI;UACDsI,mBAAmB,CAACtI,CAAC,CAAC,GAAG,CAAC;UAC1B,IAAIA,CAAC,KAAKsI,mBAAmB,CAACpI,MAAM,GAAG,CAAC,EAAE;YACtC,MAAMK,iBAAiB,CAACoI,mBAAmB,CAAC,CAAC;UACjD;QACJ;MACJ;IACJ;IACA,MAAMpI,iBAAiB,CAACoI,mBAAmB,CAAC,CAAC;EACjD,CAAC;EACDrH,qBAAqB,CAACiG,mBAAmB,GAAG,UAAUrF,eAAe,EAAE;IACnE,IAAI0G,GAAG,EAAEpE,EAAE,EAAEqE,GAAG,EAAEC,EAAE;IACpB;IACA;IACA,IAAIjC,aAAa,GAAGY,KAAK,CAACsB,IAAI,CAAC;MAAE7I,MAAM,EAAEgC,eAAe,CAACgF,kBAAkB,CAAC;IAAE,CAAC,EAAE,YAAY;MAAE,OAAO,IAAIO,KAAK,CAACvF,eAAe,CAACW,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAE,CAAC,CAAC;IAChK,KAAK,IAAIyC,GAAG,CAAC,UAAU,CAAC,EAAEA,GAAG,GAAGuB,aAAa,CAAC3G,MAAM,EAAEoF,GAAG,EAAE,EAAE;MACzD,KAAK,IAAI0D,QAAQ,CAAC,UAAU,CAAC,EAAEA,QAAQ,GAAGnC,aAAa,CAACvB,GAAG,CAAC,CAACpF,MAAM,EAAE8I,QAAQ,EAAE,EAAE;QAC7EnC,aAAa,CAACvB,GAAG,CAAC,CAAC0D,QAAQ,CAAC,GAAG,IAAI9H,YAAY,CAAC,CAAC;MACrD;IACJ;IACA,IAAI2G,MAAM,GAAG,CAAC;IACd,IAAI;MACA,KAAK,IAAIoB,EAAE,GAAGvJ,QAAQ,CAACwC,eAAe,CAACgH,yBAAyB,CAAC,CAAC,CAAC,EAAEC,EAAE,GAAGF,EAAE,CAAC9I,IAAI,CAAC,CAAC,EAAE,CAACgJ,EAAE,CAAC9I,IAAI,EAAE8I,EAAE,GAAGF,EAAE,CAAC9I,IAAI,CAAC,CAAC,EAAE;QAC3G,IAAIiD,qBAAqB,GAAG+F,EAAE,CAAC/I,KAAK,CAAC;QACrC,IAAIgD,qBAAqB,IAAI,IAAI,EAAE;UAC/B,IAAI;YACA,KAAK,IAAIgG,EAAE,IAAIP,GAAG,GAAG,KAAK,CAAC,EAAEnJ,QAAQ,CAAC0D,qBAAqB,CAACiC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAEgE,EAAE,GAAGD,EAAE,CAACjJ,IAAI,CAAC,CAAC,EAAE,CAACkJ,EAAE,CAAChJ,IAAI,EAAEgJ,EAAE,GAAGD,EAAE,CAACjJ,IAAI,CAAC,CAAC,EAAE;cACpH,IAAIuD,QAAQ,GAAG2F,EAAE,CAACjJ,KAAK,CAAC;cACxB,IAAIsD,QAAQ,IAAI,IAAI,EAAE;gBAClB,IAAI4F,SAAS,GAAG5F,QAAQ,CAAC6F,YAAY,CAAC,CAAC;gBACvC,IAAID,SAAS,IAAI,CAAC,EAAE;kBAChB,IAAIA,SAAS,IAAIzC,aAAa,CAAC3G,MAAM,EAAE;oBACnC;oBACA;kBACJ;kBACA2G,aAAa,CAACyC,SAAS,CAAC,CAACzB,MAAM,CAAC,CAACP,QAAQ,CAAC5D,QAAQ,CAACsD,QAAQ,CAAC,CAAC,CAAC;gBAClE;cACJ;YACJ;UACJ,CAAC,CACD,OAAOwC,KAAK,EAAE;YAAEX,GAAG,GAAG;cAAE3D,KAAK,EAAEsE;YAAM,CAAC;UAAE,CAAC,SACjC;YACJ,IAAI;cACA,IAAIH,EAAE,IAAI,CAACA,EAAE,CAAChJ,IAAI,KAAKyI,EAAE,GAAGM,EAAE,CAACjE,MAAM,CAAC,EAAE2D,EAAE,CAAC7I,IAAI,CAACmJ,EAAE,CAAC;YACvD,CAAC,SACO;cAAE,IAAIP,GAAG,EAAE,MAAMA,GAAG,CAAC3D,KAAK;YAAE;UACxC;QACJ;QACA2C,MAAM,EAAE;MACZ;IACJ,CAAC,CACD,OAAO4B,KAAK,EAAE;MAAEb,GAAG,GAAG;QAAE1D,KAAK,EAAEuE;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIN,EAAE,IAAI,CAACA,EAAE,CAAC9I,IAAI,KAAKmE,EAAE,GAAGyE,EAAE,CAAC9D,MAAM,CAAC,EAAEX,EAAE,CAACvE,IAAI,CAACgJ,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIL,GAAG,EAAE,MAAMA,GAAG,CAAC1D,KAAK;MAAE;IACxC;IACA,OAAO2B,aAAa;EACxB,CAAC;EACDvF,qBAAqB,CAACoI,oBAAoB,GAAG,UAAUxH,eAAe,EAAEe,aAAa,EAAE;IACnF,OAAOA,aAAa,IAAI,CAAC,IAAIA,aAAa,IAAIf,eAAe,CAACW,qBAAqB,CAAC,CAAC,GAAG,CAAC;EAC7F,CAAC;EACDvB,qBAAqB,CAACkC,cAAc,GAAG,UAAUtB,eAAe,EAAEe,aAAa,EAAEM,QAAQ,EAAER,WAAW,EAAE;IACpG,IAAI4G,GAAG,EAAEnF,EAAE;IACX,IAAIoF,MAAM,GAAG7G,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IACjC,IAAIW,QAAQ,GAAG,IAAI;IACnB,IAAIpC,qBAAqB,CAACoI,oBAAoB,CAACxH,eAAe,EAAEe,aAAa,GAAG2G,MAAM,CAAC,EAAE;MACrFlG,QAAQ,GAAGxB,eAAe,CAACgB,wBAAwB,CAACD,aAAa,GAAG2G,MAAM,CAAC,CAACC,WAAW,CAACtG,QAAQ,CAAC;IACrG;IACA,IAAIG,QAAQ,IAAI,IAAI,EAAE;MAClB,OAAOX,WAAW,GAAGW,QAAQ,CAACiD,OAAO,CAAC,CAAC,GAAGjD,QAAQ,CAACgD,SAAS,CAAC,CAAC;IAClE;IACAhD,QAAQ,GAAGxB,eAAe,CAACgB,wBAAwB,CAACD,aAAa,CAAC,CAAC6G,iBAAiB,CAACvG,QAAQ,CAAC;IAC9F,IAAIG,QAAQ,IAAI,IAAI,EAAE;MAClB,OAAOX,WAAW,GAAGW,QAAQ,CAACgD,SAAS,CAAC,CAAC,GAAGhD,QAAQ,CAACiD,OAAO,CAAC,CAAC;IAClE;IACA,IAAIrF,qBAAqB,CAACoI,oBAAoB,CAACxH,eAAe,EAAEe,aAAa,GAAG2G,MAAM,CAAC,EAAE;MACrFlG,QAAQ,GAAGxB,eAAe,CAACgB,wBAAwB,CAACD,aAAa,GAAG2G,MAAM,CAAC,CAACE,iBAAiB,CAACvG,QAAQ,CAAC;IAC3G;IACA,IAAIG,QAAQ,IAAI,IAAI,EAAE;MAClB,OAAOX,WAAW,GAAGW,QAAQ,CAACiD,OAAO,CAAC,CAAC,GAAGjD,QAAQ,CAACgD,SAAS,CAAC,CAAC;IAClE;IACA,IAAIqD,cAAc,GAAG,CAAC;IACtB,OAAOzI,qBAAqB,CAACoI,oBAAoB,CAACxH,eAAe,EAAEe,aAAa,GAAG2G,MAAM,CAAC,EAAE;MACxF3G,aAAa,IAAI2G,MAAM;MACvB,IAAI;QACA,KAAK,IAAId,EAAE,IAAIa,GAAG,GAAG,KAAK,CAAC,EAAEjK,QAAQ,CAACwC,eAAe,CAACgB,wBAAwB,CAACD,aAAa,CAAC,CAACoC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE4D,EAAE,GAAGH,EAAE,CAAC3I,IAAI,CAAC,CAAC,EAAE,CAAC8I,EAAE,CAAC5I,IAAI,EAAE4I,EAAE,GAAGH,EAAE,CAAC3I,IAAI,CAAC,CAAC,EAAE;UACtJ,IAAI6J,mBAAmB,GAAGf,EAAE,CAAC7I,KAAK,CAAC;UACnC,IAAI4J,mBAAmB,IAAI,IAAI,EAAE;YAC7B,OAAO,CAACjH,WAAW,GAAGiH,mBAAmB,CAACrD,OAAO,CAAC,CAAC,GAAGqD,mBAAmB,CAACtD,SAAS,CAAC,CAAC,IACjFkD,MAAM,GACFG,cAAc,IACbC,mBAAmB,CAACrD,OAAO,CAAC,CAAC,GAAGqD,mBAAmB,CAACtD,SAAS,CAAC,CAAC,CAAC;UAC7E;QACJ;MACJ,CAAC,CACD,OAAOuD,KAAK,EAAE;QAAEN,GAAG,GAAG;UAAEzE,KAAK,EAAE+E;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIhB,EAAE,IAAI,CAACA,EAAE,CAAC5I,IAAI,KAAKmE,EAAE,GAAGsE,EAAE,CAAC3D,MAAM,CAAC,EAAEX,EAAE,CAACvE,IAAI,CAAC6I,EAAE,CAAC;QACvD,CAAC,SACO;UAAE,IAAIa,GAAG,EAAE,MAAMA,GAAG,CAACzE,KAAK;QAAE;MACxC;MACA6E,cAAc,EAAE;IACpB;IACA,OAAOhH,WAAW,GAAGb,eAAe,CAACM,cAAc,CAAC,CAAC,CAACoB,OAAO,CAAC,CAAC,GAAG1B,eAAe,CAACM,cAAc,CAAC,CAAC,CAACiB,OAAO,CAAC,CAAC;EAChH,CAAC;EACDnC,qBAAqB,CAACqC,cAAc,GAAG,UAAUnC,KAAK,EAAE0I,SAAS,EAAEC,SAAS,EAAEpH,WAAW,EAAEM,WAAW,EAAEE,QAAQ,EAAE1B,gBAAgB,EAAEC,gBAAgB,EAAE;IAClJuB,WAAW,GAAG/B,qBAAqB,CAAC8I,yBAAyB,CAAC5I,KAAK,EAAE0I,SAAS,EAAEC,SAAS,EAAEpH,WAAW,EAAEM,WAAW,EAAEE,QAAQ,CAAC;IAC9H;IACA;IACA;IACA;IACA,IAAI8G,cAAc,GAAG/I,qBAAqB,CAACgJ,iBAAiB,CAAC9I,KAAK,EAAE0I,SAAS,EAAEC,SAAS,EAAEpH,WAAW,EAAEM,WAAW,EAAEE,QAAQ,CAAC;IAC7H,IAAI8G,cAAc,IAAI,IAAI,EAAE;MACxB,OAAO,IAAI;IACf;IACA,IAAIE,SAAS;IACb,IAAIC,gBAAgB,GAAG9J,SAAS,CAAC+J,GAAG,CAACJ,cAAc,CAAC;IACpD,IAAItH,WAAW,EAAE;MACbwH,SAAS,GAAGlH,WAAW,GAAGmH,gBAAgB;IAC9C,CAAC,MACI;MACD,KAAK,IAAIxK,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGqK,cAAc,CAACnK,MAAM,GAAG,CAAC,EAAEF,CAAC,EAAE,EAAE;QACxD,IAAI0K,QAAQ,GAAGL,cAAc,CAACrK,CAAC,CAAC;QAChCqK,cAAc,CAACrK,CAAC,CAAC,GAAGqK,cAAc,CAACA,cAAc,CAACnK,MAAM,GAAG,CAAC,GAAGF,CAAC,CAAC;QACjEqK,cAAc,CAACA,cAAc,CAACnK,MAAM,GAAG,CAAC,GAAGF,CAAC,CAAC,GAAG0K,QAAQ;MAC5D;MACAH,SAAS,GAAGlH,WAAW;MACvBA,WAAW,GAAGkH,SAAS,GAAGC,gBAAgB;IAC9C;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAClJ,qBAAqB,CAACqJ,iBAAiB,CAACH,gBAAgB,EAAE3I,gBAAgB,EAAEC,gBAAgB,CAAC,EAAE;MAChG;MACA;MACA,OAAO,IAAI;IACf;IACA,IAAI8I,YAAY,GAAGzJ,qBAAqB,CAAC0J,eAAe,CAACR,cAAc,CAAC;IACxE,IAAI3G,QAAQ,GAAG/C,YAAY,CAACkJ,WAAW,CAACe,YAAY,CAAC;IACrD,IAAIlH,QAAQ,KAAK,CAAC,CAAC,EAAE;MACjB,OAAO,IAAI;IACf;IACA,OAAO,IAAIzC,QAAQ,CAACoC,WAAW,EAAEkH,SAAS,EAAEjJ,qBAAqB,CAACwJ,uBAAuB,CAACF,YAAY,CAAC,EAAElH,QAAQ,CAAC;EACtH,CAAC;EACDpC,qBAAqB,CAACgJ,iBAAiB,GAAG,UAAU9I,KAAK,EAAE0I,SAAS,EAAEC,SAAS,EAAEpH,WAAW,EAAEM,WAAW,EAAEE,QAAQ,EAAE;IACjH,IAAIwH,WAAW,GAAG1H,WAAW;IAC7B,IAAIgH,cAAc,GAAG,IAAI3C,UAAU,CAAC,CAAC,CAAC;IACtC,IAAIsD,YAAY,GAAG,CAAC;IACpB,IAAI1E,SAAS,GAAGvD,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IACpC,IAAIkI,kBAAkB,GAAGlI,WAAW;IACpC,OAAO,CAACA,WAAW,GAAGgI,WAAW,GAAGZ,SAAS,GAAGY,WAAW,IAAIb,SAAS,KACpEc,YAAY,GAAGX,cAAc,CAACnK,MAAM,EAAE;MACtC,IAAIsB,KAAK,CAAC0J,GAAG,CAACH,WAAW,EAAExH,QAAQ,CAAC,KAAK0H,kBAAkB,EAAE;QACzDZ,cAAc,CAACW,YAAY,CAAC,EAAE;QAC9BD,WAAW,IAAIzE,SAAS;MAC5B,CAAC,MACI;QACD0E,YAAY,EAAE;QACdC,kBAAkB,GAAG,CAACA,kBAAkB;MAC5C;IACJ;IACA,IAAID,YAAY,KAAKX,cAAc,CAACnK,MAAM,IACpC6K,WAAW,MAAMhI,WAAW,GAAGoH,SAAS,GAAGD,SAAS,CAAC,IACnDc,YAAY,KAAKX,cAAc,CAACnK,MAAM,GAAG,CAAE,EAAE;MACjD,OAAOmK,cAAc;IACzB;IACA,OAAO,IAAI;EACf,CAAC;EACD/I,qBAAqB,CAAC6F,sBAAsB,GAAG,UAAUgE,cAAc,EAAE;IACrE,OAAO,CAAC,IAAIA,cAAc;EAC9B,CAAC;EACD7J,qBAAqB,CAAC8I,yBAAyB,GAAG,UAAU5I,KAAK,EAAE0I,SAAS,EAAEC,SAAS,EAAEpH,WAAW,EAAEqI,mBAAmB,EAAE7H,QAAQ,EAAE;IACjI,IAAI8H,oBAAoB,GAAGD,mBAAmB;IAC9C,IAAI9E,SAAS,GAAGvD,WAAW,GAAG,CAAC,CAAC,GAAG,CAAC;IACpC;IACA,KAAK,IAAI/C,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAChC,OAAO,CAAC+C,WAAW,GAAGsI,oBAAoB,IAAInB,SAAS,GAAGmB,oBAAoB,GAAGlB,SAAS,KACtFpH,WAAW,KAAKvB,KAAK,CAAC0J,GAAG,CAACG,oBAAoB,EAAE9H,QAAQ,CAAC,EAAE;QAC3D,IAAIO,IAAI,CAACwH,GAAG,CAACF,mBAAmB,GAAGC,oBAAoB,CAAC,GAAG/J,qBAAqB,CAACiK,kBAAkB,EAAE;UACjG,OAAOH,mBAAmB;QAC9B;QACAC,oBAAoB,IAAI/E,SAAS;MACrC;MACAA,SAAS,GAAG,CAACA,SAAS;MACtBvD,WAAW,GAAG,CAACA,WAAW;IAC9B;IACA,OAAOsI,oBAAoB;EAC/B,CAAC;EACD/J,qBAAqB,CAACqJ,iBAAiB,GAAG,UAAUa,YAAY,EAAE3J,gBAAgB,EAAEC,gBAAgB,EAAE;IAClG,OAAOD,gBAAgB,GAAGP,qBAAqB,CAACiK,kBAAkB,IAAIC,YAAY,IAC9EA,YAAY,IAAI1J,gBAAgB,GAAGR,qBAAqB,CAACiK,kBAAkB;EACnF,CAAC;EACD;AACJ;AACA;AACA;EACIjK,qBAAqB,CAACkH,eAAe,GAAG,UAAUpD,SAAS,EAAE+C,OAAO,EAAEX,QAAQ,EAAE;IAC5E,IAAIpC,SAAS,CAAClF,MAAM,KAAK,CAAC,EAAE;MACxB,MAAMM,eAAe,CAACiL,iBAAiB,CAAC,CAAC;IAC7C;IACA,IAAIC,cAAc,GAAG,CAAC,IAAKvD,OAAO,GAAG,CAAE;IACvC,IAAIwD,oBAAoB,GAAGrK,qBAAqB,CAACsK,aAAa,CAACxG,SAAS,EAAEoC,QAAQ,EAAEkE,cAAc,CAAC;IACnGpK,qBAAqB,CAACuK,mBAAmB,CAACzG,SAAS,EAAEsG,cAAc,CAAC;IACpE;IACA,IAAII,aAAa,GAAG1K,sBAAsB,CAACG,MAAM,CAAC6D,SAAS,EAAE,EAAE,GAAG+C,OAAO,CAAC;IAC1E2D,aAAa,CAACC,kBAAkB,CAACJ,oBAAoB,CAAC;IACtDG,aAAa,CAACE,WAAW,CAACxE,QAAQ,CAACtH,MAAM,CAAC;IAC1C,OAAO4L,aAAa;EACxB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIxK,qBAAqB,CAACsK,aAAa,GAAG,UAAUxG,SAAS,EAAEoC,QAAQ,EAAEkE,cAAc,EAAE;IACjF,IAAIlE,QAAQ,IAAI,IAAI,IAChBA,QAAQ,CAACtH,MAAM,GAAGwL,cAAc,GAAG,CAAC,GAAGpK,qBAAqB,CAAC2K,UAAU,IACvEP,cAAc,GAAG,CAAC,IAClBA,cAAc,GAAGpK,qBAAqB,CAAC4K,gBAAgB,EAAE;MACzD;MACA,MAAM3L,iBAAiB,CAACoI,mBAAmB,CAAC,CAAC;IACjD;IACA,OAAOrH,qBAAqB,CAAC6K,eAAe,CAAC5K,MAAM,CAAC6D,SAAS,EAAEsG,cAAc,EAAElE,QAAQ,CAAC;EAC5F,CAAC;EACD;AACJ;AACA;AACA;EACIlG,qBAAqB,CAACuK,mBAAmB,GAAG,UAAUzG,SAAS,EAAEsG,cAAc,EAAE;IAC7E,IAAItG,SAAS,CAAClF,MAAM,GAAG,CAAC,EAAE;MACtB;MACA;MACA,MAAMM,eAAe,CAACiL,iBAAiB,CAAC,CAAC;IAC7C;IACA;IACA;IACA;IACA,IAAI1E,iBAAiB,GAAG3B,SAAS,CAAC,CAAC,CAAC;IACpC,IAAI2B,iBAAiB,GAAG3B,SAAS,CAAClF,MAAM,EAAE;MACtC,MAAMM,eAAe,CAACiL,iBAAiB,CAAC,CAAC;IAC7C;IACA,IAAI1E,iBAAiB,KAAK,CAAC,EAAE;MACzB;MACA,IAAI2E,cAAc,GAAGtG,SAAS,CAAClF,MAAM,EAAE;QACnCkF,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAAClF,MAAM,GAAGwL,cAAc;MACpD,CAAC,MACI;QACD,MAAMlL,eAAe,CAACiL,iBAAiB,CAAC,CAAC;MAC7C;IACJ;EACJ,CAAC;EACDnK,qBAAqB,CAAC8K,sBAAsB,GAAG,UAAU1I,QAAQ,EAAE;IAC/D,IAAI2I,MAAM,GAAG,IAAI3E,UAAU,CAAC,CAAC,CAAC;IAC9B,IAAI4E,aAAa,GAAG,CAAC;IACrB,IAAItM,CAAC,GAAGqM,MAAM,CAACnM,MAAM,GAAG,CAAC;IACzB,OAAO,IAAI,EAAE;MACT,IAAI,CAACwD,QAAQ,GAAG,GAAG,MAAM4I,aAAa,EAAE;QACpCA,aAAa,GAAG5I,QAAQ,GAAG,GAAG;QAC9B1D,CAAC,EAAE;QACH,IAAIA,CAAC,GAAG,CAAC,EAAE;UACP;QACJ;MACJ;MACAqM,MAAM,CAACrM,CAAC,CAAC,EAAE;MACX0D,QAAQ,KAAK,CAAC;IAClB;IACA,OAAO2I,MAAM;EACjB,CAAC;EACD/K,qBAAqB,CAACwJ,uBAAuB,GAAG,UAAUpH,QAAQ,EAAE;IAChE,IAAIA,QAAQ,YAAYgE,UAAU,EAAE;MAChC,OAAO,IAAI,CAAC6E,kCAAkC,CAAC7I,QAAQ,CAAC;IAC5D;IACA,OAAO,IAAI,CAAC8I,8BAA8B,CAAC9I,QAAQ,CAAC;EACxD,CAAC;EACDpC,qBAAqB,CAACkL,8BAA8B,GAAG,UAAU9I,QAAQ,EAAE;IACvE,OAAOpC,qBAAqB,CAACwJ,uBAAuB,CAACxJ,qBAAqB,CAAC8K,sBAAsB,CAAC1I,QAAQ,CAAC,CAAC;EAChH,CAAC;EACDpC,qBAAqB,CAACiL,kCAAkC,GAAG,UAAUlC,cAAc,EAAE;IACjF,OAAO,CAACA,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAGA,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC;EAClG,CAAC;EACD/I,qBAAqB,CAACmL,QAAQ,GAAG,UAAU5F,aAAa,EAAE;IACtD,IAAI6F,SAAS,GAAG,IAAIrL,SAAS,CAAC,CAAC;IAC/B;IACA,KAAK,IAAIiE,GAAG,CAAC,UAAU,CAAC,EAAEA,GAAG,GAAGuB,aAAa,CAAC3G,MAAM,EAAEoF,GAAG,EAAE,EAAE;MACzDoH,SAAS,CAACC,MAAM,CAAC,WAAW,EAAErH,GAAG,CAAC;MAClC,KAAK,IAAIuC,MAAM,CAAC,UAAU,CAAC,EAAEA,MAAM,GAAGhB,aAAa,CAACvB,GAAG,CAAC,CAACpF,MAAM,EAAE2H,MAAM,EAAE,EAAE;QACvE,IAAI+E,YAAY,GAAG/F,aAAa,CAACvB,GAAG,CAAC,CAACuC,MAAM,CAAC;QAC7C,IAAI+E,YAAY,CAAC5F,QAAQ,CAAC,CAAC,CAAC9G,MAAM,KAAK,CAAC,EAAE;UACtCwM,SAAS,CAACC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC;QACtC,CAAC,MACI;UACDD,SAAS,CAACC,MAAM,CAAC,UAAU,EAAEC,YAAY,CAAC5F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE4F,YAAY,CAACC,aAAa,CAACD,YAAY,CAAC5F,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpH;MACJ;MACA0F,SAAS,CAACC,MAAM,CAAC,IAAI,CAAC;IAC1B;IACA,OAAOD,SAAS,CAACD,QAAQ,CAAC,CAAC;IAC3B;EACJ,CAAC;EACD;EAAUnL,qBAAqB,CAACiK,kBAAkB,GAAG,CAAC;EACtD;EAAUjK,qBAAqB,CAAC2K,UAAU,GAAG,CAAC;EAC9C;EAAU3K,qBAAqB,CAAC4K,gBAAgB,GAAG,GAAG;EACtD;EAAU5K,qBAAqB,CAAC6K,eAAe,GAAG,IAAIvL,eAAe,CAAC,CAAC;EACvE,OAAOU,qBAAqB;AAChC,CAAC,CAAC,CAAE;AACJ,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}