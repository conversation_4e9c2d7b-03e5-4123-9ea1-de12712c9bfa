{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../../../BarcodeFormat';\nimport MathUtils from '../../../common/detector/MathUtils';\n// import FormatException from '../../../FormatException';\nimport NotFoundException from '../../../NotFoundException';\nimport Result from '../../../Result';\nimport System from '../../../util/System';\nimport AbstractRSSReader from '../../rss/AbstractRSSReader';\nimport DataCharacter from '../../rss/DataCharacter';\nimport FinderPattern from '../../rss/FinderPattern';\nimport RSSUtils from '../../rss/RSSUtils';\nimport BitArrayBuilder from './BitArrayBuilder';\nimport { createDecoder } from './decoders/AbstractExpandedDecoderComplement';\nimport ExpandedPair from './ExpandedPair';\nimport ExpandedRow from './ExpandedRow';\n// import java.util.ArrayList;\n// import java.util.Iterator;\n// import java.util.List;\n// import java.util.Map;\n// import java.util.Collections;\n/** @experimental */\nvar RSSExpandedReader = /** @class */function (_super) {\n  __extends(RSSExpandedReader, _super);\n  function RSSExpandedReader() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.pairs = new Array(RSSExpandedReader.MAX_PAIRS);\n    _this.rows = new Array();\n    _this.startEnd = [2];\n    return _this;\n  }\n  RSSExpandedReader.prototype.decodeRow = function (rowNumber, row, hints) {\n    // Rows can start with even pattern in case in prev rows there where odd number of patters.\n    // So lets try twice\n    // this.pairs.clear();\n    this.pairs.length = 0;\n    this.startFromEven = false;\n    try {\n      return RSSExpandedReader.constructResult(this.decodeRow2pairs(rowNumber, row));\n    } catch (e) {\n      // OK\n      // console.log(e);\n    }\n    this.pairs.length = 0;\n    this.startFromEven = true;\n    return RSSExpandedReader.constructResult(this.decodeRow2pairs(rowNumber, row));\n  };\n  RSSExpandedReader.prototype.reset = function () {\n    this.pairs.length = 0;\n    this.rows.length = 0;\n  };\n  // Not private for testing\n  RSSExpandedReader.prototype.decodeRow2pairs = function (rowNumber, row) {\n    var done = false;\n    while (!done) {\n      try {\n        this.pairs.push(this.retrieveNextPair(row, this.pairs, rowNumber));\n      } catch (error) {\n        if (error instanceof NotFoundException) {\n          if (!this.pairs.length) {\n            throw new NotFoundException();\n          }\n          // exit this loop when retrieveNextPair() fails and throws\n          done = true;\n        }\n      }\n    }\n    // TODO: verify sequence of finder patterns as in checkPairSequence()\n    if (this.checkChecksum()) {\n      return this.pairs;\n    }\n    var tryStackedDecode;\n    if (this.rows.length) {\n      tryStackedDecode = true;\n    } else {\n      tryStackedDecode = false;\n    }\n    // let tryStackedDecode = !this.rows.isEmpty();\n    this.storeRow(rowNumber, false); // TODO: deal with reversed rows\n    if (tryStackedDecode) {\n      // When the image is 180-rotated, then rows are sorted in wrong direction.\n      // Try twice with both the directions.\n      var ps = this.checkRowsBoolean(false);\n      if (ps != null) {\n        return ps;\n      }\n      ps = this.checkRowsBoolean(true);\n      if (ps != null) {\n        return ps;\n      }\n    }\n    throw new NotFoundException();\n  };\n  // Need to Verify\n  RSSExpandedReader.prototype.checkRowsBoolean = function (reverse) {\n    // Limit number of rows we are checking\n    // We use recursive algorithm with pure complexity and don't want it to take forever\n    // Stacked barcode can have up to 11 rows, so 25 seems reasonable enough\n    if (this.rows.length > 25) {\n      this.rows.length = 0; // We will never have a chance to get result, so clear it\n      return null;\n    }\n    this.pairs.length = 0;\n    if (reverse) {\n      this.rows = this.rows.reverse();\n      // Collections.reverse(this.rows);\n    }\n    var ps = null;\n    try {\n      ps = this.checkRows(new Array(), 0);\n    } catch (e) {\n      // OK\n      console.log(e);\n    }\n    if (reverse) {\n      this.rows = this.rows.reverse();\n      // Collections.reverse(this.rows);\n    }\n    return ps;\n  };\n  // Try to construct a valid rows sequence\n  // Recursion is used to implement backtracking\n  RSSExpandedReader.prototype.checkRows = function (collectedRows, currentRow) {\n    var e_1, _a;\n    for (var i = currentRow; i < this.rows.length; i++) {\n      var row = this.rows[i];\n      this.pairs.length = 0;\n      try {\n        for (var collectedRows_1 = (e_1 = void 0, __values(collectedRows)), collectedRows_1_1 = collectedRows_1.next(); !collectedRows_1_1.done; collectedRows_1_1 = collectedRows_1.next()) {\n          var collectedRow = collectedRows_1_1.value;\n          this.pairs.push(collectedRow.getPairs());\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (collectedRows_1_1 && !collectedRows_1_1.done && (_a = collectedRows_1.return)) _a.call(collectedRows_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      this.pairs.push(row.getPairs());\n      if (!RSSExpandedReader.isValidSequence(this.pairs)) {\n        continue;\n      }\n      if (this.checkChecksum()) {\n        return this.pairs;\n      }\n      var rs = new Array(collectedRows);\n      rs.push(row);\n      try {\n        // Recursion: try to add more rows\n        return this.checkRows(rs, i + 1);\n      } catch (e) {\n        // We failed, try the next candidate\n        console.log(e);\n      }\n    }\n    throw new NotFoundException();\n  };\n  // Whether the pairs form a valid find pattern sequence,\n  // either complete or a prefix\n  RSSExpandedReader.isValidSequence = function (pairs) {\n    var e_2, _a;\n    try {\n      for (var _b = __values(RSSExpandedReader.FINDER_PATTERN_SEQUENCES), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var sequence = _c.value;\n        if (pairs.length > sequence.length) {\n          continue;\n        }\n        var stop_1 = true;\n        for (var j = 0; j < pairs.length; j++) {\n          if (pairs[j].getFinderPattern().getValue() !== sequence[j]) {\n            stop_1 = false;\n            break;\n          }\n        }\n        if (stop_1) {\n          return true;\n        }\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return false;\n  };\n  RSSExpandedReader.prototype.storeRow = function (rowNumber, wasReversed) {\n    // Discard if duplicate above or below; otherwise insert in order by row number.\n    var insertPos = 0;\n    var prevIsSame = false;\n    var nextIsSame = false;\n    while (insertPos < this.rows.length) {\n      var erow = this.rows[insertPos];\n      if (erow.getRowNumber() > rowNumber) {\n        nextIsSame = erow.isEquivalent(this.pairs);\n        break;\n      }\n      prevIsSame = erow.isEquivalent(this.pairs);\n      insertPos++;\n    }\n    if (nextIsSame || prevIsSame) {\n      return;\n    }\n    // When the row was partially decoded (e.g. 2 pairs found instead of 3),\n    // it will prevent us from detecting the barcode.\n    // Try to merge partial rows\n    // Check whether the row is part of an allready detected row\n    if (RSSExpandedReader.isPartialRow(this.pairs, this.rows)) {\n      return;\n    }\n    this.rows.push(insertPos, new ExpandedRow(this.pairs, rowNumber, wasReversed));\n    this.removePartialRows(this.pairs, this.rows);\n  };\n  // Remove all the rows that contains only specified pairs\n  RSSExpandedReader.prototype.removePartialRows = function (pairs, rows) {\n    var e_3, _a, e_4, _b, e_5, _c;\n    try {\n      // for (Iterator<ExpandedRow> iterator = rows.iterator(); iterator.hasNext();) {\n      //   ExpandedRow r = iterator.next();\n      //   if (r.getPairs().size() == pairs.size()) {\n      //     continue;\n      //   }\n      //   boolean allFound = true;\n      //   for (ExpandedPair p : r.getPairs()) {\n      //     boolean found = false;\n      //     for (ExpandedPair pp : pairs) {\n      //       if (p.equals(pp)) {\n      //         found = true;\n      //         break;\n      //       }\n      //     }\n      //     if (!found) {\n      //       allFound = false;\n      //       break;\n      //     }\n      //   }\n      //   if (allFound) {\n      //     // 'pairs' contains all the pairs from the row 'r'\n      //     iterator.remove();\n      //   }\n      // }\n      for (var rows_1 = __values(rows), rows_1_1 = rows_1.next(); !rows_1_1.done; rows_1_1 = rows_1.next()) {\n        var row = rows_1_1.value;\n        if (row.getPairs().length === pairs.length) {\n          continue;\n        }\n        var allFound = true;\n        try {\n          for (var _d = (e_4 = void 0, __values(row.getPairs())), _e = _d.next(); !_e.done; _e = _d.next()) {\n            var p = _e.value;\n            var found = false;\n            try {\n              for (var pairs_1 = (e_5 = void 0, __values(pairs)), pairs_1_1 = pairs_1.next(); !pairs_1_1.done; pairs_1_1 = pairs_1.next()) {\n                var pp = pairs_1_1.value;\n                if (ExpandedPair.equals(p, pp)) {\n                  found = true;\n                  break;\n                }\n              }\n            } catch (e_5_1) {\n              e_5 = {\n                error: e_5_1\n              };\n            } finally {\n              try {\n                if (pairs_1_1 && !pairs_1_1.done && (_c = pairs_1.return)) _c.call(pairs_1);\n              } finally {\n                if (e_5) throw e_5.error;\n              }\n            }\n            if (!found) {\n              allFound = false;\n            }\n          }\n        } catch (e_4_1) {\n          e_4 = {\n            error: e_4_1\n          };\n        } finally {\n          try {\n            if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n          } finally {\n            if (e_4) throw e_4.error;\n          }\n        }\n      }\n    } catch (e_3_1) {\n      e_3 = {\n        error: e_3_1\n      };\n    } finally {\n      try {\n        if (rows_1_1 && !rows_1_1.done && (_a = rows_1.return)) _a.call(rows_1);\n      } finally {\n        if (e_3) throw e_3.error;\n      }\n    }\n  };\n  // Returns true when one of the rows already contains all the pairs\n  RSSExpandedReader.isPartialRow = function (pairs, rows) {\n    var e_6, _a, e_7, _b, e_8, _c;\n    try {\n      for (var rows_2 = __values(rows), rows_2_1 = rows_2.next(); !rows_2_1.done; rows_2_1 = rows_2.next()) {\n        var r = rows_2_1.value;\n        var allFound = true;\n        try {\n          for (var pairs_2 = (e_7 = void 0, __values(pairs)), pairs_2_1 = pairs_2.next(); !pairs_2_1.done; pairs_2_1 = pairs_2.next()) {\n            var p = pairs_2_1.value;\n            var found = false;\n            try {\n              for (var _d = (e_8 = void 0, __values(r.getPairs())), _e = _d.next(); !_e.done; _e = _d.next()) {\n                var pp = _e.value;\n                if (p.equals(pp)) {\n                  found = true;\n                  break;\n                }\n              }\n            } catch (e_8_1) {\n              e_8 = {\n                error: e_8_1\n              };\n            } finally {\n              try {\n                if (_e && !_e.done && (_c = _d.return)) _c.call(_d);\n              } finally {\n                if (e_8) throw e_8.error;\n              }\n            }\n            if (!found) {\n              allFound = false;\n              break;\n            }\n          }\n        } catch (e_7_1) {\n          e_7 = {\n            error: e_7_1\n          };\n        } finally {\n          try {\n            if (pairs_2_1 && !pairs_2_1.done && (_b = pairs_2.return)) _b.call(pairs_2);\n          } finally {\n            if (e_7) throw e_7.error;\n          }\n        }\n        if (allFound) {\n          // the row 'r' contain all the pairs from 'pairs'\n          return true;\n        }\n      }\n    } catch (e_6_1) {\n      e_6 = {\n        error: e_6_1\n      };\n    } finally {\n      try {\n        if (rows_2_1 && !rows_2_1.done && (_a = rows_2.return)) _a.call(rows_2);\n      } finally {\n        if (e_6) throw e_6.error;\n      }\n    }\n    return false;\n  };\n  // Only used for unit testing\n  RSSExpandedReader.prototype.getRows = function () {\n    return this.rows;\n  };\n  // Not private for unit testing\n  RSSExpandedReader.constructResult = function (pairs) {\n    var binary = BitArrayBuilder.buildBitArray(pairs);\n    var decoder = createDecoder(binary);\n    var resultingString = decoder.parseInformation();\n    var firstPoints = pairs[0].getFinderPattern().getResultPoints();\n    var lastPoints = pairs[pairs.length - 1].getFinderPattern().getResultPoints();\n    var points = [firstPoints[0], firstPoints[1], lastPoints[0], lastPoints[1]];\n    return new Result(resultingString, null, null, points, BarcodeFormat.RSS_EXPANDED, null);\n  };\n  RSSExpandedReader.prototype.checkChecksum = function () {\n    var firstPair = this.pairs.get(0);\n    var checkCharacter = firstPair.getLeftChar();\n    var firstCharacter = firstPair.getRightChar();\n    if (firstCharacter === null) {\n      return false;\n    }\n    var checksum = firstCharacter.getChecksumPortion();\n    var s = 2;\n    for (var i = 1; i < this.pairs.size(); ++i) {\n      var currentPair = this.pairs.get(i);\n      checksum += currentPair.getLeftChar().getChecksumPortion();\n      s++;\n      var currentRightChar = currentPair.getRightChar();\n      if (currentRightChar != null) {\n        checksum += currentRightChar.getChecksumPortion();\n        s++;\n      }\n    }\n    checksum %= 211;\n    var checkCharacterValue = 211 * (s - 4) + checksum;\n    return checkCharacterValue === checkCharacter.getValue();\n  };\n  RSSExpandedReader.getNextSecondBar = function (row, initialPos) {\n    var currentPos;\n    if (row.get(initialPos)) {\n      currentPos = row.getNextUnset(initialPos);\n      currentPos = row.getNextSet(currentPos);\n    } else {\n      currentPos = row.getNextSet(initialPos);\n      currentPos = row.getNextUnset(currentPos);\n    }\n    return currentPos;\n  };\n  // not private for testing\n  RSSExpandedReader.prototype.retrieveNextPair = function (row, previousPairs, rowNumber) {\n    var isOddPattern = previousPairs.length % 2 === 0;\n    if (this.startFromEven) {\n      isOddPattern = !isOddPattern;\n    }\n    var pattern;\n    var keepFinding = true;\n    var forcedOffset = -1;\n    do {\n      this.findNextPair(row, previousPairs, forcedOffset);\n      pattern = this.parseFoundFinderPattern(row, rowNumber, isOddPattern);\n      if (pattern === null) {\n        forcedOffset = RSSExpandedReader.getNextSecondBar(row, this.startEnd[0]);\n      } else {\n        keepFinding = false;\n      }\n    } while (keepFinding);\n    // When stacked symbol is split over multiple rows, there's no way to guess if this pair can be last or not.\n    // boolean mayBeLast = checkPairSequence(previousPairs, pattern);\n    var leftChar = this.decodeDataCharacter(row, pattern, isOddPattern, true);\n    if (!this.isEmptyPair(previousPairs) && previousPairs[previousPairs.length - 1].mustBeLast()) {\n      throw new NotFoundException();\n    }\n    var rightChar;\n    try {\n      rightChar = this.decodeDataCharacter(row, pattern, isOddPattern, false);\n    } catch (e) {\n      rightChar = null;\n      console.log(e);\n    }\n    return new ExpandedPair(leftChar, rightChar, pattern, true);\n  };\n  RSSExpandedReader.prototype.isEmptyPair = function (pairs) {\n    if (pairs.length === 0) {\n      return true;\n    }\n    return false;\n  };\n  RSSExpandedReader.prototype.findNextPair = function (row, previousPairs, forcedOffset) {\n    var counters = this.getDecodeFinderCounters();\n    counters[0] = 0;\n    counters[1] = 0;\n    counters[2] = 0;\n    counters[3] = 0;\n    var width = row.getSize();\n    var rowOffset;\n    if (forcedOffset >= 0) {\n      rowOffset = forcedOffset;\n    } else if (this.isEmptyPair(previousPairs)) {\n      rowOffset = 0;\n    } else {\n      var lastPair = previousPairs[previousPairs.length - 1];\n      rowOffset = lastPair.getFinderPattern().getStartEnd()[1];\n    }\n    var searchingEvenPair = previousPairs.length % 2 !== 0;\n    if (this.startFromEven) {\n      searchingEvenPair = !searchingEvenPair;\n    }\n    var isWhite = false;\n    while (rowOffset < width) {\n      isWhite = !row.get(rowOffset);\n      if (!isWhite) {\n        break;\n      }\n      rowOffset++;\n    }\n    var counterPosition = 0;\n    var patternStart = rowOffset;\n    for (var x = rowOffset; x < width; x++) {\n      if (row.get(x) !== isWhite) {\n        counters[counterPosition]++;\n      } else {\n        if (counterPosition === 3) {\n          if (searchingEvenPair) {\n            RSSExpandedReader.reverseCounters(counters);\n          }\n          if (RSSExpandedReader.isFinderPattern(counters)) {\n            this.startEnd[0] = patternStart;\n            this.startEnd[1] = x;\n            return;\n          }\n          if (searchingEvenPair) {\n            RSSExpandedReader.reverseCounters(counters);\n          }\n          patternStart += counters[0] + counters[1];\n          counters[0] = counters[2];\n          counters[1] = counters[3];\n          counters[2] = 0;\n          counters[3] = 0;\n          counterPosition--;\n        } else {\n          counterPosition++;\n        }\n        counters[counterPosition] = 1;\n        isWhite = !isWhite;\n      }\n    }\n    throw new NotFoundException();\n  };\n  RSSExpandedReader.reverseCounters = function (counters) {\n    var length = counters.length;\n    for (var i = 0; i < length / 2; ++i) {\n      var tmp = counters[i];\n      counters[i] = counters[length - i - 1];\n      counters[length - i - 1] = tmp;\n    }\n  };\n  RSSExpandedReader.prototype.parseFoundFinderPattern = function (row, rowNumber, oddPattern) {\n    // Actually we found elements 2-5.\n    var firstCounter;\n    var start;\n    var end;\n    if (oddPattern) {\n      // If pattern number is odd, we need to locate element 1 *before* the current block.\n      var firstElementStart = this.startEnd[0] - 1;\n      // Locate element 1\n      while (firstElementStart >= 0 && !row.get(firstElementStart)) {\n        firstElementStart--;\n      }\n      firstElementStart++;\n      firstCounter = this.startEnd[0] - firstElementStart;\n      start = firstElementStart;\n      end = this.startEnd[1];\n    } else {\n      // If pattern number is even, the pattern is reversed, so we need to locate element 1 *after* the current block.\n      start = this.startEnd[0];\n      end = row.getNextUnset(this.startEnd[1] + 1);\n      firstCounter = end - this.startEnd[1];\n    }\n    // Make 'counters' hold 1-4\n    var counters = this.getDecodeFinderCounters();\n    System.arraycopy(counters, 0, counters, 1, counters.length - 1);\n    counters[0] = firstCounter;\n    var value;\n    try {\n      value = this.parseFinderValue(counters, RSSExpandedReader.FINDER_PATTERNS);\n    } catch (e) {\n      return null;\n    }\n    // return new FinderPattern(value, new int[] { start, end }, start, end, rowNumber});\n    return new FinderPattern(value, [start, end], start, end, rowNumber);\n  };\n  RSSExpandedReader.prototype.decodeDataCharacter = function (row, pattern, isOddPattern, leftChar) {\n    var counters = this.getDataCharacterCounters();\n    for (var x = 0; x < counters.length; x++) {\n      counters[x] = 0;\n    }\n    if (leftChar) {\n      RSSExpandedReader.recordPatternInReverse(row, pattern.getStartEnd()[0], counters);\n    } else {\n      RSSExpandedReader.recordPattern(row, pattern.getStartEnd()[1], counters);\n      // reverse it\n      for (var i = 0, j = counters.length - 1; i < j; i++, j--) {\n        var temp = counters[i];\n        counters[i] = counters[j];\n        counters[j] = temp;\n      }\n    } // counters[] has the pixels of the module\n    var numModules = 17; // left and right data characters have all the same length\n    var elementWidth = MathUtils.sum(new Int32Array(counters)) / numModules;\n    // Sanity check: element width for pattern and the character should match\n    var expectedElementWidth = (pattern.getStartEnd()[1] - pattern.getStartEnd()[0]) / 15.0;\n    if (Math.abs(elementWidth - expectedElementWidth) / expectedElementWidth > 0.3) {\n      throw new NotFoundException();\n    }\n    var oddCounts = this.getOddCounts();\n    var evenCounts = this.getEvenCounts();\n    var oddRoundingErrors = this.getOddRoundingErrors();\n    var evenRoundingErrors = this.getEvenRoundingErrors();\n    for (var i = 0; i < counters.length; i++) {\n      var value_1 = 1.0 * counters[i] / elementWidth;\n      var count = value_1 + 0.5; // Round\n      if (count < 1) {\n        if (value_1 < 0.3) {\n          throw new NotFoundException();\n        }\n        count = 1;\n      } else if (count > 8) {\n        if (value_1 > 8.7) {\n          throw new NotFoundException();\n        }\n        count = 8;\n      }\n      var offset = i / 2;\n      if ((i & 0x01) === 0) {\n        oddCounts[offset] = count;\n        oddRoundingErrors[offset] = value_1 - count;\n      } else {\n        evenCounts[offset] = count;\n        evenRoundingErrors[offset] = value_1 - count;\n      }\n    }\n    this.adjustOddEvenCounts(numModules);\n    var weightRowNumber = 4 * pattern.getValue() + (isOddPattern ? 0 : 2) + (leftChar ? 0 : 1) - 1;\n    var oddSum = 0;\n    var oddChecksumPortion = 0;\n    for (var i = oddCounts.length - 1; i >= 0; i--) {\n      if (RSSExpandedReader.isNotA1left(pattern, isOddPattern, leftChar)) {\n        var weight = RSSExpandedReader.WEIGHTS[weightRowNumber][2 * i];\n        oddChecksumPortion += oddCounts[i] * weight;\n      }\n      oddSum += oddCounts[i];\n    }\n    var evenChecksumPortion = 0;\n    // int evenSum = 0;\n    for (var i = evenCounts.length - 1; i >= 0; i--) {\n      if (RSSExpandedReader.isNotA1left(pattern, isOddPattern, leftChar)) {\n        var weight = RSSExpandedReader.WEIGHTS[weightRowNumber][2 * i + 1];\n        evenChecksumPortion += evenCounts[i] * weight;\n      }\n      // evenSum += evenCounts[i];\n    }\n    var checksumPortion = oddChecksumPortion + evenChecksumPortion;\n    if ((oddSum & 0x01) !== 0 || oddSum > 13 || oddSum < 4) {\n      throw new NotFoundException();\n    }\n    var group = (13 - oddSum) / 2;\n    var oddWidest = RSSExpandedReader.SYMBOL_WIDEST[group];\n    var evenWidest = 9 - oddWidest;\n    var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, true);\n    var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, false);\n    var tEven = RSSExpandedReader.EVEN_TOTAL_SUBSET[group];\n    var gSum = RSSExpandedReader.GSUM[group];\n    var value = vOdd * tEven + vEven + gSum;\n    return new DataCharacter(value, checksumPortion);\n  };\n  RSSExpandedReader.isNotA1left = function (pattern, isOddPattern, leftChar) {\n    // A1: pattern.getValue is 0 (A), and it's an oddPattern, and it is a left char\n    return !(pattern.getValue() === 0 && isOddPattern && leftChar);\n  };\n  RSSExpandedReader.prototype.adjustOddEvenCounts = function (numModules) {\n    var oddSum = MathUtils.sum(new Int32Array(this.getOddCounts()));\n    var evenSum = MathUtils.sum(new Int32Array(this.getEvenCounts()));\n    var incrementOdd = false;\n    var decrementOdd = false;\n    if (oddSum > 13) {\n      decrementOdd = true;\n    } else if (oddSum < 4) {\n      incrementOdd = true;\n    }\n    var incrementEven = false;\n    var decrementEven = false;\n    if (evenSum > 13) {\n      decrementEven = true;\n    } else if (evenSum < 4) {\n      incrementEven = true;\n    }\n    var mismatch = oddSum + evenSum - numModules;\n    var oddParityBad = (oddSum & 0x01) === 1;\n    var evenParityBad = (evenSum & 0x01) === 0;\n    if (mismatch === 1) {\n      if (oddParityBad) {\n        if (evenParityBad) {\n          throw new NotFoundException();\n        }\n        decrementOdd = true;\n      } else {\n        if (!evenParityBad) {\n          throw new NotFoundException();\n        }\n        decrementEven = true;\n      }\n    } else if (mismatch === -1) {\n      if (oddParityBad) {\n        if (evenParityBad) {\n          throw new NotFoundException();\n        }\n        incrementOdd = true;\n      } else {\n        if (!evenParityBad) {\n          throw new NotFoundException();\n        }\n        incrementEven = true;\n      }\n    } else if (mismatch === 0) {\n      if (oddParityBad) {\n        if (!evenParityBad) {\n          throw new NotFoundException();\n        }\n        // Both bad\n        if (oddSum < evenSum) {\n          incrementOdd = true;\n          decrementEven = true;\n        } else {\n          decrementOdd = true;\n          incrementEven = true;\n        }\n      } else {\n        if (evenParityBad) {\n          throw new NotFoundException();\n        }\n        // Nothing to do!\n      }\n    } else {\n      throw new NotFoundException();\n    }\n    if (incrementOdd) {\n      if (decrementOdd) {\n        throw new NotFoundException();\n      }\n      RSSExpandedReader.increment(this.getOddCounts(), this.getOddRoundingErrors());\n    }\n    if (decrementOdd) {\n      RSSExpandedReader.decrement(this.getOddCounts(), this.getOddRoundingErrors());\n    }\n    if (incrementEven) {\n      if (decrementEven) {\n        throw new NotFoundException();\n      }\n      RSSExpandedReader.increment(this.getEvenCounts(), this.getOddRoundingErrors());\n    }\n    if (decrementEven) {\n      RSSExpandedReader.decrement(this.getEvenCounts(), this.getEvenRoundingErrors());\n    }\n  };\n  RSSExpandedReader.SYMBOL_WIDEST = [7, 5, 4, 3, 1];\n  RSSExpandedReader.EVEN_TOTAL_SUBSET = [4, 20, 52, 104, 204];\n  RSSExpandedReader.GSUM = [0, 348, 1388, 2948, 3988];\n  RSSExpandedReader.FINDER_PATTERNS = [Int32Array.from([1, 8, 4, 1]), Int32Array.from([3, 6, 4, 1]), Int32Array.from([3, 4, 6, 1]), Int32Array.from([3, 2, 8, 1]), Int32Array.from([2, 6, 5, 1]), Int32Array.from([2, 2, 9, 1])];\n  RSSExpandedReader.WEIGHTS = [[1, 3, 9, 27, 81, 32, 96, 77], [20, 60, 180, 118, 143, 7, 21, 63], [189, 145, 13, 39, 117, 140, 209, 205], [193, 157, 49, 147, 19, 57, 171, 91], [62, 186, 136, 197, 169, 85, 44, 132], [185, 133, 188, 142, 4, 12, 36, 108], [113, 128, 173, 97, 80, 29, 87, 50], [150, 28, 84, 41, 123, 158, 52, 156], [46, 138, 203, 187, 139, 206, 196, 166], [76, 17, 51, 153, 37, 111, 122, 155], [43, 129, 176, 106, 107, 110, 119, 146], [16, 48, 144, 10, 30, 90, 59, 177], [109, 116, 137, 200, 178, 112, 125, 164], [70, 210, 208, 202, 184, 130, 179, 115], [134, 191, 151, 31, 93, 68, 204, 190], [148, 22, 66, 198, 172, 94, 71, 2], [6, 18, 54, 162, 64, 192, 154, 40], [120, 149, 25, 75, 14, 42, 126, 167], [79, 26, 78, 23, 69, 207, 199, 175], [103, 98, 83, 38, 114, 131, 182, 124], [161, 61, 183, 127, 170, 88, 53, 159], [55, 165, 73, 8, 24, 72, 5, 15], [45, 135, 194, 160, 58, 174, 100, 89]];\n  RSSExpandedReader.FINDER_PAT_A = 0;\n  RSSExpandedReader.FINDER_PAT_B = 1;\n  RSSExpandedReader.FINDER_PAT_C = 2;\n  RSSExpandedReader.FINDER_PAT_D = 3;\n  RSSExpandedReader.FINDER_PAT_E = 4;\n  RSSExpandedReader.FINDER_PAT_F = 5;\n  RSSExpandedReader.FINDER_PATTERN_SEQUENCES = [[RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_A], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_B], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_C, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_D], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_E, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_C], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_E, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_F], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_E, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_E, RSSExpandedReader.FINDER_PAT_F, RSSExpandedReader.FINDER_PAT_F], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_C, RSSExpandedReader.FINDER_PAT_C, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_D], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_C, RSSExpandedReader.FINDER_PAT_C, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_E, RSSExpandedReader.FINDER_PAT_E], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_C, RSSExpandedReader.FINDER_PAT_C, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_E, RSSExpandedReader.FINDER_PAT_F, RSSExpandedReader.FINDER_PAT_F], [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_B, RSSExpandedReader.FINDER_PAT_C, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_D, RSSExpandedReader.FINDER_PAT_E, RSSExpandedReader.FINDER_PAT_E, RSSExpandedReader.FINDER_PAT_F, RSSExpandedReader.FINDER_PAT_F]];\n  RSSExpandedReader.MAX_PAIRS = 11;\n  return RSSExpandedReader;\n}(AbstractRSSReader);\nexport default RSSExpandedReader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BarcodeFormat", "MathUtils", "NotFoundException", "Result", "System", "AbstractRSSReader", "DataCharacter", "FinderPattern", "RSSUtils", "BitArrayBuilder", "createDecoder", "ExpandedPair", "ExpandedRow", "RSSExpandedReader", "_super", "_this", "apply", "arguments", "pairs", "MAX_PAIRS", "rows", "startEnd", "decodeRow", "rowNumber", "row", "hints", "startFromEven", "constructResult", "decodeRow2pairs", "e", "reset", "push", "retrieveNextPair", "error", "checkChecksum", "tryStackedDecode", "storeRow", "ps", "checkRowsBoolean", "reverse", "checkRows", "console", "log", "collectedRows", "currentRow", "e_1", "_a", "collectedRows_1", "collectedRows_1_1", "collectedRow", "getPairs", "e_1_1", "return", "isValidSequence", "rs", "e_2", "_b", "FINDER_PATTERN_SEQUENCES", "_c", "sequence", "stop_1", "j", "getFinderPattern", "getValue", "e_2_1", "wasReversed", "insertPos", "prevIsSame", "nextIsSame", "erow", "getRowNumber", "isEquivalent", "isPartialRow", "removePartialRows", "e_3", "e_4", "e_5", "rows_1", "rows_1_1", "allFound", "_d", "_e", "found", "pairs_1", "pairs_1_1", "pp", "equals", "e_5_1", "e_4_1", "e_3_1", "e_6", "e_7", "e_8", "rows_2", "rows_2_1", "r", "pairs_2", "pairs_2_1", "e_8_1", "e_7_1", "e_6_1", "getRows", "binary", "buildBitArray", "decoder", "resultingString", "parseInformation", "firstPoints", "getResultPoints", "lastPoints", "points", "RSS_EXPANDED", "firstPair", "get", "checkCharacter", "getLeftChar", "firstCharacter", "getRightChar", "checksum", "getChecksumPortion", "size", "currentPair", "currentRightChar", "checkCharacterValue", "getNextSecondBar", "initialPos", "currentPos", "getNextUnset", "getNextSet", "previousPairs", "isOddPattern", "pattern", "keepFinding", "forcedOffset", "findNextPair", "parseFoundFinderPattern", "leftChar", "decodeDataCharacter", "isEmptyPair", "mustBeLast", "rightChar", "counters", "getDecodeFinderCounters", "width", "getSize", "rowOffset", "lastPair", "getStartEnd", "searchingEvenPair", "<PERSON><PERSON><PERSON><PERSON>", "counterPosition", "patternStart", "x", "reverseCounters", "isFinderPattern", "tmp", "oddPattern", "firstCounter", "start", "end", "firstElementStart", "arraycopy", "parseFinderValue", "FINDER_PATTERNS", "getDataCharacterCounters", "recordPatternInReverse", "recordPattern", "temp", "numModules", "elementWidth", "sum", "Int32Array", "expectedElement<PERSON>idth", "Math", "abs", "oddCounts", "getOddCounts", "evenCounts", "getEvenCounts", "oddRoundingErrors", "getOddRoundingErrors", "evenRoundingErrors", "getEvenRoundingErrors", "value_1", "count", "offset", "adjustOddEvenCounts", "weightRowNumber", "oddSum", "oddChecksumPortion", "isNotA1left", "weight", "WEIGHTS", "evenChecksumPortion", "checksumPortion", "group", "oddWidest", "SYMBOL_WIDEST", "evenWidest", "vOdd", "getRSSvalue", "vEven", "tEven", "EVEN_TOTAL_SUBSET", "gSum", "GSUM", "evenSum", "incrementOdd", "decrementOdd", "incrementEven", "decrementEven", "mismatch", "oddParityBad", "evenParityBad", "increment", "decrement", "from", "FINDER_PAT_A", "FINDER_PAT_B", "FINDER_PAT_C", "FINDER_PAT_D", "FINDER_PAT_E", "FINDER_PAT_F"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/RSSExpandedReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../../../BarcodeFormat';\nimport MathUtils from '../../../common/detector/MathUtils';\n// import FormatException from '../../../FormatException';\nimport NotFoundException from '../../../NotFoundException';\nimport Result from '../../../Result';\nimport System from '../../../util/System';\nimport AbstractRSSReader from '../../rss/AbstractRSSReader';\nimport DataCharacter from '../../rss/DataCharacter';\nimport FinderPattern from '../../rss/FinderPattern';\nimport RSSUtils from '../../rss/RSSUtils';\nimport BitArrayBuilder from './BitArrayBuilder';\nimport { createDecoder } from './decoders/AbstractExpandedDecoderComplement';\nimport ExpandedPair from './ExpandedPair';\nimport ExpandedRow from './ExpandedRow';\n// import java.util.ArrayList;\n// import java.util.Iterator;\n// import java.util.List;\n// import java.util.Map;\n// import java.util.Collections;\n/** @experimental */\nvar RSSExpandedReader = /** @class */ (function (_super) {\n    __extends(RSSExpandedReader, _super);\n    function RSSExpandedReader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.pairs = new Array(RSSExpandedReader.MAX_PAIRS);\n        _this.rows = new Array();\n        _this.startEnd = [2];\n        return _this;\n    }\n    RSSExpandedReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        // Rows can start with even pattern in case in prev rows there where odd number of patters.\n        // So lets try twice\n        // this.pairs.clear();\n        this.pairs.length = 0;\n        this.startFromEven = false;\n        try {\n            return RSSExpandedReader.constructResult(this.decodeRow2pairs(rowNumber, row));\n        }\n        catch (e) {\n            // OK\n            // console.log(e);\n        }\n        this.pairs.length = 0;\n        this.startFromEven = true;\n        return RSSExpandedReader.constructResult(this.decodeRow2pairs(rowNumber, row));\n    };\n    RSSExpandedReader.prototype.reset = function () {\n        this.pairs.length = 0;\n        this.rows.length = 0;\n    };\n    // Not private for testing\n    RSSExpandedReader.prototype.decodeRow2pairs = function (rowNumber, row) {\n        var done = false;\n        while (!done) {\n            try {\n                this.pairs.push(this.retrieveNextPair(row, this.pairs, rowNumber));\n            }\n            catch (error) {\n                if (error instanceof NotFoundException) {\n                    if (!this.pairs.length) {\n                        throw new NotFoundException();\n                    }\n                    // exit this loop when retrieveNextPair() fails and throws\n                    done = true;\n                }\n            }\n        }\n        // TODO: verify sequence of finder patterns as in checkPairSequence()\n        if (this.checkChecksum()) {\n            return this.pairs;\n        }\n        var tryStackedDecode;\n        if (this.rows.length) {\n            tryStackedDecode = true;\n        }\n        else {\n            tryStackedDecode = false;\n        }\n        // let tryStackedDecode = !this.rows.isEmpty();\n        this.storeRow(rowNumber, false); // TODO: deal with reversed rows\n        if (tryStackedDecode) {\n            // When the image is 180-rotated, then rows are sorted in wrong direction.\n            // Try twice with both the directions.\n            var ps = this.checkRowsBoolean(false);\n            if (ps != null) {\n                return ps;\n            }\n            ps = this.checkRowsBoolean(true);\n            if (ps != null) {\n                return ps;\n            }\n        }\n        throw new NotFoundException();\n    };\n    // Need to Verify\n    RSSExpandedReader.prototype.checkRowsBoolean = function (reverse) {\n        // Limit number of rows we are checking\n        // We use recursive algorithm with pure complexity and don't want it to take forever\n        // Stacked barcode can have up to 11 rows, so 25 seems reasonable enough\n        if (this.rows.length > 25) {\n            this.rows.length = 0; // We will never have a chance to get result, so clear it\n            return null;\n        }\n        this.pairs.length = 0;\n        if (reverse) {\n            this.rows = this.rows.reverse();\n            // Collections.reverse(this.rows);\n        }\n        var ps = null;\n        try {\n            ps = this.checkRows(new Array(), 0);\n        }\n        catch (e) {\n            // OK\n            console.log(e);\n        }\n        if (reverse) {\n            this.rows = this.rows.reverse();\n            // Collections.reverse(this.rows);\n        }\n        return ps;\n    };\n    // Try to construct a valid rows sequence\n    // Recursion is used to implement backtracking\n    RSSExpandedReader.prototype.checkRows = function (collectedRows, currentRow) {\n        var e_1, _a;\n        for (var i = currentRow; i < this.rows.length; i++) {\n            var row = this.rows[i];\n            this.pairs.length = 0;\n            try {\n                for (var collectedRows_1 = (e_1 = void 0, __values(collectedRows)), collectedRows_1_1 = collectedRows_1.next(); !collectedRows_1_1.done; collectedRows_1_1 = collectedRows_1.next()) {\n                    var collectedRow = collectedRows_1_1.value;\n                    this.pairs.push(collectedRow.getPairs());\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (collectedRows_1_1 && !collectedRows_1_1.done && (_a = collectedRows_1.return)) _a.call(collectedRows_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            this.pairs.push(row.getPairs());\n            if (!RSSExpandedReader.isValidSequence(this.pairs)) {\n                continue;\n            }\n            if (this.checkChecksum()) {\n                return this.pairs;\n            }\n            var rs = new Array(collectedRows);\n            rs.push(row);\n            try {\n                // Recursion: try to add more rows\n                return this.checkRows(rs, i + 1);\n            }\n            catch (e) {\n                // We failed, try the next candidate\n                console.log(e);\n            }\n        }\n        throw new NotFoundException();\n    };\n    // Whether the pairs form a valid find pattern sequence,\n    // either complete or a prefix\n    RSSExpandedReader.isValidSequence = function (pairs) {\n        var e_2, _a;\n        try {\n            for (var _b = __values(RSSExpandedReader.FINDER_PATTERN_SEQUENCES), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var sequence = _c.value;\n                if (pairs.length > sequence.length) {\n                    continue;\n                }\n                var stop_1 = true;\n                for (var j = 0; j < pairs.length; j++) {\n                    if (pairs[j].getFinderPattern().getValue() !== sequence[j]) {\n                        stop_1 = false;\n                        break;\n                    }\n                }\n                if (stop_1) {\n                    return true;\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return false;\n    };\n    RSSExpandedReader.prototype.storeRow = function (rowNumber, wasReversed) {\n        // Discard if duplicate above or below; otherwise insert in order by row number.\n        var insertPos = 0;\n        var prevIsSame = false;\n        var nextIsSame = false;\n        while (insertPos < this.rows.length) {\n            var erow = this.rows[insertPos];\n            if (erow.getRowNumber() > rowNumber) {\n                nextIsSame = erow.isEquivalent(this.pairs);\n                break;\n            }\n            prevIsSame = erow.isEquivalent(this.pairs);\n            insertPos++;\n        }\n        if (nextIsSame || prevIsSame) {\n            return;\n        }\n        // When the row was partially decoded (e.g. 2 pairs found instead of 3),\n        // it will prevent us from detecting the barcode.\n        // Try to merge partial rows\n        // Check whether the row is part of an allready detected row\n        if (RSSExpandedReader.isPartialRow(this.pairs, this.rows)) {\n            return;\n        }\n        this.rows.push(insertPos, new ExpandedRow(this.pairs, rowNumber, wasReversed));\n        this.removePartialRows(this.pairs, this.rows);\n    };\n    // Remove all the rows that contains only specified pairs\n    RSSExpandedReader.prototype.removePartialRows = function (pairs, rows) {\n        var e_3, _a, e_4, _b, e_5, _c;\n        try {\n            // for (Iterator<ExpandedRow> iterator = rows.iterator(); iterator.hasNext();) {\n            //   ExpandedRow r = iterator.next();\n            //   if (r.getPairs().size() == pairs.size()) {\n            //     continue;\n            //   }\n            //   boolean allFound = true;\n            //   for (ExpandedPair p : r.getPairs()) {\n            //     boolean found = false;\n            //     for (ExpandedPair pp : pairs) {\n            //       if (p.equals(pp)) {\n            //         found = true;\n            //         break;\n            //       }\n            //     }\n            //     if (!found) {\n            //       allFound = false;\n            //       break;\n            //     }\n            //   }\n            //   if (allFound) {\n            //     // 'pairs' contains all the pairs from the row 'r'\n            //     iterator.remove();\n            //   }\n            // }\n            for (var rows_1 = __values(rows), rows_1_1 = rows_1.next(); !rows_1_1.done; rows_1_1 = rows_1.next()) {\n                var row = rows_1_1.value;\n                if (row.getPairs().length === pairs.length) {\n                    continue;\n                }\n                var allFound = true;\n                try {\n                    for (var _d = (e_4 = void 0, __values(row.getPairs())), _e = _d.next(); !_e.done; _e = _d.next()) {\n                        var p = _e.value;\n                        var found = false;\n                        try {\n                            for (var pairs_1 = (e_5 = void 0, __values(pairs)), pairs_1_1 = pairs_1.next(); !pairs_1_1.done; pairs_1_1 = pairs_1.next()) {\n                                var pp = pairs_1_1.value;\n                                if (ExpandedPair.equals(p, pp)) {\n                                    found = true;\n                                    break;\n                                }\n                            }\n                        }\n                        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n                        finally {\n                            try {\n                                if (pairs_1_1 && !pairs_1_1.done && (_c = pairs_1.return)) _c.call(pairs_1);\n                            }\n                            finally { if (e_5) throw e_5.error; }\n                        }\n                        if (!found) {\n                            allFound = false;\n                        }\n                    }\n                }\n                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                finally {\n                    try {\n                        if (_e && !_e.done && (_b = _d.return)) _b.call(_d);\n                    }\n                    finally { if (e_4) throw e_4.error; }\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (rows_1_1 && !rows_1_1.done && (_a = rows_1.return)) _a.call(rows_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    // Returns true when one of the rows already contains all the pairs\n    RSSExpandedReader.isPartialRow = function (pairs, rows) {\n        var e_6, _a, e_7, _b, e_8, _c;\n        try {\n            for (var rows_2 = __values(rows), rows_2_1 = rows_2.next(); !rows_2_1.done; rows_2_1 = rows_2.next()) {\n                var r = rows_2_1.value;\n                var allFound = true;\n                try {\n                    for (var pairs_2 = (e_7 = void 0, __values(pairs)), pairs_2_1 = pairs_2.next(); !pairs_2_1.done; pairs_2_1 = pairs_2.next()) {\n                        var p = pairs_2_1.value;\n                        var found = false;\n                        try {\n                            for (var _d = (e_8 = void 0, __values(r.getPairs())), _e = _d.next(); !_e.done; _e = _d.next()) {\n                                var pp = _e.value;\n                                if (p.equals(pp)) {\n                                    found = true;\n                                    break;\n                                }\n                            }\n                        }\n                        catch (e_8_1) { e_8 = { error: e_8_1 }; }\n                        finally {\n                            try {\n                                if (_e && !_e.done && (_c = _d.return)) _c.call(_d);\n                            }\n                            finally { if (e_8) throw e_8.error; }\n                        }\n                        if (!found) {\n                            allFound = false;\n                            break;\n                        }\n                    }\n                }\n                catch (e_7_1) { e_7 = { error: e_7_1 }; }\n                finally {\n                    try {\n                        if (pairs_2_1 && !pairs_2_1.done && (_b = pairs_2.return)) _b.call(pairs_2);\n                    }\n                    finally { if (e_7) throw e_7.error; }\n                }\n                if (allFound) {\n                    // the row 'r' contain all the pairs from 'pairs'\n                    return true;\n                }\n            }\n        }\n        catch (e_6_1) { e_6 = { error: e_6_1 }; }\n        finally {\n            try {\n                if (rows_2_1 && !rows_2_1.done && (_a = rows_2.return)) _a.call(rows_2);\n            }\n            finally { if (e_6) throw e_6.error; }\n        }\n        return false;\n    };\n    // Only used for unit testing\n    RSSExpandedReader.prototype.getRows = function () {\n        return this.rows;\n    };\n    // Not private for unit testing\n    RSSExpandedReader.constructResult = function (pairs) {\n        var binary = BitArrayBuilder.buildBitArray(pairs);\n        var decoder = createDecoder(binary);\n        var resultingString = decoder.parseInformation();\n        var firstPoints = pairs[0].getFinderPattern().getResultPoints();\n        var lastPoints = pairs[pairs.length - 1]\n            .getFinderPattern()\n            .getResultPoints();\n        var points = [firstPoints[0], firstPoints[1], lastPoints[0], lastPoints[1]];\n        return new Result(resultingString, null, null, points, BarcodeFormat.RSS_EXPANDED, null);\n    };\n    RSSExpandedReader.prototype.checkChecksum = function () {\n        var firstPair = this.pairs.get(0);\n        var checkCharacter = firstPair.getLeftChar();\n        var firstCharacter = firstPair.getRightChar();\n        if (firstCharacter === null) {\n            return false;\n        }\n        var checksum = firstCharacter.getChecksumPortion();\n        var s = 2;\n        for (var i = 1; i < this.pairs.size(); ++i) {\n            var currentPair = this.pairs.get(i);\n            checksum += currentPair.getLeftChar().getChecksumPortion();\n            s++;\n            var currentRightChar = currentPair.getRightChar();\n            if (currentRightChar != null) {\n                checksum += currentRightChar.getChecksumPortion();\n                s++;\n            }\n        }\n        checksum %= 211;\n        var checkCharacterValue = 211 * (s - 4) + checksum;\n        return checkCharacterValue === checkCharacter.getValue();\n    };\n    RSSExpandedReader.getNextSecondBar = function (row, initialPos) {\n        var currentPos;\n        if (row.get(initialPos)) {\n            currentPos = row.getNextUnset(initialPos);\n            currentPos = row.getNextSet(currentPos);\n        }\n        else {\n            currentPos = row.getNextSet(initialPos);\n            currentPos = row.getNextUnset(currentPos);\n        }\n        return currentPos;\n    };\n    // not private for testing\n    RSSExpandedReader.prototype.retrieveNextPair = function (row, previousPairs, rowNumber) {\n        var isOddPattern = previousPairs.length % 2 === 0;\n        if (this.startFromEven) {\n            isOddPattern = !isOddPattern;\n        }\n        var pattern;\n        var keepFinding = true;\n        var forcedOffset = -1;\n        do {\n            this.findNextPair(row, previousPairs, forcedOffset);\n            pattern = this.parseFoundFinderPattern(row, rowNumber, isOddPattern);\n            if (pattern === null) {\n                forcedOffset = RSSExpandedReader.getNextSecondBar(row, this.startEnd[0]);\n            }\n            else {\n                keepFinding = false;\n            }\n        } while (keepFinding);\n        // When stacked symbol is split over multiple rows, there's no way to guess if this pair can be last or not.\n        // boolean mayBeLast = checkPairSequence(previousPairs, pattern);\n        var leftChar = this.decodeDataCharacter(row, pattern, isOddPattern, true);\n        if (!this.isEmptyPair(previousPairs) &&\n            previousPairs[previousPairs.length - 1].mustBeLast()) {\n            throw new NotFoundException();\n        }\n        var rightChar;\n        try {\n            rightChar = this.decodeDataCharacter(row, pattern, isOddPattern, false);\n        }\n        catch (e) {\n            rightChar = null;\n            console.log(e);\n        }\n        return new ExpandedPair(leftChar, rightChar, pattern, true);\n    };\n    RSSExpandedReader.prototype.isEmptyPair = function (pairs) {\n        if (pairs.length === 0) {\n            return true;\n        }\n        return false;\n    };\n    RSSExpandedReader.prototype.findNextPair = function (row, previousPairs, forcedOffset) {\n        var counters = this.getDecodeFinderCounters();\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var width = row.getSize();\n        var rowOffset;\n        if (forcedOffset >= 0) {\n            rowOffset = forcedOffset;\n        }\n        else if (this.isEmptyPair(previousPairs)) {\n            rowOffset = 0;\n        }\n        else {\n            var lastPair = previousPairs[previousPairs.length - 1];\n            rowOffset = lastPair.getFinderPattern().getStartEnd()[1];\n        }\n        var searchingEvenPair = previousPairs.length % 2 !== 0;\n        if (this.startFromEven) {\n            searchingEvenPair = !searchingEvenPair;\n        }\n        var isWhite = false;\n        while (rowOffset < width) {\n            isWhite = !row.get(rowOffset);\n            if (!isWhite) {\n                break;\n            }\n            rowOffset++;\n        }\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        for (var x = rowOffset; x < width; x++) {\n            if (row.get(x) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === 3) {\n                    if (searchingEvenPair) {\n                        RSSExpandedReader.reverseCounters(counters);\n                    }\n                    if (RSSExpandedReader.isFinderPattern(counters)) {\n                        this.startEnd[0] = patternStart;\n                        this.startEnd[1] = x;\n                        return;\n                    }\n                    if (searchingEvenPair) {\n                        RSSExpandedReader.reverseCounters(counters);\n                    }\n                    patternStart += counters[0] + counters[1];\n                    counters[0] = counters[2];\n                    counters[1] = counters[3];\n                    counters[2] = 0;\n                    counters[3] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    RSSExpandedReader.reverseCounters = function (counters) {\n        var length = counters.length;\n        for (var i = 0; i < length / 2; ++i) {\n            var tmp = counters[i];\n            counters[i] = counters[length - i - 1];\n            counters[length - i - 1] = tmp;\n        }\n    };\n    RSSExpandedReader.prototype.parseFoundFinderPattern = function (row, rowNumber, oddPattern) {\n        // Actually we found elements 2-5.\n        var firstCounter;\n        var start;\n        var end;\n        if (oddPattern) {\n            // If pattern number is odd, we need to locate element 1 *before* the current block.\n            var firstElementStart = this.startEnd[0] - 1;\n            // Locate element 1\n            while (firstElementStart >= 0 && !row.get(firstElementStart)) {\n                firstElementStart--;\n            }\n            firstElementStart++;\n            firstCounter = this.startEnd[0] - firstElementStart;\n            start = firstElementStart;\n            end = this.startEnd[1];\n        }\n        else {\n            // If pattern number is even, the pattern is reversed, so we need to locate element 1 *after* the current block.\n            start = this.startEnd[0];\n            end = row.getNextUnset(this.startEnd[1] + 1);\n            firstCounter = end - this.startEnd[1];\n        }\n        // Make 'counters' hold 1-4\n        var counters = this.getDecodeFinderCounters();\n        System.arraycopy(counters, 0, counters, 1, counters.length - 1);\n        counters[0] = firstCounter;\n        var value;\n        try {\n            value = this.parseFinderValue(counters, RSSExpandedReader.FINDER_PATTERNS);\n        }\n        catch (e) {\n            return null;\n        }\n        // return new FinderPattern(value, new int[] { start, end }, start, end, rowNumber});\n        return new FinderPattern(value, [start, end], start, end, rowNumber);\n    };\n    RSSExpandedReader.prototype.decodeDataCharacter = function (row, pattern, isOddPattern, leftChar) {\n        var counters = this.getDataCharacterCounters();\n        for (var x = 0; x < counters.length; x++) {\n            counters[x] = 0;\n        }\n        if (leftChar) {\n            RSSExpandedReader.recordPatternInReverse(row, pattern.getStartEnd()[0], counters);\n        }\n        else {\n            RSSExpandedReader.recordPattern(row, pattern.getStartEnd()[1], counters);\n            // reverse it\n            for (var i = 0, j = counters.length - 1; i < j; i++, j--) {\n                var temp = counters[i];\n                counters[i] = counters[j];\n                counters[j] = temp;\n            }\n        } // counters[] has the pixels of the module\n        var numModules = 17; // left and right data characters have all the same length\n        var elementWidth = MathUtils.sum(new Int32Array(counters)) / numModules;\n        // Sanity check: element width for pattern and the character should match\n        var expectedElementWidth = (pattern.getStartEnd()[1] - pattern.getStartEnd()[0]) / 15.0;\n        if (Math.abs(elementWidth - expectedElementWidth) / expectedElementWidth >\n            0.3) {\n            throw new NotFoundException();\n        }\n        var oddCounts = this.getOddCounts();\n        var evenCounts = this.getEvenCounts();\n        var oddRoundingErrors = this.getOddRoundingErrors();\n        var evenRoundingErrors = this.getEvenRoundingErrors();\n        for (var i = 0; i < counters.length; i++) {\n            var value_1 = (1.0 * counters[i]) / elementWidth;\n            var count = value_1 + 0.5; // Round\n            if (count < 1) {\n                if (value_1 < 0.3) {\n                    throw new NotFoundException();\n                }\n                count = 1;\n            }\n            else if (count > 8) {\n                if (value_1 > 8.7) {\n                    throw new NotFoundException();\n                }\n                count = 8;\n            }\n            var offset = i / 2;\n            if ((i & 0x01) === 0) {\n                oddCounts[offset] = count;\n                oddRoundingErrors[offset] = value_1 - count;\n            }\n            else {\n                evenCounts[offset] = count;\n                evenRoundingErrors[offset] = value_1 - count;\n            }\n        }\n        this.adjustOddEvenCounts(numModules);\n        var weightRowNumber = 4 * pattern.getValue() + (isOddPattern ? 0 : 2) + (leftChar ? 0 : 1) - 1;\n        var oddSum = 0;\n        var oddChecksumPortion = 0;\n        for (var i = oddCounts.length - 1; i >= 0; i--) {\n            if (RSSExpandedReader.isNotA1left(pattern, isOddPattern, leftChar)) {\n                var weight = RSSExpandedReader.WEIGHTS[weightRowNumber][2 * i];\n                oddChecksumPortion += oddCounts[i] * weight;\n            }\n            oddSum += oddCounts[i];\n        }\n        var evenChecksumPortion = 0;\n        // int evenSum = 0;\n        for (var i = evenCounts.length - 1; i >= 0; i--) {\n            if (RSSExpandedReader.isNotA1left(pattern, isOddPattern, leftChar)) {\n                var weight = RSSExpandedReader.WEIGHTS[weightRowNumber][2 * i + 1];\n                evenChecksumPortion += evenCounts[i] * weight;\n            }\n            // evenSum += evenCounts[i];\n        }\n        var checksumPortion = oddChecksumPortion + evenChecksumPortion;\n        if ((oddSum & 0x01) !== 0 || oddSum > 13 || oddSum < 4) {\n            throw new NotFoundException();\n        }\n        var group = (13 - oddSum) / 2;\n        var oddWidest = RSSExpandedReader.SYMBOL_WIDEST[group];\n        var evenWidest = 9 - oddWidest;\n        var vOdd = RSSUtils.getRSSvalue(oddCounts, oddWidest, true);\n        var vEven = RSSUtils.getRSSvalue(evenCounts, evenWidest, false);\n        var tEven = RSSExpandedReader.EVEN_TOTAL_SUBSET[group];\n        var gSum = RSSExpandedReader.GSUM[group];\n        var value = vOdd * tEven + vEven + gSum;\n        return new DataCharacter(value, checksumPortion);\n    };\n    RSSExpandedReader.isNotA1left = function (pattern, isOddPattern, leftChar) {\n        // A1: pattern.getValue is 0 (A), and it's an oddPattern, and it is a left char\n        return !(pattern.getValue() === 0 && isOddPattern && leftChar);\n    };\n    RSSExpandedReader.prototype.adjustOddEvenCounts = function (numModules) {\n        var oddSum = MathUtils.sum(new Int32Array(this.getOddCounts()));\n        var evenSum = MathUtils.sum(new Int32Array(this.getEvenCounts()));\n        var incrementOdd = false;\n        var decrementOdd = false;\n        if (oddSum > 13) {\n            decrementOdd = true;\n        }\n        else if (oddSum < 4) {\n            incrementOdd = true;\n        }\n        var incrementEven = false;\n        var decrementEven = false;\n        if (evenSum > 13) {\n            decrementEven = true;\n        }\n        else if (evenSum < 4) {\n            incrementEven = true;\n        }\n        var mismatch = oddSum + evenSum - numModules;\n        var oddParityBad = (oddSum & 0x01) === 1;\n        var evenParityBad = (evenSum & 0x01) === 0;\n        if (mismatch === 1) {\n            if (oddParityBad) {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                decrementOdd = true;\n            }\n            else {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                decrementEven = true;\n            }\n        }\n        else if (mismatch === -1) {\n            if (oddParityBad) {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                incrementOdd = true;\n            }\n            else {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                incrementEven = true;\n            }\n        }\n        else if (mismatch === 0) {\n            if (oddParityBad) {\n                if (!evenParityBad) {\n                    throw new NotFoundException();\n                }\n                // Both bad\n                if (oddSum < evenSum) {\n                    incrementOdd = true;\n                    decrementEven = true;\n                }\n                else {\n                    decrementOdd = true;\n                    incrementEven = true;\n                }\n            }\n            else {\n                if (evenParityBad) {\n                    throw new NotFoundException();\n                }\n                // Nothing to do!\n            }\n        }\n        else {\n            throw new NotFoundException();\n        }\n        if (incrementOdd) {\n            if (decrementOdd) {\n                throw new NotFoundException();\n            }\n            RSSExpandedReader.increment(this.getOddCounts(), this.getOddRoundingErrors());\n        }\n        if (decrementOdd) {\n            RSSExpandedReader.decrement(this.getOddCounts(), this.getOddRoundingErrors());\n        }\n        if (incrementEven) {\n            if (decrementEven) {\n                throw new NotFoundException();\n            }\n            RSSExpandedReader.increment(this.getEvenCounts(), this.getOddRoundingErrors());\n        }\n        if (decrementEven) {\n            RSSExpandedReader.decrement(this.getEvenCounts(), this.getEvenRoundingErrors());\n        }\n    };\n    RSSExpandedReader.SYMBOL_WIDEST = [7, 5, 4, 3, 1];\n    RSSExpandedReader.EVEN_TOTAL_SUBSET = [4, 20, 52, 104, 204];\n    RSSExpandedReader.GSUM = [0, 348, 1388, 2948, 3988];\n    RSSExpandedReader.FINDER_PATTERNS = [\n        Int32Array.from([1, 8, 4, 1]),\n        Int32Array.from([3, 6, 4, 1]),\n        Int32Array.from([3, 4, 6, 1]),\n        Int32Array.from([3, 2, 8, 1]),\n        Int32Array.from([2, 6, 5, 1]),\n        Int32Array.from([2, 2, 9, 1]),\n    ];\n    RSSExpandedReader.WEIGHTS = [\n        [1, 3, 9, 27, 81, 32, 96, 77],\n        [20, 60, 180, 118, 143, 7, 21, 63],\n        [189, 145, 13, 39, 117, 140, 209, 205],\n        [193, 157, 49, 147, 19, 57, 171, 91],\n        [62, 186, 136, 197, 169, 85, 44, 132],\n        [185, 133, 188, 142, 4, 12, 36, 108],\n        [113, 128, 173, 97, 80, 29, 87, 50],\n        [150, 28, 84, 41, 123, 158, 52, 156],\n        [46, 138, 203, 187, 139, 206, 196, 166],\n        [76, 17, 51, 153, 37, 111, 122, 155],\n        [43, 129, 176, 106, 107, 110, 119, 146],\n        [16, 48, 144, 10, 30, 90, 59, 177],\n        [109, 116, 137, 200, 178, 112, 125, 164],\n        [70, 210, 208, 202, 184, 130, 179, 115],\n        [134, 191, 151, 31, 93, 68, 204, 190],\n        [148, 22, 66, 198, 172, 94, 71, 2],\n        [6, 18, 54, 162, 64, 192, 154, 40],\n        [120, 149, 25, 75, 14, 42, 126, 167],\n        [79, 26, 78, 23, 69, 207, 199, 175],\n        [103, 98, 83, 38, 114, 131, 182, 124],\n        [161, 61, 183, 127, 170, 88, 53, 159],\n        [55, 165, 73, 8, 24, 72, 5, 15],\n        [45, 135, 194, 160, 58, 174, 100, 89],\n    ];\n    RSSExpandedReader.FINDER_PAT_A = 0;\n    RSSExpandedReader.FINDER_PAT_B = 1;\n    RSSExpandedReader.FINDER_PAT_C = 2;\n    RSSExpandedReader.FINDER_PAT_D = 3;\n    RSSExpandedReader.FINDER_PAT_E = 4;\n    RSSExpandedReader.FINDER_PAT_F = 5;\n    RSSExpandedReader.FINDER_PATTERN_SEQUENCES = [\n        [RSSExpandedReader.FINDER_PAT_A, RSSExpandedReader.FINDER_PAT_A],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_D,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_C,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_F,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_F,\n            RSSExpandedReader.FINDER_PAT_F,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_D,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_E,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_F,\n            RSSExpandedReader.FINDER_PAT_F,\n        ],\n        [\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_A,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_B,\n            RSSExpandedReader.FINDER_PAT_C,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_D,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_E,\n            RSSExpandedReader.FINDER_PAT_F,\n            RSSExpandedReader.FINDER_PAT_F,\n        ],\n    ];\n    RSSExpandedReader.MAX_PAIRS = 11;\n    return RSSExpandedReader;\n}(AbstractRSSReader));\nexport default RSSExpandedReader;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,aAAa,MAAM,wBAAwB;AAClD,OAAOC,SAAS,MAAM,oCAAoC;AAC1D;AACA,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,iBAAiB,MAAM,6BAA6B;AAC3D,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,aAAa,QAAQ,8CAA8C;AAC5E,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACrDzC,SAAS,CAACwC,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAAA,EAAG;IACzB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,KAAK,GAAG,IAAItC,KAAK,CAACiC,iBAAiB,CAACM,SAAS,CAAC;IACpDJ,KAAK,CAACK,IAAI,GAAG,IAAIxC,KAAK,CAAC,CAAC;IACxBmC,KAAK,CAACM,QAAQ,GAAG,CAAC,CAAC,CAAC;IACpB,OAAON,KAAK;EAChB;EACAF,iBAAiB,CAAC5B,SAAS,CAACqC,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAE;IACrE;IACA;IACA;IACA,IAAI,CAACP,KAAK,CAACvB,MAAM,GAAG,CAAC;IACrB,IAAI,CAAC+B,aAAa,GAAG,KAAK;IAC1B,IAAI;MACA,OAAOb,iBAAiB,CAACc,eAAe,CAAC,IAAI,CAACC,eAAe,CAACL,SAAS,EAAEC,GAAG,CAAC,CAAC;IAClF,CAAC,CACD,OAAOK,CAAC,EAAE;MACN;MACA;IAAA;IAEJ,IAAI,CAACX,KAAK,CAACvB,MAAM,GAAG,CAAC;IACrB,IAAI,CAAC+B,aAAa,GAAG,IAAI;IACzB,OAAOb,iBAAiB,CAACc,eAAe,CAAC,IAAI,CAACC,eAAe,CAACL,SAAS,EAAEC,GAAG,CAAC,CAAC;EAClF,CAAC;EACDX,iBAAiB,CAAC5B,SAAS,CAAC6C,KAAK,GAAG,YAAY;IAC5C,IAAI,CAACZ,KAAK,CAACvB,MAAM,GAAG,CAAC;IACrB,IAAI,CAACyB,IAAI,CAACzB,MAAM,GAAG,CAAC;EACxB,CAAC;EACD;EACAkB,iBAAiB,CAAC5B,SAAS,CAAC2C,eAAe,GAAG,UAAUL,SAAS,EAAEC,GAAG,EAAE;IACpE,IAAI1B,IAAI,GAAG,KAAK;IAChB,OAAO,CAACA,IAAI,EAAE;MACV,IAAI;QACA,IAAI,CAACoB,KAAK,CAACa,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACR,GAAG,EAAE,IAAI,CAACN,KAAK,EAAEK,SAAS,CAAC,CAAC;MACtE,CAAC,CACD,OAAOU,KAAK,EAAE;QACV,IAAIA,KAAK,YAAY/B,iBAAiB,EAAE;UACpC,IAAI,CAAC,IAAI,CAACgB,KAAK,CAACvB,MAAM,EAAE;YACpB,MAAM,IAAIO,iBAAiB,CAAC,CAAC;UACjC;UACA;UACAJ,IAAI,GAAG,IAAI;QACf;MACJ;IACJ;IACA;IACA,IAAI,IAAI,CAACoC,aAAa,CAAC,CAAC,EAAE;MACtB,OAAO,IAAI,CAAChB,KAAK;IACrB;IACA,IAAIiB,gBAAgB;IACpB,IAAI,IAAI,CAACf,IAAI,CAACzB,MAAM,EAAE;MAClBwC,gBAAgB,GAAG,IAAI;IAC3B,CAAC,MACI;MACDA,gBAAgB,GAAG,KAAK;IAC5B;IACA;IACA,IAAI,CAACC,QAAQ,CAACb,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;IACjC,IAAIY,gBAAgB,EAAE;MAClB;MACA;MACA,IAAIE,EAAE,GAAG,IAAI,CAACC,gBAAgB,CAAC,KAAK,CAAC;MACrC,IAAID,EAAE,IAAI,IAAI,EAAE;QACZ,OAAOA,EAAE;MACb;MACAA,EAAE,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAAC;MAChC,IAAID,EAAE,IAAI,IAAI,EAAE;QACZ,OAAOA,EAAE;MACb;IACJ;IACA,MAAM,IAAInC,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACD;EACAW,iBAAiB,CAAC5B,SAAS,CAACqD,gBAAgB,GAAG,UAAUC,OAAO,EAAE;IAC9D;IACA;IACA;IACA,IAAI,IAAI,CAACnB,IAAI,CAACzB,MAAM,GAAG,EAAE,EAAE;MACvB,IAAI,CAACyB,IAAI,CAACzB,MAAM,GAAG,CAAC,CAAC,CAAC;MACtB,OAAO,IAAI;IACf;IACA,IAAI,CAACuB,KAAK,CAACvB,MAAM,GAAG,CAAC;IACrB,IAAI4C,OAAO,EAAE;MACT,IAAI,CAACnB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACmB,OAAO,CAAC,CAAC;MAC/B;IACJ;IACA,IAAIF,EAAE,GAAG,IAAI;IACb,IAAI;MACAA,EAAE,GAAG,IAAI,CAACG,SAAS,CAAC,IAAI5D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACvC,CAAC,CACD,OAAOiD,CAAC,EAAE;MACN;MACAY,OAAO,CAACC,GAAG,CAACb,CAAC,CAAC;IAClB;IACA,IAAIU,OAAO,EAAE;MACT,IAAI,CAACnB,IAAI,GAAG,IAAI,CAACA,IAAI,CAACmB,OAAO,CAAC,CAAC;MAC/B;IACJ;IACA,OAAOF,EAAE;EACb,CAAC;EACD;EACA;EACAxB,iBAAiB,CAAC5B,SAAS,CAACuD,SAAS,GAAG,UAAUG,aAAa,EAAEC,UAAU,EAAE;IACzE,IAAIC,GAAG,EAAEC,EAAE;IACX,KAAK,IAAIrD,CAAC,GAAGmD,UAAU,EAAEnD,CAAC,GAAG,IAAI,CAAC2B,IAAI,CAACzB,MAAM,EAAEF,CAAC,EAAE,EAAE;MAChD,IAAI+B,GAAG,GAAG,IAAI,CAACJ,IAAI,CAAC3B,CAAC,CAAC;MACtB,IAAI,CAACyB,KAAK,CAACvB,MAAM,GAAG,CAAC;MACrB,IAAI;QACA,KAAK,IAAIoD,eAAe,IAAIF,GAAG,GAAG,KAAK,CAAC,EAAE1D,QAAQ,CAACwD,aAAa,CAAC,CAAC,EAAEK,iBAAiB,GAAGD,eAAe,CAACnD,IAAI,CAAC,CAAC,EAAE,CAACoD,iBAAiB,CAAClD,IAAI,EAAEkD,iBAAiB,GAAGD,eAAe,CAACnD,IAAI,CAAC,CAAC,EAAE;UACjL,IAAIqD,YAAY,GAAGD,iBAAiB,CAACnD,KAAK;UAC1C,IAAI,CAACqB,KAAK,CAACa,IAAI,CAACkB,YAAY,CAACC,QAAQ,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAEN,GAAG,GAAG;UAAEZ,KAAK,EAAEkB;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIH,iBAAiB,IAAI,CAACA,iBAAiB,CAAClD,IAAI,KAAKgD,EAAE,GAAGC,eAAe,CAACK,MAAM,CAAC,EAAEN,EAAE,CAACpD,IAAI,CAACqD,eAAe,CAAC;QAC/G,CAAC,SACO;UAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACZ,KAAK;QAAE;MACxC;MACA,IAAI,CAACf,KAAK,CAACa,IAAI,CAACP,GAAG,CAAC0B,QAAQ,CAAC,CAAC,CAAC;MAC/B,IAAI,CAACrC,iBAAiB,CAACwC,eAAe,CAAC,IAAI,CAACnC,KAAK,CAAC,EAAE;QAChD;MACJ;MACA,IAAI,IAAI,CAACgB,aAAa,CAAC,CAAC,EAAE;QACtB,OAAO,IAAI,CAAChB,KAAK;MACrB;MACA,IAAIoC,EAAE,GAAG,IAAI1E,KAAK,CAAC+D,aAAa,CAAC;MACjCW,EAAE,CAACvB,IAAI,CAACP,GAAG,CAAC;MACZ,IAAI;QACA;QACA,OAAO,IAAI,CAACgB,SAAS,CAACc,EAAE,EAAE7D,CAAC,GAAG,CAAC,CAAC;MACpC,CAAC,CACD,OAAOoC,CAAC,EAAE;QACN;QACAY,OAAO,CAACC,GAAG,CAACb,CAAC,CAAC;MAClB;IACJ;IACA,MAAM,IAAI3B,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACD;EACA;EACAW,iBAAiB,CAACwC,eAAe,GAAG,UAAUnC,KAAK,EAAE;IACjD,IAAIqC,GAAG,EAAET,EAAE;IACX,IAAI;MACA,KAAK,IAAIU,EAAE,GAAGrE,QAAQ,CAAC0B,iBAAiB,CAAC4C,wBAAwB,CAAC,EAAEC,EAAE,GAAGF,EAAE,CAAC5D,IAAI,CAAC,CAAC,EAAE,CAAC8D,EAAE,CAAC5D,IAAI,EAAE4D,EAAE,GAAGF,EAAE,CAAC5D,IAAI,CAAC,CAAC,EAAE;QAC1G,IAAI+D,QAAQ,GAAGD,EAAE,CAAC7D,KAAK;QACvB,IAAIqB,KAAK,CAACvB,MAAM,GAAGgE,QAAQ,CAAChE,MAAM,EAAE;UAChC;QACJ;QACA,IAAIiE,MAAM,GAAG,IAAI;QACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3C,KAAK,CAACvB,MAAM,EAAEkE,CAAC,EAAE,EAAE;UACnC,IAAI3C,KAAK,CAAC2C,CAAC,CAAC,CAACC,gBAAgB,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,KAAKJ,QAAQ,CAACE,CAAC,CAAC,EAAE;YACxDD,MAAM,GAAG,KAAK;YACd;UACJ;QACJ;QACA,IAAIA,MAAM,EAAE;UACR,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,CACD,OAAOI,KAAK,EAAE;MAAET,GAAG,GAAG;QAAEtB,KAAK,EAAE+B;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIN,EAAE,IAAI,CAACA,EAAE,CAAC5D,IAAI,KAAKgD,EAAE,GAAGU,EAAE,CAACJ,MAAM,CAAC,EAAEN,EAAE,CAACpD,IAAI,CAAC8D,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAID,GAAG,EAAE,MAAMA,GAAG,CAACtB,KAAK;MAAE;IACxC;IACA,OAAO,KAAK;EAChB,CAAC;EACDpB,iBAAiB,CAAC5B,SAAS,CAACmD,QAAQ,GAAG,UAAUb,SAAS,EAAE0C,WAAW,EAAE;IACrE;IACA,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,UAAU,GAAG,KAAK;IACtB,OAAOF,SAAS,GAAG,IAAI,CAAC9C,IAAI,CAACzB,MAAM,EAAE;MACjC,IAAI0E,IAAI,GAAG,IAAI,CAACjD,IAAI,CAAC8C,SAAS,CAAC;MAC/B,IAAIG,IAAI,CAACC,YAAY,CAAC,CAAC,GAAG/C,SAAS,EAAE;QACjC6C,UAAU,GAAGC,IAAI,CAACE,YAAY,CAAC,IAAI,CAACrD,KAAK,CAAC;QAC1C;MACJ;MACAiD,UAAU,GAAGE,IAAI,CAACE,YAAY,CAAC,IAAI,CAACrD,KAAK,CAAC;MAC1CgD,SAAS,EAAE;IACf;IACA,IAAIE,UAAU,IAAID,UAAU,EAAE;MAC1B;IACJ;IACA;IACA;IACA;IACA;IACA,IAAItD,iBAAiB,CAAC2D,YAAY,CAAC,IAAI,CAACtD,KAAK,EAAE,IAAI,CAACE,IAAI,CAAC,EAAE;MACvD;IACJ;IACA,IAAI,CAACA,IAAI,CAACW,IAAI,CAACmC,SAAS,EAAE,IAAItD,WAAW,CAAC,IAAI,CAACM,KAAK,EAAEK,SAAS,EAAE0C,WAAW,CAAC,CAAC;IAC9E,IAAI,CAACQ,iBAAiB,CAAC,IAAI,CAACvD,KAAK,EAAE,IAAI,CAACE,IAAI,CAAC;EACjD,CAAC;EACD;EACAP,iBAAiB,CAAC5B,SAAS,CAACwF,iBAAiB,GAAG,UAAUvD,KAAK,EAAEE,IAAI,EAAE;IACnE,IAAIsD,GAAG,EAAE5B,EAAE,EAAE6B,GAAG,EAAEnB,EAAE,EAAEoB,GAAG,EAAElB,EAAE;IAC7B,IAAI;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAK,IAAImB,MAAM,GAAG1F,QAAQ,CAACiC,IAAI,CAAC,EAAE0D,QAAQ,GAAGD,MAAM,CAACjF,IAAI,CAAC,CAAC,EAAE,CAACkF,QAAQ,CAAChF,IAAI,EAAEgF,QAAQ,GAAGD,MAAM,CAACjF,IAAI,CAAC,CAAC,EAAE;QAClG,IAAI4B,GAAG,GAAGsD,QAAQ,CAACjF,KAAK;QACxB,IAAI2B,GAAG,CAAC0B,QAAQ,CAAC,CAAC,CAACvD,MAAM,KAAKuB,KAAK,CAACvB,MAAM,EAAE;UACxC;QACJ;QACA,IAAIoF,QAAQ,GAAG,IAAI;QACnB,IAAI;UACA,KAAK,IAAIC,EAAE,IAAIL,GAAG,GAAG,KAAK,CAAC,EAAExF,QAAQ,CAACqC,GAAG,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE+B,EAAE,GAAGD,EAAE,CAACpF,IAAI,CAAC,CAAC,EAAE,CAACqF,EAAE,CAACnF,IAAI,EAAEmF,EAAE,GAAGD,EAAE,CAACpF,IAAI,CAAC,CAAC,EAAE;YAC9F,IAAIf,CAAC,GAAGoG,EAAE,CAACpF,KAAK;YAChB,IAAIqF,KAAK,GAAG,KAAK;YACjB,IAAI;cACA,KAAK,IAAIC,OAAO,IAAIP,GAAG,GAAG,KAAK,CAAC,EAAEzF,QAAQ,CAAC+B,KAAK,CAAC,CAAC,EAAEkE,SAAS,GAAGD,OAAO,CAACvF,IAAI,CAAC,CAAC,EAAE,CAACwF,SAAS,CAACtF,IAAI,EAAEsF,SAAS,GAAGD,OAAO,CAACvF,IAAI,CAAC,CAAC,EAAE;gBACzH,IAAIyF,EAAE,GAAGD,SAAS,CAACvF,KAAK;gBACxB,IAAIc,YAAY,CAAC2E,MAAM,CAACzG,CAAC,EAAEwG,EAAE,CAAC,EAAE;kBAC5BH,KAAK,GAAG,IAAI;kBACZ;gBACJ;cACJ;YACJ,CAAC,CACD,OAAOK,KAAK,EAAE;cAAEX,GAAG,GAAG;gBAAE3C,KAAK,EAAEsD;cAAM,CAAC;YAAE,CAAC,SACjC;cACJ,IAAI;gBACA,IAAIH,SAAS,IAAI,CAACA,SAAS,CAACtF,IAAI,KAAK4D,EAAE,GAAGyB,OAAO,CAAC/B,MAAM,CAAC,EAAEM,EAAE,CAAChE,IAAI,CAACyF,OAAO,CAAC;cAC/E,CAAC,SACO;gBAAE,IAAIP,GAAG,EAAE,MAAMA,GAAG,CAAC3C,KAAK;cAAE;YACxC;YACA,IAAI,CAACiD,KAAK,EAAE;cACRH,QAAQ,GAAG,KAAK;YACpB;UACJ;QACJ,CAAC,CACD,OAAOS,KAAK,EAAE;UAAEb,GAAG,GAAG;YAAE1C,KAAK,EAAEuD;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAIP,EAAE,IAAI,CAACA,EAAE,CAACnF,IAAI,KAAK0D,EAAE,GAAGwB,EAAE,CAAC5B,MAAM,CAAC,EAAEI,EAAE,CAAC9D,IAAI,CAACsF,EAAE,CAAC;UACvD,CAAC,SACO;YAAE,IAAIL,GAAG,EAAE,MAAMA,GAAG,CAAC1C,KAAK;UAAE;QACxC;MACJ;IACJ,CAAC,CACD,OAAOwD,KAAK,EAAE;MAAEf,GAAG,GAAG;QAAEzC,KAAK,EAAEwD;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIX,QAAQ,IAAI,CAACA,QAAQ,CAAChF,IAAI,KAAKgD,EAAE,GAAG+B,MAAM,CAACzB,MAAM,CAAC,EAAEN,EAAE,CAACpD,IAAI,CAACmF,MAAM,CAAC;MAC3E,CAAC,SACO;QAAE,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAACzC,KAAK;MAAE;IACxC;EACJ,CAAC;EACD;EACApB,iBAAiB,CAAC2D,YAAY,GAAG,UAAUtD,KAAK,EAAEE,IAAI,EAAE;IACpD,IAAIsE,GAAG,EAAE5C,EAAE,EAAE6C,GAAG,EAAEnC,EAAE,EAAEoC,GAAG,EAAElC,EAAE;IAC7B,IAAI;MACA,KAAK,IAAImC,MAAM,GAAG1G,QAAQ,CAACiC,IAAI,CAAC,EAAE0E,QAAQ,GAAGD,MAAM,CAACjG,IAAI,CAAC,CAAC,EAAE,CAACkG,QAAQ,CAAChG,IAAI,EAAEgG,QAAQ,GAAGD,MAAM,CAACjG,IAAI,CAAC,CAAC,EAAE;QAClG,IAAImG,CAAC,GAAGD,QAAQ,CAACjG,KAAK;QACtB,IAAIkF,QAAQ,GAAG,IAAI;QACnB,IAAI;UACA,KAAK,IAAIiB,OAAO,IAAIL,GAAG,GAAG,KAAK,CAAC,EAAExG,QAAQ,CAAC+B,KAAK,CAAC,CAAC,EAAE+E,SAAS,GAAGD,OAAO,CAACpG,IAAI,CAAC,CAAC,EAAE,CAACqG,SAAS,CAACnG,IAAI,EAAEmG,SAAS,GAAGD,OAAO,CAACpG,IAAI,CAAC,CAAC,EAAE;YACzH,IAAIf,CAAC,GAAGoH,SAAS,CAACpG,KAAK;YACvB,IAAIqF,KAAK,GAAG,KAAK;YACjB,IAAI;cACA,KAAK,IAAIF,EAAE,IAAIY,GAAG,GAAG,KAAK,CAAC,EAAEzG,QAAQ,CAAC4G,CAAC,CAAC7C,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE+B,EAAE,GAAGD,EAAE,CAACpF,IAAI,CAAC,CAAC,EAAE,CAACqF,EAAE,CAACnF,IAAI,EAAEmF,EAAE,GAAGD,EAAE,CAACpF,IAAI,CAAC,CAAC,EAAE;gBAC5F,IAAIyF,EAAE,GAAGJ,EAAE,CAACpF,KAAK;gBACjB,IAAIhB,CAAC,CAACyG,MAAM,CAACD,EAAE,CAAC,EAAE;kBACdH,KAAK,GAAG,IAAI;kBACZ;gBACJ;cACJ;YACJ,CAAC,CACD,OAAOgB,KAAK,EAAE;cAAEN,GAAG,GAAG;gBAAE3D,KAAK,EAAEiE;cAAM,CAAC;YAAE,CAAC,SACjC;cACJ,IAAI;gBACA,IAAIjB,EAAE,IAAI,CAACA,EAAE,CAACnF,IAAI,KAAK4D,EAAE,GAAGsB,EAAE,CAAC5B,MAAM,CAAC,EAAEM,EAAE,CAAChE,IAAI,CAACsF,EAAE,CAAC;cACvD,CAAC,SACO;gBAAE,IAAIY,GAAG,EAAE,MAAMA,GAAG,CAAC3D,KAAK;cAAE;YACxC;YACA,IAAI,CAACiD,KAAK,EAAE;cACRH,QAAQ,GAAG,KAAK;cAChB;YACJ;UACJ;QACJ,CAAC,CACD,OAAOoB,KAAK,EAAE;UAAER,GAAG,GAAG;YAAE1D,KAAK,EAAEkE;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAIF,SAAS,IAAI,CAACA,SAAS,CAACnG,IAAI,KAAK0D,EAAE,GAAGwC,OAAO,CAAC5C,MAAM,CAAC,EAAEI,EAAE,CAAC9D,IAAI,CAACsG,OAAO,CAAC;UAC/E,CAAC,SACO;YAAE,IAAIL,GAAG,EAAE,MAAMA,GAAG,CAAC1D,KAAK;UAAE;QACxC;QACA,IAAI8C,QAAQ,EAAE;UACV;UACA,OAAO,IAAI;QACf;MACJ;IACJ,CAAC,CACD,OAAOqB,KAAK,EAAE;MAAEV,GAAG,GAAG;QAAEzD,KAAK,EAAEmE;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIN,QAAQ,IAAI,CAACA,QAAQ,CAAChG,IAAI,KAAKgD,EAAE,GAAG+C,MAAM,CAACzC,MAAM,CAAC,EAAEN,EAAE,CAACpD,IAAI,CAACmG,MAAM,CAAC;MAC3E,CAAC,SACO;QAAE,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAACzD,KAAK;MAAE;IACxC;IACA,OAAO,KAAK;EAChB,CAAC;EACD;EACApB,iBAAiB,CAAC5B,SAAS,CAACoH,OAAO,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACjF,IAAI;EACpB,CAAC;EACD;EACAP,iBAAiB,CAACc,eAAe,GAAG,UAAUT,KAAK,EAAE;IACjD,IAAIoF,MAAM,GAAG7F,eAAe,CAAC8F,aAAa,CAACrF,KAAK,CAAC;IACjD,IAAIsF,OAAO,GAAG9F,aAAa,CAAC4F,MAAM,CAAC;IACnC,IAAIG,eAAe,GAAGD,OAAO,CAACE,gBAAgB,CAAC,CAAC;IAChD,IAAIC,WAAW,GAAGzF,KAAK,CAAC,CAAC,CAAC,CAAC4C,gBAAgB,CAAC,CAAC,CAAC8C,eAAe,CAAC,CAAC;IAC/D,IAAIC,UAAU,GAAG3F,KAAK,CAACA,KAAK,CAACvB,MAAM,GAAG,CAAC,CAAC,CACnCmE,gBAAgB,CAAC,CAAC,CAClB8C,eAAe,CAAC,CAAC;IACtB,IAAIE,MAAM,GAAG,CAACH,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,EAAEE,UAAU,CAAC,CAAC,CAAC,EAAEA,UAAU,CAAC,CAAC,CAAC,CAAC;IAC3E,OAAO,IAAI1G,MAAM,CAACsG,eAAe,EAAE,IAAI,EAAE,IAAI,EAAEK,MAAM,EAAE9G,aAAa,CAAC+G,YAAY,EAAE,IAAI,CAAC;EAC5F,CAAC;EACDlG,iBAAiB,CAAC5B,SAAS,CAACiD,aAAa,GAAG,YAAY;IACpD,IAAI8E,SAAS,GAAG,IAAI,CAAC9F,KAAK,CAAC+F,GAAG,CAAC,CAAC,CAAC;IACjC,IAAIC,cAAc,GAAGF,SAAS,CAACG,WAAW,CAAC,CAAC;IAC5C,IAAIC,cAAc,GAAGJ,SAAS,CAACK,YAAY,CAAC,CAAC;IAC7C,IAAID,cAAc,KAAK,IAAI,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,IAAIE,QAAQ,GAAGF,cAAc,CAACG,kBAAkB,CAAC,CAAC;IAClD,IAAIlI,CAAC,GAAG,CAAC;IACT,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACyB,KAAK,CAACsG,IAAI,CAAC,CAAC,EAAE,EAAE/H,CAAC,EAAE;MACxC,IAAIgI,WAAW,GAAG,IAAI,CAACvG,KAAK,CAAC+F,GAAG,CAACxH,CAAC,CAAC;MACnC6H,QAAQ,IAAIG,WAAW,CAACN,WAAW,CAAC,CAAC,CAACI,kBAAkB,CAAC,CAAC;MAC1DlI,CAAC,EAAE;MACH,IAAIqI,gBAAgB,GAAGD,WAAW,CAACJ,YAAY,CAAC,CAAC;MACjD,IAAIK,gBAAgB,IAAI,IAAI,EAAE;QAC1BJ,QAAQ,IAAII,gBAAgB,CAACH,kBAAkB,CAAC,CAAC;QACjDlI,CAAC,EAAE;MACP;IACJ;IACAiI,QAAQ,IAAI,GAAG;IACf,IAAIK,mBAAmB,GAAG,GAAG,IAAItI,CAAC,GAAG,CAAC,CAAC,GAAGiI,QAAQ;IAClD,OAAOK,mBAAmB,KAAKT,cAAc,CAACnD,QAAQ,CAAC,CAAC;EAC5D,CAAC;EACDlD,iBAAiB,CAAC+G,gBAAgB,GAAG,UAAUpG,GAAG,EAAEqG,UAAU,EAAE;IAC5D,IAAIC,UAAU;IACd,IAAItG,GAAG,CAACyF,GAAG,CAACY,UAAU,CAAC,EAAE;MACrBC,UAAU,GAAGtG,GAAG,CAACuG,YAAY,CAACF,UAAU,CAAC;MACzCC,UAAU,GAAGtG,GAAG,CAACwG,UAAU,CAACF,UAAU,CAAC;IAC3C,CAAC,MACI;MACDA,UAAU,GAAGtG,GAAG,CAACwG,UAAU,CAACH,UAAU,CAAC;MACvCC,UAAU,GAAGtG,GAAG,CAACuG,YAAY,CAACD,UAAU,CAAC;IAC7C;IACA,OAAOA,UAAU;EACrB,CAAC;EACD;EACAjH,iBAAiB,CAAC5B,SAAS,CAAC+C,gBAAgB,GAAG,UAAUR,GAAG,EAAEyG,aAAa,EAAE1G,SAAS,EAAE;IACpF,IAAI2G,YAAY,GAAGD,aAAa,CAACtI,MAAM,GAAG,CAAC,KAAK,CAAC;IACjD,IAAI,IAAI,CAAC+B,aAAa,EAAE;MACpBwG,YAAY,GAAG,CAACA,YAAY;IAChC;IACA,IAAIC,OAAO;IACX,IAAIC,WAAW,GAAG,IAAI;IACtB,IAAIC,YAAY,GAAG,CAAC,CAAC;IACrB,GAAG;MACC,IAAI,CAACC,YAAY,CAAC9G,GAAG,EAAEyG,aAAa,EAAEI,YAAY,CAAC;MACnDF,OAAO,GAAG,IAAI,CAACI,uBAAuB,CAAC/G,GAAG,EAAED,SAAS,EAAE2G,YAAY,CAAC;MACpE,IAAIC,OAAO,KAAK,IAAI,EAAE;QAClBE,YAAY,GAAGxH,iBAAiB,CAAC+G,gBAAgB,CAACpG,GAAG,EAAE,IAAI,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAC;MAC5E,CAAC,MACI;QACD+G,WAAW,GAAG,KAAK;MACvB;IACJ,CAAC,QAAQA,WAAW;IACpB;IACA;IACA,IAAII,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAACjH,GAAG,EAAE2G,OAAO,EAAED,YAAY,EAAE,IAAI,CAAC;IACzE,IAAI,CAAC,IAAI,CAACQ,WAAW,CAACT,aAAa,CAAC,IAChCA,aAAa,CAACA,aAAa,CAACtI,MAAM,GAAG,CAAC,CAAC,CAACgJ,UAAU,CAAC,CAAC,EAAE;MACtD,MAAM,IAAIzI,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI0I,SAAS;IACb,IAAI;MACAA,SAAS,GAAG,IAAI,CAACH,mBAAmB,CAACjH,GAAG,EAAE2G,OAAO,EAAED,YAAY,EAAE,KAAK,CAAC;IAC3E,CAAC,CACD,OAAOrG,CAAC,EAAE;MACN+G,SAAS,GAAG,IAAI;MAChBnG,OAAO,CAACC,GAAG,CAACb,CAAC,CAAC;IAClB;IACA,OAAO,IAAIlB,YAAY,CAAC6H,QAAQ,EAAEI,SAAS,EAAET,OAAO,EAAE,IAAI,CAAC;EAC/D,CAAC;EACDtH,iBAAiB,CAAC5B,SAAS,CAACyJ,WAAW,GAAG,UAAUxH,KAAK,EAAE;IACvD,IAAIA,KAAK,CAACvB,MAAM,KAAK,CAAC,EAAE;MACpB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB,CAAC;EACDkB,iBAAiB,CAAC5B,SAAS,CAACqJ,YAAY,GAAG,UAAU9G,GAAG,EAAEyG,aAAa,EAAEI,YAAY,EAAE;IACnF,IAAIQ,QAAQ,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC7CD,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACf,IAAIE,KAAK,GAAGvH,GAAG,CAACwH,OAAO,CAAC,CAAC;IACzB,IAAIC,SAAS;IACb,IAAIZ,YAAY,IAAI,CAAC,EAAE;MACnBY,SAAS,GAAGZ,YAAY;IAC5B,CAAC,MACI,IAAI,IAAI,CAACK,WAAW,CAACT,aAAa,CAAC,EAAE;MACtCgB,SAAS,GAAG,CAAC;IACjB,CAAC,MACI;MACD,IAAIC,QAAQ,GAAGjB,aAAa,CAACA,aAAa,CAACtI,MAAM,GAAG,CAAC,CAAC;MACtDsJ,SAAS,GAAGC,QAAQ,CAACpF,gBAAgB,CAAC,CAAC,CAACqF,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D;IACA,IAAIC,iBAAiB,GAAGnB,aAAa,CAACtI,MAAM,GAAG,CAAC,KAAK,CAAC;IACtD,IAAI,IAAI,CAAC+B,aAAa,EAAE;MACpB0H,iBAAiB,GAAG,CAACA,iBAAiB;IAC1C;IACA,IAAIC,OAAO,GAAG,KAAK;IACnB,OAAOJ,SAAS,GAAGF,KAAK,EAAE;MACtBM,OAAO,GAAG,CAAC7H,GAAG,CAACyF,GAAG,CAACgC,SAAS,CAAC;MAC7B,IAAI,CAACI,OAAO,EAAE;QACV;MACJ;MACAJ,SAAS,EAAE;IACf;IACA,IAAIK,eAAe,GAAG,CAAC;IACvB,IAAIC,YAAY,GAAGN,SAAS;IAC5B,KAAK,IAAIO,CAAC,GAAGP,SAAS,EAAEO,CAAC,GAAGT,KAAK,EAAES,CAAC,EAAE,EAAE;MACpC,IAAIhI,GAAG,CAACyF,GAAG,CAACuC,CAAC,CAAC,KAAKH,OAAO,EAAE;QACxBR,QAAQ,CAACS,eAAe,CAAC,EAAE;MAC/B,CAAC,MACI;QACD,IAAIA,eAAe,KAAK,CAAC,EAAE;UACvB,IAAIF,iBAAiB,EAAE;YACnBvI,iBAAiB,CAAC4I,eAAe,CAACZ,QAAQ,CAAC;UAC/C;UACA,IAAIhI,iBAAiB,CAAC6I,eAAe,CAACb,QAAQ,CAAC,EAAE;YAC7C,IAAI,CAACxH,QAAQ,CAAC,CAAC,CAAC,GAAGkI,YAAY;YAC/B,IAAI,CAAClI,QAAQ,CAAC,CAAC,CAAC,GAAGmI,CAAC;YACpB;UACJ;UACA,IAAIJ,iBAAiB,EAAE;YACnBvI,iBAAiB,CAAC4I,eAAe,CAACZ,QAAQ,CAAC;UAC/C;UACAU,YAAY,IAAIV,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzCA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzBA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzBA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;UACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;UACfS,eAAe,EAAE;QACrB,CAAC,MACI;UACDA,eAAe,EAAE;QACrB;QACAT,QAAQ,CAACS,eAAe,CAAC,GAAG,CAAC;QAC7BD,OAAO,GAAG,CAACA,OAAO;MACtB;IACJ;IACA,MAAM,IAAInJ,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACDW,iBAAiB,CAAC4I,eAAe,GAAG,UAAUZ,QAAQ,EAAE;IACpD,IAAIlJ,MAAM,GAAGkJ,QAAQ,CAAClJ,MAAM;IAC5B,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC,EAAE,EAAEF,CAAC,EAAE;MACjC,IAAIkK,GAAG,GAAGd,QAAQ,CAACpJ,CAAC,CAAC;MACrBoJ,QAAQ,CAACpJ,CAAC,CAAC,GAAGoJ,QAAQ,CAAClJ,MAAM,GAAGF,CAAC,GAAG,CAAC,CAAC;MACtCoJ,QAAQ,CAAClJ,MAAM,GAAGF,CAAC,GAAG,CAAC,CAAC,GAAGkK,GAAG;IAClC;EACJ,CAAC;EACD9I,iBAAiB,CAAC5B,SAAS,CAACsJ,uBAAuB,GAAG,UAAU/G,GAAG,EAAED,SAAS,EAAEqI,UAAU,EAAE;IACxF;IACA,IAAIC,YAAY;IAChB,IAAIC,KAAK;IACT,IAAIC,GAAG;IACP,IAAIH,UAAU,EAAE;MACZ;MACA,IAAII,iBAAiB,GAAG,IAAI,CAAC3I,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;MAC5C;MACA,OAAO2I,iBAAiB,IAAI,CAAC,IAAI,CAACxI,GAAG,CAACyF,GAAG,CAAC+C,iBAAiB,CAAC,EAAE;QAC1DA,iBAAiB,EAAE;MACvB;MACAA,iBAAiB,EAAE;MACnBH,YAAY,GAAG,IAAI,CAACxI,QAAQ,CAAC,CAAC,CAAC,GAAG2I,iBAAiB;MACnDF,KAAK,GAAGE,iBAAiB;MACzBD,GAAG,GAAG,IAAI,CAAC1I,QAAQ,CAAC,CAAC,CAAC;IAC1B,CAAC,MACI;MACD;MACAyI,KAAK,GAAG,IAAI,CAACzI,QAAQ,CAAC,CAAC,CAAC;MACxB0I,GAAG,GAAGvI,GAAG,CAACuG,YAAY,CAAC,IAAI,CAAC1G,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAC5CwI,YAAY,GAAGE,GAAG,GAAG,IAAI,CAAC1I,QAAQ,CAAC,CAAC,CAAC;IACzC;IACA;IACA,IAAIwH,QAAQ,GAAG,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC7C1I,MAAM,CAAC6J,SAAS,CAACpB,QAAQ,EAAE,CAAC,EAAEA,QAAQ,EAAE,CAAC,EAAEA,QAAQ,CAAClJ,MAAM,GAAG,CAAC,CAAC;IAC/DkJ,QAAQ,CAAC,CAAC,CAAC,GAAGgB,YAAY;IAC1B,IAAIhK,KAAK;IACT,IAAI;MACAA,KAAK,GAAG,IAAI,CAACqK,gBAAgB,CAACrB,QAAQ,EAAEhI,iBAAiB,CAACsJ,eAAe,CAAC;IAC9E,CAAC,CACD,OAAOtI,CAAC,EAAE;MACN,OAAO,IAAI;IACf;IACA;IACA,OAAO,IAAItB,aAAa,CAACV,KAAK,EAAE,CAACiK,KAAK,EAAEC,GAAG,CAAC,EAAED,KAAK,EAAEC,GAAG,EAAExI,SAAS,CAAC;EACxE,CAAC;EACDV,iBAAiB,CAAC5B,SAAS,CAACwJ,mBAAmB,GAAG,UAAUjH,GAAG,EAAE2G,OAAO,EAAED,YAAY,EAAEM,QAAQ,EAAE;IAC9F,IAAIK,QAAQ,GAAG,IAAI,CAACuB,wBAAwB,CAAC,CAAC;IAC9C,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,QAAQ,CAAClJ,MAAM,EAAE6J,CAAC,EAAE,EAAE;MACtCX,QAAQ,CAACW,CAAC,CAAC,GAAG,CAAC;IACnB;IACA,IAAIhB,QAAQ,EAAE;MACV3H,iBAAiB,CAACwJ,sBAAsB,CAAC7I,GAAG,EAAE2G,OAAO,CAACgB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,QAAQ,CAAC;IACrF,CAAC,MACI;MACDhI,iBAAiB,CAACyJ,aAAa,CAAC9I,GAAG,EAAE2G,OAAO,CAACgB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,QAAQ,CAAC;MACxE;MACA,KAAK,IAAIpJ,CAAC,GAAG,CAAC,EAAEoE,CAAC,GAAGgF,QAAQ,CAAClJ,MAAM,GAAG,CAAC,EAAEF,CAAC,GAAGoE,CAAC,EAAEpE,CAAC,EAAE,EAAEoE,CAAC,EAAE,EAAE;QACtD,IAAI0G,IAAI,GAAG1B,QAAQ,CAACpJ,CAAC,CAAC;QACtBoJ,QAAQ,CAACpJ,CAAC,CAAC,GAAGoJ,QAAQ,CAAChF,CAAC,CAAC;QACzBgF,QAAQ,CAAChF,CAAC,CAAC,GAAG0G,IAAI;MACtB;IACJ,CAAC,CAAC;IACF,IAAIC,UAAU,GAAG,EAAE,CAAC,CAAC;IACrB,IAAIC,YAAY,GAAGxK,SAAS,CAACyK,GAAG,CAAC,IAAIC,UAAU,CAAC9B,QAAQ,CAAC,CAAC,GAAG2B,UAAU;IACvE;IACA,IAAII,oBAAoB,GAAG,CAACzC,OAAO,CAACgB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGhB,OAAO,CAACgB,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;IACvF,IAAI0B,IAAI,CAACC,GAAG,CAACL,YAAY,GAAGG,oBAAoB,CAAC,GAAGA,oBAAoB,GACpE,GAAG,EAAE;MACL,MAAM,IAAI1K,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI6K,SAAS,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;IACnC,IAAIC,UAAU,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACrC,IAAIC,iBAAiB,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACnD,IAAIC,kBAAkB,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACrD,KAAK,IAAI7L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoJ,QAAQ,CAAClJ,MAAM,EAAEF,CAAC,EAAE,EAAE;MACtC,IAAI8L,OAAO,GAAI,GAAG,GAAG1C,QAAQ,CAACpJ,CAAC,CAAC,GAAIgL,YAAY;MAChD,IAAIe,KAAK,GAAGD,OAAO,GAAG,GAAG,CAAC,CAAC;MAC3B,IAAIC,KAAK,GAAG,CAAC,EAAE;QACX,IAAID,OAAO,GAAG,GAAG,EAAE;UACf,MAAM,IAAIrL,iBAAiB,CAAC,CAAC;QACjC;QACAsL,KAAK,GAAG,CAAC;MACb,CAAC,MACI,IAAIA,KAAK,GAAG,CAAC,EAAE;QAChB,IAAID,OAAO,GAAG,GAAG,EAAE;UACf,MAAM,IAAIrL,iBAAiB,CAAC,CAAC;QACjC;QACAsL,KAAK,GAAG,CAAC;MACb;MACA,IAAIC,MAAM,GAAGhM,CAAC,GAAG,CAAC;MAClB,IAAI,CAACA,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE;QAClBsL,SAAS,CAACU,MAAM,CAAC,GAAGD,KAAK;QACzBL,iBAAiB,CAACM,MAAM,CAAC,GAAGF,OAAO,GAAGC,KAAK;MAC/C,CAAC,MACI;QACDP,UAAU,CAACQ,MAAM,CAAC,GAAGD,KAAK;QAC1BH,kBAAkB,CAACI,MAAM,CAAC,GAAGF,OAAO,GAAGC,KAAK;MAChD;IACJ;IACA,IAAI,CAACE,mBAAmB,CAAClB,UAAU,CAAC;IACpC,IAAImB,eAAe,GAAG,CAAC,GAAGxD,OAAO,CAACpE,QAAQ,CAAC,CAAC,IAAImE,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,IAAIM,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9F,IAAIoD,MAAM,GAAG,CAAC;IACd,IAAIC,kBAAkB,GAAG,CAAC;IAC1B,KAAK,IAAIpM,CAAC,GAAGsL,SAAS,CAACpL,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5C,IAAIoB,iBAAiB,CAACiL,WAAW,CAAC3D,OAAO,EAAED,YAAY,EAAEM,QAAQ,CAAC,EAAE;QAChE,IAAIuD,MAAM,GAAGlL,iBAAiB,CAACmL,OAAO,CAACL,eAAe,CAAC,CAAC,CAAC,GAAGlM,CAAC,CAAC;QAC9DoM,kBAAkB,IAAId,SAAS,CAACtL,CAAC,CAAC,GAAGsM,MAAM;MAC/C;MACAH,MAAM,IAAIb,SAAS,CAACtL,CAAC,CAAC;IAC1B;IACA,IAAIwM,mBAAmB,GAAG,CAAC;IAC3B;IACA,KAAK,IAAIxM,CAAC,GAAGwL,UAAU,CAACtL,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC7C,IAAIoB,iBAAiB,CAACiL,WAAW,CAAC3D,OAAO,EAAED,YAAY,EAAEM,QAAQ,CAAC,EAAE;QAChE,IAAIuD,MAAM,GAAGlL,iBAAiB,CAACmL,OAAO,CAACL,eAAe,CAAC,CAAC,CAAC,GAAGlM,CAAC,GAAG,CAAC,CAAC;QAClEwM,mBAAmB,IAAIhB,UAAU,CAACxL,CAAC,CAAC,GAAGsM,MAAM;MACjD;MACA;IACJ;IACA,IAAIG,eAAe,GAAGL,kBAAkB,GAAGI,mBAAmB;IAC9D,IAAI,CAACL,MAAM,GAAG,IAAI,MAAM,CAAC,IAAIA,MAAM,GAAG,EAAE,IAAIA,MAAM,GAAG,CAAC,EAAE;MACpD,MAAM,IAAI1L,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAIiM,KAAK,GAAG,CAAC,EAAE,GAAGP,MAAM,IAAI,CAAC;IAC7B,IAAIQ,SAAS,GAAGvL,iBAAiB,CAACwL,aAAa,CAACF,KAAK,CAAC;IACtD,IAAIG,UAAU,GAAG,CAAC,GAAGF,SAAS;IAC9B,IAAIG,IAAI,GAAG/L,QAAQ,CAACgM,WAAW,CAACzB,SAAS,EAAEqB,SAAS,EAAE,IAAI,CAAC;IAC3D,IAAIK,KAAK,GAAGjM,QAAQ,CAACgM,WAAW,CAACvB,UAAU,EAAEqB,UAAU,EAAE,KAAK,CAAC;IAC/D,IAAII,KAAK,GAAG7L,iBAAiB,CAAC8L,iBAAiB,CAACR,KAAK,CAAC;IACtD,IAAIS,IAAI,GAAG/L,iBAAiB,CAACgM,IAAI,CAACV,KAAK,CAAC;IACxC,IAAItM,KAAK,GAAG0M,IAAI,GAAGG,KAAK,GAAGD,KAAK,GAAGG,IAAI;IACvC,OAAO,IAAItM,aAAa,CAACT,KAAK,EAAEqM,eAAe,CAAC;EACpD,CAAC;EACDrL,iBAAiB,CAACiL,WAAW,GAAG,UAAU3D,OAAO,EAAED,YAAY,EAAEM,QAAQ,EAAE;IACvE;IACA,OAAO,EAAEL,OAAO,CAACpE,QAAQ,CAAC,CAAC,KAAK,CAAC,IAAImE,YAAY,IAAIM,QAAQ,CAAC;EAClE,CAAC;EACD3H,iBAAiB,CAAC5B,SAAS,CAACyM,mBAAmB,GAAG,UAAUlB,UAAU,EAAE;IACpE,IAAIoB,MAAM,GAAG3L,SAAS,CAACyK,GAAG,CAAC,IAAIC,UAAU,CAAC,IAAI,CAACK,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/D,IAAI8B,OAAO,GAAG7M,SAAS,CAACyK,GAAG,CAAC,IAAIC,UAAU,CAAC,IAAI,CAACO,aAAa,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI6B,YAAY,GAAG,KAAK;IACxB,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIpB,MAAM,GAAG,EAAE,EAAE;MACboB,YAAY,GAAG,IAAI;IACvB,CAAC,MACI,IAAIpB,MAAM,GAAG,CAAC,EAAE;MACjBmB,YAAY,GAAG,IAAI;IACvB;IACA,IAAIE,aAAa,GAAG,KAAK;IACzB,IAAIC,aAAa,GAAG,KAAK;IACzB,IAAIJ,OAAO,GAAG,EAAE,EAAE;MACdI,aAAa,GAAG,IAAI;IACxB,CAAC,MACI,IAAIJ,OAAO,GAAG,CAAC,EAAE;MAClBG,aAAa,GAAG,IAAI;IACxB;IACA,IAAIE,QAAQ,GAAGvB,MAAM,GAAGkB,OAAO,GAAGtC,UAAU;IAC5C,IAAI4C,YAAY,GAAG,CAACxB,MAAM,GAAG,IAAI,MAAM,CAAC;IACxC,IAAIyB,aAAa,GAAG,CAACP,OAAO,GAAG,IAAI,MAAM,CAAC;IAC1C,IAAIK,QAAQ,KAAK,CAAC,EAAE;MAChB,IAAIC,YAAY,EAAE;QACd,IAAIC,aAAa,EAAE;UACf,MAAM,IAAInN,iBAAiB,CAAC,CAAC;QACjC;QACA8M,YAAY,GAAG,IAAI;MACvB,CAAC,MACI;QACD,IAAI,CAACK,aAAa,EAAE;UAChB,MAAM,IAAInN,iBAAiB,CAAC,CAAC;QACjC;QACAgN,aAAa,GAAG,IAAI;MACxB;IACJ,CAAC,MACI,IAAIC,QAAQ,KAAK,CAAC,CAAC,EAAE;MACtB,IAAIC,YAAY,EAAE;QACd,IAAIC,aAAa,EAAE;UACf,MAAM,IAAInN,iBAAiB,CAAC,CAAC;QACjC;QACA6M,YAAY,GAAG,IAAI;MACvB,CAAC,MACI;QACD,IAAI,CAACM,aAAa,EAAE;UAChB,MAAM,IAAInN,iBAAiB,CAAC,CAAC;QACjC;QACA+M,aAAa,GAAG,IAAI;MACxB;IACJ,CAAC,MACI,IAAIE,QAAQ,KAAK,CAAC,EAAE;MACrB,IAAIC,YAAY,EAAE;QACd,IAAI,CAACC,aAAa,EAAE;UAChB,MAAM,IAAInN,iBAAiB,CAAC,CAAC;QACjC;QACA;QACA,IAAI0L,MAAM,GAAGkB,OAAO,EAAE;UAClBC,YAAY,GAAG,IAAI;UACnBG,aAAa,GAAG,IAAI;QACxB,CAAC,MACI;UACDF,YAAY,GAAG,IAAI;UACnBC,aAAa,GAAG,IAAI;QACxB;MACJ,CAAC,MACI;QACD,IAAII,aAAa,EAAE;UACf,MAAM,IAAInN,iBAAiB,CAAC,CAAC;QACjC;QACA;MACJ;IACJ,CAAC,MACI;MACD,MAAM,IAAIA,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI6M,YAAY,EAAE;MACd,IAAIC,YAAY,EAAE;QACd,MAAM,IAAI9M,iBAAiB,CAAC,CAAC;MACjC;MACAW,iBAAiB,CAACyM,SAAS,CAAC,IAAI,CAACtC,YAAY,CAAC,CAAC,EAAE,IAAI,CAACI,oBAAoB,CAAC,CAAC,CAAC;IACjF;IACA,IAAI4B,YAAY,EAAE;MACdnM,iBAAiB,CAAC0M,SAAS,CAAC,IAAI,CAACvC,YAAY,CAAC,CAAC,EAAE,IAAI,CAACI,oBAAoB,CAAC,CAAC,CAAC;IACjF;IACA,IAAI6B,aAAa,EAAE;MACf,IAAIC,aAAa,EAAE;QACf,MAAM,IAAIhN,iBAAiB,CAAC,CAAC;MACjC;MACAW,iBAAiB,CAACyM,SAAS,CAAC,IAAI,CAACpC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACE,oBAAoB,CAAC,CAAC,CAAC;IAClF;IACA,IAAI8B,aAAa,EAAE;MACfrM,iBAAiB,CAAC0M,SAAS,CAAC,IAAI,CAACrC,aAAa,CAAC,CAAC,EAAE,IAAI,CAACI,qBAAqB,CAAC,CAAC,CAAC;IACnF;EACJ,CAAC;EACDzK,iBAAiB,CAACwL,aAAa,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjDxL,iBAAiB,CAAC8L,iBAAiB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3D9L,iBAAiB,CAACgM,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACnDhM,iBAAiB,CAACsJ,eAAe,GAAG,CAChCQ,UAAU,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B7C,UAAU,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B7C,UAAU,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B7C,UAAU,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B7C,UAAU,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7B7C,UAAU,CAAC6C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAChC;EACD3M,iBAAiB,CAACmL,OAAO,GAAG,CACxB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAC7B,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAClC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACtC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EACpC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EACrC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EACpC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EACnC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,EACpC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACvC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACpC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACvC,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAClC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACxC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACvC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EACrC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAClC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,EAClC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EACpC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACnC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EACrC,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EACrC,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAC/B,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CACxC;EACDnL,iBAAiB,CAAC4M,YAAY,GAAG,CAAC;EAClC5M,iBAAiB,CAAC6M,YAAY,GAAG,CAAC;EAClC7M,iBAAiB,CAAC8M,YAAY,GAAG,CAAC;EAClC9M,iBAAiB,CAAC+M,YAAY,GAAG,CAAC;EAClC/M,iBAAiB,CAACgN,YAAY,GAAG,CAAC;EAClChN,iBAAiB,CAACiN,YAAY,GAAG,CAAC;EAClCjN,iBAAiB,CAAC4C,wBAAwB,GAAG,CACzC,CAAC5C,iBAAiB,CAAC4M,YAAY,EAAE5M,iBAAiB,CAAC4M,YAAY,CAAC,EAChE,CACI5M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC6M,YAAY,CACjC,EACD,CACI7M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC8M,YAAY,EAC9B9M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC+M,YAAY,CACjC,EACD,CACI/M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAACgN,YAAY,EAC9BhN,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAAC8M,YAAY,CACjC,EACD,CACI9M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAACgN,YAAY,EAC9BhN,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAACiN,YAAY,CACjC,EACD,CACIjN,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAACgN,YAAY,EAC9BhN,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAACgN,YAAY,EAC9BhN,iBAAiB,CAACiN,YAAY,EAC9BjN,iBAAiB,CAACiN,YAAY,CACjC,EACD,CACIjN,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC8M,YAAY,EAC9B9M,iBAAiB,CAAC8M,YAAY,EAC9B9M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAAC+M,YAAY,CACjC,EACD,CACI/M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC8M,YAAY,EAC9B9M,iBAAiB,CAAC8M,YAAY,EAC9B9M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAACgN,YAAY,EAC9BhN,iBAAiB,CAACgN,YAAY,CACjC,EACD,CACIhN,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC8M,YAAY,EAC9B9M,iBAAiB,CAAC8M,YAAY,EAC9B9M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAACgN,YAAY,EAC9BhN,iBAAiB,CAACiN,YAAY,EAC9BjN,iBAAiB,CAACiN,YAAY,CACjC,EACD,CACIjN,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC4M,YAAY,EAC9B5M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC6M,YAAY,EAC9B7M,iBAAiB,CAAC8M,YAAY,EAC9B9M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAAC+M,YAAY,EAC9B/M,iBAAiB,CAACgN,YAAY,EAC9BhN,iBAAiB,CAACgN,YAAY,EAC9BhN,iBAAiB,CAACiN,YAAY,EAC9BjN,iBAAiB,CAACiN,YAAY,CACjC,CACJ;EACDjN,iBAAiB,CAACM,SAAS,GAAG,EAAE;EAChC,OAAON,iBAAiB;AAC5B,CAAC,CAACR,iBAAiB,CAAE;AACrB,eAAeQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}