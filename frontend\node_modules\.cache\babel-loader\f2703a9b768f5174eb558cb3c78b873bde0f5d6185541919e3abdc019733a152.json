{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport AlignmentPattern from './AlignmentPattern';\nimport NotFoundException from '../../NotFoundException';\n/*import java.util.ArrayList;*/\n/*import java.util.List;*/\n/**\n * <p>This class attempts to find alignment patterns in a QR Code. Alignment patterns look like finder\n * patterns but are smaller and appear at regular intervals throughout the image.</p>\n *\n * <p>At the moment this only looks for the bottom-right alignment pattern.</p>\n *\n * <p>This is mostly a simplified copy of {@link FinderPatternFinder}. It is copied,\n * pasted and stripped down here for maximum performance but does unfortunately duplicate\n * some code.</p>\n *\n * <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.</p>\n *\n * <AUTHOR> Owen\n */\nvar AlignmentPatternFinder = /** @class */function () {\n  /**\n   * <p>Creates a finder that will look in a portion of the whole image.</p>\n   *\n   * @param image image to search\n   * @param startX left column from which to start searching\n   * @param startY top row from which to start searching\n   * @param width width of region to search\n   * @param height height of region to search\n   * @param moduleSize estimated module size so far\n   */\n  function AlignmentPatternFinder(image, startX /*int*/, startY /*int*/, width /*int*/, height /*int*/, moduleSize /*float*/, resultPointCallback) {\n    this.image = image;\n    this.startX = startX;\n    this.startY = startY;\n    this.width = width;\n    this.height = height;\n    this.moduleSize = moduleSize;\n    this.resultPointCallback = resultPointCallback;\n    this.possibleCenters = []; // new Array<any>(5))\n    // TYPESCRIPTPORT: array initialization without size as the length is checked below\n    this.crossCheckStateCount = new Int32Array(3);\n  }\n  /**\n   * <p>This method attempts to find the bottom-right alignment pattern in the image. It is a bit messy since\n   * it's pretty performance-critical and so is written to be fast foremost.</p>\n   *\n   * @return {@link AlignmentPattern} if found\n   * @throws NotFoundException if not found\n   */\n  AlignmentPatternFinder.prototype.find = function () {\n    var startX = this.startX;\n    var height = this.height;\n    var width = this.width;\n    var maxJ = startX + width;\n    var middleI = this.startY + height / 2;\n    // We are looking for black/white/black modules in 1:1:1 ratio\n    // this tracks the number of black/white/black modules seen so far\n    var stateCount = new Int32Array(3);\n    var image = this.image;\n    for (var iGen = 0; iGen < height; iGen++) {\n      // Search from middle outwards\n      var i = middleI + ((iGen & 0x01) === 0 ? Math.floor((iGen + 1) / 2) : -Math.floor((iGen + 1) / 2));\n      stateCount[0] = 0;\n      stateCount[1] = 0;\n      stateCount[2] = 0;\n      var j = startX;\n      // Burn off leading white pixels before anything else; if we start in the middle of\n      // a white run, it doesn't make sense to count its length, since we don't know if the\n      // white run continued to the left of the start point\n      while (j < maxJ && !image.get(j, i)) {\n        j++;\n      }\n      var currentState = 0;\n      while (j < maxJ) {\n        if (image.get(j, i)) {\n          // Black pixel\n          if (currentState === 1) {\n            // Counting black pixels\n            stateCount[1]++;\n          } else {\n            // Counting white pixels\n            if (currentState === 2) {\n              // A winner?\n              if (this.foundPatternCross(stateCount)) {\n                // Yes\n                var confirmed = this.handlePossibleCenter(stateCount, i, j);\n                if (confirmed !== null) {\n                  return confirmed;\n                }\n              }\n              stateCount[0] = stateCount[2];\n              stateCount[1] = 1;\n              stateCount[2] = 0;\n              currentState = 1;\n            } else {\n              stateCount[++currentState]++;\n            }\n          }\n        } else {\n          // White pixel\n          if (currentState === 1) {\n            // Counting black pixels\n            currentState++;\n          }\n          stateCount[currentState]++;\n        }\n        j++;\n      }\n      if (this.foundPatternCross(stateCount)) {\n        var confirmed = this.handlePossibleCenter(stateCount, i, maxJ);\n        if (confirmed !== null) {\n          return confirmed;\n        }\n      }\n    }\n    // Hmm, nothing we saw was observed and confirmed twice. If we had\n    // any guess at all, return it.\n    if (this.possibleCenters.length !== 0) {\n      return this.possibleCenters[0];\n    }\n    throw new NotFoundException();\n  };\n  /**\n   * Given a count of black/white/black pixels just seen and an end position,\n   * figures the location of the center of this black/white/black run.\n   */\n  AlignmentPatternFinder.centerFromEnd = function (stateCount, end /*int*/) {\n    return end - stateCount[2] - stateCount[1] / 2.0;\n  };\n  /**\n   * @param stateCount count of black/white/black pixels just read\n   * @return true iff the proportions of the counts is close enough to the 1/1/1 ratios\n   *         used by alignment patterns to be considered a match\n   */\n  AlignmentPatternFinder.prototype.foundPatternCross = function (stateCount) {\n    var moduleSize = this.moduleSize;\n    var maxVariance = moduleSize / 2.0;\n    for (var i = 0; i < 3; i++) {\n      if (Math.abs(moduleSize - stateCount[i]) >= maxVariance) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\n   * <p>After a horizontal scan finds a potential alignment pattern, this method\n   * \"cross-checks\" by scanning down vertically through the center of the possible\n   * alignment pattern to see if the same proportion is detected.</p>\n   *\n   * @param startI row where an alignment pattern was detected\n   * @param centerJ center of the section that appears to cross an alignment pattern\n   * @param maxCount maximum reasonable number of modules that should be\n   * observed in any reading state, based on the results of the horizontal scan\n   * @return vertical center of alignment pattern, or {@link Float#NaN} if not found\n   */\n  AlignmentPatternFinder.prototype.crossCheckVertical = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n    var image = this.image;\n    var maxI = image.getHeight();\n    var stateCount = this.crossCheckStateCount;\n    stateCount[0] = 0;\n    stateCount[1] = 0;\n    stateCount[2] = 0;\n    // Start counting up from center\n    var i = startI;\n    while (i >= 0 && image.get(centerJ, i) && stateCount[1] <= maxCount) {\n      stateCount[1]++;\n      i--;\n    }\n    // If already too many modules in this state or ran off the edge:\n    if (i < 0 || stateCount[1] > maxCount) {\n      return NaN;\n    }\n    while (i >= 0 && !image.get(centerJ, i) && stateCount[0] <= maxCount) {\n      stateCount[0]++;\n      i--;\n    }\n    if (stateCount[0] > maxCount) {\n      return NaN;\n    }\n    // Now also count down from center\n    i = startI + 1;\n    while (i < maxI && image.get(centerJ, i) && stateCount[1] <= maxCount) {\n      stateCount[1]++;\n      i++;\n    }\n    if (i === maxI || stateCount[1] > maxCount) {\n      return NaN;\n    }\n    while (i < maxI && !image.get(centerJ, i) && stateCount[2] <= maxCount) {\n      stateCount[2]++;\n      i++;\n    }\n    if (stateCount[2] > maxCount) {\n      return NaN;\n    }\n    var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];\n    if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {\n      return NaN;\n    }\n    return this.foundPatternCross(stateCount) ? AlignmentPatternFinder.centerFromEnd(stateCount, i) : NaN;\n  };\n  /**\n   * <p>This is called when a horizontal scan finds a possible alignment pattern. It will\n   * cross check with a vertical scan, and if successful, will see if this pattern had been\n   * found on a previous horizontal scan. If so, we consider it confirmed and conclude we have\n   * found the alignment pattern.</p>\n   *\n   * @param stateCount reading state module counts from horizontal scan\n   * @param i row where alignment pattern may be found\n   * @param j end of possible alignment pattern in row\n   * @return {@link AlignmentPattern} if we have found the same pattern twice, or null if not\n   */\n  AlignmentPatternFinder.prototype.handlePossibleCenter = function (stateCount, i /*int*/, j /*int*/) {\n    var e_1, _a;\n    var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];\n    var centerJ = AlignmentPatternFinder.centerFromEnd(stateCount, j);\n    var centerI = this.crossCheckVertical(i, /*(int) */centerJ, 2 * stateCount[1], stateCountTotal);\n    if (!isNaN(centerI)) {\n      var estimatedModuleSize = (stateCount[0] + stateCount[1] + stateCount[2]) / 3.0;\n      try {\n        for (var _b = __values(this.possibleCenters), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var center = _c.value;\n          // Look for about the same center and module size:\n          if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {\n            return center.combineEstimate(centerI, centerJ, estimatedModuleSize);\n          }\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      // Hadn't found this before; save it\n      var point = new AlignmentPattern(centerJ, centerI, estimatedModuleSize);\n      this.possibleCenters.push(point);\n      if (this.resultPointCallback !== null && this.resultPointCallback !== undefined) {\n        this.resultPointCallback.foundPossibleResultPoint(point);\n      }\n    }\n    return null;\n  };\n  return AlignmentPatternFinder;\n}();\nexport default AlignmentPatternFinder;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "AlignmentPattern", "NotFoundException", "AlignmentPatternFinder", "image", "startX", "startY", "width", "height", "moduleSize", "resultPointCallback", "possibleCenters", "crossCheckStateCount", "Int32Array", "prototype", "find", "maxJ", "middleI", "stateCount", "iGen", "Math", "floor", "j", "get", "currentState", "foundPatternCross", "confirmed", "handlePossibleCenter", "centerFromEnd", "end", "max<PERSON><PERSON>ce", "abs", "crossCheckVertical", "startI", "centerJ", "maxCount", "originalStateCountTotal", "maxI", "getHeight", "NaN", "stateCountTotal", "e_1", "_a", "centerI", "isNaN", "estimatedModuleSize", "_b", "_c", "center", "aboutEquals", "combineEstimate", "e_1_1", "error", "return", "point", "push", "undefined", "foundPossibleResultPoint"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/detector/AlignmentPatternFinder.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport AlignmentPattern from './AlignmentPattern';\nimport NotFoundException from '../../NotFoundException';\n/*import java.util.ArrayList;*/\n/*import java.util.List;*/\n/**\n * <p>This class attempts to find alignment patterns in a QR Code. Alignment patterns look like finder\n * patterns but are smaller and appear at regular intervals throughout the image.</p>\n *\n * <p>At the moment this only looks for the bottom-right alignment pattern.</p>\n *\n * <p>This is mostly a simplified copy of {@link FinderPatternFinder}. It is copied,\n * pasted and stripped down here for maximum performance but does unfortunately duplicate\n * some code.</p>\n *\n * <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.</p>\n *\n * <AUTHOR> Owen\n */\nvar AlignmentPatternFinder = /** @class */ (function () {\n    /**\n     * <p>Creates a finder that will look in a portion of the whole image.</p>\n     *\n     * @param image image to search\n     * @param startX left column from which to start searching\n     * @param startY top row from which to start searching\n     * @param width width of region to search\n     * @param height height of region to search\n     * @param moduleSize estimated module size so far\n     */\n    function AlignmentPatternFinder(image, startX /*int*/, startY /*int*/, width /*int*/, height /*int*/, moduleSize /*float*/, resultPointCallback) {\n        this.image = image;\n        this.startX = startX;\n        this.startY = startY;\n        this.width = width;\n        this.height = height;\n        this.moduleSize = moduleSize;\n        this.resultPointCallback = resultPointCallback;\n        this.possibleCenters = []; // new Array<any>(5))\n        // TYPESCRIPTPORT: array initialization without size as the length is checked below\n        this.crossCheckStateCount = new Int32Array(3);\n    }\n    /**\n     * <p>This method attempts to find the bottom-right alignment pattern in the image. It is a bit messy since\n     * it's pretty performance-critical and so is written to be fast foremost.</p>\n     *\n     * @return {@link AlignmentPattern} if found\n     * @throws NotFoundException if not found\n     */\n    AlignmentPatternFinder.prototype.find = function () {\n        var startX = this.startX;\n        var height = this.height;\n        var width = this.width;\n        var maxJ = startX + width;\n        var middleI = this.startY + (height / 2);\n        // We are looking for black/white/black modules in 1:1:1 ratio\n        // this tracks the number of black/white/black modules seen so far\n        var stateCount = new Int32Array(3);\n        var image = this.image;\n        for (var iGen = 0; iGen < height; iGen++) {\n            // Search from middle outwards\n            var i = middleI + ((iGen & 0x01) === 0 ? Math.floor((iGen + 1) / 2) : -Math.floor((iGen + 1) / 2));\n            stateCount[0] = 0;\n            stateCount[1] = 0;\n            stateCount[2] = 0;\n            var j = startX;\n            // Burn off leading white pixels before anything else; if we start in the middle of\n            // a white run, it doesn't make sense to count its length, since we don't know if the\n            // white run continued to the left of the start point\n            while (j < maxJ && !image.get(j, i)) {\n                j++;\n            }\n            var currentState = 0;\n            while (j < maxJ) {\n                if (image.get(j, i)) {\n                    // Black pixel\n                    if (currentState === 1) { // Counting black pixels\n                        stateCount[1]++;\n                    }\n                    else { // Counting white pixels\n                        if (currentState === 2) { // A winner?\n                            if (this.foundPatternCross(stateCount)) { // Yes\n                                var confirmed = this.handlePossibleCenter(stateCount, i, j);\n                                if (confirmed !== null) {\n                                    return confirmed;\n                                }\n                            }\n                            stateCount[0] = stateCount[2];\n                            stateCount[1] = 1;\n                            stateCount[2] = 0;\n                            currentState = 1;\n                        }\n                        else {\n                            stateCount[++currentState]++;\n                        }\n                    }\n                }\n                else { // White pixel\n                    if (currentState === 1) { // Counting black pixels\n                        currentState++;\n                    }\n                    stateCount[currentState]++;\n                }\n                j++;\n            }\n            if (this.foundPatternCross(stateCount)) {\n                var confirmed = this.handlePossibleCenter(stateCount, i, maxJ);\n                if (confirmed !== null) {\n                    return confirmed;\n                }\n            }\n        }\n        // Hmm, nothing we saw was observed and confirmed twice. If we had\n        // any guess at all, return it.\n        if (this.possibleCenters.length !== 0) {\n            return this.possibleCenters[0];\n        }\n        throw new NotFoundException();\n    };\n    /**\n     * Given a count of black/white/black pixels just seen and an end position,\n     * figures the location of the center of this black/white/black run.\n     */\n    AlignmentPatternFinder.centerFromEnd = function (stateCount, end /*int*/) {\n        return (end - stateCount[2]) - stateCount[1] / 2.0;\n    };\n    /**\n     * @param stateCount count of black/white/black pixels just read\n     * @return true iff the proportions of the counts is close enough to the 1/1/1 ratios\n     *         used by alignment patterns to be considered a match\n     */\n    AlignmentPatternFinder.prototype.foundPatternCross = function (stateCount) {\n        var moduleSize = this.moduleSize;\n        var maxVariance = moduleSize / 2.0;\n        for (var i = 0; i < 3; i++) {\n            if (Math.abs(moduleSize - stateCount[i]) >= maxVariance) {\n                return false;\n            }\n        }\n        return true;\n    };\n    /**\n     * <p>After a horizontal scan finds a potential alignment pattern, this method\n     * \"cross-checks\" by scanning down vertically through the center of the possible\n     * alignment pattern to see if the same proportion is detected.</p>\n     *\n     * @param startI row where an alignment pattern was detected\n     * @param centerJ center of the section that appears to cross an alignment pattern\n     * @param maxCount maximum reasonable number of modules that should be\n     * observed in any reading state, based on the results of the horizontal scan\n     * @return vertical center of alignment pattern, or {@link Float#NaN} if not found\n     */\n    AlignmentPatternFinder.prototype.crossCheckVertical = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n        var image = this.image;\n        var maxI = image.getHeight();\n        var stateCount = this.crossCheckStateCount;\n        stateCount[0] = 0;\n        stateCount[1] = 0;\n        stateCount[2] = 0;\n        // Start counting up from center\n        var i = startI;\n        while (i >= 0 && image.get(centerJ, i) && stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            i--;\n        }\n        // If already too many modules in this state or ran off the edge:\n        if (i < 0 || stateCount[1] > maxCount) {\n            return NaN;\n        }\n        while (i >= 0 && !image.get(centerJ, i) && stateCount[0] <= maxCount) {\n            stateCount[0]++;\n            i--;\n        }\n        if (stateCount[0] > maxCount) {\n            return NaN;\n        }\n        // Now also count down from center\n        i = startI + 1;\n        while (i < maxI && image.get(centerJ, i) && stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            i++;\n        }\n        if (i === maxI || stateCount[1] > maxCount) {\n            return NaN;\n        }\n        while (i < maxI && !image.get(centerJ, i) && stateCount[2] <= maxCount) {\n            stateCount[2]++;\n            i++;\n        }\n        if (stateCount[2] > maxCount) {\n            return NaN;\n        }\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];\n        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {\n            return NaN;\n        }\n        return this.foundPatternCross(stateCount) ? AlignmentPatternFinder.centerFromEnd(stateCount, i) : NaN;\n    };\n    /**\n     * <p>This is called when a horizontal scan finds a possible alignment pattern. It will\n     * cross check with a vertical scan, and if successful, will see if this pattern had been\n     * found on a previous horizontal scan. If so, we consider it confirmed and conclude we have\n     * found the alignment pattern.</p>\n     *\n     * @param stateCount reading state module counts from horizontal scan\n     * @param i row where alignment pattern may be found\n     * @param j end of possible alignment pattern in row\n     * @return {@link AlignmentPattern} if we have found the same pattern twice, or null if not\n     */\n    AlignmentPatternFinder.prototype.handlePossibleCenter = function (stateCount, i /*int*/, j /*int*/) {\n        var e_1, _a;\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2];\n        var centerJ = AlignmentPatternFinder.centerFromEnd(stateCount, j);\n        var centerI = this.crossCheckVertical(i, /*(int) */ centerJ, 2 * stateCount[1], stateCountTotal);\n        if (!isNaN(centerI)) {\n            var estimatedModuleSize = (stateCount[0] + stateCount[1] + stateCount[2]) / 3.0;\n            try {\n                for (var _b = __values(this.possibleCenters), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var center = _c.value;\n                    // Look for about the same center and module size:\n                    if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {\n                        return center.combineEstimate(centerI, centerJ, estimatedModuleSize);\n                    }\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            // Hadn't found this before; save it\n            var point = new AlignmentPattern(centerJ, centerI, estimatedModuleSize);\n            this.possibleCenters.push(point);\n            if (this.resultPointCallback !== null && this.resultPointCallback !== undefined) {\n                this.resultPointCallback.foundPossibleResultPoint(point);\n            }\n        }\n        return null;\n    };\n    return AlignmentPatternFinder;\n}());\nexport default AlignmentPatternFinder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,aAAe,YAAY;EACpD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,sBAAsBA,CAACC,KAAK,EAAEC,MAAM,CAAC,SAASC,MAAM,CAAC,SAASC,KAAK,CAAC,SAASC,MAAM,CAAC,SAASC,UAAU,CAAC,WAAWC,mBAAmB,EAAE;IAC7I,IAAI,CAACN,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,eAAe,GAAG,EAAE,CAAC,CAAC;IAC3B;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;EACjD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIV,sBAAsB,CAACW,SAAS,CAACC,IAAI,GAAG,YAAY;IAChD,IAAIV,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIG,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAID,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIS,IAAI,GAAGX,MAAM,GAAGE,KAAK;IACzB,IAAIU,OAAO,GAAG,IAAI,CAACX,MAAM,GAAIE,MAAM,GAAG,CAAE;IACxC;IACA;IACA,IAAIU,UAAU,GAAG,IAAIL,UAAU,CAAC,CAAC,CAAC;IAClC,IAAIT,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAIe,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGX,MAAM,EAAEW,IAAI,EAAE,EAAE;MACtC;MACA,IAAIzB,CAAC,GAAGuB,OAAO,IAAI,CAACE,IAAI,GAAG,IAAI,MAAM,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACF,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAACC,IAAI,CAACC,KAAK,CAAC,CAACF,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;MAClGD,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACjB,IAAII,CAAC,GAAGjB,MAAM;MACd;MACA;MACA;MACA,OAAOiB,CAAC,GAAGN,IAAI,IAAI,CAACZ,KAAK,CAACmB,GAAG,CAACD,CAAC,EAAE5B,CAAC,CAAC,EAAE;QACjC4B,CAAC,EAAE;MACP;MACA,IAAIE,YAAY,GAAG,CAAC;MACpB,OAAOF,CAAC,GAAGN,IAAI,EAAE;QACb,IAAIZ,KAAK,CAACmB,GAAG,CAACD,CAAC,EAAE5B,CAAC,CAAC,EAAE;UACjB;UACA,IAAI8B,YAAY,KAAK,CAAC,EAAE;YAAE;YACtBN,UAAU,CAAC,CAAC,CAAC,EAAE;UACnB,CAAC,MACI;YAAE;YACH,IAAIM,YAAY,KAAK,CAAC,EAAE;cAAE;cACtB,IAAI,IAAI,CAACC,iBAAiB,CAACP,UAAU,CAAC,EAAE;gBAAE;gBACtC,IAAIQ,SAAS,GAAG,IAAI,CAACC,oBAAoB,CAACT,UAAU,EAAExB,CAAC,EAAE4B,CAAC,CAAC;gBAC3D,IAAII,SAAS,KAAK,IAAI,EAAE;kBACpB,OAAOA,SAAS;gBACpB;cACJ;cACAR,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;cAC7BA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;cACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;cACjBM,YAAY,GAAG,CAAC;YACpB,CAAC,MACI;cACDN,UAAU,CAAC,EAAEM,YAAY,CAAC,EAAE;YAChC;UACJ;QACJ,CAAC,MACI;UAAE;UACH,IAAIA,YAAY,KAAK,CAAC,EAAE;YAAE;YACtBA,YAAY,EAAE;UAClB;UACAN,UAAU,CAACM,YAAY,CAAC,EAAE;QAC9B;QACAF,CAAC,EAAE;MACP;MACA,IAAI,IAAI,CAACG,iBAAiB,CAACP,UAAU,CAAC,EAAE;QACpC,IAAIQ,SAAS,GAAG,IAAI,CAACC,oBAAoB,CAACT,UAAU,EAAExB,CAAC,EAAEsB,IAAI,CAAC;QAC9D,IAAIU,SAAS,KAAK,IAAI,EAAE;UACpB,OAAOA,SAAS;QACpB;MACJ;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACf,eAAe,CAACf,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO,IAAI,CAACe,eAAe,CAAC,CAAC,CAAC;IAClC;IACA,MAAM,IAAIT,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACD;AACJ;AACA;AACA;EACIC,sBAAsB,CAACyB,aAAa,GAAG,UAAUV,UAAU,EAAEW,GAAG,CAAC,SAAS;IACtE,OAAQA,GAAG,GAAGX,UAAU,CAAC,CAAC,CAAC,GAAIA,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG;EACtD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIf,sBAAsB,CAACW,SAAS,CAACW,iBAAiB,GAAG,UAAUP,UAAU,EAAE;IACvE,IAAIT,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAIqB,WAAW,GAAGrB,UAAU,GAAG,GAAG;IAClC,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAI0B,IAAI,CAACW,GAAG,CAACtB,UAAU,GAAGS,UAAU,CAACxB,CAAC,CAAC,CAAC,IAAIoC,WAAW,EAAE;QACrD,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI3B,sBAAsB,CAACW,SAAS,CAACkB,kBAAkB,GAAG,UAAUC,MAAM,CAAC,SAASC,OAAO,CAAC,SAASC,QAAQ,CAAC,SAASC,uBAAuB,CAAC,SAAS;IAChJ,IAAIhC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIiC,IAAI,GAAGjC,KAAK,CAACkC,SAAS,CAAC,CAAC;IAC5B,IAAIpB,UAAU,GAAG,IAAI,CAACN,oBAAoB;IAC1CM,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IACjB;IACA,IAAIxB,CAAC,GAAGuC,MAAM;IACd,OAAOvC,CAAC,IAAI,CAAC,IAAIU,KAAK,CAACmB,GAAG,CAACW,OAAO,EAAExC,CAAC,CAAC,IAAIwB,UAAU,CAAC,CAAC,CAAC,IAAIiB,QAAQ,EAAE;MACjEjB,UAAU,CAAC,CAAC,CAAC,EAAE;MACfxB,CAAC,EAAE;IACP;IACA;IACA,IAAIA,CAAC,GAAG,CAAC,IAAIwB,UAAU,CAAC,CAAC,CAAC,GAAGiB,QAAQ,EAAE;MACnC,OAAOI,GAAG;IACd;IACA,OAAO7C,CAAC,IAAI,CAAC,IAAI,CAACU,KAAK,CAACmB,GAAG,CAACW,OAAO,EAAExC,CAAC,CAAC,IAAIwB,UAAU,CAAC,CAAC,CAAC,IAAIiB,QAAQ,EAAE;MAClEjB,UAAU,CAAC,CAAC,CAAC,EAAE;MACfxB,CAAC,EAAE;IACP;IACA,IAAIwB,UAAU,CAAC,CAAC,CAAC,GAAGiB,QAAQ,EAAE;MAC1B,OAAOI,GAAG;IACd;IACA;IACA7C,CAAC,GAAGuC,MAAM,GAAG,CAAC;IACd,OAAOvC,CAAC,GAAG2C,IAAI,IAAIjC,KAAK,CAACmB,GAAG,CAACW,OAAO,EAAExC,CAAC,CAAC,IAAIwB,UAAU,CAAC,CAAC,CAAC,IAAIiB,QAAQ,EAAE;MACnEjB,UAAU,CAAC,CAAC,CAAC,EAAE;MACfxB,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,KAAK2C,IAAI,IAAInB,UAAU,CAAC,CAAC,CAAC,GAAGiB,QAAQ,EAAE;MACxC,OAAOI,GAAG;IACd;IACA,OAAO7C,CAAC,GAAG2C,IAAI,IAAI,CAACjC,KAAK,CAACmB,GAAG,CAACW,OAAO,EAAExC,CAAC,CAAC,IAAIwB,UAAU,CAAC,CAAC,CAAC,IAAIiB,QAAQ,EAAE;MACpEjB,UAAU,CAAC,CAAC,CAAC,EAAE;MACfxB,CAAC,EAAE;IACP;IACA,IAAIwB,UAAU,CAAC,CAAC,CAAC,GAAGiB,QAAQ,EAAE;MAC1B,OAAOI,GAAG;IACd;IACA,IAAIC,eAAe,GAAGtB,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;IACnE,IAAI,CAAC,GAAGE,IAAI,CAACW,GAAG,CAACS,eAAe,GAAGJ,uBAAuB,CAAC,IAAI,CAAC,GAAGA,uBAAuB,EAAE;MACxF,OAAOG,GAAG;IACd;IACA,OAAO,IAAI,CAACd,iBAAiB,CAACP,UAAU,CAAC,GAAGf,sBAAsB,CAACyB,aAAa,CAACV,UAAU,EAAExB,CAAC,CAAC,GAAG6C,GAAG;EACzG,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIpC,sBAAsB,CAACW,SAAS,CAACa,oBAAoB,GAAG,UAAUT,UAAU,EAAExB,CAAC,CAAC,SAAS4B,CAAC,CAAC,SAAS;IAChG,IAAImB,GAAG,EAAEC,EAAE;IACX,IAAIF,eAAe,GAAGtB,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;IACnE,IAAIgB,OAAO,GAAG/B,sBAAsB,CAACyB,aAAa,CAACV,UAAU,EAAEI,CAAC,CAAC;IACjE,IAAIqB,OAAO,GAAG,IAAI,CAACX,kBAAkB,CAACtC,CAAC,EAAE,UAAWwC,OAAO,EAAE,CAAC,GAAGhB,UAAU,CAAC,CAAC,CAAC,EAAEsB,eAAe,CAAC;IAChG,IAAI,CAACI,KAAK,CAACD,OAAO,CAAC,EAAE;MACjB,IAAIE,mBAAmB,GAAG,CAAC3B,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,IAAI,GAAG;MAC/E,IAAI;QACA,KAAK,IAAI4B,EAAE,GAAG1D,QAAQ,CAAC,IAAI,CAACuB,eAAe,CAAC,EAAEoC,EAAE,GAAGD,EAAE,CAACjD,IAAI,CAAC,CAAC,EAAE,CAACkD,EAAE,CAAChD,IAAI,EAAEgD,EAAE,GAAGD,EAAE,CAACjD,IAAI,CAAC,CAAC,EAAE;UACpF,IAAImD,MAAM,GAAGD,EAAE,CAACjD,KAAK;UACrB;UACA,IAAIkD,MAAM,CAACC,WAAW,CAACJ,mBAAmB,EAAEF,OAAO,EAAET,OAAO,CAAC,EAAE;YAC3D,OAAOc,MAAM,CAACE,eAAe,CAACP,OAAO,EAAET,OAAO,EAAEW,mBAAmB,CAAC;UACxE;QACJ;MACJ,CAAC,CACD,OAAOM,KAAK,EAAE;QAAEV,GAAG,GAAG;UAAEW,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIJ,EAAE,IAAI,CAACA,EAAE,CAAChD,IAAI,KAAK2C,EAAE,GAAGI,EAAE,CAACO,MAAM,CAAC,EAAEX,EAAE,CAAC/C,IAAI,CAACmD,EAAE,CAAC;QACvD,CAAC,SACO;UAAE,IAAIL,GAAG,EAAE,MAAMA,GAAG,CAACW,KAAK;QAAE;MACxC;MACA;MACA,IAAIE,KAAK,GAAG,IAAIrD,gBAAgB,CAACiC,OAAO,EAAES,OAAO,EAAEE,mBAAmB,CAAC;MACvE,IAAI,CAAClC,eAAe,CAAC4C,IAAI,CAACD,KAAK,CAAC;MAChC,IAAI,IAAI,CAAC5C,mBAAmB,KAAK,IAAI,IAAI,IAAI,CAACA,mBAAmB,KAAK8C,SAAS,EAAE;QAC7E,IAAI,CAAC9C,mBAAmB,CAAC+C,wBAAwB,CAACH,KAAK,CAAC;MAC5D;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD,OAAOnD,sBAAsB;AACjC,CAAC,CAAC,CAAE;AACJ,eAAeA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}