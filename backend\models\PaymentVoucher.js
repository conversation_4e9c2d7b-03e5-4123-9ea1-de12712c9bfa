const mongoose = require("mongoose");
const AutoIncrement = require("mongoose-sequence")(mongoose);

const paymentVoucherSchema = new mongoose.Schema({
  voucherId: { type: Number, unique: true },
  voucherNumber: { type: String, required: true, unique: true },
  
  // Voucher type
  voucherType: {
    type: String,
    required: true,
    enum: ['BP', 'BR', 'CP', 'CR'] // Bank Payment, Bank Receipt, Cash Payment, Cash Receipt
  },

  // Date details
  voucherDate: { type: Date, required: true }, // Voucher creation date
  transactionDate: { type: Date, required: true }, // Actual transaction date
  amount: { type: Number, required: true },
  
  // Account references
  fromAccountId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Account", 
    required: true 
  },
  toAccountId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "Account", 
    required: true 
  },
  
  // Related party (Customer/Vendor/Other)
  relatedPartyType: {
    type: String,
    enum: ['Customer', 'Vendor', 'Employee', 'Government', 'Other']
  },
  relatedPartyId: { type: mongoose.Schema.Types.ObjectId },
  relatedPartyName: { type: String },
  
  // Payment details
  paymentMethod: {
    type: String,
    enum: ['Cash', 'Cheque', 'Bank Transfer', 'Online Transfer', 'Card', 'Other'],
    default: 'Cash'
  },
  chequeNumber: { type: String },
  chequeDate: { type: Date },
  bankReference: { type: String },
  
  // Description
  narration: { type: String, required: true },
  description: { type: String },
  
  // Reference to source documents (if payment against invoice/order)
  referenceDocuments: [{
    documentType: {
      type: String,
      enum: ['PurchaseInvoice', 'SalesOrder', 'Other']
    },
    documentId: { type: mongoose.Schema.Types.ObjectId },
    documentNumber: { type: String },
    allocatedAmount: { type: Number, default: 0 }
  }],
  
  // Status
  status: {
    type: String,
    enum: ['Draft', 'Approved', 'Posted', 'Cancelled'],
    default: 'Draft'
  },
  
  // Approval workflow
  preparedBy: { type: String, required: true },
  approvedBy: { type: String },
  approvedDate: { type: Date },
  postedBy: { type: String },
  postedDate: { type: Date },
  
  // Ledger integration
  ledgerEntries: [{ 
    type: mongoose.Schema.Types.ObjectId, 
    ref: "LedgerEntry" 
  }],
  
  // Attachments
  attachments: [{ type: String }], // File paths
  
  // Metadata
  notes: { type: String },
  isReversed: { type: Boolean, default: false },
  reversalVoucherId: { type: mongoose.Schema.Types.ObjectId, ref: "PaymentVoucher" },
  
}, { 
  timestamps: true 
});

// Auto-increment for voucherId
paymentVoucherSchema.plugin(AutoIncrement, { 
  id: "payment_voucher_seq", 
  inc_field: "voucherId", 
  start_seq: 1001 
});

// Pre-save middleware to generate voucher number
paymentVoucherSchema.pre('save', async function(next) {
  if (!this.voucherNumber) {
    // Get the last voucher of the same type to determine next number
    const lastVoucher = await this.constructor.findOne({
      voucherType: this.voucherType
    }).sort({ voucherId: -1 });

    // Start from 1001 as specified in requirements
    const nextNumber = lastVoucher ? lastVoucher.voucherId + 1 : 1001;
    this.voucherNumber = `${this.voucherType}-${nextNumber}`;
  }

  // Set voucherDate to current date if not provided
  if (!this.voucherDate) {
    this.voucherDate = new Date();
  }

  next();
});

// Indexes
paymentVoucherSchema.index({ voucherType: 1, voucherNumber: 1 });
paymentVoucherSchema.index({ transactionDate: 1 });
paymentVoucherSchema.index({ relatedPartyId: 1, relatedPartyType: 1 });
paymentVoucherSchema.index({ status: 1 });

// Virtual for formatted amount
paymentVoucherSchema.virtual('formattedAmount').get(function() {
  return this.amount.toFixed(2);
});

// Method to post voucher to ledger
paymentVoucherSchema.methods.postToLedger = async function() {
  if (this.status !== 'Approved') {
    throw new Error('Voucher must be approved before posting');
  }
  
  const LedgerEntry = require('./LedgerEntry');
  
  // Create ledger entries based on voucher type
  const entries = [];
  
  if (this.voucherType === 'BP' || this.voucherType === 'CP') {
    // Payment: Credit the bank/cash account, Debit the expense/payable account
    entries.push({
      accountId: this.fromAccountId, // Bank/Cash account
      transactionDate: this.transactionDate,
      voucherType: this.voucherType,
      voucherNumber: this.voucherId.toString(),
      voucherReference: this.voucherNumber,
      debitAmount: 0,
      creditAmount: this.amount,
      balanceAmount: this.amount, // Add required field
      balanceType: 'CR', // Credit balance for bank account
      narration: this.narration,
      sourceDocumentId: this._id,
      sourceDocumentType: 'BankPayment', // Fix enum value
      relatedPartyType: this.relatedPartyType,
      relatedPartyId: this.relatedPartyId,
      relatedPartyName: this.relatedPartyName
    });

    entries.push({
      accountId: this.toAccountId, // Expense/Payable account
      transactionDate: this.transactionDate,
      voucherType: this.voucherType,
      voucherNumber: this.voucherId.toString(),
      voucherReference: this.voucherNumber,
      debitAmount: this.amount,
      creditAmount: 0,
      balanceAmount: this.amount, // Add required field
      balanceType: 'DR', // Debit balance for expense account
      narration: this.narration,
      sourceDocumentId: this._id,
      sourceDocumentType: 'BankPayment', // Fix enum value
      relatedPartyType: this.relatedPartyType,
      relatedPartyId: this.relatedPartyId,
      relatedPartyName: this.relatedPartyName
    });
  } else {
    // Receipt: Debit the bank/cash account, Credit the income/receivable account
    entries.push({
      accountId: this.toAccountId, // Bank/Cash account
      transactionDate: this.transactionDate,
      voucherType: this.voucherType,
      voucherNumber: this.voucherId.toString(),
      voucherReference: this.voucherNumber,
      debitAmount: this.amount,
      creditAmount: 0,
      balanceAmount: this.amount, // Add required field
      balanceType: 'DR', // Debit balance for bank account
      narration: this.narration,
      sourceDocumentId: this._id,
      sourceDocumentType: 'BankReceipt', // Fix enum value
      relatedPartyType: this.relatedPartyType,
      relatedPartyId: this.relatedPartyId,
      relatedPartyName: this.relatedPartyName
    });

    entries.push({
      accountId: this.fromAccountId, // Income/Receivable account
      transactionDate: this.transactionDate,
      voucherType: this.voucherType,
      voucherNumber: this.voucherId.toString(),
      voucherReference: this.voucherNumber,
      debitAmount: 0,
      creditAmount: this.amount,
      balanceAmount: this.amount, // Add required field
      balanceType: 'CR', // Credit balance for income account
      narration: this.narration,
      sourceDocumentId: this._id,
      sourceDocumentType: 'BankReceipt', // Fix enum value
      relatedPartyType: this.relatedPartyType,
      relatedPartyId: this.relatedPartyId,
      relatedPartyName: this.relatedPartyName
    });
  }
  
  // Create ledger entries
  const createdEntries = await LedgerEntry.createDoubleEntry(entries);
  this.ledgerEntries = createdEntries.map(entry => entry._id);
  this.status = 'Posted';
  this.postedDate = new Date();
  
  await this.save();
  return createdEntries;
};

module.exports = mongoose.model("PaymentVoucher", paymentVoucherSchema);
