{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.encoder {*/\n/*import java.util.Arrays;*/\nimport Arrays from '../../util/Arrays';\nimport StringBuilder from '../../util/StringBuilder';\n/**\n * JAVAPORT: The original code was a 2D array of ints, but since it only ever gets assigned\n * -1, 0, and 1, I'm going to use less memory and go with bytes.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar ByteMatrix = /** @class */function () {\n  function ByteMatrix(width /*int*/, height /*int*/) {\n    this.width = width;\n    this.height = height;\n    var bytes = new Array(height); // [height][width]\n    for (var i = 0; i !== height; i++) {\n      bytes[i] = new Uint8Array(width);\n    }\n    this.bytes = bytes;\n  }\n  ByteMatrix.prototype.getHeight = function () {\n    return this.height;\n  };\n  ByteMatrix.prototype.getWidth = function () {\n    return this.width;\n  };\n  ByteMatrix.prototype.get = function (x /*int*/, y /*int*/) {\n    return this.bytes[y][x];\n  };\n  /**\n   * @return an internal representation as bytes, in row-major order. array[y][x] represents point (x,y)\n   */\n  ByteMatrix.prototype.getArray = function () {\n    return this.bytes;\n  };\n  // TYPESCRIPTPORT: preffer to let two methods instead of override to avoid type comparison inside\n  ByteMatrix.prototype.setNumber = function (x /*int*/, y /*int*/, value /*byte|int*/) {\n    this.bytes[y][x] = value;\n  };\n  // public set(x: number /*int*/, y: number /*int*/, value: number /*int*/): void {\n  //   bytes[y][x] = (byte) value\n  // }\n  ByteMatrix.prototype.setBoolean = function (x /*int*/, y /*int*/, value) {\n    this.bytes[y][x] = /*(byte) */value ? 1 : 0;\n  };\n  ByteMatrix.prototype.clear = function (value /*byte*/) {\n    var e_1, _a;\n    try {\n      for (var _b = __values(this.bytes), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var aByte = _c.value;\n        Arrays.fill(aByte, value);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n  };\n  ByteMatrix.prototype.equals = function (o) {\n    if (!(o instanceof ByteMatrix)) {\n      return false;\n    }\n    var other = o;\n    if (this.width !== other.width) {\n      return false;\n    }\n    if (this.height !== other.height) {\n      return false;\n    }\n    for (var y = 0, height = this.height; y < height; ++y) {\n      var bytesY = this.bytes[y];\n      var otherBytesY = other.bytes[y];\n      for (var x = 0, width = this.width; x < width; ++x) {\n        if (bytesY[x] !== otherBytesY[x]) {\n          return false;\n        }\n      }\n    }\n    return true;\n  };\n  /*@Override*/\n  ByteMatrix.prototype.toString = function () {\n    var result = new StringBuilder(); // (2 * width * height + 2)\n    for (var y = 0, height = this.height; y < height; ++y) {\n      var bytesY = this.bytes[y];\n      for (var x = 0, width = this.width; x < width; ++x) {\n        switch (bytesY[x]) {\n          case 0:\n            result.append(' 0');\n            break;\n          case 1:\n            result.append(' 1');\n            break;\n          default:\n            result.append('  ');\n            break;\n        }\n      }\n      result.append('\\n');\n    }\n    return result.toString();\n  };\n  return ByteMatrix;\n}();\nexport default ByteMatrix;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "<PERSON><PERSON><PERSON>", "StringBuilder", "ByteMatrix", "width", "height", "bytes", "Array", "Uint8Array", "prototype", "getHeight", "getWidth", "get", "x", "y", "getArray", "setNumber", "setBoolean", "clear", "e_1", "_a", "_b", "_c", "aByte", "fill", "e_1_1", "error", "return", "equals", "other", "bytesY", "otherBytesY", "toString", "result", "append"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/encoder/ByteMatrix.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.encoder {*/\n/*import java.util.Arrays;*/\nimport Arrays from '../../util/Arrays';\nimport StringBuilder from '../../util/StringBuilder';\n/**\n * JAVAPORT: The original code was a 2D array of ints, but since it only ever gets assigned\n * -1, 0, and 1, I'm going to use less memory and go with bytes.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar ByteMatrix = /** @class */ (function () {\n    function ByteMatrix(width /*int*/, height /*int*/) {\n        this.width = width;\n        this.height = height;\n        var bytes = new Array(height); // [height][width]\n        for (var i = 0; i !== height; i++) {\n            bytes[i] = new Uint8Array(width);\n        }\n        this.bytes = bytes;\n    }\n    ByteMatrix.prototype.getHeight = function () {\n        return this.height;\n    };\n    ByteMatrix.prototype.getWidth = function () {\n        return this.width;\n    };\n    ByteMatrix.prototype.get = function (x /*int*/, y /*int*/) {\n        return this.bytes[y][x];\n    };\n    /**\n     * @return an internal representation as bytes, in row-major order. array[y][x] represents point (x,y)\n     */\n    ByteMatrix.prototype.getArray = function () {\n        return this.bytes;\n    };\n    // TYPESCRIPTPORT: preffer to let two methods instead of override to avoid type comparison inside\n    ByteMatrix.prototype.setNumber = function (x /*int*/, y /*int*/, value /*byte|int*/) {\n        this.bytes[y][x] = value;\n    };\n    // public set(x: number /*int*/, y: number /*int*/, value: number /*int*/): void {\n    //   bytes[y][x] = (byte) value\n    // }\n    ByteMatrix.prototype.setBoolean = function (x /*int*/, y /*int*/, value) {\n        this.bytes[y][x] = /*(byte) */ (value ? 1 : 0);\n    };\n    ByteMatrix.prototype.clear = function (value /*byte*/) {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this.bytes), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var aByte = _c.value;\n                Arrays.fill(aByte, value);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    ByteMatrix.prototype.equals = function (o) {\n        if (!(o instanceof ByteMatrix)) {\n            return false;\n        }\n        var other = o;\n        if (this.width !== other.width) {\n            return false;\n        }\n        if (this.height !== other.height) {\n            return false;\n        }\n        for (var y = 0, height = this.height; y < height; ++y) {\n            var bytesY = this.bytes[y];\n            var otherBytesY = other.bytes[y];\n            for (var x = 0, width = this.width; x < width; ++x) {\n                if (bytesY[x] !== otherBytesY[x]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    /*@Override*/\n    ByteMatrix.prototype.toString = function () {\n        var result = new StringBuilder(); // (2 * width * height + 2)\n        for (var y = 0, height = this.height; y < height; ++y) {\n            var bytesY = this.bytes[y];\n            for (var x = 0, width = this.width; x < width; ++x) {\n                switch (bytesY[x]) {\n                    case 0:\n                        result.append(' 0');\n                        break;\n                    case 1:\n                        result.append(' 1');\n                        break;\n                    default:\n                        result.append('  ');\n                        break;\n                }\n            }\n            result.append('\\n');\n        }\n        return result.toString();\n    };\n    return ByteMatrix;\n}());\nexport default ByteMatrix;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA,OAAOW,MAAM,MAAM,mBAAmB;AACtC,OAAOC,aAAa,MAAM,0BAA0B;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IAC/C,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAIC,KAAK,GAAG,IAAIC,KAAK,CAACF,MAAM,CAAC,CAAC,CAAC;IAC/B,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,KAAKW,MAAM,EAAEX,CAAC,EAAE,EAAE;MAC/BY,KAAK,CAACZ,CAAC,CAAC,GAAG,IAAIc,UAAU,CAACJ,KAAK,CAAC;IACpC;IACA,IAAI,CAACE,KAAK,GAAGA,KAAK;EACtB;EACAH,UAAU,CAACM,SAAS,CAACC,SAAS,GAAG,YAAY;IACzC,OAAO,IAAI,CAACL,MAAM;EACtB,CAAC;EACDF,UAAU,CAACM,SAAS,CAACE,QAAQ,GAAG,YAAY;IACxC,OAAO,IAAI,CAACP,KAAK;EACrB,CAAC;EACDD,UAAU,CAACM,SAAS,CAACG,GAAG,GAAG,UAAUC,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IACvD,OAAO,IAAI,CAACR,KAAK,CAACQ,CAAC,CAAC,CAACD,CAAC,CAAC;EAC3B,CAAC;EACD;AACJ;AACA;EACIV,UAAU,CAACM,SAAS,CAACM,QAAQ,GAAG,YAAY;IACxC,OAAO,IAAI,CAACT,KAAK;EACrB,CAAC;EACD;EACAH,UAAU,CAACM,SAAS,CAACO,SAAS,GAAG,UAAUH,CAAC,CAAC,SAASC,CAAC,CAAC,SAAShB,KAAK,CAAC,cAAc;IACjF,IAAI,CAACQ,KAAK,CAACQ,CAAC,CAAC,CAACD,CAAC,CAAC,GAAGf,KAAK;EAC5B,CAAC;EACD;EACA;EACA;EACAK,UAAU,CAACM,SAAS,CAACQ,UAAU,GAAG,UAAUJ,CAAC,CAAC,SAASC,CAAC,CAAC,SAAShB,KAAK,EAAE;IACrE,IAAI,CAACQ,KAAK,CAACQ,CAAC,CAAC,CAACD,CAAC,CAAC,GAAG,WAAaf,KAAK,GAAG,CAAC,GAAG,CAAE;EAClD,CAAC;EACDK,UAAU,CAACM,SAAS,CAACS,KAAK,GAAG,UAAUpB,KAAK,CAAC,UAAU;IACnD,IAAIqB,GAAG,EAAEC,EAAE;IACX,IAAI;MACA,KAAK,IAAIC,EAAE,GAAGjC,QAAQ,CAAC,IAAI,CAACkB,KAAK,CAAC,EAAEgB,EAAE,GAAGD,EAAE,CAACxB,IAAI,CAAC,CAAC,EAAE,CAACyB,EAAE,CAACvB,IAAI,EAAEuB,EAAE,GAAGD,EAAE,CAACxB,IAAI,CAAC,CAAC,EAAE;QAC1E,IAAI0B,KAAK,GAAGD,EAAE,CAACxB,KAAK;QACpBG,MAAM,CAACuB,IAAI,CAACD,KAAK,EAAEzB,KAAK,CAAC;MAC7B;IACJ,CAAC,CACD,OAAO2B,KAAK,EAAE;MAAEN,GAAG,GAAG;QAAEO,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,EAAE,IAAI,CAACA,EAAE,CAACvB,IAAI,KAAKqB,EAAE,GAAGC,EAAE,CAACM,MAAM,CAAC,EAAEP,EAAE,CAACzB,IAAI,CAAC0B,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACO,KAAK;MAAE;IACxC;EACJ,CAAC;EACDvB,UAAU,CAACM,SAAS,CAACmB,MAAM,GAAG,UAAUvC,CAAC,EAAE;IACvC,IAAI,EAAEA,CAAC,YAAYc,UAAU,CAAC,EAAE;MAC5B,OAAO,KAAK;IAChB;IACA,IAAI0B,KAAK,GAAGxC,CAAC;IACb,IAAI,IAAI,CAACe,KAAK,KAAKyB,KAAK,CAACzB,KAAK,EAAE;MAC5B,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACC,MAAM,KAAKwB,KAAK,CAACxB,MAAM,EAAE;MAC9B,OAAO,KAAK;IAChB;IACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAET,MAAM,GAAG,IAAI,CAACA,MAAM,EAAES,CAAC,GAAGT,MAAM,EAAE,EAAES,CAAC,EAAE;MACnD,IAAIgB,MAAM,GAAG,IAAI,CAACxB,KAAK,CAACQ,CAAC,CAAC;MAC1B,IAAIiB,WAAW,GAAGF,KAAK,CAACvB,KAAK,CAACQ,CAAC,CAAC;MAChC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAET,KAAK,GAAG,IAAI,CAACA,KAAK,EAAES,CAAC,GAAGT,KAAK,EAAE,EAAES,CAAC,EAAE;QAChD,IAAIiB,MAAM,CAACjB,CAAC,CAAC,KAAKkB,WAAW,CAAClB,CAAC,CAAC,EAAE;UAC9B,OAAO,KAAK;QAChB;MACJ;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;EACAV,UAAU,CAACM,SAAS,CAACuB,QAAQ,GAAG,YAAY;IACxC,IAAIC,MAAM,GAAG,IAAI/B,aAAa,CAAC,CAAC,CAAC,CAAC;IAClC,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAET,MAAM,GAAG,IAAI,CAACA,MAAM,EAAES,CAAC,GAAGT,MAAM,EAAE,EAAES,CAAC,EAAE;MACnD,IAAIgB,MAAM,GAAG,IAAI,CAACxB,KAAK,CAACQ,CAAC,CAAC;MAC1B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAET,KAAK,GAAG,IAAI,CAACA,KAAK,EAAES,CAAC,GAAGT,KAAK,EAAE,EAAES,CAAC,EAAE;QAChD,QAAQiB,MAAM,CAACjB,CAAC,CAAC;UACb,KAAK,CAAC;YACFoB,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;YACnB;UACJ,KAAK,CAAC;YACFD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;YACnB;UACJ;YACID,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;YACnB;QACR;MACJ;MACAD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACvB;IACA,OAAOD,MAAM,CAACD,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACD,OAAO7B,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,eAAeA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}