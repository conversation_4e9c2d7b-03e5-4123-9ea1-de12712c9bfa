{"ast": null, "code": "var ExpandedPair = /** @class */function () {\n  function ExpandedPair(leftChar, rightChar, finderPatter, mayBeLast) {\n    this.leftchar = leftChar;\n    this.rightchar = rightChar;\n    this.finderpattern = finderPatter;\n    this.maybeLast = mayBeLast;\n  }\n  ExpandedPair.prototype.mayBeLast = function () {\n    return this.maybeLast;\n  };\n  ExpandedPair.prototype.getLeftChar = function () {\n    return this.leftchar;\n  };\n  ExpandedPair.prototype.getRightChar = function () {\n    return this.rightchar;\n  };\n  ExpandedPair.prototype.getFinderPattern = function () {\n    return this.finderpattern;\n  };\n  ExpandedPair.prototype.mustBeLast = function () {\n    return this.rightchar == null;\n  };\n  ExpandedPair.prototype.toString = function () {\n    return '[ ' + this.leftchar + ', ' + this.rightchar + ' : ' + (this.finderpattern == null ? 'null' : this.finderpattern.getValue()) + ' ]';\n  };\n  ExpandedPair.equals = function (o1, o2) {\n    if (!(o1 instanceof ExpandedPair)) {\n      return false;\n    }\n    return ExpandedPair.equalsOrNull(o1.leftchar, o2.leftchar) && ExpandedPair.equalsOrNull(o1.rightchar, o2.rightchar) && ExpandedPair.equalsOrNull(o1.finderpattern, o2.finderpattern);\n  };\n  ExpandedPair.equalsOrNull = function (o1, o2) {\n    return o1 === null ? o2 === null : ExpandedPair.equals(o1, o2);\n  };\n  ExpandedPair.prototype.hashCode = function () {\n    // return ExpandedPair.hashNotNull(leftChar) ^ hashNotNull(rightChar) ^ hashNotNull(finderPattern);\n    var value = this.leftchar.getValue() ^ this.rightchar.getValue() ^ this.finderpattern.getValue();\n    return value;\n  };\n  return ExpandedPair;\n}();\nexport default ExpandedPair;", "map": {"version": 3, "names": ["ExpandedPair", "leftChar", "rightChar", "finder<PERSON>atter", "mayBeLast", "leftchar", "<PERSON><PERSON><PERSON>", "finder<PERSON><PERSON>n", "maybeLast", "prototype", "getLeftChar", "getRightChar", "getFinderPattern", "mustBeLast", "toString", "getValue", "equals", "o1", "o2", "equalsOrNull", "hashCode", "value"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/ExpandedPair.js"], "sourcesContent": ["var ExpandedPair = /** @class */ (function () {\n    function ExpandedPair(leftChar, rightChar, finderPatter, mayBeLast) {\n        this.leftchar = leftChar;\n        this.rightchar = rightChar;\n        this.finderpattern = finderPatter;\n        this.maybeLast = mayBeLast;\n    }\n    ExpandedPair.prototype.mayBeLast = function () {\n        return this.maybeLast;\n    };\n    ExpandedPair.prototype.getLeftChar = function () {\n        return this.leftchar;\n    };\n    ExpandedPair.prototype.getRightChar = function () {\n        return this.rightchar;\n    };\n    ExpandedPair.prototype.getFinderPattern = function () {\n        return this.finderpattern;\n    };\n    ExpandedPair.prototype.mustBeLast = function () {\n        return this.rightchar == null;\n    };\n    ExpandedPair.prototype.toString = function () {\n        return '[ ' + this.leftchar + ', ' + this.rightchar + ' : ' + (this.finderpattern == null ? 'null' : this.finderpattern.getValue()) + ' ]';\n    };\n    ExpandedPair.equals = function (o1, o2) {\n        if (!(o1 instanceof ExpandedPair)) {\n            return false;\n        }\n        return ExpandedPair.equalsOrNull(o1.leftchar, o2.leftchar) &&\n            ExpandedPair.equalsOrNull(o1.rightchar, o2.rightchar) &&\n            ExpandedPair.equalsOrNull(o1.finderpattern, o2.finderpattern);\n    };\n    ExpandedPair.equalsOrNull = function (o1, o2) {\n        return o1 === null ? o2 === null : ExpandedPair.equals(o1, o2);\n    };\n    ExpandedPair.prototype.hashCode = function () {\n        // return ExpandedPair.hashNotNull(leftChar) ^ hashNotNull(rightChar) ^ hashNotNull(finderPattern);\n        var value = this.leftchar.getValue() ^ this.rightchar.getValue() ^ this.finderpattern.getValue();\n        return value;\n    };\n    return ExpandedPair;\n}());\nexport default ExpandedPair;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAACC,QAAQ,EAAEC,SAAS,EAAEC,YAAY,EAAEC,SAAS,EAAE;IAChE,IAAI,CAACC,QAAQ,GAAGJ,QAAQ;IACxB,IAAI,CAACK,SAAS,GAAGJ,SAAS;IAC1B,IAAI,CAACK,aAAa,GAAGJ,YAAY;IACjC,IAAI,CAACK,SAAS,GAAGJ,SAAS;EAC9B;EACAJ,YAAY,CAACS,SAAS,CAACL,SAAS,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACI,SAAS;EACzB,CAAC;EACDR,YAAY,CAACS,SAAS,CAACC,WAAW,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACL,QAAQ;EACxB,CAAC;EACDL,YAAY,CAACS,SAAS,CAACE,YAAY,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACL,SAAS;EACzB,CAAC;EACDN,YAAY,CAACS,SAAS,CAACG,gBAAgB,GAAG,YAAY;IAClD,OAAO,IAAI,CAACL,aAAa;EAC7B,CAAC;EACDP,YAAY,CAACS,SAAS,CAACI,UAAU,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACP,SAAS,IAAI,IAAI;EACjC,CAAC;EACDN,YAAY,CAACS,SAAS,CAACK,QAAQ,GAAG,YAAY;IAC1C,OAAO,IAAI,GAAG,IAAI,CAACT,QAAQ,GAAG,IAAI,GAAG,IAAI,CAACC,SAAS,GAAG,KAAK,IAAI,IAAI,CAACC,aAAa,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,CAACA,aAAa,CAACQ,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI;EAC9I,CAAC;EACDf,YAAY,CAACgB,MAAM,GAAG,UAAUC,EAAE,EAAEC,EAAE,EAAE;IACpC,IAAI,EAAED,EAAE,YAAYjB,YAAY,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,OAAOA,YAAY,CAACmB,YAAY,CAACF,EAAE,CAACZ,QAAQ,EAAEa,EAAE,CAACb,QAAQ,CAAC,IACtDL,YAAY,CAACmB,YAAY,CAACF,EAAE,CAACX,SAAS,EAAEY,EAAE,CAACZ,SAAS,CAAC,IACrDN,YAAY,CAACmB,YAAY,CAACF,EAAE,CAACV,aAAa,EAAEW,EAAE,CAACX,aAAa,CAAC;EACrE,CAAC;EACDP,YAAY,CAACmB,YAAY,GAAG,UAAUF,EAAE,EAAEC,EAAE,EAAE;IAC1C,OAAOD,EAAE,KAAK,IAAI,GAAGC,EAAE,KAAK,IAAI,GAAGlB,YAAY,CAACgB,MAAM,CAACC,EAAE,EAAEC,EAAE,CAAC;EAClE,CAAC;EACDlB,YAAY,CAACS,SAAS,CAACW,QAAQ,GAAG,YAAY;IAC1C;IACA,IAAIC,KAAK,GAAG,IAAI,CAAChB,QAAQ,CAACU,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACT,SAAS,CAACS,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACR,aAAa,CAACQ,QAAQ,CAAC,CAAC;IAChG,OAAOM,KAAK;EAChB,CAAC;EACD,OAAOrB,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}