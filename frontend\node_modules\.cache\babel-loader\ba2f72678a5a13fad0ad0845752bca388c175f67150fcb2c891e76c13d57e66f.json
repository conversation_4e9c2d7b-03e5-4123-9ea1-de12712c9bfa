{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <AUTHOR>\n * <AUTHOR>\n * <AUTHOR>\n */\nvar MaskUtil = /** @class */function () {\n  function MaskUtil() {\n    // do nothing\n  }\n  /**\n   * Apply mask penalty rule 1 and return the penalty. Find repetitive cells with the same color and\n   * give penalty to them. Example: 00000 or 11111.\n   */\n  MaskUtil.applyMaskPenaltyRule1 = function (matrix) {\n    return MaskUtil.applyMaskPenaltyRule1Internal(matrix, true) + MaskUtil.applyMaskPenaltyRule1Internal(matrix, false);\n  };\n  /**\n   * Apply mask penalty rule 2 and return the penalty. Find 2x2 blocks with the same color and give\n   * penalty to them. This is actually equivalent to the spec's rule, which is to find MxN blocks and give a\n   * penalty proportional to (M-1)x(N-1), because this is the number of 2x2 blocks inside such a block.\n   */\n  MaskUtil.applyMaskPenaltyRule2 = function (matrix) {\n    var penalty = 0;\n    var array = matrix.getArray();\n    var width = matrix.getWidth();\n    var height = matrix.getHeight();\n    for (var y = 0; y < height - 1; y++) {\n      var arrayY = array[y];\n      for (var x = 0; x < width - 1; x++) {\n        var value = arrayY[x];\n        if (value === arrayY[x + 1] && value === array[y + 1][x] && value === array[y + 1][x + 1]) {\n          penalty++;\n        }\n      }\n    }\n    return MaskUtil.N2 * penalty;\n  };\n  /**\n   * Apply mask penalty rule 3 and return the penalty. Find consecutive runs of 1:1:3:1:1:4\n   * starting with black, or 4:1:1:3:1:1 starting with white, and give penalty to them.  If we\n   * find patterns like 000010111010000, we give penalty once.\n   */\n  MaskUtil.applyMaskPenaltyRule3 = function (matrix) {\n    var numPenalties = 0;\n    var array = matrix.getArray();\n    var width = matrix.getWidth();\n    var height = matrix.getHeight();\n    for (var y = 0; y < height; y++) {\n      for (var x = 0; x < width; x++) {\n        var arrayY = array[y]; // We can at least optimize this access\n        if (x + 6 < width && arrayY[x] === 1 && arrayY[x + 1] === 0 && arrayY[x + 2] === 1 && arrayY[x + 3] === 1 && arrayY[x + 4] === 1 && arrayY[x + 5] === 0 && arrayY[x + 6] === 1 && (MaskUtil.isWhiteHorizontal(arrayY, x - 4, x) || MaskUtil.isWhiteHorizontal(arrayY, x + 7, x + 11))) {\n          numPenalties++;\n        }\n        if (y + 6 < height && array[y][x] === 1 && array[y + 1][x] === 0 && array[y + 2][x] === 1 && array[y + 3][x] === 1 && array[y + 4][x] === 1 && array[y + 5][x] === 0 && array[y + 6][x] === 1 && (MaskUtil.isWhiteVertical(array, x, y - 4, y) || MaskUtil.isWhiteVertical(array, x, y + 7, y + 11))) {\n          numPenalties++;\n        }\n      }\n    }\n    return numPenalties * MaskUtil.N3;\n  };\n  MaskUtil.isWhiteHorizontal = function (rowArray, from /*int*/, to /*int*/) {\n    from = Math.max(from, 0);\n    to = Math.min(to, rowArray.length);\n    for (var i = from; i < to; i++) {\n      if (rowArray[i] === 1) {\n        return false;\n      }\n    }\n    return true;\n  };\n  MaskUtil.isWhiteVertical = function (array, col /*int*/, from /*int*/, to /*int*/) {\n    from = Math.max(from, 0);\n    to = Math.min(to, array.length);\n    for (var i = from; i < to; i++) {\n      if (array[i][col] === 1) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\n   * Apply mask penalty rule 4 and return the penalty. Calculate the ratio of dark cells and give\n   * penalty if the ratio is far from 50%. It gives 10 penalty for 5% distance.\n   */\n  MaskUtil.applyMaskPenaltyRule4 = function (matrix) {\n    var numDarkCells = 0;\n    var array = matrix.getArray();\n    var width = matrix.getWidth();\n    var height = matrix.getHeight();\n    for (var y = 0; y < height; y++) {\n      var arrayY = array[y];\n      for (var x = 0; x < width; x++) {\n        if (arrayY[x] === 1) {\n          numDarkCells++;\n        }\n      }\n    }\n    var numTotalCells = matrix.getHeight() * matrix.getWidth();\n    var fivePercentVariances = Math.floor(Math.abs(numDarkCells * 2 - numTotalCells) * 10 / numTotalCells);\n    return fivePercentVariances * MaskUtil.N4;\n  };\n  /**\n   * Return the mask bit for \"getMaskPattern\" at \"x\" and \"y\". See 8.8 of JISX0510:2004 for mask\n   * pattern conditions.\n   */\n  MaskUtil.getDataMaskBit = function (maskPattern /*int*/, x /*int*/, y /*int*/) {\n    var intermediate; /*int*/\n    var temp; /*int*/\n    switch (maskPattern) {\n      case 0:\n        intermediate = y + x & 0x1;\n        break;\n      case 1:\n        intermediate = y & 0x1;\n        break;\n      case 2:\n        intermediate = x % 3;\n        break;\n      case 3:\n        intermediate = (y + x) % 3;\n        break;\n      case 4:\n        intermediate = Math.floor(y / 2) + Math.floor(x / 3) & 0x1;\n        break;\n      case 5:\n        temp = y * x;\n        intermediate = (temp & 0x1) + temp % 3;\n        break;\n      case 6:\n        temp = y * x;\n        intermediate = (temp & 0x1) + temp % 3 & 0x1;\n        break;\n      case 7:\n        temp = y * x;\n        intermediate = temp % 3 + (y + x & 0x1) & 0x1;\n        break;\n      default:\n        throw new IllegalArgumentException('Invalid mask pattern: ' + maskPattern);\n    }\n    return intermediate === 0;\n  };\n  /**\n   * Helper function for applyMaskPenaltyRule1. We need this for doing this calculation in both\n   * vertical and horizontal orders respectively.\n   */\n  MaskUtil.applyMaskPenaltyRule1Internal = function (matrix, isHorizontal) {\n    var penalty = 0;\n    var iLimit = isHorizontal ? matrix.getHeight() : matrix.getWidth();\n    var jLimit = isHorizontal ? matrix.getWidth() : matrix.getHeight();\n    var array = matrix.getArray();\n    for (var i = 0; i < iLimit; i++) {\n      var numSameBitCells = 0;\n      var prevBit = -1;\n      for (var j = 0; j < jLimit; j++) {\n        var bit = isHorizontal ? array[i][j] : array[j][i];\n        if (bit === prevBit) {\n          numSameBitCells++;\n        } else {\n          if (numSameBitCells >= 5) {\n            penalty += MaskUtil.N1 + (numSameBitCells - 5);\n          }\n          numSameBitCells = 1; // Include the cell itself.\n          prevBit = bit;\n        }\n      }\n      if (numSameBitCells >= 5) {\n        penalty += MaskUtil.N1 + (numSameBitCells - 5);\n      }\n    }\n    return penalty;\n  };\n  // Penalty weights from section 6.8.2.1\n  MaskUtil.N1 = 3;\n  MaskUtil.N2 = 3;\n  MaskUtil.N3 = 40;\n  MaskUtil.N4 = 10;\n  return MaskUtil;\n}();\nexport default MaskUtil;", "map": {"version": 3, "names": ["IllegalArgumentException", "<PERSON><PERSON><PERSON>", "applyMaskPenaltyRule1", "matrix", "applyMaskPenaltyRule1Internal", "applyMaskPenaltyRule2", "penalty", "array", "getArray", "width", "getWidth", "height", "getHeight", "y", "arrayY", "x", "value", "N2", "applyMaskPenaltyRule3", "numPenalties", "isWhiteHorizontal", "isWhiteVertical", "N3", "rowArray", "from", "to", "Math", "max", "min", "length", "i", "col", "applyMaskPenaltyRule4", "numDarkCells", "numTotalCells", "fivePercentVariances", "floor", "abs", "N4", "getDataMaskBit", "maskPattern", "intermediate", "temp", "isHorizontal", "iLimit", "jLimit", "numSameBitCells", "prevBit", "j", "bit", "N1"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/encoder/MaskUtil.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <AUTHOR>\n * <AUTHOR>\n * <AUTHOR>\n */\nvar MaskUtil = /** @class */ (function () {\n    function MaskUtil() {\n        // do nothing\n    }\n    /**\n     * Apply mask penalty rule 1 and return the penalty. Find repetitive cells with the same color and\n     * give penalty to them. Example: 00000 or 11111.\n     */\n    MaskUtil.applyMaskPenaltyRule1 = function (matrix) {\n        return MaskUtil.applyMaskPenaltyRule1Internal(matrix, true) + MaskUtil.applyMaskPenaltyRule1Internal(matrix, false);\n    };\n    /**\n     * Apply mask penalty rule 2 and return the penalty. Find 2x2 blocks with the same color and give\n     * penalty to them. This is actually equivalent to the spec's rule, which is to find MxN blocks and give a\n     * penalty proportional to (M-1)x(N-1), because this is the number of 2x2 blocks inside such a block.\n     */\n    MaskUtil.applyMaskPenaltyRule2 = function (matrix) {\n        var penalty = 0;\n        var array = matrix.getArray();\n        var width = matrix.getWidth();\n        var height = matrix.getHeight();\n        for (var y = 0; y < height - 1; y++) {\n            var arrayY = array[y];\n            for (var x = 0; x < width - 1; x++) {\n                var value = arrayY[x];\n                if (value === arrayY[x + 1] && value === array[y + 1][x] && value === array[y + 1][x + 1]) {\n                    penalty++;\n                }\n            }\n        }\n        return MaskUtil.N2 * penalty;\n    };\n    /**\n     * Apply mask penalty rule 3 and return the penalty. Find consecutive runs of 1:1:3:1:1:4\n     * starting with black, or 4:1:1:3:1:1 starting with white, and give penalty to them.  If we\n     * find patterns like 000010111010000, we give penalty once.\n     */\n    MaskUtil.applyMaskPenaltyRule3 = function (matrix) {\n        var numPenalties = 0;\n        var array = matrix.getArray();\n        var width = matrix.getWidth();\n        var height = matrix.getHeight();\n        for (var y = 0; y < height; y++) {\n            for (var x = 0; x < width; x++) {\n                var arrayY = array[y]; // We can at least optimize this access\n                if (x + 6 < width &&\n                    arrayY[x] === 1 &&\n                    arrayY[x + 1] === 0 &&\n                    arrayY[x + 2] === 1 &&\n                    arrayY[x + 3] === 1 &&\n                    arrayY[x + 4] === 1 &&\n                    arrayY[x + 5] === 0 &&\n                    arrayY[x + 6] === 1 &&\n                    (MaskUtil.isWhiteHorizontal(arrayY, x - 4, x) || MaskUtil.isWhiteHorizontal(arrayY, x + 7, x + 11))) {\n                    numPenalties++;\n                }\n                if (y + 6 < height &&\n                    array[y][x] === 1 &&\n                    array[y + 1][x] === 0 &&\n                    array[y + 2][x] === 1 &&\n                    array[y + 3][x] === 1 &&\n                    array[y + 4][x] === 1 &&\n                    array[y + 5][x] === 0 &&\n                    array[y + 6][x] === 1 &&\n                    (MaskUtil.isWhiteVertical(array, x, y - 4, y) || MaskUtil.isWhiteVertical(array, x, y + 7, y + 11))) {\n                    numPenalties++;\n                }\n            }\n        }\n        return numPenalties * MaskUtil.N3;\n    };\n    MaskUtil.isWhiteHorizontal = function (rowArray, from /*int*/, to /*int*/) {\n        from = Math.max(from, 0);\n        to = Math.min(to, rowArray.length);\n        for (var i = from; i < to; i++) {\n            if (rowArray[i] === 1) {\n                return false;\n            }\n        }\n        return true;\n    };\n    MaskUtil.isWhiteVertical = function (array, col /*int*/, from /*int*/, to /*int*/) {\n        from = Math.max(from, 0);\n        to = Math.min(to, array.length);\n        for (var i = from; i < to; i++) {\n            if (array[i][col] === 1) {\n                return false;\n            }\n        }\n        return true;\n    };\n    /**\n     * Apply mask penalty rule 4 and return the penalty. Calculate the ratio of dark cells and give\n     * penalty if the ratio is far from 50%. It gives 10 penalty for 5% distance.\n     */\n    MaskUtil.applyMaskPenaltyRule4 = function (matrix) {\n        var numDarkCells = 0;\n        var array = matrix.getArray();\n        var width = matrix.getWidth();\n        var height = matrix.getHeight();\n        for (var y = 0; y < height; y++) {\n            var arrayY = array[y];\n            for (var x = 0; x < width; x++) {\n                if (arrayY[x] === 1) {\n                    numDarkCells++;\n                }\n            }\n        }\n        var numTotalCells = matrix.getHeight() * matrix.getWidth();\n        var fivePercentVariances = Math.floor(Math.abs(numDarkCells * 2 - numTotalCells) * 10 / numTotalCells);\n        return fivePercentVariances * MaskUtil.N4;\n    };\n    /**\n     * Return the mask bit for \"getMaskPattern\" at \"x\" and \"y\". See 8.8 of JISX0510:2004 for mask\n     * pattern conditions.\n     */\n    MaskUtil.getDataMaskBit = function (maskPattern /*int*/, x /*int*/, y /*int*/) {\n        var intermediate; /*int*/\n        var temp; /*int*/\n        switch (maskPattern) {\n            case 0:\n                intermediate = (y + x) & 0x1;\n                break;\n            case 1:\n                intermediate = y & 0x1;\n                break;\n            case 2:\n                intermediate = x % 3;\n                break;\n            case 3:\n                intermediate = (y + x) % 3;\n                break;\n            case 4:\n                intermediate = (Math.floor(y / 2) + Math.floor(x / 3)) & 0x1;\n                break;\n            case 5:\n                temp = y * x;\n                intermediate = (temp & 0x1) + (temp % 3);\n                break;\n            case 6:\n                temp = y * x;\n                intermediate = ((temp & 0x1) + (temp % 3)) & 0x1;\n                break;\n            case 7:\n                temp = y * x;\n                intermediate = ((temp % 3) + ((y + x) & 0x1)) & 0x1;\n                break;\n            default:\n                throw new IllegalArgumentException('Invalid mask pattern: ' + maskPattern);\n        }\n        return intermediate === 0;\n    };\n    /**\n     * Helper function for applyMaskPenaltyRule1. We need this for doing this calculation in both\n     * vertical and horizontal orders respectively.\n     */\n    MaskUtil.applyMaskPenaltyRule1Internal = function (matrix, isHorizontal) {\n        var penalty = 0;\n        var iLimit = isHorizontal ? matrix.getHeight() : matrix.getWidth();\n        var jLimit = isHorizontal ? matrix.getWidth() : matrix.getHeight();\n        var array = matrix.getArray();\n        for (var i = 0; i < iLimit; i++) {\n            var numSameBitCells = 0;\n            var prevBit = -1;\n            for (var j = 0; j < jLimit; j++) {\n                var bit = isHorizontal ? array[i][j] : array[j][i];\n                if (bit === prevBit) {\n                    numSameBitCells++;\n                }\n                else {\n                    if (numSameBitCells >= 5) {\n                        penalty += MaskUtil.N1 + (numSameBitCells - 5);\n                    }\n                    numSameBitCells = 1; // Include the cell itself.\n                    prevBit = bit;\n                }\n            }\n            if (numSameBitCells >= 5) {\n                penalty += MaskUtil.N1 + (numSameBitCells - 5);\n            }\n        }\n        return penalty;\n    };\n    // Penalty weights from section 6.8.2.1\n    MaskUtil.N1 = 3;\n    MaskUtil.N2 = 3;\n    MaskUtil.N3 = 40;\n    MaskUtil.N4 = 10;\n    return MaskUtil;\n}());\nexport default MaskUtil;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAAA,EAAG;IAChB;EAAA;EAEJ;AACJ;AACA;AACA;EACIA,QAAQ,CAACC,qBAAqB,GAAG,UAAUC,MAAM,EAAE;IAC/C,OAAOF,QAAQ,CAACG,6BAA6B,CAACD,MAAM,EAAE,IAAI,CAAC,GAAGF,QAAQ,CAACG,6BAA6B,CAACD,MAAM,EAAE,KAAK,CAAC;EACvH,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIF,QAAQ,CAACI,qBAAqB,GAAG,UAAUF,MAAM,EAAE;IAC/C,IAAIG,OAAO,GAAG,CAAC;IACf,IAAIC,KAAK,GAAGJ,MAAM,CAACK,QAAQ,CAAC,CAAC;IAC7B,IAAIC,KAAK,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC;IAC7B,IAAIC,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,CAAC;IAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;MACjC,IAAIC,MAAM,GAAGP,KAAK,CAACM,CAAC,CAAC;MACrB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,GAAG,CAAC,EAAEM,CAAC,EAAE,EAAE;QAChC,IAAIC,KAAK,GAAGF,MAAM,CAACC,CAAC,CAAC;QACrB,IAAIC,KAAK,KAAKF,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,IAAIC,KAAK,KAAKT,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,CAAC,IAAIC,KAAK,KAAKT,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,GAAG,CAAC,CAAC,EAAE;UACvFT,OAAO,EAAE;QACb;MACJ;IACJ;IACA,OAAOL,QAAQ,CAACgB,EAAE,GAAGX,OAAO;EAChC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIL,QAAQ,CAACiB,qBAAqB,GAAG,UAAUf,MAAM,EAAE;IAC/C,IAAIgB,YAAY,GAAG,CAAC;IACpB,IAAIZ,KAAK,GAAGJ,MAAM,CAACK,QAAQ,CAAC,CAAC;IAC7B,IAAIC,KAAK,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC;IAC7B,IAAIC,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,CAAC;IAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,EAAE,EAAE;QAC5B,IAAID,MAAM,GAAGP,KAAK,CAACM,CAAC,CAAC,CAAC,CAAC;QACvB,IAAIE,CAAC,GAAG,CAAC,GAAGN,KAAK,IACbK,MAAM,CAACC,CAAC,CAAC,KAAK,CAAC,IACfD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IACnBD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IACnBD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IACnBD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IACnBD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,IACnBD,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,KAClBd,QAAQ,CAACmB,iBAAiB,CAACN,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,IAAId,QAAQ,CAACmB,iBAAiB,CAACN,MAAM,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;UACrGI,YAAY,EAAE;QAClB;QACA,IAAIN,CAAC,GAAG,CAAC,GAAGF,MAAM,IACdJ,KAAK,CAACM,CAAC,CAAC,CAACE,CAAC,CAAC,KAAK,CAAC,IACjBR,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,CAAC,KAAK,CAAC,IACrBR,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,CAAC,KAAK,CAAC,IACrBR,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,CAAC,KAAK,CAAC,IACrBR,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,CAAC,KAAK,CAAC,IACrBR,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,CAAC,KAAK,CAAC,IACrBR,KAAK,CAACM,CAAC,GAAG,CAAC,CAAC,CAACE,CAAC,CAAC,KAAK,CAAC,KACpBd,QAAQ,CAACoB,eAAe,CAACd,KAAK,EAAEQ,CAAC,EAAEF,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,IAAIZ,QAAQ,CAACoB,eAAe,CAACd,KAAK,EAAEQ,CAAC,EAAEF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE;UACrGM,YAAY,EAAE;QAClB;MACJ;IACJ;IACA,OAAOA,YAAY,GAAGlB,QAAQ,CAACqB,EAAE;EACrC,CAAC;EACDrB,QAAQ,CAACmB,iBAAiB,GAAG,UAAUG,QAAQ,EAAEC,IAAI,CAAC,SAASC,EAAE,CAAC,SAAS;IACvED,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC;IACxBC,EAAE,GAAGC,IAAI,CAACE,GAAG,CAACH,EAAE,EAAEF,QAAQ,CAACM,MAAM,CAAC;IAClC,KAAK,IAAIC,CAAC,GAAGN,IAAI,EAAEM,CAAC,GAAGL,EAAE,EAAEK,CAAC,EAAE,EAAE;MAC5B,IAAIP,QAAQ,CAACO,CAAC,CAAC,KAAK,CAAC,EAAE;QACnB,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD7B,QAAQ,CAACoB,eAAe,GAAG,UAAUd,KAAK,EAAEwB,GAAG,CAAC,SAASP,IAAI,CAAC,SAASC,EAAE,CAAC,SAAS;IAC/ED,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC;IACxBC,EAAE,GAAGC,IAAI,CAACE,GAAG,CAACH,EAAE,EAAElB,KAAK,CAACsB,MAAM,CAAC;IAC/B,KAAK,IAAIC,CAAC,GAAGN,IAAI,EAAEM,CAAC,GAAGL,EAAE,EAAEK,CAAC,EAAE,EAAE;MAC5B,IAAIvB,KAAK,CAACuB,CAAC,CAAC,CAACC,GAAG,CAAC,KAAK,CAAC,EAAE;QACrB,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;EACI9B,QAAQ,CAAC+B,qBAAqB,GAAG,UAAU7B,MAAM,EAAE;IAC/C,IAAI8B,YAAY,GAAG,CAAC;IACpB,IAAI1B,KAAK,GAAGJ,MAAM,CAACK,QAAQ,CAAC,CAAC;IAC7B,IAAIC,KAAK,GAAGN,MAAM,CAACO,QAAQ,CAAC,CAAC;IAC7B,IAAIC,MAAM,GAAGR,MAAM,CAACS,SAAS,CAAC,CAAC;IAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,EAAEE,CAAC,EAAE,EAAE;MAC7B,IAAIC,MAAM,GAAGP,KAAK,CAACM,CAAC,CAAC;MACrB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,EAAE,EAAE;QAC5B,IAAID,MAAM,CAACC,CAAC,CAAC,KAAK,CAAC,EAAE;UACjBkB,YAAY,EAAE;QAClB;MACJ;IACJ;IACA,IAAIC,aAAa,GAAG/B,MAAM,CAACS,SAAS,CAAC,CAAC,GAAGT,MAAM,CAACO,QAAQ,CAAC,CAAC;IAC1D,IAAIyB,oBAAoB,GAAGT,IAAI,CAACU,KAAK,CAACV,IAAI,CAACW,GAAG,CAACJ,YAAY,GAAG,CAAC,GAAGC,aAAa,CAAC,GAAG,EAAE,GAAGA,aAAa,CAAC;IACtG,OAAOC,oBAAoB,GAAGlC,QAAQ,CAACqC,EAAE;EAC7C,CAAC;EACD;AACJ;AACA;AACA;EACIrC,QAAQ,CAACsC,cAAc,GAAG,UAAUC,WAAW,CAAC,SAASzB,CAAC,CAAC,SAASF,CAAC,CAAC,SAAS;IAC3E,IAAI4B,YAAY,CAAC,CAAC;IAClB,IAAIC,IAAI,CAAC,CAAC;IACV,QAAQF,WAAW;MACf,KAAK,CAAC;QACFC,YAAY,GAAI5B,CAAC,GAAGE,CAAC,GAAI,GAAG;QAC5B;MACJ,KAAK,CAAC;QACF0B,YAAY,GAAG5B,CAAC,GAAG,GAAG;QACtB;MACJ,KAAK,CAAC;QACF4B,YAAY,GAAG1B,CAAC,GAAG,CAAC;QACpB;MACJ,KAAK,CAAC;QACF0B,YAAY,GAAG,CAAC5B,CAAC,GAAGE,CAAC,IAAI,CAAC;QAC1B;MACJ,KAAK,CAAC;QACF0B,YAAY,GAAIf,IAAI,CAACU,KAAK,CAACvB,CAAC,GAAG,CAAC,CAAC,GAAGa,IAAI,CAACU,KAAK,CAACrB,CAAC,GAAG,CAAC,CAAC,GAAI,GAAG;QAC5D;MACJ,KAAK,CAAC;QACF2B,IAAI,GAAG7B,CAAC,GAAGE,CAAC;QACZ0B,YAAY,GAAG,CAACC,IAAI,GAAG,GAAG,IAAKA,IAAI,GAAG,CAAE;QACxC;MACJ,KAAK,CAAC;QACFA,IAAI,GAAG7B,CAAC,GAAGE,CAAC;QACZ0B,YAAY,GAAI,CAACC,IAAI,GAAG,GAAG,IAAKA,IAAI,GAAG,CAAE,GAAI,GAAG;QAChD;MACJ,KAAK,CAAC;QACFA,IAAI,GAAG7B,CAAC,GAAGE,CAAC;QACZ0B,YAAY,GAAKC,IAAI,GAAG,CAAC,IAAM7B,CAAC,GAAGE,CAAC,GAAI,GAAG,CAAC,GAAI,GAAG;QACnD;MACJ;QACI,MAAM,IAAIf,wBAAwB,CAAC,wBAAwB,GAAGwC,WAAW,CAAC;IAClF;IACA,OAAOC,YAAY,KAAK,CAAC;EAC7B,CAAC;EACD;AACJ;AACA;AACA;EACIxC,QAAQ,CAACG,6BAA6B,GAAG,UAAUD,MAAM,EAAEwC,YAAY,EAAE;IACrE,IAAIrC,OAAO,GAAG,CAAC;IACf,IAAIsC,MAAM,GAAGD,YAAY,GAAGxC,MAAM,CAACS,SAAS,CAAC,CAAC,GAAGT,MAAM,CAACO,QAAQ,CAAC,CAAC;IAClE,IAAImC,MAAM,GAAGF,YAAY,GAAGxC,MAAM,CAACO,QAAQ,CAAC,CAAC,GAAGP,MAAM,CAACS,SAAS,CAAC,CAAC;IAClE,IAAIL,KAAK,GAAGJ,MAAM,CAACK,QAAQ,CAAC,CAAC;IAC7B,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,MAAM,EAAEd,CAAC,EAAE,EAAE;MAC7B,IAAIgB,eAAe,GAAG,CAAC;MACvB,IAAIC,OAAO,GAAG,CAAC,CAAC;MAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;QAC7B,IAAIC,GAAG,GAAGN,YAAY,GAAGpC,KAAK,CAACuB,CAAC,CAAC,CAACkB,CAAC,CAAC,GAAGzC,KAAK,CAACyC,CAAC,CAAC,CAAClB,CAAC,CAAC;QAClD,IAAImB,GAAG,KAAKF,OAAO,EAAE;UACjBD,eAAe,EAAE;QACrB,CAAC,MACI;UACD,IAAIA,eAAe,IAAI,CAAC,EAAE;YACtBxC,OAAO,IAAIL,QAAQ,CAACiD,EAAE,IAAIJ,eAAe,GAAG,CAAC,CAAC;UAClD;UACAA,eAAe,GAAG,CAAC,CAAC,CAAC;UACrBC,OAAO,GAAGE,GAAG;QACjB;MACJ;MACA,IAAIH,eAAe,IAAI,CAAC,EAAE;QACtBxC,OAAO,IAAIL,QAAQ,CAACiD,EAAE,IAAIJ,eAAe,GAAG,CAAC,CAAC;MAClD;IACJ;IACA,OAAOxC,OAAO;EAClB,CAAC;EACD;EACAL,QAAQ,CAACiD,EAAE,GAAG,CAAC;EACfjD,QAAQ,CAACgB,EAAE,GAAG,CAAC;EACfhB,QAAQ,CAACqB,EAAE,GAAG,EAAE;EAChBrB,QAAQ,CAACqC,EAAE,GAAG,EAAE;EAChB,OAAOrC,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}