{"ast": null, "code": "/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport SimpleToken from './SimpleToken';\nvar BinaryShiftToken = /** @class */function (_super) {\n  __extends(BinaryShiftToken, _super);\n  function BinaryShiftToken(previous, binaryShiftStart, binaryShiftByteCount) {\n    var _this = _super.call(this, previous, 0, 0) || this;\n    _this.binaryShiftStart = binaryShiftStart;\n    _this.binaryShiftByteCount = binaryShiftByteCount;\n    return _this;\n  }\n  /**\n   * @Override\n   */\n  BinaryShiftToken.prototype.appendTo = function (bitArray, text) {\n    for (var i = 0; i < this.binaryShiftByteCount; i++) {\n      if (i === 0 || i === 31 && this.binaryShiftByteCount <= 62) {\n        // We need a header before the first character, and before\n        // character 31 when the total byte code is <= 62\n        bitArray.appendBits(31, 5); // BINARY_SHIFT\n        if (this.binaryShiftByteCount > 62) {\n          bitArray.appendBits(this.binaryShiftByteCount - 31, 16);\n        } else if (i === 0) {\n          // 1 <= binaryShiftByteCode <= 62\n          bitArray.appendBits(Math.min(this.binaryShiftByteCount, 31), 5);\n        } else {\n          // 32 <= binaryShiftCount <= 62 and i == 31\n          bitArray.appendBits(this.binaryShiftByteCount - 31, 5);\n        }\n      }\n      bitArray.appendBits(text[this.binaryShiftStart + i], 8);\n    }\n  };\n  BinaryShiftToken.prototype.addBinaryShift = function (start, byteCount) {\n    // int bitCount = (byteCount * 8) + (byteCount <= 31 ? 10 : byteCount <= 62 ? 20 : 21);\n    return new BinaryShiftToken(this, start, byteCount);\n  };\n  /**\n   * @Override\n   */\n  BinaryShiftToken.prototype.toString = function () {\n    return '<' + this.binaryShiftStart + '::' + (this.binaryShiftStart + this.binaryShiftByteCount - 1) + '>';\n  };\n  return BinaryShiftToken;\n}(SimpleToken);\nexport default BinaryShiftToken;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "SimpleToken", "BinaryShiftToken", "_super", "previous", "binaryShiftStart", "binaryShiftByteCount", "_this", "call", "appendTo", "bitArray", "text", "i", "appendBits", "Math", "min", "addBinaryShift", "start", "byteCount", "toString"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/BinaryShiftToken.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport SimpleToken from './SimpleToken';\nvar BinaryShiftToken = /** @class */ (function (_super) {\n    __extends(BinaryShiftToken, _super);\n    function BinaryShiftToken(previous, binaryShiftStart, binaryShiftByteCount) {\n        var _this = _super.call(this, previous, 0, 0) || this;\n        _this.binaryShiftStart = binaryShiftStart;\n        _this.binaryShiftByteCount = binaryShiftByteCount;\n        return _this;\n    }\n    /**\n     * @Override\n     */\n    BinaryShiftToken.prototype.appendTo = function (bitArray, text) {\n        for (var i = 0; i < this.binaryShiftByteCount; i++) {\n            if (i === 0 || (i === 31 && this.binaryShiftByteCount <= 62)) {\n                // We need a header before the first character, and before\n                // character 31 when the total byte code is <= 62\n                bitArray.appendBits(31, 5); // BINARY_SHIFT\n                if (this.binaryShiftByteCount > 62) {\n                    bitArray.appendBits(this.binaryShiftByteCount - 31, 16);\n                }\n                else if (i === 0) {\n                    // 1 <= binaryShiftByteCode <= 62\n                    bitArray.appendBits(Math.min(this.binaryShiftByteCount, 31), 5);\n                }\n                else {\n                    // 32 <= binaryShiftCount <= 62 and i == 31\n                    bitArray.appendBits(this.binaryShiftByteCount - 31, 5);\n                }\n            }\n            bitArray.appendBits(text[this.binaryShiftStart + i], 8);\n        }\n    };\n    BinaryShiftToken.prototype.addBinaryShift = function (start, byteCount) {\n        // int bitCount = (byteCount * 8) + (byteCount <= 31 ? 10 : byteCount <= 62 ? 20 : 21);\n        return new BinaryShiftToken(this, start, byteCount);\n    };\n    /**\n     * @Override\n     */\n    BinaryShiftToken.prototype.toString = function () {\n        return '<' + this.binaryShiftStart + '::' + (this.binaryShiftStart + this.binaryShiftByteCount - 1) + '>';\n    };\n    return BinaryShiftToken;\n}(SimpleToken));\nexport default BinaryShiftToken;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,WAAW,MAAM,eAAe;AACvC,IAAIC,gBAAgB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACpDhB,SAAS,CAACe,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAACE,QAAQ,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAE;IACxE,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI;IACrDG,KAAK,CAACF,gBAAgB,GAAGA,gBAAgB;IACzCE,KAAK,CAACD,oBAAoB,GAAGA,oBAAoB;IACjD,OAAOC,KAAK;EAChB;EACA;AACJ;AACA;EACIL,gBAAgB,CAACH,SAAS,CAACU,QAAQ,GAAG,UAAUC,QAAQ,EAAEC,IAAI,EAAE;IAC5D,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACN,oBAAoB,EAAEM,CAAC,EAAE,EAAE;MAChD,IAAIA,CAAC,KAAK,CAAC,IAAKA,CAAC,KAAK,EAAE,IAAI,IAAI,CAACN,oBAAoB,IAAI,EAAG,EAAE;QAC1D;QACA;QACAI,QAAQ,CAACG,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,IAAI,CAACP,oBAAoB,GAAG,EAAE,EAAE;UAChCI,QAAQ,CAACG,UAAU,CAAC,IAAI,CAACP,oBAAoB,GAAG,EAAE,EAAE,EAAE,CAAC;QAC3D,CAAC,MACI,IAAIM,CAAC,KAAK,CAAC,EAAE;UACd;UACAF,QAAQ,CAACG,UAAU,CAACC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACT,oBAAoB,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QACnE,CAAC,MACI;UACD;UACAI,QAAQ,CAACG,UAAU,CAAC,IAAI,CAACP,oBAAoB,GAAG,EAAE,EAAE,CAAC,CAAC;QAC1D;MACJ;MACAI,QAAQ,CAACG,UAAU,CAACF,IAAI,CAAC,IAAI,CAACN,gBAAgB,GAAGO,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3D;EACJ,CAAC;EACDV,gBAAgB,CAACH,SAAS,CAACiB,cAAc,GAAG,UAAUC,KAAK,EAAEC,SAAS,EAAE;IACpE;IACA,OAAO,IAAIhB,gBAAgB,CAAC,IAAI,EAAEe,KAAK,EAAEC,SAAS,CAAC;EACvD,CAAC;EACD;AACJ;AACA;EACIhB,gBAAgB,CAACH,SAAS,CAACoB,QAAQ,GAAG,YAAY;IAC9C,OAAO,GAAG,GAAG,IAAI,CAACd,gBAAgB,GAAG,IAAI,IAAI,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAAC,GAAG,GAAG;EAC7G,CAAC;EACD,OAAOJ,gBAAgB;AAC3B,CAAC,CAACD,WAAW,CAAE;AACf,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}