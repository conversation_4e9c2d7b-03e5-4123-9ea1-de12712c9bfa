{"ast": null, "code": "/*\n * Direct port to TypeScript of ZXing by <PERSON>\n */\n/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Enumerates barcode formats known to this package. Please keep alphabetized.\n *\n * <AUTHOR>\n */\nvar BarcodeFormat;\n(function (BarcodeFormat) {\n  /** Aztec 2D barcode format. */\n  BarcodeFormat[BarcodeFormat[\"AZTEC\"] = 0] = \"AZTEC\";\n  /** CODABAR 1D format. */\n  BarcodeFormat[BarcodeFormat[\"CODABAR\"] = 1] = \"CODABAR\";\n  /** Code 39 1D format. */\n  BarcodeFormat[BarcodeFormat[\"CODE_39\"] = 2] = \"CODE_39\";\n  /** Code 93 1D format. */\n  BarcodeFormat[BarcodeFormat[\"CODE_93\"] = 3] = \"CODE_93\";\n  /** Code 128 1D format. */\n  BarcodeFormat[BarcodeFormat[\"CODE_128\"] = 4] = \"CODE_128\";\n  /** Data Matrix 2D barcode format. */\n  BarcodeFormat[BarcodeFormat[\"DATA_MATRIX\"] = 5] = \"DATA_MATRIX\";\n  /** EAN-8 1D format. */\n  BarcodeFormat[BarcodeFormat[\"EAN_8\"] = 6] = \"EAN_8\";\n  /** EAN-13 1D format. */\n  BarcodeFormat[BarcodeFormat[\"EAN_13\"] = 7] = \"EAN_13\";\n  /** ITF (Interleaved Two of Five) 1D format. */\n  BarcodeFormat[BarcodeFormat[\"ITF\"] = 8] = \"ITF\";\n  /** MaxiCode 2D barcode format. */\n  BarcodeFormat[BarcodeFormat[\"MAXICODE\"] = 9] = \"MAXICODE\";\n  /** PDF417 format. */\n  BarcodeFormat[BarcodeFormat[\"PDF_417\"] = 10] = \"PDF_417\";\n  /** QR Code 2D barcode format. */\n  BarcodeFormat[BarcodeFormat[\"QR_CODE\"] = 11] = \"QR_CODE\";\n  /** RSS 14 */\n  BarcodeFormat[BarcodeFormat[\"RSS_14\"] = 12] = \"RSS_14\";\n  /** RSS EXPANDED */\n  BarcodeFormat[BarcodeFormat[\"RSS_EXPANDED\"] = 13] = \"RSS_EXPANDED\";\n  /** UPC-A 1D format. */\n  BarcodeFormat[BarcodeFormat[\"UPC_A\"] = 14] = \"UPC_A\";\n  /** UPC-E 1D format. */\n  BarcodeFormat[BarcodeFormat[\"UPC_E\"] = 15] = \"UPC_E\";\n  /** UPC/EAN extension format. Not a stand-alone format. */\n  BarcodeFormat[BarcodeFormat[\"UPC_EAN_EXTENSION\"] = 16] = \"UPC_EAN_EXTENSION\";\n})(BarcodeFormat || (BarcodeFormat = {}));\nexport default BarcodeFormat;", "map": {"version": 3, "names": ["BarcodeFormat"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/BarcodeFormat.js"], "sourcesContent": ["/*\n * Direct port to TypeScript of ZXing by <PERSON>\n */\n/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Enumerates barcode formats known to this package. Please keep alphabetized.\n *\n * <AUTHOR>\n */\nvar BarcodeFormat;\n(function (BarcodeFormat) {\n    /** Aztec 2D barcode format. */\n    BarcodeFormat[BarcodeFormat[\"AZTEC\"] = 0] = \"AZTEC\";\n    /** CODABAR 1D format. */\n    BarcodeFormat[BarcodeFormat[\"CODABAR\"] = 1] = \"CODABAR\";\n    /** Code 39 1D format. */\n    BarcodeFormat[BarcodeFormat[\"CODE_39\"] = 2] = \"CODE_39\";\n    /** Code 93 1D format. */\n    BarcodeFormat[BarcodeFormat[\"CODE_93\"] = 3] = \"CODE_93\";\n    /** Code 128 1D format. */\n    BarcodeFormat[BarcodeFormat[\"CODE_128\"] = 4] = \"CODE_128\";\n    /** Data Matrix 2D barcode format. */\n    BarcodeFormat[BarcodeFormat[\"DATA_MATRIX\"] = 5] = \"DATA_MATRIX\";\n    /** EAN-8 1D format. */\n    BarcodeFormat[BarcodeFormat[\"EAN_8\"] = 6] = \"EAN_8\";\n    /** EAN-13 1D format. */\n    BarcodeFormat[BarcodeFormat[\"EAN_13\"] = 7] = \"EAN_13\";\n    /** ITF (Interleaved Two of Five) 1D format. */\n    BarcodeFormat[BarcodeFormat[\"ITF\"] = 8] = \"ITF\";\n    /** MaxiCode 2D barcode format. */\n    BarcodeFormat[BarcodeFormat[\"MAXICODE\"] = 9] = \"MAXICODE\";\n    /** PDF417 format. */\n    BarcodeFormat[BarcodeFormat[\"PDF_417\"] = 10] = \"PDF_417\";\n    /** QR Code 2D barcode format. */\n    BarcodeFormat[BarcodeFormat[\"QR_CODE\"] = 11] = \"QR_CODE\";\n    /** RSS 14 */\n    BarcodeFormat[BarcodeFormat[\"RSS_14\"] = 12] = \"RSS_14\";\n    /** RSS EXPANDED */\n    BarcodeFormat[BarcodeFormat[\"RSS_EXPANDED\"] = 13] = \"RSS_EXPANDED\";\n    /** UPC-A 1D format. */\n    BarcodeFormat[BarcodeFormat[\"UPC_A\"] = 14] = \"UPC_A\";\n    /** UPC-E 1D format. */\n    BarcodeFormat[BarcodeFormat[\"UPC_E\"] = 15] = \"UPC_E\";\n    /** UPC/EAN extension format. Not a stand-alone format. */\n    BarcodeFormat[BarcodeFormat[\"UPC_EAN_EXTENSION\"] = 16] = \"UPC_EAN_EXTENSION\";\n})(BarcodeFormat || (BarcodeFormat = {}));\nexport default BarcodeFormat;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,aAAa;AACjB,CAAC,UAAUA,aAAa,EAAE;EACtB;EACAA,aAAa,CAACA,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnD;EACAA,aAAa,CAACA,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvD;EACAA,aAAa,CAACA,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvD;EACAA,aAAa,CAACA,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACvD;EACAA,aAAa,CAACA,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzD;EACAA,aAAa,CAACA,aAAa,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC/D;EACAA,aAAa,CAACA,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnD;EACAA,aAAa,CAACA,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACrD;EACAA,aAAa,CAACA,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EAC/C;EACAA,aAAa,CAACA,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EACzD;EACAA,aAAa,CAACA,aAAa,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS;EACxD;EACAA,aAAa,CAACA,aAAa,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG,SAAS;EACxD;EACAA,aAAa,CAACA,aAAa,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,GAAG,QAAQ;EACtD;EACAA,aAAa,CAACA,aAAa,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc;EAClE;EACAA,aAAa,CAACA,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EACpD;EACAA,aAAa,CAACA,aAAa,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO;EACpD;EACAA,aAAa,CAACA,aAAa,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC,GAAG,mBAAmB;AAChF,CAAC,EAAEA,aAAa,KAAKA,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;AACzC,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}