{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport NotFoundException from '../NotFoundException';\n/**\n * Implementations of this class can, given locations of finder patterns for a QR code in an\n * image, sample the right points in the image to reconstruct the QR code, accounting for\n * perspective distortion. It is abstracted since it is relatively expensive and should be allowed\n * to take advantage of platform-specific optimized implementations, like Sun's Java Advanced\n * Imaging library, but which may not be available in other environments such as J2ME, and vice\n * versa.\n *\n * The implementation used can be controlled by calling {@link #setGridSampler(GridSampler)}\n * with an instance of a class which implements this interface.\n *\n * <AUTHOR>\n */\nvar GridSampler = /** @class */function () {\n  function GridSampler() {}\n  /**\n   * <p>Checks a set of points that have been transformed to sample points on an image against\n   * the image's dimensions to see if the point are even within the image.</p>\n   *\n   * <p>This method will actually \"nudge\" the endpoints back onto the image if they are found to be\n   * barely (less than 1 pixel) off the image. This accounts for imperfect detection of finder\n   * patterns in an image where the QR Code runs all the way to the image border.</p>\n   *\n   * <p>For efficiency, the method will check points from either end of the line until one is found\n   * to be within the image. Because the set of points are assumed to be linear, this is valid.</p>\n   *\n   * @param image image into which the points should map\n   * @param points actual points in x1,y1,...,xn,yn form\n   * @throws NotFoundException if an endpoint is lies outside the image boundaries\n   */\n  GridSampler.checkAndNudgePoints = function (image, points) {\n    var width = image.getWidth();\n    var height = image.getHeight();\n    // Check and nudge points from start until we see some that are OK:\n    var nudged = true;\n    for (var offset = 0; offset < points.length && nudged; offset += 2) {\n      var x = Math.floor(points[offset]);\n      var y = Math.floor(points[offset + 1]);\n      if (x < -1 || x > width || y < -1 || y > height) {\n        throw new NotFoundException();\n      }\n      nudged = false;\n      if (x === -1) {\n        points[offset] = 0.0;\n        nudged = true;\n      } else if (x === width) {\n        points[offset] = width - 1;\n        nudged = true;\n      }\n      if (y === -1) {\n        points[offset + 1] = 0.0;\n        nudged = true;\n      } else if (y === height) {\n        points[offset + 1] = height - 1;\n        nudged = true;\n      }\n    }\n    // Check and nudge points from end:\n    nudged = true;\n    for (var offset = points.length - 2; offset >= 0 && nudged; offset -= 2) {\n      var x = Math.floor(points[offset]);\n      var y = Math.floor(points[offset + 1]);\n      if (x < -1 || x > width || y < -1 || y > height) {\n        throw new NotFoundException();\n      }\n      nudged = false;\n      if (x === -1) {\n        points[offset] = 0.0;\n        nudged = true;\n      } else if (x === width) {\n        points[offset] = width - 1;\n        nudged = true;\n      }\n      if (y === -1) {\n        points[offset + 1] = 0.0;\n        nudged = true;\n      } else if (y === height) {\n        points[offset + 1] = height - 1;\n        nudged = true;\n      }\n    }\n  };\n  return GridSampler;\n}();\nexport default GridSampler;", "map": {"version": 3, "names": ["NotFoundException", "GridSampler", "checkAndNudgePoints", "image", "points", "width", "getWidth", "height", "getHeight", "nudged", "offset", "length", "x", "Math", "floor", "y"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/GridSampler.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport NotFoundException from '../NotFoundException';\n/**\n * Implementations of this class can, given locations of finder patterns for a QR code in an\n * image, sample the right points in the image to reconstruct the QR code, accounting for\n * perspective distortion. It is abstracted since it is relatively expensive and should be allowed\n * to take advantage of platform-specific optimized implementations, like Sun's Java Advanced\n * Imaging library, but which may not be available in other environments such as J2ME, and vice\n * versa.\n *\n * The implementation used can be controlled by calling {@link #setGridSampler(GridSampler)}\n * with an instance of a class which implements this interface.\n *\n * <AUTHOR>\n */\nvar GridSampler = /** @class */ (function () {\n    function GridSampler() {\n    }\n    /**\n     * <p>Checks a set of points that have been transformed to sample points on an image against\n     * the image's dimensions to see if the point are even within the image.</p>\n     *\n     * <p>This method will actually \"nudge\" the endpoints back onto the image if they are found to be\n     * barely (less than 1 pixel) off the image. This accounts for imperfect detection of finder\n     * patterns in an image where the QR Code runs all the way to the image border.</p>\n     *\n     * <p>For efficiency, the method will check points from either end of the line until one is found\n     * to be within the image. Because the set of points are assumed to be linear, this is valid.</p>\n     *\n     * @param image image into which the points should map\n     * @param points actual points in x1,y1,...,xn,yn form\n     * @throws NotFoundException if an endpoint is lies outside the image boundaries\n     */\n    GridSampler.checkAndNudgePoints = function (image, points) {\n        var width = image.getWidth();\n        var height = image.getHeight();\n        // Check and nudge points from start until we see some that are OK:\n        var nudged = true;\n        for (var offset = 0; offset < points.length && nudged; offset += 2) {\n            var x = Math.floor(points[offset]);\n            var y = Math.floor(points[offset + 1]);\n            if (x < -1 || x > width || y < -1 || y > height) {\n                throw new NotFoundException();\n            }\n            nudged = false;\n            if (x === -1) {\n                points[offset] = 0.0;\n                nudged = true;\n            }\n            else if (x === width) {\n                points[offset] = width - 1;\n                nudged = true;\n            }\n            if (y === -1) {\n                points[offset + 1] = 0.0;\n                nudged = true;\n            }\n            else if (y === height) {\n                points[offset + 1] = height - 1;\n                nudged = true;\n            }\n        }\n        // Check and nudge points from end:\n        nudged = true;\n        for (var offset = points.length - 2; offset >= 0 && nudged; offset -= 2) {\n            var x = Math.floor(points[offset]);\n            var y = Math.floor(points[offset + 1]);\n            if (x < -1 || x > width || y < -1 || y > height) {\n                throw new NotFoundException();\n            }\n            nudged = false;\n            if (x === -1) {\n                points[offset] = 0.0;\n                nudged = true;\n            }\n            else if (x === width) {\n                points[offset] = width - 1;\n                nudged = true;\n            }\n            if (y === -1) {\n                points[offset + 1] = 0.0;\n                nudged = true;\n            }\n            else if (y === height) {\n                points[offset + 1] = height - 1;\n                nudged = true;\n            }\n        }\n    };\n    return GridSampler;\n}());\nexport default GridSampler;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,iBAAiB,MAAM,sBAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG,CACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,WAAW,CAACC,mBAAmB,GAAG,UAAUC,KAAK,EAAEC,MAAM,EAAE;IACvD,IAAIC,KAAK,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC;IAC5B,IAAIC,MAAM,GAAGJ,KAAK,CAACK,SAAS,CAAC,CAAC;IAC9B;IACA,IAAIC,MAAM,GAAG,IAAI;IACjB,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGN,MAAM,CAACO,MAAM,IAAIF,MAAM,EAAEC,MAAM,IAAI,CAAC,EAAE;MAChE,IAAIE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACV,MAAM,CAACM,MAAM,CAAC,CAAC;MAClC,IAAIK,CAAC,GAAGF,IAAI,CAACC,KAAK,CAACV,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC,CAAC;MACtC,IAAIE,CAAC,GAAG,CAAC,CAAC,IAAIA,CAAC,GAAGP,KAAK,IAAIU,CAAC,GAAG,CAAC,CAAC,IAAIA,CAAC,GAAGR,MAAM,EAAE;QAC7C,MAAM,IAAIP,iBAAiB,CAAC,CAAC;MACjC;MACAS,MAAM,GAAG,KAAK;MACd,IAAIG,CAAC,KAAK,CAAC,CAAC,EAAE;QACVR,MAAM,CAACM,MAAM,CAAC,GAAG,GAAG;QACpBD,MAAM,GAAG,IAAI;MACjB,CAAC,MACI,IAAIG,CAAC,KAAKP,KAAK,EAAE;QAClBD,MAAM,CAACM,MAAM,CAAC,GAAGL,KAAK,GAAG,CAAC;QAC1BI,MAAM,GAAG,IAAI;MACjB;MACA,IAAIM,CAAC,KAAK,CAAC,CAAC,EAAE;QACVX,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;QACxBD,MAAM,GAAG,IAAI;MACjB,CAAC,MACI,IAAIM,CAAC,KAAKR,MAAM,EAAE;QACnBH,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC,GAAGH,MAAM,GAAG,CAAC;QAC/BE,MAAM,GAAG,IAAI;MACjB;IACJ;IACA;IACAA,MAAM,GAAG,IAAI;IACb,KAAK,IAAIC,MAAM,GAAGN,MAAM,CAACO,MAAM,GAAG,CAAC,EAAED,MAAM,IAAI,CAAC,IAAID,MAAM,EAAEC,MAAM,IAAI,CAAC,EAAE;MACrE,IAAIE,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACV,MAAM,CAACM,MAAM,CAAC,CAAC;MAClC,IAAIK,CAAC,GAAGF,IAAI,CAACC,KAAK,CAACV,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC,CAAC;MACtC,IAAIE,CAAC,GAAG,CAAC,CAAC,IAAIA,CAAC,GAAGP,KAAK,IAAIU,CAAC,GAAG,CAAC,CAAC,IAAIA,CAAC,GAAGR,MAAM,EAAE;QAC7C,MAAM,IAAIP,iBAAiB,CAAC,CAAC;MACjC;MACAS,MAAM,GAAG,KAAK;MACd,IAAIG,CAAC,KAAK,CAAC,CAAC,EAAE;QACVR,MAAM,CAACM,MAAM,CAAC,GAAG,GAAG;QACpBD,MAAM,GAAG,IAAI;MACjB,CAAC,MACI,IAAIG,CAAC,KAAKP,KAAK,EAAE;QAClBD,MAAM,CAACM,MAAM,CAAC,GAAGL,KAAK,GAAG,CAAC;QAC1BI,MAAM,GAAG,IAAI;MACjB;MACA,IAAIM,CAAC,KAAK,CAAC,CAAC,EAAE;QACVX,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;QACxBD,MAAM,GAAG,IAAI;MACjB,CAAC,MACI,IAAIM,CAAC,KAAKR,MAAM,EAAE;QACnBH,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC,GAAGH,MAAM,GAAG,CAAC;QAC/BE,MAAM,GAAG,IAAI;MACjB;IACJ;EACJ,CAAC;EACD,OAAOR,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}