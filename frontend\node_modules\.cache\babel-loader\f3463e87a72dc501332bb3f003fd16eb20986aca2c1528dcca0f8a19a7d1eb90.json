{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useValidation } from \"../../../validation/index.js\";\nimport { useUtils } from \"../useUtils.js\";\nimport { adjustSectionValue, getSectionOrder } from \"./useField.utils.js\";\nimport { useFieldState } from \"./useFieldState.js\";\nimport { useFieldCharacterEditing } from \"./useFieldCharacterEditing.js\";\nimport { useFieldV7TextField } from \"./useFieldV7TextField.js\";\nimport { useFieldV6TextField } from \"./useFieldV6TextField.js\";\nexport const useField = params => {\n  const utils = useUtils();\n  const {\n    internalProps,\n    internalProps: {\n      unstableFieldRef,\n      minutesStep,\n      enableAccessibleFieldDOMStructure = false,\n      disabled = false,\n      readOnly = false\n    },\n    forwardedProps: {\n      onKeyDown,\n      error,\n      clearable,\n      onClear\n    },\n    fieldValueManager,\n    valueManager,\n    validator\n  } = params;\n  const isRtl = useRtl();\n  const stateResponse = useFieldState(params);\n  const {\n    state,\n    activeSectionIndex,\n    parsedSelectedSections,\n    setSelectedSections,\n    clearValue,\n    clearActiveSection,\n    updateSectionValue,\n    setTempAndroidValueStr,\n    sectionsValueBoundaries,\n    localizedDigits,\n    timezone\n  } = stateResponse;\n  const characterEditingResponse = useFieldCharacterEditing({\n    sections: state.sections,\n    updateSectionValue,\n    sectionsValueBoundaries,\n    localizedDigits,\n    setTempAndroidValueStr,\n    timezone\n  });\n  const {\n    resetCharacterQuery\n  } = characterEditingResponse;\n  const areAllSectionsEmpty = valueManager.areValuesEqual(utils, state.value, valueManager.emptyValue);\n  const useFieldTextField = enableAccessibleFieldDOMStructure ? useFieldV7TextField : useFieldV6TextField;\n  const sectionOrder = React.useMemo(() => getSectionOrder(state.sections, isRtl && !enableAccessibleFieldDOMStructure), [state.sections, isRtl, enableAccessibleFieldDOMStructure]);\n  const {\n    returnedValue,\n    interactions\n  } = useFieldTextField(_extends({}, params, stateResponse, characterEditingResponse, {\n    areAllSectionsEmpty,\n    sectionOrder\n  }));\n  const handleContainerKeyDown = useEventCallback(event => {\n    onKeyDown?.(event);\n    if (disabled) {\n      return;\n    }\n    // eslint-disable-next-line default-case\n    switch (true) {\n      // Select all\n      case (event.ctrlKey || event.metaKey) && String.fromCharCode(event.keyCode) === 'A' && !event.shiftKey && !event.altKey:\n        {\n          // prevent default to make sure that the next line \"select all\" while updating\n          // the internal state at the same time.\n          event.preventDefault();\n          setSelectedSections('all');\n          break;\n        }\n\n      // Move selection to next section\n      case event.key === 'ArrowRight':\n        {\n          event.preventDefault();\n          if (parsedSelectedSections == null) {\n            setSelectedSections(sectionOrder.startIndex);\n          } else if (parsedSelectedSections === 'all') {\n            setSelectedSections(sectionOrder.endIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[parsedSelectedSections].rightIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Move selection to previous section\n      case event.key === 'ArrowLeft':\n        {\n          event.preventDefault();\n          if (parsedSelectedSections == null) {\n            setSelectedSections(sectionOrder.endIndex);\n          } else if (parsedSelectedSections === 'all') {\n            setSelectedSections(sectionOrder.startIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[parsedSelectedSections].leftIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Reset the value of the selected section\n      case event.key === 'Delete':\n        {\n          event.preventDefault();\n          if (readOnly) {\n            break;\n          }\n          if (parsedSelectedSections == null || parsedSelectedSections === 'all') {\n            clearValue();\n          } else {\n            clearActiveSection();\n          }\n          resetCharacterQuery();\n          break;\n        }\n\n      // Increment / decrement the selected section value\n      case ['ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown'].includes(event.key):\n        {\n          event.preventDefault();\n          if (readOnly || activeSectionIndex == null) {\n            break;\n          }\n\n          // if all sections are selected, mark the currently editing one as selected\n          if (parsedSelectedSections === 'all') {\n            setSelectedSections(activeSectionIndex);\n          }\n          const activeSection = state.sections[activeSectionIndex];\n          const activeDateManager = fieldValueManager.getActiveDateManager(utils, state, activeSection);\n          const newSectionValue = adjustSectionValue(utils, timezone, activeSection, event.key, sectionsValueBoundaries, localizedDigits, activeDateManager.date, {\n            minutesStep\n          });\n          updateSectionValue({\n            activeSection,\n            newSectionValue,\n            shouldGoToNextSection: false\n          });\n          break;\n        }\n    }\n  });\n  useEnhancedEffect(() => {\n    interactions.syncSelectionToDOM();\n  });\n  const {\n    hasValidationError\n  } = useValidation({\n    props: internalProps,\n    validator,\n    timezone,\n    value: state.value,\n    onError: internalProps.onError\n  });\n  const inputError = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (error !== undefined) {\n      return error;\n    }\n    return hasValidationError;\n  }, [hasValidationError, error]);\n  React.useEffect(() => {\n    if (!inputError && activeSectionIndex == null) {\n      resetCharacterQuery();\n    }\n  }, [state.referenceValue, activeSectionIndex, inputError]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // If `tempValueStrAndroid` is still defined for some section when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && activeSectionIndex != null) {\n      resetCharacterQuery();\n      clearActiveSection();\n    }\n  }, [state.sections]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: interactions.getActiveSectionIndexFromDOM,\n    setSelectedSections: interactions.setSelectedSections,\n    focusField: interactions.focusField,\n    isFieldFocused: interactions.isFieldFocused\n  }));\n  const handleClearValue = useEventCallback((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!interactions.isFieldFocused()) {\n      // setSelectedSections is called internally\n      interactions.focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const commonForwardedProps = {\n    onKeyDown: handleContainerKeyDown,\n    onClear: handleClearValue,\n    error: inputError,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled)\n  };\n  const commonAdditionalProps = {\n    disabled,\n    readOnly\n  };\n  return _extends({}, params.forwardedProps, commonForwardedProps, commonAdditionalProps, returnedValue);\n};", "map": {"version": 3, "names": ["_extends", "React", "useEnhancedEffect", "useEventCallback", "useRtl", "useValidation", "useUtils", "adjustSectionValue", "getSectionOrder", "useFieldState", "useFieldCharacterEditing", "useFieldV7TextField", "useFieldV6TextField", "useField", "params", "utils", "internalProps", "unstableFieldRef", "minutesStep", "enableAccessibleFieldDOMStructure", "disabled", "readOnly", "forwardedProps", "onKeyDown", "error", "clearable", "onClear", "field<PERSON><PERSON>ueManager", "valueManager", "validator", "isRtl", "stateResponse", "state", "activeSectionIndex", "parsedSelectedSections", "setSelectedSections", "clearValue", "clearActiveSection", "updateSectionValue", "setTempAndroidValueStr", "sectionsValueBoundaries", "localizedDigits", "timezone", "characterEditingResponse", "sections", "resetCharacterQuery", "areAllSectionsEmpty", "areValuesEqual", "value", "emptyValue", "useFieldTextField", "sectionOrder", "useMemo", "returnedValue", "interactions", "handleContainerKeyDown", "event", "ctrl<PERSON>ey", "metaKey", "String", "fromCharCode", "keyCode", "shift<PERSON>ey", "altKey", "preventDefault", "key", "startIndex", "endIndex", "nextSectionIndex", "neighbors", "rightIndex", "leftIndex", "includes", "activeSection", "activeDateManager", "getActiveDateManager", "newSectionValue", "date", "shouldGoToNextSection", "syncSelectionToDOM", "hasValidationError", "props", "onError", "inputError", "undefined", "useEffect", "referenceValue", "tempValueStrAndroid", "useImperativeHandle", "getSections", "getActiveSectionIndex", "getActiveSectionIndexFromDOM", "focusField", "isFieldFocused", "handleClearValue", "args", "commonForwardedProps", "Boolean", "commonAdditionalProps"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useField.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useValidation } from \"../../../validation/index.js\";\nimport { useUtils } from \"../useUtils.js\";\nimport { adjustSectionValue, getSectionOrder } from \"./useField.utils.js\";\nimport { useFieldState } from \"./useFieldState.js\";\nimport { useFieldCharacterEditing } from \"./useFieldCharacterEditing.js\";\nimport { useFieldV7TextField } from \"./useFieldV7TextField.js\";\nimport { useFieldV6TextField } from \"./useFieldV6TextField.js\";\nexport const useField = params => {\n  const utils = useUtils();\n  const {\n    internalProps,\n    internalProps: {\n      unstableFieldRef,\n      minutesStep,\n      enableAccessibleFieldDOMStructure = false,\n      disabled = false,\n      readOnly = false\n    },\n    forwardedProps: {\n      onKeyDown,\n      error,\n      clearable,\n      onClear\n    },\n    fieldValueManager,\n    valueManager,\n    validator\n  } = params;\n  const isRtl = useRtl();\n  const stateResponse = useFieldState(params);\n  const {\n    state,\n    activeSectionIndex,\n    parsedSelectedSections,\n    setSelectedSections,\n    clearValue,\n    clearActiveSection,\n    updateSectionValue,\n    setTempAndroidValueStr,\n    sectionsValueBoundaries,\n    localizedDigits,\n    timezone\n  } = stateResponse;\n  const characterEditingResponse = useFieldCharacterEditing({\n    sections: state.sections,\n    updateSectionValue,\n    sectionsValueBoundaries,\n    localizedDigits,\n    setTempAndroidValueStr,\n    timezone\n  });\n  const {\n    resetCharacterQuery\n  } = characterEditingResponse;\n  const areAllSectionsEmpty = valueManager.areValuesEqual(utils, state.value, valueManager.emptyValue);\n  const useFieldTextField = enableAccessibleFieldDOMStructure ? useFieldV7TextField : useFieldV6TextField;\n  const sectionOrder = React.useMemo(() => getSectionOrder(state.sections, isRtl && !enableAccessibleFieldDOMStructure), [state.sections, isRtl, enableAccessibleFieldDOMStructure]);\n  const {\n    returnedValue,\n    interactions\n  } = useFieldTextField(_extends({}, params, stateResponse, characterEditingResponse, {\n    areAllSectionsEmpty,\n    sectionOrder\n  }));\n  const handleContainerKeyDown = useEventCallback(event => {\n    onKeyDown?.(event);\n    if (disabled) {\n      return;\n    }\n    // eslint-disable-next-line default-case\n    switch (true) {\n      // Select all\n      case (event.ctrlKey || event.metaKey) && String.fromCharCode(event.keyCode) === 'A' && !event.shiftKey && !event.altKey:\n        {\n          // prevent default to make sure that the next line \"select all\" while updating\n          // the internal state at the same time.\n          event.preventDefault();\n          setSelectedSections('all');\n          break;\n        }\n\n      // Move selection to next section\n      case event.key === 'ArrowRight':\n        {\n          event.preventDefault();\n          if (parsedSelectedSections == null) {\n            setSelectedSections(sectionOrder.startIndex);\n          } else if (parsedSelectedSections === 'all') {\n            setSelectedSections(sectionOrder.endIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[parsedSelectedSections].rightIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Move selection to previous section\n      case event.key === 'ArrowLeft':\n        {\n          event.preventDefault();\n          if (parsedSelectedSections == null) {\n            setSelectedSections(sectionOrder.endIndex);\n          } else if (parsedSelectedSections === 'all') {\n            setSelectedSections(sectionOrder.startIndex);\n          } else {\n            const nextSectionIndex = sectionOrder.neighbors[parsedSelectedSections].leftIndex;\n            if (nextSectionIndex !== null) {\n              setSelectedSections(nextSectionIndex);\n            }\n          }\n          break;\n        }\n\n      // Reset the value of the selected section\n      case event.key === 'Delete':\n        {\n          event.preventDefault();\n          if (readOnly) {\n            break;\n          }\n          if (parsedSelectedSections == null || parsedSelectedSections === 'all') {\n            clearValue();\n          } else {\n            clearActiveSection();\n          }\n          resetCharacterQuery();\n          break;\n        }\n\n      // Increment / decrement the selected section value\n      case ['ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown'].includes(event.key):\n        {\n          event.preventDefault();\n          if (readOnly || activeSectionIndex == null) {\n            break;\n          }\n\n          // if all sections are selected, mark the currently editing one as selected\n          if (parsedSelectedSections === 'all') {\n            setSelectedSections(activeSectionIndex);\n          }\n          const activeSection = state.sections[activeSectionIndex];\n          const activeDateManager = fieldValueManager.getActiveDateManager(utils, state, activeSection);\n          const newSectionValue = adjustSectionValue(utils, timezone, activeSection, event.key, sectionsValueBoundaries, localizedDigits, activeDateManager.date, {\n            minutesStep\n          });\n          updateSectionValue({\n            activeSection,\n            newSectionValue,\n            shouldGoToNextSection: false\n          });\n          break;\n        }\n    }\n  });\n  useEnhancedEffect(() => {\n    interactions.syncSelectionToDOM();\n  });\n  const {\n    hasValidationError\n  } = useValidation({\n    props: internalProps,\n    validator,\n    timezone,\n    value: state.value,\n    onError: internalProps.onError\n  });\n  const inputError = React.useMemo(() => {\n    // only override when `error` is undefined.\n    // in case of multi input fields, the `error` value is provided externally and will always be defined.\n    if (error !== undefined) {\n      return error;\n    }\n    return hasValidationError;\n  }, [hasValidationError, error]);\n  React.useEffect(() => {\n    if (!inputError && activeSectionIndex == null) {\n      resetCharacterQuery();\n    }\n  }, [state.referenceValue, activeSectionIndex, inputError]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  // If `tempValueStrAndroid` is still defined for some section when running `useEffect`,\n  // Then `onChange` has only been called once, which means the user pressed `Backspace` to reset the section.\n  // This causes a small flickering on Android,\n  // But we can't use `useEnhancedEffect` which is always called before the second `onChange` call and then would cause false positives.\n  React.useEffect(() => {\n    if (state.tempValueStrAndroid != null && activeSectionIndex != null) {\n      resetCharacterQuery();\n      clearActiveSection();\n    }\n  }, [state.sections]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  React.useImperativeHandle(unstableFieldRef, () => ({\n    getSections: () => state.sections,\n    getActiveSectionIndex: interactions.getActiveSectionIndexFromDOM,\n    setSelectedSections: interactions.setSelectedSections,\n    focusField: interactions.focusField,\n    isFieldFocused: interactions.isFieldFocused\n  }));\n  const handleClearValue = useEventCallback((event, ...args) => {\n    event.preventDefault();\n    onClear?.(event, ...args);\n    clearValue();\n    if (!interactions.isFieldFocused()) {\n      // setSelectedSections is called internally\n      interactions.focusField(0);\n    } else {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const commonForwardedProps = {\n    onKeyDown: handleContainerKeyDown,\n    onClear: handleClearValue,\n    error: inputError,\n    clearable: Boolean(clearable && !areAllSectionsEmpty && !readOnly && !disabled)\n  };\n  const commonAdditionalProps = {\n    disabled,\n    readOnly\n  };\n  return _extends({}, params.forwardedProps, commonForwardedProps, commonAdditionalProps, returnedValue);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,kBAAkB,EAAEC,eAAe,QAAQ,qBAAqB;AACzE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,MAAMC,QAAQ,GAAGC,MAAM,IAAI;EAChC,MAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJU,aAAa;IACbA,aAAa,EAAE;MACbC,gBAAgB;MAChBC,WAAW;MACXC,iCAAiC,GAAG,KAAK;MACzCC,QAAQ,GAAG,KAAK;MAChBC,QAAQ,GAAG;IACb,CAAC;IACDC,cAAc,EAAE;MACdC,SAAS;MACTC,KAAK;MACLC,SAAS;MACTC;IACF,CAAC;IACDC,iBAAiB;IACjBC,YAAY;IACZC;EACF,CAAC,GAAGf,MAAM;EACV,MAAMgB,KAAK,GAAG1B,MAAM,CAAC,CAAC;EACtB,MAAM2B,aAAa,GAAGtB,aAAa,CAACK,MAAM,CAAC;EAC3C,MAAM;IACJkB,KAAK;IACLC,kBAAkB;IAClBC,sBAAsB;IACtBC,mBAAmB;IACnBC,UAAU;IACVC,kBAAkB;IAClBC,kBAAkB;IAClBC,sBAAsB;IACtBC,uBAAuB;IACvBC,eAAe;IACfC;EACF,CAAC,GAAGX,aAAa;EACjB,MAAMY,wBAAwB,GAAGjC,wBAAwB,CAAC;IACxDkC,QAAQ,EAAEZ,KAAK,CAACY,QAAQ;IACxBN,kBAAkB;IAClBE,uBAAuB;IACvBC,eAAe;IACfF,sBAAsB;IACtBG;EACF,CAAC,CAAC;EACF,MAAM;IACJG;EACF,CAAC,GAAGF,wBAAwB;EAC5B,MAAMG,mBAAmB,GAAGlB,YAAY,CAACmB,cAAc,CAAChC,KAAK,EAAEiB,KAAK,CAACgB,KAAK,EAAEpB,YAAY,CAACqB,UAAU,CAAC;EACpG,MAAMC,iBAAiB,GAAG/B,iCAAiC,GAAGR,mBAAmB,GAAGC,mBAAmB;EACvG,MAAMuC,YAAY,GAAGlD,KAAK,CAACmD,OAAO,CAAC,MAAM5C,eAAe,CAACwB,KAAK,CAACY,QAAQ,EAAEd,KAAK,IAAI,CAACX,iCAAiC,CAAC,EAAE,CAACa,KAAK,CAACY,QAAQ,EAAEd,KAAK,EAAEX,iCAAiC,CAAC,CAAC;EAClL,MAAM;IACJkC,aAAa;IACbC;EACF,CAAC,GAAGJ,iBAAiB,CAAClD,QAAQ,CAAC,CAAC,CAAC,EAAEc,MAAM,EAAEiB,aAAa,EAAEY,wBAAwB,EAAE;IAClFG,mBAAmB;IACnBK;EACF,CAAC,CAAC,CAAC;EACH,MAAMI,sBAAsB,GAAGpD,gBAAgB,CAACqD,KAAK,IAAI;IACvDjC,SAAS,GAAGiC,KAAK,CAAC;IAClB,IAAIpC,QAAQ,EAAE;MACZ;IACF;IACA;IACA,QAAQ,IAAI;MACV;MACA,KAAK,CAACoC,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,OAAO,KAAKC,MAAM,CAACC,YAAY,CAACJ,KAAK,CAACK,OAAO,CAAC,KAAK,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,IAAI,CAACN,KAAK,CAACO,MAAM;QACrH;UACE;UACA;UACAP,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB7B,mBAAmB,CAAC,KAAK,CAAC;UAC1B;QACF;;MAEF;MACA,KAAKqB,KAAK,CAACS,GAAG,KAAK,YAAY;QAC7B;UACET,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB,IAAI9B,sBAAsB,IAAI,IAAI,EAAE;YAClCC,mBAAmB,CAACgB,YAAY,CAACe,UAAU,CAAC;UAC9C,CAAC,MAAM,IAAIhC,sBAAsB,KAAK,KAAK,EAAE;YAC3CC,mBAAmB,CAACgB,YAAY,CAACgB,QAAQ,CAAC;UAC5C,CAAC,MAAM;YACL,MAAMC,gBAAgB,GAAGjB,YAAY,CAACkB,SAAS,CAACnC,sBAAsB,CAAC,CAACoC,UAAU;YAClF,IAAIF,gBAAgB,KAAK,IAAI,EAAE;cAC7BjC,mBAAmB,CAACiC,gBAAgB,CAAC;YACvC;UACF;UACA;QACF;;MAEF;MACA,KAAKZ,KAAK,CAACS,GAAG,KAAK,WAAW;QAC5B;UACET,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB,IAAI9B,sBAAsB,IAAI,IAAI,EAAE;YAClCC,mBAAmB,CAACgB,YAAY,CAACgB,QAAQ,CAAC;UAC5C,CAAC,MAAM,IAAIjC,sBAAsB,KAAK,KAAK,EAAE;YAC3CC,mBAAmB,CAACgB,YAAY,CAACe,UAAU,CAAC;UAC9C,CAAC,MAAM;YACL,MAAME,gBAAgB,GAAGjB,YAAY,CAACkB,SAAS,CAACnC,sBAAsB,CAAC,CAACqC,SAAS;YACjF,IAAIH,gBAAgB,KAAK,IAAI,EAAE;cAC7BjC,mBAAmB,CAACiC,gBAAgB,CAAC;YACvC;UACF;UACA;QACF;;MAEF;MACA,KAAKZ,KAAK,CAACS,GAAG,KAAK,QAAQ;QACzB;UACET,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB,IAAI3C,QAAQ,EAAE;YACZ;UACF;UACA,IAAIa,sBAAsB,IAAI,IAAI,IAAIA,sBAAsB,KAAK,KAAK,EAAE;YACtEE,UAAU,CAAC,CAAC;UACd,CAAC,MAAM;YACLC,kBAAkB,CAAC,CAAC;UACtB;UACAQ,mBAAmB,CAAC,CAAC;UACrB;QACF;;MAEF;MACA,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC2B,QAAQ,CAAChB,KAAK,CAACS,GAAG,CAAC;QACpF;UACET,KAAK,CAACQ,cAAc,CAAC,CAAC;UACtB,IAAI3C,QAAQ,IAAIY,kBAAkB,IAAI,IAAI,EAAE;YAC1C;UACF;;UAEA;UACA,IAAIC,sBAAsB,KAAK,KAAK,EAAE;YACpCC,mBAAmB,CAACF,kBAAkB,CAAC;UACzC;UACA,MAAMwC,aAAa,GAAGzC,KAAK,CAACY,QAAQ,CAACX,kBAAkB,CAAC;UACxD,MAAMyC,iBAAiB,GAAG/C,iBAAiB,CAACgD,oBAAoB,CAAC5D,KAAK,EAAEiB,KAAK,EAAEyC,aAAa,CAAC;UAC7F,MAAMG,eAAe,GAAGrE,kBAAkB,CAACQ,KAAK,EAAE2B,QAAQ,EAAE+B,aAAa,EAAEjB,KAAK,CAACS,GAAG,EAAEzB,uBAAuB,EAAEC,eAAe,EAAEiC,iBAAiB,CAACG,IAAI,EAAE;YACtJ3D;UACF,CAAC,CAAC;UACFoB,kBAAkB,CAAC;YACjBmC,aAAa;YACbG,eAAe;YACfE,qBAAqB,EAAE;UACzB,CAAC,CAAC;UACF;QACF;IACJ;EACF,CAAC,CAAC;EACF5E,iBAAiB,CAAC,MAAM;IACtBoD,YAAY,CAACyB,kBAAkB,CAAC,CAAC;EACnC,CAAC,CAAC;EACF,MAAM;IACJC;EACF,CAAC,GAAG3E,aAAa,CAAC;IAChB4E,KAAK,EAAEjE,aAAa;IACpBa,SAAS;IACTa,QAAQ;IACRM,KAAK,EAAEhB,KAAK,CAACgB,KAAK;IAClBkC,OAAO,EAAElE,aAAa,CAACkE;EACzB,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGlF,KAAK,CAACmD,OAAO,CAAC,MAAM;IACrC;IACA;IACA,IAAI5B,KAAK,KAAK4D,SAAS,EAAE;MACvB,OAAO5D,KAAK;IACd;IACA,OAAOwD,kBAAkB;EAC3B,CAAC,EAAE,CAACA,kBAAkB,EAAExD,KAAK,CAAC,CAAC;EAC/BvB,KAAK,CAACoF,SAAS,CAAC,MAAM;IACpB,IAAI,CAACF,UAAU,IAAIlD,kBAAkB,IAAI,IAAI,EAAE;MAC7CY,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACb,KAAK,CAACsD,cAAc,EAAErD,kBAAkB,EAAEkD,UAAU,CAAC,CAAC,CAAC,CAAC;;EAE5D;EACA;EACA;EACA;EACAlF,KAAK,CAACoF,SAAS,CAAC,MAAM;IACpB,IAAIrD,KAAK,CAACuD,mBAAmB,IAAI,IAAI,IAAItD,kBAAkB,IAAI,IAAI,EAAE;MACnEY,mBAAmB,CAAC,CAAC;MACrBR,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,EAAE,CAACL,KAAK,CAACY,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtB3C,KAAK,CAACuF,mBAAmB,CAACvE,gBAAgB,EAAE,OAAO;IACjDwE,WAAW,EAAEA,CAAA,KAAMzD,KAAK,CAACY,QAAQ;IACjC8C,qBAAqB,EAAEpC,YAAY,CAACqC,4BAA4B;IAChExD,mBAAmB,EAAEmB,YAAY,CAACnB,mBAAmB;IACrDyD,UAAU,EAAEtC,YAAY,CAACsC,UAAU;IACnCC,cAAc,EAAEvC,YAAY,CAACuC;EAC/B,CAAC,CAAC,CAAC;EACH,MAAMC,gBAAgB,GAAG3F,gBAAgB,CAAC,CAACqD,KAAK,EAAE,GAAGuC,IAAI,KAAK;IAC5DvC,KAAK,CAACQ,cAAc,CAAC,CAAC;IACtBtC,OAAO,GAAG8B,KAAK,EAAE,GAAGuC,IAAI,CAAC;IACzB3D,UAAU,CAAC,CAAC;IACZ,IAAI,CAACkB,YAAY,CAACuC,cAAc,CAAC,CAAC,EAAE;MAClC;MACAvC,YAAY,CAACsC,UAAU,CAAC,CAAC,CAAC;IAC5B,CAAC,MAAM;MACLzD,mBAAmB,CAACgB,YAAY,CAACe,UAAU,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,MAAM8B,oBAAoB,GAAG;IAC3BzE,SAAS,EAAEgC,sBAAsB;IACjC7B,OAAO,EAAEoE,gBAAgB;IACzBtE,KAAK,EAAE2D,UAAU;IACjB1D,SAAS,EAAEwE,OAAO,CAACxE,SAAS,IAAI,CAACqB,mBAAmB,IAAI,CAACzB,QAAQ,IAAI,CAACD,QAAQ;EAChF,CAAC;EACD,MAAM8E,qBAAqB,GAAG;IAC5B9E,QAAQ;IACRC;EACF,CAAC;EACD,OAAOrB,QAAQ,CAAC,CAAC,CAAC,EAAEc,MAAM,CAACQ,cAAc,EAAE0E,oBAAoB,EAAEE,qBAAqB,EAAE7C,aAAa,CAAC;AACxG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}