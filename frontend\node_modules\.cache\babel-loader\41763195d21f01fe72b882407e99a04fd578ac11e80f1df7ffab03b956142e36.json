{"ast": null, "code": "import IllegalArgumentException from '../../../IllegalArgumentException';\nimport ArithmeticException from '../../../ArithmeticException';\nvar ModulusBase = /** @class */function () {\n  function ModulusBase() {}\n  ModulusBase.prototype.add = function (a, b) {\n    return (a + b) % this.modulus;\n  };\n  ModulusBase.prototype.subtract = function (a, b) {\n    return (this.modulus + a - b) % this.modulus;\n  };\n  ModulusBase.prototype.exp = function (a) {\n    return this.expTable[a];\n  };\n  ModulusBase.prototype.log = function (a) {\n    if (a === 0) {\n      throw new IllegalArgumentException();\n    }\n    return this.logTable[a];\n  };\n  ModulusBase.prototype.inverse = function (a) {\n    if (a === 0) {\n      throw new ArithmeticException();\n    }\n    return this.expTable[this.modulus - this.logTable[a] - 1];\n  };\n  ModulusBase.prototype.multiply = function (a, b) {\n    if (a === 0 || b === 0) {\n      return 0;\n    }\n    return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.modulus - 1)];\n  };\n  ModulusBase.prototype.getSize = function () {\n    return this.modulus;\n  };\n  ModulusBase.prototype.equals = function (o) {\n    return o === this;\n  };\n  return ModulusBase;\n}();\nexport default ModulusBase;", "map": {"version": 3, "names": ["IllegalArgumentException", "ArithmeticException", "ModulusBase", "prototype", "add", "a", "b", "modulus", "subtract", "exp", "expTable", "log", "logTable", "inverse", "multiply", "getSize", "equals", "o"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/ec/ModulusBase.js"], "sourcesContent": ["import IllegalArgumentException from '../../../IllegalArgumentException';\nimport ArithmeticException from '../../../ArithmeticException';\nvar ModulusBase = /** @class */ (function () {\n    function ModulusBase() {\n    }\n    ModulusBase.prototype.add = function (a, b) {\n        return (a + b) % this.modulus;\n    };\n    ModulusBase.prototype.subtract = function (a, b) {\n        return (this.modulus + a - b) % this.modulus;\n    };\n    ModulusBase.prototype.exp = function (a) {\n        return this.expTable[a];\n    };\n    ModulusBase.prototype.log = function (a) {\n        if (a === 0) {\n            throw new IllegalArgumentException();\n        }\n        return this.logTable[a];\n    };\n    ModulusBase.prototype.inverse = function (a) {\n        if (a === 0) {\n            throw new ArithmeticException();\n        }\n        return this.expTable[this.modulus - this.logTable[a] - 1];\n    };\n    ModulusBase.prototype.multiply = function (a, b) {\n        if (a === 0 || b === 0) {\n            return 0;\n        }\n        return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.modulus - 1)];\n    };\n    ModulusBase.prototype.getSize = function () {\n        return this.modulus;\n    };\n    ModulusBase.prototype.equals = function (o) {\n        return o === this;\n    };\n    return ModulusBase;\n}());\nexport default ModulusBase;\n"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,mCAAmC;AACxE,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG,CACvB;EACAA,WAAW,CAACC,SAAS,CAACC,GAAG,GAAG,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACxC,OAAO,CAACD,CAAC,GAAGC,CAAC,IAAI,IAAI,CAACC,OAAO;EACjC,CAAC;EACDL,WAAW,CAACC,SAAS,CAACK,QAAQ,GAAG,UAAUH,CAAC,EAAEC,CAAC,EAAE;IAC7C,OAAO,CAAC,IAAI,CAACC,OAAO,GAAGF,CAAC,GAAGC,CAAC,IAAI,IAAI,CAACC,OAAO;EAChD,CAAC;EACDL,WAAW,CAACC,SAAS,CAACM,GAAG,GAAG,UAAUJ,CAAC,EAAE;IACrC,OAAO,IAAI,CAACK,QAAQ,CAACL,CAAC,CAAC;EAC3B,CAAC;EACDH,WAAW,CAACC,SAAS,CAACQ,GAAG,GAAG,UAAUN,CAAC,EAAE;IACrC,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,MAAM,IAAIL,wBAAwB,CAAC,CAAC;IACxC;IACA,OAAO,IAAI,CAACY,QAAQ,CAACP,CAAC,CAAC;EAC3B,CAAC;EACDH,WAAW,CAACC,SAAS,CAACU,OAAO,GAAG,UAAUR,CAAC,EAAE;IACzC,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,MAAM,IAAIJ,mBAAmB,CAAC,CAAC;IACnC;IACA,OAAO,IAAI,CAACS,QAAQ,CAAC,IAAI,CAACH,OAAO,GAAG,IAAI,CAACK,QAAQ,CAACP,CAAC,CAAC,GAAG,CAAC,CAAC;EAC7D,CAAC;EACDH,WAAW,CAACC,SAAS,CAACW,QAAQ,GAAG,UAAUT,CAAC,EAAEC,CAAC,EAAE;IAC7C,IAAID,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,EAAE;MACpB,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACI,QAAQ,CAAC,CAAC,IAAI,CAACE,QAAQ,CAACP,CAAC,CAAC,GAAG,IAAI,CAACO,QAAQ,CAACN,CAAC,CAAC,KAAK,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,CAAC;EACpF,CAAC;EACDL,WAAW,CAACC,SAAS,CAACY,OAAO,GAAG,YAAY;IACxC,OAAO,IAAI,CAACR,OAAO;EACvB,CAAC;EACDL,WAAW,CAACC,SAAS,CAACa,MAAM,GAAG,UAAUC,CAAC,EAAE;IACxC,OAAOA,CAAC,KAAK,IAAI;EACrB,CAAC;EACD,OAAOf,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}