{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n/**\n * <AUTHOR> Grau\n */\nvar BarcodeMetadata = /** @class */function () {\n  function BarcodeMetadata(columnCount, rowCountUpperPart, rowCountLowerPart, errorCorrectionLevel) {\n    this.columnCount = columnCount;\n    this.errorCorrectionLevel = errorCorrectionLevel;\n    this.rowCountUpperPart = rowCountUpperPart;\n    this.rowCountLowerPart = rowCountLowerPart;\n    this.rowCount = rowCountUpperPart + rowCountLowerPart;\n  }\n  BarcodeMetadata.prototype.getColumnCount = function () {\n    return this.columnCount;\n  };\n  BarcodeMetadata.prototype.getErrorCorrectionLevel = function () {\n    return this.errorCorrectionLevel;\n  };\n  BarcodeMetadata.prototype.getRowCount = function () {\n    return this.rowCount;\n  };\n  BarcodeMetadata.prototype.getRowCountUpperPart = function () {\n    return this.rowCountUpperPart;\n  };\n  BarcodeMetadata.prototype.getRowCountLowerPart = function () {\n    return this.rowCountLowerPart;\n  };\n  return BarcodeMetadata;\n}();\nexport default BarcodeMetadata;", "map": {"version": 3, "names": ["BarcodeMetadata", "columnCount", "rowCountUpperPart", "rowCountLowerPart", "errorCorrectionLevel", "rowCount", "prototype", "getColumnCount", "getErrorCorrectionLevel", "getRowCount", "getRowCountUpperPart", "getRowCountLowerPart"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/BarcodeMetadata.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.pdf417.decoder;\n/**\n * <AUTHOR> Grau\n */\nvar BarcodeMetadata = /** @class */ (function () {\n    function BarcodeMetadata(columnCount, rowCountUpperPart, rowCountLowerPart, errorCorrectionLevel) {\n        this.columnCount = columnCount;\n        this.errorCorrectionLevel = errorCorrectionLevel;\n        this.rowCountUpperPart = rowCountUpperPart;\n        this.rowCountLowerPart = rowCountLowerPart;\n        this.rowCount = rowCountUpperPart + rowCountLowerPart;\n    }\n    BarcodeMetadata.prototype.getColumnCount = function () {\n        return this.columnCount;\n    };\n    BarcodeMetadata.prototype.getErrorCorrectionLevel = function () {\n        return this.errorCorrectionLevel;\n    };\n    BarcodeMetadata.prototype.getRowCount = function () {\n        return this.rowCount;\n    };\n    BarcodeMetadata.prototype.getRowCountUpperPart = function () {\n        return this.rowCountUpperPart;\n    };\n    BarcodeMetadata.prototype.getRowCountLowerPart = function () {\n        return this.rowCountLowerPart;\n    };\n    return BarcodeMetadata;\n}());\nexport default BarcodeMetadata;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,eAAe,GAAG,aAAe,YAAY;EAC7C,SAASA,eAAeA,CAACC,WAAW,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAE;IAC9F,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACG,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,QAAQ,GAAGH,iBAAiB,GAAGC,iBAAiB;EACzD;EACAH,eAAe,CAACM,SAAS,CAACC,cAAc,GAAG,YAAY;IACnD,OAAO,IAAI,CAACN,WAAW;EAC3B,CAAC;EACDD,eAAe,CAACM,SAAS,CAACE,uBAAuB,GAAG,YAAY;IAC5D,OAAO,IAAI,CAACJ,oBAAoB;EACpC,CAAC;EACDJ,eAAe,CAACM,SAAS,CAACG,WAAW,GAAG,YAAY;IAChD,OAAO,IAAI,CAACJ,QAAQ;EACxB,CAAC;EACDL,eAAe,CAACM,SAAS,CAACI,oBAAoB,GAAG,YAAY;IACzD,OAAO,IAAI,CAACR,iBAAiB;EACjC,CAAC;EACDF,eAAe,CAACM,SAAS,CAACK,oBAAoB,GAAG,YAAY;IACzD,OAAO,IAAI,CAACR,iBAAiB;EACjC,CAAC;EACD,OAAOH,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}