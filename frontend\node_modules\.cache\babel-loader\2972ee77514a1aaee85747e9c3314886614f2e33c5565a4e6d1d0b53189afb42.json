{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.mod10 = mod10;\nexports.mod11 = mod11;\nfunction mod10(number) {\n  var sum = 0;\n  for (var i = 0; i < number.length; i++) {\n    var n = parseInt(number[i]);\n    if ((i + number.length) % 2 === 0) {\n      sum += n;\n    } else {\n      sum += n * 2 % 10 + Math.floor(n * 2 / 10);\n    }\n  }\n  return (10 - sum % 10) % 10;\n}\nfunction mod11(number) {\n  var sum = 0;\n  var weights = [2, 3, 4, 5, 6, 7];\n  for (var i = 0; i < number.length; i++) {\n    var n = parseInt(number[number.length - 1 - i]);\n    sum += weights[i % weights.length] * n;\n  }\n  return (11 - sum % 11) % 11;\n}", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "mod10", "mod11", "number", "sum", "i", "length", "n", "parseInt", "Math", "floor", "weights"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/MSI/checksums.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\nexports.mod10 = mod10;\nexports.mod11 = mod11;\nfunction mod10(number) {\n\tvar sum = 0;\n\tfor (var i = 0; i < number.length; i++) {\n\t\tvar n = parseInt(number[i]);\n\t\tif ((i + number.length) % 2 === 0) {\n\t\t\tsum += n;\n\t\t} else {\n\t\t\tsum += n * 2 % 10 + Math.floor(n * 2 / 10);\n\t\t}\n\t}\n\treturn (10 - sum % 10) % 10;\n}\n\nfunction mod11(number) {\n\tvar sum = 0;\n\tvar weights = [2, 3, 4, 5, 6, 7];\n\tfor (var i = 0; i < number.length; i++) {\n\t\tvar n = parseInt(number[number.length - 1 - i]);\n\t\tsum += weights[i % weights.length] * n;\n\t}\n\treturn (11 - sum % 11) % 11;\n}"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AACFD,OAAO,CAACE,KAAK,GAAGA,KAAK;AACrBF,OAAO,CAACG,KAAK,GAAGA,KAAK;AACrB,SAASD,KAAKA,CAACE,MAAM,EAAE;EACtB,IAAIC,GAAG,GAAG,CAAC;EACX,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIE,CAAC,GAAGC,QAAQ,CAACL,MAAM,CAACE,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACA,CAAC,GAAGF,MAAM,CAACG,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;MAClCF,GAAG,IAAIG,CAAC;IACT,CAAC,MAAM;MACNH,GAAG,IAAIG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAGE,IAAI,CAACC,KAAK,CAACH,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;IAC3C;EACD;EACA,OAAO,CAAC,EAAE,GAAGH,GAAG,GAAG,EAAE,IAAI,EAAE;AAC5B;AAEA,SAASF,KAAKA,CAACC,MAAM,EAAE;EACtB,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIO,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAChC,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIE,CAAC,GAAGC,QAAQ,CAACL,MAAM,CAACA,MAAM,CAACG,MAAM,GAAG,CAAC,GAAGD,CAAC,CAAC,CAAC;IAC/CD,GAAG,IAAIO,OAAO,CAACN,CAAC,GAAGM,OAAO,CAACL,MAAM,CAAC,GAAGC,CAAC;EACvC;EACA,OAAO,CAAC,EAAE,GAAGH,GAAG,GAAG,EAAE,IAAI,EAAE;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}