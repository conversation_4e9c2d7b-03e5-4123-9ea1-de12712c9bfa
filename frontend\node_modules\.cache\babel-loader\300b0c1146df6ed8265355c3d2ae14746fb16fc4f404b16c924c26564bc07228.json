{"ast": null, "code": "import SimpleToken from './SimpleToken';\nimport BinaryShiftToken from './BinaryShiftToken';\nexport function addBinaryShift(token, start, byteCount) {\n  // int bitCount = (byteCount * 8) + (byteCount <= 31 ? 10 : byteCount <= 62 ? 20 : 21);\n  return new BinaryShiftToken(token, start, byteCount);\n}\nexport function add(token, value, bitCount) {\n  return new SimpleToken(token, value, bitCount);\n}", "map": {"version": 3, "names": ["SimpleToken", "BinaryShiftToken", "addBinaryShift", "token", "start", "byteCount", "add", "value", "bitCount"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/TokenHelpers.js"], "sourcesContent": ["import SimpleToken from './SimpleToken';\nimport BinaryShiftToken from './BinaryShiftToken';\nexport function addBinaryShift(token, start, byteCount) {\n    // int bitCount = (byteCount * 8) + (byteCount <= 31 ? 10 : byteCount <= 62 ? 20 : 21);\n    return new BinaryShiftToken(token, start, byteCount);\n}\nexport function add(token, value, bitCount) {\n    return new SimpleToken(token, value, bitCount);\n}\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAO,SAASC,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EACpD;EACA,OAAO,IAAIJ,gBAAgB,CAACE,KAAK,EAAEC,KAAK,EAAEC,SAAS,CAAC;AACxD;AACA,OAAO,SAASC,GAAGA,CAACH,KAAK,EAAEI,KAAK,EAAEC,QAAQ,EAAE;EACxC,OAAO,IAAIR,WAAW,CAACG,KAAK,EAAEI,KAAK,EAAEC,QAAQ,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}