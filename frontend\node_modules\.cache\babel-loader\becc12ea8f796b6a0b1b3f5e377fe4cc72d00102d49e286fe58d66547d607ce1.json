{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport ChecksumException from '../../ChecksumException';\nimport BitMatrix from '../../common/BitMatrix';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport BitMatrixParser from './BitMatrixParser';\nimport DataBlock from './DataBlock';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\nimport QRCodeDecoderMetaData from './QRCodeDecoderMetaData';\n/*import java.util.Map;*/\n/**\n * <p>The main class which implements QR Code decoding -- as opposed to locating and extracting\n * the QR Code from an image.</p>\n *\n * <AUTHOR> Owen\n */\nvar Decoder = /** @class */function () {\n  function Decoder() {\n    this.rsDecoder = new ReedSolomonDecoder(GenericGF.QR_CODE_FIELD_256);\n  }\n  // public decode(image: boolean[][]): DecoderResult /*throws ChecksumException, FormatException*/ {\n  //   return decode(image, null)\n  // }\n  /**\n   * <p>Convenience method that can decode a QR Code represented as a 2D array of booleans.\n   * \"true\" is taken to mean a black module.</p>\n   *\n   * @param image booleans representing white/black QR Code modules\n   * @param hints decoding hints that should be used to influence decoding\n   * @return text and bytes encoded within the QR Code\n   * @throws FormatException if the QR Code cannot be decoded\n   * @throws ChecksumException if error correction fails\n   */\n  Decoder.prototype.decodeBooleanArray = function (image, hints) {\n    return this.decodeBitMatrix(BitMatrix.parseFromBooleanArray(image), hints);\n  };\n  // public decodeBitMatrix(bits: BitMatrix): DecoderResult /*throws ChecksumException, FormatException*/ {\n  //   return decode(bits, null)\n  // }\n  /**\n   * <p>Decodes a QR Code represented as a {@link BitMatrix}. A 1 or \"true\" is taken to mean a black module.</p>\n   *\n   * @param bits booleans representing white/black QR Code modules\n   * @param hints decoding hints that should be used to influence decoding\n   * @return text and bytes encoded within the QR Code\n   * @throws FormatException if the QR Code cannot be decoded\n   * @throws ChecksumException if error correction fails\n   */\n  Decoder.prototype.decodeBitMatrix = function (bits, hints) {\n    // Construct a parser and read version, error-correction level\n    var parser = new BitMatrixParser(bits);\n    var ex = null;\n    try {\n      return this.decodeBitMatrixParser(parser, hints);\n    } catch (e /*: FormatException, ChecksumException*/) {\n      ex = e;\n    }\n    try {\n      // Revert the bit matrix\n      parser.remask();\n      // Will be attempting a mirrored reading of the version and format info.\n      parser.setMirror(true);\n      // Preemptively read the version.\n      parser.readVersion();\n      // Preemptively read the format information.\n      parser.readFormatInformation();\n      /*\n       * Since we're here, this means we have successfully detected some kind\n       * of version and format information when mirrored. This is a good sign,\n       * that the QR code may be mirrored, and we should try once more with a\n       * mirrored content.\n       */\n      // Prepare for a mirrored reading.\n      parser.mirror();\n      var result = this.decodeBitMatrixParser(parser, hints);\n      // Success! Notify the caller that the code was mirrored.\n      result.setOther(new QRCodeDecoderMetaData(true));\n      return result;\n    } catch (e /*FormatException | ChecksumException*/) {\n      // Throw the exception from the original reading\n      if (ex !== null) {\n        throw ex;\n      }\n      throw e;\n    }\n  };\n  Decoder.prototype.decodeBitMatrixParser = function (parser, hints) {\n    var e_1, _a, e_2, _b;\n    var version = parser.readVersion();\n    var ecLevel = parser.readFormatInformation().getErrorCorrectionLevel();\n    // Read codewords\n    var codewords = parser.readCodewords();\n    // Separate into data blocks\n    var dataBlocks = DataBlock.getDataBlocks(codewords, version, ecLevel);\n    // Count total number of data bytes\n    var totalBytes = 0;\n    try {\n      for (var dataBlocks_1 = __values(dataBlocks), dataBlocks_1_1 = dataBlocks_1.next(); !dataBlocks_1_1.done; dataBlocks_1_1 = dataBlocks_1.next()) {\n        var dataBlock = dataBlocks_1_1.value;\n        totalBytes += dataBlock.getNumDataCodewords();\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (dataBlocks_1_1 && !dataBlocks_1_1.done && (_a = dataBlocks_1.return)) _a.call(dataBlocks_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    var resultBytes = new Uint8Array(totalBytes);\n    var resultOffset = 0;\n    try {\n      // Error-correct and copy data blocks together into a stream of bytes\n      for (var dataBlocks_2 = __values(dataBlocks), dataBlocks_2_1 = dataBlocks_2.next(); !dataBlocks_2_1.done; dataBlocks_2_1 = dataBlocks_2.next()) {\n        var dataBlock = dataBlocks_2_1.value;\n        var codewordBytes = dataBlock.getCodewords();\n        var numDataCodewords = dataBlock.getNumDataCodewords();\n        this.correctErrors(codewordBytes, numDataCodewords);\n        for (var i = 0; i < numDataCodewords; i++) {\n          resultBytes[resultOffset++] = codewordBytes[i];\n        }\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (dataBlocks_2_1 && !dataBlocks_2_1.done && (_b = dataBlocks_2.return)) _b.call(dataBlocks_2);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    // Decode the contents of that stream of bytes\n    return DecodedBitStreamParser.decode(resultBytes, version, ecLevel, hints);\n  };\n  /**\n   * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n   * correct the errors in-place using Reed-Solomon error correction.</p>\n   *\n   * @param codewordBytes data and error correction codewords\n   * @param numDataCodewords number of codewords that are data bytes\n   * @throws ChecksumException if error correction fails\n   */\n  Decoder.prototype.correctErrors = function (codewordBytes, numDataCodewords /*int*/) {\n    // const numCodewords = codewordBytes.length;\n    // First read into an array of ints\n    var codewordsInts = new Int32Array(codewordBytes);\n    // TYPESCRIPTPORT: not realy necessary to transform to ints? could redesign everything to work with unsigned bytes?\n    // const codewordsInts = new Int32Array(numCodewords)\n    // for (let i = 0; i < numCodewords; i++) {\n    //   codewordsInts[i] = codewordBytes[i] & 0xFF\n    // }\n    try {\n      this.rsDecoder.decode(codewordsInts, codewordBytes.length - numDataCodewords);\n    } catch (ignored /*: ReedSolomonException*/) {\n      throw new ChecksumException();\n    }\n    // Copy back into array of bytes -- only need to worry about the bytes that were data\n    // We don't care about errors in the error-correction codewords\n    for (var i = 0; i < numDataCodewords; i++) {\n      codewordBytes[i] = /*(byte) */codewordsInts[i];\n    }\n  };\n  return Decoder;\n}();\nexport default Decoder;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "ChecksumException", "BitMatrix", "GenericGF", "ReedSolomonDecoder", "BitMatrixParser", "DataBlock", "DecodedBitStreamParser", "QRCodeDecoderMetaData", "Decoder", "rsDecoder", "QR_CODE_FIELD_256", "prototype", "decodeBooleanArray", "image", "hints", "decodeBitMatrix", "parseFromBooleanArray", "bits", "parser", "ex", "decodeBitMatrixParser", "e", "remask", "setMirror", "readVersion", "readFormatInformation", "mirror", "result", "setOther", "e_1", "_a", "e_2", "_b", "version", "ecLevel", "getErrorCorrectionLevel", "codewords", "readCodewords", "dataBlocks", "getDataBlocks", "totalBytes", "dataBlocks_1", "dataBlocks_1_1", "dataBlock", "getNumDataCodewords", "e_1_1", "error", "return", "resultBytes", "Uint8Array", "resultOffset", "dataBlocks_2", "dataBlocks_2_1", "codewordBytes", "getCodewords", "numDataCodewords", "correctErrors", "e_2_1", "decode", "codewordsInts", "Int32Array", "ignored"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/Decoder.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport ChecksumException from '../../ChecksumException';\nimport BitMatrix from '../../common/BitMatrix';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport BitMatrixParser from './BitMatrixParser';\nimport DataBlock from './DataBlock';\nimport DecodedBitStreamParser from './DecodedBitStreamParser';\nimport QRCodeDecoderMetaData from './QRCodeDecoderMetaData';\n/*import java.util.Map;*/\n/**\n * <p>The main class which implements QR Code decoding -- as opposed to locating and extracting\n * the QR Code from an image.</p>\n *\n * <AUTHOR> Owen\n */\nvar Decoder = /** @class */ (function () {\n    function Decoder() {\n        this.rsDecoder = new ReedSolomonDecoder(GenericGF.QR_CODE_FIELD_256);\n    }\n    // public decode(image: boolean[][]): DecoderResult /*throws ChecksumException, FormatException*/ {\n    //   return decode(image, null)\n    // }\n    /**\n     * <p>Convenience method that can decode a QR Code represented as a 2D array of booleans.\n     * \"true\" is taken to mean a black module.</p>\n     *\n     * @param image booleans representing white/black QR Code modules\n     * @param hints decoding hints that should be used to influence decoding\n     * @return text and bytes encoded within the QR Code\n     * @throws FormatException if the QR Code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.decodeBooleanArray = function (image, hints) {\n        return this.decodeBitMatrix(BitMatrix.parseFromBooleanArray(image), hints);\n    };\n    // public decodeBitMatrix(bits: BitMatrix): DecoderResult /*throws ChecksumException, FormatException*/ {\n    //   return decode(bits, null)\n    // }\n    /**\n     * <p>Decodes a QR Code represented as a {@link BitMatrix}. A 1 or \"true\" is taken to mean a black module.</p>\n     *\n     * @param bits booleans representing white/black QR Code modules\n     * @param hints decoding hints that should be used to influence decoding\n     * @return text and bytes encoded within the QR Code\n     * @throws FormatException if the QR Code cannot be decoded\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.decodeBitMatrix = function (bits, hints) {\n        // Construct a parser and read version, error-correction level\n        var parser = new BitMatrixParser(bits);\n        var ex = null;\n        try {\n            return this.decodeBitMatrixParser(parser, hints);\n        }\n        catch (e /*: FormatException, ChecksumException*/) {\n            ex = e;\n        }\n        try {\n            // Revert the bit matrix\n            parser.remask();\n            // Will be attempting a mirrored reading of the version and format info.\n            parser.setMirror(true);\n            // Preemptively read the version.\n            parser.readVersion();\n            // Preemptively read the format information.\n            parser.readFormatInformation();\n            /*\n             * Since we're here, this means we have successfully detected some kind\n             * of version and format information when mirrored. This is a good sign,\n             * that the QR code may be mirrored, and we should try once more with a\n             * mirrored content.\n             */\n            // Prepare for a mirrored reading.\n            parser.mirror();\n            var result = this.decodeBitMatrixParser(parser, hints);\n            // Success! Notify the caller that the code was mirrored.\n            result.setOther(new QRCodeDecoderMetaData(true));\n            return result;\n        }\n        catch (e /*FormatException | ChecksumException*/) {\n            // Throw the exception from the original reading\n            if (ex !== null) {\n                throw ex;\n            }\n            throw e;\n        }\n    };\n    Decoder.prototype.decodeBitMatrixParser = function (parser, hints) {\n        var e_1, _a, e_2, _b;\n        var version = parser.readVersion();\n        var ecLevel = parser.readFormatInformation().getErrorCorrectionLevel();\n        // Read codewords\n        var codewords = parser.readCodewords();\n        // Separate into data blocks\n        var dataBlocks = DataBlock.getDataBlocks(codewords, version, ecLevel);\n        // Count total number of data bytes\n        var totalBytes = 0;\n        try {\n            for (var dataBlocks_1 = __values(dataBlocks), dataBlocks_1_1 = dataBlocks_1.next(); !dataBlocks_1_1.done; dataBlocks_1_1 = dataBlocks_1.next()) {\n                var dataBlock = dataBlocks_1_1.value;\n                totalBytes += dataBlock.getNumDataCodewords();\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (dataBlocks_1_1 && !dataBlocks_1_1.done && (_a = dataBlocks_1.return)) _a.call(dataBlocks_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var resultBytes = new Uint8Array(totalBytes);\n        var resultOffset = 0;\n        try {\n            // Error-correct and copy data blocks together into a stream of bytes\n            for (var dataBlocks_2 = __values(dataBlocks), dataBlocks_2_1 = dataBlocks_2.next(); !dataBlocks_2_1.done; dataBlocks_2_1 = dataBlocks_2.next()) {\n                var dataBlock = dataBlocks_2_1.value;\n                var codewordBytes = dataBlock.getCodewords();\n                var numDataCodewords = dataBlock.getNumDataCodewords();\n                this.correctErrors(codewordBytes, numDataCodewords);\n                for (var i = 0; i < numDataCodewords; i++) {\n                    resultBytes[resultOffset++] = codewordBytes[i];\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (dataBlocks_2_1 && !dataBlocks_2_1.done && (_b = dataBlocks_2.return)) _b.call(dataBlocks_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        // Decode the contents of that stream of bytes\n        return DecodedBitStreamParser.decode(resultBytes, version, ecLevel, hints);\n    };\n    /**\n     * <p>Given data and error-correction codewords received, possibly corrupted by errors, attempts to\n     * correct the errors in-place using Reed-Solomon error correction.</p>\n     *\n     * @param codewordBytes data and error correction codewords\n     * @param numDataCodewords number of codewords that are data bytes\n     * @throws ChecksumException if error correction fails\n     */\n    Decoder.prototype.correctErrors = function (codewordBytes, numDataCodewords /*int*/) {\n        // const numCodewords = codewordBytes.length;\n        // First read into an array of ints\n        var codewordsInts = new Int32Array(codewordBytes);\n        // TYPESCRIPTPORT: not realy necessary to transform to ints? could redesign everything to work with unsigned bytes?\n        // const codewordsInts = new Int32Array(numCodewords)\n        // for (let i = 0; i < numCodewords; i++) {\n        //   codewordsInts[i] = codewordBytes[i] & 0xFF\n        // }\n        try {\n            this.rsDecoder.decode(codewordsInts, codewordBytes.length - numDataCodewords);\n        }\n        catch (ignored /*: ReedSolomonException*/) {\n            throw new ChecksumException();\n        }\n        // Copy back into array of bytes -- only need to worry about the bytes that were data\n        // We don't care about errors in the error-correction codewords\n        for (var i = 0; i < numDataCodewords; i++) {\n            codewordBytes[i] = /*(byte) */ codewordsInts[i];\n        }\n    };\n    return Decoder;\n}());\nexport default Decoder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG;IACf,IAAI,CAACC,SAAS,GAAG,IAAIN,kBAAkB,CAACD,SAAS,CAACQ,iBAAiB,CAAC;EACxE;EACA;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIF,OAAO,CAACG,SAAS,CAACC,kBAAkB,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAC3D,OAAO,IAAI,CAACC,eAAe,CAACd,SAAS,CAACe,qBAAqB,CAACH,KAAK,CAAC,EAAEC,KAAK,CAAC;EAC9E,CAAC;EACD;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,OAAO,CAACG,SAAS,CAACI,eAAe,GAAG,UAAUE,IAAI,EAAEH,KAAK,EAAE;IACvD;IACA,IAAII,MAAM,GAAG,IAAId,eAAe,CAACa,IAAI,CAAC;IACtC,IAAIE,EAAE,GAAG,IAAI;IACb,IAAI;MACA,OAAO,IAAI,CAACC,qBAAqB,CAACF,MAAM,EAAEJ,KAAK,CAAC;IACpD,CAAC,CACD,OAAOO,CAAC,CAAC,0CAA0C;MAC/CF,EAAE,GAAGE,CAAC;IACV;IACA,IAAI;MACA;MACAH,MAAM,CAACI,MAAM,CAAC,CAAC;MACf;MACAJ,MAAM,CAACK,SAAS,CAAC,IAAI,CAAC;MACtB;MACAL,MAAM,CAACM,WAAW,CAAC,CAAC;MACpB;MACAN,MAAM,CAACO,qBAAqB,CAAC,CAAC;MAC9B;AACZ;AACA;AACA;AACA;AACA;MACY;MACAP,MAAM,CAACQ,MAAM,CAAC,CAAC;MACf,IAAIC,MAAM,GAAG,IAAI,CAACP,qBAAqB,CAACF,MAAM,EAAEJ,KAAK,CAAC;MACtD;MACAa,MAAM,CAACC,QAAQ,CAAC,IAAIrB,qBAAqB,CAAC,IAAI,CAAC,CAAC;MAChD,OAAOoB,MAAM;IACjB,CAAC,CACD,OAAON,CAAC,CAAC,yCAAyC;MAC9C;MACA,IAAIF,EAAE,KAAK,IAAI,EAAE;QACb,MAAMA,EAAE;MACZ;MACA,MAAME,CAAC;IACX;EACJ,CAAC;EACDb,OAAO,CAACG,SAAS,CAACS,qBAAqB,GAAG,UAAUF,MAAM,EAAEJ,KAAK,EAAE;IAC/D,IAAIe,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB,IAAIC,OAAO,GAAGf,MAAM,CAACM,WAAW,CAAC,CAAC;IAClC,IAAIU,OAAO,GAAGhB,MAAM,CAACO,qBAAqB,CAAC,CAAC,CAACU,uBAAuB,CAAC,CAAC;IACtE;IACA,IAAIC,SAAS,GAAGlB,MAAM,CAACmB,aAAa,CAAC,CAAC;IACtC;IACA,IAAIC,UAAU,GAAGjC,SAAS,CAACkC,aAAa,CAACH,SAAS,EAAEH,OAAO,EAAEC,OAAO,CAAC;IACrE;IACA,IAAIM,UAAU,GAAG,CAAC;IAClB,IAAI;MACA,KAAK,IAAIC,YAAY,GAAGtD,QAAQ,CAACmD,UAAU,CAAC,EAAEI,cAAc,GAAGD,YAAY,CAAC7C,IAAI,CAAC,CAAC,EAAE,CAAC8C,cAAc,CAAC5C,IAAI,EAAE4C,cAAc,GAAGD,YAAY,CAAC7C,IAAI,CAAC,CAAC,EAAE;QAC5I,IAAI+C,SAAS,GAAGD,cAAc,CAAC7C,KAAK;QACpC2C,UAAU,IAAIG,SAAS,CAACC,mBAAmB,CAAC,CAAC;MACjD;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEhB,GAAG,GAAG;QAAEiB,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,cAAc,IAAI,CAACA,cAAc,CAAC5C,IAAI,KAAKgC,EAAE,GAAGW,YAAY,CAACM,MAAM,CAAC,EAAEjB,EAAE,CAACpC,IAAI,CAAC+C,YAAY,CAAC;MACnG,CAAC,SACO;QAAE,IAAIZ,GAAG,EAAE,MAAMA,GAAG,CAACiB,KAAK;MAAE;IACxC;IACA,IAAIE,WAAW,GAAG,IAAIC,UAAU,CAACT,UAAU,CAAC;IAC5C,IAAIU,YAAY,GAAG,CAAC;IACpB,IAAI;MACA;MACA,KAAK,IAAIC,YAAY,GAAGhE,QAAQ,CAACmD,UAAU,CAAC,EAAEc,cAAc,GAAGD,YAAY,CAACvD,IAAI,CAAC,CAAC,EAAE,CAACwD,cAAc,CAACtD,IAAI,EAAEsD,cAAc,GAAGD,YAAY,CAACvD,IAAI,CAAC,CAAC,EAAE;QAC5I,IAAI+C,SAAS,GAAGS,cAAc,CAACvD,KAAK;QACpC,IAAIwD,aAAa,GAAGV,SAAS,CAACW,YAAY,CAAC,CAAC;QAC5C,IAAIC,gBAAgB,GAAGZ,SAAS,CAACC,mBAAmB,CAAC,CAAC;QACtD,IAAI,CAACY,aAAa,CAACH,aAAa,EAAEE,gBAAgB,CAAC;QACnD,KAAK,IAAI9D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,gBAAgB,EAAE9D,CAAC,EAAE,EAAE;UACvCuD,WAAW,CAACE,YAAY,EAAE,CAAC,GAAGG,aAAa,CAAC5D,CAAC,CAAC;QAClD;MACJ;IACJ,CAAC,CACD,OAAOgE,KAAK,EAAE;MAAE1B,GAAG,GAAG;QAAEe,KAAK,EAAEW;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIL,cAAc,IAAI,CAACA,cAAc,CAACtD,IAAI,KAAKkC,EAAE,GAAGmB,YAAY,CAACJ,MAAM,CAAC,EAAEf,EAAE,CAACtC,IAAI,CAACyD,YAAY,CAAC;MACnG,CAAC,SACO;QAAE,IAAIpB,GAAG,EAAE,MAAMA,GAAG,CAACe,KAAK;MAAE;IACxC;IACA;IACA,OAAOxC,sBAAsB,CAACoD,MAAM,CAACV,WAAW,EAAEf,OAAO,EAAEC,OAAO,EAAEpB,KAAK,CAAC;EAC9E,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,OAAO,CAACG,SAAS,CAAC6C,aAAa,GAAG,UAAUH,aAAa,EAAEE,gBAAgB,CAAC,SAAS;IACjF;IACA;IACA,IAAII,aAAa,GAAG,IAAIC,UAAU,CAACP,aAAa,CAAC;IACjD;IACA;IACA;IACA;IACA;IACA,IAAI;MACA,IAAI,CAAC5C,SAAS,CAACiD,MAAM,CAACC,aAAa,EAAEN,aAAa,CAAC1D,MAAM,GAAG4D,gBAAgB,CAAC;IACjF,CAAC,CACD,OAAOM,OAAO,CAAC,4BAA4B;MACvC,MAAM,IAAI7D,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA;IACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,gBAAgB,EAAE9D,CAAC,EAAE,EAAE;MACvC4D,aAAa,CAAC5D,CAAC,CAAC,GAAG,WAAYkE,aAAa,CAAClE,CAAC,CAAC;IACnD;EACJ,CAAC;EACD,OAAOe,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}