{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport OneDReader from './OneDReader';\nimport NotFoundException from '../NotFoundException';\nimport FormatException from '../FormatException';\n/**\n * <p>Encapsulates functionality and implementation that is common to UPC and EAN families\n * of one-dimensional barcodes.</p>\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Alasdair Mackintosh)\n */\nvar AbstractUPCEANReader = /** @class */function (_super) {\n  __extends(AbstractUPCEANReader, _super);\n  function AbstractUPCEANReader() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.decodeRowStringBuffer = '';\n    return _this;\n  }\n  // private final UPCEANExtensionSupport extensionReader;\n  // private final EANManufacturerOrgSupport eanManSupport;\n  /*\n  protected UPCEANReader() {\n      decodeRowStringBuffer = new StringBuilder(20);\n      extensionReader = new UPCEANExtensionSupport();\n      eanManSupport = new EANManufacturerOrgSupport();\n  }\n  */\n  AbstractUPCEANReader.findStartGuardPattern = function (row) {\n    var foundStart = false;\n    var startRange;\n    var nextStart = 0;\n    var counters = Int32Array.from([0, 0, 0]);\n    while (!foundStart) {\n      counters = Int32Array.from([0, 0, 0]);\n      startRange = AbstractUPCEANReader.findGuardPattern(row, nextStart, false, this.START_END_PATTERN, counters);\n      var start = startRange[0];\n      nextStart = startRange[1];\n      var quietStart = start - (nextStart - start);\n      if (quietStart >= 0) {\n        foundStart = row.isRange(quietStart, start, false);\n      }\n    }\n    return startRange;\n  };\n  AbstractUPCEANReader.checkChecksum = function (s) {\n    return AbstractUPCEANReader.checkStandardUPCEANChecksum(s);\n  };\n  AbstractUPCEANReader.checkStandardUPCEANChecksum = function (s) {\n    var length = s.length;\n    if (length === 0) return false;\n    var check = parseInt(s.charAt(length - 1), 10);\n    return AbstractUPCEANReader.getStandardUPCEANChecksum(s.substring(0, length - 1)) === check;\n  };\n  AbstractUPCEANReader.getStandardUPCEANChecksum = function (s) {\n    var length = s.length;\n    var sum = 0;\n    for (var i = length - 1; i >= 0; i -= 2) {\n      var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n      if (digit < 0 || digit > 9) {\n        throw new FormatException();\n      }\n      sum += digit;\n    }\n    sum *= 3;\n    for (var i = length - 2; i >= 0; i -= 2) {\n      var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n      if (digit < 0 || digit > 9) {\n        throw new FormatException();\n      }\n      sum += digit;\n    }\n    return (1000 - sum) % 10;\n  };\n  AbstractUPCEANReader.decodeEnd = function (row, endStart) {\n    return AbstractUPCEANReader.findGuardPattern(row, endStart, false, AbstractUPCEANReader.START_END_PATTERN, new Int32Array(AbstractUPCEANReader.START_END_PATTERN.length).fill(0));\n  };\n  /**\n   * @throws NotFoundException\n   */\n  AbstractUPCEANReader.findGuardPatternWithoutCounters = function (row, rowOffset, whiteFirst, pattern) {\n    return this.findGuardPattern(row, rowOffset, whiteFirst, pattern, new Int32Array(pattern.length));\n  };\n  /**\n   * @param row row of black/white values to search\n   * @param rowOffset position to start search\n   * @param whiteFirst if true, indicates that the pattern specifies white/black/white/...\n   * pixel counts, otherwise, it is interpreted as black/white/black/...\n   * @param pattern pattern of counts of number of black and white pixels that are being\n   * searched for as a pattern\n   * @param counters array of counters, as long as pattern, to re-use\n   * @return start/end horizontal offset of guard pattern, as an array of two ints\n   * @throws NotFoundException if pattern is not found\n   */\n  AbstractUPCEANReader.findGuardPattern = function (row, rowOffset, whiteFirst, pattern, counters) {\n    var width = row.getSize();\n    rowOffset = whiteFirst ? row.getNextUnset(rowOffset) : row.getNextSet(rowOffset);\n    var counterPosition = 0;\n    var patternStart = rowOffset;\n    var patternLength = pattern.length;\n    var isWhite = whiteFirst;\n    for (var x = rowOffset; x < width; x++) {\n      if (row.get(x) !== isWhite) {\n        counters[counterPosition]++;\n      } else {\n        if (counterPosition === patternLength - 1) {\n          if (OneDReader.patternMatchVariance(counters, pattern, AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE) < AbstractUPCEANReader.MAX_AVG_VARIANCE) {\n            return Int32Array.from([patternStart, x]);\n          }\n          patternStart += counters[0] + counters[1];\n          var slice = counters.slice(2, counters.length);\n          for (var i = 0; i < counterPosition - 1; i++) {\n            counters[i] = slice[i];\n          }\n          counters[counterPosition - 1] = 0;\n          counters[counterPosition] = 0;\n          counterPosition--;\n        } else {\n          counterPosition++;\n        }\n        counters[counterPosition] = 1;\n        isWhite = !isWhite;\n      }\n    }\n    throw new NotFoundException();\n  };\n  AbstractUPCEANReader.decodeDigit = function (row, counters, rowOffset, patterns) {\n    this.recordPattern(row, rowOffset, counters);\n    var bestVariance = this.MAX_AVG_VARIANCE;\n    var bestMatch = -1;\n    var max = patterns.length;\n    for (var i = 0; i < max; i++) {\n      var pattern = patterns[i];\n      var variance = OneDReader.patternMatchVariance(counters, pattern, AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE);\n      if (variance < bestVariance) {\n        bestVariance = variance;\n        bestMatch = i;\n      }\n    }\n    if (bestMatch >= 0) {\n      return bestMatch;\n    } else {\n      throw new NotFoundException();\n    }\n  };\n  // These two values are critical for determining how permissive the decoding will be.\n  // We've arrived at these values through a lot of trial and error. Setting them any higher\n  // lets false positives creep in quickly.\n  AbstractUPCEANReader.MAX_AVG_VARIANCE = 0.48;\n  AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE = 0.7;\n  /**\n   * Start/end guard pattern.\n   */\n  AbstractUPCEANReader.START_END_PATTERN = Int32Array.from([1, 1, 1]);\n  /**\n   * Pattern marking the middle of a UPC/EAN pattern, separating the two halves.\n   */\n  AbstractUPCEANReader.MIDDLE_PATTERN = Int32Array.from([1, 1, 1, 1, 1]);\n  /**\n   * end guard pattern.\n   */\n  AbstractUPCEANReader.END_PATTERN = Int32Array.from([1, 1, 1, 1, 1, 1]);\n  /**\n   * \"Odd\", or \"L\" patterns used to encode UPC/EAN digits.\n   */\n  AbstractUPCEANReader.L_PATTERNS = [Int32Array.from([3, 2, 1, 1]), Int32Array.from([2, 2, 2, 1]), Int32Array.from([2, 1, 2, 2]), Int32Array.from([1, 4, 1, 1]), Int32Array.from([1, 1, 3, 2]), Int32Array.from([1, 2, 3, 1]), Int32Array.from([1, 1, 1, 4]), Int32Array.from([1, 3, 1, 2]), Int32Array.from([1, 2, 1, 3]), Int32Array.from([3, 1, 1, 2])];\n  return AbstractUPCEANReader;\n}(OneDReader);\nexport default AbstractUPCEANReader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "OneDReader", "NotFoundException", "FormatException", "AbstractUPCEANReader", "_super", "_this", "apply", "arguments", "decodeRowStringBuffer", "findStartGuardPattern", "row", "foundStart", "startRange", "nextStart", "counters", "Int32Array", "from", "<PERSON><PERSON><PERSON><PERSON>att<PERSON>", "START_END_PATTERN", "start", "quietStart", "isRange", "checkChecksum", "s", "checkStandardUPCEANChecksum", "length", "check", "parseInt", "char<PERSON>t", "getStandardUPCEANChecksum", "substring", "sum", "i", "digit", "charCodeAt", "decodeEnd", "endStart", "fill", "findGuardPatternWithoutCounters", "rowOffset", "white<PERSON><PERSON><PERSON>", "pattern", "width", "getSize", "getNextUnset", "getNextSet", "counterPosition", "patternStart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "x", "get", "patternMatchVariance", "MAX_INDIVIDUAL_VARIANCE", "MAX_AVG_VARIANCE", "slice", "decodeDigit", "patterns", "recordPattern", "bestVariance", "bestMatch", "max", "variance", "MIDDLE_PATTERN", "END_PATTERN", "L_PATTERNS"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/AbstractUPCEANReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport OneDReader from './OneDReader';\nimport NotFoundException from '../NotFoundException';\nimport FormatException from '../FormatException';\n/**\n * <p>Encapsulates functionality and implementation that is common to UPC and EAN families\n * of one-dimensional barcodes.</p>\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Alasdair Mackintosh)\n */\nvar AbstractUPCEANReader = /** @class */ (function (_super) {\n    __extends(AbstractUPCEANReader, _super);\n    function AbstractUPCEANReader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.decodeRowStringBuffer = '';\n        return _this;\n    }\n    // private final UPCEANExtensionSupport extensionReader;\n    // private final EANManufacturerOrgSupport eanManSupport;\n    /*\n    protected UPCEANReader() {\n        decodeRowStringBuffer = new StringBuilder(20);\n        extensionReader = new UPCEANExtensionSupport();\n        eanManSupport = new EANManufacturerOrgSupport();\n    }\n    */\n    AbstractUPCEANReader.findStartGuardPattern = function (row) {\n        var foundStart = false;\n        var startRange;\n        var nextStart = 0;\n        var counters = Int32Array.from([0, 0, 0]);\n        while (!foundStart) {\n            counters = Int32Array.from([0, 0, 0]);\n            startRange = AbstractUPCEANReader.findGuardPattern(row, nextStart, false, this.START_END_PATTERN, counters);\n            var start = startRange[0];\n            nextStart = startRange[1];\n            var quietStart = start - (nextStart - start);\n            if (quietStart >= 0) {\n                foundStart = row.isRange(quietStart, start, false);\n            }\n        }\n        return startRange;\n    };\n    AbstractUPCEANReader.checkChecksum = function (s) {\n        return AbstractUPCEANReader.checkStandardUPCEANChecksum(s);\n    };\n    AbstractUPCEANReader.checkStandardUPCEANChecksum = function (s) {\n        var length = s.length;\n        if (length === 0)\n            return false;\n        var check = parseInt(s.charAt(length - 1), 10);\n        return AbstractUPCEANReader.getStandardUPCEANChecksum(s.substring(0, length - 1)) === check;\n    };\n    AbstractUPCEANReader.getStandardUPCEANChecksum = function (s) {\n        var length = s.length;\n        var sum = 0;\n        for (var i = length - 1; i >= 0; i -= 2) {\n            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            if (digit < 0 || digit > 9) {\n                throw new FormatException();\n            }\n            sum += digit;\n        }\n        sum *= 3;\n        for (var i = length - 2; i >= 0; i -= 2) {\n            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            if (digit < 0 || digit > 9) {\n                throw new FormatException();\n            }\n            sum += digit;\n        }\n        return (1000 - sum) % 10;\n    };\n    AbstractUPCEANReader.decodeEnd = function (row, endStart) {\n        return AbstractUPCEANReader.findGuardPattern(row, endStart, false, AbstractUPCEANReader.START_END_PATTERN, new Int32Array(AbstractUPCEANReader.START_END_PATTERN.length).fill(0));\n    };\n    /**\n     * @throws NotFoundException\n     */\n    AbstractUPCEANReader.findGuardPatternWithoutCounters = function (row, rowOffset, whiteFirst, pattern) {\n        return this.findGuardPattern(row, rowOffset, whiteFirst, pattern, new Int32Array(pattern.length));\n    };\n    /**\n     * @param row row of black/white values to search\n     * @param rowOffset position to start search\n     * @param whiteFirst if true, indicates that the pattern specifies white/black/white/...\n     * pixel counts, otherwise, it is interpreted as black/white/black/...\n     * @param pattern pattern of counts of number of black and white pixels that are being\n     * searched for as a pattern\n     * @param counters array of counters, as long as pattern, to re-use\n     * @return start/end horizontal offset of guard pattern, as an array of two ints\n     * @throws NotFoundException if pattern is not found\n     */\n    AbstractUPCEANReader.findGuardPattern = function (row, rowOffset, whiteFirst, pattern, counters) {\n        var width = row.getSize();\n        rowOffset = whiteFirst ? row.getNextUnset(rowOffset) : row.getNextSet(rowOffset);\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        var patternLength = pattern.length;\n        var isWhite = whiteFirst;\n        for (var x = rowOffset; x < width; x++) {\n            if (row.get(x) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    if (OneDReader.patternMatchVariance(counters, pattern, AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE) < AbstractUPCEANReader.MAX_AVG_VARIANCE) {\n                        return Int32Array.from([patternStart, x]);\n                    }\n                    patternStart += counters[0] + counters[1];\n                    var slice = counters.slice(2, counters.length);\n                    for (var i = 0; i < counterPosition - 1; i++) {\n                        counters[i] = slice[i];\n                    }\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    AbstractUPCEANReader.decodeDigit = function (row, counters, rowOffset, patterns) {\n        this.recordPattern(row, rowOffset, counters);\n        var bestVariance = this.MAX_AVG_VARIANCE;\n        var bestMatch = -1;\n        var max = patterns.length;\n        for (var i = 0; i < max; i++) {\n            var pattern = patterns[i];\n            var variance = OneDReader.patternMatchVariance(counters, pattern, AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE);\n            if (variance < bestVariance) {\n                bestVariance = variance;\n                bestMatch = i;\n            }\n        }\n        if (bestMatch >= 0) {\n            return bestMatch;\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    // These two values are critical for determining how permissive the decoding will be.\n    // We've arrived at these values through a lot of trial and error. Setting them any higher\n    // lets false positives creep in quickly.\n    AbstractUPCEANReader.MAX_AVG_VARIANCE = 0.48;\n    AbstractUPCEANReader.MAX_INDIVIDUAL_VARIANCE = 0.7;\n    /**\n     * Start/end guard pattern.\n     */\n    AbstractUPCEANReader.START_END_PATTERN = Int32Array.from([1, 1, 1]);\n    /**\n     * Pattern marking the middle of a UPC/EAN pattern, separating the two halves.\n     */\n    AbstractUPCEANReader.MIDDLE_PATTERN = Int32Array.from([1, 1, 1, 1, 1]);\n    /**\n     * end guard pattern.\n     */\n    AbstractUPCEANReader.END_PATTERN = Int32Array.from([1, 1, 1, 1, 1, 1]);\n    /**\n     * \"Odd\", or \"L\" patterns used to encode UPC/EAN digits.\n     */\n    AbstractUPCEANReader.L_PATTERNS = [\n        Int32Array.from([3, 2, 1, 1]),\n        Int32Array.from([2, 2, 2, 1]),\n        Int32Array.from([2, 1, 2, 2]),\n        Int32Array.from([1, 4, 1, 1]),\n        Int32Array.from([1, 1, 3, 2]),\n        Int32Array.from([1, 2, 3, 1]),\n        Int32Array.from([1, 1, 1, 4]),\n        Int32Array.from([1, 3, 1, 2]),\n        Int32Array.from([1, 2, 1, 3]),\n        Int32Array.from([3, 1, 1, 2]),\n    ];\n    return AbstractUPCEANReader;\n}(OneDReader));\nexport default AbstractUPCEANReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,UAAU,MAAM,cAAc;AACrC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,eAAe,MAAM,oBAAoB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACxDlB,SAAS,CAACiB,oBAAoB,EAAEC,MAAM,CAAC;EACvC,SAASD,oBAAoBA,CAAA,EAAG;IAC5B,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,qBAAqB,GAAG,EAAE;IAChC,OAAOH,KAAK;EAChB;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIF,oBAAoB,CAACM,qBAAqB,GAAG,UAAUC,GAAG,EAAE;IACxD,IAAIC,UAAU,GAAG,KAAK;IACtB,IAAIC,UAAU;IACd,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,QAAQ,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzC,OAAO,CAACL,UAAU,EAAE;MAChBG,QAAQ,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACrCJ,UAAU,GAAGT,oBAAoB,CAACc,gBAAgB,CAACP,GAAG,EAAEG,SAAS,EAAE,KAAK,EAAE,IAAI,CAACK,iBAAiB,EAAEJ,QAAQ,CAAC;MAC3G,IAAIK,KAAK,GAAGP,UAAU,CAAC,CAAC,CAAC;MACzBC,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;MACzB,IAAIQ,UAAU,GAAGD,KAAK,IAAIN,SAAS,GAAGM,KAAK,CAAC;MAC5C,IAAIC,UAAU,IAAI,CAAC,EAAE;QACjBT,UAAU,GAAGD,GAAG,CAACW,OAAO,CAACD,UAAU,EAAED,KAAK,EAAE,KAAK,CAAC;MACtD;IACJ;IACA,OAAOP,UAAU;EACrB,CAAC;EACDT,oBAAoB,CAACmB,aAAa,GAAG,UAAUC,CAAC,EAAE;IAC9C,OAAOpB,oBAAoB,CAACqB,2BAA2B,CAACD,CAAC,CAAC;EAC9D,CAAC;EACDpB,oBAAoB,CAACqB,2BAA2B,GAAG,UAAUD,CAAC,EAAE;IAC5D,IAAIE,MAAM,GAAGF,CAAC,CAACE,MAAM;IACrB,IAAIA,MAAM,KAAK,CAAC,EACZ,OAAO,KAAK;IAChB,IAAIC,KAAK,GAAGC,QAAQ,CAACJ,CAAC,CAACK,MAAM,CAACH,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9C,OAAOtB,oBAAoB,CAAC0B,yBAAyB,CAACN,CAAC,CAACO,SAAS,CAAC,CAAC,EAAEL,MAAM,GAAG,CAAC,CAAC,CAAC,KAAKC,KAAK;EAC/F,CAAC;EACDvB,oBAAoB,CAAC0B,yBAAyB,GAAG,UAAUN,CAAC,EAAE;IAC1D,IAAIE,MAAM,GAAGF,CAAC,CAACE,MAAM;IACrB,IAAIM,GAAG,GAAG,CAAC;IACX,KAAK,IAAIC,CAAC,GAAGP,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACrC,IAAIC,KAAK,GAAGV,CAAC,CAACK,MAAM,CAACI,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;MACzD,IAAID,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI/B,eAAe,CAAC,CAAC;MAC/B;MACA6B,GAAG,IAAIE,KAAK;IAChB;IACAF,GAAG,IAAI,CAAC;IACR,KAAK,IAAIC,CAAC,GAAGP,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACrC,IAAIC,KAAK,GAAGV,CAAC,CAACK,MAAM,CAACI,CAAC,CAAC,CAACE,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;MACzD,IAAID,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI/B,eAAe,CAAC,CAAC;MAC/B;MACA6B,GAAG,IAAIE,KAAK;IAChB;IACA,OAAO,CAAC,IAAI,GAAGF,GAAG,IAAI,EAAE;EAC5B,CAAC;EACD5B,oBAAoB,CAACgC,SAAS,GAAG,UAAUzB,GAAG,EAAE0B,QAAQ,EAAE;IACtD,OAAOjC,oBAAoB,CAACc,gBAAgB,CAACP,GAAG,EAAE0B,QAAQ,EAAE,KAAK,EAAEjC,oBAAoB,CAACe,iBAAiB,EAAE,IAAIH,UAAU,CAACZ,oBAAoB,CAACe,iBAAiB,CAACO,MAAM,CAAC,CAACY,IAAI,CAAC,CAAC,CAAC,CAAC;EACrL,CAAC;EACD;AACJ;AACA;EACIlC,oBAAoB,CAACmC,+BAA+B,GAAG,UAAU5B,GAAG,EAAE6B,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAE;IAClG,OAAO,IAAI,CAACxB,gBAAgB,CAACP,GAAG,EAAE6B,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAE,IAAI1B,UAAU,CAAC0B,OAAO,CAAChB,MAAM,CAAC,CAAC;EACrG,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItB,oBAAoB,CAACc,gBAAgB,GAAG,UAAUP,GAAG,EAAE6B,SAAS,EAAEC,UAAU,EAAEC,OAAO,EAAE3B,QAAQ,EAAE;IAC7F,IAAI4B,KAAK,GAAGhC,GAAG,CAACiC,OAAO,CAAC,CAAC;IACzBJ,SAAS,GAAGC,UAAU,GAAG9B,GAAG,CAACkC,YAAY,CAACL,SAAS,CAAC,GAAG7B,GAAG,CAACmC,UAAU,CAACN,SAAS,CAAC;IAChF,IAAIO,eAAe,GAAG,CAAC;IACvB,IAAIC,YAAY,GAAGR,SAAS;IAC5B,IAAIS,aAAa,GAAGP,OAAO,CAAChB,MAAM;IAClC,IAAIwB,OAAO,GAAGT,UAAU;IACxB,KAAK,IAAIU,CAAC,GAAGX,SAAS,EAAEW,CAAC,GAAGR,KAAK,EAAEQ,CAAC,EAAE,EAAE;MACpC,IAAIxC,GAAG,CAACyC,GAAG,CAACD,CAAC,CAAC,KAAKD,OAAO,EAAE;QACxBnC,QAAQ,CAACgC,eAAe,CAAC,EAAE;MAC/B,CAAC,MACI;QACD,IAAIA,eAAe,KAAKE,aAAa,GAAG,CAAC,EAAE;UACvC,IAAIhD,UAAU,CAACoD,oBAAoB,CAACtC,QAAQ,EAAE2B,OAAO,EAAEtC,oBAAoB,CAACkD,uBAAuB,CAAC,GAAGlD,oBAAoB,CAACmD,gBAAgB,EAAE;YAC1I,OAAOvC,UAAU,CAACC,IAAI,CAAC,CAAC+B,YAAY,EAAEG,CAAC,CAAC,CAAC;UAC7C;UACAH,YAAY,IAAIjC,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzC,IAAIyC,KAAK,GAAGzC,QAAQ,CAACyC,KAAK,CAAC,CAAC,EAAEzC,QAAQ,CAACW,MAAM,CAAC;UAC9C,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,eAAe,GAAG,CAAC,EAAEd,CAAC,EAAE,EAAE;YAC1ClB,QAAQ,CAACkB,CAAC,CAAC,GAAGuB,KAAK,CAACvB,CAAC,CAAC;UAC1B;UACAlB,QAAQ,CAACgC,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;UACjChC,QAAQ,CAACgC,eAAe,CAAC,GAAG,CAAC;UAC7BA,eAAe,EAAE;QACrB,CAAC,MACI;UACDA,eAAe,EAAE;QACrB;QACAhC,QAAQ,CAACgC,eAAe,CAAC,GAAG,CAAC;QAC7BG,OAAO,GAAG,CAACA,OAAO;MACtB;IACJ;IACA,MAAM,IAAIhD,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACDE,oBAAoB,CAACqD,WAAW,GAAG,UAAU9C,GAAG,EAAEI,QAAQ,EAAEyB,SAAS,EAAEkB,QAAQ,EAAE;IAC7E,IAAI,CAACC,aAAa,CAAChD,GAAG,EAAE6B,SAAS,EAAEzB,QAAQ,CAAC;IAC5C,IAAI6C,YAAY,GAAG,IAAI,CAACL,gBAAgB;IACxC,IAAIM,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIC,GAAG,GAAGJ,QAAQ,CAAChC,MAAM;IACzB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,GAAG,EAAE7B,CAAC,EAAE,EAAE;MAC1B,IAAIS,OAAO,GAAGgB,QAAQ,CAACzB,CAAC,CAAC;MACzB,IAAI8B,QAAQ,GAAG9D,UAAU,CAACoD,oBAAoB,CAACtC,QAAQ,EAAE2B,OAAO,EAAEtC,oBAAoB,CAACkD,uBAAuB,CAAC;MAC/G,IAAIS,QAAQ,GAAGH,YAAY,EAAE;QACzBA,YAAY,GAAGG,QAAQ;QACvBF,SAAS,GAAG5B,CAAC;MACjB;IACJ;IACA,IAAI4B,SAAS,IAAI,CAAC,EAAE;MAChB,OAAOA,SAAS;IACpB,CAAC,MACI;MACD,MAAM,IAAI3D,iBAAiB,CAAC,CAAC;IACjC;EACJ,CAAC;EACD;EACA;EACA;EACAE,oBAAoB,CAACmD,gBAAgB,GAAG,IAAI;EAC5CnD,oBAAoB,CAACkD,uBAAuB,GAAG,GAAG;EAClD;AACJ;AACA;EACIlD,oBAAoB,CAACe,iBAAiB,GAAGH,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE;AACJ;AACA;EACIb,oBAAoB,CAAC4D,cAAc,GAAGhD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACtE;AACJ;AACA;EACIb,oBAAoB,CAAC6D,WAAW,GAAGjD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACtE;AACJ;AACA;EACIb,oBAAoB,CAAC8D,UAAU,GAAG,CAC9BlD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAC7BD,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAChC;EACD,OAAOb,oBAAoB;AAC/B,CAAC,CAACH,UAAU,CAAE;AACd,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}