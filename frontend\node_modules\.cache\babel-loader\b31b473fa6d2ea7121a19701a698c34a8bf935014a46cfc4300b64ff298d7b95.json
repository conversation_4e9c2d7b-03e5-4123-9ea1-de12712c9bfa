{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport GenericGFPoly from './GenericGFPoly';\nimport System from '../../util/System';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Implements Reed-Solomon encoding, as the name implies.</p>\n *\n * <AUTHOR>\n * <AUTHOR>\n */\nvar ReedSolomonEncoder = /** @class */function () {\n  /**\n   * A reed solomon error-correcting encoding constructor is created by\n   * passing as Galois Field with of size equal to the number of code\n   * words (symbols) in the alphabet (the number of values in each\n   * element of arrays that are encoded/decoded).\n   * @param field A galois field with a number of elements equal to the size\n   * of the alphabet of symbols to encode.\n   */\n  function ReedSolomonEncoder(field) {\n    this.field = field;\n    this.cachedGenerators = [];\n    this.cachedGenerators.push(new GenericGFPoly(field, Int32Array.from([1])));\n  }\n  ReedSolomonEncoder.prototype.buildGenerator = function (degree /*int*/) {\n    var cachedGenerators = this.cachedGenerators;\n    if (degree >= cachedGenerators.length) {\n      var lastGenerator = cachedGenerators[cachedGenerators.length - 1];\n      var field = this.field;\n      for (var d = cachedGenerators.length; d <= degree; d++) {\n        var nextGenerator = lastGenerator.multiply(new GenericGFPoly(field, Int32Array.from([1, field.exp(d - 1 + field.getGeneratorBase())])));\n        cachedGenerators.push(nextGenerator);\n        lastGenerator = nextGenerator;\n      }\n    }\n    return cachedGenerators[degree];\n  };\n  /**\n   * <p>Encode a sequence of code words (symbols) using Reed-Solomon to allow decoders\n   * to detect and correct errors that may have been introduced when the resulting\n   * data is stored or transmitted.</p>\n   *\n   * @param toEncode array used for both and output. Caller initializes the array with\n   * the code words (symbols) to be encoded followed by empty elements allocated to make\n   * space for error-correction code words in the encoded output. The array contains\n   * the encdoded output when encode returns. Code words are encoded as numbers from\n   * 0 to n-1, where n is the number of possible code words (symbols), as determined\n   * by the size of the Galois Field passed in the constructor of this object.\n   * @param ecBytes the number of elements reserved in the array (first parameter)\n   * to store error-correction code words. Thus, the number of code words (symbols)\n   * to encode in the first parameter is thus toEncode.length - ecBytes.\n   * Note, the use of \"bytes\" in the name of this parameter is misleading, as there may\n   * be more or fewer than 256 symbols being encoded, as determined by the number of\n   * elements in the Galois Field passed as a constructor to this object.\n   * @throws IllegalArgumentException thrown in response to validation errros.\n   */\n  ReedSolomonEncoder.prototype.encode = function (toEncode, ecBytes /*int*/) {\n    if (ecBytes === 0) {\n      throw new IllegalArgumentException('No error correction bytes');\n    }\n    var dataBytes = toEncode.length - ecBytes;\n    if (dataBytes <= 0) {\n      throw new IllegalArgumentException('No data bytes provided');\n    }\n    var generator = this.buildGenerator(ecBytes);\n    var infoCoefficients = new Int32Array(dataBytes);\n    System.arraycopy(toEncode, 0, infoCoefficients, 0, dataBytes);\n    var info = new GenericGFPoly(this.field, infoCoefficients);\n    info = info.multiplyByMonomial(ecBytes, 1);\n    var remainder = info.divide(generator)[1];\n    var coefficients = remainder.getCoefficients();\n    var numZeroCoefficients = ecBytes - coefficients.length;\n    for (var i = 0; i < numZeroCoefficients; i++) {\n      toEncode[dataBytes + i] = 0;\n    }\n    System.arraycopy(coefficients, 0, toEncode, dataBytes + numZeroCoefficients, coefficients.length);\n  };\n  return ReedSolomonEncoder;\n}();\nexport default ReedSolomonEncoder;", "map": {"version": 3, "names": ["GenericGFPoly", "System", "IllegalArgumentException", "ReedSolomonEncoder", "field", "cachedGenerators", "push", "Int32Array", "from", "prototype", "buildGenerator", "degree", "length", "lastGenerator", "d", "nextGenerator", "multiply", "exp", "getGeneratorBase", "encode", "toEncode", "ecBytes", "dataBytes", "generator", "infoCoefficients", "arraycopy", "info", "multiplyByMonomial", "remainder", "divide", "coefficients", "getCoefficients", "numZeroCoefficients", "i"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonEncoder.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport GenericGFPoly from './GenericGFPoly';\nimport System from '../../util/System';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Implements Reed-Solomon encoding, as the name implies.</p>\n *\n * <AUTHOR>\n * <AUTHOR>\n */\nvar ReedSolomonEncoder = /** @class */ (function () {\n    /**\n     * A reed solomon error-correcting encoding constructor is created by\n     * passing as Galois Field with of size equal to the number of code\n     * words (symbols) in the alphabet (the number of values in each\n     * element of arrays that are encoded/decoded).\n     * @param field A galois field with a number of elements equal to the size\n     * of the alphabet of symbols to encode.\n     */\n    function ReedSolomonEncoder(field) {\n        this.field = field;\n        this.cachedGenerators = [];\n        this.cachedGenerators.push(new GenericGFPoly(field, Int32Array.from([1])));\n    }\n    ReedSolomonEncoder.prototype.buildGenerator = function (degree /*int*/) {\n        var cachedGenerators = this.cachedGenerators;\n        if (degree >= cachedGenerators.length) {\n            var lastGenerator = cachedGenerators[cachedGenerators.length - 1];\n            var field = this.field;\n            for (var d = cachedGenerators.length; d <= degree; d++) {\n                var nextGenerator = lastGenerator.multiply(new GenericGFPoly(field, Int32Array.from([1, field.exp(d - 1 + field.getGeneratorBase())])));\n                cachedGenerators.push(nextGenerator);\n                lastGenerator = nextGenerator;\n            }\n        }\n        return cachedGenerators[degree];\n    };\n    /**\n     * <p>Encode a sequence of code words (symbols) using Reed-Solomon to allow decoders\n     * to detect and correct errors that may have been introduced when the resulting\n     * data is stored or transmitted.</p>\n     *\n     * @param toEncode array used for both and output. Caller initializes the array with\n     * the code words (symbols) to be encoded followed by empty elements allocated to make\n     * space for error-correction code words in the encoded output. The array contains\n     * the encdoded output when encode returns. Code words are encoded as numbers from\n     * 0 to n-1, where n is the number of possible code words (symbols), as determined\n     * by the size of the Galois Field passed in the constructor of this object.\n     * @param ecBytes the number of elements reserved in the array (first parameter)\n     * to store error-correction code words. Thus, the number of code words (symbols)\n     * to encode in the first parameter is thus toEncode.length - ecBytes.\n     * Note, the use of \"bytes\" in the name of this parameter is misleading, as there may\n     * be more or fewer than 256 symbols being encoded, as determined by the number of\n     * elements in the Galois Field passed as a constructor to this object.\n     * @throws IllegalArgumentException thrown in response to validation errros.\n     */\n    ReedSolomonEncoder.prototype.encode = function (toEncode, ecBytes /*int*/) {\n        if (ecBytes === 0) {\n            throw new IllegalArgumentException('No error correction bytes');\n        }\n        var dataBytes = toEncode.length - ecBytes;\n        if (dataBytes <= 0) {\n            throw new IllegalArgumentException('No data bytes provided');\n        }\n        var generator = this.buildGenerator(ecBytes);\n        var infoCoefficients = new Int32Array(dataBytes);\n        System.arraycopy(toEncode, 0, infoCoefficients, 0, dataBytes);\n        var info = new GenericGFPoly(this.field, infoCoefficients);\n        info = info.multiplyByMonomial(ecBytes, 1);\n        var remainder = info.divide(generator)[1];\n        var coefficients = remainder.getCoefficients();\n        var numZeroCoefficients = ecBytes - coefficients.length;\n        for (var i = 0; i < numZeroCoefficients; i++) {\n            toEncode[dataBytes + i] = 0;\n        }\n        System.arraycopy(coefficients, 0, toEncode, dataBytes + numZeroCoefficients, coefficients.length);\n    };\n    return ReedSolomonEncoder;\n}());\nexport default ReedSolomonEncoder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,kBAAkB,GAAG,aAAe,YAAY;EAChD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,kBAAkBA,CAACC,KAAK,EAAE;IAC/B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACA,gBAAgB,CAACC,IAAI,CAAC,IAAIN,aAAa,CAACI,KAAK,EAAEG,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9E;EACAL,kBAAkB,CAACM,SAAS,CAACC,cAAc,GAAG,UAAUC,MAAM,CAAC,SAAS;IACpE,IAAIN,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC5C,IAAIM,MAAM,IAAIN,gBAAgB,CAACO,MAAM,EAAE;MACnC,IAAIC,aAAa,GAAGR,gBAAgB,CAACA,gBAAgB,CAACO,MAAM,GAAG,CAAC,CAAC;MACjE,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,KAAK,IAAIU,CAAC,GAAGT,gBAAgB,CAACO,MAAM,EAAEE,CAAC,IAAIH,MAAM,EAAEG,CAAC,EAAE,EAAE;QACpD,IAAIC,aAAa,GAAGF,aAAa,CAACG,QAAQ,CAAC,IAAIhB,aAAa,CAACI,KAAK,EAAEG,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEJ,KAAK,CAACa,GAAG,CAACH,CAAC,GAAG,CAAC,GAAGV,KAAK,CAACc,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvIb,gBAAgB,CAACC,IAAI,CAACS,aAAa,CAAC;QACpCF,aAAa,GAAGE,aAAa;MACjC;IACJ;IACA,OAAOV,gBAAgB,CAACM,MAAM,CAAC;EACnC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,kBAAkB,CAACM,SAAS,CAACU,MAAM,GAAG,UAAUC,QAAQ,EAAEC,OAAO,CAAC,SAAS;IACvE,IAAIA,OAAO,KAAK,CAAC,EAAE;MACf,MAAM,IAAInB,wBAAwB,CAAC,2BAA2B,CAAC;IACnE;IACA,IAAIoB,SAAS,GAAGF,QAAQ,CAACR,MAAM,GAAGS,OAAO;IACzC,IAAIC,SAAS,IAAI,CAAC,EAAE;MAChB,MAAM,IAAIpB,wBAAwB,CAAC,wBAAwB,CAAC;IAChE;IACA,IAAIqB,SAAS,GAAG,IAAI,CAACb,cAAc,CAACW,OAAO,CAAC;IAC5C,IAAIG,gBAAgB,GAAG,IAAIjB,UAAU,CAACe,SAAS,CAAC;IAChDrB,MAAM,CAACwB,SAAS,CAACL,QAAQ,EAAE,CAAC,EAAEI,gBAAgB,EAAE,CAAC,EAAEF,SAAS,CAAC;IAC7D,IAAII,IAAI,GAAG,IAAI1B,aAAa,CAAC,IAAI,CAACI,KAAK,EAAEoB,gBAAgB,CAAC;IAC1DE,IAAI,GAAGA,IAAI,CAACC,kBAAkB,CAACN,OAAO,EAAE,CAAC,CAAC;IAC1C,IAAIO,SAAS,GAAGF,IAAI,CAACG,MAAM,CAACN,SAAS,CAAC,CAAC,CAAC,CAAC;IACzC,IAAIO,YAAY,GAAGF,SAAS,CAACG,eAAe,CAAC,CAAC;IAC9C,IAAIC,mBAAmB,GAAGX,OAAO,GAAGS,YAAY,CAAClB,MAAM;IACvD,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,mBAAmB,EAAEC,CAAC,EAAE,EAAE;MAC1Cb,QAAQ,CAACE,SAAS,GAAGW,CAAC,CAAC,GAAG,CAAC;IAC/B;IACAhC,MAAM,CAACwB,SAAS,CAACK,YAAY,EAAE,CAAC,EAAEV,QAAQ,EAAEE,SAAS,GAAGU,mBAAmB,EAAEF,YAAY,CAAClB,MAAM,CAAC;EACrG,CAAC;EACD,OAAOT,kBAAkB;AAC7B,CAAC,CAAC,CAAE;AACJ,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}