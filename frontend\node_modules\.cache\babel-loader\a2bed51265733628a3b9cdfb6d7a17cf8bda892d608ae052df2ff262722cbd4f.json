{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import java.util.Formatter;\nimport Formatter from '../../util/Formatter';\nimport BoundingBox from './BoundingBox';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResultColumn = /** @class */function () {\n  function DetectionResultColumn(boundingBox) {\n    this.boundingBox = new BoundingBox(boundingBox);\n    // this.codewords = new Codeword[boundingBox.getMaxY() - boundingBox.getMinY() + 1];\n    this.codewords = new Array(boundingBox.getMaxY() - boundingBox.getMinY() + 1);\n  }\n  /*final*/\n  DetectionResultColumn.prototype.getCodewordNearby = function (imageRow) {\n    var codeword = this.getCodeword(imageRow);\n    if (codeword != null) {\n      return codeword;\n    }\n    for (var i = 1; i < DetectionResultColumn.MAX_NEARBY_DISTANCE; i++) {\n      var nearImageRow = this.imageRowToCodewordIndex(imageRow) - i;\n      if (nearImageRow >= 0) {\n        codeword = this.codewords[nearImageRow];\n        if (codeword != null) {\n          return codeword;\n        }\n      }\n      nearImageRow = this.imageRowToCodewordIndex(imageRow) + i;\n      if (nearImageRow < this.codewords.length) {\n        codeword = this.codewords[nearImageRow];\n        if (codeword != null) {\n          return codeword;\n        }\n      }\n    }\n    return null;\n  };\n  /*final int*/\n  DetectionResultColumn.prototype.imageRowToCodewordIndex = function (imageRow) {\n    return imageRow - this.boundingBox.getMinY();\n  };\n  /*final void*/\n  DetectionResultColumn.prototype.setCodeword = function (imageRow, codeword) {\n    this.codewords[this.imageRowToCodewordIndex(imageRow)] = codeword;\n  };\n  /*final*/\n  DetectionResultColumn.prototype.getCodeword = function (imageRow) {\n    return this.codewords[this.imageRowToCodewordIndex(imageRow)];\n  };\n  /*final*/\n  DetectionResultColumn.prototype.getBoundingBox = function () {\n    return this.boundingBox;\n  };\n  /*final*/\n  DetectionResultColumn.prototype.getCodewords = function () {\n    return this.codewords;\n  };\n  // @Override\n  DetectionResultColumn.prototype.toString = function () {\n    var e_1, _a;\n    var formatter = new Formatter();\n    var row = 0;\n    try {\n      for (var _b = __values(this.codewords), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var codeword = _c.value;\n        if (codeword == null) {\n          formatter.format('%3d:    |   %n', row++);\n          continue;\n        }\n        formatter.format('%3d: %3d|%3d%n', row++, codeword.getRowNumber(), codeword.getValue());\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    return formatter.toString();\n  };\n  DetectionResultColumn.MAX_NEARBY_DISTANCE = 5;\n  return DetectionResultColumn;\n}();\nexport default DetectionResultColumn;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "<PERSON><PERSON><PERSON>", "BoundingBox", "DetectionResultColumn", "boundingBox", "codewords", "Array", "getMaxY", "getMinY", "prototype", "getCodewordNearby", "imageRow", "codeword", "getCodeword", "MAX_NEARBY_DISTANCE", "nearImageRow", "imageRowToCodewordIndex", "setCodeword", "getBoundingBox", "getCodewords", "toString", "e_1", "_a", "formatter", "row", "_b", "_c", "format", "getRowNumber", "getValue", "e_1_1", "error", "return"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/DetectionResultColumn.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import java.util.Formatter;\nimport Formatter from '../../util/Formatter';\nimport BoundingBox from './BoundingBox';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResultColumn = /** @class */ (function () {\n    function DetectionResultColumn(boundingBox) {\n        this.boundingBox = new BoundingBox(boundingBox);\n        // this.codewords = new Codeword[boundingBox.getMaxY() - boundingBox.getMinY() + 1];\n        this.codewords = new Array(boundingBox.getMaxY() - boundingBox.getMinY() + 1);\n    }\n    /*final*/ DetectionResultColumn.prototype.getCodewordNearby = function (imageRow) {\n        var codeword = this.getCodeword(imageRow);\n        if (codeword != null) {\n            return codeword;\n        }\n        for (var i = 1; i < DetectionResultColumn.MAX_NEARBY_DISTANCE; i++) {\n            var nearImageRow = this.imageRowToCodewordIndex(imageRow) - i;\n            if (nearImageRow >= 0) {\n                codeword = this.codewords[nearImageRow];\n                if (codeword != null) {\n                    return codeword;\n                }\n            }\n            nearImageRow = this.imageRowToCodewordIndex(imageRow) + i;\n            if (nearImageRow < this.codewords.length) {\n                codeword = this.codewords[nearImageRow];\n                if (codeword != null) {\n                    return codeword;\n                }\n            }\n        }\n        return null;\n    };\n    /*final int*/ DetectionResultColumn.prototype.imageRowToCodewordIndex = function (imageRow) {\n        return imageRow - this.boundingBox.getMinY();\n    };\n    /*final void*/ DetectionResultColumn.prototype.setCodeword = function (imageRow, codeword) {\n        this.codewords[this.imageRowToCodewordIndex(imageRow)] = codeword;\n    };\n    /*final*/ DetectionResultColumn.prototype.getCodeword = function (imageRow) {\n        return this.codewords[this.imageRowToCodewordIndex(imageRow)];\n    };\n    /*final*/ DetectionResultColumn.prototype.getBoundingBox = function () {\n        return this.boundingBox;\n    };\n    /*final*/ DetectionResultColumn.prototype.getCodewords = function () {\n        return this.codewords;\n    };\n    // @Override\n    DetectionResultColumn.prototype.toString = function () {\n        var e_1, _a;\n        var formatter = new Formatter();\n        var row = 0;\n        try {\n            for (var _b = __values(this.codewords), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var codeword = _c.value;\n                if (codeword == null) {\n                    formatter.format('%3d:    |   %n', row++);\n                    continue;\n                }\n                formatter.format('%3d: %3d|%3d%n', row++, codeword.getRowNumber(), codeword.getValue());\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return formatter.toString();\n    };\n    DetectionResultColumn.MAX_NEARBY_DISTANCE = 5;\n    return DetectionResultColumn;\n}());\nexport default DetectionResultColumn;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA,OAAOW,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,WAAW,MAAM,eAAe;AACvC;AACA;AACA;AACA,IAAIC,qBAAqB,GAAG,aAAe,YAAY;EACnD,SAASA,qBAAqBA,CAACC,WAAW,EAAE;IACxC,IAAI,CAACA,WAAW,GAAG,IAAIF,WAAW,CAACE,WAAW,CAAC;IAC/C;IACA,IAAI,CAACC,SAAS,GAAG,IAAIC,KAAK,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC,GAAGH,WAAW,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EACjF;EACA;EAAUL,qBAAqB,CAACM,SAAS,CAACC,iBAAiB,GAAG,UAAUC,QAAQ,EAAE;IAC9E,IAAIC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAACF,QAAQ,CAAC;IACzC,IAAIC,QAAQ,IAAI,IAAI,EAAE;MAClB,OAAOA,QAAQ;IACnB;IACA,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,qBAAqB,CAACW,mBAAmB,EAAEpB,CAAC,EAAE,EAAE;MAChE,IAAIqB,YAAY,GAAG,IAAI,CAACC,uBAAuB,CAACL,QAAQ,CAAC,GAAGjB,CAAC;MAC7D,IAAIqB,YAAY,IAAI,CAAC,EAAE;QACnBH,QAAQ,GAAG,IAAI,CAACP,SAAS,CAACU,YAAY,CAAC;QACvC,IAAIH,QAAQ,IAAI,IAAI,EAAE;UAClB,OAAOA,QAAQ;QACnB;MACJ;MACAG,YAAY,GAAG,IAAI,CAACC,uBAAuB,CAACL,QAAQ,CAAC,GAAGjB,CAAC;MACzD,IAAIqB,YAAY,GAAG,IAAI,CAACV,SAAS,CAACT,MAAM,EAAE;QACtCgB,QAAQ,GAAG,IAAI,CAACP,SAAS,CAACU,YAAY,CAAC;QACvC,IAAIH,QAAQ,IAAI,IAAI,EAAE;UAClB,OAAOA,QAAQ;QACnB;MACJ;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;EAAcT,qBAAqB,CAACM,SAAS,CAACO,uBAAuB,GAAG,UAAUL,QAAQ,EAAE;IACxF,OAAOA,QAAQ,GAAG,IAAI,CAACP,WAAW,CAACI,OAAO,CAAC,CAAC;EAChD,CAAC;EACD;EAAeL,qBAAqB,CAACM,SAAS,CAACQ,WAAW,GAAG,UAAUN,QAAQ,EAAEC,QAAQ,EAAE;IACvF,IAAI,CAACP,SAAS,CAAC,IAAI,CAACW,uBAAuB,CAACL,QAAQ,CAAC,CAAC,GAAGC,QAAQ;EACrE,CAAC;EACD;EAAUT,qBAAqB,CAACM,SAAS,CAACI,WAAW,GAAG,UAAUF,QAAQ,EAAE;IACxE,OAAO,IAAI,CAACN,SAAS,CAAC,IAAI,CAACW,uBAAuB,CAACL,QAAQ,CAAC,CAAC;EACjE,CAAC;EACD;EAAUR,qBAAqB,CAACM,SAAS,CAACS,cAAc,GAAG,YAAY;IACnE,OAAO,IAAI,CAACd,WAAW;EAC3B,CAAC;EACD;EAAUD,qBAAqB,CAACM,SAAS,CAACU,YAAY,GAAG,YAAY;IACjE,OAAO,IAAI,CAACd,SAAS;EACzB,CAAC;EACD;EACAF,qBAAqB,CAACM,SAAS,CAACW,QAAQ,GAAG,YAAY;IACnD,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIC,SAAS,GAAG,IAAItB,SAAS,CAAC,CAAC;IAC/B,IAAIuB,GAAG,GAAG,CAAC;IACX,IAAI;MACA,KAAK,IAAIC,EAAE,GAAGrC,QAAQ,CAAC,IAAI,CAACiB,SAAS,CAAC,EAAEqB,EAAE,GAAGD,EAAE,CAAC5B,IAAI,CAAC,CAAC,EAAE,CAAC6B,EAAE,CAAC3B,IAAI,EAAE2B,EAAE,GAAGD,EAAE,CAAC5B,IAAI,CAAC,CAAC,EAAE;QAC9E,IAAIe,QAAQ,GAAGc,EAAE,CAAC5B,KAAK;QACvB,IAAIc,QAAQ,IAAI,IAAI,EAAE;UAClBW,SAAS,CAACI,MAAM,CAAC,gBAAgB,EAAEH,GAAG,EAAE,CAAC;UACzC;QACJ;QACAD,SAAS,CAACI,MAAM,CAAC,gBAAgB,EAAEH,GAAG,EAAE,EAAEZ,QAAQ,CAACgB,YAAY,CAAC,CAAC,EAAEhB,QAAQ,CAACiB,QAAQ,CAAC,CAAC,CAAC;MAC3F;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAET,GAAG,GAAG;QAAEU,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIJ,EAAE,IAAI,CAACA,EAAE,CAAC3B,IAAI,KAAKuB,EAAE,GAAGG,EAAE,CAACO,MAAM,CAAC,EAAEV,EAAE,CAAC3B,IAAI,CAAC8B,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIJ,GAAG,EAAE,MAAMA,GAAG,CAACU,KAAK;MAAE;IACxC;IACA,OAAOR,SAAS,CAACH,QAAQ,CAAC,CAAC;EAC/B,CAAC;EACDjB,qBAAqB,CAACW,mBAAmB,GAAG,CAAC;EAC7C,OAAOX,qBAAqB;AAChC,CAAC,CAAC,CAAE;AACJ,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}