{"ast": null, "code": "import SimpleToken from './SimpleToken';\nexport var /*final*/MODE_NAMES = ['UPPER', 'LOWER', 'DIGIT', 'MIXED', 'PUNCT'];\nexport var /*final*/MODE_UPPER = 0; // 5 bits\nexport var /*final*/MODE_LOWER = 1; // 5 bits\nexport var /*final*/MODE_DIGIT = 2; // 4 bits\nexport var /*final*/MODE_MIXED = 3; // 5 bits\nexport var /*final*/MODE_PUNCT = 4; // 5 bits\nexport var EMPTY_TOKEN = new SimpleToken(null, 0, 0);", "map": {"version": 3, "names": ["SimpleToken", "MODE_NAMES", "MODE_UPPER", "MODE_LOWER", "MODE_DIGIT", "MODE_MIXED", "MODE_PUNCT", "EMPTY_TOKEN"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/EncoderConstants.js"], "sourcesContent": ["import SimpleToken from './SimpleToken';\nexport var /*final*/ MODE_NAMES = [\n    'UPPER',\n    'LOWER',\n    'DIGIT',\n    'MIXED',\n    'PUNCT'\n];\nexport var /*final*/ MODE_UPPER = 0; // 5 bits\nexport var /*final*/ MODE_LOWER = 1; // 5 bits\nexport var /*final*/ MODE_DIGIT = 2; // 4 bits\nexport var /*final*/ MODE_MIXED = 3; // 5 bits\nexport var /*final*/ MODE_PUNCT = 4; // 5 bits\nexport var EMPTY_TOKEN = new SimpleToken(null, 0, 0);\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,OAAO,IAAI,SAAUC,UAAU,GAAG,CAC9B,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,CACV;AACD,OAAO,IAAI,SAAUC,UAAU,GAAG,CAAC,CAAC,CAAC;AACrC,OAAO,IAAI,SAAUC,UAAU,GAAG,CAAC,CAAC,CAAC;AACrC,OAAO,IAAI,SAAUC,UAAU,GAAG,CAAC,CAAC,CAAC;AACrC,OAAO,IAAI,SAAUC,UAAU,GAAG,CAAC,CAAC,CAAC;AACrC,OAAO,IAAI,SAAUC,UAAU,GAAG,CAAC,CAAC,CAAC;AACrC,OAAO,IAAIC,WAAW,GAAG,IAAIP,WAAW,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}