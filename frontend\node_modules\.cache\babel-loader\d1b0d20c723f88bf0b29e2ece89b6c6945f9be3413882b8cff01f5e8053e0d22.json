{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport UPCEANExtensionSupport from './UPCEANExtensionSupport';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport NotFoundException from '../NotFoundException';\nimport FormatException from '../FormatException';\nimport ChecksumException from '../ChecksumException';\n/**\n * <p>Encapsulates functionality and implementation that is common to UPC and EAN families\n * of one-dimensional barcodes.</p>\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Alasdair Mackintosh)\n */\nvar UPCEANReader = /** @class */function (_super) {\n  __extends(UPCEANReader, _super);\n  function UPCEANReader() {\n    var _this = _super.call(this) || this;\n    _this.decodeRowStringBuffer = '';\n    UPCEANReader.L_AND_G_PATTERNS = UPCEANReader.L_PATTERNS.map(function (arr) {\n      return Int32Array.from(arr);\n    });\n    for (var i = 10; i < 20; i++) {\n      var widths = UPCEANReader.L_PATTERNS[i - 10];\n      var reversedWidths = new Int32Array(widths.length);\n      for (var j = 0; j < widths.length; j++) {\n        reversedWidths[j] = widths[widths.length - j - 1];\n      }\n      UPCEANReader.L_AND_G_PATTERNS[i] = reversedWidths;\n    }\n    return _this;\n  }\n  UPCEANReader.prototype.decodeRow = function (rowNumber, row, hints) {\n    var startGuardRange = UPCEANReader.findStartGuardPattern(row);\n    var resultPointCallback = hints == null ? null : hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n    if (resultPointCallback != null) {\n      var resultPoint_1 = new ResultPoint((startGuardRange[0] + startGuardRange[1]) / 2.0, rowNumber);\n      resultPointCallback.foundPossibleResultPoint(resultPoint_1);\n    }\n    var budello = this.decodeMiddle(row, startGuardRange, this.decodeRowStringBuffer);\n    var endStart = budello.rowOffset;\n    var result = budello.resultString;\n    if (resultPointCallback != null) {\n      var resultPoint_2 = new ResultPoint(endStart, rowNumber);\n      resultPointCallback.foundPossibleResultPoint(resultPoint_2);\n    }\n    var endRange = UPCEANReader.decodeEnd(row, endStart);\n    if (resultPointCallback != null) {\n      var resultPoint_3 = new ResultPoint((endRange[0] + endRange[1]) / 2.0, rowNumber);\n      resultPointCallback.foundPossibleResultPoint(resultPoint_3);\n    }\n    // Make sure there is a quiet zone at least as big as the end pattern after the barcode. The\n    // spec might want more whitespace, but in practice this is the maximum we can count on.\n    var end = endRange[1];\n    var quietEnd = end + (end - endRange[0]);\n    if (quietEnd >= row.getSize() || !row.isRange(end, quietEnd, false)) {\n      throw new NotFoundException();\n    }\n    var resultString = result.toString();\n    // UPC/EAN should never be less than 8 chars anyway\n    if (resultString.length < 8) {\n      throw new FormatException();\n    }\n    if (!UPCEANReader.checkChecksum(resultString)) {\n      throw new ChecksumException();\n    }\n    var left = (startGuardRange[1] + startGuardRange[0]) / 2.0;\n    var right = (endRange[1] + endRange[0]) / 2.0;\n    var format = this.getBarcodeFormat();\n    var resultPoint = [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)];\n    var decodeResult = new Result(resultString, null, 0, resultPoint, format, new Date().getTime());\n    var extensionLength = 0;\n    try {\n      var extensionResult = UPCEANExtensionSupport.decodeRow(rowNumber, row, endRange[1]);\n      decodeResult.putMetadata(ResultMetadataType.UPC_EAN_EXTENSION, extensionResult.getText());\n      decodeResult.putAllMetadata(extensionResult.getResultMetadata());\n      decodeResult.addResultPoints(extensionResult.getResultPoints());\n      extensionLength = extensionResult.getText().length;\n    } catch (err) {}\n    var allowedExtensions = hints == null ? null : hints.get(DecodeHintType.ALLOWED_EAN_EXTENSIONS);\n    if (allowedExtensions != null) {\n      var valid = false;\n      for (var length_1 in allowedExtensions) {\n        if (extensionLength.toString() === length_1) {\n          // check me\n          valid = true;\n          break;\n        }\n      }\n      if (!valid) {\n        throw new NotFoundException();\n      }\n    }\n    if (format === BarcodeFormat.EAN_13 || format === BarcodeFormat.UPC_A) {\n      // let countryID = eanManSupport.lookupContryIdentifier(resultString); todo\n      // if (countryID != null) {\n      //     decodeResult.putMetadata(ResultMetadataType.POSSIBLE_COUNTRY, countryID);\n      // }\n    }\n    return decodeResult;\n  };\n  UPCEANReader.checkChecksum = function (s) {\n    return UPCEANReader.checkStandardUPCEANChecksum(s);\n  };\n  UPCEANReader.checkStandardUPCEANChecksum = function (s) {\n    var length = s.length;\n    if (length === 0) return false;\n    var check = parseInt(s.charAt(length - 1), 10);\n    return UPCEANReader.getStandardUPCEANChecksum(s.substring(0, length - 1)) === check;\n  };\n  UPCEANReader.getStandardUPCEANChecksum = function (s) {\n    var length = s.length;\n    var sum = 0;\n    for (var i = length - 1; i >= 0; i -= 2) {\n      var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n      if (digit < 0 || digit > 9) {\n        throw new FormatException();\n      }\n      sum += digit;\n    }\n    sum *= 3;\n    for (var i = length - 2; i >= 0; i -= 2) {\n      var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n      if (digit < 0 || digit > 9) {\n        throw new FormatException();\n      }\n      sum += digit;\n    }\n    return (1000 - sum) % 10;\n  };\n  UPCEANReader.decodeEnd = function (row, endStart) {\n    return UPCEANReader.findGuardPattern(row, endStart, false, UPCEANReader.START_END_PATTERN, new Int32Array(UPCEANReader.START_END_PATTERN.length).fill(0));\n  };\n  return UPCEANReader;\n}(AbstractUPCEANReader);\nexport default UPCEANReader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "BarcodeFormat", "DecodeHintType", "Result", "ResultMetadataType", "ResultPoint", "UPCEANExtensionSupport", "AbstractUPCEANReader", "NotFoundException", "FormatException", "ChecksumException", "UPCEANReader", "_super", "_this", "call", "decodeRowStringBuffer", "L_AND_G_PATTERNS", "L_PATTERNS", "map", "arr", "Int32Array", "from", "i", "widths", "reversedWidths", "length", "j", "decodeRow", "rowNumber", "row", "hints", "startGuardRange", "findStartGuardPattern", "resultPointCallback", "get", "NEED_RESULT_POINT_CALLBACK", "resultPoint_1", "foundPossibleResultPoint", "budello", "decodeMiddle", "endStart", "rowOffset", "result", "resultString", "resultPoint_2", "endRange", "decodeEnd", "resultPoint_3", "end", "quietEnd", "getSize", "isRange", "toString", "checkChecksum", "left", "right", "format", "getBarcodeFormat", "resultPoint", "decodeResult", "Date", "getTime", "extensionLength", "extensionResult", "putMetadata", "UPC_EAN_EXTENSION", "getText", "putAllMetadata", "getResultMetadata", "addResultPoints", "getResultPoints", "err", "allowedExtensions", "ALLOWED_EAN_EXTENSIONS", "valid", "length_1", "EAN_13", "UPC_A", "s", "checkStandardUPCEANChecksum", "check", "parseInt", "char<PERSON>t", "getStandardUPCEANChecksum", "substring", "sum", "digit", "charCodeAt", "<PERSON><PERSON><PERSON><PERSON>att<PERSON>", "START_END_PATTERN", "fill"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/UPCEANReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport Result from '../Result';\nimport ResultMetadataType from '../ResultMetadataType';\nimport ResultPoint from '../ResultPoint';\nimport UPCEANExtensionSupport from './UPCEANExtensionSupport';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport NotFoundException from '../NotFoundException';\nimport FormatException from '../FormatException';\nimport ChecksumException from '../ChecksumException';\n/**\n * <p>Encapsulates functionality and implementation that is common to UPC and EAN families\n * of one-dimensional barcodes.</p>\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Alasdair Mackintosh)\n */\nvar UPCEANReader = /** @class */ (function (_super) {\n    __extends(UPCEANReader, _super);\n    function UPCEANReader() {\n        var _this = _super.call(this) || this;\n        _this.decodeRowStringBuffer = '';\n        UPCEANReader.L_AND_G_PATTERNS = UPCEANReader.L_PATTERNS.map(function (arr) { return Int32Array.from(arr); });\n        for (var i = 10; i < 20; i++) {\n            var widths = UPCEANReader.L_PATTERNS[i - 10];\n            var reversedWidths = new Int32Array(widths.length);\n            for (var j = 0; j < widths.length; j++) {\n                reversedWidths[j] = widths[widths.length - j - 1];\n            }\n            UPCEANReader.L_AND_G_PATTERNS[i] = reversedWidths;\n        }\n        return _this;\n    }\n    UPCEANReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var startGuardRange = UPCEANReader.findStartGuardPattern(row);\n        var resultPointCallback = hints == null ? null : hints.get(DecodeHintType.NEED_RESULT_POINT_CALLBACK);\n        if (resultPointCallback != null) {\n            var resultPoint_1 = new ResultPoint((startGuardRange[0] + startGuardRange[1]) / 2.0, rowNumber);\n            resultPointCallback.foundPossibleResultPoint(resultPoint_1);\n        }\n        var budello = this.decodeMiddle(row, startGuardRange, this.decodeRowStringBuffer);\n        var endStart = budello.rowOffset;\n        var result = budello.resultString;\n        if (resultPointCallback != null) {\n            var resultPoint_2 = new ResultPoint(endStart, rowNumber);\n            resultPointCallback.foundPossibleResultPoint(resultPoint_2);\n        }\n        var endRange = UPCEANReader.decodeEnd(row, endStart);\n        if (resultPointCallback != null) {\n            var resultPoint_3 = new ResultPoint((endRange[0] + endRange[1]) / 2.0, rowNumber);\n            resultPointCallback.foundPossibleResultPoint(resultPoint_3);\n        }\n        // Make sure there is a quiet zone at least as big as the end pattern after the barcode. The\n        // spec might want more whitespace, but in practice this is the maximum we can count on.\n        var end = endRange[1];\n        var quietEnd = end + (end - endRange[0]);\n        if (quietEnd >= row.getSize() || !row.isRange(end, quietEnd, false)) {\n            throw new NotFoundException();\n        }\n        var resultString = result.toString();\n        // UPC/EAN should never be less than 8 chars anyway\n        if (resultString.length < 8) {\n            throw new FormatException();\n        }\n        if (!UPCEANReader.checkChecksum(resultString)) {\n            throw new ChecksumException();\n        }\n        var left = (startGuardRange[1] + startGuardRange[0]) / 2.0;\n        var right = (endRange[1] + endRange[0]) / 2.0;\n        var format = this.getBarcodeFormat();\n        var resultPoint = [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)];\n        var decodeResult = new Result(resultString, null, 0, resultPoint, format, new Date().getTime());\n        var extensionLength = 0;\n        try {\n            var extensionResult = UPCEANExtensionSupport.decodeRow(rowNumber, row, endRange[1]);\n            decodeResult.putMetadata(ResultMetadataType.UPC_EAN_EXTENSION, extensionResult.getText());\n            decodeResult.putAllMetadata(extensionResult.getResultMetadata());\n            decodeResult.addResultPoints(extensionResult.getResultPoints());\n            extensionLength = extensionResult.getText().length;\n        }\n        catch (err) {\n        }\n        var allowedExtensions = hints == null ? null : hints.get(DecodeHintType.ALLOWED_EAN_EXTENSIONS);\n        if (allowedExtensions != null) {\n            var valid = false;\n            for (var length_1 in allowedExtensions) {\n                if (extensionLength.toString() === length_1) { // check me\n                    valid = true;\n                    break;\n                }\n            }\n            if (!valid) {\n                throw new NotFoundException();\n            }\n        }\n        if (format === BarcodeFormat.EAN_13 || format === BarcodeFormat.UPC_A) {\n            // let countryID = eanManSupport.lookupContryIdentifier(resultString); todo\n            // if (countryID != null) {\n            //     decodeResult.putMetadata(ResultMetadataType.POSSIBLE_COUNTRY, countryID);\n            // }\n        }\n        return decodeResult;\n    };\n    UPCEANReader.checkChecksum = function (s) {\n        return UPCEANReader.checkStandardUPCEANChecksum(s);\n    };\n    UPCEANReader.checkStandardUPCEANChecksum = function (s) {\n        var length = s.length;\n        if (length === 0)\n            return false;\n        var check = parseInt(s.charAt(length - 1), 10);\n        return UPCEANReader.getStandardUPCEANChecksum(s.substring(0, length - 1)) === check;\n    };\n    UPCEANReader.getStandardUPCEANChecksum = function (s) {\n        var length = s.length;\n        var sum = 0;\n        for (var i = length - 1; i >= 0; i -= 2) {\n            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            if (digit < 0 || digit > 9) {\n                throw new FormatException();\n            }\n            sum += digit;\n        }\n        sum *= 3;\n        for (var i = length - 2; i >= 0; i -= 2) {\n            var digit = s.charAt(i).charCodeAt(0) - '0'.charCodeAt(0);\n            if (digit < 0 || digit > 9) {\n                throw new FormatException();\n            }\n            sum += digit;\n        }\n        return (1000 - sum) % 10;\n    };\n    UPCEANReader.decodeEnd = function (row, endStart) {\n        return UPCEANReader.findGuardPattern(row, endStart, false, UPCEANReader.START_END_PATTERN, new Int32Array(UPCEANReader.START_END_PATTERN.length).fill(0));\n    };\n    return UPCEANReader;\n}(AbstractUPCEANReader));\nexport default UPCEANReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAe,UAAUC,MAAM,EAAE;EAChDzB,SAAS,CAACwB,YAAY,EAAEC,MAAM,CAAC;EAC/B,SAASD,YAAYA,CAAA,EAAG;IACpB,IAAIE,KAAK,GAAGD,MAAM,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,qBAAqB,GAAG,EAAE;IAChCJ,YAAY,CAACK,gBAAgB,GAAGL,YAAY,CAACM,UAAU,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;MAAE,OAAOC,UAAU,CAACC,IAAI,CAACF,GAAG,CAAC;IAAE,CAAC,CAAC;IAC5G,KAAK,IAAIG,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIC,MAAM,GAAGZ,YAAY,CAACM,UAAU,CAACK,CAAC,GAAG,EAAE,CAAC;MAC5C,IAAIE,cAAc,GAAG,IAAIJ,UAAU,CAACG,MAAM,CAACE,MAAM,CAAC;MAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;QACpCF,cAAc,CAACE,CAAC,CAAC,GAAGH,MAAM,CAACA,MAAM,CAACE,MAAM,GAAGC,CAAC,GAAG,CAAC,CAAC;MACrD;MACAf,YAAY,CAACK,gBAAgB,CAACM,CAAC,CAAC,GAAGE,cAAc;IACrD;IACA,OAAOX,KAAK;EAChB;EACAF,YAAY,CAACZ,SAAS,CAAC4B,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAE;IAChE,IAAIC,eAAe,GAAGpB,YAAY,CAACqB,qBAAqB,CAACH,GAAG,CAAC;IAC7D,IAAII,mBAAmB,GAAGH,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGA,KAAK,CAACI,GAAG,CAAChC,cAAc,CAACiC,0BAA0B,CAAC;IACrG,IAAIF,mBAAmB,IAAI,IAAI,EAAE;MAC7B,IAAIG,aAAa,GAAG,IAAI/B,WAAW,CAAC,CAAC0B,eAAe,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC,IAAI,GAAG,EAAEH,SAAS,CAAC;MAC/FK,mBAAmB,CAACI,wBAAwB,CAACD,aAAa,CAAC;IAC/D;IACA,IAAIE,OAAO,GAAG,IAAI,CAACC,YAAY,CAACV,GAAG,EAAEE,eAAe,EAAE,IAAI,CAAChB,qBAAqB,CAAC;IACjF,IAAIyB,QAAQ,GAAGF,OAAO,CAACG,SAAS;IAChC,IAAIC,MAAM,GAAGJ,OAAO,CAACK,YAAY;IACjC,IAAIV,mBAAmB,IAAI,IAAI,EAAE;MAC7B,IAAIW,aAAa,GAAG,IAAIvC,WAAW,CAACmC,QAAQ,EAAEZ,SAAS,CAAC;MACxDK,mBAAmB,CAACI,wBAAwB,CAACO,aAAa,CAAC;IAC/D;IACA,IAAIC,QAAQ,GAAGlC,YAAY,CAACmC,SAAS,CAACjB,GAAG,EAAEW,QAAQ,CAAC;IACpD,IAAIP,mBAAmB,IAAI,IAAI,EAAE;MAC7B,IAAIc,aAAa,GAAG,IAAI1C,WAAW,CAAC,CAACwC,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG,EAAEjB,SAAS,CAAC;MACjFK,mBAAmB,CAACI,wBAAwB,CAACU,aAAa,CAAC;IAC/D;IACA;IACA;IACA,IAAIC,GAAG,GAAGH,QAAQ,CAAC,CAAC,CAAC;IACrB,IAAII,QAAQ,GAAGD,GAAG,IAAIA,GAAG,GAAGH,QAAQ,CAAC,CAAC,CAAC,CAAC;IACxC,IAAII,QAAQ,IAAIpB,GAAG,CAACqB,OAAO,CAAC,CAAC,IAAI,CAACrB,GAAG,CAACsB,OAAO,CAACH,GAAG,EAAEC,QAAQ,EAAE,KAAK,CAAC,EAAE;MACjE,MAAM,IAAIzC,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAImC,YAAY,GAAGD,MAAM,CAACU,QAAQ,CAAC,CAAC;IACpC;IACA,IAAIT,YAAY,CAAClB,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIhB,eAAe,CAAC,CAAC;IAC/B;IACA,IAAI,CAACE,YAAY,CAAC0C,aAAa,CAACV,YAAY,CAAC,EAAE;MAC3C,MAAM,IAAIjC,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI4C,IAAI,GAAG,CAACvB,eAAe,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC,IAAI,GAAG;IAC1D,IAAIwB,KAAK,GAAG,CAACV,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,IAAI,GAAG;IAC7C,IAAIW,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACpC,IAAIC,WAAW,GAAG,CAAC,IAAIrD,WAAW,CAACiD,IAAI,EAAE1B,SAAS,CAAC,EAAE,IAAIvB,WAAW,CAACkD,KAAK,EAAE3B,SAAS,CAAC,CAAC;IACvF,IAAI+B,YAAY,GAAG,IAAIxD,MAAM,CAACwC,YAAY,EAAE,IAAI,EAAE,CAAC,EAAEe,WAAW,EAAEF,MAAM,EAAE,IAAII,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;IAC/F,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAI;MACA,IAAIC,eAAe,GAAGzD,sBAAsB,CAACqB,SAAS,CAACC,SAAS,EAAEC,GAAG,EAAEgB,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnFc,YAAY,CAACK,WAAW,CAAC5D,kBAAkB,CAAC6D,iBAAiB,EAAEF,eAAe,CAACG,OAAO,CAAC,CAAC,CAAC;MACzFP,YAAY,CAACQ,cAAc,CAACJ,eAAe,CAACK,iBAAiB,CAAC,CAAC,CAAC;MAChET,YAAY,CAACU,eAAe,CAACN,eAAe,CAACO,eAAe,CAAC,CAAC,CAAC;MAC/DR,eAAe,GAAGC,eAAe,CAACG,OAAO,CAAC,CAAC,CAACzC,MAAM;IACtD,CAAC,CACD,OAAO8C,GAAG,EAAE,CACZ;IACA,IAAIC,iBAAiB,GAAG1C,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGA,KAAK,CAACI,GAAG,CAAChC,cAAc,CAACuE,sBAAsB,CAAC;IAC/F,IAAID,iBAAiB,IAAI,IAAI,EAAE;MAC3B,IAAIE,KAAK,GAAG,KAAK;MACjB,KAAK,IAAIC,QAAQ,IAAIH,iBAAiB,EAAE;QACpC,IAAIV,eAAe,CAACV,QAAQ,CAAC,CAAC,KAAKuB,QAAQ,EAAE;UAAE;UAC3CD,KAAK,GAAG,IAAI;UACZ;QACJ;MACJ;MACA,IAAI,CAACA,KAAK,EAAE;QACR,MAAM,IAAIlE,iBAAiB,CAAC,CAAC;MACjC;IACJ;IACA,IAAIgD,MAAM,KAAKvD,aAAa,CAAC2E,MAAM,IAAIpB,MAAM,KAAKvD,aAAa,CAAC4E,KAAK,EAAE;MACnE;MACA;MACA;MACA;IAAA;IAEJ,OAAOlB,YAAY;EACvB,CAAC;EACDhD,YAAY,CAAC0C,aAAa,GAAG,UAAUyB,CAAC,EAAE;IACtC,OAAOnE,YAAY,CAACoE,2BAA2B,CAACD,CAAC,CAAC;EACtD,CAAC;EACDnE,YAAY,CAACoE,2BAA2B,GAAG,UAAUD,CAAC,EAAE;IACpD,IAAIrD,MAAM,GAAGqD,CAAC,CAACrD,MAAM;IACrB,IAAIA,MAAM,KAAK,CAAC,EACZ,OAAO,KAAK;IAChB,IAAIuD,KAAK,GAAGC,QAAQ,CAACH,CAAC,CAACI,MAAM,CAACzD,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IAC9C,OAAOd,YAAY,CAACwE,yBAAyB,CAACL,CAAC,CAACM,SAAS,CAAC,CAAC,EAAE3D,MAAM,GAAG,CAAC,CAAC,CAAC,KAAKuD,KAAK;EACvF,CAAC;EACDrE,YAAY,CAACwE,yBAAyB,GAAG,UAAUL,CAAC,EAAE;IAClD,IAAIrD,MAAM,GAAGqD,CAAC,CAACrD,MAAM;IACrB,IAAI4D,GAAG,GAAG,CAAC;IACX,KAAK,IAAI/D,CAAC,GAAGG,MAAM,GAAG,CAAC,EAAEH,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACrC,IAAIgE,KAAK,GAAGR,CAAC,CAACI,MAAM,CAAC5D,CAAC,CAAC,CAACiE,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;MACzD,IAAID,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7E,eAAe,CAAC,CAAC;MAC/B;MACA4E,GAAG,IAAIC,KAAK;IAChB;IACAD,GAAG,IAAI,CAAC;IACR,KAAK,IAAI/D,CAAC,GAAGG,MAAM,GAAG,CAAC,EAAEH,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MACrC,IAAIgE,KAAK,GAAGR,CAAC,CAACI,MAAM,CAAC5D,CAAC,CAAC,CAACiE,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;MACzD,IAAID,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7E,eAAe,CAAC,CAAC;MAC/B;MACA4E,GAAG,IAAIC,KAAK;IAChB;IACA,OAAO,CAAC,IAAI,GAAGD,GAAG,IAAI,EAAE;EAC5B,CAAC;EACD1E,YAAY,CAACmC,SAAS,GAAG,UAAUjB,GAAG,EAAEW,QAAQ,EAAE;IAC9C,OAAO7B,YAAY,CAAC6E,gBAAgB,CAAC3D,GAAG,EAAEW,QAAQ,EAAE,KAAK,EAAE7B,YAAY,CAAC8E,iBAAiB,EAAE,IAAIrE,UAAU,CAACT,YAAY,CAAC8E,iBAAiB,CAAChE,MAAM,CAAC,CAACiE,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7J,CAAC;EACD,OAAO/E,YAAY;AACvB,CAAC,CAACJ,oBAAoB,CAAE;AACxB,eAAeI,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}