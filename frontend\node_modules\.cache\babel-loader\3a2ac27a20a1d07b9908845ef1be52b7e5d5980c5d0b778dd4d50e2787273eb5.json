{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { buildSectionsFromFormat } from \"../internals/hooks/useField/buildSectionsFromFormat.js\";\nimport { getLocalizedDigits } from \"../internals/hooks/useField/useField.utils.js\";\nimport { usePickersTranslations } from \"./usePickersTranslations.js\";\n/**\n * Returns the parsed format to be rendered in the field when there is no value or in other parts of the Picker.\n * This format is localized (e.g: `AAAA` for the year with the French locale) and cannot be parsed by your date library.\n * @param {object} The parameters needed to build the placeholder.\n * @param {string} params.format Format of the date to use.\n * @param {'dense' | 'spacious'} params.formatDensity Density of the format (setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character).\n * @param {boolean} params.shouldRespectLeadingZeros If `true`, the format will respect the leading zeroes, if `false`, the format will always add leading zeroes.\n * @returns\n */\nexport const useParsedFormat = parameters => {\n  const {\n    format,\n    formatDensity = 'dense',\n    shouldRespectLeadingZeros = false\n  } = parameters;\n  const utils = useUtils();\n  const isRtl = useRtl();\n  const translations = usePickersTranslations();\n  const localizedDigits = React.useMemo(() => getLocalizedDigits(utils), [utils]);\n  return React.useMemo(() => {\n    const sections = buildSectionsFromFormat({\n      utils,\n      format,\n      formatDensity,\n      isRtl,\n      shouldRespectLeadingZeros,\n      localeText: translations,\n      localizedDigits,\n      date: null,\n      // TODO v9: Make sure we still don't reverse in `buildSectionsFromFormat` when using `useParsedFormat`.\n      enableAccessibleFieldDOMStructure: false\n    });\n    return sections.map(section => `${section.startSeparator}${section.placeholder}${section.endSeparator}`).join('');\n  }, [utils, isRtl, translations, localizedDigits, format, formatDensity, shouldRespectLeadingZeros]);\n};", "map": {"version": 3, "names": ["React", "useRtl", "useUtils", "buildSectionsFromFormat", "getLocalizedDigits", "usePickersTranslations", "useParsedFormat", "parameters", "format", "formatDensity", "shouldRespectLeadingZeros", "utils", "isRtl", "translations", "localizedDigits", "useMemo", "sections", "localeText", "date", "enableAccessibleFieldDOMStructure", "map", "section", "startSeparator", "placeholder", "endSeparator", "join"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/hooks/useParsedFormat.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { useUtils } from \"../internals/hooks/useUtils.js\";\nimport { buildSectionsFromFormat } from \"../internals/hooks/useField/buildSectionsFromFormat.js\";\nimport { getLocalizedDigits } from \"../internals/hooks/useField/useField.utils.js\";\nimport { usePickersTranslations } from \"./usePickersTranslations.js\";\n/**\n * Returns the parsed format to be rendered in the field when there is no value or in other parts of the Picker.\n * This format is localized (e.g: `AAAA` for the year with the French locale) and cannot be parsed by your date library.\n * @param {object} The parameters needed to build the placeholder.\n * @param {string} params.format Format of the date to use.\n * @param {'dense' | 'spacious'} params.formatDensity Density of the format (setting `formatDensity` to `\"spacious\"` will add a space before and after each `/`, `-` and `.` character).\n * @param {boolean} params.shouldRespectLeadingZeros If `true`, the format will respect the leading zeroes, if `false`, the format will always add leading zeroes.\n * @returns\n */\nexport const useParsedFormat = parameters => {\n  const {\n    format,\n    formatDensity = 'dense',\n    shouldRespectLeadingZeros = false\n  } = parameters;\n  const utils = useUtils();\n  const isRtl = useRtl();\n  const translations = usePickersTranslations();\n  const localizedDigits = React.useMemo(() => getLocalizedDigits(utils), [utils]);\n  return React.useMemo(() => {\n    const sections = buildSectionsFromFormat({\n      utils,\n      format,\n      formatDensity,\n      isRtl,\n      shouldRespectLeadingZeros,\n      localeText: translations,\n      localizedDigits,\n      date: null,\n      // TODO v9: Make sure we still don't reverse in `buildSectionsFromFormat` when using `useParsedFormat`.\n      enableAccessibleFieldDOMStructure: false\n    });\n    return sections.map(section => `${section.startSeparator}${section.placeholder}${section.endSeparator}`).join('');\n  }, [utils, isRtl, translations, localizedDigits, format, formatDensity, shouldRespectLeadingZeros]);\n};"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,QAAQ,QAAQ,gCAAgC;AACzD,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,kBAAkB,QAAQ,+CAA+C;AAClF,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGC,UAAU,IAAI;EAC3C,MAAM;IACJC,MAAM;IACNC,aAAa,GAAG,OAAO;IACvBC,yBAAyB,GAAG;EAC9B,CAAC,GAAGH,UAAU;EACd,MAAMI,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,MAAMU,KAAK,GAAGX,MAAM,CAAC,CAAC;EACtB,MAAMY,YAAY,GAAGR,sBAAsB,CAAC,CAAC;EAC7C,MAAMS,eAAe,GAAGd,KAAK,CAACe,OAAO,CAAC,MAAMX,kBAAkB,CAACO,KAAK,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAC/E,OAAOX,KAAK,CAACe,OAAO,CAAC,MAAM;IACzB,MAAMC,QAAQ,GAAGb,uBAAuB,CAAC;MACvCQ,KAAK;MACLH,MAAM;MACNC,aAAa;MACbG,KAAK;MACLF,yBAAyB;MACzBO,UAAU,EAAEJ,YAAY;MACxBC,eAAe;MACfI,IAAI,EAAE,IAAI;MACV;MACAC,iCAAiC,EAAE;IACrC,CAAC,CAAC;IACF,OAAOH,QAAQ,CAACI,GAAG,CAACC,OAAO,IAAI,GAAGA,OAAO,CAACC,cAAc,GAAGD,OAAO,CAACE,WAAW,GAAGF,OAAO,CAACG,YAAY,EAAE,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACnH,CAAC,EAAE,CAACd,KAAK,EAAEC,KAAK,EAAEC,YAAY,EAAEC,eAAe,EAAEN,MAAM,EAAEC,aAAa,EAAEC,yBAAyB,CAAC,CAAC;AACrG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}