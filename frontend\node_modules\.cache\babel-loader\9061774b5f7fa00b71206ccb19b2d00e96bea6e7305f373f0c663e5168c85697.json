{"ast": null, "code": "/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.NotFoundException;\nimport NotFoundException from '../../NotFoundException';\n// import com.google.zxing.ResultPoint;\nimport ResultPoint from '../../ResultPoint';\n/**\n * <AUTHOR>\n */\nvar BoundingBox = /** @class */function () {\n  function BoundingBox(image, topLeft, bottomLeft, topRight, bottomRight) {\n    if (image instanceof BoundingBox) {\n      this.constructor_2(image);\n    } else {\n      this.constructor_1(image, topLeft, bottomLeft, topRight, bottomRight);\n    }\n  }\n  /**\n   *\n   * @param image\n   * @param topLeft\n   * @param bottomLeft\n   * @param topRight\n   * @param bottomRight\n   *\n   * @throws NotFoundException\n   */\n  BoundingBox.prototype.constructor_1 = function (image, topLeft, bottomLeft, topRight, bottomRight) {\n    var leftUnspecified = topLeft == null || bottomLeft == null;\n    var rightUnspecified = topRight == null || bottomRight == null;\n    if (leftUnspecified && rightUnspecified) {\n      throw new NotFoundException();\n    }\n    if (leftUnspecified) {\n      topLeft = new ResultPoint(0, topRight.getY());\n      bottomLeft = new ResultPoint(0, bottomRight.getY());\n    } else if (rightUnspecified) {\n      topRight = new ResultPoint(image.getWidth() - 1, topLeft.getY());\n      bottomRight = new ResultPoint(image.getWidth() - 1, bottomLeft.getY());\n    }\n    this.image = image;\n    this.topLeft = topLeft;\n    this.bottomLeft = bottomLeft;\n    this.topRight = topRight;\n    this.bottomRight = bottomRight;\n    this.minX = Math.trunc(Math.min(topLeft.getX(), bottomLeft.getX()));\n    this.maxX = Math.trunc(Math.max(topRight.getX(), bottomRight.getX()));\n    this.minY = Math.trunc(Math.min(topLeft.getY(), topRight.getY()));\n    this.maxY = Math.trunc(Math.max(bottomLeft.getY(), bottomRight.getY()));\n  };\n  BoundingBox.prototype.constructor_2 = function (boundingBox) {\n    this.image = boundingBox.image;\n    this.topLeft = boundingBox.getTopLeft();\n    this.bottomLeft = boundingBox.getBottomLeft();\n    this.topRight = boundingBox.getTopRight();\n    this.bottomRight = boundingBox.getBottomRight();\n    this.minX = boundingBox.getMinX();\n    this.maxX = boundingBox.getMaxX();\n    this.minY = boundingBox.getMinY();\n    this.maxY = boundingBox.getMaxY();\n  };\n  /**\n   * @throws NotFoundException\n   */\n  BoundingBox.merge = function (leftBox, rightBox) {\n    if (leftBox == null) {\n      return rightBox;\n    }\n    if (rightBox == null) {\n      return leftBox;\n    }\n    return new BoundingBox(leftBox.image, leftBox.topLeft, leftBox.bottomLeft, rightBox.topRight, rightBox.bottomRight);\n  };\n  /**\n   * @throws NotFoundException\n   */\n  BoundingBox.prototype.addMissingRows = function (missingStartRows, missingEndRows, isLeft) {\n    var newTopLeft = this.topLeft;\n    var newBottomLeft = this.bottomLeft;\n    var newTopRight = this.topRight;\n    var newBottomRight = this.bottomRight;\n    if (missingStartRows > 0) {\n      var top_1 = isLeft ? this.topLeft : this.topRight;\n      var newMinY = Math.trunc(top_1.getY() - missingStartRows);\n      if (newMinY < 0) {\n        newMinY = 0;\n      }\n      var newTop = new ResultPoint(top_1.getX(), newMinY);\n      if (isLeft) {\n        newTopLeft = newTop;\n      } else {\n        newTopRight = newTop;\n      }\n    }\n    if (missingEndRows > 0) {\n      var bottom = isLeft ? this.bottomLeft : this.bottomRight;\n      var newMaxY = Math.trunc(bottom.getY() + missingEndRows);\n      if (newMaxY >= this.image.getHeight()) {\n        newMaxY = this.image.getHeight() - 1;\n      }\n      var newBottom = new ResultPoint(bottom.getX(), newMaxY);\n      if (isLeft) {\n        newBottomLeft = newBottom;\n      } else {\n        newBottomRight = newBottom;\n      }\n    }\n    return new BoundingBox(this.image, newTopLeft, newBottomLeft, newTopRight, newBottomRight);\n  };\n  BoundingBox.prototype.getMinX = function () {\n    return this.minX;\n  };\n  BoundingBox.prototype.getMaxX = function () {\n    return this.maxX;\n  };\n  BoundingBox.prototype.getMinY = function () {\n    return this.minY;\n  };\n  BoundingBox.prototype.getMaxY = function () {\n    return this.maxY;\n  };\n  BoundingBox.prototype.getTopLeft = function () {\n    return this.topLeft;\n  };\n  BoundingBox.prototype.getTopRight = function () {\n    return this.topRight;\n  };\n  BoundingBox.prototype.getBottomLeft = function () {\n    return this.bottomLeft;\n  };\n  BoundingBox.prototype.getBottomRight = function () {\n    return this.bottomRight;\n  };\n  return BoundingBox;\n}();\nexport default BoundingBox;", "map": {"version": 3, "names": ["NotFoundException", "ResultPoint", "BoundingBox", "image", "topLeft", "bottomLeft", "topRight", "bottomRight", "constructor_2", "constructor_1", "prototype", "leftUnspecified", "rightUnspecified", "getY", "getWidth", "minX", "Math", "trunc", "min", "getX", "maxX", "max", "minY", "maxY", "boundingBox", "getTopLeft", "getBottomLeft", "getTopRight", "getBottomRight", "getMinX", "getMaxX", "getMinY", "getMaxY", "merge", "leftBox", "rightBox", "addMissingRows", "missingStartRows", "missingEndRows", "isLeft", "newTopLeft", "newBottomLeft", "newTopRight", "newBottomRight", "top_1", "newMinY", "newTop", "bottom", "newMaxY", "getHeight", "newBottom"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/BoundingBox.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.NotFoundException;\nimport NotFoundException from '../../NotFoundException';\n// import com.google.zxing.ResultPoint;\nimport ResultPoint from '../../ResultPoint';\n/**\n * <AUTHOR>\n */\nvar BoundingBox = /** @class */ (function () {\n    function BoundingBox(image, topLeft, bottomLeft, topRight, bottomRight) {\n        if (image instanceof BoundingBox) {\n            this.constructor_2(image);\n        }\n        else {\n            this.constructor_1(image, topLeft, bottomLeft, topRight, bottomRight);\n        }\n    }\n    /**\n     *\n     * @param image\n     * @param topLeft\n     * @param bottomLeft\n     * @param topRight\n     * @param bottomRight\n     *\n     * @throws NotFoundException\n     */\n    BoundingBox.prototype.constructor_1 = function (image, topLeft, bottomLeft, topRight, bottomRight) {\n        var leftUnspecified = topLeft == null || bottomLeft == null;\n        var rightUnspecified = topRight == null || bottomRight == null;\n        if (leftUnspecified && rightUnspecified) {\n            throw new NotFoundException();\n        }\n        if (leftUnspecified) {\n            topLeft = new ResultPoint(0, topRight.getY());\n            bottomLeft = new ResultPoint(0, bottomRight.getY());\n        }\n        else if (rightUnspecified) {\n            topRight = new ResultPoint(image.getWidth() - 1, topLeft.getY());\n            bottomRight = new ResultPoint(image.getWidth() - 1, bottomLeft.getY());\n        }\n        this.image = image;\n        this.topLeft = topLeft;\n        this.bottomLeft = bottomLeft;\n        this.topRight = topRight;\n        this.bottomRight = bottomRight;\n        this.minX = Math.trunc(Math.min(topLeft.getX(), bottomLeft.getX()));\n        this.maxX = Math.trunc(Math.max(topRight.getX(), bottomRight.getX()));\n        this.minY = Math.trunc(Math.min(topLeft.getY(), topRight.getY()));\n        this.maxY = Math.trunc(Math.max(bottomLeft.getY(), bottomRight.getY()));\n    };\n    BoundingBox.prototype.constructor_2 = function (boundingBox) {\n        this.image = boundingBox.image;\n        this.topLeft = boundingBox.getTopLeft();\n        this.bottomLeft = boundingBox.getBottomLeft();\n        this.topRight = boundingBox.getTopRight();\n        this.bottomRight = boundingBox.getBottomRight();\n        this.minX = boundingBox.getMinX();\n        this.maxX = boundingBox.getMaxX();\n        this.minY = boundingBox.getMinY();\n        this.maxY = boundingBox.getMaxY();\n    };\n    /**\n     * @throws NotFoundException\n     */\n    BoundingBox.merge = function (leftBox, rightBox) {\n        if (leftBox == null) {\n            return rightBox;\n        }\n        if (rightBox == null) {\n            return leftBox;\n        }\n        return new BoundingBox(leftBox.image, leftBox.topLeft, leftBox.bottomLeft, rightBox.topRight, rightBox.bottomRight);\n    };\n    /**\n     * @throws NotFoundException\n     */\n    BoundingBox.prototype.addMissingRows = function (missingStartRows, missingEndRows, isLeft) {\n        var newTopLeft = this.topLeft;\n        var newBottomLeft = this.bottomLeft;\n        var newTopRight = this.topRight;\n        var newBottomRight = this.bottomRight;\n        if (missingStartRows > 0) {\n            var top_1 = isLeft ? this.topLeft : this.topRight;\n            var newMinY = Math.trunc(top_1.getY() - missingStartRows);\n            if (newMinY < 0) {\n                newMinY = 0;\n            }\n            var newTop = new ResultPoint(top_1.getX(), newMinY);\n            if (isLeft) {\n                newTopLeft = newTop;\n            }\n            else {\n                newTopRight = newTop;\n            }\n        }\n        if (missingEndRows > 0) {\n            var bottom = isLeft ? this.bottomLeft : this.bottomRight;\n            var newMaxY = Math.trunc(bottom.getY() + missingEndRows);\n            if (newMaxY >= this.image.getHeight()) {\n                newMaxY = this.image.getHeight() - 1;\n            }\n            var newBottom = new ResultPoint(bottom.getX(), newMaxY);\n            if (isLeft) {\n                newBottomLeft = newBottom;\n            }\n            else {\n                newBottomRight = newBottom;\n            }\n        }\n        return new BoundingBox(this.image, newTopLeft, newBottomLeft, newTopRight, newBottomRight);\n    };\n    BoundingBox.prototype.getMinX = function () {\n        return this.minX;\n    };\n    BoundingBox.prototype.getMaxX = function () {\n        return this.maxX;\n    };\n    BoundingBox.prototype.getMinY = function () {\n        return this.minY;\n    };\n    BoundingBox.prototype.getMaxY = function () {\n        return this.maxY;\n    };\n    BoundingBox.prototype.getTopLeft = function () {\n        return this.topLeft;\n    };\n    BoundingBox.prototype.getTopRight = function () {\n        return this.topRight;\n    };\n    BoundingBox.prototype.getBottomLeft = function () {\n        return this.bottomLeft;\n    };\n    BoundingBox.prototype.getBottomRight = function () {\n        return this.bottomRight;\n    };\n    return BoundingBox;\n}());\nexport default BoundingBox;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,iBAAiB,MAAM,yBAAyB;AACvD;AACA,OAAOC,WAAW,MAAM,mBAAmB;AAC3C;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAE;IACpE,IAAIJ,KAAK,YAAYD,WAAW,EAAE;MAC9B,IAAI,CAACM,aAAa,CAACL,KAAK,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACM,aAAa,CAACN,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,CAAC;IACzE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIL,WAAW,CAACQ,SAAS,CAACD,aAAa,GAAG,UAAUN,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAE;IAC/F,IAAII,eAAe,GAAGP,OAAO,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI;IAC3D,IAAIO,gBAAgB,GAAGN,QAAQ,IAAI,IAAI,IAAIC,WAAW,IAAI,IAAI;IAC9D,IAAII,eAAe,IAAIC,gBAAgB,EAAE;MACrC,MAAM,IAAIZ,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAIW,eAAe,EAAE;MACjBP,OAAO,GAAG,IAAIH,WAAW,CAAC,CAAC,EAAEK,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC;MAC7CR,UAAU,GAAG,IAAIJ,WAAW,CAAC,CAAC,EAAEM,WAAW,CAACM,IAAI,CAAC,CAAC,CAAC;IACvD,CAAC,MACI,IAAID,gBAAgB,EAAE;MACvBN,QAAQ,GAAG,IAAIL,WAAW,CAACE,KAAK,CAACW,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEV,OAAO,CAACS,IAAI,CAAC,CAAC,CAAC;MAChEN,WAAW,GAAG,IAAIN,WAAW,CAACE,KAAK,CAACW,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAET,UAAU,CAACQ,IAAI,CAAC,CAAC,CAAC;IAC1E;IACA,IAAI,CAACV,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACQ,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACd,OAAO,CAACe,IAAI,CAAC,CAAC,EAAEd,UAAU,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC;IACnE,IAAI,CAACC,IAAI,GAAGJ,IAAI,CAACC,KAAK,CAACD,IAAI,CAACK,GAAG,CAACf,QAAQ,CAACa,IAAI,CAAC,CAAC,EAAEZ,WAAW,CAACY,IAAI,CAAC,CAAC,CAAC,CAAC;IACrE,IAAI,CAACG,IAAI,GAAGN,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACd,OAAO,CAACS,IAAI,CAAC,CAAC,EAAEP,QAAQ,CAACO,IAAI,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI,CAACU,IAAI,GAAGP,IAAI,CAACC,KAAK,CAACD,IAAI,CAACK,GAAG,CAAChB,UAAU,CAACQ,IAAI,CAAC,CAAC,EAAEN,WAAW,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;EAC3E,CAAC;EACDX,WAAW,CAACQ,SAAS,CAACF,aAAa,GAAG,UAAUgB,WAAW,EAAE;IACzD,IAAI,CAACrB,KAAK,GAAGqB,WAAW,CAACrB,KAAK;IAC9B,IAAI,CAACC,OAAO,GAAGoB,WAAW,CAACC,UAAU,CAAC,CAAC;IACvC,IAAI,CAACpB,UAAU,GAAGmB,WAAW,CAACE,aAAa,CAAC,CAAC;IAC7C,IAAI,CAACpB,QAAQ,GAAGkB,WAAW,CAACG,WAAW,CAAC,CAAC;IACzC,IAAI,CAACpB,WAAW,GAAGiB,WAAW,CAACI,cAAc,CAAC,CAAC;IAC/C,IAAI,CAACb,IAAI,GAAGS,WAAW,CAACK,OAAO,CAAC,CAAC;IACjC,IAAI,CAACT,IAAI,GAAGI,WAAW,CAACM,OAAO,CAAC,CAAC;IACjC,IAAI,CAACR,IAAI,GAAGE,WAAW,CAACO,OAAO,CAAC,CAAC;IACjC,IAAI,CAACR,IAAI,GAAGC,WAAW,CAACQ,OAAO,CAAC,CAAC;EACrC,CAAC;EACD;AACJ;AACA;EACI9B,WAAW,CAAC+B,KAAK,GAAG,UAAUC,OAAO,EAAEC,QAAQ,EAAE;IAC7C,IAAID,OAAO,IAAI,IAAI,EAAE;MACjB,OAAOC,QAAQ;IACnB;IACA,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAClB,OAAOD,OAAO;IAClB;IACA,OAAO,IAAIhC,WAAW,CAACgC,OAAO,CAAC/B,KAAK,EAAE+B,OAAO,CAAC9B,OAAO,EAAE8B,OAAO,CAAC7B,UAAU,EAAE8B,QAAQ,CAAC7B,QAAQ,EAAE6B,QAAQ,CAAC5B,WAAW,CAAC;EACvH,CAAC;EACD;AACJ;AACA;EACIL,WAAW,CAACQ,SAAS,CAAC0B,cAAc,GAAG,UAAUC,gBAAgB,EAAEC,cAAc,EAAEC,MAAM,EAAE;IACvF,IAAIC,UAAU,GAAG,IAAI,CAACpC,OAAO;IAC7B,IAAIqC,aAAa,GAAG,IAAI,CAACpC,UAAU;IACnC,IAAIqC,WAAW,GAAG,IAAI,CAACpC,QAAQ;IAC/B,IAAIqC,cAAc,GAAG,IAAI,CAACpC,WAAW;IACrC,IAAI8B,gBAAgB,GAAG,CAAC,EAAE;MACtB,IAAIO,KAAK,GAAGL,MAAM,GAAG,IAAI,CAACnC,OAAO,GAAG,IAAI,CAACE,QAAQ;MACjD,IAAIuC,OAAO,GAAG7B,IAAI,CAACC,KAAK,CAAC2B,KAAK,CAAC/B,IAAI,CAAC,CAAC,GAAGwB,gBAAgB,CAAC;MACzD,IAAIQ,OAAO,GAAG,CAAC,EAAE;QACbA,OAAO,GAAG,CAAC;MACf;MACA,IAAIC,MAAM,GAAG,IAAI7C,WAAW,CAAC2C,KAAK,CAACzB,IAAI,CAAC,CAAC,EAAE0B,OAAO,CAAC;MACnD,IAAIN,MAAM,EAAE;QACRC,UAAU,GAAGM,MAAM;MACvB,CAAC,MACI;QACDJ,WAAW,GAAGI,MAAM;MACxB;IACJ;IACA,IAAIR,cAAc,GAAG,CAAC,EAAE;MACpB,IAAIS,MAAM,GAAGR,MAAM,GAAG,IAAI,CAAClC,UAAU,GAAG,IAAI,CAACE,WAAW;MACxD,IAAIyC,OAAO,GAAGhC,IAAI,CAACC,KAAK,CAAC8B,MAAM,CAAClC,IAAI,CAAC,CAAC,GAAGyB,cAAc,CAAC;MACxD,IAAIU,OAAO,IAAI,IAAI,CAAC7C,KAAK,CAAC8C,SAAS,CAAC,CAAC,EAAE;QACnCD,OAAO,GAAG,IAAI,CAAC7C,KAAK,CAAC8C,SAAS,CAAC,CAAC,GAAG,CAAC;MACxC;MACA,IAAIC,SAAS,GAAG,IAAIjD,WAAW,CAAC8C,MAAM,CAAC5B,IAAI,CAAC,CAAC,EAAE6B,OAAO,CAAC;MACvD,IAAIT,MAAM,EAAE;QACRE,aAAa,GAAGS,SAAS;MAC7B,CAAC,MACI;QACDP,cAAc,GAAGO,SAAS;MAC9B;IACJ;IACA,OAAO,IAAIhD,WAAW,CAAC,IAAI,CAACC,KAAK,EAAEqC,UAAU,EAAEC,aAAa,EAAEC,WAAW,EAAEC,cAAc,CAAC;EAC9F,CAAC;EACDzC,WAAW,CAACQ,SAAS,CAACmB,OAAO,GAAG,YAAY;IACxC,OAAO,IAAI,CAACd,IAAI;EACpB,CAAC;EACDb,WAAW,CAACQ,SAAS,CAACoB,OAAO,GAAG,YAAY;IACxC,OAAO,IAAI,CAACV,IAAI;EACpB,CAAC;EACDlB,WAAW,CAACQ,SAAS,CAACqB,OAAO,GAAG,YAAY;IACxC,OAAO,IAAI,CAACT,IAAI;EACpB,CAAC;EACDpB,WAAW,CAACQ,SAAS,CAACsB,OAAO,GAAG,YAAY;IACxC,OAAO,IAAI,CAACT,IAAI;EACpB,CAAC;EACDrB,WAAW,CAACQ,SAAS,CAACe,UAAU,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACrB,OAAO;EACvB,CAAC;EACDF,WAAW,CAACQ,SAAS,CAACiB,WAAW,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACrB,QAAQ;EACxB,CAAC;EACDJ,WAAW,CAACQ,SAAS,CAACgB,aAAa,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACrB,UAAU;EAC1B,CAAC;EACDH,WAAW,CAACQ,SAAS,CAACkB,cAAc,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACrB,WAAW;EAC3B,CAAC;EACD,OAAOL,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}