{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nvar ObjectRenderer = function () {\n  function ObjectRenderer(object, encodings, options) {\n    _classCallCheck(this, ObjectRenderer);\n    this.object = object;\n    this.encodings = encodings;\n    this.options = options;\n  }\n  _createClass(ObjectRenderer, [{\n    key: \"render\",\n    value: function render() {\n      this.object.encodings = this.encodings;\n    }\n  }]);\n  return ObjectRenderer;\n}();\nexports.default = ObjectRenderer;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_classCallCheck", "instance", "TypeError", "O<PERSON><PERSON><PERSON><PERSON>", "object", "encodings", "options", "render", "default"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/renderers/object.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ObjectRenderer = function () {\n\tfunction ObjectRenderer(object, encodings, options) {\n\t\t_classCallCheck(this, ObjectRenderer);\n\n\t\tthis.object = object;\n\t\tthis.encodings = encodings;\n\t\tthis.options = options;\n\t}\n\n\t_createClass(ObjectRenderer, [{\n\t\tkey: \"render\",\n\t\tvalue: function render() {\n\t\t\tthis.object.encodings = this.encodings;\n\t\t}\n\t}]);\n\n\treturn ObjectRenderer;\n}();\n\nexports.default = ObjectRenderer;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,SAASI,eAAeA,CAACC,QAAQ,EAAEL,WAAW,EAAE;EAAE,IAAI,EAAEK,QAAQ,YAAYL,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIM,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,IAAIC,cAAc,GAAG,YAAY;EAChC,SAASA,cAAcA,CAACC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAE;IACnDN,eAAe,CAAC,IAAI,EAAEG,cAAc,CAAC;IAErC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,OAAO,GAAGA,OAAO;EACvB;EAEArB,YAAY,CAACkB,cAAc,EAAE,CAAC;IAC7BR,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAASuB,MAAMA,CAAA,EAAG;MACxB,IAAI,CAACH,MAAM,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS;IACvC;EACD,CAAC,CAAC,CAAC;EAEH,OAAOF,cAAc;AACtB,CAAC,CAAC,CAAC;AAEHpB,OAAO,CAACyB,OAAO,GAAGL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}