{"ast": null, "code": "/*\n * Copyright (C) 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport ResultMetadataType from '../ResultMetadataType';\nimport NotFoundException from '../NotFoundException';\n/**\n * @see UPCEANExtension5Support\n */\nvar UPCEANExtension2Support = /** @class */function () {\n  function UPCEANExtension2Support() {\n    this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n    this.decodeRowStringBuffer = '';\n  }\n  UPCEANExtension2Support.prototype.decodeRow = function (rowNumber, row, extensionStartRange) {\n    var result = this.decodeRowStringBuffer;\n    var end = this.decodeMiddle(row, extensionStartRange, result);\n    var resultString = result.toString();\n    var extensionData = UPCEANExtension2Support.parseExtensionString(resultString);\n    var resultPoints = [new ResultPoint((extensionStartRange[0] + extensionStartRange[1]) / 2.0, rowNumber), new ResultPoint(end, rowNumber)];\n    var extensionResult = new Result(resultString, null, 0, resultPoints, BarcodeFormat.UPC_EAN_EXTENSION, new Date().getTime());\n    if (extensionData != null) {\n      extensionResult.putAllMetadata(extensionData);\n    }\n    return extensionResult;\n  };\n  UPCEANExtension2Support.prototype.decodeMiddle = function (row, startRange, resultString) {\n    var e_1, _a;\n    var counters = this.decodeMiddleCounters;\n    counters[0] = 0;\n    counters[1] = 0;\n    counters[2] = 0;\n    counters[3] = 0;\n    var end = row.getSize();\n    var rowOffset = startRange[1];\n    var checkParity = 0;\n    for (var x = 0; x < 2 && rowOffset < end; x++) {\n      var bestMatch = AbstractUPCEANReader.decodeDigit(row, counters, rowOffset, AbstractUPCEANReader.L_AND_G_PATTERNS);\n      resultString += String.fromCharCode('0'.charCodeAt(0) + bestMatch % 10);\n      try {\n        for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n          var counter = counters_1_1.value;\n          rowOffset += counter;\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      if (bestMatch >= 10) {\n        checkParity |= 1 << 1 - x;\n      }\n      if (x !== 1) {\n        // Read off separator if not last\n        rowOffset = row.getNextSet(rowOffset);\n        rowOffset = row.getNextUnset(rowOffset);\n      }\n    }\n    if (resultString.length !== 2) {\n      throw new NotFoundException();\n    }\n    if (parseInt(resultString.toString()) % 4 !== checkParity) {\n      throw new NotFoundException();\n    }\n    return rowOffset;\n  };\n  UPCEANExtension2Support.parseExtensionString = function (raw) {\n    if (raw.length !== 2) {\n      return null;\n    }\n    return new Map([[ResultMetadataType.ISSUE_NUMBER, parseInt(raw)]]);\n  };\n  return UPCEANExtension2Support;\n}();\nexport default UPCEANExtension2Support;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BarcodeFormat", "AbstractUPCEANReader", "Result", "ResultPoint", "ResultMetadataType", "NotFoundException", "UPCEANExtension2Support", "decodeMiddleCounters", "Int32Array", "from", "decodeRowStringBuffer", "prototype", "decodeRow", "rowNumber", "row", "extensionStartRange", "result", "end", "decodeMiddle", "resultString", "toString", "extensionData", "parseExtensionString", "resultPoints", "extensionResult", "UPC_EAN_EXTENSION", "Date", "getTime", "putAllMetadata", "startRange", "e_1", "_a", "counters", "getSize", "rowOffset", "checkParity", "x", "bestMatch", "decodeDigit", "L_AND_G_PATTERNS", "String", "fromCharCode", "charCodeAt", "counters_1", "counters_1_1", "counter", "e_1_1", "error", "return", "getNextSet", "getNextUnset", "parseInt", "raw", "Map", "ISSUE_NUMBER"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/UPCEANExtension2Support.js"], "sourcesContent": ["/*\n * Copyright (C) 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BarcodeFormat from '../BarcodeFormat';\nimport AbstractUPCEANReader from './AbstractUPCEANReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\nimport ResultMetadataType from '../ResultMetadataType';\nimport NotFoundException from '../NotFoundException';\n/**\n * @see UPCEANExtension5Support\n */\nvar UPCEANExtension2Support = /** @class */ (function () {\n    function UPCEANExtension2Support() {\n        this.decodeMiddleCounters = Int32Array.from([0, 0, 0, 0]);\n        this.decodeRowStringBuffer = '';\n    }\n    UPCEANExtension2Support.prototype.decodeRow = function (rowNumber, row, extensionStartRange) {\n        var result = this.decodeRowStringBuffer;\n        var end = this.decodeMiddle(row, extensionStartRange, result);\n        var resultString = result.toString();\n        var extensionData = UPCEANExtension2Support.parseExtensionString(resultString);\n        var resultPoints = [\n            new ResultPoint((extensionStartRange[0] + extensionStartRange[1]) / 2.0, rowNumber),\n            new ResultPoint(end, rowNumber)\n        ];\n        var extensionResult = new Result(resultString, null, 0, resultPoints, BarcodeFormat.UPC_EAN_EXTENSION, new Date().getTime());\n        if (extensionData != null) {\n            extensionResult.putAllMetadata(extensionData);\n        }\n        return extensionResult;\n    };\n    UPCEANExtension2Support.prototype.decodeMiddle = function (row, startRange, resultString) {\n        var e_1, _a;\n        var counters = this.decodeMiddleCounters;\n        counters[0] = 0;\n        counters[1] = 0;\n        counters[2] = 0;\n        counters[3] = 0;\n        var end = row.getSize();\n        var rowOffset = startRange[1];\n        var checkParity = 0;\n        for (var x = 0; x < 2 && rowOffset < end; x++) {\n            var bestMatch = AbstractUPCEANReader.decodeDigit(row, counters, rowOffset, AbstractUPCEANReader.L_AND_G_PATTERNS);\n            resultString += String.fromCharCode(('0'.charCodeAt(0) + bestMatch % 10));\n            try {\n                for (var counters_1 = (e_1 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    rowOffset += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            if (bestMatch >= 10) {\n                checkParity |= 1 << (1 - x);\n            }\n            if (x !== 1) {\n                // Read off separator if not last\n                rowOffset = row.getNextSet(rowOffset);\n                rowOffset = row.getNextUnset(rowOffset);\n            }\n        }\n        if (resultString.length !== 2) {\n            throw new NotFoundException();\n        }\n        if (parseInt(resultString.toString()) % 4 !== checkParity) {\n            throw new NotFoundException();\n        }\n        return rowOffset;\n    };\n    UPCEANExtension2Support.parseExtensionString = function (raw) {\n        if (raw.length !== 2) {\n            return null;\n        }\n        return new Map([[ResultMetadataType.ISSUE_NUMBER, parseInt(raw)]]);\n    };\n    return UPCEANExtension2Support;\n}());\nexport default UPCEANExtension2Support;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,kBAAkB,MAAM,uBAAuB;AACtD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA;AACA;AACA,IAAIC,uBAAuB,GAAG,aAAe,YAAY;EACrD,SAASA,uBAAuBA,CAAA,EAAG;IAC/B,IAAI,CAACC,oBAAoB,GAAGC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IACzD,IAAI,CAACC,qBAAqB,GAAG,EAAE;EACnC;EACAJ,uBAAuB,CAACK,SAAS,CAACC,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,mBAAmB,EAAE;IACzF,IAAIC,MAAM,GAAG,IAAI,CAACN,qBAAqB;IACvC,IAAIO,GAAG,GAAG,IAAI,CAACC,YAAY,CAACJ,GAAG,EAAEC,mBAAmB,EAAEC,MAAM,CAAC;IAC7D,IAAIG,YAAY,GAAGH,MAAM,CAACI,QAAQ,CAAC,CAAC;IACpC,IAAIC,aAAa,GAAGf,uBAAuB,CAACgB,oBAAoB,CAACH,YAAY,CAAC;IAC9E,IAAII,YAAY,GAAG,CACf,IAAIpB,WAAW,CAAC,CAACY,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,IAAI,GAAG,EAAEF,SAAS,CAAC,EACnF,IAAIV,WAAW,CAACc,GAAG,EAAEJ,SAAS,CAAC,CAClC;IACD,IAAIW,eAAe,GAAG,IAAItB,MAAM,CAACiB,YAAY,EAAE,IAAI,EAAE,CAAC,EAAEI,YAAY,EAAEvB,aAAa,CAACyB,iBAAiB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;IAC5H,IAAIN,aAAa,IAAI,IAAI,EAAE;MACvBG,eAAe,CAACI,cAAc,CAACP,aAAa,CAAC;IACjD;IACA,OAAOG,eAAe;EAC1B,CAAC;EACDlB,uBAAuB,CAACK,SAAS,CAACO,YAAY,GAAG,UAAUJ,GAAG,EAAEe,UAAU,EAAEV,YAAY,EAAE;IACtF,IAAIW,GAAG,EAAEC,EAAE;IACX,IAAIC,QAAQ,GAAG,IAAI,CAACzB,oBAAoB;IACxCyB,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACfA,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;IACf,IAAIf,GAAG,GAAGH,GAAG,CAACmB,OAAO,CAAC,CAAC;IACvB,IAAIC,SAAS,GAAGL,UAAU,CAAC,CAAC,CAAC;IAC7B,IAAIM,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAIF,SAAS,GAAGjB,GAAG,EAAEmB,CAAC,EAAE,EAAE;MAC3C,IAAIC,SAAS,GAAGpC,oBAAoB,CAACqC,WAAW,CAACxB,GAAG,EAAEkB,QAAQ,EAAEE,SAAS,EAAEjC,oBAAoB,CAACsC,gBAAgB,CAAC;MACjHpB,YAAY,IAAIqB,MAAM,CAACC,YAAY,CAAE,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGL,SAAS,GAAG,EAAG,CAAC;MACzE,IAAI;QACA,KAAK,IAAIM,UAAU,IAAIb,GAAG,GAAG,KAAK,CAAC,EAAE3C,QAAQ,CAAC6C,QAAQ,CAAC,CAAC,EAAEY,YAAY,GAAGD,UAAU,CAAC/C,IAAI,CAAC,CAAC,EAAE,CAACgD,YAAY,CAAC9C,IAAI,EAAE8C,YAAY,GAAGD,UAAU,CAAC/C,IAAI,CAAC,CAAC,EAAE;UAC9I,IAAIiD,OAAO,GAAGD,YAAY,CAAC/C,KAAK;UAChCqC,SAAS,IAAIW,OAAO;QACxB;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAEhB,GAAG,GAAG;UAAEiB,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,YAAY,IAAI,CAACA,YAAY,CAAC9C,IAAI,KAAKiC,EAAE,GAAGY,UAAU,CAACK,MAAM,CAAC,EAAEjB,EAAE,CAACrC,IAAI,CAACiD,UAAU,CAAC;QAC3F,CAAC,SACO;UAAE,IAAIb,GAAG,EAAE,MAAMA,GAAG,CAACiB,KAAK;QAAE;MACxC;MACA,IAAIV,SAAS,IAAI,EAAE,EAAE;QACjBF,WAAW,IAAI,CAAC,IAAK,CAAC,GAAGC,CAAE;MAC/B;MACA,IAAIA,CAAC,KAAK,CAAC,EAAE;QACT;QACAF,SAAS,GAAGpB,GAAG,CAACmC,UAAU,CAACf,SAAS,CAAC;QACrCA,SAAS,GAAGpB,GAAG,CAACoC,YAAY,CAAChB,SAAS,CAAC;MAC3C;IACJ;IACA,IAAIf,YAAY,CAACxB,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIU,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI8C,QAAQ,CAAChC,YAAY,CAACC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAKe,WAAW,EAAE;MACvD,MAAM,IAAI9B,iBAAiB,CAAC,CAAC;IACjC;IACA,OAAO6B,SAAS;EACpB,CAAC;EACD5B,uBAAuB,CAACgB,oBAAoB,GAAG,UAAU8B,GAAG,EAAE;IAC1D,IAAIA,GAAG,CAACzD,MAAM,KAAK,CAAC,EAAE;MAClB,OAAO,IAAI;IACf;IACA,OAAO,IAAI0D,GAAG,CAAC,CAAC,CAACjD,kBAAkB,CAACkD,YAAY,EAAEH,QAAQ,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;EACtE,CAAC;EACD,OAAO9C,uBAAuB;AAClC,CAAC,CAAC,CAAE;AACJ,eAAeA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}