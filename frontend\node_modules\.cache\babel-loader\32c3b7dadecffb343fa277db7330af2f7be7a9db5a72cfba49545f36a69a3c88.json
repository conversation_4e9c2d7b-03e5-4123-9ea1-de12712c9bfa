{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes CodaBar barcodes. </p>\n *\n * <AUTHOR> @dodobelieve\n * @see CodaBarReader\n */\nvar CodaBarReader = /** @class */function (_super) {\n  __extends(CodaBarReader, _super);\n  function CodaBarReader() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.CODA_BAR_CHAR_SET = {\n      nnnnnww: '0',\n      nnnnwwn: '1',\n      nnnwnnw: '2',\n      wwnnnnn: '3',\n      nnwnnwn: '4',\n      wnnnnwn: '5',\n      nwnnnnw: '6',\n      nwnnwnn: '7',\n      nwwnnnn: '8',\n      wnnwnnn: '9',\n      nnnwwnn: '-',\n      nnwwnnn: '$',\n      wnnnwnw: ':',\n      wnwnnnw: '/',\n      wnwnwnn: '.',\n      nnwwwww: '+',\n      nnwwnwn: 'A',\n      nwnwnnw: 'B',\n      nnnwnww: 'C',\n      nnnwwwn: 'D'\n    };\n    return _this;\n  }\n  CodaBarReader.prototype.decodeRow = function (rowNumber, row, hints) {\n    var validRowData = this.getValidRowData(row);\n    if (!validRowData) throw new NotFoundException();\n    var retStr = this.codaBarDecodeRow(validRowData.row);\n    if (!retStr) throw new NotFoundException();\n    return new Result(retStr, null, 0, [new ResultPoint(validRowData.left, rowNumber), new ResultPoint(validRowData.right, rowNumber)], BarcodeFormat.CODABAR, new Date().getTime());\n  };\n  /**\n   * converts bit array to valid data array(lengths of black bits and white bits)\n   * @param row bit array to convert\n   */\n  CodaBarReader.prototype.getValidRowData = function (row) {\n    var booleanArr = row.toArray();\n    var startIndex = booleanArr.indexOf(true);\n    if (startIndex === -1) return null;\n    var lastIndex = booleanArr.lastIndexOf(true);\n    if (lastIndex <= startIndex) return null;\n    booleanArr = booleanArr.slice(startIndex, lastIndex + 1);\n    var result = [];\n    var lastBit = booleanArr[0];\n    var bitLength = 1;\n    for (var i = 1; i < booleanArr.length; i++) {\n      if (booleanArr[i] === lastBit) {\n        bitLength++;\n      } else {\n        lastBit = booleanArr[i];\n        result.push(bitLength);\n        bitLength = 1;\n      }\n    }\n    result.push(bitLength);\n    // CodaBar code data valid\n    if (result.length < 23 && (result.length + 1) % 8 !== 0) return null;\n    return {\n      row: result,\n      left: startIndex,\n      right: lastIndex\n    };\n  };\n  /**\n   * decode codabar code\n   * @param row row to cecode\n   */\n  CodaBarReader.prototype.codaBarDecodeRow = function (row) {\n    var code = [];\n    var barThreshold = Math.ceil(row.reduce(function (pre, item) {\n      return (pre + item) / 2;\n    }, 0));\n    // Read one encoded character at a time.\n    while (row.length > 0) {\n      var seg = row.splice(0, 8).splice(0, 7);\n      var key = seg.map(function (len) {\n        return len < barThreshold ? 'n' : 'w';\n      }).join('');\n      if (this.CODA_BAR_CHAR_SET[key] === undefined) return null;\n      code.push(this.CODA_BAR_CHAR_SET[key]);\n    }\n    var strCode = code.join('');\n    if (this.validCodaBarString(strCode)) return strCode;\n    return null;\n  };\n  /**\n   * check if the string is a CodaBar string\n   * @param src string to determine\n   */\n  CodaBarReader.prototype.validCodaBarString = function (src) {\n    var reg = /^[A-D].{1,}[A-D]$/;\n    return reg.test(src);\n  };\n  return CodaBarReader;\n}(OneDReader);\nexport default CodaBarReader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "BarcodeFormat", "NotFoundException", "OneDReader", "Result", "ResultPoint", "CodaBarReader", "_super", "_this", "apply", "arguments", "CODA_BAR_CHAR_SET", "nnnnnww", "nnnnwwn", "nnnwnnw", "wwnnnnn", "nnwnnwn", "wnnnnwn", "nwnnnnw", "nwnnwnn", "nwwnnnn", "wnnwnnn", "nnnwwnn", "nnwwnnn", "wnnnwnw", "wnwnnnw", "wnwnwnn", "nnwwwww", "nnwwnwn", "nwnwnnw", "nnnwnww", "nnnwwwn", "decodeRow", "rowNumber", "row", "hints", "validRowData", "getValidRowData", "retStr", "codaBarDecodeRow", "left", "right", "CODABAR", "Date", "getTime", "booleanArr", "toArray", "startIndex", "indexOf", "lastIndex", "lastIndexOf", "slice", "result", "lastBit", "bitLength", "i", "length", "push", "code", "bar<PERSON><PERSON><PERSON><PERSON>", "Math", "ceil", "reduce", "pre", "item", "seg", "splice", "key", "map", "len", "join", "undefined", "strCode", "validCodaBarString", "src", "reg", "test"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/CodaBarReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes CodaBar barcodes. </p>\n *\n * <AUTHOR> @dodobelieve\n * @see CodaBarReader\n */\nvar CodaBarReader = /** @class */ (function (_super) {\n    __extends(CodaBarReader, _super);\n    function CodaBarReader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.CODA_BAR_CHAR_SET = {\n            nnnnnww: '0',\n            nnnnwwn: '1',\n            nnnwnnw: '2',\n            wwnnnnn: '3',\n            nnwnnwn: '4',\n            wnnnnwn: '5',\n            nwnnnnw: '6',\n            nwnnwnn: '7',\n            nwwnnnn: '8',\n            wnnwnnn: '9',\n            nnnwwnn: '-',\n            nnwwnnn: '$',\n            wnnnwnw: ':',\n            wnwnnnw: '/',\n            wnwnwnn: '.',\n            nnwwwww: '+',\n            nnwwnwn: 'A',\n            nwnwnnw: 'B',\n            nnnwnww: 'C',\n            nnnwwwn: 'D'\n        };\n        return _this;\n    }\n    CodaBarReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var validRowData = this.getValidRowData(row);\n        if (!validRowData)\n            throw new NotFoundException();\n        var retStr = this.codaBarDecodeRow(validRowData.row);\n        if (!retStr)\n            throw new NotFoundException();\n        return new Result(retStr, null, 0, [new ResultPoint(validRowData.left, rowNumber), new ResultPoint(validRowData.right, rowNumber)], BarcodeFormat.CODABAR, new Date().getTime());\n    };\n    /**\n     * converts bit array to valid data array(lengths of black bits and white bits)\n     * @param row bit array to convert\n     */\n    CodaBarReader.prototype.getValidRowData = function (row) {\n        var booleanArr = row.toArray();\n        var startIndex = booleanArr.indexOf(true);\n        if (startIndex === -1)\n            return null;\n        var lastIndex = booleanArr.lastIndexOf(true);\n        if (lastIndex <= startIndex)\n            return null;\n        booleanArr = booleanArr.slice(startIndex, lastIndex + 1);\n        var result = [];\n        var lastBit = booleanArr[0];\n        var bitLength = 1;\n        for (var i = 1; i < booleanArr.length; i++) {\n            if (booleanArr[i] === lastBit) {\n                bitLength++;\n            }\n            else {\n                lastBit = booleanArr[i];\n                result.push(bitLength);\n                bitLength = 1;\n            }\n        }\n        result.push(bitLength);\n        // CodaBar code data valid\n        if (result.length < 23 && (result.length + 1) % 8 !== 0)\n            return null;\n        return { row: result, left: startIndex, right: lastIndex };\n    };\n    /**\n     * decode codabar code\n     * @param row row to cecode\n     */\n    CodaBarReader.prototype.codaBarDecodeRow = function (row) {\n        var code = [];\n        var barThreshold = Math.ceil(row.reduce(function (pre, item) { return (pre + item) / 2; }, 0));\n        // Read one encoded character at a time.\n        while (row.length > 0) {\n            var seg = row.splice(0, 8).splice(0, 7);\n            var key = seg.map(function (len) { return (len < barThreshold ? 'n' : 'w'); }).join('');\n            if (this.CODA_BAR_CHAR_SET[key] === undefined)\n                return null;\n            code.push(this.CODA_BAR_CHAR_SET[key]);\n        }\n        var strCode = code.join('');\n        if (this.validCodaBarString(strCode))\n            return strCode;\n        return null;\n    };\n    /**\n     * check if the string is a CodaBar string\n     * @param src string to determine\n     */\n    CodaBarReader.prototype.validCodaBarString = function (src) {\n        var reg = /^[A-D].{1,}[A-D]$/;\n        return reg.test(src);\n    };\n    return CodaBarReader;\n}(OneDReader));\nexport default CodaBarReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,WAAW,MAAM,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,aAAe,UAAUC,MAAM,EAAE;EACjDpB,SAAS,CAACmB,aAAa,EAAEC,MAAM,CAAC;EAChC,SAASD,aAAaA,CAAA,EAAG;IACrB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,iBAAiB,GAAG;MACtBC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE,GAAG;MACZC,OAAO,EAAE;IACb,CAAC;IACD,OAAOvB,KAAK;EAChB;EACAF,aAAa,CAACP,SAAS,CAACiC,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAE;IACjE,IAAIC,YAAY,GAAG,IAAI,CAACC,eAAe,CAACH,GAAG,CAAC;IAC5C,IAAI,CAACE,YAAY,EACb,MAAM,IAAIlC,iBAAiB,CAAC,CAAC;IACjC,IAAIoC,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACH,YAAY,CAACF,GAAG,CAAC;IACpD,IAAI,CAACI,MAAM,EACP,MAAM,IAAIpC,iBAAiB,CAAC,CAAC;IACjC,OAAO,IAAIE,MAAM,CAACkC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,IAAIjC,WAAW,CAAC+B,YAAY,CAACI,IAAI,EAAEP,SAAS,CAAC,EAAE,IAAI5B,WAAW,CAAC+B,YAAY,CAACK,KAAK,EAAER,SAAS,CAAC,CAAC,EAAEhC,aAAa,CAACyC,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EACpL,CAAC;EACD;AACJ;AACA;AACA;EACItC,aAAa,CAACP,SAAS,CAACsC,eAAe,GAAG,UAAUH,GAAG,EAAE;IACrD,IAAIW,UAAU,GAAGX,GAAG,CAACY,OAAO,CAAC,CAAC;IAC9B,IAAIC,UAAU,GAAGF,UAAU,CAACG,OAAO,CAAC,IAAI,CAAC;IACzC,IAAID,UAAU,KAAK,CAAC,CAAC,EACjB,OAAO,IAAI;IACf,IAAIE,SAAS,GAAGJ,UAAU,CAACK,WAAW,CAAC,IAAI,CAAC;IAC5C,IAAID,SAAS,IAAIF,UAAU,EACvB,OAAO,IAAI;IACfF,UAAU,GAAGA,UAAU,CAACM,KAAK,CAACJ,UAAU,EAAEE,SAAS,GAAG,CAAC,CAAC;IACxD,IAAIG,MAAM,GAAG,EAAE;IACf,IAAIC,OAAO,GAAGR,UAAU,CAAC,CAAC,CAAC;IAC3B,IAAIS,SAAS,GAAG,CAAC;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,UAAU,CAACW,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,IAAIV,UAAU,CAACU,CAAC,CAAC,KAAKF,OAAO,EAAE;QAC3BC,SAAS,EAAE;MACf,CAAC,MACI;QACDD,OAAO,GAAGR,UAAU,CAACU,CAAC,CAAC;QACvBH,MAAM,CAACK,IAAI,CAACH,SAAS,CAAC;QACtBA,SAAS,GAAG,CAAC;MACjB;IACJ;IACAF,MAAM,CAACK,IAAI,CAACH,SAAS,CAAC;IACtB;IACA,IAAIF,MAAM,CAACI,MAAM,GAAG,EAAE,IAAI,CAACJ,MAAM,CAACI,MAAM,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EACnD,OAAO,IAAI;IACf,OAAO;MAAEtB,GAAG,EAAEkB,MAAM;MAAEZ,IAAI,EAAEO,UAAU;MAAEN,KAAK,EAAEQ;IAAU,CAAC;EAC9D,CAAC;EACD;AACJ;AACA;AACA;EACI3C,aAAa,CAACP,SAAS,CAACwC,gBAAgB,GAAG,UAAUL,GAAG,EAAE;IACtD,IAAIwB,IAAI,GAAG,EAAE;IACb,IAAIC,YAAY,GAAGC,IAAI,CAACC,IAAI,CAAC3B,GAAG,CAAC4B,MAAM,CAAC,UAAUC,GAAG,EAAEC,IAAI,EAAE;MAAE,OAAO,CAACD,GAAG,GAAGC,IAAI,IAAI,CAAC;IAAE,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9F;IACA,OAAO9B,GAAG,CAACsB,MAAM,GAAG,CAAC,EAAE;MACnB,IAAIS,GAAG,GAAG/B,GAAG,CAACgC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACA,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MACvC,IAAIC,GAAG,GAAGF,GAAG,CAACG,GAAG,CAAC,UAAUC,GAAG,EAAE;QAAE,OAAQA,GAAG,GAAGV,YAAY,GAAG,GAAG,GAAG,GAAG;MAAG,CAAC,CAAC,CAACW,IAAI,CAAC,EAAE,CAAC;MACvF,IAAI,IAAI,CAAC3D,iBAAiB,CAACwD,GAAG,CAAC,KAAKI,SAAS,EACzC,OAAO,IAAI;MACfb,IAAI,CAACD,IAAI,CAAC,IAAI,CAAC9C,iBAAiB,CAACwD,GAAG,CAAC,CAAC;IAC1C;IACA,IAAIK,OAAO,GAAGd,IAAI,CAACY,IAAI,CAAC,EAAE,CAAC;IAC3B,IAAI,IAAI,CAACG,kBAAkB,CAACD,OAAO,CAAC,EAChC,OAAOA,OAAO;IAClB,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;EACIlE,aAAa,CAACP,SAAS,CAAC0E,kBAAkB,GAAG,UAAUC,GAAG,EAAE;IACxD,IAAIC,GAAG,GAAG,mBAAmB;IAC7B,OAAOA,GAAG,CAACC,IAAI,CAACF,GAAG,CAAC;EACxB,CAAC;EACD,OAAOpE,aAAa;AACxB,CAAC,CAACH,UAAU,CAAE;AACd,eAAeG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}