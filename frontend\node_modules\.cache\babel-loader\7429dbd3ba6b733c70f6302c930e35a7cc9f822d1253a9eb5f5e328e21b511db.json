{"ast": null, "code": "import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nlet warnedOnceNotValidView = false;\nexport function useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view: inView,\n  views,\n  autoFocus,\n  focusedView: inFocusedView,\n  onFocusedViewChange\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceNotValidView) {\n      if (inView != null && !views.includes(inView)) {\n        console.warn(`MUI X: \\`view=\"${inView}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n      if (inView == null && openTo != null && !views.includes(openTo)) {\n        console.warn(`MUI X: \\`openTo=\"${openTo}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n    }\n  }\n  const previousOpenTo = React.useRef(openTo);\n  const previousViews = React.useRef(views);\n  const defaultView = React.useRef(views.includes(openTo) ? openTo : views[0]);\n  const [view, setView] = useControlled({\n    name: 'useViews',\n    state: 'view',\n    controlled: inView,\n    default: defaultView.current\n  });\n  const defaultFocusedView = React.useRef(autoFocus ? view : null);\n  const [focusedView, setFocusedView] = useControlled({\n    name: 'useViews',\n    state: 'focusedView',\n    controlled: inFocusedView,\n    default: defaultFocusedView.current\n  });\n  React.useEffect(() => {\n    // Update the current view when `openTo` or `views` props change\n    if (previousOpenTo.current && previousOpenTo.current !== openTo || previousViews.current && previousViews.current.some(previousView => !views.includes(previousView))) {\n      setView(views.includes(openTo) ? openTo : views[0]);\n      previousViews.current = views;\n      previousOpenTo.current = openTo;\n    }\n  }, [openTo, setView, view, views]);\n  const viewIndex = views.indexOf(view);\n  const previousView = views[viewIndex - 1] ?? null;\n  const nextView = views[viewIndex + 1] ?? null;\n  const handleFocusedViewChange = useEventCallback((viewToFocus, hasFocus) => {\n    if (hasFocus) {\n      // Focus event\n      setFocusedView(viewToFocus);\n    } else {\n      // Blur event\n      setFocusedView(prevFocusedView => viewToFocus === prevFocusedView ? null : prevFocusedView // If false the blur is due to view switching\n      );\n    }\n    onFocusedViewChange?.(viewToFocus, hasFocus);\n  });\n  const handleChangeView = useEventCallback(newView => {\n    // always keep the focused view in sync\n    handleFocusedViewChange(newView, true);\n    if (newView === view) {\n      return;\n    }\n    setView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  });\n  const goToNextView = useEventCallback(() => {\n    if (nextView) {\n      handleChangeView(nextView);\n    }\n  });\n  const setValueAndGoToNextView = useEventCallback((value, currentViewSelectionState, selectedView) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const hasMoreViews = selectedView ?\n    // handles case like `DateTimePicker`, where a view might return a `finish` selection state\n    // but when it's not the final view given all `views` -> overall selection state should be `partial`.\n    views.indexOf(selectedView) < views.length - 1 : Boolean(nextView);\n    const globalSelectionState = isSelectionFinishedOnCurrentView && hasMoreViews ? 'partial' : currentViewSelectionState;\n    onChange(value, globalSelectionState, selectedView);\n    // Detects if the selected view is not the active one.\n    // Can happen if multiple views are displayed, like in `DesktopDateTimePicker` or `MultiSectionDigitalClock`.\n    if (selectedView && selectedView !== view) {\n      const nextViewAfterSelected = views[views.indexOf(selectedView) + 1];\n      if (nextViewAfterSelected) {\n        // move to next view after the selected one\n        handleChangeView(nextViewAfterSelected);\n      }\n    } else if (isSelectionFinishedOnCurrentView) {\n      goToNextView();\n    }\n  });\n  return {\n    view,\n    setView: handleChangeView,\n    focusedView,\n    setFocusedView: handleFocusedViewChange,\n    nextView,\n    previousView,\n    // Always return up-to-date default view instead of the initial one (i.e. defaultView.current)\n    defaultView: views.includes(openTo) ? openTo : views[0],\n    goToNextView,\n    setValueAndGoToNextView\n  };\n}", "map": {"version": 3, "names": ["React", "useEventCallback", "unstable_useControlled", "useControlled", "warnedOnceNotValidView", "useViews", "onChange", "onViewChange", "openTo", "view", "inView", "views", "autoFocus", "focused<PERSON>iew", "inFocusedView", "onFocusedViewChange", "process", "env", "NODE_ENV", "includes", "console", "warn", "join", "previousOpenTo", "useRef", "previousViews", "defaultView", "<PERSON><PERSON><PERSON><PERSON>", "name", "state", "controlled", "default", "current", "defaultFocusedView", "setFocusedView", "useEffect", "some", "previousView", "viewIndex", "indexOf", "next<PERSON>iew", "handleFocusedViewChange", "viewToFocus", "hasFocus", "prevFocusedView", "handleChangeView", "newView", "goToNextView", "setValueAndGoToNextView", "value", "currentViewSelectionState", "<PERSON><PERSON><PERSON><PERSON>", "isSelectionFinishedOnCurrentView", "hasMoreViews", "length", "Boolean", "globalSelectionState", "nextViewAfterSelected"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useViews.js"], "sourcesContent": ["import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { unstable_useControlled as useControlled } from '@mui/utils';\nlet warnedOnceNotValidView = false;\nexport function useViews({\n  onChange,\n  onViewChange,\n  openTo,\n  view: inView,\n  views,\n  autoFocus,\n  focusedView: inFocusedView,\n  onFocusedViewChange\n}) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnceNotValidView) {\n      if (inView != null && !views.includes(inView)) {\n        console.warn(`MUI X: \\`view=\"${inView}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n      if (inView == null && openTo != null && !views.includes(openTo)) {\n        console.warn(`MUI X: \\`openTo=\"${openTo}\"\\` is not a valid prop.`, `It must be an element of \\`views=[\"${views.join('\", \"')}\"]\\`.`);\n        warnedOnceNotValidView = true;\n      }\n    }\n  }\n  const previousOpenTo = React.useRef(openTo);\n  const previousViews = React.useRef(views);\n  const defaultView = React.useRef(views.includes(openTo) ? openTo : views[0]);\n  const [view, setView] = useControlled({\n    name: 'useViews',\n    state: 'view',\n    controlled: inView,\n    default: defaultView.current\n  });\n  const defaultFocusedView = React.useRef(autoFocus ? view : null);\n  const [focusedView, setFocusedView] = useControlled({\n    name: 'useViews',\n    state: 'focusedView',\n    controlled: inFocusedView,\n    default: defaultFocusedView.current\n  });\n  React.useEffect(() => {\n    // Update the current view when `openTo` or `views` props change\n    if (previousOpenTo.current && previousOpenTo.current !== openTo || previousViews.current && previousViews.current.some(previousView => !views.includes(previousView))) {\n      setView(views.includes(openTo) ? openTo : views[0]);\n      previousViews.current = views;\n      previousOpenTo.current = openTo;\n    }\n  }, [openTo, setView, view, views]);\n  const viewIndex = views.indexOf(view);\n  const previousView = views[viewIndex - 1] ?? null;\n  const nextView = views[viewIndex + 1] ?? null;\n  const handleFocusedViewChange = useEventCallback((viewToFocus, hasFocus) => {\n    if (hasFocus) {\n      // Focus event\n      setFocusedView(viewToFocus);\n    } else {\n      // Blur event\n      setFocusedView(prevFocusedView => viewToFocus === prevFocusedView ? null : prevFocusedView // If false the blur is due to view switching\n      );\n    }\n    onFocusedViewChange?.(viewToFocus, hasFocus);\n  });\n  const handleChangeView = useEventCallback(newView => {\n    // always keep the focused view in sync\n    handleFocusedViewChange(newView, true);\n    if (newView === view) {\n      return;\n    }\n    setView(newView);\n    if (onViewChange) {\n      onViewChange(newView);\n    }\n  });\n  const goToNextView = useEventCallback(() => {\n    if (nextView) {\n      handleChangeView(nextView);\n    }\n  });\n  const setValueAndGoToNextView = useEventCallback((value, currentViewSelectionState, selectedView) => {\n    const isSelectionFinishedOnCurrentView = currentViewSelectionState === 'finish';\n    const hasMoreViews = selectedView ?\n    // handles case like `DateTimePicker`, where a view might return a `finish` selection state\n    // but when it's not the final view given all `views` -> overall selection state should be `partial`.\n    views.indexOf(selectedView) < views.length - 1 : Boolean(nextView);\n    const globalSelectionState = isSelectionFinishedOnCurrentView && hasMoreViews ? 'partial' : currentViewSelectionState;\n    onChange(value, globalSelectionState, selectedView);\n    // Detects if the selected view is not the active one.\n    // Can happen if multiple views are displayed, like in `DesktopDateTimePicker` or `MultiSectionDigitalClock`.\n    if (selectedView && selectedView !== view) {\n      const nextViewAfterSelected = views[views.indexOf(selectedView) + 1];\n      if (nextViewAfterSelected) {\n        // move to next view after the selected one\n        handleChangeView(nextViewAfterSelected);\n      }\n    } else if (isSelectionFinishedOnCurrentView) {\n      goToNextView();\n    }\n  });\n  return {\n    view,\n    setView: handleChangeView,\n    focusedView,\n    setFocusedView: handleFocusedViewChange,\n    nextView,\n    previousView,\n    // Always return up-to-date default view instead of the initial one (i.e. defaultView.current)\n    defaultView: views.includes(openTo) ? openTo : views[0],\n    goToNextView,\n    setValueAndGoToNextView\n  };\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,sBAAsB,IAAIC,aAAa,QAAQ,YAAY;AACpE,IAAIC,sBAAsB,GAAG,KAAK;AAClC,OAAO,SAASC,QAAQA,CAAC;EACvBC,QAAQ;EACRC,YAAY;EACZC,MAAM;EACNC,IAAI,EAAEC,MAAM;EACZC,KAAK;EACLC,SAAS;EACTC,WAAW,EAAEC,aAAa;EAC1BC;AACF,CAAC,EAAE;EACD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACd,sBAAsB,EAAE;MAC3B,IAAIM,MAAM,IAAI,IAAI,IAAI,CAACC,KAAK,CAACQ,QAAQ,CAACT,MAAM,CAAC,EAAE;QAC7CU,OAAO,CAACC,IAAI,CAAC,kBAAkBX,MAAM,0BAA0B,EAAE,sCAAsCC,KAAK,CAACW,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACjIlB,sBAAsB,GAAG,IAAI;MAC/B;MACA,IAAIM,MAAM,IAAI,IAAI,IAAIF,MAAM,IAAI,IAAI,IAAI,CAACG,KAAK,CAACQ,QAAQ,CAACX,MAAM,CAAC,EAAE;QAC/DY,OAAO,CAACC,IAAI,CAAC,oBAAoBb,MAAM,0BAA0B,EAAE,sCAAsCG,KAAK,CAACW,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACnIlB,sBAAsB,GAAG,IAAI;MAC/B;IACF;EACF;EACA,MAAMmB,cAAc,GAAGvB,KAAK,CAACwB,MAAM,CAAChB,MAAM,CAAC;EAC3C,MAAMiB,aAAa,GAAGzB,KAAK,CAACwB,MAAM,CAACb,KAAK,CAAC;EACzC,MAAMe,WAAW,GAAG1B,KAAK,CAACwB,MAAM,CAACb,KAAK,CAACQ,QAAQ,CAACX,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAC;EAC5E,MAAM,CAACF,IAAI,EAAEkB,OAAO,CAAC,GAAGxB,aAAa,CAAC;IACpCyB,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAEpB,MAAM;IAClBqB,OAAO,EAAEL,WAAW,CAACM;EACvB,CAAC,CAAC;EACF,MAAMC,kBAAkB,GAAGjC,KAAK,CAACwB,MAAM,CAACZ,SAAS,GAAGH,IAAI,GAAG,IAAI,CAAC;EAChE,MAAM,CAACI,WAAW,EAAEqB,cAAc,CAAC,GAAG/B,aAAa,CAAC;IAClDyB,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAEhB,aAAa;IACzBiB,OAAO,EAAEE,kBAAkB,CAACD;EAC9B,CAAC,CAAC;EACFhC,KAAK,CAACmC,SAAS,CAAC,MAAM;IACpB;IACA,IAAIZ,cAAc,CAACS,OAAO,IAAIT,cAAc,CAACS,OAAO,KAAKxB,MAAM,IAAIiB,aAAa,CAACO,OAAO,IAAIP,aAAa,CAACO,OAAO,CAACI,IAAI,CAACC,YAAY,IAAI,CAAC1B,KAAK,CAACQ,QAAQ,CAACkB,YAAY,CAAC,CAAC,EAAE;MACrKV,OAAO,CAAChB,KAAK,CAACQ,QAAQ,CAACX,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAC;MACnDc,aAAa,CAACO,OAAO,GAAGrB,KAAK;MAC7BY,cAAc,CAACS,OAAO,GAAGxB,MAAM;IACjC;EACF,CAAC,EAAE,CAACA,MAAM,EAAEmB,OAAO,EAAElB,IAAI,EAAEE,KAAK,CAAC,CAAC;EAClC,MAAM2B,SAAS,GAAG3B,KAAK,CAAC4B,OAAO,CAAC9B,IAAI,CAAC;EACrC,MAAM4B,YAAY,GAAG1B,KAAK,CAAC2B,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI;EACjD,MAAME,QAAQ,GAAG7B,KAAK,CAAC2B,SAAS,GAAG,CAAC,CAAC,IAAI,IAAI;EAC7C,MAAMG,uBAAuB,GAAGxC,gBAAgB,CAAC,CAACyC,WAAW,EAAEC,QAAQ,KAAK;IAC1E,IAAIA,QAAQ,EAAE;MACZ;MACAT,cAAc,CAACQ,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL;MACAR,cAAc,CAACU,eAAe,IAAIF,WAAW,KAAKE,eAAe,GAAG,IAAI,GAAGA,eAAe,CAAC;MAC3F,CAAC;IACH;IACA7B,mBAAmB,GAAG2B,WAAW,EAAEC,QAAQ,CAAC;EAC9C,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAG5C,gBAAgB,CAAC6C,OAAO,IAAI;IACnD;IACAL,uBAAuB,CAACK,OAAO,EAAE,IAAI,CAAC;IACtC,IAAIA,OAAO,KAAKrC,IAAI,EAAE;MACpB;IACF;IACAkB,OAAO,CAACmB,OAAO,CAAC;IAChB,IAAIvC,YAAY,EAAE;MAChBA,YAAY,CAACuC,OAAO,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMC,YAAY,GAAG9C,gBAAgB,CAAC,MAAM;IAC1C,IAAIuC,QAAQ,EAAE;MACZK,gBAAgB,CAACL,QAAQ,CAAC;IAC5B;EACF,CAAC,CAAC;EACF,MAAMQ,uBAAuB,GAAG/C,gBAAgB,CAAC,CAACgD,KAAK,EAAEC,yBAAyB,EAAEC,YAAY,KAAK;IACnG,MAAMC,gCAAgC,GAAGF,yBAAyB,KAAK,QAAQ;IAC/E,MAAMG,YAAY,GAAGF,YAAY;IACjC;IACA;IACAxC,KAAK,CAAC4B,OAAO,CAACY,YAAY,CAAC,GAAGxC,KAAK,CAAC2C,MAAM,GAAG,CAAC,GAAGC,OAAO,CAACf,QAAQ,CAAC;IAClE,MAAMgB,oBAAoB,GAAGJ,gCAAgC,IAAIC,YAAY,GAAG,SAAS,GAAGH,yBAAyB;IACrH5C,QAAQ,CAAC2C,KAAK,EAAEO,oBAAoB,EAAEL,YAAY,CAAC;IACnD;IACA;IACA,IAAIA,YAAY,IAAIA,YAAY,KAAK1C,IAAI,EAAE;MACzC,MAAMgD,qBAAqB,GAAG9C,KAAK,CAACA,KAAK,CAAC4B,OAAO,CAACY,YAAY,CAAC,GAAG,CAAC,CAAC;MACpE,IAAIM,qBAAqB,EAAE;QACzB;QACAZ,gBAAgB,CAACY,qBAAqB,CAAC;MACzC;IACF,CAAC,MAAM,IAAIL,gCAAgC,EAAE;MAC3CL,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;EACF,OAAO;IACLtC,IAAI;IACJkB,OAAO,EAAEkB,gBAAgB;IACzBhC,WAAW;IACXqB,cAAc,EAAEO,uBAAuB;IACvCD,QAAQ;IACRH,YAAY;IACZ;IACAX,WAAW,EAAEf,KAAK,CAACQ,QAAQ,CAACX,MAAM,CAAC,GAAGA,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC;IACvDoC,YAAY;IACZC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}