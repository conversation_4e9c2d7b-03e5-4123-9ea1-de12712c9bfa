{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { PickersActionBar } from \"../PickersActionBar/index.js\";\nimport { getPickersLayoutUtilityClass } from \"./pickersLayoutClasses.js\";\nimport { PickersShortcuts } from \"../PickersShortcuts/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction toolbarHasView(toolbarProps) {\n  return toolbarProps.view !== null;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    isLandscape\n  } = ownerState;\n  const slots = {\n    root: ['root', isLandscape && 'landscape'],\n    contentWrapper: ['contentWrapper'],\n    toolbar: ['toolbar'],\n    actionBar: ['actionBar'],\n    tabs: ['tabs'],\n    landscape: ['landscape'],\n    shortcuts: ['shortcuts']\n  };\n  return composeClasses(slots, getPickersLayoutUtilityClass, classes);\n};\nconst usePickerLayout = props => {\n  const {\n    wrapperVariant,\n    onAccept,\n    onClear,\n    onCancel,\n    onSetToday,\n    view,\n    views,\n    onViewChange,\n    value,\n    onChange,\n    onSelectShortcut,\n    isValid,\n    isLandscape,\n    disabled,\n    readOnly,\n    children,\n    slots,\n    slotProps\n    // TODO: Remove this \"as\" hack. It get introduced to mark `value` prop in PickersLayoutProps as not required.\n    // The true type should be\n    // - For pickers value: TDate | null\n    // - For range pickers value: [TDate | null, TDate | null]\n  } = props;\n  const classes = useUtilityClasses(props);\n\n  // Action bar\n  const ActionBar = slots?.actionBar ?? PickersActionBar;\n  const actionBarProps = useSlotProps({\n    elementType: ActionBar,\n    externalSlotProps: slotProps?.actionBar,\n    additionalProps: {\n      onAccept,\n      onClear,\n      onCancel,\n      onSetToday,\n      actions: wrapperVariant === 'desktop' ? [] : ['cancel', 'accept']\n    },\n    className: classes.actionBar,\n    ownerState: _extends({}, props, {\n      wrapperVariant\n    })\n  });\n  const actionBar = /*#__PURE__*/_jsx(ActionBar, _extends({}, actionBarProps));\n\n  // Toolbar\n  const Toolbar = slots?.toolbar;\n  const toolbarProps = useSlotProps({\n    elementType: Toolbar,\n    externalSlotProps: slotProps?.toolbar,\n    additionalProps: {\n      isLandscape,\n      onChange,\n      value,\n      view,\n      onViewChange,\n      views,\n      disabled,\n      readOnly\n    },\n    className: classes.toolbar,\n    ownerState: _extends({}, props, {\n      wrapperVariant\n    })\n  });\n  const toolbar = toolbarHasView(toolbarProps) && !!Toolbar ? /*#__PURE__*/_jsx(Toolbar, _extends({}, toolbarProps)) : null;\n\n  // Content\n  const content = children;\n\n  // Tabs\n  const Tabs = slots?.tabs;\n  const tabs = view && Tabs ? /*#__PURE__*/_jsx(Tabs, _extends({\n    view: view,\n    onViewChange: onViewChange,\n    className: classes.tabs\n  }, slotProps?.tabs)) : null;\n\n  // Shortcuts\n  const Shortcuts = slots?.shortcuts ?? PickersShortcuts;\n  const shortcutsProps = useSlotProps({\n    elementType: Shortcuts,\n    externalSlotProps: slotProps?.shortcuts,\n    additionalProps: {\n      isValid,\n      isLandscape,\n      onChange: onSelectShortcut\n    },\n    className: classes.shortcuts,\n    ownerState: {\n      isValid,\n      isLandscape,\n      onChange: onSelectShortcut,\n      wrapperVariant\n    }\n  });\n  const shortcuts = view && !!Shortcuts ? /*#__PURE__*/_jsx(Shortcuts, _extends({}, shortcutsProps)) : null;\n  return {\n    toolbar,\n    content,\n    tabs,\n    actionBar,\n    shortcuts\n  };\n};\nexport default usePickerLayout;", "map": {"version": 3, "names": ["_extends", "React", "useSlotProps", "composeClasses", "PickersActionBar", "getPickersLayoutUtilityClass", "PickersShortcuts", "jsx", "_jsx", "toolbarHasView", "toolbarProps", "view", "useUtilityClasses", "ownerState", "classes", "isLandscape", "slots", "root", "contentWrapper", "toolbar", "actionBar", "tabs", "landscape", "shortcuts", "usePickerLayout", "props", "wrapperVariant", "onAccept", "onClear", "onCancel", "onSetToday", "views", "onViewChange", "value", "onChange", "onSelectShortcut", "<PERSON><PERSON><PERSON><PERSON>", "disabled", "readOnly", "children", "slotProps", "ActionBar", "actionBarProps", "elementType", "externalSlotProps", "additionalProps", "actions", "className", "<PERSON><PERSON><PERSON>", "content", "Tabs", "Shortcuts", "shortcutsProps"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/PickersLayout/usePickerLayout.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { PickersActionBar } from \"../PickersActionBar/index.js\";\nimport { getPickersLayoutUtilityClass } from \"./pickersLayoutClasses.js\";\nimport { PickersShortcuts } from \"../PickersShortcuts/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction toolbarHasView(toolbarProps) {\n  return toolbarProps.view !== null;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    isLandscape\n  } = ownerState;\n  const slots = {\n    root: ['root', isLandscape && 'landscape'],\n    contentWrapper: ['contentWrapper'],\n    toolbar: ['toolbar'],\n    actionBar: ['actionBar'],\n    tabs: ['tabs'],\n    landscape: ['landscape'],\n    shortcuts: ['shortcuts']\n  };\n  return composeClasses(slots, getPickersLayoutUtilityClass, classes);\n};\nconst usePickerLayout = props => {\n  const {\n    wrapperVariant,\n    onAccept,\n    onClear,\n    onCancel,\n    onSetToday,\n    view,\n    views,\n    onViewChange,\n    value,\n    onChange,\n    onSelectShortcut,\n    isValid,\n    isLandscape,\n    disabled,\n    readOnly,\n    children,\n    slots,\n    slotProps\n    // TODO: Remove this \"as\" hack. It get introduced to mark `value` prop in PickersLayoutProps as not required.\n    // The true type should be\n    // - For pickers value: TDate | null\n    // - For range pickers value: [TDate | null, TDate | null]\n  } = props;\n  const classes = useUtilityClasses(props);\n\n  // Action bar\n  const ActionBar = slots?.actionBar ?? PickersActionBar;\n  const actionBarProps = useSlotProps({\n    elementType: ActionBar,\n    externalSlotProps: slotProps?.actionBar,\n    additionalProps: {\n      onAccept,\n      onClear,\n      onCancel,\n      onSetToday,\n      actions: wrapperVariant === 'desktop' ? [] : ['cancel', 'accept']\n    },\n    className: classes.actionBar,\n    ownerState: _extends({}, props, {\n      wrapperVariant\n    })\n  });\n  const actionBar = /*#__PURE__*/_jsx(ActionBar, _extends({}, actionBarProps));\n\n  // Toolbar\n  const Toolbar = slots?.toolbar;\n  const toolbarProps = useSlotProps({\n    elementType: Toolbar,\n    externalSlotProps: slotProps?.toolbar,\n    additionalProps: {\n      isLandscape,\n      onChange,\n      value,\n      view,\n      onViewChange,\n      views,\n      disabled,\n      readOnly\n    },\n    className: classes.toolbar,\n    ownerState: _extends({}, props, {\n      wrapperVariant\n    })\n  });\n  const toolbar = toolbarHasView(toolbarProps) && !!Toolbar ? /*#__PURE__*/_jsx(Toolbar, _extends({}, toolbarProps)) : null;\n\n  // Content\n  const content = children;\n\n  // Tabs\n  const Tabs = slots?.tabs;\n  const tabs = view && Tabs ? /*#__PURE__*/_jsx(Tabs, _extends({\n    view: view,\n    onViewChange: onViewChange,\n    className: classes.tabs\n  }, slotProps?.tabs)) : null;\n\n  // Shortcuts\n  const Shortcuts = slots?.shortcuts ?? PickersShortcuts;\n  const shortcutsProps = useSlotProps({\n    elementType: Shortcuts,\n    externalSlotProps: slotProps?.shortcuts,\n    additionalProps: {\n      isValid,\n      isLandscape,\n      onChange: onSelectShortcut\n    },\n    className: classes.shortcuts,\n    ownerState: {\n      isValid,\n      isLandscape,\n      onChange: onSelectShortcut,\n      wrapperVariant\n    }\n  });\n  const shortcuts = view && !!Shortcuts ? /*#__PURE__*/_jsx(Shortcuts, _extends({}, shortcutsProps)) : null;\n  return {\n    toolbar,\n    content,\n    tabs,\n    actionBar,\n    shortcuts\n  };\n};\nexport default usePickerLayout;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,4BAA4B,QAAQ,2BAA2B;AACxE,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,cAAcA,CAACC,YAAY,EAAE;EACpC,OAAOA,YAAY,CAACC,IAAI,KAAK,IAAI;AACnC;AACA,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,WAAW,IAAI,WAAW,CAAC;IAC1CG,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,SAAS,EAAE,CAAC,WAAW;EACzB,CAAC;EACD,OAAOpB,cAAc,CAACa,KAAK,EAAEX,4BAA4B,EAAES,OAAO,CAAC;AACrE,CAAC;AACD,MAAMU,eAAe,GAAGC,KAAK,IAAI;EAC/B,MAAM;IACJC,cAAc;IACdC,QAAQ;IACRC,OAAO;IACPC,QAAQ;IACRC,UAAU;IACVnB,IAAI;IACJoB,KAAK;IACLC,YAAY;IACZC,KAAK;IACLC,QAAQ;IACRC,gBAAgB;IAChBC,OAAO;IACPrB,WAAW;IACXsB,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRvB,KAAK;IACLwB;IACA;IACA;IACA;IACA;EACF,CAAC,GAAGf,KAAK;EACT,MAAMX,OAAO,GAAGF,iBAAiB,CAACa,KAAK,CAAC;;EAExC;EACA,MAAMgB,SAAS,GAAGzB,KAAK,EAAEI,SAAS,IAAIhB,gBAAgB;EACtD,MAAMsC,cAAc,GAAGxC,YAAY,CAAC;IAClCyC,WAAW,EAAEF,SAAS;IACtBG,iBAAiB,EAAEJ,SAAS,EAAEpB,SAAS;IACvCyB,eAAe,EAAE;MACflB,QAAQ;MACRC,OAAO;MACPC,QAAQ;MACRC,UAAU;MACVgB,OAAO,EAAEpB,cAAc,KAAK,SAAS,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE,QAAQ;IAClE,CAAC;IACDqB,SAAS,EAAEjC,OAAO,CAACM,SAAS;IAC5BP,UAAU,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;MAC9BC;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAMN,SAAS,GAAG,aAAaZ,IAAI,CAACiC,SAAS,EAAEzC,QAAQ,CAAC,CAAC,CAAC,EAAE0C,cAAc,CAAC,CAAC;;EAE5E;EACA,MAAMM,OAAO,GAAGhC,KAAK,EAAEG,OAAO;EAC9B,MAAMT,YAAY,GAAGR,YAAY,CAAC;IAChCyC,WAAW,EAAEK,OAAO;IACpBJ,iBAAiB,EAAEJ,SAAS,EAAErB,OAAO;IACrC0B,eAAe,EAAE;MACf9B,WAAW;MACXmB,QAAQ;MACRD,KAAK;MACLtB,IAAI;MACJqB,YAAY;MACZD,KAAK;MACLM,QAAQ;MACRC;IACF,CAAC;IACDS,SAAS,EAAEjC,OAAO,CAACK,OAAO;IAC1BN,UAAU,EAAEb,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;MAC9BC;IACF,CAAC;EACH,CAAC,CAAC;EACF,MAAMP,OAAO,GAAGV,cAAc,CAACC,YAAY,CAAC,IAAI,CAAC,CAACsC,OAAO,GAAG,aAAaxC,IAAI,CAACwC,OAAO,EAAEhD,QAAQ,CAAC,CAAC,CAAC,EAAEU,YAAY,CAAC,CAAC,GAAG,IAAI;;EAEzH;EACA,MAAMuC,OAAO,GAAGV,QAAQ;;EAExB;EACA,MAAMW,IAAI,GAAGlC,KAAK,EAAEK,IAAI;EACxB,MAAMA,IAAI,GAAGV,IAAI,IAAIuC,IAAI,GAAG,aAAa1C,IAAI,CAAC0C,IAAI,EAAElD,QAAQ,CAAC;IAC3DW,IAAI,EAAEA,IAAI;IACVqB,YAAY,EAAEA,YAAY;IAC1Be,SAAS,EAAEjC,OAAO,CAACO;EACrB,CAAC,EAAEmB,SAAS,EAAEnB,IAAI,CAAC,CAAC,GAAG,IAAI;;EAE3B;EACA,MAAM8B,SAAS,GAAGnC,KAAK,EAAEO,SAAS,IAAIjB,gBAAgB;EACtD,MAAM8C,cAAc,GAAGlD,YAAY,CAAC;IAClCyC,WAAW,EAAEQ,SAAS;IACtBP,iBAAiB,EAAEJ,SAAS,EAAEjB,SAAS;IACvCsB,eAAe,EAAE;MACfT,OAAO;MACPrB,WAAW;MACXmB,QAAQ,EAAEC;IACZ,CAAC;IACDY,SAAS,EAAEjC,OAAO,CAACS,SAAS;IAC5BV,UAAU,EAAE;MACVuB,OAAO;MACPrB,WAAW;MACXmB,QAAQ,EAAEC,gBAAgB;MAC1BT;IACF;EACF,CAAC,CAAC;EACF,MAAMH,SAAS,GAAGZ,IAAI,IAAI,CAAC,CAACwC,SAAS,GAAG,aAAa3C,IAAI,CAAC2C,SAAS,EAAEnD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,cAAc,CAAC,CAAC,GAAG,IAAI;EACzG,OAAO;IACLjC,OAAO;IACP8B,OAAO;IACP5B,IAAI;IACJD,SAAS;IACTG;EACF,CAAC;AACH,CAAC;AACD,eAAeC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}