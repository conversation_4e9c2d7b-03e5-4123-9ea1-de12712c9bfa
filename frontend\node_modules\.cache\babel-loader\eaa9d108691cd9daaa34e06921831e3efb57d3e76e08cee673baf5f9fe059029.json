{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Code128Reader from './Code128Reader';\nimport Code39Reader from './Code39Reader';\nimport Code93Reader from './Code93Reader';\nimport ITFReader from './ITFReader';\nimport MultiFormatUPCEANReader from './MultiFormatUPCEANReader';\nimport OneDReader from './OneDReader';\nimport CodaBarReader from './CodaBarReader';\nimport RSSExpandedReader from './rss/expanded/RSSExpandedReader';\nimport RSS14Reader from './rss/RSS14Reader';\n/**\n * <AUTHOR> Switkin <<EMAIL>>\n * <AUTHOR> Owen\n */\nvar MultiFormatOneDReader = /** @class */function (_super) {\n  __extends(MultiFormatOneDReader, _super);\n  function MultiFormatOneDReader(hints) {\n    var _this = _super.call(this) || this;\n    _this.readers = [];\n    var possibleFormats = !hints ? null : hints.get(DecodeHintType.POSSIBLE_FORMATS);\n    var useCode39CheckDigit = hints && hints.get(DecodeHintType.ASSUME_CODE_39_CHECK_DIGIT) !== undefined;\n    var useCode39ExtendedMode = hints && hints.get(DecodeHintType.ENABLE_CODE_39_EXTENDED_MODE) !== undefined;\n    if (possibleFormats) {\n      if (possibleFormats.includes(BarcodeFormat.EAN_13) || possibleFormats.includes(BarcodeFormat.UPC_A) || possibleFormats.includes(BarcodeFormat.EAN_8) || possibleFormats.includes(BarcodeFormat.UPC_E)) {\n        _this.readers.push(new MultiFormatUPCEANReader(hints));\n      }\n      if (possibleFormats.includes(BarcodeFormat.CODE_39)) {\n        _this.readers.push(new Code39Reader(useCode39CheckDigit, useCode39ExtendedMode));\n      }\n      if (possibleFormats.includes(BarcodeFormat.CODE_93)) {\n        _this.readers.push(new Code93Reader());\n      }\n      if (possibleFormats.includes(BarcodeFormat.CODE_128)) {\n        _this.readers.push(new Code128Reader());\n      }\n      if (possibleFormats.includes(BarcodeFormat.ITF)) {\n        _this.readers.push(new ITFReader());\n      }\n      if (possibleFormats.includes(BarcodeFormat.CODABAR)) {\n        _this.readers.push(new CodaBarReader());\n      }\n      if (possibleFormats.includes(BarcodeFormat.RSS_14)) {\n        _this.readers.push(new RSS14Reader());\n      }\n      if (possibleFormats.includes(BarcodeFormat.RSS_EXPANDED)) {\n        console.warn('RSS Expanded reader IS NOT ready for production yet! use at your own risk.');\n        _this.readers.push(new RSSExpandedReader());\n      }\n    }\n    if (_this.readers.length === 0) {\n      _this.readers.push(new MultiFormatUPCEANReader(hints));\n      _this.readers.push(new Code39Reader());\n      // this.readers.push(new CodaBarReader());\n      _this.readers.push(new Code93Reader());\n      _this.readers.push(new MultiFormatUPCEANReader(hints));\n      _this.readers.push(new Code128Reader());\n      _this.readers.push(new ITFReader());\n      _this.readers.push(new RSS14Reader());\n      // this.readers.push(new RSSExpandedReader());\n    }\n    return _this;\n  }\n  // @Override\n  MultiFormatOneDReader.prototype.decodeRow = function (rowNumber, row, hints) {\n    for (var i = 0; i < this.readers.length; i++) {\n      try {\n        return this.readers[i].decodeRow(rowNumber, row, hints);\n      } catch (re) {\n        // continue\n      }\n    }\n    throw new NotFoundException();\n  };\n  // @Override\n  MultiFormatOneDReader.prototype.reset = function () {\n    this.readers.forEach(function (reader) {\n      return reader.reset();\n    });\n  };\n  return MultiFormatOneDReader;\n}(OneDReader);\nexport default MultiFormatOneDReader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "BarcodeFormat", "DecodeHintType", "NotFoundException", "Code128Reader", "Code39Reader", "Code93<PERSON><PERSON>er", "ITFReader", "MultiFormatUPCEANReader", "OneDReader", "CodaBarReader", "RSSExpandedReader", "RSS14Reader", "MultiFormatOneDReader", "_super", "hints", "_this", "call", "readers", "possibleFormats", "get", "POSSIBLE_FORMATS", "useCode39CheckDigit", "ASSUME_CODE_39_CHECK_DIGIT", "undefined", "useCode39ExtendedMode", "ENABLE_CODE_39_EXTENDED_MODE", "includes", "EAN_13", "UPC_A", "EAN_8", "UPC_E", "push", "CODE_39", "CODE_93", "CODE_128", "ITF", "CODABAR", "RSS_14", "RSS_EXPANDED", "console", "warn", "length", "decodeRow", "rowNumber", "row", "i", "re", "reset", "for<PERSON>ach", "reader"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/MultiFormatOneDReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport DecodeHintType from '../DecodeHintType';\nimport NotFoundException from '../NotFoundException';\nimport Code128Reader from './Code128Reader';\nimport Code39Reader from './Code39Reader';\nimport Code93Reader from './Code93Reader';\nimport ITFReader from './ITFReader';\nimport MultiFormatUPCEANReader from './MultiFormatUPCEANReader';\nimport OneDReader from './OneDReader';\nimport CodaBarReader from './CodaBarReader';\nimport RSSExpandedReader from './rss/expanded/RSSExpandedReader';\nimport RSS14Reader from './rss/RSS14Reader';\n/**\n * <AUTHOR> Switkin <<EMAIL>>\n * <AUTHOR> Owen\n */\nvar MultiFormatOneDReader = /** @class */ (function (_super) {\n    __extends(MultiFormatOneDReader, _super);\n    function MultiFormatOneDReader(hints) {\n        var _this = _super.call(this) || this;\n        _this.readers = [];\n        var possibleFormats = !hints ? null : hints.get(DecodeHintType.POSSIBLE_FORMATS);\n        var useCode39CheckDigit = hints && hints.get(DecodeHintType.ASSUME_CODE_39_CHECK_DIGIT) !== undefined;\n        var useCode39ExtendedMode = hints && hints.get(DecodeHintType.ENABLE_CODE_39_EXTENDED_MODE) !== undefined;\n        if (possibleFormats) {\n            if (possibleFormats.includes(BarcodeFormat.EAN_13) ||\n                possibleFormats.includes(BarcodeFormat.UPC_A) ||\n                possibleFormats.includes(BarcodeFormat.EAN_8) ||\n                possibleFormats.includes(BarcodeFormat.UPC_E)) {\n                _this.readers.push(new MultiFormatUPCEANReader(hints));\n            }\n            if (possibleFormats.includes(BarcodeFormat.CODE_39)) {\n                _this.readers.push(new Code39Reader(useCode39CheckDigit, useCode39ExtendedMode));\n            }\n            if (possibleFormats.includes(BarcodeFormat.CODE_93)) {\n                _this.readers.push(new Code93Reader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.CODE_128)) {\n                _this.readers.push(new Code128Reader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.ITF)) {\n                _this.readers.push(new ITFReader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.CODABAR)) {\n                _this.readers.push(new CodaBarReader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.RSS_14)) {\n                _this.readers.push(new RSS14Reader());\n            }\n            if (possibleFormats.includes(BarcodeFormat.RSS_EXPANDED)) {\n                console.warn('RSS Expanded reader IS NOT ready for production yet! use at your own risk.');\n                _this.readers.push(new RSSExpandedReader());\n            }\n        }\n        if (_this.readers.length === 0) {\n            _this.readers.push(new MultiFormatUPCEANReader(hints));\n            _this.readers.push(new Code39Reader());\n            // this.readers.push(new CodaBarReader());\n            _this.readers.push(new Code93Reader());\n            _this.readers.push(new MultiFormatUPCEANReader(hints));\n            _this.readers.push(new Code128Reader());\n            _this.readers.push(new ITFReader());\n            _this.readers.push(new RSS14Reader());\n            // this.readers.push(new RSSExpandedReader());\n        }\n        return _this;\n    }\n    // @Override\n    MultiFormatOneDReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        for (var i = 0; i < this.readers.length; i++) {\n            try {\n                return this.readers[i].decodeRow(rowNumber, row, hints);\n            }\n            catch (re) {\n                // continue\n            }\n        }\n        throw new NotFoundException();\n    };\n    // @Override\n    MultiFormatOneDReader.prototype.reset = function () {\n        this.readers.forEach(function (reader) { return reader.reset(); });\n    };\n    return MultiFormatOneDReader;\n}(OneDReader));\nexport default MultiFormatOneDReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,kCAAkC;AAChE,OAAOC,WAAW,MAAM,mBAAmB;AAC3C;AACA;AACA;AACA;AACA,IAAIC,qBAAqB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACzD3B,SAAS,CAAC0B,qBAAqB,EAAEC,MAAM,CAAC;EACxC,SAASD,qBAAqBA,CAACE,KAAK,EAAE;IAClC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACE,OAAO,GAAG,EAAE;IAClB,IAAIC,eAAe,GAAG,CAACJ,KAAK,GAAG,IAAI,GAAGA,KAAK,CAACK,GAAG,CAAClB,cAAc,CAACmB,gBAAgB,CAAC;IAChF,IAAIC,mBAAmB,GAAGP,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAClB,cAAc,CAACqB,0BAA0B,CAAC,KAAKC,SAAS;IACrG,IAAIC,qBAAqB,GAAGV,KAAK,IAAIA,KAAK,CAACK,GAAG,CAAClB,cAAc,CAACwB,4BAA4B,CAAC,KAAKF,SAAS;IACzG,IAAIL,eAAe,EAAE;MACjB,IAAIA,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAAC2B,MAAM,CAAC,IAC9CT,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAAC4B,KAAK,CAAC,IAC7CV,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAAC6B,KAAK,CAAC,IAC7CX,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAAC8B,KAAK,CAAC,EAAE;QAC/Cf,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAIxB,uBAAuB,CAACO,KAAK,CAAC,CAAC;MAC1D;MACA,IAAII,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAACgC,OAAO,CAAC,EAAE;QACjDjB,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAI3B,YAAY,CAACiB,mBAAmB,EAAEG,qBAAqB,CAAC,CAAC;MACpF;MACA,IAAIN,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAACiC,OAAO,CAAC,EAAE;QACjDlB,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAI1B,YAAY,CAAC,CAAC,CAAC;MAC1C;MACA,IAAIa,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAACkC,QAAQ,CAAC,EAAE;QAClDnB,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAI5B,aAAa,CAAC,CAAC,CAAC;MAC3C;MACA,IAAIe,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAACmC,GAAG,CAAC,EAAE;QAC7CpB,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAIzB,SAAS,CAAC,CAAC,CAAC;MACvC;MACA,IAAIY,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAACoC,OAAO,CAAC,EAAE;QACjDrB,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAItB,aAAa,CAAC,CAAC,CAAC;MAC3C;MACA,IAAIS,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAACqC,MAAM,CAAC,EAAE;QAChDtB,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAIpB,WAAW,CAAC,CAAC,CAAC;MACzC;MACA,IAAIO,eAAe,CAACQ,QAAQ,CAAC1B,aAAa,CAACsC,YAAY,CAAC,EAAE;QACtDC,OAAO,CAACC,IAAI,CAAC,4EAA4E,CAAC;QAC1FzB,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAIrB,iBAAiB,CAAC,CAAC,CAAC;MAC/C;IACJ;IACA,IAAIK,KAAK,CAACE,OAAO,CAACwB,MAAM,KAAK,CAAC,EAAE;MAC5B1B,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAIxB,uBAAuB,CAACO,KAAK,CAAC,CAAC;MACtDC,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAI3B,YAAY,CAAC,CAAC,CAAC;MACtC;MACAW,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAI1B,YAAY,CAAC,CAAC,CAAC;MACtCU,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAIxB,uBAAuB,CAACO,KAAK,CAAC,CAAC;MACtDC,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAI5B,aAAa,CAAC,CAAC,CAAC;MACvCY,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAIzB,SAAS,CAAC,CAAC,CAAC;MACnCS,KAAK,CAACE,OAAO,CAACc,IAAI,CAAC,IAAIpB,WAAW,CAAC,CAAC,CAAC;MACrC;IACJ;IACA,OAAOI,KAAK;EAChB;EACA;EACAH,qBAAqB,CAACd,SAAS,CAAC4C,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAE9B,KAAK,EAAE;IACzE,KAAK,IAAI+B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5B,OAAO,CAACwB,MAAM,EAAEI,CAAC,EAAE,EAAE;MAC1C,IAAI;QACA,OAAO,IAAI,CAAC5B,OAAO,CAAC4B,CAAC,CAAC,CAACH,SAAS,CAACC,SAAS,EAAEC,GAAG,EAAE9B,KAAK,CAAC;MAC3D,CAAC,CACD,OAAOgC,EAAE,EAAE;QACP;MAAA;IAER;IACA,MAAM,IAAI5C,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACD;EACAU,qBAAqB,CAACd,SAAS,CAACiD,KAAK,GAAG,YAAY;IAChD,IAAI,CAAC9B,OAAO,CAAC+B,OAAO,CAAC,UAAUC,MAAM,EAAE;MAAE,OAAOA,MAAM,CAACF,KAAK,CAAC,CAAC;IAAE,CAAC,CAAC;EACtE,CAAC;EACD,OAAOnC,qBAAqB;AAChC,CAAC,CAACJ,UAAU,CAAE;AACd,eAAeI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}