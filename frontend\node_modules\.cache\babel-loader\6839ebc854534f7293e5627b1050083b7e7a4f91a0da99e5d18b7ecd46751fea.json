{"ast": null, "code": "import GeneralAppIdDecoder from './GeneralAppIdDecoder';\nvar AbstractExpandedDecoder = /** @class */function () {\n  function AbstractExpandedDecoder(information) {\n    this.information = information;\n    this.generalDecoder = new GeneralAppIdDecoder(information);\n  }\n  AbstractExpandedDecoder.prototype.getInformation = function () {\n    return this.information;\n  };\n  AbstractExpandedDecoder.prototype.getGeneralDecoder = function () {\n    return this.generalDecoder;\n  };\n  return AbstractExpandedDecoder;\n}();\nexport default AbstractExpandedDecoder;", "map": {"version": 3, "names": ["GeneralAppIdDecoder", "AbstractExpandedDecoder", "information", "generalDecoder", "prototype", "getInformation", "getGeneralDecoder"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoder.js"], "sourcesContent": ["import GeneralAppIdDecoder from './GeneralAppIdDecoder';\nvar AbstractExpandedDecoder = /** @class */ (function () {\n    function AbstractExpandedDecoder(information) {\n        this.information = information;\n        this.generalDecoder = new GeneralAppIdDecoder(information);\n    }\n    AbstractExpandedDecoder.prototype.getInformation = function () {\n        return this.information;\n    };\n    AbstractExpandedDecoder.prototype.getGeneralDecoder = function () {\n        return this.generalDecoder;\n    };\n    return AbstractExpandedDecoder;\n}());\nexport default AbstractExpandedDecoder;\n"], "mappings": "AAAA,OAAOA,mBAAmB,MAAM,uBAAuB;AACvD,IAAIC,uBAAuB,GAAG,aAAe,YAAY;EACrD,SAASA,uBAAuBA,CAACC,WAAW,EAAE;IAC1C,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAG,IAAIH,mBAAmB,CAACE,WAAW,CAAC;EAC9D;EACAD,uBAAuB,CAACG,SAAS,CAACC,cAAc,GAAG,YAAY;IAC3D,OAAO,IAAI,CAACH,WAAW;EAC3B,CAAC;EACDD,uBAAuB,CAACG,SAAS,CAACE,iBAAiB,GAAG,YAAY;IAC9D,OAAO,IAAI,CAACH,cAAc;EAC9B,CAAC;EACD,OAAOF,uBAAuB;AAClC,CAAC,CAAC,CAAE;AACJ,eAAeA,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}