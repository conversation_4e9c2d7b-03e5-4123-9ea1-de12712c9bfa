[{"D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\index.js": "1", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\theme.js": "2", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\App.js": "3", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CustomerList.js": "4", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageItems.js": "5", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageOrders.js": "6", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\VendorList.js": "7", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManagePurchaseInvoices.js": "8", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PurchaseInvoiceList.js": "9", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManagePurchaseReturns.js": "10", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\Layout.js": "11", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageCategories.js": "12", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\GeneralLedger.js": "13", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\StockTracking.js": "14", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PurchaseReturnList.js": "15", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageSalesReturns.js": "16", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BarcodeGenerator.js": "17", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\SalesReturnList.js": "18", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\SubsidiaryLedger.js": "19", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ChartOfAccounts.js": "20", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddCustomerPage.js": "21", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\Dashboard.js": "22", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddVendorPage.js": "23", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\RolesPage.js": "24", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\LoginPage.js": "25", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\UsersPage.js": "26", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\CompanySetupPage.js": "27", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\SettingsPage.js": "28", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ContactsPage.js": "29", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\UserManagementPage.js": "30", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\BankAccountsPage.js": "31", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\PurchasesPage.js": "32", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\OrderListPage.js": "33", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\SalesPage.js": "34", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ItemsPage.js": "35", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\BankPaymentsPage.js": "36", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AccountingPage.js": "37", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddBankAccountPage.js": "38", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\EditCustomerDialog.js": "39", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CustomerDetailView.js": "40", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\TableOptions.js": "41", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PermissionButton.js": "42", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\VendorDetailView.js": "43", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ItemDialog.js": "44", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\EditVendorDialog.js": "45", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CategoryDialog.js": "46", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\numberUtils.js": "47", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\axiosConfig.js": "48", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\permissions.js": "49", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\exportUtils.js": "50", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\formatUtils.js": "51", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\accountsDiagnostics.js": "52", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\hooks\\usePermissionDialog.js": "53", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CompanySettings.js": "54", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankAccounts.js": "55", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\Breadcrumb.js": "56", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankPaymentVoucherForm.js": "57", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\OrderList.js": "58", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankAccountDialog.js": "59"}, {"size": 487, "mtime": *************, "results": "60", "hashOfConfig": "61"}, {"size": 1711, "mtime": *************, "results": "62", "hashOfConfig": "61"}, {"size": 12471, "mtime": *************, "results": "63", "hashOfConfig": "61"}, {"size": 20876, "mtime": *************, "results": "64", "hashOfConfig": "61"}, {"size": 23732, "mtime": *************, "results": "65", "hashOfConfig": "61"}, {"size": 88741, "mtime": *************, "results": "66", "hashOfConfig": "61"}, {"size": 24032, "mtime": *************, "results": "67", "hashOfConfig": "61"}, {"size": 35918, "mtime": *************, "results": "68", "hashOfConfig": "61"}, {"size": 35881, "mtime": *************, "results": "69", "hashOfConfig": "61"}, {"size": 58815, "mtime": *************, "results": "70", "hashOfConfig": "61"}, {"size": 11977, "mtime": *************, "results": "71", "hashOfConfig": "61"}, {"size": 16174, "mtime": *************, "results": "72", "hashOfConfig": "61"}, {"size": 14109, "mtime": 1750491174548, "results": "73", "hashOfConfig": "61"}, {"size": 23611, "mtime": 1750162839511, "results": "74", "hashOfConfig": "61"}, {"size": 29474, "mtime": 1750325857119, "results": "75", "hashOfConfig": "61"}, {"size": 62581, "mtime": 1750704988831, "results": "76", "hashOfConfig": "61"}, {"size": 31001, "mtime": 1750609054159, "results": "77", "hashOfConfig": "61"}, {"size": 25257, "mtime": 1750325614617, "results": "78", "hashOfConfig": "61"}, {"size": 15956, "mtime": 1750774528571, "results": "79", "hashOfConfig": "61"}, {"size": 26761, "mtime": 1751015269902, "results": "80", "hashOfConfig": "61"}, {"size": 7726, "mtime": 1750753330934, "results": "81", "hashOfConfig": "61"}, {"size": 8233, "mtime": 1750156655509, "results": "82", "hashOfConfig": "61"}, {"size": 9363, "mtime": 1750753367891, "results": "83", "hashOfConfig": "61"}, {"size": 15849, "mtime": 1750597635521, "results": "84", "hashOfConfig": "61"}, {"size": 6021, "mtime": 1750080940725, "results": "85", "hashOfConfig": "61"}, {"size": 13037, "mtime": 1750161015627, "results": "86", "hashOfConfig": "61"}, {"size": 240, "mtime": 1739903579278, "results": "87", "hashOfConfig": "61"}, {"size": 7454, "mtime": 1750743539133, "results": "88", "hashOfConfig": "61"}, {"size": 9090, "mtime": 1750158689515, "results": "89", "hashOfConfig": "61"}, {"size": 10356, "mtime": 1750160765339, "results": "90", "hashOfConfig": "61"}, {"size": 231, "mtime": 1739903559736, "results": "91", "hashOfConfig": "61"}, {"size": 10625, "mtime": 1750416760057, "results": "92", "hashOfConfig": "61"}, {"size": 165, "mtime": 1745405035949, "results": "93", "hashOfConfig": "61"}, {"size": 11283, "mtime": 1750416849544, "results": "94", "hashOfConfig": "61"}, {"size": 9130, "mtime": 1750405715772, "results": "95", "hashOfConfig": "61"}, {"size": 10340, "mtime": 1751027712430, "results": "96", "hashOfConfig": "61"}, {"size": 17647, "mtime": 1750743500650, "results": "97", "hashOfConfig": "61"}, {"size": 7421, "mtime": 1751006179600, "results": "98", "hashOfConfig": "61"}, {"size": 4330, "mtime": 1750768613497, "results": "99", "hashOfConfig": "61"}, {"size": 4645, "mtime": 1745389234175, "results": "100", "hashOfConfig": "61"}, {"size": 7358, "mtime": 1745587458186, "results": "101", "hashOfConfig": "61"}, {"size": 2592, "mtime": 1750160631902, "results": "102", "hashOfConfig": "61"}, {"size": 4330, "mtime": 1749628388061, "results": "103", "hashOfConfig": "61"}, {"size": 18438, "mtime": 1750749236110, "results": "104", "hashOfConfig": "61"}, {"size": 6061, "mtime": 1750768589153, "results": "105", "hashOfConfig": "61"}, {"size": 2264, "mtime": 1750157299019, "results": "106", "hashOfConfig": "61"}, {"size": 4971, "mtime": 1751448259504, "results": "107", "hashOfConfig": "61"}, {"size": 1085, "mtime": 1750146077194, "results": "108", "hashOfConfig": "61"}, {"size": 3215, "mtime": 1750185528170, "results": "109", "hashOfConfig": "61"}, {"size": 24681, "mtime": 1750704902557, "results": "110", "hashOfConfig": "61"}, {"size": 1615, "mtime": 1745441585441, "results": "111", "hashOfConfig": "61"}, {"size": 5517, "mtime": 1751015219936, "results": "112", "hashOfConfig": "61"}, {"size": 1079, "mtime": 1750158561838, "results": "113", "hashOfConfig": "61"}, {"size": 12504, "mtime": 1750146421218, "results": "114", "hashOfConfig": "61"}, {"size": 19086, "mtime": 1751011508555, "results": "115", "hashOfConfig": "61"}, {"size": 813, "mtime": 1740212405282, "results": "116", "hashOfConfig": "61"}, {"size": 35235, "mtime": 1751451109746, "results": "117", "hashOfConfig": "61"}, {"size": 47465, "mtime": 1750703698962, "results": "118", "hashOfConfig": "61"}, {"size": 5306, "mtime": 1751005845192, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\index.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\theme.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\App.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CustomerList.js", ["297", "298", "299", "300", "301", "302"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageItems.js", ["303", "304"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageOrders.js", ["305", "306"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\VendorList.js", ["307", "308"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManagePurchaseInvoices.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PurchaseInvoiceList.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManagePurchaseReturns.js", ["309", "310"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\Layout.js", ["311", "312"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageCategories.js", ["313", "314", "315", "316"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\GeneralLedger.js", ["317", "318", "319"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\StockTracking.js", ["320", "321", "322"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PurchaseReturnList.js", ["323"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageSalesReturns.js", ["324"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BarcodeGenerator.js", ["325", "326", "327"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\SalesReturnList.js", ["328"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\SubsidiaryLedger.js", ["329", "330", "331", "332", "333"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ChartOfAccounts.js", ["334", "335", "336"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddCustomerPage.js", ["337"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\Dashboard.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddVendorPage.js", ["338"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\RolesPage.js", ["339", "340"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\UsersPage.js", ["341", "342", "343"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\CompanySetupPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\SettingsPage.js", ["344"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ContactsPage.js", ["345"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\UserManagementPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\BankAccountsPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\PurchasesPage.js", ["346", "347"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\OrderListPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\SalesPage.js", ["348", "349"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ItemsPage.js", ["350"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\BankPaymentsPage.js", ["351", "352", "353", "354"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AccountingPage.js", ["355", "356", "357"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddBankAccountPage.js", ["358"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\EditCustomerDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CustomerDetailView.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\TableOptions.js", ["359"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PermissionButton.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\VendorDetailView.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ItemDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\EditVendorDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CategoryDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\numberUtils.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\axiosConfig.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\permissions.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\exportUtils.js", ["360"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\formatUtils.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\accountsDiagnostics.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\hooks\\usePermissionDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CompanySettings.js", ["361", "362", "363", "364", "365"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankAccounts.js", ["366", "367"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\Breadcrumb.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankPaymentVoucherForm.js", ["368", "369", "370", "371"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\OrderList.js", ["372", "373"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankAccountDialog.js", [], [], {"ruleId": "374", "severity": 1, "message": "375", "line": 12, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 12, "endColumn": 9}, {"ruleId": "374", "severity": 1, "message": "378", "line": 68, "column": 10, "nodeType": "376", "messageId": "377", "endLine": 68, "endColumn": 23}, {"ruleId": "374", "severity": 1, "message": "379", "line": 68, "column": 25, "nodeType": "376", "messageId": "377", "endLine": 68, "endColumn": 41}, {"ruleId": "374", "severity": 1, "message": "380", "line": 69, "column": 22, "nodeType": "376", "messageId": "377", "endLine": 69, "endColumn": 35}, {"ruleId": "374", "severity": 1, "message": "381", "line": 70, "column": 25, "nodeType": "376", "messageId": "377", "endLine": 70, "endColumn": 41}, {"ruleId": "382", "severity": 1, "message": "383", "line": 109, "column": 6, "nodeType": "384", "endLine": 109, "endColumn": 8, "suggestions": "385"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 4, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 4, "endColumn": 9}, {"ruleId": "374", "severity": 1, "message": "386", "line": 16, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 16, "endColumn": 10}, {"ruleId": "382", "severity": 1, "message": "387", "line": 409, "column": 6, "nodeType": "384", "endLine": 409, "endColumn": 45, "suggestions": "388"}, {"ruleId": "374", "severity": 1, "message": "389", "line": 455, "column": 9, "nodeType": "376", "messageId": "377", "endLine": 455, "endColumn": 17}, {"ruleId": "374", "severity": 1, "message": "375", "line": 11, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 11, "endColumn": 9}, {"ruleId": "382", "severity": 1, "message": "390", "line": 108, "column": 6, "nodeType": "384", "endLine": 108, "endColumn": 8, "suggestions": "391"}, {"ruleId": "374", "severity": 1, "message": "392", "line": 25, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 25, "endColumn": 15}, {"ruleId": "374", "severity": 1, "message": "393", "line": 33, "column": 10, "nodeType": "376", "messageId": "377", "endLine": 33, "endColumn": 17}, {"ruleId": "374", "severity": 1, "message": "394", "line": 17, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 17, "endColumn": 11}, {"ruleId": "374", "severity": 1, "message": "395", "line": 18, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 18, "endColumn": 8}, {"ruleId": "374", "severity": 1, "message": "375", "line": 3, "column": 8, "nodeType": "376", "messageId": "377", "endLine": 3, "endColumn": 14}, {"ruleId": "374", "severity": 1, "message": "386", "line": 4, "column": 43, "nodeType": "376", "messageId": "377", "endLine": 4, "endColumn": 50}, {"ruleId": "374", "severity": 1, "message": "380", "line": 37, "column": 22, "nodeType": "376", "messageId": "377", "endLine": 37, "endColumn": 35}, {"ruleId": "374", "severity": 1, "message": "381", "line": 38, "column": 25, "nodeType": "376", "messageId": "377", "endLine": 38, "endColumn": 41}, {"ruleId": "374", "severity": 1, "message": "396", "line": 17, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 17, "endColumn": 12}, {"ruleId": "382", "severity": 1, "message": "397", "line": 56, "column": 6, "nodeType": "384", "endLine": 56, "endColumn": 8, "suggestions": "398"}, {"ruleId": "382", "severity": 1, "message": "399", "line": 60, "column": 6, "nodeType": "384", "endLine": 60, "endColumn": 15, "suggestions": "400"}, {"ruleId": "374", "severity": 1, "message": "401", "line": 29, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 29, "endColumn": 13}, {"ruleId": "374", "severity": 1, "message": "386", "line": 30, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 30, "endColumn": 10}, {"ruleId": "374", "severity": 1, "message": "402", "line": 46, "column": 8, "nodeType": "376", "messageId": "377", "endLine": 46, "endColumn": 13}, {"ruleId": "382", "severity": 1, "message": "403", "line": 65, "column": 6, "nodeType": "384", "endLine": 65, "endColumn": 8, "suggestions": "404"}, {"ruleId": "374", "severity": 1, "message": "393", "line": 34, "column": 10, "nodeType": "376", "messageId": "377", "endLine": 34, "endColumn": 17}, {"ruleId": "382", "severity": 1, "message": "405", "line": 102, "column": 6, "nodeType": "384", "endLine": 102, "endColumn": 8, "suggestions": "406"}, {"ruleId": "382", "severity": 1, "message": "407", "line": 167, "column": 6, "nodeType": "384", "endLine": 167, "endColumn": 61, "suggestions": "408"}, {"ruleId": "374", "severity": 1, "message": "409", "line": 672, "column": 10, "nodeType": "376", "messageId": "377", "endLine": 672, "endColumn": 27}, {"ruleId": "382", "severity": 1, "message": "403", "line": 65, "column": 6, "nodeType": "384", "endLine": 65, "endColumn": 8, "suggestions": "410"}, {"ruleId": "374", "severity": 1, "message": "411", "line": 22, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 22, "endColumn": 13}, {"ruleId": "374", "severity": 1, "message": "412", "line": 31, "column": 21, "nodeType": "376", "messageId": "377", "endLine": 31, "endColumn": 32}, {"ruleId": "382", "severity": 1, "message": "413", "line": 85, "column": 6, "nodeType": "384", "endLine": 85, "endColumn": 8, "suggestions": "414"}, {"ruleId": "382", "severity": 1, "message": "415", "line": 91, "column": 6, "nodeType": "384", "endLine": 91, "endColumn": 15, "suggestions": "416"}, {"ruleId": "374", "severity": 1, "message": "417", "line": 184, "column": 9, "nodeType": "376", "messageId": "377", "endLine": 184, "endColumn": 26}, {"ruleId": "374", "severity": 1, "message": "418", "line": 42, "column": 17, "nodeType": "376", "messageId": "377", "endLine": 42, "endColumn": 27}, {"ruleId": "374", "severity": 1, "message": "419", "line": 101, "column": 9, "nodeType": "376", "messageId": "377", "endLine": 101, "endColumn": 20}, {"ruleId": "382", "severity": 1, "message": "397", "line": 111, "column": 6, "nodeType": "384", "endLine": 111, "endColumn": 24, "suggestions": "420"}, {"ruleId": "374", "severity": 1, "message": "421", "line": 77, "column": 13, "nodeType": "376", "messageId": "377", "endLine": 77, "endColumn": 21}, {"ruleId": "374", "severity": 1, "message": "421", "line": 90, "column": 13, "nodeType": "376", "messageId": "377", "endLine": 90, "endColumn": 21}, {"ruleId": "374", "severity": 1, "message": "401", "line": 16, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 16, "endColumn": 13}, {"ruleId": "382", "severity": 1, "message": "422", "line": 78, "column": 6, "nodeType": "384", "endLine": 78, "endColumn": 25, "suggestions": "423"}, {"ruleId": "374", "severity": 1, "message": "401", "line": 16, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 16, "endColumn": 13}, {"ruleId": "374", "severity": 1, "message": "424", "line": 32, "column": 17, "nodeType": "376", "messageId": "377", "endLine": 32, "endColumn": 25}, {"ruleId": "382", "severity": 1, "message": "425", "line": 84, "column": 6, "nodeType": "384", "endLine": 84, "endColumn": 25, "suggestions": "426"}, {"ruleId": "374", "severity": 1, "message": "427", "line": 20, "column": 21, "nodeType": "376", "messageId": "377", "endLine": 20, "endColumn": 39}, {"ruleId": "374", "severity": 1, "message": "375", "line": 7, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 7, "endColumn": 9}, {"ruleId": "374", "severity": 1, "message": "375", "line": 7, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 7, "endColumn": 9}, {"ruleId": "374", "severity": 1, "message": "428", "line": 19, "column": 18, "nodeType": "376", "messageId": "377", "endLine": 19, "endColumn": 31}, {"ruleId": "374", "severity": 1, "message": "375", "line": 7, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 7, "endColumn": 9}, {"ruleId": "374", "severity": 1, "message": "429", "line": 19, "column": 17, "nodeType": "376", "messageId": "377", "endLine": 19, "endColumn": 26}, {"ruleId": "374", "severity": 1, "message": "375", "line": 5, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 5, "endColumn": 9}, {"ruleId": "374", "severity": 1, "message": "430", "line": 8, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 8, "endColumn": 7}, {"ruleId": "374", "severity": 1, "message": "431", "line": 22, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 22, "endColumn": 16}, {"ruleId": "374", "severity": 1, "message": "432", "line": 40, "column": 9, "nodeType": "376", "messageId": "377", "endLine": 40, "endColumn": 17}, {"ruleId": "382", "severity": 1, "message": "433", "line": 52, "column": 6, "nodeType": "384", "endLine": 52, "endColumn": 25, "suggestions": "434"}, {"ruleId": "374", "severity": 1, "message": "375", "line": 7, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 7, "endColumn": 9}, {"ruleId": "374", "severity": 1, "message": "435", "line": 45, "column": 10, "nodeType": "376", "messageId": "377", "endLine": 45, "endColumn": 17}, {"ruleId": "436", "severity": 1, "message": "437", "line": 91, "column": 9, "nodeType": "438", "messageId": "439", "endLine": 101, "endColumn": 10}, {"ruleId": "374", "severity": 1, "message": "421", "line": 68, "column": 13, "nodeType": "376", "messageId": "377", "endLine": 68, "endColumn": 21}, {"ruleId": "374", "severity": 1, "message": "440", "line": 94, "column": 9, "nodeType": "376", "messageId": "377", "endLine": 94, "endColumn": 26}, {"ruleId": "374", "severity": 1, "message": "441", "line": 4, "column": 10, "nodeType": "376", "messageId": "377", "endLine": 4, "endColumn": 16}, {"ruleId": "374", "severity": 1, "message": "442", "line": 35, "column": 10, "nodeType": "376", "messageId": "377", "endLine": 35, "endColumn": 19}, {"ruleId": "374", "severity": 1, "message": "443", "line": 36, "column": 10, "nodeType": "376", "messageId": "377", "endLine": 36, "endColumn": 22}, {"ruleId": "374", "severity": 1, "message": "444", "line": 85, "column": 7, "nodeType": "376", "messageId": "377", "endLine": 85, "endColumn": 23}, {"ruleId": "374", "severity": 1, "message": "445", "line": 106, "column": 7, "nodeType": "376", "messageId": "377", "endLine": 106, "endColumn": 23}, {"ruleId": "374", "severity": 1, "message": "446", "line": 116, "column": 7, "nodeType": "376", "messageId": "377", "endLine": 116, "endColumn": 26}, {"ruleId": "374", "severity": 1, "message": "380", "line": 41, "column": 22, "nodeType": "376", "messageId": "377", "endLine": 41, "endColumn": 35}, {"ruleId": "374", "severity": 1, "message": "381", "line": 42, "column": 25, "nodeType": "376", "messageId": "377", "endLine": 42, "endColumn": 41}, {"ruleId": "374", "severity": 1, "message": "395", "line": 14, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 14, "endColumn": 8}, {"ruleId": "374", "severity": 1, "message": "447", "line": 15, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 15, "endColumn": 10}, {"ruleId": "374", "severity": 1, "message": "448", "line": 16, "column": 3, "nodeType": "376", "messageId": "377", "endLine": 16, "endColumn": 7}, {"ruleId": "382", "severity": 1, "message": "449", "line": 89, "column": 6, "nodeType": "384", "endLine": 89, "endColumn": 17, "suggestions": "450"}, {"ruleId": "374", "severity": 1, "message": "380", "line": 64, "column": 22, "nodeType": "376", "messageId": "377", "endLine": 64, "endColumn": 35}, {"ruleId": "374", "severity": 1, "message": "381", "line": 65, "column": 25, "nodeType": "376", "messageId": "377", "endLine": 65, "endColumn": 41}, "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'showSearchBox' is assigned a value but never used.", "'setShowSearchBox' is assigned a value but never used.", "'setSortOption' is assigned a value but never used.", "'setSortDirection' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCustomers'. Either include it or remove the dependency array.", "ArrayExpression", ["451"], "'Tooltip' is defined but never used.", "React Hook useEffect has a missing dependency: 'autoAddScannedItem'. Either include it or remove the dependency array.", ["452"], "'editItem' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchVendors'. Either include it or remove the dependency array.", ["453"], "'Autocomplete' is defined but never used.", "'AddIcon' is defined but never used.", "'Snackbar' is defined but never used.", "'Alert' is defined but never used.", "'TextField' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAccounts'. Either include it or remove the dependency array.", ["454"], "React Hook useEffect has a missing dependency: 'fetchLedgerData'. Either include it or remove the dependency array.", ["455"], "'IconButton' is defined but never used.", "'dayjs' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReturns'. Either include it or remove the dependency array.", ["456"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["457"], "React Hook useEffect has a missing dependency: 'generateBarcode'. Either include it or remove the dependency array.", ["458"], "'barcodesGenerated' is assigned a value but never used.", ["459"], "'Pagination' is defined but never used.", "'AccountIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["460"], "React Hook useEffect has a missing dependency: 'fetchSubsidiaryLedgerData'. Either include it or remove the dependency array.", ["461"], "'getSelectedEntity' is assigned a value but never used.", "'FilterIcon' is defined but never used.", "'sourceTypes' is assigned a value but never used.", ["462"], "'response' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRoles'. Either include it or remove the dependency array.", ["463"], "'ViewIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["464"], "'AccountBalanceIcon' is defined but never used.", "'PurchasesIcon' is defined but never used.", "'SalesIcon' is defined but never used.", "'Grid' is defined but never used.", "'DialogActions' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchVouchers'. Either include it or remove the dependency array.", ["465"], "'loading' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'openFilterPopover' is assigned a value but never used.", "'saveAs' is defined but never used.", "'orderDate' is assigned a value but never used.", "'deliveryDate' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "'handleDateChange' is assigned a value but never used.", "'handleResetSettings' is assigned a value but never used.", "'Divider' is defined but never used.", "'Chip' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchVoucher'. Either include it or remove the dependency array.", ["466"], {"desc": "467", "fix": "468"}, {"desc": "469", "fix": "470"}, {"desc": "471", "fix": "472"}, {"desc": "473", "fix": "474"}, {"desc": "475", "fix": "476"}, {"desc": "477", "fix": "478"}, {"desc": "479", "fix": "480"}, {"desc": "481", "fix": "482"}, {"desc": "477", "fix": "483"}, {"desc": "484", "fix": "485"}, {"desc": "486", "fix": "487"}, {"desc": "488", "fix": "489"}, {"desc": "490", "fix": "491"}, {"desc": "492", "fix": "493"}, {"desc": "494", "fix": "495"}, {"desc": "496", "fix": "497"}, "Update the dependencies array to be: [fetchCustomers]", {"range": "498", "text": "499"}, "Update the dependencies array to be: [scannedBarcode, availableItems, items, autoAddScannedItem]", {"range": "500", "text": "501"}, "Update the dependencies array to be: [fetchVendors]", {"range": "502", "text": "503"}, "Update the dependencies array to be: [fetchAccounts]", {"range": "504", "text": "505"}, "Update the dependencies array to be: [fetchLedgerData, filters]", {"range": "506", "text": "507"}, "Update the dependencies array to be: [fetchReturns]", {"range": "508", "text": "509"}, "Update the dependencies array to be: [fetchProducts]", {"range": "510", "text": "511"}, "Update the dependencies array to be: [selectedProduct, selectedProductData, barcodeSettings, generateBarcode]", {"range": "512", "text": "513"}, {"range": "514", "text": "509"}, "Update the dependencies array to be: [fetchInitialData]", {"range": "515", "text": "516"}, "Update the dependencies array to be: [fetchSubsidiaryLedgerData, filters]", {"range": "517", "text": "518"}, "Update the dependencies array to be: [fetchAccounts, filterSourceType]", {"range": "519", "text": "520"}, "Update the dependencies array to be: [fetchRoles, location.pathname]", {"range": "521", "text": "522"}, "Update the dependencies array to be: [fetchUsers, location.pathname]", {"range": "523", "text": "524"}, "Update the dependencies array to be: [fetchVouchers, page, rowsPerPage]", {"range": "525", "text": "526"}, "Update the dependencies array to be: [fetchVoucher, voucherId]", {"range": "527", "text": "528"}, [3227, 3229], "[fetchCustomers]", [14486, 14525], "[scannedBarcode, availableItems, items, autoAddScannedItem]", [3254, 3256], "[fetchVendors]", [1358, 1360], "[fetchAccounts]", [1412, 1421], "[fetchLedgerData, filters]", [1531, 1533], "[fetchReturns]", [3060, 3062], "[fetchProducts]", [5503, 5558], "[selectedProduct, selectedProductData, barcodeSettings, generateBarcode]", [1533, 1535], [2151, 2153], "[fetchInitialData]", [2251, 2260], "[fetchSubsidiaryLedgerData, filters]", [2559, 2577], "[fetchAccounts, filterSourceType]", [1978, 1997], "[fetchRoles, location.pathname]", [1998, 2017], "[fetchUsers, location.pathname]", [1374, 1393], "[fetchVouchers, page, rowsPerPage]", [2552, 2563], "[fetchVoucher, voucherId]"]