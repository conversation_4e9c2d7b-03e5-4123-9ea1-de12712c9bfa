[{"D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\index.js": "1", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\theme.js": "2", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\App.js": "3", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CustomerList.js": "4", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageItems.js": "5", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageOrders.js": "6", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\VendorList.js": "7", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManagePurchaseInvoices.js": "8", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PurchaseInvoiceList.js": "9", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManagePurchaseReturns.js": "10", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\Layout.js": "11", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageCategories.js": "12", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\GeneralLedger.js": "13", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\StockTracking.js": "14", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PurchaseReturnList.js": "15", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageSalesReturns.js": "16", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BarcodeGenerator.js": "17", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\SalesReturnList.js": "18", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\SubsidiaryLedger.js": "19", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ChartOfAccounts.js": "20", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddCustomerPage.js": "21", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\Dashboard.js": "22", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddVendorPage.js": "23", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\RolesPage.js": "24", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\LoginPage.js": "25", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\UsersPage.js": "26", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\CompanySetupPage.js": "27", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\SettingsPage.js": "28", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ContactsPage.js": "29", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\UserManagementPage.js": "30", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\BankAccountsPage.js": "31", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\PurchasesPage.js": "32", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\OrderListPage.js": "33", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\SalesPage.js": "34", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ItemsPage.js": "35", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\BankPaymentsPage.js": "36", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AccountingPage.js": "37", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddBankAccountPage.js": "38", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\EditCustomerDialog.js": "39", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CustomerDetailView.js": "40", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\TableOptions.js": "41", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PermissionButton.js": "42", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\VendorDetailView.js": "43", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ItemDialog.js": "44", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\EditVendorDialog.js": "45", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CategoryDialog.js": "46", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\numberUtils.js": "47", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\axiosConfig.js": "48", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\permissions.js": "49", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\exportUtils.js": "50", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\formatUtils.js": "51", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\accountsDiagnostics.js": "52", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\hooks\\usePermissionDialog.js": "53", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CompanySettings.js": "54", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankAccounts.js": "55", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\Breadcrumb.js": "56", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankPaymentVoucherForm.js": "57", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\OrderList.js": "58", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankAccountDialog.js": "59", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ManageBankPaymentPage.js": "60"}, {"size": 487, "mtime": *************, "results": "61", "hashOfConfig": "62"}, {"size": 1711, "mtime": *************, "results": "63", "hashOfConfig": "62"}, {"size": 12626, "mtime": *************, "results": "64", "hashOfConfig": "62"}, {"size": 20876, "mtime": *************, "results": "65", "hashOfConfig": "62"}, {"size": 23732, "mtime": *************, "results": "66", "hashOfConfig": "62"}, {"size": 88741, "mtime": *************, "results": "67", "hashOfConfig": "62"}, {"size": 24032, "mtime": *************, "results": "68", "hashOfConfig": "62"}, {"size": 35918, "mtime": *************, "results": "69", "hashOfConfig": "62"}, {"size": 35881, "mtime": *************, "results": "70", "hashOfConfig": "62"}, {"size": 58815, "mtime": *************, "results": "71", "hashOfConfig": "62"}, {"size": 11977, "mtime": *************, "results": "72", "hashOfConfig": "62"}, {"size": 16174, "mtime": *************, "results": "73", "hashOfConfig": "62"}, {"size": 14109, "mtime": 1750491174548, "results": "74", "hashOfConfig": "62"}, {"size": 23611, "mtime": 1750162839511, "results": "75", "hashOfConfig": "62"}, {"size": 29474, "mtime": 1750325857119, "results": "76", "hashOfConfig": "62"}, {"size": 62581, "mtime": 1750704988831, "results": "77", "hashOfConfig": "62"}, {"size": 31001, "mtime": 1750609054159, "results": "78", "hashOfConfig": "62"}, {"size": 25257, "mtime": 1750325614617, "results": "79", "hashOfConfig": "62"}, {"size": 15956, "mtime": 1750774528571, "results": "80", "hashOfConfig": "62"}, {"size": 26761, "mtime": 1751015269902, "results": "81", "hashOfConfig": "62"}, {"size": 7726, "mtime": 1750753330934, "results": "82", "hashOfConfig": "62"}, {"size": 8233, "mtime": 1750156655509, "results": "83", "hashOfConfig": "62"}, {"size": 9363, "mtime": 1750753367891, "results": "84", "hashOfConfig": "62"}, {"size": 15849, "mtime": 1750597635521, "results": "85", "hashOfConfig": "62"}, {"size": 6021, "mtime": 1750080940725, "results": "86", "hashOfConfig": "62"}, {"size": 13037, "mtime": 1750161015627, "results": "87", "hashOfConfig": "62"}, {"size": 240, "mtime": 1739903579278, "results": "88", "hashOfConfig": "62"}, {"size": 7454, "mtime": 1750743539133, "results": "89", "hashOfConfig": "62"}, {"size": 9090, "mtime": 1750158689515, "results": "90", "hashOfConfig": "62"}, {"size": 10356, "mtime": 1750160765339, "results": "91", "hashOfConfig": "62"}, {"size": 231, "mtime": 1739903559736, "results": "92", "hashOfConfig": "62"}, {"size": 10625, "mtime": 1750416760057, "results": "93", "hashOfConfig": "62"}, {"size": 165, "mtime": 1745405035949, "results": "94", "hashOfConfig": "62"}, {"size": 11283, "mtime": 1750416849544, "results": "95", "hashOfConfig": "62"}, {"size": 9130, "mtime": 1750405715772, "results": "96", "hashOfConfig": "62"}, {"size": 9084, "mtime": 1751461169255, "results": "97", "hashOfConfig": "62"}, {"size": 17647, "mtime": 1750743500650, "results": "98", "hashOfConfig": "62"}, {"size": 7421, "mtime": 1751006179600, "results": "99", "hashOfConfig": "62"}, {"size": 4330, "mtime": 1750768613497, "results": "100", "hashOfConfig": "62"}, {"size": 4645, "mtime": 1745389234175, "results": "101", "hashOfConfig": "62"}, {"size": 7358, "mtime": 1745587458186, "results": "102", "hashOfConfig": "62"}, {"size": 2592, "mtime": 1750160631902, "results": "103", "hashOfConfig": "62"}, {"size": 4330, "mtime": 1749628388061, "results": "104", "hashOfConfig": "62"}, {"size": 18438, "mtime": 1750749236110, "results": "105", "hashOfConfig": "62"}, {"size": 6061, "mtime": 1750768589153, "results": "106", "hashOfConfig": "62"}, {"size": 2264, "mtime": 1750157299019, "results": "107", "hashOfConfig": "62"}, {"size": 4971, "mtime": 1751448259504, "results": "108", "hashOfConfig": "62"}, {"size": 1085, "mtime": 1750146077194, "results": "109", "hashOfConfig": "62"}, {"size": 3215, "mtime": 1750185528170, "results": "110", "hashOfConfig": "62"}, {"size": 24681, "mtime": 1750704902557, "results": "111", "hashOfConfig": "62"}, {"size": 1615, "mtime": 1745441585441, "results": "112", "hashOfConfig": "62"}, {"size": 5517, "mtime": 1751015219936, "results": "113", "hashOfConfig": "62"}, {"size": 1079, "mtime": 1750158561838, "results": "114", "hashOfConfig": "62"}, {"size": 12504, "mtime": 1750146421218, "results": "115", "hashOfConfig": "62"}, {"size": 19086, "mtime": 1751011508555, "results": "116", "hashOfConfig": "62"}, {"size": 813, "mtime": 1740212405282, "results": "117", "hashOfConfig": "62"}, {"size": 35113, "mtime": 1751460297744, "results": "118", "hashOfConfig": "62"}, {"size": 47465, "mtime": 1750703698962, "results": "119", "hashOfConfig": "62"}, {"size": 5306, "mtime": 1751005845192, "results": "120", "hashOfConfig": "62"}, {"size": 2712, "mtime": 1751461101856, "results": "121", "hashOfConfig": "62"}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\index.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\theme.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\App.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CustomerList.js", ["302", "303", "304", "305", "306", "307"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageItems.js", ["308", "309"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageOrders.js", ["310", "311"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\VendorList.js", ["312", "313"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManagePurchaseInvoices.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PurchaseInvoiceList.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManagePurchaseReturns.js", ["314", "315"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\Layout.js", ["316", "317"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageCategories.js", ["318", "319", "320", "321"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\GeneralLedger.js", ["322", "323", "324"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\StockTracking.js", ["325", "326", "327"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PurchaseReturnList.js", ["328"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ManageSalesReturns.js", ["329"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BarcodeGenerator.js", ["330", "331", "332"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\SalesReturnList.js", ["333"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\SubsidiaryLedger.js", ["334", "335", "336", "337", "338"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ChartOfAccounts.js", ["339", "340", "341"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddCustomerPage.js", ["342"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\Dashboard.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddVendorPage.js", ["343"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\RolesPage.js", ["344", "345"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\LoginPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\UsersPage.js", ["346", "347", "348"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\CompanySetupPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\SettingsPage.js", ["349"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ContactsPage.js", ["350"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\UserManagementPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\BankAccountsPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\PurchasesPage.js", ["351", "352"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\OrderListPage.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\SalesPage.js", ["353", "354"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ItemsPage.js", ["355"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\BankPaymentsPage.js", ["356"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AccountingPage.js", ["357", "358", "359"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\AddBankAccountPage.js", ["360"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\EditCustomerDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CustomerDetailView.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\TableOptions.js", ["361"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\PermissionButton.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\VendorDetailView.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\ItemDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\EditVendorDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CategoryDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\numberUtils.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\axiosConfig.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\permissions.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\exportUtils.js", ["362"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\formatUtils.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\utils\\accountsDiagnostics.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\hooks\\usePermissionDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\CompanySettings.js", ["363", "364", "365", "366", "367"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankAccounts.js", ["368", "369"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\Breadcrumb.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankPaymentVoucherForm.js", ["370", "371", "372", "373"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\OrderList.js", ["374", "375"], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\components\\BankAccountDialog.js", [], [], "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\src\\pages\\ManageBankPaymentPage.js", [], [], {"ruleId": "376", "severity": 1, "message": "377", "line": 12, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 12, "endColumn": 9}, {"ruleId": "376", "severity": 1, "message": "380", "line": 68, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 68, "endColumn": 23}, {"ruleId": "376", "severity": 1, "message": "381", "line": 68, "column": 25, "nodeType": "378", "messageId": "379", "endLine": 68, "endColumn": 41}, {"ruleId": "376", "severity": 1, "message": "382", "line": 69, "column": 22, "nodeType": "378", "messageId": "379", "endLine": 69, "endColumn": 35}, {"ruleId": "376", "severity": 1, "message": "383", "line": 70, "column": 25, "nodeType": "378", "messageId": "379", "endLine": 70, "endColumn": 41}, {"ruleId": "384", "severity": 1, "message": "385", "line": 109, "column": 6, "nodeType": "386", "endLine": 109, "endColumn": 8, "suggestions": "387"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 4, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 4, "endColumn": 9}, {"ruleId": "376", "severity": 1, "message": "388", "line": 16, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 16, "endColumn": 10}, {"ruleId": "384", "severity": 1, "message": "389", "line": 409, "column": 6, "nodeType": "386", "endLine": 409, "endColumn": 45, "suggestions": "390"}, {"ruleId": "376", "severity": 1, "message": "391", "line": 455, "column": 9, "nodeType": "378", "messageId": "379", "endLine": 455, "endColumn": 17}, {"ruleId": "376", "severity": 1, "message": "377", "line": 11, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 11, "endColumn": 9}, {"ruleId": "384", "severity": 1, "message": "392", "line": 108, "column": 6, "nodeType": "386", "endLine": 108, "endColumn": 8, "suggestions": "393"}, {"ruleId": "376", "severity": 1, "message": "394", "line": 25, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 25, "endColumn": 15}, {"ruleId": "376", "severity": 1, "message": "395", "line": 33, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 33, "endColumn": 17}, {"ruleId": "376", "severity": 1, "message": "396", "line": 17, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 17, "endColumn": 11}, {"ruleId": "376", "severity": 1, "message": "397", "line": 18, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 18, "endColumn": 8}, {"ruleId": "376", "severity": 1, "message": "377", "line": 3, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 3, "endColumn": 14}, {"ruleId": "376", "severity": 1, "message": "388", "line": 4, "column": 43, "nodeType": "378", "messageId": "379", "endLine": 4, "endColumn": 50}, {"ruleId": "376", "severity": 1, "message": "382", "line": 37, "column": 22, "nodeType": "378", "messageId": "379", "endLine": 37, "endColumn": 35}, {"ruleId": "376", "severity": 1, "message": "383", "line": 38, "column": 25, "nodeType": "378", "messageId": "379", "endLine": 38, "endColumn": 41}, {"ruleId": "376", "severity": 1, "message": "398", "line": 17, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 17, "endColumn": 12}, {"ruleId": "384", "severity": 1, "message": "399", "line": 56, "column": 6, "nodeType": "386", "endLine": 56, "endColumn": 8, "suggestions": "400"}, {"ruleId": "384", "severity": 1, "message": "401", "line": 60, "column": 6, "nodeType": "386", "endLine": 60, "endColumn": 15, "suggestions": "402"}, {"ruleId": "376", "severity": 1, "message": "403", "line": 29, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 29, "endColumn": 13}, {"ruleId": "376", "severity": 1, "message": "388", "line": 30, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 30, "endColumn": 10}, {"ruleId": "376", "severity": 1, "message": "404", "line": 46, "column": 8, "nodeType": "378", "messageId": "379", "endLine": 46, "endColumn": 13}, {"ruleId": "384", "severity": 1, "message": "405", "line": 65, "column": 6, "nodeType": "386", "endLine": 65, "endColumn": 8, "suggestions": "406"}, {"ruleId": "376", "severity": 1, "message": "395", "line": 34, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 34, "endColumn": 17}, {"ruleId": "384", "severity": 1, "message": "407", "line": 102, "column": 6, "nodeType": "386", "endLine": 102, "endColumn": 8, "suggestions": "408"}, {"ruleId": "384", "severity": 1, "message": "409", "line": 167, "column": 6, "nodeType": "386", "endLine": 167, "endColumn": 61, "suggestions": "410"}, {"ruleId": "376", "severity": 1, "message": "411", "line": 672, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 672, "endColumn": 27}, {"ruleId": "384", "severity": 1, "message": "405", "line": 65, "column": 6, "nodeType": "386", "endLine": 65, "endColumn": 8, "suggestions": "412"}, {"ruleId": "376", "severity": 1, "message": "413", "line": 22, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 22, "endColumn": 13}, {"ruleId": "376", "severity": 1, "message": "414", "line": 31, "column": 21, "nodeType": "378", "messageId": "379", "endLine": 31, "endColumn": 32}, {"ruleId": "384", "severity": 1, "message": "415", "line": 85, "column": 6, "nodeType": "386", "endLine": 85, "endColumn": 8, "suggestions": "416"}, {"ruleId": "384", "severity": 1, "message": "417", "line": 91, "column": 6, "nodeType": "386", "endLine": 91, "endColumn": 15, "suggestions": "418"}, {"ruleId": "376", "severity": 1, "message": "419", "line": 184, "column": 9, "nodeType": "378", "messageId": "379", "endLine": 184, "endColumn": 26}, {"ruleId": "376", "severity": 1, "message": "420", "line": 42, "column": 17, "nodeType": "378", "messageId": "379", "endLine": 42, "endColumn": 27}, {"ruleId": "376", "severity": 1, "message": "421", "line": 101, "column": 9, "nodeType": "378", "messageId": "379", "endLine": 101, "endColumn": 20}, {"ruleId": "384", "severity": 1, "message": "399", "line": 111, "column": 6, "nodeType": "386", "endLine": 111, "endColumn": 24, "suggestions": "422"}, {"ruleId": "376", "severity": 1, "message": "423", "line": 77, "column": 13, "nodeType": "378", "messageId": "379", "endLine": 77, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "423", "line": 90, "column": 13, "nodeType": "378", "messageId": "379", "endLine": 90, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "403", "line": 16, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 16, "endColumn": 13}, {"ruleId": "384", "severity": 1, "message": "424", "line": 78, "column": 6, "nodeType": "386", "endLine": 78, "endColumn": 25, "suggestions": "425"}, {"ruleId": "376", "severity": 1, "message": "403", "line": 16, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 16, "endColumn": 13}, {"ruleId": "376", "severity": 1, "message": "426", "line": 32, "column": 17, "nodeType": "378", "messageId": "379", "endLine": 32, "endColumn": 25}, {"ruleId": "384", "severity": 1, "message": "427", "line": 84, "column": 6, "nodeType": "386", "endLine": 84, "endColumn": 25, "suggestions": "428"}, {"ruleId": "376", "severity": 1, "message": "429", "line": 20, "column": 21, "nodeType": "378", "messageId": "379", "endLine": 20, "endColumn": 39}, {"ruleId": "376", "severity": 1, "message": "377", "line": 7, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 7, "endColumn": 9}, {"ruleId": "376", "severity": 1, "message": "377", "line": 7, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 7, "endColumn": 9}, {"ruleId": "376", "severity": 1, "message": "430", "line": 19, "column": 18, "nodeType": "378", "messageId": "379", "endLine": 19, "endColumn": 31}, {"ruleId": "376", "severity": 1, "message": "377", "line": 7, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 7, "endColumn": 9}, {"ruleId": "376", "severity": 1, "message": "431", "line": 19, "column": 17, "nodeType": "378", "messageId": "379", "endLine": 19, "endColumn": 26}, {"ruleId": "376", "severity": 1, "message": "377", "line": 5, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 5, "endColumn": 9}, {"ruleId": "384", "severity": 1, "message": "432", "line": 43, "column": 6, "nodeType": "386", "endLine": 43, "endColumn": 25, "suggestions": "433"}, {"ruleId": "376", "severity": 1, "message": "377", "line": 7, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 7, "endColumn": 9}, {"ruleId": "376", "severity": 1, "message": "434", "line": 45, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 45, "endColumn": 17}, {"ruleId": "435", "severity": 1, "message": "436", "line": 91, "column": 9, "nodeType": "437", "messageId": "438", "endLine": 101, "endColumn": 10}, {"ruleId": "376", "severity": 1, "message": "423", "line": 68, "column": 13, "nodeType": "378", "messageId": "379", "endLine": 68, "endColumn": 21}, {"ruleId": "376", "severity": 1, "message": "439", "line": 94, "column": 9, "nodeType": "378", "messageId": "379", "endLine": 94, "endColumn": 26}, {"ruleId": "376", "severity": 1, "message": "440", "line": 4, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 4, "endColumn": 16}, {"ruleId": "376", "severity": 1, "message": "441", "line": 35, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 35, "endColumn": 19}, {"ruleId": "376", "severity": 1, "message": "442", "line": 36, "column": 10, "nodeType": "378", "messageId": "379", "endLine": 36, "endColumn": 22}, {"ruleId": "376", "severity": 1, "message": "443", "line": 85, "column": 7, "nodeType": "378", "messageId": "379", "endLine": 85, "endColumn": 23}, {"ruleId": "376", "severity": 1, "message": "444", "line": 106, "column": 7, "nodeType": "378", "messageId": "379", "endLine": 106, "endColumn": 23}, {"ruleId": "376", "severity": 1, "message": "445", "line": 116, "column": 7, "nodeType": "378", "messageId": "379", "endLine": 116, "endColumn": 26}, {"ruleId": "376", "severity": 1, "message": "382", "line": 41, "column": 22, "nodeType": "378", "messageId": "379", "endLine": 41, "endColumn": 35}, {"ruleId": "376", "severity": 1, "message": "383", "line": 42, "column": 25, "nodeType": "378", "messageId": "379", "endLine": 42, "endColumn": 41}, {"ruleId": "376", "severity": 1, "message": "397", "line": 14, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 14, "endColumn": 8}, {"ruleId": "376", "severity": 1, "message": "446", "line": 15, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 15, "endColumn": 10}, {"ruleId": "376", "severity": 1, "message": "447", "line": 16, "column": 3, "nodeType": "378", "messageId": "379", "endLine": 16, "endColumn": 7}, {"ruleId": "384", "severity": 1, "message": "448", "line": 89, "column": 6, "nodeType": "386", "endLine": 89, "endColumn": 17, "suggestions": "449"}, {"ruleId": "376", "severity": 1, "message": "382", "line": 64, "column": 22, "nodeType": "378", "messageId": "379", "endLine": 64, "endColumn": 35}, {"ruleId": "376", "severity": 1, "message": "383", "line": 65, "column": 25, "nodeType": "378", "messageId": "379", "endLine": 65, "endColumn": 41}, "no-unused-vars", "'Button' is defined but never used.", "Identifier", "unusedVar", "'showSearchBox' is assigned a value but never used.", "'setShowSearchBox' is assigned a value but never used.", "'setSortOption' is assigned a value but never used.", "'setSortDirection' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchCustomers'. Either include it or remove the dependency array.", "ArrayExpression", ["450"], "'Tooltip' is defined but never used.", "React Hook useEffect has a missing dependency: 'autoAddScannedItem'. Either include it or remove the dependency array.", ["451"], "'editItem' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchVendors'. Either include it or remove the dependency array.", ["452"], "'Autocomplete' is defined but never used.", "'AddIcon' is defined but never used.", "'Snackbar' is defined but never used.", "'Alert' is defined but never used.", "'TextField' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAccounts'. Either include it or remove the dependency array.", ["453"], "React Hook useEffect has a missing dependency: 'fetchLedgerData'. Either include it or remove the dependency array.", ["454"], "'IconButton' is defined but never used.", "'dayjs' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReturns'. Either include it or remove the dependency array.", ["455"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["456"], "React Hook useEffect has a missing dependency: 'generateBarcode'. Either include it or remove the dependency array.", ["457"], "'barcodesGenerated' is assigned a value but never used.", ["458"], "'Pagination' is defined but never used.", "'AccountIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["459"], "React Hook useEffect has a missing dependency: 'fetchSubsidiaryLedgerData'. Either include it or remove the dependency array.", ["460"], "'getSelectedEntity' is assigned a value but never used.", "'FilterIcon' is defined but never used.", "'sourceTypes' is assigned a value but never used.", ["461"], "'response' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRoles'. Either include it or remove the dependency array.", ["462"], "'ViewIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["463"], "'AccountBalanceIcon' is defined but never used.", "'PurchasesIcon' is defined but never used.", "'SalesIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchVouchers'. Either include it or remove the dependency array.", ["464"], "'loading' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'openFilterPopover' is assigned a value but never used.", "'saveAs' is defined but never used.", "'orderDate' is assigned a value but never used.", "'deliveryDate' is assigned a value but never used.", "'handleFileUpload' is assigned a value but never used.", "'handleDateChange' is assigned a value but never used.", "'handleResetSettings' is assigned a value but never used.", "'Divider' is defined but never used.", "'Chip' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchVoucher'. Either include it or remove the dependency array.", ["465"], {"desc": "466", "fix": "467"}, {"desc": "468", "fix": "469"}, {"desc": "470", "fix": "471"}, {"desc": "472", "fix": "473"}, {"desc": "474", "fix": "475"}, {"desc": "476", "fix": "477"}, {"desc": "478", "fix": "479"}, {"desc": "480", "fix": "481"}, {"desc": "476", "fix": "482"}, {"desc": "483", "fix": "484"}, {"desc": "485", "fix": "486"}, {"desc": "487", "fix": "488"}, {"desc": "489", "fix": "490"}, {"desc": "491", "fix": "492"}, {"desc": "493", "fix": "494"}, {"desc": "495", "fix": "496"}, "Update the dependencies array to be: [fetchCustomers]", {"range": "497", "text": "498"}, "Update the dependencies array to be: [scannedBarcode, availableItems, items, autoAddScannedItem]", {"range": "499", "text": "500"}, "Update the dependencies array to be: [fetchVendors]", {"range": "501", "text": "502"}, "Update the dependencies array to be: [fetchAccounts]", {"range": "503", "text": "504"}, "Update the dependencies array to be: [fetchLedgerData, filters]", {"range": "505", "text": "506"}, "Update the dependencies array to be: [fetchReturns]", {"range": "507", "text": "508"}, "Update the dependencies array to be: [fetchProducts]", {"range": "509", "text": "510"}, "Update the dependencies array to be: [selectedProduct, selectedProductData, barcodeSettings, generateBarcode]", {"range": "511", "text": "512"}, {"range": "513", "text": "508"}, "Update the dependencies array to be: [fetchInitialData]", {"range": "514", "text": "515"}, "Update the dependencies array to be: [fetchSubsidiaryLedgerData, filters]", {"range": "516", "text": "517"}, "Update the dependencies array to be: [fetchAccounts, filterSourceType]", {"range": "518", "text": "519"}, "Update the dependencies array to be: [fetchRoles, location.pathname]", {"range": "520", "text": "521"}, "Update the dependencies array to be: [fetchUsers, location.pathname]", {"range": "522", "text": "523"}, "Update the dependencies array to be: [fetchVouchers, page, rowsPerPage]", {"range": "524", "text": "525"}, "Update the dependencies array to be: [fetchVoucher, voucherId]", {"range": "526", "text": "527"}, [3227, 3229], "[fetchCustomers]", [14486, 14525], "[scannedBarcode, availableItems, items, autoAddScannedItem]", [3254, 3256], "[fetchVendors]", [1358, 1360], "[fetchAccounts]", [1412, 1421], "[fetchLedgerData, filters]", [1531, 1533], "[fetchReturns]", [3060, 3062], "[fetchProducts]", [5503, 5558], "[selectedProduct, selectedProductData, barcodeSettings, generateBarcode]", [1533, 1535], [2151, 2153], "[fetchInitialData]", [2251, 2260], "[fetchSubsidiaryLedgerData, filters]", [2559, 2577], "[fetchAccounts, filterSourceType]", [1978, 1997], "[fetchRoles, location.pathname]", [1998, 2017], "[fetchUsers, location.pathname]", [1049, 1068], "[fetchVouchers, page, rowsPerPage]", [2552, 2563], "[fetchVoucher, voucherId]"]