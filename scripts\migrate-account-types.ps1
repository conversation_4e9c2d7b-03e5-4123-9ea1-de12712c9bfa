Write-Host "🚀 Starting Chart of Accounts Migration..." -ForegroundColor Cyan

# Navigate to backend directory
Set-Location -Path .\backend

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js and try again." -ForegroundColor Red
    exit 1
}

# Run the migration script
Write-Host "🔄 Running account migration script..." -ForegroundColor Yellow
node scripts/migrateAccountSourceTypes.js

# Check if the script ran successfully
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Account migration completed successfully!" -ForegroundColor Green
} else {
    Write-Host "❌ Account migration failed with exit code $LASTEXITCODE" -ForegroundColor Red
    exit $LASTEXITCODE
}

# Return to the root directory
Set-Location -Path ..

Write-Host "🎉 Chart of Accounts migration process completed!" -ForegroundColor Cyan 