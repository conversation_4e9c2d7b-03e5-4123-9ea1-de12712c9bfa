{"ast": null, "code": "import * as React from 'react';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useId from '@mui/utils/useId';\nimport { getSectionValueNow, getSectionValueText, parseSelectedSections } from \"./useField.utils.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { usePickersTranslations } from \"../../../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../useUtils.js\";\nexport const useFieldV7TextField = params => {\n  const {\n    internalProps: {\n      disabled,\n      readOnly = false\n    },\n    forwardedProps: {\n      sectionListRef: inSectionListRef,\n      onBlur,\n      onClick,\n      onFocus,\n      onInput,\n      onPaste,\n      focused: focusedProp,\n      autoFocus = false\n    },\n    fieldValueManager,\n    applyCharacterEditing,\n    resetCharacterQuery,\n    setSelectedSections,\n    parsedSelectedSections,\n    state,\n    clearActiveSection,\n    clearValue,\n    updateSectionValue,\n    updateValueFromValueStr,\n    sectionOrder,\n    areAllSectionsEmpty,\n    sectionsValueBoundaries\n  } = params;\n  const sectionListRef = React.useRef(null);\n  const handleSectionListRef = useForkRef(inSectionListRef, sectionListRef);\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n  const id = useId();\n  const [focused, setFocused] = React.useState(false);\n  const interactions = React.useMemo(() => ({\n    syncSelectionToDOM: () => {\n      if (!sectionListRef.current) {\n        return;\n      }\n      const selection = document.getSelection();\n      if (!selection) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        // If the selection contains an element inside the field, we reset it.\n        if (selection.rangeCount > 0 && sectionListRef.current.getRoot().contains(selection.getRangeAt(0).startContainer)) {\n          selection.removeAllRanges();\n        }\n        if (focused) {\n          sectionListRef.current.getRoot().blur();\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      if (!sectionListRef.current.getRoot().contains(getActiveElement(document))) {\n        return;\n      }\n      const range = new window.Range();\n      let target;\n      if (parsedSelectedSections === 'all') {\n        target = sectionListRef.current.getRoot();\n      } else {\n        const section = state.sections[parsedSelectedSections];\n        if (section.type === 'empty') {\n          target = sectionListRef.current.getSectionContainer(parsedSelectedSections);\n        } else {\n          target = sectionListRef.current.getSectionContent(parsedSelectedSections);\n        }\n      }\n      range.selectNodeContents(target);\n      target.focus();\n      selection.removeAllRanges();\n      selection.addRange(range);\n    },\n    getActiveSectionIndexFromDOM: () => {\n      const activeElement = getActiveElement(document);\n      if (!activeElement || !sectionListRef.current || !sectionListRef.current.getRoot().contains(activeElement)) {\n        return null;\n      }\n      return sectionListRef.current.getSectionIndexFromDOMElement(activeElement);\n    },\n    focusField: (newSelectedSections = 0) => {\n      if (!sectionListRef.current) {\n        return;\n      }\n      const newParsedSelectedSections = parseSelectedSections(newSelectedSections, state.sections);\n      setFocused(true);\n      sectionListRef.current.getSectionContent(newParsedSelectedSections).focus();\n    },\n    setSelectedSections: newSelectedSections => {\n      if (!sectionListRef.current) {\n        return;\n      }\n      const newParsedSelectedSections = parseSelectedSections(newSelectedSections, state.sections);\n      const newActiveSectionIndex = newParsedSelectedSections === 'all' ? 0 : newParsedSelectedSections;\n      setFocused(newActiveSectionIndex !== null);\n      setSelectedSections(newSelectedSections);\n    },\n    isFieldFocused: () => {\n      const activeElement = getActiveElement(document);\n      return !!sectionListRef.current && sectionListRef.current.getRoot().contains(activeElement);\n    }\n  }), [parsedSelectedSections, setSelectedSections, state.sections, focused]);\n\n  /**\n   * If a section content has been updated with a value we don't want to keep,\n   * Then we need to imperatively revert it (we can't let React do it because the value did not change in his internal representation).\n   */\n  const revertDOMSectionChange = useEventCallback(sectionIndex => {\n    if (!sectionListRef.current) {\n      return;\n    }\n    const section = state.sections[sectionIndex];\n    sectionListRef.current.getSectionContent(sectionIndex).innerHTML = section.value || section.placeholder;\n    interactions.syncSelectionToDOM();\n  });\n  const handleContainerClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleContainerClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented() || !sectionListRef.current) {\n      return;\n    }\n    setFocused(true);\n    onClick?.(event, ...args);\n    if (parsedSelectedSections === 'all') {\n      setTimeout(() => {\n        const cursorPosition = document.getSelection().getRangeAt(0).startOffset;\n        if (cursorPosition === 0) {\n          setSelectedSections(sectionOrder.startIndex);\n          return;\n        }\n        let sectionIndex = 0;\n        let cursorOnStartOfSection = 0;\n        while (cursorOnStartOfSection < cursorPosition && sectionIndex < state.sections.length) {\n          const section = state.sections[sectionIndex];\n          sectionIndex += 1;\n          cursorOnStartOfSection += `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`.length;\n        }\n        setSelectedSections(sectionIndex - 1);\n      });\n    } else if (!focused) {\n      setFocused(true);\n      setSelectedSections(sectionOrder.startIndex);\n    } else {\n      const hasClickedOnASection = sectionListRef.current.getRoot().contains(event.target);\n      if (!hasClickedOnASection) {\n        setSelectedSections(sectionOrder.startIndex);\n      }\n    }\n  });\n  const handleContainerInput = useEventCallback(event => {\n    onInput?.(event);\n    if (!sectionListRef.current || parsedSelectedSections !== 'all') {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    sectionListRef.current.getRoot().innerHTML = state.sections.map(section => `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`).join('');\n    interactions.syncSelectionToDOM();\n    if (keyPressed.length === 0 || keyPressed.charCodeAt(0) === 10) {\n      resetCharacterQuery();\n      clearValue();\n      setSelectedSections('all');\n    } else if (keyPressed.length > 1) {\n      updateValueFromValueStr(keyPressed);\n    } else {\n      if (parsedSelectedSections === 'all') {\n        setSelectedSections(0);\n      }\n      applyCharacterEditing({\n        keyPressed,\n        sectionIndex: 0\n      });\n    }\n  });\n  const handleContainerPaste = useEventCallback(event => {\n    onPaste?.(event);\n    if (readOnly || parsedSelectedSections !== 'all') {\n      event.preventDefault();\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    event.preventDefault();\n    resetCharacterQuery();\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerFocus = useEventCallback((...args) => {\n    onFocus?.(...args);\n    if (focused || !sectionListRef.current) {\n      return;\n    }\n    setFocused(true);\n    const isFocusInsideASection = sectionListRef.current.getSectionIndexFromDOMElement(getActiveElement(document)) != null;\n    if (!isFocusInsideASection) {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleContainerBlur = useEventCallback((...args) => {\n    onBlur?.(...args);\n    setTimeout(() => {\n      if (!sectionListRef.current) {\n        return;\n      }\n      const activeElement = getActiveElement(document);\n      const shouldBlur = !sectionListRef.current.getRoot().contains(activeElement);\n      if (shouldBlur) {\n        setFocused(false);\n        setSelectedSections(null);\n      }\n    });\n  });\n  const getInputContainerClickHandler = useEventCallback(sectionIndex => event => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call to this function is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  });\n  const handleInputContentMouseUp = useEventCallback(event => {\n    // Without this, the browser will remove the selected when clicking inside an already-selected section.\n    event.preventDefault();\n  });\n  const getInputContentFocusHandler = useEventCallback(sectionIndex => () => {\n    setSelectedSections(sectionIndex);\n  });\n  const handleInputContentPaste = useEventCallback(event => {\n    // prevent default to avoid the input `onInput` handler being called\n    event.preventDefault();\n    if (readOnly || disabled || typeof parsedSelectedSections !== 'number') {\n      return;\n    }\n    const activeSection = state.sections[parsedSelectedSections];\n    const pastedValue = event.clipboardData.getData('text');\n    const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n    const digitsOnly = /^[0-9]+$/.test(pastedValue);\n    const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n    const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n    if (isValidPastedValue) {\n      resetCharacterQuery();\n      updateSectionValue({\n        activeSection,\n        newSectionValue: pastedValue,\n        shouldGoToNextSection: true\n      });\n    }\n    // If the pasted value corresponds to a single section, but not the expected type, we skip the modification\n    else if (!lettersOnly && !digitsOnly) {\n      resetCharacterQuery();\n      updateValueFromValueStr(pastedValue);\n    }\n  });\n  const handleInputContentDragOver = useEventCallback(event => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'none';\n  });\n  const handleInputContentInput = useEventCallback(event => {\n    if (!sectionListRef.current) {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    const sectionIndex = sectionListRef.current.getSectionIndexFromDOMElement(target);\n    const section = state.sections[sectionIndex];\n    if (readOnly || !sectionListRef.current) {\n      revertDOMSectionChange(sectionIndex);\n      return;\n    }\n    if (keyPressed.length === 0) {\n      if (section.value === '') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      const inputType = event.nativeEvent.inputType;\n      if (inputType === 'insertParagraph' || inputType === 'insertLineBreak') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      resetCharacterQuery();\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex\n    });\n\n    // The DOM value needs to remain the one React is expecting.\n    revertDOMSectionChange(sectionIndex);\n  });\n  useEnhancedEffect(() => {\n    if (!focused || !sectionListRef.current) {\n      return;\n    }\n    if (parsedSelectedSections === 'all') {\n      sectionListRef.current.getRoot().focus();\n    } else if (typeof parsedSelectedSections === 'number') {\n      const domElement = sectionListRef.current.getSectionContent(parsedSelectedSections);\n      if (domElement) {\n        domElement.focus();\n      }\n    }\n  }, [parsedSelectedSections, focused]);\n  const sectionBoundaries = React.useMemo(() => {\n    return state.sections.reduce((acc, next) => {\n      acc[next.type] = sectionsValueBoundaries[next.type]({\n        currentDate: null,\n        contentType: next.contentType,\n        format: next.format\n      });\n      return acc;\n    }, {});\n  }, [sectionsValueBoundaries, state.sections]);\n  const isContainerEditable = parsedSelectedSections === 'all';\n  const elements = React.useMemo(() => {\n    return state.sections.map((section, index) => {\n      const isEditable = !isContainerEditable && !disabled && !readOnly;\n      return {\n        container: {\n          'data-sectionindex': index,\n          onClick: getInputContainerClickHandler(index)\n        },\n        content: {\n          tabIndex: isContainerEditable || index > 0 ? -1 : 0,\n          contentEditable: !isContainerEditable && !disabled && !readOnly,\n          role: 'spinbutton',\n          id: `${id}-${section.type}`,\n          'aria-labelledby': `${id}-${section.type}`,\n          'aria-readonly': readOnly,\n          'aria-valuenow': getSectionValueNow(section, utils),\n          'aria-valuemin': sectionBoundaries[section.type].minimum,\n          'aria-valuemax': sectionBoundaries[section.type].maximum,\n          'aria-valuetext': section.value ? getSectionValueText(section, utils) : translations.empty,\n          'aria-label': translations[section.type],\n          'aria-disabled': disabled,\n          spellCheck: isEditable ? false : undefined,\n          autoCapitalize: isEditable ? 'off' : undefined,\n          autoCorrect: isEditable ? 'off' : undefined,\n          [parseInt(React.version, 10) >= 17 ? 'enterKeyHint' : 'enterkeyhint']: isEditable ? 'next' : undefined,\n          children: section.value || section.placeholder,\n          onInput: handleInputContentInput,\n          onPaste: handleInputContentPaste,\n          onFocus: getInputContentFocusHandler(index),\n          onDragOver: handleInputContentDragOver,\n          onMouseUp: handleInputContentMouseUp,\n          inputMode: section.contentType === 'letter' ? 'text' : 'numeric'\n        },\n        before: {\n          children: section.startSeparator\n        },\n        after: {\n          children: section.endSeparator\n        }\n      };\n    });\n  }, [state.sections, getInputContentFocusHandler, handleInputContentPaste, handleInputContentDragOver, handleInputContentInput, getInputContainerClickHandler, handleInputContentMouseUp, disabled, readOnly, isContainerEditable, translations, utils, sectionBoundaries, id]);\n  const handleValueStrChange = useEventCallback(event => {\n    updateValueFromValueStr(event.target.value);\n  });\n  const valueStr = React.useMemo(() => areAllSectionsEmpty ? '' : fieldValueManager.getV7HiddenInputValueFromSections(state.sections), [areAllSectionsEmpty, state.sections, fieldValueManager]);\n  React.useEffect(() => {\n    if (sectionListRef.current == null) {\n      throw new Error(['MUI X: The `sectionListRef` prop has not been initialized by `PickersSectionList`', 'You probably tried to pass a component to the `textField` slot that contains an `<input />` element instead of a `PickersSectionList`.', '', 'If you want to keep using an `<input />` HTML element for the editing, please remove the `enableAccessibleFieldDOMStructure` prop from your picker or field component:', '', '<DatePicker slots={{ textField: MyCustomTextField }} />', '', 'Learn more about the field accessible DOM structure on the MUI documentation: https://mui.com/x/react-date-pickers/fields/#fields-to-edit-a-single-element'].join('\\n'));\n    }\n    if (autoFocus && sectionListRef.current) {\n      sectionListRef.current.getSectionContent(sectionOrder.startIndex).focus();\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  return {\n    interactions,\n    returnedValue: {\n      // Forwarded\n      autoFocus,\n      readOnly,\n      focused: focusedProp ?? focused,\n      sectionListRef: handleSectionListRef,\n      onBlur: handleContainerBlur,\n      onClick: handleContainerClick,\n      onFocus: handleContainerFocus,\n      onInput: handleContainerInput,\n      onPaste: handleContainerPaste,\n      // Additional\n      enableAccessibleFieldDOMStructure: true,\n      elements,\n      // TODO v7: Try to set to undefined when there is a section selected.\n      tabIndex: parsedSelectedSections === 0 ? -1 : 0,\n      contentEditable: isContainerEditable,\n      value: valueStr,\n      onChange: handleValueStrChange,\n      areAllSectionsEmpty\n    }\n  };\n};", "map": {"version": 3, "names": ["React", "useForkRef", "useEventCallback", "useEnhancedEffect", "useId", "getSectionValueNow", "getSectionValueText", "parseSelectedSections", "getActiveElement", "usePickersTranslations", "useUtils", "useFieldV7TextField", "params", "internalProps", "disabled", "readOnly", "forwardedProps", "sectionListRef", "inSectionListRef", "onBlur", "onClick", "onFocus", "onInput", "onPaste", "focused", "focusedProp", "autoFocus", "field<PERSON><PERSON>ueManager", "applyCharacterEditing", "resetCharacterQuery", "setSelectedSections", "parsedSelectedSections", "state", "clearActiveSection", "clearValue", "updateSectionValue", "updateValueFromValueStr", "sectionOrder", "areAllSectionsEmpty", "sectionsValueBoundaries", "useRef", "handleSectionListRef", "translations", "utils", "id", "setFocused", "useState", "interactions", "useMemo", "syncSelectionToDOM", "current", "selection", "document", "getSelection", "rangeCount", "getRoot", "contains", "getRangeAt", "startContainer", "removeAllRanges", "blur", "range", "window", "Range", "target", "section", "sections", "type", "getSectionContainer", "getSectionContent", "selectNodeContents", "focus", "addRange", "getActiveSectionIndexFromDOM", "activeElement", "getSectionIndexFromDOMElement", "focusField", "newSelectedSections", "newParsedSelectedSections", "newActiveSectionIndex", "isFieldFocused", "revertDOMSectionChange", "sectionIndex", "innerHTML", "value", "placeholder", "handleContainerClick", "event", "args", "isDefaultPrevented", "setTimeout", "cursorPosition", "startOffset", "startIndex", "cursorOnStartOfSection", "length", "startSeparator", "endSeparator", "hasClickedOnASection", "handleContainerInput", "keyPressed", "textContent", "map", "join", "charCodeAt", "handleContainerPaste", "preventDefault", "pastedValue", "clipboardData", "getData", "handleContainerFocus", "isFocusInsideASection", "handleContainerBlur", "<PERSON><PERSON><PERSON><PERSON>", "getInputContainerClickHandler", "handleInputContentMouseUp", "getInputContentFocusHandler", "handleInputContentPaste", "activeSection", "lettersOnly", "test", "digitsOnly", "digitsAndLetterOnly", "isValidPastedValue", "contentType", "newSectionValue", "shouldGoToNextSection", "handleInputContentDragOver", "dataTransfer", "dropEffect", "handleInputContentInput", "inputType", "nativeEvent", "dom<PERSON>lement", "sectionBoundaries", "reduce", "acc", "next", "currentDate", "format", "isContainerEditable", "elements", "index", "isEditable", "container", "content", "tabIndex", "contentEditable", "role", "minimum", "maximum", "empty", "spell<PERSON>heck", "undefined", "autoCapitalize", "autoCorrect", "parseInt", "version", "children", "onDragOver", "onMouseUp", "inputMode", "before", "after", "handleValueStrChange", "valueStr", "getV7HiddenInputValueFromSections", "useEffect", "Error", "returnedValue", "enableAccessibleFieldDOMStructure", "onChange"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useField/useFieldV7TextField.js"], "sourcesContent": ["import * as React from 'react';\nimport useForkRef from '@mui/utils/useForkRef';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useId from '@mui/utils/useId';\nimport { getSectionValueNow, getSectionValueText, parseSelectedSections } from \"./useField.utils.js\";\nimport { getActiveElement } from \"../../utils/utils.js\";\nimport { usePickersTranslations } from \"../../../hooks/usePickersTranslations.js\";\nimport { useUtils } from \"../useUtils.js\";\nexport const useFieldV7TextField = params => {\n  const {\n    internalProps: {\n      disabled,\n      readOnly = false\n    },\n    forwardedProps: {\n      sectionListRef: inSectionListRef,\n      onBlur,\n      onClick,\n      onFocus,\n      onInput,\n      onPaste,\n      focused: focusedProp,\n      autoFocus = false\n    },\n    fieldValueManager,\n    applyCharacterEditing,\n    resetCharacterQuery,\n    setSelectedSections,\n    parsedSelectedSections,\n    state,\n    clearActiveSection,\n    clearValue,\n    updateSectionValue,\n    updateValueFromValueStr,\n    sectionOrder,\n    areAllSectionsEmpty,\n    sectionsValueBoundaries\n  } = params;\n  const sectionListRef = React.useRef(null);\n  const handleSectionListRef = useForkRef(inSectionListRef, sectionListRef);\n  const translations = usePickersTranslations();\n  const utils = useUtils();\n  const id = useId();\n  const [focused, setFocused] = React.useState(false);\n  const interactions = React.useMemo(() => ({\n    syncSelectionToDOM: () => {\n      if (!sectionListRef.current) {\n        return;\n      }\n      const selection = document.getSelection();\n      if (!selection) {\n        return;\n      }\n      if (parsedSelectedSections == null) {\n        // If the selection contains an element inside the field, we reset it.\n        if (selection.rangeCount > 0 && sectionListRef.current.getRoot().contains(selection.getRangeAt(0).startContainer)) {\n          selection.removeAllRanges();\n        }\n        if (focused) {\n          sectionListRef.current.getRoot().blur();\n        }\n        return;\n      }\n\n      // On multi input range pickers we want to update selection range only for the active input\n      if (!sectionListRef.current.getRoot().contains(getActiveElement(document))) {\n        return;\n      }\n      const range = new window.Range();\n      let target;\n      if (parsedSelectedSections === 'all') {\n        target = sectionListRef.current.getRoot();\n      } else {\n        const section = state.sections[parsedSelectedSections];\n        if (section.type === 'empty') {\n          target = sectionListRef.current.getSectionContainer(parsedSelectedSections);\n        } else {\n          target = sectionListRef.current.getSectionContent(parsedSelectedSections);\n        }\n      }\n      range.selectNodeContents(target);\n      target.focus();\n      selection.removeAllRanges();\n      selection.addRange(range);\n    },\n    getActiveSectionIndexFromDOM: () => {\n      const activeElement = getActiveElement(document);\n      if (!activeElement || !sectionListRef.current || !sectionListRef.current.getRoot().contains(activeElement)) {\n        return null;\n      }\n      return sectionListRef.current.getSectionIndexFromDOMElement(activeElement);\n    },\n    focusField: (newSelectedSections = 0) => {\n      if (!sectionListRef.current) {\n        return;\n      }\n      const newParsedSelectedSections = parseSelectedSections(newSelectedSections, state.sections);\n      setFocused(true);\n      sectionListRef.current.getSectionContent(newParsedSelectedSections).focus();\n    },\n    setSelectedSections: newSelectedSections => {\n      if (!sectionListRef.current) {\n        return;\n      }\n      const newParsedSelectedSections = parseSelectedSections(newSelectedSections, state.sections);\n      const newActiveSectionIndex = newParsedSelectedSections === 'all' ? 0 : newParsedSelectedSections;\n      setFocused(newActiveSectionIndex !== null);\n      setSelectedSections(newSelectedSections);\n    },\n    isFieldFocused: () => {\n      const activeElement = getActiveElement(document);\n      return !!sectionListRef.current && sectionListRef.current.getRoot().contains(activeElement);\n    }\n  }), [parsedSelectedSections, setSelectedSections, state.sections, focused]);\n\n  /**\n   * If a section content has been updated with a value we don't want to keep,\n   * Then we need to imperatively revert it (we can't let React do it because the value did not change in his internal representation).\n   */\n  const revertDOMSectionChange = useEventCallback(sectionIndex => {\n    if (!sectionListRef.current) {\n      return;\n    }\n    const section = state.sections[sectionIndex];\n    sectionListRef.current.getSectionContent(sectionIndex).innerHTML = section.value || section.placeholder;\n    interactions.syncSelectionToDOM();\n  });\n  const handleContainerClick = useEventCallback((event, ...args) => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call of `handleContainerClick` is actually intended, or a side effect.\n    if (event.isDefaultPrevented() || !sectionListRef.current) {\n      return;\n    }\n    setFocused(true);\n    onClick?.(event, ...args);\n    if (parsedSelectedSections === 'all') {\n      setTimeout(() => {\n        const cursorPosition = document.getSelection().getRangeAt(0).startOffset;\n        if (cursorPosition === 0) {\n          setSelectedSections(sectionOrder.startIndex);\n          return;\n        }\n        let sectionIndex = 0;\n        let cursorOnStartOfSection = 0;\n        while (cursorOnStartOfSection < cursorPosition && sectionIndex < state.sections.length) {\n          const section = state.sections[sectionIndex];\n          sectionIndex += 1;\n          cursorOnStartOfSection += `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`.length;\n        }\n        setSelectedSections(sectionIndex - 1);\n      });\n    } else if (!focused) {\n      setFocused(true);\n      setSelectedSections(sectionOrder.startIndex);\n    } else {\n      const hasClickedOnASection = sectionListRef.current.getRoot().contains(event.target);\n      if (!hasClickedOnASection) {\n        setSelectedSections(sectionOrder.startIndex);\n      }\n    }\n  });\n  const handleContainerInput = useEventCallback(event => {\n    onInput?.(event);\n    if (!sectionListRef.current || parsedSelectedSections !== 'all') {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    sectionListRef.current.getRoot().innerHTML = state.sections.map(section => `${section.startSeparator}${section.value || section.placeholder}${section.endSeparator}`).join('');\n    interactions.syncSelectionToDOM();\n    if (keyPressed.length === 0 || keyPressed.charCodeAt(0) === 10) {\n      resetCharacterQuery();\n      clearValue();\n      setSelectedSections('all');\n    } else if (keyPressed.length > 1) {\n      updateValueFromValueStr(keyPressed);\n    } else {\n      if (parsedSelectedSections === 'all') {\n        setSelectedSections(0);\n      }\n      applyCharacterEditing({\n        keyPressed,\n        sectionIndex: 0\n      });\n    }\n  });\n  const handleContainerPaste = useEventCallback(event => {\n    onPaste?.(event);\n    if (readOnly || parsedSelectedSections !== 'all') {\n      event.preventDefault();\n      return;\n    }\n    const pastedValue = event.clipboardData.getData('text');\n    event.preventDefault();\n    resetCharacterQuery();\n    updateValueFromValueStr(pastedValue);\n  });\n  const handleContainerFocus = useEventCallback((...args) => {\n    onFocus?.(...args);\n    if (focused || !sectionListRef.current) {\n      return;\n    }\n    setFocused(true);\n    const isFocusInsideASection = sectionListRef.current.getSectionIndexFromDOMElement(getActiveElement(document)) != null;\n    if (!isFocusInsideASection) {\n      setSelectedSections(sectionOrder.startIndex);\n    }\n  });\n  const handleContainerBlur = useEventCallback((...args) => {\n    onBlur?.(...args);\n    setTimeout(() => {\n      if (!sectionListRef.current) {\n        return;\n      }\n      const activeElement = getActiveElement(document);\n      const shouldBlur = !sectionListRef.current.getRoot().contains(activeElement);\n      if (shouldBlur) {\n        setFocused(false);\n        setSelectedSections(null);\n      }\n    });\n  });\n  const getInputContainerClickHandler = useEventCallback(sectionIndex => event => {\n    // The click event on the clear button would propagate to the input, trigger this handler and result in a wrong section selection.\n    // We avoid this by checking if the call to this function is actually intended, or a side effect.\n    if (event.isDefaultPrevented()) {\n      return;\n    }\n    setSelectedSections(sectionIndex);\n  });\n  const handleInputContentMouseUp = useEventCallback(event => {\n    // Without this, the browser will remove the selected when clicking inside an already-selected section.\n    event.preventDefault();\n  });\n  const getInputContentFocusHandler = useEventCallback(sectionIndex => () => {\n    setSelectedSections(sectionIndex);\n  });\n  const handleInputContentPaste = useEventCallback(event => {\n    // prevent default to avoid the input `onInput` handler being called\n    event.preventDefault();\n    if (readOnly || disabled || typeof parsedSelectedSections !== 'number') {\n      return;\n    }\n    const activeSection = state.sections[parsedSelectedSections];\n    const pastedValue = event.clipboardData.getData('text');\n    const lettersOnly = /^[a-zA-Z]+$/.test(pastedValue);\n    const digitsOnly = /^[0-9]+$/.test(pastedValue);\n    const digitsAndLetterOnly = /^(([a-zA-Z]+)|)([0-9]+)(([a-zA-Z]+)|)$/.test(pastedValue);\n    const isValidPastedValue = activeSection.contentType === 'letter' && lettersOnly || activeSection.contentType === 'digit' && digitsOnly || activeSection.contentType === 'digit-with-letter' && digitsAndLetterOnly;\n    if (isValidPastedValue) {\n      resetCharacterQuery();\n      updateSectionValue({\n        activeSection,\n        newSectionValue: pastedValue,\n        shouldGoToNextSection: true\n      });\n    }\n    // If the pasted value corresponds to a single section, but not the expected type, we skip the modification\n    else if (!lettersOnly && !digitsOnly) {\n      resetCharacterQuery();\n      updateValueFromValueStr(pastedValue);\n    }\n  });\n  const handleInputContentDragOver = useEventCallback(event => {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'none';\n  });\n  const handleInputContentInput = useEventCallback(event => {\n    if (!sectionListRef.current) {\n      return;\n    }\n    const target = event.target;\n    const keyPressed = target.textContent ?? '';\n    const sectionIndex = sectionListRef.current.getSectionIndexFromDOMElement(target);\n    const section = state.sections[sectionIndex];\n    if (readOnly || !sectionListRef.current) {\n      revertDOMSectionChange(sectionIndex);\n      return;\n    }\n    if (keyPressed.length === 0) {\n      if (section.value === '') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      const inputType = event.nativeEvent.inputType;\n      if (inputType === 'insertParagraph' || inputType === 'insertLineBreak') {\n        revertDOMSectionChange(sectionIndex);\n        return;\n      }\n      resetCharacterQuery();\n      clearActiveSection();\n      return;\n    }\n    applyCharacterEditing({\n      keyPressed,\n      sectionIndex\n    });\n\n    // The DOM value needs to remain the one React is expecting.\n    revertDOMSectionChange(sectionIndex);\n  });\n  useEnhancedEffect(() => {\n    if (!focused || !sectionListRef.current) {\n      return;\n    }\n    if (parsedSelectedSections === 'all') {\n      sectionListRef.current.getRoot().focus();\n    } else if (typeof parsedSelectedSections === 'number') {\n      const domElement = sectionListRef.current.getSectionContent(parsedSelectedSections);\n      if (domElement) {\n        domElement.focus();\n      }\n    }\n  }, [parsedSelectedSections, focused]);\n  const sectionBoundaries = React.useMemo(() => {\n    return state.sections.reduce((acc, next) => {\n      acc[next.type] = sectionsValueBoundaries[next.type]({\n        currentDate: null,\n        contentType: next.contentType,\n        format: next.format\n      });\n      return acc;\n    }, {});\n  }, [sectionsValueBoundaries, state.sections]);\n  const isContainerEditable = parsedSelectedSections === 'all';\n  const elements = React.useMemo(() => {\n    return state.sections.map((section, index) => {\n      const isEditable = !isContainerEditable && !disabled && !readOnly;\n      return {\n        container: {\n          'data-sectionindex': index,\n          onClick: getInputContainerClickHandler(index)\n        },\n        content: {\n          tabIndex: isContainerEditable || index > 0 ? -1 : 0,\n          contentEditable: !isContainerEditable && !disabled && !readOnly,\n          role: 'spinbutton',\n          id: `${id}-${section.type}`,\n          'aria-labelledby': `${id}-${section.type}`,\n          'aria-readonly': readOnly,\n          'aria-valuenow': getSectionValueNow(section, utils),\n          'aria-valuemin': sectionBoundaries[section.type].minimum,\n          'aria-valuemax': sectionBoundaries[section.type].maximum,\n          'aria-valuetext': section.value ? getSectionValueText(section, utils) : translations.empty,\n          'aria-label': translations[section.type],\n          'aria-disabled': disabled,\n          spellCheck: isEditable ? false : undefined,\n          autoCapitalize: isEditable ? 'off' : undefined,\n          autoCorrect: isEditable ? 'off' : undefined,\n          [parseInt(React.version, 10) >= 17 ? 'enterKeyHint' : 'enterkeyhint']: isEditable ? 'next' : undefined,\n          children: section.value || section.placeholder,\n          onInput: handleInputContentInput,\n          onPaste: handleInputContentPaste,\n          onFocus: getInputContentFocusHandler(index),\n          onDragOver: handleInputContentDragOver,\n          onMouseUp: handleInputContentMouseUp,\n          inputMode: section.contentType === 'letter' ? 'text' : 'numeric'\n        },\n        before: {\n          children: section.startSeparator\n        },\n        after: {\n          children: section.endSeparator\n        }\n      };\n    });\n  }, [state.sections, getInputContentFocusHandler, handleInputContentPaste, handleInputContentDragOver, handleInputContentInput, getInputContainerClickHandler, handleInputContentMouseUp, disabled, readOnly, isContainerEditable, translations, utils, sectionBoundaries, id]);\n  const handleValueStrChange = useEventCallback(event => {\n    updateValueFromValueStr(event.target.value);\n  });\n  const valueStr = React.useMemo(() => areAllSectionsEmpty ? '' : fieldValueManager.getV7HiddenInputValueFromSections(state.sections), [areAllSectionsEmpty, state.sections, fieldValueManager]);\n  React.useEffect(() => {\n    if (sectionListRef.current == null) {\n      throw new Error(['MUI X: The `sectionListRef` prop has not been initialized by `PickersSectionList`', 'You probably tried to pass a component to the `textField` slot that contains an `<input />` element instead of a `PickersSectionList`.', '', 'If you want to keep using an `<input />` HTML element for the editing, please remove the `enableAccessibleFieldDOMStructure` prop from your picker or field component:', '', '<DatePicker slots={{ textField: MyCustomTextField }} />', '', 'Learn more about the field accessible DOM structure on the MUI documentation: https://mui.com/x/react-date-pickers/fields/#fields-to-edit-a-single-element'].join('\\n'));\n    }\n    if (autoFocus && sectionListRef.current) {\n      sectionListRef.current.getSectionContent(sectionOrder.startIndex).focus();\n    }\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  return {\n    interactions,\n    returnedValue: {\n      // Forwarded\n      autoFocus,\n      readOnly,\n      focused: focusedProp ?? focused,\n      sectionListRef: handleSectionListRef,\n      onBlur: handleContainerBlur,\n      onClick: handleContainerClick,\n      onFocus: handleContainerFocus,\n      onInput: handleContainerInput,\n      onPaste: handleContainerPaste,\n      // Additional\n      enableAccessibleFieldDOMStructure: true,\n      elements,\n      // TODO v7: Try to set to undefined when there is a section selected.\n      tabIndex: parsedSelectedSections === 0 ? -1 : 0,\n      contentEditable: isContainerEditable,\n      value: valueStr,\n      onChange: handleValueStrChange,\n      areAllSectionsEmpty\n    }\n  };\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,KAAK,MAAM,kBAAkB;AACpC,SAASC,kBAAkB,EAAEC,mBAAmB,EAAEC,qBAAqB,QAAQ,qBAAqB;AACpG,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,sBAAsB,QAAQ,0CAA0C;AACjF,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,OAAO,MAAMC,mBAAmB,GAAGC,MAAM,IAAI;EAC3C,MAAM;IACJC,aAAa,EAAE;MACbC,QAAQ;MACRC,QAAQ,GAAG;IACb,CAAC;IACDC,cAAc,EAAE;MACdC,cAAc,EAAEC,gBAAgB;MAChCC,MAAM;MACNC,OAAO;MACPC,OAAO;MACPC,OAAO;MACPC,OAAO;MACPC,OAAO,EAAEC,WAAW;MACpBC,SAAS,GAAG;IACd,CAAC;IACDC,iBAAiB;IACjBC,qBAAqB;IACrBC,mBAAmB;IACnBC,mBAAmB;IACnBC,sBAAsB;IACtBC,KAAK;IACLC,kBAAkB;IAClBC,UAAU;IACVC,kBAAkB;IAClBC,uBAAuB;IACvBC,YAAY;IACZC,mBAAmB;IACnBC;EACF,CAAC,GAAG3B,MAAM;EACV,MAAMK,cAAc,GAAGjB,KAAK,CAACwC,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMC,oBAAoB,GAAGxC,UAAU,CAACiB,gBAAgB,EAAED,cAAc,CAAC;EACzE,MAAMyB,YAAY,GAAGjC,sBAAsB,CAAC,CAAC;EAC7C,MAAMkC,KAAK,GAAGjC,QAAQ,CAAC,CAAC;EACxB,MAAMkC,EAAE,GAAGxC,KAAK,CAAC,CAAC;EAClB,MAAM,CAACoB,OAAO,EAAEqB,UAAU,CAAC,GAAG7C,KAAK,CAAC8C,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAMC,YAAY,GAAG/C,KAAK,CAACgD,OAAO,CAAC,OAAO;IACxCC,kBAAkB,EAAEA,CAAA,KAAM;MACxB,IAAI,CAAChC,cAAc,CAACiC,OAAO,EAAE;QAC3B;MACF;MACA,MAAMC,SAAS,GAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC;MACzC,IAAI,CAACF,SAAS,EAAE;QACd;MACF;MACA,IAAIpB,sBAAsB,IAAI,IAAI,EAAE;QAClC;QACA,IAAIoB,SAAS,CAACG,UAAU,GAAG,CAAC,IAAIrC,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACL,SAAS,CAACM,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,EAAE;UACjHP,SAAS,CAACQ,eAAe,CAAC,CAAC;QAC7B;QACA,IAAInC,OAAO,EAAE;UACXP,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAACK,IAAI,CAAC,CAAC;QACzC;QACA;MACF;;MAEA;MACA,IAAI,CAAC3C,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAChD,gBAAgB,CAAC4C,QAAQ,CAAC,CAAC,EAAE;QAC1E;MACF;MACA,MAAMS,KAAK,GAAG,IAAIC,MAAM,CAACC,KAAK,CAAC,CAAC;MAChC,IAAIC,MAAM;MACV,IAAIjC,sBAAsB,KAAK,KAAK,EAAE;QACpCiC,MAAM,GAAG/C,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC;MAC3C,CAAC,MAAM;QACL,MAAMU,OAAO,GAAGjC,KAAK,CAACkC,QAAQ,CAACnC,sBAAsB,CAAC;QACtD,IAAIkC,OAAO,CAACE,IAAI,KAAK,OAAO,EAAE;UAC5BH,MAAM,GAAG/C,cAAc,CAACiC,OAAO,CAACkB,mBAAmB,CAACrC,sBAAsB,CAAC;QAC7E,CAAC,MAAM;UACLiC,MAAM,GAAG/C,cAAc,CAACiC,OAAO,CAACmB,iBAAiB,CAACtC,sBAAsB,CAAC;QAC3E;MACF;MACA8B,KAAK,CAACS,kBAAkB,CAACN,MAAM,CAAC;MAChCA,MAAM,CAACO,KAAK,CAAC,CAAC;MACdpB,SAAS,CAACQ,eAAe,CAAC,CAAC;MAC3BR,SAAS,CAACqB,QAAQ,CAACX,KAAK,CAAC;IAC3B,CAAC;IACDY,4BAA4B,EAAEA,CAAA,KAAM;MAClC,MAAMC,aAAa,GAAGlE,gBAAgB,CAAC4C,QAAQ,CAAC;MAChD,IAAI,CAACsB,aAAa,IAAI,CAACzD,cAAc,CAACiC,OAAO,IAAI,CAACjC,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACkB,aAAa,CAAC,EAAE;QAC1G,OAAO,IAAI;MACb;MACA,OAAOzD,cAAc,CAACiC,OAAO,CAACyB,6BAA6B,CAACD,aAAa,CAAC;IAC5E,CAAC;IACDE,UAAU,EAAEA,CAACC,mBAAmB,GAAG,CAAC,KAAK;MACvC,IAAI,CAAC5D,cAAc,CAACiC,OAAO,EAAE;QAC3B;MACF;MACA,MAAM4B,yBAAyB,GAAGvE,qBAAqB,CAACsE,mBAAmB,EAAE7C,KAAK,CAACkC,QAAQ,CAAC;MAC5FrB,UAAU,CAAC,IAAI,CAAC;MAChB5B,cAAc,CAACiC,OAAO,CAACmB,iBAAiB,CAACS,yBAAyB,CAAC,CAACP,KAAK,CAAC,CAAC;IAC7E,CAAC;IACDzC,mBAAmB,EAAE+C,mBAAmB,IAAI;MAC1C,IAAI,CAAC5D,cAAc,CAACiC,OAAO,EAAE;QAC3B;MACF;MACA,MAAM4B,yBAAyB,GAAGvE,qBAAqB,CAACsE,mBAAmB,EAAE7C,KAAK,CAACkC,QAAQ,CAAC;MAC5F,MAAMa,qBAAqB,GAAGD,yBAAyB,KAAK,KAAK,GAAG,CAAC,GAAGA,yBAAyB;MACjGjC,UAAU,CAACkC,qBAAqB,KAAK,IAAI,CAAC;MAC1CjD,mBAAmB,CAAC+C,mBAAmB,CAAC;IAC1C,CAAC;IACDG,cAAc,EAAEA,CAAA,KAAM;MACpB,MAAMN,aAAa,GAAGlE,gBAAgB,CAAC4C,QAAQ,CAAC;MAChD,OAAO,CAAC,CAACnC,cAAc,CAACiC,OAAO,IAAIjC,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACkB,aAAa,CAAC;IAC7F;EACF,CAAC,CAAC,EAAE,CAAC3C,sBAAsB,EAAED,mBAAmB,EAAEE,KAAK,CAACkC,QAAQ,EAAE1C,OAAO,CAAC,CAAC;;EAE3E;AACF;AACA;AACA;EACE,MAAMyD,sBAAsB,GAAG/E,gBAAgB,CAACgF,YAAY,IAAI;IAC9D,IAAI,CAACjE,cAAc,CAACiC,OAAO,EAAE;MAC3B;IACF;IACA,MAAMe,OAAO,GAAGjC,KAAK,CAACkC,QAAQ,CAACgB,YAAY,CAAC;IAC5CjE,cAAc,CAACiC,OAAO,CAACmB,iBAAiB,CAACa,YAAY,CAAC,CAACC,SAAS,GAAGlB,OAAO,CAACmB,KAAK,IAAInB,OAAO,CAACoB,WAAW;IACvGtC,YAAY,CAACE,kBAAkB,CAAC,CAAC;EACnC,CAAC,CAAC;EACF,MAAMqC,oBAAoB,GAAGpF,gBAAgB,CAAC,CAACqF,KAAK,EAAE,GAAGC,IAAI,KAAK;IAChE;IACA;IACA,IAAID,KAAK,CAACE,kBAAkB,CAAC,CAAC,IAAI,CAACxE,cAAc,CAACiC,OAAO,EAAE;MACzD;IACF;IACAL,UAAU,CAAC,IAAI,CAAC;IAChBzB,OAAO,GAAGmE,KAAK,EAAE,GAAGC,IAAI,CAAC;IACzB,IAAIzD,sBAAsB,KAAK,KAAK,EAAE;MACpC2D,UAAU,CAAC,MAAM;QACf,MAAMC,cAAc,GAAGvC,QAAQ,CAACC,YAAY,CAAC,CAAC,CAACI,UAAU,CAAC,CAAC,CAAC,CAACmC,WAAW;QACxE,IAAID,cAAc,KAAK,CAAC,EAAE;UACxB7D,mBAAmB,CAACO,YAAY,CAACwD,UAAU,CAAC;UAC5C;QACF;QACA,IAAIX,YAAY,GAAG,CAAC;QACpB,IAAIY,sBAAsB,GAAG,CAAC;QAC9B,OAAOA,sBAAsB,GAAGH,cAAc,IAAIT,YAAY,GAAGlD,KAAK,CAACkC,QAAQ,CAAC6B,MAAM,EAAE;UACtF,MAAM9B,OAAO,GAAGjC,KAAK,CAACkC,QAAQ,CAACgB,YAAY,CAAC;UAC5CA,YAAY,IAAI,CAAC;UACjBY,sBAAsB,IAAI,GAAG7B,OAAO,CAAC+B,cAAc,GAAG/B,OAAO,CAACmB,KAAK,IAAInB,OAAO,CAACoB,WAAW,GAAGpB,OAAO,CAACgC,YAAY,EAAE,CAACF,MAAM;QAC5H;QACAjE,mBAAmB,CAACoD,YAAY,GAAG,CAAC,CAAC;MACvC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAAC1D,OAAO,EAAE;MACnBqB,UAAU,CAAC,IAAI,CAAC;MAChBf,mBAAmB,CAACO,YAAY,CAACwD,UAAU,CAAC;IAC9C,CAAC,MAAM;MACL,MAAMK,oBAAoB,GAAGjF,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAACC,QAAQ,CAAC+B,KAAK,CAACvB,MAAM,CAAC;MACpF,IAAI,CAACkC,oBAAoB,EAAE;QACzBpE,mBAAmB,CAACO,YAAY,CAACwD,UAAU,CAAC;MAC9C;IACF;EACF,CAAC,CAAC;EACF,MAAMM,oBAAoB,GAAGjG,gBAAgB,CAACqF,KAAK,IAAI;IACrDjE,OAAO,GAAGiE,KAAK,CAAC;IAChB,IAAI,CAACtE,cAAc,CAACiC,OAAO,IAAInB,sBAAsB,KAAK,KAAK,EAAE;MAC/D;IACF;IACA,MAAMiC,MAAM,GAAGuB,KAAK,CAACvB,MAAM;IAC3B,MAAMoC,UAAU,GAAGpC,MAAM,CAACqC,WAAW,IAAI,EAAE;IAC3CpF,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAAC4B,SAAS,GAAGnD,KAAK,CAACkC,QAAQ,CAACoC,GAAG,CAACrC,OAAO,IAAI,GAAGA,OAAO,CAAC+B,cAAc,GAAG/B,OAAO,CAACmB,KAAK,IAAInB,OAAO,CAACoB,WAAW,GAAGpB,OAAO,CAACgC,YAAY,EAAE,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC;IAC9KxD,YAAY,CAACE,kBAAkB,CAAC,CAAC;IACjC,IAAImD,UAAU,CAACL,MAAM,KAAK,CAAC,IAAIK,UAAU,CAACI,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;MAC9D3E,mBAAmB,CAAC,CAAC;MACrBK,UAAU,CAAC,CAAC;MACZJ,mBAAmB,CAAC,KAAK,CAAC;IAC5B,CAAC,MAAM,IAAIsE,UAAU,CAACL,MAAM,GAAG,CAAC,EAAE;MAChC3D,uBAAuB,CAACgE,UAAU,CAAC;IACrC,CAAC,MAAM;MACL,IAAIrE,sBAAsB,KAAK,KAAK,EAAE;QACpCD,mBAAmB,CAAC,CAAC,CAAC;MACxB;MACAF,qBAAqB,CAAC;QACpBwE,UAAU;QACVlB,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAMuB,oBAAoB,GAAGvG,gBAAgB,CAACqF,KAAK,IAAI;IACrDhE,OAAO,GAAGgE,KAAK,CAAC;IAChB,IAAIxE,QAAQ,IAAIgB,sBAAsB,KAAK,KAAK,EAAE;MAChDwD,KAAK,CAACmB,cAAc,CAAC,CAAC;MACtB;IACF;IACA,MAAMC,WAAW,GAAGpB,KAAK,CAACqB,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvDtB,KAAK,CAACmB,cAAc,CAAC,CAAC;IACtB7E,mBAAmB,CAAC,CAAC;IACrBO,uBAAuB,CAACuE,WAAW,CAAC;EACtC,CAAC,CAAC;EACF,MAAMG,oBAAoB,GAAG5G,gBAAgB,CAAC,CAAC,GAAGsF,IAAI,KAAK;IACzDnE,OAAO,GAAG,GAAGmE,IAAI,CAAC;IAClB,IAAIhE,OAAO,IAAI,CAACP,cAAc,CAACiC,OAAO,EAAE;MACtC;IACF;IACAL,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMkE,qBAAqB,GAAG9F,cAAc,CAACiC,OAAO,CAACyB,6BAA6B,CAACnE,gBAAgB,CAAC4C,QAAQ,CAAC,CAAC,IAAI,IAAI;IACtH,IAAI,CAAC2D,qBAAqB,EAAE;MAC1BjF,mBAAmB,CAACO,YAAY,CAACwD,UAAU,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,MAAMmB,mBAAmB,GAAG9G,gBAAgB,CAAC,CAAC,GAAGsF,IAAI,KAAK;IACxDrE,MAAM,GAAG,GAAGqE,IAAI,CAAC;IACjBE,UAAU,CAAC,MAAM;MACf,IAAI,CAACzE,cAAc,CAACiC,OAAO,EAAE;QAC3B;MACF;MACA,MAAMwB,aAAa,GAAGlE,gBAAgB,CAAC4C,QAAQ,CAAC;MAChD,MAAM6D,UAAU,GAAG,CAAChG,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAACC,QAAQ,CAACkB,aAAa,CAAC;MAC5E,IAAIuC,UAAU,EAAE;QACdpE,UAAU,CAAC,KAAK,CAAC;QACjBf,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMoF,6BAA6B,GAAGhH,gBAAgB,CAACgF,YAAY,IAAIK,KAAK,IAAI;IAC9E;IACA;IACA,IAAIA,KAAK,CAACE,kBAAkB,CAAC,CAAC,EAAE;MAC9B;IACF;IACA3D,mBAAmB,CAACoD,YAAY,CAAC;EACnC,CAAC,CAAC;EACF,MAAMiC,yBAAyB,GAAGjH,gBAAgB,CAACqF,KAAK,IAAI;IAC1D;IACAA,KAAK,CAACmB,cAAc,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMU,2BAA2B,GAAGlH,gBAAgB,CAACgF,YAAY,IAAI,MAAM;IACzEpD,mBAAmB,CAACoD,YAAY,CAAC;EACnC,CAAC,CAAC;EACF,MAAMmC,uBAAuB,GAAGnH,gBAAgB,CAACqF,KAAK,IAAI;IACxD;IACAA,KAAK,CAACmB,cAAc,CAAC,CAAC;IACtB,IAAI3F,QAAQ,IAAID,QAAQ,IAAI,OAAOiB,sBAAsB,KAAK,QAAQ,EAAE;MACtE;IACF;IACA,MAAMuF,aAAa,GAAGtF,KAAK,CAACkC,QAAQ,CAACnC,sBAAsB,CAAC;IAC5D,MAAM4E,WAAW,GAAGpB,KAAK,CAACqB,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACvD,MAAMU,WAAW,GAAG,aAAa,CAACC,IAAI,CAACb,WAAW,CAAC;IACnD,MAAMc,UAAU,GAAG,UAAU,CAACD,IAAI,CAACb,WAAW,CAAC;IAC/C,MAAMe,mBAAmB,GAAG,wCAAwC,CAACF,IAAI,CAACb,WAAW,CAAC;IACtF,MAAMgB,kBAAkB,GAAGL,aAAa,CAACM,WAAW,KAAK,QAAQ,IAAIL,WAAW,IAAID,aAAa,CAACM,WAAW,KAAK,OAAO,IAAIH,UAAU,IAAIH,aAAa,CAACM,WAAW,KAAK,mBAAmB,IAAIF,mBAAmB;IACnN,IAAIC,kBAAkB,EAAE;MACtB9F,mBAAmB,CAAC,CAAC;MACrBM,kBAAkB,CAAC;QACjBmF,aAAa;QACbO,eAAe,EAAElB,WAAW;QAC5BmB,qBAAqB,EAAE;MACzB,CAAC,CAAC;IACJ;IACA;IAAA,KACK,IAAI,CAACP,WAAW,IAAI,CAACE,UAAU,EAAE;MACpC5F,mBAAmB,CAAC,CAAC;MACrBO,uBAAuB,CAACuE,WAAW,CAAC;IACtC;EACF,CAAC,CAAC;EACF,MAAMoB,0BAA0B,GAAG7H,gBAAgB,CAACqF,KAAK,IAAI;IAC3DA,KAAK,CAACmB,cAAc,CAAC,CAAC;IACtBnB,KAAK,CAACyC,YAAY,CAACC,UAAU,GAAG,MAAM;EACxC,CAAC,CAAC;EACF,MAAMC,uBAAuB,GAAGhI,gBAAgB,CAACqF,KAAK,IAAI;IACxD,IAAI,CAACtE,cAAc,CAACiC,OAAO,EAAE;MAC3B;IACF;IACA,MAAMc,MAAM,GAAGuB,KAAK,CAACvB,MAAM;IAC3B,MAAMoC,UAAU,GAAGpC,MAAM,CAACqC,WAAW,IAAI,EAAE;IAC3C,MAAMnB,YAAY,GAAGjE,cAAc,CAACiC,OAAO,CAACyB,6BAA6B,CAACX,MAAM,CAAC;IACjF,MAAMC,OAAO,GAAGjC,KAAK,CAACkC,QAAQ,CAACgB,YAAY,CAAC;IAC5C,IAAInE,QAAQ,IAAI,CAACE,cAAc,CAACiC,OAAO,EAAE;MACvC+B,sBAAsB,CAACC,YAAY,CAAC;MACpC;IACF;IACA,IAAIkB,UAAU,CAACL,MAAM,KAAK,CAAC,EAAE;MAC3B,IAAI9B,OAAO,CAACmB,KAAK,KAAK,EAAE,EAAE;QACxBH,sBAAsB,CAACC,YAAY,CAAC;QACpC;MACF;MACA,MAAMiD,SAAS,GAAG5C,KAAK,CAAC6C,WAAW,CAACD,SAAS;MAC7C,IAAIA,SAAS,KAAK,iBAAiB,IAAIA,SAAS,KAAK,iBAAiB,EAAE;QACtElD,sBAAsB,CAACC,YAAY,CAAC;QACpC;MACF;MACArD,mBAAmB,CAAC,CAAC;MACrBI,kBAAkB,CAAC,CAAC;MACpB;IACF;IACAL,qBAAqB,CAAC;MACpBwE,UAAU;MACVlB;IACF,CAAC,CAAC;;IAEF;IACAD,sBAAsB,CAACC,YAAY,CAAC;EACtC,CAAC,CAAC;EACF/E,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACqB,OAAO,IAAI,CAACP,cAAc,CAACiC,OAAO,EAAE;MACvC;IACF;IACA,IAAInB,sBAAsB,KAAK,KAAK,EAAE;MACpCd,cAAc,CAACiC,OAAO,CAACK,OAAO,CAAC,CAAC,CAACgB,KAAK,CAAC,CAAC;IAC1C,CAAC,MAAM,IAAI,OAAOxC,sBAAsB,KAAK,QAAQ,EAAE;MACrD,MAAMsG,UAAU,GAAGpH,cAAc,CAACiC,OAAO,CAACmB,iBAAiB,CAACtC,sBAAsB,CAAC;MACnF,IAAIsG,UAAU,EAAE;QACdA,UAAU,CAAC9D,KAAK,CAAC,CAAC;MACpB;IACF;EACF,CAAC,EAAE,CAACxC,sBAAsB,EAAEP,OAAO,CAAC,CAAC;EACrC,MAAM8G,iBAAiB,GAAGtI,KAAK,CAACgD,OAAO,CAAC,MAAM;IAC5C,OAAOhB,KAAK,CAACkC,QAAQ,CAACqE,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MAC1CD,GAAG,CAACC,IAAI,CAACtE,IAAI,CAAC,GAAG5B,uBAAuB,CAACkG,IAAI,CAACtE,IAAI,CAAC,CAAC;QAClDuE,WAAW,EAAE,IAAI;QACjBd,WAAW,EAAEa,IAAI,CAACb,WAAW;QAC7Be,MAAM,EAAEF,IAAI,CAACE;MACf,CAAC,CAAC;MACF,OAAOH,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR,CAAC,EAAE,CAACjG,uBAAuB,EAAEP,KAAK,CAACkC,QAAQ,CAAC,CAAC;EAC7C,MAAM0E,mBAAmB,GAAG7G,sBAAsB,KAAK,KAAK;EAC5D,MAAM8G,QAAQ,GAAG7I,KAAK,CAACgD,OAAO,CAAC,MAAM;IACnC,OAAOhB,KAAK,CAACkC,QAAQ,CAACoC,GAAG,CAAC,CAACrC,OAAO,EAAE6E,KAAK,KAAK;MAC5C,MAAMC,UAAU,GAAG,CAACH,mBAAmB,IAAI,CAAC9H,QAAQ,IAAI,CAACC,QAAQ;MACjE,OAAO;QACLiI,SAAS,EAAE;UACT,mBAAmB,EAAEF,KAAK;UAC1B1H,OAAO,EAAE8F,6BAA6B,CAAC4B,KAAK;QAC9C,CAAC;QACDG,OAAO,EAAE;UACPC,QAAQ,EAAEN,mBAAmB,IAAIE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;UACnDK,eAAe,EAAE,CAACP,mBAAmB,IAAI,CAAC9H,QAAQ,IAAI,CAACC,QAAQ;UAC/DqI,IAAI,EAAE,YAAY;UAClBxG,EAAE,EAAE,GAAGA,EAAE,IAAIqB,OAAO,CAACE,IAAI,EAAE;UAC3B,iBAAiB,EAAE,GAAGvB,EAAE,IAAIqB,OAAO,CAACE,IAAI,EAAE;UAC1C,eAAe,EAAEpD,QAAQ;UACzB,eAAe,EAAEV,kBAAkB,CAAC4D,OAAO,EAAEtB,KAAK,CAAC;UACnD,eAAe,EAAE2F,iBAAiB,CAACrE,OAAO,CAACE,IAAI,CAAC,CAACkF,OAAO;UACxD,eAAe,EAAEf,iBAAiB,CAACrE,OAAO,CAACE,IAAI,CAAC,CAACmF,OAAO;UACxD,gBAAgB,EAAErF,OAAO,CAACmB,KAAK,GAAG9E,mBAAmB,CAAC2D,OAAO,EAAEtB,KAAK,CAAC,GAAGD,YAAY,CAAC6G,KAAK;UAC1F,YAAY,EAAE7G,YAAY,CAACuB,OAAO,CAACE,IAAI,CAAC;UACxC,eAAe,EAAErD,QAAQ;UACzB0I,UAAU,EAAET,UAAU,GAAG,KAAK,GAAGU,SAAS;UAC1CC,cAAc,EAAEX,UAAU,GAAG,KAAK,GAAGU,SAAS;UAC9CE,WAAW,EAAEZ,UAAU,GAAG,KAAK,GAAGU,SAAS;UAC3C,CAACG,QAAQ,CAAC5J,KAAK,CAAC6J,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,cAAc,GAAG,cAAc,GAAGd,UAAU,GAAG,MAAM,GAAGU,SAAS;UACtGK,QAAQ,EAAE7F,OAAO,CAACmB,KAAK,IAAInB,OAAO,CAACoB,WAAW;UAC9C/D,OAAO,EAAE4G,uBAAuB;UAChC3G,OAAO,EAAE8F,uBAAuB;UAChChG,OAAO,EAAE+F,2BAA2B,CAAC0B,KAAK,CAAC;UAC3CiB,UAAU,EAAEhC,0BAA0B;UACtCiC,SAAS,EAAE7C,yBAAyB;UACpC8C,SAAS,EAAEhG,OAAO,CAAC2D,WAAW,KAAK,QAAQ,GAAG,MAAM,GAAG;QACzD,CAAC;QACDsC,MAAM,EAAE;UACNJ,QAAQ,EAAE7F,OAAO,CAAC+B;QACpB,CAAC;QACDmE,KAAK,EAAE;UACLL,QAAQ,EAAE7F,OAAO,CAACgC;QACpB;MACF,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjE,KAAK,CAACkC,QAAQ,EAAEkD,2BAA2B,EAAEC,uBAAuB,EAAEU,0BAA0B,EAAEG,uBAAuB,EAAEhB,6BAA6B,EAAEC,yBAAyB,EAAErG,QAAQ,EAAEC,QAAQ,EAAE6H,mBAAmB,EAAElG,YAAY,EAAEC,KAAK,EAAE2F,iBAAiB,EAAE1F,EAAE,CAAC,CAAC;EAC9Q,MAAMwH,oBAAoB,GAAGlK,gBAAgB,CAACqF,KAAK,IAAI;IACrDnD,uBAAuB,CAACmD,KAAK,CAACvB,MAAM,CAACoB,KAAK,CAAC;EAC7C,CAAC,CAAC;EACF,MAAMiF,QAAQ,GAAGrK,KAAK,CAACgD,OAAO,CAAC,MAAMV,mBAAmB,GAAG,EAAE,GAAGX,iBAAiB,CAAC2I,iCAAiC,CAACtI,KAAK,CAACkC,QAAQ,CAAC,EAAE,CAAC5B,mBAAmB,EAAEN,KAAK,CAACkC,QAAQ,EAAEvC,iBAAiB,CAAC,CAAC;EAC9L3B,KAAK,CAACuK,SAAS,CAAC,MAAM;IACpB,IAAItJ,cAAc,CAACiC,OAAO,IAAI,IAAI,EAAE;MAClC,MAAM,IAAIsH,KAAK,CAAC,CAAC,mFAAmF,EAAE,wIAAwI,EAAE,EAAE,EAAE,wKAAwK,EAAE,EAAE,EAAE,yDAAyD,EAAE,EAAE,EAAE,4JAA4J,CAAC,CAACjE,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5oB;IACA,IAAI7E,SAAS,IAAIT,cAAc,CAACiC,OAAO,EAAE;MACvCjC,cAAc,CAACiC,OAAO,CAACmB,iBAAiB,CAAChC,YAAY,CAACwD,UAAU,CAAC,CAACtB,KAAK,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,OAAO;IACLxB,YAAY;IACZ0H,aAAa,EAAE;MACb;MACA/I,SAAS;MACTX,QAAQ;MACRS,OAAO,EAAEC,WAAW,IAAID,OAAO;MAC/BP,cAAc,EAAEwB,oBAAoB;MACpCtB,MAAM,EAAE6F,mBAAmB;MAC3B5F,OAAO,EAAEkE,oBAAoB;MAC7BjE,OAAO,EAAEyF,oBAAoB;MAC7BxF,OAAO,EAAE6E,oBAAoB;MAC7B5E,OAAO,EAAEkF,oBAAoB;MAC7B;MACAiE,iCAAiC,EAAE,IAAI;MACvC7B,QAAQ;MACR;MACAK,QAAQ,EAAEnH,sBAAsB,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAC/CoH,eAAe,EAAEP,mBAAmB;MACpCxD,KAAK,EAAEiF,QAAQ;MACfM,QAAQ,EAAEP,oBAAoB;MAC9B9H;IACF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}