{"ast": null, "code": "/*\n* Copyright 2012 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder.ec;\nimport IllegalArgumentException from '../../../IllegalArgumentException';\nimport System from '../../../util/System';\nimport StringBuilder from '../../../util/StringBuilder';\n/**\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.GenericGFPoly\n */\nvar ModulusPoly = /** @class */function () {\n  function ModulusPoly(field, coefficients) {\n    if (coefficients.length === 0) {\n      throw new IllegalArgumentException();\n    }\n    this.field = field;\n    var coefficientsLength = /*int*/coefficients.length;\n    if (coefficientsLength > 1 && coefficients[0] === 0) {\n      // Leading term must be non-zero for anything except the constant polynomial \"0\"\n      var firstNonZero = /*int*/1;\n      while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n        firstNonZero++;\n      }\n      if (firstNonZero === coefficientsLength) {\n        this.coefficients = new Int32Array([0]);\n      } else {\n        this.coefficients = new Int32Array(coefficientsLength - firstNonZero);\n        System.arraycopy(coefficients, firstNonZero, this.coefficients, 0, this.coefficients.length);\n      }\n    } else {\n      this.coefficients = coefficients;\n    }\n  }\n  ModulusPoly.prototype.getCoefficients = function () {\n    return this.coefficients;\n  };\n  /**\n   * @return degree of this polynomial\n   */\n  ModulusPoly.prototype.getDegree = function () {\n    return this.coefficients.length - 1;\n  };\n  /**\n   * @return true iff this polynomial is the monomial \"0\"\n   */\n  ModulusPoly.prototype.isZero = function () {\n    return this.coefficients[0] === 0;\n  };\n  /**\n   * @return coefficient of x^degree term in this polynomial\n   */\n  ModulusPoly.prototype.getCoefficient = function (degree) {\n    return this.coefficients[this.coefficients.length - 1 - degree];\n  };\n  /**\n   * @return evaluation of this polynomial at a given point\n   */\n  ModulusPoly.prototype.evaluateAt = function (a) {\n    var e_1, _a;\n    if (a === 0) {\n      // Just return the x^0 coefficient\n      return this.getCoefficient(0);\n    }\n    if (a === 1) {\n      // Just the sum of the coefficients\n      var sum = /*int*/0;\n      try {\n        for (var _b = __values(this.coefficients), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var coefficient = _c.value /*int*/;\n          sum = this.field.add(sum, coefficient);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      return sum;\n    }\n    var result = /*int*/this.coefficients[0];\n    var size = /*int*/this.coefficients.length;\n    for (var i /*int*/ = 1; i < size; i++) {\n      result = this.field.add(this.field.multiply(a, result), this.coefficients[i]);\n    }\n    return result;\n  };\n  ModulusPoly.prototype.add = function (other) {\n    if (!this.field.equals(other.field)) {\n      throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n    }\n    if (this.isZero()) {\n      return other;\n    }\n    if (other.isZero()) {\n      return this;\n    }\n    var smallerCoefficients = this.coefficients;\n    var largerCoefficients = other.coefficients;\n    if (smallerCoefficients.length > largerCoefficients.length) {\n      var temp = smallerCoefficients;\n      smallerCoefficients = largerCoefficients;\n      largerCoefficients = temp;\n    }\n    var sumDiff = new Int32Array(largerCoefficients.length);\n    var lengthDiff = /*int*/largerCoefficients.length - smallerCoefficients.length;\n    // Copy high-order terms only found in higher-degree polynomial's coefficients\n    System.arraycopy(largerCoefficients, 0, sumDiff, 0, lengthDiff);\n    for (var i /*int*/ = lengthDiff; i < largerCoefficients.length; i++) {\n      sumDiff[i] = this.field.add(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n    }\n    return new ModulusPoly(this.field, sumDiff);\n  };\n  ModulusPoly.prototype.subtract = function (other) {\n    if (!this.field.equals(other.field)) {\n      throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n    }\n    if (other.isZero()) {\n      return this;\n    }\n    return this.add(other.negative());\n  };\n  ModulusPoly.prototype.multiply = function (other) {\n    if (other instanceof ModulusPoly) {\n      return this.multiplyOther(other);\n    }\n    return this.multiplyScalar(other);\n  };\n  ModulusPoly.prototype.multiplyOther = function (other) {\n    if (!this.field.equals(other.field)) {\n      throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n    }\n    if (this.isZero() || other.isZero()) {\n      // return this.field.getZero();\n      return new ModulusPoly(this.field, new Int32Array([0]));\n    }\n    var aCoefficients = this.coefficients;\n    var aLength = /*int*/aCoefficients.length;\n    var bCoefficients = other.coefficients;\n    var bLength = /*int*/bCoefficients.length;\n    var product = new Int32Array(aLength + bLength - 1);\n    for (var i /*int*/ = 0; i < aLength; i++) {\n      var aCoeff = /*int*/aCoefficients[i];\n      for (var j /*int*/ = 0; j < bLength; j++) {\n        product[i + j] = this.field.add(product[i + j], this.field.multiply(aCoeff, bCoefficients[j]));\n      }\n    }\n    return new ModulusPoly(this.field, product);\n  };\n  ModulusPoly.prototype.negative = function () {\n    var size = /*int*/this.coefficients.length;\n    var negativeCoefficients = new Int32Array(size);\n    for (var i /*int*/ = 0; i < size; i++) {\n      negativeCoefficients[i] = this.field.subtract(0, this.coefficients[i]);\n    }\n    return new ModulusPoly(this.field, negativeCoefficients);\n  };\n  ModulusPoly.prototype.multiplyScalar = function (scalar) {\n    if (scalar === 0) {\n      return new ModulusPoly(this.field, new Int32Array([0]));\n    }\n    if (scalar === 1) {\n      return this;\n    }\n    var size = /*int*/this.coefficients.length;\n    var product = new Int32Array(size);\n    for (var i /*int*/ = 0; i < size; i++) {\n      product[i] = this.field.multiply(this.coefficients[i], scalar);\n    }\n    return new ModulusPoly(this.field, product);\n  };\n  ModulusPoly.prototype.multiplyByMonomial = function (degree, coefficient) {\n    if (degree < 0) {\n      throw new IllegalArgumentException();\n    }\n    if (coefficient === 0) {\n      return new ModulusPoly(this.field, new Int32Array([0]));\n    }\n    var size = /*int*/this.coefficients.length;\n    var product = new Int32Array(size + degree);\n    for (var i /*int*/ = 0; i < size; i++) {\n      product[i] = this.field.multiply(this.coefficients[i], coefficient);\n    }\n    return new ModulusPoly(this.field, product);\n  };\n  /*\n  ModulusPoly[] divide(other: ModulusPoly) {\n    if (!field.equals(other.field)) {\n      throw new IllegalArgumentException(\"ModulusPolys do not have same ModulusGF field\");\n    }\n    if (other.isZero()) {\n      throw new IllegalArgumentException(\"Divide by 0\");\n    }\n       let quotient: ModulusPoly = field.getZero();\n    let remainder: ModulusPoly = this;\n       let denominatorLeadingTerm: /*int/ number = other.getCoefficient(other.getDegree());\n    let inverseDenominatorLeadingTerm: /*int/ number = field.inverse(denominatorLeadingTerm);\n       while (remainder.getDegree() >= other.getDegree() && !remainder.isZero()) {\n      let degreeDifference: /*int/ number = remainder.getDegree() - other.getDegree();\n      let scale: /*int/ number = field.multiply(remainder.getCoefficient(remainder.getDegree()), inverseDenominatorLeadingTerm);\n      let term: ModulusPoly = other.multiplyByMonomial(degreeDifference, scale);\n      let iterationQuotient: ModulusPoly = field.buildMonomial(degreeDifference, scale);\n      quotient = quotient.add(iterationQuotient);\n      remainder = remainder.subtract(term);\n    }\n       return new ModulusPoly[] { quotient, remainder };\n  }\n  */\n  // @Override\n  ModulusPoly.prototype.toString = function () {\n    var result = new StringBuilder(/*8 * this.getDegree()*/); // dynamic string size in JS\n    for (var degree /*int*/ = this.getDegree(); degree >= 0; degree--) {\n      var coefficient = /*int*/this.getCoefficient(degree);\n      if (coefficient !== 0) {\n        if (coefficient < 0) {\n          result.append(' - ');\n          coefficient = -coefficient;\n        } else {\n          if (result.length() > 0) {\n            result.append(' + ');\n          }\n        }\n        if (degree === 0 || coefficient !== 1) {\n          result.append(coefficient);\n        }\n        if (degree !== 0) {\n          if (degree === 1) {\n            result.append('x');\n          } else {\n            result.append('x^');\n            result.append(degree);\n          }\n        }\n      }\n    }\n    return result.toString();\n  };\n  return ModulusPoly;\n}();\nexport default ModulusPoly;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "IllegalArgumentException", "System", "StringBuilder", "ModulusPoly", "field", "coefficients", "<PERSON><PERSON><PERSON><PERSON>", "firstNonZero", "Int32Array", "arraycopy", "prototype", "getCoefficients", "getDegree", "isZero", "getCoefficient", "degree", "evaluateAt", "a", "e_1", "_a", "sum", "_b", "_c", "coefficient", "add", "e_1_1", "error", "return", "result", "size", "multiply", "other", "equals", "smallerCoefficients", "largerCoefficients", "temp", "sumDiff", "lengthDiff", "subtract", "negative", "multiplyOther", "multiplyScalar", "aCoefficients", "a<PERSON><PERSON><PERSON>", "bCoefficients", "b<PERSON><PERSON><PERSON>", "product", "<PERSON><PERSON><PERSON><PERSON>", "j", "negativeCoefficients", "scalar", "multiplyByMonomial", "toString", "append"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/ec/ModulusPoly.js"], "sourcesContent": ["/*\n* Copyright 2012 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder.ec;\nimport IllegalArgumentException from '../../../IllegalArgumentException';\nimport System from '../../../util/System';\nimport StringBuilder from '../../../util/StringBuilder';\n/**\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.GenericGFPoly\n */\nvar ModulusPoly = /** @class */ (function () {\n    function ModulusPoly(field, coefficients) {\n        if (coefficients.length === 0) {\n            throw new IllegalArgumentException();\n        }\n        this.field = field;\n        var coefficientsLength = /*int*/ coefficients.length;\n        if (coefficientsLength > 1 && coefficients[0] === 0) {\n            // Leading term must be non-zero for anything except the constant polynomial \"0\"\n            var firstNonZero = /*int*/ 1;\n            while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n                firstNonZero++;\n            }\n            if (firstNonZero === coefficientsLength) {\n                this.coefficients = new Int32Array([0]);\n            }\n            else {\n                this.coefficients = new Int32Array(coefficientsLength - firstNonZero);\n                System.arraycopy(coefficients, firstNonZero, this.coefficients, 0, this.coefficients.length);\n            }\n        }\n        else {\n            this.coefficients = coefficients;\n        }\n    }\n    ModulusPoly.prototype.getCoefficients = function () {\n        return this.coefficients;\n    };\n    /**\n     * @return degree of this polynomial\n     */\n    ModulusPoly.prototype.getDegree = function () {\n        return this.coefficients.length - 1;\n    };\n    /**\n     * @return true iff this polynomial is the monomial \"0\"\n     */\n    ModulusPoly.prototype.isZero = function () {\n        return this.coefficients[0] === 0;\n    };\n    /**\n     * @return coefficient of x^degree term in this polynomial\n     */\n    ModulusPoly.prototype.getCoefficient = function (degree) {\n        return this.coefficients[this.coefficients.length - 1 - degree];\n    };\n    /**\n     * @return evaluation of this polynomial at a given point\n     */\n    ModulusPoly.prototype.evaluateAt = function (a) {\n        var e_1, _a;\n        if (a === 0) {\n            // Just return the x^0 coefficient\n            return this.getCoefficient(0);\n        }\n        if (a === 1) {\n            // Just the sum of the coefficients\n            var sum = /*int*/ 0;\n            try {\n                for (var _b = __values(this.coefficients), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var coefficient = _c.value /*int*/;\n                    sum = this.field.add(sum, coefficient);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            return sum;\n        }\n        var result = /*int*/ this.coefficients[0];\n        var size = /*int*/ this.coefficients.length;\n        for (var i /*int*/ = 1; i < size; i++) {\n            result = this.field.add(this.field.multiply(a, result), this.coefficients[i]);\n        }\n        return result;\n    };\n    ModulusPoly.prototype.add = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n        }\n        if (this.isZero()) {\n            return other;\n        }\n        if (other.isZero()) {\n            return this;\n        }\n        var smallerCoefficients = this.coefficients;\n        var largerCoefficients = other.coefficients;\n        if (smallerCoefficients.length > largerCoefficients.length) {\n            var temp = smallerCoefficients;\n            smallerCoefficients = largerCoefficients;\n            largerCoefficients = temp;\n        }\n        var sumDiff = new Int32Array(largerCoefficients.length);\n        var lengthDiff = /*int*/ largerCoefficients.length - smallerCoefficients.length;\n        // Copy high-order terms only found in higher-degree polynomial's coefficients\n        System.arraycopy(largerCoefficients, 0, sumDiff, 0, lengthDiff);\n        for (var i /*int*/ = lengthDiff; i < largerCoefficients.length; i++) {\n            sumDiff[i] = this.field.add(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n        }\n        return new ModulusPoly(this.field, sumDiff);\n    };\n    ModulusPoly.prototype.subtract = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n        }\n        if (other.isZero()) {\n            return this;\n        }\n        return this.add(other.negative());\n    };\n    ModulusPoly.prototype.multiply = function (other) {\n        if (other instanceof ModulusPoly) {\n            return this.multiplyOther(other);\n        }\n        return this.multiplyScalar(other);\n    };\n    ModulusPoly.prototype.multiplyOther = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('ModulusPolys do not have same ModulusGF field');\n        }\n        if (this.isZero() || other.isZero()) {\n            // return this.field.getZero();\n            return new ModulusPoly(this.field, new Int32Array([0]));\n        }\n        var aCoefficients = this.coefficients;\n        var aLength = /*int*/ aCoefficients.length;\n        var bCoefficients = other.coefficients;\n        var bLength = /*int*/ bCoefficients.length;\n        var product = new Int32Array(aLength + bLength - 1);\n        for (var i /*int*/ = 0; i < aLength; i++) {\n            var aCoeff = /*int*/ aCoefficients[i];\n            for (var j /*int*/ = 0; j < bLength; j++) {\n                product[i + j] = this.field.add(product[i + j], this.field.multiply(aCoeff, bCoefficients[j]));\n            }\n        }\n        return new ModulusPoly(this.field, product);\n    };\n    ModulusPoly.prototype.negative = function () {\n        var size = /*int*/ this.coefficients.length;\n        var negativeCoefficients = new Int32Array(size);\n        for (var i /*int*/ = 0; i < size; i++) {\n            negativeCoefficients[i] = this.field.subtract(0, this.coefficients[i]);\n        }\n        return new ModulusPoly(this.field, negativeCoefficients);\n    };\n    ModulusPoly.prototype.multiplyScalar = function (scalar) {\n        if (scalar === 0) {\n            return new ModulusPoly(this.field, new Int32Array([0]));\n        }\n        if (scalar === 1) {\n            return this;\n        }\n        var size = /*int*/ this.coefficients.length;\n        var product = new Int32Array(size);\n        for (var i /*int*/ = 0; i < size; i++) {\n            product[i] = this.field.multiply(this.coefficients[i], scalar);\n        }\n        return new ModulusPoly(this.field, product);\n    };\n    ModulusPoly.prototype.multiplyByMonomial = function (degree, coefficient) {\n        if (degree < 0) {\n            throw new IllegalArgumentException();\n        }\n        if (coefficient === 0) {\n            return new ModulusPoly(this.field, new Int32Array([0]));\n        }\n        var size = /*int*/ this.coefficients.length;\n        var product = new Int32Array(size + degree);\n        for (var i /*int*/ = 0; i < size; i++) {\n            product[i] = this.field.multiply(this.coefficients[i], coefficient);\n        }\n        return new ModulusPoly(this.field, product);\n    };\n    /*\n    ModulusPoly[] divide(other: ModulusPoly) {\n      if (!field.equals(other.field)) {\n        throw new IllegalArgumentException(\"ModulusPolys do not have same ModulusGF field\");\n      }\n      if (other.isZero()) {\n        throw new IllegalArgumentException(\"Divide by 0\");\n      }\n  \n      let quotient: ModulusPoly = field.getZero();\n      let remainder: ModulusPoly = this;\n  \n      let denominatorLeadingTerm: /*int/ number = other.getCoefficient(other.getDegree());\n      let inverseDenominatorLeadingTerm: /*int/ number = field.inverse(denominatorLeadingTerm);\n  \n      while (remainder.getDegree() >= other.getDegree() && !remainder.isZero()) {\n        let degreeDifference: /*int/ number = remainder.getDegree() - other.getDegree();\n        let scale: /*int/ number = field.multiply(remainder.getCoefficient(remainder.getDegree()), inverseDenominatorLeadingTerm);\n        let term: ModulusPoly = other.multiplyByMonomial(degreeDifference, scale);\n        let iterationQuotient: ModulusPoly = field.buildMonomial(degreeDifference, scale);\n        quotient = quotient.add(iterationQuotient);\n        remainder = remainder.subtract(term);\n      }\n  \n      return new ModulusPoly[] { quotient, remainder };\n    }\n    */\n    // @Override\n    ModulusPoly.prototype.toString = function () {\n        var result = new StringBuilder( /*8 * this.getDegree()*/); // dynamic string size in JS\n        for (var degree /*int*/ = this.getDegree(); degree >= 0; degree--) {\n            var coefficient = /*int*/ this.getCoefficient(degree);\n            if (coefficient !== 0) {\n                if (coefficient < 0) {\n                    result.append(' - ');\n                    coefficient = -coefficient;\n                }\n                else {\n                    if (result.length() > 0) {\n                        result.append(' + ');\n                    }\n                }\n                if (degree === 0 || coefficient !== 1) {\n                    result.append(coefficient);\n                }\n                if (degree !== 0) {\n                    if (degree === 1) {\n                        result.append('x');\n                    }\n                    else {\n                        result.append('x^');\n                        result.append(degree);\n                    }\n                }\n            }\n        }\n        return result.toString();\n    };\n    return ModulusPoly;\n}());\nexport default ModulusPoly;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,wBAAwB,MAAM,mCAAmC;AACxE,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,aAAa,MAAM,6BAA6B;AACvD;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAACC,KAAK,EAAEC,YAAY,EAAE;IACtC,IAAIA,YAAY,CAACV,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIK,wBAAwB,CAAC,CAAC;IACxC;IACA,IAAI,CAACI,KAAK,GAAGA,KAAK;IAClB,IAAIE,kBAAkB,GAAG,OAAQD,YAAY,CAACV,MAAM;IACpD,IAAIW,kBAAkB,GAAG,CAAC,IAAID,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MACjD;MACA,IAAIE,YAAY,GAAG,OAAQ,CAAC;MAC5B,OAAOA,YAAY,GAAGD,kBAAkB,IAAID,YAAY,CAACE,YAAY,CAAC,KAAK,CAAC,EAAE;QAC1EA,YAAY,EAAE;MAClB;MACA,IAAIA,YAAY,KAAKD,kBAAkB,EAAE;QACrC,IAAI,CAACD,YAAY,GAAG,IAAIG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C,CAAC,MACI;QACD,IAAI,CAACH,YAAY,GAAG,IAAIG,UAAU,CAACF,kBAAkB,GAAGC,YAAY,CAAC;QACrEN,MAAM,CAACQ,SAAS,CAACJ,YAAY,EAAEE,YAAY,EAAE,IAAI,CAACF,YAAY,EAAE,CAAC,EAAE,IAAI,CAACA,YAAY,CAACV,MAAM,CAAC;MAChG;IACJ,CAAC,MACI;MACD,IAAI,CAACU,YAAY,GAAGA,YAAY;IACpC;EACJ;EACAF,WAAW,CAACO,SAAS,CAACC,eAAe,GAAG,YAAY;IAChD,OAAO,IAAI,CAACN,YAAY;EAC5B,CAAC;EACD;AACJ;AACA;EACIF,WAAW,CAACO,SAAS,CAACE,SAAS,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACP,YAAY,CAACV,MAAM,GAAG,CAAC;EACvC,CAAC;EACD;AACJ;AACA;EACIQ,WAAW,CAACO,SAAS,CAACG,MAAM,GAAG,YAAY;IACvC,OAAO,IAAI,CAACR,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC;EACrC,CAAC;EACD;AACJ;AACA;EACIF,WAAW,CAACO,SAAS,CAACI,cAAc,GAAG,UAAUC,MAAM,EAAE;IACrD,OAAO,IAAI,CAACV,YAAY,CAAC,IAAI,CAACA,YAAY,CAACV,MAAM,GAAG,CAAC,GAAGoB,MAAM,CAAC;EACnE,CAAC;EACD;AACJ;AACA;EACIZ,WAAW,CAACO,SAAS,CAACM,UAAU,GAAG,UAAUC,CAAC,EAAE;IAC5C,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIF,CAAC,KAAK,CAAC,EAAE;MACT;MACA,OAAO,IAAI,CAACH,cAAc,CAAC,CAAC,CAAC;IACjC;IACA,IAAIG,CAAC,KAAK,CAAC,EAAE;MACT;MACA,IAAIG,GAAG,GAAG,OAAQ,CAAC;MACnB,IAAI;QACA,KAAK,IAAIC,EAAE,GAAGlC,QAAQ,CAAC,IAAI,CAACkB,YAAY,CAAC,EAAEiB,EAAE,GAAGD,EAAE,CAACzB,IAAI,CAAC,CAAC,EAAE,CAAC0B,EAAE,CAACxB,IAAI,EAAEwB,EAAE,GAAGD,EAAE,CAACzB,IAAI,CAAC,CAAC,EAAE;UACjF,IAAI2B,WAAW,GAAGD,EAAE,CAACzB,KAAK,CAAC;UAC3BuB,GAAG,GAAG,IAAI,CAAChB,KAAK,CAACoB,GAAG,CAACJ,GAAG,EAAEG,WAAW,CAAC;QAC1C;MACJ,CAAC,CACD,OAAOE,KAAK,EAAE;QAAEP,GAAG,GAAG;UAAEQ,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIH,EAAE,IAAI,CAACA,EAAE,CAACxB,IAAI,KAAKqB,EAAE,GAAGE,EAAE,CAACM,MAAM,CAAC,EAAER,EAAE,CAACzB,IAAI,CAAC2B,EAAE,CAAC;QACvD,CAAC,SACO;UAAE,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAACQ,KAAK;QAAE;MACxC;MACA,OAAON,GAAG;IACd;IACA,IAAIQ,MAAM,GAAG,OAAQ,IAAI,CAACvB,YAAY,CAAC,CAAC,CAAC;IACzC,IAAIwB,IAAI,GAAG,OAAQ,IAAI,CAACxB,YAAY,CAACV,MAAM;IAC3C,KAAK,IAAIF,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGoC,IAAI,EAAEpC,CAAC,EAAE,EAAE;MACnCmC,MAAM,GAAG,IAAI,CAACxB,KAAK,CAACoB,GAAG,CAAC,IAAI,CAACpB,KAAK,CAAC0B,QAAQ,CAACb,CAAC,EAAEW,MAAM,CAAC,EAAE,IAAI,CAACvB,YAAY,CAACZ,CAAC,CAAC,CAAC;IACjF;IACA,OAAOmC,MAAM;EACjB,CAAC;EACDzB,WAAW,CAACO,SAAS,CAACc,GAAG,GAAG,UAAUO,KAAK,EAAE;IACzC,IAAI,CAAC,IAAI,CAAC3B,KAAK,CAAC4B,MAAM,CAACD,KAAK,CAAC3B,KAAK,CAAC,EAAE;MACjC,MAAM,IAAIJ,wBAAwB,CAAC,+CAA+C,CAAC;IACvF;IACA,IAAI,IAAI,CAACa,MAAM,CAAC,CAAC,EAAE;MACf,OAAOkB,KAAK;IAChB;IACA,IAAIA,KAAK,CAAClB,MAAM,CAAC,CAAC,EAAE;MAChB,OAAO,IAAI;IACf;IACA,IAAIoB,mBAAmB,GAAG,IAAI,CAAC5B,YAAY;IAC3C,IAAI6B,kBAAkB,GAAGH,KAAK,CAAC1B,YAAY;IAC3C,IAAI4B,mBAAmB,CAACtC,MAAM,GAAGuC,kBAAkB,CAACvC,MAAM,EAAE;MACxD,IAAIwC,IAAI,GAAGF,mBAAmB;MAC9BA,mBAAmB,GAAGC,kBAAkB;MACxCA,kBAAkB,GAAGC,IAAI;IAC7B;IACA,IAAIC,OAAO,GAAG,IAAI5B,UAAU,CAAC0B,kBAAkB,CAACvC,MAAM,CAAC;IACvD,IAAI0C,UAAU,GAAG,OAAQH,kBAAkB,CAACvC,MAAM,GAAGsC,mBAAmB,CAACtC,MAAM;IAC/E;IACAM,MAAM,CAACQ,SAAS,CAACyB,kBAAkB,EAAE,CAAC,EAAEE,OAAO,EAAE,CAAC,EAAEC,UAAU,CAAC;IAC/D,KAAK,IAAI5C,CAAC,CAAC,UAAU4C,UAAU,EAAE5C,CAAC,GAAGyC,kBAAkB,CAACvC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACjE2C,OAAO,CAAC3C,CAAC,CAAC,GAAG,IAAI,CAACW,KAAK,CAACoB,GAAG,CAACS,mBAAmB,CAACxC,CAAC,GAAG4C,UAAU,CAAC,EAAEH,kBAAkB,CAACzC,CAAC,CAAC,CAAC;IAC3F;IACA,OAAO,IAAIU,WAAW,CAAC,IAAI,CAACC,KAAK,EAAEgC,OAAO,CAAC;EAC/C,CAAC;EACDjC,WAAW,CAACO,SAAS,CAAC4B,QAAQ,GAAG,UAAUP,KAAK,EAAE;IAC9C,IAAI,CAAC,IAAI,CAAC3B,KAAK,CAAC4B,MAAM,CAACD,KAAK,CAAC3B,KAAK,CAAC,EAAE;MACjC,MAAM,IAAIJ,wBAAwB,CAAC,+CAA+C,CAAC;IACvF;IACA,IAAI+B,KAAK,CAAClB,MAAM,CAAC,CAAC,EAAE;MAChB,OAAO,IAAI;IACf;IACA,OAAO,IAAI,CAACW,GAAG,CAACO,KAAK,CAACQ,QAAQ,CAAC,CAAC,CAAC;EACrC,CAAC;EACDpC,WAAW,CAACO,SAAS,CAACoB,QAAQ,GAAG,UAAUC,KAAK,EAAE;IAC9C,IAAIA,KAAK,YAAY5B,WAAW,EAAE;MAC9B,OAAO,IAAI,CAACqC,aAAa,CAACT,KAAK,CAAC;IACpC;IACA,OAAO,IAAI,CAACU,cAAc,CAACV,KAAK,CAAC;EACrC,CAAC;EACD5B,WAAW,CAACO,SAAS,CAAC8B,aAAa,GAAG,UAAUT,KAAK,EAAE;IACnD,IAAI,CAAC,IAAI,CAAC3B,KAAK,CAAC4B,MAAM,CAACD,KAAK,CAAC3B,KAAK,CAAC,EAAE;MACjC,MAAM,IAAIJ,wBAAwB,CAAC,+CAA+C,CAAC;IACvF;IACA,IAAI,IAAI,CAACa,MAAM,CAAC,CAAC,IAAIkB,KAAK,CAAClB,MAAM,CAAC,CAAC,EAAE;MACjC;MACA,OAAO,IAAIV,WAAW,CAAC,IAAI,CAACC,KAAK,EAAE,IAAII,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,IAAIkC,aAAa,GAAG,IAAI,CAACrC,YAAY;IACrC,IAAIsC,OAAO,GAAG,OAAQD,aAAa,CAAC/C,MAAM;IAC1C,IAAIiD,aAAa,GAAGb,KAAK,CAAC1B,YAAY;IACtC,IAAIwC,OAAO,GAAG,OAAQD,aAAa,CAACjD,MAAM;IAC1C,IAAImD,OAAO,GAAG,IAAItC,UAAU,CAACmC,OAAO,GAAGE,OAAO,GAAG,CAAC,CAAC;IACnD,KAAK,IAAIpD,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGkD,OAAO,EAAElD,CAAC,EAAE,EAAE;MACtC,IAAIsD,MAAM,GAAG,OAAQL,aAAa,CAACjD,CAAC,CAAC;MACrC,KAAK,IAAIuD,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;QACtCF,OAAO,CAACrD,CAAC,GAAGuD,CAAC,CAAC,GAAG,IAAI,CAAC5C,KAAK,CAACoB,GAAG,CAACsB,OAAO,CAACrD,CAAC,GAAGuD,CAAC,CAAC,EAAE,IAAI,CAAC5C,KAAK,CAAC0B,QAAQ,CAACiB,MAAM,EAAEH,aAAa,CAACI,CAAC,CAAC,CAAC,CAAC;MAClG;IACJ;IACA,OAAO,IAAI7C,WAAW,CAAC,IAAI,CAACC,KAAK,EAAE0C,OAAO,CAAC;EAC/C,CAAC;EACD3C,WAAW,CAACO,SAAS,CAAC6B,QAAQ,GAAG,YAAY;IACzC,IAAIV,IAAI,GAAG,OAAQ,IAAI,CAACxB,YAAY,CAACV,MAAM;IAC3C,IAAIsD,oBAAoB,GAAG,IAAIzC,UAAU,CAACqB,IAAI,CAAC;IAC/C,KAAK,IAAIpC,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGoC,IAAI,EAAEpC,CAAC,EAAE,EAAE;MACnCwD,oBAAoB,CAACxD,CAAC,CAAC,GAAG,IAAI,CAACW,KAAK,CAACkC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACjC,YAAY,CAACZ,CAAC,CAAC,CAAC;IAC1E;IACA,OAAO,IAAIU,WAAW,CAAC,IAAI,CAACC,KAAK,EAAE6C,oBAAoB,CAAC;EAC5D,CAAC;EACD9C,WAAW,CAACO,SAAS,CAAC+B,cAAc,GAAG,UAAUS,MAAM,EAAE;IACrD,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,IAAI/C,WAAW,CAAC,IAAI,CAACC,KAAK,EAAE,IAAII,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,IAAI0C,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,IAAI;IACf;IACA,IAAIrB,IAAI,GAAG,OAAQ,IAAI,CAACxB,YAAY,CAACV,MAAM;IAC3C,IAAImD,OAAO,GAAG,IAAItC,UAAU,CAACqB,IAAI,CAAC;IAClC,KAAK,IAAIpC,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGoC,IAAI,EAAEpC,CAAC,EAAE,EAAE;MACnCqD,OAAO,CAACrD,CAAC,CAAC,GAAG,IAAI,CAACW,KAAK,CAAC0B,QAAQ,CAAC,IAAI,CAACzB,YAAY,CAACZ,CAAC,CAAC,EAAEyD,MAAM,CAAC;IAClE;IACA,OAAO,IAAI/C,WAAW,CAAC,IAAI,CAACC,KAAK,EAAE0C,OAAO,CAAC;EAC/C,CAAC;EACD3C,WAAW,CAACO,SAAS,CAACyC,kBAAkB,GAAG,UAAUpC,MAAM,EAAEQ,WAAW,EAAE;IACtE,IAAIR,MAAM,GAAG,CAAC,EAAE;MACZ,MAAM,IAAIf,wBAAwB,CAAC,CAAC;IACxC;IACA,IAAIuB,WAAW,KAAK,CAAC,EAAE;MACnB,OAAO,IAAIpB,WAAW,CAAC,IAAI,CAACC,KAAK,EAAE,IAAII,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,IAAIqB,IAAI,GAAG,OAAQ,IAAI,CAACxB,YAAY,CAACV,MAAM;IAC3C,IAAImD,OAAO,GAAG,IAAItC,UAAU,CAACqB,IAAI,GAAGd,MAAM,CAAC;IAC3C,KAAK,IAAItB,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGoC,IAAI,EAAEpC,CAAC,EAAE,EAAE;MACnCqD,OAAO,CAACrD,CAAC,CAAC,GAAG,IAAI,CAACW,KAAK,CAAC0B,QAAQ,CAAC,IAAI,CAACzB,YAAY,CAACZ,CAAC,CAAC,EAAE8B,WAAW,CAAC;IACvE;IACA,OAAO,IAAIpB,WAAW,CAAC,IAAI,CAACC,KAAK,EAAE0C,OAAO,CAAC;EAC/C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAKI;EACA3C,WAAW,CAACO,SAAS,CAAC0C,QAAQ,GAAG,YAAY;IACzC,IAAIxB,MAAM,GAAG,IAAI1B,aAAa,CAAE,yBAAyB,CAAC,CAAC;IAC3D,KAAK,IAAIa,MAAM,CAAC,UAAU,IAAI,CAACH,SAAS,CAAC,CAAC,EAAEG,MAAM,IAAI,CAAC,EAAEA,MAAM,EAAE,EAAE;MAC/D,IAAIQ,WAAW,GAAG,OAAQ,IAAI,CAACT,cAAc,CAACC,MAAM,CAAC;MACrD,IAAIQ,WAAW,KAAK,CAAC,EAAE;QACnB,IAAIA,WAAW,GAAG,CAAC,EAAE;UACjBK,MAAM,CAACyB,MAAM,CAAC,KAAK,CAAC;UACpB9B,WAAW,GAAG,CAACA,WAAW;QAC9B,CAAC,MACI;UACD,IAAIK,MAAM,CAACjC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;YACrBiC,MAAM,CAACyB,MAAM,CAAC,KAAK,CAAC;UACxB;QACJ;QACA,IAAItC,MAAM,KAAK,CAAC,IAAIQ,WAAW,KAAK,CAAC,EAAE;UACnCK,MAAM,CAACyB,MAAM,CAAC9B,WAAW,CAAC;QAC9B;QACA,IAAIR,MAAM,KAAK,CAAC,EAAE;UACd,IAAIA,MAAM,KAAK,CAAC,EAAE;YACda,MAAM,CAACyB,MAAM,CAAC,GAAG,CAAC;UACtB,CAAC,MACI;YACDzB,MAAM,CAACyB,MAAM,CAAC,IAAI,CAAC;YACnBzB,MAAM,CAACyB,MAAM,CAACtC,MAAM,CAAC;UACzB;QACJ;MACJ;IACJ;IACA,OAAOa,MAAM,CAACwB,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACD,OAAOjD,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}