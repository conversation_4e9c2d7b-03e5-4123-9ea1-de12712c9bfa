{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport IndexOutOfBoundsException from './IndexOutOfBoundsException';\n/**\n * Custom Error class of type Exception.\n */\nvar ArrayIndexOutOfBoundsException = /** @class */function (_super) {\n  __extends(ArrayIndexOutOfBoundsException, _super);\n  function ArrayIndexOutOfBoundsException(index, message) {\n    if (index === void 0) {\n      index = undefined;\n    }\n    if (message === void 0) {\n      message = undefined;\n    }\n    var _this = _super.call(this, message) || this;\n    _this.index = index;\n    _this.message = message;\n    return _this;\n  }\n  ArrayIndexOutOfBoundsException.kind = 'ArrayIndexOutOfBoundsException';\n  return ArrayIndexOutOfBoundsException;\n}(IndexOutOfBoundsException);\nexport default ArrayIndexOutOfBoundsException;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "IndexOutOfBoundsException", "ArrayIndexOutOfBoundsException", "_super", "index", "message", "undefined", "_this", "call", "kind"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/ArrayIndexOutOfBoundsException.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport IndexOutOfBoundsException from './IndexOutOfBoundsException';\n/**\n * Custom Error class of type Exception.\n */\nvar ArrayIndexOutOfBoundsException = /** @class */ (function (_super) {\n    __extends(ArrayIndexOutOfBoundsException, _super);\n    function ArrayIndexOutOfBoundsException(index, message) {\n        if (index === void 0) { index = undefined; }\n        if (message === void 0) { message = undefined; }\n        var _this = _super.call(this, message) || this;\n        _this.index = index;\n        _this.message = message;\n        return _this;\n    }\n    ArrayIndexOutOfBoundsException.kind = 'ArrayIndexOutOfBoundsException';\n    return ArrayIndexOutOfBoundsException;\n}(IndexOutOfBoundsException));\nexport default ArrayIndexOutOfBoundsException;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,yBAAyB,MAAM,6BAA6B;AACnE;AACA;AACA;AACA,IAAIC,8BAA8B,GAAG,aAAe,UAAUC,MAAM,EAAE;EAClEhB,SAAS,CAACe,8BAA8B,EAAEC,MAAM,CAAC;EACjD,SAASD,8BAA8BA,CAACE,KAAK,EAAEC,OAAO,EAAE;IACpD,IAAID,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAGE,SAAS;IAAE;IAC3C,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAGC,SAAS;IAAE;IAC/C,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEH,OAAO,CAAC,IAAI,IAAI;IAC9CE,KAAK,CAACH,KAAK,GAAGA,KAAK;IACnBG,KAAK,CAACF,OAAO,GAAGA,OAAO;IACvB,OAAOE,KAAK;EAChB;EACAL,8BAA8B,CAACO,IAAI,GAAG,gCAAgC;EACtE,OAAOP,8BAA8B;AACzC,CAAC,CAACD,yBAAyB,CAAE;AAC7B,eAAeC,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}