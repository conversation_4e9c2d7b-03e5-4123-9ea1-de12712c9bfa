{"ast": null, "code": "import BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport EncodeHintType from '../EncodeHintType';\nimport ByteMatrix from '../qrcode/encoder/ByteMatrix';\nimport Charset from '../util/Charset';\nimport { DefaultPlacement, ErrorCorrection, HighLevelEncoder, MinimalEncoder, SymbolInfo } from './encoder';\nvar DataMatrixWriter = /** @class */function () {\n  function DataMatrixWriter() {}\n  DataMatrixWriter.prototype.encode = function (contents, format, width, height, hints) {\n    if (hints === void 0) {\n      hints = null;\n    }\n    if (contents.trim() === '') {\n      throw new Error('Found empty contents');\n    }\n    if (format !== BarcodeFormat.DATA_MATRIX) {\n      throw new Error('Can only encode DATA_MATRIX, but got ' + format);\n    }\n    if (width < 0 || height < 0) {\n      throw new Error('Requested dimensions can\\'t be negative: ' + width + 'x' + height);\n    }\n    // Try to get force shape & min / max size\n    var shape = 0 /* FORCE_NONE */;\n    var minSize = null;\n    var maxSize = null;\n    if (hints != null) {\n      var requestedShape = hints.get(EncodeHintType.DATA_MATRIX_SHAPE);\n      if (requestedShape != null) {\n        shape = requestedShape;\n      }\n      var requestedMinSize = hints.get(EncodeHintType.MIN_SIZE);\n      if (requestedMinSize != null) {\n        minSize = requestedMinSize;\n      }\n      var requestedMaxSize = hints.get(EncodeHintType.MAX_SIZE);\n      if (requestedMaxSize != null) {\n        maxSize = requestedMaxSize;\n      }\n    }\n    // 1. step: Data encodation\n    var encoded;\n    var hasCompactionHint = hints != null && hints.has(EncodeHintType.DATA_MATRIX_COMPACT) && Boolean(hints.get(EncodeHintType.DATA_MATRIX_COMPACT).toString());\n    if (hasCompactionHint) {\n      var hasGS1FormatHint = hints.has(EncodeHintType.GS1_FORMAT) && Boolean(hints.get(EncodeHintType.GS1_FORMAT).toString());\n      var charset = null;\n      var hasEncodingHint = hints.has(EncodeHintType.CHARACTER_SET);\n      if (hasEncodingHint) {\n        charset = Charset.forName(hints.get(EncodeHintType.CHARACTER_SET).toString());\n      }\n      encoded = MinimalEncoder.encodeHighLevel(contents, charset, hasGS1FormatHint ? 0x1d : -1, shape);\n    } else {\n      var hasForceC40Hint = hints != null && hints.has(EncodeHintType.FORCE_C40) && Boolean(hints.get(EncodeHintType.FORCE_C40).toString());\n      encoded = HighLevelEncoder.encodeHighLevel(contents, shape, minSize, maxSize, hasForceC40Hint);\n    }\n    var symbolInfo = SymbolInfo.lookup(encoded.length, shape, minSize, maxSize, true);\n    // 2. step: ECC generation\n    var codewords = ErrorCorrection.encodeECC200(encoded, symbolInfo);\n    // 3. step: Module placement in Matrix\n    var placement = new DefaultPlacement(codewords, symbolInfo.getSymbolDataWidth(), symbolInfo.getSymbolDataHeight());\n    placement.place();\n    // 4. step: low-level encoding\n    return this.encodeLowLevel(placement, symbolInfo, width, height);\n  };\n  /**\n   * Encode the given symbol info to a bit matrix.\n   *\n   * @param placement  The DataMatrix placement.\n   * @param symbolInfo The symbol info to encode.\n   * @return The bit matrix generated.\n   */\n  DataMatrixWriter.prototype.encodeLowLevel = function (placement, symbolInfo, width, height) {\n    var symbolWidth = symbolInfo.getSymbolDataWidth();\n    var symbolHeight = symbolInfo.getSymbolDataHeight();\n    var matrix = new ByteMatrix(symbolInfo.getSymbolWidth(), symbolInfo.getSymbolHeight());\n    var matrixY = 0;\n    for (var y = 0; y < symbolHeight; y++) {\n      // Fill the top edge with alternate 0 / 1\n      var matrixX = void 0;\n      if (y % symbolInfo.matrixHeight === 0) {\n        matrixX = 0;\n        for (var x = 0; x < symbolInfo.getSymbolWidth(); x++) {\n          matrix.setBoolean(matrixX, matrixY, x % 2 === 0);\n          matrixX++;\n        }\n        matrixY++;\n      }\n      matrixX = 0;\n      for (var x = 0; x < symbolWidth; x++) {\n        // Fill the right edge with full 1\n        if (x % symbolInfo.matrixWidth === 0) {\n          matrix.setBoolean(matrixX, matrixY, true);\n          matrixX++;\n        }\n        matrix.setBoolean(matrixX, matrixY, placement.getBit(x, y));\n        matrixX++;\n        // Fill the right edge with alternate 0 / 1\n        if (x % symbolInfo.matrixWidth === symbolInfo.matrixWidth - 1) {\n          matrix.setBoolean(matrixX, matrixY, y % 2 === 0);\n          matrixX++;\n        }\n      }\n      matrixY++;\n      // Fill the bottom edge with full 1\n      if (y % symbolInfo.matrixHeight === symbolInfo.matrixHeight - 1) {\n        matrixX = 0;\n        for (var x = 0; x < symbolInfo.getSymbolWidth(); x++) {\n          matrix.setBoolean(matrixX, matrixY, true);\n          matrixX++;\n        }\n        matrixY++;\n      }\n    }\n    return this.convertByteMatrixToBitMatrix(matrix, width, height);\n  };\n  /**\n   * Convert the ByteMatrix to BitMatrix.\n   *\n   * @param reqHeight The requested height of the image (in pixels) with the Datamatrix code\n   * @param reqWidth The requested width of the image (in pixels) with the Datamatrix code\n   * @param matrix The input matrix.\n   * @return The output matrix.\n   */\n  DataMatrixWriter.prototype.convertByteMatrixToBitMatrix = function (matrix, reqWidth, reqHeight) {\n    var matrixWidth = matrix.getWidth();\n    var matrixHeight = matrix.getHeight();\n    var outputWidth = Math.max(reqWidth, matrixWidth);\n    var outputHeight = Math.max(reqHeight, matrixHeight);\n    var multiple = Math.min(outputWidth / matrixWidth, outputHeight / matrixHeight);\n    var leftPadding = (outputWidth - matrixWidth * multiple) / 2;\n    var topPadding = (outputHeight - matrixHeight * multiple) / 2;\n    var output;\n    // remove padding if requested width and height are too small\n    if (reqHeight < matrixHeight || reqWidth < matrixWidth) {\n      leftPadding = 0;\n      topPadding = 0;\n      output = new BitMatrix(matrixWidth, matrixHeight);\n    } else {\n      output = new BitMatrix(reqWidth, reqHeight);\n    }\n    output.clear();\n    for (var inputY = 0, outputY = topPadding; inputY < matrixHeight; inputY++, outputY += multiple) {\n      // Write the contents of this row of the bytematrix\n      for (var inputX = 0, outputX = leftPadding; inputX < matrixWidth; inputX++, outputX += multiple) {\n        if (matrix.get(inputX, inputY) === 1) {\n          output.setRegion(outputX, outputY, multiple, multiple);\n        }\n      }\n    }\n    return output;\n  };\n  return DataMatrixWriter;\n}();\nexport default DataMatrixWriter;", "map": {"version": 3, "names": ["BarcodeFormat", "BitMatrix", "EncodeHintType", "ByteMatrix", "Charset", "DefaultPlacement", "ErrorCorrection", "HighLevelEncoder", "MinimalEncoder", "SymbolInfo", "DataMatrixWriter", "prototype", "encode", "contents", "format", "width", "height", "hints", "trim", "Error", "DATA_MATRIX", "shape", "minSize", "maxSize", "requested<PERSON>hape", "get", "DATA_MATRIX_SHAPE", "requestedMinSize", "MIN_SIZE", "requestedMaxSize", "MAX_SIZE", "encoded", "hasCompactionHint", "has", "DATA_MATRIX_COMPACT", "Boolean", "toString", "hasGS1FormatHint", "GS1_FORMAT", "charset", "hasEncodingHint", "CHARACTER_SET", "forName", "encodeHighLevel", "hasForceC40Hint", "FORCE_C40", "symbolInfo", "lookup", "length", "codewords", "encodeECC200", "placement", "getSymbolDataWidth", "getSymbolDataHeight", "place", "encodeLowLevel", "symbolWidth", "symbolHeight", "matrix", "getSymbolWidth", "getSymbolHeight", "matrixY", "y", "matrixX", "matrixHeight", "x", "setBoolean", "matrixWidth", "getBit", "convertByteMatrixToBitMatrix", "req<PERSON>id<PERSON>", "reqHeight", "getWidth", "getHeight", "outputWidth", "Math", "max", "outputHeight", "multiple", "min", "leftPadding", "topPadding", "output", "clear", "inputY", "outputY", "inputX", "outputX", "setRegion"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/DataMatrixWriter.js"], "sourcesContent": ["import BarcodeFormat from '../BarcodeFormat';\nimport BitMatrix from '../common/BitMatrix';\nimport EncodeHintType from '../EncodeHintType';\nimport ByteMatrix from '../qrcode/encoder/ByteMatrix';\nimport Charset from '../util/Charset';\nimport { DefaultPlacement, ErrorCorrection, HighLevelEncoder, MinimalEncoder, SymbolInfo, } from './encoder';\nvar DataMatrixWriter = /** @class */ (function () {\n    function DataMatrixWriter() {\n    }\n    DataMatrixWriter.prototype.encode = function (contents, format, width, height, hints) {\n        if (hints === void 0) { hints = null; }\n        if (contents.trim() === '') {\n            throw new Error('Found empty contents');\n        }\n        if (format !== BarcodeFormat.DATA_MATRIX) {\n            throw new Error('Can only encode DATA_MATRIX, but got ' + format);\n        }\n        if (width < 0 || height < 0) {\n            throw new Error('Requested dimensions can\\'t be negative: ' + width + 'x' + height);\n        }\n        // Try to get force shape & min / max size\n        var shape = 0 /* FORCE_NONE */;\n        var minSize = null;\n        var maxSize = null;\n        if (hints != null) {\n            var requestedShape = hints.get(EncodeHintType.DATA_MATRIX_SHAPE);\n            if (requestedShape != null) {\n                shape = requestedShape;\n            }\n            var requestedMinSize = hints.get(EncodeHintType.MIN_SIZE);\n            if (requestedMinSize != null) {\n                minSize = requestedMinSize;\n            }\n            var requestedMaxSize = hints.get(EncodeHintType.MAX_SIZE);\n            if (requestedMaxSize != null) {\n                maxSize = requestedMaxSize;\n            }\n        }\n        // 1. step: Data encodation\n        var encoded;\n        var hasCompactionHint = hints != null &&\n            hints.has(EncodeHintType.DATA_MATRIX_COMPACT) &&\n            Boolean(hints.get(EncodeHintType.DATA_MATRIX_COMPACT).toString());\n        if (hasCompactionHint) {\n            var hasGS1FormatHint = hints.has(EncodeHintType.GS1_FORMAT) &&\n                Boolean(hints.get(EncodeHintType.GS1_FORMAT).toString());\n            var charset = null;\n            var hasEncodingHint = hints.has(EncodeHintType.CHARACTER_SET);\n            if (hasEncodingHint) {\n                charset = Charset.forName(hints.get(EncodeHintType.CHARACTER_SET).toString());\n            }\n            encoded = MinimalEncoder.encodeHighLevel(contents, charset, hasGS1FormatHint ? 0x1d : -1, shape);\n        }\n        else {\n            var hasForceC40Hint = hints != null &&\n                hints.has(EncodeHintType.FORCE_C40) &&\n                Boolean(hints.get(EncodeHintType.FORCE_C40).toString());\n            encoded = HighLevelEncoder.encodeHighLevel(contents, shape, minSize, maxSize, hasForceC40Hint);\n        }\n        var symbolInfo = SymbolInfo.lookup(encoded.length, shape, minSize, maxSize, true);\n        // 2. step: ECC generation\n        var codewords = ErrorCorrection.encodeECC200(encoded, symbolInfo);\n        // 3. step: Module placement in Matrix\n        var placement = new DefaultPlacement(codewords, symbolInfo.getSymbolDataWidth(), symbolInfo.getSymbolDataHeight());\n        placement.place();\n        // 4. step: low-level encoding\n        return this.encodeLowLevel(placement, symbolInfo, width, height);\n    };\n    /**\n     * Encode the given symbol info to a bit matrix.\n     *\n     * @param placement  The DataMatrix placement.\n     * @param symbolInfo The symbol info to encode.\n     * @return The bit matrix generated.\n     */\n    DataMatrixWriter.prototype.encodeLowLevel = function (placement, symbolInfo, width, height) {\n        var symbolWidth = symbolInfo.getSymbolDataWidth();\n        var symbolHeight = symbolInfo.getSymbolDataHeight();\n        var matrix = new ByteMatrix(symbolInfo.getSymbolWidth(), symbolInfo.getSymbolHeight());\n        var matrixY = 0;\n        for (var y = 0; y < symbolHeight; y++) {\n            // Fill the top edge with alternate 0 / 1\n            var matrixX = void 0;\n            if (y % symbolInfo.matrixHeight === 0) {\n                matrixX = 0;\n                for (var x = 0; x < symbolInfo.getSymbolWidth(); x++) {\n                    matrix.setBoolean(matrixX, matrixY, x % 2 === 0);\n                    matrixX++;\n                }\n                matrixY++;\n            }\n            matrixX = 0;\n            for (var x = 0; x < symbolWidth; x++) {\n                // Fill the right edge with full 1\n                if (x % symbolInfo.matrixWidth === 0) {\n                    matrix.setBoolean(matrixX, matrixY, true);\n                    matrixX++;\n                }\n                matrix.setBoolean(matrixX, matrixY, placement.getBit(x, y));\n                matrixX++;\n                // Fill the right edge with alternate 0 / 1\n                if (x % symbolInfo.matrixWidth === symbolInfo.matrixWidth - 1) {\n                    matrix.setBoolean(matrixX, matrixY, y % 2 === 0);\n                    matrixX++;\n                }\n            }\n            matrixY++;\n            // Fill the bottom edge with full 1\n            if (y % symbolInfo.matrixHeight === symbolInfo.matrixHeight - 1) {\n                matrixX = 0;\n                for (var x = 0; x < symbolInfo.getSymbolWidth(); x++) {\n                    matrix.setBoolean(matrixX, matrixY, true);\n                    matrixX++;\n                }\n                matrixY++;\n            }\n        }\n        return this.convertByteMatrixToBitMatrix(matrix, width, height);\n    };\n    /**\n     * Convert the ByteMatrix to BitMatrix.\n     *\n     * @param reqHeight The requested height of the image (in pixels) with the Datamatrix code\n     * @param reqWidth The requested width of the image (in pixels) with the Datamatrix code\n     * @param matrix The input matrix.\n     * @return The output matrix.\n     */\n    DataMatrixWriter.prototype.convertByteMatrixToBitMatrix = function (matrix, reqWidth, reqHeight) {\n        var matrixWidth = matrix.getWidth();\n        var matrixHeight = matrix.getHeight();\n        var outputWidth = Math.max(reqWidth, matrixWidth);\n        var outputHeight = Math.max(reqHeight, matrixHeight);\n        var multiple = Math.min(outputWidth / matrixWidth, outputHeight / matrixHeight);\n        var leftPadding = (outputWidth - matrixWidth * multiple) / 2;\n        var topPadding = (outputHeight - matrixHeight * multiple) / 2;\n        var output;\n        // remove padding if requested width and height are too small\n        if (reqHeight < matrixHeight || reqWidth < matrixWidth) {\n            leftPadding = 0;\n            topPadding = 0;\n            output = new BitMatrix(matrixWidth, matrixHeight);\n        }\n        else {\n            output = new BitMatrix(reqWidth, reqHeight);\n        }\n        output.clear();\n        for (var inputY = 0, outputY = topPadding; inputY < matrixHeight; inputY++, outputY += multiple) {\n            // Write the contents of this row of the bytematrix\n            for (var inputX = 0, outputX = leftPadding; inputX < matrixWidth; inputX++, outputX += multiple) {\n                if (matrix.get(inputX, inputY) === 1) {\n                    output.setRegion(outputX, outputY, multiple, multiple);\n                }\n            }\n        }\n        return output;\n    };\n    return DataMatrixWriter;\n}());\nexport default DataMatrixWriter;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,UAAU,MAAM,8BAA8B;AACrD,OAAOC,OAAO,MAAM,iBAAiB;AACrC,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,UAAU,QAAS,WAAW;AAC5G,IAAIC,gBAAgB,GAAG,aAAe,YAAY;EAC9C,SAASA,gBAAgBA,CAAA,EAAG,CAC5B;EACAA,gBAAgB,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAClF,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC,IAAIJ,QAAQ,CAACK,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACxB,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;IAC3C;IACA,IAAIL,MAAM,KAAKd,aAAa,CAACoB,WAAW,EAAE;MACtC,MAAM,IAAID,KAAK,CAAC,uCAAuC,GAAGL,MAAM,CAAC;IACrE;IACA,IAAIC,KAAK,GAAG,CAAC,IAAIC,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIG,KAAK,CAAC,2CAA2C,GAAGJ,KAAK,GAAG,GAAG,GAAGC,MAAM,CAAC;IACvF;IACA;IACA,IAAIK,KAAK,GAAG,CAAC,CAAC;IACd,IAAIC,OAAO,GAAG,IAAI;IAClB,IAAIC,OAAO,GAAG,IAAI;IAClB,IAAIN,KAAK,IAAI,IAAI,EAAE;MACf,IAAIO,cAAc,GAAGP,KAAK,CAACQ,GAAG,CAACvB,cAAc,CAACwB,iBAAiB,CAAC;MAChE,IAAIF,cAAc,IAAI,IAAI,EAAE;QACxBH,KAAK,GAAGG,cAAc;MAC1B;MACA,IAAIG,gBAAgB,GAAGV,KAAK,CAACQ,GAAG,CAACvB,cAAc,CAAC0B,QAAQ,CAAC;MACzD,IAAID,gBAAgB,IAAI,IAAI,EAAE;QAC1BL,OAAO,GAAGK,gBAAgB;MAC9B;MACA,IAAIE,gBAAgB,GAAGZ,KAAK,CAACQ,GAAG,CAACvB,cAAc,CAAC4B,QAAQ,CAAC;MACzD,IAAID,gBAAgB,IAAI,IAAI,EAAE;QAC1BN,OAAO,GAAGM,gBAAgB;MAC9B;IACJ;IACA;IACA,IAAIE,OAAO;IACX,IAAIC,iBAAiB,GAAGf,KAAK,IAAI,IAAI,IACjCA,KAAK,CAACgB,GAAG,CAAC/B,cAAc,CAACgC,mBAAmB,CAAC,IAC7CC,OAAO,CAAClB,KAAK,CAACQ,GAAG,CAACvB,cAAc,CAACgC,mBAAmB,CAAC,CAACE,QAAQ,CAAC,CAAC,CAAC;IACrE,IAAIJ,iBAAiB,EAAE;MACnB,IAAIK,gBAAgB,GAAGpB,KAAK,CAACgB,GAAG,CAAC/B,cAAc,CAACoC,UAAU,CAAC,IACvDH,OAAO,CAAClB,KAAK,CAACQ,GAAG,CAACvB,cAAc,CAACoC,UAAU,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAC;MAC5D,IAAIG,OAAO,GAAG,IAAI;MAClB,IAAIC,eAAe,GAAGvB,KAAK,CAACgB,GAAG,CAAC/B,cAAc,CAACuC,aAAa,CAAC;MAC7D,IAAID,eAAe,EAAE;QACjBD,OAAO,GAAGnC,OAAO,CAACsC,OAAO,CAACzB,KAAK,CAACQ,GAAG,CAACvB,cAAc,CAACuC,aAAa,CAAC,CAACL,QAAQ,CAAC,CAAC,CAAC;MACjF;MACAL,OAAO,GAAGvB,cAAc,CAACmC,eAAe,CAAC9B,QAAQ,EAAE0B,OAAO,EAAEF,gBAAgB,GAAG,IAAI,GAAG,CAAC,CAAC,EAAEhB,KAAK,CAAC;IACpG,CAAC,MACI;MACD,IAAIuB,eAAe,GAAG3B,KAAK,IAAI,IAAI,IAC/BA,KAAK,CAACgB,GAAG,CAAC/B,cAAc,CAAC2C,SAAS,CAAC,IACnCV,OAAO,CAAClB,KAAK,CAACQ,GAAG,CAACvB,cAAc,CAAC2C,SAAS,CAAC,CAACT,QAAQ,CAAC,CAAC,CAAC;MAC3DL,OAAO,GAAGxB,gBAAgB,CAACoC,eAAe,CAAC9B,QAAQ,EAAEQ,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEqB,eAAe,CAAC;IAClG;IACA,IAAIE,UAAU,GAAGrC,UAAU,CAACsC,MAAM,CAAChB,OAAO,CAACiB,MAAM,EAAE3B,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE,IAAI,CAAC;IACjF;IACA,IAAI0B,SAAS,GAAG3C,eAAe,CAAC4C,YAAY,CAACnB,OAAO,EAAEe,UAAU,CAAC;IACjE;IACA,IAAIK,SAAS,GAAG,IAAI9C,gBAAgB,CAAC4C,SAAS,EAAEH,UAAU,CAACM,kBAAkB,CAAC,CAAC,EAAEN,UAAU,CAACO,mBAAmB,CAAC,CAAC,CAAC;IAClHF,SAAS,CAACG,KAAK,CAAC,CAAC;IACjB;IACA,OAAO,IAAI,CAACC,cAAc,CAACJ,SAAS,EAAEL,UAAU,EAAE/B,KAAK,EAAEC,MAAM,CAAC;EACpE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIN,gBAAgB,CAACC,SAAS,CAAC4C,cAAc,GAAG,UAAUJ,SAAS,EAAEL,UAAU,EAAE/B,KAAK,EAAEC,MAAM,EAAE;IACxF,IAAIwC,WAAW,GAAGV,UAAU,CAACM,kBAAkB,CAAC,CAAC;IACjD,IAAIK,YAAY,GAAGX,UAAU,CAACO,mBAAmB,CAAC,CAAC;IACnD,IAAIK,MAAM,GAAG,IAAIvD,UAAU,CAAC2C,UAAU,CAACa,cAAc,CAAC,CAAC,EAAEb,UAAU,CAACc,eAAe,CAAC,CAAC,CAAC;IACtF,IAAIC,OAAO,GAAG,CAAC;IACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,YAAY,EAAEK,CAAC,EAAE,EAAE;MACnC;MACA,IAAIC,OAAO,GAAG,KAAK,CAAC;MACpB,IAAID,CAAC,GAAGhB,UAAU,CAACkB,YAAY,KAAK,CAAC,EAAE;QACnCD,OAAO,GAAG,CAAC;QACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,UAAU,CAACa,cAAc,CAAC,CAAC,EAAEM,CAAC,EAAE,EAAE;UAClDP,MAAM,CAACQ,UAAU,CAACH,OAAO,EAAEF,OAAO,EAAEI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;UAChDF,OAAO,EAAE;QACb;QACAF,OAAO,EAAE;MACb;MACAE,OAAO,GAAG,CAAC;MACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,WAAW,EAAES,CAAC,EAAE,EAAE;QAClC;QACA,IAAIA,CAAC,GAAGnB,UAAU,CAACqB,WAAW,KAAK,CAAC,EAAE;UAClCT,MAAM,CAACQ,UAAU,CAACH,OAAO,EAAEF,OAAO,EAAE,IAAI,CAAC;UACzCE,OAAO,EAAE;QACb;QACAL,MAAM,CAACQ,UAAU,CAACH,OAAO,EAAEF,OAAO,EAAEV,SAAS,CAACiB,MAAM,CAACH,CAAC,EAAEH,CAAC,CAAC,CAAC;QAC3DC,OAAO,EAAE;QACT;QACA,IAAIE,CAAC,GAAGnB,UAAU,CAACqB,WAAW,KAAKrB,UAAU,CAACqB,WAAW,GAAG,CAAC,EAAE;UAC3DT,MAAM,CAACQ,UAAU,CAACH,OAAO,EAAEF,OAAO,EAAEC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;UAChDC,OAAO,EAAE;QACb;MACJ;MACAF,OAAO,EAAE;MACT;MACA,IAAIC,CAAC,GAAGhB,UAAU,CAACkB,YAAY,KAAKlB,UAAU,CAACkB,YAAY,GAAG,CAAC,EAAE;QAC7DD,OAAO,GAAG,CAAC;QACX,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,UAAU,CAACa,cAAc,CAAC,CAAC,EAAEM,CAAC,EAAE,EAAE;UAClDP,MAAM,CAACQ,UAAU,CAACH,OAAO,EAAEF,OAAO,EAAE,IAAI,CAAC;UACzCE,OAAO,EAAE;QACb;QACAF,OAAO,EAAE;MACb;IACJ;IACA,OAAO,IAAI,CAACQ,4BAA4B,CAACX,MAAM,EAAE3C,KAAK,EAAEC,MAAM,CAAC;EACnE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,gBAAgB,CAACC,SAAS,CAAC0D,4BAA4B,GAAG,UAAUX,MAAM,EAAEY,QAAQ,EAAEC,SAAS,EAAE;IAC7F,IAAIJ,WAAW,GAAGT,MAAM,CAACc,QAAQ,CAAC,CAAC;IACnC,IAAIR,YAAY,GAAGN,MAAM,CAACe,SAAS,CAAC,CAAC;IACrC,IAAIC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACN,QAAQ,EAAEH,WAAW,CAAC;IACjD,IAAIU,YAAY,GAAGF,IAAI,CAACC,GAAG,CAACL,SAAS,EAAEP,YAAY,CAAC;IACpD,IAAIc,QAAQ,GAAGH,IAAI,CAACI,GAAG,CAACL,WAAW,GAAGP,WAAW,EAAEU,YAAY,GAAGb,YAAY,CAAC;IAC/E,IAAIgB,WAAW,GAAG,CAACN,WAAW,GAAGP,WAAW,GAAGW,QAAQ,IAAI,CAAC;IAC5D,IAAIG,UAAU,GAAG,CAACJ,YAAY,GAAGb,YAAY,GAAGc,QAAQ,IAAI,CAAC;IAC7D,IAAII,MAAM;IACV;IACA,IAAIX,SAAS,GAAGP,YAAY,IAAIM,QAAQ,GAAGH,WAAW,EAAE;MACpDa,WAAW,GAAG,CAAC;MACfC,UAAU,GAAG,CAAC;MACdC,MAAM,GAAG,IAAIjF,SAAS,CAACkE,WAAW,EAAEH,YAAY,CAAC;IACrD,CAAC,MACI;MACDkB,MAAM,GAAG,IAAIjF,SAAS,CAACqE,QAAQ,EAAEC,SAAS,CAAC;IAC/C;IACAW,MAAM,CAACC,KAAK,CAAC,CAAC;IACd,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEC,OAAO,GAAGJ,UAAU,EAAEG,MAAM,GAAGpB,YAAY,EAAEoB,MAAM,EAAE,EAAEC,OAAO,IAAIP,QAAQ,EAAE;MAC7F;MACA,KAAK,IAAIQ,MAAM,GAAG,CAAC,EAAEC,OAAO,GAAGP,WAAW,EAAEM,MAAM,GAAGnB,WAAW,EAAEmB,MAAM,EAAE,EAAEC,OAAO,IAAIT,QAAQ,EAAE;QAC7F,IAAIpB,MAAM,CAACjC,GAAG,CAAC6D,MAAM,EAAEF,MAAM,CAAC,KAAK,CAAC,EAAE;UAClCF,MAAM,CAACM,SAAS,CAACD,OAAO,EAAEF,OAAO,EAAEP,QAAQ,EAAEA,QAAQ,CAAC;QAC1D;MACJ;IACJ;IACA,OAAOI,MAAM;EACjB,CAAC;EACD,OAAOxE,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}