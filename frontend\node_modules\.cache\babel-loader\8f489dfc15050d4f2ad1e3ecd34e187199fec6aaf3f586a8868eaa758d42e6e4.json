{"ast": null, "code": "/*\n* Copyright 2012 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder.ec;\n// import com.google.zxing.ChecksumException;\nimport ChecksumException from '../../../ChecksumException';\nimport ModulusPoly from './ModulusPoly';\nimport ModulusGF from './ModulusGF';\n/**\n * <p>PDF417 error correction implementation.</p>\n *\n * <p>This <a href=\"http://en.wikipedia.org/wiki/Reed%E2%80%93Solomon_error_correction#Example\">example</a>\n * is quite useful in understanding the algorithm.</p>\n *\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.ReedSolomonDecoder\n */\nvar ErrorCorrection = /** @class */function () {\n  function ErrorCorrection() {\n    this.field = ModulusGF.PDF417_GF;\n  }\n  /**\n   * @param received received codewords\n   * @param numECCodewords number of those codewords used for EC\n   * @param erasures location of erasures\n   * @return number of errors\n   * @throws ChecksumException if errors cannot be corrected, maybe because of too many errors\n   */\n  ErrorCorrection.prototype.decode = function (received, numECCodewords, erasures) {\n    var e_1, _a;\n    var poly = new ModulusPoly(this.field, received);\n    var S = new Int32Array(numECCodewords);\n    var error = false;\n    for (var i /*int*/ = numECCodewords; i > 0; i--) {\n      var evaluation = poly.evaluateAt(this.field.exp(i));\n      S[numECCodewords - i] = evaluation;\n      if (evaluation !== 0) {\n        error = true;\n      }\n    }\n    if (!error) {\n      return 0;\n    }\n    var knownErrors = this.field.getOne();\n    if (erasures != null) {\n      try {\n        for (var erasures_1 = __values(erasures), erasures_1_1 = erasures_1.next(); !erasures_1_1.done; erasures_1_1 = erasures_1.next()) {\n          var erasure = erasures_1_1.value;\n          var b = this.field.exp(received.length - 1 - erasure);\n          // Add (1 - bx) term:\n          var term = new ModulusPoly(this.field, new Int32Array([this.field.subtract(0, b), 1]));\n          knownErrors = knownErrors.multiply(term);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (erasures_1_1 && !erasures_1_1.done && (_a = erasures_1.return)) _a.call(erasures_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }\n    var syndrome = new ModulusPoly(this.field, S);\n    // syndrome = syndrome.multiply(knownErrors);\n    var sigmaOmega = this.runEuclideanAlgorithm(this.field.buildMonomial(numECCodewords, 1), syndrome, numECCodewords);\n    var sigma = sigmaOmega[0];\n    var omega = sigmaOmega[1];\n    // sigma = sigma.multiply(knownErrors);\n    var errorLocations = this.findErrorLocations(sigma);\n    var errorMagnitudes = this.findErrorMagnitudes(omega, sigma, errorLocations);\n    for (var i /*int*/ = 0; i < errorLocations.length; i++) {\n      var position = received.length - 1 - this.field.log(errorLocations[i]);\n      if (position < 0) {\n        throw ChecksumException.getChecksumInstance();\n      }\n      received[position] = this.field.subtract(received[position], errorMagnitudes[i]);\n    }\n    return errorLocations.length;\n  };\n  /**\n   *\n   * @param ModulusPoly\n   * @param a\n   * @param ModulusPoly\n   * @param b\n   * @param int\n   * @param R\n   * @throws ChecksumException\n   */\n  ErrorCorrection.prototype.runEuclideanAlgorithm = function (a, b, R) {\n    // Assume a's degree is >= b's\n    if (a.getDegree() < b.getDegree()) {\n      var temp = a;\n      a = b;\n      b = temp;\n    }\n    var rLast = a;\n    var r = b;\n    var tLast = this.field.getZero();\n    var t = this.field.getOne();\n    // Run Euclidean algorithm until r's degree is less than R/2\n    while (r.getDegree() >= Math.round(R / 2)) {\n      var rLastLast = rLast;\n      var tLastLast = tLast;\n      rLast = r;\n      tLast = t;\n      // Divide rLastLast by rLast, with quotient in q and remainder in r\n      if (rLast.isZero()) {\n        // Oops, Euclidean algorithm already terminated?\n        throw ChecksumException.getChecksumInstance();\n      }\n      r = rLastLast;\n      var q = this.field.getZero();\n      var denominatorLeadingTerm = rLast.getCoefficient(rLast.getDegree());\n      var dltInverse = this.field.inverse(denominatorLeadingTerm);\n      while (r.getDegree() >= rLast.getDegree() && !r.isZero()) {\n        var degreeDiff = r.getDegree() - rLast.getDegree();\n        var scale = this.field.multiply(r.getCoefficient(r.getDegree()), dltInverse);\n        q = q.add(this.field.buildMonomial(degreeDiff, scale));\n        r = r.subtract(rLast.multiplyByMonomial(degreeDiff, scale));\n      }\n      t = q.multiply(tLast).subtract(tLastLast).negative();\n    }\n    var sigmaTildeAtZero = t.getCoefficient(0);\n    if (sigmaTildeAtZero === 0) {\n      throw ChecksumException.getChecksumInstance();\n    }\n    var inverse = this.field.inverse(sigmaTildeAtZero);\n    var sigma = t.multiply(inverse);\n    var omega = r.multiply(inverse);\n    return [sigma, omega];\n  };\n  /**\n   *\n   * @param errorLocator\n   * @throws ChecksumException\n   */\n  ErrorCorrection.prototype.findErrorLocations = function (errorLocator) {\n    // This is a direct application of Chien's search\n    var numErrors = errorLocator.getDegree();\n    var result = new Int32Array(numErrors);\n    var e = 0;\n    for (var i /*int*/ = 1; i < this.field.getSize() && e < numErrors; i++) {\n      if (errorLocator.evaluateAt(i) === 0) {\n        result[e] = this.field.inverse(i);\n        e++;\n      }\n    }\n    if (e !== numErrors) {\n      throw ChecksumException.getChecksumInstance();\n    }\n    return result;\n  };\n  ErrorCorrection.prototype.findErrorMagnitudes = function (errorEvaluator, errorLocator, errorLocations) {\n    var errorLocatorDegree = errorLocator.getDegree();\n    var formalDerivativeCoefficients = new Int32Array(errorLocatorDegree);\n    for (var i /*int*/ = 1; i <= errorLocatorDegree; i++) {\n      formalDerivativeCoefficients[errorLocatorDegree - i] = this.field.multiply(i, errorLocator.getCoefficient(i));\n    }\n    var formalDerivative = new ModulusPoly(this.field, formalDerivativeCoefficients);\n    // This is directly applying Forney's Formula\n    var s = errorLocations.length;\n    var result = new Int32Array(s);\n    for (var i /*int*/ = 0; i < s; i++) {\n      var xiInverse = this.field.inverse(errorLocations[i]);\n      var numerator = this.field.subtract(0, errorEvaluator.evaluateAt(xiInverse));\n      var denominator = this.field.inverse(formalDerivative.evaluateAt(xiInverse));\n      result[i] = this.field.multiply(numerator, denominator);\n    }\n    return result;\n  };\n  return ErrorCorrection;\n}();\nexport default ErrorCorrection;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "ChecksumException", "ModulusPoly", "ModulusGF", "ErrorCorrection", "field", "PDF417_GF", "prototype", "decode", "received", "numECCodewords", "erasures", "e_1", "_a", "poly", "S", "Int32Array", "error", "evaluation", "evaluateAt", "exp", "knownErrors", "getOne", "erasures_1", "erasures_1_1", "erasure", "b", "term", "subtract", "multiply", "e_1_1", "return", "syndrome", "sigmaOmega", "runEuclideanAlgorithm", "buildMonomial", "sigma", "omega", "errorLocations", "findErrorLocations", "errorMagnitudes", "findErrorMagnitudes", "position", "log", "getChecksumInstance", "a", "R", "getDegree", "temp", "rLast", "r", "tLast", "getZero", "t", "Math", "round", "rLastLast", "tLastLast", "isZero", "q", "denominatorLeadingTerm", "getCoefficient", "dltInverse", "inverse", "degreeDiff", "scale", "add", "multiplyByMonomial", "negative", "sigmaTildeAtZero", "errorLocator", "numErrors", "result", "e", "getSize", "errorEvaluator", "errorLocatorDegree", "formalDerivativeCoefficients", "formalDerivative", "xiInverse", "numerator", "denominator"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/ec/ErrorCorrection.js"], "sourcesContent": ["/*\n* Copyright 2012 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder.ec;\n// import com.google.zxing.ChecksumException;\nimport ChecksumException from '../../../ChecksumException';\nimport ModulusPoly from './ModulusPoly';\nimport ModulusGF from './ModulusGF';\n/**\n * <p>PDF417 error correction implementation.</p>\n *\n * <p>This <a href=\"http://en.wikipedia.org/wiki/Reed%E2%80%93Solomon_error_correction#Example\">example</a>\n * is quite useful in understanding the algorithm.</p>\n *\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.ReedSolomonDecoder\n */\nvar ErrorCorrection = /** @class */ (function () {\n    function ErrorCorrection() {\n        this.field = ModulusGF.PDF417_GF;\n    }\n    /**\n     * @param received received codewords\n     * @param numECCodewords number of those codewords used for EC\n     * @param erasures location of erasures\n     * @return number of errors\n     * @throws ChecksumException if errors cannot be corrected, maybe because of too many errors\n     */\n    ErrorCorrection.prototype.decode = function (received, numECCodewords, erasures) {\n        var e_1, _a;\n        var poly = new ModulusPoly(this.field, received);\n        var S = new Int32Array(numECCodewords);\n        var error = false;\n        for (var i /*int*/ = numECCodewords; i > 0; i--) {\n            var evaluation = poly.evaluateAt(this.field.exp(i));\n            S[numECCodewords - i] = evaluation;\n            if (evaluation !== 0) {\n                error = true;\n            }\n        }\n        if (!error) {\n            return 0;\n        }\n        var knownErrors = this.field.getOne();\n        if (erasures != null) {\n            try {\n                for (var erasures_1 = __values(erasures), erasures_1_1 = erasures_1.next(); !erasures_1_1.done; erasures_1_1 = erasures_1.next()) {\n                    var erasure = erasures_1_1.value;\n                    var b = this.field.exp(received.length - 1 - erasure);\n                    // Add (1 - bx) term:\n                    var term = new ModulusPoly(this.field, new Int32Array([this.field.subtract(0, b), 1]));\n                    knownErrors = knownErrors.multiply(term);\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (erasures_1_1 && !erasures_1_1.done && (_a = erasures_1.return)) _a.call(erasures_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n        }\n        var syndrome = new ModulusPoly(this.field, S);\n        // syndrome = syndrome.multiply(knownErrors);\n        var sigmaOmega = this.runEuclideanAlgorithm(this.field.buildMonomial(numECCodewords, 1), syndrome, numECCodewords);\n        var sigma = sigmaOmega[0];\n        var omega = sigmaOmega[1];\n        // sigma = sigma.multiply(knownErrors);\n        var errorLocations = this.findErrorLocations(sigma);\n        var errorMagnitudes = this.findErrorMagnitudes(omega, sigma, errorLocations);\n        for (var i /*int*/ = 0; i < errorLocations.length; i++) {\n            var position = received.length - 1 - this.field.log(errorLocations[i]);\n            if (position < 0) {\n                throw ChecksumException.getChecksumInstance();\n            }\n            received[position] = this.field.subtract(received[position], errorMagnitudes[i]);\n        }\n        return errorLocations.length;\n    };\n    /**\n     *\n     * @param ModulusPoly\n     * @param a\n     * @param ModulusPoly\n     * @param b\n     * @param int\n     * @param R\n     * @throws ChecksumException\n     */\n    ErrorCorrection.prototype.runEuclideanAlgorithm = function (a, b, R) {\n        // Assume a's degree is >= b's\n        if (a.getDegree() < b.getDegree()) {\n            var temp = a;\n            a = b;\n            b = temp;\n        }\n        var rLast = a;\n        var r = b;\n        var tLast = this.field.getZero();\n        var t = this.field.getOne();\n        // Run Euclidean algorithm until r's degree is less than R/2\n        while (r.getDegree() >= Math.round(R / 2)) {\n            var rLastLast = rLast;\n            var tLastLast = tLast;\n            rLast = r;\n            tLast = t;\n            // Divide rLastLast by rLast, with quotient in q and remainder in r\n            if (rLast.isZero()) {\n                // Oops, Euclidean algorithm already terminated?\n                throw ChecksumException.getChecksumInstance();\n            }\n            r = rLastLast;\n            var q = this.field.getZero();\n            var denominatorLeadingTerm = rLast.getCoefficient(rLast.getDegree());\n            var dltInverse = this.field.inverse(denominatorLeadingTerm);\n            while (r.getDegree() >= rLast.getDegree() && !r.isZero()) {\n                var degreeDiff = r.getDegree() - rLast.getDegree();\n                var scale = this.field.multiply(r.getCoefficient(r.getDegree()), dltInverse);\n                q = q.add(this.field.buildMonomial(degreeDiff, scale));\n                r = r.subtract(rLast.multiplyByMonomial(degreeDiff, scale));\n            }\n            t = q.multiply(tLast).subtract(tLastLast).negative();\n        }\n        var sigmaTildeAtZero = t.getCoefficient(0);\n        if (sigmaTildeAtZero === 0) {\n            throw ChecksumException.getChecksumInstance();\n        }\n        var inverse = this.field.inverse(sigmaTildeAtZero);\n        var sigma = t.multiply(inverse);\n        var omega = r.multiply(inverse);\n        return [sigma, omega];\n    };\n    /**\n     *\n     * @param errorLocator\n     * @throws ChecksumException\n     */\n    ErrorCorrection.prototype.findErrorLocations = function (errorLocator) {\n        // This is a direct application of Chien's search\n        var numErrors = errorLocator.getDegree();\n        var result = new Int32Array(numErrors);\n        var e = 0;\n        for (var i /*int*/ = 1; i < this.field.getSize() && e < numErrors; i++) {\n            if (errorLocator.evaluateAt(i) === 0) {\n                result[e] = this.field.inverse(i);\n                e++;\n            }\n        }\n        if (e !== numErrors) {\n            throw ChecksumException.getChecksumInstance();\n        }\n        return result;\n    };\n    ErrorCorrection.prototype.findErrorMagnitudes = function (errorEvaluator, errorLocator, errorLocations) {\n        var errorLocatorDegree = errorLocator.getDegree();\n        var formalDerivativeCoefficients = new Int32Array(errorLocatorDegree);\n        for (var i /*int*/ = 1; i <= errorLocatorDegree; i++) {\n            formalDerivativeCoefficients[errorLocatorDegree - i] =\n                this.field.multiply(i, errorLocator.getCoefficient(i));\n        }\n        var formalDerivative = new ModulusPoly(this.field, formalDerivativeCoefficients);\n        // This is directly applying Forney's Formula\n        var s = errorLocations.length;\n        var result = new Int32Array(s);\n        for (var i /*int*/ = 0; i < s; i++) {\n            var xiInverse = this.field.inverse(errorLocations[i]);\n            var numerator = this.field.subtract(0, errorEvaluator.evaluateAt(xiInverse));\n            var denominator = this.field.inverse(formalDerivative.evaluateAt(xiInverse));\n            result[i] = this.field.multiply(numerator, denominator);\n        }\n        return result;\n    };\n    return ErrorCorrection;\n}());\nexport default ErrorCorrection;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA,OAAOW,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG;IACvB,IAAI,CAACC,KAAK,GAAGF,SAAS,CAACG,SAAS;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIF,eAAe,CAACG,SAAS,CAACC,MAAM,GAAG,UAAUC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IAC7E,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIC,IAAI,GAAG,IAAIZ,WAAW,CAAC,IAAI,CAACG,KAAK,EAAEI,QAAQ,CAAC;IAChD,IAAIM,CAAC,GAAG,IAAIC,UAAU,CAACN,cAAc,CAAC;IACtC,IAAIO,KAAK,GAAG,KAAK;IACjB,KAAK,IAAIvB,CAAC,CAAC,UAAUgB,cAAc,EAAEhB,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC7C,IAAIwB,UAAU,GAAGJ,IAAI,CAACK,UAAU,CAAC,IAAI,CAACd,KAAK,CAACe,GAAG,CAAC1B,CAAC,CAAC,CAAC;MACnDqB,CAAC,CAACL,cAAc,GAAGhB,CAAC,CAAC,GAAGwB,UAAU;MAClC,IAAIA,UAAU,KAAK,CAAC,EAAE;QAClBD,KAAK,GAAG,IAAI;MAChB;IACJ;IACA,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,CAAC;IACZ;IACA,IAAII,WAAW,GAAG,IAAI,CAAChB,KAAK,CAACiB,MAAM,CAAC,CAAC;IACrC,IAAIX,QAAQ,IAAI,IAAI,EAAE;MAClB,IAAI;QACA,KAAK,IAAIY,UAAU,GAAGnC,QAAQ,CAACuB,QAAQ,CAAC,EAAEa,YAAY,GAAGD,UAAU,CAAC1B,IAAI,CAAC,CAAC,EAAE,CAAC2B,YAAY,CAACzB,IAAI,EAAEyB,YAAY,GAAGD,UAAU,CAAC1B,IAAI,CAAC,CAAC,EAAE;UAC9H,IAAI4B,OAAO,GAAGD,YAAY,CAAC1B,KAAK;UAChC,IAAI4B,CAAC,GAAG,IAAI,CAACrB,KAAK,CAACe,GAAG,CAACX,QAAQ,CAACb,MAAM,GAAG,CAAC,GAAG6B,OAAO,CAAC;UACrD;UACA,IAAIE,IAAI,GAAG,IAAIzB,WAAW,CAAC,IAAI,CAACG,KAAK,EAAE,IAAIW,UAAU,CAAC,CAAC,IAAI,CAACX,KAAK,CAACuB,QAAQ,CAAC,CAAC,EAAEF,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACtFL,WAAW,GAAGA,WAAW,CAACQ,QAAQ,CAACF,IAAI,CAAC;QAC5C;MACJ,CAAC,CACD,OAAOG,KAAK,EAAE;QAAElB,GAAG,GAAG;UAAEK,KAAK,EAAEa;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIN,YAAY,IAAI,CAACA,YAAY,CAACzB,IAAI,KAAKc,EAAE,GAAGU,UAAU,CAACQ,MAAM,CAAC,EAAElB,EAAE,CAAClB,IAAI,CAAC4B,UAAU,CAAC;QAC3F,CAAC,SACO;UAAE,IAAIX,GAAG,EAAE,MAAMA,GAAG,CAACK,KAAK;QAAE;MACxC;IACJ;IACA,IAAIe,QAAQ,GAAG,IAAI9B,WAAW,CAAC,IAAI,CAACG,KAAK,EAAEU,CAAC,CAAC;IAC7C;IACA,IAAIkB,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC7B,KAAK,CAAC8B,aAAa,CAACzB,cAAc,EAAE,CAAC,CAAC,EAAEsB,QAAQ,EAAEtB,cAAc,CAAC;IAClH,IAAI0B,KAAK,GAAGH,UAAU,CAAC,CAAC,CAAC;IACzB,IAAII,KAAK,GAAGJ,UAAU,CAAC,CAAC,CAAC;IACzB;IACA,IAAIK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAACH,KAAK,CAAC;IACnD,IAAII,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAACJ,KAAK,EAAED,KAAK,EAAEE,cAAc,CAAC;IAC5E,KAAK,IAAI5C,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG4C,cAAc,CAAC1C,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpD,IAAIgD,QAAQ,GAAGjC,QAAQ,CAACb,MAAM,GAAG,CAAC,GAAG,IAAI,CAACS,KAAK,CAACsC,GAAG,CAACL,cAAc,CAAC5C,CAAC,CAAC,CAAC;MACtE,IAAIgD,QAAQ,GAAG,CAAC,EAAE;QACd,MAAMzC,iBAAiB,CAAC2C,mBAAmB,CAAC,CAAC;MACjD;MACAnC,QAAQ,CAACiC,QAAQ,CAAC,GAAG,IAAI,CAACrC,KAAK,CAACuB,QAAQ,CAACnB,QAAQ,CAACiC,QAAQ,CAAC,EAAEF,eAAe,CAAC9C,CAAC,CAAC,CAAC;IACpF;IACA,OAAO4C,cAAc,CAAC1C,MAAM;EAChC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIQ,eAAe,CAACG,SAAS,CAAC2B,qBAAqB,GAAG,UAAUW,CAAC,EAAEnB,CAAC,EAAEoB,CAAC,EAAE;IACjE;IACA,IAAID,CAAC,CAACE,SAAS,CAAC,CAAC,GAAGrB,CAAC,CAACqB,SAAS,CAAC,CAAC,EAAE;MAC/B,IAAIC,IAAI,GAAGH,CAAC;MACZA,CAAC,GAAGnB,CAAC;MACLA,CAAC,GAAGsB,IAAI;IACZ;IACA,IAAIC,KAAK,GAAGJ,CAAC;IACb,IAAIK,CAAC,GAAGxB,CAAC;IACT,IAAIyB,KAAK,GAAG,IAAI,CAAC9C,KAAK,CAAC+C,OAAO,CAAC,CAAC;IAChC,IAAIC,CAAC,GAAG,IAAI,CAAChD,KAAK,CAACiB,MAAM,CAAC,CAAC;IAC3B;IACA,OAAO4B,CAAC,CAACH,SAAS,CAAC,CAAC,IAAIO,IAAI,CAACC,KAAK,CAACT,CAAC,GAAG,CAAC,CAAC,EAAE;MACvC,IAAIU,SAAS,GAAGP,KAAK;MACrB,IAAIQ,SAAS,GAAGN,KAAK;MACrBF,KAAK,GAAGC,CAAC;MACTC,KAAK,GAAGE,CAAC;MACT;MACA,IAAIJ,KAAK,CAACS,MAAM,CAAC,CAAC,EAAE;QAChB;QACA,MAAMzD,iBAAiB,CAAC2C,mBAAmB,CAAC,CAAC;MACjD;MACAM,CAAC,GAAGM,SAAS;MACb,IAAIG,CAAC,GAAG,IAAI,CAACtD,KAAK,CAAC+C,OAAO,CAAC,CAAC;MAC5B,IAAIQ,sBAAsB,GAAGX,KAAK,CAACY,cAAc,CAACZ,KAAK,CAACF,SAAS,CAAC,CAAC,CAAC;MACpE,IAAIe,UAAU,GAAG,IAAI,CAACzD,KAAK,CAAC0D,OAAO,CAACH,sBAAsB,CAAC;MAC3D,OAAOV,CAAC,CAACH,SAAS,CAAC,CAAC,IAAIE,KAAK,CAACF,SAAS,CAAC,CAAC,IAAI,CAACG,CAAC,CAACQ,MAAM,CAAC,CAAC,EAAE;QACtD,IAAIM,UAAU,GAAGd,CAAC,CAACH,SAAS,CAAC,CAAC,GAAGE,KAAK,CAACF,SAAS,CAAC,CAAC;QAClD,IAAIkB,KAAK,GAAG,IAAI,CAAC5D,KAAK,CAACwB,QAAQ,CAACqB,CAAC,CAACW,cAAc,CAACX,CAAC,CAACH,SAAS,CAAC,CAAC,CAAC,EAAEe,UAAU,CAAC;QAC5EH,CAAC,GAAGA,CAAC,CAACO,GAAG,CAAC,IAAI,CAAC7D,KAAK,CAAC8B,aAAa,CAAC6B,UAAU,EAAEC,KAAK,CAAC,CAAC;QACtDf,CAAC,GAAGA,CAAC,CAACtB,QAAQ,CAACqB,KAAK,CAACkB,kBAAkB,CAACH,UAAU,EAAEC,KAAK,CAAC,CAAC;MAC/D;MACAZ,CAAC,GAAGM,CAAC,CAAC9B,QAAQ,CAACsB,KAAK,CAAC,CAACvB,QAAQ,CAAC6B,SAAS,CAAC,CAACW,QAAQ,CAAC,CAAC;IACxD;IACA,IAAIC,gBAAgB,GAAGhB,CAAC,CAACQ,cAAc,CAAC,CAAC,CAAC;IAC1C,IAAIQ,gBAAgB,KAAK,CAAC,EAAE;MACxB,MAAMpE,iBAAiB,CAAC2C,mBAAmB,CAAC,CAAC;IACjD;IACA,IAAImB,OAAO,GAAG,IAAI,CAAC1D,KAAK,CAAC0D,OAAO,CAACM,gBAAgB,CAAC;IAClD,IAAIjC,KAAK,GAAGiB,CAAC,CAACxB,QAAQ,CAACkC,OAAO,CAAC;IAC/B,IAAI1B,KAAK,GAAGa,CAAC,CAACrB,QAAQ,CAACkC,OAAO,CAAC;IAC/B,OAAO,CAAC3B,KAAK,EAAEC,KAAK,CAAC;EACzB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIjC,eAAe,CAACG,SAAS,CAACgC,kBAAkB,GAAG,UAAU+B,YAAY,EAAE;IACnE;IACA,IAAIC,SAAS,GAAGD,YAAY,CAACvB,SAAS,CAAC,CAAC;IACxC,IAAIyB,MAAM,GAAG,IAAIxD,UAAU,CAACuD,SAAS,CAAC;IACtC,IAAIE,CAAC,GAAG,CAAC;IACT,KAAK,IAAI/E,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACW,KAAK,CAACqE,OAAO,CAAC,CAAC,IAAID,CAAC,GAAGF,SAAS,EAAE7E,CAAC,EAAE,EAAE;MACpE,IAAI4E,YAAY,CAACnD,UAAU,CAACzB,CAAC,CAAC,KAAK,CAAC,EAAE;QAClC8E,MAAM,CAACC,CAAC,CAAC,GAAG,IAAI,CAACpE,KAAK,CAAC0D,OAAO,CAACrE,CAAC,CAAC;QACjC+E,CAAC,EAAE;MACP;IACJ;IACA,IAAIA,CAAC,KAAKF,SAAS,EAAE;MACjB,MAAMtE,iBAAiB,CAAC2C,mBAAmB,CAAC,CAAC;IACjD;IACA,OAAO4B,MAAM;EACjB,CAAC;EACDpE,eAAe,CAACG,SAAS,CAACkC,mBAAmB,GAAG,UAAUkC,cAAc,EAAEL,YAAY,EAAEhC,cAAc,EAAE;IACpG,IAAIsC,kBAAkB,GAAGN,YAAY,CAACvB,SAAS,CAAC,CAAC;IACjD,IAAI8B,4BAA4B,GAAG,IAAI7D,UAAU,CAAC4D,kBAAkB,CAAC;IACrE,KAAK,IAAIlF,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,IAAIkF,kBAAkB,EAAElF,CAAC,EAAE,EAAE;MAClDmF,4BAA4B,CAACD,kBAAkB,GAAGlF,CAAC,CAAC,GAChD,IAAI,CAACW,KAAK,CAACwB,QAAQ,CAACnC,CAAC,EAAE4E,YAAY,CAACT,cAAc,CAACnE,CAAC,CAAC,CAAC;IAC9D;IACA,IAAIoF,gBAAgB,GAAG,IAAI5E,WAAW,CAAC,IAAI,CAACG,KAAK,EAAEwE,4BAA4B,CAAC;IAChF;IACA,IAAIvF,CAAC,GAAGgD,cAAc,CAAC1C,MAAM;IAC7B,IAAI4E,MAAM,GAAG,IAAIxD,UAAU,CAAC1B,CAAC,CAAC;IAC9B,KAAK,IAAII,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;MAChC,IAAIqF,SAAS,GAAG,IAAI,CAAC1E,KAAK,CAAC0D,OAAO,CAACzB,cAAc,CAAC5C,CAAC,CAAC,CAAC;MACrD,IAAIsF,SAAS,GAAG,IAAI,CAAC3E,KAAK,CAACuB,QAAQ,CAAC,CAAC,EAAE+C,cAAc,CAACxD,UAAU,CAAC4D,SAAS,CAAC,CAAC;MAC5E,IAAIE,WAAW,GAAG,IAAI,CAAC5E,KAAK,CAAC0D,OAAO,CAACe,gBAAgB,CAAC3D,UAAU,CAAC4D,SAAS,CAAC,CAAC;MAC5EP,MAAM,CAAC9E,CAAC,CAAC,GAAG,IAAI,CAACW,KAAK,CAACwB,QAAQ,CAACmD,SAAS,EAAEC,WAAW,CAAC;IAC3D;IACA,OAAOT,MAAM;EACjB,CAAC;EACD,OAAOpE,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}