{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Meta-data container for QR Code decoding. Instances of this class may be used to convey information back to the\n * decoding caller. Callers are expected to process this.\n *\n * @see com.google.zxing.common.DecoderResult#getOther()\n */\nvar QRCodeDecoderMetaData = /** @class */function () {\n  function QRCodeDecoderMetaData(mirrored) {\n    this.mirrored = mirrored;\n  }\n  /**\n   * @return true if the QR Code was mirrored.\n   */\n  QRCodeDecoderMetaData.prototype.isMirrored = function () {\n    return this.mirrored;\n  };\n  /**\n   * Apply the result points' order correction due to mirroring.\n   *\n   * @param points Array of points to apply mirror correction to.\n   */\n  QRCodeDecoderMetaData.prototype.applyMirroredCorrection = function (points) {\n    if (!this.mirrored || points === null || points.length < 3) {\n      return;\n    }\n    var bottomLeft = points[0];\n    points[0] = points[2];\n    points[2] = bottomLeft;\n    // No need to 'fix' top-left and alignment pattern.\n  };\n  return QRCodeDecoderMetaData;\n}();\nexport default QRCodeDecoderMetaData;", "map": {"version": 3, "names": ["QRCodeDecoderMetaData", "mirrored", "prototype", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "applyMirroredCorrection", "points", "length", "bottomLeft"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/QRCodeDecoderMetaData.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * Meta-data container for QR Code decoding. Instances of this class may be used to convey information back to the\n * decoding caller. Callers are expected to process this.\n *\n * @see com.google.zxing.common.DecoderResult#getOther()\n */\nvar QRCodeDecoderMetaData = /** @class */ (function () {\n    function QRCodeDecoderMetaData(mirrored) {\n        this.mirrored = mirrored;\n    }\n    /**\n     * @return true if the QR Code was mirrored.\n     */\n    QRCodeDecoderMetaData.prototype.isMirrored = function () {\n        return this.mirrored;\n    };\n    /**\n     * Apply the result points' order correction due to mirroring.\n     *\n     * @param points Array of points to apply mirror correction to.\n     */\n    QRCodeDecoderMetaData.prototype.applyMirroredCorrection = function (points) {\n        if (!this.mirrored || points === null || points.length < 3) {\n            return;\n        }\n        var bottomLeft = points[0];\n        points[0] = points[2];\n        points[2] = bottomLeft;\n        // No need to 'fix' top-left and alignment pattern.\n    };\n    return QRCodeDecoderMetaData;\n}());\nexport default QRCodeDecoderMetaData;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,qBAAqB,GAAG,aAAe,YAAY;EACnD,SAASA,qBAAqBA,CAACC,QAAQ,EAAE;IACrC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;AACJ;AACA;EACID,qBAAqB,CAACE,SAAS,CAACC,UAAU,GAAG,YAAY;IACrD,OAAO,IAAI,CAACF,QAAQ;EACxB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACID,qBAAqB,CAACE,SAAS,CAACE,uBAAuB,GAAG,UAAUC,MAAM,EAAE;IACxE,IAAI,CAAC,IAAI,CAACJ,QAAQ,IAAII,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MACxD;IACJ;IACA,IAAIC,UAAU,GAAGF,MAAM,CAAC,CAAC,CAAC;IAC1BA,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IACrBA,MAAM,CAAC,CAAC,CAAC,GAAGE,UAAU;IACtB;EACJ,CAAC;EACD,OAAOP,qBAAqB;AAChC,CAAC,CAAC,CAAE;AACJ,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}