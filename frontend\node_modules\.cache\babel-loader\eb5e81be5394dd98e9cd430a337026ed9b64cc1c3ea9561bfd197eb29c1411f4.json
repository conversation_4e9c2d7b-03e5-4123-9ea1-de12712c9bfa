{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"autoFocus\", \"className\", \"value\", \"defaultValue\", \"referenceDate\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onChange\", \"readOnly\", \"shouldDisableYear\", \"disableHighlightToday\", \"onYearFocus\", \"hasFocus\", \"onFocusedViewChange\", \"yearsOrder\", \"yearsPerRow\", \"timezone\", \"gridLabelId\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_useForkRef as useForkRef, unstable_composeClasses as composeClasses, unstable_useControlled as useControlled, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { PickersYear } from \"./PickersYear.js\";\nimport { useUtils, useNow, useDefaultDates } from \"../internals/hooks/useUtils.js\";\nimport { getYearCalendarUtilityClass } from \"./yearCalendarClasses.js\";\nimport { applyDefaultDate } from \"../internals/utils/date-utils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nimport { useControlledValueWithTimezone } from \"../internals/hooks/useValueWithTimezone.js\";\nimport { DIALOG_WIDTH, MAX_CALENDAR_HEIGHT } from \"../internals/constants/dimensions.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getYearCalendarUtilityClass, classes);\n};\nfunction useYearCalendarDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  return _extends({\n    disablePast: false,\n    disableFuture: false\n  }, themeProps, {\n    yearsPerRow: themeProps.yearsPerRow ?? 3,\n    minDate: applyDefaultDate(utils, themeProps.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, themeProps.maxDate, defaultDates.maxDate)\n  });\n}\nconst YearCalendarRoot = styled('div', {\n  name: 'MuiYearCalendar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  display: 'flex',\n  flexDirection: 'row',\n  flexWrap: 'wrap',\n  overflowY: 'auto',\n  height: '100%',\n  padding: '0 4px',\n  width: DIALOG_WIDTH,\n  maxHeight: MAX_CALENDAR_HEIGHT,\n  // avoid padding increasing width over defined\n  boxSizing: 'border-box',\n  position: 'relative'\n});\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n *\n * API:\n *\n * - [YearCalendar API](https://mui.com/x/api/date-pickers/year-calendar/)\n */\nexport const YearCalendar = /*#__PURE__*/React.forwardRef(function YearCalendar(inProps, ref) {\n  const props = useYearCalendarDefaultizedProps(inProps, 'MuiYearCalendar');\n  const {\n      autoFocus,\n      className,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onChange,\n      readOnly,\n      shouldDisableYear,\n      onYearFocus,\n      hasFocus,\n      onFocusedViewChange,\n      yearsOrder = 'asc',\n      yearsPerRow,\n      timezone: timezoneProp,\n      gridLabelId,\n      slots,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValueWithTimezone({\n    name: 'YearCalendar',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const now = useNow(timezone);\n  const isRtl = useRtl();\n  const utils = useUtils();\n  const referenceDate = React.useMemo(() => singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    timezone,\n    referenceDate: referenceDateProp,\n    granularity: SECTION_TYPE_GRANULARITY.year\n  }), [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const todayYear = React.useMemo(() => utils.getYear(now), [utils, now]);\n  const selectedYear = React.useMemo(() => {\n    if (value != null) {\n      return utils.getYear(value);\n    }\n    return null;\n  }, [value, utils]);\n  const [focusedYear, setFocusedYear] = React.useState(() => selectedYear || utils.getYear(referenceDate));\n  const [internalHasFocus, setInternalHasFocus] = useControlled({\n    name: 'YearCalendar',\n    state: 'hasFocus',\n    controlled: hasFocus,\n    default: autoFocus ?? false\n  });\n  const changeHasFocus = useEventCallback(newHasFocus => {\n    setInternalHasFocus(newHasFocus);\n    if (onFocusedViewChange) {\n      onFocusedViewChange(newHasFocus);\n    }\n  });\n  const isYearDisabled = React.useCallback(dateToValidate => {\n    if (disablePast && utils.isBeforeYear(dateToValidate, now)) {\n      return true;\n    }\n    if (disableFuture && utils.isAfterYear(dateToValidate, now)) {\n      return true;\n    }\n    if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {\n      return true;\n    }\n    if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {\n      return true;\n    }\n    if (!shouldDisableYear) {\n      return false;\n    }\n    const yearToValidate = utils.startOfYear(dateToValidate);\n    return shouldDisableYear(yearToValidate);\n  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableYear, utils]);\n  const handleYearSelection = useEventCallback((event, year) => {\n    if (readOnly) {\n      return;\n    }\n    const newDate = utils.setYear(value ?? referenceDate, year);\n    handleValueChange(newDate);\n  });\n  const focusYear = useEventCallback(year => {\n    if (!isYearDisabled(utils.setYear(value ?? referenceDate, year))) {\n      setFocusedYear(year);\n      changeHasFocus(true);\n      onYearFocus?.(year);\n    }\n  });\n  React.useEffect(() => {\n    setFocusedYear(prevFocusedYear => selectedYear !== null && prevFocusedYear !== selectedYear ? selectedYear : prevFocusedYear);\n  }, [selectedYear]);\n  const verticalDirection = yearsOrder !== 'desc' ? yearsPerRow * 1 : yearsPerRow * -1;\n  const horizontalDirection = isRtl && yearsOrder === 'asc' || !isRtl && yearsOrder === 'desc' ? -1 : 1;\n  const handleKeyDown = useEventCallback((event, year) => {\n    switch (event.key) {\n      case 'ArrowUp':\n        focusYear(year - verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        focusYear(year + verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        focusYear(year - horizontalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowRight':\n        focusYear(year + horizontalDirection);\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  });\n  const handleYearFocus = useEventCallback((event, year) => {\n    focusYear(year);\n  });\n  const handleYearBlur = useEventCallback((event, year) => {\n    if (focusedYear === year) {\n      changeHasFocus(false);\n    }\n  });\n  const scrollerRef = React.useRef(null);\n  const handleRef = useForkRef(ref, scrollerRef);\n  React.useEffect(() => {\n    if (autoFocus || scrollerRef.current === null) {\n      return;\n    }\n    const tabbableButton = scrollerRef.current.querySelector('[tabindex=\"0\"]');\n    if (!tabbableButton) {\n      return;\n    }\n\n    // Taken from useScroll in x-data-grid, but vertically centered\n    const offsetHeight = tabbableButton.offsetHeight;\n    const offsetTop = tabbableButton.offsetTop;\n    const clientHeight = scrollerRef.current.clientHeight;\n    const scrollTop = scrollerRef.current.scrollTop;\n    const elementBottom = offsetTop + offsetHeight;\n    if (offsetHeight > clientHeight || offsetTop < scrollTop) {\n      // Button already visible\n      return;\n    }\n    scrollerRef.current.scrollTop = elementBottom - clientHeight / 2 - offsetHeight / 2;\n  }, [autoFocus]);\n  const yearRange = utils.getYearRange([minDate, maxDate]);\n  if (yearsOrder === 'desc') {\n    yearRange.reverse();\n  }\n  return /*#__PURE__*/_jsx(YearCalendarRoot, _extends({\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    role: \"radiogroup\",\n    \"aria-labelledby\": gridLabelId\n  }, other, {\n    children: yearRange.map(year => {\n      const yearNumber = utils.getYear(year);\n      const isSelected = yearNumber === selectedYear;\n      const isDisabled = disabled || isYearDisabled(year);\n      return /*#__PURE__*/_jsx(PickersYear, {\n        selected: isSelected,\n        value: yearNumber,\n        onClick: handleYearSelection,\n        onKeyDown: handleKeyDown,\n        autoFocus: internalHasFocus && yearNumber === focusedYear,\n        disabled: isDisabled,\n        tabIndex: yearNumber === focusedYear && !isDisabled ? 0 : -1,\n        onFocus: handleYearFocus,\n        onBlur: handleYearBlur,\n        \"aria-current\": todayYear === yearNumber ? 'date' : undefined,\n        yearsPerRow: yearsPerRow,\n        slots: slots,\n        slotProps: slotProps,\n        children: utils.format(year, 'year')\n      }, utils.format(year, 'year'));\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? YearCalendar.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true` picker is disabled\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  gridLabelId: PropTypes.string,\n  hasFocus: PropTypes.bool,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: PropTypes.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: PropTypes.object,\n  /**\n   * Callback fired when the value changes.\n   * @template TDate\n   * @param {TDate} value The new value.\n   */\n  onChange: PropTypes.func,\n  onFocusedViewChange: PropTypes.func,\n  onYearFocus: PropTypes.func,\n  /**\n   * If `true` picker is readonly\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid year using the validation props, except callbacks such as `shouldDisableYear`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific year.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default 3\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n} : void 0;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "useRtl", "styled", "useThemeProps", "unstable_useForkRef", "useForkRef", "unstable_composeClasses", "composeClasses", "unstable_useControlled", "useControlled", "unstable_useEventCallback", "useEventCallback", "PickersYear", "useUtils", "useNow", "useDefaultDates", "getYearCalendarUtilityClass", "applyDefaultDate", "singleItemValueManager", "SECTION_TYPE_GRANULARITY", "useControlledValueWithTimezone", "DIALOG_WIDTH", "MAX_CALENDAR_HEIGHT", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "useYearCalendarDefaultizedProps", "props", "name", "utils", "defaultDates", "themeProps", "disablePast", "disableFuture", "yearsPerRow", "minDate", "maxDate", "YearCalendarRoot", "slot", "overridesResolver", "styles", "display", "flexDirection", "flexWrap", "overflowY", "height", "padding", "width", "maxHeight", "boxSizing", "position", "YearCalendar", "forwardRef", "inProps", "ref", "autoFocus", "className", "value", "valueProp", "defaultValue", "referenceDate", "referenceDateProp", "disabled", "onChange", "readOnly", "shouldDisableYear", "onYearFocus", "hasFocus", "onFocusedViewChange", "yearsOrder", "timezone", "timezoneProp", "gridLabelId", "slotProps", "other", "handleValueChange", "valueManager", "now", "isRtl", "useMemo", "getInitialReferenceValue", "granularity", "year", "todayYear", "getYear", "selected<PERSON>ear", "focusedYear", "setFocusedYear", "useState", "internalHasFocus", "setInternalHasFocus", "state", "controlled", "default", "changeHasFocus", "newHasFocus", "isYearDisabled", "useCallback", "dateToValidate", "isBeforeYear", "isAfterYear", "yearToValidate", "startOfYear", "handleYearSelection", "event", "newDate", "setYear", "focusYear", "useEffect", "prevFocusedYear", "verticalDirection", "horizontalDirection", "handleKeyDown", "key", "preventDefault", "handleYearFocus", "handleYearBlur", "scrollerRef", "useRef", "handleRef", "current", "tabbableButton", "querySelector", "offsetHeight", "offsetTop", "clientHeight", "scrollTop", "elementBottom", "year<PERSON><PERSON><PERSON>", "getYearRange", "reverse", "role", "children", "map", "yearNumber", "isSelected", "isDisabled", "selected", "onClick", "onKeyDown", "tabIndex", "onFocus", "onBlur", "undefined", "format", "process", "env", "NODE_ENV", "propTypes", "bool", "object", "string", "disableHighlightToday", "func", "sx", "oneOfType", "arrayOf", "oneOf"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/YearCalendar/YearCalendar.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"autoFocus\", \"className\", \"value\", \"defaultValue\", \"referenceDate\", \"disabled\", \"disableFuture\", \"disablePast\", \"maxDate\", \"minDate\", \"onChange\", \"readOnly\", \"shouldDisableYear\", \"disableHighlightToday\", \"onYearFocus\", \"hasFocus\", \"onFocusedViewChange\", \"yearsOrder\", \"yearsPerRow\", \"timezone\", \"gridLabelId\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport { styled, useThemeProps } from '@mui/material/styles';\nimport { unstable_useForkRef as useForkRef, unstable_composeClasses as composeClasses, unstable_useControlled as useControlled, unstable_useEventCallback as useEventCallback } from '@mui/utils';\nimport { PickersYear } from \"./PickersYear.js\";\nimport { useUtils, useNow, useDefaultDates } from \"../internals/hooks/useUtils.js\";\nimport { getYearCalendarUtilityClass } from \"./yearCalendarClasses.js\";\nimport { applyDefaultDate } from \"../internals/utils/date-utils.js\";\nimport { singleItemValueManager } from \"../internals/utils/valueManagers.js\";\nimport { SECTION_TYPE_GRANULARITY } from \"../internals/utils/getDefaultReferenceDate.js\";\nimport { useControlledValueWithTimezone } from \"../internals/hooks/useValueWithTimezone.js\";\nimport { DIALOG_WIDTH, MAX_CALENDAR_HEIGHT } from \"../internals/constants/dimensions.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getYearCalendarUtilityClass, classes);\n};\nfunction useYearCalendarDefaultizedProps(props, name) {\n  const utils = useUtils();\n  const defaultDates = useDefaultDates();\n  const themeProps = useThemeProps({\n    props,\n    name\n  });\n  return _extends({\n    disablePast: false,\n    disableFuture: false\n  }, themeProps, {\n    yearsPerRow: themeProps.yearsPerRow ?? 3,\n    minDate: applyDefaultDate(utils, themeProps.minDate, defaultDates.minDate),\n    maxDate: applyDefaultDate(utils, themeProps.maxDate, defaultDates.maxDate)\n  });\n}\nconst YearCalendarRoot = styled('div', {\n  name: 'MuiYearCalendar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  display: 'flex',\n  flexDirection: 'row',\n  flexWrap: 'wrap',\n  overflowY: 'auto',\n  height: '100%',\n  padding: '0 4px',\n  width: DIALOG_WIDTH,\n  maxHeight: MAX_CALENDAR_HEIGHT,\n  // avoid padding increasing width over defined\n  boxSizing: 'border-box',\n  position: 'relative'\n});\n/**\n * Demos:\n *\n * - [DateCalendar](https://mui.com/x/react-date-pickers/date-calendar/)\n *\n * API:\n *\n * - [YearCalendar API](https://mui.com/x/api/date-pickers/year-calendar/)\n */\nexport const YearCalendar = /*#__PURE__*/React.forwardRef(function YearCalendar(inProps, ref) {\n  const props = useYearCalendarDefaultizedProps(inProps, 'MuiYearCalendar');\n  const {\n      autoFocus,\n      className,\n      value: valueProp,\n      defaultValue,\n      referenceDate: referenceDateProp,\n      disabled,\n      disableFuture,\n      disablePast,\n      maxDate,\n      minDate,\n      onChange,\n      readOnly,\n      shouldDisableYear,\n      onYearFocus,\n      hasFocus,\n      onFocusedViewChange,\n      yearsOrder = 'asc',\n      yearsPerRow,\n      timezone: timezoneProp,\n      gridLabelId,\n      slots,\n      slotProps\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    value,\n    handleValueChange,\n    timezone\n  } = useControlledValueWithTimezone({\n    name: 'YearCalendar',\n    timezone: timezoneProp,\n    value: valueProp,\n    defaultValue,\n    referenceDate: referenceDateProp,\n    onChange,\n    valueManager: singleItemValueManager\n  });\n  const now = useNow(timezone);\n  const isRtl = useRtl();\n  const utils = useUtils();\n  const referenceDate = React.useMemo(() => singleItemValueManager.getInitialReferenceValue({\n    value,\n    utils,\n    props,\n    timezone,\n    referenceDate: referenceDateProp,\n    granularity: SECTION_TYPE_GRANULARITY.year\n  }), [] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n  const ownerState = props;\n  const classes = useUtilityClasses(ownerState);\n  const todayYear = React.useMemo(() => utils.getYear(now), [utils, now]);\n  const selectedYear = React.useMemo(() => {\n    if (value != null) {\n      return utils.getYear(value);\n    }\n    return null;\n  }, [value, utils]);\n  const [focusedYear, setFocusedYear] = React.useState(() => selectedYear || utils.getYear(referenceDate));\n  const [internalHasFocus, setInternalHasFocus] = useControlled({\n    name: 'YearCalendar',\n    state: 'hasFocus',\n    controlled: hasFocus,\n    default: autoFocus ?? false\n  });\n  const changeHasFocus = useEventCallback(newHasFocus => {\n    setInternalHasFocus(newHasFocus);\n    if (onFocusedViewChange) {\n      onFocusedViewChange(newHasFocus);\n    }\n  });\n  const isYearDisabled = React.useCallback(dateToValidate => {\n    if (disablePast && utils.isBeforeYear(dateToValidate, now)) {\n      return true;\n    }\n    if (disableFuture && utils.isAfterYear(dateToValidate, now)) {\n      return true;\n    }\n    if (minDate && utils.isBeforeYear(dateToValidate, minDate)) {\n      return true;\n    }\n    if (maxDate && utils.isAfterYear(dateToValidate, maxDate)) {\n      return true;\n    }\n    if (!shouldDisableYear) {\n      return false;\n    }\n    const yearToValidate = utils.startOfYear(dateToValidate);\n    return shouldDisableYear(yearToValidate);\n  }, [disableFuture, disablePast, maxDate, minDate, now, shouldDisableYear, utils]);\n  const handleYearSelection = useEventCallback((event, year) => {\n    if (readOnly) {\n      return;\n    }\n    const newDate = utils.setYear(value ?? referenceDate, year);\n    handleValueChange(newDate);\n  });\n  const focusYear = useEventCallback(year => {\n    if (!isYearDisabled(utils.setYear(value ?? referenceDate, year))) {\n      setFocusedYear(year);\n      changeHasFocus(true);\n      onYearFocus?.(year);\n    }\n  });\n  React.useEffect(() => {\n    setFocusedYear(prevFocusedYear => selectedYear !== null && prevFocusedYear !== selectedYear ? selectedYear : prevFocusedYear);\n  }, [selectedYear]);\n  const verticalDirection = yearsOrder !== 'desc' ? yearsPerRow * 1 : yearsPerRow * -1;\n  const horizontalDirection = isRtl && yearsOrder === 'asc' || !isRtl && yearsOrder === 'desc' ? -1 : 1;\n  const handleKeyDown = useEventCallback((event, year) => {\n    switch (event.key) {\n      case 'ArrowUp':\n        focusYear(year - verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n        focusYear(year + verticalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowLeft':\n        focusYear(year - horizontalDirection);\n        event.preventDefault();\n        break;\n      case 'ArrowRight':\n        focusYear(year + horizontalDirection);\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  });\n  const handleYearFocus = useEventCallback((event, year) => {\n    focusYear(year);\n  });\n  const handleYearBlur = useEventCallback((event, year) => {\n    if (focusedYear === year) {\n      changeHasFocus(false);\n    }\n  });\n  const scrollerRef = React.useRef(null);\n  const handleRef = useForkRef(ref, scrollerRef);\n  React.useEffect(() => {\n    if (autoFocus || scrollerRef.current === null) {\n      return;\n    }\n    const tabbableButton = scrollerRef.current.querySelector('[tabindex=\"0\"]');\n    if (!tabbableButton) {\n      return;\n    }\n\n    // Taken from useScroll in x-data-grid, but vertically centered\n    const offsetHeight = tabbableButton.offsetHeight;\n    const offsetTop = tabbableButton.offsetTop;\n    const clientHeight = scrollerRef.current.clientHeight;\n    const scrollTop = scrollerRef.current.scrollTop;\n    const elementBottom = offsetTop + offsetHeight;\n    if (offsetHeight > clientHeight || offsetTop < scrollTop) {\n      // Button already visible\n      return;\n    }\n    scrollerRef.current.scrollTop = elementBottom - clientHeight / 2 - offsetHeight / 2;\n  }, [autoFocus]);\n  const yearRange = utils.getYearRange([minDate, maxDate]);\n  if (yearsOrder === 'desc') {\n    yearRange.reverse();\n  }\n  return /*#__PURE__*/_jsx(YearCalendarRoot, _extends({\n    ref: handleRef,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    role: \"radiogroup\",\n    \"aria-labelledby\": gridLabelId\n  }, other, {\n    children: yearRange.map(year => {\n      const yearNumber = utils.getYear(year);\n      const isSelected = yearNumber === selectedYear;\n      const isDisabled = disabled || isYearDisabled(year);\n      return /*#__PURE__*/_jsx(PickersYear, {\n        selected: isSelected,\n        value: yearNumber,\n        onClick: handleYearSelection,\n        onKeyDown: handleKeyDown,\n        autoFocus: internalHasFocus && yearNumber === focusedYear,\n        disabled: isDisabled,\n        tabIndex: yearNumber === focusedYear && !isDisabled ? 0 : -1,\n        onFocus: handleYearFocus,\n        onBlur: handleYearBlur,\n        \"aria-current\": todayYear === yearNumber ? 'date' : undefined,\n        yearsPerRow: yearsPerRow,\n        slots: slots,\n        slotProps: slotProps,\n        children: utils.format(year, 'year')\n      }, utils.format(year, 'year'));\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? YearCalendar.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  autoFocus: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  className: PropTypes.string,\n  /**\n   * The default selected value.\n   * Used when the component is not controlled.\n   */\n  defaultValue: PropTypes.object,\n  /**\n   * If `true` picker is disabled\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, disable values after the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disableFuture: PropTypes.bool,\n  /**\n   * If `true`, today's date is rendering without highlighting with circle.\n   * @default false\n   */\n  disableHighlightToday: PropTypes.bool,\n  /**\n   * If `true`, disable values before the current date for date components, time for time components and both for date time components.\n   * @default false\n   */\n  disablePast: PropTypes.bool,\n  gridLabelId: PropTypes.string,\n  hasFocus: PropTypes.bool,\n  /**\n   * Maximal selectable date.\n   * @default 2099-12-31\n   */\n  maxDate: PropTypes.object,\n  /**\n   * Minimal selectable date.\n   * @default 1900-01-01\n   */\n  minDate: PropTypes.object,\n  /**\n   * Callback fired when the value changes.\n   * @template TDate\n   * @param {TDate} value The new value.\n   */\n  onChange: PropTypes.func,\n  onFocusedViewChange: PropTypes.func,\n  onYearFocus: PropTypes.func,\n  /**\n   * If `true` picker is readonly\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * The date used to generate the new value when both `value` and `defaultValue` are empty.\n   * @default The closest valid year using the validation props, except callbacks such as `shouldDisableYear`.\n   */\n  referenceDate: PropTypes.object,\n  /**\n   * Disable specific year.\n   * @template TDate\n   * @param {TDate} year The year to test.\n   * @returns {boolean} If `true`, the year will be disabled.\n   */\n  shouldDisableYear: PropTypes.func,\n  /**\n   * The props used for each component slot.\n   * @default {}\n   */\n  slotProps: PropTypes.object,\n  /**\n   * Overridable component slots.\n   * @default {}\n   */\n  slots: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Choose which timezone to use for the value.\n   * Example: \"default\", \"system\", \"UTC\", \"America/New_York\".\n   * If you pass values from other timezones to some props, they will be converted to this timezone before being used.\n   * @see See the {@link https://mui.com/x/react-date-pickers/timezone/ timezones documentation} for more details.\n   * @default The timezone of the `value` or `defaultValue` prop is defined, 'default' otherwise.\n   */\n  timezone: PropTypes.string,\n  /**\n   * The selected value.\n   * Used when the component is controlled.\n   */\n  value: PropTypes.object,\n  /**\n   * Years are displayed in ascending (chronological) order by default.\n   * If `desc`, years are displayed in descending order.\n   * @default 'asc'\n   */\n  yearsOrder: PropTypes.oneOf(['asc', 'desc']),\n  /**\n   * Years rendered per row.\n   * @default 3\n   */\n  yearsPerRow: PropTypes.oneOf([3, 4])\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,aAAa,EAAE,UAAU,EAAE,qBAAqB,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,CAAC;AAC9V,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,uBAAuB,IAAIC,cAAc,EAAEC,sBAAsB,IAAIC,aAAa,EAAEC,yBAAyB,IAAIC,gBAAgB,QAAQ,YAAY;AACjM,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,QAAQ,EAAEC,MAAM,EAAEC,eAAe,QAAQ,gCAAgC;AAClF,SAASC,2BAA2B,QAAQ,0BAA0B;AACtE,SAASC,gBAAgB,QAAQ,kCAAkC;AACnE,SAASC,sBAAsB,QAAQ,qCAAqC;AAC5E,SAASC,wBAAwB,QAAQ,+CAA+C;AACxF,SAASC,8BAA8B,QAAQ,4CAA4C;AAC3F,SAASC,YAAY,EAAEC,mBAAmB,QAAQ,sCAAsC;AACxF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOtB,cAAc,CAACqB,KAAK,EAAEZ,2BAA2B,EAAEW,OAAO,CAAC;AACpE,CAAC;AACD,SAASG,+BAA+BA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACpD,MAAMC,KAAK,GAAGpB,QAAQ,CAAC,CAAC;EACxB,MAAMqB,YAAY,GAAGnB,eAAe,CAAC,CAAC;EACtC,MAAMoB,UAAU,GAAGhC,aAAa,CAAC;IAC/B4B,KAAK;IACLC;EACF,CAAC,CAAC;EACF,OAAOpC,QAAQ,CAAC;IACdwC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE;EACjB,CAAC,EAAEF,UAAU,EAAE;IACbG,WAAW,EAAEH,UAAU,CAACG,WAAW,IAAI,CAAC;IACxCC,OAAO,EAAEtB,gBAAgB,CAACgB,KAAK,EAAEE,UAAU,CAACI,OAAO,EAAEL,YAAY,CAACK,OAAO,CAAC;IAC1EC,OAAO,EAAEvB,gBAAgB,CAACgB,KAAK,EAAEE,UAAU,CAACK,OAAO,EAAEN,YAAY,CAACM,OAAO;EAC3E,CAAC,CAAC;AACJ;AACA,MAAMC,gBAAgB,GAAGvC,MAAM,CAAC,KAAK,EAAE;EACrC8B,IAAI,EAAE,iBAAiB;EACvBU,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACZ,KAAK,EAAEa,MAAM,KAAKA,MAAM,CAACf;AAC/C,CAAC,CAAC,CAAC;EACDgB,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,KAAK;EACpBC,QAAQ,EAAE,MAAM;EAChBC,SAAS,EAAE,MAAM;EACjBC,MAAM,EAAE,MAAM;EACdC,OAAO,EAAE,OAAO;EAChBC,KAAK,EAAE9B,YAAY;EACnB+B,SAAS,EAAE9B,mBAAmB;EAC9B;EACA+B,SAAS,EAAE,YAAY;EACvBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAG,aAAazD,KAAK,CAAC0D,UAAU,CAAC,SAASD,YAAYA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC5F,MAAM3B,KAAK,GAAGD,+BAA+B,CAAC2B,OAAO,EAAE,iBAAiB,CAAC;EACzE,MAAM;MACFE,SAAS;MACTC,SAAS;MACTC,KAAK,EAAEC,SAAS;MAChBC,YAAY;MACZC,aAAa,EAAEC,iBAAiB;MAChCC,QAAQ;MACR7B,aAAa;MACbD,WAAW;MACXI,OAAO;MACPD,OAAO;MACP4B,QAAQ;MACRC,QAAQ;MACRC,iBAAiB;MACjBC,WAAW;MACXC,QAAQ;MACRC,mBAAmB;MACnBC,UAAU,GAAG,KAAK;MAClBnC,WAAW;MACXoC,QAAQ,EAAEC,YAAY;MACtBC,WAAW;MACXhD,KAAK;MACLiD;IACF,CAAC,GAAG9C,KAAK;IACT+C,KAAK,GAAGnF,6BAA6B,CAACoC,KAAK,EAAElC,SAAS,CAAC;EACzD,MAAM;IACJgE,KAAK;IACLkB,iBAAiB;IACjBL;EACF,CAAC,GAAGtD,8BAA8B,CAAC;IACjCY,IAAI,EAAE,cAAc;IACpB0C,QAAQ,EAAEC,YAAY;IACtBd,KAAK,EAAEC,SAAS;IAChBC,YAAY;IACZC,aAAa,EAAEC,iBAAiB;IAChCE,QAAQ;IACRa,YAAY,EAAE9D;EAChB,CAAC,CAAC;EACF,MAAM+D,GAAG,GAAGnE,MAAM,CAAC4D,QAAQ,CAAC;EAC5B,MAAMQ,KAAK,GAAGjF,MAAM,CAAC,CAAC;EACtB,MAAMgC,KAAK,GAAGpB,QAAQ,CAAC,CAAC;EACxB,MAAMmD,aAAa,GAAGlE,KAAK,CAACqF,OAAO,CAAC,MAAMjE,sBAAsB,CAACkE,wBAAwB,CAAC;IACxFvB,KAAK;IACL5B,KAAK;IACLF,KAAK;IACL2C,QAAQ;IACRV,aAAa,EAAEC,iBAAiB;IAChCoB,WAAW,EAAElE,wBAAwB,CAACmE;EACxC,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,CAAC;EACD,MAAM5D,UAAU,GAAGK,KAAK;EACxB,MAAMJ,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM6D,SAAS,GAAGzF,KAAK,CAACqF,OAAO,CAAC,MAAMlD,KAAK,CAACuD,OAAO,CAACP,GAAG,CAAC,EAAE,CAAChD,KAAK,EAAEgD,GAAG,CAAC,CAAC;EACvE,MAAMQ,YAAY,GAAG3F,KAAK,CAACqF,OAAO,CAAC,MAAM;IACvC,IAAItB,KAAK,IAAI,IAAI,EAAE;MACjB,OAAO5B,KAAK,CAACuD,OAAO,CAAC3B,KAAK,CAAC;IAC7B;IACA,OAAO,IAAI;EACb,CAAC,EAAE,CAACA,KAAK,EAAE5B,KAAK,CAAC,CAAC;EAClB,MAAM,CAACyD,WAAW,EAAEC,cAAc,CAAC,GAAG7F,KAAK,CAAC8F,QAAQ,CAAC,MAAMH,YAAY,IAAIxD,KAAK,CAACuD,OAAO,CAACxB,aAAa,CAAC,CAAC;EACxG,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,aAAa,CAAC;IAC5DuB,IAAI,EAAE,cAAc;IACpB+D,KAAK,EAAE,UAAU;IACjBC,UAAU,EAAEzB,QAAQ;IACpB0B,OAAO,EAAEtC,SAAS,IAAI;EACxB,CAAC,CAAC;EACF,MAAMuC,cAAc,GAAGvF,gBAAgB,CAACwF,WAAW,IAAI;IACrDL,mBAAmB,CAACK,WAAW,CAAC;IAChC,IAAI3B,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC2B,WAAW,CAAC;IAClC;EACF,CAAC,CAAC;EACF,MAAMC,cAAc,GAAGtG,KAAK,CAACuG,WAAW,CAACC,cAAc,IAAI;IACzD,IAAIlE,WAAW,IAAIH,KAAK,CAACsE,YAAY,CAACD,cAAc,EAAErB,GAAG,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAI5C,aAAa,IAAIJ,KAAK,CAACuE,WAAW,CAACF,cAAc,EAAErB,GAAG,CAAC,EAAE;MAC3D,OAAO,IAAI;IACb;IACA,IAAI1C,OAAO,IAAIN,KAAK,CAACsE,YAAY,CAACD,cAAc,EAAE/D,OAAO,CAAC,EAAE;MAC1D,OAAO,IAAI;IACb;IACA,IAAIC,OAAO,IAAIP,KAAK,CAACuE,WAAW,CAACF,cAAc,EAAE9D,OAAO,CAAC,EAAE;MACzD,OAAO,IAAI;IACb;IACA,IAAI,CAAC6B,iBAAiB,EAAE;MACtB,OAAO,KAAK;IACd;IACA,MAAMoC,cAAc,GAAGxE,KAAK,CAACyE,WAAW,CAACJ,cAAc,CAAC;IACxD,OAAOjC,iBAAiB,CAACoC,cAAc,CAAC;EAC1C,CAAC,EAAE,CAACpE,aAAa,EAAED,WAAW,EAAEI,OAAO,EAAED,OAAO,EAAE0C,GAAG,EAAEZ,iBAAiB,EAAEpC,KAAK,CAAC,CAAC;EACjF,MAAM0E,mBAAmB,GAAGhG,gBAAgB,CAAC,CAACiG,KAAK,EAAEtB,IAAI,KAAK;IAC5D,IAAIlB,QAAQ,EAAE;MACZ;IACF;IACA,MAAMyC,OAAO,GAAG5E,KAAK,CAAC6E,OAAO,CAACjD,KAAK,IAAIG,aAAa,EAAEsB,IAAI,CAAC;IAC3DP,iBAAiB,CAAC8B,OAAO,CAAC;EAC5B,CAAC,CAAC;EACF,MAAME,SAAS,GAAGpG,gBAAgB,CAAC2E,IAAI,IAAI;IACzC,IAAI,CAACc,cAAc,CAACnE,KAAK,CAAC6E,OAAO,CAACjD,KAAK,IAAIG,aAAa,EAAEsB,IAAI,CAAC,CAAC,EAAE;MAChEK,cAAc,CAACL,IAAI,CAAC;MACpBY,cAAc,CAAC,IAAI,CAAC;MACpB5B,WAAW,GAAGgB,IAAI,CAAC;IACrB;EACF,CAAC,CAAC;EACFxF,KAAK,CAACkH,SAAS,CAAC,MAAM;IACpBrB,cAAc,CAACsB,eAAe,IAAIxB,YAAY,KAAK,IAAI,IAAIwB,eAAe,KAAKxB,YAAY,GAAGA,YAAY,GAAGwB,eAAe,CAAC;EAC/H,CAAC,EAAE,CAACxB,YAAY,CAAC,CAAC;EAClB,MAAMyB,iBAAiB,GAAGzC,UAAU,KAAK,MAAM,GAAGnC,WAAW,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;EACpF,MAAM6E,mBAAmB,GAAGjC,KAAK,IAAIT,UAAU,KAAK,KAAK,IAAI,CAACS,KAAK,IAAIT,UAAU,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;EACrG,MAAM2C,aAAa,GAAGzG,gBAAgB,CAAC,CAACiG,KAAK,EAAEtB,IAAI,KAAK;IACtD,QAAQsB,KAAK,CAACS,GAAG;MACf,KAAK,SAAS;QACZN,SAAS,CAACzB,IAAI,GAAG4B,iBAAiB,CAAC;QACnCN,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,WAAW;QACdP,SAAS,CAACzB,IAAI,GAAG4B,iBAAiB,CAAC;QACnCN,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,WAAW;QACdP,SAAS,CAACzB,IAAI,GAAG6B,mBAAmB,CAAC;QACrCP,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF,KAAK,YAAY;QACfP,SAAS,CAACzB,IAAI,GAAG6B,mBAAmB,CAAC;QACrCP,KAAK,CAACU,cAAc,CAAC,CAAC;QACtB;MACF;QACE;IACJ;EACF,CAAC,CAAC;EACF,MAAMC,eAAe,GAAG5G,gBAAgB,CAAC,CAACiG,KAAK,EAAEtB,IAAI,KAAK;IACxDyB,SAAS,CAACzB,IAAI,CAAC;EACjB,CAAC,CAAC;EACF,MAAMkC,cAAc,GAAG7G,gBAAgB,CAAC,CAACiG,KAAK,EAAEtB,IAAI,KAAK;IACvD,IAAII,WAAW,KAAKJ,IAAI,EAAE;MACxBY,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMuB,WAAW,GAAG3H,KAAK,CAAC4H,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMC,SAAS,GAAGtH,UAAU,CAACqD,GAAG,EAAE+D,WAAW,CAAC;EAC9C3H,KAAK,CAACkH,SAAS,CAAC,MAAM;IACpB,IAAIrD,SAAS,IAAI8D,WAAW,CAACG,OAAO,KAAK,IAAI,EAAE;MAC7C;IACF;IACA,MAAMC,cAAc,GAAGJ,WAAW,CAACG,OAAO,CAACE,aAAa,CAAC,gBAAgB,CAAC;IAC1E,IAAI,CAACD,cAAc,EAAE;MACnB;IACF;;IAEA;IACA,MAAME,YAAY,GAAGF,cAAc,CAACE,YAAY;IAChD,MAAMC,SAAS,GAAGH,cAAc,CAACG,SAAS;IAC1C,MAAMC,YAAY,GAAGR,WAAW,CAACG,OAAO,CAACK,YAAY;IACrD,MAAMC,SAAS,GAAGT,WAAW,CAACG,OAAO,CAACM,SAAS;IAC/C,MAAMC,aAAa,GAAGH,SAAS,GAAGD,YAAY;IAC9C,IAAIA,YAAY,GAAGE,YAAY,IAAID,SAAS,GAAGE,SAAS,EAAE;MACxD;MACA;IACF;IACAT,WAAW,CAACG,OAAO,CAACM,SAAS,GAAGC,aAAa,GAAGF,YAAY,GAAG,CAAC,GAAGF,YAAY,GAAG,CAAC;EACrF,CAAC,EAAE,CAACpE,SAAS,CAAC,CAAC;EACf,MAAMyE,SAAS,GAAGnG,KAAK,CAACoG,YAAY,CAAC,CAAC9F,OAAO,EAAEC,OAAO,CAAC,CAAC;EACxD,IAAIiC,UAAU,KAAK,MAAM,EAAE;IACzB2D,SAAS,CAACE,OAAO,CAAC,CAAC;EACrB;EACA,OAAO,aAAa9G,IAAI,CAACiB,gBAAgB,EAAE7C,QAAQ,CAAC;IAClD8D,GAAG,EAAEiE,SAAS;IACd/D,SAAS,EAAE5D,IAAI,CAAC2B,OAAO,CAACE,IAAI,EAAE+B,SAAS,CAAC;IACxClC,UAAU,EAAEA,UAAU;IACtB6G,IAAI,EAAE,YAAY;IAClB,iBAAiB,EAAE3D;EACrB,CAAC,EAAEE,KAAK,EAAE;IACR0D,QAAQ,EAAEJ,SAAS,CAACK,GAAG,CAACnD,IAAI,IAAI;MAC9B,MAAMoD,UAAU,GAAGzG,KAAK,CAACuD,OAAO,CAACF,IAAI,CAAC;MACtC,MAAMqD,UAAU,GAAGD,UAAU,KAAKjD,YAAY;MAC9C,MAAMmD,UAAU,GAAG1E,QAAQ,IAAIkC,cAAc,CAACd,IAAI,CAAC;MACnD,OAAO,aAAa9D,IAAI,CAACZ,WAAW,EAAE;QACpCiI,QAAQ,EAAEF,UAAU;QACpB9E,KAAK,EAAE6E,UAAU;QACjBI,OAAO,EAAEnC,mBAAmB;QAC5BoC,SAAS,EAAE3B,aAAa;QACxBzD,SAAS,EAAEkC,gBAAgB,IAAI6C,UAAU,KAAKhD,WAAW;QACzDxB,QAAQ,EAAE0E,UAAU;QACpBI,QAAQ,EAAEN,UAAU,KAAKhD,WAAW,IAAI,CAACkD,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5DK,OAAO,EAAE1B,eAAe;QACxB2B,MAAM,EAAE1B,cAAc;QACtB,cAAc,EAAEjC,SAAS,KAAKmD,UAAU,GAAG,MAAM,GAAGS,SAAS;QAC7D7G,WAAW,EAAEA,WAAW;QACxBV,KAAK,EAAEA,KAAK;QACZiD,SAAS,EAAEA,SAAS;QACpB2D,QAAQ,EAAEvG,KAAK,CAACmH,MAAM,CAAC9D,IAAI,EAAE,MAAM;MACrC,CAAC,EAAErD,KAAK,CAACmH,MAAM,CAAC9D,IAAI,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF+D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhG,YAAY,CAACiG,SAAS,GAAG;EAC/D;EACA;EACA;EACA;EACA7F,SAAS,EAAE5D,SAAS,CAAC0J,IAAI;EACzB;AACF;AACA;EACE9H,OAAO,EAAE5B,SAAS,CAAC2J,MAAM;EACzB9F,SAAS,EAAE7D,SAAS,CAAC4J,MAAM;EAC3B;AACF;AACA;AACA;EACE5F,YAAY,EAAEhE,SAAS,CAAC2J,MAAM;EAC9B;AACF;AACA;EACExF,QAAQ,EAAEnE,SAAS,CAAC0J,IAAI;EACxB;AACF;AACA;AACA;EACEpH,aAAa,EAAEtC,SAAS,CAAC0J,IAAI;EAC7B;AACF;AACA;AACA;EACEG,qBAAqB,EAAE7J,SAAS,CAAC0J,IAAI;EACrC;AACF;AACA;AACA;EACErH,WAAW,EAAErC,SAAS,CAAC0J,IAAI;EAC3B7E,WAAW,EAAE7E,SAAS,CAAC4J,MAAM;EAC7BpF,QAAQ,EAAExE,SAAS,CAAC0J,IAAI;EACxB;AACF;AACA;AACA;EACEjH,OAAO,EAAEzC,SAAS,CAAC2J,MAAM;EACzB;AACF;AACA;AACA;EACEnH,OAAO,EAAExC,SAAS,CAAC2J,MAAM;EACzB;AACF;AACA;AACA;AACA;EACEvF,QAAQ,EAAEpE,SAAS,CAAC8J,IAAI;EACxBrF,mBAAmB,EAAEzE,SAAS,CAAC8J,IAAI;EACnCvF,WAAW,EAAEvE,SAAS,CAAC8J,IAAI;EAC3B;AACF;AACA;EACEzF,QAAQ,EAAErE,SAAS,CAAC0J,IAAI;EACxB;AACF;AACA;AACA;EACEzF,aAAa,EAAEjE,SAAS,CAAC2J,MAAM;EAC/B;AACF;AACA;AACA;AACA;AACA;EACErF,iBAAiB,EAAEtE,SAAS,CAAC8J,IAAI;EACjC;AACF;AACA;AACA;EACEhF,SAAS,EAAE9E,SAAS,CAAC2J,MAAM;EAC3B;AACF;AACA;AACA;EACE9H,KAAK,EAAE7B,SAAS,CAAC2J,MAAM;EACvB;AACF;AACA;EACEI,EAAE,EAAE/J,SAAS,CAACgK,SAAS,CAAC,CAAChK,SAAS,CAACiK,OAAO,CAACjK,SAAS,CAACgK,SAAS,CAAC,CAAChK,SAAS,CAAC8J,IAAI,EAAE9J,SAAS,CAAC2J,MAAM,EAAE3J,SAAS,CAAC0J,IAAI,CAAC,CAAC,CAAC,EAAE1J,SAAS,CAAC8J,IAAI,EAAE9J,SAAS,CAAC2J,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;EACEhF,QAAQ,EAAE3E,SAAS,CAAC4J,MAAM;EAC1B;AACF;AACA;AACA;EACE9F,KAAK,EAAE9D,SAAS,CAAC2J,MAAM;EACvB;AACF;AACA;AACA;AACA;EACEjF,UAAU,EAAE1E,SAAS,CAACkK,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;EAC5C;AACF;AACA;AACA;EACE3H,WAAW,EAAEvC,SAAS,CAACkK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACrC,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}