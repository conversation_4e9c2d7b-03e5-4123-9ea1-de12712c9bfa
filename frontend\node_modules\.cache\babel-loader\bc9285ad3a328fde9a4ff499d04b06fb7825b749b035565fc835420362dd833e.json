{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Learning Projects\\\\uni-core-business-suite_v3\\\\frontend\\\\src\\\\components\\\\BankPaymentVoucherForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Card, CardContent, Typography, Button, Grid, TextField, FormControl, InputLabel, Select, MenuItem, Alert, Divider, Chip, Autocomplete, FormHelperText } from \"@mui/material\";\nimport { Save as SaveIcon, Check as ApproveIcon, PostAdd as PostIcon, Cancel as CancelIcon } from \"@mui/icons-material\";\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\nimport dayjs from \"dayjs\";\nimport axios from \"../utils/axiosConfig\";\nimport { formatCurrency } from \"../utils/numberUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BankPaymentVoucherForm = ({\n  voucherId,\n  readOnly = false,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [voucher, setVoucher] = useState({\n    voucherType: 'BP',\n    // Bank Payment\n    voucherDate: dayjs(),\n    // Voucher creation date\n    transactionDate: dayjs(),\n    // Actual transaction date\n    amount: '',\n    fromAccountId: '',\n    // Bank account (credit)\n    toAccountId: '',\n    // Debit account (vendor, expense, etc.)\n    relatedPartyType: '',\n    relatedPartyId: '',\n    relatedPartyName: '',\n    paymentMethod: 'Bank Transfer',\n    chequeNumber: '',\n    chequeDate: null,\n    bankReference: '',\n    narration: '',\n    description: '',\n    status: 'Draft',\n    referenceDocuments: []\n  });\n  const [accounts, setAccounts] = useState([]);\n  const [bankAccounts, setBankAccounts] = useState([]);\n  const [vendors, setVendors] = useState([]);\n  const [purchaseInvoices, setPurchaseInvoices] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [selectedVendor, setSelectedVendor] = useState(null);\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\n  const paymentMethods = ['Bank Transfer', 'Cheque', 'Online Transfer', 'Card', 'Other'];\n  useEffect(() => {\n    fetchInitialData();\n    if (voucherId) {\n      fetchVoucher();\n    }\n  }, [voucherId]);\n\n  // Fetch vendor's purchase invoices when vendor is selected\n  useEffect(() => {\n    if (selectedVendor) {\n      fetchVendorInvoices(selectedVendor._id);\n    } else {\n      setPurchaseInvoices([]);\n      setSelectedInvoice(null);\n    }\n  }, [selectedVendor]);\n\n  // Update amount when invoice is selected\n  useEffect(() => {\n    if (selectedInvoice) {\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\n\n      // Create a detailed narration\n      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber || 'Unknown'}`;\n\n      // If there are returns, add details\n      if (selectedInvoice.hasReturns && selectedInvoice.returnDetails) {\n        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\n        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount || 0)}, Returns: ${formatCurrency(selectedInvoice.returnAmount || 0)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount || 0)})`;\n      }\n      setVoucher(prev => ({\n        ...prev,\n        amount: remainingAmount.toFixed(2),\n        narration\n      }));\n    }\n  }, [selectedInvoice]);\n\n  // Add error handling for ResizeObserver\n  useEffect(() => {\n    // Suppress ResizeObserver loop error\n    const originalError = window.console.error;\n    window.console.error = (...args) => {\n      var _args$, _args$$includes;\n      if ((_args$ = args[0]) !== null && _args$ !== void 0 && (_args$$includes = _args$.includes) !== null && _args$$includes !== void 0 && _args$$includes.call(_args$, 'ResizeObserver loop')) {\n        // Ignore ResizeObserver loop errors\n        return;\n      }\n      originalError(...args);\n    };\n    return () => {\n      window.console.error = originalError;\n    };\n  }, []);\n  const fetchInitialData = async () => {\n    try {\n      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([axios.get(\"/api/accounts?active=true\"), axios.get(\"/api/vendors\"), axios.get(\"/api/bank-accounts\")]);\n      setAccounts(accountsRes.data);\n      setVendors(vendorsRes.data);\n\n      // Map bank accounts to their corresponding chart of accounts entries\n      const bankAccs = bankAccountsRes.data;\n      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);\n\n      // Get bank account details from chart of accounts\n      if (bankAccountIds.length > 0) {\n        const bankAccountsDetails = accountsRes.data.filter(acc => bankAccountIds.includes(acc._id));\n        setBankAccounts(bankAccountsDetails);\n      }\n    } catch (error) {\n      console.error(\"Error fetching initial data:\", error);\n    }\n  };\n  const fetchVoucher = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);\n      const voucherData = response.data;\n      setVoucher({\n        ...voucherData,\n        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt),\n        transactionDate: dayjs(voucherData.transactionDate),\n        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,\n        fromAccountId: voucherData.fromAccountId._id,\n        toAccountId: voucherData.toAccountId._id\n      });\n\n      // If this is a vendor payment, fetch the vendor and invoice details\n      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {\n        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);\n        setSelectedVendor(vendorRes.data);\n\n        // If there's a reference to a purchase invoice\n        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {\n          const invoiceRef = voucherData.referenceDocuments.find(doc => doc.documentType === 'PurchaseInvoice');\n          if (invoiceRef && invoiceRef.documentId) {\n            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);\n            setSelectedInvoice(invoiceRes.data);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching voucher:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchVendorInvoices = async vendorId => {\n    try {\n      console.log(`Fetching invoices for vendor: ${vendorId}`);\n      // Fetch unpaid or partially paid invoices for this vendor\n      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);\n      console.log('Vendor invoices response:', response.data);\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        setPurchaseInvoices(response.data);\n      } else {\n        console.log('No unpaid invoices found for this vendor');\n        setPurchaseInvoices([]);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error fetching vendor invoices:\", error);\n      alert(`Failed to fetch vendor invoices: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message}`);\n      setPurchaseInvoices([]);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setVoucher(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when field is updated\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n  const handleVendorChange = vendor => {\n    setSelectedVendor(vendor);\n    setSelectedInvoice(null); // Clear selected invoice when vendor changes\n\n    if (vendor) {\n      // Update voucher with vendor details\n      setVoucher(prev => ({\n        ...prev,\n        relatedPartyType: 'Vendor',\n        relatedPartyId: vendor._id,\n        relatedPartyName: vendor.name,\n        toAccountId: vendor.accountId || '',\n        // Set vendor's account as debit account\n        amount: '',\n        // Clear amount when vendor changes\n        narration: '',\n        // Clear narration when vendor changes\n        referenceDocuments: [] // Clear reference documents\n      }));\n    } else {\n      // Clear vendor-related fields\n      setVoucher(prev => ({\n        ...prev,\n        relatedPartyType: '',\n        relatedPartyId: '',\n        relatedPartyName: '',\n        toAccountId: '',\n        amount: '',\n        narration: '',\n        referenceDocuments: []\n      }));\n    }\n  };\n  const handleInvoiceChange = invoice => {\n    setSelectedInvoice(invoice);\n    if (invoice) {\n      console.log('Selected invoice:', invoice);\n\n      // Use the adjusted remaining amount that accounts for returns\n      const remainingAmount = invoice.remainingAmount;\n      console.log(`Using adjusted remaining amount: ${remainingAmount}`);\n\n      // Create a detailed narration\n      let narration = `Payment against invoice ${invoice.invoiceNumber}`;\n\n      // If there are returns, add details\n      if (invoice.hasReturns) {\n        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\n        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;\n      }\n\n      // Update amount field with remaining amount\n      setVoucher(prev => ({\n        ...prev,\n        amount: remainingAmount.toString(),\n        narration\n      }));\n\n      // Update reference documents\n      const referenceDoc = {\n        documentType: 'PurchaseInvoice',\n        documentId: invoice._id,\n        documentNumber: invoice.invoiceNumber,\n        allocatedAmount: remainingAmount,\n        originalAmount: invoice.originalAmount,\n        returnAmount: invoice.returnAmount,\n        adjustedAmount: invoice.adjustedAmount\n      };\n      setVoucher(prev => ({\n        ...prev,\n        referenceDocuments: [referenceDoc]\n      }));\n    } else {\n      setVoucher(prev => ({\n        ...prev,\n        referenceDocuments: []\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!voucher.fromAccountId) newErrors.fromAccountId = \"Bank is required\";\n    if (!voucher.toAccountId) newErrors.toAccountId = \"Title (debit account) is required\";\n    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {\n      newErrors.amount = \"Valid amount is required\";\n    }\n    if (!voucher.voucherDate) newErrors.voucherDate = \"Voucher date is required\";\n    if (!voucher.transactionDate) newErrors.transactionDate = \"Transaction date is required\";\n    if (!voucher.narration) newErrors.narration = \"Narration is required\";\n\n    // Vendor-invoice linkage validation\n    if (selectedVendor && selectedInvoice) {\n      // Ensure the selected invoice belongs to the selected vendor\n      if (selectedInvoice.vendorId !== selectedVendor._id) {\n        newErrors.invoice = \"Selected invoice does not belong to the selected vendor\";\n      }\n\n      // Ensure payment amount doesn't exceed remaining amount\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\n      const paymentAmount = parseFloat(voucher.amount) || 0;\n      if (paymentAmount > remainingAmount) {\n        newErrors.amount = `Payment amount cannot exceed remaining amount of ${remainingAmount.toFixed(2)}`;\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSave = async () => {\n    if (!validateForm()) return;\n    try {\n      setLoading(true);\n      const voucherData = {\n        ...voucher,\n        voucherDate: voucher.voucherDate.toISOString(),\n        transactionDate: voucher.transactionDate.toISOString(),\n        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,\n        amount: parseFloat(voucher.amount)\n      };\n      let response;\n      if (voucherId) {\n        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);\n      } else {\n        response = await axios.post(\"/api/payment-vouchers\", voucherData);\n      }\n      if (onSave) onSave(response.data.voucher);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error(\"Error saving voucher:\", error);\n      alert(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Error saving bank payment voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApprove = async () => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\n      if (onSave) onSave();\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error(\"Error approving voucher:\", error);\n      alert(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || \"Error approving voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePost = async () => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\n      if (onSave) onSave();\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error(\"Error posting voucher:\", error);\n      alert(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || \"Error posting voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');\n  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';\n  const canPost = !readOnly && voucherId && voucher.status === 'Approved';\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n      dateAdapter: AdapterDayjs,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Bank Payment Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this), voucherId && /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Voucher No.\",\n                value: voucher.voucherNumber || '',\n                margin: \"normal\",\n                InputProps: {\n                  readOnly: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 408,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                label: \"Voucher Date\",\n                value: voucher.voucherDate,\n                onChange: newValue => handleInputChange('voucherDate', newValue),\n                format: \"DD/MMM/YYYY\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  fullWidth: true,\n                  margin: \"normal\",\n                  error: !!errors.voucherDate,\n                  helperText: errors.voucherDate,\n                  InputProps: {\n                    ...params.InputProps,\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 21\n                }, this),\n                readOnly: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                label: \"Transaction Date\",\n                value: voucher.transactionDate,\n                onChange: newValue => handleInputChange('transactionDate', newValue),\n                format: \"DD/MMM/YYYY\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  fullWidth: true,\n                  margin: \"normal\",\n                  error: !!errors.transactionDate,\n                  helperText: errors.transactionDate,\n                  InputProps: {\n                    ...params.InputProps,\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this),\n                readOnly: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Transaction ID\",\n                value: voucher.bankReference || '',\n                onChange: e => handleInputChange('bankReference', e.target.value),\n                margin: \"normal\",\n                InputProps: {\n                  readOnly: !canEdit\n                },\n                helperText: \"Optional field for internal reference or payment ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.fromAccountId,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Bank\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.fromAccountId,\n                  onChange: e => handleInputChange('fromAccountId', e.target.value),\n                  label: \"Bank\",\n                  disabled: !canEdit,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Select Bank\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 485,\n                    columnNumber: 21\n                  }, this), bankAccounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: account._id,\n                    children: account.accountName\n                  }, account._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 489,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this), errors.fromAccountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.fromAccountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 495,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Payment Method\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.paymentMethod,\n                  onChange: e => handleInputChange('paymentMethod', e.target.value),\n                  label: \"Payment Method\",\n                  disabled: !canEdit,\n                  children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: method,\n                    children: method\n                  }, method, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 509,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this), voucher.paymentMethod === 'Cheque' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Cheque Number\",\n                  value: voucher.chequeNumber || '',\n                  onChange: e => handleInputChange('chequeNumber', e.target.value),\n                  margin: \"normal\",\n                  InputProps: {\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 519,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                  label: \"Cheque Date\",\n                  value: voucher.chequeDate,\n                  onChange: newValue => handleInputChange('chequeDate', newValue),\n                  renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                    ...params,\n                    fullWidth: true,\n                    margin: \"normal\",\n                    InputProps: {\n                      ...params.InputProps,\n                      readOnly: !canEdit\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 25\n                  }, this),\n                  readOnly: !canEdit\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 528,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Payment Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 555,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: vendors,\n                getOptionLabel: vendor => vendor.name || '',\n                value: selectedVendor,\n                onChange: (event, newValue) => handleVendorChange(newValue),\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Vendor\",\n                  margin: \"normal\",\n                  fullWidth: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 21\n                }, this),\n                disabled: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 560,\n                columnNumber: 17\n              }, this), selectedVendor && /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: purchaseInvoices,\n                getOptionLabel: invoice => {\n                  if (!invoice) return '';\n                  const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;\n                  const invoiceDate = new Date(invoice.invoiceDate).toLocaleDateString('en-GB', {\n                    day: '2-digit',\n                    month: 'short',\n                    year: 'numeric'\n                  });\n\n                  // If there are returns, show the adjusted amount\n                  if (invoice.hasReturns) {\n                    return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} (Returns Applied)`;\n                  }\n\n                  // Otherwise just show the remaining amount\n                  return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} outstanding`;\n                },\n                value: selectedInvoice,\n                onChange: (event, newValue) => handleInvoiceChange(newValue),\n                renderOption: (props, invoice) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%',\n                      maxWidth: '100%',\n                      overflow: 'hidden'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"medium\",\n                      noWrap: true,\n                      children: [invoice.invoiceNumber, \" - \", formatCurrency(invoice.remainingAmount), \" outstanding\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 603,\n                      columnNumber: 27\n                    }, this), invoice.hasReturns && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        display: \"block\",\n                        noWrap: true,\n                        children: [\"Original: \", formatCurrency(invoice.originalAmount), \" | Returns: \", formatCurrency(invoice.returnAmount), \" | Adjusted: \", formatCurrency(invoice.adjustedAmount)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 608,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"primary\",\n                        display: \"block\",\n                        noWrap: true,\n                        children: [invoice.returnDetails.length, \" return(s) applied -\", invoice.returnDetails.map((ret, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [ret.returnNumber, \" (\", ret.status, \")\", idx < invoice.returnDetails.length - 1 ? ', ' : '']\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 616,\n                          columnNumber: 35\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 613,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 23\n                }, this),\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Purchase Invoice\",\n                  margin: \"normal\",\n                  fullWidth: true,\n                  error: purchaseInvoices.length === 0 || !!errors.invoice,\n                  helperText: errors.invoice || (purchaseInvoices.length === 0 ? \"No unpaid invoices found for this vendor\" : \"Optional: Select invoice for payment tracking. Partial payments are supported.\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 628,\n                  columnNumber: 23\n                }, this),\n                disabled: !canEdit || purchaseInvoices.length === 0,\n                noOptionsText: \"No unpaid invoices found\",\n                ListboxProps: {\n                  style: {\n                    maxHeight: '200px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 19\n              }, this), !selectedVendor && /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.toAccountId,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 656,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.toAccountId,\n                  onChange: e => handleInputChange('toAccountId', e.target.value),\n                  label: \"Title\",\n                  disabled: !canEdit,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Select Account\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 664,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 663,\n                    columnNumber: 23\n                  }, this), accounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: account._id,\n                    children: [account.accountCode, \" - \", account.accountName]\n                  }, account._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 657,\n                  columnNumber: 21\n                }, this), errors.toAccountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.toAccountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Amount\",\n                type: \"number\",\n                value: voucher.amount,\n                onChange: e => handleInputChange('amount', e.target.value),\n                margin: \"normal\",\n                error: !!errors.amount,\n                helperText: errors.amount,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Narration\",\n                value: voucher.narration,\n                onChange: e => handleInputChange('narration', e.target.value),\n                margin: \"normal\",\n                multiline: true,\n                rows: 2,\n                error: !!errors.narration,\n                helperText: errors.narration,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Description\",\n                value: voucher.description || '',\n                onChange: e => handleInputChange('description', e.target.value),\n                margin: \"normal\",\n                multiline: true,\n                rows: 2,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 554,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 553,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              gap: 2,\n              mt: 2\n            },\n            children: [onCancel && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 727,\n                columnNumber: 30\n              }, this),\n              onClick: onCancel,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 17\n            }, this), canEdit && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 30\n              }, this),\n              onClick: handleSave,\n              disabled: loading,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this), canApprove && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"warning\",\n              startIcon: /*#__PURE__*/_jsxDEV(ApproveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 30\n              }, this),\n              onClick: handleApprove,\n              disabled: loading,\n              children: \"Approve\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 17\n            }, this), canPost && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              startIcon: /*#__PURE__*/_jsxDEV(PostIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 30\n              }, this),\n              onClick: handlePost,\n              disabled: loading,\n              children: \"Post to Ledger\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 721,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 395,\n    columnNumber: 5\n  }, this);\n};\n_s(BankPaymentVoucherForm, \"buRCZTx5Ubo79tZQuzwSeBiGLKU=\");\n_c = BankPaymentVoucherForm;\nexport default BankPaymentVoucherForm;\nvar _c;\n$RefreshReg$(_c, \"BankPaymentVoucherForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Grid", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Divider", "Chip", "Autocomplete", "FormHelperText", "Save", "SaveIcon", "Check", "ApproveIcon", "PostAdd", "PostIcon", "Cancel", "CancelIcon", "DatePicker", "LocalizationProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayjs", "axios", "formatCurrency", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BankPaymentVoucherForm", "voucherId", "readOnly", "onSave", "onCancel", "_s", "voucher", "setVoucher", "voucherType", "voucherDate", "transactionDate", "amount", "fromAccountId", "toAccountId", "relatedPartyType", "relatedPartyId", "relatedPartyName", "paymentMethod", "chequeNumber", "chequeDate", "bankReference", "narration", "description", "status", "referenceDocuments", "accounts", "setAccounts", "bankAccounts", "setBankAccounts", "vendors", "setVendors", "purchaseInvoices", "setPurchaseInvoices", "loading", "setLoading", "errors", "setErrors", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVendor", "selectedInvoice", "setSelectedInvoice", "paymentMethods", "fetchInitialData", "fetchVoucher", "fetchVendorInvoices", "_id", "remainingAmount", "invoiceNumber", "hasReturns", "returnDetails", "returnStatuses", "map", "ret", "returnNumber", "join", "originalAmount", "returnAmount", "adjustedAmount", "prev", "toFixed", "originalError", "window", "console", "error", "args", "_args$", "_args$$includes", "includes", "call", "accountsRes", "vendorsRes", "bankAccountsRes", "Promise", "all", "get", "data", "bankAccs", "bankAccountIds", "bank", "accountId", "filter", "id", "length", "bankAccountsDetails", "acc", "response", "voucherData", "createdAt", "vendorRes", "invoiceRef", "find", "doc", "documentType", "documentId", "invoiceRes", "vendorId", "log", "Array", "isArray", "_error$response", "_error$response$data", "alert", "message", "handleInputChange", "field", "value", "handleVendorChange", "vendor", "name", "handleInvoiceChange", "invoice", "toString", "referenceDoc", "documentNumber", "allocatedAmount", "validateForm", "newErrors", "isNaN", "parseFloat", "paymentAmount", "Object", "keys", "handleSave", "toISOString", "put", "post", "_error$response2", "_error$response2$data", "handleApprove", "_error$response3", "_error$response3$data", "handlePost", "_error$response4", "_error$response4$data", "canEdit", "canApprove", "canPost", "children", "dateAdapter", "container", "spacing", "item", "xs", "md", "variant", "sx", "mb", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "label", "voucherNumber", "margin", "InputProps", "onChange", "newValue", "format", "renderInput", "params", "helperText", "e", "target", "disabled", "account", "accountName", "method", "options", "getOptionLabel", "event", "invoiceLabel", "invoiceDate", "Date", "toLocaleDateString", "day", "month", "year", "renderOption", "props", "width", "max<PERSON><PERSON><PERSON>", "overflow", "fontWeight", "noWrap", "color", "display", "idx", "noOptionsText", "ListboxProps", "style", "maxHeight", "accountCode", "type", "multiline", "rows", "justifyContent", "gap", "mt", "startIcon", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/components/BankPaymentVoucherForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  Button,\r\n  Grid,\r\n  TextField,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Alert,\r\n  Divider,\r\n  Chip,\r\n  Autocomplete,\r\n  FormHelperText\r\n} from \"@mui/material\";\r\nimport {\r\n  Save as SaveIcon,\r\n  Check as ApproveIcon,\r\n  PostAdd as PostIcon,\r\n  Cancel as CancelIcon\r\n} from \"@mui/icons-material\";\r\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\r\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\r\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\r\nimport dayjs from \"dayjs\";\r\nimport axios from \"../utils/axiosConfig\";\r\nimport { formatCurrency } from \"../utils/numberUtils\";\r\n\r\nconst BankPaymentVoucherForm = ({ voucherId, readOnly = false, onSave, onCancel }) => {\r\n  const [voucher, setVoucher] = useState({\r\n    voucherType: 'BP', // Bank Payment\r\n    voucherDate: dayjs(), // Voucher creation date\r\n    transactionDate: dayjs(), // Actual transaction date\r\n    amount: '',\r\n    fromAccountId: '', // Bank account (credit)\r\n    toAccountId: '', // Debit account (vendor, expense, etc.)\r\n    relatedPartyType: '',\r\n    relatedPartyId: '',\r\n    relatedPartyName: '',\r\n    paymentMethod: 'Bank Transfer',\r\n    chequeNumber: '',\r\n    chequeDate: null,\r\n    bankReference: '',\r\n    narration: '',\r\n    description: '',\r\n    status: 'Draft',\r\n    referenceDocuments: []\r\n  });\r\n\r\n  const [accounts, setAccounts] = useState([]);\r\n  const [bankAccounts, setBankAccounts] = useState([]);\r\n  const [vendors, setVendors] = useState([]);\r\n  const [purchaseInvoices, setPurchaseInvoices] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [errors, setErrors] = useState({});\r\n  const [selectedVendor, setSelectedVendor] = useState(null);\r\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\r\n\r\n  const paymentMethods = [\r\n    'Bank Transfer',\r\n    'Cheque',\r\n    'Online Transfer',\r\n    'Card',\r\n    'Other'\r\n  ];\r\n\r\n  useEffect(() => {\r\n    fetchInitialData();\r\n    if (voucherId) {\r\n      fetchVoucher();\r\n    }\r\n  }, [voucherId]);\r\n\r\n  // Fetch vendor's purchase invoices when vendor is selected\r\n  useEffect(() => {\r\n    if (selectedVendor) {\r\n      fetchVendorInvoices(selectedVendor._id);\r\n    } else {\r\n      setPurchaseInvoices([]);\r\n      setSelectedInvoice(null);\r\n    }\r\n  }, [selectedVendor]);\r\n\r\n  // Update amount when invoice is selected\r\n  useEffect(() => {\r\n    if (selectedInvoice) {\r\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\r\n\r\n      // Create a detailed narration\r\n      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber || 'Unknown'}`;\r\n\r\n      // If there are returns, add details\r\n      if (selectedInvoice.hasReturns && selectedInvoice.returnDetails) {\r\n        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\r\n        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount || 0)}, Returns: ${formatCurrency(selectedInvoice.returnAmount || 0)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount || 0)})`;\r\n      }\r\n\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        amount: remainingAmount.toFixed(2),\r\n        narration\r\n      }));\r\n    }\r\n  }, [selectedInvoice]);\r\n\r\n  // Add error handling for ResizeObserver\r\n  useEffect(() => {\r\n    // Suppress ResizeObserver loop error\r\n    const originalError = window.console.error;\r\n    window.console.error = (...args) => {\r\n      if (args[0]?.includes?.('ResizeObserver loop')) {\r\n        // Ignore ResizeObserver loop errors\r\n        return;\r\n      }\r\n      originalError(...args);\r\n    };\r\n\r\n    return () => {\r\n      window.console.error = originalError;\r\n    };\r\n  }, []);\r\n\r\n  const fetchInitialData = async () => {\r\n    try {\r\n      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([\r\n        axios.get(\"/api/accounts?active=true\"),\r\n        axios.get(\"/api/vendors\"),\r\n        axios.get(\"/api/bank-accounts\")\r\n      ]);\r\n\r\n      setAccounts(accountsRes.data);\r\n      setVendors(vendorsRes.data);\r\n      \r\n      // Map bank accounts to their corresponding chart of accounts entries\r\n      const bankAccs = bankAccountsRes.data;\r\n      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);\r\n      \r\n      // Get bank account details from chart of accounts\r\n      if (bankAccountIds.length > 0) {\r\n        const bankAccountsDetails = accountsRes.data.filter(acc => \r\n          bankAccountIds.includes(acc._id)\r\n        );\r\n        setBankAccounts(bankAccountsDetails);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching initial data:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchVoucher = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);\r\n      const voucherData = response.data;\r\n      \r\n      setVoucher({\r\n        ...voucherData,\r\n        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt),\r\n        transactionDate: dayjs(voucherData.transactionDate),\r\n        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,\r\n        fromAccountId: voucherData.fromAccountId._id,\r\n        toAccountId: voucherData.toAccountId._id\r\n      });\r\n\r\n      // If this is a vendor payment, fetch the vendor and invoice details\r\n      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {\r\n        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);\r\n        setSelectedVendor(vendorRes.data);\r\n        \r\n        // If there's a reference to a purchase invoice\r\n        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {\r\n          const invoiceRef = voucherData.referenceDocuments.find(doc => \r\n            doc.documentType === 'PurchaseInvoice'\r\n          );\r\n          \r\n          if (invoiceRef && invoiceRef.documentId) {\r\n            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);\r\n            setSelectedInvoice(invoiceRes.data);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching voucher:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchVendorInvoices = async (vendorId) => {\r\n    try {\r\n      console.log(`Fetching invoices for vendor: ${vendorId}`);\r\n      // Fetch unpaid or partially paid invoices for this vendor\r\n      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);\r\n      console.log('Vendor invoices response:', response.data);\r\n      \r\n      if (Array.isArray(response.data) && response.data.length > 0) {\r\n        setPurchaseInvoices(response.data);\r\n      } else {\r\n        console.log('No unpaid invoices found for this vendor');\r\n        setPurchaseInvoices([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching vendor invoices:\", error);\r\n      alert(`Failed to fetch vendor invoices: ${error.response?.data?.message || error.message}`);\r\n      setPurchaseInvoices([]);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setVoucher(prev => ({ ...prev, [field]: value }));\r\n    \r\n    // Clear error when field is updated\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: null }));\r\n    }\r\n  };\r\n\r\n  const handleVendorChange = (vendor) => {\r\n    setSelectedVendor(vendor);\r\n    setSelectedInvoice(null); // Clear selected invoice when vendor changes\r\n\r\n    if (vendor) {\r\n      // Update voucher with vendor details\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        relatedPartyType: 'Vendor',\r\n        relatedPartyId: vendor._id,\r\n        relatedPartyName: vendor.name,\r\n        toAccountId: vendor.accountId || '', // Set vendor's account as debit account\r\n        amount: '', // Clear amount when vendor changes\r\n        narration: '', // Clear narration when vendor changes\r\n        referenceDocuments: [] // Clear reference documents\r\n      }));\r\n    } else {\r\n      // Clear vendor-related fields\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        relatedPartyType: '',\r\n        relatedPartyId: '',\r\n        relatedPartyName: '',\r\n        toAccountId: '',\r\n        amount: '',\r\n        narration: '',\r\n        referenceDocuments: []\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleInvoiceChange = (invoice) => {\r\n    setSelectedInvoice(invoice);\r\n    \r\n    if (invoice) {\r\n      console.log('Selected invoice:', invoice);\r\n      \r\n      // Use the adjusted remaining amount that accounts for returns\r\n      const remainingAmount = invoice.remainingAmount;\r\n      \r\n      console.log(`Using adjusted remaining amount: ${remainingAmount}`);\r\n      \r\n      // Create a detailed narration\r\n      let narration = `Payment against invoice ${invoice.invoiceNumber}`;\r\n      \r\n      // If there are returns, add details\r\n      if (invoice.hasReturns) {\r\n        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\r\n        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;\r\n      }\r\n      \r\n      // Update amount field with remaining amount\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        amount: remainingAmount.toString(),\r\n        narration\r\n      }));\r\n      \r\n      // Update reference documents\r\n      const referenceDoc = {\r\n        documentType: 'PurchaseInvoice',\r\n        documentId: invoice._id,\r\n        documentNumber: invoice.invoiceNumber,\r\n        allocatedAmount: remainingAmount,\r\n        originalAmount: invoice.originalAmount,\r\n        returnAmount: invoice.returnAmount,\r\n        adjustedAmount: invoice.adjustedAmount\r\n      };\r\n      \r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        referenceDocuments: [referenceDoc]\r\n      }));\r\n    } else {\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        referenceDocuments: []\r\n      }));\r\n    }\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n\r\n    if (!voucher.fromAccountId) newErrors.fromAccountId = \"Bank is required\";\r\n    if (!voucher.toAccountId) newErrors.toAccountId = \"Title (debit account) is required\";\r\n    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {\r\n      newErrors.amount = \"Valid amount is required\";\r\n    }\r\n    if (!voucher.voucherDate) newErrors.voucherDate = \"Voucher date is required\";\r\n    if (!voucher.transactionDate) newErrors.transactionDate = \"Transaction date is required\";\r\n    if (!voucher.narration) newErrors.narration = \"Narration is required\";\r\n\r\n    // Vendor-invoice linkage validation\r\n    if (selectedVendor && selectedInvoice) {\r\n      // Ensure the selected invoice belongs to the selected vendor\r\n      if (selectedInvoice.vendorId !== selectedVendor._id) {\r\n        newErrors.invoice = \"Selected invoice does not belong to the selected vendor\";\r\n      }\r\n\r\n      // Ensure payment amount doesn't exceed remaining amount\r\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\r\n      const paymentAmount = parseFloat(voucher.amount) || 0;\r\n      if (paymentAmount > remainingAmount) {\r\n        newErrors.amount = `Payment amount cannot exceed remaining amount of ${remainingAmount.toFixed(2)}`;\r\n      }\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!validateForm()) return;\r\n    \r\n    try {\r\n      setLoading(true);\r\n      \r\n      const voucherData = {\r\n        ...voucher,\r\n        voucherDate: voucher.voucherDate.toISOString(),\r\n        transactionDate: voucher.transactionDate.toISOString(),\r\n        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,\r\n        amount: parseFloat(voucher.amount)\r\n      };\r\n\r\n      let response;\r\n      if (voucherId) {\r\n        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);\r\n      } else {\r\n        response = await axios.post(\"/api/payment-vouchers\", voucherData);\r\n      }\r\n\r\n      if (onSave) onSave(response.data.voucher);\r\n    } catch (error) {\r\n      console.error(\"Error saving voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error saving bank payment voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleApprove = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\r\n      if (onSave) onSave();\r\n    } catch (error) {\r\n      console.error(\"Error approving voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error approving voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePost = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\r\n      if (onSave) onSave();\r\n    } catch (error) {\r\n      console.error(\"Error posting voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error posting voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');\r\n  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';\r\n  const canPost = !readOnly && voucherId && voucher.status === 'Approved';\r\n\r\n  return (\r\n    <Box>\r\n      <LocalizationProvider dateAdapter={AdapterDayjs}>\r\n        <Grid container spacing={3}>\r\n          {/* Left Column */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\r\n              <CardContent>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Bank Payment Details\r\n                </Typography>\r\n                \r\n                {/* Voucher Number (shown only for existing vouchers) */}\r\n                {voucherId && (\r\n                  <TextField\r\n                    fullWidth\r\n                    label=\"Voucher No.\"\r\n                    value={voucher.voucherNumber || ''}\r\n                    margin=\"normal\"\r\n                    InputProps={{ readOnly: true }}\r\n                  />\r\n                )}\r\n\r\n                {/* Voucher Date */}\r\n                <DatePicker\r\n                  label=\"Voucher Date\"\r\n                  value={voucher.voucherDate}\r\n                  onChange={(newValue) => handleInputChange('voucherDate', newValue)}\r\n                  format=\"DD/MMM/YYYY\"\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      fullWidth\r\n                      margin=\"normal\"\r\n                      error={!!errors.voucherDate}\r\n                      helperText={errors.voucherDate}\r\n                      InputProps={{\r\n                        ...params.InputProps,\r\n                        readOnly: !canEdit\r\n                      }}\r\n                    />\r\n                  )}\r\n                  readOnly={!canEdit}\r\n                />\r\n\r\n                {/* Transaction Date */}\r\n                <DatePicker\r\n                  label=\"Transaction Date\"\r\n                  value={voucher.transactionDate}\r\n                  onChange={(newValue) => handleInputChange('transactionDate', newValue)}\r\n                  format=\"DD/MMM/YYYY\"\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      fullWidth\r\n                      margin=\"normal\"\r\n                      error={!!errors.transactionDate}\r\n                      helperText={errors.transactionDate}\r\n                      InputProps={{\r\n                        ...params.InputProps,\r\n                        readOnly: !canEdit\r\n                      }}\r\n                    />\r\n                  )}\r\n                  readOnly={!canEdit}\r\n                />\r\n                \r\n                {/* Transaction ID / Reference */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Transaction ID\"\r\n                  value={voucher.bankReference || ''}\r\n                  onChange={(e) => handleInputChange('bankReference', e.target.value)}\r\n                  margin=\"normal\"\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                  helperText=\"Optional field for internal reference or payment ID\"\r\n                />\r\n\r\n                {/* Bank Account (Credit Account) */}\r\n                <FormControl\r\n                  fullWidth\r\n                  margin=\"normal\"\r\n                  error={!!errors.fromAccountId}\r\n                >\r\n                  <InputLabel>Bank</InputLabel>\r\n                  <Select\r\n                    value={voucher.fromAccountId}\r\n                    onChange={(e) => handleInputChange('fromAccountId', e.target.value)}\r\n                    label=\"Bank\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    <MenuItem value=\"\">\r\n                      <em>Select Bank</em>\r\n                    </MenuItem>\r\n                    {bankAccounts.map((account) => (\r\n                      <MenuItem key={account._id} value={account._id}>\r\n                        {account.accountName}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                  {errors.fromAccountId && (\r\n                    <FormHelperText>{errors.fromAccountId}</FormHelperText>\r\n                  )}\r\n                </FormControl>\r\n                \r\n                {/* Payment Method */}\r\n                <FormControl fullWidth margin=\"normal\">\r\n                  <InputLabel>Payment Method</InputLabel>\r\n                  <Select\r\n                    value={voucher.paymentMethod}\r\n                    onChange={(e) => handleInputChange('paymentMethod', e.target.value)}\r\n                    label=\"Payment Method\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    {paymentMethods.map((method) => (\r\n                      <MenuItem key={method} value={method}>\r\n                        {method}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n                \r\n                {/* Cheque Details (shown only for cheque payments) */}\r\n                {voucher.paymentMethod === 'Cheque' && (\r\n                  <>\r\n                    <TextField\r\n                      fullWidth\r\n                      label=\"Cheque Number\"\r\n                      value={voucher.chequeNumber || ''}\r\n                      onChange={(e) => handleInputChange('chequeNumber', e.target.value)}\r\n                      margin=\"normal\"\r\n                      InputProps={{ readOnly: !canEdit }}\r\n                    />\r\n                    \r\n                    <DatePicker\r\n                      label=\"Cheque Date\"\r\n                      value={voucher.chequeDate}\r\n                      onChange={(newValue) => handleInputChange('chequeDate', newValue)}\r\n                      renderInput={(params) => (\r\n                        <TextField\r\n                          {...params}\r\n                          fullWidth\r\n                          margin=\"normal\"\r\n                          InputProps={{\r\n                            ...params.InputProps,\r\n                            readOnly: !canEdit\r\n                          }}\r\n                        />\r\n                      )}\r\n                      readOnly={!canEdit}\r\n                    />\r\n                  </>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          \r\n          {/* Right Column */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\r\n              <CardContent>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Payment Details\r\n                </Typography>\r\n                \r\n                {/* Vendor Selection */}\r\n                <Autocomplete\r\n                  options={vendors}\r\n                  getOptionLabel={(vendor) => vendor.name || ''}\r\n                  value={selectedVendor}\r\n                  onChange={(event, newValue) => handleVendorChange(newValue)}\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      label=\"Vendor\"\r\n                      margin=\"normal\"\r\n                      fullWidth\r\n                    />\r\n                  )}\r\n                  disabled={!canEdit}\r\n                />\r\n                \r\n                {/* Purchase Invoice Selection (shown only if vendor is selected) */}\r\n                {selectedVendor && (\r\n                  <Autocomplete\r\n                    options={purchaseInvoices}\r\n                    getOptionLabel={(invoice) => {\r\n                      if (!invoice) return '';\r\n\r\n                      const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;\r\n                      const invoiceDate = new Date(invoice.invoiceDate).toLocaleDateString('en-GB', {\r\n                        day: '2-digit',\r\n                        month: 'short',\r\n                        year: 'numeric'\r\n                      });\r\n\r\n                      // If there are returns, show the adjusted amount\r\n                      if (invoice.hasReturns) {\r\n                        return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} (Returns Applied)`;\r\n                      }\r\n\r\n                      // Otherwise just show the remaining amount\r\n                      return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} outstanding`;\r\n                    }}\r\n                    value={selectedInvoice}\r\n                    onChange={(event, newValue) => handleInvoiceChange(newValue)}\r\n                    renderOption={(props, invoice) => (\r\n                      <li {...props}>\r\n                        <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>\r\n                          <Typography variant=\"body1\" fontWeight=\"medium\" noWrap>\r\n                            {invoice.invoiceNumber} - {formatCurrency(invoice.remainingAmount)} outstanding\r\n                          </Typography>\r\n                          {invoice.hasReturns && (\r\n                            <>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" noWrap>\r\n                                Original: {formatCurrency(invoice.originalAmount)} | \r\n                                Returns: {formatCurrency(invoice.returnAmount)} | \r\n                                Adjusted: {formatCurrency(invoice.adjustedAmount)}\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"primary\" display=\"block\" noWrap>\r\n                                {invoice.returnDetails.length} return(s) applied - \r\n                                {invoice.returnDetails.map((ret, idx) => (\r\n                                  <span key={idx}>\r\n                                    {ret.returnNumber} ({ret.status})\r\n                                    {idx < invoice.returnDetails.length - 1 ? ', ' : ''}\r\n                                  </span>\r\n                                ))}\r\n                              </Typography>\r\n                            </>\r\n                          )}\r\n                        </Box>\r\n                      </li>\r\n                    )}\r\n                    renderInput={(params) => (\r\n                      <TextField\r\n                        {...params}\r\n                        label=\"Purchase Invoice\"\r\n                        margin=\"normal\"\r\n                        fullWidth\r\n                        error={purchaseInvoices.length === 0 || !!errors.invoice}\r\n                        helperText={\r\n                          errors.invoice ||\r\n                          (purchaseInvoices.length === 0 ? \"No unpaid invoices found for this vendor\" :\r\n                          \"Optional: Select invoice for payment tracking. Partial payments are supported.\")\r\n                        }\r\n                      />\r\n                    )}\r\n                    disabled={!canEdit || purchaseInvoices.length === 0}\r\n                    noOptionsText=\"No unpaid invoices found\"\r\n                    ListboxProps={{\r\n                      style: { maxHeight: '200px' }\r\n                    }}\r\n                  />\r\n                )}\r\n                \r\n                {/* Title (Debit Account - shown if no vendor is selected) */}\r\n                {!selectedVendor && (\r\n                  <FormControl\r\n                    fullWidth\r\n                    margin=\"normal\"\r\n                    error={!!errors.toAccountId}\r\n                  >\r\n                    <InputLabel>Title</InputLabel>\r\n                    <Select\r\n                      value={voucher.toAccountId}\r\n                      onChange={(e) => handleInputChange('toAccountId', e.target.value)}\r\n                      label=\"Title\"\r\n                      disabled={!canEdit}\r\n                    >\r\n                      <MenuItem value=\"\">\r\n                        <em>Select Account</em>\r\n                      </MenuItem>\r\n                      {accounts.map((account) => (\r\n                        <MenuItem key={account._id} value={account._id}>\r\n                          {account.accountCode} - {account.accountName}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                    {errors.toAccountId && (\r\n                      <FormHelperText>{errors.toAccountId}</FormHelperText>\r\n                    )}\r\n                  </FormControl>\r\n                )}\r\n                \r\n                {/* Amount */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Amount\"\r\n                  type=\"number\"\r\n                  value={voucher.amount}\r\n                  onChange={(e) => handleInputChange('amount', e.target.value)}\r\n                  margin=\"normal\"\r\n                  error={!!errors.amount}\r\n                  helperText={errors.amount}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n                \r\n                {/* Narration */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Narration\"\r\n                  value={voucher.narration}\r\n                  onChange={(e) => handleInputChange('narration', e.target.value)}\r\n                  margin=\"normal\"\r\n                  multiline\r\n                  rows={2}\r\n                  error={!!errors.narration}\r\n                  helperText={errors.narration}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n                \r\n                {/* Description */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Description\"\r\n                  value={voucher.description || ''}\r\n                  onChange={(e) => handleInputChange('description', e.target.value)}\r\n                  margin=\"normal\"\r\n                  multiline\r\n                  rows={2}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          \r\n          {/* Action Buttons */}\r\n          <Grid item xs={12}>\r\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>\r\n              {onCancel && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  color=\"secondary\"\r\n                  startIcon={<CancelIcon />}\r\n                  onClick={onCancel}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n              )}\r\n              \r\n              {canEdit && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  startIcon={<SaveIcon />}\r\n                  onClick={handleSave}\r\n                  disabled={loading}\r\n                >\r\n                  Save\r\n                </Button>\r\n              )}\r\n              \r\n              {canApprove && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"warning\"\r\n                  startIcon={<ApproveIcon />}\r\n                  onClick={handleApprove}\r\n                  disabled={loading}\r\n                >\r\n                  Approve\r\n                </Button>\r\n              )}\r\n              \r\n              {canPost && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"success\"\r\n                  startIcon={<PostIcon />}\r\n                  onClick={handlePost}\r\n                  disabled={loading}\r\n                >\r\n                  Post to Ledger\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          </Grid>\r\n        </Grid>\r\n      </LocalizationProvider>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default BankPaymentVoucherForm; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,YAAY,EACZC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,WAAW,EACpBC,OAAO,IAAIC,QAAQ,EACnBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,cAAc,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,QAAQ,GAAG,KAAK;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC;IACrC4C,WAAW,EAAE,IAAI;IAAE;IACnBC,WAAW,EAAEhB,KAAK,CAAC,CAAC;IAAE;IACtBiB,eAAe,EAAEjB,KAAK,CAAC,CAAC;IAAE;IAC1BkB,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE,EAAE;IAAE;IACnBC,WAAW,EAAE,EAAE;IAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,eAAe;IAC9BC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,MAAM,EAAEC,SAAS,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAM6E,cAAc,GAAG,CACrB,eAAe,EACf,QAAQ,EACR,iBAAiB,EACjB,MAAM,EACN,OAAO,CACR;EAED5E,SAAS,CAAC,MAAM;IACd6E,gBAAgB,CAAC,CAAC;IAClB,IAAIzC,SAAS,EAAE;MACb0C,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC1C,SAAS,CAAC,CAAC;;EAEf;EACApC,SAAS,CAAC,MAAM;IACd,IAAIwE,cAAc,EAAE;MAClBO,mBAAmB,CAACP,cAAc,CAACQ,GAAG,CAAC;IACzC,CAAC,MAAM;MACLb,mBAAmB,CAAC,EAAE,CAAC;MACvBQ,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;;EAEpB;EACAxE,SAAS,CAAC,MAAM;IACd,IAAI0E,eAAe,EAAE;MACnB,MAAMO,eAAe,GAAGP,eAAe,CAACO,eAAe,IAAI,CAAC;;MAE5D;MACA,IAAIzB,SAAS,GAAG,2BAA2BkB,eAAe,CAACQ,aAAa,IAAI,SAAS,EAAE;;MAEvF;MACA,IAAIR,eAAe,CAACS,UAAU,IAAIT,eAAe,CAACU,aAAa,EAAE;QAC/D,MAAMC,cAAc,GAAGX,eAAe,CAACU,aAAa,CAACE,GAAG,CAACC,GAAG,IAAI,GAAGA,GAAG,CAACC,YAAY,KAAKD,GAAG,CAAC7B,MAAM,GAAG,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAAC;QACjHjC,SAAS,GAAG,2BAA2BkB,eAAe,CAACQ,aAAa,eAAepD,cAAc,CAAC4C,eAAe,CAACgB,cAAc,IAAI,CAAC,CAAC,cAAc5D,cAAc,CAAC4C,eAAe,CAACiB,YAAY,IAAI,CAAC,CAAC,KAAKN,cAAc,gBAAgBvD,cAAc,CAAC4C,eAAe,CAACkB,cAAc,IAAI,CAAC,CAAC,GAAG;MAChS;MAEAlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP/C,MAAM,EAAEmC,eAAe,CAACa,OAAO,CAAC,CAAC,CAAC;QAClCtC;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACkB,eAAe,CAAC,CAAC;;EAErB;EACA1E,SAAS,CAAC,MAAM;IACd;IACA,MAAM+F,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACC,KAAK;IAC1CF,MAAM,CAACC,OAAO,CAACC,KAAK,GAAG,CAAC,GAAGC,IAAI,KAAK;MAAA,IAAAC,MAAA,EAAAC,eAAA;MAClC,KAAAD,MAAA,GAAID,IAAI,CAAC,CAAC,CAAC,cAAAC,MAAA,gBAAAC,eAAA,GAAPD,MAAA,CAASE,QAAQ,cAAAD,eAAA,eAAjBA,eAAA,CAAAE,IAAA,CAAAH,MAAA,EAAoB,qBAAqB,CAAC,EAAE;QAC9C;QACA;MACF;MACAL,aAAa,CAAC,GAAGI,IAAI,CAAC;IACxB,CAAC;IAED,OAAO,MAAM;MACXH,MAAM,CAACC,OAAO,CAACC,KAAK,GAAGH,aAAa;IACtC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMlB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM,CAAC2B,WAAW,EAAEC,UAAU,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnE/E,KAAK,CAACgF,GAAG,CAAC,2BAA2B,CAAC,EACtChF,KAAK,CAACgF,GAAG,CAAC,cAAc,CAAC,EACzBhF,KAAK,CAACgF,GAAG,CAAC,oBAAoB,CAAC,CAChC,CAAC;MAEFhD,WAAW,CAAC2C,WAAW,CAACM,IAAI,CAAC;MAC7B7C,UAAU,CAACwC,UAAU,CAACK,IAAI,CAAC;;MAE3B;MACA,MAAMC,QAAQ,GAAGL,eAAe,CAACI,IAAI;MACrC,MAAME,cAAc,GAAGD,QAAQ,CAACzB,GAAG,CAAC2B,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC;;MAE5E;MACA,IAAIJ,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMC,mBAAmB,GAAGd,WAAW,CAACM,IAAI,CAACK,MAAM,CAACI,GAAG,IACrDP,cAAc,CAACV,QAAQ,CAACiB,GAAG,CAACvC,GAAG,CACjC,CAAC;QACDjB,eAAe,CAACuD,mBAAmB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMpB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmD,QAAQ,GAAG,MAAM3F,KAAK,CAACgF,GAAG,CAAC,yBAAyBzE,SAAS,EAAE,CAAC;MACtE,MAAMqF,WAAW,GAAGD,QAAQ,CAACV,IAAI;MAEjCpE,UAAU,CAAC;QACT,GAAG+E,WAAW;QACd7E,WAAW,EAAE6E,WAAW,CAAC7E,WAAW,GAAGhB,KAAK,CAAC6F,WAAW,CAAC7E,WAAW,CAAC,GAAGhB,KAAK,CAAC6F,WAAW,CAACC,SAAS,CAAC;QACpG7E,eAAe,EAAEjB,KAAK,CAAC6F,WAAW,CAAC5E,eAAe,CAAC;QACnDS,UAAU,EAAEmE,WAAW,CAACnE,UAAU,GAAG1B,KAAK,CAAC6F,WAAW,CAACnE,UAAU,CAAC,GAAG,IAAI;QACzEP,aAAa,EAAE0E,WAAW,CAAC1E,aAAa,CAACiC,GAAG;QAC5ChC,WAAW,EAAEyE,WAAW,CAACzE,WAAW,CAACgC;MACvC,CAAC,CAAC;;MAEF;MACA,IAAIyC,WAAW,CAACxE,gBAAgB,KAAK,QAAQ,IAAIwE,WAAW,CAACvE,cAAc,EAAE;QAC3E,MAAMyE,SAAS,GAAG,MAAM9F,KAAK,CAACgF,GAAG,CAAC,gBAAgBY,WAAW,CAACvE,cAAc,EAAE,CAAC;QAC/EuB,iBAAiB,CAACkD,SAAS,CAACb,IAAI,CAAC;;QAEjC;QACA,IAAIW,WAAW,CAAC9D,kBAAkB,IAAI8D,WAAW,CAAC9D,kBAAkB,CAAC0D,MAAM,GAAG,CAAC,EAAE;UAC/E,MAAMO,UAAU,GAAGH,WAAW,CAAC9D,kBAAkB,CAACkE,IAAI,CAACC,GAAG,IACxDA,GAAG,CAACC,YAAY,KAAK,iBACvB,CAAC;UAED,IAAIH,UAAU,IAAIA,UAAU,CAACI,UAAU,EAAE;YACvC,MAAMC,UAAU,GAAG,MAAMpG,KAAK,CAACgF,GAAG,CAAC,0BAA0Be,UAAU,CAACI,UAAU,EAAE,CAAC;YACrFrD,kBAAkB,CAACsD,UAAU,CAACnB,IAAI,CAAC;UACrC;QACF;MACF;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAG,MAAOmD,QAAQ,IAAK;IAC9C,IAAI;MACFjC,OAAO,CAACkC,GAAG,CAAC,iCAAiCD,QAAQ,EAAE,CAAC;MACxD;MACA,MAAMV,QAAQ,GAAG,MAAM3F,KAAK,CAACgF,GAAG,CAAC,yCAAyCqB,QAAQ,EAAE,CAAC;MACrFjC,OAAO,CAACkC,GAAG,CAAC,2BAA2B,EAAEX,QAAQ,CAACV,IAAI,CAAC;MAEvD,IAAIsB,KAAK,CAACC,OAAO,CAACb,QAAQ,CAACV,IAAI,CAAC,IAAIU,QAAQ,CAACV,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;QAC5DlD,mBAAmB,CAACqD,QAAQ,CAACV,IAAI,CAAC;MACpC,CAAC,MAAM;QACLb,OAAO,CAACkC,GAAG,CAAC,0CAA0C,CAAC;QACvDhE,mBAAmB,CAAC,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MAAA,IAAAoC,eAAA,EAAAC,oBAAA;MACdtC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDsC,KAAK,CAAC,oCAAoC,EAAAF,eAAA,GAAApC,KAAK,CAACsB,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAIvC,KAAK,CAACuC,OAAO,EAAE,CAAC;MAC3FtE,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAMuE,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1ClG,UAAU,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC8C,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAItE,MAAM,CAACqE,KAAK,CAAC,EAAE;MACjBpE,SAAS,CAACsB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAC8C,KAAK,GAAG;MAAK,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAME,kBAAkB,GAAIC,MAAM,IAAK;IACrCrE,iBAAiB,CAACqE,MAAM,CAAC;IACzBnE,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE1B,IAAImE,MAAM,EAAE;MACV;MACApG,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP5C,gBAAgB,EAAE,QAAQ;QAC1BC,cAAc,EAAE4F,MAAM,CAAC9D,GAAG;QAC1B7B,gBAAgB,EAAE2F,MAAM,CAACC,IAAI;QAC7B/F,WAAW,EAAE8F,MAAM,CAAC5B,SAAS,IAAI,EAAE;QAAE;QACrCpE,MAAM,EAAE,EAAE;QAAE;QACZU,SAAS,EAAE,EAAE;QAAE;QACfG,kBAAkB,EAAE,EAAE,CAAC;MACzB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACAjB,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP5C,gBAAgB,EAAE,EAAE;QACpBC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBH,WAAW,EAAE,EAAE;QACfF,MAAM,EAAE,EAAE;QACVU,SAAS,EAAE,EAAE;QACbG,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMqF,mBAAmB,GAAIC,OAAO,IAAK;IACvCtE,kBAAkB,CAACsE,OAAO,CAAC;IAE3B,IAAIA,OAAO,EAAE;MACXhD,OAAO,CAACkC,GAAG,CAAC,mBAAmB,EAAEc,OAAO,CAAC;;MAEzC;MACA,MAAMhE,eAAe,GAAGgE,OAAO,CAAChE,eAAe;MAE/CgB,OAAO,CAACkC,GAAG,CAAC,oCAAoClD,eAAe,EAAE,CAAC;;MAElE;MACA,IAAIzB,SAAS,GAAG,2BAA2ByF,OAAO,CAAC/D,aAAa,EAAE;;MAElE;MACA,IAAI+D,OAAO,CAAC9D,UAAU,EAAE;QACtB,MAAME,cAAc,GAAG4D,OAAO,CAAC7D,aAAa,CAACE,GAAG,CAACC,GAAG,IAAI,GAAGA,GAAG,CAACC,YAAY,KAAKD,GAAG,CAAC7B,MAAM,GAAG,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAAC;QACzGjC,SAAS,GAAG,2BAA2ByF,OAAO,CAAC/D,aAAa,eAAepD,cAAc,CAACmH,OAAO,CAACvD,cAAc,CAAC,cAAc5D,cAAc,CAACmH,OAAO,CAACtD,YAAY,CAAC,KAAKN,cAAc,gBAAgBvD,cAAc,CAACmH,OAAO,CAACrD,cAAc,CAAC,GAAG;MACjP;;MAEA;MACAlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP/C,MAAM,EAAEmC,eAAe,CAACiE,QAAQ,CAAC,CAAC;QAClC1F;MACF,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM2F,YAAY,GAAG;QACnBpB,YAAY,EAAE,iBAAiB;QAC/BC,UAAU,EAAEiB,OAAO,CAACjE,GAAG;QACvBoE,cAAc,EAAEH,OAAO,CAAC/D,aAAa;QACrCmE,eAAe,EAAEpE,eAAe;QAChCS,cAAc,EAAEuD,OAAO,CAACvD,cAAc;QACtCC,YAAY,EAAEsD,OAAO,CAACtD,YAAY;QAClCC,cAAc,EAAEqD,OAAO,CAACrD;MAC1B,CAAC;MAEDlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPlC,kBAAkB,EAAE,CAACwF,YAAY;MACnC,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLzG,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPlC,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAM2F,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC9G,OAAO,CAACM,aAAa,EAAEwG,SAAS,CAACxG,aAAa,GAAG,kBAAkB;IACxE,IAAI,CAACN,OAAO,CAACO,WAAW,EAAEuG,SAAS,CAACvG,WAAW,GAAG,mCAAmC;IACrF,IAAI,CAACP,OAAO,CAACK,MAAM,IAAI0G,KAAK,CAACC,UAAU,CAAChH,OAAO,CAACK,MAAM,CAAC,CAAC,IAAI2G,UAAU,CAAChH,OAAO,CAACK,MAAM,CAAC,IAAI,CAAC,EAAE;MAC3FyG,SAAS,CAACzG,MAAM,GAAG,0BAA0B;IAC/C;IACA,IAAI,CAACL,OAAO,CAACG,WAAW,EAAE2G,SAAS,CAAC3G,WAAW,GAAG,0BAA0B;IAC5E,IAAI,CAACH,OAAO,CAACI,eAAe,EAAE0G,SAAS,CAAC1G,eAAe,GAAG,8BAA8B;IACxF,IAAI,CAACJ,OAAO,CAACe,SAAS,EAAE+F,SAAS,CAAC/F,SAAS,GAAG,uBAAuB;;IAErE;IACA,IAAIgB,cAAc,IAAIE,eAAe,EAAE;MACrC;MACA,IAAIA,eAAe,CAACwD,QAAQ,KAAK1D,cAAc,CAACQ,GAAG,EAAE;QACnDuE,SAAS,CAACN,OAAO,GAAG,yDAAyD;MAC/E;;MAEA;MACA,MAAMhE,eAAe,GAAGP,eAAe,CAACO,eAAe,IAAI,CAAC;MAC5D,MAAMyE,aAAa,GAAGD,UAAU,CAAChH,OAAO,CAACK,MAAM,CAAC,IAAI,CAAC;MACrD,IAAI4G,aAAa,GAAGzE,eAAe,EAAE;QACnCsE,SAAS,CAACzG,MAAM,GAAG,oDAAoDmC,eAAe,CAACa,OAAO,CAAC,CAAC,CAAC,EAAE;MACrG;IACF;IAEAvB,SAAS,CAACgF,SAAS,CAAC;IACpB,OAAOI,MAAM,CAACC,IAAI,CAACL,SAAS,CAAC,CAAClC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMwC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;IAErB,IAAI;MACFjF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMoD,WAAW,GAAG;QAClB,GAAGhF,OAAO;QACVG,WAAW,EAAEH,OAAO,CAACG,WAAW,CAACkH,WAAW,CAAC,CAAC;QAC9CjH,eAAe,EAAEJ,OAAO,CAACI,eAAe,CAACiH,WAAW,CAAC,CAAC;QACtDxG,UAAU,EAAEb,OAAO,CAACa,UAAU,GAAGb,OAAO,CAACa,UAAU,CAACwG,WAAW,CAAC,CAAC,GAAG,IAAI;QACxEhH,MAAM,EAAE2G,UAAU,CAAChH,OAAO,CAACK,MAAM;MACnC,CAAC;MAED,IAAI0E,QAAQ;MACZ,IAAIpF,SAAS,EAAE;QACboF,QAAQ,GAAG,MAAM3F,KAAK,CAACkI,GAAG,CAAC,yBAAyB3H,SAAS,EAAE,EAAEqF,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLD,QAAQ,GAAG,MAAM3F,KAAK,CAACmI,IAAI,CAAC,uBAAuB,EAAEvC,WAAW,CAAC;MACnE;MAEA,IAAInF,MAAM,EAAEA,MAAM,CAACkF,QAAQ,CAACV,IAAI,CAACrE,OAAO,CAAC;IAC3C,CAAC,CAAC,OAAOyD,KAAK,EAAE;MAAA,IAAA+D,gBAAA,EAAAC,qBAAA;MACdjE,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CsC,KAAK,CAAC,EAAAyB,gBAAA,GAAA/D,KAAK,CAACsB,QAAQ,cAAAyC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnD,IAAI,cAAAoD,qBAAA,uBAApBA,qBAAA,CAAsBzB,OAAO,KAAI,mCAAmC,CAAC;IAC7E,CAAC,SAAS;MACRpE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8F,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF9F,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMxC,KAAK,CAACkI,GAAG,CAAC,yBAAyB3H,SAAS,UAAU,CAAC;MAC7D,IAAIE,MAAM,EAAEA,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4D,KAAK,EAAE;MAAA,IAAAkE,gBAAA,EAAAC,qBAAA;MACdpE,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDsC,KAAK,CAAC,EAAA4B,gBAAA,GAAAlE,KAAK,CAACsB,QAAQ,cAAA4C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtD,IAAI,cAAAuD,qBAAA,uBAApBA,qBAAA,CAAsB5B,OAAO,KAAI,yBAAyB,CAAC;IACnE,CAAC,SAAS;MACRpE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFjG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMxC,KAAK,CAACkI,GAAG,CAAC,yBAAyB3H,SAAS,OAAO,CAAC;MAC1D,IAAIE,MAAM,EAAEA,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4D,KAAK,EAAE;MAAA,IAAAqE,gBAAA,EAAAC,qBAAA;MACdvE,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CsC,KAAK,CAAC,EAAA+B,gBAAA,GAAArE,KAAK,CAACsB,QAAQ,cAAA+C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzD,IAAI,cAAA0D,qBAAA,uBAApBA,qBAAA,CAAsB/B,OAAO,KAAI,uBAAuB,CAAC;IACjE,CAAC,SAAS;MACRpE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoG,OAAO,GAAG,CAACpI,QAAQ,KAAK,CAACD,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,OAAO,CAAC;EACvE,MAAMgH,UAAU,GAAG,CAACrI,QAAQ,IAAID,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,OAAO;EACvE,MAAMiH,OAAO,GAAG,CAACtI,QAAQ,IAAID,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,UAAU;EAEvE,oBACE1B,OAAA,CAAC/B,GAAG;IAAA2K,QAAA,eACF5I,OAAA,CAACN,oBAAoB;MAACmJ,WAAW,EAAElJ,YAAa;MAAAiJ,QAAA,eAC9C5I,OAAA,CAAC1B,IAAI;QAACwK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAH,QAAA,gBAEzB5I,OAAA,CAAC1B,IAAI;UAAC0K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACvB5I,OAAA,CAAC9B,IAAI;YAACiL,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACrC5I,OAAA,CAAC7B,WAAW;cAAAyK,QAAA,gBACV5I,OAAA,CAAC5B,UAAU;gBAAC+K,OAAO,EAAC,IAAI;gBAACG,YAAY;gBAAAV,QAAA,EAAC;cAEtC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAGZtJ,SAAS,iBACRJ,OAAA,CAACzB,SAAS;gBACRoL,SAAS;gBACTC,KAAK,EAAC,aAAa;gBACnBhD,KAAK,EAAEnG,OAAO,CAACoJ,aAAa,IAAI,EAAG;gBACnCC,MAAM,EAAC,QAAQ;gBACfC,UAAU,EAAE;kBAAE1J,QAAQ,EAAE;gBAAK;cAAE;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACF,eAGD1J,OAAA,CAACP,UAAU;gBACTmK,KAAK,EAAC,cAAc;gBACpBhD,KAAK,EAAEnG,OAAO,CAACG,WAAY;gBAC3BoJ,QAAQ,EAAGC,QAAQ,IAAKvD,iBAAiB,CAAC,aAAa,EAAEuD,QAAQ,CAAE;gBACnEC,MAAM,EAAC,aAAa;gBACpBC,WAAW,EAAGC,MAAM,iBAClBpK,OAAA,CAACzB,SAAS;kBAAA,GACJ6L,MAAM;kBACVT,SAAS;kBACTG,MAAM,EAAC,QAAQ;kBACf5F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAAC1B,WAAY;kBAC5ByJ,UAAU,EAAE/H,MAAM,CAAC1B,WAAY;kBAC/BmJ,UAAU,EAAE;oBACV,GAAGK,MAAM,CAACL,UAAU;oBACpB1J,QAAQ,EAAE,CAACoI;kBACb;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFrJ,QAAQ,EAAE,CAACoI;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGF1J,OAAA,CAACP,UAAU;gBACTmK,KAAK,EAAC,kBAAkB;gBACxBhD,KAAK,EAAEnG,OAAO,CAACI,eAAgB;gBAC/BmJ,QAAQ,EAAGC,QAAQ,IAAKvD,iBAAiB,CAAC,iBAAiB,EAAEuD,QAAQ,CAAE;gBACvEC,MAAM,EAAC,aAAa;gBACpBC,WAAW,EAAGC,MAAM,iBAClBpK,OAAA,CAACzB,SAAS;kBAAA,GACJ6L,MAAM;kBACVT,SAAS;kBACTG,MAAM,EAAC,QAAQ;kBACf5F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACzB,eAAgB;kBAChCwJ,UAAU,EAAE/H,MAAM,CAACzB,eAAgB;kBACnCkJ,UAAU,EAAE;oBACV,GAAGK,MAAM,CAACL,UAAU;oBACpB1J,QAAQ,EAAE,CAACoI;kBACb;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFrJ,QAAQ,EAAE,CAACoI;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGF1J,OAAA,CAACzB,SAAS;gBACRoL,SAAS;gBACTC,KAAK,EAAC,gBAAgB;gBACtBhD,KAAK,EAAEnG,OAAO,CAACc,aAAa,IAAI,EAAG;gBACnCyI,QAAQ,EAAGM,CAAC,IAAK5D,iBAAiB,CAAC,eAAe,EAAE4D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE;gBACpEkD,MAAM,EAAC,QAAQ;gBACfC,UAAU,EAAE;kBAAE1J,QAAQ,EAAE,CAACoI;gBAAQ,CAAE;gBACnC4B,UAAU,EAAC;cAAqD;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eAGF1J,OAAA,CAACxB,WAAW;gBACVmL,SAAS;gBACTG,MAAM,EAAC,QAAQ;gBACf5F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACvB,aAAc;gBAAA6H,QAAA,gBAE9B5I,OAAA,CAACvB,UAAU;kBAAAmK,QAAA,EAAC;gBAAI;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7B1J,OAAA,CAACtB,MAAM;kBACLkI,KAAK,EAAEnG,OAAO,CAACM,aAAc;kBAC7BiJ,QAAQ,EAAGM,CAAC,IAAK5D,iBAAiB,CAAC,eAAe,EAAE4D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE;kBACpEgD,KAAK,EAAC,MAAM;kBACZY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,gBAEnB5I,OAAA,CAACrB,QAAQ;oBAACiI,KAAK,EAAC,EAAE;oBAAAgC,QAAA,eAChB5I,OAAA;sBAAA4I,QAAA,EAAI;oBAAW;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,EACV5H,YAAY,CAACwB,GAAG,CAAEmH,OAAO,iBACxBzK,OAAA,CAACrB,QAAQ;oBAAmBiI,KAAK,EAAE6D,OAAO,CAACzH,GAAI;oBAAA4F,QAAA,EAC5C6B,OAAO,CAACC;kBAAW,GADPD,OAAO,CAACzH,GAAG;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRpH,MAAM,CAACvB,aAAa,iBACnBf,OAAA,CAAChB,cAAc;kBAAA4J,QAAA,EAAEtG,MAAM,CAACvB;gBAAa;kBAAAwI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eAGd1J,OAAA,CAACxB,WAAW;gBAACmL,SAAS;gBAACG,MAAM,EAAC,QAAQ;gBAAAlB,QAAA,gBACpC5I,OAAA,CAACvB,UAAU;kBAAAmK,QAAA,EAAC;gBAAc;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvC1J,OAAA,CAACtB,MAAM;kBACLkI,KAAK,EAAEnG,OAAO,CAACW,aAAc;kBAC7B4I,QAAQ,EAAGM,CAAC,IAAK5D,iBAAiB,CAAC,eAAe,EAAE4D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE;kBACpEgD,KAAK,EAAC,gBAAgB;kBACtBY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,EAElBhG,cAAc,CAACU,GAAG,CAAEqH,MAAM,iBACzB3K,OAAA,CAACrB,QAAQ;oBAAciI,KAAK,EAAE+D,MAAO;oBAAA/B,QAAA,EAClC+B;kBAAM,GADMA,MAAM;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGbjJ,OAAO,CAACW,aAAa,KAAK,QAAQ,iBACjCpB,OAAA,CAAAE,SAAA;gBAAA0I,QAAA,gBACE5I,OAAA,CAACzB,SAAS;kBACRoL,SAAS;kBACTC,KAAK,EAAC,eAAe;kBACrBhD,KAAK,EAAEnG,OAAO,CAACY,YAAY,IAAI,EAAG;kBAClC2I,QAAQ,EAAGM,CAAC,IAAK5D,iBAAiB,CAAC,cAAc,EAAE4D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE;kBACnEkD,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBAAE1J,QAAQ,EAAE,CAACoI;kBAAQ;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eAEF1J,OAAA,CAACP,UAAU;kBACTmK,KAAK,EAAC,aAAa;kBACnBhD,KAAK,EAAEnG,OAAO,CAACa,UAAW;kBAC1B0I,QAAQ,EAAGC,QAAQ,IAAKvD,iBAAiB,CAAC,YAAY,EAAEuD,QAAQ,CAAE;kBAClEE,WAAW,EAAGC,MAAM,iBAClBpK,OAAA,CAACzB,SAAS;oBAAA,GACJ6L,MAAM;oBACVT,SAAS;oBACTG,MAAM,EAAC,QAAQ;oBACfC,UAAU,EAAE;sBACV,GAAGK,MAAM,CAACL,UAAU;sBACpB1J,QAAQ,EAAE,CAACoI;oBACb;kBAAE;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACD;kBACFrJ,QAAQ,EAAE,CAACoI;gBAAQ;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA,eACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1J,OAAA,CAAC1B,IAAI;UAAC0K,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACvB5I,OAAA,CAAC9B,IAAI;YAACiL,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACrC5I,OAAA,CAAC7B,WAAW;cAAAyK,QAAA,gBACV5I,OAAA,CAAC5B,UAAU;gBAAC+K,OAAO,EAAC,IAAI;gBAACG,YAAY;gBAAAV,QAAA,EAAC;cAEtC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAGb1J,OAAA,CAACjB,YAAY;gBACX6L,OAAO,EAAE5I,OAAQ;gBACjB6I,cAAc,EAAG/D,MAAM,IAAKA,MAAM,CAACC,IAAI,IAAI,EAAG;gBAC9CH,KAAK,EAAEpE,cAAe;gBACtBwH,QAAQ,EAAEA,CAACc,KAAK,EAAEb,QAAQ,KAAKpD,kBAAkB,CAACoD,QAAQ,CAAE;gBAC5DE,WAAW,EAAGC,MAAM,iBAClBpK,OAAA,CAACzB,SAAS;kBAAA,GACJ6L,MAAM;kBACVR,KAAK,EAAC,QAAQ;kBACdE,MAAM,EAAC,QAAQ;kBACfH,SAAS;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACD;gBACFc,QAAQ,EAAE,CAAC/B;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EAGDlH,cAAc,iBACbxC,OAAA,CAACjB,YAAY;gBACX6L,OAAO,EAAE1I,gBAAiB;gBAC1B2I,cAAc,EAAG5D,OAAO,IAAK;kBAC3B,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;kBAEvB,MAAM8D,YAAY,GAAG,GAAG9D,OAAO,CAAC/D,aAAa,IAAI,SAAS,EAAE;kBAC5D,MAAM8H,WAAW,GAAG,IAAIC,IAAI,CAAChE,OAAO,CAAC+D,WAAW,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;oBAC5EC,GAAG,EAAE,SAAS;oBACdC,KAAK,EAAE,OAAO;oBACdC,IAAI,EAAE;kBACR,CAAC,CAAC;;kBAEF;kBACA,IAAIpE,OAAO,CAAC9D,UAAU,EAAE;oBACtB,OAAO,GAAG4H,YAAY,KAAKC,WAAW,OAAOlL,cAAc,CAACmH,OAAO,CAAChE,eAAe,CAAC,oBAAoB;kBAC1G;;kBAEA;kBACA,OAAO,GAAG8H,YAAY,KAAKC,WAAW,OAAOlL,cAAc,CAACmH,OAAO,CAAChE,eAAe,CAAC,cAAc;gBACpG,CAAE;gBACF2D,KAAK,EAAElE,eAAgB;gBACvBsH,QAAQ,EAAEA,CAACc,KAAK,EAAEb,QAAQ,KAAKjD,mBAAmB,CAACiD,QAAQ,CAAE;gBAC7DqB,YAAY,EAAEA,CAACC,KAAK,EAAEtE,OAAO,kBAC3BjH,OAAA;kBAAA,GAAQuL,KAAK;kBAAA3C,QAAA,eACX5I,OAAA,CAAC/B,GAAG;oBAACmL,EAAE,EAAE;sBAAEoC,KAAK,EAAE,MAAM;sBAAEC,QAAQ,EAAE,MAAM;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA9C,QAAA,gBAC/D5I,OAAA,CAAC5B,UAAU;sBAAC+K,OAAO,EAAC,OAAO;sBAACwC,UAAU,EAAC,QAAQ;sBAACC,MAAM;sBAAAhD,QAAA,GACnD3B,OAAO,CAAC/D,aAAa,EAAC,KAAG,EAACpD,cAAc,CAACmH,OAAO,CAAChE,eAAe,CAAC,EAAC,cACrE;oBAAA;sBAAAsG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,EACZzC,OAAO,CAAC9D,UAAU,iBACjBnD,OAAA,CAAAE,SAAA;sBAAA0I,QAAA,gBACE5I,OAAA,CAAC5B,UAAU;wBAAC+K,OAAO,EAAC,SAAS;wBAAC0C,KAAK,EAAC,gBAAgB;wBAACC,OAAO,EAAC,OAAO;wBAACF,MAAM;wBAAAhD,QAAA,GAAC,YAChE,EAAC9I,cAAc,CAACmH,OAAO,CAACvD,cAAc,CAAC,EAAC,cACzC,EAAC5D,cAAc,CAACmH,OAAO,CAACtD,YAAY,CAAC,EAAC,eACrC,EAAC7D,cAAc,CAACmH,OAAO,CAACrD,cAAc,CAAC;sBAAA;wBAAA2F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACb1J,OAAA,CAAC5B,UAAU;wBAAC+K,OAAO,EAAC,SAAS;wBAAC0C,KAAK,EAAC,SAAS;wBAACC,OAAO,EAAC,OAAO;wBAACF,MAAM;wBAAAhD,QAAA,GACjE3B,OAAO,CAAC7D,aAAa,CAACiC,MAAM,EAAC,sBAC9B,EAAC4B,OAAO,CAAC7D,aAAa,CAACE,GAAG,CAAC,CAACC,GAAG,EAAEwI,GAAG,kBAClC/L,OAAA;0BAAA4I,QAAA,GACGrF,GAAG,CAACC,YAAY,EAAC,IAAE,EAACD,GAAG,CAAC7B,MAAM,EAAC,GAChC,EAACqK,GAAG,GAAG9E,OAAO,CAAC7D,aAAa,CAACiC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE;wBAAA,GAF1C0G,GAAG;0BAAAxC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAGR,CACP,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA,eACb,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACJ;gBACFS,WAAW,EAAGC,MAAM,iBAClBpK,OAAA,CAACzB,SAAS;kBAAA,GACJ6L,MAAM;kBACVR,KAAK,EAAC,kBAAkB;kBACxBE,MAAM,EAAC,QAAQ;kBACfH,SAAS;kBACTzF,KAAK,EAAEhC,gBAAgB,CAACmD,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC/C,MAAM,CAAC2E,OAAQ;kBACzDoD,UAAU,EACR/H,MAAM,CAAC2E,OAAO,KACb/E,gBAAgB,CAACmD,MAAM,KAAK,CAAC,GAAG,0CAA0C,GAC3E,gFAAgF;gBACjF;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACD;gBACFc,QAAQ,EAAE,CAAC/B,OAAO,IAAIvG,gBAAgB,CAACmD,MAAM,KAAK,CAAE;gBACpD2G,aAAa,EAAC,0BAA0B;gBACxCC,YAAY,EAAE;kBACZC,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAC9B;cAAE;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EAGA,CAAClH,cAAc,iBACdxC,OAAA,CAACxB,WAAW;gBACVmL,SAAS;gBACTG,MAAM,EAAC,QAAQ;gBACf5F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACtB,WAAY;gBAAA4H,QAAA,gBAE5B5I,OAAA,CAACvB,UAAU;kBAAAmK,QAAA,EAAC;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9B1J,OAAA,CAACtB,MAAM;kBACLkI,KAAK,EAAEnG,OAAO,CAACO,WAAY;kBAC3BgJ,QAAQ,EAAGM,CAAC,IAAK5D,iBAAiB,CAAC,aAAa,EAAE4D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE;kBAClEgD,KAAK,EAAC,OAAO;kBACbY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,gBAEnB5I,OAAA,CAACrB,QAAQ;oBAACiI,KAAK,EAAC,EAAE;oBAAAgC,QAAA,eAChB5I,OAAA;sBAAA4I,QAAA,EAAI;oBAAc;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACV9H,QAAQ,CAAC0B,GAAG,CAAEmH,OAAO,iBACpBzK,OAAA,CAACrB,QAAQ;oBAAmBiI,KAAK,EAAE6D,OAAO,CAACzH,GAAI;oBAAA4F,QAAA,GAC5C6B,OAAO,CAAC2B,WAAW,EAAC,KAAG,EAAC3B,OAAO,CAACC,WAAW;kBAAA,GAD/BD,OAAO,CAACzH,GAAG;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRpH,MAAM,CAACtB,WAAW,iBACjBhB,OAAA,CAAChB,cAAc;kBAAA4J,QAAA,EAAEtG,MAAM,CAACtB;gBAAW;kBAAAuI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACrD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CACd,eAGD1J,OAAA,CAACzB,SAAS;gBACRoL,SAAS;gBACTC,KAAK,EAAC,QAAQ;gBACdyC,IAAI,EAAC,QAAQ;gBACbzF,KAAK,EAAEnG,OAAO,CAACK,MAAO;gBACtBkJ,QAAQ,EAAGM,CAAC,IAAK5D,iBAAiB,CAAC,QAAQ,EAAE4D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE;gBAC7DkD,MAAM,EAAC,QAAQ;gBACf5F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACxB,MAAO;gBACvBuJ,UAAU,EAAE/H,MAAM,CAACxB,MAAO;gBAC1BiJ,UAAU,EAAE;kBAAE1J,QAAQ,EAAE,CAACoI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAGF1J,OAAA,CAACzB,SAAS;gBACRoL,SAAS;gBACTC,KAAK,EAAC,WAAW;gBACjBhD,KAAK,EAAEnG,OAAO,CAACe,SAAU;gBACzBwI,QAAQ,EAAGM,CAAC,IAAK5D,iBAAiB,CAAC,WAAW,EAAE4D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE;gBAChEkD,MAAM,EAAC,QAAQ;gBACfwC,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRrI,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACd,SAAU;gBAC1B6I,UAAU,EAAE/H,MAAM,CAACd,SAAU;gBAC7BuI,UAAU,EAAE;kBAAE1J,QAAQ,EAAE,CAACoI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAGF1J,OAAA,CAACzB,SAAS;gBACRoL,SAAS;gBACTC,KAAK,EAAC,aAAa;gBACnBhD,KAAK,EAAEnG,OAAO,CAACgB,WAAW,IAAI,EAAG;gBACjCuI,QAAQ,EAAGM,CAAC,IAAK5D,iBAAiB,CAAC,aAAa,EAAE4D,CAAC,CAACC,MAAM,CAAC3D,KAAK,CAAE;gBAClEkD,MAAM,EAAC,QAAQ;gBACfwC,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRxC,UAAU,EAAE;kBAAE1J,QAAQ,EAAE,CAACoI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGP1J,OAAA,CAAC1B,IAAI;UAAC0K,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAL,QAAA,eAChB5I,OAAA,CAAC/B,GAAG;YAACmL,EAAE,EAAE;cAAE0C,OAAO,EAAE,MAAM;cAAEU,cAAc,EAAE,UAAU;cAAEC,GAAG,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA9D,QAAA,GACrErI,QAAQ,iBACPP,OAAA,CAAC3B,MAAM;cACL8K,OAAO,EAAC,UAAU;cAClB0C,KAAK,EAAC,WAAW;cACjBc,SAAS,eAAE3M,OAAA,CAACR,UAAU;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BkD,OAAO,EAAErM,QAAS;cAAAqI,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAjB,OAAO,iBACNzI,OAAA,CAAC3B,MAAM;cACL8K,OAAO,EAAC,WAAW;cACnB0C,KAAK,EAAC,SAAS;cACfc,SAAS,eAAE3M,OAAA,CAACd,QAAQ;gBAAAqK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBkD,OAAO,EAAE/E,UAAW;cACpB2C,QAAQ,EAAEpI,OAAQ;cAAAwG,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAhB,UAAU,iBACT1I,OAAA,CAAC3B,MAAM;cACL8K,OAAO,EAAC,WAAW;cACnB0C,KAAK,EAAC,SAAS;cACfc,SAAS,eAAE3M,OAAA,CAACZ,WAAW;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BkD,OAAO,EAAEzE,aAAc;cACvBqC,QAAQ,EAAEpI,OAAQ;cAAAwG,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAf,OAAO,iBACN3I,OAAA,CAAC3B,MAAM;cACL8K,OAAO,EAAC,WAAW;cACnB0C,KAAK,EAAC,SAAS;cACfc,SAAS,eAAE3M,OAAA,CAACV,QAAQ;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBkD,OAAO,EAAEtE,UAAW;cACpBkC,QAAQ,EAAEpI,OAAQ;cAAAwG,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEV,CAAC;AAAClJ,EAAA,CAtuBIL,sBAAsB;AAAA0M,EAAA,GAAtB1M,sBAAsB;AAwuB5B,eAAeA,sBAAsB;AAAC,IAAA0M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}