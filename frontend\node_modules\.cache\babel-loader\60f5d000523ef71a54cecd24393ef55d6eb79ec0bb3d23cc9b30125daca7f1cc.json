{"ast": null, "code": "import StringBuilder from '../../util/StringBuilder';\nimport { ALOG, FACTORS, FACTOR_SETS, LOG } from './constants';\n/**\n * Error Correction Code for ECC200.\n */\nvar ErrorCorrection = /** @class */function () {\n  function ErrorCorrection() {}\n  /**\n   * Creates the ECC200 error correction for an encoded message.\n   *\n   * @param codewords  the codewords\n   * @param symbolInfo information about the symbol to be encoded\n   * @return the codewords with interleaved error correction.\n   */\n  ErrorCorrection.encodeECC200 = function (codewords, symbolInfo) {\n    if (codewords.length !== symbolInfo.getDataCapacity()) {\n      throw new Error('The number of codewords does not match the selected symbol');\n    }\n    var sb = new StringBuilder();\n    sb.append(codewords);\n    var blockCount = symbolInfo.getInterleavedBlockCount();\n    if (blockCount === 1) {\n      var ecc = this.createECCBlock(codewords, symbolInfo.getErrorCodewords());\n      sb.append(ecc);\n    } else {\n      // sb.setLength(sb.capacity());\n      var dataSizes = [];\n      var errorSizes = [];\n      for (var i = 0; i < blockCount; i++) {\n        dataSizes[i] = symbolInfo.getDataLengthForInterleavedBlock(i + 1);\n        errorSizes[i] = symbolInfo.getErrorLengthForInterleavedBlock(i + 1);\n      }\n      for (var block = 0; block < blockCount; block++) {\n        var temp = new StringBuilder();\n        for (var d = block; d < symbolInfo.getDataCapacity(); d += blockCount) {\n          temp.append(codewords.charAt(d));\n        }\n        var ecc = this.createECCBlock(temp.toString(), errorSizes[block]);\n        var pos = 0;\n        for (var e = block; e < errorSizes[block] * blockCount; e += blockCount) {\n          sb.setCharAt(symbolInfo.getDataCapacity() + e, ecc.charAt(pos++));\n        }\n      }\n    }\n    return sb.toString();\n  };\n  ErrorCorrection.createECCBlock = function (codewords, numECWords) {\n    var table = -1;\n    for (var i = 0; i < FACTOR_SETS.length; i++) {\n      if (FACTOR_SETS[i] === numECWords) {\n        table = i;\n        break;\n      }\n    }\n    if (table < 0) {\n      throw new Error('Illegal number of error correction codewords specified: ' + numECWords);\n    }\n    var poly = FACTORS[table];\n    var ecc = [];\n    for (var i = 0; i < numECWords; i++) {\n      ecc[i] = 0;\n    }\n    for (var i = 0; i < codewords.length; i++) {\n      var m = ecc[numECWords - 1] ^ codewords.charAt(i).charCodeAt(0);\n      for (var k = numECWords - 1; k > 0; k--) {\n        if (m !== 0 && poly[k] !== 0) {\n          ecc[k] = ecc[k - 1] ^ ALOG[(LOG[m] + LOG[poly[k]]) % 255];\n        } else {\n          ecc[k] = ecc[k - 1];\n        }\n      }\n      if (m !== 0 && poly[0] !== 0) {\n        ecc[0] = ALOG[(LOG[m] + LOG[poly[0]]) % 255];\n      } else {\n        ecc[0] = 0;\n      }\n    }\n    var eccReversed = [];\n    for (var i = 0; i < numECWords; i++) {\n      eccReversed[i] = ecc[numECWords - i - 1];\n    }\n    return eccReversed.map(function (c) {\n      return String.fromCharCode(c);\n    }).join('');\n  };\n  return ErrorCorrection;\n}();\nexport default ErrorCorrection;", "map": {"version": 3, "names": ["StringBuilder", "ALOG", "FACTORS", "FACTOR_SETS", "LOG", "ErrorCorrection", "encodeECC200", "codewords", "symbolInfo", "length", "getDataCapacity", "Error", "sb", "append", "blockCount", "getInterleavedBlockCount", "ecc", "createECCBlock", "getErrorCodewords", "dataSizes", "errorSizes", "i", "getDataLengthForInterleavedBlock", "getErrorLengthForInterleavedBlock", "block", "temp", "d", "char<PERSON>t", "toString", "pos", "e", "setCharAt", "numECWords", "table", "poly", "m", "charCodeAt", "k", "eccReversed", "map", "c", "String", "fromCharCode", "join"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/ErrorCorrection.js"], "sourcesContent": ["import StringBuilder from '../../util/StringBuilder';\nimport { ALOG, FACTORS, FACTOR_SETS, LOG } from './constants';\n/**\n * Error Correction Code for ECC200.\n */\nvar ErrorCorrection = /** @class */ (function () {\n    function ErrorCorrection() {\n    }\n    /**\n     * Creates the ECC200 error correction for an encoded message.\n     *\n     * @param codewords  the codewords\n     * @param symbolInfo information about the symbol to be encoded\n     * @return the codewords with interleaved error correction.\n     */\n    ErrorCorrection.encodeECC200 = function (codewords, symbolInfo) {\n        if (codewords.length !== symbolInfo.getDataCapacity()) {\n            throw new Error('The number of codewords does not match the selected symbol');\n        }\n        var sb = new StringBuilder();\n        sb.append(codewords);\n        var blockCount = symbolInfo.getInterleavedBlockCount();\n        if (blockCount === 1) {\n            var ecc = this.createECCBlock(codewords, symbolInfo.getErrorCodewords());\n            sb.append(ecc);\n        }\n        else {\n            // sb.setLength(sb.capacity());\n            var dataSizes = [];\n            var errorSizes = [];\n            for (var i = 0; i < blockCount; i++) {\n                dataSizes[i] = symbolInfo.getDataLengthForInterleavedBlock(i + 1);\n                errorSizes[i] = symbolInfo.getErrorLengthForInterleavedBlock(i + 1);\n            }\n            for (var block = 0; block < blockCount; block++) {\n                var temp = new StringBuilder();\n                for (var d = block; d < symbolInfo.getDataCapacity(); d += blockCount) {\n                    temp.append(codewords.charAt(d));\n                }\n                var ecc = this.createECCBlock(temp.toString(), errorSizes[block]);\n                var pos = 0;\n                for (var e = block; e < errorSizes[block] * blockCount; e += blockCount) {\n                    sb.setCharAt(symbolInfo.getDataCapacity() + e, ecc.charAt(pos++));\n                }\n            }\n        }\n        return sb.toString();\n    };\n    ErrorCorrection.createECCBlock = function (codewords, numECWords) {\n        var table = -1;\n        for (var i = 0; i < FACTOR_SETS.length; i++) {\n            if (FACTOR_SETS[i] === numECWords) {\n                table = i;\n                break;\n            }\n        }\n        if (table < 0) {\n            throw new Error('Illegal number of error correction codewords specified: ' + numECWords);\n        }\n        var poly = FACTORS[table];\n        var ecc = [];\n        for (var i = 0; i < numECWords; i++) {\n            ecc[i] = 0;\n        }\n        for (var i = 0; i < codewords.length; i++) {\n            var m = ecc[numECWords - 1] ^ codewords.charAt(i).charCodeAt(0);\n            for (var k = numECWords - 1; k > 0; k--) {\n                if (m !== 0 && poly[k] !== 0) {\n                    ecc[k] = ecc[k - 1] ^ ALOG[(LOG[m] + LOG[poly[k]]) % 255];\n                }\n                else {\n                    ecc[k] = ecc[k - 1];\n                }\n            }\n            if (m !== 0 && poly[0] !== 0) {\n                ecc[0] = ALOG[(LOG[m] + LOG[poly[0]]) % 255];\n            }\n            else {\n                ecc[0] = 0;\n            }\n        }\n        var eccReversed = [];\n        for (var i = 0; i < numECWords; i++) {\n            eccReversed[i] = ecc[numECWords - i - 1];\n        }\n        return eccReversed.map(function (c) { return String.fromCharCode(c); }).join('');\n    };\n    return ErrorCorrection;\n}());\nexport default ErrorCorrection;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0BAA0B;AACpD,SAASC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,GAAG,QAAQ,aAAa;AAC7D;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG,CAC3B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,eAAe,CAACC,YAAY,GAAG,UAAUC,SAAS,EAAEC,UAAU,EAAE;IAC5D,IAAID,SAAS,CAACE,MAAM,KAAKD,UAAU,CAACE,eAAe,CAAC,CAAC,EAAE;MACnD,MAAM,IAAIC,KAAK,CAAC,4DAA4D,CAAC;IACjF;IACA,IAAIC,EAAE,GAAG,IAAIZ,aAAa,CAAC,CAAC;IAC5BY,EAAE,CAACC,MAAM,CAACN,SAAS,CAAC;IACpB,IAAIO,UAAU,GAAGN,UAAU,CAACO,wBAAwB,CAAC,CAAC;IACtD,IAAID,UAAU,KAAK,CAAC,EAAE;MAClB,IAAIE,GAAG,GAAG,IAAI,CAACC,cAAc,CAACV,SAAS,EAAEC,UAAU,CAACU,iBAAiB,CAAC,CAAC,CAAC;MACxEN,EAAE,CAACC,MAAM,CAACG,GAAG,CAAC;IAClB,CAAC,MACI;MACD;MACA,IAAIG,SAAS,GAAG,EAAE;MAClB,IAAIC,UAAU,GAAG,EAAE;MACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,UAAU,EAAEO,CAAC,EAAE,EAAE;QACjCF,SAAS,CAACE,CAAC,CAAC,GAAGb,UAAU,CAACc,gCAAgC,CAACD,CAAC,GAAG,CAAC,CAAC;QACjED,UAAU,CAACC,CAAC,CAAC,GAAGb,UAAU,CAACe,iCAAiC,CAACF,CAAC,GAAG,CAAC,CAAC;MACvE;MACA,KAAK,IAAIG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGV,UAAU,EAAEU,KAAK,EAAE,EAAE;QAC7C,IAAIC,IAAI,GAAG,IAAIzB,aAAa,CAAC,CAAC;QAC9B,KAAK,IAAI0B,CAAC,GAAGF,KAAK,EAAEE,CAAC,GAAGlB,UAAU,CAACE,eAAe,CAAC,CAAC,EAAEgB,CAAC,IAAIZ,UAAU,EAAE;UACnEW,IAAI,CAACZ,MAAM,CAACN,SAAS,CAACoB,MAAM,CAACD,CAAC,CAAC,CAAC;QACpC;QACA,IAAIV,GAAG,GAAG,IAAI,CAACC,cAAc,CAACQ,IAAI,CAACG,QAAQ,CAAC,CAAC,EAAER,UAAU,CAACI,KAAK,CAAC,CAAC;QACjE,IAAIK,GAAG,GAAG,CAAC;QACX,KAAK,IAAIC,CAAC,GAAGN,KAAK,EAAEM,CAAC,GAAGV,UAAU,CAACI,KAAK,CAAC,GAAGV,UAAU,EAAEgB,CAAC,IAAIhB,UAAU,EAAE;UACrEF,EAAE,CAACmB,SAAS,CAACvB,UAAU,CAACE,eAAe,CAAC,CAAC,GAAGoB,CAAC,EAAEd,GAAG,CAACW,MAAM,CAACE,GAAG,EAAE,CAAC,CAAC;QACrE;MACJ;IACJ;IACA,OAAOjB,EAAE,CAACgB,QAAQ,CAAC,CAAC;EACxB,CAAC;EACDvB,eAAe,CAACY,cAAc,GAAG,UAAUV,SAAS,EAAEyB,UAAU,EAAE;IAC9D,IAAIC,KAAK,GAAG,CAAC,CAAC;IACd,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,WAAW,CAACM,MAAM,EAAEY,CAAC,EAAE,EAAE;MACzC,IAAIlB,WAAW,CAACkB,CAAC,CAAC,KAAKW,UAAU,EAAE;QAC/BC,KAAK,GAAGZ,CAAC;QACT;MACJ;IACJ;IACA,IAAIY,KAAK,GAAG,CAAC,EAAE;MACX,MAAM,IAAItB,KAAK,CAAC,0DAA0D,GAAGqB,UAAU,CAAC;IAC5F;IACA,IAAIE,IAAI,GAAGhC,OAAO,CAAC+B,KAAK,CAAC;IACzB,IAAIjB,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,UAAU,EAAEX,CAAC,EAAE,EAAE;MACjCL,GAAG,CAACK,CAAC,CAAC,GAAG,CAAC;IACd;IACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,SAAS,CAACE,MAAM,EAAEY,CAAC,EAAE,EAAE;MACvC,IAAIc,CAAC,GAAGnB,GAAG,CAACgB,UAAU,GAAG,CAAC,CAAC,GAAGzB,SAAS,CAACoB,MAAM,CAACN,CAAC,CAAC,CAACe,UAAU,CAAC,CAAC,CAAC;MAC/D,KAAK,IAAIC,CAAC,GAAGL,UAAU,GAAG,CAAC,EAAEK,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACrC,IAAIF,CAAC,KAAK,CAAC,IAAID,IAAI,CAACG,CAAC,CAAC,KAAK,CAAC,EAAE;UAC1BrB,GAAG,CAACqB,CAAC,CAAC,GAAGrB,GAAG,CAACqB,CAAC,GAAG,CAAC,CAAC,GAAGpC,IAAI,CAAC,CAACG,GAAG,CAAC+B,CAAC,CAAC,GAAG/B,GAAG,CAAC8B,IAAI,CAACG,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;QAC7D,CAAC,MACI;UACDrB,GAAG,CAACqB,CAAC,CAAC,GAAGrB,GAAG,CAACqB,CAAC,GAAG,CAAC,CAAC;QACvB;MACJ;MACA,IAAIF,CAAC,KAAK,CAAC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAC1BlB,GAAG,CAAC,CAAC,CAAC,GAAGf,IAAI,CAAC,CAACG,GAAG,CAAC+B,CAAC,CAAC,GAAG/B,GAAG,CAAC8B,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC;MAChD,CAAC,MACI;QACDlB,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;MACd;IACJ;IACA,IAAIsB,WAAW,GAAG,EAAE;IACpB,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,UAAU,EAAEX,CAAC,EAAE,EAAE;MACjCiB,WAAW,CAACjB,CAAC,CAAC,GAAGL,GAAG,CAACgB,UAAU,GAAGX,CAAC,GAAG,CAAC,CAAC;IAC5C;IACA,OAAOiB,WAAW,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOC,MAAM,CAACC,YAAY,CAACF,CAAC,CAAC;IAAE,CAAC,CAAC,CAACG,IAAI,CAAC,EAAE,CAAC;EACpF,CAAC;EACD,OAAOtC,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}