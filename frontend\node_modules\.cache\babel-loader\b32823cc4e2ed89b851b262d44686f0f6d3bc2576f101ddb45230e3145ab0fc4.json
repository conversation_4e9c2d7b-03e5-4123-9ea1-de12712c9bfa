{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\nimport MathUtils from './common/detector/MathUtils';\nimport Float from './util/Float';\n/**\n * <p>Encapsulates a point of interest in an image containing a barcode. Typically, this\n * would be the location of a finder pattern or the corner of the barcode, for example.</p>\n *\n * <AUTHOR> <PERSON>\n */\nvar ResultPoint = /** @class */function () {\n  function ResultPoint(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n  ResultPoint.prototype.getX = function () {\n    return this.x;\n  };\n  ResultPoint.prototype.getY = function () {\n    return this.y;\n  };\n  /*@Override*/\n  ResultPoint.prototype.equals = function (other) {\n    if (other instanceof ResultPoint) {\n      var otherPoint = other;\n      return this.x === otherPoint.x && this.y === otherPoint.y;\n    }\n    return false;\n  };\n  /*@Override*/\n  ResultPoint.prototype.hashCode = function () {\n    return 31 * Float.floatToIntBits(this.x) + Float.floatToIntBits(this.y);\n  };\n  /*@Override*/\n  ResultPoint.prototype.toString = function () {\n    return '(' + this.x + ',' + this.y + ')';\n  };\n  /**\n   * Orders an array of three ResultPoints in an order [A,B,C] such that AB is less than AC\n   * and BC is less than AC, and the angle between BC and BA is less than 180 degrees.\n   *\n   * @param patterns array of three {@code ResultPoint} to order\n   */\n  ResultPoint.orderBestPatterns = function (patterns) {\n    // Find distances between pattern centers\n    var zeroOneDistance = this.distance(patterns[0], patterns[1]);\n    var oneTwoDistance = this.distance(patterns[1], patterns[2]);\n    var zeroTwoDistance = this.distance(patterns[0], patterns[2]);\n    var pointA;\n    var pointB;\n    var pointC;\n    // Assume one closest to other two is B; A and C will just be guesses at first\n    if (oneTwoDistance >= zeroOneDistance && oneTwoDistance >= zeroTwoDistance) {\n      pointB = patterns[0];\n      pointA = patterns[1];\n      pointC = patterns[2];\n    } else if (zeroTwoDistance >= oneTwoDistance && zeroTwoDistance >= zeroOneDistance) {\n      pointB = patterns[1];\n      pointA = patterns[0];\n      pointC = patterns[2];\n    } else {\n      pointB = patterns[2];\n      pointA = patterns[0];\n      pointC = patterns[1];\n    }\n    // Use cross product to figure out whether A and C are correct or flipped.\n    // This asks whether BC x BA has a positive z component, which is the arrangement\n    // we want for A, B, C. If it's negative, then we've got it flipped around and\n    // should swap A and C.\n    if (this.crossProductZ(pointA, pointB, pointC) < 0.0) {\n      var temp = pointA;\n      pointA = pointC;\n      pointC = temp;\n    }\n    patterns[0] = pointA;\n    patterns[1] = pointB;\n    patterns[2] = pointC;\n  };\n  /**\n   * @param pattern1 first pattern\n   * @param pattern2 second pattern\n   * @return distance between two points\n   */\n  ResultPoint.distance = function (pattern1, pattern2) {\n    return MathUtils.distance(pattern1.x, pattern1.y, pattern2.x, pattern2.y);\n  };\n  /**\n   * Returns the z component of the cross product between vectors BC and BA.\n   */\n  ResultPoint.crossProductZ = function (pointA, pointB, pointC) {\n    var bX = pointB.x;\n    var bY = pointB.y;\n    return (pointC.x - bX) * (pointA.y - bY) - (pointC.y - bY) * (pointA.x - bX);\n  };\n  return ResultPoint;\n}();\nexport default ResultPoint;", "map": {"version": 3, "names": ["MathUtils", "Float", "ResultPoint", "x", "y", "prototype", "getX", "getY", "equals", "other", "otherPoint", "hashCode", "floatToIntBits", "toString", "orderBestPatterns", "patterns", "zeroOneDistance", "distance", "oneTwoDistance", "zeroTwoDistance", "pointA", "pointB", "pointC", "crossProductZ", "temp", "pattern1", "pattern2", "bX", "bY"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/ResultPoint.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\nimport MathUtils from './common/detector/MathUtils';\nimport Float from './util/Float';\n/**\n * <p>Encapsulates a point of interest in an image containing a barcode. Typically, this\n * would be the location of a finder pattern or the corner of the barcode, for example.</p>\n *\n * <AUTHOR> <PERSON>\n */\nvar ResultPoint = /** @class */ (function () {\n    function ResultPoint(x, y) {\n        this.x = x;\n        this.y = y;\n    }\n    ResultPoint.prototype.getX = function () {\n        return this.x;\n    };\n    ResultPoint.prototype.getY = function () {\n        return this.y;\n    };\n    /*@Override*/\n    ResultPoint.prototype.equals = function (other) {\n        if (other instanceof ResultPoint) {\n            var otherPoint = other;\n            return this.x === otherPoint.x && this.y === otherPoint.y;\n        }\n        return false;\n    };\n    /*@Override*/\n    ResultPoint.prototype.hashCode = function () {\n        return 31 * Float.floatToIntBits(this.x) + Float.floatToIntBits(this.y);\n    };\n    /*@Override*/\n    ResultPoint.prototype.toString = function () {\n        return '(' + this.x + ',' + this.y + ')';\n    };\n    /**\n     * Orders an array of three ResultPoints in an order [A,B,C] such that AB is less than AC\n     * and BC is less than AC, and the angle between BC and BA is less than 180 degrees.\n     *\n     * @param patterns array of three {@code ResultPoint} to order\n     */\n    ResultPoint.orderBestPatterns = function (patterns) {\n        // Find distances between pattern centers\n        var zeroOneDistance = this.distance(patterns[0], patterns[1]);\n        var oneTwoDistance = this.distance(patterns[1], patterns[2]);\n        var zeroTwoDistance = this.distance(patterns[0], patterns[2]);\n        var pointA;\n        var pointB;\n        var pointC;\n        // Assume one closest to other two is B; A and C will just be guesses at first\n        if (oneTwoDistance >= zeroOneDistance && oneTwoDistance >= zeroTwoDistance) {\n            pointB = patterns[0];\n            pointA = patterns[1];\n            pointC = patterns[2];\n        }\n        else if (zeroTwoDistance >= oneTwoDistance && zeroTwoDistance >= zeroOneDistance) {\n            pointB = patterns[1];\n            pointA = patterns[0];\n            pointC = patterns[2];\n        }\n        else {\n            pointB = patterns[2];\n            pointA = patterns[0];\n            pointC = patterns[1];\n        }\n        // Use cross product to figure out whether A and C are correct or flipped.\n        // This asks whether BC x BA has a positive z component, which is the arrangement\n        // we want for A, B, C. If it's negative, then we've got it flipped around and\n        // should swap A and C.\n        if (this.crossProductZ(pointA, pointB, pointC) < 0.0) {\n            var temp = pointA;\n            pointA = pointC;\n            pointC = temp;\n        }\n        patterns[0] = pointA;\n        patterns[1] = pointB;\n        patterns[2] = pointC;\n    };\n    /**\n     * @param pattern1 first pattern\n     * @param pattern2 second pattern\n     * @return distance between two points\n     */\n    ResultPoint.distance = function (pattern1, pattern2) {\n        return MathUtils.distance(pattern1.x, pattern1.y, pattern2.x, pattern2.y);\n    };\n    /**\n     * Returns the z component of the cross product between vectors BC and BA.\n     */\n    ResultPoint.crossProductZ = function (pointA, pointB, pointC) {\n        var bX = pointB.x;\n        var bY = pointB.y;\n        return ((pointC.x - bX) * (pointA.y - bY)) - ((pointC.y - bY) * (pointA.x - bX));\n    };\n    return ResultPoint;\n}());\nexport default ResultPoint;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,SAAS,MAAM,6BAA6B;AACnD,OAAOC,KAAK,MAAM,cAAc;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACvB,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACd;EACAF,WAAW,CAACG,SAAS,CAACC,IAAI,GAAG,YAAY;IACrC,OAAO,IAAI,CAACH,CAAC;EACjB,CAAC;EACDD,WAAW,CAACG,SAAS,CAACE,IAAI,GAAG,YAAY;IACrC,OAAO,IAAI,CAACH,CAAC;EACjB,CAAC;EACD;EACAF,WAAW,CAACG,SAAS,CAACG,MAAM,GAAG,UAAUC,KAAK,EAAE;IAC5C,IAAIA,KAAK,YAAYP,WAAW,EAAE;MAC9B,IAAIQ,UAAU,GAAGD,KAAK;MACtB,OAAO,IAAI,CAACN,CAAC,KAAKO,UAAU,CAACP,CAAC,IAAI,IAAI,CAACC,CAAC,KAAKM,UAAU,CAACN,CAAC;IAC7D;IACA,OAAO,KAAK;EAChB,CAAC;EACD;EACAF,WAAW,CAACG,SAAS,CAACM,QAAQ,GAAG,YAAY;IACzC,OAAO,EAAE,GAAGV,KAAK,CAACW,cAAc,CAAC,IAAI,CAACT,CAAC,CAAC,GAAGF,KAAK,CAACW,cAAc,CAAC,IAAI,CAACR,CAAC,CAAC;EAC3E,CAAC;EACD;EACAF,WAAW,CAACG,SAAS,CAACQ,QAAQ,GAAG,YAAY;IACzC,OAAO,GAAG,GAAG,IAAI,CAACV,CAAC,GAAG,GAAG,GAAG,IAAI,CAACC,CAAC,GAAG,GAAG;EAC5C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIF,WAAW,CAACY,iBAAiB,GAAG,UAAUC,QAAQ,EAAE;IAChD;IACA,IAAIC,eAAe,GAAG,IAAI,CAACC,QAAQ,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAIG,cAAc,GAAG,IAAI,CAACD,QAAQ,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC5D,IAAII,eAAe,GAAG,IAAI,CAACF,QAAQ,CAACF,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC7D,IAAIK,MAAM;IACV,IAAIC,MAAM;IACV,IAAIC,MAAM;IACV;IACA,IAAIJ,cAAc,IAAIF,eAAe,IAAIE,cAAc,IAAIC,eAAe,EAAE;MACxEE,MAAM,GAAGN,QAAQ,CAAC,CAAC,CAAC;MACpBK,MAAM,GAAGL,QAAQ,CAAC,CAAC,CAAC;MACpBO,MAAM,GAAGP,QAAQ,CAAC,CAAC,CAAC;IACxB,CAAC,MACI,IAAII,eAAe,IAAID,cAAc,IAAIC,eAAe,IAAIH,eAAe,EAAE;MAC9EK,MAAM,GAAGN,QAAQ,CAAC,CAAC,CAAC;MACpBK,MAAM,GAAGL,QAAQ,CAAC,CAAC,CAAC;MACpBO,MAAM,GAAGP,QAAQ,CAAC,CAAC,CAAC;IACxB,CAAC,MACI;MACDM,MAAM,GAAGN,QAAQ,CAAC,CAAC,CAAC;MACpBK,MAAM,GAAGL,QAAQ,CAAC,CAAC,CAAC;MACpBO,MAAM,GAAGP,QAAQ,CAAC,CAAC,CAAC;IACxB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACQ,aAAa,CAACH,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC,GAAG,GAAG,EAAE;MAClD,IAAIE,IAAI,GAAGJ,MAAM;MACjBA,MAAM,GAAGE,MAAM;MACfA,MAAM,GAAGE,IAAI;IACjB;IACAT,QAAQ,CAAC,CAAC,CAAC,GAAGK,MAAM;IACpBL,QAAQ,CAAC,CAAC,CAAC,GAAGM,MAAM;IACpBN,QAAQ,CAAC,CAAC,CAAC,GAAGO,MAAM;EACxB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIpB,WAAW,CAACe,QAAQ,GAAG,UAAUQ,QAAQ,EAAEC,QAAQ,EAAE;IACjD,OAAO1B,SAAS,CAACiB,QAAQ,CAACQ,QAAQ,CAACtB,CAAC,EAAEsB,QAAQ,CAACrB,CAAC,EAAEsB,QAAQ,CAACvB,CAAC,EAAEuB,QAAQ,CAACtB,CAAC,CAAC;EAC7E,CAAC;EACD;AACJ;AACA;EACIF,WAAW,CAACqB,aAAa,GAAG,UAAUH,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;IAC1D,IAAIK,EAAE,GAAGN,MAAM,CAAClB,CAAC;IACjB,IAAIyB,EAAE,GAAGP,MAAM,CAACjB,CAAC;IACjB,OAAQ,CAACkB,MAAM,CAACnB,CAAC,GAAGwB,EAAE,KAAKP,MAAM,CAAChB,CAAC,GAAGwB,EAAE,CAAC,GAAK,CAACN,MAAM,CAAClB,CAAC,GAAGwB,EAAE,KAAKR,MAAM,CAACjB,CAAC,GAAGwB,EAAE,CAAE;EACpF,CAAC;EACD,OAAOzB,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}