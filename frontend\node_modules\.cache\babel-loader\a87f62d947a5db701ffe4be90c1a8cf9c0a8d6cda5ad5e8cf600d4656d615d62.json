{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport LuminanceSource from './LuminanceSource';\n/*namespace com.google.zxing {*/\n/**\n * A wrapper implementation of {@link LuminanceSource} which inverts the luminances it returns -- black becomes\n * white and vice versa, and each value becomes (255-value).\n *\n * <AUTHOR> Owen\n */\nvar InvertedLuminanceSource = /** @class */function (_super) {\n  __extends(InvertedLuminanceSource, _super);\n  function InvertedLuminanceSource(delegate) {\n    var _this = _super.call(this, delegate.getWidth(), delegate.getHeight()) || this;\n    _this.delegate = delegate;\n    return _this;\n  }\n  /*@Override*/\n  InvertedLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n    var sourceRow = this.delegate.getRow(y, row);\n    var width = this.getWidth();\n    for (var i = 0; i < width; i++) {\n      sourceRow[i] = /*(byte)*/255 - (sourceRow[i] & 0xFF);\n    }\n    return sourceRow;\n  };\n  /*@Override*/\n  InvertedLuminanceSource.prototype.getMatrix = function () {\n    var matrix = this.delegate.getMatrix();\n    var length = this.getWidth() * this.getHeight();\n    var invertedMatrix = new Uint8ClampedArray(length);\n    for (var i = 0; i < length; i++) {\n      invertedMatrix[i] = /*(byte)*/255 - (matrix[i] & 0xFF);\n    }\n    return invertedMatrix;\n  };\n  /*@Override*/\n  InvertedLuminanceSource.prototype.isCropSupported = function () {\n    return this.delegate.isCropSupported();\n  };\n  /*@Override*/\n  InvertedLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n    return new InvertedLuminanceSource(this.delegate.crop(left, top, width, height));\n  };\n  /*@Override*/\n  InvertedLuminanceSource.prototype.isRotateSupported = function () {\n    return this.delegate.isRotateSupported();\n  };\n  /**\n   * @return original delegate {@link LuminanceSource} since invert undoes itself\n   */\n  /*@Override*/\n  InvertedLuminanceSource.prototype.invert = function () {\n    return this.delegate;\n  };\n  /*@Override*/\n  InvertedLuminanceSource.prototype.rotateCounterClockwise = function () {\n    return new InvertedLuminanceSource(this.delegate.rotateCounterClockwise());\n  };\n  /*@Override*/\n  InvertedLuminanceSource.prototype.rotateCounterClockwise45 = function () {\n    return new InvertedLuminanceSource(this.delegate.rotateCounterClockwise45());\n  };\n  return InvertedLuminanceSource;\n}(LuminanceSource);\nexport default InvertedLuminanceSource;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "LuminanceSource", "InvertedLuminanceSource", "_super", "delegate", "_this", "call", "getWidth", "getHeight", "getRow", "y", "row", "sourceRow", "width", "i", "getMatrix", "matrix", "length", "invertedMatrix", "Uint8ClampedArray", "isCropSupported", "crop", "left", "top", "height", "isRotateSupported", "invert", "rotateCounterClockwise", "rotateCounterClockwise45"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/InvertedLuminanceSource.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport LuminanceSource from './LuminanceSource';\n/*namespace com.google.zxing {*/\n/**\n * A wrapper implementation of {@link LuminanceSource} which inverts the luminances it returns -- black becomes\n * white and vice versa, and each value becomes (255-value).\n *\n * <AUTHOR> Owen\n */\nvar InvertedLuminanceSource = /** @class */ (function (_super) {\n    __extends(InvertedLuminanceSource, _super);\n    function InvertedLuminanceSource(delegate) {\n        var _this = _super.call(this, delegate.getWidth(), delegate.getHeight()) || this;\n        _this.delegate = delegate;\n        return _this;\n    }\n    /*@Override*/\n    InvertedLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n        var sourceRow = this.delegate.getRow(y, row);\n        var width = this.getWidth();\n        for (var i = 0; i < width; i++) {\n            sourceRow[i] = /*(byte)*/ (255 - (sourceRow[i] & 0xFF));\n        }\n        return sourceRow;\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.getMatrix = function () {\n        var matrix = this.delegate.getMatrix();\n        var length = this.getWidth() * this.getHeight();\n        var invertedMatrix = new Uint8ClampedArray(length);\n        for (var i = 0; i < length; i++) {\n            invertedMatrix[i] = /*(byte)*/ (255 - (matrix[i] & 0xFF));\n        }\n        return invertedMatrix;\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.isCropSupported = function () {\n        return this.delegate.isCropSupported();\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        return new InvertedLuminanceSource(this.delegate.crop(left, top, width, height));\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.isRotateSupported = function () {\n        return this.delegate.isRotateSupported();\n    };\n    /**\n     * @return original delegate {@link LuminanceSource} since invert undoes itself\n     */\n    /*@Override*/\n    InvertedLuminanceSource.prototype.invert = function () {\n        return this.delegate;\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.rotateCounterClockwise = function () {\n        return new InvertedLuminanceSource(this.delegate.rotateCounterClockwise());\n    };\n    /*@Override*/\n    InvertedLuminanceSource.prototype.rotateCounterClockwise45 = function () {\n        return new InvertedLuminanceSource(this.delegate.rotateCounterClockwise45());\n    };\n    return InvertedLuminanceSource;\n}(LuminanceSource));\nexport default InvertedLuminanceSource;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,eAAe,MAAM,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,uBAAuB,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC3DhB,SAAS,CAACe,uBAAuB,EAAEC,MAAM,CAAC;EAC1C,SAASD,uBAAuBA,CAACE,QAAQ,EAAE;IACvC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,QAAQ,CAACG,QAAQ,CAAC,CAAC,EAAEH,QAAQ,CAACI,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI;IAChFH,KAAK,CAACD,QAAQ,GAAGA,QAAQ;IACzB,OAAOC,KAAK;EAChB;EACA;EACAH,uBAAuB,CAACH,SAAS,CAACU,MAAM,GAAG,UAAUC,CAAC,CAAC,SAASC,GAAG,EAAE;IACjE,IAAIC,SAAS,GAAG,IAAI,CAACR,QAAQ,CAACK,MAAM,CAACC,CAAC,EAAEC,GAAG,CAAC;IAC5C,IAAIE,KAAK,GAAG,IAAI,CAACN,QAAQ,CAAC,CAAC;IAC3B,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,EAAEC,CAAC,EAAE,EAAE;MAC5BF,SAAS,CAACE,CAAC,CAAC,GAAG,UAAY,GAAG,IAAIF,SAAS,CAACE,CAAC,CAAC,GAAG,IAAI,CAAE;IAC3D;IACA,OAAOF,SAAS;EACpB,CAAC;EACD;EACAV,uBAAuB,CAACH,SAAS,CAACgB,SAAS,GAAG,YAAY;IACtD,IAAIC,MAAM,GAAG,IAAI,CAACZ,QAAQ,CAACW,SAAS,CAAC,CAAC;IACtC,IAAIE,MAAM,GAAG,IAAI,CAACV,QAAQ,CAAC,CAAC,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC/C,IAAIU,cAAc,GAAG,IAAIC,iBAAiB,CAACF,MAAM,CAAC;IAClD,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,MAAM,EAAEH,CAAC,EAAE,EAAE;MAC7BI,cAAc,CAACJ,CAAC,CAAC,GAAG,UAAY,GAAG,IAAIE,MAAM,CAACF,CAAC,CAAC,GAAG,IAAI,CAAE;IAC7D;IACA,OAAOI,cAAc;EACzB,CAAC;EACD;EACAhB,uBAAuB,CAACH,SAAS,CAACqB,eAAe,GAAG,YAAY;IAC5D,OAAO,IAAI,CAAChB,QAAQ,CAACgB,eAAe,CAAC,CAAC;EAC1C,CAAC;EACD;EACAlB,uBAAuB,CAACH,SAAS,CAACsB,IAAI,GAAG,UAAUC,IAAI,CAAC,SAASC,GAAG,CAAC,SAASV,KAAK,CAAC,SAASW,MAAM,CAAC,SAAS;IACzG,OAAO,IAAItB,uBAAuB,CAAC,IAAI,CAACE,QAAQ,CAACiB,IAAI,CAACC,IAAI,EAAEC,GAAG,EAAEV,KAAK,EAAEW,MAAM,CAAC,CAAC;EACpF,CAAC;EACD;EACAtB,uBAAuB,CAACH,SAAS,CAAC0B,iBAAiB,GAAG,YAAY;IAC9D,OAAO,IAAI,CAACrB,QAAQ,CAACqB,iBAAiB,CAAC,CAAC;EAC5C,CAAC;EACD;AACJ;AACA;EACI;EACAvB,uBAAuB,CAACH,SAAS,CAAC2B,MAAM,GAAG,YAAY;IACnD,OAAO,IAAI,CAACtB,QAAQ;EACxB,CAAC;EACD;EACAF,uBAAuB,CAACH,SAAS,CAAC4B,sBAAsB,GAAG,YAAY;IACnE,OAAO,IAAIzB,uBAAuB,CAAC,IAAI,CAACE,QAAQ,CAACuB,sBAAsB,CAAC,CAAC,CAAC;EAC9E,CAAC;EACD;EACAzB,uBAAuB,CAACH,SAAS,CAAC6B,wBAAwB,GAAG,YAAY;IACrE,OAAO,IAAI1B,uBAAuB,CAAC,IAAI,CAACE,QAAQ,CAACwB,wBAAwB,CAAC,CAAC,CAAC;EAChF,CAAC;EACD,OAAO1B,uBAAuB;AAClC,CAAC,CAACD,eAAe,CAAE;AACnB,eAAeC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}