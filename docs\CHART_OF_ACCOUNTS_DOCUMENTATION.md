# Chart of Accounts System Documentation

## Overview

The Chart of Accounts (CoA) has been redesigned to better organize and manage different types of accounts based on their source and purpose. This document outlines the new account categorization system, editing rules, and implementation details.

## Account Types

Accounts are now categorized into four main types based on their source:

### 1. Form-Created Accounts (Type 1)
- **Source Type:** `form_created`
- **Description:** Accounts automatically created from other entity forms/screens
- **Examples:** Customer accounts, Vendor accounts, Item/Inventory accounts, Bank accounts
- **Rules:**
  - ✅ Opening balances can ONLY be edited from their source forms (e.g., Customer form)
  - ❌ Cannot be manually added through the Chart of Accounts screen
  - ❌ Cannot be deleted through the Chart of Accounts screen

### 2. System-Generated Transaction Accounts (Type 2)
- **Source Type:** `system_generated`
- **Description:** Accounts generated from transaction forms (invoices, returns, etc.)
- **Examples:** Sales Tax Receivable, Sales Tax Payable, Withholding Tax accounts
- **Rules:**
  - ✅ Tax-related accounts can have their opening balances edited in the Chart of Accounts
  - ❌ Cannot be manually added through the Chart of Accounts screen
  - ❌ Cannot be deleted through the Chart of Accounts screen

### 3. User-Defined Accounts (Type 3)
- **Source Type:** `user_defined`
- **Description:** Accounts manually added by users through the Chart of Accounts screen
- **Examples:** Owner's Capital, Shop Rent, Utility Bills, Employee Salaries
- **Rules:**
  - ✅ Can be created manually through the Chart of Accounts screen
  - ✅ All fields can be edited (name, code, type, opening balance, etc.)
  - ✅ Can be deleted if they have no transactions

### 4. Built-In System Accounts (Type 4)
- **Source Type:** `built_in`
- **Description:** Default accounts required by the accounting system
- **Examples:** Cash in Hand, Retained Earnings
- **Rules:**
  - ✅ Opening balances can be edited through the Chart of Accounts screen
  - ❌ Cannot be manually added through the Chart of Accounts screen
  - ❌ Cannot be deleted through the Chart of Accounts screen

## Entity References

Form-created accounts maintain a reference to the entity that created them:

```json
"entityReference": {
  "entityType": "customer", // or "vendor", "item", "bank"
  "entityId": "ObjectId(...)" // Reference to the entity
}
```

This allows for bidirectional synchronization between the entity and its account.

## User Interface Updates

The Chart of Accounts screen has been enhanced with:

1. **Tab-based filtering** - Filter accounts by their source type
2. **Source type indicators** - Visual chips showing the source type of each account
3. **Conditional editing** - Edit buttons are disabled for form-created accounts
4. **Informative messages** - Clear instructions about which fields can be edited

## Implementation Details

### Database Schema Changes

The Account model has been updated with:

- New `sourceType` field (enum: 'form_created', 'system_generated', 'user_defined', 'built_in')
- New `entityReference` object with `entityType` and `entityId` fields
- New virtual property `isOpeningBalanceEditable` to determine if opening balance can be edited

### Migration Process

A migration script (`migrateAccountSourceTypes.js`) has been created to:

1. Update existing accounts with appropriate source types
2. Create entity references for form-created accounts
3. Ensure data consistency across the system

To run the migration:

```powershell
.\migrate-account-types.ps1
```

### API Changes

The account routes have been updated to enforce the new editing rules:

- Form-created accounts can only be edited from their source forms
- Only user-defined accounts can be deleted
- Opening balance editing is restricted based on account source type

## Best Practices

1. **Entity Management**
   - Always create and manage customers, vendors, items, and bank accounts from their respective forms
   - Their accounts will be automatically created and managed in the Chart of Accounts

2. **Custom Accounts**
   - Use the Chart of Accounts screen to create custom accounts for expenses, equity, etc.
   - Assign appropriate account types and subtypes for accurate financial reporting

3. **Opening Balances**
   - Set opening balances for customers and vendors in their respective forms
   - Set opening balances for built-in accounts like Cash in Hand directly in the Chart of Accounts

## Troubleshooting

If you encounter issues with account synchronization:

1. Check the entity reference in the account document
2. Verify that the entity exists and has the correct accountId reference
3. Use the debug endpoints (/api/accounts/debug-sync) to diagnose connection issues

---

*This documentation reflects the Chart of Accounts system as of the latest update. Please refer to the codebase for the most current implementation details.* 