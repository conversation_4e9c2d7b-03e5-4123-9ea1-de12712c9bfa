{"ast": null, "code": "import ResultPoint from '../../ResultPoint';\nvar FinderPattern = /** @class */function () {\n  function FinderPattern(value, startEnd, start, end, rowNumber) {\n    this.value = value;\n    this.startEnd = startEnd;\n    this.value = value;\n    this.startEnd = startEnd;\n    this.resultPoints = new Array();\n    this.resultPoints.push(new ResultPoint(start, rowNumber));\n    this.resultPoints.push(new ResultPoint(end, rowNumber));\n  }\n  FinderPattern.prototype.getValue = function () {\n    return this.value;\n  };\n  FinderPattern.prototype.getStartEnd = function () {\n    return this.startEnd;\n  };\n  FinderPattern.prototype.getResultPoints = function () {\n    return this.resultPoints;\n  };\n  FinderPattern.prototype.equals = function (o) {\n    if (!(o instanceof FinderPattern)) {\n      return false;\n    }\n    var that = o;\n    return this.value === that.value;\n  };\n  FinderPattern.prototype.hashCode = function () {\n    return this.value;\n  };\n  return FinderPattern;\n}();\nexport default FinderPattern;", "map": {"version": 3, "names": ["ResultPoint", "FinderPattern", "value", "startEnd", "start", "end", "rowNumber", "resultPoints", "Array", "push", "prototype", "getValue", "getStartEnd", "getResultPoints", "equals", "o", "that", "hashCode"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/FinderPattern.js"], "sourcesContent": ["import ResultPoint from '../../ResultPoint';\nvar FinderPattern = /** @class */ (function () {\n    function FinderPattern(value, startEnd, start, end, rowNumber) {\n        this.value = value;\n        this.startEnd = startEnd;\n        this.value = value;\n        this.startEnd = startEnd;\n        this.resultPoints = new Array();\n        this.resultPoints.push(new ResultPoint(start, rowNumber));\n        this.resultPoints.push(new ResultPoint(end, rowNumber));\n    }\n    FinderPattern.prototype.getValue = function () {\n        return this.value;\n    };\n    FinderPattern.prototype.getStartEnd = function () {\n        return this.startEnd;\n    };\n    FinderPattern.prototype.getResultPoints = function () {\n        return this.resultPoints;\n    };\n    FinderPattern.prototype.equals = function (o) {\n        if (!(o instanceof FinderPattern)) {\n            return false;\n        }\n        var that = o;\n        return this.value === that.value;\n    };\n    FinderPattern.prototype.hashCode = function () {\n        return this.value;\n    };\n    return FinderPattern;\n}());\nexport default FinderPattern;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,IAAIC,aAAa,GAAG,aAAe,YAAY;EAC3C,SAASA,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,GAAG,EAAEC,SAAS,EAAE;IAC3D,IAAI,CAACJ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACI,YAAY,GAAG,IAAIC,KAAK,CAAC,CAAC;IAC/B,IAAI,CAACD,YAAY,CAACE,IAAI,CAAC,IAAIT,WAAW,CAACI,KAAK,EAAEE,SAAS,CAAC,CAAC;IACzD,IAAI,CAACC,YAAY,CAACE,IAAI,CAAC,IAAIT,WAAW,CAACK,GAAG,EAAEC,SAAS,CAAC,CAAC;EAC3D;EACAL,aAAa,CAACS,SAAS,CAACC,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACT,KAAK;EACrB,CAAC;EACDD,aAAa,CAACS,SAAS,CAACE,WAAW,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACT,QAAQ;EACxB,CAAC;EACDF,aAAa,CAACS,SAAS,CAACG,eAAe,GAAG,YAAY;IAClD,OAAO,IAAI,CAACN,YAAY;EAC5B,CAAC;EACDN,aAAa,CAACS,SAAS,CAACI,MAAM,GAAG,UAAUC,CAAC,EAAE;IAC1C,IAAI,EAAEA,CAAC,YAAYd,aAAa,CAAC,EAAE;MAC/B,OAAO,KAAK;IAChB;IACA,IAAIe,IAAI,GAAGD,CAAC;IACZ,OAAO,IAAI,CAACb,KAAK,KAAKc,IAAI,CAACd,KAAK;EACpC,CAAC;EACDD,aAAa,CAACS,SAAS,CAACO,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACf,KAAK;EACrB,CAAC;EACD,OAAOD,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}