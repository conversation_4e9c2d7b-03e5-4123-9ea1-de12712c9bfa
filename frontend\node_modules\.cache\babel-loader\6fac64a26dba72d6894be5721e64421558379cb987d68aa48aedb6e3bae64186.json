{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _constants = require('./constants');\nvar _encoder = require('./encoder');\nvar _encoder2 = _interopRequireDefault(_encoder);\nvar _Barcode2 = require('../Barcode');\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\n\n// Base class for EAN8 & EAN13\nvar EAN = function (_Barcode) {\n  _inherits(EAN, _Barcode);\n  function EAN(data, options) {\n    _classCallCheck(this, EAN);\n\n    // Make sure the font is not bigger than the space between the guard bars\n    var _this = _possibleConstructorReturn(this, (EAN.__proto__ || Object.getPrototypeOf(EAN)).call(this, data, options));\n    _this.fontSize = !options.flat && options.fontSize > options.width * 10 ? options.width * 10 : options.fontSize;\n\n    // Make the guard bars go down half the way of the text\n    _this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;\n    return _this;\n  }\n  _createClass(EAN, [{\n    key: 'encode',\n    value: function encode() {\n      return this.options.flat ? this.encodeFlat() : this.encodeGuarded();\n    }\n  }, {\n    key: 'leftText',\n    value: function leftText(from, to) {\n      return this.text.substr(from, to);\n    }\n  }, {\n    key: 'leftEncode',\n    value: function leftEncode(data, structure) {\n      return (0, _encoder2.default)(data, structure);\n    }\n  }, {\n    key: 'rightText',\n    value: function rightText(from, to) {\n      return this.text.substr(from, to);\n    }\n  }, {\n    key: 'rightEncode',\n    value: function rightEncode(data, structure) {\n      return (0, _encoder2.default)(data, structure);\n    }\n  }, {\n    key: 'encodeGuarded',\n    value: function encodeGuarded() {\n      var textOptions = {\n        fontSize: this.fontSize\n      };\n      var guardOptions = {\n        height: this.guardHeight\n      };\n      return [{\n        data: _constants.SIDE_BIN,\n        options: guardOptions\n      }, {\n        data: this.leftEncode(),\n        text: this.leftText(),\n        options: textOptions\n      }, {\n        data: _constants.MIDDLE_BIN,\n        options: guardOptions\n      }, {\n        data: this.rightEncode(),\n        text: this.rightText(),\n        options: textOptions\n      }, {\n        data: _constants.SIDE_BIN,\n        options: guardOptions\n      }];\n    }\n  }, {\n    key: 'encodeFlat',\n    value: function encodeFlat() {\n      var data = [_constants.SIDE_BIN, this.leftEncode(), _constants.MIDDLE_BIN, this.rightEncode(), _constants.SIDE_BIN];\n      return {\n        data: data.join(''),\n        text: this.text\n      };\n    }\n  }]);\n  return EAN;\n}(_Barcode3.default);\nexports.default = EAN;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_constants", "require", "_encoder", "_encoder2", "_interopRequireDefault", "_Barcode2", "_Barcode3", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "call", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "EAN", "_Barcode", "data", "options", "_this", "getPrototypeOf", "fontSize", "flat", "width", "guardHeight", "height", "textMargin", "encode", "encodeFlat", "encodeGuarded", "leftText", "from", "to", "text", "substr", "leftEncode", "structure", "rightText", "rightEncode", "textOptions", "guardOptions", "SIDE_BIN", "MIDDLE_BIN", "join"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/EAN_UPC/EAN.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _constants = require('./constants');\n\nvar _encoder = require('./encoder');\n\nvar _encoder2 = _interopRequireDefault(_encoder);\n\nvar _Barcode2 = require('../Barcode');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n// Base class for EAN8 & EAN13\nvar EAN = function (_Barcode) {\n\t_inherits(EAN, _Barcode);\n\n\tfunction EAN(data, options) {\n\t\t_classCallCheck(this, EAN);\n\n\t\t// Make sure the font is not bigger than the space between the guard bars\n\t\tvar _this = _possibleConstructorReturn(this, (EAN.__proto__ || Object.getPrototypeOf(EAN)).call(this, data, options));\n\n\t\t_this.fontSize = !options.flat && options.fontSize > options.width * 10 ? options.width * 10 : options.fontSize;\n\n\t\t// Make the guard bars go down half the way of the text\n\t\t_this.guardHeight = options.height + _this.fontSize / 2 + options.textMargin;\n\t\treturn _this;\n\t}\n\n\t_createClass(EAN, [{\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\treturn this.options.flat ? this.encodeFlat() : this.encodeGuarded();\n\t\t}\n\t}, {\n\t\tkey: 'leftText',\n\t\tvalue: function leftText(from, to) {\n\t\t\treturn this.text.substr(from, to);\n\t\t}\n\t}, {\n\t\tkey: 'leftEncode',\n\t\tvalue: function leftEncode(data, structure) {\n\t\t\treturn (0, _encoder2.default)(data, structure);\n\t\t}\n\t}, {\n\t\tkey: 'rightText',\n\t\tvalue: function rightText(from, to) {\n\t\t\treturn this.text.substr(from, to);\n\t\t}\n\t}, {\n\t\tkey: 'rightEncode',\n\t\tvalue: function rightEncode(data, structure) {\n\t\t\treturn (0, _encoder2.default)(data, structure);\n\t\t}\n\t}, {\n\t\tkey: 'encodeGuarded',\n\t\tvalue: function encodeGuarded() {\n\t\t\tvar textOptions = { fontSize: this.fontSize };\n\t\t\tvar guardOptions = { height: this.guardHeight };\n\n\t\t\treturn [{ data: _constants.SIDE_BIN, options: guardOptions }, { data: this.leftEncode(), text: this.leftText(), options: textOptions }, { data: _constants.MIDDLE_BIN, options: guardOptions }, { data: this.rightEncode(), text: this.rightText(), options: textOptions }, { data: _constants.SIDE_BIN, options: guardOptions }];\n\t\t}\n\t}, {\n\t\tkey: 'encodeFlat',\n\t\tvalue: function encodeFlat() {\n\t\t\tvar data = [_constants.SIDE_BIN, this.leftEncode(), _constants.MIDDLE_BIN, this.rightEncode(), _constants.SIDE_BIN];\n\n\t\t\treturn {\n\t\t\t\tdata: data.join(''),\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}]);\n\n\treturn EAN;\n}(_Barcode3.default);\n\nexports.default = EAN;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEvC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEnC,IAAIE,SAAS,GAAGC,sBAAsB,CAACF,QAAQ,CAAC;AAEhD,IAAIG,SAAS,GAAGJ,OAAO,CAAC,YAAY,CAAC;AAErC,IAAIK,SAAS,GAAGF,sBAAsB,CAACC,SAAS,CAAC;AAEjD,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEf,WAAW,EAAE;EAAE,IAAI,EAAEe,QAAQ,YAAYf,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIgB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAI,CAACD,IAAI,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGD,IAAI;AAAE;AAE/O,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIP,SAAS,CAAC,0DAA0D,GAAG,OAAOO,UAAU,CAAC;EAAE;EAAED,QAAQ,CAACnB,SAAS,GAAGlB,MAAM,CAACuC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACpB,SAAS,EAAE;IAAEsB,WAAW,EAAE;MAAErC,KAAK,EAAEkC,QAAQ;MAAE1B,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAI0B,UAAU,EAAEtC,MAAM,CAACyC,cAAc,GAAGzC,MAAM,CAACyC,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AAAE;;AAE7e;AACA,IAAIK,GAAG,GAAG,UAAUC,QAAQ,EAAE;EAC7BR,SAAS,CAACO,GAAG,EAAEC,QAAQ,CAAC;EAExB,SAASD,GAAGA,CAACE,IAAI,EAAEC,OAAO,EAAE;IAC3BjB,eAAe,CAAC,IAAI,EAAEc,GAAG,CAAC;;IAE1B;IACA,IAAII,KAAK,GAAGf,0BAA0B,CAAC,IAAI,EAAE,CAACW,GAAG,CAACD,SAAS,IAAI1C,MAAM,CAACgD,cAAc,CAACL,GAAG,CAAC,EAAET,IAAI,CAAC,IAAI,EAAEW,IAAI,EAAEC,OAAO,CAAC,CAAC;IAErHC,KAAK,CAACE,QAAQ,GAAG,CAACH,OAAO,CAACI,IAAI,IAAIJ,OAAO,CAACG,QAAQ,GAAGH,OAAO,CAACK,KAAK,GAAG,EAAE,GAAGL,OAAO,CAACK,KAAK,GAAG,EAAE,GAAGL,OAAO,CAACG,QAAQ;;IAE/G;IACAF,KAAK,CAACK,WAAW,GAAGN,OAAO,CAACO,MAAM,GAAGN,KAAK,CAACE,QAAQ,GAAG,CAAC,GAAGH,OAAO,CAACQ,UAAU;IAC5E,OAAOP,KAAK;EACb;EAEA3C,YAAY,CAACuC,GAAG,EAAE,CAAC;IAClB7B,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAASoD,MAAMA,CAAA,EAAG;MACxB,OAAO,IAAI,CAACT,OAAO,CAACI,IAAI,GAAG,IAAI,CAACM,UAAU,CAAC,CAAC,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC;IACpE;EACD,CAAC,EAAE;IACF3C,GAAG,EAAE,UAAU;IACfX,KAAK,EAAE,SAASuD,QAAQA,CAACC,IAAI,EAAEC,EAAE,EAAE;MAClC,OAAO,IAAI,CAACC,IAAI,CAACC,MAAM,CAACH,IAAI,EAAEC,EAAE,CAAC;IAClC;EACD,CAAC,EAAE;IACF9C,GAAG,EAAE,YAAY;IACjBX,KAAK,EAAE,SAAS4D,UAAUA,CAAClB,IAAI,EAAEmB,SAAS,EAAE;MAC3C,OAAO,CAAC,CAAC,EAAE1C,SAAS,CAACM,OAAO,EAAEiB,IAAI,EAAEmB,SAAS,CAAC;IAC/C;EACD,CAAC,EAAE;IACFlD,GAAG,EAAE,WAAW;IAChBX,KAAK,EAAE,SAAS8D,SAASA,CAACN,IAAI,EAAEC,EAAE,EAAE;MACnC,OAAO,IAAI,CAACC,IAAI,CAACC,MAAM,CAACH,IAAI,EAAEC,EAAE,CAAC;IAClC;EACD,CAAC,EAAE;IACF9C,GAAG,EAAE,aAAa;IAClBX,KAAK,EAAE,SAAS+D,WAAWA,CAACrB,IAAI,EAAEmB,SAAS,EAAE;MAC5C,OAAO,CAAC,CAAC,EAAE1C,SAAS,CAACM,OAAO,EAAEiB,IAAI,EAAEmB,SAAS,CAAC;IAC/C;EACD,CAAC,EAAE;IACFlD,GAAG,EAAE,eAAe;IACpBX,KAAK,EAAE,SAASsD,aAAaA,CAAA,EAAG;MAC/B,IAAIU,WAAW,GAAG;QAAElB,QAAQ,EAAE,IAAI,CAACA;MAAS,CAAC;MAC7C,IAAImB,YAAY,GAAG;QAAEf,MAAM,EAAE,IAAI,CAACD;MAAY,CAAC;MAE/C,OAAO,CAAC;QAAEP,IAAI,EAAE1B,UAAU,CAACkD,QAAQ;QAAEvB,OAAO,EAAEsB;MAAa,CAAC,EAAE;QAAEvB,IAAI,EAAE,IAAI,CAACkB,UAAU,CAAC,CAAC;QAAEF,IAAI,EAAE,IAAI,CAACH,QAAQ,CAAC,CAAC;QAAEZ,OAAO,EAAEqB;MAAY,CAAC,EAAE;QAAEtB,IAAI,EAAE1B,UAAU,CAACmD,UAAU;QAAExB,OAAO,EAAEsB;MAAa,CAAC,EAAE;QAAEvB,IAAI,EAAE,IAAI,CAACqB,WAAW,CAAC,CAAC;QAAEL,IAAI,EAAE,IAAI,CAACI,SAAS,CAAC,CAAC;QAAEnB,OAAO,EAAEqB;MAAY,CAAC,EAAE;QAAEtB,IAAI,EAAE1B,UAAU,CAACkD,QAAQ;QAAEvB,OAAO,EAAEsB;MAAa,CAAC,CAAC;IAClU;EACD,CAAC,EAAE;IACFtD,GAAG,EAAE,YAAY;IACjBX,KAAK,EAAE,SAASqD,UAAUA,CAAA,EAAG;MAC5B,IAAIX,IAAI,GAAG,CAAC1B,UAAU,CAACkD,QAAQ,EAAE,IAAI,CAACN,UAAU,CAAC,CAAC,EAAE5C,UAAU,CAACmD,UAAU,EAAE,IAAI,CAACJ,WAAW,CAAC,CAAC,EAAE/C,UAAU,CAACkD,QAAQ,CAAC;MAEnH,OAAO;QACNxB,IAAI,EAAEA,IAAI,CAAC0B,IAAI,CAAC,EAAE,CAAC;QACnBV,IAAI,EAAE,IAAI,CAACA;MACZ,CAAC;IACF;EACD,CAAC,CAAC,CAAC;EAEH,OAAOlB,GAAG;AACX,CAAC,CAAClB,SAAS,CAACG,OAAO,CAAC;AAEpB1B,OAAO,CAAC0B,OAAO,GAAGe,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}