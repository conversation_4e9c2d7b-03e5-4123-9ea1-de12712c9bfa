{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport StringBuilder from './util/StringBuilder';\nimport UnsupportedOperationException from './UnsupportedOperationException';\n/*namespace com.google.zxing {*/\n/**\n * The purpose of this class hierarchy is to abstract different bitmap implementations across\n * platforms into a standard interface for requesting greyscale luminance values. The interface\n * only provides immutable methods; therefore crop and rotation create copies. This is to ensure\n * that one Reader does not modify the original luminance source and leave it in an unknown state\n * for other Readers in the chain.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar LuminanceSource = /** @class */function () {\n  function LuminanceSource(width /*int*/, height /*int*/) {\n    this.width = width;\n    this.height = height;\n  }\n  /**\n   * @return The width of the bitmap.\n   */\n  LuminanceSource.prototype.getWidth = function () {\n    return this.width;\n  };\n  /**\n   * @return The height of the bitmap.\n   */\n  LuminanceSource.prototype.getHeight = function () {\n    return this.height;\n  };\n  /**\n   * @return Whether this subclass supports cropping.\n   */\n  LuminanceSource.prototype.isCropSupported = function () {\n    return false;\n  };\n  /**\n   * Returns a new object with cropped image data. Implementations may keep a reference to the\n   * original data rather than a copy. Only callable if isCropSupported() is true.\n   *\n   * @param left The left coordinate, which must be in [0,getWidth())\n   * @param top The top coordinate, which must be in [0,getHeight())\n   * @param width The width of the rectangle to crop.\n   * @param height The height of the rectangle to crop.\n   * @return A cropped version of this object.\n   */\n  LuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n    throw new UnsupportedOperationException('This luminance source does not support cropping.');\n  };\n  /**\n   * @return Whether this subclass supports counter-clockwise rotation.\n   */\n  LuminanceSource.prototype.isRotateSupported = function () {\n    return false;\n  };\n  /**\n   * Returns a new object with rotated image data by 90 degrees counterclockwise.\n   * Only callable if {@link #isRotateSupported()} is true.\n   *\n   * @return A rotated version of this object.\n   */\n  LuminanceSource.prototype.rotateCounterClockwise = function () {\n    throw new UnsupportedOperationException('This luminance source does not support rotation by 90 degrees.');\n  };\n  /**\n   * Returns a new object with rotated image data by 45 degrees counterclockwise.\n   * Only callable if {@link #isRotateSupported()} is true.\n   *\n   * @return A rotated version of this object.\n   */\n  LuminanceSource.prototype.rotateCounterClockwise45 = function () {\n    throw new UnsupportedOperationException('This luminance source does not support rotation by 45 degrees.');\n  };\n  /*@Override*/\n  LuminanceSource.prototype.toString = function () {\n    var row = new Uint8ClampedArray(this.width);\n    var result = new StringBuilder();\n    for (var y = 0; y < this.height; y++) {\n      var sourceRow = this.getRow(y, row);\n      for (var x = 0; x < this.width; x++) {\n        var luminance = sourceRow[x] & 0xFF;\n        var c = void 0;\n        if (luminance < 0x40) {\n          c = '#';\n        } else if (luminance < 0x80) {\n          c = '+';\n        } else if (luminance < 0xC0) {\n          c = '.';\n        } else {\n          c = ' ';\n        }\n        result.append(c);\n      }\n      result.append('\\n');\n    }\n    return result.toString();\n  };\n  return LuminanceSource;\n}();\nexport default LuminanceSource;", "map": {"version": 3, "names": ["StringBuilder", "UnsupportedOperationException", "LuminanceSource", "width", "height", "prototype", "getWidth", "getHeight", "isCropSupported", "crop", "left", "top", "isRotateSupported", "rotateCounterClockwise", "rotateCounterClockwise45", "toString", "row", "Uint8ClampedArray", "result", "y", "sourceRow", "getRow", "x", "luminance", "c", "append"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/LuminanceSource.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport StringBuilder from './util/StringBuilder';\nimport UnsupportedOperationException from './UnsupportedOperationException';\n/*namespace com.google.zxing {*/\n/**\n * The purpose of this class hierarchy is to abstract different bitmap implementations across\n * platforms into a standard interface for requesting greyscale luminance values. The interface\n * only provides immutable methods; therefore crop and rotation create copies. This is to ensure\n * that one Reader does not modify the original luminance source and leave it in an unknown state\n * for other Readers in the chain.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar LuminanceSource = /** @class */ (function () {\n    function LuminanceSource(width /*int*/, height /*int*/) {\n        this.width = width;\n        this.height = height;\n    }\n    /**\n     * @return The width of the bitmap.\n     */\n    LuminanceSource.prototype.getWidth = function () {\n        return this.width;\n    };\n    /**\n     * @return The height of the bitmap.\n     */\n    LuminanceSource.prototype.getHeight = function () {\n        return this.height;\n    };\n    /**\n     * @return Whether this subclass supports cropping.\n     */\n    LuminanceSource.prototype.isCropSupported = function () {\n        return false;\n    };\n    /**\n     * Returns a new object with cropped image data. Implementations may keep a reference to the\n     * original data rather than a copy. Only callable if isCropSupported() is true.\n     *\n     * @param left The left coordinate, which must be in [0,getWidth())\n     * @param top The top coordinate, which must be in [0,getHeight())\n     * @param width The width of the rectangle to crop.\n     * @param height The height of the rectangle to crop.\n     * @return A cropped version of this object.\n     */\n    LuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        throw new UnsupportedOperationException('This luminance source does not support cropping.');\n    };\n    /**\n     * @return Whether this subclass supports counter-clockwise rotation.\n     */\n    LuminanceSource.prototype.isRotateSupported = function () {\n        return false;\n    };\n    /**\n     * Returns a new object with rotated image data by 90 degrees counterclockwise.\n     * Only callable if {@link #isRotateSupported()} is true.\n     *\n     * @return A rotated version of this object.\n     */\n    LuminanceSource.prototype.rotateCounterClockwise = function () {\n        throw new UnsupportedOperationException('This luminance source does not support rotation by 90 degrees.');\n    };\n    /**\n     * Returns a new object with rotated image data by 45 degrees counterclockwise.\n     * Only callable if {@link #isRotateSupported()} is true.\n     *\n     * @return A rotated version of this object.\n     */\n    LuminanceSource.prototype.rotateCounterClockwise45 = function () {\n        throw new UnsupportedOperationException('This luminance source does not support rotation by 45 degrees.');\n    };\n    /*@Override*/\n    LuminanceSource.prototype.toString = function () {\n        var row = new Uint8ClampedArray(this.width);\n        var result = new StringBuilder();\n        for (var y = 0; y < this.height; y++) {\n            var sourceRow = this.getRow(y, row);\n            for (var x = 0; x < this.width; x++) {\n                var luminance = sourceRow[x] & 0xFF;\n                var c = void 0;\n                if (luminance < 0x40) {\n                    c = '#';\n                }\n                else if (luminance < 0x80) {\n                    c = '+';\n                }\n                else if (luminance < 0xC0) {\n                    c = '.';\n                }\n                else {\n                    c = ' ';\n                }\n                result.append(c);\n            }\n            result.append('\\n');\n        }\n        return result.toString();\n    };\n    return LuminanceSource;\n}());\nexport default LuminanceSource;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,sBAAsB;AAChD,OAAOC,6BAA6B,MAAM,iCAAiC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C,SAASA,eAAeA,CAACC,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IACpD,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;EACIF,eAAe,CAACG,SAAS,CAACC,QAAQ,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACH,KAAK;EACrB,CAAC;EACD;AACJ;AACA;EACID,eAAe,CAACG,SAAS,CAACE,SAAS,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACH,MAAM;EACtB,CAAC;EACD;AACJ;AACA;EACIF,eAAe,CAACG,SAAS,CAACG,eAAe,GAAG,YAAY;IACpD,OAAO,KAAK;EAChB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,eAAe,CAACG,SAAS,CAACI,IAAI,GAAG,UAAUC,IAAI,CAAC,SAASC,GAAG,CAAC,SAASR,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IACjG,MAAM,IAAIH,6BAA6B,CAAC,kDAAkD,CAAC;EAC/F,CAAC;EACD;AACJ;AACA;EACIC,eAAe,CAACG,SAAS,CAACO,iBAAiB,GAAG,YAAY;IACtD,OAAO,KAAK;EAChB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIV,eAAe,CAACG,SAAS,CAACQ,sBAAsB,GAAG,YAAY;IAC3D,MAAM,IAAIZ,6BAA6B,CAAC,gEAAgE,CAAC;EAC7G,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAe,CAACG,SAAS,CAACS,wBAAwB,GAAG,YAAY;IAC7D,MAAM,IAAIb,6BAA6B,CAAC,gEAAgE,CAAC;EAC7G,CAAC;EACD;EACAC,eAAe,CAACG,SAAS,CAACU,QAAQ,GAAG,YAAY;IAC7C,IAAIC,GAAG,GAAG,IAAIC,iBAAiB,CAAC,IAAI,CAACd,KAAK,CAAC;IAC3C,IAAIe,MAAM,GAAG,IAAIlB,aAAa,CAAC,CAAC;IAChC,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACf,MAAM,EAAEe,CAAC,EAAE,EAAE;MAClC,IAAIC,SAAS,GAAG,IAAI,CAACC,MAAM,CAACF,CAAC,EAAEH,GAAG,CAAC;MACnC,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACnB,KAAK,EAAEmB,CAAC,EAAE,EAAE;QACjC,IAAIC,SAAS,GAAGH,SAAS,CAACE,CAAC,CAAC,GAAG,IAAI;QACnC,IAAIE,CAAC,GAAG,KAAK,CAAC;QACd,IAAID,SAAS,GAAG,IAAI,EAAE;UAClBC,CAAC,GAAG,GAAG;QACX,CAAC,MACI,IAAID,SAAS,GAAG,IAAI,EAAE;UACvBC,CAAC,GAAG,GAAG;QACX,CAAC,MACI,IAAID,SAAS,GAAG,IAAI,EAAE;UACvBC,CAAC,GAAG,GAAG;QACX,CAAC,MACI;UACDA,CAAC,GAAG,GAAG;QACX;QACAN,MAAM,CAACO,MAAM,CAACD,CAAC,CAAC;MACpB;MACAN,MAAM,CAACO,MAAM,CAAC,IAAI,CAAC;IACvB;IACA,OAAOP,MAAM,CAACH,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACD,OAAOb,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}