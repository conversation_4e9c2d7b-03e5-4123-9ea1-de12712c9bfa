{"ast": null, "code": "var DecodedObject = /** @class */function () {\n  function DecodedObject(newPosition) {\n    this.newPosition = newPosition;\n  }\n  DecodedObject.prototype.getNewPosition = function () {\n    return this.newPosition;\n  };\n  return DecodedObject;\n}();\nexport default DecodedObject;", "map": {"version": 3, "names": ["DecodedObject", "newPosition", "prototype", "getNewPosition"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/DecodedObject.js"], "sourcesContent": ["var DecodedObject = /** @class */ (function () {\n    function DecodedObject(newPosition) {\n        this.newPosition = newPosition;\n    }\n    DecodedObject.prototype.getNewPosition = function () {\n        return this.newPosition;\n    };\n    return DecodedObject;\n}());\nexport default DecodedObject;\n"], "mappings": "AAAA,IAAIA,aAAa,GAAG,aAAe,YAAY;EAC3C,SAASA,aAAaA,CAACC,WAAW,EAAE;IAChC,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACAD,aAAa,CAACE,SAAS,CAACC,cAAc,GAAG,YAAY;IACjD,OAAO,IAAI,CAACF,WAAW;EAC3B,CAAC;EACD,OAAOD,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}