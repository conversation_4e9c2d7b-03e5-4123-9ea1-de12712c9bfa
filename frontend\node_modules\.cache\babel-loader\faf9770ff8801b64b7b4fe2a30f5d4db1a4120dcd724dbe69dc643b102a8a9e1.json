{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport AI01weightDecoder from './AI01weightDecoder';\nimport NotFoundException from '../../../../NotFoundException';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI013x0x1xDecoder = /** @class */function (_super) {\n  __extends(AI013x0x1xDecoder, _super);\n  function AI013x0x1xDecoder(information, firstAIdigits, dateCode) {\n    var _this = _super.call(this, information) || this;\n    _this.dateCode = dateCode;\n    _this.firstAIdigits = firstAIdigits;\n    return _this;\n  }\n  AI013x0x1xDecoder.prototype.parseInformation = function () {\n    if (this.getInformation().getSize() !== AI013x0x1xDecoder.HEADER_SIZE + AI013x0x1xDecoder.GTIN_SIZE + AI013x0x1xDecoder.WEIGHT_SIZE + AI013x0x1xDecoder.DATE_SIZE) {\n      throw new NotFoundException();\n    }\n    var buf = new StringBuilder();\n    this.encodeCompressedGtin(buf, AI013x0x1xDecoder.HEADER_SIZE);\n    this.encodeCompressedWeight(buf, AI013x0x1xDecoder.HEADER_SIZE + AI013x0x1xDecoder.GTIN_SIZE, AI013x0x1xDecoder.WEIGHT_SIZE);\n    this.encodeCompressedDate(buf, AI013x0x1xDecoder.HEADER_SIZE + AI013x0x1xDecoder.GTIN_SIZE + AI013x0x1xDecoder.WEIGHT_SIZE);\n    return buf.toString();\n  };\n  AI013x0x1xDecoder.prototype.encodeCompressedDate = function (buf, currentPos) {\n    var numericDate = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos, AI013x0x1xDecoder.DATE_SIZE);\n    if (numericDate === 38400) {\n      return;\n    }\n    buf.append('(');\n    buf.append(this.dateCode);\n    buf.append(')');\n    var day = numericDate % 32;\n    numericDate /= 32;\n    var month = numericDate % 12 + 1;\n    numericDate /= 12;\n    var year = numericDate;\n    if (year / 10 === 0) {\n      buf.append('0');\n    }\n    buf.append(year);\n    if (month / 10 === 0) {\n      buf.append('0');\n    }\n    buf.append(month);\n    if (day / 10 === 0) {\n      buf.append('0');\n    }\n    buf.append(day);\n  };\n  AI013x0x1xDecoder.prototype.addWeightCode = function (buf, weight) {\n    buf.append('(');\n    buf.append(this.firstAIdigits);\n    buf.append(weight / 100000);\n    buf.append(')');\n  };\n  AI013x0x1xDecoder.prototype.checkWeight = function (weight) {\n    return weight % 100000;\n  };\n  AI013x0x1xDecoder.HEADER_SIZE = 7 + 1;\n  AI013x0x1xDecoder.WEIGHT_SIZE = 20;\n  AI013x0x1xDecoder.DATE_SIZE = 16;\n  return AI013x0x1xDecoder;\n}(AI01weightDecoder);\nexport default AI013x0x1xDecoder;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "AI01weightDecoder", "NotFoundException", "StringBuilder", "AI013x0x1xDecoder", "_super", "information", "firstAIdigits", "dateCode", "_this", "call", "parseInformation", "getInformation", "getSize", "HEADER_SIZE", "GTIN_SIZE", "WEIGHT_SIZE", "DATE_SIZE", "buf", "encodeCompressedGtin", "encodeCompressedWeight", "encodeCompressedDate", "toString", "currentPos", "numericDate", "getGeneralDecoder", "extractNumericValueFromBitArray", "append", "day", "month", "year", "addWeightCode", "weight", "checkWeight"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI013x0x1xDecoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AI01weightDecoder from './AI01weightDecoder';\nimport NotFoundException from '../../../../NotFoundException';\nimport StringBuilder from '../../../../util/StringBuilder';\nvar AI013x0x1xDecoder = /** @class */ (function (_super) {\n    __extends(AI013x0x1xDecoder, _super);\n    function AI013x0x1xDecoder(information, firstAIdigits, dateCode) {\n        var _this = _super.call(this, information) || this;\n        _this.dateCode = dateCode;\n        _this.firstAIdigits = firstAIdigits;\n        return _this;\n    }\n    AI013x0x1xDecoder.prototype.parseInformation = function () {\n        if (this.getInformation().getSize() !==\n            AI013x0x1xDecoder.HEADER_SIZE +\n                AI013x0x1xDecoder.GTIN_SIZE +\n                AI013x0x1xDecoder.WEIGHT_SIZE +\n                AI013x0x1xDecoder.DATE_SIZE) {\n            throw new NotFoundException();\n        }\n        var buf = new StringBuilder();\n        this.encodeCompressedGtin(buf, AI013x0x1xDecoder.HEADER_SIZE);\n        this.encodeCompressedWeight(buf, AI013x0x1xDecoder.HEADER_SIZE + AI013x0x1xDecoder.GTIN_SIZE, AI013x0x1xDecoder.WEIGHT_SIZE);\n        this.encodeCompressedDate(buf, AI013x0x1xDecoder.HEADER_SIZE +\n            AI013x0x1xDecoder.GTIN_SIZE +\n            AI013x0x1xDecoder.WEIGHT_SIZE);\n        return buf.toString();\n    };\n    AI013x0x1xDecoder.prototype.encodeCompressedDate = function (buf, currentPos) {\n        var numericDate = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos, AI013x0x1xDecoder.DATE_SIZE);\n        if (numericDate === 38400) {\n            return;\n        }\n        buf.append('(');\n        buf.append(this.dateCode);\n        buf.append(')');\n        var day = numericDate % 32;\n        numericDate /= 32;\n        var month = (numericDate % 12) + 1;\n        numericDate /= 12;\n        var year = numericDate;\n        if (year / 10 === 0) {\n            buf.append('0');\n        }\n        buf.append(year);\n        if (month / 10 === 0) {\n            buf.append('0');\n        }\n        buf.append(month);\n        if (day / 10 === 0) {\n            buf.append('0');\n        }\n        buf.append(day);\n    };\n    AI013x0x1xDecoder.prototype.addWeightCode = function (buf, weight) {\n        buf.append('(');\n        buf.append(this.firstAIdigits);\n        buf.append(weight / 100000);\n        buf.append(')');\n    };\n    AI013x0x1xDecoder.prototype.checkWeight = function (weight) {\n        return weight % 100000;\n    };\n    AI013x0x1xDecoder.HEADER_SIZE = 7 + 1;\n    AI013x0x1xDecoder.WEIGHT_SIZE = 20;\n    AI013x0x1xDecoder.DATE_SIZE = 16;\n    return AI013x0x1xDecoder;\n}(AI01weightDecoder));\nexport default AI013x0x1xDecoder;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,IAAIC,iBAAiB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACrDlB,SAAS,CAACiB,iBAAiB,EAAEC,MAAM,CAAC;EACpC,SAASD,iBAAiBA,CAACE,WAAW,EAAEC,aAAa,EAAEC,QAAQ,EAAE;IAC7D,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,WAAW,CAAC,IAAI,IAAI;IAClDG,KAAK,CAACD,QAAQ,GAAGA,QAAQ;IACzBC,KAAK,CAACF,aAAa,GAAGA,aAAa;IACnC,OAAOE,KAAK;EAChB;EACAL,iBAAiB,CAACL,SAAS,CAACY,gBAAgB,GAAG,YAAY;IACvD,IAAI,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,KAC/BT,iBAAiB,CAACU,WAAW,GACzBV,iBAAiB,CAACW,SAAS,GAC3BX,iBAAiB,CAACY,WAAW,GAC7BZ,iBAAiB,CAACa,SAAS,EAAE;MACjC,MAAM,IAAIf,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAIgB,GAAG,GAAG,IAAIf,aAAa,CAAC,CAAC;IAC7B,IAAI,CAACgB,oBAAoB,CAACD,GAAG,EAAEd,iBAAiB,CAACU,WAAW,CAAC;IAC7D,IAAI,CAACM,sBAAsB,CAACF,GAAG,EAAEd,iBAAiB,CAACU,WAAW,GAAGV,iBAAiB,CAACW,SAAS,EAAEX,iBAAiB,CAACY,WAAW,CAAC;IAC5H,IAAI,CAACK,oBAAoB,CAACH,GAAG,EAAEd,iBAAiB,CAACU,WAAW,GACxDV,iBAAiB,CAACW,SAAS,GAC3BX,iBAAiB,CAACY,WAAW,CAAC;IAClC,OAAOE,GAAG,CAACI,QAAQ,CAAC,CAAC;EACzB,CAAC;EACDlB,iBAAiB,CAACL,SAAS,CAACsB,oBAAoB,GAAG,UAAUH,GAAG,EAAEK,UAAU,EAAE;IAC1E,IAAIC,WAAW,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAACC,+BAA+B,CAACH,UAAU,EAAEnB,iBAAiB,CAACa,SAAS,CAAC;IACnH,IAAIO,WAAW,KAAK,KAAK,EAAE;MACvB;IACJ;IACAN,GAAG,CAACS,MAAM,CAAC,GAAG,CAAC;IACfT,GAAG,CAACS,MAAM,CAAC,IAAI,CAACnB,QAAQ,CAAC;IACzBU,GAAG,CAACS,MAAM,CAAC,GAAG,CAAC;IACf,IAAIC,GAAG,GAAGJ,WAAW,GAAG,EAAE;IAC1BA,WAAW,IAAI,EAAE;IACjB,IAAIK,KAAK,GAAIL,WAAW,GAAG,EAAE,GAAI,CAAC;IAClCA,WAAW,IAAI,EAAE;IACjB,IAAIM,IAAI,GAAGN,WAAW;IACtB,IAAIM,IAAI,GAAG,EAAE,KAAK,CAAC,EAAE;MACjBZ,GAAG,CAACS,MAAM,CAAC,GAAG,CAAC;IACnB;IACAT,GAAG,CAACS,MAAM,CAACG,IAAI,CAAC;IAChB,IAAID,KAAK,GAAG,EAAE,KAAK,CAAC,EAAE;MAClBX,GAAG,CAACS,MAAM,CAAC,GAAG,CAAC;IACnB;IACAT,GAAG,CAACS,MAAM,CAACE,KAAK,CAAC;IACjB,IAAID,GAAG,GAAG,EAAE,KAAK,CAAC,EAAE;MAChBV,GAAG,CAACS,MAAM,CAAC,GAAG,CAAC;IACnB;IACAT,GAAG,CAACS,MAAM,CAACC,GAAG,CAAC;EACnB,CAAC;EACDxB,iBAAiB,CAACL,SAAS,CAACgC,aAAa,GAAG,UAAUb,GAAG,EAAEc,MAAM,EAAE;IAC/Dd,GAAG,CAACS,MAAM,CAAC,GAAG,CAAC;IACfT,GAAG,CAACS,MAAM,CAAC,IAAI,CAACpB,aAAa,CAAC;IAC9BW,GAAG,CAACS,MAAM,CAACK,MAAM,GAAG,MAAM,CAAC;IAC3Bd,GAAG,CAACS,MAAM,CAAC,GAAG,CAAC;EACnB,CAAC;EACDvB,iBAAiB,CAACL,SAAS,CAACkC,WAAW,GAAG,UAAUD,MAAM,EAAE;IACxD,OAAOA,MAAM,GAAG,MAAM;EAC1B,CAAC;EACD5B,iBAAiB,CAACU,WAAW,GAAG,CAAC,GAAG,CAAC;EACrCV,iBAAiB,CAACY,WAAW,GAAG,EAAE;EAClCZ,iBAAiB,CAACa,SAAS,GAAG,EAAE;EAChC,OAAOb,iBAAiB;AAC5B,CAAC,CAACH,iBAAiB,CAAE;AACrB,eAAeG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}