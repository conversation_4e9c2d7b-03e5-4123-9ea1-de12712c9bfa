{"ast": null, "code": "import { ASCIIEncoder } from './ASCIIEncoder';\nimport { Base256Encoder } from './Base256Encoder';\nimport { C40Encoder } from './C40Encoder';\nimport DefaultPlacement from './DefaultPlacement';\nimport { EdifactEncoder } from './EdifactEncoder';\nimport { EncoderContext } from './EncoderContext';\nimport ErrorCorrection from './ErrorCorrection';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { MinimalEncoder } from './MinimalEncoder';\nimport SymbolInfo from './SymbolInfo';\nimport { TextEncoder } from './TextEncoder';\nimport { X12Encoder } from './X12Encoder';\nexport { ASCIIEncoder, Base256Encoder, C40Encoder, EdifactEncoder, EncoderContext, ErrorCorrection, DefaultPlacement, HighLevelEncoder, MinimalEncoder, SymbolInfo, TextEncoder, X12Encoder };", "map": {"version": 3, "names": ["ASCIIEncoder", "Base256Encoder", "C40Encoder", "DefaultPlacement", "EdifactEncoder", "EncoderContext", "ErrorCorrection", "HighLevelEncoder", "MinimalEncoder", "SymbolInfo", "TextEncoder", "X12Encoder"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/index.js"], "sourcesContent": ["import { ASCIIEncoder } from './ASCIIEncoder';\nimport { Base256Encoder } from './Base256Encoder';\nimport { C40Encoder } from './C40Encoder';\nimport DefaultPlacement from './DefaultPlacement';\nimport { EdifactEncoder } from './EdifactEncoder';\nimport { EncoderContext } from './EncoderContext';\nimport ErrorCorrection from './ErrorCorrection';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { MinimalEncoder } from './MinimalEncoder';\nimport SymbolInfo from './SymbolInfo';\nimport { TextEncoder } from './TextEncoder';\nimport { X12Encoder } from './X12Encoder';\nexport { ASCIIEncoder, Base256Encoder, C40Encoder, EdifactEncoder, EncoderContext, ErrorCorrection, DefaultPlacement, HighLevelEncoder, MinimalEncoder, SymbolInfo, TextEncoder, X12Encoder, };\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASX,YAAY,EAAEC,cAAc,EAAEC,UAAU,EAAEE,cAAc,EAAEC,cAAc,EAAEC,eAAe,EAAEH,gBAAgB,EAAEI,gBAAgB,EAAEC,cAAc,EAAEC,UAAU,EAAEC,WAAW,EAAEC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}