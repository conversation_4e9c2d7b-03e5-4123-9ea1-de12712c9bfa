{"ast": null, "code": "import CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Just to make a shortcut between Java code and TS code.\n */\nvar StandardCharsets = /** @class */function () {\n  function StandardCharsets() {}\n  StandardCharsets.ISO_8859_1 = CharacterSetECI.ISO8859_1;\n  return StandardCharsets;\n}();\nexport default StandardCharsets;", "map": {"version": 3, "names": ["CharacterSetECI", "StandardCharsets", "ISO_8859_1", "ISO8859_1"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/StandardCharsets.js"], "sourcesContent": ["import CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Just to make a shortcut between Java code and TS code.\n */\nvar StandardCharsets = /** @class */ (function () {\n    function StandardCharsets() {\n    }\n    StandardCharsets.ISO_8859_1 = CharacterSetECI.ISO8859_1;\n    return StandardCharsets;\n}());\nexport default StandardCharsets;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2BAA2B;AACvD;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAe,YAAY;EAC9C,SAASA,gBAAgBA,CAAA,EAAG,CAC5B;EACAA,gBAAgB,CAACC,UAAU,GAAGF,eAAe,CAACG,SAAS;EACvD,OAAOF,gBAAgB;AAC3B,CAAC,CAAC,CAAE;AACJ,eAAeA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}