{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes Code 39 barcodes. Supports \"Full ASCII Code 39\" if USE_CODE_39_EXTENDED_MODE is set.</p>\n *\n * <AUTHOR> Owen\n * @see Code93Reader\n */\nvar Code39Reader = /** @class */function (_super) {\n  __extends(Code39Reader, _super);\n  /**\n   * Creates a reader that assumes all encoded data is data, and does not treat the final\n   * character as a check digit. It will not decoded \"extended Code 39\" sequences.\n   */\n  // public Code39Reader() {\n  //   this(false);\n  // }\n  /**\n   * Creates a reader that can be configured to check the last character as a check digit.\n   * It will not decoded \"extended Code 39\" sequences.\n   *\n   * @param usingCheckDigit if true, treat the last data character as a check digit, not\n   * data, and verify that the checksum passes.\n   */\n  // public Code39Reader(boolean usingCheckDigit) {\n  //   this(usingCheckDigit, false);\n  // }\n  /**\n   * Creates a reader that can be configured to check the last character as a check digit,\n   * or optionally attempt to decode \"extended Code 39\" sequences that are used to encode\n   * the full ASCII character set.\n   *\n   * @param usingCheckDigit if true, treat the last data character as a check digit, not\n   * data, and verify that the checksum passes.\n   * @param extendedMode if true, will attempt to decode extended Code 39 sequences in the\n   * text.\n   */\n  function Code39Reader(usingCheckDigit, extendedMode) {\n    if (usingCheckDigit === void 0) {\n      usingCheckDigit = false;\n    }\n    if (extendedMode === void 0) {\n      extendedMode = false;\n    }\n    var _this = _super.call(this) || this;\n    _this.usingCheckDigit = usingCheckDigit;\n    _this.extendedMode = extendedMode;\n    _this.decodeRowResult = '';\n    _this.counters = new Int32Array(9);\n    return _this;\n  }\n  Code39Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n    var e_1, _a, e_2, _b;\n    var theCounters = this.counters;\n    theCounters.fill(0);\n    this.decodeRowResult = '';\n    var start = Code39Reader.findAsteriskPattern(row, theCounters);\n    // Read off white space\n    var nextStart = row.getNextSet(start[1]);\n    var end = row.getSize();\n    var decodedChar;\n    var lastStart;\n    do {\n      Code39Reader.recordPattern(row, nextStart, theCounters);\n      var pattern = Code39Reader.toNarrowWidePattern(theCounters);\n      if (pattern < 0) {\n        throw new NotFoundException();\n      }\n      decodedChar = Code39Reader.patternToChar(pattern);\n      this.decodeRowResult += decodedChar;\n      lastStart = nextStart;\n      try {\n        for (var theCounters_1 = (e_1 = void 0, __values(theCounters)), theCounters_1_1 = theCounters_1.next(); !theCounters_1_1.done; theCounters_1_1 = theCounters_1.next()) {\n          var counter = theCounters_1_1.value;\n          nextStart += counter;\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (theCounters_1_1 && !theCounters_1_1.done && (_a = theCounters_1.return)) _a.call(theCounters_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n      // Read off white space\n      nextStart = row.getNextSet(nextStart);\n    } while (decodedChar !== '*');\n    this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 1); // remove asterisk\n    // Look for whitespace after pattern:\n    var lastPatternSize = 0;\n    try {\n      for (var theCounters_2 = __values(theCounters), theCounters_2_1 = theCounters_2.next(); !theCounters_2_1.done; theCounters_2_1 = theCounters_2.next()) {\n        var counter = theCounters_2_1.value;\n        lastPatternSize += counter;\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (theCounters_2_1 && !theCounters_2_1.done && (_b = theCounters_2.return)) _b.call(theCounters_2);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    var whiteSpaceAfterEnd = nextStart - lastStart - lastPatternSize;\n    // If 50% of last pattern size, following last pattern, is not whitespace, fail\n    // (but if it's whitespace to the very end of the image, that's OK)\n    if (nextStart !== end && whiteSpaceAfterEnd * 2 < lastPatternSize) {\n      throw new NotFoundException();\n    }\n    if (this.usingCheckDigit) {\n      var max = this.decodeRowResult.length - 1;\n      var total = 0;\n      for (var i = 0; i < max; i++) {\n        total += Code39Reader.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(i));\n      }\n      if (this.decodeRowResult.charAt(max) !== Code39Reader.ALPHABET_STRING.charAt(total % 43)) {\n        throw new ChecksumException();\n      }\n      this.decodeRowResult = this.decodeRowResult.substring(0, max);\n    }\n    if (this.decodeRowResult.length === 0) {\n      // false positive\n      throw new NotFoundException();\n    }\n    var resultString;\n    if (this.extendedMode) {\n      resultString = Code39Reader.decodeExtended(this.decodeRowResult);\n    } else {\n      resultString = this.decodeRowResult;\n    }\n    var left = (start[1] + start[0]) / 2.0;\n    var right = lastStart + lastPatternSize / 2.0;\n    return new Result(resultString, null, 0, [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)], BarcodeFormat.CODE_39, new Date().getTime());\n  };\n  Code39Reader.findAsteriskPattern = function (row, counters) {\n    var width = row.getSize();\n    var rowOffset = row.getNextSet(0);\n    var counterPosition = 0;\n    var patternStart = rowOffset;\n    var isWhite = false;\n    var patternLength = counters.length;\n    for (var i = rowOffset; i < width; i++) {\n      if (row.get(i) !== isWhite) {\n        counters[counterPosition]++;\n      } else {\n        if (counterPosition === patternLength - 1) {\n          // Look for whitespace before start pattern, >= 50% of width of start pattern\n          if (this.toNarrowWidePattern(counters) === Code39Reader.ASTERISK_ENCODING && row.isRange(Math.max(0, patternStart - Math.floor((i - patternStart) / 2)), patternStart, false)) {\n            return [patternStart, i];\n          }\n          patternStart += counters[0] + counters[1];\n          counters.copyWithin(0, 2, 2 + counterPosition - 1);\n          counters[counterPosition - 1] = 0;\n          counters[counterPosition] = 0;\n          counterPosition--;\n        } else {\n          counterPosition++;\n        }\n        counters[counterPosition] = 1;\n        isWhite = !isWhite;\n      }\n    }\n    throw new NotFoundException();\n  };\n  // For efficiency, returns -1 on failure. Not throwing here saved as many as 700 exceptions\n  // per image when using some of our blackbox images.\n  Code39Reader.toNarrowWidePattern = function (counters) {\n    var e_3, _a;\n    var numCounters = counters.length;\n    var maxNarrowCounter = 0;\n    var wideCounters;\n    do {\n      var minCounter = 0x7fffffff;\n      try {\n        for (var counters_1 = (e_3 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n          var counter = counters_1_1.value;\n          if (counter < minCounter && counter > maxNarrowCounter) {\n            minCounter = counter;\n          }\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n      maxNarrowCounter = minCounter;\n      wideCounters = 0;\n      var totalWideCountersWidth = 0;\n      var pattern = 0;\n      for (var i = 0; i < numCounters; i++) {\n        var counter = counters[i];\n        if (counter > maxNarrowCounter) {\n          pattern |= 1 << numCounters - 1 - i;\n          wideCounters++;\n          totalWideCountersWidth += counter;\n        }\n      }\n      if (wideCounters === 3) {\n        // Found 3 wide counters, but are they close enough in width?\n        // We can perform a cheap, conservative check to see if any individual\n        // counter is more than 1.5 times the average:\n        for (var i = 0; i < numCounters && wideCounters > 0; i++) {\n          var counter = counters[i];\n          if (counter > maxNarrowCounter) {\n            wideCounters--;\n            // totalWideCountersWidth = 3 * average, so this checks if counter >= 3/2 * average\n            if (counter * 2 >= totalWideCountersWidth) {\n              return -1;\n            }\n          }\n        }\n        return pattern;\n      }\n    } while (wideCounters > 3);\n    return -1;\n  };\n  Code39Reader.patternToChar = function (pattern) {\n    for (var i = 0; i < Code39Reader.CHARACTER_ENCODINGS.length; i++) {\n      if (Code39Reader.CHARACTER_ENCODINGS[i] === pattern) {\n        return Code39Reader.ALPHABET_STRING.charAt(i);\n      }\n    }\n    if (pattern === Code39Reader.ASTERISK_ENCODING) {\n      return '*';\n    }\n    throw new NotFoundException();\n  };\n  Code39Reader.decodeExtended = function (encoded) {\n    var length = encoded.length;\n    var decoded = '';\n    for (var i = 0; i < length; i++) {\n      var c = encoded.charAt(i);\n      if (c === '+' || c === '$' || c === '%' || c === '/') {\n        var next = encoded.charAt(i + 1);\n        var decodedChar = '\\0';\n        switch (c) {\n          case '+':\n            // +A to +Z map to a to z\n            if (next >= 'A' && next <= 'Z') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) + 32);\n            } else {\n              throw new FormatException();\n            }\n            break;\n          case '$':\n            // $A to $Z map to control codes SH to SB\n            if (next >= 'A' && next <= 'Z') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) - 64);\n            } else {\n              throw new FormatException();\n            }\n            break;\n          case '%':\n            // %A to %E map to control codes ESC to US\n            if (next >= 'A' && next <= 'E') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) - 38);\n            } else if (next >= 'F' && next <= 'J') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) - 11);\n            } else if (next >= 'K' && next <= 'O') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) + 16);\n            } else if (next >= 'P' && next <= 'T') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) + 43);\n            } else if (next === 'U') {\n              decodedChar = '\\0';\n            } else if (next === 'V') {\n              decodedChar = '@';\n            } else if (next === 'W') {\n              decodedChar = '`';\n            } else if (next === 'X' || next === 'Y' || next === 'Z') {\n              decodedChar = '\\x7f';\n            } else {\n              throw new FormatException();\n            }\n            break;\n          case '/':\n            // /A to /O map to ! to , and /Z maps to :\n            if (next >= 'A' && next <= 'O') {\n              decodedChar = String.fromCharCode(next.charCodeAt(0) - 32);\n            } else if (next === 'Z') {\n              decodedChar = ':';\n            } else {\n              throw new FormatException();\n            }\n            break;\n        }\n        decoded += decodedChar;\n        // bump up i again since we read two characters\n        i++;\n      } else {\n        decoded += c;\n      }\n    }\n    return decoded;\n  };\n  Code39Reader.ALPHABET_STRING = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%';\n  /**\n   * These represent the encodings of characters, as patterns of wide and narrow bars.\n   * The 9 least-significant bits of each int correspond to the pattern of wide and narrow,\n   * with 1s representing \"wide\" and 0s representing narrow.\n   */\n  Code39Reader.CHARACTER_ENCODINGS = [0x034, 0x121, 0x061, 0x160, 0x031, 0x130, 0x070, 0x025, 0x124, 0x064, 0x109, 0x049, 0x148, 0x019, 0x118, 0x058, 0x00D, 0x10C, 0x04C, 0x01C, 0x103, 0x043, 0x142, 0x013, 0x112, 0x052, 0x007, 0x106, 0x046, 0x016, 0x181, 0x0C1, 0x1C0, 0x091, 0x190, 0x0D0, 0x085, 0x184, 0x0C4, 0x0A8, 0x0A2, 0x08A, 0x02A // /-%\n  ];\n  Code39Reader.ASTERISK_ENCODING = 0x094;\n  return Code39Reader;\n}(OneDReader);\nexport default Code39Reader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BarcodeFormat", "ChecksumException", "FormatException", "NotFoundException", "OneDReader", "Result", "ResultPoint", "Code39Reader", "_super", "usingCheckDigit", "extendedMode", "_this", "decodeRowResult", "counters", "Int32Array", "decodeRow", "rowNumber", "row", "hints", "e_1", "_a", "e_2", "_b", "theCounters", "fill", "start", "findAsteriskPattern", "nextStart", "getNextSet", "end", "getSize", "decodedChar", "lastStart", "recordPattern", "pattern", "toNarrowWidePattern", "patternToChar", "theCounters_1", "theCounters_1_1", "counter", "e_1_1", "error", "return", "substring", "lastPatternSize", "theCounters_2", "theCounters_2_1", "e_2_1", "whiteSpaceAfterEnd", "max", "total", "ALPHABET_STRING", "indexOf", "char<PERSON>t", "resultString", "decodeExtended", "left", "right", "CODE_39", "Date", "getTime", "width", "rowOffset", "counterPosition", "patternStart", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "get", "ASTERISK_ENCODING", "isRange", "Math", "floor", "copyWithin", "e_3", "numCounters", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wideCounters", "<PERSON><PERSON><PERSON><PERSON>", "counters_1", "counters_1_1", "e_3_1", "totalWideCountersWidth", "CHARACTER_ENCODINGS", "encoded", "decoded", "c", "String", "fromCharCode", "charCodeAt"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/Code39Reader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport ChecksumException from '../ChecksumException';\nimport FormatException from '../FormatException';\nimport NotFoundException from '../NotFoundException';\nimport OneDReader from './OneDReader';\nimport Result from '../Result';\nimport ResultPoint from '../ResultPoint';\n/**\n * <p>Decodes Code 39 barcodes. Supports \"Full ASCII Code 39\" if USE_CODE_39_EXTENDED_MODE is set.</p>\n *\n * <AUTHOR> Owen\n * @see Code93Reader\n */\nvar Code39Reader = /** @class */ (function (_super) {\n    __extends(Code39Reader, _super);\n    /**\n     * Creates a reader that assumes all encoded data is data, and does not treat the final\n     * character as a check digit. It will not decoded \"extended Code 39\" sequences.\n     */\n    // public Code39Reader() {\n    //   this(false);\n    // }\n    /**\n     * Creates a reader that can be configured to check the last character as a check digit.\n     * It will not decoded \"extended Code 39\" sequences.\n     *\n     * @param usingCheckDigit if true, treat the last data character as a check digit, not\n     * data, and verify that the checksum passes.\n     */\n    // public Code39Reader(boolean usingCheckDigit) {\n    //   this(usingCheckDigit, false);\n    // }\n    /**\n     * Creates a reader that can be configured to check the last character as a check digit,\n     * or optionally attempt to decode \"extended Code 39\" sequences that are used to encode\n     * the full ASCII character set.\n     *\n     * @param usingCheckDigit if true, treat the last data character as a check digit, not\n     * data, and verify that the checksum passes.\n     * @param extendedMode if true, will attempt to decode extended Code 39 sequences in the\n     * text.\n     */\n    function Code39Reader(usingCheckDigit, extendedMode) {\n        if (usingCheckDigit === void 0) { usingCheckDigit = false; }\n        if (extendedMode === void 0) { extendedMode = false; }\n        var _this = _super.call(this) || this;\n        _this.usingCheckDigit = usingCheckDigit;\n        _this.extendedMode = extendedMode;\n        _this.decodeRowResult = '';\n        _this.counters = new Int32Array(9);\n        return _this;\n    }\n    Code39Reader.prototype.decodeRow = function (rowNumber, row, hints) {\n        var e_1, _a, e_2, _b;\n        var theCounters = this.counters;\n        theCounters.fill(0);\n        this.decodeRowResult = '';\n        var start = Code39Reader.findAsteriskPattern(row, theCounters);\n        // Read off white space\n        var nextStart = row.getNextSet(start[1]);\n        var end = row.getSize();\n        var decodedChar;\n        var lastStart;\n        do {\n            Code39Reader.recordPattern(row, nextStart, theCounters);\n            var pattern = Code39Reader.toNarrowWidePattern(theCounters);\n            if (pattern < 0) {\n                throw new NotFoundException();\n            }\n            decodedChar = Code39Reader.patternToChar(pattern);\n            this.decodeRowResult += decodedChar;\n            lastStart = nextStart;\n            try {\n                for (var theCounters_1 = (e_1 = void 0, __values(theCounters)), theCounters_1_1 = theCounters_1.next(); !theCounters_1_1.done; theCounters_1_1 = theCounters_1.next()) {\n                    var counter = theCounters_1_1.value;\n                    nextStart += counter;\n                }\n            }\n            catch (e_1_1) { e_1 = { error: e_1_1 }; }\n            finally {\n                try {\n                    if (theCounters_1_1 && !theCounters_1_1.done && (_a = theCounters_1.return)) _a.call(theCounters_1);\n                }\n                finally { if (e_1) throw e_1.error; }\n            }\n            // Read off white space\n            nextStart = row.getNextSet(nextStart);\n        } while (decodedChar !== '*');\n        this.decodeRowResult = this.decodeRowResult.substring(0, this.decodeRowResult.length - 1); // remove asterisk\n        // Look for whitespace after pattern:\n        var lastPatternSize = 0;\n        try {\n            for (var theCounters_2 = __values(theCounters), theCounters_2_1 = theCounters_2.next(); !theCounters_2_1.done; theCounters_2_1 = theCounters_2.next()) {\n                var counter = theCounters_2_1.value;\n                lastPatternSize += counter;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (theCounters_2_1 && !theCounters_2_1.done && (_b = theCounters_2.return)) _b.call(theCounters_2);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        var whiteSpaceAfterEnd = nextStart - lastStart - lastPatternSize;\n        // If 50% of last pattern size, following last pattern, is not whitespace, fail\n        // (but if it's whitespace to the very end of the image, that's OK)\n        if (nextStart !== end && (whiteSpaceAfterEnd * 2) < lastPatternSize) {\n            throw new NotFoundException();\n        }\n        if (this.usingCheckDigit) {\n            var max = this.decodeRowResult.length - 1;\n            var total = 0;\n            for (var i = 0; i < max; i++) {\n                total += Code39Reader.ALPHABET_STRING.indexOf(this.decodeRowResult.charAt(i));\n            }\n            if (this.decodeRowResult.charAt(max) !== Code39Reader.ALPHABET_STRING.charAt(total % 43)) {\n                throw new ChecksumException();\n            }\n            this.decodeRowResult = this.decodeRowResult.substring(0, max);\n        }\n        if (this.decodeRowResult.length === 0) {\n            // false positive\n            throw new NotFoundException();\n        }\n        var resultString;\n        if (this.extendedMode) {\n            resultString = Code39Reader.decodeExtended(this.decodeRowResult);\n        }\n        else {\n            resultString = this.decodeRowResult;\n        }\n        var left = (start[1] + start[0]) / 2.0;\n        var right = lastStart + lastPatternSize / 2.0;\n        return new Result(resultString, null, 0, [new ResultPoint(left, rowNumber), new ResultPoint(right, rowNumber)], BarcodeFormat.CODE_39, new Date().getTime());\n    };\n    Code39Reader.findAsteriskPattern = function (row, counters) {\n        var width = row.getSize();\n        var rowOffset = row.getNextSet(0);\n        var counterPosition = 0;\n        var patternStart = rowOffset;\n        var isWhite = false;\n        var patternLength = counters.length;\n        for (var i = rowOffset; i < width; i++) {\n            if (row.get(i) !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    // Look for whitespace before start pattern, >= 50% of width of start pattern\n                    if (this.toNarrowWidePattern(counters) === Code39Reader.ASTERISK_ENCODING &&\n                        row.isRange(Math.max(0, patternStart - Math.floor((i - patternStart) / 2)), patternStart, false)) {\n                        return [patternStart, i];\n                    }\n                    patternStart += counters[0] + counters[1];\n                    counters.copyWithin(0, 2, 2 + counterPosition - 1);\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        throw new NotFoundException();\n    };\n    // For efficiency, returns -1 on failure. Not throwing here saved as many as 700 exceptions\n    // per image when using some of our blackbox images.\n    Code39Reader.toNarrowWidePattern = function (counters) {\n        var e_3, _a;\n        var numCounters = counters.length;\n        var maxNarrowCounter = 0;\n        var wideCounters;\n        do {\n            var minCounter = 0x7fffffff;\n            try {\n                for (var counters_1 = (e_3 = void 0, __values(counters)), counters_1_1 = counters_1.next(); !counters_1_1.done; counters_1_1 = counters_1.next()) {\n                    var counter = counters_1_1.value;\n                    if (counter < minCounter && counter > maxNarrowCounter) {\n                        minCounter = counter;\n                    }\n                }\n            }\n            catch (e_3_1) { e_3 = { error: e_3_1 }; }\n            finally {\n                try {\n                    if (counters_1_1 && !counters_1_1.done && (_a = counters_1.return)) _a.call(counters_1);\n                }\n                finally { if (e_3) throw e_3.error; }\n            }\n            maxNarrowCounter = minCounter;\n            wideCounters = 0;\n            var totalWideCountersWidth = 0;\n            var pattern = 0;\n            for (var i = 0; i < numCounters; i++) {\n                var counter = counters[i];\n                if (counter > maxNarrowCounter) {\n                    pattern |= 1 << (numCounters - 1 - i);\n                    wideCounters++;\n                    totalWideCountersWidth += counter;\n                }\n            }\n            if (wideCounters === 3) {\n                // Found 3 wide counters, but are they close enough in width?\n                // We can perform a cheap, conservative check to see if any individual\n                // counter is more than 1.5 times the average:\n                for (var i = 0; i < numCounters && wideCounters > 0; i++) {\n                    var counter = counters[i];\n                    if (counter > maxNarrowCounter) {\n                        wideCounters--;\n                        // totalWideCountersWidth = 3 * average, so this checks if counter >= 3/2 * average\n                        if ((counter * 2) >= totalWideCountersWidth) {\n                            return -1;\n                        }\n                    }\n                }\n                return pattern;\n            }\n        } while (wideCounters > 3);\n        return -1;\n    };\n    Code39Reader.patternToChar = function (pattern) {\n        for (var i = 0; i < Code39Reader.CHARACTER_ENCODINGS.length; i++) {\n            if (Code39Reader.CHARACTER_ENCODINGS[i] === pattern) {\n                return Code39Reader.ALPHABET_STRING.charAt(i);\n            }\n        }\n        if (pattern === Code39Reader.ASTERISK_ENCODING) {\n            return '*';\n        }\n        throw new NotFoundException();\n    };\n    Code39Reader.decodeExtended = function (encoded) {\n        var length = encoded.length;\n        var decoded = '';\n        for (var i = 0; i < length; i++) {\n            var c = encoded.charAt(i);\n            if (c === '+' || c === '$' || c === '%' || c === '/') {\n                var next = encoded.charAt(i + 1);\n                var decodedChar = '\\0';\n                switch (c) {\n                    case '+':\n                        // +A to +Z map to a to z\n                        if (next >= 'A' && next <= 'Z') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 32);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case '$':\n                        // $A to $Z map to control codes SH to SB\n                        if (next >= 'A' && next <= 'Z') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 64);\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case '%':\n                        // %A to %E map to control codes ESC to US\n                        if (next >= 'A' && next <= 'E') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 38);\n                        }\n                        else if (next >= 'F' && next <= 'J') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 11);\n                        }\n                        else if (next >= 'K' && next <= 'O') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 16);\n                        }\n                        else if (next >= 'P' && next <= 'T') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) + 43);\n                        }\n                        else if (next === 'U') {\n                            decodedChar = '\\0';\n                        }\n                        else if (next === 'V') {\n                            decodedChar = '@';\n                        }\n                        else if (next === 'W') {\n                            decodedChar = '`';\n                        }\n                        else if (next === 'X' || next === 'Y' || next === 'Z') {\n                            decodedChar = '\\x7f';\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                    case '/':\n                        // /A to /O map to ! to , and /Z maps to :\n                        if (next >= 'A' && next <= 'O') {\n                            decodedChar = String.fromCharCode(next.charCodeAt(0) - 32);\n                        }\n                        else if (next === 'Z') {\n                            decodedChar = ':';\n                        }\n                        else {\n                            throw new FormatException();\n                        }\n                        break;\n                }\n                decoded += decodedChar;\n                // bump up i again since we read two characters\n                i++;\n            }\n            else {\n                decoded += c;\n            }\n        }\n        return decoded;\n    };\n    Code39Reader.ALPHABET_STRING = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-. $/+%';\n    /**\n     * These represent the encodings of characters, as patterns of wide and narrow bars.\n     * The 9 least-significant bits of each int correspond to the pattern of wide and narrow,\n     * with 1s representing \"wide\" and 0s representing narrow.\n     */\n    Code39Reader.CHARACTER_ENCODINGS = [\n        0x034, 0x121, 0x061, 0x160, 0x031, 0x130, 0x070, 0x025, 0x124, 0x064,\n        0x109, 0x049, 0x148, 0x019, 0x118, 0x058, 0x00D, 0x10C, 0x04C, 0x01C,\n        0x103, 0x043, 0x142, 0x013, 0x112, 0x052, 0x007, 0x106, 0x046, 0x016,\n        0x181, 0x0C1, 0x1C0, 0x091, 0x190, 0x0D0, 0x085, 0x184, 0x0C4, 0x0A8,\n        0x0A2, 0x08A, 0x02A // /-%\n    ];\n    Code39Reader.ASTERISK_ENCODING = 0x094;\n    return Code39Reader;\n}(OneDReader));\nexport default Code39Reader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,eAAe,MAAM,oBAAoB;AAChD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,WAAW,MAAM,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAe,UAAUC,MAAM,EAAE;EAChDnC,SAAS,CAACkC,YAAY,EAAEC,MAAM,CAAC;EAC/B;AACJ;AACA;AACA;EACI;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASD,YAAYA,CAACE,eAAe,EAAEC,YAAY,EAAE;IACjD,IAAID,eAAe,KAAK,KAAK,CAAC,EAAE;MAAEA,eAAe,GAAG,KAAK;IAAE;IAC3D,IAAIC,YAAY,KAAK,KAAK,CAAC,EAAE;MAAEA,YAAY,GAAG,KAAK;IAAE;IACrD,IAAIC,KAAK,GAAGH,MAAM,CAACd,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCiB,KAAK,CAACF,eAAe,GAAGA,eAAe;IACvCE,KAAK,CAACD,YAAY,GAAGA,YAAY;IACjCC,KAAK,CAACC,eAAe,GAAG,EAAE;IAC1BD,KAAK,CAACE,QAAQ,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;IAClC,OAAOH,KAAK;EAChB;EACAJ,YAAY,CAACtB,SAAS,CAAC8B,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEC,KAAK,EAAE;IAChE,IAAIC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE;IACpB,IAAIC,WAAW,GAAG,IAAI,CAACV,QAAQ;IAC/BU,WAAW,CAACC,IAAI,CAAC,CAAC,CAAC;IACnB,IAAI,CAACZ,eAAe,GAAG,EAAE;IACzB,IAAIa,KAAK,GAAGlB,YAAY,CAACmB,mBAAmB,CAACT,GAAG,EAAEM,WAAW,CAAC;IAC9D;IACA,IAAII,SAAS,GAAGV,GAAG,CAACW,UAAU,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC;IACxC,IAAII,GAAG,GAAGZ,GAAG,CAACa,OAAO,CAAC,CAAC;IACvB,IAAIC,WAAW;IACf,IAAIC,SAAS;IACb,GAAG;MACCzB,YAAY,CAAC0B,aAAa,CAAChB,GAAG,EAAEU,SAAS,EAAEJ,WAAW,CAAC;MACvD,IAAIW,OAAO,GAAG3B,YAAY,CAAC4B,mBAAmB,CAACZ,WAAW,CAAC;MAC3D,IAAIW,OAAO,GAAG,CAAC,EAAE;QACb,MAAM,IAAI/B,iBAAiB,CAAC,CAAC;MACjC;MACA4B,WAAW,GAAGxB,YAAY,CAAC6B,aAAa,CAACF,OAAO,CAAC;MACjD,IAAI,CAACtB,eAAe,IAAImB,WAAW;MACnCC,SAAS,GAAGL,SAAS;MACrB,IAAI;QACA,KAAK,IAAIU,aAAa,IAAIlB,GAAG,GAAG,KAAK,CAAC,EAAEhC,QAAQ,CAACoC,WAAW,CAAC,CAAC,EAAEe,eAAe,GAAGD,aAAa,CAACzC,IAAI,CAAC,CAAC,EAAE,CAAC0C,eAAe,CAACxC,IAAI,EAAEwC,eAAe,GAAGD,aAAa,CAACzC,IAAI,CAAC,CAAC,EAAE;UACnK,IAAI2C,OAAO,GAAGD,eAAe,CAACzC,KAAK;UACnC8B,SAAS,IAAIY,OAAO;QACxB;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAErB,GAAG,GAAG;UAAEsB,KAAK,EAAED;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,eAAe,IAAI,CAACA,eAAe,CAACxC,IAAI,KAAKsB,EAAE,GAAGiB,aAAa,CAACK,MAAM,CAAC,EAAEtB,EAAE,CAAC1B,IAAI,CAAC2C,aAAa,CAAC;QACvG,CAAC,SACO;UAAE,IAAIlB,GAAG,EAAE,MAAMA,GAAG,CAACsB,KAAK;QAAE;MACxC;MACA;MACAd,SAAS,GAAGV,GAAG,CAACW,UAAU,CAACD,SAAS,CAAC;IACzC,CAAC,QAAQI,WAAW,KAAK,GAAG;IAC5B,IAAI,CAACnB,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC+B,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC/B,eAAe,CAACjB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;IAC3F;IACA,IAAIiD,eAAe,GAAG,CAAC;IACvB,IAAI;MACA,KAAK,IAAIC,aAAa,GAAG1D,QAAQ,CAACoC,WAAW,CAAC,EAAEuB,eAAe,GAAGD,aAAa,CAACjD,IAAI,CAAC,CAAC,EAAE,CAACkD,eAAe,CAAChD,IAAI,EAAEgD,eAAe,GAAGD,aAAa,CAACjD,IAAI,CAAC,CAAC,EAAE;QACnJ,IAAI2C,OAAO,GAAGO,eAAe,CAACjD,KAAK;QACnC+C,eAAe,IAAIL,OAAO;MAC9B;IACJ,CAAC,CACD,OAAOQ,KAAK,EAAE;MAAE1B,GAAG,GAAG;QAAEoB,KAAK,EAAEM;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAID,eAAe,IAAI,CAACA,eAAe,CAAChD,IAAI,KAAKwB,EAAE,GAAGuB,aAAa,CAACH,MAAM,CAAC,EAAEpB,EAAE,CAAC5B,IAAI,CAACmD,aAAa,CAAC;MACvG,CAAC,SACO;QAAE,IAAIxB,GAAG,EAAE,MAAMA,GAAG,CAACoB,KAAK;MAAE;IACxC;IACA,IAAIO,kBAAkB,GAAGrB,SAAS,GAAGK,SAAS,GAAGY,eAAe;IAChE;IACA;IACA,IAAIjB,SAAS,KAAKE,GAAG,IAAKmB,kBAAkB,GAAG,CAAC,GAAIJ,eAAe,EAAE;MACjE,MAAM,IAAIzC,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI,IAAI,CAACM,eAAe,EAAE;MACtB,IAAIwC,GAAG,GAAG,IAAI,CAACrC,eAAe,CAACjB,MAAM,GAAG,CAAC;MACzC,IAAIuD,KAAK,GAAG,CAAC;MACb,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,GAAG,EAAExD,CAAC,EAAE,EAAE;QAC1ByD,KAAK,IAAI3C,YAAY,CAAC4C,eAAe,CAACC,OAAO,CAAC,IAAI,CAACxC,eAAe,CAACyC,MAAM,CAAC5D,CAAC,CAAC,CAAC;MACjF;MACA,IAAI,IAAI,CAACmB,eAAe,CAACyC,MAAM,CAACJ,GAAG,CAAC,KAAK1C,YAAY,CAAC4C,eAAe,CAACE,MAAM,CAACH,KAAK,GAAG,EAAE,CAAC,EAAE;QACtF,MAAM,IAAIjD,iBAAiB,CAAC,CAAC;MACjC;MACA,IAAI,CAACW,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC+B,SAAS,CAAC,CAAC,EAAEM,GAAG,CAAC;IACjE;IACA,IAAI,IAAI,CAACrC,eAAe,CAACjB,MAAM,KAAK,CAAC,EAAE;MACnC;MACA,MAAM,IAAIQ,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAImD,YAAY;IAChB,IAAI,IAAI,CAAC5C,YAAY,EAAE;MACnB4C,YAAY,GAAG/C,YAAY,CAACgD,cAAc,CAAC,IAAI,CAAC3C,eAAe,CAAC;IACpE,CAAC,MACI;MACD0C,YAAY,GAAG,IAAI,CAAC1C,eAAe;IACvC;IACA,IAAI4C,IAAI,GAAG,CAAC/B,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG;IACtC,IAAIgC,KAAK,GAAGzB,SAAS,GAAGY,eAAe,GAAG,GAAG;IAC7C,OAAO,IAAIvC,MAAM,CAACiD,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,IAAIhD,WAAW,CAACkD,IAAI,EAAExC,SAAS,CAAC,EAAE,IAAIV,WAAW,CAACmD,KAAK,EAAEzC,SAAS,CAAC,CAAC,EAAEhB,aAAa,CAAC0D,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAChK,CAAC;EACDrD,YAAY,CAACmB,mBAAmB,GAAG,UAAUT,GAAG,EAAEJ,QAAQ,EAAE;IACxD,IAAIgD,KAAK,GAAG5C,GAAG,CAACa,OAAO,CAAC,CAAC;IACzB,IAAIgC,SAAS,GAAG7C,GAAG,CAACW,UAAU,CAAC,CAAC,CAAC;IACjC,IAAImC,eAAe,GAAG,CAAC;IACvB,IAAIC,YAAY,GAAGF,SAAS;IAC5B,IAAIG,OAAO,GAAG,KAAK;IACnB,IAAIC,aAAa,GAAGrD,QAAQ,CAAClB,MAAM;IACnC,KAAK,IAAIF,CAAC,GAAGqE,SAAS,EAAErE,CAAC,GAAGoE,KAAK,EAAEpE,CAAC,EAAE,EAAE;MACpC,IAAIwB,GAAG,CAACkD,GAAG,CAAC1E,CAAC,CAAC,KAAKwE,OAAO,EAAE;QACxBpD,QAAQ,CAACkD,eAAe,CAAC,EAAE;MAC/B,CAAC,MACI;QACD,IAAIA,eAAe,KAAKG,aAAa,GAAG,CAAC,EAAE;UACvC;UACA,IAAI,IAAI,CAAC/B,mBAAmB,CAACtB,QAAQ,CAAC,KAAKN,YAAY,CAAC6D,iBAAiB,IACrEnD,GAAG,CAACoD,OAAO,CAACC,IAAI,CAACrB,GAAG,CAAC,CAAC,EAAEe,YAAY,GAAGM,IAAI,CAACC,KAAK,CAAC,CAAC9E,CAAC,GAAGuE,YAAY,IAAI,CAAC,CAAC,CAAC,EAAEA,YAAY,EAAE,KAAK,CAAC,EAAE;YAClG,OAAO,CAACA,YAAY,EAAEvE,CAAC,CAAC;UAC5B;UACAuE,YAAY,IAAInD,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzCA,QAAQ,CAAC2D,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,GAAGT,eAAe,GAAG,CAAC,CAAC;UAClDlD,QAAQ,CAACkD,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;UACjClD,QAAQ,CAACkD,eAAe,CAAC,GAAG,CAAC;UAC7BA,eAAe,EAAE;QACrB,CAAC,MACI;UACDA,eAAe,EAAE;QACrB;QACAlD,QAAQ,CAACkD,eAAe,CAAC,GAAG,CAAC;QAC7BE,OAAO,GAAG,CAACA,OAAO;MACtB;IACJ;IACA,MAAM,IAAI9D,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACD;EACA;EACAI,YAAY,CAAC4B,mBAAmB,GAAG,UAAUtB,QAAQ,EAAE;IACnD,IAAI4D,GAAG,EAAErD,EAAE;IACX,IAAIsD,WAAW,GAAG7D,QAAQ,CAAClB,MAAM;IACjC,IAAIgF,gBAAgB,GAAG,CAAC;IACxB,IAAIC,YAAY;IAChB,GAAG;MACC,IAAIC,UAAU,GAAG,UAAU;MAC3B,IAAI;QACA,KAAK,IAAIC,UAAU,IAAIL,GAAG,GAAG,KAAK,CAAC,EAAEtF,QAAQ,CAAC0B,QAAQ,CAAC,CAAC,EAAEkE,YAAY,GAAGD,UAAU,CAAClF,IAAI,CAAC,CAAC,EAAE,CAACmF,YAAY,CAACjF,IAAI,EAAEiF,YAAY,GAAGD,UAAU,CAAClF,IAAI,CAAC,CAAC,EAAE;UAC9I,IAAI2C,OAAO,GAAGwC,YAAY,CAAClF,KAAK;UAChC,IAAI0C,OAAO,GAAGsC,UAAU,IAAItC,OAAO,GAAGoC,gBAAgB,EAAE;YACpDE,UAAU,GAAGtC,OAAO;UACxB;QACJ;MACJ,CAAC,CACD,OAAOyC,KAAK,EAAE;QAAEP,GAAG,GAAG;UAAEhC,KAAK,EAAEuC;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAID,YAAY,IAAI,CAACA,YAAY,CAACjF,IAAI,KAAKsB,EAAE,GAAG0D,UAAU,CAACpC,MAAM,CAAC,EAAEtB,EAAE,CAAC1B,IAAI,CAACoF,UAAU,CAAC;QAC3F,CAAC,SACO;UAAE,IAAIL,GAAG,EAAE,MAAMA,GAAG,CAAChC,KAAK;QAAE;MACxC;MACAkC,gBAAgB,GAAGE,UAAU;MAC7BD,YAAY,GAAG,CAAC;MAChB,IAAIK,sBAAsB,GAAG,CAAC;MAC9B,IAAI/C,OAAO,GAAG,CAAC;MACf,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,WAAW,EAAEjF,CAAC,EAAE,EAAE;QAClC,IAAI8C,OAAO,GAAG1B,QAAQ,CAACpB,CAAC,CAAC;QACzB,IAAI8C,OAAO,GAAGoC,gBAAgB,EAAE;UAC5BzC,OAAO,IAAI,CAAC,IAAKwC,WAAW,GAAG,CAAC,GAAGjF,CAAE;UACrCmF,YAAY,EAAE;UACdK,sBAAsB,IAAI1C,OAAO;QACrC;MACJ;MACA,IAAIqC,YAAY,KAAK,CAAC,EAAE;QACpB;QACA;QACA;QACA,KAAK,IAAInF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiF,WAAW,IAAIE,YAAY,GAAG,CAAC,EAAEnF,CAAC,EAAE,EAAE;UACtD,IAAI8C,OAAO,GAAG1B,QAAQ,CAACpB,CAAC,CAAC;UACzB,IAAI8C,OAAO,GAAGoC,gBAAgB,EAAE;YAC5BC,YAAY,EAAE;YACd;YACA,IAAKrC,OAAO,GAAG,CAAC,IAAK0C,sBAAsB,EAAE;cACzC,OAAO,CAAC,CAAC;YACb;UACJ;QACJ;QACA,OAAO/C,OAAO;MAClB;IACJ,CAAC,QAAQ0C,YAAY,GAAG,CAAC;IACzB,OAAO,CAAC,CAAC;EACb,CAAC;EACDrE,YAAY,CAAC6B,aAAa,GAAG,UAAUF,OAAO,EAAE;IAC5C,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,YAAY,CAAC2E,mBAAmB,CAACvF,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC9D,IAAIc,YAAY,CAAC2E,mBAAmB,CAACzF,CAAC,CAAC,KAAKyC,OAAO,EAAE;QACjD,OAAO3B,YAAY,CAAC4C,eAAe,CAACE,MAAM,CAAC5D,CAAC,CAAC;MACjD;IACJ;IACA,IAAIyC,OAAO,KAAK3B,YAAY,CAAC6D,iBAAiB,EAAE;MAC5C,OAAO,GAAG;IACd;IACA,MAAM,IAAIjE,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACDI,YAAY,CAACgD,cAAc,GAAG,UAAU4B,OAAO,EAAE;IAC7C,IAAIxF,MAAM,GAAGwF,OAAO,CAACxF,MAAM;IAC3B,IAAIyF,OAAO,GAAG,EAAE;IAChB,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC7B,IAAI4F,CAAC,GAAGF,OAAO,CAAC9B,MAAM,CAAC5D,CAAC,CAAC;MACzB,IAAI4F,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,IAAIA,CAAC,KAAK,GAAG,EAAE;QAClD,IAAIzF,IAAI,GAAGuF,OAAO,CAAC9B,MAAM,CAAC5D,CAAC,GAAG,CAAC,CAAC;QAChC,IAAIsC,WAAW,GAAG,IAAI;QACtB,QAAQsD,CAAC;UACL,KAAK,GAAG;YACJ;YACA,IAAIzF,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cAC5BmC,WAAW,GAAGuD,MAAM,CAACC,YAAY,CAAC3F,IAAI,CAAC4F,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI;cACD,MAAM,IAAItF,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAK,GAAG;YACJ;YACA,IAAIN,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cAC5BmC,WAAW,GAAGuD,MAAM,CAACC,YAAY,CAAC3F,IAAI,CAAC4F,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI;cACD,MAAM,IAAItF,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAK,GAAG;YACJ;YACA,IAAIN,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cAC5BmC,WAAW,GAAGuD,MAAM,CAACC,YAAY,CAAC3F,IAAI,CAAC4F,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAI5F,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cACjCmC,WAAW,GAAGuD,MAAM,CAACC,YAAY,CAAC3F,IAAI,CAAC4F,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAI5F,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cACjCmC,WAAW,GAAGuD,MAAM,CAACC,YAAY,CAAC3F,IAAI,CAAC4F,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAI5F,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cACjCmC,WAAW,GAAGuD,MAAM,CAACC,YAAY,CAAC3F,IAAI,CAAC4F,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAI5F,IAAI,KAAK,GAAG,EAAE;cACnBmC,WAAW,GAAG,IAAI;YACtB,CAAC,MACI,IAAInC,IAAI,KAAK,GAAG,EAAE;cACnBmC,WAAW,GAAG,GAAG;YACrB,CAAC,MACI,IAAInC,IAAI,KAAK,GAAG,EAAE;cACnBmC,WAAW,GAAG,GAAG;YACrB,CAAC,MACI,IAAInC,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,GAAG,EAAE;cACnDmC,WAAW,GAAG,MAAM;YACxB,CAAC,MACI;cACD,MAAM,IAAI7B,eAAe,CAAC,CAAC;YAC/B;YACA;UACJ,KAAK,GAAG;YACJ;YACA,IAAIN,IAAI,IAAI,GAAG,IAAIA,IAAI,IAAI,GAAG,EAAE;cAC5BmC,WAAW,GAAGuD,MAAM,CAACC,YAAY,CAAC3F,IAAI,CAAC4F,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9D,CAAC,MACI,IAAI5F,IAAI,KAAK,GAAG,EAAE;cACnBmC,WAAW,GAAG,GAAG;YACrB,CAAC,MACI;cACD,MAAM,IAAI7B,eAAe,CAAC,CAAC;YAC/B;YACA;QACR;QACAkF,OAAO,IAAIrD,WAAW;QACtB;QACAtC,CAAC,EAAE;MACP,CAAC,MACI;QACD2F,OAAO,IAAIC,CAAC;MAChB;IACJ;IACA,OAAOD,OAAO;EAClB,CAAC;EACD7E,YAAY,CAAC4C,eAAe,GAAG,6CAA6C;EAC5E;AACJ;AACA;AACA;AACA;EACI5C,YAAY,CAAC2E,mBAAmB,GAAG,CAC/B,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EACpE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAAA,CACvB;EACD3E,YAAY,CAAC6D,iBAAiB,GAAG,KAAK;EACtC,OAAO7D,YAAY;AACvB,CAAC,CAACH,UAAU,CAAE;AACd,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}