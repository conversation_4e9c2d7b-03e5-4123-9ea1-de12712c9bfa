{"ast": null, "code": "import IllegalStateException from '../../../../IllegalStateException';\nimport GeneralAppIdDecoder from './GeneralAppIdDecoder';\nimport AI01AndOtherAIs from './AI01AndOtherAIs';\nimport AnyAIDecoder from './AnyAIDecoder';\nimport AI013103decoder from './AI013103decoder';\nimport AI01320xDecoder from './AI01320xDecoder';\nimport AI01392xDecoder from './AI01392xDecoder';\nimport AI01393xDecoder from './AI01393xDecoder';\nimport AI013x0x1xDecoder from './AI013x0x1xDecoder';\nexport function createDecoder(information) {\n  try {\n    if (information.get(1)) {\n      return new AI01AndOtherAIs(information);\n    }\n    if (!information.get(2)) {\n      return new AnyAIDecoder(information);\n    }\n    var fourBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 4);\n    switch (fourBitEncodationMethod) {\n      case 4:\n        return new AI013103decoder(information);\n      case 5:\n        return new AI01320xDecoder(information);\n    }\n    var fiveBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 5);\n    switch (fiveBitEncodationMethod) {\n      case 12:\n        return new AI01392xDecoder(information);\n      case 13:\n        return new AI01393xDecoder(information);\n    }\n    var sevenBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 7);\n    switch (sevenBitEncodationMethod) {\n      case 56:\n        return new AI013x0x1xDecoder(information, '310', '11');\n      case 57:\n        return new AI013x0x1xDecoder(information, '320', '11');\n      case 58:\n        return new AI013x0x1xDecoder(information, '310', '13');\n      case 59:\n        return new AI013x0x1xDecoder(information, '320', '13');\n      case 60:\n        return new AI013x0x1xDecoder(information, '310', '15');\n      case 61:\n        return new AI013x0x1xDecoder(information, '320', '15');\n      case 62:\n        return new AI013x0x1xDecoder(information, '310', '17');\n      case 63:\n        return new AI013x0x1xDecoder(information, '320', '17');\n    }\n  } catch (e) {\n    console.log(e);\n    throw new IllegalStateException('unknown decoder: ' + information);\n  }\n}", "map": {"version": 3, "names": ["IllegalStateException", "GeneralAppIdDecoder", "AI01AndOtherAIs", "AnyAIDecoder", "AI013103decoder", "AI01320xDecoder", "AI01392xDecoder", "AI01393xDecoder", "AI013x0x1xDecoder", "createDecoder", "information", "get", "fourBitEncodationMethod", "extractNumericValueFromBitArray", "fiveBitEncodationMethod", "sevenBitEncodationMethod", "e", "console", "log"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AbstractExpandedDecoderComplement.js"], "sourcesContent": ["import IllegalStateException from '../../../../IllegalStateException';\nimport GeneralAppIdDecoder from './GeneralAppIdDecoder';\nimport AI01AndOtherAIs from './AI01AndOtherAIs';\nimport AnyAIDecoder from './AnyAIDecoder';\nimport AI013103decoder from './AI013103decoder';\nimport AI01320xDecoder from './AI01320xDecoder';\nimport AI01392xDecoder from './AI01392xDecoder';\nimport AI01393xDecoder from './AI01393xDecoder';\nimport AI013x0x1xDecoder from './AI013x0x1xDecoder';\nexport function createDecoder(information) {\n    try {\n        if (information.get(1)) {\n            return new AI01AndOtherAIs(information);\n        }\n        if (!information.get(2)) {\n            return new AnyAIDecoder(information);\n        }\n        var fourBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 4);\n        switch (fourBitEncodationMethod) {\n            case 4: return new AI013103decoder(information);\n            case 5: return new AI01320xDecoder(information);\n        }\n        var fiveBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 5);\n        switch (fiveBitEncodationMethod) {\n            case 12: return new AI01392xDecoder(information);\n            case 13: return new AI01393xDecoder(information);\n        }\n        var sevenBitEncodationMethod = GeneralAppIdDecoder.extractNumericValueFromBitArray(information, 1, 7);\n        switch (sevenBitEncodationMethod) {\n            case 56: return new AI013x0x1xDecoder(information, '310', '11');\n            case 57: return new AI013x0x1xDecoder(information, '320', '11');\n            case 58: return new AI013x0x1xDecoder(information, '310', '13');\n            case 59: return new AI013x0x1xDecoder(information, '320', '13');\n            case 60: return new AI013x0x1xDecoder(information, '310', '15');\n            case 61: return new AI013x0x1xDecoder(information, '320', '15');\n            case 62: return new AI013x0x1xDecoder(information, '310', '17');\n            case 63: return new AI013x0x1xDecoder(information, '320', '17');\n        }\n    }\n    catch (e) {\n        console.log(e);\n        throw new IllegalStateException('unknown decoder: ' + information);\n    }\n}\n"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,mCAAmC;AACrE,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAO,SAASC,aAAaA,CAACC,WAAW,EAAE;EACvC,IAAI;IACA,IAAIA,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE;MACpB,OAAO,IAAIT,eAAe,CAACQ,WAAW,CAAC;IAC3C;IACA,IAAI,CAACA,WAAW,CAACC,GAAG,CAAC,CAAC,CAAC,EAAE;MACrB,OAAO,IAAIR,YAAY,CAACO,WAAW,CAAC;IACxC;IACA,IAAIE,uBAAuB,GAAGX,mBAAmB,CAACY,+BAA+B,CAACH,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;IACpG,QAAQE,uBAAuB;MAC3B,KAAK,CAAC;QAAE,OAAO,IAAIR,eAAe,CAACM,WAAW,CAAC;MAC/C,KAAK,CAAC;QAAE,OAAO,IAAIL,eAAe,CAACK,WAAW,CAAC;IACnD;IACA,IAAII,uBAAuB,GAAGb,mBAAmB,CAACY,+BAA+B,CAACH,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;IACpG,QAAQI,uBAAuB;MAC3B,KAAK,EAAE;QAAE,OAAO,IAAIR,eAAe,CAACI,WAAW,CAAC;MAChD,KAAK,EAAE;QAAE,OAAO,IAAIH,eAAe,CAACG,WAAW,CAAC;IACpD;IACA,IAAIK,wBAAwB,GAAGd,mBAAmB,CAACY,+BAA+B,CAACH,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;IACrG,QAAQK,wBAAwB;MAC5B,KAAK,EAAE;QAAE,OAAO,IAAIP,iBAAiB,CAACE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;MAC/D,KAAK,EAAE;QAAE,OAAO,IAAIF,iBAAiB,CAACE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;MAC/D,KAAK,EAAE;QAAE,OAAO,IAAIF,iBAAiB,CAACE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;MAC/D,KAAK,EAAE;QAAE,OAAO,IAAIF,iBAAiB,CAACE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;MAC/D,KAAK,EAAE;QAAE,OAAO,IAAIF,iBAAiB,CAACE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;MAC/D,KAAK,EAAE;QAAE,OAAO,IAAIF,iBAAiB,CAACE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;MAC/D,KAAK,EAAE;QAAE,OAAO,IAAIF,iBAAiB,CAACE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;MAC/D,KAAK,EAAE;QAAE,OAAO,IAAIF,iBAAiB,CAACE,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;IACnE;EACJ,CAAC,CACD,OAAOM,CAAC,EAAE;IACNC,OAAO,CAACC,GAAG,CAACF,CAAC,CAAC;IACd,MAAM,IAAIhB,qBAAqB,CAAC,mBAAmB,GAAGU,WAAW,CAAC;EACtE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}