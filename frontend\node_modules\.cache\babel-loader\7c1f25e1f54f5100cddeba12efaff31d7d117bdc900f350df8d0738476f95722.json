{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Encapsulates a type of hint that a caller may pass to a barcode reader to help it\n * more quickly or accurately decode it. It is up to implementations to decide what,\n * if anything, to do with the information that is supplied.\n *\n * <AUTHOR>\n * <AUTHOR> (<PERSON>)\n * @see Reader#decode(BinaryBitmap,java.util.Map)\n */\nvar DecodeHintType;\n(function (DecodeHintType) {\n  /**\n   * Unspecified, application-specific hint. Maps to an unspecified {@link Object}.\n   */\n  DecodeHintType[DecodeHintType[\"OTHER\"] = 0] = \"OTHER\"; /*(Object.class)*/\n  /**\n   * Image is a pure monochrome image of a barcode. Doesn't matter what it maps to;\n   * use {@link Boolean#TRUE}.\n   */\n  DecodeHintType[DecodeHintType[\"PURE_BARCODE\"] = 1] = \"PURE_BARCODE\"; /*(Void.class)*/\n  /**\n   * Image is known to be of one of a few possible formats.\n   * Maps to a {@link List} of {@link BarcodeFormat}s.\n   */\n  DecodeHintType[DecodeHintType[\"POSSIBLE_FORMATS\"] = 2] = \"POSSIBLE_FORMATS\"; /*(List.class)*/\n  /**\n   * Spend more time to try to find a barcode; optimize for accuracy, not speed.\n   * Doesn't matter what it maps to; use {@link Boolean#TRUE}.\n   */\n  DecodeHintType[DecodeHintType[\"TRY_HARDER\"] = 3] = \"TRY_HARDER\"; /*(Void.class)*/\n  /**\n   * Specifies what character encoding to use when decoding, where applicable (type String)\n   */\n  DecodeHintType[DecodeHintType[\"CHARACTER_SET\"] = 4] = \"CHARACTER_SET\"; /*(String.class)*/\n  /**\n   * Allowed lengths of encoded data -- reject anything else. Maps to an {@code Int32Array}.\n   */\n  DecodeHintType[DecodeHintType[\"ALLOWED_LENGTHS\"] = 5] = \"ALLOWED_LENGTHS\"; /*(Int32Array.class)*/\n  /**\n   * Assume Code 39 codes employ a check digit. Doesn't matter what it maps to;\n   * use {@link Boolean#TRUE}.\n   */\n  DecodeHintType[DecodeHintType[\"ASSUME_CODE_39_CHECK_DIGIT\"] = 6] = \"ASSUME_CODE_39_CHECK_DIGIT\"; /*(Void.class)*/\n  /**\n   * Enable extended mode for Code 39 codes. Doesn't matter what it maps to;\n   * use {@link Boolean#TRUE}.\n   */\n  DecodeHintType[DecodeHintType[\"ENABLE_CODE_39_EXTENDED_MODE\"] = 7] = \"ENABLE_CODE_39_EXTENDED_MODE\"; /*(Void.class)*/\n  /**\n   * Assume the barcode is being processed as a GS1 barcode, and modify behavior as needed.\n   * For example this affects FNC1 handling for Code 128 (aka GS1-128). Doesn't matter what it maps to;\n   * use {@link Boolean#TRUE}.\n   */\n  DecodeHintType[DecodeHintType[\"ASSUME_GS1\"] = 8] = \"ASSUME_GS1\"; /*(Void.class)*/\n  /**\n   * If true, return the start and end digits in a Codabar barcode instead of stripping them. They\n   * are alpha, whereas the rest are numeric. By default, they are stripped, but this causes them\n   * to not be. Doesn't matter what it maps to; use {@link Boolean#TRUE}.\n   */\n  DecodeHintType[DecodeHintType[\"RETURN_CODABAR_START_END\"] = 9] = \"RETURN_CODABAR_START_END\"; /*(Void.class)*/\n  /**\n   * The caller needs to be notified via callback when a possible {@link ResultPoint}\n   * is found. Maps to a {@link ResultPointCallback}.\n   */\n  DecodeHintType[DecodeHintType[\"NEED_RESULT_POINT_CALLBACK\"] = 10] = \"NEED_RESULT_POINT_CALLBACK\"; /*(ResultPointCallback.class)*/\n  /**\n   * Allowed extension lengths for EAN or UPC barcodes. Other formats will ignore this.\n   * Maps to an {@code Int32Array} of the allowed extension lengths, for example [2], [5], or [2, 5].\n   * If it is optional to have an extension, do not set this hint. If this is set,\n   * and a UPC or EAN barcode is found but an extension is not, then no result will be returned\n   * at all.\n   */\n  DecodeHintType[DecodeHintType[\"ALLOWED_EAN_EXTENSIONS\"] = 11] = \"ALLOWED_EAN_EXTENSIONS\"; /*(Int32Array.class)*/\n  // End of enumeration values.\n  /**\n   * Data type the hint is expecting.\n   * Among the possible values the {@link Void} stands out as being used for\n   * hints that do not expect a value to be supplied (flag hints). Such hints\n   * will possibly have their value ignored, or replaced by a\n   * {@link Boolean#TRUE}. Hint suppliers should probably use\n   * {@link Boolean#TRUE} as directed by the actual hint documentation.\n   */\n  // private valueType: Class<?>\n  // DecodeHintType(valueType: Class<?>) {\n  //   this.valueType = valueType\n  // }\n  // public getValueType(): Class<?> {\n  //   return valueType\n  // }\n})(DecodeHintType || (DecodeHintType = {}));\nexport default DecodeHintType;", "map": {"version": 3, "names": ["DecodeHintType"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/DecodeHintType.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * Encapsulates a type of hint that a caller may pass to a barcode reader to help it\n * more quickly or accurately decode it. It is up to implementations to decide what,\n * if anything, to do with the information that is supplied.\n *\n * <AUTHOR>\n * <AUTHOR> (<PERSON>)\n * @see Reader#decode(BinaryBitmap,java.util.Map)\n */\nvar DecodeHintType;\n(function (DecodeHintType) {\n    /**\n     * Unspecified, application-specific hint. Maps to an unspecified {@link Object}.\n     */\n    DecodeHintType[DecodeHintType[\"OTHER\"] = 0] = \"OTHER\"; /*(Object.class)*/\n    /**\n     * Image is a pure monochrome image of a barcode. Doesn't matter what it maps to;\n     * use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"PURE_BARCODE\"] = 1] = \"PURE_BARCODE\"; /*(Void.class)*/\n    /**\n     * Image is known to be of one of a few possible formats.\n     * Maps to a {@link List} of {@link BarcodeFormat}s.\n     */\n    DecodeHintType[DecodeHintType[\"POSSIBLE_FORMATS\"] = 2] = \"POSSIBLE_FORMATS\"; /*(List.class)*/\n    /**\n     * Spend more time to try to find a barcode; optimize for accuracy, not speed.\n     * Doesn't matter what it maps to; use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"TRY_HARDER\"] = 3] = \"TRY_HARDER\"; /*(Void.class)*/\n    /**\n     * Specifies what character encoding to use when decoding, where applicable (type String)\n     */\n    DecodeHintType[DecodeHintType[\"CHARACTER_SET\"] = 4] = \"CHARACTER_SET\"; /*(String.class)*/\n    /**\n     * Allowed lengths of encoded data -- reject anything else. Maps to an {@code Int32Array}.\n     */\n    DecodeHintType[DecodeHintType[\"ALLOWED_LENGTHS\"] = 5] = \"ALLOWED_LENGTHS\"; /*(Int32Array.class)*/\n    /**\n     * Assume Code 39 codes employ a check digit. Doesn't matter what it maps to;\n     * use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"ASSUME_CODE_39_CHECK_DIGIT\"] = 6] = \"ASSUME_CODE_39_CHECK_DIGIT\"; /*(Void.class)*/\n    /**\n     * Enable extended mode for Code 39 codes. Doesn't matter what it maps to;\n     * use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"ENABLE_CODE_39_EXTENDED_MODE\"] = 7] = \"ENABLE_CODE_39_EXTENDED_MODE\"; /*(Void.class)*/\n    /**\n     * Assume the barcode is being processed as a GS1 barcode, and modify behavior as needed.\n     * For example this affects FNC1 handling for Code 128 (aka GS1-128). Doesn't matter what it maps to;\n     * use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"ASSUME_GS1\"] = 8] = \"ASSUME_GS1\"; /*(Void.class)*/\n    /**\n     * If true, return the start and end digits in a Codabar barcode instead of stripping them. They\n     * are alpha, whereas the rest are numeric. By default, they are stripped, but this causes them\n     * to not be. Doesn't matter what it maps to; use {@link Boolean#TRUE}.\n     */\n    DecodeHintType[DecodeHintType[\"RETURN_CODABAR_START_END\"] = 9] = \"RETURN_CODABAR_START_END\"; /*(Void.class)*/\n    /**\n     * The caller needs to be notified via callback when a possible {@link ResultPoint}\n     * is found. Maps to a {@link ResultPointCallback}.\n     */\n    DecodeHintType[DecodeHintType[\"NEED_RESULT_POINT_CALLBACK\"] = 10] = \"NEED_RESULT_POINT_CALLBACK\"; /*(ResultPointCallback.class)*/\n    /**\n     * Allowed extension lengths for EAN or UPC barcodes. Other formats will ignore this.\n     * Maps to an {@code Int32Array} of the allowed extension lengths, for example [2], [5], or [2, 5].\n     * If it is optional to have an extension, do not set this hint. If this is set,\n     * and a UPC or EAN barcode is found but an extension is not, then no result will be returned\n     * at all.\n     */\n    DecodeHintType[DecodeHintType[\"ALLOWED_EAN_EXTENSIONS\"] = 11] = \"ALLOWED_EAN_EXTENSIONS\"; /*(Int32Array.class)*/\n    // End of enumeration values.\n    /**\n     * Data type the hint is expecting.\n     * Among the possible values the {@link Void} stands out as being used for\n     * hints that do not expect a value to be supplied (flag hints). Such hints\n     * will possibly have their value ignored, or replaced by a\n     * {@link Boolean#TRUE}. Hint suppliers should probably use\n     * {@link Boolean#TRUE} as directed by the actual hint documentation.\n     */\n    // private valueType: Class<?>\n    // DecodeHintType(valueType: Class<?>) {\n    //   this.valueType = valueType\n    // }\n    // public getValueType(): Class<?> {\n    //   return valueType\n    // }\n})(DecodeHintType || (DecodeHintType = {}));\nexport default DecodeHintType;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;EACvD;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC;EACrE;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC;EAC7E;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;EACjE;AACJ;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC;EACvE;AACJ;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,GAAG,iBAAiB,CAAC,CAAC;EAC3E;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,GAAG,4BAA4B,CAAC,CAAC;EACjG;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,8BAA8B,CAAC,GAAG,CAAC,CAAC,GAAG,8BAA8B,CAAC,CAAC;EACrG;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;EACjE;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,GAAG,0BAA0B,CAAC,CAAC;EAC7F;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,4BAA4B,CAAC,GAAG,EAAE,CAAC,GAAG,4BAA4B,CAAC,CAAC;EAClG;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,wBAAwB,CAAC,GAAG,EAAE,CAAC,GAAG,wBAAwB,CAAC,CAAC;EAC1F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;AACJ,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}