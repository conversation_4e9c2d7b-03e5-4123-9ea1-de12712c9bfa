{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\nimport IllegalArgumentException from '../IllegalArgumentException';\n/**\n * <p>This provides an easy abstraction to read bits at a time from a sequence of bytes, where the\n * number of bits read is not often a multiple of 8.</p>\n *\n * <p>This class is thread-safe but not reentrant -- unless the caller modifies the bytes array\n * it passed in, in which case all bets are off.</p>\n *\n * <AUTHOR>\n */\nvar BitSource = /** @class */function () {\n  /**\n   * @param bytes bytes from which this will read bits. Bits will be read from the first byte first.\n   * Bits are read within a byte from most-significant to least-significant bit.\n   */\n  function BitSource(bytes) {\n    this.bytes = bytes;\n    this.byteOffset = 0;\n    this.bitOffset = 0;\n  }\n  /**\n   * @return index of next bit in current byte which would be read by the next call to {@link #readBits(int)}.\n   */\n  BitSource.prototype.getBitOffset = function () {\n    return this.bitOffset;\n  };\n  /**\n   * @return index of next byte in input byte array which would be read by the next call to {@link #readBits(int)}.\n   */\n  BitSource.prototype.getByteOffset = function () {\n    return this.byteOffset;\n  };\n  /**\n   * @param numBits number of bits to read\n   * @return int representing the bits read. The bits will appear as the least-significant\n   *         bits of the int\n   * @throws IllegalArgumentException if numBits isn't in [1,32] or more than is available\n   */\n  BitSource.prototype.readBits = function (numBits /*int*/) {\n    if (numBits < 1 || numBits > 32 || numBits > this.available()) {\n      throw new IllegalArgumentException('' + numBits);\n    }\n    var result = 0;\n    var bitOffset = this.bitOffset;\n    var byteOffset = this.byteOffset;\n    var bytes = this.bytes;\n    // First, read remainder from current byte\n    if (bitOffset > 0) {\n      var bitsLeft = 8 - bitOffset;\n      var toRead = numBits < bitsLeft ? numBits : bitsLeft;\n      var bitsToNotRead = bitsLeft - toRead;\n      var mask = 0xFF >> 8 - toRead << bitsToNotRead;\n      result = (bytes[byteOffset] & mask) >> bitsToNotRead;\n      numBits -= toRead;\n      bitOffset += toRead;\n      if (bitOffset === 8) {\n        bitOffset = 0;\n        byteOffset++;\n      }\n    }\n    // Next read whole bytes\n    if (numBits > 0) {\n      while (numBits >= 8) {\n        result = result << 8 | bytes[byteOffset] & 0xFF;\n        byteOffset++;\n        numBits -= 8;\n      }\n      // Finally read a partial byte\n      if (numBits > 0) {\n        var bitsToNotRead = 8 - numBits;\n        var mask = 0xFF >> bitsToNotRead << bitsToNotRead;\n        result = result << numBits | (bytes[byteOffset] & mask) >> bitsToNotRead;\n        bitOffset += numBits;\n      }\n    }\n    this.bitOffset = bitOffset;\n    this.byteOffset = byteOffset;\n    return result;\n  };\n  /**\n   * @return number of bits that can be read successfully\n   */\n  BitSource.prototype.available = function () {\n    return 8 * (this.bytes.length - this.byteOffset) - this.bitOffset;\n  };\n  return BitSource;\n}();\nexport default BitSource;", "map": {"version": 3, "names": ["IllegalArgumentException", "BitSource", "bytes", "byteOffset", "bitOffset", "prototype", "getBitOffset", "getByteOffset", "readBits", "numBits", "available", "result", "bitsLeft", "toRead", "bitsToNotRead", "mask", "length"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/BitSource.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\nimport IllegalArgumentException from '../IllegalArgumentException';\n/**\n * <p>This provides an easy abstraction to read bits at a time from a sequence of bytes, where the\n * number of bits read is not often a multiple of 8.</p>\n *\n * <p>This class is thread-safe but not reentrant -- unless the caller modifies the bytes array\n * it passed in, in which case all bets are off.</p>\n *\n * <AUTHOR>\n */\nvar BitSource = /** @class */ (function () {\n    /**\n     * @param bytes bytes from which this will read bits. Bits will be read from the first byte first.\n     * Bits are read within a byte from most-significant to least-significant bit.\n     */\n    function BitSource(bytes) {\n        this.bytes = bytes;\n        this.byteOffset = 0;\n        this.bitOffset = 0;\n    }\n    /**\n     * @return index of next bit in current byte which would be read by the next call to {@link #readBits(int)}.\n     */\n    BitSource.prototype.getBitOffset = function () {\n        return this.bitOffset;\n    };\n    /**\n     * @return index of next byte in input byte array which would be read by the next call to {@link #readBits(int)}.\n     */\n    BitSource.prototype.getByteOffset = function () {\n        return this.byteOffset;\n    };\n    /**\n     * @param numBits number of bits to read\n     * @return int representing the bits read. The bits will appear as the least-significant\n     *         bits of the int\n     * @throws IllegalArgumentException if numBits isn't in [1,32] or more than is available\n     */\n    BitSource.prototype.readBits = function (numBits /*int*/) {\n        if (numBits < 1 || numBits > 32 || numBits > this.available()) {\n            throw new IllegalArgumentException('' + numBits);\n        }\n        var result = 0;\n        var bitOffset = this.bitOffset;\n        var byteOffset = this.byteOffset;\n        var bytes = this.bytes;\n        // First, read remainder from current byte\n        if (bitOffset > 0) {\n            var bitsLeft = 8 - bitOffset;\n            var toRead = numBits < bitsLeft ? numBits : bitsLeft;\n            var bitsToNotRead = bitsLeft - toRead;\n            var mask = (0xFF >> (8 - toRead)) << bitsToNotRead;\n            result = (bytes[byteOffset] & mask) >> bitsToNotRead;\n            numBits -= toRead;\n            bitOffset += toRead;\n            if (bitOffset === 8) {\n                bitOffset = 0;\n                byteOffset++;\n            }\n        }\n        // Next read whole bytes\n        if (numBits > 0) {\n            while (numBits >= 8) {\n                result = (result << 8) | (bytes[byteOffset] & 0xFF);\n                byteOffset++;\n                numBits -= 8;\n            }\n            // Finally read a partial byte\n            if (numBits > 0) {\n                var bitsToNotRead = 8 - numBits;\n                var mask = (0xFF >> bitsToNotRead) << bitsToNotRead;\n                result = (result << numBits) | ((bytes[byteOffset] & mask) >> bitsToNotRead);\n                bitOffset += numBits;\n            }\n        }\n        this.bitOffset = bitOffset;\n        this.byteOffset = byteOffset;\n        return result;\n    };\n    /**\n     * @return number of bits that can be read successfully\n     */\n    BitSource.prototype.available = function () {\n        return 8 * (this.bytes.length - this.byteOffset) - this.bitOffset;\n    };\n    return BitSource;\n}());\nexport default BitSource;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,wBAAwB,MAAM,6BAA6B;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,YAAY;EACvC;AACJ;AACA;AACA;EACI,SAASA,SAASA,CAACC,KAAK,EAAE;IACtB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,SAAS,GAAG,CAAC;EACtB;EACA;AACJ;AACA;EACIH,SAAS,CAACI,SAAS,CAACC,YAAY,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACF,SAAS;EACzB,CAAC;EACD;AACJ;AACA;EACIH,SAAS,CAACI,SAAS,CAACE,aAAa,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACJ,UAAU;EAC1B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIF,SAAS,CAACI,SAAS,CAACG,QAAQ,GAAG,UAAUC,OAAO,CAAC,SAAS;IACtD,IAAIA,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,EAAE,IAAIA,OAAO,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE;MAC3D,MAAM,IAAIV,wBAAwB,CAAC,EAAE,GAAGS,OAAO,CAAC;IACpD;IACA,IAAIE,MAAM,GAAG,CAAC;IACd,IAAIP,SAAS,GAAG,IAAI,CAACA,SAAS;IAC9B,IAAID,UAAU,GAAG,IAAI,CAACA,UAAU;IAChC,IAAID,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB;IACA,IAAIE,SAAS,GAAG,CAAC,EAAE;MACf,IAAIQ,QAAQ,GAAG,CAAC,GAAGR,SAAS;MAC5B,IAAIS,MAAM,GAAGJ,OAAO,GAAGG,QAAQ,GAAGH,OAAO,GAAGG,QAAQ;MACpD,IAAIE,aAAa,GAAGF,QAAQ,GAAGC,MAAM;MACrC,IAAIE,IAAI,GAAI,IAAI,IAAK,CAAC,GAAGF,MAAO,IAAKC,aAAa;MAClDH,MAAM,GAAG,CAACT,KAAK,CAACC,UAAU,CAAC,GAAGY,IAAI,KAAKD,aAAa;MACpDL,OAAO,IAAII,MAAM;MACjBT,SAAS,IAAIS,MAAM;MACnB,IAAIT,SAAS,KAAK,CAAC,EAAE;QACjBA,SAAS,GAAG,CAAC;QACbD,UAAU,EAAE;MAChB;IACJ;IACA;IACA,IAAIM,OAAO,GAAG,CAAC,EAAE;MACb,OAAOA,OAAO,IAAI,CAAC,EAAE;QACjBE,MAAM,GAAIA,MAAM,IAAI,CAAC,GAAKT,KAAK,CAACC,UAAU,CAAC,GAAG,IAAK;QACnDA,UAAU,EAAE;QACZM,OAAO,IAAI,CAAC;MAChB;MACA;MACA,IAAIA,OAAO,GAAG,CAAC,EAAE;QACb,IAAIK,aAAa,GAAG,CAAC,GAAGL,OAAO;QAC/B,IAAIM,IAAI,GAAI,IAAI,IAAID,aAAa,IAAKA,aAAa;QACnDH,MAAM,GAAIA,MAAM,IAAIF,OAAO,GAAK,CAACP,KAAK,CAACC,UAAU,CAAC,GAAGY,IAAI,KAAKD,aAAc;QAC5EV,SAAS,IAAIK,OAAO;MACxB;IACJ;IACA,IAAI,CAACL,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,OAAOQ,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACIV,SAAS,CAACI,SAAS,CAACK,SAAS,GAAG,YAAY;IACxC,OAAO,CAAC,IAAI,IAAI,CAACR,KAAK,CAACc,MAAM,GAAG,IAAI,CAACb,UAAU,CAAC,GAAG,IAAI,CAACC,SAAS;EACrE,CAAC;EACD,OAAOH,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}