{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * This class hierarchy provides a set of methods to convert luminance data to 1 bit data.\n * It allows the algorithm to vary polymorphically, for example allowing a very expensive\n * thresholding technique for servers and a fast one for mobile. It also permits the implementation\n * to vary, e.g. a JNI version for Android and a Java fallback version for other platforms.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar Binarizer = /** @class */function () {\n  function Binarizer(source) {\n    this.source = source;\n  }\n  Binarizer.prototype.getLuminanceSource = function () {\n    return this.source;\n  };\n  Binarizer.prototype.getWidth = function () {\n    return this.source.getWidth();\n  };\n  Binarizer.prototype.getHeight = function () {\n    return this.source.getHeight();\n  };\n  return Binarizer;\n}();\nexport default Binarizer;", "map": {"version": 3, "names": ["Binarizer", "source", "prototype", "getLuminanceSource", "getWidth", "getHeight"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/Binarizer.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * This class hierarchy provides a set of methods to convert luminance data to 1 bit data.\n * It allows the algorithm to vary polymorphically, for example allowing a very expensive\n * thresholding technique for servers and a fast one for mobile. It also permits the implementation\n * to vary, e.g. a JNI version for Android and a Java fallback version for other platforms.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar Binarizer = /** @class */ (function () {\n    function Binarizer(source) {\n        this.source = source;\n    }\n    Binarizer.prototype.getLuminanceSource = function () {\n        return this.source;\n    };\n    Binarizer.prototype.getWidth = function () {\n        return this.source.getWidth();\n    };\n    Binarizer.prototype.getHeight = function () {\n        return this.source.getHeight();\n    };\n    return Binarizer;\n}());\nexport default Binarizer;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACC,MAAM,EAAE;IACvB,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB;EACAD,SAAS,CAACE,SAAS,CAACC,kBAAkB,GAAG,YAAY;IACjD,OAAO,IAAI,CAACF,MAAM;EACtB,CAAC;EACDD,SAAS,CAACE,SAAS,CAACE,QAAQ,GAAG,YAAY;IACvC,OAAO,IAAI,CAACH,MAAM,CAACG,QAAQ,CAAC,CAAC;EACjC,CAAC;EACDJ,SAAS,CAACE,SAAS,CAACG,SAAS,GAAG,YAAY;IACxC,OAAO,IAAI,CAACJ,MAAM,CAACI,SAAS,CAAC,CAAC;EAClC,CAAC;EACD,OAAOL,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}