{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _constants = require('./constants');\nvar _Barcode2 = require('../Barcode');\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\nvar ITF = function (_Barcode) {\n  _inherits(ITF, _Barcode);\n  function ITF() {\n    _classCallCheck(this, ITF);\n    return _possibleConstructorReturn(this, (ITF.__proto__ || Object.getPrototypeOf(ITF)).apply(this, arguments));\n  }\n  _createClass(ITF, [{\n    key: 'valid',\n    value: function valid() {\n      return this.data.search(/^([0-9]{2})+$/) !== -1;\n    }\n  }, {\n    key: 'encode',\n    value: function encode() {\n      var _this2 = this;\n\n      // Calculate all the digit pairs\n      var encoded = this.data.match(/.{2}/g).map(function (pair) {\n        return _this2.encodePair(pair);\n      }).join('');\n      return {\n        data: _constants.START_BIN + encoded + _constants.END_BIN,\n        text: this.text\n      };\n    }\n\n    // Calculate the data of a number pair\n  }, {\n    key: 'encodePair',\n    value: function encodePair(pair) {\n      var second = _constants.BINARIES[pair[1]];\n      return _constants.BINARIES[pair[0]].split('').map(function (first, idx) {\n        return (first === '1' ? '111' : '1') + (second[idx] === '1' ? '000' : '0');\n      }).join('');\n    }\n  }]);\n  return ITF;\n}(_Barcode3.default);\nexports.default = ITF;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_constants", "require", "_Barcode2", "_Barcode3", "_interopRequireDefault", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "call", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "ITF", "_Barcode", "getPrototypeOf", "apply", "arguments", "valid", "data", "search", "encode", "_this2", "encoded", "match", "map", "pair", "encodePair", "join", "START_BIN", "END_BIN", "text", "second", "BINARIES", "split", "first", "idx"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/ITF/ITF.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _constants = require('./constants');\n\nvar _Barcode2 = require('../Barcode');\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar ITF = function (_Barcode) {\n\t_inherits(ITF, _Barcode);\n\n\tfunction ITF() {\n\t\t_classCallCheck(this, ITF);\n\n\t\treturn _possibleConstructorReturn(this, (ITF.__proto__ || Object.getPrototypeOf(ITF)).apply(this, arguments));\n\t}\n\n\t_createClass(ITF, [{\n\t\tkey: 'valid',\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^([0-9]{2})+$/) !== -1;\n\t\t}\n\t}, {\n\t\tkey: 'encode',\n\t\tvalue: function encode() {\n\t\t\tvar _this2 = this;\n\n\t\t\t// Calculate all the digit pairs\n\t\t\tvar encoded = this.data.match(/.{2}/g).map(function (pair) {\n\t\t\t\treturn _this2.encodePair(pair);\n\t\t\t}).join('');\n\n\t\t\treturn {\n\t\t\t\tdata: _constants.START_BIN + encoded + _constants.END_BIN,\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\n\t\t// Calculate the data of a number pair\n\n\t}, {\n\t\tkey: 'encodePair',\n\t\tvalue: function encodePair(pair) {\n\t\t\tvar second = _constants.BINARIES[pair[1]];\n\n\t\t\treturn _constants.BINARIES[pair[0]].split('').map(function (first, idx) {\n\t\t\t\treturn (first === '1' ? '111' : '1') + (second[idx] === '1' ? '000' : '0');\n\t\t\t}).join('');\n\t\t}\n\t}]);\n\n\treturn ITF;\n}(_Barcode3.default);\n\nexports.default = ITF;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AAEvC,IAAIC,SAAS,GAAGD,OAAO,CAAC,YAAY,CAAC;AAErC,IAAIE,SAAS,GAAGC,sBAAsB,CAACF,SAAS,CAAC;AAEjD,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEb,WAAW,EAAE;EAAE,IAAI,EAAEa,QAAQ,YAAYb,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIc,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAI,CAACD,IAAI,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGD,IAAI;AAAE;AAE/O,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIP,SAAS,CAAC,0DAA0D,GAAG,OAAOO,UAAU,CAAC;EAAE;EAAED,QAAQ,CAACjB,SAAS,GAAGlB,MAAM,CAACqC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAClB,SAAS,EAAE;IAAEoB,WAAW,EAAE;MAAEnC,KAAK,EAAEgC,QAAQ;MAAExB,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIwB,UAAU,EAAEpC,MAAM,CAACuC,cAAc,GAAGvC,MAAM,CAACuC,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AAAE;AAE7e,IAAIK,GAAG,GAAG,UAAUC,QAAQ,EAAE;EAC7BR,SAAS,CAACO,GAAG,EAAEC,QAAQ,CAAC;EAExB,SAASD,GAAGA,CAAA,EAAG;IACdd,eAAe,CAAC,IAAI,EAAEc,GAAG,CAAC;IAE1B,OAAOX,0BAA0B,CAAC,IAAI,EAAE,CAACW,GAAG,CAACD,SAAS,IAAIxC,MAAM,CAAC2C,cAAc,CAACF,GAAG,CAAC,EAAEG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EAC9G;EAEAzC,YAAY,CAACqC,GAAG,EAAE,CAAC;IAClB3B,GAAG,EAAE,OAAO;IACZX,KAAK,EAAE,SAAS2C,KAAKA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACC,IAAI,CAACC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;IAChD;EACD,CAAC,EAAE;IACFlC,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAAS8C,MAAMA,CAAA,EAAG;MACxB,IAAIC,MAAM,GAAG,IAAI;;MAEjB;MACA,IAAIC,OAAO,GAAG,IAAI,CAACJ,IAAI,CAACK,KAAK,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC1D,OAAOJ,MAAM,CAACK,UAAU,CAACD,IAAI,CAAC;MAC/B,CAAC,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;MAEX,OAAO;QACNT,IAAI,EAAE5B,UAAU,CAACsC,SAAS,GAAGN,OAAO,GAAGhC,UAAU,CAACuC,OAAO;QACzDC,IAAI,EAAE,IAAI,CAACA;MACZ,CAAC;IACF;;IAEA;EAED,CAAC,EAAE;IACF7C,GAAG,EAAE,YAAY;IACjBX,KAAK,EAAE,SAASoD,UAAUA,CAACD,IAAI,EAAE;MAChC,IAAIM,MAAM,GAAGzC,UAAU,CAAC0C,QAAQ,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC;MAEzC,OAAOnC,UAAU,CAAC0C,QAAQ,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC,CAACQ,KAAK,CAAC,EAAE,CAAC,CAACT,GAAG,CAAC,UAAUU,KAAK,EAAEC,GAAG,EAAE;QACvE,OAAO,CAACD,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAKH,MAAM,CAACI,GAAG,CAAC,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,CAAC;MAC3E,CAAC,CAAC,CAACR,IAAI,CAAC,EAAE,CAAC;IACZ;EACD,CAAC,CAAC,CAAC;EAEH,OAAOf,GAAG;AACX,CAAC,CAACnB,SAAS,CAACI,OAAO,CAAC;AAEpBxB,OAAO,CAACwB,OAAO,GAAGe,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}