{"ast": null, "code": "/**\n * Java Formatter class polyfill that works in the JS way.\n */\nvar Formatter = /** @class */function () {\n  function Formatter() {\n    this.buffer = '';\n  }\n  /**\n   *\n   * @see https://stackoverflow.com/a/13439711/4367683\n   *\n   * @param str\n   * @param arr\n   */\n  Formatter.form = function (str, arr) {\n    var i = -1;\n    function callback(exp, p0, p1, p2, p3, p4) {\n      if (exp === '%%') return '%';\n      if (arr[++i] === undefined) return undefined;\n      exp = p2 ? parseInt(p2.substr(1)) : undefined;\n      var base = p3 ? parseInt(p3.substr(1)) : undefined;\n      var val;\n      switch (p4) {\n        case 's':\n          val = arr[i];\n          break;\n        case 'c':\n          val = arr[i][0];\n          break;\n        case 'f':\n          val = parseFloat(arr[i]).toFixed(exp);\n          break;\n        case 'p':\n          val = parseFloat(arr[i]).toPrecision(exp);\n          break;\n        case 'e':\n          val = parseFloat(arr[i]).toExponential(exp);\n          break;\n        case 'x':\n          val = parseInt(arr[i]).toString(base ? base : 16);\n          break;\n        case 'd':\n          val = parseFloat(parseInt(arr[i], base ? base : 10).toPrecision(exp)).toFixed(0);\n          break;\n      }\n      val = typeof val === 'object' ? JSON.stringify(val) : (+val).toString(base);\n      var size = parseInt(p1); /* padding size */\n      var ch = p1 && p1[0] + '' === '0' ? '0' : ' '; /* isnull? */\n      while (val.length < size) val = p0 !== undefined ? val + ch : ch + val; /* isminus? */\n      return val;\n    }\n    var regex = /%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;\n    return str.replace(regex, callback);\n  };\n  /**\n   *\n   * @param append The new string to append.\n   * @param args Argumets values to be formated.\n   */\n  Formatter.prototype.format = function (append) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    this.buffer += Formatter.form(append, args);\n  };\n  /**\n   * Returns the Formatter string value.\n   */\n  Formatter.prototype.toString = function () {\n    return this.buffer;\n  };\n  return Formatter;\n}();\nexport default Formatter;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "buffer", "form", "str", "arr", "i", "callback", "exp", "p0", "p1", "p2", "p3", "p4", "undefined", "parseInt", "substr", "base", "val", "parseFloat", "toFixed", "toPrecision", "toExponential", "toString", "JSON", "stringify", "size", "ch", "length", "regex", "replace", "prototype", "format", "append", "args", "_i", "arguments"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/Formatter.js"], "sourcesContent": ["/**\n * Java Formatter class polyfill that works in the JS way.\n */\nvar Formatter = /** @class */ (function () {\n    function Formatter() {\n        this.buffer = '';\n    }\n    /**\n     *\n     * @see https://stackoverflow.com/a/13439711/4367683\n     *\n     * @param str\n     * @param arr\n     */\n    Formatter.form = function (str, arr) {\n        var i = -1;\n        function callback(exp, p0, p1, p2, p3, p4) {\n            if (exp === '%%')\n                return '%';\n            if (arr[++i] === undefined)\n                return undefined;\n            exp = p2 ? parseInt(p2.substr(1)) : undefined;\n            var base = p3 ? parseInt(p3.substr(1)) : undefined;\n            var val;\n            switch (p4) {\n                case 's':\n                    val = arr[i];\n                    break;\n                case 'c':\n                    val = arr[i][0];\n                    break;\n                case 'f':\n                    val = parseFloat(arr[i]).toFixed(exp);\n                    break;\n                case 'p':\n                    val = parseFloat(arr[i]).toPrecision(exp);\n                    break;\n                case 'e':\n                    val = parseFloat(arr[i]).toExponential(exp);\n                    break;\n                case 'x':\n                    val = parseInt(arr[i]).toString(base ? base : 16);\n                    break;\n                case 'd':\n                    val = parseFloat(parseInt(arr[i], base ? base : 10).toPrecision(exp)).toFixed(0);\n                    break;\n            }\n            val = typeof val === 'object' ? JSON.stringify(val) : (+val).toString(base);\n            var size = parseInt(p1); /* padding size */\n            var ch = p1 && (p1[0] + '') === '0' ? '0' : ' '; /* isnull? */\n            while (val.length < size)\n                val = p0 !== undefined ? val + ch : ch + val; /* isminus? */\n            return val;\n        }\n        var regex = /%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;\n        return str.replace(regex, callback);\n    };\n    /**\n     *\n     * @param append The new string to append.\n     * @param args Argumets values to be formated.\n     */\n    Formatter.prototype.format = function (append) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        this.buffer += Formatter.form(append, args);\n    };\n    /**\n     * Returns the Formatter string value.\n     */\n    Formatter.prototype.toString = function () {\n        return this.buffer;\n    };\n    return Formatter;\n}());\nexport default Formatter;\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAAA,EAAG;IACjB,IAAI,CAACC,MAAM,GAAG,EAAE;EACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACID,SAAS,CAACE,IAAI,GAAG,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACjC,IAAIC,CAAC,GAAG,CAAC,CAAC;IACV,SAASC,QAAQA,CAACC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MACvC,IAAIL,GAAG,KAAK,IAAI,EACZ,OAAO,GAAG;MACd,IAAIH,GAAG,CAAC,EAAEC,CAAC,CAAC,KAAKQ,SAAS,EACtB,OAAOA,SAAS;MACpBN,GAAG,GAAGG,EAAE,GAAGI,QAAQ,CAACJ,EAAE,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGF,SAAS;MAC7C,IAAIG,IAAI,GAAGL,EAAE,GAAGG,QAAQ,CAACH,EAAE,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGF,SAAS;MAClD,IAAII,GAAG;MACP,QAAQL,EAAE;QACN,KAAK,GAAG;UACJK,GAAG,GAAGb,GAAG,CAACC,CAAC,CAAC;UACZ;QACJ,KAAK,GAAG;UACJY,GAAG,GAAGb,GAAG,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;UACf;QACJ,KAAK,GAAG;UACJY,GAAG,GAAGC,UAAU,CAACd,GAAG,CAACC,CAAC,CAAC,CAAC,CAACc,OAAO,CAACZ,GAAG,CAAC;UACrC;QACJ,KAAK,GAAG;UACJU,GAAG,GAAGC,UAAU,CAACd,GAAG,CAACC,CAAC,CAAC,CAAC,CAACe,WAAW,CAACb,GAAG,CAAC;UACzC;QACJ,KAAK,GAAG;UACJU,GAAG,GAAGC,UAAU,CAACd,GAAG,CAACC,CAAC,CAAC,CAAC,CAACgB,aAAa,CAACd,GAAG,CAAC;UAC3C;QACJ,KAAK,GAAG;UACJU,GAAG,GAAGH,QAAQ,CAACV,GAAG,CAACC,CAAC,CAAC,CAAC,CAACiB,QAAQ,CAACN,IAAI,GAAGA,IAAI,GAAG,EAAE,CAAC;UACjD;QACJ,KAAK,GAAG;UACJC,GAAG,GAAGC,UAAU,CAACJ,QAAQ,CAACV,GAAG,CAACC,CAAC,CAAC,EAAEW,IAAI,GAAGA,IAAI,GAAG,EAAE,CAAC,CAACI,WAAW,CAACb,GAAG,CAAC,CAAC,CAACY,OAAO,CAAC,CAAC,CAAC;UAChF;MACR;MACAF,GAAG,GAAG,OAAOA,GAAG,KAAK,QAAQ,GAAGM,IAAI,CAACC,SAAS,CAACP,GAAG,CAAC,GAAG,CAAC,CAACA,GAAG,EAAEK,QAAQ,CAACN,IAAI,CAAC;MAC3E,IAAIS,IAAI,GAAGX,QAAQ,CAACL,EAAE,CAAC,CAAC,CAAC;MACzB,IAAIiB,EAAE,GAAGjB,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,KAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;MACjD,OAAOQ,GAAG,CAACU,MAAM,GAAGF,IAAI,EACpBR,GAAG,GAAGT,EAAE,KAAKK,SAAS,GAAGI,GAAG,GAAGS,EAAE,GAAGA,EAAE,GAAGT,GAAG,CAAC,CAAC;MAClD,OAAOA,GAAG;IACd;IACA,IAAIW,KAAK,GAAG,uDAAuD;IACnE,OAAOzB,GAAG,CAAC0B,OAAO,CAACD,KAAK,EAAEtB,QAAQ,CAAC;EACvC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIN,SAAS,CAAC8B,SAAS,CAACC,MAAM,GAAG,UAAUC,MAAM,EAAE;IAC3C,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACR,MAAM,EAAEO,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAChC;IACA,IAAI,CAACjC,MAAM,IAAID,SAAS,CAACE,IAAI,CAAC8B,MAAM,EAAEC,IAAI,CAAC;EAC/C,CAAC;EACD;AACJ;AACA;EACIjC,SAAS,CAAC8B,SAAS,CAACR,QAAQ,GAAG,YAAY;IACvC,OAAO,IAAI,CAACrB,MAAM;EACtB,CAAC;EACD,OAAOD,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}