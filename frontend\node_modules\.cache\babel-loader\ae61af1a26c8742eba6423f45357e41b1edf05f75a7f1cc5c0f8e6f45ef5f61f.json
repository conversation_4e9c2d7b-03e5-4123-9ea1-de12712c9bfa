{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _constants = require('./constants');\n\n// Encode data string\nvar encode = function encode(data, structure, separator) {\n  var encoded = data.split('').map(function (val, idx) {\n    return _constants.BINARIES[structure[idx]];\n  }).map(function (val, idx) {\n    return val ? val[data[idx]] : '';\n  });\n  if (separator) {\n    var last = data.length - 1;\n    encoded = encoded.map(function (val, idx) {\n      return idx < last ? val + separator : val;\n    });\n  }\n  return encoded.join('');\n};\nexports.default = encode;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_constants", "require", "encode", "data", "structure", "separator", "encoded", "split", "map", "val", "idx", "BINARIES", "last", "length", "join", "default"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/EAN_UPC/encoder.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _constants = require('./constants');\n\n// Encode data string\nvar encode = function encode(data, structure, separator) {\n\tvar encoded = data.split('').map(function (val, idx) {\n\t\treturn _constants.BINARIES[structure[idx]];\n\t}).map(function (val, idx) {\n\t\treturn val ? val[data[idx]] : '';\n\t});\n\n\tif (separator) {\n\t\tvar last = data.length - 1;\n\t\tencoded = encoded.map(function (val, idx) {\n\t\t\treturn idx < last ? val + separator : val;\n\t\t});\n\t}\n\n\treturn encoded.join('');\n};\n\nexports.default = encode;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAEvC;AACA,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAE;EACxD,IAAIC,OAAO,GAAGH,IAAI,CAACI,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACpD,OAAOV,UAAU,CAACW,QAAQ,CAACP,SAAS,CAACM,GAAG,CAAC,CAAC;EAC3C,CAAC,CAAC,CAACF,GAAG,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IAC1B,OAAOD,GAAG,GAAGA,GAAG,CAACN,IAAI,CAACO,GAAG,CAAC,CAAC,GAAG,EAAE;EACjC,CAAC,CAAC;EAEF,IAAIL,SAAS,EAAE;IACd,IAAIO,IAAI,GAAGT,IAAI,CAACU,MAAM,GAAG,CAAC;IAC1BP,OAAO,GAAGA,OAAO,CAACE,GAAG,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;MACzC,OAAOA,GAAG,GAAGE,IAAI,GAAGH,GAAG,GAAGJ,SAAS,GAAGI,GAAG;IAC1C,CAAC,CAAC;EACH;EAEA,OAAOH,OAAO,CAACQ,IAAI,CAAC,EAAE,CAAC;AACxB,CAAC;AAEDhB,OAAO,CAACiB,OAAO,GAAGb,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}