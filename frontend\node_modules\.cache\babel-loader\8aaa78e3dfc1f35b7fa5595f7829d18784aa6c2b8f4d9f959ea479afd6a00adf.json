{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport EncodeHintType from '../EncodeHintType';\nimport BitMatrix from '../common/BitMatrix';\nimport ErrorCorrectionLevel from './decoder/ErrorCorrectionLevel';\nimport Encoder from './encoder/Encoder';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport IllegalStateException from '../IllegalStateException';\n/*import java.util.Map;*/\n/**\n * This object renders a QR Code as a BitMatrix 2D array of greyscale values.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar QRCodeWriter = /** @class */function () {\n  function QRCodeWriter() {}\n  /*@Override*/\n  // public encode(contents: string, format: BarcodeFormat, width: number /*int*/, height: number /*int*/): BitMatrix\n  //     /*throws WriterException */ {\n  //   return encode(contents, format, width, height, null)\n  // }\n  /*@Override*/\n  QRCodeWriter.prototype.encode = function (contents, format, width /*int*/, height /*int*/, hints) {\n    if (contents.length === 0) {\n      throw new IllegalArgumentException('Found empty contents');\n    }\n    if (format !== BarcodeFormat.QR_CODE) {\n      throw new IllegalArgumentException('Can only encode QR_CODE, but got ' + format);\n    }\n    if (width < 0 || height < 0) {\n      throw new IllegalArgumentException(\"Requested dimensions are too small: \" + width + \"x\" + height);\n    }\n    var errorCorrectionLevel = ErrorCorrectionLevel.L;\n    var quietZone = QRCodeWriter.QUIET_ZONE_SIZE;\n    if (hints !== null) {\n      if (undefined !== hints.get(EncodeHintType.ERROR_CORRECTION)) {\n        errorCorrectionLevel = ErrorCorrectionLevel.fromString(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n      }\n      if (undefined !== hints.get(EncodeHintType.MARGIN)) {\n        quietZone = Number.parseInt(hints.get(EncodeHintType.MARGIN).toString(), 10);\n      }\n    }\n    var code = Encoder.encode(contents, errorCorrectionLevel, hints);\n    return QRCodeWriter.renderResult(code, width, height, quietZone);\n  };\n  // Note that the input matrix uses 0 == white, 1 == black, while the output matrix uses\n  // 0 == black, 255 == white (i.e. an 8 bit greyscale bitmap).\n  QRCodeWriter.renderResult = function (code, width /*int*/, height /*int*/, quietZone /*int*/) {\n    var input = code.getMatrix();\n    if (input === null) {\n      throw new IllegalStateException();\n    }\n    var inputWidth = input.getWidth();\n    var inputHeight = input.getHeight();\n    var qrWidth = inputWidth + quietZone * 2;\n    var qrHeight = inputHeight + quietZone * 2;\n    var outputWidth = Math.max(width, qrWidth);\n    var outputHeight = Math.max(height, qrHeight);\n    var multiple = Math.min(Math.floor(outputWidth / qrWidth), Math.floor(outputHeight / qrHeight));\n    // Padding includes both the quiet zone and the extra white pixels to accommodate the requested\n    // dimensions. For example, if input is 25x25 the QR will be 33x33 including the quiet zone.\n    // If the requested size is 200x160, the multiple will be 4, for a QR of 132x132. These will\n    // handle all the padding from 100x100 (the actual QR) up to 200x160.\n    var leftPadding = Math.floor((outputWidth - inputWidth * multiple) / 2);\n    var topPadding = Math.floor((outputHeight - inputHeight * multiple) / 2);\n    var output = new BitMatrix(outputWidth, outputHeight);\n    for (var inputY = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n      // Write the contents of this row of the barcode\n      for (var inputX = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n        if (input.get(inputX, inputY) === 1) {\n          output.setRegion(outputX, outputY, multiple, multiple);\n        }\n      }\n    }\n    return output;\n  };\n  QRCodeWriter.QUIET_ZONE_SIZE = 4;\n  return QRCodeWriter;\n}();\nexport default QRCodeWriter;", "map": {"version": 3, "names": ["BarcodeFormat", "EncodeHintType", "BitMatrix", "ErrorCorrectionLevel", "Encoder", "IllegalArgumentException", "IllegalStateException", "QRCodeWriter", "prototype", "encode", "contents", "format", "width", "height", "hints", "length", "QR_CODE", "errorCorrectionLevel", "L", "quietZone", "QUIET_ZONE_SIZE", "undefined", "get", "ERROR_CORRECTION", "fromString", "toString", "MARGIN", "Number", "parseInt", "code", "renderResult", "input", "getMatrix", "inputWidth", "getWidth", "inputHeight", "getHeight", "qrWidth", "qrHeight", "outputWidth", "Math", "max", "outputHeight", "multiple", "min", "floor", "leftPadding", "topPadding", "output", "inputY", "outputY", "inputX", "outputX", "setRegion"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/QRCodeWriter.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport EncodeHintType from '../EncodeHintType';\nimport BitMatrix from '../common/BitMatrix';\nimport ErrorCorrectionLevel from './decoder/ErrorCorrectionLevel';\nimport Encoder from './encoder/Encoder';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport IllegalStateException from '../IllegalStateException';\n/*import java.util.Map;*/\n/**\n * This object renders a QR Code as a BitMatrix 2D array of greyscale values.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar QRCodeWriter = /** @class */ (function () {\n    function QRCodeWriter() {\n    }\n    /*@Override*/\n    // public encode(contents: string, format: BarcodeFormat, width: number /*int*/, height: number /*int*/): BitMatrix\n    //     /*throws WriterException */ {\n    //   return encode(contents, format, width, height, null)\n    // }\n    /*@Override*/\n    QRCodeWriter.prototype.encode = function (contents, format, width /*int*/, height /*int*/, hints) {\n        if (contents.length === 0) {\n            throw new IllegalArgumentException('Found empty contents');\n        }\n        if (format !== BarcodeFormat.QR_CODE) {\n            throw new IllegalArgumentException('Can only encode QR_CODE, but got ' + format);\n        }\n        if (width < 0 || height < 0) {\n            throw new IllegalArgumentException(\"Requested dimensions are too small: \" + width + \"x\" + height);\n        }\n        var errorCorrectionLevel = ErrorCorrectionLevel.L;\n        var quietZone = QRCodeWriter.QUIET_ZONE_SIZE;\n        if (hints !== null) {\n            if (undefined !== hints.get(EncodeHintType.ERROR_CORRECTION)) {\n                errorCorrectionLevel = ErrorCorrectionLevel.fromString(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n            }\n            if (undefined !== hints.get(EncodeHintType.MARGIN)) {\n                quietZone = Number.parseInt(hints.get(EncodeHintType.MARGIN).toString(), 10);\n            }\n        }\n        var code = Encoder.encode(contents, errorCorrectionLevel, hints);\n        return QRCodeWriter.renderResult(code, width, height, quietZone);\n    };\n    // Note that the input matrix uses 0 == white, 1 == black, while the output matrix uses\n    // 0 == black, 255 == white (i.e. an 8 bit greyscale bitmap).\n    QRCodeWriter.renderResult = function (code, width /*int*/, height /*int*/, quietZone /*int*/) {\n        var input = code.getMatrix();\n        if (input === null) {\n            throw new IllegalStateException();\n        }\n        var inputWidth = input.getWidth();\n        var inputHeight = input.getHeight();\n        var qrWidth = inputWidth + (quietZone * 2);\n        var qrHeight = inputHeight + (quietZone * 2);\n        var outputWidth = Math.max(width, qrWidth);\n        var outputHeight = Math.max(height, qrHeight);\n        var multiple = Math.min(Math.floor(outputWidth / qrWidth), Math.floor(outputHeight / qrHeight));\n        // Padding includes both the quiet zone and the extra white pixels to accommodate the requested\n        // dimensions. For example, if input is 25x25 the QR will be 33x33 including the quiet zone.\n        // If the requested size is 200x160, the multiple will be 4, for a QR of 132x132. These will\n        // handle all the padding from 100x100 (the actual QR) up to 200x160.\n        var leftPadding = Math.floor((outputWidth - (inputWidth * multiple)) / 2);\n        var topPadding = Math.floor((outputHeight - (inputHeight * multiple)) / 2);\n        var output = new BitMatrix(outputWidth, outputHeight);\n        for (var inputY = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n            // Write the contents of this row of the barcode\n            for (var inputX = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n                if (input.get(inputX, inputY) === 1) {\n                    output.setRegion(outputX, outputY, multiple, multiple);\n                }\n            }\n        }\n        return output;\n    };\n    QRCodeWriter.QUIET_ZONE_SIZE = 4;\n    return QRCodeWriter;\n}());\nexport default QRCodeWriter;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,SAAS,MAAM,qBAAqB;AAC3C,OAAOC,oBAAoB,MAAM,gCAAgC;AACjE,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,wBAAwB,MAAM,6BAA6B;AAClE,OAAOC,qBAAqB,MAAM,0BAA0B;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG,CACxB;EACA;EACA;EACA;EACA;EACA;EACA;EACAA,YAAY,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,CAAC,SAASC,MAAM,CAAC,SAASC,KAAK,EAAE;IAC9F,IAAIJ,QAAQ,CAACK,MAAM,KAAK,CAAC,EAAE;MACvB,MAAM,IAAIV,wBAAwB,CAAC,sBAAsB,CAAC;IAC9D;IACA,IAAIM,MAAM,KAAKX,aAAa,CAACgB,OAAO,EAAE;MAClC,MAAM,IAAIX,wBAAwB,CAAC,mCAAmC,GAAGM,MAAM,CAAC;IACpF;IACA,IAAIC,KAAK,GAAG,CAAC,IAAIC,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIR,wBAAwB,CAAC,sCAAsC,GAAGO,KAAK,GAAG,GAAG,GAAGC,MAAM,CAAC;IACrG;IACA,IAAII,oBAAoB,GAAGd,oBAAoB,CAACe,CAAC;IACjD,IAAIC,SAAS,GAAGZ,YAAY,CAACa,eAAe;IAC5C,IAAIN,KAAK,KAAK,IAAI,EAAE;MAChB,IAAIO,SAAS,KAAKP,KAAK,CAACQ,GAAG,CAACrB,cAAc,CAACsB,gBAAgB,CAAC,EAAE;QAC1DN,oBAAoB,GAAGd,oBAAoB,CAACqB,UAAU,CAACV,KAAK,CAACQ,GAAG,CAACrB,cAAc,CAACsB,gBAAgB,CAAC,CAACE,QAAQ,CAAC,CAAC,CAAC;MACjH;MACA,IAAIJ,SAAS,KAAKP,KAAK,CAACQ,GAAG,CAACrB,cAAc,CAACyB,MAAM,CAAC,EAAE;QAChDP,SAAS,GAAGQ,MAAM,CAACC,QAAQ,CAACd,KAAK,CAACQ,GAAG,CAACrB,cAAc,CAACyB,MAAM,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;MAChF;IACJ;IACA,IAAII,IAAI,GAAGzB,OAAO,CAACK,MAAM,CAACC,QAAQ,EAAEO,oBAAoB,EAAEH,KAAK,CAAC;IAChE,OAAOP,YAAY,CAACuB,YAAY,CAACD,IAAI,EAAEjB,KAAK,EAAEC,MAAM,EAAEM,SAAS,CAAC;EACpE,CAAC;EACD;EACA;EACAZ,YAAY,CAACuB,YAAY,GAAG,UAAUD,IAAI,EAAEjB,KAAK,CAAC,SAASC,MAAM,CAAC,SAASM,SAAS,CAAC,SAAS;IAC1F,IAAIY,KAAK,GAAGF,IAAI,CAACG,SAAS,CAAC,CAAC;IAC5B,IAAID,KAAK,KAAK,IAAI,EAAE;MAChB,MAAM,IAAIzB,qBAAqB,CAAC,CAAC;IACrC;IACA,IAAI2B,UAAU,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC;IACjC,IAAIC,WAAW,GAAGJ,KAAK,CAACK,SAAS,CAAC,CAAC;IACnC,IAAIC,OAAO,GAAGJ,UAAU,GAAId,SAAS,GAAG,CAAE;IAC1C,IAAImB,QAAQ,GAAGH,WAAW,GAAIhB,SAAS,GAAG,CAAE;IAC5C,IAAIoB,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC7B,KAAK,EAAEyB,OAAO,CAAC;IAC1C,IAAIK,YAAY,GAAGF,IAAI,CAACC,GAAG,CAAC5B,MAAM,EAAEyB,QAAQ,CAAC;IAC7C,IAAIK,QAAQ,GAAGH,IAAI,CAACI,GAAG,CAACJ,IAAI,CAACK,KAAK,CAACN,WAAW,GAAGF,OAAO,CAAC,EAAEG,IAAI,CAACK,KAAK,CAACH,YAAY,GAAGJ,QAAQ,CAAC,CAAC;IAC/F;IACA;IACA;IACA;IACA,IAAIQ,WAAW,GAAGN,IAAI,CAACK,KAAK,CAAC,CAACN,WAAW,GAAIN,UAAU,GAAGU,QAAS,IAAI,CAAC,CAAC;IACzE,IAAII,UAAU,GAAGP,IAAI,CAACK,KAAK,CAAC,CAACH,YAAY,GAAIP,WAAW,GAAGQ,QAAS,IAAI,CAAC,CAAC;IAC1E,IAAIK,MAAM,GAAG,IAAI9C,SAAS,CAACqC,WAAW,EAAEG,YAAY,CAAC;IACrD,KAAK,IAAIO,MAAM,GAAG,CAAC,EAAEC,OAAO,GAAGH,UAAU,EAAEE,MAAM,GAAGd,WAAW,EAAEc,MAAM,EAAE,EAAEC,OAAO,IAAIP,QAAQ,EAAE;MAC5F;MACA,KAAK,IAAIQ,MAAM,GAAG,CAAC,EAAEC,OAAO,GAAGN,WAAW,EAAEK,MAAM,GAAGlB,UAAU,EAAEkB,MAAM,EAAE,EAAEC,OAAO,IAAIT,QAAQ,EAAE;QAC5F,IAAIZ,KAAK,CAACT,GAAG,CAAC6B,MAAM,EAAEF,MAAM,CAAC,KAAK,CAAC,EAAE;UACjCD,MAAM,CAACK,SAAS,CAACD,OAAO,EAAEF,OAAO,EAAEP,QAAQ,EAAEA,QAAQ,CAAC;QAC1D;MACJ;IACJ;IACA,OAAOK,MAAM;EACjB,CAAC;EACDzC,YAAY,CAACa,eAAe,GAAG,CAAC;EAChC,OAAOb,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}