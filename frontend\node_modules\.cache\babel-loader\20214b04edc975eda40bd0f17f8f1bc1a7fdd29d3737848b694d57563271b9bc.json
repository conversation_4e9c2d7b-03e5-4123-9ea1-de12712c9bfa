{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Learning Projects\\\\uni-core-business-suite_v3\\\\frontend\\\\src\\\\pages\\\\ManageBankPaymentPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Box, Card, CardContent, Typography, Button, CircularProgress, Alert } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Save as SaveIcon } from '@mui/icons-material';\nimport BankPaymentVoucherForm from '../components/BankPaymentVoucherForm';\nimport { useSnackbar } from 'notistack';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ManageBankPaymentPage = () => {\n  _s();\n  var _location$state, _location$state2;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n\n  // Get voucherId from URL params or location state\n  const searchParams = new URLSearchParams(location.search);\n  const voucherId = searchParams.get('id') || ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.voucherId);\n  const readOnly = searchParams.get('view') === 'true' || ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.readOnly);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const handleSave = async savedVoucher => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Show success message\n      enqueueSnackbar(voucherId ? 'Bank payment updated successfully!' : 'Bank payment created successfully!', {\n        variant: 'success'\n      });\n\n      // Navigate back to bank payments list\n      navigate('/bank-payments');\n    } catch (error) {\n      console.error('Error saving bank payment:', error);\n      setError(error.message || 'Failed to save bank payment');\n      enqueueSnackbar('Failed to save bank payment', {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancel = () => {\n    navigate('/bank-payments');\n  };\n  const getPageTitle = () => {\n    if (readOnly) return 'View Bank Payment';\n    if (voucherId) return 'Edit Bank Payment';\n    return 'Create New Bank Payment';\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 22\n        }, this),\n        onClick: handleCancel,\n        sx: {\n          mr: 2\n        },\n        children: \"Back to Bank Payments\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        children: getPageTitle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(BankPaymentVoucherForm, {\n          voucherId: voucherId,\n          readOnly: readOnly,\n          onSave: handleSave,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(ManageBankPaymentPage, \"VkxuwYK3geuDU8nguG9urVlZnFM=\", false, function () {\n  return [useNavigate, useLocation, useSnackbar];\n});\n_c = ManageBankPaymentPage;\nexport default ManageBankPaymentPage;\nvar _c;\n$RefreshReg$(_c, \"ManageBankPaymentPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useLocation", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "ArrowBack", "ArrowBackIcon", "Save", "SaveIcon", "BankPaymentVoucherForm", "useSnackbar", "jsxDEV", "_jsxDEV", "ManageBankPaymentPage", "_s", "_location$state", "_location$state2", "navigate", "location", "enqueueSnackbar", "searchParams", "URLSearchParams", "search", "voucherId", "get", "state", "readOnly", "loading", "setLoading", "error", "setError", "handleSave", "savedVoucher", "variant", "console", "message", "handleCancel", "getPageTitle", "sx", "p", "children", "display", "alignItems", "mb", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "mr", "component", "severity", "justifyContent", "my", "onSave", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/pages/ManageBankPaymentPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  CircularProgress,\n  Alert\n} from '@mui/material';\nimport { ArrowBack as ArrowBackIcon, Save as SaveIcon } from '@mui/icons-material';\nimport BankPaymentVoucherForm from '../components/BankPaymentVoucherForm';\nimport { useSnackbar } from 'notistack';\n\nconst ManageBankPaymentPage = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { enqueueSnackbar } = useSnackbar();\n  \n  // Get voucherId from URL params or location state\n  const searchParams = new URLSearchParams(location.search);\n  const voucherId = searchParams.get('id') || location.state?.voucherId;\n  const readOnly = searchParams.get('view') === 'true' || location.state?.readOnly;\n  \n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const handleSave = async (savedVoucher) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // Show success message\n      enqueueSnackbar(\n        voucherId ? 'Bank payment updated successfully!' : 'Bank payment created successfully!',\n        { variant: 'success' }\n      );\n      \n      // Navigate back to bank payments list\n      navigate('/bank-payments');\n      \n    } catch (error) {\n      console.error('Error saving bank payment:', error);\n      setError(error.message || 'Failed to save bank payment');\n      enqueueSnackbar('Failed to save bank payment', { variant: 'error' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate('/bank-payments');\n  };\n\n  const getPageTitle = () => {\n    if (readOnly) return 'View Bank Payment';\n    if (voucherId) return 'Edit Bank Payment';\n    return 'Create New Bank Payment';\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n        <Button\n          startIcon={<ArrowBackIcon />}\n          onClick={handleCancel}\n          sx={{ mr: 2 }}\n        >\n          Back to Bank Payments\n        </Button>\n        <Typography variant=\"h4\" component=\"h1\">\n          {getPageTitle()}\n        </Typography>\n      </Box>\n\n      {/* Error Alert */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Loading Indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Main Content */}\n      <Card>\n        <CardContent>\n          <BankPaymentVoucherForm\n            voucherId={voucherId}\n            readOnly={readOnly}\n            onSave={handleSave}\n            onCancel={handleCancel}\n          />\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default ManageBankPaymentPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SAASC,SAAS,IAAIC,aAAa,EAAEC,IAAI,IAAIC,QAAQ,QAAQ,qBAAqB;AAClF,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,SAASC,WAAW,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAClC,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsB;EAAgB,CAAC,GAAGT,WAAW,CAAC,CAAC;;EAEzC;EACA,MAAMU,YAAY,GAAG,IAAIC,eAAe,CAACH,QAAQ,CAACI,MAAM,CAAC;EACzD,MAAMC,SAAS,GAAGH,YAAY,CAACI,GAAG,CAAC,IAAI,CAAC,MAAAT,eAAA,GAAIG,QAAQ,CAACO,KAAK,cAAAV,eAAA,uBAAdA,eAAA,CAAgBQ,SAAS;EACrE,MAAMG,QAAQ,GAAGN,YAAY,CAACI,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM,MAAAR,gBAAA,GAAIE,QAAQ,CAACO,KAAK,cAAAT,gBAAA,uBAAdA,gBAAA,CAAgBU,QAAQ;EAEhF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMqC,UAAU,GAAG,MAAOC,YAAY,IAAK;IACzC,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACAX,eAAe,CACbI,SAAS,GAAG,oCAAoC,GAAG,oCAAoC,EACvF;QAAEU,OAAO,EAAE;MAAU,CACvB,CAAC;;MAED;MACAhB,QAAQ,CAAC,gBAAgB,CAAC;IAE5B,CAAC,CAAC,OAAOY,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAACD,KAAK,CAACM,OAAO,IAAI,6BAA6B,CAAC;MACxDhB,eAAe,CAAC,6BAA6B,EAAE;QAAEc,OAAO,EAAE;MAAQ,CAAC,CAAC;IACtE,CAAC,SAAS;MACRL,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBnB,QAAQ,CAAC,gBAAgB,CAAC;EAC5B,CAAC;EAED,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIX,QAAQ,EAAE,OAAO,mBAAmB;IACxC,IAAIH,SAAS,EAAE,OAAO,mBAAmB;IACzC,OAAO,yBAAyB;EAClC,CAAC;EAED,oBACEX,OAAA,CAACd,GAAG;IAACwC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhB5B,OAAA,CAACd,GAAG;MAACwC,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACxD5B,OAAA,CAACV,MAAM;QACL0C,SAAS,eAAEhC,OAAA,CAACN,aAAa;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BC,OAAO,EAAEb,YAAa;QACtBE,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EACf;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTpC,OAAA,CAACX,UAAU;QAACgC,OAAO,EAAC,IAAI;QAACkB,SAAS,EAAC,IAAI;QAAAX,QAAA,EACpCH,YAAY,CAAC;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLnB,KAAK,iBACJjB,OAAA,CAACR,KAAK;MAACgD,QAAQ,EAAC,OAAO;MAACd,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EACnCX;IAAK;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGArB,OAAO,iBACNf,OAAA,CAACd,GAAG;MAACwC,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEY,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAd,QAAA,eAC5D5B,OAAA,CAACT,gBAAgB;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDpC,OAAA,CAACb,IAAI;MAAAyC,QAAA,eACH5B,OAAA,CAACZ,WAAW;QAAAwC,QAAA,eACV5B,OAAA,CAACH,sBAAsB;UACrBc,SAAS,EAAEA,SAAU;UACrBG,QAAQ,EAAEA,QAAS;UACnB6B,MAAM,EAAExB,UAAW;UACnByB,QAAQ,EAAEpB;QAAa;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAClC,EAAA,CAzFID,qBAAqB;EAAA,QACRjB,WAAW,EACXC,WAAW,EACAa,WAAW;AAAA;AAAA+C,EAAA,GAHnC5C,qBAAqB;AA2F3B,eAAeA,qBAAqB;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}