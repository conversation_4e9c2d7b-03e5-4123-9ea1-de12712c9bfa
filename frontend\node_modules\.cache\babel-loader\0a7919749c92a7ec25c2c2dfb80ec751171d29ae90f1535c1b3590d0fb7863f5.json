{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * Symbol info table for DataMatrix.\n */\nvar SymbolInfo = /** @class */function () {\n  function SymbolInfo(rectangular, dataCapacity, errorCodewords, matrixWidth, matrixHeight, dataRegions, rsBlockData, rsBlockError) {\n    if (rsBlockData === void 0) {\n      rsBlockData = 0;\n    }\n    if (rsBlockError === void 0) {\n      rsBlockError = 0;\n    }\n    this.rectangular = rectangular;\n    this.dataCapacity = dataCapacity;\n    this.errorCodewords = errorCodewords;\n    this.matrixWidth = matrixWidth;\n    this.matrixHeight = matrixHeight;\n    this.dataRegions = dataRegions;\n    this.rsBlockData = rsBlockData;\n    this.rsBlockError = rsBlockError;\n  }\n  SymbolInfo.lookup = function (dataCodewords, shape, minSize, maxSize, fail) {\n    var e_1, _a;\n    if (shape === void 0) {\n      shape = 0 /* FORCE_NONE */;\n    }\n    if (minSize === void 0) {\n      minSize = null;\n    }\n    if (maxSize === void 0) {\n      maxSize = null;\n    }\n    if (fail === void 0) {\n      fail = true;\n    }\n    try {\n      for (var PROD_SYMBOLS_1 = __values(PROD_SYMBOLS), PROD_SYMBOLS_1_1 = PROD_SYMBOLS_1.next(); !PROD_SYMBOLS_1_1.done; PROD_SYMBOLS_1_1 = PROD_SYMBOLS_1.next()) {\n        var symbol = PROD_SYMBOLS_1_1.value;\n        if (shape === 1 /* FORCE_SQUARE */ && symbol.rectangular) {\n          continue;\n        }\n        if (shape === 2 /* FORCE_RECTANGLE */ && !symbol.rectangular) {\n          continue;\n        }\n        if (minSize != null && (symbol.getSymbolWidth() < minSize.getWidth() || symbol.getSymbolHeight() < minSize.getHeight())) {\n          continue;\n        }\n        if (maxSize != null && (symbol.getSymbolWidth() > maxSize.getWidth() || symbol.getSymbolHeight() > maxSize.getHeight())) {\n          continue;\n        }\n        if (dataCodewords <= symbol.dataCapacity) {\n          return symbol;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (PROD_SYMBOLS_1_1 && !PROD_SYMBOLS_1_1.done && (_a = PROD_SYMBOLS_1.return)) _a.call(PROD_SYMBOLS_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    if (fail) {\n      throw new Error(\"Can't find a symbol arrangement that matches the message. Data codewords: \" + dataCodewords);\n    }\n    return null;\n  };\n  SymbolInfo.prototype.getHorizontalDataRegions = function () {\n    switch (this.dataRegions) {\n      case 1:\n        return 1;\n      case 2:\n      case 4:\n        return 2;\n      case 16:\n        return 4;\n      case 36:\n        return 6;\n      default:\n        throw new Error('Cannot handle this number of data regions');\n    }\n  };\n  SymbolInfo.prototype.getVerticalDataRegions = function () {\n    switch (this.dataRegions) {\n      case 1:\n      case 2:\n        return 1;\n      case 4:\n        return 2;\n      case 16:\n        return 4;\n      case 36:\n        return 6;\n      default:\n        throw new Error('Cannot handle this number of data regions');\n    }\n  };\n  SymbolInfo.prototype.getSymbolDataWidth = function () {\n    return this.getHorizontalDataRegions() * this.matrixWidth;\n  };\n  SymbolInfo.prototype.getSymbolDataHeight = function () {\n    return this.getVerticalDataRegions() * this.matrixHeight;\n  };\n  SymbolInfo.prototype.getSymbolWidth = function () {\n    return this.getSymbolDataWidth() + this.getHorizontalDataRegions() * 2;\n  };\n  SymbolInfo.prototype.getSymbolHeight = function () {\n    return this.getSymbolDataHeight() + this.getVerticalDataRegions() * 2;\n  };\n  SymbolInfo.prototype.getCodewordCount = function () {\n    return this.dataCapacity + this.errorCodewords;\n  };\n  SymbolInfo.prototype.getInterleavedBlockCount = function () {\n    if (!this.rsBlockData) return 1;\n    return this.dataCapacity / this.rsBlockData;\n  };\n  SymbolInfo.prototype.getDataCapacity = function () {\n    return this.dataCapacity;\n  };\n  SymbolInfo.prototype.getErrorCodewords = function () {\n    return this.errorCodewords;\n  };\n  SymbolInfo.prototype.getDataLengthForInterleavedBlock = function (index) {\n    return this.rsBlockData;\n  };\n  SymbolInfo.prototype.getErrorLengthForInterleavedBlock = function (index) {\n    return this.rsBlockError;\n  };\n  return SymbolInfo;\n}();\nexport default SymbolInfo;\nvar DataMatrixSymbolInfo144 = /** @class */function (_super) {\n  __extends(DataMatrixSymbolInfo144, _super);\n  function DataMatrixSymbolInfo144() {\n    return _super.call(this, false, 1558, 620, 22, 22, 36, -1, 62) || this;\n  }\n  DataMatrixSymbolInfo144.prototype.getInterleavedBlockCount = function () {\n    return 10;\n  };\n  DataMatrixSymbolInfo144.prototype.getDataLengthForInterleavedBlock = function (index) {\n    return index <= 8 ? 156 : 155;\n  };\n  return DataMatrixSymbolInfo144;\n}(SymbolInfo);\nexport var PROD_SYMBOLS = [new SymbolInfo(false, 3, 5, 8, 8, 1), new SymbolInfo(false, 5, 7, 10, 10, 1), /*rect*/new SymbolInfo(true, 5, 7, 16, 6, 1), new SymbolInfo(false, 8, 10, 12, 12, 1), /*rect*/new SymbolInfo(true, 10, 11, 14, 6, 2), new SymbolInfo(false, 12, 12, 14, 14, 1), /*rect*/new SymbolInfo(true, 16, 14, 24, 10, 1), new SymbolInfo(false, 18, 14, 16, 16, 1), new SymbolInfo(false, 22, 18, 18, 18, 1), /*rect*/new SymbolInfo(true, 22, 18, 16, 10, 2), new SymbolInfo(false, 30, 20, 20, 20, 1), /*rect*/new SymbolInfo(true, 32, 24, 16, 14, 2), new SymbolInfo(false, 36, 24, 22, 22, 1), new SymbolInfo(false, 44, 28, 24, 24, 1), /*rect*/new SymbolInfo(true, 49, 28, 22, 14, 2), new SymbolInfo(false, 62, 36, 14, 14, 4), new SymbolInfo(false, 86, 42, 16, 16, 4), new SymbolInfo(false, 114, 48, 18, 18, 4), new SymbolInfo(false, 144, 56, 20, 20, 4), new SymbolInfo(false, 174, 68, 22, 22, 4), new SymbolInfo(false, 204, 84, 24, 24, 4, 102, 42), new SymbolInfo(false, 280, 112, 14, 14, 16, 140, 56), new SymbolInfo(false, 368, 144, 16, 16, 16, 92, 36), new SymbolInfo(false, 456, 192, 18, 18, 16, 114, 48), new SymbolInfo(false, 576, 224, 20, 20, 16, 144, 56), new SymbolInfo(false, 696, 272, 22, 22, 16, 174, 68), new SymbolInfo(false, 816, 336, 24, 24, 16, 136, 56), new SymbolInfo(false, 1050, 408, 18, 18, 36, 175, 68), new SymbolInfo(false, 1304, 496, 20, 20, 36, 163, 62), new DataMatrixSymbolInfo144()];", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "SymbolInfo", "rectangular", "dataCapacity", "errorCodewords", "matrixWidth", "matrixHeight", "dataRegions", "rsBlockData", "rsBlockError", "lookup", "dataCodewords", "shape", "minSize", "maxSize", "fail", "e_1", "_a", "PROD_SYMBOLS_1", "PROD_SYMBOLS", "PROD_SYMBOLS_1_1", "symbol", "getSymbolWidth", "getWidth", "getSymbolHeight", "getHeight", "e_1_1", "error", "return", "Error", "getHorizontalDataRegions", "getVerticalDataRegions", "getSymbolDataWidth", "getSymbolDataHeight", "getCodewordCount", "getInterleavedBlockCount", "getDataCapacity", "getErrorCodewords", "getDataLengthForInterleavedBlock", "index", "getErrorLengthForInterleavedBlock", "DataMatrixSymbolInfo144", "_super"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/SymbolInfo.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * Symbol info table for DataMatrix.\n */\nvar SymbolInfo = /** @class */ (function () {\n    function SymbolInfo(rectangular, dataCapacity, errorCodewords, matrixWidth, matrixHeight, dataRegions, rsBlockData, rsBlockError) {\n        if (rsBlockData === void 0) { rsBlockData = 0; }\n        if (rsBlockError === void 0) { rsBlockError = 0; }\n        this.rectangular = rectangular;\n        this.dataCapacity = dataCapacity;\n        this.errorCodewords = errorCodewords;\n        this.matrixWidth = matrixWidth;\n        this.matrixHeight = matrixHeight;\n        this.dataRegions = dataRegions;\n        this.rsBlockData = rsBlockData;\n        this.rsBlockError = rsBlockError;\n    }\n    SymbolInfo.lookup = function (dataCodewords, shape, minSize, maxSize, fail) {\n        var e_1, _a;\n        if (shape === void 0) { shape = 0 /* FORCE_NONE */; }\n        if (minSize === void 0) { minSize = null; }\n        if (maxSize === void 0) { maxSize = null; }\n        if (fail === void 0) { fail = true; }\n        try {\n            for (var PROD_SYMBOLS_1 = __values(PROD_SYMBOLS), PROD_SYMBOLS_1_1 = PROD_SYMBOLS_1.next(); !PROD_SYMBOLS_1_1.done; PROD_SYMBOLS_1_1 = PROD_SYMBOLS_1.next()) {\n                var symbol = PROD_SYMBOLS_1_1.value;\n                if (shape === 1 /* FORCE_SQUARE */ && symbol.rectangular) {\n                    continue;\n                }\n                if (shape === 2 /* FORCE_RECTANGLE */ && !symbol.rectangular) {\n                    continue;\n                }\n                if (minSize != null &&\n                    (symbol.getSymbolWidth() < minSize.getWidth() ||\n                        symbol.getSymbolHeight() < minSize.getHeight())) {\n                    continue;\n                }\n                if (maxSize != null &&\n                    (symbol.getSymbolWidth() > maxSize.getWidth() ||\n                        symbol.getSymbolHeight() > maxSize.getHeight())) {\n                    continue;\n                }\n                if (dataCodewords <= symbol.dataCapacity) {\n                    return symbol;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (PROD_SYMBOLS_1_1 && !PROD_SYMBOLS_1_1.done && (_a = PROD_SYMBOLS_1.return)) _a.call(PROD_SYMBOLS_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        if (fail) {\n            throw new Error(\"Can't find a symbol arrangement that matches the message. Data codewords: \" +\n                dataCodewords);\n        }\n        return null;\n    };\n    SymbolInfo.prototype.getHorizontalDataRegions = function () {\n        switch (this.dataRegions) {\n            case 1:\n                return 1;\n            case 2:\n            case 4:\n                return 2;\n            case 16:\n                return 4;\n            case 36:\n                return 6;\n            default:\n                throw new Error('Cannot handle this number of data regions');\n        }\n    };\n    SymbolInfo.prototype.getVerticalDataRegions = function () {\n        switch (this.dataRegions) {\n            case 1:\n            case 2:\n                return 1;\n            case 4:\n                return 2;\n            case 16:\n                return 4;\n            case 36:\n                return 6;\n            default:\n                throw new Error('Cannot handle this number of data regions');\n        }\n    };\n    SymbolInfo.prototype.getSymbolDataWidth = function () {\n        return this.getHorizontalDataRegions() * this.matrixWidth;\n    };\n    SymbolInfo.prototype.getSymbolDataHeight = function () {\n        return this.getVerticalDataRegions() * this.matrixHeight;\n    };\n    SymbolInfo.prototype.getSymbolWidth = function () {\n        return this.getSymbolDataWidth() + this.getHorizontalDataRegions() * 2;\n    };\n    SymbolInfo.prototype.getSymbolHeight = function () {\n        return this.getSymbolDataHeight() + this.getVerticalDataRegions() * 2;\n    };\n    SymbolInfo.prototype.getCodewordCount = function () {\n        return this.dataCapacity + this.errorCodewords;\n    };\n    SymbolInfo.prototype.getInterleavedBlockCount = function () {\n        if (!this.rsBlockData)\n            return 1;\n        return this.dataCapacity / this.rsBlockData;\n    };\n    SymbolInfo.prototype.getDataCapacity = function () {\n        return this.dataCapacity;\n    };\n    SymbolInfo.prototype.getErrorCodewords = function () {\n        return this.errorCodewords;\n    };\n    SymbolInfo.prototype.getDataLengthForInterleavedBlock = function (index) {\n        return this.rsBlockData;\n    };\n    SymbolInfo.prototype.getErrorLengthForInterleavedBlock = function (index) {\n        return this.rsBlockError;\n    };\n    return SymbolInfo;\n}());\nexport default SymbolInfo;\nvar DataMatrixSymbolInfo144 = /** @class */ (function (_super) {\n    __extends(DataMatrixSymbolInfo144, _super);\n    function DataMatrixSymbolInfo144() {\n        return _super.call(this, false, 1558, 620, 22, 22, 36, -1, 62) || this;\n    }\n    DataMatrixSymbolInfo144.prototype.getInterleavedBlockCount = function () {\n        return 10;\n    };\n    DataMatrixSymbolInfo144.prototype.getDataLengthForInterleavedBlock = function (index) {\n        return index <= 8 ? 156 : 155;\n    };\n    return DataMatrixSymbolInfo144;\n}(SymbolInfo));\nexport var PROD_SYMBOLS = [\n    new SymbolInfo(false, 3, 5, 8, 8, 1),\n    new SymbolInfo(false, 5, 7, 10, 10, 1),\n    /*rect*/ new SymbolInfo(true, 5, 7, 16, 6, 1),\n    new SymbolInfo(false, 8, 10, 12, 12, 1),\n    /*rect*/ new SymbolInfo(true, 10, 11, 14, 6, 2),\n    new SymbolInfo(false, 12, 12, 14, 14, 1),\n    /*rect*/ new SymbolInfo(true, 16, 14, 24, 10, 1),\n    new SymbolInfo(false, 18, 14, 16, 16, 1),\n    new SymbolInfo(false, 22, 18, 18, 18, 1),\n    /*rect*/ new SymbolInfo(true, 22, 18, 16, 10, 2),\n    new SymbolInfo(false, 30, 20, 20, 20, 1),\n    /*rect*/ new SymbolInfo(true, 32, 24, 16, 14, 2),\n    new SymbolInfo(false, 36, 24, 22, 22, 1),\n    new SymbolInfo(false, 44, 28, 24, 24, 1),\n    /*rect*/ new SymbolInfo(true, 49, 28, 22, 14, 2),\n    new SymbolInfo(false, 62, 36, 14, 14, 4),\n    new SymbolInfo(false, 86, 42, 16, 16, 4),\n    new SymbolInfo(false, 114, 48, 18, 18, 4),\n    new SymbolInfo(false, 144, 56, 20, 20, 4),\n    new SymbolInfo(false, 174, 68, 22, 22, 4),\n    new SymbolInfo(false, 204, 84, 24, 24, 4, 102, 42),\n    new SymbolInfo(false, 280, 112, 14, 14, 16, 140, 56),\n    new SymbolInfo(false, 368, 144, 16, 16, 16, 92, 36),\n    new SymbolInfo(false, 456, 192, 18, 18, 16, 114, 48),\n    new SymbolInfo(false, 576, 224, 20, 20, 16, 144, 56),\n    new SymbolInfo(false, 696, 272, 22, 22, 16, 174, 68),\n    new SymbolInfo(false, 816, 336, 24, 24, 16, 136, 56),\n    new SymbolInfo(false, 1050, 408, 18, 18, 36, 175, 68),\n    new SymbolInfo(false, 1304, 496, 20, 20, 36, 163, 62),\n    new DataMatrixSymbolInfo144(),\n];\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA;AACA,IAAIW,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAACC,WAAW,EAAEC,YAAY,EAAEC,cAAc,EAAEC,WAAW,EAAEC,YAAY,EAAEC,WAAW,EAAEC,WAAW,EAAEC,YAAY,EAAE;IAC9H,IAAID,WAAW,KAAK,KAAK,CAAC,EAAE;MAAEA,WAAW,GAAG,CAAC;IAAE;IAC/C,IAAIC,YAAY,KAAK,KAAK,CAAC,EAAE;MAAEA,YAAY,GAAG,CAAC;IAAE;IACjD,IAAI,CAACP,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,YAAY,GAAGA,YAAY;EACpC;EACAR,UAAU,CAACS,MAAM,GAAG,UAAUC,aAAa,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,IAAI,EAAE;IACxE,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIL,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC,CAAC;IAAkB;IACpD,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,IAAI;IAAE;IAC1C,IAAIC,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAG,IAAI;IAAE;IAC1C,IAAIC,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG,IAAI;IAAE;IACpC,IAAI;MACA,KAAK,IAAIG,cAAc,GAAG9B,QAAQ,CAAC+B,YAAY,CAAC,EAAEC,gBAAgB,GAAGF,cAAc,CAACrB,IAAI,CAAC,CAAC,EAAE,CAACuB,gBAAgB,CAACrB,IAAI,EAAEqB,gBAAgB,GAAGF,cAAc,CAACrB,IAAI,CAAC,CAAC,EAAE;QAC1J,IAAIwB,MAAM,GAAGD,gBAAgB,CAACtB,KAAK;QACnC,IAAIc,KAAK,KAAK,CAAC,CAAC,sBAAsBS,MAAM,CAACnB,WAAW,EAAE;UACtD;QACJ;QACA,IAAIU,KAAK,KAAK,CAAC,CAAC,yBAAyB,CAACS,MAAM,CAACnB,WAAW,EAAE;UAC1D;QACJ;QACA,IAAIW,OAAO,IAAI,IAAI,KACdQ,MAAM,CAACC,cAAc,CAAC,CAAC,GAAGT,OAAO,CAACU,QAAQ,CAAC,CAAC,IACzCF,MAAM,CAACG,eAAe,CAAC,CAAC,GAAGX,OAAO,CAACY,SAAS,CAAC,CAAC,CAAC,EAAE;UACrD;QACJ;QACA,IAAIX,OAAO,IAAI,IAAI,KACdO,MAAM,CAACC,cAAc,CAAC,CAAC,GAAGR,OAAO,CAACS,QAAQ,CAAC,CAAC,IACzCF,MAAM,CAACG,eAAe,CAAC,CAAC,GAAGV,OAAO,CAACW,SAAS,CAAC,CAAC,CAAC,EAAE;UACrD;QACJ;QACA,IAAId,aAAa,IAAIU,MAAM,CAAClB,YAAY,EAAE;UACtC,OAAOkB,MAAM;QACjB;MACJ;IACJ,CAAC,CACD,OAAOK,KAAK,EAAE;MAAEV,GAAG,GAAG;QAAEW,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIN,gBAAgB,IAAI,CAACA,gBAAgB,CAACrB,IAAI,KAAKkB,EAAE,GAAGC,cAAc,CAACU,MAAM,CAAC,EAAEX,EAAE,CAACtB,IAAI,CAACuB,cAAc,CAAC;MAC3G,CAAC,SACO;QAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACW,KAAK;MAAE;IACxC;IACA,IAAIZ,IAAI,EAAE;MACN,MAAM,IAAIc,KAAK,CAAC,4EAA4E,GACxFlB,aAAa,CAAC;IACtB;IACA,OAAO,IAAI;EACf,CAAC;EACDV,UAAU,CAACf,SAAS,CAAC4C,wBAAwB,GAAG,YAAY;IACxD,QAAQ,IAAI,CAACvB,WAAW;MACpB,KAAK,CAAC;QACF,OAAO,CAAC;MACZ,KAAK,CAAC;MACN,KAAK,CAAC;QACF,OAAO,CAAC;MACZ,KAAK,EAAE;QACH,OAAO,CAAC;MACZ,KAAK,EAAE;QACH,OAAO,CAAC;MACZ;QACI,MAAM,IAAIsB,KAAK,CAAC,2CAA2C,CAAC;IACpE;EACJ,CAAC;EACD5B,UAAU,CAACf,SAAS,CAAC6C,sBAAsB,GAAG,YAAY;IACtD,QAAQ,IAAI,CAACxB,WAAW;MACpB,KAAK,CAAC;MACN,KAAK,CAAC;QACF,OAAO,CAAC;MACZ,KAAK,CAAC;QACF,OAAO,CAAC;MACZ,KAAK,EAAE;QACH,OAAO,CAAC;MACZ,KAAK,EAAE;QACH,OAAO,CAAC;MACZ;QACI,MAAM,IAAIsB,KAAK,CAAC,2CAA2C,CAAC;IACpE;EACJ,CAAC;EACD5B,UAAU,CAACf,SAAS,CAAC8C,kBAAkB,GAAG,YAAY;IAClD,OAAO,IAAI,CAACF,wBAAwB,CAAC,CAAC,GAAG,IAAI,CAACzB,WAAW;EAC7D,CAAC;EACDJ,UAAU,CAACf,SAAS,CAAC+C,mBAAmB,GAAG,YAAY;IACnD,OAAO,IAAI,CAACF,sBAAsB,CAAC,CAAC,GAAG,IAAI,CAACzB,YAAY;EAC5D,CAAC;EACDL,UAAU,CAACf,SAAS,CAACoC,cAAc,GAAG,YAAY;IAC9C,OAAO,IAAI,CAACU,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACF,wBAAwB,CAAC,CAAC,GAAG,CAAC;EAC1E,CAAC;EACD7B,UAAU,CAACf,SAAS,CAACsC,eAAe,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACS,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAACF,sBAAsB,CAAC,CAAC,GAAG,CAAC;EACzE,CAAC;EACD9B,UAAU,CAACf,SAAS,CAACgD,gBAAgB,GAAG,YAAY;IAChD,OAAO,IAAI,CAAC/B,YAAY,GAAG,IAAI,CAACC,cAAc;EAClD,CAAC;EACDH,UAAU,CAACf,SAAS,CAACiD,wBAAwB,GAAG,YAAY;IACxD,IAAI,CAAC,IAAI,CAAC3B,WAAW,EACjB,OAAO,CAAC;IACZ,OAAO,IAAI,CAACL,YAAY,GAAG,IAAI,CAACK,WAAW;EAC/C,CAAC;EACDP,UAAU,CAACf,SAAS,CAACkD,eAAe,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACjC,YAAY;EAC5B,CAAC;EACDF,UAAU,CAACf,SAAS,CAACmD,iBAAiB,GAAG,YAAY;IACjD,OAAO,IAAI,CAACjC,cAAc;EAC9B,CAAC;EACDH,UAAU,CAACf,SAAS,CAACoD,gCAAgC,GAAG,UAAUC,KAAK,EAAE;IACrE,OAAO,IAAI,CAAC/B,WAAW;EAC3B,CAAC;EACDP,UAAU,CAACf,SAAS,CAACsD,iCAAiC,GAAG,UAAUD,KAAK,EAAE;IACtE,OAAO,IAAI,CAAC9B,YAAY;EAC5B,CAAC;EACD,OAAOR,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,eAAeA,UAAU;AACzB,IAAIwC,uBAAuB,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC3DpE,SAAS,CAACmE,uBAAuB,EAAEC,MAAM,CAAC;EAC1C,SAASD,uBAAuBA,CAAA,EAAG;IAC/B,OAAOC,MAAM,CAAC/C,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI;EAC1E;EACA8C,uBAAuB,CAACvD,SAAS,CAACiD,wBAAwB,GAAG,YAAY;IACrE,OAAO,EAAE;EACb,CAAC;EACDM,uBAAuB,CAACvD,SAAS,CAACoD,gCAAgC,GAAG,UAAUC,KAAK,EAAE;IAClF,OAAOA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;EACjC,CAAC;EACD,OAAOE,uBAAuB;AAClC,CAAC,CAACxC,UAAU,CAAE;AACd,OAAO,IAAIkB,YAAY,GAAG,CACtB,IAAIlB,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EACpC,IAAIA,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACtC,QAAS,IAAIA,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAC7C,IAAIA,UAAU,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACvC,QAAS,IAAIA,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAC/C,IAAIA,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,QAAS,IAAIA,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChD,IAAIA,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,IAAIA,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,QAAS,IAAIA,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChD,IAAIA,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,QAAS,IAAIA,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChD,IAAIA,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,IAAIA,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,QAAS,IAAIA,UAAU,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAChD,IAAIA,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,IAAIA,UAAU,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACxC,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACzC,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACzC,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EACzC,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAClD,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EACpD,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EACnD,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EACpD,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EACpD,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EACpD,IAAIA,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EACpD,IAAIA,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EACrD,IAAIA,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,EACrD,IAAIwC,uBAAuB,CAAC,CAAC,CAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}