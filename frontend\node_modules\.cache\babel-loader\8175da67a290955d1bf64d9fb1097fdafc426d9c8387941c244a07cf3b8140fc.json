{"ast": null, "code": "import StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport { EDIFACT_ENCODATION, ASCII_ENCODATION } from './constants';\nimport HighLevelEncoder from './HighLevelEncoder';\nvar EdifactEncoder = /** @class */function () {\n  function EdifactEncoder() {}\n  EdifactEncoder.prototype.getEncodingMode = function () {\n    return EDIFACT_ENCODATION;\n  };\n  EdifactEncoder.prototype.encode = function (context) {\n    // step F\n    var buffer = new StringBuilder();\n    while (context.hasMoreCharacters()) {\n      var c = context.getCurrentChar();\n      this.encodeChar(c, buffer);\n      context.pos++;\n      var count = buffer.length();\n      if (count >= 4) {\n        context.writeCodewords(this.encodeToCodewords(buffer.toString()));\n        var test_1 = buffer.toString().substring(4);\n        buffer.setLengthToZero();\n        buffer.append(test_1);\n        // buffer.delete(0, 4);\n        // for (let i = 0; i < 4; i++) {\n        //  buffer.deleteCharAt(i);\n        // }\n        var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n        if (newMode !== this.getEncodingMode()) {\n          // Return to ASCII encodation, which will actually handle latch to new mode\n          context.signalEncoderChange(ASCII_ENCODATION);\n          break;\n        }\n      }\n    }\n    buffer.append(StringUtils.getCharAt(31)); // Unlatch\n    this.handleEOD(context, buffer);\n  };\n  /**\n   * Handle \"end of data\" situations\n   *\n   * @param context the encoder context\n   * @param buffer  the buffer with the remaining encoded characters\n   */\n  EdifactEncoder.prototype.handleEOD = function (context, buffer) {\n    try {\n      var count = buffer.length();\n      if (count === 0) {\n        return; // Already finished\n      }\n      if (count === 1) {\n        // Only an unlatch at the end\n        context.updateSymbolInfo();\n        var available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();\n        var remaining = context.getRemainingCharacters();\n        // The following two lines are a hack inspired by the 'fix' from https://sourceforge.net/p/barcode4j/svn/221/\n        if (remaining > available) {\n          context.updateSymbolInfo(context.getCodewordCount() + 1);\n          available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();\n        }\n        if (remaining <= available && available <= 2) {\n          return; // No unlatch\n        }\n      }\n      if (count > 4) {\n        throw new Error('Count must not exceed 4');\n      }\n      var restChars = count - 1;\n      var encoded = this.encodeToCodewords(buffer.toString());\n      var endOfSymbolReached = !context.hasMoreCharacters();\n      var restInAscii = endOfSymbolReached && restChars <= 2;\n      if (restChars <= 2) {\n        context.updateSymbolInfo(context.getCodewordCount() + restChars);\n        var available = context.getSymbolInfo().getDataCapacity() - context.getCodewordCount();\n        if (available >= 3) {\n          restInAscii = false;\n          context.updateSymbolInfo(context.getCodewordCount() + encoded.length);\n          // available = context.symbolInfo.dataCapacity - context.getCodewordCount();\n        }\n      }\n      if (restInAscii) {\n        context.resetSymbolInfo();\n        context.pos -= restChars;\n      } else {\n        context.writeCodewords(encoded);\n      }\n    } finally {\n      context.signalEncoderChange(ASCII_ENCODATION);\n    }\n  };\n  EdifactEncoder.prototype.encodeChar = function (c, sb) {\n    if (c >= ' '.charCodeAt(0) && c <= '?'.charCodeAt(0)) {\n      sb.append(c);\n    } else if (c >= '@'.charCodeAt(0) && c <= '^'.charCodeAt(0)) {\n      sb.append(StringUtils.getCharAt(c - 64));\n    } else {\n      HighLevelEncoder.illegalCharacter(StringUtils.getCharAt(c));\n    }\n  };\n  EdifactEncoder.prototype.encodeToCodewords = function (sb) {\n    var len = sb.length;\n    if (len === 0) {\n      throw new Error('StringBuilder must not be empty');\n    }\n    var c1 = sb.charAt(0).charCodeAt(0);\n    var c2 = len >= 2 ? sb.charAt(1).charCodeAt(0) : 0;\n    var c3 = len >= 3 ? sb.charAt(2).charCodeAt(0) : 0;\n    var c4 = len >= 4 ? sb.charAt(3).charCodeAt(0) : 0;\n    var v = (c1 << 18) + (c2 << 12) + (c3 << 6) + c4;\n    var cw1 = v >> 16 & 255;\n    var cw2 = v >> 8 & 255;\n    var cw3 = v & 255;\n    var res = new StringBuilder();\n    res.append(cw1);\n    if (len >= 2) {\n      res.append(cw2);\n    }\n    if (len >= 3) {\n      res.append(cw3);\n    }\n    return res.toString();\n  };\n  return EdifactEncoder;\n}();\nexport { EdifactEncoder };", "map": {"version": 3, "names": ["StringUtils", "StringBuilder", "EDIFACT_ENCODATION", "ASCII_ENCODATION", "HighLevelEncoder", "EdifactEncoder", "prototype", "getEncodingMode", "encode", "context", "buffer", "hasMoreCharacters", "c", "getCurrentChar", "encodeChar", "pos", "count", "length", "writeCodewords", "encodeToCodewords", "toString", "test_1", "substring", "setLengthToZero", "append", "newMode", "lookAheadTest", "getMessage", "signalEncoderChange", "getCharAt", "handleEOD", "updateSymbolInfo", "available", "getSymbolInfo", "getDataCapacity", "getCodewordCount", "remaining", "getRemainingCharacters", "Error", "restChars", "encoded", "endOfSymbolReached", "restInAscii", "resetSymbolInfo", "sb", "charCodeAt", "illegalCharacter", "len", "c1", "char<PERSON>t", "c2", "c3", "c4", "v", "cw1", "cw2", "cw3", "res"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/EdifactEncoder.js"], "sourcesContent": ["import StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport { EDIFACT_ENCODATION, ASCII_ENCODATION } from './constants';\nimport HighLevelEncoder from './HighLevelEncoder';\nvar EdifactEncoder = /** @class */ (function () {\n    function EdifactEncoder() {\n    }\n    EdifactEncoder.prototype.getEncodingMode = function () {\n        return EDIFACT_ENCODATION;\n    };\n    EdifactEncoder.prototype.encode = function (context) {\n        // step F\n        var buffer = new StringBuilder();\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            this.encodeChar(c, buffer);\n            context.pos++;\n            var count = buffer.length();\n            if (count >= 4) {\n                context.writeCodewords(this.encodeToCodewords(buffer.toString()));\n                var test_1 = buffer.toString().substring(4);\n                buffer.setLengthToZero();\n                buffer.append(test_1);\n                // buffer.delete(0, 4);\n                // for (let i = 0; i < 4; i++) {\n                //  buffer.deleteCharAt(i);\n                // }\n                var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n                if (newMode !== this.getEncodingMode()) {\n                    // Return to ASCII encodation, which will actually handle latch to new mode\n                    context.signalEncoderChange(ASCII_ENCODATION);\n                    break;\n                }\n            }\n        }\n        buffer.append(StringUtils.getCharAt(31)); // Unlatch\n        this.handleEOD(context, buffer);\n    };\n    /**\n     * Handle \"end of data\" situations\n     *\n     * @param context the encoder context\n     * @param buffer  the buffer with the remaining encoded characters\n     */\n    EdifactEncoder.prototype.handleEOD = function (context, buffer) {\n        try {\n            var count = buffer.length();\n            if (count === 0) {\n                return; // Already finished\n            }\n            if (count === 1) {\n                // Only an unlatch at the end\n                context.updateSymbolInfo();\n                var available = context.getSymbolInfo().getDataCapacity() -\n                    context.getCodewordCount();\n                var remaining = context.getRemainingCharacters();\n                // The following two lines are a hack inspired by the 'fix' from https://sourceforge.net/p/barcode4j/svn/221/\n                if (remaining > available) {\n                    context.updateSymbolInfo(context.getCodewordCount() + 1);\n                    available =\n                        context.getSymbolInfo().getDataCapacity() -\n                            context.getCodewordCount();\n                }\n                if (remaining <= available && available <= 2) {\n                    return; // No unlatch\n                }\n            }\n            if (count > 4) {\n                throw new Error('Count must not exceed 4');\n            }\n            var restChars = count - 1;\n            var encoded = this.encodeToCodewords(buffer.toString());\n            var endOfSymbolReached = !context.hasMoreCharacters();\n            var restInAscii = endOfSymbolReached && restChars <= 2;\n            if (restChars <= 2) {\n                context.updateSymbolInfo(context.getCodewordCount() + restChars);\n                var available = context.getSymbolInfo().getDataCapacity() -\n                    context.getCodewordCount();\n                if (available >= 3) {\n                    restInAscii = false;\n                    context.updateSymbolInfo(context.getCodewordCount() + encoded.length);\n                    // available = context.symbolInfo.dataCapacity - context.getCodewordCount();\n                }\n            }\n            if (restInAscii) {\n                context.resetSymbolInfo();\n                context.pos -= restChars;\n            }\n            else {\n                context.writeCodewords(encoded);\n            }\n        }\n        finally {\n            context.signalEncoderChange(ASCII_ENCODATION);\n        }\n    };\n    EdifactEncoder.prototype.encodeChar = function (c, sb) {\n        if (c >= ' '.charCodeAt(0) && c <= '?'.charCodeAt(0)) {\n            sb.append(c);\n        }\n        else if (c >= '@'.charCodeAt(0) && c <= '^'.charCodeAt(0)) {\n            sb.append(StringUtils.getCharAt(c - 64));\n        }\n        else {\n            HighLevelEncoder.illegalCharacter(StringUtils.getCharAt(c));\n        }\n    };\n    EdifactEncoder.prototype.encodeToCodewords = function (sb) {\n        var len = sb.length;\n        if (len === 0) {\n            throw new Error('StringBuilder must not be empty');\n        }\n        var c1 = sb.charAt(0).charCodeAt(0);\n        var c2 = len >= 2 ? sb.charAt(1).charCodeAt(0) : 0;\n        var c3 = len >= 3 ? sb.charAt(2).charCodeAt(0) : 0;\n        var c4 = len >= 4 ? sb.charAt(3).charCodeAt(0) : 0;\n        var v = (c1 << 18) + (c2 << 12) + (c3 << 6) + c4;\n        var cw1 = (v >> 16) & 255;\n        var cw2 = (v >> 8) & 255;\n        var cw3 = v & 255;\n        var res = new StringBuilder();\n        res.append(cw1);\n        if (len >= 2) {\n            res.append(cw2);\n        }\n        if (len >= 3) {\n            res.append(cw3);\n        }\n        return res.toString();\n    };\n    return EdifactEncoder;\n}());\nexport { EdifactEncoder };\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,aAAa;AAClE,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,IAAIC,cAAc,GAAG,aAAe,YAAY;EAC5C,SAASA,cAAcA,CAAA,EAAG,CAC1B;EACAA,cAAc,CAACC,SAAS,CAACC,eAAe,GAAG,YAAY;IACnD,OAAOL,kBAAkB;EAC7B,CAAC;EACDG,cAAc,CAACC,SAAS,CAACE,MAAM,GAAG,UAAUC,OAAO,EAAE;IACjD;IACA,IAAIC,MAAM,GAAG,IAAIT,aAAa,CAAC,CAAC;IAChC,OAAOQ,OAAO,CAACE,iBAAiB,CAAC,CAAC,EAAE;MAChC,IAAIC,CAAC,GAAGH,OAAO,CAACI,cAAc,CAAC,CAAC;MAChC,IAAI,CAACC,UAAU,CAACF,CAAC,EAAEF,MAAM,CAAC;MAC1BD,OAAO,CAACM,GAAG,EAAE;MACb,IAAIC,KAAK,GAAGN,MAAM,CAACO,MAAM,CAAC,CAAC;MAC3B,IAAID,KAAK,IAAI,CAAC,EAAE;QACZP,OAAO,CAACS,cAAc,CAAC,IAAI,CAACC,iBAAiB,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,CAAC,CAAC;QACjE,IAAIC,MAAM,GAAGX,MAAM,CAACU,QAAQ,CAAC,CAAC,CAACE,SAAS,CAAC,CAAC,CAAC;QAC3CZ,MAAM,CAACa,eAAe,CAAC,CAAC;QACxBb,MAAM,CAACc,MAAM,CAACH,MAAM,CAAC;QACrB;QACA;QACA;QACA;QACA,IAAII,OAAO,GAAGrB,gBAAgB,CAACsB,aAAa,CAACjB,OAAO,CAACkB,UAAU,CAAC,CAAC,EAAElB,OAAO,CAACM,GAAG,EAAE,IAAI,CAACR,eAAe,CAAC,CAAC,CAAC;QACvG,IAAIkB,OAAO,KAAK,IAAI,CAAClB,eAAe,CAAC,CAAC,EAAE;UACpC;UACAE,OAAO,CAACmB,mBAAmB,CAACzB,gBAAgB,CAAC;UAC7C;QACJ;MACJ;IACJ;IACAO,MAAM,CAACc,MAAM,CAACxB,WAAW,CAAC6B,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,SAAS,CAACrB,OAAO,EAAEC,MAAM,CAAC;EACnC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIL,cAAc,CAACC,SAAS,CAACwB,SAAS,GAAG,UAAUrB,OAAO,EAAEC,MAAM,EAAE;IAC5D,IAAI;MACA,IAAIM,KAAK,GAAGN,MAAM,CAACO,MAAM,CAAC,CAAC;MAC3B,IAAID,KAAK,KAAK,CAAC,EAAE;QACb,OAAO,CAAC;MACZ;MACA,IAAIA,KAAK,KAAK,CAAC,EAAE;QACb;QACAP,OAAO,CAACsB,gBAAgB,CAAC,CAAC;QAC1B,IAAIC,SAAS,GAAGvB,OAAO,CAACwB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,GACrDzB,OAAO,CAAC0B,gBAAgB,CAAC,CAAC;QAC9B,IAAIC,SAAS,GAAG3B,OAAO,CAAC4B,sBAAsB,CAAC,CAAC;QAChD;QACA,IAAID,SAAS,GAAGJ,SAAS,EAAE;UACvBvB,OAAO,CAACsB,gBAAgB,CAACtB,OAAO,CAAC0B,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC;UACxDH,SAAS,GACLvB,OAAO,CAACwB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,GACrCzB,OAAO,CAAC0B,gBAAgB,CAAC,CAAC;QACtC;QACA,IAAIC,SAAS,IAAIJ,SAAS,IAAIA,SAAS,IAAI,CAAC,EAAE;UAC1C,OAAO,CAAC;QACZ;MACJ;MACA,IAAIhB,KAAK,GAAG,CAAC,EAAE;QACX,MAAM,IAAIsB,KAAK,CAAC,yBAAyB,CAAC;MAC9C;MACA,IAAIC,SAAS,GAAGvB,KAAK,GAAG,CAAC;MACzB,IAAIwB,OAAO,GAAG,IAAI,CAACrB,iBAAiB,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,CAAC;MACvD,IAAIqB,kBAAkB,GAAG,CAAChC,OAAO,CAACE,iBAAiB,CAAC,CAAC;MACrD,IAAI+B,WAAW,GAAGD,kBAAkB,IAAIF,SAAS,IAAI,CAAC;MACtD,IAAIA,SAAS,IAAI,CAAC,EAAE;QAChB9B,OAAO,CAACsB,gBAAgB,CAACtB,OAAO,CAAC0B,gBAAgB,CAAC,CAAC,GAAGI,SAAS,CAAC;QAChE,IAAIP,SAAS,GAAGvB,OAAO,CAACwB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,GACrDzB,OAAO,CAAC0B,gBAAgB,CAAC,CAAC;QAC9B,IAAIH,SAAS,IAAI,CAAC,EAAE;UAChBU,WAAW,GAAG,KAAK;UACnBjC,OAAO,CAACsB,gBAAgB,CAACtB,OAAO,CAAC0B,gBAAgB,CAAC,CAAC,GAAGK,OAAO,CAACvB,MAAM,CAAC;UACrE;QACJ;MACJ;MACA,IAAIyB,WAAW,EAAE;QACbjC,OAAO,CAACkC,eAAe,CAAC,CAAC;QACzBlC,OAAO,CAACM,GAAG,IAAIwB,SAAS;MAC5B,CAAC,MACI;QACD9B,OAAO,CAACS,cAAc,CAACsB,OAAO,CAAC;MACnC;IACJ,CAAC,SACO;MACJ/B,OAAO,CAACmB,mBAAmB,CAACzB,gBAAgB,CAAC;IACjD;EACJ,CAAC;EACDE,cAAc,CAACC,SAAS,CAACQ,UAAU,GAAG,UAAUF,CAAC,EAAEgC,EAAE,EAAE;IACnD,IAAIhC,CAAC,IAAI,GAAG,CAACiC,UAAU,CAAC,CAAC,CAAC,IAAIjC,CAAC,IAAI,GAAG,CAACiC,UAAU,CAAC,CAAC,CAAC,EAAE;MAClDD,EAAE,CAACpB,MAAM,CAACZ,CAAC,CAAC;IAChB,CAAC,MACI,IAAIA,CAAC,IAAI,GAAG,CAACiC,UAAU,CAAC,CAAC,CAAC,IAAIjC,CAAC,IAAI,GAAG,CAACiC,UAAU,CAAC,CAAC,CAAC,EAAE;MACvDD,EAAE,CAACpB,MAAM,CAACxB,WAAW,CAAC6B,SAAS,CAACjB,CAAC,GAAG,EAAE,CAAC,CAAC;IAC5C,CAAC,MACI;MACDR,gBAAgB,CAAC0C,gBAAgB,CAAC9C,WAAW,CAAC6B,SAAS,CAACjB,CAAC,CAAC,CAAC;IAC/D;EACJ,CAAC;EACDP,cAAc,CAACC,SAAS,CAACa,iBAAiB,GAAG,UAAUyB,EAAE,EAAE;IACvD,IAAIG,GAAG,GAAGH,EAAE,CAAC3B,MAAM;IACnB,IAAI8B,GAAG,KAAK,CAAC,EAAE;MACX,MAAM,IAAIT,KAAK,CAAC,iCAAiC,CAAC;IACtD;IACA,IAAIU,EAAE,GAAGJ,EAAE,CAACK,MAAM,CAAC,CAAC,CAAC,CAACJ,UAAU,CAAC,CAAC,CAAC;IACnC,IAAIK,EAAE,GAAGH,GAAG,IAAI,CAAC,GAAGH,EAAE,CAACK,MAAM,CAAC,CAAC,CAAC,CAACJ,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IAClD,IAAIM,EAAE,GAAGJ,GAAG,IAAI,CAAC,GAAGH,EAAE,CAACK,MAAM,CAAC,CAAC,CAAC,CAACJ,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IAClD,IAAIO,EAAE,GAAGL,GAAG,IAAI,CAAC,GAAGH,EAAE,CAACK,MAAM,CAAC,CAAC,CAAC,CAACJ,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IAClD,IAAIQ,CAAC,GAAG,CAACL,EAAE,IAAI,EAAE,KAAKE,EAAE,IAAI,EAAE,CAAC,IAAIC,EAAE,IAAI,CAAC,CAAC,GAAGC,EAAE;IAChD,IAAIE,GAAG,GAAID,CAAC,IAAI,EAAE,GAAI,GAAG;IACzB,IAAIE,GAAG,GAAIF,CAAC,IAAI,CAAC,GAAI,GAAG;IACxB,IAAIG,GAAG,GAAGH,CAAC,GAAG,GAAG;IACjB,IAAII,GAAG,GAAG,IAAIxD,aAAa,CAAC,CAAC;IAC7BwD,GAAG,CAACjC,MAAM,CAAC8B,GAAG,CAAC;IACf,IAAIP,GAAG,IAAI,CAAC,EAAE;MACVU,GAAG,CAACjC,MAAM,CAAC+B,GAAG,CAAC;IACnB;IACA,IAAIR,GAAG,IAAI,CAAC,EAAE;MACVU,GAAG,CAACjC,MAAM,CAACgC,GAAG,CAAC;IACnB;IACA,OAAOC,GAAG,CAACrC,QAAQ,CAAC,CAAC;EACzB,CAAC;EACD,OAAOf,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}