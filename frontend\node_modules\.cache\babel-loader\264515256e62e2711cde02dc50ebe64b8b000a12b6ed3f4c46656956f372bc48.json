{"ast": null, "code": "/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport Token from './Token';\nimport Integer from '../../util/Integer';\nvar SimpleToken = /** @class */function (_super) {\n  __extends(SimpleToken, _super);\n  function SimpleToken(previous, value, bitCount) {\n    var _this = _super.call(this, previous) || this;\n    _this.value = value;\n    _this.bitCount = bitCount;\n    return _this;\n  }\n  /**\n   * @Override\n   */\n  SimpleToken.prototype.appendTo = function (bitArray, text) {\n    bitArray.appendBits(this.value, this.bitCount);\n  };\n  SimpleToken.prototype.add = function (value, bitCount) {\n    return new SimpleToken(this, value, bitCount);\n  };\n  SimpleToken.prototype.addBinaryShift = function (start, byteCount) {\n    // no-op can't binary shift a simple token\n    console.warn('addBinaryShift on SimpleToken, this simply returns a copy of this token');\n    return new SimpleToken(this, start, byteCount);\n  };\n  /**\n   * @Override\n   */\n  SimpleToken.prototype.toString = function () {\n    var value = this.value & (1 << this.bitCount) - 1;\n    value |= 1 << this.bitCount;\n    return '<' + Integer.toBinaryString(value | 1 << this.bitCount).substring(1) + '>';\n  };\n  return SimpleToken;\n}(Token);\nexport default SimpleToken;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "Token", "Integer", "SimpleToken", "_super", "previous", "value", "bitCount", "_this", "call", "appendTo", "bitArray", "text", "appendBits", "add", "addBinaryShift", "start", "byteCount", "console", "warn", "toString", "toBinaryString", "substring"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/SimpleToken.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport Token from './Token';\nimport Integer from '../../util/Integer';\nvar SimpleToken = /** @class */ (function (_super) {\n    __extends(SimpleToken, _super);\n    function SimpleToken(previous, value, bitCount) {\n        var _this = _super.call(this, previous) || this;\n        _this.value = value;\n        _this.bitCount = bitCount;\n        return _this;\n    }\n    /**\n     * @Override\n     */\n    SimpleToken.prototype.appendTo = function (bitArray, text) {\n        bitArray.appendBits(this.value, this.bitCount);\n    };\n    SimpleToken.prototype.add = function (value, bitCount) {\n        return new SimpleToken(this, value, bitCount);\n    };\n    SimpleToken.prototype.addBinaryShift = function (start, byteCount) {\n        // no-op can't binary shift a simple token\n        console.warn('addBinaryShift on SimpleToken, this simply returns a copy of this token');\n        return new SimpleToken(this, start, byteCount);\n    };\n    /**\n     * @Override\n     */\n    SimpleToken.prototype.toString = function () {\n        var value = this.value & ((1 << this.bitCount) - 1);\n        value |= 1 << this.bitCount;\n        return '<' + Integer.toBinaryString(value | (1 << this.bitCount)).substring(1) + '>';\n    };\n    return SimpleToken;\n}(Token));\nexport default SimpleToken;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,KAAK,MAAM,SAAS;AAC3B,OAAOC,OAAO,MAAM,oBAAoB;AACxC,IAAIC,WAAW,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC/CjB,SAAS,CAACgB,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAC5C,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,QAAQ,CAAC,IAAI,IAAI;IAC/CG,KAAK,CAACF,KAAK,GAAGA,KAAK;IACnBE,KAAK,CAACD,QAAQ,GAAGA,QAAQ;IACzB,OAAOC,KAAK;EAChB;EACA;AACJ;AACA;EACIL,WAAW,CAACJ,SAAS,CAACW,QAAQ,GAAG,UAAUC,QAAQ,EAAEC,IAAI,EAAE;IACvDD,QAAQ,CAACE,UAAU,CAAC,IAAI,CAACP,KAAK,EAAE,IAAI,CAACC,QAAQ,CAAC;EAClD,CAAC;EACDJ,WAAW,CAACJ,SAAS,CAACe,GAAG,GAAG,UAAUR,KAAK,EAAEC,QAAQ,EAAE;IACnD,OAAO,IAAIJ,WAAW,CAAC,IAAI,EAAEG,KAAK,EAAEC,QAAQ,CAAC;EACjD,CAAC;EACDJ,WAAW,CAACJ,SAAS,CAACgB,cAAc,GAAG,UAAUC,KAAK,EAAEC,SAAS,EAAE;IAC/D;IACAC,OAAO,CAACC,IAAI,CAAC,yEAAyE,CAAC;IACvF,OAAO,IAAIhB,WAAW,CAAC,IAAI,EAAEa,KAAK,EAAEC,SAAS,CAAC;EAClD,CAAC;EACD;AACJ;AACA;EACId,WAAW,CAACJ,SAAS,CAACqB,QAAQ,GAAG,YAAY;IACzC,IAAId,KAAK,GAAG,IAAI,CAACA,KAAK,GAAI,CAAC,CAAC,IAAI,IAAI,CAACC,QAAQ,IAAI,CAAE;IACnDD,KAAK,IAAI,CAAC,IAAI,IAAI,CAACC,QAAQ;IAC3B,OAAO,GAAG,GAAGL,OAAO,CAACmB,cAAc,CAACf,KAAK,GAAI,CAAC,IAAI,IAAI,CAACC,QAAS,CAAC,CAACe,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EACxF,CAAC;EACD,OAAOnB,WAAW;AACtB,CAAC,CAACF,KAAK,CAAE;AACT,eAAeE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}