{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var DataMaskValues;\n(function (DataMaskValues) {\n  DataMaskValues[DataMaskValues[\"DATA_MASK_000\"] = 0] = \"DATA_MASK_000\";\n  DataMaskValues[DataMaskValues[\"DATA_MASK_001\"] = 1] = \"DATA_MASK_001\";\n  DataMaskValues[DataMaskValues[\"DATA_MASK_010\"] = 2] = \"DATA_MASK_010\";\n  DataMaskValues[DataMaskValues[\"DATA_MASK_011\"] = 3] = \"DATA_MASK_011\";\n  DataMaskValues[DataMaskValues[\"DATA_MASK_100\"] = 4] = \"DATA_MASK_100\";\n  DataMaskValues[DataMaskValues[\"DATA_MASK_101\"] = 5] = \"DATA_MASK_101\";\n  DataMaskValues[DataMaskValues[\"DATA_MASK_110\"] = 6] = \"DATA_MASK_110\";\n  DataMaskValues[DataMaskValues[\"DATA_MASK_111\"] = 7] = \"DATA_MASK_111\";\n})(DataMaskValues || (DataMaskValues = {}));\n/**\n * <p>Encapsulates data masks for the data bits in a QR code, per ISO 18004:2006 6.8. Implementations\n * of this class can un-mask a raw BitMatrix. For simplicity, they will unmask the entire BitMatrix,\n * including areas used for finder patterns, timing patterns, etc. These areas should be unused\n * after the point they are unmasked anyway.</p>\n *\n * <p>Note that the diagram in section 6.8.1 is misleading since it indicates that i is column position\n * and j is row position. In fact, as the text says, i is row position and j is column position.</p>\n *\n * <AUTHOR> Owen\n */\nvar DataMask = /** @class */function () {\n  // See ISO 18004:2006 6.8.1\n  function DataMask(value, isMasked) {\n    this.value = value;\n    this.isMasked = isMasked;\n  }\n  // End of enum constants.\n  /**\n   * <p>Implementations of this method reverse the data masking process applied to a QR Code and\n   * make its bits ready to read.</p>\n   *\n   * @param bits representation of QR Code bits\n   * @param dimension dimension of QR Code, represented by bits, being unmasked\n   */\n  DataMask.prototype.unmaskBitMatrix = function (bits, dimension /*int*/) {\n    for (var i = 0; i < dimension; i++) {\n      for (var j = 0; j < dimension; j++) {\n        if (this.isMasked(i, j)) {\n          bits.flip(j, i);\n        }\n      }\n    }\n  };\n  DataMask.values = new Map([\n  /**\n   * 000: mask bits for which (x + y) mod 2 == 0\n   */\n  [DataMaskValues.DATA_MASK_000, new DataMask(DataMaskValues.DATA_MASK_000, function (i /*int*/, j /*int*/) {\n    return (i + j & 0x01) === 0;\n  })],\n  /**\n   * 001: mask bits for which x mod 2 == 0\n   */\n  [DataMaskValues.DATA_MASK_001, new DataMask(DataMaskValues.DATA_MASK_001, function (i /*int*/, j /*int*/) {\n    return (i & 0x01) === 0;\n  })],\n  /**\n   * 010: mask bits for which y mod 3 == 0\n   */\n  [DataMaskValues.DATA_MASK_010, new DataMask(DataMaskValues.DATA_MASK_010, function (i /*int*/, j /*int*/) {\n    return j % 3 === 0;\n  })],\n  /**\n   * 011: mask bits for which (x + y) mod 3 == 0\n   */\n  [DataMaskValues.DATA_MASK_011, new DataMask(DataMaskValues.DATA_MASK_011, function (i /*int*/, j /*int*/) {\n    return (i + j) % 3 === 0;\n  })],\n  /**\n   * 100: mask bits for which (x/2 + y/3) mod 2 == 0\n   */\n  [DataMaskValues.DATA_MASK_100, new DataMask(DataMaskValues.DATA_MASK_100, function (i /*int*/, j /*int*/) {\n    return (Math.floor(i / 2) + Math.floor(j / 3) & 0x01) === 0;\n  })],\n  /**\n   * 101: mask bits for which xy mod 2 + xy mod 3 == 0\n   * equivalently, such that xy mod 6 == 0\n   */\n  [DataMaskValues.DATA_MASK_101, new DataMask(DataMaskValues.DATA_MASK_101, function (i /*int*/, j /*int*/) {\n    return i * j % 6 === 0;\n  })],\n  /**\n   * 110: mask bits for which (xy mod 2 + xy mod 3) mod 2 == 0\n   * equivalently, such that xy mod 6 < 3\n   */\n  [DataMaskValues.DATA_MASK_110, new DataMask(DataMaskValues.DATA_MASK_110, function (i /*int*/, j /*int*/) {\n    return i * j % 6 < 3;\n  })],\n  /**\n   * 111: mask bits for which ((x+y)mod 2 + xy mod 3) mod 2 == 0\n   * equivalently, such that (x + y + xy mod 3) mod 2 == 0\n   */\n  [DataMaskValues.DATA_MASK_111, new DataMask(DataMaskValues.DATA_MASK_111, function (i /*int*/, j /*int*/) {\n    return (i + j + i * j % 3 & 0x01) === 0;\n  })]]);\n  return DataMask;\n}();\nexport default DataMask;", "map": {"version": 3, "names": ["DataMaskValues", "DataMask", "value", "isMasked", "prototype", "unmaskBitMatrix", "bits", "dimension", "i", "j", "flip", "values", "Map", "DATA_MASK_000", "DATA_MASK_001", "DATA_MASK_010", "DATA_MASK_011", "DATA_MASK_100", "Math", "floor", "DATA_MASK_101", "DATA_MASK_110", "DATA_MASK_111"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/DataMask.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nexport var DataMaskValues;\n(function (DataMaskValues) {\n    DataMaskValues[DataMaskValues[\"DATA_MASK_000\"] = 0] = \"DATA_MASK_000\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_001\"] = 1] = \"DATA_MASK_001\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_010\"] = 2] = \"DATA_MASK_010\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_011\"] = 3] = \"DATA_MASK_011\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_100\"] = 4] = \"DATA_MASK_100\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_101\"] = 5] = \"DATA_MASK_101\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_110\"] = 6] = \"DATA_MASK_110\";\n    DataMaskValues[DataMaskValues[\"DATA_MASK_111\"] = 7] = \"DATA_MASK_111\";\n})(DataMaskValues || (DataMaskValues = {}));\n/**\n * <p>Encapsulates data masks for the data bits in a QR code, per ISO 18004:2006 6.8. Implementations\n * of this class can un-mask a raw BitMatrix. For simplicity, they will unmask the entire BitMatrix,\n * including areas used for finder patterns, timing patterns, etc. These areas should be unused\n * after the point they are unmasked anyway.</p>\n *\n * <p>Note that the diagram in section 6.8.1 is misleading since it indicates that i is column position\n * and j is row position. In fact, as the text says, i is row position and j is column position.</p>\n *\n * <AUTHOR> Owen\n */\nvar DataMask = /** @class */ (function () {\n    // See ISO 18004:2006 6.8.1\n    function DataMask(value, isMasked) {\n        this.value = value;\n        this.isMasked = isMasked;\n    }\n    // End of enum constants.\n    /**\n     * <p>Implementations of this method reverse the data masking process applied to a QR Code and\n     * make its bits ready to read.</p>\n     *\n     * @param bits representation of QR Code bits\n     * @param dimension dimension of QR Code, represented by bits, being unmasked\n     */\n    DataMask.prototype.unmaskBitMatrix = function (bits, dimension /*int*/) {\n        for (var i = 0; i < dimension; i++) {\n            for (var j = 0; j < dimension; j++) {\n                if (this.isMasked(i, j)) {\n                    bits.flip(j, i);\n                }\n            }\n        }\n    };\n    DataMask.values = new Map([\n        /**\n         * 000: mask bits for which (x + y) mod 2 == 0\n         */\n        [DataMaskValues.DATA_MASK_000, new DataMask(DataMaskValues.DATA_MASK_000, function (i /*int*/, j /*int*/) { return ((i + j) & 0x01) === 0; })],\n        /**\n         * 001: mask bits for which x mod 2 == 0\n         */\n        [DataMaskValues.DATA_MASK_001, new DataMask(DataMaskValues.DATA_MASK_001, function (i /*int*/, j /*int*/) { return (i & 0x01) === 0; })],\n        /**\n         * 010: mask bits for which y mod 3 == 0\n         */\n        [DataMaskValues.DATA_MASK_010, new DataMask(DataMaskValues.DATA_MASK_010, function (i /*int*/, j /*int*/) { return j % 3 === 0; })],\n        /**\n         * 011: mask bits for which (x + y) mod 3 == 0\n         */\n        [DataMaskValues.DATA_MASK_011, new DataMask(DataMaskValues.DATA_MASK_011, function (i /*int*/, j /*int*/) { return (i + j) % 3 === 0; })],\n        /**\n         * 100: mask bits for which (x/2 + y/3) mod 2 == 0\n         */\n        [DataMaskValues.DATA_MASK_100, new DataMask(DataMaskValues.DATA_MASK_100, function (i /*int*/, j /*int*/) { return ((Math.floor(i / 2) + Math.floor(j / 3)) & 0x01) === 0; })],\n        /**\n         * 101: mask bits for which xy mod 2 + xy mod 3 == 0\n         * equivalently, such that xy mod 6 == 0\n         */\n        [DataMaskValues.DATA_MASK_101, new DataMask(DataMaskValues.DATA_MASK_101, function (i /*int*/, j /*int*/) { return (i * j) % 6 === 0; })],\n        /**\n         * 110: mask bits for which (xy mod 2 + xy mod 3) mod 2 == 0\n         * equivalently, such that xy mod 6 < 3\n         */\n        [DataMaskValues.DATA_MASK_110, new DataMask(DataMaskValues.DATA_MASK_110, function (i /*int*/, j /*int*/) { return ((i * j) % 6) < 3; })],\n        /**\n         * 111: mask bits for which ((x+y)mod 2 + xy mod 3) mod 2 == 0\n         * equivalently, such that (x + y + xy mod 3) mod 2 == 0\n         */\n        [DataMaskValues.DATA_MASK_111, new DataMask(DataMaskValues.DATA_MASK_111, function (i /*int*/, j /*int*/) { return ((i + j + ((i * j) % 3)) & 0x01) === 0; })],\n    ]);\n    return DataMask;\n}());\nexport default DataMask;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,cAAc;AACzB,CAAC,UAAUA,cAAc,EAAE;EACvBA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACrEA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACrEA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACrEA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACrEA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACrEA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACrEA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACrEA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;AACzE,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,aAAe,YAAY;EACtC;EACA,SAASA,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC/B,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIF,QAAQ,CAACG,SAAS,CAACC,eAAe,GAAG,UAAUC,IAAI,EAAEC,SAAS,CAAC,SAAS;IACpE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,EAAEC,CAAC,EAAE,EAAE;MAChC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,EAAEE,CAAC,EAAE,EAAE;QAChC,IAAI,IAAI,CAACN,QAAQ,CAACK,CAAC,EAAEC,CAAC,CAAC,EAAE;UACrBH,IAAI,CAACI,IAAI,CAACD,CAAC,EAAED,CAAC,CAAC;QACnB;MACJ;IACJ;EACJ,CAAC;EACDP,QAAQ,CAACU,MAAM,GAAG,IAAIC,GAAG,CAAC;EACtB;AACR;AACA;EACQ,CAACZ,cAAc,CAACa,aAAa,EAAE,IAAIZ,QAAQ,CAACD,cAAc,CAACa,aAAa,EAAE,UAAUL,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAAE,OAAO,CAAED,CAAC,GAAGC,CAAC,GAAI,IAAI,MAAM,CAAC;EAAE,CAAC,CAAC,CAAC;EAC9I;AACR;AACA;EACQ,CAACT,cAAc,CAACc,aAAa,EAAE,IAAIb,QAAQ,CAACD,cAAc,CAACc,aAAa,EAAE,UAAUN,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAAE,OAAO,CAACD,CAAC,GAAG,IAAI,MAAM,CAAC;EAAE,CAAC,CAAC,CAAC;EACxI;AACR;AACA;EACQ,CAACR,cAAc,CAACe,aAAa,EAAE,IAAId,QAAQ,CAACD,cAAc,CAACe,aAAa,EAAE,UAAUP,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAAE,OAAOA,CAAC,GAAG,CAAC,KAAK,CAAC;EAAE,CAAC,CAAC,CAAC;EACnI;AACR;AACA;EACQ,CAACT,cAAc,CAACgB,aAAa,EAAE,IAAIf,QAAQ,CAACD,cAAc,CAACgB,aAAa,EAAE,UAAUR,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAAE,OAAO,CAACD,CAAC,GAAGC,CAAC,IAAI,CAAC,KAAK,CAAC;EAAE,CAAC,CAAC,CAAC;EACzI;AACR;AACA;EACQ,CAACT,cAAc,CAACiB,aAAa,EAAE,IAAIhB,QAAQ,CAACD,cAAc,CAACiB,aAAa,EAAE,UAAUT,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAAE,OAAO,CAAES,IAAI,CAACC,KAAK,CAACX,CAAC,GAAG,CAAC,CAAC,GAAGU,IAAI,CAACC,KAAK,CAACV,CAAC,GAAG,CAAC,CAAC,GAAI,IAAI,MAAM,CAAC;EAAE,CAAC,CAAC,CAAC;EAC9K;AACR;AACA;AACA;EACQ,CAACT,cAAc,CAACoB,aAAa,EAAE,IAAInB,QAAQ,CAACD,cAAc,CAACoB,aAAa,EAAE,UAAUZ,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAAE,OAAQD,CAAC,GAAGC,CAAC,GAAI,CAAC,KAAK,CAAC;EAAE,CAAC,CAAC,CAAC;EACzI;AACR;AACA;AACA;EACQ,CAACT,cAAc,CAACqB,aAAa,EAAE,IAAIpB,QAAQ,CAACD,cAAc,CAACqB,aAAa,EAAE,UAAUb,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAAE,OAASD,CAAC,GAAGC,CAAC,GAAI,CAAC,GAAI,CAAC;EAAE,CAAC,CAAC,CAAC;EACzI;AACR;AACA;AACA;EACQ,CAACT,cAAc,CAACsB,aAAa,EAAE,IAAIrB,QAAQ,CAACD,cAAc,CAACsB,aAAa,EAAE,UAAUd,CAAC,CAAC,SAASC,CAAC,CAAC,SAAS;IAAE,OAAO,CAAED,CAAC,GAAGC,CAAC,GAAKD,CAAC,GAAGC,CAAC,GAAI,CAAE,GAAI,IAAI,MAAM,CAAC;EAAE,CAAC,CAAC,CAAC,CACjK,CAAC;EACF,OAAOR,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}