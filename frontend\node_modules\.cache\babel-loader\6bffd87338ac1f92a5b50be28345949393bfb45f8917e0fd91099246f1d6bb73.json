{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const getPickersLocalization = pickersTranslations => {\n  return {\n    components: {\n      MuiLocalizationProvider: {\n        defaultProps: {\n          localeText: _extends({}, pickersTranslations)\n        }\n      }\n    }\n  };\n};\nexport const buildGetOpenDialogAriaText = params => {\n  const {\n    utils,\n    formatKey,\n    contextTranslation,\n    propsTranslation\n  } = params;\n  return value => {\n    const formattedValue = value !== null && utils.isValid(value) ? utils.format(value, formatKey) : null;\n    const translation = propsTranslation ?? contextTranslation;\n    return translation(value, utils, formattedValue);\n  };\n};", "map": {"version": 3, "names": ["_extends", "getPickersLocalization", "pickersTranslations", "components", "MuiLocalizationProvider", "defaultProps", "localeText", "buildGetOpenDialogAriaText", "params", "utils", "formatKey", "contextTranslation", "propsTranslation", "value", "formattedValue", "<PERSON><PERSON><PERSON><PERSON>", "format", "translation"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/locales/utils/getPickersLocalization.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const getPickersLocalization = pickersTranslations => {\n  return {\n    components: {\n      MuiLocalizationProvider: {\n        defaultProps: {\n          localeText: _extends({}, pickersTranslations)\n        }\n      }\n    }\n  };\n};\nexport const buildGetOpenDialogAriaText = params => {\n  const {\n    utils,\n    formatKey,\n    contextTranslation,\n    propsTranslation\n  } = params;\n  return value => {\n    const formattedValue = value !== null && utils.isValid(value) ? utils.format(value, formatKey) : null;\n    const translation = propsTranslation ?? contextTranslation;\n    return translation(value, utils, formattedValue);\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,MAAMC,sBAAsB,GAAGC,mBAAmB,IAAI;EAC3D,OAAO;IACLC,UAAU,EAAE;MACVC,uBAAuB,EAAE;QACvBC,YAAY,EAAE;UACZC,UAAU,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEE,mBAAmB;QAC9C;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,OAAO,MAAMK,0BAA0B,GAAGC,MAAM,IAAI;EAClD,MAAM;IACJC,KAAK;IACLC,SAAS;IACTC,kBAAkB;IAClBC;EACF,CAAC,GAAGJ,MAAM;EACV,OAAOK,KAAK,IAAI;IACd,MAAMC,cAAc,GAAGD,KAAK,KAAK,IAAI,IAAIJ,KAAK,CAACM,OAAO,CAACF,KAAK,CAAC,GAAGJ,KAAK,CAACO,MAAM,CAACH,KAAK,EAAEH,SAAS,CAAC,GAAG,IAAI;IACrG,MAAMO,WAAW,GAAGL,gBAAgB,IAAID,kBAAkB;IAC1D,OAAOM,WAAW,CAACJ,KAAK,EAAEJ,KAAK,EAAEK,cAAc,CAAC;EAClD,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}