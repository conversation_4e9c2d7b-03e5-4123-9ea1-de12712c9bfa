{"ast": null, "code": "import EncodeHintType from '../core/EncodeHintType';\nimport Encoder from '../core/qrcode/encoder/Encoder';\nimport ErrorCorrectionLevel from '../core/qrcode/decoder/ErrorCorrectionLevel';\nimport IllegalArgumentException from '../core/IllegalArgumentException';\nimport IllegalStateException from '../core/IllegalStateException';\n/**\n * @deprecated Moving to @zxing/browser\n */\nvar BrowserQRCodeSvgWriter = /** @class */function () {\n  function BrowserQRCodeSvgWriter() {}\n  /**\n   * Writes and renders a QRCode SVG element.\n   *\n   * @param contents\n   * @param width\n   * @param height\n   * @param hints\n   */\n  BrowserQRCodeSvgWriter.prototype.write = function (contents, width, height, hints) {\n    if (hints === void 0) {\n      hints = null;\n    }\n    if (contents.length === 0) {\n      throw new IllegalArgumentException('Found empty contents');\n    }\n    // if (format != BarcodeFormat.QR_CODE) {\n    //   throw new IllegalArgumentException(\"Can only encode QR_CODE, but got \" + format)\n    // }\n    if (width < 0 || height < 0) {\n      throw new IllegalArgumentException('Requested dimensions are too small: ' + width + 'x' + height);\n    }\n    var errorCorrectionLevel = ErrorCorrectionLevel.L;\n    var quietZone = BrowserQRCodeSvgWriter.QUIET_ZONE_SIZE;\n    if (hints !== null) {\n      if (undefined !== hints.get(EncodeHintType.ERROR_CORRECTION)) {\n        errorCorrectionLevel = ErrorCorrectionLevel.fromString(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n      }\n      if (undefined !== hints.get(EncodeHintType.MARGIN)) {\n        quietZone = Number.parseInt(hints.get(EncodeHintType.MARGIN).toString(), 10);\n      }\n    }\n    var code = Encoder.encode(contents, errorCorrectionLevel, hints);\n    return this.renderResult(code, width, height, quietZone);\n  };\n  /**\n   * Renders the result and then appends it to the DOM.\n   */\n  BrowserQRCodeSvgWriter.prototype.writeToDom = function (containerElement, contents, width, height, hints) {\n    if (hints === void 0) {\n      hints = null;\n    }\n    if (typeof containerElement === 'string') {\n      containerElement = document.querySelector(containerElement);\n    }\n    var svgElement = this.write(contents, width, height, hints);\n    if (containerElement) containerElement.appendChild(svgElement);\n  };\n  /**\n   * Note that the input matrix uses 0 == white, 1 == black.\n   * The output matrix uses 0 == black, 255 == white (i.e. an 8 bit greyscale bitmap).\n   */\n  BrowserQRCodeSvgWriter.prototype.renderResult = function (code, width /*int*/, height /*int*/, quietZone /*int*/) {\n    var input = code.getMatrix();\n    if (input === null) {\n      throw new IllegalStateException();\n    }\n    var inputWidth = input.getWidth();\n    var inputHeight = input.getHeight();\n    var qrWidth = inputWidth + quietZone * 2;\n    var qrHeight = inputHeight + quietZone * 2;\n    var outputWidth = Math.max(width, qrWidth);\n    var outputHeight = Math.max(height, qrHeight);\n    var multiple = Math.min(Math.floor(outputWidth / qrWidth), Math.floor(outputHeight / qrHeight));\n    // Padding includes both the quiet zone and the extra white pixels to accommodate the requested\n    // dimensions. For example, if input is 25x25 the QR will be 33x33 including the quiet zone.\n    // If the requested size is 200x160, the multiple will be 4, for a QR of 132x132. These will\n    // handle all the padding from 100x100 (the actual QR) up to 200x160.\n    var leftPadding = Math.floor((outputWidth - inputWidth * multiple) / 2);\n    var topPadding = Math.floor((outputHeight - inputHeight * multiple) / 2);\n    var svgElement = this.createSVGElement(outputWidth, outputHeight);\n    for (var inputY = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n      // Write the contents of this row of the barcode\n      for (var inputX = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n        if (input.get(inputX, inputY) === 1) {\n          var svgRectElement = this.createSvgRectElement(outputX, outputY, multiple, multiple);\n          svgElement.appendChild(svgRectElement);\n        }\n      }\n    }\n    return svgElement;\n  };\n  /**\n   * Creates a SVG element.\n   *\n   * @param w SVG's width attribute\n   * @param h SVG's height attribute\n   */\n  BrowserQRCodeSvgWriter.prototype.createSVGElement = function (w, h) {\n    var svgElement = document.createElementNS(BrowserQRCodeSvgWriter.SVG_NS, 'svg');\n    svgElement.setAttributeNS(null, 'height', w.toString());\n    svgElement.setAttributeNS(null, 'width', h.toString());\n    return svgElement;\n  };\n  /**\n   * Creates a SVG rect element.\n   *\n   * @param x Element's x coordinate\n   * @param y Element's y coordinate\n   * @param w Element's width attribute\n   * @param h Element's height attribute\n   */\n  BrowserQRCodeSvgWriter.prototype.createSvgRectElement = function (x, y, w, h) {\n    var rect = document.createElementNS(BrowserQRCodeSvgWriter.SVG_NS, 'rect');\n    rect.setAttributeNS(null, 'x', x.toString());\n    rect.setAttributeNS(null, 'y', y.toString());\n    rect.setAttributeNS(null, 'height', w.toString());\n    rect.setAttributeNS(null, 'width', h.toString());\n    rect.setAttributeNS(null, 'fill', '#000000');\n    return rect;\n  };\n  BrowserQRCodeSvgWriter.QUIET_ZONE_SIZE = 4;\n  /**\n   * SVG markup NameSpace\n   */\n  BrowserQRCodeSvgWriter.SVG_NS = 'http://www.w3.org/2000/svg';\n  return BrowserQRCodeSvgWriter;\n}();\nexport { BrowserQRCodeSvgWriter };", "map": {"version": 3, "names": ["EncodeHintType", "Encoder", "ErrorCorrectionLevel", "IllegalArgumentException", "IllegalStateException", "BrowserQRCodeSvgWriter", "prototype", "write", "contents", "width", "height", "hints", "length", "errorCorrectionLevel", "L", "quietZone", "QUIET_ZONE_SIZE", "undefined", "get", "ERROR_CORRECTION", "fromString", "toString", "MARGIN", "Number", "parseInt", "code", "encode", "renderResult", "writeToDom", "containerElement", "document", "querySelector", "svgElement", "append<PERSON><PERSON><PERSON>", "input", "getMatrix", "inputWidth", "getWidth", "inputHeight", "getHeight", "qrWidth", "qrHeight", "outputWidth", "Math", "max", "outputHeight", "multiple", "min", "floor", "leftPadding", "topPadding", "createSVGElement", "inputY", "outputY", "inputX", "outputX", "svgRectElement", "createSvgRectElement", "w", "h", "createElementNS", "SVG_NS", "setAttributeNS", "x", "y", "rect"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/browser/BrowserQRCodeSvgWriter.js"], "sourcesContent": ["import EncodeHintType from '../core/EncodeHintType';\nimport Encoder from '../core/qrcode/encoder/Encoder';\nimport ErrorCorrectionLevel from '../core/qrcode/decoder/ErrorCorrectionLevel';\nimport IllegalArgumentException from '../core/IllegalArgumentException';\nimport IllegalStateException from '../core/IllegalStateException';\n/**\n * @deprecated Moving to @zxing/browser\n */\nvar BrowserQRCodeSvgWriter = /** @class */ (function () {\n    function BrowserQRCodeSvgWriter() {\n    }\n    /**\n     * Writes and renders a QRCode SVG element.\n     *\n     * @param contents\n     * @param width\n     * @param height\n     * @param hints\n     */\n    BrowserQRCodeSvgWriter.prototype.write = function (contents, width, height, hints) {\n        if (hints === void 0) { hints = null; }\n        if (contents.length === 0) {\n            throw new IllegalArgumentException('Found empty contents');\n        }\n        // if (format != BarcodeFormat.QR_CODE) {\n        //   throw new IllegalArgumentException(\"Can only encode QR_CODE, but got \" + format)\n        // }\n        if (width < 0 || height < 0) {\n            throw new IllegalArgumentException('Requested dimensions are too small: ' + width + 'x' + height);\n        }\n        var errorCorrectionLevel = ErrorCorrectionLevel.L;\n        var quietZone = BrowserQRCodeSvgWriter.QUIET_ZONE_SIZE;\n        if (hints !== null) {\n            if (undefined !== hints.get(EncodeHintType.ERROR_CORRECTION)) {\n                errorCorrectionLevel = ErrorCorrectionLevel.fromString(hints.get(EncodeHintType.ERROR_CORRECTION).toString());\n            }\n            if (undefined !== hints.get(EncodeHintType.MARGIN)) {\n                quietZone = Number.parseInt(hints.get(EncodeHintType.MARGIN).toString(), 10);\n            }\n        }\n        var code = Encoder.encode(contents, errorCorrectionLevel, hints);\n        return this.renderResult(code, width, height, quietZone);\n    };\n    /**\n     * Renders the result and then appends it to the DOM.\n     */\n    BrowserQRCodeSvgWriter.prototype.writeToDom = function (containerElement, contents, width, height, hints) {\n        if (hints === void 0) { hints = null; }\n        if (typeof containerElement === 'string') {\n            containerElement = document.querySelector(containerElement);\n        }\n        var svgElement = this.write(contents, width, height, hints);\n        if (containerElement)\n            containerElement.appendChild(svgElement);\n    };\n    /**\n     * Note that the input matrix uses 0 == white, 1 == black.\n     * The output matrix uses 0 == black, 255 == white (i.e. an 8 bit greyscale bitmap).\n     */\n    BrowserQRCodeSvgWriter.prototype.renderResult = function (code, width /*int*/, height /*int*/, quietZone /*int*/) {\n        var input = code.getMatrix();\n        if (input === null) {\n            throw new IllegalStateException();\n        }\n        var inputWidth = input.getWidth();\n        var inputHeight = input.getHeight();\n        var qrWidth = inputWidth + (quietZone * 2);\n        var qrHeight = inputHeight + (quietZone * 2);\n        var outputWidth = Math.max(width, qrWidth);\n        var outputHeight = Math.max(height, qrHeight);\n        var multiple = Math.min(Math.floor(outputWidth / qrWidth), Math.floor(outputHeight / qrHeight));\n        // Padding includes both the quiet zone and the extra white pixels to accommodate the requested\n        // dimensions. For example, if input is 25x25 the QR will be 33x33 including the quiet zone.\n        // If the requested size is 200x160, the multiple will be 4, for a QR of 132x132. These will\n        // handle all the padding from 100x100 (the actual QR) up to 200x160.\n        var leftPadding = Math.floor((outputWidth - (inputWidth * multiple)) / 2);\n        var topPadding = Math.floor((outputHeight - (inputHeight * multiple)) / 2);\n        var svgElement = this.createSVGElement(outputWidth, outputHeight);\n        for (var inputY = 0, outputY = topPadding; inputY < inputHeight; inputY++, outputY += multiple) {\n            // Write the contents of this row of the barcode\n            for (var inputX = 0, outputX = leftPadding; inputX < inputWidth; inputX++, outputX += multiple) {\n                if (input.get(inputX, inputY) === 1) {\n                    var svgRectElement = this.createSvgRectElement(outputX, outputY, multiple, multiple);\n                    svgElement.appendChild(svgRectElement);\n                }\n            }\n        }\n        return svgElement;\n    };\n    /**\n     * Creates a SVG element.\n     *\n     * @param w SVG's width attribute\n     * @param h SVG's height attribute\n     */\n    BrowserQRCodeSvgWriter.prototype.createSVGElement = function (w, h) {\n        var svgElement = document.createElementNS(BrowserQRCodeSvgWriter.SVG_NS, 'svg');\n        svgElement.setAttributeNS(null, 'height', w.toString());\n        svgElement.setAttributeNS(null, 'width', h.toString());\n        return svgElement;\n    };\n    /**\n     * Creates a SVG rect element.\n     *\n     * @param x Element's x coordinate\n     * @param y Element's y coordinate\n     * @param w Element's width attribute\n     * @param h Element's height attribute\n     */\n    BrowserQRCodeSvgWriter.prototype.createSvgRectElement = function (x, y, w, h) {\n        var rect = document.createElementNS(BrowserQRCodeSvgWriter.SVG_NS, 'rect');\n        rect.setAttributeNS(null, 'x', x.toString());\n        rect.setAttributeNS(null, 'y', y.toString());\n        rect.setAttributeNS(null, 'height', w.toString());\n        rect.setAttributeNS(null, 'width', h.toString());\n        rect.setAttributeNS(null, 'fill', '#000000');\n        return rect;\n    };\n    BrowserQRCodeSvgWriter.QUIET_ZONE_SIZE = 4;\n    /**\n     * SVG markup NameSpace\n     */\n    BrowserQRCodeSvgWriter.SVG_NS = 'http://www.w3.org/2000/svg';\n    return BrowserQRCodeSvgWriter;\n}());\nexport { BrowserQRCodeSvgWriter };\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,wBAAwB;AACnD,OAAOC,OAAO,MAAM,gCAAgC;AACpD,OAAOC,oBAAoB,MAAM,6CAA6C;AAC9E,OAAOC,wBAAwB,MAAM,kCAAkC;AACvE,OAAOC,qBAAqB,MAAM,+BAA+B;AACjE;AACA;AACA;AACA,IAAIC,sBAAsB,GAAG,aAAe,YAAY;EACpD,SAASA,sBAAsBA,CAAA,EAAG,CAClC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,sBAAsB,CAACC,SAAS,CAACC,KAAK,GAAG,UAAUC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAC/E,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC,IAAIH,QAAQ,CAACI,MAAM,KAAK,CAAC,EAAE;MACvB,MAAM,IAAIT,wBAAwB,CAAC,sBAAsB,CAAC;IAC9D;IACA;IACA;IACA;IACA,IAAIM,KAAK,GAAG,CAAC,IAAIC,MAAM,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIP,wBAAwB,CAAC,sCAAsC,GAAGM,KAAK,GAAG,GAAG,GAAGC,MAAM,CAAC;IACrG;IACA,IAAIG,oBAAoB,GAAGX,oBAAoB,CAACY,CAAC;IACjD,IAAIC,SAAS,GAAGV,sBAAsB,CAACW,eAAe;IACtD,IAAIL,KAAK,KAAK,IAAI,EAAE;MAChB,IAAIM,SAAS,KAAKN,KAAK,CAACO,GAAG,CAAClB,cAAc,CAACmB,gBAAgB,CAAC,EAAE;QAC1DN,oBAAoB,GAAGX,oBAAoB,CAACkB,UAAU,CAACT,KAAK,CAACO,GAAG,CAAClB,cAAc,CAACmB,gBAAgB,CAAC,CAACE,QAAQ,CAAC,CAAC,CAAC;MACjH;MACA,IAAIJ,SAAS,KAAKN,KAAK,CAACO,GAAG,CAAClB,cAAc,CAACsB,MAAM,CAAC,EAAE;QAChDP,SAAS,GAAGQ,MAAM,CAACC,QAAQ,CAACb,KAAK,CAACO,GAAG,CAAClB,cAAc,CAACsB,MAAM,CAAC,CAACD,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC;MAChF;IACJ;IACA,IAAII,IAAI,GAAGxB,OAAO,CAACyB,MAAM,CAAClB,QAAQ,EAAEK,oBAAoB,EAAEF,KAAK,CAAC;IAChE,OAAO,IAAI,CAACgB,YAAY,CAACF,IAAI,EAAEhB,KAAK,EAAEC,MAAM,EAAEK,SAAS,CAAC;EAC5D,CAAC;EACD;AACJ;AACA;EACIV,sBAAsB,CAACC,SAAS,CAACsB,UAAU,GAAG,UAAUC,gBAAgB,EAAErB,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAE;IACtG,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC,IAAI,OAAOkB,gBAAgB,KAAK,QAAQ,EAAE;MACtCA,gBAAgB,GAAGC,QAAQ,CAACC,aAAa,CAACF,gBAAgB,CAAC;IAC/D;IACA,IAAIG,UAAU,GAAG,IAAI,CAACzB,KAAK,CAACC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,CAAC;IAC3D,IAAIkB,gBAAgB,EAChBA,gBAAgB,CAACI,WAAW,CAACD,UAAU,CAAC;EAChD,CAAC;EACD;AACJ;AACA;AACA;EACI3B,sBAAsB,CAACC,SAAS,CAACqB,YAAY,GAAG,UAAUF,IAAI,EAAEhB,KAAK,CAAC,SAASC,MAAM,CAAC,SAASK,SAAS,CAAC,SAAS;IAC9G,IAAImB,KAAK,GAAGT,IAAI,CAACU,SAAS,CAAC,CAAC;IAC5B,IAAID,KAAK,KAAK,IAAI,EAAE;MAChB,MAAM,IAAI9B,qBAAqB,CAAC,CAAC;IACrC;IACA,IAAIgC,UAAU,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC;IACjC,IAAIC,WAAW,GAAGJ,KAAK,CAACK,SAAS,CAAC,CAAC;IACnC,IAAIC,OAAO,GAAGJ,UAAU,GAAIrB,SAAS,GAAG,CAAE;IAC1C,IAAI0B,QAAQ,GAAGH,WAAW,GAAIvB,SAAS,GAAG,CAAE;IAC5C,IAAI2B,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACnC,KAAK,EAAE+B,OAAO,CAAC;IAC1C,IAAIK,YAAY,GAAGF,IAAI,CAACC,GAAG,CAAClC,MAAM,EAAE+B,QAAQ,CAAC;IAC7C,IAAIK,QAAQ,GAAGH,IAAI,CAACI,GAAG,CAACJ,IAAI,CAACK,KAAK,CAACN,WAAW,GAAGF,OAAO,CAAC,EAAEG,IAAI,CAACK,KAAK,CAACH,YAAY,GAAGJ,QAAQ,CAAC,CAAC;IAC/F;IACA;IACA;IACA;IACA,IAAIQ,WAAW,GAAGN,IAAI,CAACK,KAAK,CAAC,CAACN,WAAW,GAAIN,UAAU,GAAGU,QAAS,IAAI,CAAC,CAAC;IACzE,IAAII,UAAU,GAAGP,IAAI,CAACK,KAAK,CAAC,CAACH,YAAY,GAAIP,WAAW,GAAGQ,QAAS,IAAI,CAAC,CAAC;IAC1E,IAAId,UAAU,GAAG,IAAI,CAACmB,gBAAgB,CAACT,WAAW,EAAEG,YAAY,CAAC;IACjE,KAAK,IAAIO,MAAM,GAAG,CAAC,EAAEC,OAAO,GAAGH,UAAU,EAAEE,MAAM,GAAGd,WAAW,EAAEc,MAAM,EAAE,EAAEC,OAAO,IAAIP,QAAQ,EAAE;MAC5F;MACA,KAAK,IAAIQ,MAAM,GAAG,CAAC,EAAEC,OAAO,GAAGN,WAAW,EAAEK,MAAM,GAAGlB,UAAU,EAAEkB,MAAM,EAAE,EAAEC,OAAO,IAAIT,QAAQ,EAAE;QAC5F,IAAIZ,KAAK,CAAChB,GAAG,CAACoC,MAAM,EAAEF,MAAM,CAAC,KAAK,CAAC,EAAE;UACjC,IAAII,cAAc,GAAG,IAAI,CAACC,oBAAoB,CAACF,OAAO,EAAEF,OAAO,EAAEP,QAAQ,EAAEA,QAAQ,CAAC;UACpFd,UAAU,CAACC,WAAW,CAACuB,cAAc,CAAC;QAC1C;MACJ;IACJ;IACA,OAAOxB,UAAU;EACrB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI3B,sBAAsB,CAACC,SAAS,CAAC6C,gBAAgB,GAAG,UAAUO,CAAC,EAAEC,CAAC,EAAE;IAChE,IAAI3B,UAAU,GAAGF,QAAQ,CAAC8B,eAAe,CAACvD,sBAAsB,CAACwD,MAAM,EAAE,KAAK,CAAC;IAC/E7B,UAAU,CAAC8B,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAEJ,CAAC,CAACrC,QAAQ,CAAC,CAAC,CAAC;IACvDW,UAAU,CAAC8B,cAAc,CAAC,IAAI,EAAE,OAAO,EAAEH,CAAC,CAACtC,QAAQ,CAAC,CAAC,CAAC;IACtD,OAAOW,UAAU;EACrB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI3B,sBAAsB,CAACC,SAAS,CAACmD,oBAAoB,GAAG,UAAUM,CAAC,EAAEC,CAAC,EAAEN,CAAC,EAAEC,CAAC,EAAE;IAC1E,IAAIM,IAAI,GAAGnC,QAAQ,CAAC8B,eAAe,CAACvD,sBAAsB,CAACwD,MAAM,EAAE,MAAM,CAAC;IAC1EI,IAAI,CAACH,cAAc,CAAC,IAAI,EAAE,GAAG,EAAEC,CAAC,CAAC1C,QAAQ,CAAC,CAAC,CAAC;IAC5C4C,IAAI,CAACH,cAAc,CAAC,IAAI,EAAE,GAAG,EAAEE,CAAC,CAAC3C,QAAQ,CAAC,CAAC,CAAC;IAC5C4C,IAAI,CAACH,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAEJ,CAAC,CAACrC,QAAQ,CAAC,CAAC,CAAC;IACjD4C,IAAI,CAACH,cAAc,CAAC,IAAI,EAAE,OAAO,EAAEH,CAAC,CAACtC,QAAQ,CAAC,CAAC,CAAC;IAChD4C,IAAI,CAACH,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC;IAC5C,OAAOG,IAAI;EACf,CAAC;EACD5D,sBAAsB,CAACW,eAAe,GAAG,CAAC;EAC1C;AACJ;AACA;EACIX,sBAAsB,CAACwD,MAAM,GAAG,4BAA4B;EAC5D,OAAOxD,sBAAsB;AACjC,CAAC,CAAC,CAAE;AACJ,SAASA,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}