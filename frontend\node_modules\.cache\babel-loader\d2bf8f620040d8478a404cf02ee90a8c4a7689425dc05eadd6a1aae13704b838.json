{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.common {*/\nimport GridSampler from './GridSampler';\nimport BitMatrix from './BitMatrix';\nimport PerspectiveTransform from './PerspectiveTransform';\nimport NotFoundException from '../NotFoundException';\n/**\n * <AUTHOR> Owen\n */\nvar DefaultGridSampler = /** @class */function (_super) {\n  __extends(DefaultGridSampler, _super);\n  function DefaultGridSampler() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /*@Override*/\n  DefaultGridSampler.prototype.sampleGrid = function (image, dimensionX /*int*/, dimensionY /*int*/, p1ToX /*float*/, p1ToY /*float*/, p2ToX /*float*/, p2ToY /*float*/, p3ToX /*float*/, p3ToY /*float*/, p4ToX /*float*/, p4ToY /*float*/, p1FromX /*float*/, p1FromY /*float*/, p2FromX /*float*/, p2FromY /*float*/, p3FromX /*float*/, p3FromY /*float*/, p4FromX /*float*/, p4FromY /*float*/) {\n    var transform = PerspectiveTransform.quadrilateralToQuadrilateral(p1ToX, p1ToY, p2ToX, p2ToY, p3ToX, p3ToY, p4ToX, p4ToY, p1FromX, p1FromY, p2FromX, p2FromY, p3FromX, p3FromY, p4FromX, p4FromY);\n    return this.sampleGridWithTransform(image, dimensionX, dimensionY, transform);\n  };\n  /*@Override*/\n  DefaultGridSampler.prototype.sampleGridWithTransform = function (image, dimensionX /*int*/, dimensionY /*int*/, transform) {\n    if (dimensionX <= 0 || dimensionY <= 0) {\n      throw new NotFoundException();\n    }\n    var bits = new BitMatrix(dimensionX, dimensionY);\n    var points = new Float32Array(2 * dimensionX);\n    for (var y = 0; y < dimensionY; y++) {\n      var max = points.length;\n      var iValue = y + 0.5;\n      for (var x = 0; x < max; x += 2) {\n        points[x] = x / 2 + 0.5;\n        points[x + 1] = iValue;\n      }\n      transform.transformPoints(points);\n      // Quick check to see if points transformed to something inside the image\n      // sufficient to check the endpoints\n      GridSampler.checkAndNudgePoints(image, points);\n      try {\n        for (var x = 0; x < max; x += 2) {\n          if (image.get(Math.floor(points[x]), Math.floor(points[x + 1]))) {\n            // Black(-ish) pixel\n            bits.set(x / 2, y);\n          }\n        }\n      } catch (aioobe /*: ArrayIndexOutOfBoundsException*/) {\n        // This feels wrong, but, sometimes if the finder patterns are misidentified, the resulting\n        // transform gets \"twisted\" such that it maps a straight line of points to a set of points\n        // whose endpoints are in bounds, but others are not. There is probably some mathematical\n        // way to detect this about the transformation that I don't know yet.\n        // This results in an ugly runtime exception despite our clever checks above -- can't have\n        // that. We could check each point's coordinates but that feels duplicative. We settle for\n        // catching and wrapping ArrayIndexOutOfBoundsException.\n        throw new NotFoundException();\n      }\n    }\n    return bits;\n  };\n  return DefaultGridSampler;\n}(GridSampler);\nexport default DefaultGridSampler;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "GridSampler", "BitMatrix", "PerspectiveTransform", "NotFoundException", "DefaultGridSampler", "_super", "apply", "arguments", "sampleGrid", "image", "dimensionX", "dimensionY", "p1ToX", "p1ToY", "p2ToX", "p2ToY", "p3ToX", "p3ToY", "p4ToX", "p4ToY", "p1FromX", "p1FromY", "p2FromX", "p2FromY", "p3FromX", "p3FromY", "p4FromX", "p4FromY", "transform", "quadrilateralToQuadrilateral", "sampleGridWithTransform", "bits", "points", "Float32Array", "y", "max", "length", "iValue", "x", "transformPoints", "checkAndNudgePoints", "get", "Math", "floor", "set", "aioobe"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/DefaultGridSampler.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.common {*/\nimport GridSampler from './GridSampler';\nimport BitMatrix from './BitMatrix';\nimport PerspectiveTransform from './PerspectiveTransform';\nimport NotFoundException from '../NotFoundException';\n/**\n * <AUTHOR> Owen\n */\nvar DefaultGridSampler = /** @class */ (function (_super) {\n    __extends(DefaultGridSampler, _super);\n    function DefaultGridSampler() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /*@Override*/\n    DefaultGridSampler.prototype.sampleGrid = function (image, dimensionX /*int*/, dimensionY /*int*/, p1ToX /*float*/, p1ToY /*float*/, p2ToX /*float*/, p2ToY /*float*/, p3ToX /*float*/, p3ToY /*float*/, p4ToX /*float*/, p4ToY /*float*/, p1FromX /*float*/, p1FromY /*float*/, p2FromX /*float*/, p2FromY /*float*/, p3FromX /*float*/, p3FromY /*float*/, p4FromX /*float*/, p4FromY /*float*/) {\n        var transform = PerspectiveTransform.quadrilateralToQuadrilateral(p1ToX, p1ToY, p2ToX, p2ToY, p3ToX, p3ToY, p4ToX, p4ToY, p1FromX, p1FromY, p2FromX, p2FromY, p3FromX, p3FromY, p4FromX, p4FromY);\n        return this.sampleGridWithTransform(image, dimensionX, dimensionY, transform);\n    };\n    /*@Override*/\n    DefaultGridSampler.prototype.sampleGridWithTransform = function (image, dimensionX /*int*/, dimensionY /*int*/, transform) {\n        if (dimensionX <= 0 || dimensionY <= 0) {\n            throw new NotFoundException();\n        }\n        var bits = new BitMatrix(dimensionX, dimensionY);\n        var points = new Float32Array(2 * dimensionX);\n        for (var y = 0; y < dimensionY; y++) {\n            var max = points.length;\n            var iValue = y + 0.5;\n            for (var x = 0; x < max; x += 2) {\n                points[x] = (x / 2) + 0.5;\n                points[x + 1] = iValue;\n            }\n            transform.transformPoints(points);\n            // Quick check to see if points transformed to something inside the image\n            // sufficient to check the endpoints\n            GridSampler.checkAndNudgePoints(image, points);\n            try {\n                for (var x = 0; x < max; x += 2) {\n                    if (image.get(Math.floor(points[x]), Math.floor(points[x + 1]))) {\n                        // Black(-ish) pixel\n                        bits.set(x / 2, y);\n                    }\n                }\n            }\n            catch (aioobe /*: ArrayIndexOutOfBoundsException*/) {\n                // This feels wrong, but, sometimes if the finder patterns are misidentified, the resulting\n                // transform gets \"twisted\" such that it maps a straight line of points to a set of points\n                // whose endpoints are in bounds, but others are not. There is probably some mathematical\n                // way to detect this about the transformation that I don't know yet.\n                // This results in an ugly runtime exception despite our clever checks above -- can't have\n                // that. We could check each point's coordinates but that feels duplicative. We settle for\n                // catching and wrapping ArrayIndexOutOfBoundsException.\n                throw new NotFoundException();\n            }\n        }\n        return bits;\n    };\n    return DefaultGridSampler;\n}(GridSampler));\nexport default DefaultGridSampler;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA;AACA;AACA,IAAIC,kBAAkB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACtDnB,SAAS,CAACkB,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAAA,EAAG;IAC1B,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACA;EACAH,kBAAkB,CAACN,SAAS,CAACU,UAAU,GAAG,UAAUC,KAAK,EAAEC,UAAU,CAAC,SAASC,UAAU,CAAC,SAASC,KAAK,CAAC,WAAWC,KAAK,CAAC,WAAWC,KAAK,CAAC,WAAWC,KAAK,CAAC,WAAWC,KAAK,CAAC,WAAWC,KAAK,CAAC,WAAWC,KAAK,CAAC,WAAWC,KAAK,CAAC,WAAWC,OAAO,CAAC,WAAWC,OAAO,CAAC,WAAWC,OAAO,CAAC,WAAWC,OAAO,CAAC,WAAWC,OAAO,CAAC,WAAWC,OAAO,CAAC,WAAWC,OAAO,CAAC,WAAWC,OAAO,CAAC,WAAW;IAC/X,IAAIC,SAAS,GAAG1B,oBAAoB,CAAC2B,4BAA4B,CAACjB,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,CAAC;IACjM,OAAO,IAAI,CAACG,uBAAuB,CAACrB,KAAK,EAAEC,UAAU,EAAEC,UAAU,EAAEiB,SAAS,CAAC;EACjF,CAAC;EACD;EACAxB,kBAAkB,CAACN,SAAS,CAACgC,uBAAuB,GAAG,UAAUrB,KAAK,EAAEC,UAAU,CAAC,SAASC,UAAU,CAAC,SAASiB,SAAS,EAAE;IACvH,IAAIlB,UAAU,IAAI,CAAC,IAAIC,UAAU,IAAI,CAAC,EAAE;MACpC,MAAM,IAAIR,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI4B,IAAI,GAAG,IAAI9B,SAAS,CAACS,UAAU,EAAEC,UAAU,CAAC;IAChD,IAAIqB,MAAM,GAAG,IAAIC,YAAY,CAAC,CAAC,GAAGvB,UAAU,CAAC;IAC7C,KAAK,IAAIwB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,UAAU,EAAEuB,CAAC,EAAE,EAAE;MACjC,IAAIC,GAAG,GAAGH,MAAM,CAACI,MAAM;MACvB,IAAIC,MAAM,GAAGH,CAAC,GAAG,GAAG;MACpB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,IAAI,CAAC,EAAE;QAC7BN,MAAM,CAACM,CAAC,CAAC,GAAIA,CAAC,GAAG,CAAC,GAAI,GAAG;QACzBN,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,GAAGD,MAAM;MAC1B;MACAT,SAAS,CAACW,eAAe,CAACP,MAAM,CAAC;MACjC;MACA;MACAhC,WAAW,CAACwC,mBAAmB,CAAC/B,KAAK,EAAEuB,MAAM,CAAC;MAC9C,IAAI;QACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,EAAEG,CAAC,IAAI,CAAC,EAAE;UAC7B,IAAI7B,KAAK,CAACgC,GAAG,CAACC,IAAI,CAACC,KAAK,CAACX,MAAM,CAACM,CAAC,CAAC,CAAC,EAAEI,IAAI,CAACC,KAAK,CAACX,MAAM,CAACM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YAC7D;YACAP,IAAI,CAACa,GAAG,CAACN,CAAC,GAAG,CAAC,EAAEJ,CAAC,CAAC;UACtB;QACJ;MACJ,CAAC,CACD,OAAOW,MAAM,CAAC,sCAAsC;QAChD;QACA;QACA;QACA;QACA;QACA;QACA;QACA,MAAM,IAAI1C,iBAAiB,CAAC,CAAC;MACjC;IACJ;IACA,OAAO4B,IAAI;EACf,CAAC;EACD,OAAO3B,kBAAkB;AAC7B,CAAC,CAACJ,WAAW,CAAE;AACf,eAAeI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}