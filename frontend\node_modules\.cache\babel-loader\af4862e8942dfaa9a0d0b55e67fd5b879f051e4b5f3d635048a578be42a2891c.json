{"ast": null, "code": "import { ECIEncoderSet } from './ECIEncoderSet';\nimport Integer from '../util/Integer';\nimport StringBuilder from '../util/StringBuilder';\nvar COST_PER_ECI = 3; // approximated (latch + 2 codewords)\nvar MinimalECIInput = /** @class */function () {\n  /**\n   * Constructs a minimal input\n   *\n   * @param stringToEncode the character string to encode\n   * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n   *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n   *   charset to encode any character in the input that can be encoded by it if the charset is among the\n   *   supported charsets.\n   * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not GS1\n   *   input.\n   */\n  function MinimalECIInput(stringToEncode, priorityCharset, fnc1) {\n    this.fnc1 = fnc1;\n    var encoderSet = new ECIEncoderSet(stringToEncode, priorityCharset, fnc1);\n    if (encoderSet.length() === 1) {\n      // optimization for the case when all can be encoded without ECI in ISO-8859-1\n      for (var i = 0; i < this.bytes.length; i++) {\n        var c = stringToEncode.charAt(i).charCodeAt(0);\n        this.bytes[i] = c === fnc1 ? 1000 : c;\n      }\n    } else {\n      this.bytes = this.encodeMinimally(stringToEncode, encoderSet, fnc1);\n    }\n  }\n  MinimalECIInput.prototype.getFNC1Character = function () {\n    return this.fnc1;\n  };\n  /**\n   * Returns the length of this input.  The length is the number\n   * of {@code byte}s, FNC1 characters or ECIs in the sequence.\n   *\n   * @return  the number of {@code char}s in this sequence\n   */\n  MinimalECIInput.prototype.length = function () {\n    return this.bytes.length;\n  };\n  MinimalECIInput.prototype.haveNCharacters = function (index, n) {\n    if (index + n - 1 >= this.bytes.length) {\n      return false;\n    }\n    for (var i = 0; i < n; i++) {\n      if (this.isECI(index + i)) {\n        return false;\n      }\n    }\n    return true;\n  };\n  /**\n   * Returns the {@code byte} value at the specified index.  An index ranges from zero\n   * to {@code length() - 1}.  The first {@code byte} value of the sequence is at\n   * index zero, the next at index one, and so on, as for array\n   * indexing.\n   *\n   * @param   index the index of the {@code byte} value to be returned\n   *\n   * @return  the specified {@code byte} value as character or the FNC1 character\n   *\n   * @throws  IndexOutOfBoundsException\n   *          if the {@code index} argument is negative or not less than\n   *          {@code length()}\n   * @throws  IllegalArgumentException\n   *          if the value at the {@code index} argument is an ECI (@see #isECI)\n   */\n  MinimalECIInput.prototype.charAt = function (index) {\n    if (index < 0 || index >= this.length()) {\n      throw new Error('' + index);\n    }\n    if (this.isECI(index)) {\n      throw new Error('value at ' + index + ' is not a character but an ECI');\n    }\n    return this.isFNC1(index) ? this.fnc1 : this.bytes[index];\n  };\n  /**\n   * Returns a {@code CharSequence} that is a subsequence of this sequence.\n   * The subsequence starts with the {@code char} value at the specified index and\n   * ends with the {@code char} value at index {@code end - 1}.  The length\n   * (in {@code char}s) of the\n   * returned sequence is {@code end - start}, so if {@code start == end}\n   * then an empty sequence is returned.\n   *\n   * @param   start   the start index, inclusive\n   * @param   end     the end index, exclusive\n   *\n   * @return  the specified subsequence\n   *\n   * @throws  IndexOutOfBoundsException\n   *          if {@code start} or {@code end} are negative,\n   *          if {@code end} is greater than {@code length()},\n   *          or if {@code start} is greater than {@code end}\n   * @throws  IllegalArgumentException\n   *          if a value in the range {@code start}-{@code end} is an ECI (@see #isECI)\n   */\n  MinimalECIInput.prototype.subSequence = function (start, end) {\n    if (start < 0 || start > end || end > this.length()) {\n      throw new Error('' + start);\n    }\n    var result = new StringBuilder();\n    for (var i = start; i < end; i++) {\n      if (this.isECI(i)) {\n        throw new Error('value at ' + i + ' is not a character but an ECI');\n      }\n      result.append(this.charAt(i));\n    }\n    return result.toString();\n  };\n  /**\n   * Determines if a value is an ECI\n   *\n   * @param   index the index of the value\n   *\n   * @return  true if the value at position {@code index} is an ECI\n   *\n   * @throws  IndexOutOfBoundsException\n   *          if the {@code index} argument is negative or not less than\n   *          {@code length()}\n   */\n  MinimalECIInput.prototype.isECI = function (index) {\n    if (index < 0 || index >= this.length()) {\n      throw new Error('' + index);\n    }\n    return this.bytes[index] > 255 && this.bytes[index] <= 999;\n  };\n  /**\n   * Determines if a value is the FNC1 character\n   *\n   * @param   index the index of the value\n   *\n   * @return  true if the value at position {@code index} is the FNC1 character\n   *\n   * @throws  IndexOutOfBoundsException\n   *          if the {@code index} argument is negative or not less than\n   *          {@code length()}\n   */\n  MinimalECIInput.prototype.isFNC1 = function (index) {\n    if (index < 0 || index >= this.length()) {\n      throw new Error('' + index);\n    }\n    return this.bytes[index] === 1000;\n  };\n  /**\n   * Returns the {@code int} ECI value at the specified index.  An index ranges from zero\n   * to {@code length() - 1}.  The first {@code byte} value of the sequence is at\n   * index zero, the next at index one, and so on, as for array\n   * indexing.\n   *\n   * @param   index the index of the {@code int} value to be returned\n   *\n   * @return  the specified {@code int} ECI value.\n   *          The ECI specified the encoding of all bytes with a higher index until the\n   *          next ECI or until the end of the input if no other ECI follows.\n   *\n   * @throws  IndexOutOfBoundsException\n   *          if the {@code index} argument is negative or not less than\n   *          {@code length()}\n   * @throws  IllegalArgumentException\n   *          if the value at the {@code index} argument is not an ECI (@see #isECI)\n   */\n  MinimalECIInput.prototype.getECIValue = function (index) {\n    if (index < 0 || index >= this.length()) {\n      throw new Error('' + index);\n    }\n    if (!this.isECI(index)) {\n      throw new Error('value at ' + index + ' is not an ECI but a character');\n    }\n    return this.bytes[index] - 256;\n  };\n  MinimalECIInput.prototype.addEdge = function (edges, to, edge) {\n    if (edges[to][edge.encoderIndex] == null || edges[to][edge.encoderIndex].cachedTotalSize > edge.cachedTotalSize) {\n      edges[to][edge.encoderIndex] = edge;\n    }\n  };\n  MinimalECIInput.prototype.addEdges = function (stringToEncode, encoderSet, edges, from, previous, fnc1) {\n    var ch = stringToEncode.charAt(from).charCodeAt(0);\n    var start = 0;\n    var end = encoderSet.length();\n    if (encoderSet.getPriorityEncoderIndex() >= 0 && (ch === fnc1 || encoderSet.canEncode(ch, encoderSet.getPriorityEncoderIndex()))) {\n      start = encoderSet.getPriorityEncoderIndex();\n      end = start + 1;\n    }\n    for (var i = start; i < end; i++) {\n      if (ch === fnc1 || encoderSet.canEncode(ch, i)) {\n        this.addEdge(edges, from + 1, new InputEdge(ch, encoderSet, i, previous, fnc1));\n      }\n    }\n  };\n  MinimalECIInput.prototype.encodeMinimally = function (stringToEncode, encoderSet, fnc1) {\n    var inputLength = stringToEncode.length;\n    // Array that represents vertices. There is a vertex for every character and encoding.\n    var edges = new InputEdge[inputLength + 1][encoderSet.length()]();\n    this.addEdges(stringToEncode, encoderSet, edges, 0, null, fnc1);\n    for (var i = 1; i <= inputLength; i++) {\n      for (var j = 0; j < encoderSet.length(); j++) {\n        if (edges[i][j] != null && i < inputLength) {\n          this.addEdges(stringToEncode, encoderSet, edges, i, edges[i][j], fnc1);\n        }\n      }\n      // optimize memory by removing edges that have been passed.\n      for (var j = 0; j < encoderSet.length(); j++) {\n        edges[i - 1][j] = null;\n      }\n    }\n    var minimalJ = -1;\n    var minimalSize = Integer.MAX_VALUE;\n    for (var j = 0; j < encoderSet.length(); j++) {\n      if (edges[inputLength][j] != null) {\n        var edge = edges[inputLength][j];\n        if (edge.cachedTotalSize < minimalSize) {\n          minimalSize = edge.cachedTotalSize;\n          minimalJ = j;\n        }\n      }\n    }\n    if (minimalJ < 0) {\n      throw new Error('Failed to encode \"' + stringToEncode + '\"');\n    }\n    var intsAL = [];\n    var current = edges[inputLength][minimalJ];\n    while (current != null) {\n      if (current.isFNC1()) {\n        intsAL.unshift(1000);\n      } else {\n        var bytes = encoderSet.encode(current.c, current.encoderIndex);\n        for (var i = bytes.length - 1; i >= 0; i--) {\n          intsAL.unshift(bytes[i] & 0xff);\n        }\n      }\n      var previousEncoderIndex = current.previous === null ? 0 : current.previous.encoderIndex;\n      if (previousEncoderIndex !== current.encoderIndex) {\n        intsAL.unshift(256 + encoderSet.getECIValue(current.encoderIndex));\n      }\n      current = current.previous;\n    }\n    var ints = [];\n    for (var i = 0; i < ints.length; i++) {\n      ints[i] = intsAL[i];\n    }\n    return ints;\n  };\n  return MinimalECIInput;\n}();\nexport { MinimalECIInput };\nvar InputEdge = /** @class */function () {\n  function InputEdge(c, encoderSet, encoderIndex, previous, fnc1) {\n    this.c = c;\n    this.encoderSet = encoderSet;\n    this.encoderIndex = encoderIndex;\n    this.previous = previous;\n    this.fnc1 = fnc1;\n    this.c = c === fnc1 ? 1000 : c;\n    var size = this.isFNC1() ? 1 : encoderSet.encode(c, encoderIndex).length;\n    var previousEncoderIndex = previous === null ? 0 : previous.encoderIndex;\n    if (previousEncoderIndex !== encoderIndex) {\n      size += COST_PER_ECI;\n    }\n    if (previous != null) {\n      size += previous.cachedTotalSize;\n    }\n    this.cachedTotalSize = size;\n  }\n  InputEdge.prototype.isFNC1 = function () {\n    return this.c === 1000;\n  };\n  return InputEdge;\n}();", "map": {"version": 3, "names": ["ECIEncoderSet", "Integer", "StringBuilder", "COST_PER_ECI", "MinimalECIInput", "stringToEncode", "priorityCharset", "fnc1", "encoderSet", "length", "i", "bytes", "c", "char<PERSON>t", "charCodeAt", "encodeMinimally", "prototype", "getFNC1Character", "haveNCharacters", "index", "n", "isECI", "Error", "isFNC1", "subSequence", "start", "end", "result", "append", "toString", "getECIValue", "addEdge", "edges", "to", "edge", "encoderIndex", "cachedTotalSize", "addEdges", "from", "previous", "ch", "getPriorityEncoderIndex", "canEncode", "InputEdge", "inputLength", "j", "minimalJ", "minimalSize", "MAX_VALUE", "intsAL", "current", "unshift", "encode", "previousEncoderIndex", "ints", "size"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/MinimalECIInput.js"], "sourcesContent": ["import { ECIEncoderSet } from './ECIEncoderSet';\nimport Integer from '../util/Integer';\nimport StringBuilder from '../util/StringBuilder';\nvar COST_PER_ECI = 3; // approximated (latch + 2 codewords)\nvar MinimalECIInput = /** @class */ (function () {\n    /**\n     * Constructs a minimal input\n     *\n     * @param stringToEncode the character string to encode\n     * @param priorityCharset The preferred {@link Charset}. When the value of the argument is null, the algorithm\n     *   chooses charsets that leads to a minimal representation. Otherwise the algorithm will use the priority\n     *   charset to encode any character in the input that can be encoded by it if the charset is among the\n     *   supported charsets.\n     * @param fnc1 denotes the character in the input that represents the FNC1 character or -1 if this is not GS1\n     *   input.\n     */\n    function MinimalECIInput(stringToEncode, priorityCharset, fnc1) {\n        this.fnc1 = fnc1;\n        var encoderSet = new ECIEncoderSet(stringToEncode, priorityCharset, fnc1);\n        if (encoderSet.length() === 1) {\n            // optimization for the case when all can be encoded without ECI in ISO-8859-1\n            for (var i = 0; i < this.bytes.length; i++) {\n                var c = stringToEncode.charAt(i).charCodeAt(0);\n                this.bytes[i] = c === fnc1 ? 1000 : c;\n            }\n        }\n        else {\n            this.bytes = this.encodeMinimally(stringToEncode, encoderSet, fnc1);\n        }\n    }\n    MinimalECIInput.prototype.getFNC1Character = function () {\n        return this.fnc1;\n    };\n    /**\n     * Returns the length of this input.  The length is the number\n     * of {@code byte}s, FNC1 characters or ECIs in the sequence.\n     *\n     * @return  the number of {@code char}s in this sequence\n     */\n    MinimalECIInput.prototype.length = function () {\n        return this.bytes.length;\n    };\n    MinimalECIInput.prototype.haveNCharacters = function (index, n) {\n        if (index + n - 1 >= this.bytes.length) {\n            return false;\n        }\n        for (var i = 0; i < n; i++) {\n            if (this.isECI(index + i)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    /**\n     * Returns the {@code byte} value at the specified index.  An index ranges from zero\n     * to {@code length() - 1}.  The first {@code byte} value of the sequence is at\n     * index zero, the next at index one, and so on, as for array\n     * indexing.\n     *\n     * @param   index the index of the {@code byte} value to be returned\n     *\n     * @return  the specified {@code byte} value as character or the FNC1 character\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if the {@code index} argument is negative or not less than\n     *          {@code length()}\n     * @throws  IllegalArgumentException\n     *          if the value at the {@code index} argument is an ECI (@see #isECI)\n     */\n    MinimalECIInput.prototype.charAt = function (index) {\n        if (index < 0 || index >= this.length()) {\n            throw new Error('' + index);\n        }\n        if (this.isECI(index)) {\n            throw new Error('value at ' + index + ' is not a character but an ECI');\n        }\n        return this.isFNC1(index) ? this.fnc1 : this.bytes[index];\n    };\n    /**\n     * Returns a {@code CharSequence} that is a subsequence of this sequence.\n     * The subsequence starts with the {@code char} value at the specified index and\n     * ends with the {@code char} value at index {@code end - 1}.  The length\n     * (in {@code char}s) of the\n     * returned sequence is {@code end - start}, so if {@code start == end}\n     * then an empty sequence is returned.\n     *\n     * @param   start   the start index, inclusive\n     * @param   end     the end index, exclusive\n     *\n     * @return  the specified subsequence\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if {@code start} or {@code end} are negative,\n     *          if {@code end} is greater than {@code length()},\n     *          or if {@code start} is greater than {@code end}\n     * @throws  IllegalArgumentException\n     *          if a value in the range {@code start}-{@code end} is an ECI (@see #isECI)\n     */\n    MinimalECIInput.prototype.subSequence = function (start, end) {\n        if (start < 0 || start > end || end > this.length()) {\n            throw new Error('' + start);\n        }\n        var result = new StringBuilder();\n        for (var i = start; i < end; i++) {\n            if (this.isECI(i)) {\n                throw new Error('value at ' + i + ' is not a character but an ECI');\n            }\n            result.append(this.charAt(i));\n        }\n        return result.toString();\n    };\n    /**\n     * Determines if a value is an ECI\n     *\n     * @param   index the index of the value\n     *\n     * @return  true if the value at position {@code index} is an ECI\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if the {@code index} argument is negative or not less than\n     *          {@code length()}\n     */\n    MinimalECIInput.prototype.isECI = function (index) {\n        if (index < 0 || index >= this.length()) {\n            throw new Error('' + index);\n        }\n        return this.bytes[index] > 255 && this.bytes[index] <= 999;\n    };\n    /**\n     * Determines if a value is the FNC1 character\n     *\n     * @param   index the index of the value\n     *\n     * @return  true if the value at position {@code index} is the FNC1 character\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if the {@code index} argument is negative or not less than\n     *          {@code length()}\n     */\n    MinimalECIInput.prototype.isFNC1 = function (index) {\n        if (index < 0 || index >= this.length()) {\n            throw new Error('' + index);\n        }\n        return this.bytes[index] === 1000;\n    };\n    /**\n     * Returns the {@code int} ECI value at the specified index.  An index ranges from zero\n     * to {@code length() - 1}.  The first {@code byte} value of the sequence is at\n     * index zero, the next at index one, and so on, as for array\n     * indexing.\n     *\n     * @param   index the index of the {@code int} value to be returned\n     *\n     * @return  the specified {@code int} ECI value.\n     *          The ECI specified the encoding of all bytes with a higher index until the\n     *          next ECI or until the end of the input if no other ECI follows.\n     *\n     * @throws  IndexOutOfBoundsException\n     *          if the {@code index} argument is negative or not less than\n     *          {@code length()}\n     * @throws  IllegalArgumentException\n     *          if the value at the {@code index} argument is not an ECI (@see #isECI)\n     */\n    MinimalECIInput.prototype.getECIValue = function (index) {\n        if (index < 0 || index >= this.length()) {\n            throw new Error('' + index);\n        }\n        if (!this.isECI(index)) {\n            throw new Error('value at ' + index + ' is not an ECI but a character');\n        }\n        return this.bytes[index] - 256;\n    };\n    MinimalECIInput.prototype.addEdge = function (edges, to, edge) {\n        if (edges[to][edge.encoderIndex] == null ||\n            edges[to][edge.encoderIndex].cachedTotalSize > edge.cachedTotalSize) {\n            edges[to][edge.encoderIndex] = edge;\n        }\n    };\n    MinimalECIInput.prototype.addEdges = function (stringToEncode, encoderSet, edges, from, previous, fnc1) {\n        var ch = stringToEncode.charAt(from).charCodeAt(0);\n        var start = 0;\n        var end = encoderSet.length();\n        if (encoderSet.getPriorityEncoderIndex() >= 0 &&\n            (ch === fnc1 ||\n                encoderSet.canEncode(ch, encoderSet.getPriorityEncoderIndex()))) {\n            start = encoderSet.getPriorityEncoderIndex();\n            end = start + 1;\n        }\n        for (var i = start; i < end; i++) {\n            if (ch === fnc1 || encoderSet.canEncode(ch, i)) {\n                this.addEdge(edges, from + 1, new InputEdge(ch, encoderSet, i, previous, fnc1));\n            }\n        }\n    };\n    MinimalECIInput.prototype.encodeMinimally = function (stringToEncode, encoderSet, fnc1) {\n        var inputLength = stringToEncode.length;\n        // Array that represents vertices. There is a vertex for every character and encoding.\n        var edges = new InputEdge[inputLength + 1][encoderSet.length()]();\n        this.addEdges(stringToEncode, encoderSet, edges, 0, null, fnc1);\n        for (var i = 1; i <= inputLength; i++) {\n            for (var j = 0; j < encoderSet.length(); j++) {\n                if (edges[i][j] != null && i < inputLength) {\n                    this.addEdges(stringToEncode, encoderSet, edges, i, edges[i][j], fnc1);\n                }\n            }\n            // optimize memory by removing edges that have been passed.\n            for (var j = 0; j < encoderSet.length(); j++) {\n                edges[i - 1][j] = null;\n            }\n        }\n        var minimalJ = -1;\n        var minimalSize = Integer.MAX_VALUE;\n        for (var j = 0; j < encoderSet.length(); j++) {\n            if (edges[inputLength][j] != null) {\n                var edge = edges[inputLength][j];\n                if (edge.cachedTotalSize < minimalSize) {\n                    minimalSize = edge.cachedTotalSize;\n                    minimalJ = j;\n                }\n            }\n        }\n        if (minimalJ < 0) {\n            throw new Error('Failed to encode \"' + stringToEncode + '\"');\n        }\n        var intsAL = [];\n        var current = edges[inputLength][minimalJ];\n        while (current != null) {\n            if (current.isFNC1()) {\n                intsAL.unshift(1000);\n            }\n            else {\n                var bytes = encoderSet.encode(current.c, current.encoderIndex);\n                for (var i = bytes.length - 1; i >= 0; i--) {\n                    intsAL.unshift(bytes[i] & 0xff);\n                }\n            }\n            var previousEncoderIndex = current.previous === null ? 0 : current.previous.encoderIndex;\n            if (previousEncoderIndex !== current.encoderIndex) {\n                intsAL.unshift(256 + encoderSet.getECIValue(current.encoderIndex));\n            }\n            current = current.previous;\n        }\n        var ints = [];\n        for (var i = 0; i < ints.length; i++) {\n            ints[i] = intsAL[i];\n        }\n        return ints;\n    };\n    return MinimalECIInput;\n}());\nexport { MinimalECIInput };\nvar InputEdge = /** @class */ (function () {\n    function InputEdge(c, encoderSet, encoderIndex, previous, fnc1) {\n        this.c = c;\n        this.encoderSet = encoderSet;\n        this.encoderIndex = encoderIndex;\n        this.previous = previous;\n        this.fnc1 = fnc1;\n        this.c = c === fnc1 ? 1000 : c;\n        var size = this.isFNC1() ? 1 : encoderSet.encode(c, encoderIndex).length;\n        var previousEncoderIndex = previous === null ? 0 : previous.encoderIndex;\n        if (previousEncoderIndex !== encoderIndex) {\n            size += COST_PER_ECI;\n        }\n        if (previous != null) {\n            size += previous.cachedTotalSize;\n        }\n        this.cachedTotalSize = size;\n    }\n    InputEdge.prototype.isFNC1 = function () {\n        return this.c === 1000;\n    };\n    return InputEdge;\n}());\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,aAAa,MAAM,uBAAuB;AACjD,IAAIC,YAAY,GAAG,CAAC,CAAC,CAAC;AACtB,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,eAAeA,CAACC,cAAc,EAAEC,eAAe,EAAEC,IAAI,EAAE;IAC5D,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAIC,UAAU,GAAG,IAAIR,aAAa,CAACK,cAAc,EAAEC,eAAe,EAAEC,IAAI,CAAC;IACzE,IAAIC,UAAU,CAACC,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;MAC3B;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,KAAK,CAACF,MAAM,EAAEC,CAAC,EAAE,EAAE;QACxC,IAAIE,CAAC,GAAGP,cAAc,CAACQ,MAAM,CAACH,CAAC,CAAC,CAACI,UAAU,CAAC,CAAC,CAAC;QAC9C,IAAI,CAACH,KAAK,CAACD,CAAC,CAAC,GAAGE,CAAC,KAAKL,IAAI,GAAG,IAAI,GAAGK,CAAC;MACzC;IACJ,CAAC,MACI;MACD,IAAI,CAACD,KAAK,GAAG,IAAI,CAACI,eAAe,CAACV,cAAc,EAAEG,UAAU,EAAED,IAAI,CAAC;IACvE;EACJ;EACAH,eAAe,CAACY,SAAS,CAACC,gBAAgB,GAAG,YAAY;IACrD,OAAO,IAAI,CAACV,IAAI;EACpB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIH,eAAe,CAACY,SAAS,CAACP,MAAM,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACE,KAAK,CAACF,MAAM;EAC5B,CAAC;EACDL,eAAe,CAACY,SAAS,CAACE,eAAe,GAAG,UAAUC,KAAK,EAAEC,CAAC,EAAE;IAC5D,IAAID,KAAK,GAAGC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACT,KAAK,CAACF,MAAM,EAAE;MACpC,OAAO,KAAK;IAChB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,EAAEV,CAAC,EAAE,EAAE;MACxB,IAAI,IAAI,CAACW,KAAK,CAACF,KAAK,GAAGT,CAAC,CAAC,EAAE;QACvB,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIN,eAAe,CAACY,SAAS,CAACH,MAAM,GAAG,UAAUM,KAAK,EAAE;IAChD,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACV,MAAM,CAAC,CAAC,EAAE;MACrC,MAAM,IAAIa,KAAK,CAAC,EAAE,GAAGH,KAAK,CAAC;IAC/B;IACA,IAAI,IAAI,CAACE,KAAK,CAACF,KAAK,CAAC,EAAE;MACnB,MAAM,IAAIG,KAAK,CAAC,WAAW,GAAGH,KAAK,GAAG,gCAAgC,CAAC;IAC3E;IACA,OAAO,IAAI,CAACI,MAAM,CAACJ,KAAK,CAAC,GAAG,IAAI,CAACZ,IAAI,GAAG,IAAI,CAACI,KAAK,CAACQ,KAAK,CAAC;EAC7D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIf,eAAe,CAACY,SAAS,CAACQ,WAAW,GAAG,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAC1D,IAAID,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGC,GAAG,IAAIA,GAAG,GAAG,IAAI,CAACjB,MAAM,CAAC,CAAC,EAAE;MACjD,MAAM,IAAIa,KAAK,CAAC,EAAE,GAAGG,KAAK,CAAC;IAC/B;IACA,IAAIE,MAAM,GAAG,IAAIzB,aAAa,CAAC,CAAC;IAChC,KAAK,IAAIQ,CAAC,GAAGe,KAAK,EAAEf,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;MAC9B,IAAI,IAAI,CAACW,KAAK,CAACX,CAAC,CAAC,EAAE;QACf,MAAM,IAAIY,KAAK,CAAC,WAAW,GAAGZ,CAAC,GAAG,gCAAgC,CAAC;MACvE;MACAiB,MAAM,CAACC,MAAM,CAAC,IAAI,CAACf,MAAM,CAACH,CAAC,CAAC,CAAC;IACjC;IACA,OAAOiB,MAAM,CAACE,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIzB,eAAe,CAACY,SAAS,CAACK,KAAK,GAAG,UAAUF,KAAK,EAAE;IAC/C,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACV,MAAM,CAAC,CAAC,EAAE;MACrC,MAAM,IAAIa,KAAK,CAAC,EAAE,GAAGH,KAAK,CAAC;IAC/B;IACA,OAAO,IAAI,CAACR,KAAK,CAACQ,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI,CAACR,KAAK,CAACQ,KAAK,CAAC,IAAI,GAAG;EAC9D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIf,eAAe,CAACY,SAAS,CAACO,MAAM,GAAG,UAAUJ,KAAK,EAAE;IAChD,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACV,MAAM,CAAC,CAAC,EAAE;MACrC,MAAM,IAAIa,KAAK,CAAC,EAAE,GAAGH,KAAK,CAAC;IAC/B;IACA,OAAO,IAAI,CAACR,KAAK,CAACQ,KAAK,CAAC,KAAK,IAAI;EACrC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIf,eAAe,CAACY,SAAS,CAACc,WAAW,GAAG,UAAUX,KAAK,EAAE;IACrD,IAAIA,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAI,IAAI,CAACV,MAAM,CAAC,CAAC,EAAE;MACrC,MAAM,IAAIa,KAAK,CAAC,EAAE,GAAGH,KAAK,CAAC;IAC/B;IACA,IAAI,CAAC,IAAI,CAACE,KAAK,CAACF,KAAK,CAAC,EAAE;MACpB,MAAM,IAAIG,KAAK,CAAC,WAAW,GAAGH,KAAK,GAAG,gCAAgC,CAAC;IAC3E;IACA,OAAO,IAAI,CAACR,KAAK,CAACQ,KAAK,CAAC,GAAG,GAAG;EAClC,CAAC;EACDf,eAAe,CAACY,SAAS,CAACe,OAAO,GAAG,UAAUC,KAAK,EAAEC,EAAE,EAAEC,IAAI,EAAE;IAC3D,IAAIF,KAAK,CAACC,EAAE,CAAC,CAACC,IAAI,CAACC,YAAY,CAAC,IAAI,IAAI,IACpCH,KAAK,CAACC,EAAE,CAAC,CAACC,IAAI,CAACC,YAAY,CAAC,CAACC,eAAe,GAAGF,IAAI,CAACE,eAAe,EAAE;MACrEJ,KAAK,CAACC,EAAE,CAAC,CAACC,IAAI,CAACC,YAAY,CAAC,GAAGD,IAAI;IACvC;EACJ,CAAC;EACD9B,eAAe,CAACY,SAAS,CAACqB,QAAQ,GAAG,UAAUhC,cAAc,EAAEG,UAAU,EAAEwB,KAAK,EAAEM,IAAI,EAAEC,QAAQ,EAAEhC,IAAI,EAAE;IACpG,IAAIiC,EAAE,GAAGnC,cAAc,CAACQ,MAAM,CAACyB,IAAI,CAAC,CAACxB,UAAU,CAAC,CAAC,CAAC;IAClD,IAAIW,KAAK,GAAG,CAAC;IACb,IAAIC,GAAG,GAAGlB,UAAU,CAACC,MAAM,CAAC,CAAC;IAC7B,IAAID,UAAU,CAACiC,uBAAuB,CAAC,CAAC,IAAI,CAAC,KACxCD,EAAE,KAAKjC,IAAI,IACRC,UAAU,CAACkC,SAAS,CAACF,EAAE,EAAEhC,UAAU,CAACiC,uBAAuB,CAAC,CAAC,CAAC,CAAC,EAAE;MACrEhB,KAAK,GAAGjB,UAAU,CAACiC,uBAAuB,CAAC,CAAC;MAC5Cf,GAAG,GAAGD,KAAK,GAAG,CAAC;IACnB;IACA,KAAK,IAAIf,CAAC,GAAGe,KAAK,EAAEf,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;MAC9B,IAAI8B,EAAE,KAAKjC,IAAI,IAAIC,UAAU,CAACkC,SAAS,CAACF,EAAE,EAAE9B,CAAC,CAAC,EAAE;QAC5C,IAAI,CAACqB,OAAO,CAACC,KAAK,EAAEM,IAAI,GAAG,CAAC,EAAE,IAAIK,SAAS,CAACH,EAAE,EAAEhC,UAAU,EAAEE,CAAC,EAAE6B,QAAQ,EAAEhC,IAAI,CAAC,CAAC;MACnF;IACJ;EACJ,CAAC;EACDH,eAAe,CAACY,SAAS,CAACD,eAAe,GAAG,UAAUV,cAAc,EAAEG,UAAU,EAAED,IAAI,EAAE;IACpF,IAAIqC,WAAW,GAAGvC,cAAc,CAACI,MAAM;IACvC;IACA,IAAIuB,KAAK,GAAG,IAAIW,SAAS,CAACC,WAAW,GAAG,CAAC,CAAC,CAACpC,UAAU,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,IAAI,CAAC4B,QAAQ,CAAChC,cAAc,EAAEG,UAAU,EAAEwB,KAAK,EAAE,CAAC,EAAE,IAAI,EAAEzB,IAAI,CAAC;IAC/D,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIkC,WAAW,EAAElC,CAAC,EAAE,EAAE;MACnC,KAAK,IAAImC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,UAAU,CAACC,MAAM,CAAC,CAAC,EAAEoC,CAAC,EAAE,EAAE;QAC1C,IAAIb,KAAK,CAACtB,CAAC,CAAC,CAACmC,CAAC,CAAC,IAAI,IAAI,IAAInC,CAAC,GAAGkC,WAAW,EAAE;UACxC,IAAI,CAACP,QAAQ,CAAChC,cAAc,EAAEG,UAAU,EAAEwB,KAAK,EAAEtB,CAAC,EAAEsB,KAAK,CAACtB,CAAC,CAAC,CAACmC,CAAC,CAAC,EAAEtC,IAAI,CAAC;QAC1E;MACJ;MACA;MACA,KAAK,IAAIsC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,UAAU,CAACC,MAAM,CAAC,CAAC,EAAEoC,CAAC,EAAE,EAAE;QAC1Cb,KAAK,CAACtB,CAAC,GAAG,CAAC,CAAC,CAACmC,CAAC,CAAC,GAAG,IAAI;MAC1B;IACJ;IACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAIC,WAAW,GAAG9C,OAAO,CAAC+C,SAAS;IACnC,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrC,UAAU,CAACC,MAAM,CAAC,CAAC,EAAEoC,CAAC,EAAE,EAAE;MAC1C,IAAIb,KAAK,CAACY,WAAW,CAAC,CAACC,CAAC,CAAC,IAAI,IAAI,EAAE;QAC/B,IAAIX,IAAI,GAAGF,KAAK,CAACY,WAAW,CAAC,CAACC,CAAC,CAAC;QAChC,IAAIX,IAAI,CAACE,eAAe,GAAGW,WAAW,EAAE;UACpCA,WAAW,GAAGb,IAAI,CAACE,eAAe;UAClCU,QAAQ,GAAGD,CAAC;QAChB;MACJ;IACJ;IACA,IAAIC,QAAQ,GAAG,CAAC,EAAE;MACd,MAAM,IAAIxB,KAAK,CAAC,oBAAoB,GAAGjB,cAAc,GAAG,GAAG,CAAC;IAChE;IACA,IAAI4C,MAAM,GAAG,EAAE;IACf,IAAIC,OAAO,GAAGlB,KAAK,CAACY,WAAW,CAAC,CAACE,QAAQ,CAAC;IAC1C,OAAOI,OAAO,IAAI,IAAI,EAAE;MACpB,IAAIA,OAAO,CAAC3B,MAAM,CAAC,CAAC,EAAE;QAClB0B,MAAM,CAACE,OAAO,CAAC,IAAI,CAAC;MACxB,CAAC,MACI;QACD,IAAIxC,KAAK,GAAGH,UAAU,CAAC4C,MAAM,CAACF,OAAO,CAACtC,CAAC,EAAEsC,OAAO,CAACf,YAAY,CAAC;QAC9D,KAAK,IAAIzB,CAAC,GAAGC,KAAK,CAACF,MAAM,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACxCuC,MAAM,CAACE,OAAO,CAACxC,KAAK,CAACD,CAAC,CAAC,GAAG,IAAI,CAAC;QACnC;MACJ;MACA,IAAI2C,oBAAoB,GAAGH,OAAO,CAACX,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAGW,OAAO,CAACX,QAAQ,CAACJ,YAAY;MACxF,IAAIkB,oBAAoB,KAAKH,OAAO,CAACf,YAAY,EAAE;QAC/Cc,MAAM,CAACE,OAAO,CAAC,GAAG,GAAG3C,UAAU,CAACsB,WAAW,CAACoB,OAAO,CAACf,YAAY,CAAC,CAAC;MACtE;MACAe,OAAO,GAAGA,OAAO,CAACX,QAAQ;IAC9B;IACA,IAAIe,IAAI,GAAG,EAAE;IACb,KAAK,IAAI5C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,IAAI,CAAC7C,MAAM,EAAEC,CAAC,EAAE,EAAE;MAClC4C,IAAI,CAAC5C,CAAC,CAAC,GAAGuC,MAAM,CAACvC,CAAC,CAAC;IACvB;IACA,OAAO4C,IAAI;EACf,CAAC;EACD,OAAOlD,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,SAASA,eAAe;AACxB,IAAIuC,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAAC/B,CAAC,EAAEJ,UAAU,EAAE2B,YAAY,EAAEI,QAAQ,EAAEhC,IAAI,EAAE;IAC5D,IAAI,CAACK,CAAC,GAAGA,CAAC;IACV,IAAI,CAACJ,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC2B,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACI,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAChC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACK,CAAC,GAAGA,CAAC,KAAKL,IAAI,GAAG,IAAI,GAAGK,CAAC;IAC9B,IAAI2C,IAAI,GAAG,IAAI,CAAChC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAGf,UAAU,CAAC4C,MAAM,CAACxC,CAAC,EAAEuB,YAAY,CAAC,CAAC1B,MAAM;IACxE,IAAI4C,oBAAoB,GAAGd,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAGA,QAAQ,CAACJ,YAAY;IACxE,IAAIkB,oBAAoB,KAAKlB,YAAY,EAAE;MACvCoB,IAAI,IAAIpD,YAAY;IACxB;IACA,IAAIoC,QAAQ,IAAI,IAAI,EAAE;MAClBgB,IAAI,IAAIhB,QAAQ,CAACH,eAAe;IACpC;IACA,IAAI,CAACA,eAAe,GAAGmB,IAAI;EAC/B;EACAZ,SAAS,CAAC3B,SAAS,CAACO,MAAM,GAAG,YAAY;IACrC,OAAO,IAAI,CAACX,CAAC,KAAK,IAAI;EAC1B,CAAC;EACD,OAAO+B,SAAS;AACpB,CAAC,CAAC,CAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}