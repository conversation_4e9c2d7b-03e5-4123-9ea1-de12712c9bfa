{"ast": null, "code": "import * as React from 'react';\nimport clsx from 'clsx';\nimport { TransitionGroup } from 'react-transition-group';\nimport Fade from '@mui/material/Fade';\nimport { styled, useTheme, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getPickersFadeTransitionGroupUtilityClass } from \"./pickersFadeTransitionGroupClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getPickersFadeTransitionGroupUtilityClass, classes);\n};\nconst PickersFadeTransitionGroupRoot = styled(TransitionGroup, {\n  name: 'MuiPickersFadeTransitionGroup',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})({\n  display: 'block',\n  position: 'relative'\n});\n\n/**\n * @ignore - do not document.\n */\nexport function PickersFadeTransitionGroup(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersFadeTransitionGroup'\n  });\n  const {\n    children,\n    className,\n    reduceAnimations,\n    transKey\n  } = props;\n  const classes = useUtilityClasses(props);\n  const theme = useTheme();\n  if (reduceAnimations) {\n    return children;\n  }\n  return /*#__PURE__*/_jsx(PickersFadeTransitionGroupRoot, {\n    className: clsx(classes.root, className),\n    children: /*#__PURE__*/_jsx(Fade, {\n      appear: false,\n      mountOnEnter: true,\n      unmountOnExit: true,\n      timeout: {\n        appear: theme.transitions.duration.enteringScreen,\n        enter: theme.transitions.duration.enteringScreen,\n        exit: 0\n      },\n      children: children\n    }, transKey)\n  });\n}", "map": {"version": 3, "names": ["React", "clsx", "TransitionGroup", "Fade", "styled", "useTheme", "useThemeProps", "composeClasses", "getPickersFadeTransitionGroupUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "PickersFadeTransitionGroupRoot", "name", "slot", "overridesResolver", "_", "styles", "display", "position", "PickersFadeTransitionGroup", "inProps", "props", "children", "className", "reduceAnimations", "transKey", "theme", "appear", "mountOnEnter", "unmountOnExit", "timeout", "transitions", "duration", "enteringScreen", "enter", "exit"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/DateCalendar/PickersFadeTransitionGroup.js"], "sourcesContent": ["import * as React from 'react';\nimport clsx from 'clsx';\nimport { TransitionGroup } from 'react-transition-group';\nimport Fade from '@mui/material/Fade';\nimport { styled, useTheme, useThemeProps } from '@mui/material/styles';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { getPickersFadeTransitionGroupUtilityClass } from \"./pickersFadeTransitionGroupClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getPickersFadeTransitionGroupUtilityClass, classes);\n};\nconst PickersFadeTransitionGroupRoot = styled(TransitionGroup, {\n  name: 'MuiPickersFadeTransitionGroup',\n  slot: 'Root',\n  overridesResolver: (_, styles) => styles.root\n})({\n  display: 'block',\n  position: 'relative'\n});\n\n/**\n * @ignore - do not document.\n */\nexport function PickersFadeTransitionGroup(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiPickersFadeTransitionGroup'\n  });\n  const {\n    children,\n    className,\n    reduceAnimations,\n    transKey\n  } = props;\n  const classes = useUtilityClasses(props);\n  const theme = useTheme();\n  if (reduceAnimations) {\n    return children;\n  }\n  return /*#__PURE__*/_jsx(PickersFadeTransitionGroupRoot, {\n    className: clsx(classes.root, className),\n    children: /*#__PURE__*/_jsx(Fade, {\n      appear: false,\n      mountOnEnter: true,\n      unmountOnExit: true,\n      timeout: {\n        appear: theme.transitions.duration.enteringScreen,\n        enter: theme.transitions.duration.enteringScreen,\n        exit: 0\n      },\n      children: children\n    }, transKey)\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,sBAAsB;AACtE,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,yCAAyC,QAAQ,wCAAwC;AAClG,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOR,cAAc,CAACO,KAAK,EAAEN,yCAAyC,EAAEK,OAAO,CAAC;AAClF,CAAC;AACD,MAAMG,8BAA8B,GAAGZ,MAAM,CAACF,eAAe,EAAE;EAC7De,IAAI,EAAE,+BAA+B;EACrCC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC3C,CAAC,CAAC,CAAC;EACDO,OAAO,EAAE,OAAO;EAChBC,QAAQ,EAAE;AACZ,CAAC,CAAC;;AAEF;AACA;AACA;AACA,OAAO,SAASC,0BAA0BA,CAACC,OAAO,EAAE;EAClD,MAAMC,KAAK,GAAGpB,aAAa,CAAC;IAC1BoB,KAAK,EAAED,OAAO;IACdR,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJU,QAAQ;IACRC,SAAS;IACTC,gBAAgB;IAChBC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAMb,OAAO,GAAGF,iBAAiB,CAACe,KAAK,CAAC;EACxC,MAAMK,KAAK,GAAG1B,QAAQ,CAAC,CAAC;EACxB,IAAIwB,gBAAgB,EAAE;IACpB,OAAOF,QAAQ;EACjB;EACA,OAAO,aAAajB,IAAI,CAACM,8BAA8B,EAAE;IACvDY,SAAS,EAAE3B,IAAI,CAACY,OAAO,CAACE,IAAI,EAAEa,SAAS,CAAC;IACxCD,QAAQ,EAAE,aAAajB,IAAI,CAACP,IAAI,EAAE;MAChC6B,MAAM,EAAE,KAAK;MACbC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,OAAO,EAAE;QACPH,MAAM,EAAED,KAAK,CAACK,WAAW,CAACC,QAAQ,CAACC,cAAc;QACjDC,KAAK,EAAER,KAAK,CAACK,WAAW,CAACC,QAAQ,CAACC,cAAc;QAChDE,IAAI,EAAE;MACR,CAAC;MACDb,QAAQ,EAAEA;IACZ,CAAC,EAAEG,QAAQ;EACb,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}