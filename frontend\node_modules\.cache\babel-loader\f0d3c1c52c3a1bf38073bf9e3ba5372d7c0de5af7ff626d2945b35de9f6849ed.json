{"ast": null, "code": "/**\n * Ponyfill for Java's Float class.\n */\nvar Float = /** @class */function () {\n  function Float() {}\n  /**\n   * SincTS has no difference between int and float, there's all numbers,\n   * this is used only to polyfill Java code.\n   */\n  Float.floatToIntBits = function (f) {\n    return f;\n  };\n  /**\n   * The float max value in JS is the number max value.\n   */\n  Float.MAX_VALUE = Number.MAX_SAFE_INTEGER;\n  return Float;\n}();\nexport default Float;", "map": {"version": 3, "names": ["Float", "floatToIntBits", "f", "MAX_VALUE", "Number", "MAX_SAFE_INTEGER"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/Float.js"], "sourcesContent": ["/**\n * Ponyfill for Java's Float class.\n */\nvar Float = /** @class */ (function () {\n    function Float() {\n    }\n    /**\n     * SincTS has no difference between int and float, there's all numbers,\n     * this is used only to polyfill Java code.\n     */\n    Float.floatToIntBits = function (f) {\n        return f;\n    };\n    /**\n     * The float max value in JS is the number max value.\n     */\n    Float.MAX_VALUE = Number.MAX_SAFE_INTEGER;\n    return Float;\n}());\nexport default Float;\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,KAAK,GAAG,aAAe,YAAY;EACnC,SAASA,KAAKA,CAAA,EAAG,CACjB;EACA;AACJ;AACA;AACA;EACIA,KAAK,CAACC,cAAc,GAAG,UAAUC,CAAC,EAAE;IAChC,OAAOA,CAAC;EACZ,CAAC;EACD;AACJ;AACA;EACIF,KAAK,CAACG,SAAS,GAAGC,MAAM,CAACC,gBAAgB;EACzC,OAAOL,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}