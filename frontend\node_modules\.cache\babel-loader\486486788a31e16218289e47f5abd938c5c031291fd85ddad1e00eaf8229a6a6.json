{"ast": null, "code": "export const areViewsEqual = (views, expectedViews) => {\n  if (views.length !== expectedViews.length) {\n    return false;\n  }\n  return expectedViews.every(expectedView => views.includes(expectedView));\n};\nexport const applyDefaultViewProps = ({\n  openTo,\n  defaultOpenTo,\n  views,\n  defaultViews\n}) => {\n  const viewsWithDefault = views ?? defaultViews;\n  let openToWithDefault;\n  if (openTo != null) {\n    openToWithDefault = openTo;\n  } else if (viewsWithDefault.includes(defaultOpenTo)) {\n    openToWithDefault = defaultOpenTo;\n  } else if (viewsWithDefault.length > 0) {\n    openToWithDefault = viewsWithDefault[0];\n  } else {\n    throw new Error('MUI X: The `views` prop must contain at least one view.');\n  }\n  return {\n    views: viewsWithDefault,\n    openTo: openToWithDefault\n  };\n};", "map": {"version": 3, "names": ["areViewsEqual", "views", "expectedViews", "length", "every", "expected<PERSON>iew", "includes", "applyDefaultViewProps", "openTo", "defaultOpenTo", "defaultViews", "viewsWithDefault", "openToWithDefault", "Error"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/utils/views.js"], "sourcesContent": ["export const areViewsEqual = (views, expectedViews) => {\n  if (views.length !== expectedViews.length) {\n    return false;\n  }\n  return expectedViews.every(expectedView => views.includes(expectedView));\n};\nexport const applyDefaultViewProps = ({\n  openTo,\n  defaultOpenTo,\n  views,\n  defaultViews\n}) => {\n  const viewsWithDefault = views ?? defaultViews;\n  let openToWithDefault;\n  if (openTo != null) {\n    openToWithDefault = openTo;\n  } else if (viewsWithDefault.includes(defaultOpenTo)) {\n    openToWithDefault = defaultOpenTo;\n  } else if (viewsWithDefault.length > 0) {\n    openToWithDefault = viewsWithDefault[0];\n  } else {\n    throw new Error('MUI X: The `views` prop must contain at least one view.');\n  }\n  return {\n    views: viewsWithDefault,\n    openTo: openToWithDefault\n  };\n};"], "mappings": "AAAA,OAAO,MAAMA,aAAa,GAAGA,CAACC,KAAK,EAAEC,aAAa,KAAK;EACrD,IAAID,KAAK,CAACE,MAAM,KAAKD,aAAa,CAACC,MAAM,EAAE;IACzC,OAAO,KAAK;EACd;EACA,OAAOD,aAAa,CAACE,KAAK,CAACC,YAAY,IAAIJ,KAAK,CAACK,QAAQ,CAACD,YAAY,CAAC,CAAC;AAC1E,CAAC;AACD,OAAO,MAAME,qBAAqB,GAAGA,CAAC;EACpCC,MAAM;EACNC,aAAa;EACbR,KAAK;EACLS;AACF,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAGV,KAAK,IAAIS,YAAY;EAC9C,IAAIE,iBAAiB;EACrB,IAAIJ,MAAM,IAAI,IAAI,EAAE;IAClBI,iBAAiB,GAAGJ,MAAM;EAC5B,CAAC,MAAM,IAAIG,gBAAgB,CAACL,QAAQ,CAACG,aAAa,CAAC,EAAE;IACnDG,iBAAiB,GAAGH,aAAa;EACnC,CAAC,MAAM,IAAIE,gBAAgB,CAACR,MAAM,GAAG,CAAC,EAAE;IACtCS,iBAAiB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACzC,CAAC,MAAM;IACL,MAAM,IAAIE,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EACA,OAAO;IACLZ,KAAK,EAAEU,gBAAgB;IACvBH,MAAM,EAAEI;EACV,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}