{"ast": null, "code": "function fixProto(target, prototype) {\n  var setPrototypeOf = Object.setPrototypeOf;\n  setPrototypeOf ? setPrototypeOf(target, prototype) : target.__proto__ = prototype;\n}\nfunction fixStack(target, fn) {\n  if (fn === void 0) {\n    fn = target.constructor;\n  }\n  var captureStackTrace = Error.captureStackTrace;\n  captureStackTrace && captureStackTrace(target, fn);\n}\nvar __extends = undefined && undefined.__extends || function () {\n  var _extendStatics = function extendStatics(d, b) {\n    _extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) {\n        if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n      }\n    };\n    return _extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    _extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar CustomError = function (_super) {\n  __extends(CustomError, _super);\n  function CustomError(message, options) {\n    var _newTarget = this.constructor;\n    var _this = _super.call(this, message, options) || this;\n    Object.defineProperty(_this, 'name', {\n      value: _newTarget.name,\n      enumerable: false,\n      configurable: true\n    });\n    fixProto(_this, _newTarget.prototype);\n    fixStack(_this);\n    return _this;\n  }\n  return CustomError;\n}(Error);\nvar __spreadArray = undefined && undefined.__spreadArray || function (to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n};\nfunction customErrorFactory(fn, parent) {\n  if (parent === void 0) {\n    parent = Error;\n  }\n  function CustomError() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (!(this instanceof CustomError)) return new (CustomError.bind.apply(CustomError, __spreadArray([void 0], args, false)))();\n    parent.apply(this, args);\n    Object.defineProperty(this, 'name', {\n      value: fn.name || parent.name,\n      enumerable: false,\n      configurable: true\n    });\n    fn.apply(this, args);\n    fixStack(this, CustomError);\n  }\n  return Object.defineProperties(CustomError, {\n    prototype: {\n      value: Object.create(parent.prototype, {\n        constructor: {\n          value: CustomError,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  });\n}\nexport { CustomError, customErrorFactory };", "map": {"version": 3, "names": ["fixProto", "target", "prototype", "setPrototypeOf", "Object", "__proto__", "fixStack", "fn", "constructor", "captureStackTrace", "Error", "CustomError", "_super", "__extends", "message", "options", "_this", "call", "defineProperty", "value", "_newTarget", "name", "enumerable", "configurable", "customErrorFactory", "parent", "args", "_i", "arguments", "length", "bind", "apply", "__spread<PERSON><PERSON>y", "defineProperties", "create", "writable"], "sources": ["D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\node_modules\\ts-custom-error\\dist\\src\\utils.js", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\node_modules\\ts-custom-error\\dist\\src\\custom-error.js", "D:\\My Learning Projects\\uni-core-business-suite_v3\\frontend\\node_modules\\ts-custom-error\\dist\\src\\factory.js"], "sourcesContent": ["export function fixProto(target, prototype) {\n    var setPrototypeOf = Object.setPrototypeOf;\n    setPrototypeOf\n        ? setPrototypeOf(target, prototype)\n        : (target.__proto__ = prototype);\n}\nexport function fixStack(target, fn) {\n    if (fn === void 0) { fn = target.constructor; }\n    var captureStackTrace = Error.captureStackTrace;\n    captureStackTrace && captureStackTrace(target, fn);\n}\n//# sourceMappingURL=utils.js.map", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { fixProto, fixStack } from './utils';\nvar CustomError = (function (_super) {\n    __extends(CustomError, _super);\n    function CustomError(message, options) {\n        var _newTarget = this.constructor;\n        var _this = _super.call(this, message, options) || this;\n        Object.defineProperty(_this, 'name', {\n            value: _newTarget.name,\n            enumerable: false,\n            configurable: true,\n        });\n        fixProto(_this, _newTarget.prototype);\n        fixStack(_this);\n        return _this;\n    }\n    return CustomError;\n}(Error));\nexport { CustomError };\n//# sourceMappingURL=custom-error.js.map", "var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { fixStack } from './utils';\nexport function customErrorFactory(fn, parent) {\n    if (parent === void 0) { parent = Error; }\n    function CustomError() {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (!(this instanceof CustomError))\n            return new (CustomError.bind.apply(CustomError, __spreadArray([void 0], args, false)))();\n        parent.apply(this, args);\n        Object.defineProperty(this, 'name', {\n            value: fn.name || parent.name,\n            enumerable: false,\n            configurable: true,\n        });\n        fn.apply(this, args);\n        fixStack(this, CustomError);\n    }\n    return Object.defineProperties(CustomError, {\n        prototype: {\n            value: Object.create(parent.prototype, {\n                constructor: {\n                    value: CustomError,\n                    writable: true,\n                    configurable: true,\n                },\n            }),\n        },\n    });\n}\n//# sourceMappingURL=factory.js.map"], "mappings": "AASM,SAAUA,QAAVA,CAAmBC,MAAnB,EAAkCC,SAAlC,EAA+C;EACpD,IAAMC,cAAc,GAAcC,MAAc,CAACD,cAAjD;EACAA,cAAc,GACXA,cAAc,CAACF,MAAD,EAASC,SAAT,CADH,GAETD,MAAc,CAACI,SAAf,GAA2BH,SAFhC;AAGA;AAQK,SAAUI,QAAVA,CAAmBL,MAAnB,EAAkCM,EAAlC,EAAmE;EAAjC,IAAAA,EAAA;IAAAA,EAAe,GAAAN,MAAM,CAACO,WAAtB;EAAiC;EACxE,IAAMC,iBAAiB,GAAcC,KAAa,CAACD,iBAAnD;EACAA,iBAAiB,IAAIA,iBAAiB,CAACR,MAAD,EAASM,EAAT,CAAtC;AACA;;;;;;;;;;;;;;;;;;;;;;;ACCD,IAAAI,WAAA,aAAAC,MAAA;EAAiCC,SAAA,CAAAF,WAAA,EAAAC,MAAA;EAGhC,SAAYD,YAAAG,OAAZ,EAA8BC,OAA9B,EAAoD;;IAApD,IAAAC,KAAA,GACCJ,MAAM,CAAAK,IAAA,OAAAH,OAAN,EAAeC,OAAf,KAAuB,IADxB;IAKCX,MAAM,CAACc,cAAP,CAAsBF,KAAtB,EAA4B,MAA5B,EAAoC;MACnCG,KAAK,EAAEC,UAAA,CAAWC,IADiB;MAEnCC,UAAU,EAAE,KAFuB;MAGnCC,YAAY,EAAE;KAHf;IAQAvB,QAAQ,CAACgB,KAAD,EAAOI,UAAA,CAAWlB,SAAlB,CAAR;IAEAI,QAAQ,CAACU,KAAD,CAAR;;EACA;EACF,OAAAL,WAAA;AAAC,CApBD,CAAiCD,KAAjC;;;;;;;;;;ACeM,SAAUc,kBAAVA,CACLjB,EADK,EAELkB,MAFK,EAEkC;EAAvC,IAAAA,MAAA;IAAAA,MAAA,GAAAf,KAAA;EAAuC;EAEvC,SAASC,WAATA,CAAA,EAAoB;IAA0C,IAAAe,IAAA;SAAA,IAAcC,EAAA,MAAAA,EAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,EAAA;MAAdD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;IAE7D,IAAI,EAAE,IAAgB,YAAAhB,WAAlB,CAAJ,EAAoC,YAAWA,WAAW,CAAAmB,IAAX,CAAWC,KAAX,CAAApB,WAAA,EAAWqB,aAAI,WAAAN,IAAJ,EAAQ,KAAR,CAAX,CAAX;IAEpCD,MAAM,CAACM,KAAP,CAAa,IAAb,EAAmBL,IAAnB;IAEAtB,MAAM,CAACc,cAAP,CAAsB,IAAtB,EAA4B,MAA5B,EAAoC;MACnCC,KAAK,EAAEZ,EAAE,CAACc,IAAH,IAAWI,MAAM,CAACJ,IADU;MAEnCC,UAAU,EAAE,KAFuB;MAGnCC,YAAY,EAAE;KAHf;IAMAhB,EAAE,CAACwB,KAAH,CAAS,IAAT,EAAeL,IAAf;IAEApB,QAAQ,CAAC,IAAD,EAAOK,WAAP,CAAR;EACA;EAED,OAAOP,MAAM,CAAC6B,gBAAP,CAAwBtB,WAAxB,EAAqC;IAC3CT,SAAS,EAAE;MACViB,KAAK,EAAEf,MAAM,CAAC8B,MAAP,CAAcT,MAAM,CAACvB,SAArB,EAAgC;QACtCM,WAAW,EAAE;UACZW,KAAK,EAAER,WADK;UAEZwB,QAAQ,EAAE,IAFE;UAGZZ,YAAY,EAAE;QAHF;OADP;IADG;EADgC,CAArC,CAAP;AAWA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}