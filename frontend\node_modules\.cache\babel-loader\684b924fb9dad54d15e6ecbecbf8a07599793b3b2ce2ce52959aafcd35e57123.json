{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"value\", \"referenceDate\"];\nimport { areDatesEqual, getTodayDate, replaceInvalidDateByNull } from \"./date-utils.js\";\nimport { getDefaultReferenceDate } from \"./getDefaultReferenceDate.js\";\nimport { createDateStrForV7HiddenInputFromSections, createDateStrForV6InputFromSections } from \"../hooks/useField/useField.utils.js\";\nexport const singleItemValueManager = {\n  emptyValue: null,\n  getTodayValue: getTodayDate,\n  getInitialReferenceValue: _ref => {\n    let {\n        value,\n        referenceDate\n      } = _ref,\n      params = _objectWithoutPropertiesLoose(_ref, _excluded);\n    if (value != null && params.utils.isValid(value)) {\n      return value;\n    }\n    if (referenceDate != null) {\n      return referenceDate;\n    }\n    return getDefaultReferenceDate(params);\n  },\n  cleanValue: replaceInvalidDateByNull,\n  areValuesEqual: areDatesEqual,\n  isSameError: (a, b) => a === b,\n  hasError: error => error != null,\n  defaultErrorState: null,\n  getTimezone: (utils, value) => value == null || !utils.isValid(value) ? null : utils.getTimezone(value),\n  setTimezone: (utils, timezone, value) => value == null ? null : utils.setTimezone(value, timezone)\n};\nexport const singleItemFieldValueManager = {\n  updateReferenceValue: (utils, value, prevReferenceValue) => value == null || !utils.isValid(value) ? prevReferenceValue : value,\n  getSectionsFromValue: (utils, date, prevSections, getSectionsFromDate) => {\n    const shouldReUsePrevDateSections = !utils.isValid(date) && !!prevSections;\n    if (shouldReUsePrevDateSections) {\n      return prevSections;\n    }\n    return getSectionsFromDate(date);\n  },\n  getV7HiddenInputValueFromSections: createDateStrForV7HiddenInputFromSections,\n  getV6InputValueFromSections: createDateStrForV6InputFromSections,\n  getActiveDateManager: (utils, state) => ({\n    date: state.value,\n    referenceDate: state.referenceValue,\n    getSections: sections => sections,\n    getNewValuesFromNewActiveDate: newActiveDate => ({\n      value: newActiveDate,\n      referenceValue: newActiveDate == null || !utils.isValid(newActiveDate) ? state.referenceValue : newActiveDate\n    })\n  }),\n  parseValueStr: (valueStr, referenceValue, parseDate) => parseDate(valueStr.trim(), referenceValue)\n};", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_excluded", "areDatesEqual", "getTodayDate", "replaceInvalidDateByNull", "getDefaultReferenceDate", "createDateStrForV7HiddenInputFromSections", "createDateStrForV6InputFromSections", "singleItemValueManager", "emptyValue", "getTodayValue", "getInitialReferenceValue", "_ref", "value", "referenceDate", "params", "utils", "<PERSON><PERSON><PERSON><PERSON>", "cleanValue", "areValuesEqual", "isSameError", "a", "b", "<PERSON><PERSON><PERSON><PERSON>", "error", "defaultErrorState", "getTimezone", "setTimezone", "timezone", "singleItemFieldValueManager", "updateReferenceValue", "prevReferenceValue", "getSectionsFromValue", "date", "prevSections", "getSectionsFromDate", "shouldReUsePrevDateSections", "getV7HiddenInputValueFromSections", "getV6InputValueFromSections", "getActiveDateManager", "state", "referenceValue", "getSections", "sections", "getNewValuesFromNewActiveDate", "newActiveDate", "parseValueStr", "valueStr", "parseDate", "trim"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/utils/valueManagers.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"value\", \"referenceDate\"];\nimport { areDatesEqual, getTodayDate, replaceInvalidDateByNull } from \"./date-utils.js\";\nimport { getDefaultReferenceDate } from \"./getDefaultReferenceDate.js\";\nimport { createDateStrForV7HiddenInputFromSections, createDateStrForV6InputFromSections } from \"../hooks/useField/useField.utils.js\";\nexport const singleItemValueManager = {\n  emptyValue: null,\n  getTodayValue: getTodayDate,\n  getInitialReferenceValue: _ref => {\n    let {\n        value,\n        referenceDate\n      } = _ref,\n      params = _objectWithoutPropertiesLoose(_ref, _excluded);\n    if (value != null && params.utils.isValid(value)) {\n      return value;\n    }\n    if (referenceDate != null) {\n      return referenceDate;\n    }\n    return getDefaultReferenceDate(params);\n  },\n  cleanValue: replaceInvalidDateByNull,\n  areValuesEqual: areDatesEqual,\n  isSameError: (a, b) => a === b,\n  hasError: error => error != null,\n  defaultErrorState: null,\n  getTimezone: (utils, value) => value == null || !utils.isValid(value) ? null : utils.getTimezone(value),\n  setTimezone: (utils, timezone, value) => value == null ? null : utils.setTimezone(value, timezone)\n};\nexport const singleItemFieldValueManager = {\n  updateReferenceValue: (utils, value, prevReferenceValue) => value == null || !utils.isValid(value) ? prevReferenceValue : value,\n  getSectionsFromValue: (utils, date, prevSections, getSectionsFromDate) => {\n    const shouldReUsePrevDateSections = !utils.isValid(date) && !!prevSections;\n    if (shouldReUsePrevDateSections) {\n      return prevSections;\n    }\n    return getSectionsFromDate(date);\n  },\n  getV7HiddenInputValueFromSections: createDateStrForV7HiddenInputFromSections,\n  getV6InputValueFromSections: createDateStrForV6InputFromSections,\n  getActiveDateManager: (utils, state) => ({\n    date: state.value,\n    referenceDate: state.referenceValue,\n    getSections: sections => sections,\n    getNewValuesFromNewActiveDate: newActiveDate => ({\n      value: newActiveDate,\n      referenceValue: newActiveDate == null || !utils.isValid(newActiveDate) ? state.referenceValue : newActiveDate\n    })\n  }),\n  parseValueStr: (valueStr, referenceValue, parseDate) => parseDate(valueStr.trim(), referenceValue)\n};"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,eAAe,CAAC;AAC5C,SAASC,aAAa,EAAEC,YAAY,EAAEC,wBAAwB,QAAQ,iBAAiB;AACvF,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,yCAAyC,EAAEC,mCAAmC,QAAQ,qCAAqC;AACpI,OAAO,MAAMC,sBAAsB,GAAG;EACpCC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAEP,YAAY;EAC3BQ,wBAAwB,EAAEC,IAAI,IAAI;IAChC,IAAI;QACAC,KAAK;QACLC;MACF,CAAC,GAAGF,IAAI;MACRG,MAAM,GAAGf,6BAA6B,CAACY,IAAI,EAAEX,SAAS,CAAC;IACzD,IAAIY,KAAK,IAAI,IAAI,IAAIE,MAAM,CAACC,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;MAChD,OAAOA,KAAK;IACd;IACA,IAAIC,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOA,aAAa;IACtB;IACA,OAAOT,uBAAuB,CAACU,MAAM,CAAC;EACxC,CAAC;EACDG,UAAU,EAAEd,wBAAwB;EACpCe,cAAc,EAAEjB,aAAa;EAC7BkB,WAAW,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,KAAKC,CAAC;EAC9BC,QAAQ,EAAEC,KAAK,IAAIA,KAAK,IAAI,IAAI;EAChCC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAEA,CAACV,KAAK,EAAEH,KAAK,KAAKA,KAAK,IAAI,IAAI,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,GAAG,IAAI,GAAGG,KAAK,CAACU,WAAW,CAACb,KAAK,CAAC;EACvGc,WAAW,EAAEA,CAACX,KAAK,EAAEY,QAAQ,EAAEf,KAAK,KAAKA,KAAK,IAAI,IAAI,GAAG,IAAI,GAAGG,KAAK,CAACW,WAAW,CAACd,KAAK,EAAEe,QAAQ;AACnG,CAAC;AACD,OAAO,MAAMC,2BAA2B,GAAG;EACzCC,oBAAoB,EAAEA,CAACd,KAAK,EAAEH,KAAK,EAAEkB,kBAAkB,KAAKlB,KAAK,IAAI,IAAI,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,GAAGkB,kBAAkB,GAAGlB,KAAK;EAC/HmB,oBAAoB,EAAEA,CAAChB,KAAK,EAAEiB,IAAI,EAAEC,YAAY,EAAEC,mBAAmB,KAAK;IACxE,MAAMC,2BAA2B,GAAG,CAACpB,KAAK,CAACC,OAAO,CAACgB,IAAI,CAAC,IAAI,CAAC,CAACC,YAAY;IAC1E,IAAIE,2BAA2B,EAAE;MAC/B,OAAOF,YAAY;IACrB;IACA,OAAOC,mBAAmB,CAACF,IAAI,CAAC;EAClC,CAAC;EACDI,iCAAiC,EAAE/B,yCAAyC;EAC5EgC,2BAA2B,EAAE/B,mCAAmC;EAChEgC,oBAAoB,EAAEA,CAACvB,KAAK,EAAEwB,KAAK,MAAM;IACvCP,IAAI,EAAEO,KAAK,CAAC3B,KAAK;IACjBC,aAAa,EAAE0B,KAAK,CAACC,cAAc;IACnCC,WAAW,EAAEC,QAAQ,IAAIA,QAAQ;IACjCC,6BAA6B,EAAEC,aAAa,KAAK;MAC/ChC,KAAK,EAAEgC,aAAa;MACpBJ,cAAc,EAAEI,aAAa,IAAI,IAAI,IAAI,CAAC7B,KAAK,CAACC,OAAO,CAAC4B,aAAa,CAAC,GAAGL,KAAK,CAACC,cAAc,GAAGI;IAClG,CAAC;EACH,CAAC,CAAC;EACFC,aAAa,EAAEA,CAACC,QAAQ,EAAEN,cAAc,EAAEO,SAAS,KAAKA,SAAS,CAACD,QAAQ,CAACE,IAAI,CAAC,CAAC,EAAER,cAAc;AACnG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}