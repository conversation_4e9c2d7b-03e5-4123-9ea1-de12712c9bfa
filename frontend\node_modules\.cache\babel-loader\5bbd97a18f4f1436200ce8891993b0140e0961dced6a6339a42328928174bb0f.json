{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport { C40Encoder } from './C40Encoder';\nimport { TEXT_ENCODATION } from './constants';\nvar TextEncoder = /** @class */function (_super) {\n  __extends(TextEncoder, _super);\n  function TextEncoder() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  TextEncoder.prototype.getEncodingMode = function () {\n    return TEXT_ENCODATION;\n  };\n  TextEncoder.prototype.encodeChar = function (c, sb) {\n    if (c === ' '.charCodeAt(0)) {\n      sb.append(3);\n      return 1;\n    }\n    if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n      sb.append(c - 48 + 4);\n      return 1;\n    }\n    if (c >= 'a'.charCodeAt(0) && c <= 'z'.charCodeAt(0)) {\n      sb.append(c - 97 + 14);\n      return 1;\n    }\n    if (c < ' '.charCodeAt(0)) {\n      sb.append(0); // Shift 1 Set\n      sb.append(c);\n      return 2;\n    }\n    if (c <= '/'.charCodeAt(0)) {\n      sb.append(1); // Shift 2 Set\n      sb.append(c - 33);\n      return 2;\n    }\n    if (c <= '@'.charCodeAt(0)) {\n      sb.append(1); // Shift 2 Set\n      sb.append(c - 58 + 15);\n      return 2;\n    }\n    if (c >= '['.charCodeAt(0) && c <= '_'.charCodeAt(0)) {\n      sb.append(1); // Shift 2 Set\n      sb.append(c - 91 + 22);\n      return 2;\n    }\n    if (c === '`'.charCodeAt(0)) {\n      sb.append(2); // Shift 3 Set\n      sb.append(0); // '`' - 96 == 0\n      return 2;\n    }\n    if (c <= 'Z'.charCodeAt(0)) {\n      sb.append(2); // Shift 3 Set\n      sb.append(c - 65 + 1);\n      return 2;\n    }\n    if (c <= 127) {\n      sb.append(2); // Shift 3 Set\n      sb.append(c - 123 + 27);\n      return 2;\n    }\n    sb.append(1 + \"\\u001E\"); // Shift 2, Upper Shift\n    var len = 2;\n    len += this.encodeChar(c - 128, sb);\n    return len;\n  };\n  return TextEncoder;\n}(C40Encoder);\nexport { TextEncoder };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "C40Encoder", "TEXT_ENCODATION", "TextEncoder", "_super", "apply", "arguments", "getEncodingMode", "encodeChar", "c", "sb", "charCodeAt", "append", "len"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/TextEncoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { C40Encoder } from './C40Encoder';\nimport { TEXT_ENCODATION } from './constants';\nvar TextEncoder = /** @class */ (function (_super) {\n    __extends(TextEncoder, _super);\n    function TextEncoder() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    TextEncoder.prototype.getEncodingMode = function () {\n        return TEXT_ENCODATION;\n    };\n    TextEncoder.prototype.encodeChar = function (c, sb) {\n        if (c === ' '.charCodeAt(0)) {\n            sb.append(3);\n            return 1;\n        }\n        if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n            sb.append(c - 48 + 4);\n            return 1;\n        }\n        if (c >= 'a'.charCodeAt(0) && c <= 'z'.charCodeAt(0)) {\n            sb.append(c - 97 + 14);\n            return 1;\n        }\n        if (c < ' '.charCodeAt(0)) {\n            sb.append(0); // Shift 1 Set\n            sb.append(c);\n            return 2;\n        }\n        if (c <= '/'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 33);\n            return 2;\n        }\n        if (c <= '@'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 58 + 15);\n            return 2;\n        }\n        if (c >= '['.charCodeAt(0) && c <= '_'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 91 + 22);\n            return 2;\n        }\n        if (c === '`'.charCodeAt(0)) {\n            sb.append(2); // Shift 3 Set\n            sb.append(0); // '`' - 96 == 0\n            return 2;\n        }\n        if (c <= 'Z'.charCodeAt(0)) {\n            sb.append(2); // Shift 3 Set\n            sb.append(c - 65 + 1);\n            return 2;\n        }\n        if (c <= 127) {\n            sb.append(2); // Shift 3 Set\n            sb.append(c - 123 + 27);\n            return 2;\n        }\n        sb.append(1 + \"\\u001E\"); // Shift 2, Upper Shift\n        var len = 2;\n        len += this.encodeChar(c - 128, sb);\n        return len;\n    };\n    return TextEncoder;\n}(C40Encoder));\nexport { TextEncoder };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,SAASI,UAAU,QAAQ,cAAc;AACzC,SAASC,eAAe,QAAQ,aAAa;AAC7C,IAAIC,WAAW,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC/CjB,SAAS,CAACgB,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAAA,EAAG;IACnB,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACAH,WAAW,CAACJ,SAAS,CAACQ,eAAe,GAAG,YAAY;IAChD,OAAOL,eAAe;EAC1B,CAAC;EACDC,WAAW,CAACJ,SAAS,CAACS,UAAU,GAAG,UAAUC,CAAC,EAAEC,EAAE,EAAE;IAChD,IAAID,CAAC,KAAK,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MACzBD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC;MACZ,OAAO,CAAC;IACZ;IACA,IAAIH,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,IAAIF,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MAClDD,EAAE,CAACE,MAAM,CAACH,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MACrB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,IAAIF,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MAClDD,EAAE,CAACE,MAAM,CAACH,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;MACtB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,GAAG,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MACvBD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdF,EAAE,CAACE,MAAM,CAACH,CAAC,CAAC;MACZ,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MACxBD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdF,EAAE,CAACE,MAAM,CAACH,CAAC,GAAG,EAAE,CAAC;MACjB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MACxBD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdF,EAAE,CAACE,MAAM,CAACH,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;MACtB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,IAAIF,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MAClDD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdF,EAAE,CAACE,MAAM,CAACH,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;MACtB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,KAAK,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MACzBD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdF,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,OAAO,CAAC;IACZ;IACA,IAAIH,CAAC,IAAI,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC,EAAE;MACxBD,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdF,EAAE,CAACE,MAAM,CAACH,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MACrB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,EAAE;MACVC,EAAE,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdF,EAAE,CAACE,MAAM,CAACH,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;MACvB,OAAO,CAAC;IACZ;IACAC,EAAE,CAACE,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;IACzB,IAAIC,GAAG,GAAG,CAAC;IACXA,GAAG,IAAI,IAAI,CAACL,UAAU,CAACC,CAAC,GAAG,GAAG,EAAEC,EAAE,CAAC;IACnC,OAAOG,GAAG;EACd,CAAC;EACD,OAAOV,WAAW;AACtB,CAAC,CAACF,UAAU,CAAE;AACd,SAASE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}