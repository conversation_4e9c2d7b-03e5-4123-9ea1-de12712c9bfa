{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport AbstractGenericGF from './AbstractGenericGF';\nimport System from '../../util/System';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Represents a polynomial whose coefficients are elements of a GF.\n * Instances of this class are immutable.</p>\n *\n * <p>Much credit is due to <PERSON> since portions of this code are an indirect\n * port of his C++ Reed-Solomon implementation.</p>\n *\n * <AUTHOR>\n */\nvar GenericGFPoly = /** @class */function () {\n  /**\n   * @param field the {@link GenericGF} instance representing the field to use\n   * to perform computations\n   * @param coefficients coefficients as ints representing elements of GF(size), arranged\n   * from most significant (highest-power term) coefficient to least significant\n   * @throws IllegalArgumentException if argument is null or empty,\n   * or if leading coefficient is 0 and this is not a\n   * constant polynomial (that is, it is not the monomial \"0\")\n   */\n  function GenericGFPoly(field, coefficients) {\n    if (coefficients.length === 0) {\n      throw new IllegalArgumentException();\n    }\n    this.field = field;\n    var coefficientsLength = coefficients.length;\n    if (coefficientsLength > 1 && coefficients[0] === 0) {\n      // Leading term must be non-zero for anything except the constant polynomial \"0\"\n      var firstNonZero = 1;\n      while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n        firstNonZero++;\n      }\n      if (firstNonZero === coefficientsLength) {\n        this.coefficients = Int32Array.from([0]);\n      } else {\n        this.coefficients = new Int32Array(coefficientsLength - firstNonZero);\n        System.arraycopy(coefficients, firstNonZero, this.coefficients, 0, this.coefficients.length);\n      }\n    } else {\n      this.coefficients = coefficients;\n    }\n  }\n  GenericGFPoly.prototype.getCoefficients = function () {\n    return this.coefficients;\n  };\n  /**\n   * @return degree of this polynomial\n   */\n  GenericGFPoly.prototype.getDegree = function () {\n    return this.coefficients.length - 1;\n  };\n  /**\n   * @return true iff this polynomial is the monomial \"0\"\n   */\n  GenericGFPoly.prototype.isZero = function () {\n    return this.coefficients[0] === 0;\n  };\n  /**\n   * @return coefficient of x^degree term in this polynomial\n   */\n  GenericGFPoly.prototype.getCoefficient = function (degree /*int*/) {\n    return this.coefficients[this.coefficients.length - 1 - degree];\n  };\n  /**\n   * @return evaluation of this polynomial at a given point\n   */\n  GenericGFPoly.prototype.evaluateAt = function (a /*int*/) {\n    if (a === 0) {\n      // Just return the x^0 coefficient\n      return this.getCoefficient(0);\n    }\n    var coefficients = this.coefficients;\n    var result;\n    if (a === 1) {\n      // Just the sum of the coefficients\n      result = 0;\n      for (var i = 0, length_1 = coefficients.length; i !== length_1; i++) {\n        var coefficient = coefficients[i];\n        result = AbstractGenericGF.addOrSubtract(result, coefficient);\n      }\n      return result;\n    }\n    result = coefficients[0];\n    var size = coefficients.length;\n    var field = this.field;\n    for (var i = 1; i < size; i++) {\n      result = AbstractGenericGF.addOrSubtract(field.multiply(a, result), coefficients[i]);\n    }\n    return result;\n  };\n  GenericGFPoly.prototype.addOrSubtract = function (other) {\n    if (!this.field.equals(other.field)) {\n      throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n    }\n    if (this.isZero()) {\n      return other;\n    }\n    if (other.isZero()) {\n      return this;\n    }\n    var smallerCoefficients = this.coefficients;\n    var largerCoefficients = other.coefficients;\n    if (smallerCoefficients.length > largerCoefficients.length) {\n      var temp = smallerCoefficients;\n      smallerCoefficients = largerCoefficients;\n      largerCoefficients = temp;\n    }\n    var sumDiff = new Int32Array(largerCoefficients.length);\n    var lengthDiff = largerCoefficients.length - smallerCoefficients.length;\n    // Copy high-order terms only found in higher-degree polynomial's coefficients\n    System.arraycopy(largerCoefficients, 0, sumDiff, 0, lengthDiff);\n    for (var i = lengthDiff; i < largerCoefficients.length; i++) {\n      sumDiff[i] = AbstractGenericGF.addOrSubtract(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n    }\n    return new GenericGFPoly(this.field, sumDiff);\n  };\n  GenericGFPoly.prototype.multiply = function (other) {\n    if (!this.field.equals(other.field)) {\n      throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n    }\n    if (this.isZero() || other.isZero()) {\n      return this.field.getZero();\n    }\n    var aCoefficients = this.coefficients;\n    var aLength = aCoefficients.length;\n    var bCoefficients = other.coefficients;\n    var bLength = bCoefficients.length;\n    var product = new Int32Array(aLength + bLength - 1);\n    var field = this.field;\n    for (var i = 0; i < aLength; i++) {\n      var aCoeff = aCoefficients[i];\n      for (var j = 0; j < bLength; j++) {\n        product[i + j] = AbstractGenericGF.addOrSubtract(product[i + j], field.multiply(aCoeff, bCoefficients[j]));\n      }\n    }\n    return new GenericGFPoly(field, product);\n  };\n  GenericGFPoly.prototype.multiplyScalar = function (scalar /*int*/) {\n    if (scalar === 0) {\n      return this.field.getZero();\n    }\n    if (scalar === 1) {\n      return this;\n    }\n    var size = this.coefficients.length;\n    var field = this.field;\n    var product = new Int32Array(size);\n    var coefficients = this.coefficients;\n    for (var i = 0; i < size; i++) {\n      product[i] = field.multiply(coefficients[i], scalar);\n    }\n    return new GenericGFPoly(field, product);\n  };\n  GenericGFPoly.prototype.multiplyByMonomial = function (degree /*int*/, coefficient /*int*/) {\n    if (degree < 0) {\n      throw new IllegalArgumentException();\n    }\n    if (coefficient === 0) {\n      return this.field.getZero();\n    }\n    var coefficients = this.coefficients;\n    var size = coefficients.length;\n    var product = new Int32Array(size + degree);\n    var field = this.field;\n    for (var i = 0; i < size; i++) {\n      product[i] = field.multiply(coefficients[i], coefficient);\n    }\n    return new GenericGFPoly(field, product);\n  };\n  GenericGFPoly.prototype.divide = function (other) {\n    if (!this.field.equals(other.field)) {\n      throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n    }\n    if (other.isZero()) {\n      throw new IllegalArgumentException('Divide by 0');\n    }\n    var field = this.field;\n    var quotient = field.getZero();\n    var remainder = this;\n    var denominatorLeadingTerm = other.getCoefficient(other.getDegree());\n    var inverseDenominatorLeadingTerm = field.inverse(denominatorLeadingTerm);\n    while (remainder.getDegree() >= other.getDegree() && !remainder.isZero()) {\n      var degreeDifference = remainder.getDegree() - other.getDegree();\n      var scale = field.multiply(remainder.getCoefficient(remainder.getDegree()), inverseDenominatorLeadingTerm);\n      var term = other.multiplyByMonomial(degreeDifference, scale);\n      var iterationQuotient = field.buildMonomial(degreeDifference, scale);\n      quotient = quotient.addOrSubtract(iterationQuotient);\n      remainder = remainder.addOrSubtract(term);\n    }\n    return [quotient, remainder];\n  };\n  /*@Override*/\n  GenericGFPoly.prototype.toString = function () {\n    var result = '';\n    for (var degree = this.getDegree(); degree >= 0; degree--) {\n      var coefficient = this.getCoefficient(degree);\n      if (coefficient !== 0) {\n        if (coefficient < 0) {\n          result += ' - ';\n          coefficient = -coefficient;\n        } else {\n          if (result.length > 0) {\n            result += ' + ';\n          }\n        }\n        if (degree === 0 || coefficient !== 1) {\n          var alphaPower = this.field.log(coefficient);\n          if (alphaPower === 0) {\n            result += '1';\n          } else if (alphaPower === 1) {\n            result += 'a';\n          } else {\n            result += 'a^';\n            result += alphaPower;\n          }\n        }\n        if (degree !== 0) {\n          if (degree === 1) {\n            result += 'x';\n          } else {\n            result += 'x^';\n            result += degree;\n          }\n        }\n      }\n    }\n    return result;\n  };\n  return GenericGFPoly;\n}();\nexport default GenericGFPoly;", "map": {"version": 3, "names": ["AbstractGenericGF", "System", "IllegalArgumentException", "GenericGFPoly", "field", "coefficients", "length", "<PERSON><PERSON><PERSON><PERSON>", "firstNonZero", "Int32Array", "from", "arraycopy", "prototype", "getCoefficients", "getDegree", "isZero", "getCoefficient", "degree", "evaluateAt", "a", "result", "i", "length_1", "coefficient", "addOrSubtract", "size", "multiply", "other", "equals", "smallerCoefficients", "largerCoefficients", "temp", "sumDiff", "lengthDiff", "getZero", "aCoefficients", "a<PERSON><PERSON><PERSON>", "bCoefficients", "b<PERSON><PERSON><PERSON>", "product", "<PERSON><PERSON><PERSON><PERSON>", "j", "multiplyScalar", "scalar", "multiplyByMonomial", "divide", "quotient", "remainder", "denominatorLeadingTerm", "inverseDenominatorLeadingTerm", "inverse", "degreeDifference", "scale", "term", "iterationQuotient", "buildMonomial", "toString", "alphaPower", "log"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGFPoly.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport AbstractGenericGF from './AbstractGenericGF';\nimport System from '../../util/System';\nimport IllegalArgumentException from '../../IllegalArgumentException';\n/**\n * <p>Represents a polynomial whose coefficients are elements of a GF.\n * Instances of this class are immutable.</p>\n *\n * <p>Much credit is due to <PERSON> since portions of this code are an indirect\n * port of his C++ Reed-Solomon implementation.</p>\n *\n * <AUTHOR>\n */\nvar GenericGFPoly = /** @class */ (function () {\n    /**\n     * @param field the {@link GenericGF} instance representing the field to use\n     * to perform computations\n     * @param coefficients coefficients as ints representing elements of GF(size), arranged\n     * from most significant (highest-power term) coefficient to least significant\n     * @throws IllegalArgumentException if argument is null or empty,\n     * or if leading coefficient is 0 and this is not a\n     * constant polynomial (that is, it is not the monomial \"0\")\n     */\n    function GenericGFPoly(field, coefficients) {\n        if (coefficients.length === 0) {\n            throw new IllegalArgumentException();\n        }\n        this.field = field;\n        var coefficientsLength = coefficients.length;\n        if (coefficientsLength > 1 && coefficients[0] === 0) {\n            // Leading term must be non-zero for anything except the constant polynomial \"0\"\n            var firstNonZero = 1;\n            while (firstNonZero < coefficientsLength && coefficients[firstNonZero] === 0) {\n                firstNonZero++;\n            }\n            if (firstNonZero === coefficientsLength) {\n                this.coefficients = Int32Array.from([0]);\n            }\n            else {\n                this.coefficients = new Int32Array(coefficientsLength - firstNonZero);\n                System.arraycopy(coefficients, firstNonZero, this.coefficients, 0, this.coefficients.length);\n            }\n        }\n        else {\n            this.coefficients = coefficients;\n        }\n    }\n    GenericGFPoly.prototype.getCoefficients = function () {\n        return this.coefficients;\n    };\n    /**\n     * @return degree of this polynomial\n     */\n    GenericGFPoly.prototype.getDegree = function () {\n        return this.coefficients.length - 1;\n    };\n    /**\n     * @return true iff this polynomial is the monomial \"0\"\n     */\n    GenericGFPoly.prototype.isZero = function () {\n        return this.coefficients[0] === 0;\n    };\n    /**\n     * @return coefficient of x^degree term in this polynomial\n     */\n    GenericGFPoly.prototype.getCoefficient = function (degree /*int*/) {\n        return this.coefficients[this.coefficients.length - 1 - degree];\n    };\n    /**\n     * @return evaluation of this polynomial at a given point\n     */\n    GenericGFPoly.prototype.evaluateAt = function (a /*int*/) {\n        if (a === 0) {\n            // Just return the x^0 coefficient\n            return this.getCoefficient(0);\n        }\n        var coefficients = this.coefficients;\n        var result;\n        if (a === 1) {\n            // Just the sum of the coefficients\n            result = 0;\n            for (var i = 0, length_1 = coefficients.length; i !== length_1; i++) {\n                var coefficient = coefficients[i];\n                result = AbstractGenericGF.addOrSubtract(result, coefficient);\n            }\n            return result;\n        }\n        result = coefficients[0];\n        var size = coefficients.length;\n        var field = this.field;\n        for (var i = 1; i < size; i++) {\n            result = AbstractGenericGF.addOrSubtract(field.multiply(a, result), coefficients[i]);\n        }\n        return result;\n    };\n    GenericGFPoly.prototype.addOrSubtract = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n        }\n        if (this.isZero()) {\n            return other;\n        }\n        if (other.isZero()) {\n            return this;\n        }\n        var smallerCoefficients = this.coefficients;\n        var largerCoefficients = other.coefficients;\n        if (smallerCoefficients.length > largerCoefficients.length) {\n            var temp = smallerCoefficients;\n            smallerCoefficients = largerCoefficients;\n            largerCoefficients = temp;\n        }\n        var sumDiff = new Int32Array(largerCoefficients.length);\n        var lengthDiff = largerCoefficients.length - smallerCoefficients.length;\n        // Copy high-order terms only found in higher-degree polynomial's coefficients\n        System.arraycopy(largerCoefficients, 0, sumDiff, 0, lengthDiff);\n        for (var i = lengthDiff; i < largerCoefficients.length; i++) {\n            sumDiff[i] = AbstractGenericGF.addOrSubtract(smallerCoefficients[i - lengthDiff], largerCoefficients[i]);\n        }\n        return new GenericGFPoly(this.field, sumDiff);\n    };\n    GenericGFPoly.prototype.multiply = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n        }\n        if (this.isZero() || other.isZero()) {\n            return this.field.getZero();\n        }\n        var aCoefficients = this.coefficients;\n        var aLength = aCoefficients.length;\n        var bCoefficients = other.coefficients;\n        var bLength = bCoefficients.length;\n        var product = new Int32Array(aLength + bLength - 1);\n        var field = this.field;\n        for (var i = 0; i < aLength; i++) {\n            var aCoeff = aCoefficients[i];\n            for (var j = 0; j < bLength; j++) {\n                product[i + j] = AbstractGenericGF.addOrSubtract(product[i + j], field.multiply(aCoeff, bCoefficients[j]));\n            }\n        }\n        return new GenericGFPoly(field, product);\n    };\n    GenericGFPoly.prototype.multiplyScalar = function (scalar /*int*/) {\n        if (scalar === 0) {\n            return this.field.getZero();\n        }\n        if (scalar === 1) {\n            return this;\n        }\n        var size = this.coefficients.length;\n        var field = this.field;\n        var product = new Int32Array(size);\n        var coefficients = this.coefficients;\n        for (var i = 0; i < size; i++) {\n            product[i] = field.multiply(coefficients[i], scalar);\n        }\n        return new GenericGFPoly(field, product);\n    };\n    GenericGFPoly.prototype.multiplyByMonomial = function (degree /*int*/, coefficient /*int*/) {\n        if (degree < 0) {\n            throw new IllegalArgumentException();\n        }\n        if (coefficient === 0) {\n            return this.field.getZero();\n        }\n        var coefficients = this.coefficients;\n        var size = coefficients.length;\n        var product = new Int32Array(size + degree);\n        var field = this.field;\n        for (var i = 0; i < size; i++) {\n            product[i] = field.multiply(coefficients[i], coefficient);\n        }\n        return new GenericGFPoly(field, product);\n    };\n    GenericGFPoly.prototype.divide = function (other) {\n        if (!this.field.equals(other.field)) {\n            throw new IllegalArgumentException('GenericGFPolys do not have same GenericGF field');\n        }\n        if (other.isZero()) {\n            throw new IllegalArgumentException('Divide by 0');\n        }\n        var field = this.field;\n        var quotient = field.getZero();\n        var remainder = this;\n        var denominatorLeadingTerm = other.getCoefficient(other.getDegree());\n        var inverseDenominatorLeadingTerm = field.inverse(denominatorLeadingTerm);\n        while (remainder.getDegree() >= other.getDegree() && !remainder.isZero()) {\n            var degreeDifference = remainder.getDegree() - other.getDegree();\n            var scale = field.multiply(remainder.getCoefficient(remainder.getDegree()), inverseDenominatorLeadingTerm);\n            var term = other.multiplyByMonomial(degreeDifference, scale);\n            var iterationQuotient = field.buildMonomial(degreeDifference, scale);\n            quotient = quotient.addOrSubtract(iterationQuotient);\n            remainder = remainder.addOrSubtract(term);\n        }\n        return [quotient, remainder];\n    };\n    /*@Override*/\n    GenericGFPoly.prototype.toString = function () {\n        var result = '';\n        for (var degree = this.getDegree(); degree >= 0; degree--) {\n            var coefficient = this.getCoefficient(degree);\n            if (coefficient !== 0) {\n                if (coefficient < 0) {\n                    result += ' - ';\n                    coefficient = -coefficient;\n                }\n                else {\n                    if (result.length > 0) {\n                        result += ' + ';\n                    }\n                }\n                if (degree === 0 || coefficient !== 1) {\n                    var alphaPower = this.field.log(coefficient);\n                    if (alphaPower === 0) {\n                        result += '1';\n                    }\n                    else if (alphaPower === 1) {\n                        result += 'a';\n                    }\n                    else {\n                        result += 'a^';\n                        result += alphaPower;\n                    }\n                }\n                if (degree !== 0) {\n                    if (degree === 1) {\n                        result += 'x';\n                    }\n                    else {\n                        result += 'x^';\n                        result += degree;\n                    }\n                }\n            }\n        }\n        return result;\n    };\n    return GenericGFPoly;\n}());\nexport default GenericGFPoly;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,wBAAwB,MAAM,gCAAgC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,aAAe,YAAY;EAC3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,aAAaA,CAACC,KAAK,EAAEC,YAAY,EAAE;IACxC,IAAIA,YAAY,CAACC,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIJ,wBAAwB,CAAC,CAAC;IACxC;IACA,IAAI,CAACE,KAAK,GAAGA,KAAK;IAClB,IAAIG,kBAAkB,GAAGF,YAAY,CAACC,MAAM;IAC5C,IAAIC,kBAAkB,GAAG,CAAC,IAAIF,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;MACjD;MACA,IAAIG,YAAY,GAAG,CAAC;MACpB,OAAOA,YAAY,GAAGD,kBAAkB,IAAIF,YAAY,CAACG,YAAY,CAAC,KAAK,CAAC,EAAE;QAC1EA,YAAY,EAAE;MAClB;MACA,IAAIA,YAAY,KAAKD,kBAAkB,EAAE;QACrC,IAAI,CAACF,YAAY,GAAGI,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,MACI;QACD,IAAI,CAACL,YAAY,GAAG,IAAII,UAAU,CAACF,kBAAkB,GAAGC,YAAY,CAAC;QACrEP,MAAM,CAACU,SAAS,CAACN,YAAY,EAAEG,YAAY,EAAE,IAAI,CAACH,YAAY,EAAE,CAAC,EAAE,IAAI,CAACA,YAAY,CAACC,MAAM,CAAC;MAChG;IACJ,CAAC,MACI;MACD,IAAI,CAACD,YAAY,GAAGA,YAAY;IACpC;EACJ;EACAF,aAAa,CAACS,SAAS,CAACC,eAAe,GAAG,YAAY;IAClD,OAAO,IAAI,CAACR,YAAY;EAC5B,CAAC;EACD;AACJ;AACA;EACIF,aAAa,CAACS,SAAS,CAACE,SAAS,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACT,YAAY,CAACC,MAAM,GAAG,CAAC;EACvC,CAAC;EACD;AACJ;AACA;EACIH,aAAa,CAACS,SAAS,CAACG,MAAM,GAAG,YAAY;IACzC,OAAO,IAAI,CAACV,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC;EACrC,CAAC;EACD;AACJ;AACA;EACIF,aAAa,CAACS,SAAS,CAACI,cAAc,GAAG,UAAUC,MAAM,CAAC,SAAS;IAC/D,OAAO,IAAI,CAACZ,YAAY,CAAC,IAAI,CAACA,YAAY,CAACC,MAAM,GAAG,CAAC,GAAGW,MAAM,CAAC;EACnE,CAAC;EACD;AACJ;AACA;EACId,aAAa,CAACS,SAAS,CAACM,UAAU,GAAG,UAAUC,CAAC,CAAC,SAAS;IACtD,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT;MACA,OAAO,IAAI,CAACH,cAAc,CAAC,CAAC,CAAC;IACjC;IACA,IAAIX,YAAY,GAAG,IAAI,CAACA,YAAY;IACpC,IAAIe,MAAM;IACV,IAAID,CAAC,KAAK,CAAC,EAAE;MACT;MACAC,MAAM,GAAG,CAAC;MACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,QAAQ,GAAGjB,YAAY,CAACC,MAAM,EAAEe,CAAC,KAAKC,QAAQ,EAAED,CAAC,EAAE,EAAE;QACjE,IAAIE,WAAW,GAAGlB,YAAY,CAACgB,CAAC,CAAC;QACjCD,MAAM,GAAGpB,iBAAiB,CAACwB,aAAa,CAACJ,MAAM,EAAEG,WAAW,CAAC;MACjE;MACA,OAAOH,MAAM;IACjB;IACAA,MAAM,GAAGf,YAAY,CAAC,CAAC,CAAC;IACxB,IAAIoB,IAAI,GAAGpB,YAAY,CAACC,MAAM;IAC9B,IAAIF,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,IAAI,EAAEJ,CAAC,EAAE,EAAE;MAC3BD,MAAM,GAAGpB,iBAAiB,CAACwB,aAAa,CAACpB,KAAK,CAACsB,QAAQ,CAACP,CAAC,EAAEC,MAAM,CAAC,EAAEf,YAAY,CAACgB,CAAC,CAAC,CAAC;IACxF;IACA,OAAOD,MAAM;EACjB,CAAC;EACDjB,aAAa,CAACS,SAAS,CAACY,aAAa,GAAG,UAAUG,KAAK,EAAE;IACrD,IAAI,CAAC,IAAI,CAACvB,KAAK,CAACwB,MAAM,CAACD,KAAK,CAACvB,KAAK,CAAC,EAAE;MACjC,MAAM,IAAIF,wBAAwB,CAAC,iDAAiD,CAAC;IACzF;IACA,IAAI,IAAI,CAACa,MAAM,CAAC,CAAC,EAAE;MACf,OAAOY,KAAK;IAChB;IACA,IAAIA,KAAK,CAACZ,MAAM,CAAC,CAAC,EAAE;MAChB,OAAO,IAAI;IACf;IACA,IAAIc,mBAAmB,GAAG,IAAI,CAACxB,YAAY;IAC3C,IAAIyB,kBAAkB,GAAGH,KAAK,CAACtB,YAAY;IAC3C,IAAIwB,mBAAmB,CAACvB,MAAM,GAAGwB,kBAAkB,CAACxB,MAAM,EAAE;MACxD,IAAIyB,IAAI,GAAGF,mBAAmB;MAC9BA,mBAAmB,GAAGC,kBAAkB;MACxCA,kBAAkB,GAAGC,IAAI;IAC7B;IACA,IAAIC,OAAO,GAAG,IAAIvB,UAAU,CAACqB,kBAAkB,CAACxB,MAAM,CAAC;IACvD,IAAI2B,UAAU,GAAGH,kBAAkB,CAACxB,MAAM,GAAGuB,mBAAmB,CAACvB,MAAM;IACvE;IACAL,MAAM,CAACU,SAAS,CAACmB,kBAAkB,EAAE,CAAC,EAAEE,OAAO,EAAE,CAAC,EAAEC,UAAU,CAAC;IAC/D,KAAK,IAAIZ,CAAC,GAAGY,UAAU,EAAEZ,CAAC,GAAGS,kBAAkB,CAACxB,MAAM,EAAEe,CAAC,EAAE,EAAE;MACzDW,OAAO,CAACX,CAAC,CAAC,GAAGrB,iBAAiB,CAACwB,aAAa,CAACK,mBAAmB,CAACR,CAAC,GAAGY,UAAU,CAAC,EAAEH,kBAAkB,CAACT,CAAC,CAAC,CAAC;IAC5G;IACA,OAAO,IAAIlB,aAAa,CAAC,IAAI,CAACC,KAAK,EAAE4B,OAAO,CAAC;EACjD,CAAC;EACD7B,aAAa,CAACS,SAAS,CAACc,QAAQ,GAAG,UAAUC,KAAK,EAAE;IAChD,IAAI,CAAC,IAAI,CAACvB,KAAK,CAACwB,MAAM,CAACD,KAAK,CAACvB,KAAK,CAAC,EAAE;MACjC,MAAM,IAAIF,wBAAwB,CAAC,iDAAiD,CAAC;IACzF;IACA,IAAI,IAAI,CAACa,MAAM,CAAC,CAAC,IAAIY,KAAK,CAACZ,MAAM,CAAC,CAAC,EAAE;MACjC,OAAO,IAAI,CAACX,KAAK,CAAC8B,OAAO,CAAC,CAAC;IAC/B;IACA,IAAIC,aAAa,GAAG,IAAI,CAAC9B,YAAY;IACrC,IAAI+B,OAAO,GAAGD,aAAa,CAAC7B,MAAM;IAClC,IAAI+B,aAAa,GAAGV,KAAK,CAACtB,YAAY;IACtC,IAAIiC,OAAO,GAAGD,aAAa,CAAC/B,MAAM;IAClC,IAAIiC,OAAO,GAAG,IAAI9B,UAAU,CAAC2B,OAAO,GAAGE,OAAO,GAAG,CAAC,CAAC;IACnD,IAAIlC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,OAAO,EAAEf,CAAC,EAAE,EAAE;MAC9B,IAAImB,MAAM,GAAGL,aAAa,CAACd,CAAC,CAAC;MAC7B,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;QAC9BF,OAAO,CAAClB,CAAC,GAAGoB,CAAC,CAAC,GAAGzC,iBAAiB,CAACwB,aAAa,CAACe,OAAO,CAAClB,CAAC,GAAGoB,CAAC,CAAC,EAAErC,KAAK,CAACsB,QAAQ,CAACc,MAAM,EAAEH,aAAa,CAACI,CAAC,CAAC,CAAC,CAAC;MAC9G;IACJ;IACA,OAAO,IAAItC,aAAa,CAACC,KAAK,EAAEmC,OAAO,CAAC;EAC5C,CAAC;EACDpC,aAAa,CAACS,SAAS,CAAC8B,cAAc,GAAG,UAAUC,MAAM,CAAC,SAAS;IAC/D,IAAIA,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,IAAI,CAACvC,KAAK,CAAC8B,OAAO,CAAC,CAAC;IAC/B;IACA,IAAIS,MAAM,KAAK,CAAC,EAAE;MACd,OAAO,IAAI;IACf;IACA,IAAIlB,IAAI,GAAG,IAAI,CAACpB,YAAY,CAACC,MAAM;IACnC,IAAIF,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAImC,OAAO,GAAG,IAAI9B,UAAU,CAACgB,IAAI,CAAC;IAClC,IAAIpB,YAAY,GAAG,IAAI,CAACA,YAAY;IACpC,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,IAAI,EAAEJ,CAAC,EAAE,EAAE;MAC3BkB,OAAO,CAAClB,CAAC,CAAC,GAAGjB,KAAK,CAACsB,QAAQ,CAACrB,YAAY,CAACgB,CAAC,CAAC,EAAEsB,MAAM,CAAC;IACxD;IACA,OAAO,IAAIxC,aAAa,CAACC,KAAK,EAAEmC,OAAO,CAAC;EAC5C,CAAC;EACDpC,aAAa,CAACS,SAAS,CAACgC,kBAAkB,GAAG,UAAU3B,MAAM,CAAC,SAASM,WAAW,CAAC,SAAS;IACxF,IAAIN,MAAM,GAAG,CAAC,EAAE;MACZ,MAAM,IAAIf,wBAAwB,CAAC,CAAC;IACxC;IACA,IAAIqB,WAAW,KAAK,CAAC,EAAE;MACnB,OAAO,IAAI,CAACnB,KAAK,CAAC8B,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI7B,YAAY,GAAG,IAAI,CAACA,YAAY;IACpC,IAAIoB,IAAI,GAAGpB,YAAY,CAACC,MAAM;IAC9B,IAAIiC,OAAO,GAAG,IAAI9B,UAAU,CAACgB,IAAI,GAAGR,MAAM,CAAC;IAC3C,IAAIb,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,IAAI,EAAEJ,CAAC,EAAE,EAAE;MAC3BkB,OAAO,CAAClB,CAAC,CAAC,GAAGjB,KAAK,CAACsB,QAAQ,CAACrB,YAAY,CAACgB,CAAC,CAAC,EAAEE,WAAW,CAAC;IAC7D;IACA,OAAO,IAAIpB,aAAa,CAACC,KAAK,EAAEmC,OAAO,CAAC;EAC5C,CAAC;EACDpC,aAAa,CAACS,SAAS,CAACiC,MAAM,GAAG,UAAUlB,KAAK,EAAE;IAC9C,IAAI,CAAC,IAAI,CAACvB,KAAK,CAACwB,MAAM,CAACD,KAAK,CAACvB,KAAK,CAAC,EAAE;MACjC,MAAM,IAAIF,wBAAwB,CAAC,iDAAiD,CAAC;IACzF;IACA,IAAIyB,KAAK,CAACZ,MAAM,CAAC,CAAC,EAAE;MAChB,MAAM,IAAIb,wBAAwB,CAAC,aAAa,CAAC;IACrD;IACA,IAAIE,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI0C,QAAQ,GAAG1C,KAAK,CAAC8B,OAAO,CAAC,CAAC;IAC9B,IAAIa,SAAS,GAAG,IAAI;IACpB,IAAIC,sBAAsB,GAAGrB,KAAK,CAACX,cAAc,CAACW,KAAK,CAACb,SAAS,CAAC,CAAC,CAAC;IACpE,IAAImC,6BAA6B,GAAG7C,KAAK,CAAC8C,OAAO,CAACF,sBAAsB,CAAC;IACzE,OAAOD,SAAS,CAACjC,SAAS,CAAC,CAAC,IAAIa,KAAK,CAACb,SAAS,CAAC,CAAC,IAAI,CAACiC,SAAS,CAAChC,MAAM,CAAC,CAAC,EAAE;MACtE,IAAIoC,gBAAgB,GAAGJ,SAAS,CAACjC,SAAS,CAAC,CAAC,GAAGa,KAAK,CAACb,SAAS,CAAC,CAAC;MAChE,IAAIsC,KAAK,GAAGhD,KAAK,CAACsB,QAAQ,CAACqB,SAAS,CAAC/B,cAAc,CAAC+B,SAAS,CAACjC,SAAS,CAAC,CAAC,CAAC,EAAEmC,6BAA6B,CAAC;MAC1G,IAAII,IAAI,GAAG1B,KAAK,CAACiB,kBAAkB,CAACO,gBAAgB,EAAEC,KAAK,CAAC;MAC5D,IAAIE,iBAAiB,GAAGlD,KAAK,CAACmD,aAAa,CAACJ,gBAAgB,EAAEC,KAAK,CAAC;MACpEN,QAAQ,GAAGA,QAAQ,CAACtB,aAAa,CAAC8B,iBAAiB,CAAC;MACpDP,SAAS,GAAGA,SAAS,CAACvB,aAAa,CAAC6B,IAAI,CAAC;IAC7C;IACA,OAAO,CAACP,QAAQ,EAAEC,SAAS,CAAC;EAChC,CAAC;EACD;EACA5C,aAAa,CAACS,SAAS,CAAC4C,QAAQ,GAAG,YAAY;IAC3C,IAAIpC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIH,MAAM,GAAG,IAAI,CAACH,SAAS,CAAC,CAAC,EAAEG,MAAM,IAAI,CAAC,EAAEA,MAAM,EAAE,EAAE;MACvD,IAAIM,WAAW,GAAG,IAAI,CAACP,cAAc,CAACC,MAAM,CAAC;MAC7C,IAAIM,WAAW,KAAK,CAAC,EAAE;QACnB,IAAIA,WAAW,GAAG,CAAC,EAAE;UACjBH,MAAM,IAAI,KAAK;UACfG,WAAW,GAAG,CAACA,WAAW;QAC9B,CAAC,MACI;UACD,IAAIH,MAAM,CAACd,MAAM,GAAG,CAAC,EAAE;YACnBc,MAAM,IAAI,KAAK;UACnB;QACJ;QACA,IAAIH,MAAM,KAAK,CAAC,IAAIM,WAAW,KAAK,CAAC,EAAE;UACnC,IAAIkC,UAAU,GAAG,IAAI,CAACrD,KAAK,CAACsD,GAAG,CAACnC,WAAW,CAAC;UAC5C,IAAIkC,UAAU,KAAK,CAAC,EAAE;YAClBrC,MAAM,IAAI,GAAG;UACjB,CAAC,MACI,IAAIqC,UAAU,KAAK,CAAC,EAAE;YACvBrC,MAAM,IAAI,GAAG;UACjB,CAAC,MACI;YACDA,MAAM,IAAI,IAAI;YACdA,MAAM,IAAIqC,UAAU;UACxB;QACJ;QACA,IAAIxC,MAAM,KAAK,CAAC,EAAE;UACd,IAAIA,MAAM,KAAK,CAAC,EAAE;YACdG,MAAM,IAAI,GAAG;UACjB,CAAC,MACI;YACDA,MAAM,IAAI,IAAI;YACdA,MAAM,IAAIH,MAAM;UACpB;QACJ;MACJ;IACJ;IACA,OAAOG,MAAM;EACjB,CAAC;EACD,OAAOjB,aAAa;AACxB,CAAC,CAAC,CAAE;AACJ,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}