{"ast": null, "code": "import { warnOnce } from '@mui/x-internals/warning';\nimport { usePickerValue } from \"./usePickerValue.js\";\nimport { usePickerViews } from \"./usePickerViews.js\";\nimport { usePickerLayoutProps } from \"./usePickerLayoutProps.js\";\nimport { usePickerOwnerState } from \"./usePickerOwnerState.js\";\nexport const usePicker = ({\n  props,\n  valueManager,\n  valueType,\n  wrapperVariant,\n  additionalViewProps,\n  validator,\n  autoFocusView,\n  rendererInterceptor,\n  fieldRef\n}) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.renderInput != null) {\n      warnOnce(['MUI X: The `renderInput` prop has been removed in version 6.0 of the Date and Time Pickers.', 'You can replace it with the `textField` component slot in most cases.', 'For more information, please have a look at the migration guide (https://mui.com/x/migration/migration-pickers-v5/#input-renderer-required-in-v5).']);\n    }\n  }\n  const pickerValueResponse = usePickerValue({\n    props,\n    valueManager,\n    valueType,\n    wrapperVariant,\n    validator\n  });\n  const pickerViewsResponse = usePickerViews({\n    props,\n    additionalViewProps,\n    autoFocusView,\n    fieldRef,\n    propsFromPickerValue: pickerValueResponse.viewProps,\n    rendererInterceptor\n  });\n  const pickerLayoutResponse = usePickerLayoutProps({\n    props,\n    wrapperVariant,\n    propsFromPickerValue: pickerValueResponse.layoutProps,\n    propsFromPickerViews: pickerViewsResponse.layoutProps\n  });\n  const pickerOwnerState = usePickerOwnerState({\n    props,\n    pickerValueResponse\n  });\n  return {\n    // Picker value\n    open: pickerValueResponse.open,\n    actions: pickerValueResponse.actions,\n    fieldProps: pickerValueResponse.fieldProps,\n    // Picker views\n    renderCurrentView: pickerViewsResponse.renderCurrentView,\n    hasUIView: pickerViewsResponse.hasUIView,\n    shouldRestoreFocus: pickerViewsResponse.shouldRestoreFocus,\n    // Picker layout\n    layoutProps: pickerLayoutResponse.layoutProps,\n    // Picker context\n    contextValue: pickerValueResponse.contextValue,\n    // Picker owner state\n    ownerState: pickerOwnerState\n  };\n};", "map": {"version": 3, "names": ["warnOnce", "usePickerValue", "usePickerViews", "usePickerLayoutProps", "usePickerOwnerState", "usePicker", "props", "valueManager", "valueType", "wrapperVariant", "additionalViewProps", "validator", "autoFocusView", "rendererInterceptor", "fieldRef", "process", "env", "NODE_ENV", "renderInput", "pickerValueResponse", "pickerViewsResponse", "propsFromPickerValue", "viewProps", "pickerLayoutResponse", "layoutProps", "propsFromPickerViews", "pickerOwnerState", "open", "actions", "fieldProps", "renderCurrentView", "hasUIView", "shouldRestoreFocus", "contextValue", "ownerState"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePicker.js"], "sourcesContent": ["import { warnOnce } from '@mui/x-internals/warning';\nimport { usePickerValue } from \"./usePickerValue.js\";\nimport { usePickerViews } from \"./usePickerViews.js\";\nimport { usePickerLayoutProps } from \"./usePickerLayoutProps.js\";\nimport { usePickerOwnerState } from \"./usePickerOwnerState.js\";\nexport const usePicker = ({\n  props,\n  valueManager,\n  valueType,\n  wrapperVariant,\n  additionalViewProps,\n  validator,\n  autoFocusView,\n  rendererInterceptor,\n  fieldRef\n}) => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.renderInput != null) {\n      warnOnce(['MUI X: The `renderInput` prop has been removed in version 6.0 of the Date and Time Pickers.', 'You can replace it with the `textField` component slot in most cases.', 'For more information, please have a look at the migration guide (https://mui.com/x/migration/migration-pickers-v5/#input-renderer-required-in-v5).']);\n    }\n  }\n  const pickerValueResponse = usePickerValue({\n    props,\n    valueManager,\n    valueType,\n    wrapperVariant,\n    validator\n  });\n  const pickerViewsResponse = usePickerViews({\n    props,\n    additionalViewProps,\n    autoFocusView,\n    fieldRef,\n    propsFromPickerValue: pickerValueResponse.viewProps,\n    rendererInterceptor\n  });\n  const pickerLayoutResponse = usePickerLayoutProps({\n    props,\n    wrapperVariant,\n    propsFromPickerValue: pickerValueResponse.layoutProps,\n    propsFromPickerViews: pickerViewsResponse.layoutProps\n  });\n  const pickerOwnerState = usePickerOwnerState({\n    props,\n    pickerValueResponse\n  });\n  return {\n    // Picker value\n    open: pickerValueResponse.open,\n    actions: pickerValueResponse.actions,\n    fieldProps: pickerValueResponse.fieldProps,\n    // Picker views\n    renderCurrentView: pickerViewsResponse.renderCurrentView,\n    hasUIView: pickerViewsResponse.hasUIView,\n    shouldRestoreFocus: pickerViewsResponse.shouldRestoreFocus,\n    // Picker layout\n    layoutProps: pickerLayoutResponse.layoutProps,\n    // Picker context\n    contextValue: pickerValueResponse.contextValue,\n    // Picker owner state\n    ownerState: pickerOwnerState\n  };\n};"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,OAAO,MAAMC,SAAS,GAAGA,CAAC;EACxBC,KAAK;EACLC,YAAY;EACZC,SAAS;EACTC,cAAc;EACdC,mBAAmB;EACnBC,SAAS;EACTC,aAAa;EACbC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EACJ,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIX,KAAK,CAACY,WAAW,IAAI,IAAI,EAAE;MAC7BlB,QAAQ,CAAC,CAAC,6FAA6F,EAAE,uEAAuE,EAAE,oJAAoJ,CAAC,CAAC;IAC1U;EACF;EACA,MAAMmB,mBAAmB,GAAGlB,cAAc,CAAC;IACzCK,KAAK;IACLC,YAAY;IACZC,SAAS;IACTC,cAAc;IACdE;EACF,CAAC,CAAC;EACF,MAAMS,mBAAmB,GAAGlB,cAAc,CAAC;IACzCI,KAAK;IACLI,mBAAmB;IACnBE,aAAa;IACbE,QAAQ;IACRO,oBAAoB,EAAEF,mBAAmB,CAACG,SAAS;IACnDT;EACF,CAAC,CAAC;EACF,MAAMU,oBAAoB,GAAGpB,oBAAoB,CAAC;IAChDG,KAAK;IACLG,cAAc;IACdY,oBAAoB,EAAEF,mBAAmB,CAACK,WAAW;IACrDC,oBAAoB,EAAEL,mBAAmB,CAACI;EAC5C,CAAC,CAAC;EACF,MAAME,gBAAgB,GAAGtB,mBAAmB,CAAC;IAC3CE,KAAK;IACLa;EACF,CAAC,CAAC;EACF,OAAO;IACL;IACAQ,IAAI,EAAER,mBAAmB,CAACQ,IAAI;IAC9BC,OAAO,EAAET,mBAAmB,CAACS,OAAO;IACpCC,UAAU,EAAEV,mBAAmB,CAACU,UAAU;IAC1C;IACAC,iBAAiB,EAAEV,mBAAmB,CAACU,iBAAiB;IACxDC,SAAS,EAAEX,mBAAmB,CAACW,SAAS;IACxCC,kBAAkB,EAAEZ,mBAAmB,CAACY,kBAAkB;IAC1D;IACAR,WAAW,EAAED,oBAAoB,CAACC,WAAW;IAC7C;IACAS,YAAY,EAAEd,mBAAmB,CAACc,YAAY;IAC9C;IACAC,UAAU,EAAER;EACd,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}