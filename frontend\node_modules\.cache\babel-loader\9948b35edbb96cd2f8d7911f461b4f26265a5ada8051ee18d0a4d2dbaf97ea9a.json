{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport AbstractExpandedDecoder from './AbstractExpandedDecoder';\nvar AI01decoder = /** @class */function (_super) {\n  __extends(AI01decoder, _super);\n  function AI01decoder(information) {\n    return _super.call(this, information) || this;\n  }\n  AI01decoder.prototype.encodeCompressedGtin = function (buf, currentPos) {\n    buf.append('(01)');\n    var initialPosition = buf.length();\n    buf.append('9');\n    this.encodeCompressedGtinWithoutAI(buf, currentPos, initialPosition);\n  };\n  AI01decoder.prototype.encodeCompressedGtinWithoutAI = function (buf, currentPos, initialBufferPosition) {\n    for (var i = 0; i < 4; ++i) {\n      var currentBlock = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos + 10 * i, 10);\n      if (currentBlock / 100 === 0) {\n        buf.append('0');\n      }\n      if (currentBlock / 10 === 0) {\n        buf.append('0');\n      }\n      buf.append(currentBlock);\n    }\n    AI01decoder.appendCheckDigit(buf, initialBufferPosition);\n  };\n  AI01decoder.appendCheckDigit = function (buf, currentPos) {\n    var checkDigit = 0;\n    for (var i = 0; i < 13; i++) {\n      // let digit = buf.charAt(i + currentPos) - '0';\n      // To be checked\n      var digit = buf.charAt(i + currentPos).charCodeAt(0) - '0'.charCodeAt(0);\n      checkDigit += (i & 0x01) === 0 ? 3 * digit : digit;\n    }\n    checkDigit = 10 - checkDigit % 10;\n    if (checkDigit === 10) {\n      checkDigit = 0;\n    }\n    buf.append(checkDigit);\n  };\n  AI01decoder.GTIN_SIZE = 40;\n  return AI01decoder;\n}(AbstractExpandedDecoder);\nexport default AI01decoder;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "AbstractExpandedDecoder", "AI01decoder", "_super", "information", "call", "encodeCompressedGtin", "buf", "currentPos", "append", "initialPosition", "length", "encodeCompressedGtinWithoutAI", "initialBufferPosition", "i", "currentBlock", "getGeneralDecoder", "extractNumericValueFromBitArray", "appendCheckDigit", "checkDigit", "digit", "char<PERSON>t", "charCodeAt", "GTIN_SIZE"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/AI01decoder.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport AbstractExpandedDecoder from './AbstractExpandedDecoder';\nvar AI01decoder = /** @class */ (function (_super) {\n    __extends(AI01decoder, _super);\n    function AI01decoder(information) {\n        return _super.call(this, information) || this;\n    }\n    AI01decoder.prototype.encodeCompressedGtin = function (buf, currentPos) {\n        buf.append('(01)');\n        var initialPosition = buf.length();\n        buf.append('9');\n        this.encodeCompressedGtinWithoutAI(buf, currentPos, initialPosition);\n    };\n    AI01decoder.prototype.encodeCompressedGtinWithoutAI = function (buf, currentPos, initialBufferPosition) {\n        for (var i = 0; i < 4; ++i) {\n            var currentBlock = this.getGeneralDecoder().extractNumericValueFromBitArray(currentPos + 10 * i, 10);\n            if (currentBlock / 100 === 0) {\n                buf.append('0');\n            }\n            if (currentBlock / 10 === 0) {\n                buf.append('0');\n            }\n            buf.append(currentBlock);\n        }\n        AI01decoder.appendCheckDigit(buf, initialBufferPosition);\n    };\n    AI01decoder.appendCheckDigit = function (buf, currentPos) {\n        var checkDigit = 0;\n        for (var i = 0; i < 13; i++) {\n            // let digit = buf.charAt(i + currentPos) - '0';\n            // To be checked\n            var digit = buf.charAt(i + currentPos).charCodeAt(0) - '0'.charCodeAt(0);\n            checkDigit += (i & 0x01) === 0 ? 3 * digit : digit;\n        }\n        checkDigit = 10 - (checkDigit % 10);\n        if (checkDigit === 10) {\n            checkDigit = 0;\n        }\n        buf.append(checkDigit);\n    };\n    AI01decoder.GTIN_SIZE = 40;\n    return AI01decoder;\n}(AbstractExpandedDecoder));\nexport default AI01decoder;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,uBAAuB,MAAM,2BAA2B;AAC/D,IAAIC,WAAW,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC/ChB,SAAS,CAACe,WAAW,EAAEC,MAAM,CAAC;EAC9B,SAASD,WAAWA,CAACE,WAAW,EAAE;IAC9B,OAAOD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAED,WAAW,CAAC,IAAI,IAAI;EACjD;EACAF,WAAW,CAACH,SAAS,CAACO,oBAAoB,GAAG,UAAUC,GAAG,EAAEC,UAAU,EAAE;IACpED,GAAG,CAACE,MAAM,CAAC,MAAM,CAAC;IAClB,IAAIC,eAAe,GAAGH,GAAG,CAACI,MAAM,CAAC,CAAC;IAClCJ,GAAG,CAACE,MAAM,CAAC,GAAG,CAAC;IACf,IAAI,CAACG,6BAA6B,CAACL,GAAG,EAAEC,UAAU,EAAEE,eAAe,CAAC;EACxE,CAAC;EACDR,WAAW,CAACH,SAAS,CAACa,6BAA6B,GAAG,UAAUL,GAAG,EAAEC,UAAU,EAAEK,qBAAqB,EAAE;IACpG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;MACxB,IAAIC,YAAY,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAACC,+BAA+B,CAACT,UAAU,GAAG,EAAE,GAAGM,CAAC,EAAE,EAAE,CAAC;MACpG,IAAIC,YAAY,GAAG,GAAG,KAAK,CAAC,EAAE;QAC1BR,GAAG,CAACE,MAAM,CAAC,GAAG,CAAC;MACnB;MACA,IAAIM,YAAY,GAAG,EAAE,KAAK,CAAC,EAAE;QACzBR,GAAG,CAACE,MAAM,CAAC,GAAG,CAAC;MACnB;MACAF,GAAG,CAACE,MAAM,CAACM,YAAY,CAAC;IAC5B;IACAb,WAAW,CAACgB,gBAAgB,CAACX,GAAG,EAAEM,qBAAqB,CAAC;EAC5D,CAAC;EACDX,WAAW,CAACgB,gBAAgB,GAAG,UAAUX,GAAG,EAAEC,UAAU,EAAE;IACtD,IAAIW,UAAU,GAAG,CAAC;IAClB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MACzB;MACA;MACA,IAAIM,KAAK,GAAGb,GAAG,CAACc,MAAM,CAACP,CAAC,GAAGN,UAAU,CAAC,CAACc,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,CAACA,UAAU,CAAC,CAAC,CAAC;MACxEH,UAAU,IAAI,CAACL,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAGM,KAAK,GAAGA,KAAK;IACtD;IACAD,UAAU,GAAG,EAAE,GAAIA,UAAU,GAAG,EAAG;IACnC,IAAIA,UAAU,KAAK,EAAE,EAAE;MACnBA,UAAU,GAAG,CAAC;IAClB;IACAZ,GAAG,CAACE,MAAM,CAACU,UAAU,CAAC;EAC1B,CAAC;EACDjB,WAAW,CAACqB,SAAS,GAAG,EAAE;EAC1B,OAAOrB,WAAW;AACtB,CAAC,CAACD,uBAAuB,CAAE;AAC3B,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}