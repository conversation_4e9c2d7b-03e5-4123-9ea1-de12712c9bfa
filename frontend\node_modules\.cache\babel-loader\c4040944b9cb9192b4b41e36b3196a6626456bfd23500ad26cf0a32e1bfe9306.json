{"ast": null, "code": "/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport DecoderResult from '../../common/DecoderResult';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport IllegalStateException from '../../IllegalStateException';\nimport FormatException from '../../FormatException';\nimport StringUtils from '../../common/StringUtils';\nimport Integer from '../../util/Integer';\n// import java.util.Arrays;\nvar Table;\n(function (Table) {\n  Table[Table[\"UPPER\"] = 0] = \"UPPER\";\n  Table[Table[\"LOWER\"] = 1] = \"LOWER\";\n  Table[Table[\"MIXED\"] = 2] = \"MIXED\";\n  Table[Table[\"DIGIT\"] = 3] = \"DIGIT\";\n  Table[Table[\"PUNCT\"] = 4] = \"PUNCT\";\n  Table[Table[\"BINARY\"] = 5] = \"BINARY\";\n})(Table || (Table = {}));\n/**\n * <p>The main class which implements Aztec Code decoding -- as opposed to locating and extracting\n * the Aztec Code from an image.</p>\n *\n * <AUTHOR> Olivier\n */\nvar Decoder = /** @class */function () {\n  function Decoder() {}\n  Decoder.prototype.decode = function (detectorResult) {\n    this.ddata = detectorResult;\n    var matrix = detectorResult.getBits();\n    var rawbits = this.extractBits(matrix);\n    var correctedBits = this.correctBits(rawbits);\n    var rawBytes = Decoder.convertBoolArrayToByteArray(correctedBits);\n    var result = Decoder.getEncodedData(correctedBits);\n    var decoderResult = new DecoderResult(rawBytes, result, null, null);\n    decoderResult.setNumBits(correctedBits.length);\n    return decoderResult;\n  };\n  // This method is used for testing the high-level encoder\n  Decoder.highLevelDecode = function (correctedBits) {\n    return this.getEncodedData(correctedBits);\n  };\n  /**\n   * Gets the string encoded in the aztec code bits\n   *\n   * @return the decoded string\n   */\n  Decoder.getEncodedData = function (correctedBits) {\n    var endIndex = correctedBits.length;\n    var latchTable = Table.UPPER; // table most recently latched to\n    var shiftTable = Table.UPPER; // table to use for the next read\n    var result = '';\n    var index = 0;\n    while (index < endIndex) {\n      if (shiftTable === Table.BINARY) {\n        if (endIndex - index < 5) {\n          break;\n        }\n        var length_1 = Decoder.readCode(correctedBits, index, 5);\n        index += 5;\n        if (length_1 === 0) {\n          if (endIndex - index < 11) {\n            break;\n          }\n          length_1 = Decoder.readCode(correctedBits, index, 11) + 31;\n          index += 11;\n        }\n        for (var charCount = 0; charCount < length_1; charCount++) {\n          if (endIndex - index < 8) {\n            index = endIndex; // Force outer loop to exit\n            break;\n          }\n          var code = Decoder.readCode(correctedBits, index, 8);\n          result += /*(char)*/StringUtils.castAsNonUtf8Char(code);\n          index += 8;\n        }\n        // Go back to whatever mode we had been in\n        shiftTable = latchTable;\n      } else {\n        var size = shiftTable === Table.DIGIT ? 4 : 5;\n        if (endIndex - index < size) {\n          break;\n        }\n        var code = Decoder.readCode(correctedBits, index, size);\n        index += size;\n        var str = Decoder.getCharacter(shiftTable, code);\n        if (str.startsWith('CTRL_')) {\n          // Table changes\n          // ISO/IEC 24778:2008 prescribes ending a shift sequence in the mode from which it was invoked.\n          // That's including when that mode is a shift.\n          // Our test case dlusbs.png for issue #642 exercises that.\n          latchTable = shiftTable; // Latch the current mode, so as to return to Upper after U/S B/S\n          shiftTable = Decoder.getTable(str.charAt(5));\n          if (str.charAt(6) === 'L') {\n            latchTable = shiftTable;\n          }\n        } else {\n          result += str;\n          // Go back to whatever mode we had been in\n          shiftTable = latchTable;\n        }\n      }\n    }\n    return result;\n  };\n  /**\n   * gets the table corresponding to the char passed\n   */\n  Decoder.getTable = function (t) {\n    switch (t) {\n      case 'L':\n        return Table.LOWER;\n      case 'P':\n        return Table.PUNCT;\n      case 'M':\n        return Table.MIXED;\n      case 'D':\n        return Table.DIGIT;\n      case 'B':\n        return Table.BINARY;\n      case 'U':\n      default:\n        return Table.UPPER;\n    }\n  };\n  /**\n   * Gets the character (or string) corresponding to the passed code in the given table\n   *\n   * @param table the table used\n   * @param code the code of the character\n   */\n  Decoder.getCharacter = function (table, code) {\n    switch (table) {\n      case Table.UPPER:\n        return Decoder.UPPER_TABLE[code];\n      case Table.LOWER:\n        return Decoder.LOWER_TABLE[code];\n      case Table.MIXED:\n        return Decoder.MIXED_TABLE[code];\n      case Table.PUNCT:\n        return Decoder.PUNCT_TABLE[code];\n      case Table.DIGIT:\n        return Decoder.DIGIT_TABLE[code];\n      default:\n        // Should not reach here.\n        throw new IllegalStateException('Bad table');\n    }\n  };\n  /**\n   * <p>Performs RS error correction on an array of bits.</p>\n   *\n   * @return the corrected array\n   * @throws FormatException if the input contains too many errors\n   */\n  Decoder.prototype.correctBits = function (rawbits) {\n    var gf;\n    var codewordSize;\n    if (this.ddata.getNbLayers() <= 2) {\n      codewordSize = 6;\n      gf = GenericGF.AZTEC_DATA_6;\n    } else if (this.ddata.getNbLayers() <= 8) {\n      codewordSize = 8;\n      gf = GenericGF.AZTEC_DATA_8;\n    } else if (this.ddata.getNbLayers() <= 22) {\n      codewordSize = 10;\n      gf = GenericGF.AZTEC_DATA_10;\n    } else {\n      codewordSize = 12;\n      gf = GenericGF.AZTEC_DATA_12;\n    }\n    var numDataCodewords = this.ddata.getNbDatablocks();\n    var numCodewords = rawbits.length / codewordSize;\n    if (numCodewords < numDataCodewords) {\n      throw new FormatException();\n    }\n    var offset = rawbits.length % codewordSize;\n    var dataWords = new Int32Array(numCodewords);\n    for (var i = 0; i < numCodewords; i++, offset += codewordSize) {\n      dataWords[i] = Decoder.readCode(rawbits, offset, codewordSize);\n    }\n    try {\n      var rsDecoder = new ReedSolomonDecoder(gf);\n      rsDecoder.decode(dataWords, numCodewords - numDataCodewords);\n    } catch (ex) {\n      throw new FormatException(ex);\n    }\n    // Now perform the unstuffing operation.\n    // First, count how many bits are going to be thrown out as stuffing\n    var mask = (1 << codewordSize) - 1;\n    var stuffedBits = 0;\n    for (var i = 0; i < numDataCodewords; i++) {\n      var dataWord = dataWords[i];\n      if (dataWord === 0 || dataWord === mask) {\n        throw new FormatException();\n      } else if (dataWord === 1 || dataWord === mask - 1) {\n        stuffedBits++;\n      }\n    }\n    // Now, actually unpack the bits and remove the stuffing\n    var correctedBits = new Array(numDataCodewords * codewordSize - stuffedBits);\n    var index = 0;\n    for (var i = 0; i < numDataCodewords; i++) {\n      var dataWord = dataWords[i];\n      if (dataWord === 1 || dataWord === mask - 1) {\n        // next codewordSize-1 bits are all zeros or all ones\n        correctedBits.fill(dataWord > 1, index, index + codewordSize - 1);\n        // Arrays.fill(correctedBits, index, index + codewordSize - 1, dataWord > 1);\n        index += codewordSize - 1;\n      } else {\n        for (var bit = codewordSize - 1; bit >= 0; --bit) {\n          correctedBits[index++] = (dataWord & 1 << bit) !== 0;\n        }\n      }\n    }\n    return correctedBits;\n  };\n  /**\n   * Gets the array of bits from an Aztec Code matrix\n   *\n   * @return the array of bits\n   */\n  Decoder.prototype.extractBits = function (matrix) {\n    var compact = this.ddata.isCompact();\n    var layers = this.ddata.getNbLayers();\n    var baseMatrixSize = (compact ? 11 : 14) + layers * 4; // not including alignment lines\n    var alignmentMap = new Int32Array(baseMatrixSize);\n    var rawbits = new Array(this.totalBitsInLayer(layers, compact));\n    if (compact) {\n      for (var i = 0; i < alignmentMap.length; i++) {\n        alignmentMap[i] = i;\n      }\n    } else {\n      var matrixSize = baseMatrixSize + 1 + 2 * Integer.truncDivision(Integer.truncDivision(baseMatrixSize, 2) - 1, 15);\n      var origCenter = baseMatrixSize / 2;\n      var center = Integer.truncDivision(matrixSize, 2);\n      for (var i = 0; i < origCenter; i++) {\n        var newOffset = i + Integer.truncDivision(i, 15);\n        alignmentMap[origCenter - i - 1] = center - newOffset - 1;\n        alignmentMap[origCenter + i] = center + newOffset + 1;\n      }\n    }\n    for (var i = 0, rowOffset = 0; i < layers; i++) {\n      var rowSize = (layers - i) * 4 + (compact ? 9 : 12);\n      // The top-left most point of this layer is <low, low> (not including alignment lines)\n      var low = i * 2;\n      // The bottom-right most point of this layer is <high, high> (not including alignment lines)\n      var high = baseMatrixSize - 1 - low;\n      // We pull bits from the two 2 x rowSize columns and two rowSize x 2 rows\n      for (var j = 0; j < rowSize; j++) {\n        var columnOffset = j * 2;\n        for (var k = 0; k < 2; k++) {\n          // left column\n          rawbits[rowOffset + columnOffset + k] = matrix.get(alignmentMap[low + k], alignmentMap[low + j]);\n          // bottom row\n          rawbits[rowOffset + 2 * rowSize + columnOffset + k] = matrix.get(alignmentMap[low + j], alignmentMap[high - k]);\n          // right column\n          rawbits[rowOffset + 4 * rowSize + columnOffset + k] = matrix.get(alignmentMap[high - k], alignmentMap[high - j]);\n          // top row\n          rawbits[rowOffset + 6 * rowSize + columnOffset + k] = matrix.get(alignmentMap[high - j], alignmentMap[low + k]);\n        }\n      }\n      rowOffset += rowSize * 8;\n    }\n    return rawbits;\n  };\n  /**\n   * Reads a code of given length and at given index in an array of bits\n   */\n  Decoder.readCode = function (rawbits, startIndex, length) {\n    var res = 0;\n    for (var i = startIndex; i < startIndex + length; i++) {\n      res <<= 1;\n      if (rawbits[i]) {\n        res |= 0x01;\n      }\n    }\n    return res;\n  };\n  /**\n   * Reads a code of length 8 in an array of bits, padding with zeros\n   */\n  Decoder.readByte = function (rawbits, startIndex) {\n    var n = rawbits.length - startIndex;\n    if (n >= 8) {\n      return Decoder.readCode(rawbits, startIndex, 8);\n    }\n    return Decoder.readCode(rawbits, startIndex, n) << 8 - n;\n  };\n  /**\n   * Packs a bit array into bytes, most significant bit first\n   */\n  Decoder.convertBoolArrayToByteArray = function (boolArr) {\n    var byteArr = new Uint8Array((boolArr.length + 7) / 8);\n    for (var i = 0; i < byteArr.length; i++) {\n      byteArr[i] = Decoder.readByte(boolArr, 8 * i);\n    }\n    return byteArr;\n  };\n  Decoder.prototype.totalBitsInLayer = function (layers, compact) {\n    return ((compact ? 88 : 112) + 16 * layers) * layers;\n  };\n  Decoder.UPPER_TABLE = ['CTRL_PS', ' ', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'CTRL_LL', 'CTRL_ML', 'CTRL_DL', 'CTRL_BS'];\n  Decoder.LOWER_TABLE = ['CTRL_PS', ' ', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'CTRL_US', 'CTRL_ML', 'CTRL_DL', 'CTRL_BS'];\n  Decoder.MIXED_TABLE = ['CTRL_PS', ' ', '\\x01', '\\x02', '\\x03', '\\x04', '\\x05', '\\x06', '\\x07', '\\b', '\\t', '\\n', '\\x0b', '\\f', '\\r', '\\x1b', '\\x1c', '\\x1d', '\\x1e', '\\x1f', '@', '\\\\', '^', '_', '`', '|', '~', '\\x7f', 'CTRL_LL', 'CTRL_UL', 'CTRL_PL', 'CTRL_BS'];\n  Decoder.PUNCT_TABLE = ['', '\\r', '\\r\\n', '. ', ', ', ': ', '!', '\"', '#', '$', '%', '&', '\\'', '(', ')', '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?', '[', ']', '{', '}', 'CTRL_UL'];\n  Decoder.DIGIT_TABLE = ['CTRL_PS', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ',', '.', 'CTRL_UL', 'CTRL_US'];\n  return Decoder;\n}();\nexport default Decoder;", "map": {"version": 3, "names": ["DecoderResult", "GenericGF", "ReedSolomonDecoder", "IllegalStateException", "FormatException", "StringUtils", "Integer", "Table", "Decoder", "prototype", "decode", "detectorResult", "ddata", "matrix", "getBits", "rawbits", "extractBits", "correctedBits", "correctBits", "rawBytes", "convertBoolArrayToByteArray", "result", "getEncodedData", "decoderResult", "setNumBits", "length", "highLevelDecode", "endIndex", "latchTable", "UPPER", "shiftTable", "index", "BINARY", "length_1", "readCode", "charCount", "code", "castAsNonUtf8Char", "size", "DIGIT", "str", "getCharacter", "startsWith", "getTable", "char<PERSON>t", "t", "LOWER", "PUNCT", "MIXED", "table", "UPPER_TABLE", "LOWER_TABLE", "MIXED_TABLE", "PUNCT_TABLE", "DIGIT_TABLE", "gf", "codewordSize", "getNbLayers", "AZTEC_DATA_6", "AZTEC_DATA_8", "AZTEC_DATA_10", "AZTEC_DATA_12", "numDataCodewords", "getNbDatablocks", "numCodewords", "offset", "dataWords", "Int32Array", "i", "rsDecoder", "ex", "mask", "stuffedBits", "dataWord", "Array", "fill", "bit", "compact", "isCompact", "layers", "baseMatrixSize", "alignmentMap", "totalBitsInLayer", "matrixSize", "truncDivision", "origCenter", "center", "newOffset", "rowOffset", "rowSize", "low", "high", "j", "columnOffset", "k", "get", "startIndex", "res", "readByte", "n", "boolArr", "byteArr", "Uint8Array"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/decoder/Decoder.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport DecoderResult from '../../common/DecoderResult';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport IllegalStateException from '../../IllegalStateException';\nimport FormatException from '../../FormatException';\nimport StringUtils from '../../common/StringUtils';\nimport Integer from '../../util/Integer';\n// import java.util.Arrays;\nvar Table;\n(function (Table) {\n    Table[Table[\"UPPER\"] = 0] = \"UPPER\";\n    Table[Table[\"LOWER\"] = 1] = \"LOWER\";\n    Table[Table[\"MIXED\"] = 2] = \"MIXED\";\n    Table[Table[\"DIGIT\"] = 3] = \"DIGIT\";\n    Table[Table[\"PUNCT\"] = 4] = \"PUNCT\";\n    Table[Table[\"BINARY\"] = 5] = \"BINARY\";\n})(Table || (Table = {}));\n/**\n * <p>The main class which implements Aztec Code decoding -- as opposed to locating and extracting\n * the Aztec Code from an image.</p>\n *\n * <AUTHOR> Olivier\n */\nvar Decoder = /** @class */ (function () {\n    function Decoder() {\n    }\n    Decoder.prototype.decode = function (detectorResult) {\n        this.ddata = detectorResult;\n        var matrix = detectorResult.getBits();\n        var rawbits = this.extractBits(matrix);\n        var correctedBits = this.correctBits(rawbits);\n        var rawBytes = Decoder.convertBoolArrayToByteArray(correctedBits);\n        var result = Decoder.getEncodedData(correctedBits);\n        var decoderResult = new DecoderResult(rawBytes, result, null, null);\n        decoderResult.setNumBits(correctedBits.length);\n        return decoderResult;\n    };\n    // This method is used for testing the high-level encoder\n    Decoder.highLevelDecode = function (correctedBits) {\n        return this.getEncodedData(correctedBits);\n    };\n    /**\n     * Gets the string encoded in the aztec code bits\n     *\n     * @return the decoded string\n     */\n    Decoder.getEncodedData = function (correctedBits) {\n        var endIndex = correctedBits.length;\n        var latchTable = Table.UPPER; // table most recently latched to\n        var shiftTable = Table.UPPER; // table to use for the next read\n        var result = '';\n        var index = 0;\n        while (index < endIndex) {\n            if (shiftTable === Table.BINARY) {\n                if (endIndex - index < 5) {\n                    break;\n                }\n                var length_1 = Decoder.readCode(correctedBits, index, 5);\n                index += 5;\n                if (length_1 === 0) {\n                    if (endIndex - index < 11) {\n                        break;\n                    }\n                    length_1 = Decoder.readCode(correctedBits, index, 11) + 31;\n                    index += 11;\n                }\n                for (var charCount = 0; charCount < length_1; charCount++) {\n                    if (endIndex - index < 8) {\n                        index = endIndex; // Force outer loop to exit\n                        break;\n                    }\n                    var code = Decoder.readCode(correctedBits, index, 8);\n                    result += /*(char)*/ StringUtils.castAsNonUtf8Char(code);\n                    index += 8;\n                }\n                // Go back to whatever mode we had been in\n                shiftTable = latchTable;\n            }\n            else {\n                var size = shiftTable === Table.DIGIT ? 4 : 5;\n                if (endIndex - index < size) {\n                    break;\n                }\n                var code = Decoder.readCode(correctedBits, index, size);\n                index += size;\n                var str = Decoder.getCharacter(shiftTable, code);\n                if (str.startsWith('CTRL_')) {\n                    // Table changes\n                    // ISO/IEC 24778:2008 prescribes ending a shift sequence in the mode from which it was invoked.\n                    // That's including when that mode is a shift.\n                    // Our test case dlusbs.png for issue #642 exercises that.\n                    latchTable = shiftTable; // Latch the current mode, so as to return to Upper after U/S B/S\n                    shiftTable = Decoder.getTable(str.charAt(5));\n                    if (str.charAt(6) === 'L') {\n                        latchTable = shiftTable;\n                    }\n                }\n                else {\n                    result += str;\n                    // Go back to whatever mode we had been in\n                    shiftTable = latchTable;\n                }\n            }\n        }\n        return result;\n    };\n    /**\n     * gets the table corresponding to the char passed\n     */\n    Decoder.getTable = function (t) {\n        switch (t) {\n            case 'L':\n                return Table.LOWER;\n            case 'P':\n                return Table.PUNCT;\n            case 'M':\n                return Table.MIXED;\n            case 'D':\n                return Table.DIGIT;\n            case 'B':\n                return Table.BINARY;\n            case 'U':\n            default:\n                return Table.UPPER;\n        }\n    };\n    /**\n     * Gets the character (or string) corresponding to the passed code in the given table\n     *\n     * @param table the table used\n     * @param code the code of the character\n     */\n    Decoder.getCharacter = function (table, code) {\n        switch (table) {\n            case Table.UPPER:\n                return Decoder.UPPER_TABLE[code];\n            case Table.LOWER:\n                return Decoder.LOWER_TABLE[code];\n            case Table.MIXED:\n                return Decoder.MIXED_TABLE[code];\n            case Table.PUNCT:\n                return Decoder.PUNCT_TABLE[code];\n            case Table.DIGIT:\n                return Decoder.DIGIT_TABLE[code];\n            default:\n                // Should not reach here.\n                throw new IllegalStateException('Bad table');\n        }\n    };\n    /**\n     * <p>Performs RS error correction on an array of bits.</p>\n     *\n     * @return the corrected array\n     * @throws FormatException if the input contains too many errors\n     */\n    Decoder.prototype.correctBits = function (rawbits) {\n        var gf;\n        var codewordSize;\n        if (this.ddata.getNbLayers() <= 2) {\n            codewordSize = 6;\n            gf = GenericGF.AZTEC_DATA_6;\n        }\n        else if (this.ddata.getNbLayers() <= 8) {\n            codewordSize = 8;\n            gf = GenericGF.AZTEC_DATA_8;\n        }\n        else if (this.ddata.getNbLayers() <= 22) {\n            codewordSize = 10;\n            gf = GenericGF.AZTEC_DATA_10;\n        }\n        else {\n            codewordSize = 12;\n            gf = GenericGF.AZTEC_DATA_12;\n        }\n        var numDataCodewords = this.ddata.getNbDatablocks();\n        var numCodewords = rawbits.length / codewordSize;\n        if (numCodewords < numDataCodewords) {\n            throw new FormatException();\n        }\n        var offset = rawbits.length % codewordSize;\n        var dataWords = new Int32Array(numCodewords);\n        for (var i = 0; i < numCodewords; i++, offset += codewordSize) {\n            dataWords[i] = Decoder.readCode(rawbits, offset, codewordSize);\n        }\n        try {\n            var rsDecoder = new ReedSolomonDecoder(gf);\n            rsDecoder.decode(dataWords, numCodewords - numDataCodewords);\n        }\n        catch (ex) {\n            throw new FormatException(ex);\n        }\n        // Now perform the unstuffing operation.\n        // First, count how many bits are going to be thrown out as stuffing\n        var mask = (1 << codewordSize) - 1;\n        var stuffedBits = 0;\n        for (var i = 0; i < numDataCodewords; i++) {\n            var dataWord = dataWords[i];\n            if (dataWord === 0 || dataWord === mask) {\n                throw new FormatException();\n            }\n            else if (dataWord === 1 || dataWord === mask - 1) {\n                stuffedBits++;\n            }\n        }\n        // Now, actually unpack the bits and remove the stuffing\n        var correctedBits = new Array(numDataCodewords * codewordSize - stuffedBits);\n        var index = 0;\n        for (var i = 0; i < numDataCodewords; i++) {\n            var dataWord = dataWords[i];\n            if (dataWord === 1 || dataWord === mask - 1) {\n                // next codewordSize-1 bits are all zeros or all ones\n                correctedBits.fill(dataWord > 1, index, index + codewordSize - 1);\n                // Arrays.fill(correctedBits, index, index + codewordSize - 1, dataWord > 1);\n                index += codewordSize - 1;\n            }\n            else {\n                for (var bit = codewordSize - 1; bit >= 0; --bit) {\n                    correctedBits[index++] = (dataWord & (1 << bit)) !== 0;\n                }\n            }\n        }\n        return correctedBits;\n    };\n    /**\n     * Gets the array of bits from an Aztec Code matrix\n     *\n     * @return the array of bits\n     */\n    Decoder.prototype.extractBits = function (matrix) {\n        var compact = this.ddata.isCompact();\n        var layers = this.ddata.getNbLayers();\n        var baseMatrixSize = (compact ? 11 : 14) + layers * 4; // not including alignment lines\n        var alignmentMap = new Int32Array(baseMatrixSize);\n        var rawbits = new Array(this.totalBitsInLayer(layers, compact));\n        if (compact) {\n            for (var i = 0; i < alignmentMap.length; i++) {\n                alignmentMap[i] = i;\n            }\n        }\n        else {\n            var matrixSize = baseMatrixSize + 1 + 2 * Integer.truncDivision((Integer.truncDivision(baseMatrixSize, 2) - 1), 15);\n            var origCenter = baseMatrixSize / 2;\n            var center = Integer.truncDivision(matrixSize, 2);\n            for (var i = 0; i < origCenter; i++) {\n                var newOffset = i + Integer.truncDivision(i, 15);\n                alignmentMap[origCenter - i - 1] = center - newOffset - 1;\n                alignmentMap[origCenter + i] = center + newOffset + 1;\n            }\n        }\n        for (var i = 0, rowOffset = 0; i < layers; i++) {\n            var rowSize = (layers - i) * 4 + (compact ? 9 : 12);\n            // The top-left most point of this layer is <low, low> (not including alignment lines)\n            var low = i * 2;\n            // The bottom-right most point of this layer is <high, high> (not including alignment lines)\n            var high = baseMatrixSize - 1 - low;\n            // We pull bits from the two 2 x rowSize columns and two rowSize x 2 rows\n            for (var j = 0; j < rowSize; j++) {\n                var columnOffset = j * 2;\n                for (var k = 0; k < 2; k++) {\n                    // left column\n                    rawbits[rowOffset + columnOffset + k] =\n                        matrix.get(alignmentMap[low + k], alignmentMap[low + j]);\n                    // bottom row\n                    rawbits[rowOffset + 2 * rowSize + columnOffset + k] =\n                        matrix.get(alignmentMap[low + j], alignmentMap[high - k]);\n                    // right column\n                    rawbits[rowOffset + 4 * rowSize + columnOffset + k] =\n                        matrix.get(alignmentMap[high - k], alignmentMap[high - j]);\n                    // top row\n                    rawbits[rowOffset + 6 * rowSize + columnOffset + k] =\n                        matrix.get(alignmentMap[high - j], alignmentMap[low + k]);\n                }\n            }\n            rowOffset += rowSize * 8;\n        }\n        return rawbits;\n    };\n    /**\n     * Reads a code of given length and at given index in an array of bits\n     */\n    Decoder.readCode = function (rawbits, startIndex, length) {\n        var res = 0;\n        for (var i = startIndex; i < startIndex + length; i++) {\n            res <<= 1;\n            if (rawbits[i]) {\n                res |= 0x01;\n            }\n        }\n        return res;\n    };\n    /**\n     * Reads a code of length 8 in an array of bits, padding with zeros\n     */\n    Decoder.readByte = function (rawbits, startIndex) {\n        var n = rawbits.length - startIndex;\n        if (n >= 8) {\n            return Decoder.readCode(rawbits, startIndex, 8);\n        }\n        return Decoder.readCode(rawbits, startIndex, n) << (8 - n);\n    };\n    /**\n     * Packs a bit array into bytes, most significant bit first\n     */\n    Decoder.convertBoolArrayToByteArray = function (boolArr) {\n        var byteArr = new Uint8Array((boolArr.length + 7) / 8);\n        for (var i = 0; i < byteArr.length; i++) {\n            byteArr[i] = Decoder.readByte(boolArr, 8 * i);\n        }\n        return byteArr;\n    };\n    Decoder.prototype.totalBitsInLayer = function (layers, compact) {\n        return ((compact ? 88 : 112) + 16 * layers) * layers;\n    };\n    Decoder.UPPER_TABLE = [\n        'CTRL_PS', ' ', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',\n        'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'CTRL_LL', 'CTRL_ML', 'CTRL_DL', 'CTRL_BS'\n    ];\n    Decoder.LOWER_TABLE = [\n        'CTRL_PS', ' ', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p',\n        'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', 'CTRL_US', 'CTRL_ML', 'CTRL_DL', 'CTRL_BS'\n    ];\n    Decoder.MIXED_TABLE = [\n        'CTRL_PS', ' ', '\\x01', '\\x02', '\\x03', '\\x04', '\\x05', '\\x06', '\\x07', '\\b', '\\t', '\\n',\n        '\\x0b', '\\f', '\\r', '\\x1b', '\\x1c', '\\x1d', '\\x1e', '\\x1f', '@', '\\\\', '^', '_',\n        '`', '|', '~', '\\x7f', 'CTRL_LL', 'CTRL_UL', 'CTRL_PL', 'CTRL_BS'\n    ];\n    Decoder.PUNCT_TABLE = [\n        '', '\\r', '\\r\\n', '. ', ', ', ': ', '!', '\"', '#', '$', '%', '&', '\\'', '(', ')',\n        '*', '+', ',', '-', '.', '/', ':', ';', '<', '=', '>', '?', '[', ']', '{', '}', 'CTRL_UL'\n    ];\n    Decoder.DIGIT_TABLE = [\n        'CTRL_PS', ' ', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ',', '.', 'CTRL_UL', 'CTRL_US'\n    ];\n    return Decoder;\n}());\nexport default Decoder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,aAAa,MAAM,4BAA4B;AACtD,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,qBAAqB,MAAM,6BAA6B;AAC/D,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,OAAO,MAAM,oBAAoB;AACxC;AACA,IAAIC,KAAK;AACT,CAAC,UAAUA,KAAK,EAAE;EACdA,KAAK,CAACA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnCA,KAAK,CAACA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnCA,KAAK,CAACA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnCA,KAAK,CAACA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnCA,KAAK,CAACA,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EACnCA,KAAK,CAACA,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AACzC,CAAC,EAAEA,KAAK,KAAKA,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG,CACnB;EACAA,OAAO,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,cAAc,EAAE;IACjD,IAAI,CAACC,KAAK,GAAGD,cAAc;IAC3B,IAAIE,MAAM,GAAGF,cAAc,CAACG,OAAO,CAAC,CAAC;IACrC,IAAIC,OAAO,GAAG,IAAI,CAACC,WAAW,CAACH,MAAM,CAAC;IACtC,IAAII,aAAa,GAAG,IAAI,CAACC,WAAW,CAACH,OAAO,CAAC;IAC7C,IAAII,QAAQ,GAAGX,OAAO,CAACY,2BAA2B,CAACH,aAAa,CAAC;IACjE,IAAII,MAAM,GAAGb,OAAO,CAACc,cAAc,CAACL,aAAa,CAAC;IAClD,IAAIM,aAAa,GAAG,IAAIvB,aAAa,CAACmB,QAAQ,EAAEE,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;IACnEE,aAAa,CAACC,UAAU,CAACP,aAAa,CAACQ,MAAM,CAAC;IAC9C,OAAOF,aAAa;EACxB,CAAC;EACD;EACAf,OAAO,CAACkB,eAAe,GAAG,UAAUT,aAAa,EAAE;IAC/C,OAAO,IAAI,CAACK,cAAc,CAACL,aAAa,CAAC;EAC7C,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIT,OAAO,CAACc,cAAc,GAAG,UAAUL,aAAa,EAAE;IAC9C,IAAIU,QAAQ,GAAGV,aAAa,CAACQ,MAAM;IACnC,IAAIG,UAAU,GAAGrB,KAAK,CAACsB,KAAK,CAAC,CAAC;IAC9B,IAAIC,UAAU,GAAGvB,KAAK,CAACsB,KAAK,CAAC,CAAC;IAC9B,IAAIR,MAAM,GAAG,EAAE;IACf,IAAIU,KAAK,GAAG,CAAC;IACb,OAAOA,KAAK,GAAGJ,QAAQ,EAAE;MACrB,IAAIG,UAAU,KAAKvB,KAAK,CAACyB,MAAM,EAAE;QAC7B,IAAIL,QAAQ,GAAGI,KAAK,GAAG,CAAC,EAAE;UACtB;QACJ;QACA,IAAIE,QAAQ,GAAGzB,OAAO,CAAC0B,QAAQ,CAACjB,aAAa,EAAEc,KAAK,EAAE,CAAC,CAAC;QACxDA,KAAK,IAAI,CAAC;QACV,IAAIE,QAAQ,KAAK,CAAC,EAAE;UAChB,IAAIN,QAAQ,GAAGI,KAAK,GAAG,EAAE,EAAE;YACvB;UACJ;UACAE,QAAQ,GAAGzB,OAAO,CAAC0B,QAAQ,CAACjB,aAAa,EAAEc,KAAK,EAAE,EAAE,CAAC,GAAG,EAAE;UAC1DA,KAAK,IAAI,EAAE;QACf;QACA,KAAK,IAAII,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGF,QAAQ,EAAEE,SAAS,EAAE,EAAE;UACvD,IAAIR,QAAQ,GAAGI,KAAK,GAAG,CAAC,EAAE;YACtBA,KAAK,GAAGJ,QAAQ,CAAC,CAAC;YAClB;UACJ;UACA,IAAIS,IAAI,GAAG5B,OAAO,CAAC0B,QAAQ,CAACjB,aAAa,EAAEc,KAAK,EAAE,CAAC,CAAC;UACpDV,MAAM,IAAI,UAAWhB,WAAW,CAACgC,iBAAiB,CAACD,IAAI,CAAC;UACxDL,KAAK,IAAI,CAAC;QACd;QACA;QACAD,UAAU,GAAGF,UAAU;MAC3B,CAAC,MACI;QACD,IAAIU,IAAI,GAAGR,UAAU,KAAKvB,KAAK,CAACgC,KAAK,GAAG,CAAC,GAAG,CAAC;QAC7C,IAAIZ,QAAQ,GAAGI,KAAK,GAAGO,IAAI,EAAE;UACzB;QACJ;QACA,IAAIF,IAAI,GAAG5B,OAAO,CAAC0B,QAAQ,CAACjB,aAAa,EAAEc,KAAK,EAAEO,IAAI,CAAC;QACvDP,KAAK,IAAIO,IAAI;QACb,IAAIE,GAAG,GAAGhC,OAAO,CAACiC,YAAY,CAACX,UAAU,EAAEM,IAAI,CAAC;QAChD,IAAII,GAAG,CAACE,UAAU,CAAC,OAAO,CAAC,EAAE;UACzB;UACA;UACA;UACA;UACAd,UAAU,GAAGE,UAAU,CAAC,CAAC;UACzBA,UAAU,GAAGtB,OAAO,CAACmC,QAAQ,CAACH,GAAG,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;UAC5C,IAAIJ,GAAG,CAACI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YACvBhB,UAAU,GAAGE,UAAU;UAC3B;QACJ,CAAC,MACI;UACDT,MAAM,IAAImB,GAAG;UACb;UACAV,UAAU,GAAGF,UAAU;QAC3B;MACJ;IACJ;IACA,OAAOP,MAAM;EACjB,CAAC;EACD;AACJ;AACA;EACIb,OAAO,CAACmC,QAAQ,GAAG,UAAUE,CAAC,EAAE;IAC5B,QAAQA,CAAC;MACL,KAAK,GAAG;QACJ,OAAOtC,KAAK,CAACuC,KAAK;MACtB,KAAK,GAAG;QACJ,OAAOvC,KAAK,CAACwC,KAAK;MACtB,KAAK,GAAG;QACJ,OAAOxC,KAAK,CAACyC,KAAK;MACtB,KAAK,GAAG;QACJ,OAAOzC,KAAK,CAACgC,KAAK;MACtB,KAAK,GAAG;QACJ,OAAOhC,KAAK,CAACyB,MAAM;MACvB,KAAK,GAAG;MACR;QACI,OAAOzB,KAAK,CAACsB,KAAK;IAC1B;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIrB,OAAO,CAACiC,YAAY,GAAG,UAAUQ,KAAK,EAAEb,IAAI,EAAE;IAC1C,QAAQa,KAAK;MACT,KAAK1C,KAAK,CAACsB,KAAK;QACZ,OAAOrB,OAAO,CAAC0C,WAAW,CAACd,IAAI,CAAC;MACpC,KAAK7B,KAAK,CAACuC,KAAK;QACZ,OAAOtC,OAAO,CAAC2C,WAAW,CAACf,IAAI,CAAC;MACpC,KAAK7B,KAAK,CAACyC,KAAK;QACZ,OAAOxC,OAAO,CAAC4C,WAAW,CAAChB,IAAI,CAAC;MACpC,KAAK7B,KAAK,CAACwC,KAAK;QACZ,OAAOvC,OAAO,CAAC6C,WAAW,CAACjB,IAAI,CAAC;MACpC,KAAK7B,KAAK,CAACgC,KAAK;QACZ,OAAO/B,OAAO,CAAC8C,WAAW,CAAClB,IAAI,CAAC;MACpC;QACI;QACA,MAAM,IAAIjC,qBAAqB,CAAC,WAAW,CAAC;IACpD;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIK,OAAO,CAACC,SAAS,CAACS,WAAW,GAAG,UAAUH,OAAO,EAAE;IAC/C,IAAIwC,EAAE;IACN,IAAIC,YAAY;IAChB,IAAI,IAAI,CAAC5C,KAAK,CAAC6C,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE;MAC/BD,YAAY,GAAG,CAAC;MAChBD,EAAE,GAAGtD,SAAS,CAACyD,YAAY;IAC/B,CAAC,MACI,IAAI,IAAI,CAAC9C,KAAK,CAAC6C,WAAW,CAAC,CAAC,IAAI,CAAC,EAAE;MACpCD,YAAY,GAAG,CAAC;MAChBD,EAAE,GAAGtD,SAAS,CAAC0D,YAAY;IAC/B,CAAC,MACI,IAAI,IAAI,CAAC/C,KAAK,CAAC6C,WAAW,CAAC,CAAC,IAAI,EAAE,EAAE;MACrCD,YAAY,GAAG,EAAE;MACjBD,EAAE,GAAGtD,SAAS,CAAC2D,aAAa;IAChC,CAAC,MACI;MACDJ,YAAY,GAAG,EAAE;MACjBD,EAAE,GAAGtD,SAAS,CAAC4D,aAAa;IAChC;IACA,IAAIC,gBAAgB,GAAG,IAAI,CAAClD,KAAK,CAACmD,eAAe,CAAC,CAAC;IACnD,IAAIC,YAAY,GAAGjD,OAAO,CAACU,MAAM,GAAG+B,YAAY;IAChD,IAAIQ,YAAY,GAAGF,gBAAgB,EAAE;MACjC,MAAM,IAAI1D,eAAe,CAAC,CAAC;IAC/B;IACA,IAAI6D,MAAM,GAAGlD,OAAO,CAACU,MAAM,GAAG+B,YAAY;IAC1C,IAAIU,SAAS,GAAG,IAAIC,UAAU,CAACH,YAAY,CAAC;IAC5C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAEH,MAAM,IAAIT,YAAY,EAAE;MAC3DU,SAAS,CAACE,CAAC,CAAC,GAAG5D,OAAO,CAAC0B,QAAQ,CAACnB,OAAO,EAAEkD,MAAM,EAAET,YAAY,CAAC;IAClE;IACA,IAAI;MACA,IAAIa,SAAS,GAAG,IAAInE,kBAAkB,CAACqD,EAAE,CAAC;MAC1Cc,SAAS,CAAC3D,MAAM,CAACwD,SAAS,EAAEF,YAAY,GAAGF,gBAAgB,CAAC;IAChE,CAAC,CACD,OAAOQ,EAAE,EAAE;MACP,MAAM,IAAIlE,eAAe,CAACkE,EAAE,CAAC;IACjC;IACA;IACA;IACA,IAAIC,IAAI,GAAG,CAAC,CAAC,IAAIf,YAAY,IAAI,CAAC;IAClC,IAAIgB,WAAW,GAAG,CAAC;IACnB,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,gBAAgB,EAAEM,CAAC,EAAE,EAAE;MACvC,IAAIK,QAAQ,GAAGP,SAAS,CAACE,CAAC,CAAC;MAC3B,IAAIK,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAKF,IAAI,EAAE;QACrC,MAAM,IAAInE,eAAe,CAAC,CAAC;MAC/B,CAAC,MACI,IAAIqE,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAKF,IAAI,GAAG,CAAC,EAAE;QAC9CC,WAAW,EAAE;MACjB;IACJ;IACA;IACA,IAAIvD,aAAa,GAAG,IAAIyD,KAAK,CAACZ,gBAAgB,GAAGN,YAAY,GAAGgB,WAAW,CAAC;IAC5E,IAAIzC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIqC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,gBAAgB,EAAEM,CAAC,EAAE,EAAE;MACvC,IAAIK,QAAQ,GAAGP,SAAS,CAACE,CAAC,CAAC;MAC3B,IAAIK,QAAQ,KAAK,CAAC,IAAIA,QAAQ,KAAKF,IAAI,GAAG,CAAC,EAAE;QACzC;QACAtD,aAAa,CAAC0D,IAAI,CAACF,QAAQ,GAAG,CAAC,EAAE1C,KAAK,EAAEA,KAAK,GAAGyB,YAAY,GAAG,CAAC,CAAC;QACjE;QACAzB,KAAK,IAAIyB,YAAY,GAAG,CAAC;MAC7B,CAAC,MACI;QACD,KAAK,IAAIoB,GAAG,GAAGpB,YAAY,GAAG,CAAC,EAAEoB,GAAG,IAAI,CAAC,EAAE,EAAEA,GAAG,EAAE;UAC9C3D,aAAa,CAACc,KAAK,EAAE,CAAC,GAAG,CAAC0C,QAAQ,GAAI,CAAC,IAAIG,GAAI,MAAM,CAAC;QAC1D;MACJ;IACJ;IACA,OAAO3D,aAAa;EACxB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIT,OAAO,CAACC,SAAS,CAACO,WAAW,GAAG,UAAUH,MAAM,EAAE;IAC9C,IAAIgE,OAAO,GAAG,IAAI,CAACjE,KAAK,CAACkE,SAAS,CAAC,CAAC;IACpC,IAAIC,MAAM,GAAG,IAAI,CAACnE,KAAK,CAAC6C,WAAW,CAAC,CAAC;IACrC,IAAIuB,cAAc,GAAG,CAACH,OAAO,GAAG,EAAE,GAAG,EAAE,IAAIE,MAAM,GAAG,CAAC,CAAC,CAAC;IACvD,IAAIE,YAAY,GAAG,IAAId,UAAU,CAACa,cAAc,CAAC;IACjD,IAAIjE,OAAO,GAAG,IAAI2D,KAAK,CAAC,IAAI,CAACQ,gBAAgB,CAACH,MAAM,EAAEF,OAAO,CAAC,CAAC;IAC/D,IAAIA,OAAO,EAAE;MACT,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGa,YAAY,CAACxD,MAAM,EAAE2C,CAAC,EAAE,EAAE;QAC1Ca,YAAY,CAACb,CAAC,CAAC,GAAGA,CAAC;MACvB;IACJ,CAAC,MACI;MACD,IAAIe,UAAU,GAAGH,cAAc,GAAG,CAAC,GAAG,CAAC,GAAG1E,OAAO,CAAC8E,aAAa,CAAE9E,OAAO,CAAC8E,aAAa,CAACJ,cAAc,EAAE,CAAC,CAAC,GAAG,CAAC,EAAG,EAAE,CAAC;MACnH,IAAIK,UAAU,GAAGL,cAAc,GAAG,CAAC;MACnC,IAAIM,MAAM,GAAGhF,OAAO,CAAC8E,aAAa,CAACD,UAAU,EAAE,CAAC,CAAC;MACjD,KAAK,IAAIf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,UAAU,EAAEjB,CAAC,EAAE,EAAE;QACjC,IAAImB,SAAS,GAAGnB,CAAC,GAAG9D,OAAO,CAAC8E,aAAa,CAAChB,CAAC,EAAE,EAAE,CAAC;QAChDa,YAAY,CAACI,UAAU,GAAGjB,CAAC,GAAG,CAAC,CAAC,GAAGkB,MAAM,GAAGC,SAAS,GAAG,CAAC;QACzDN,YAAY,CAACI,UAAU,GAAGjB,CAAC,CAAC,GAAGkB,MAAM,GAAGC,SAAS,GAAG,CAAC;MACzD;IACJ;IACA,KAAK,IAAInB,CAAC,GAAG,CAAC,EAAEoB,SAAS,GAAG,CAAC,EAAEpB,CAAC,GAAGW,MAAM,EAAEX,CAAC,EAAE,EAAE;MAC5C,IAAIqB,OAAO,GAAG,CAACV,MAAM,GAAGX,CAAC,IAAI,CAAC,IAAIS,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;MACnD;MACA,IAAIa,GAAG,GAAGtB,CAAC,GAAG,CAAC;MACf;MACA,IAAIuB,IAAI,GAAGX,cAAc,GAAG,CAAC,GAAGU,GAAG;MACnC;MACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,EAAEG,CAAC,EAAE,EAAE;QAC9B,IAAIC,YAAY,GAAGD,CAAC,GAAG,CAAC;QACxB,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UACxB;UACA/E,OAAO,CAACyE,SAAS,GAAGK,YAAY,GAAGC,CAAC,CAAC,GACjCjF,MAAM,CAACkF,GAAG,CAACd,YAAY,CAACS,GAAG,GAAGI,CAAC,CAAC,EAAEb,YAAY,CAACS,GAAG,GAAGE,CAAC,CAAC,CAAC;UAC5D;UACA7E,OAAO,CAACyE,SAAS,GAAG,CAAC,GAAGC,OAAO,GAAGI,YAAY,GAAGC,CAAC,CAAC,GAC/CjF,MAAM,CAACkF,GAAG,CAACd,YAAY,CAACS,GAAG,GAAGE,CAAC,CAAC,EAAEX,YAAY,CAACU,IAAI,GAAGG,CAAC,CAAC,CAAC;UAC7D;UACA/E,OAAO,CAACyE,SAAS,GAAG,CAAC,GAAGC,OAAO,GAAGI,YAAY,GAAGC,CAAC,CAAC,GAC/CjF,MAAM,CAACkF,GAAG,CAACd,YAAY,CAACU,IAAI,GAAGG,CAAC,CAAC,EAAEb,YAAY,CAACU,IAAI,GAAGC,CAAC,CAAC,CAAC;UAC9D;UACA7E,OAAO,CAACyE,SAAS,GAAG,CAAC,GAAGC,OAAO,GAAGI,YAAY,GAAGC,CAAC,CAAC,GAC/CjF,MAAM,CAACkF,GAAG,CAACd,YAAY,CAACU,IAAI,GAAGC,CAAC,CAAC,EAAEX,YAAY,CAACS,GAAG,GAAGI,CAAC,CAAC,CAAC;QACjE;MACJ;MACAN,SAAS,IAAIC,OAAO,GAAG,CAAC;IAC5B;IACA,OAAO1E,OAAO;EAClB,CAAC;EACD;AACJ;AACA;EACIP,OAAO,CAAC0B,QAAQ,GAAG,UAAUnB,OAAO,EAAEiF,UAAU,EAAEvE,MAAM,EAAE;IACtD,IAAIwE,GAAG,GAAG,CAAC;IACX,KAAK,IAAI7B,CAAC,GAAG4B,UAAU,EAAE5B,CAAC,GAAG4B,UAAU,GAAGvE,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACnD6B,GAAG,KAAK,CAAC;MACT,IAAIlF,OAAO,CAACqD,CAAC,CAAC,EAAE;QACZ6B,GAAG,IAAI,IAAI;MACf;IACJ;IACA,OAAOA,GAAG;EACd,CAAC;EACD;AACJ;AACA;EACIzF,OAAO,CAAC0F,QAAQ,GAAG,UAAUnF,OAAO,EAAEiF,UAAU,EAAE;IAC9C,IAAIG,CAAC,GAAGpF,OAAO,CAACU,MAAM,GAAGuE,UAAU;IACnC,IAAIG,CAAC,IAAI,CAAC,EAAE;MACR,OAAO3F,OAAO,CAAC0B,QAAQ,CAACnB,OAAO,EAAEiF,UAAU,EAAE,CAAC,CAAC;IACnD;IACA,OAAOxF,OAAO,CAAC0B,QAAQ,CAACnB,OAAO,EAAEiF,UAAU,EAAEG,CAAC,CAAC,IAAK,CAAC,GAAGA,CAAE;EAC9D,CAAC;EACD;AACJ;AACA;EACI3F,OAAO,CAACY,2BAA2B,GAAG,UAAUgF,OAAO,EAAE;IACrD,IAAIC,OAAO,GAAG,IAAIC,UAAU,CAAC,CAACF,OAAO,CAAC3E,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;IACtD,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,OAAO,CAAC5E,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACrCiC,OAAO,CAACjC,CAAC,CAAC,GAAG5D,OAAO,CAAC0F,QAAQ,CAACE,OAAO,EAAE,CAAC,GAAGhC,CAAC,CAAC;IACjD;IACA,OAAOiC,OAAO;EAClB,CAAC;EACD7F,OAAO,CAACC,SAAS,CAACyE,gBAAgB,GAAG,UAAUH,MAAM,EAAEF,OAAO,EAAE;IAC5D,OAAO,CAAC,CAACA,OAAO,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,GAAGE,MAAM,IAAIA,MAAM;EACxD,CAAC;EACDvE,OAAO,CAAC0C,WAAW,GAAG,CAClB,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC9F,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC/F;EACD1C,OAAO,CAAC2C,WAAW,GAAG,CAClB,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAC9F,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC/F;EACD3C,OAAO,CAAC4C,WAAW,GAAG,CAClB,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EACxF,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAC/E,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CACpE;EACD5C,OAAO,CAAC6C,WAAW,GAAG,CAClB,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAChF,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,CAC5F;EACD7C,OAAO,CAAC8C,WAAW,GAAG,CAClB,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,CACnG;EACD,OAAO9C,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}