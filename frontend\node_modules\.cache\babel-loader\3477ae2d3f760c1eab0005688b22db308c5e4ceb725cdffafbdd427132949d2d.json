{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport BarcodeMetadata from './BarcodeMetadata';\nimport DetectionResultColumn from './DetectionResultColumn';\nimport BarcodeValue from './BarcodeValue';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResultRowIndicatorColumn = /** @class */function (_super) {\n  __extends(DetectionResultRowIndicatorColumn, _super);\n  function DetectionResultRowIndicatorColumn(boundingBox, isLeft) {\n    var _this = _super.call(this, boundingBox) || this;\n    _this._isLeft = isLeft;\n    return _this;\n  }\n  DetectionResultRowIndicatorColumn.prototype.setRowNumbers = function () {\n    var e_1, _a;\n    try {\n      for (var _b = __values(this.getCodewords()), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var codeword = _c.value /*Codeword*/;\n        if (codeword != null) {\n          codeword.setRowNumberAsRowIndicatorColumn();\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n  };\n  // TODO implement properly\n  // TODO maybe we should add missing codewords to store the correct row number to make\n  // finding row numbers for other columns easier\n  // use row height count to make detection of invalid row numbers more reliable\n  DetectionResultRowIndicatorColumn.prototype.adjustCompleteIndicatorColumnRowNumbers = function (barcodeMetadata) {\n    var codewords = this.getCodewords();\n    this.setRowNumbers();\n    this.removeIncorrectCodewords(codewords, barcodeMetadata);\n    var boundingBox = this.getBoundingBox();\n    var top = this._isLeft ? boundingBox.getTopLeft() : boundingBox.getTopRight();\n    var bottom = this._isLeft ? boundingBox.getBottomLeft() : boundingBox.getBottomRight();\n    var firstRow = this.imageRowToCodewordIndex(Math.trunc(top.getY()));\n    var lastRow = this.imageRowToCodewordIndex(Math.trunc(bottom.getY()));\n    // We need to be careful using the average row height. Barcode could be skewed so that we have smaller and\n    // taller rows\n    // float averageRowHeight = (lastRow - firstRow) / /*(float)*/ barcodeMetadata.getRowCount();\n    var barcodeRow = -1;\n    var maxRowHeight = 1;\n    var currentRowHeight = 0;\n    for (var codewordsRow /*int*/ = firstRow; codewordsRow < lastRow; codewordsRow++) {\n      if (codewords[codewordsRow] == null) {\n        continue;\n      }\n      var codeword = codewords[codewordsRow];\n      //      float expectedRowNumber = (codewordsRow - firstRow) / averageRowHeight;\n      //      if (Math.abs(codeword.getRowNumber() - expectedRowNumber) > 2) {\n      //        SimpleLog.log(LEVEL.WARNING,\n      //            \"Removing codeword, rowNumberSkew too high, codeword[\" + codewordsRow + \"]: Expected Row: \" +\n      //                expectedRowNumber + \", RealRow: \" + codeword.getRowNumber() + \", value: \" + codeword.getValue());\n      //        codewords[codewordsRow] = null;\n      //      }\n      var rowDifference = codeword.getRowNumber() - barcodeRow;\n      // TODO improve handling with case where first row indicator doesn't start with 0\n      if (rowDifference === 0) {\n        currentRowHeight++;\n      } else if (rowDifference === 1) {\n        maxRowHeight = Math.max(maxRowHeight, currentRowHeight);\n        currentRowHeight = 1;\n        barcodeRow = codeword.getRowNumber();\n      } else if (rowDifference < 0 || codeword.getRowNumber() >= barcodeMetadata.getRowCount() || rowDifference > codewordsRow) {\n        codewords[codewordsRow] = null;\n      } else {\n        var checkedRows = void 0;\n        if (maxRowHeight > 2) {\n          checkedRows = (maxRowHeight - 2) * rowDifference;\n        } else {\n          checkedRows = rowDifference;\n        }\n        var closePreviousCodewordFound = checkedRows >= codewordsRow;\n        for (var i /*int*/ = 1; i <= checkedRows && !closePreviousCodewordFound; i++) {\n          // there must be (height * rowDifference) number of codewords missing. For now we assume height = 1.\n          // This should hopefully get rid of most problems already.\n          closePreviousCodewordFound = codewords[codewordsRow - i] != null;\n        }\n        if (closePreviousCodewordFound) {\n          codewords[codewordsRow] = null;\n        } else {\n          barcodeRow = codeword.getRowNumber();\n          currentRowHeight = 1;\n        }\n      }\n    }\n    // return (int) (averageRowHeight + 0.5);\n  };\n  DetectionResultRowIndicatorColumn.prototype.getRowHeights = function () {\n    var e_2, _a;\n    var barcodeMetadata = this.getBarcodeMetadata();\n    if (barcodeMetadata == null) {\n      return null;\n    }\n    this.adjustIncompleteIndicatorColumnRowNumbers(barcodeMetadata);\n    var result = new Int32Array(barcodeMetadata.getRowCount());\n    try {\n      for (var _b = __values(this.getCodewords()), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var codeword = _c.value /*Codeword*/;\n        if (codeword != null) {\n          var rowNumber = codeword.getRowNumber();\n          if (rowNumber >= result.length) {\n            // We have more rows than the barcode metadata allows for, ignore them.\n            continue;\n          }\n          result[rowNumber]++;\n        } // else throw exception?\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return result;\n  };\n  // TODO maybe we should add missing codewords to store the correct row number to make\n  // finding row numbers for other columns easier\n  // use row height count to make detection of invalid row numbers more reliable\n  DetectionResultRowIndicatorColumn.prototype.adjustIncompleteIndicatorColumnRowNumbers = function (barcodeMetadata) {\n    var boundingBox = this.getBoundingBox();\n    var top = this._isLeft ? boundingBox.getTopLeft() : boundingBox.getTopRight();\n    var bottom = this._isLeft ? boundingBox.getBottomLeft() : boundingBox.getBottomRight();\n    var firstRow = this.imageRowToCodewordIndex(Math.trunc(top.getY()));\n    var lastRow = this.imageRowToCodewordIndex(Math.trunc(bottom.getY()));\n    // float averageRowHeight = (lastRow - firstRow) / /*(float)*/ barcodeMetadata.getRowCount();\n    var codewords = this.getCodewords();\n    var barcodeRow = -1;\n    var maxRowHeight = 1;\n    var currentRowHeight = 0;\n    for (var codewordsRow /*int*/ = firstRow; codewordsRow < lastRow; codewordsRow++) {\n      if (codewords[codewordsRow] == null) {\n        continue;\n      }\n      var codeword = codewords[codewordsRow];\n      codeword.setRowNumberAsRowIndicatorColumn();\n      var rowDifference = codeword.getRowNumber() - barcodeRow;\n      // TODO improve handling with case where first row indicator doesn't start with 0\n      if (rowDifference === 0) {\n        currentRowHeight++;\n      } else if (rowDifference === 1) {\n        maxRowHeight = Math.max(maxRowHeight, currentRowHeight);\n        currentRowHeight = 1;\n        barcodeRow = codeword.getRowNumber();\n      } else if (codeword.getRowNumber() >= barcodeMetadata.getRowCount()) {\n        codewords[codewordsRow] = null;\n      } else {\n        barcodeRow = codeword.getRowNumber();\n        currentRowHeight = 1;\n      }\n    }\n    // return (int) (averageRowHeight + 0.5);\n  };\n  DetectionResultRowIndicatorColumn.prototype.getBarcodeMetadata = function () {\n    var e_3, _a;\n    var codewords = this.getCodewords();\n    var barcodeColumnCount = new BarcodeValue();\n    var barcodeRowCountUpperPart = new BarcodeValue();\n    var barcodeRowCountLowerPart = new BarcodeValue();\n    var barcodeECLevel = new BarcodeValue();\n    try {\n      for (var codewords_1 = __values(codewords), codewords_1_1 = codewords_1.next(); !codewords_1_1.done; codewords_1_1 = codewords_1.next()) {\n        var codeword = codewords_1_1.value /*Codeword*/;\n        if (codeword == null) {\n          continue;\n        }\n        codeword.setRowNumberAsRowIndicatorColumn();\n        var rowIndicatorValue = codeword.getValue() % 30;\n        var codewordRowNumber = codeword.getRowNumber();\n        if (!this._isLeft) {\n          codewordRowNumber += 2;\n        }\n        switch (codewordRowNumber % 3) {\n          case 0:\n            barcodeRowCountUpperPart.setValue(rowIndicatorValue * 3 + 1);\n            break;\n          case 1:\n            barcodeECLevel.setValue(rowIndicatorValue / 3);\n            barcodeRowCountLowerPart.setValue(rowIndicatorValue % 3);\n            break;\n          case 2:\n            barcodeColumnCount.setValue(rowIndicatorValue + 1);\n            break;\n        }\n      }\n    } catch (e_3_1) {\n      e_3 = {\n        error: e_3_1\n      };\n    } finally {\n      try {\n        if (codewords_1_1 && !codewords_1_1.done && (_a = codewords_1.return)) _a.call(codewords_1);\n      } finally {\n        if (e_3) throw e_3.error;\n      }\n    }\n    // Maybe we should check if we have ambiguous values?\n    if (barcodeColumnCount.getValue().length === 0 || barcodeRowCountUpperPart.getValue().length === 0 || barcodeRowCountLowerPart.getValue().length === 0 || barcodeECLevel.getValue().length === 0 || barcodeColumnCount.getValue()[0] < 1 || barcodeRowCountUpperPart.getValue()[0] + barcodeRowCountLowerPart.getValue()[0] < PDF417Common.MIN_ROWS_IN_BARCODE || barcodeRowCountUpperPart.getValue()[0] + barcodeRowCountLowerPart.getValue()[0] > PDF417Common.MAX_ROWS_IN_BARCODE) {\n      return null;\n    }\n    var barcodeMetadata = new BarcodeMetadata(barcodeColumnCount.getValue()[0], barcodeRowCountUpperPart.getValue()[0], barcodeRowCountLowerPart.getValue()[0], barcodeECLevel.getValue()[0]);\n    this.removeIncorrectCodewords(codewords, barcodeMetadata);\n    return barcodeMetadata;\n  };\n  DetectionResultRowIndicatorColumn.prototype.removeIncorrectCodewords = function (codewords, barcodeMetadata) {\n    // Remove codewords which do not match the metadata\n    // TODO Maybe we should keep the incorrect codewords for the start and end positions?\n    for (var codewordRow /*int*/ = 0; codewordRow < codewords.length; codewordRow++) {\n      var codeword = codewords[codewordRow];\n      if (codewords[codewordRow] == null) {\n        continue;\n      }\n      var rowIndicatorValue = codeword.getValue() % 30;\n      var codewordRowNumber = codeword.getRowNumber();\n      if (codewordRowNumber > barcodeMetadata.getRowCount()) {\n        codewords[codewordRow] = null;\n        continue;\n      }\n      if (!this._isLeft) {\n        codewordRowNumber += 2;\n      }\n      switch (codewordRowNumber % 3) {\n        case 0:\n          if (rowIndicatorValue * 3 + 1 !== barcodeMetadata.getRowCountUpperPart()) {\n            codewords[codewordRow] = null;\n          }\n          break;\n        case 1:\n          if (Math.trunc(rowIndicatorValue / 3) !== barcodeMetadata.getErrorCorrectionLevel() || rowIndicatorValue % 3 !== barcodeMetadata.getRowCountLowerPart()) {\n            codewords[codewordRow] = null;\n          }\n          break;\n        case 2:\n          if (rowIndicatorValue + 1 !== barcodeMetadata.getColumnCount()) {\n            codewords[codewordRow] = null;\n          }\n          break;\n      }\n    }\n  };\n  DetectionResultRowIndicatorColumn.prototype.isLeft = function () {\n    return this._isLeft;\n  };\n  // @Override\n  DetectionResultRowIndicatorColumn.prototype.toString = function () {\n    return 'IsLeft: ' + this._isLeft + '\\n' + _super.prototype.toString.call(this);\n  };\n  return DetectionResultRowIndicatorColumn;\n}(DetectionResultColumn);\nexport default DetectionResultRowIndicatorColumn;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "PDF417<PERSON><PERSON><PERSON>", "BarcodeMetadata", "DetectionResultColumn", "BarcodeValue", "DetectionResultRowIndicatorColumn", "_super", "boundingBox", "isLeft", "_this", "_isLeft", "setRowNumbers", "e_1", "_a", "_b", "getCodewords", "_c", "codeword", "setRowNumberAsRowIndicatorColumn", "e_1_1", "error", "return", "adjustCompleteIndicatorColumnRowNumbers", "barcodeMetadata", "codewords", "removeIncorrectCodewords", "getBoundingBox", "top", "getTopLeft", "getTopRight", "bottom", "getBottomLeft", "getBottomRight", "firstRow", "imageRowToCodewordIndex", "Math", "trunc", "getY", "lastRow", "barcodeRow", "maxRowHeight", "currentRowHeight", "codewordsRow", "rowDifference", "getRowNumber", "max", "getRowCount", "checkedRows", "closePreviousCodewordFound", "getRowHeights", "e_2", "getBarcodeMetadata", "adjustIncompleteIndicatorColumnRowNumbers", "result", "Int32Array", "rowNumber", "e_2_1", "e_3", "barcodeColumnCount", "barcodeRowCountUpperPart", "barcodeRowCountLowerPart", "barcodeECLevel", "codewords_1", "codewords_1_1", "rowIndicatorValue", "getValue", "codewordRowNumber", "setValue", "e_3_1", "MIN_ROWS_IN_BARCODE", "MAX_ROWS_IN_BARCODE", "codewordRow", "getRowCountUpperPart", "getErrorCorrectionLevel", "getRowCountLowerPart", "getColumnCount", "toString"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/DetectionResultRowIndicatorColumn.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport BarcodeMetadata from './BarcodeMetadata';\nimport DetectionResultColumn from './DetectionResultColumn';\nimport BarcodeValue from './BarcodeValue';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResultRowIndicatorColumn = /** @class */ (function (_super) {\n    __extends(DetectionResultRowIndicatorColumn, _super);\n    function DetectionResultRowIndicatorColumn(boundingBox, isLeft) {\n        var _this = _super.call(this, boundingBox) || this;\n        _this._isLeft = isLeft;\n        return _this;\n    }\n    DetectionResultRowIndicatorColumn.prototype.setRowNumbers = function () {\n        var e_1, _a;\n        try {\n            for (var _b = __values(this.getCodewords()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var codeword = _c.value /*Codeword*/;\n                if (codeword != null) {\n                    codeword.setRowNumberAsRowIndicatorColumn();\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    // TODO implement properly\n    // TODO maybe we should add missing codewords to store the correct row number to make\n    // finding row numbers for other columns easier\n    // use row height count to make detection of invalid row numbers more reliable\n    DetectionResultRowIndicatorColumn.prototype.adjustCompleteIndicatorColumnRowNumbers = function (barcodeMetadata) {\n        var codewords = this.getCodewords();\n        this.setRowNumbers();\n        this.removeIncorrectCodewords(codewords, barcodeMetadata);\n        var boundingBox = this.getBoundingBox();\n        var top = this._isLeft ? boundingBox.getTopLeft() : boundingBox.getTopRight();\n        var bottom = this._isLeft ? boundingBox.getBottomLeft() : boundingBox.getBottomRight();\n        var firstRow = this.imageRowToCodewordIndex(Math.trunc(top.getY()));\n        var lastRow = this.imageRowToCodewordIndex(Math.trunc(bottom.getY()));\n        // We need to be careful using the average row height. Barcode could be skewed so that we have smaller and\n        // taller rows\n        // float averageRowHeight = (lastRow - firstRow) / /*(float)*/ barcodeMetadata.getRowCount();\n        var barcodeRow = -1;\n        var maxRowHeight = 1;\n        var currentRowHeight = 0;\n        for (var codewordsRow /*int*/ = firstRow; codewordsRow < lastRow; codewordsRow++) {\n            if (codewords[codewordsRow] == null) {\n                continue;\n            }\n            var codeword = codewords[codewordsRow];\n            //      float expectedRowNumber = (codewordsRow - firstRow) / averageRowHeight;\n            //      if (Math.abs(codeword.getRowNumber() - expectedRowNumber) > 2) {\n            //        SimpleLog.log(LEVEL.WARNING,\n            //            \"Removing codeword, rowNumberSkew too high, codeword[\" + codewordsRow + \"]: Expected Row: \" +\n            //                expectedRowNumber + \", RealRow: \" + codeword.getRowNumber() + \", value: \" + codeword.getValue());\n            //        codewords[codewordsRow] = null;\n            //      }\n            var rowDifference = codeword.getRowNumber() - barcodeRow;\n            // TODO improve handling with case where first row indicator doesn't start with 0\n            if (rowDifference === 0) {\n                currentRowHeight++;\n            }\n            else if (rowDifference === 1) {\n                maxRowHeight = Math.max(maxRowHeight, currentRowHeight);\n                currentRowHeight = 1;\n                barcodeRow = codeword.getRowNumber();\n            }\n            else if (rowDifference < 0 ||\n                codeword.getRowNumber() >= barcodeMetadata.getRowCount() ||\n                rowDifference > codewordsRow) {\n                codewords[codewordsRow] = null;\n            }\n            else {\n                var checkedRows = void 0;\n                if (maxRowHeight > 2) {\n                    checkedRows = (maxRowHeight - 2) * rowDifference;\n                }\n                else {\n                    checkedRows = rowDifference;\n                }\n                var closePreviousCodewordFound = checkedRows >= codewordsRow;\n                for (var i /*int*/ = 1; i <= checkedRows && !closePreviousCodewordFound; i++) {\n                    // there must be (height * rowDifference) number of codewords missing. For now we assume height = 1.\n                    // This should hopefully get rid of most problems already.\n                    closePreviousCodewordFound = codewords[codewordsRow - i] != null;\n                }\n                if (closePreviousCodewordFound) {\n                    codewords[codewordsRow] = null;\n                }\n                else {\n                    barcodeRow = codeword.getRowNumber();\n                    currentRowHeight = 1;\n                }\n            }\n        }\n        // return (int) (averageRowHeight + 0.5);\n    };\n    DetectionResultRowIndicatorColumn.prototype.getRowHeights = function () {\n        var e_2, _a;\n        var barcodeMetadata = this.getBarcodeMetadata();\n        if (barcodeMetadata == null) {\n            return null;\n        }\n        this.adjustIncompleteIndicatorColumnRowNumbers(barcodeMetadata);\n        var result = new Int32Array(barcodeMetadata.getRowCount());\n        try {\n            for (var _b = __values(this.getCodewords()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var codeword = _c.value /*Codeword*/;\n                if (codeword != null) {\n                    var rowNumber = codeword.getRowNumber();\n                    if (rowNumber >= result.length) {\n                        // We have more rows than the barcode metadata allows for, ignore them.\n                        continue;\n                    }\n                    result[rowNumber]++;\n                } // else throw exception?\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return result;\n    };\n    // TODO maybe we should add missing codewords to store the correct row number to make\n    // finding row numbers for other columns easier\n    // use row height count to make detection of invalid row numbers more reliable\n    DetectionResultRowIndicatorColumn.prototype.adjustIncompleteIndicatorColumnRowNumbers = function (barcodeMetadata) {\n        var boundingBox = this.getBoundingBox();\n        var top = this._isLeft ? boundingBox.getTopLeft() : boundingBox.getTopRight();\n        var bottom = this._isLeft ? boundingBox.getBottomLeft() : boundingBox.getBottomRight();\n        var firstRow = this.imageRowToCodewordIndex(Math.trunc(top.getY()));\n        var lastRow = this.imageRowToCodewordIndex(Math.trunc(bottom.getY()));\n        // float averageRowHeight = (lastRow - firstRow) / /*(float)*/ barcodeMetadata.getRowCount();\n        var codewords = this.getCodewords();\n        var barcodeRow = -1;\n        var maxRowHeight = 1;\n        var currentRowHeight = 0;\n        for (var codewordsRow /*int*/ = firstRow; codewordsRow < lastRow; codewordsRow++) {\n            if (codewords[codewordsRow] == null) {\n                continue;\n            }\n            var codeword = codewords[codewordsRow];\n            codeword.setRowNumberAsRowIndicatorColumn();\n            var rowDifference = codeword.getRowNumber() - barcodeRow;\n            // TODO improve handling with case where first row indicator doesn't start with 0\n            if (rowDifference === 0) {\n                currentRowHeight++;\n            }\n            else if (rowDifference === 1) {\n                maxRowHeight = Math.max(maxRowHeight, currentRowHeight);\n                currentRowHeight = 1;\n                barcodeRow = codeword.getRowNumber();\n            }\n            else if (codeword.getRowNumber() >= barcodeMetadata.getRowCount()) {\n                codewords[codewordsRow] = null;\n            }\n            else {\n                barcodeRow = codeword.getRowNumber();\n                currentRowHeight = 1;\n            }\n        }\n        // return (int) (averageRowHeight + 0.5);\n    };\n    DetectionResultRowIndicatorColumn.prototype.getBarcodeMetadata = function () {\n        var e_3, _a;\n        var codewords = this.getCodewords();\n        var barcodeColumnCount = new BarcodeValue();\n        var barcodeRowCountUpperPart = new BarcodeValue();\n        var barcodeRowCountLowerPart = new BarcodeValue();\n        var barcodeECLevel = new BarcodeValue();\n        try {\n            for (var codewords_1 = __values(codewords), codewords_1_1 = codewords_1.next(); !codewords_1_1.done; codewords_1_1 = codewords_1.next()) {\n                var codeword = codewords_1_1.value /*Codeword*/;\n                if (codeword == null) {\n                    continue;\n                }\n                codeword.setRowNumberAsRowIndicatorColumn();\n                var rowIndicatorValue = codeword.getValue() % 30;\n                var codewordRowNumber = codeword.getRowNumber();\n                if (!this._isLeft) {\n                    codewordRowNumber += 2;\n                }\n                switch (codewordRowNumber % 3) {\n                    case 0:\n                        barcodeRowCountUpperPart.setValue(rowIndicatorValue * 3 + 1);\n                        break;\n                    case 1:\n                        barcodeECLevel.setValue(rowIndicatorValue / 3);\n                        barcodeRowCountLowerPart.setValue(rowIndicatorValue % 3);\n                        break;\n                    case 2:\n                        barcodeColumnCount.setValue(rowIndicatorValue + 1);\n                        break;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (codewords_1_1 && !codewords_1_1.done && (_a = codewords_1.return)) _a.call(codewords_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        // Maybe we should check if we have ambiguous values?\n        if ((barcodeColumnCount.getValue().length === 0) ||\n            (barcodeRowCountUpperPart.getValue().length === 0) ||\n            (barcodeRowCountLowerPart.getValue().length === 0) ||\n            (barcodeECLevel.getValue().length === 0) ||\n            barcodeColumnCount.getValue()[0] < 1 ||\n            barcodeRowCountUpperPart.getValue()[0] + barcodeRowCountLowerPart.getValue()[0] < PDF417Common.MIN_ROWS_IN_BARCODE ||\n            barcodeRowCountUpperPart.getValue()[0] + barcodeRowCountLowerPart.getValue()[0] > PDF417Common.MAX_ROWS_IN_BARCODE) {\n            return null;\n        }\n        var barcodeMetadata = new BarcodeMetadata(barcodeColumnCount.getValue()[0], barcodeRowCountUpperPart.getValue()[0], barcodeRowCountLowerPart.getValue()[0], barcodeECLevel.getValue()[0]);\n        this.removeIncorrectCodewords(codewords, barcodeMetadata);\n        return barcodeMetadata;\n    };\n    DetectionResultRowIndicatorColumn.prototype.removeIncorrectCodewords = function (codewords, barcodeMetadata) {\n        // Remove codewords which do not match the metadata\n        // TODO Maybe we should keep the incorrect codewords for the start and end positions?\n        for (var codewordRow /*int*/ = 0; codewordRow < codewords.length; codewordRow++) {\n            var codeword = codewords[codewordRow];\n            if (codewords[codewordRow] == null) {\n                continue;\n            }\n            var rowIndicatorValue = codeword.getValue() % 30;\n            var codewordRowNumber = codeword.getRowNumber();\n            if (codewordRowNumber > barcodeMetadata.getRowCount()) {\n                codewords[codewordRow] = null;\n                continue;\n            }\n            if (!this._isLeft) {\n                codewordRowNumber += 2;\n            }\n            switch (codewordRowNumber % 3) {\n                case 0:\n                    if (rowIndicatorValue * 3 + 1 !== barcodeMetadata.getRowCountUpperPart()) {\n                        codewords[codewordRow] = null;\n                    }\n                    break;\n                case 1:\n                    if (Math.trunc(rowIndicatorValue / 3) !== barcodeMetadata.getErrorCorrectionLevel() ||\n                        rowIndicatorValue % 3 !== barcodeMetadata.getRowCountLowerPart()) {\n                        codewords[codewordRow] = null;\n                    }\n                    break;\n                case 2:\n                    if (rowIndicatorValue + 1 !== barcodeMetadata.getColumnCount()) {\n                        codewords[codewordRow] = null;\n                    }\n                    break;\n            }\n        }\n    };\n    DetectionResultRowIndicatorColumn.prototype.isLeft = function () {\n        return this._isLeft;\n    };\n    // @Override\n    DetectionResultRowIndicatorColumn.prototype.toString = function () {\n        return 'IsLeft: ' + this._isLeft + '\\n' + _super.prototype.toString.call(this);\n    };\n    return DetectionResultRowIndicatorColumn;\n}(DetectionResultColumn));\nexport default DetectionResultRowIndicatorColumn;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAII,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,YAAY,MAAM,gBAAgB;AACzC;AACA;AACA;AACA,IAAIC,iCAAiC,GAAG,aAAe,UAAUC,MAAM,EAAE;EACrEhC,SAAS,CAAC+B,iCAAiC,EAAEC,MAAM,CAAC;EACpD,SAASD,iCAAiCA,CAACE,WAAW,EAAEC,MAAM,EAAE;IAC5D,IAAIC,KAAK,GAAGH,MAAM,CAACX,IAAI,CAAC,IAAI,EAAEY,WAAW,CAAC,IAAI,IAAI;IAClDE,KAAK,CAACC,OAAO,GAAGF,MAAM;IACtB,OAAOC,KAAK;EAChB;EACAJ,iCAAiC,CAACnB,SAAS,CAACyB,aAAa,GAAG,YAAY;IACpE,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAI;MACA,KAAK,IAAIC,EAAE,GAAG1B,QAAQ,CAAC,IAAI,CAAC2B,YAAY,CAAC,CAAC,CAAC,EAAEC,EAAE,GAAGF,EAAE,CAACjB,IAAI,CAAC,CAAC,EAAE,CAACmB,EAAE,CAACjB,IAAI,EAAEiB,EAAE,GAAGF,EAAE,CAACjB,IAAI,CAAC,CAAC,EAAE;QACnF,IAAIoB,QAAQ,GAAGD,EAAE,CAAClB,KAAK,CAAC;QACxB,IAAImB,QAAQ,IAAI,IAAI,EAAE;UAClBA,QAAQ,CAACC,gCAAgC,CAAC,CAAC;QAC/C;MACJ;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEP,GAAG,GAAG;QAAEQ,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,EAAE,IAAI,CAACA,EAAE,CAACjB,IAAI,KAAKc,EAAE,GAAGC,EAAE,CAACO,MAAM,CAAC,EAAER,EAAE,CAAClB,IAAI,CAACmB,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACQ,KAAK;MAAE;IACxC;EACJ,CAAC;EACD;EACA;EACA;EACA;EACAf,iCAAiC,CAACnB,SAAS,CAACoC,uCAAuC,GAAG,UAAUC,eAAe,EAAE;IAC7G,IAAIC,SAAS,GAAG,IAAI,CAACT,YAAY,CAAC,CAAC;IACnC,IAAI,CAACJ,aAAa,CAAC,CAAC;IACpB,IAAI,CAACc,wBAAwB,CAACD,SAAS,EAAED,eAAe,CAAC;IACzD,IAAIhB,WAAW,GAAG,IAAI,CAACmB,cAAc,CAAC,CAAC;IACvC,IAAIC,GAAG,GAAG,IAAI,CAACjB,OAAO,GAAGH,WAAW,CAACqB,UAAU,CAAC,CAAC,GAAGrB,WAAW,CAACsB,WAAW,CAAC,CAAC;IAC7E,IAAIC,MAAM,GAAG,IAAI,CAACpB,OAAO,GAAGH,WAAW,CAACwB,aAAa,CAAC,CAAC,GAAGxB,WAAW,CAACyB,cAAc,CAAC,CAAC;IACtF,IAAIC,QAAQ,GAAG,IAAI,CAACC,uBAAuB,CAACC,IAAI,CAACC,KAAK,CAACT,GAAG,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC;IACnE,IAAIC,OAAO,GAAG,IAAI,CAACJ,uBAAuB,CAACC,IAAI,CAACC,KAAK,CAACN,MAAM,CAACO,IAAI,CAAC,CAAC,CAAC,CAAC;IACrE;IACA;IACA;IACA,IAAIE,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,gBAAgB,GAAG,CAAC;IACxB,KAAK,IAAIC,YAAY,CAAC,UAAUT,QAAQ,EAAES,YAAY,GAAGJ,OAAO,EAAEI,YAAY,EAAE,EAAE;MAC9E,IAAIlB,SAAS,CAACkB,YAAY,CAAC,IAAI,IAAI,EAAE;QACjC;MACJ;MACA,IAAIzB,QAAQ,GAAGO,SAAS,CAACkB,YAAY,CAAC;MACtC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAIC,aAAa,GAAG1B,QAAQ,CAAC2B,YAAY,CAAC,CAAC,GAAGL,UAAU;MACxD;MACA,IAAII,aAAa,KAAK,CAAC,EAAE;QACrBF,gBAAgB,EAAE;MACtB,CAAC,MACI,IAAIE,aAAa,KAAK,CAAC,EAAE;QAC1BH,YAAY,GAAGL,IAAI,CAACU,GAAG,CAACL,YAAY,EAAEC,gBAAgB,CAAC;QACvDA,gBAAgB,GAAG,CAAC;QACpBF,UAAU,GAAGtB,QAAQ,CAAC2B,YAAY,CAAC,CAAC;MACxC,CAAC,MACI,IAAID,aAAa,GAAG,CAAC,IACtB1B,QAAQ,CAAC2B,YAAY,CAAC,CAAC,IAAIrB,eAAe,CAACuB,WAAW,CAAC,CAAC,IACxDH,aAAa,GAAGD,YAAY,EAAE;QAC9BlB,SAAS,CAACkB,YAAY,CAAC,GAAG,IAAI;MAClC,CAAC,MACI;QACD,IAAIK,WAAW,GAAG,KAAK,CAAC;QACxB,IAAIP,YAAY,GAAG,CAAC,EAAE;UAClBO,WAAW,GAAG,CAACP,YAAY,GAAG,CAAC,IAAIG,aAAa;QACpD,CAAC,MACI;UACDI,WAAW,GAAGJ,aAAa;QAC/B;QACA,IAAIK,0BAA0B,GAAGD,WAAW,IAAIL,YAAY;QAC5D,KAAK,IAAIhD,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,IAAIqD,WAAW,IAAI,CAACC,0BAA0B,EAAEtD,CAAC,EAAE,EAAE;UAC1E;UACA;UACAsD,0BAA0B,GAAGxB,SAAS,CAACkB,YAAY,GAAGhD,CAAC,CAAC,IAAI,IAAI;QACpE;QACA,IAAIsD,0BAA0B,EAAE;UAC5BxB,SAAS,CAACkB,YAAY,CAAC,GAAG,IAAI;QAClC,CAAC,MACI;UACDH,UAAU,GAAGtB,QAAQ,CAAC2B,YAAY,CAAC,CAAC;UACpCH,gBAAgB,GAAG,CAAC;QACxB;MACJ;IACJ;IACA;EACJ,CAAC;EACDpC,iCAAiC,CAACnB,SAAS,CAAC+D,aAAa,GAAG,YAAY;IACpE,IAAIC,GAAG,EAAErC,EAAE;IACX,IAAIU,eAAe,GAAG,IAAI,CAAC4B,kBAAkB,CAAC,CAAC;IAC/C,IAAI5B,eAAe,IAAI,IAAI,EAAE;MACzB,OAAO,IAAI;IACf;IACA,IAAI,CAAC6B,yCAAyC,CAAC7B,eAAe,CAAC;IAC/D,IAAI8B,MAAM,GAAG,IAAIC,UAAU,CAAC/B,eAAe,CAACuB,WAAW,CAAC,CAAC,CAAC;IAC1D,IAAI;MACA,KAAK,IAAIhC,EAAE,GAAG1B,QAAQ,CAAC,IAAI,CAAC2B,YAAY,CAAC,CAAC,CAAC,EAAEC,EAAE,GAAGF,EAAE,CAACjB,IAAI,CAAC,CAAC,EAAE,CAACmB,EAAE,CAACjB,IAAI,EAAEiB,EAAE,GAAGF,EAAE,CAACjB,IAAI,CAAC,CAAC,EAAE;QACnF,IAAIoB,QAAQ,GAAGD,EAAE,CAAClB,KAAK,CAAC;QACxB,IAAImB,QAAQ,IAAI,IAAI,EAAE;UAClB,IAAIsC,SAAS,GAAGtC,QAAQ,CAAC2B,YAAY,CAAC,CAAC;UACvC,IAAIW,SAAS,IAAIF,MAAM,CAACzD,MAAM,EAAE;YAC5B;YACA;UACJ;UACAyD,MAAM,CAACE,SAAS,CAAC,EAAE;QACvB,CAAC,CAAC;MACN;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEN,GAAG,GAAG;QAAE9B,KAAK,EAAEoC;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIxC,EAAE,IAAI,CAACA,EAAE,CAACjB,IAAI,KAAKc,EAAE,GAAGC,EAAE,CAACO,MAAM,CAAC,EAAER,EAAE,CAAClB,IAAI,CAACmB,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIoC,GAAG,EAAE,MAAMA,GAAG,CAAC9B,KAAK;MAAE;IACxC;IACA,OAAOiC,MAAM;EACjB,CAAC;EACD;EACA;EACA;EACAhD,iCAAiC,CAACnB,SAAS,CAACkE,yCAAyC,GAAG,UAAU7B,eAAe,EAAE;IAC/G,IAAIhB,WAAW,GAAG,IAAI,CAACmB,cAAc,CAAC,CAAC;IACvC,IAAIC,GAAG,GAAG,IAAI,CAACjB,OAAO,GAAGH,WAAW,CAACqB,UAAU,CAAC,CAAC,GAAGrB,WAAW,CAACsB,WAAW,CAAC,CAAC;IAC7E,IAAIC,MAAM,GAAG,IAAI,CAACpB,OAAO,GAAGH,WAAW,CAACwB,aAAa,CAAC,CAAC,GAAGxB,WAAW,CAACyB,cAAc,CAAC,CAAC;IACtF,IAAIC,QAAQ,GAAG,IAAI,CAACC,uBAAuB,CAACC,IAAI,CAACC,KAAK,CAACT,GAAG,CAACU,IAAI,CAAC,CAAC,CAAC,CAAC;IACnE,IAAIC,OAAO,GAAG,IAAI,CAACJ,uBAAuB,CAACC,IAAI,CAACC,KAAK,CAACN,MAAM,CAACO,IAAI,CAAC,CAAC,CAAC,CAAC;IACrE;IACA,IAAIb,SAAS,GAAG,IAAI,CAACT,YAAY,CAAC,CAAC;IACnC,IAAIwB,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,gBAAgB,GAAG,CAAC;IACxB,KAAK,IAAIC,YAAY,CAAC,UAAUT,QAAQ,EAAES,YAAY,GAAGJ,OAAO,EAAEI,YAAY,EAAE,EAAE;MAC9E,IAAIlB,SAAS,CAACkB,YAAY,CAAC,IAAI,IAAI,EAAE;QACjC;MACJ;MACA,IAAIzB,QAAQ,GAAGO,SAAS,CAACkB,YAAY,CAAC;MACtCzB,QAAQ,CAACC,gCAAgC,CAAC,CAAC;MAC3C,IAAIyB,aAAa,GAAG1B,QAAQ,CAAC2B,YAAY,CAAC,CAAC,GAAGL,UAAU;MACxD;MACA,IAAII,aAAa,KAAK,CAAC,EAAE;QACrBF,gBAAgB,EAAE;MACtB,CAAC,MACI,IAAIE,aAAa,KAAK,CAAC,EAAE;QAC1BH,YAAY,GAAGL,IAAI,CAACU,GAAG,CAACL,YAAY,EAAEC,gBAAgB,CAAC;QACvDA,gBAAgB,GAAG,CAAC;QACpBF,UAAU,GAAGtB,QAAQ,CAAC2B,YAAY,CAAC,CAAC;MACxC,CAAC,MACI,IAAI3B,QAAQ,CAAC2B,YAAY,CAAC,CAAC,IAAIrB,eAAe,CAACuB,WAAW,CAAC,CAAC,EAAE;QAC/DtB,SAAS,CAACkB,YAAY,CAAC,GAAG,IAAI;MAClC,CAAC,MACI;QACDH,UAAU,GAAGtB,QAAQ,CAAC2B,YAAY,CAAC,CAAC;QACpCH,gBAAgB,GAAG,CAAC;MACxB;IACJ;IACA;EACJ,CAAC;EACDpC,iCAAiC,CAACnB,SAAS,CAACiE,kBAAkB,GAAG,YAAY;IACzE,IAAIM,GAAG,EAAE5C,EAAE;IACX,IAAIW,SAAS,GAAG,IAAI,CAACT,YAAY,CAAC,CAAC;IACnC,IAAI2C,kBAAkB,GAAG,IAAItD,YAAY,CAAC,CAAC;IAC3C,IAAIuD,wBAAwB,GAAG,IAAIvD,YAAY,CAAC,CAAC;IACjD,IAAIwD,wBAAwB,GAAG,IAAIxD,YAAY,CAAC,CAAC;IACjD,IAAIyD,cAAc,GAAG,IAAIzD,YAAY,CAAC,CAAC;IACvC,IAAI;MACA,KAAK,IAAI0D,WAAW,GAAG1E,QAAQ,CAACoC,SAAS,CAAC,EAAEuC,aAAa,GAAGD,WAAW,CAACjE,IAAI,CAAC,CAAC,EAAE,CAACkE,aAAa,CAAChE,IAAI,EAAEgE,aAAa,GAAGD,WAAW,CAACjE,IAAI,CAAC,CAAC,EAAE;QACrI,IAAIoB,QAAQ,GAAG8C,aAAa,CAACjE,KAAK,CAAC;QACnC,IAAImB,QAAQ,IAAI,IAAI,EAAE;UAClB;QACJ;QACAA,QAAQ,CAACC,gCAAgC,CAAC,CAAC;QAC3C,IAAI8C,iBAAiB,GAAG/C,QAAQ,CAACgD,QAAQ,CAAC,CAAC,GAAG,EAAE;QAChD,IAAIC,iBAAiB,GAAGjD,QAAQ,CAAC2B,YAAY,CAAC,CAAC;QAC/C,IAAI,CAAC,IAAI,CAAClC,OAAO,EAAE;UACfwD,iBAAiB,IAAI,CAAC;QAC1B;QACA,QAAQA,iBAAiB,GAAG,CAAC;UACzB,KAAK,CAAC;YACFP,wBAAwB,CAACQ,QAAQ,CAACH,iBAAiB,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5D;UACJ,KAAK,CAAC;YACFH,cAAc,CAACM,QAAQ,CAACH,iBAAiB,GAAG,CAAC,CAAC;YAC9CJ,wBAAwB,CAACO,QAAQ,CAACH,iBAAiB,GAAG,CAAC,CAAC;YACxD;UACJ,KAAK,CAAC;YACFN,kBAAkB,CAACS,QAAQ,CAACH,iBAAiB,GAAG,CAAC,CAAC;YAClD;QACR;MACJ;IACJ,CAAC,CACD,OAAOI,KAAK,EAAE;MAAEX,GAAG,GAAG;QAAErC,KAAK,EAAEgD;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIL,aAAa,IAAI,CAACA,aAAa,CAAChE,IAAI,KAAKc,EAAE,GAAGiD,WAAW,CAACzC,MAAM,CAAC,EAAER,EAAE,CAAClB,IAAI,CAACmE,WAAW,CAAC;MAC/F,CAAC,SACO;QAAE,IAAIL,GAAG,EAAE,MAAMA,GAAG,CAACrC,KAAK;MAAE;IACxC;IACA;IACA,IAAKsC,kBAAkB,CAACO,QAAQ,CAAC,CAAC,CAACrE,MAAM,KAAK,CAAC,IAC1C+D,wBAAwB,CAACM,QAAQ,CAAC,CAAC,CAACrE,MAAM,KAAK,CAAE,IACjDgE,wBAAwB,CAACK,QAAQ,CAAC,CAAC,CAACrE,MAAM,KAAK,CAAE,IACjDiE,cAAc,CAACI,QAAQ,CAAC,CAAC,CAACrE,MAAM,KAAK,CAAE,IACxC8D,kBAAkB,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IACpCN,wBAAwB,CAACM,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGL,wBAAwB,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGhE,YAAY,CAACoE,mBAAmB,IAClHV,wBAAwB,CAACM,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGL,wBAAwB,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGhE,YAAY,CAACqE,mBAAmB,EAAE;MACpH,OAAO,IAAI;IACf;IACA,IAAI/C,eAAe,GAAG,IAAIrB,eAAe,CAACwD,kBAAkB,CAACO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEN,wBAAwB,CAACM,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEL,wBAAwB,CAACK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEJ,cAAc,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzL,IAAI,CAACxC,wBAAwB,CAACD,SAAS,EAAED,eAAe,CAAC;IACzD,OAAOA,eAAe;EAC1B,CAAC;EACDlB,iCAAiC,CAACnB,SAAS,CAACuC,wBAAwB,GAAG,UAAUD,SAAS,EAAED,eAAe,EAAE;IACzG;IACA;IACA,KAAK,IAAIgD,WAAW,CAAC,UAAU,CAAC,EAAEA,WAAW,GAAG/C,SAAS,CAAC5B,MAAM,EAAE2E,WAAW,EAAE,EAAE;MAC7E,IAAItD,QAAQ,GAAGO,SAAS,CAAC+C,WAAW,CAAC;MACrC,IAAI/C,SAAS,CAAC+C,WAAW,CAAC,IAAI,IAAI,EAAE;QAChC;MACJ;MACA,IAAIP,iBAAiB,GAAG/C,QAAQ,CAACgD,QAAQ,CAAC,CAAC,GAAG,EAAE;MAChD,IAAIC,iBAAiB,GAAGjD,QAAQ,CAAC2B,YAAY,CAAC,CAAC;MAC/C,IAAIsB,iBAAiB,GAAG3C,eAAe,CAACuB,WAAW,CAAC,CAAC,EAAE;QACnDtB,SAAS,CAAC+C,WAAW,CAAC,GAAG,IAAI;QAC7B;MACJ;MACA,IAAI,CAAC,IAAI,CAAC7D,OAAO,EAAE;QACfwD,iBAAiB,IAAI,CAAC;MAC1B;MACA,QAAQA,iBAAiB,GAAG,CAAC;QACzB,KAAK,CAAC;UACF,IAAIF,iBAAiB,GAAG,CAAC,GAAG,CAAC,KAAKzC,eAAe,CAACiD,oBAAoB,CAAC,CAAC,EAAE;YACtEhD,SAAS,CAAC+C,WAAW,CAAC,GAAG,IAAI;UACjC;UACA;QACJ,KAAK,CAAC;UACF,IAAIpC,IAAI,CAACC,KAAK,CAAC4B,iBAAiB,GAAG,CAAC,CAAC,KAAKzC,eAAe,CAACkD,uBAAuB,CAAC,CAAC,IAC/ET,iBAAiB,GAAG,CAAC,KAAKzC,eAAe,CAACmD,oBAAoB,CAAC,CAAC,EAAE;YAClElD,SAAS,CAAC+C,WAAW,CAAC,GAAG,IAAI;UACjC;UACA;QACJ,KAAK,CAAC;UACF,IAAIP,iBAAiB,GAAG,CAAC,KAAKzC,eAAe,CAACoD,cAAc,CAAC,CAAC,EAAE;YAC5DnD,SAAS,CAAC+C,WAAW,CAAC,GAAG,IAAI;UACjC;UACA;MACR;IACJ;EACJ,CAAC;EACDlE,iCAAiC,CAACnB,SAAS,CAACsB,MAAM,GAAG,YAAY;IAC7D,OAAO,IAAI,CAACE,OAAO;EACvB,CAAC;EACD;EACAL,iCAAiC,CAACnB,SAAS,CAAC0F,QAAQ,GAAG,YAAY;IAC/D,OAAO,UAAU,GAAG,IAAI,CAAClE,OAAO,GAAG,IAAI,GAAGJ,MAAM,CAACpB,SAAS,CAAC0F,QAAQ,CAACjF,IAAI,CAAC,IAAI,CAAC;EAClF,CAAC;EACD,OAAOU,iCAAiC;AAC5C,CAAC,CAACF,qBAAqB,CAAE;AACzB,eAAeE,iCAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}