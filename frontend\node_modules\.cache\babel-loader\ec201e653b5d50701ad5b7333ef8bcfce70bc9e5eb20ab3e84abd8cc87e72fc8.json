{"ast": null, "code": "import * as React from 'react';\nexport function usePickerOwnerState(parameters) {\n  const {\n    props,\n    pickerValueResponse\n  } = parameters;\n  return React.useMemo(() => ({\n    value: pickerValueResponse.viewProps.value,\n    open: pickerValueResponse.open,\n    disabled: props.disabled ?? false,\n    readOnly: props.readOnly ?? false\n  }), [pickerValueResponse.viewProps.value, pickerValueResponse.open, props.disabled, props.readOnly]);\n}", "map": {"version": 3, "names": ["React", "usePickerOwnerState", "parameters", "props", "pickerValueResponse", "useMemo", "value", "viewProps", "open", "disabled", "readOnly"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePickerOwnerState.js"], "sourcesContent": ["import * as React from 'react';\nexport function usePickerOwnerState(parameters) {\n  const {\n    props,\n    pickerValueResponse\n  } = parameters;\n  return React.useMemo(() => ({\n    value: pickerValueResponse.viewProps.value,\n    open: pickerValueResponse.open,\n    disabled: props.disabled ?? false,\n    readOnly: props.readOnly ?? false\n  }), [pickerValueResponse.viewProps.value, pickerValueResponse.open, props.disabled, props.readOnly]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EAC9C,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGF,UAAU;EACd,OAAOF,KAAK,CAACK,OAAO,CAAC,OAAO;IAC1BC,KAAK,EAAEF,mBAAmB,CAACG,SAAS,CAACD,KAAK;IAC1CE,IAAI,EAAEJ,mBAAmB,CAACI,IAAI;IAC9BC,QAAQ,EAAEN,KAAK,CAACM,QAAQ,IAAI,KAAK;IACjCC,QAAQ,EAAEP,KAAK,CAACO,QAAQ,IAAI;EAC9B,CAAC,CAAC,EAAE,CAACN,mBAAmB,CAACG,SAAS,CAACD,KAAK,EAAEF,mBAAmB,CAACI,IAAI,EAAEL,KAAK,CAACM,QAAQ,EAAEN,KAAK,CAACO,QAAQ,CAAC,CAAC;AACtG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}