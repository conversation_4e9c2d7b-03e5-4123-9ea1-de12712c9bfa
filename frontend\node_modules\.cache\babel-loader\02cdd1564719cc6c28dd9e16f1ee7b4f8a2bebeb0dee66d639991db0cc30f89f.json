{"ast": null, "code": "/*\n* Copyright 2009 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import com.google.zxing.NotFoundException;\n// import com.google.zxing.ResultPoint;\nimport ResultPoint from '../../ResultPoint';\nimport System from '../../util/System';\nimport Arrays from '../../util/Arrays';\nimport PDF417DetectorResult from './PDF417DetectorResult';\n// import java.util.ArrayList;\n// import java.util.Arrays;\n// import java.util.List;\n// import java.util.Map;\n/**\n * <p>Encapsulates logic that can detect a PDF417 Code in an image, even if the\n * PDF417 Code is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR> Lab (<EMAIL>)\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Grau\n */\nvar Detector = /** @class */function () {\n  function Detector() {}\n  /**\n   * <p>Detects a PDF417 Code in an image. Only checks 0 and 180 degree rotations.</p>\n   *\n   * @param image barcode image to decode\n   * @param hints optional hints to detector\n   * @param multiple if true, then the image is searched for multiple codes. If false, then at most one code will\n   * be found and returned\n   * @return {@link PDF417DetectorResult} encapsulating results of detecting a PDF417 code\n   * @throws NotFoundException if no PDF417 Code can be found\n   */\n  Detector.detectMultiple = function (image, hints, multiple) {\n    // TODO detection improvement, tryHarder could try several different luminance thresholds/blackpoints or even\n    // different binarizers\n    // boolean tryHarder = hints != null && hints.containsKey(DecodeHintType.TRY_HARDER);\n    var bitMatrix = image.getBlackMatrix();\n    var barcodeCoordinates = Detector.detect(multiple, bitMatrix);\n    if (!barcodeCoordinates.length) {\n      bitMatrix = bitMatrix.clone();\n      bitMatrix.rotate180();\n      barcodeCoordinates = Detector.detect(multiple, bitMatrix);\n    }\n    return new PDF417DetectorResult(bitMatrix, barcodeCoordinates);\n  };\n  /**\n   * Detects PDF417 codes in an image. Only checks 0 degree rotation\n   * @param multiple if true, then the image is searched for multiple codes. If false, then at most one code will\n   * be found and returned\n   * @param bitMatrix bit matrix to detect barcodes in\n   * @return List of ResultPoint arrays containing the coordinates of found barcodes\n   */\n  Detector.detect = function (multiple, bitMatrix) {\n    var e_1, _a;\n    var barcodeCoordinates = new Array();\n    var row = 0;\n    var column = 0;\n    var foundBarcodeInRow = false;\n    while (row < bitMatrix.getHeight()) {\n      var vertices = Detector.findVertices(bitMatrix, row, column);\n      if (vertices[0] == null && vertices[3] == null) {\n        if (!foundBarcodeInRow) {\n          // we didn't find any barcode so that's the end of searching\n          break;\n        }\n        // we didn't find a barcode starting at the given column and row. Try again from the first column and slightly\n        // below the lowest barcode we found so far.\n        foundBarcodeInRow = false;\n        column = 0;\n        try {\n          for (var barcodeCoordinates_1 = (e_1 = void 0, __values(barcodeCoordinates)), barcodeCoordinates_1_1 = barcodeCoordinates_1.next(); !barcodeCoordinates_1_1.done; barcodeCoordinates_1_1 = barcodeCoordinates_1.next()) {\n            var barcodeCoordinate = barcodeCoordinates_1_1.value;\n            if (barcodeCoordinate[1] != null) {\n              row = Math.trunc(Math.max(row, barcodeCoordinate[1].getY()));\n            }\n            if (barcodeCoordinate[3] != null) {\n              row = Math.max(row, Math.trunc(barcodeCoordinate[3].getY()));\n            }\n          }\n        } catch (e_1_1) {\n          e_1 = {\n            error: e_1_1\n          };\n        } finally {\n          try {\n            if (barcodeCoordinates_1_1 && !barcodeCoordinates_1_1.done && (_a = barcodeCoordinates_1.return)) _a.call(barcodeCoordinates_1);\n          } finally {\n            if (e_1) throw e_1.error;\n          }\n        }\n        row += Detector.ROW_STEP;\n        continue;\n      }\n      foundBarcodeInRow = true;\n      barcodeCoordinates.push(vertices);\n      if (!multiple) {\n        break;\n      }\n      // if we didn't find a right row indicator column, then continue the search for the next barcode after the\n      // start pattern of the barcode just found.\n      if (vertices[2] != null) {\n        column = Math.trunc(vertices[2].getX());\n        row = Math.trunc(vertices[2].getY());\n      } else {\n        column = Math.trunc(vertices[4].getX());\n        row = Math.trunc(vertices[4].getY());\n      }\n    }\n    return barcodeCoordinates;\n  };\n  /**\n   * Locate the vertices and the codewords area of a black blob using the Start\n   * and Stop patterns as locators.\n   *\n   * @param matrix the scanned barcode image.\n   * @return an array containing the vertices:\n   *           vertices[0] x, y top left barcode\n   *           vertices[1] x, y bottom left barcode\n   *           vertices[2] x, y top right barcode\n   *           vertices[3] x, y bottom right barcode\n   *           vertices[4] x, y top left codeword area\n   *           vertices[5] x, y bottom left codeword area\n   *           vertices[6] x, y top right codeword area\n   *           vertices[7] x, y bottom right codeword area\n   */\n  Detector.findVertices = function (matrix, startRow, startColumn) {\n    var height = matrix.getHeight();\n    var width = matrix.getWidth();\n    // const result = new ResultPoint[8];\n    var result = new Array(8);\n    Detector.copyToResult(result, Detector.findRowsWithPattern(matrix, height, width, startRow, startColumn, Detector.START_PATTERN), Detector.INDEXES_START_PATTERN);\n    if (result[4] != null) {\n      startColumn = Math.trunc(result[4].getX());\n      startRow = Math.trunc(result[4].getY());\n    }\n    Detector.copyToResult(result, Detector.findRowsWithPattern(matrix, height, width, startRow, startColumn, Detector.STOP_PATTERN), Detector.INDEXES_STOP_PATTERN);\n    return result;\n  };\n  Detector.copyToResult = function (result, tmpResult, destinationIndexes) {\n    for (var i = 0; i < destinationIndexes.length; i++) {\n      result[destinationIndexes[i]] = tmpResult[i];\n    }\n  };\n  Detector.findRowsWithPattern = function (matrix, height, width, startRow, startColumn, pattern) {\n    // const result = new ResultPoint[4];\n    var result = new Array(4);\n    var found = false;\n    var counters = new Int32Array(pattern.length);\n    for (; startRow < height; startRow += Detector.ROW_STEP) {\n      var loc = Detector.findGuardPattern(matrix, startColumn, startRow, width, false, pattern, counters);\n      if (loc != null) {\n        while (startRow > 0) {\n          var previousRowLoc = Detector.findGuardPattern(matrix, startColumn, --startRow, width, false, pattern, counters);\n          if (previousRowLoc != null) {\n            loc = previousRowLoc;\n          } else {\n            startRow++;\n            break;\n          }\n        }\n        result[0] = new ResultPoint(loc[0], startRow);\n        result[1] = new ResultPoint(loc[1], startRow);\n        found = true;\n        break;\n      }\n    }\n    var stopRow = startRow + 1;\n    // Last row of the current symbol that contains pattern\n    if (found) {\n      var skippedRowCount = 0;\n      var previousRowLoc = Int32Array.from([Math.trunc(result[0].getX()), Math.trunc(result[1].getX())]);\n      for (; stopRow < height; stopRow++) {\n        var loc = Detector.findGuardPattern(matrix, previousRowLoc[0], stopRow, width, false, pattern, counters);\n        // a found pattern is only considered to belong to the same barcode if the start and end positions\n        // don't differ too much. Pattern drift should be not bigger than two for consecutive rows. With\n        // a higher number of skipped rows drift could be larger. To keep it simple for now, we allow a slightly\n        // larger drift and don't check for skipped rows.\n        if (loc != null && Math.abs(previousRowLoc[0] - loc[0]) < Detector.MAX_PATTERN_DRIFT && Math.abs(previousRowLoc[1] - loc[1]) < Detector.MAX_PATTERN_DRIFT) {\n          previousRowLoc = loc;\n          skippedRowCount = 0;\n        } else {\n          if (skippedRowCount > Detector.SKIPPED_ROW_COUNT_MAX) {\n            break;\n          } else {\n            skippedRowCount++;\n          }\n        }\n      }\n      stopRow -= skippedRowCount + 1;\n      result[2] = new ResultPoint(previousRowLoc[0], stopRow);\n      result[3] = new ResultPoint(previousRowLoc[1], stopRow);\n    }\n    if (stopRow - startRow < Detector.BARCODE_MIN_HEIGHT) {\n      Arrays.fill(result, null);\n    }\n    return result;\n  };\n  /**\n   * @param matrix row of black/white values to search\n   * @param column x position to start search\n   * @param row y position to start search\n   * @param width the number of pixels to search on this row\n   * @param pattern pattern of counts of number of black and white pixels that are\n   *                 being searched for as a pattern\n   * @param counters array of counters, as long as pattern, to re-use\n   * @return start/end horizontal offset of guard pattern, as an array of two ints.\n   */\n  Detector.findGuardPattern = function (matrix, column, row, width, whiteFirst, pattern, counters) {\n    Arrays.fillWithin(counters, 0, counters.length, 0);\n    var patternStart = column;\n    var pixelDrift = 0;\n    // if there are black pixels left of the current pixel shift to the left, but only for MAX_PIXEL_DRIFT pixels\n    while (matrix.get(patternStart, row) && patternStart > 0 && pixelDrift++ < Detector.MAX_PIXEL_DRIFT) {\n      patternStart--;\n    }\n    var x = patternStart;\n    var counterPosition = 0;\n    var patternLength = pattern.length;\n    for (var isWhite = whiteFirst; x < width; x++) {\n      var pixel = matrix.get(x, row);\n      if (pixel !== isWhite) {\n        counters[counterPosition]++;\n      } else {\n        if (counterPosition === patternLength - 1) {\n          if (Detector.patternMatchVariance(counters, pattern, Detector.MAX_INDIVIDUAL_VARIANCE) < Detector.MAX_AVG_VARIANCE) {\n            return new Int32Array([patternStart, x]);\n          }\n          patternStart += counters[0] + counters[1];\n          System.arraycopy(counters, 2, counters, 0, counterPosition - 1);\n          counters[counterPosition - 1] = 0;\n          counters[counterPosition] = 0;\n          counterPosition--;\n        } else {\n          counterPosition++;\n        }\n        counters[counterPosition] = 1;\n        isWhite = !isWhite;\n      }\n    }\n    if (counterPosition === patternLength - 1 && Detector.patternMatchVariance(counters, pattern, Detector.MAX_INDIVIDUAL_VARIANCE) < Detector.MAX_AVG_VARIANCE) {\n      return new Int32Array([patternStart, x - 1]);\n    }\n    return null;\n  };\n  /**\n   * Determines how closely a set of observed counts of runs of black/white\n   * values matches a given target pattern. This is reported as the ratio of\n   * the total variance from the expected pattern proportions across all\n   * pattern elements, to the length of the pattern.\n   *\n   * @param counters observed counters\n   * @param pattern expected pattern\n   * @param maxIndividualVariance The most any counter can differ before we give up\n   * @return ratio of total variance between counters and pattern compared to total pattern size\n   */\n  Detector.patternMatchVariance = function (counters, pattern, maxIndividualVariance) {\n    var numCounters = counters.length;\n    var total = 0;\n    var patternLength = 0;\n    for (var i = 0; i < numCounters; i++) {\n      total += counters[i];\n      patternLength += pattern[i];\n    }\n    if (total < patternLength) {\n      // If we don't even have one pixel per unit of bar width, assume this\n      // is too small to reliably match, so fail:\n      return /*Float.POSITIVE_INFINITY*/Infinity;\n    }\n    // We're going to fake floating-point math in integers. We just need to use more bits.\n    // Scale up patternLength so that intermediate values below like scaledCounter will have\n    // more \"significant digits\".\n    var unitBarWidth = total / patternLength;\n    maxIndividualVariance *= unitBarWidth;\n    var totalVariance = 0.0;\n    for (var x = 0; x < numCounters; x++) {\n      var counter = counters[x];\n      var scaledPattern = pattern[x] * unitBarWidth;\n      var variance = counter > scaledPattern ? counter - scaledPattern : scaledPattern - counter;\n      if (variance > maxIndividualVariance) {\n        return /*Float.POSITIVE_INFINITY*/Infinity;\n      }\n      totalVariance += variance;\n    }\n    return totalVariance / total;\n  };\n  Detector.INDEXES_START_PATTERN = Int32Array.from([0, 4, 1, 5]);\n  Detector.INDEXES_STOP_PATTERN = Int32Array.from([6, 2, 7, 3]);\n  Detector.MAX_AVG_VARIANCE = 0.42;\n  Detector.MAX_INDIVIDUAL_VARIANCE = 0.8;\n  // B S B S B S B S Bar/Space pattern\n  // 11111111 0 1 0 1 0 1 000\n  Detector.START_PATTERN = Int32Array.from([8, 1, 1, 1, 1, 1, 1, 3]);\n  // 1111111 0 1 000 1 0 1 00 1\n  Detector.STOP_PATTERN = Int32Array.from([7, 1, 1, 3, 1, 1, 1, 2, 1]);\n  Detector.MAX_PIXEL_DRIFT = 3;\n  Detector.MAX_PATTERN_DRIFT = 5;\n  // if we set the value too low, then we don't detect the correct height of the bar if the start patterns are damaged.\n  // if we set the value too high, then we might detect the start pattern from a neighbor barcode.\n  Detector.SKIPPED_ROW_COUNT_MAX = 25;\n  // A PDF471 barcode should have at least 3 rows, with each row being >= 3 times the module width. Therefore it should be at least\n  // 9 pixels tall. To be conservative, we use about half the size to ensure we don't miss it.\n  Detector.ROW_STEP = 5;\n  Detector.BARCODE_MIN_HEIGHT = 10;\n  return Detector;\n}();\nexport default Detector;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "ResultPoint", "System", "<PERSON><PERSON><PERSON>", "PDF417DetectorResult", "Detector", "detectMultiple", "image", "hints", "multiple", "bitMatrix", "getBlackMatrix", "barcodeCoordinates", "detect", "clone", "rotate180", "e_1", "_a", "Array", "row", "column", "foundBarcodeInRow", "getHeight", "vertices", "findVertices", "barcodeCoordinates_1", "barcodeCoordinates_1_1", "barcodeCoordinate", "Math", "trunc", "max", "getY", "e_1_1", "error", "return", "ROW_STEP", "push", "getX", "matrix", "startRow", "startColumn", "height", "width", "getWidth", "result", "copyToResult", "findRowsWithPattern", "START_PATTERN", "INDEXES_START_PATTERN", "STOP_PATTERN", "INDEXES_STOP_PATTERN", "tmpResult", "destinationIndexes", "pattern", "found", "counters", "Int32Array", "loc", "<PERSON><PERSON><PERSON><PERSON>att<PERSON>", "previousRowLoc", "stopRow", "skippedRowCount", "from", "abs", "MAX_PATTERN_DRIFT", "SKIPPED_ROW_COUNT_MAX", "BARCODE_MIN_HEIGHT", "fill", "white<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "patternStart", "pixelDrift", "get", "MAX_PIXEL_DRIFT", "x", "counterPosition", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pixel", "patternMatchVariance", "MAX_INDIVIDUAL_VARIANCE", "MAX_AVG_VARIANCE", "arraycopy", "maxIndividualV<PERSON>ce", "numCounters", "total", "Infinity", "unitBarWidth", "totalVariance", "counter", "scaledPattern", "variance"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/detector/Detector.js"], "sourcesContent": ["/*\n* Copyright 2009 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// import com.google.zxing.NotFoundException;\n// import com.google.zxing.ResultPoint;\nimport ResultPoint from '../../ResultPoint';\nimport System from '../../util/System';\nimport Arrays from '../../util/Arrays';\nimport PDF417DetectorResult from './PDF417DetectorResult';\n// import java.util.ArrayList;\n// import java.util.Arrays;\n// import java.util.List;\n// import java.util.Map;\n/**\n * <p>Encapsulates logic that can detect a PDF417 Code in an image, even if the\n * PDF417 Code is rotated or skewed, or partially obscured.</p>\n *\n * <AUTHOR> Lab (<EMAIL>)\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Grau\n */\nvar Detector = /** @class */ (function () {\n    function Detector() {\n    }\n    /**\n     * <p>Detects a PDF417 Code in an image. Only checks 0 and 180 degree rotations.</p>\n     *\n     * @param image barcode image to decode\n     * @param hints optional hints to detector\n     * @param multiple if true, then the image is searched for multiple codes. If false, then at most one code will\n     * be found and returned\n     * @return {@link PDF417DetectorResult} encapsulating results of detecting a PDF417 code\n     * @throws NotFoundException if no PDF417 Code can be found\n     */\n    Detector.detectMultiple = function (image, hints, multiple) {\n        // TODO detection improvement, tryHarder could try several different luminance thresholds/blackpoints or even\n        // different binarizers\n        // boolean tryHarder = hints != null && hints.containsKey(DecodeHintType.TRY_HARDER);\n        var bitMatrix = image.getBlackMatrix();\n        var barcodeCoordinates = Detector.detect(multiple, bitMatrix);\n        if (!barcodeCoordinates.length) {\n            bitMatrix = bitMatrix.clone();\n            bitMatrix.rotate180();\n            barcodeCoordinates = Detector.detect(multiple, bitMatrix);\n        }\n        return new PDF417DetectorResult(bitMatrix, barcodeCoordinates);\n    };\n    /**\n     * Detects PDF417 codes in an image. Only checks 0 degree rotation\n     * @param multiple if true, then the image is searched for multiple codes. If false, then at most one code will\n     * be found and returned\n     * @param bitMatrix bit matrix to detect barcodes in\n     * @return List of ResultPoint arrays containing the coordinates of found barcodes\n     */\n    Detector.detect = function (multiple, bitMatrix) {\n        var e_1, _a;\n        var barcodeCoordinates = new Array();\n        var row = 0;\n        var column = 0;\n        var foundBarcodeInRow = false;\n        while (row < bitMatrix.getHeight()) {\n            var vertices = Detector.findVertices(bitMatrix, row, column);\n            if (vertices[0] == null && vertices[3] == null) {\n                if (!foundBarcodeInRow) {\n                    // we didn't find any barcode so that's the end of searching\n                    break;\n                }\n                // we didn't find a barcode starting at the given column and row. Try again from the first column and slightly\n                // below the lowest barcode we found so far.\n                foundBarcodeInRow = false;\n                column = 0;\n                try {\n                    for (var barcodeCoordinates_1 = (e_1 = void 0, __values(barcodeCoordinates)), barcodeCoordinates_1_1 = barcodeCoordinates_1.next(); !barcodeCoordinates_1_1.done; barcodeCoordinates_1_1 = barcodeCoordinates_1.next()) {\n                        var barcodeCoordinate = barcodeCoordinates_1_1.value;\n                        if (barcodeCoordinate[1] != null) {\n                            row = Math.trunc(Math.max(row, barcodeCoordinate[1].getY()));\n                        }\n                        if (barcodeCoordinate[3] != null) {\n                            row = Math.max(row, Math.trunc(barcodeCoordinate[3].getY()));\n                        }\n                    }\n                }\n                catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                finally {\n                    try {\n                        if (barcodeCoordinates_1_1 && !barcodeCoordinates_1_1.done && (_a = barcodeCoordinates_1.return)) _a.call(barcodeCoordinates_1);\n                    }\n                    finally { if (e_1) throw e_1.error; }\n                }\n                row += Detector.ROW_STEP;\n                continue;\n            }\n            foundBarcodeInRow = true;\n            barcodeCoordinates.push(vertices);\n            if (!multiple) {\n                break;\n            }\n            // if we didn't find a right row indicator column, then continue the search for the next barcode after the\n            // start pattern of the barcode just found.\n            if (vertices[2] != null) {\n                column = Math.trunc(vertices[2].getX());\n                row = Math.trunc(vertices[2].getY());\n            }\n            else {\n                column = Math.trunc(vertices[4].getX());\n                row = Math.trunc(vertices[4].getY());\n            }\n        }\n        return barcodeCoordinates;\n    };\n    /**\n     * Locate the vertices and the codewords area of a black blob using the Start\n     * and Stop patterns as locators.\n     *\n     * @param matrix the scanned barcode image.\n     * @return an array containing the vertices:\n     *           vertices[0] x, y top left barcode\n     *           vertices[1] x, y bottom left barcode\n     *           vertices[2] x, y top right barcode\n     *           vertices[3] x, y bottom right barcode\n     *           vertices[4] x, y top left codeword area\n     *           vertices[5] x, y bottom left codeword area\n     *           vertices[6] x, y top right codeword area\n     *           vertices[7] x, y bottom right codeword area\n     */\n    Detector.findVertices = function (matrix, startRow, startColumn) {\n        var height = matrix.getHeight();\n        var width = matrix.getWidth();\n        // const result = new ResultPoint[8];\n        var result = new Array(8);\n        Detector.copyToResult(result, Detector.findRowsWithPattern(matrix, height, width, startRow, startColumn, Detector.START_PATTERN), Detector.INDEXES_START_PATTERN);\n        if (result[4] != null) {\n            startColumn = Math.trunc(result[4].getX());\n            startRow = Math.trunc(result[4].getY());\n        }\n        Detector.copyToResult(result, Detector.findRowsWithPattern(matrix, height, width, startRow, startColumn, Detector.STOP_PATTERN), Detector.INDEXES_STOP_PATTERN);\n        return result;\n    };\n    Detector.copyToResult = function (result, tmpResult, destinationIndexes) {\n        for (var i = 0; i < destinationIndexes.length; i++) {\n            result[destinationIndexes[i]] = tmpResult[i];\n        }\n    };\n    Detector.findRowsWithPattern = function (matrix, height, width, startRow, startColumn, pattern) {\n        // const result = new ResultPoint[4];\n        var result = new Array(4);\n        var found = false;\n        var counters = new Int32Array(pattern.length);\n        for (; startRow < height; startRow += Detector.ROW_STEP) {\n            var loc = Detector.findGuardPattern(matrix, startColumn, startRow, width, false, pattern, counters);\n            if (loc != null) {\n                while (startRow > 0) {\n                    var previousRowLoc = Detector.findGuardPattern(matrix, startColumn, --startRow, width, false, pattern, counters);\n                    if (previousRowLoc != null) {\n                        loc = previousRowLoc;\n                    }\n                    else {\n                        startRow++;\n                        break;\n                    }\n                }\n                result[0] = new ResultPoint(loc[0], startRow);\n                result[1] = new ResultPoint(loc[1], startRow);\n                found = true;\n                break;\n            }\n        }\n        var stopRow = startRow + 1;\n        // Last row of the current symbol that contains pattern\n        if (found) {\n            var skippedRowCount = 0;\n            var previousRowLoc = Int32Array.from([Math.trunc(result[0].getX()), Math.trunc(result[1].getX())]);\n            for (; stopRow < height; stopRow++) {\n                var loc = Detector.findGuardPattern(matrix, previousRowLoc[0], stopRow, width, false, pattern, counters);\n                // a found pattern is only considered to belong to the same barcode if the start and end positions\n                // don't differ too much. Pattern drift should be not bigger than two for consecutive rows. With\n                // a higher number of skipped rows drift could be larger. To keep it simple for now, we allow a slightly\n                // larger drift and don't check for skipped rows.\n                if (loc != null &&\n                    Math.abs(previousRowLoc[0] - loc[0]) < Detector.MAX_PATTERN_DRIFT &&\n                    Math.abs(previousRowLoc[1] - loc[1]) < Detector.MAX_PATTERN_DRIFT) {\n                    previousRowLoc = loc;\n                    skippedRowCount = 0;\n                }\n                else {\n                    if (skippedRowCount > Detector.SKIPPED_ROW_COUNT_MAX) {\n                        break;\n                    }\n                    else {\n                        skippedRowCount++;\n                    }\n                }\n            }\n            stopRow -= skippedRowCount + 1;\n            result[2] = new ResultPoint(previousRowLoc[0], stopRow);\n            result[3] = new ResultPoint(previousRowLoc[1], stopRow);\n        }\n        if (stopRow - startRow < Detector.BARCODE_MIN_HEIGHT) {\n            Arrays.fill(result, null);\n        }\n        return result;\n    };\n    /**\n     * @param matrix row of black/white values to search\n     * @param column x position to start search\n     * @param row y position to start search\n     * @param width the number of pixels to search on this row\n     * @param pattern pattern of counts of number of black and white pixels that are\n     *                 being searched for as a pattern\n     * @param counters array of counters, as long as pattern, to re-use\n     * @return start/end horizontal offset of guard pattern, as an array of two ints.\n     */\n    Detector.findGuardPattern = function (matrix, column, row, width, whiteFirst, pattern, counters) {\n        Arrays.fillWithin(counters, 0, counters.length, 0);\n        var patternStart = column;\n        var pixelDrift = 0;\n        // if there are black pixels left of the current pixel shift to the left, but only for MAX_PIXEL_DRIFT pixels\n        while (matrix.get(patternStart, row) && patternStart > 0 && pixelDrift++ < Detector.MAX_PIXEL_DRIFT) {\n            patternStart--;\n        }\n        var x = patternStart;\n        var counterPosition = 0;\n        var patternLength = pattern.length;\n        for (var isWhite = whiteFirst; x < width; x++) {\n            var pixel = matrix.get(x, row);\n            if (pixel !== isWhite) {\n                counters[counterPosition]++;\n            }\n            else {\n                if (counterPosition === patternLength - 1) {\n                    if (Detector.patternMatchVariance(counters, pattern, Detector.MAX_INDIVIDUAL_VARIANCE) < Detector.MAX_AVG_VARIANCE) {\n                        return new Int32Array([patternStart, x]);\n                    }\n                    patternStart += counters[0] + counters[1];\n                    System.arraycopy(counters, 2, counters, 0, counterPosition - 1);\n                    counters[counterPosition - 1] = 0;\n                    counters[counterPosition] = 0;\n                    counterPosition--;\n                }\n                else {\n                    counterPosition++;\n                }\n                counters[counterPosition] = 1;\n                isWhite = !isWhite;\n            }\n        }\n        if (counterPosition === patternLength - 1 &&\n            Detector.patternMatchVariance(counters, pattern, Detector.MAX_INDIVIDUAL_VARIANCE) < Detector.MAX_AVG_VARIANCE) {\n            return new Int32Array([patternStart, x - 1]);\n        }\n        return null;\n    };\n    /**\n     * Determines how closely a set of observed counts of runs of black/white\n     * values matches a given target pattern. This is reported as the ratio of\n     * the total variance from the expected pattern proportions across all\n     * pattern elements, to the length of the pattern.\n     *\n     * @param counters observed counters\n     * @param pattern expected pattern\n     * @param maxIndividualVariance The most any counter can differ before we give up\n     * @return ratio of total variance between counters and pattern compared to total pattern size\n     */\n    Detector.patternMatchVariance = function (counters, pattern, maxIndividualVariance) {\n        var numCounters = counters.length;\n        var total = 0;\n        var patternLength = 0;\n        for (var i = 0; i < numCounters; i++) {\n            total += counters[i];\n            patternLength += pattern[i];\n        }\n        if (total < patternLength) {\n            // If we don't even have one pixel per unit of bar width, assume this\n            // is too small to reliably match, so fail:\n            return /*Float.POSITIVE_INFINITY*/ Infinity;\n        }\n        // We're going to fake floating-point math in integers. We just need to use more bits.\n        // Scale up patternLength so that intermediate values below like scaledCounter will have\n        // more \"significant digits\".\n        var unitBarWidth = total / patternLength;\n        maxIndividualVariance *= unitBarWidth;\n        var totalVariance = 0.0;\n        for (var x = 0; x < numCounters; x++) {\n            var counter = counters[x];\n            var scaledPattern = pattern[x] * unitBarWidth;\n            var variance = counter > scaledPattern ? counter - scaledPattern : scaledPattern - counter;\n            if (variance > maxIndividualVariance) {\n                return /*Float.POSITIVE_INFINITY*/ Infinity;\n            }\n            totalVariance += variance;\n        }\n        return totalVariance / total;\n    };\n    Detector.INDEXES_START_PATTERN = Int32Array.from([0, 4, 1, 5]);\n    Detector.INDEXES_STOP_PATTERN = Int32Array.from([6, 2, 7, 3]);\n    Detector.MAX_AVG_VARIANCE = 0.42;\n    Detector.MAX_INDIVIDUAL_VARIANCE = 0.8;\n    // B S B S B S B S Bar/Space pattern\n    // 11111111 0 1 0 1 0 1 000\n    Detector.START_PATTERN = Int32Array.from([8, 1, 1, 1, 1, 1, 1, 3]);\n    // 1111111 0 1 000 1 0 1 00 1\n    Detector.STOP_PATTERN = Int32Array.from([7, 1, 1, 3, 1, 1, 1, 2, 1]);\n    Detector.MAX_PIXEL_DRIFT = 3;\n    Detector.MAX_PATTERN_DRIFT = 5;\n    // if we set the value too low, then we don't detect the correct height of the bar if the start patterns are damaged.\n    // if we set the value too high, then we might detect the start pattern from a neighbor barcode.\n    Detector.SKIPPED_ROW_COUNT_MAX = 25;\n    // A PDF471 barcode should have at least 3 rows, with each row being >= 3 times the module width. Therefore it should be at least\n    // 9 pixels tall. To be conservative, we use about half the size to ensure we don't miss it.\n    Detector.ROW_STEP = 5;\n    Detector.BARCODE_MIN_HEIGHT = 10;\n    return Detector;\n}());\nexport default Detector;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA,OAAOW,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAAA,EAAG,CACpB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,QAAQ,CAACC,cAAc,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACxD;IACA;IACA;IACA,IAAIC,SAAS,GAAGH,KAAK,CAACI,cAAc,CAAC,CAAC;IACtC,IAAIC,kBAAkB,GAAGP,QAAQ,CAACQ,MAAM,CAACJ,QAAQ,EAAEC,SAAS,CAAC;IAC7D,IAAI,CAACE,kBAAkB,CAAChB,MAAM,EAAE;MAC5Bc,SAAS,GAAGA,SAAS,CAACI,KAAK,CAAC,CAAC;MAC7BJ,SAAS,CAACK,SAAS,CAAC,CAAC;MACrBH,kBAAkB,GAAGP,QAAQ,CAACQ,MAAM,CAACJ,QAAQ,EAAEC,SAAS,CAAC;IAC7D;IACA,OAAO,IAAIN,oBAAoB,CAACM,SAAS,EAAEE,kBAAkB,CAAC;EAClE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIP,QAAQ,CAACQ,MAAM,GAAG,UAAUJ,QAAQ,EAAEC,SAAS,EAAE;IAC7C,IAAIM,GAAG,EAAEC,EAAE;IACX,IAAIL,kBAAkB,GAAG,IAAIM,KAAK,CAAC,CAAC;IACpC,IAAIC,GAAG,GAAG,CAAC;IACX,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,OAAOF,GAAG,GAAGT,SAAS,CAACY,SAAS,CAAC,CAAC,EAAE;MAChC,IAAIC,QAAQ,GAAGlB,QAAQ,CAACmB,YAAY,CAACd,SAAS,EAAES,GAAG,EAAEC,MAAM,CAAC;MAC5D,IAAIG,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,IAAIA,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QAC5C,IAAI,CAACF,iBAAiB,EAAE;UACpB;UACA;QACJ;QACA;QACA;QACAA,iBAAiB,GAAG,KAAK;QACzBD,MAAM,GAAG,CAAC;QACV,IAAI;UACA,KAAK,IAAIK,oBAAoB,IAAIT,GAAG,GAAG,KAAK,CAAC,EAAE5B,QAAQ,CAACwB,kBAAkB,CAAC,CAAC,EAAEc,sBAAsB,GAAGD,oBAAoB,CAAC5B,IAAI,CAAC,CAAC,EAAE,CAAC6B,sBAAsB,CAAC3B,IAAI,EAAE2B,sBAAsB,GAAGD,oBAAoB,CAAC5B,IAAI,CAAC,CAAC,EAAE;YACpN,IAAI8B,iBAAiB,GAAGD,sBAAsB,CAAC5B,KAAK;YACpD,IAAI6B,iBAAiB,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;cAC9BR,GAAG,GAAGS,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACX,GAAG,EAAEQ,iBAAiB,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;YAChE;YACA,IAAIJ,iBAAiB,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;cAC9BR,GAAG,GAAGS,IAAI,CAACE,GAAG,CAACX,GAAG,EAAES,IAAI,CAACC,KAAK,CAACF,iBAAiB,CAAC,CAAC,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,CAAC;YAChE;UACJ;QACJ,CAAC,CACD,OAAOC,KAAK,EAAE;UAAEhB,GAAG,GAAG;YAAEiB,KAAK,EAAED;UAAM,CAAC;QAAE,CAAC,SACjC;UACJ,IAAI;YACA,IAAIN,sBAAsB,IAAI,CAACA,sBAAsB,CAAC3B,IAAI,KAAKkB,EAAE,GAAGQ,oBAAoB,CAACS,MAAM,CAAC,EAAEjB,EAAE,CAACtB,IAAI,CAAC8B,oBAAoB,CAAC;UACnI,CAAC,SACO;YAAE,IAAIT,GAAG,EAAE,MAAMA,GAAG,CAACiB,KAAK;UAAE;QACxC;QACAd,GAAG,IAAId,QAAQ,CAAC8B,QAAQ;QACxB;MACJ;MACAd,iBAAiB,GAAG,IAAI;MACxBT,kBAAkB,CAACwB,IAAI,CAACb,QAAQ,CAAC;MACjC,IAAI,CAACd,QAAQ,EAAE;QACX;MACJ;MACA;MACA;MACA,IAAIc,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QACrBH,MAAM,GAAGQ,IAAI,CAACC,KAAK,CAACN,QAAQ,CAAC,CAAC,CAAC,CAACc,IAAI,CAAC,CAAC,CAAC;QACvClB,GAAG,GAAGS,IAAI,CAACC,KAAK,CAACN,QAAQ,CAAC,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC,CAAC;MACxC,CAAC,MACI;QACDX,MAAM,GAAGQ,IAAI,CAACC,KAAK,CAACN,QAAQ,CAAC,CAAC,CAAC,CAACc,IAAI,CAAC,CAAC,CAAC;QACvClB,GAAG,GAAGS,IAAI,CAACC,KAAK,CAACN,QAAQ,CAAC,CAAC,CAAC,CAACQ,IAAI,CAAC,CAAC,CAAC;MACxC;IACJ;IACA,OAAOnB,kBAAkB;EAC7B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIP,QAAQ,CAACmB,YAAY,GAAG,UAAUc,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAE;IAC7D,IAAIC,MAAM,GAAGH,MAAM,CAAChB,SAAS,CAAC,CAAC;IAC/B,IAAIoB,KAAK,GAAGJ,MAAM,CAACK,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAIC,MAAM,GAAG,IAAI1B,KAAK,CAAC,CAAC,CAAC;IACzBb,QAAQ,CAACwC,YAAY,CAACD,MAAM,EAAEvC,QAAQ,CAACyC,mBAAmB,CAACR,MAAM,EAAEG,MAAM,EAAEC,KAAK,EAAEH,QAAQ,EAAEC,WAAW,EAAEnC,QAAQ,CAAC0C,aAAa,CAAC,EAAE1C,QAAQ,CAAC2C,qBAAqB,CAAC;IACjK,IAAIJ,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MACnBJ,WAAW,GAAGZ,IAAI,CAACC,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC;MAC1CE,QAAQ,GAAGX,IAAI,CAACC,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,CAACb,IAAI,CAAC,CAAC,CAAC;IAC3C;IACA1B,QAAQ,CAACwC,YAAY,CAACD,MAAM,EAAEvC,QAAQ,CAACyC,mBAAmB,CAACR,MAAM,EAAEG,MAAM,EAAEC,KAAK,EAAEH,QAAQ,EAAEC,WAAW,EAAEnC,QAAQ,CAAC4C,YAAY,CAAC,EAAE5C,QAAQ,CAAC6C,oBAAoB,CAAC;IAC/J,OAAON,MAAM;EACjB,CAAC;EACDvC,QAAQ,CAACwC,YAAY,GAAG,UAAUD,MAAM,EAAEO,SAAS,EAAEC,kBAAkB,EAAE;IACrE,KAAK,IAAI1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0D,kBAAkB,CAACxD,MAAM,EAAEF,CAAC,EAAE,EAAE;MAChDkD,MAAM,CAACQ,kBAAkB,CAAC1D,CAAC,CAAC,CAAC,GAAGyD,SAAS,CAACzD,CAAC,CAAC;IAChD;EACJ,CAAC;EACDW,QAAQ,CAACyC,mBAAmB,GAAG,UAAUR,MAAM,EAAEG,MAAM,EAAEC,KAAK,EAAEH,QAAQ,EAAEC,WAAW,EAAEa,OAAO,EAAE;IAC5F;IACA,IAAIT,MAAM,GAAG,IAAI1B,KAAK,CAAC,CAAC,CAAC;IACzB,IAAIoC,KAAK,GAAG,KAAK;IACjB,IAAIC,QAAQ,GAAG,IAAIC,UAAU,CAACH,OAAO,CAACzD,MAAM,CAAC;IAC7C,OAAO2C,QAAQ,GAAGE,MAAM,EAAEF,QAAQ,IAAIlC,QAAQ,CAAC8B,QAAQ,EAAE;MACrD,IAAIsB,GAAG,GAAGpD,QAAQ,CAACqD,gBAAgB,CAACpB,MAAM,EAAEE,WAAW,EAAED,QAAQ,EAAEG,KAAK,EAAE,KAAK,EAAEW,OAAO,EAAEE,QAAQ,CAAC;MACnG,IAAIE,GAAG,IAAI,IAAI,EAAE;QACb,OAAOlB,QAAQ,GAAG,CAAC,EAAE;UACjB,IAAIoB,cAAc,GAAGtD,QAAQ,CAACqD,gBAAgB,CAACpB,MAAM,EAAEE,WAAW,EAAE,EAAED,QAAQ,EAAEG,KAAK,EAAE,KAAK,EAAEW,OAAO,EAAEE,QAAQ,CAAC;UAChH,IAAII,cAAc,IAAI,IAAI,EAAE;YACxBF,GAAG,GAAGE,cAAc;UACxB,CAAC,MACI;YACDpB,QAAQ,EAAE;YACV;UACJ;QACJ;QACAK,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI3C,WAAW,CAACwD,GAAG,CAAC,CAAC,CAAC,EAAElB,QAAQ,CAAC;QAC7CK,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI3C,WAAW,CAACwD,GAAG,CAAC,CAAC,CAAC,EAAElB,QAAQ,CAAC;QAC7Ce,KAAK,GAAG,IAAI;QACZ;MACJ;IACJ;IACA,IAAIM,OAAO,GAAGrB,QAAQ,GAAG,CAAC;IAC1B;IACA,IAAIe,KAAK,EAAE;MACP,IAAIO,eAAe,GAAG,CAAC;MACvB,IAAIF,cAAc,GAAGH,UAAU,CAACM,IAAI,CAAC,CAAClC,IAAI,CAACC,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC,EAAET,IAAI,CAACC,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAClG,OAAOuB,OAAO,GAAGnB,MAAM,EAAEmB,OAAO,EAAE,EAAE;QAChC,IAAIH,GAAG,GAAGpD,QAAQ,CAACqD,gBAAgB,CAACpB,MAAM,EAAEqB,cAAc,CAAC,CAAC,CAAC,EAAEC,OAAO,EAAElB,KAAK,EAAE,KAAK,EAAEW,OAAO,EAAEE,QAAQ,CAAC;QACxG;QACA;QACA;QACA;QACA,IAAIE,GAAG,IAAI,IAAI,IACX7B,IAAI,CAACmC,GAAG,CAACJ,cAAc,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpD,QAAQ,CAAC2D,iBAAiB,IACjEpC,IAAI,CAACmC,GAAG,CAACJ,cAAc,CAAC,CAAC,CAAC,GAAGF,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGpD,QAAQ,CAAC2D,iBAAiB,EAAE;UACnEL,cAAc,GAAGF,GAAG;UACpBI,eAAe,GAAG,CAAC;QACvB,CAAC,MACI;UACD,IAAIA,eAAe,GAAGxD,QAAQ,CAAC4D,qBAAqB,EAAE;YAClD;UACJ,CAAC,MACI;YACDJ,eAAe,EAAE;UACrB;QACJ;MACJ;MACAD,OAAO,IAAIC,eAAe,GAAG,CAAC;MAC9BjB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI3C,WAAW,CAAC0D,cAAc,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC;MACvDhB,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI3C,WAAW,CAAC0D,cAAc,CAAC,CAAC,CAAC,EAAEC,OAAO,CAAC;IAC3D;IACA,IAAIA,OAAO,GAAGrB,QAAQ,GAAGlC,QAAQ,CAAC6D,kBAAkB,EAAE;MAClD/D,MAAM,CAACgE,IAAI,CAACvB,MAAM,EAAE,IAAI,CAAC;IAC7B;IACA,OAAOA,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIvC,QAAQ,CAACqD,gBAAgB,GAAG,UAAUpB,MAAM,EAAElB,MAAM,EAAED,GAAG,EAAEuB,KAAK,EAAE0B,UAAU,EAAEf,OAAO,EAAEE,QAAQ,EAAE;IAC7FpD,MAAM,CAACkE,UAAU,CAACd,QAAQ,EAAE,CAAC,EAAEA,QAAQ,CAAC3D,MAAM,EAAE,CAAC,CAAC;IAClD,IAAI0E,YAAY,GAAGlD,MAAM;IACzB,IAAImD,UAAU,GAAG,CAAC;IAClB;IACA,OAAOjC,MAAM,CAACkC,GAAG,CAACF,YAAY,EAAEnD,GAAG,CAAC,IAAImD,YAAY,GAAG,CAAC,IAAIC,UAAU,EAAE,GAAGlE,QAAQ,CAACoE,eAAe,EAAE;MACjGH,YAAY,EAAE;IAClB;IACA,IAAII,CAAC,GAAGJ,YAAY;IACpB,IAAIK,eAAe,GAAG,CAAC;IACvB,IAAIC,aAAa,GAAGvB,OAAO,CAACzD,MAAM;IAClC,KAAK,IAAIiF,OAAO,GAAGT,UAAU,EAAEM,CAAC,GAAGhC,KAAK,EAAEgC,CAAC,EAAE,EAAE;MAC3C,IAAII,KAAK,GAAGxC,MAAM,CAACkC,GAAG,CAACE,CAAC,EAAEvD,GAAG,CAAC;MAC9B,IAAI2D,KAAK,KAAKD,OAAO,EAAE;QACnBtB,QAAQ,CAACoB,eAAe,CAAC,EAAE;MAC/B,CAAC,MACI;QACD,IAAIA,eAAe,KAAKC,aAAa,GAAG,CAAC,EAAE;UACvC,IAAIvE,QAAQ,CAAC0E,oBAAoB,CAACxB,QAAQ,EAAEF,OAAO,EAAEhD,QAAQ,CAAC2E,uBAAuB,CAAC,GAAG3E,QAAQ,CAAC4E,gBAAgB,EAAE;YAChH,OAAO,IAAIzB,UAAU,CAAC,CAACc,YAAY,EAAEI,CAAC,CAAC,CAAC;UAC5C;UACAJ,YAAY,IAAIf,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC;UACzCrD,MAAM,CAACgF,SAAS,CAAC3B,QAAQ,EAAE,CAAC,EAAEA,QAAQ,EAAE,CAAC,EAAEoB,eAAe,GAAG,CAAC,CAAC;UAC/DpB,QAAQ,CAACoB,eAAe,GAAG,CAAC,CAAC,GAAG,CAAC;UACjCpB,QAAQ,CAACoB,eAAe,CAAC,GAAG,CAAC;UAC7BA,eAAe,EAAE;QACrB,CAAC,MACI;UACDA,eAAe,EAAE;QACrB;QACApB,QAAQ,CAACoB,eAAe,CAAC,GAAG,CAAC;QAC7BE,OAAO,GAAG,CAACA,OAAO;MACtB;IACJ;IACA,IAAIF,eAAe,KAAKC,aAAa,GAAG,CAAC,IACrCvE,QAAQ,CAAC0E,oBAAoB,CAACxB,QAAQ,EAAEF,OAAO,EAAEhD,QAAQ,CAAC2E,uBAAuB,CAAC,GAAG3E,QAAQ,CAAC4E,gBAAgB,EAAE;MAChH,OAAO,IAAIzB,UAAU,CAAC,CAACc,YAAY,EAAEI,CAAC,GAAG,CAAC,CAAC,CAAC;IAChD;IACA,OAAO,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrE,QAAQ,CAAC0E,oBAAoB,GAAG,UAAUxB,QAAQ,EAAEF,OAAO,EAAE8B,qBAAqB,EAAE;IAChF,IAAIC,WAAW,GAAG7B,QAAQ,CAAC3D,MAAM;IACjC,IAAIyF,KAAK,GAAG,CAAC;IACb,IAAIT,aAAa,GAAG,CAAC;IACrB,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,WAAW,EAAE1F,CAAC,EAAE,EAAE;MAClC2F,KAAK,IAAI9B,QAAQ,CAAC7D,CAAC,CAAC;MACpBkF,aAAa,IAAIvB,OAAO,CAAC3D,CAAC,CAAC;IAC/B;IACA,IAAI2F,KAAK,GAAGT,aAAa,EAAE;MACvB;MACA;MACA,OAAO,2BAA4BU,QAAQ;IAC/C;IACA;IACA;IACA;IACA,IAAIC,YAAY,GAAGF,KAAK,GAAGT,aAAa;IACxCO,qBAAqB,IAAII,YAAY;IACrC,IAAIC,aAAa,GAAG,GAAG;IACvB,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,WAAW,EAAEV,CAAC,EAAE,EAAE;MAClC,IAAIe,OAAO,GAAGlC,QAAQ,CAACmB,CAAC,CAAC;MACzB,IAAIgB,aAAa,GAAGrC,OAAO,CAACqB,CAAC,CAAC,GAAGa,YAAY;MAC7C,IAAII,QAAQ,GAAGF,OAAO,GAAGC,aAAa,GAAGD,OAAO,GAAGC,aAAa,GAAGA,aAAa,GAAGD,OAAO;MAC1F,IAAIE,QAAQ,GAAGR,qBAAqB,EAAE;QAClC,OAAO,2BAA4BG,QAAQ;MAC/C;MACAE,aAAa,IAAIG,QAAQ;IAC7B;IACA,OAAOH,aAAa,GAAGH,KAAK;EAChC,CAAC;EACDhF,QAAQ,CAAC2C,qBAAqB,GAAGQ,UAAU,CAACM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9DzD,QAAQ,CAAC6C,oBAAoB,GAAGM,UAAU,CAACM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAC7DzD,QAAQ,CAAC4E,gBAAgB,GAAG,IAAI;EAChC5E,QAAQ,CAAC2E,uBAAuB,GAAG,GAAG;EACtC;EACA;EACA3E,QAAQ,CAAC0C,aAAa,GAAGS,UAAU,CAACM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAClE;EACAzD,QAAQ,CAAC4C,YAAY,GAAGO,UAAU,CAACM,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EACpEzD,QAAQ,CAACoE,eAAe,GAAG,CAAC;EAC5BpE,QAAQ,CAAC2D,iBAAiB,GAAG,CAAC;EAC9B;EACA;EACA3D,QAAQ,CAAC4D,qBAAqB,GAAG,EAAE;EACnC;EACA;EACA5D,QAAQ,CAAC8B,QAAQ,GAAG,CAAC;EACrB9B,QAAQ,CAAC6D,kBAAkB,GAAG,EAAE;EAChC,OAAO7D,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}