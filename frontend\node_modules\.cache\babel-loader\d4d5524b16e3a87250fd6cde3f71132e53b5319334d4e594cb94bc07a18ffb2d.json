{"ast": null, "code": "import FormatException from '../../../../FormatException';\nimport IllegalStateException from '../../../../IllegalStateException';\nimport StringBuilder from '../../../../util/StringBuilder';\nimport BlockParsedResult from './BlockParsedResult';\nimport DecodedChar from './DecodedChar';\nimport DecodedInformation from './DecodedInformation';\nimport DecodedNumeric from './DecodedNumeric';\nimport FieldParser from './FieldParser';\nvar GeneralAppIdDecoder = /** @class */function () {\n  function GeneralAppIdDecoder(information) {\n    this.buffer = new StringBuilder();\n    this.information = information;\n  }\n  GeneralAppIdDecoder.prototype.decodeAllCodes = function (buff, initialPosition) {\n    var currentPosition = initialPosition;\n    var remaining = null;\n    do {\n      var info = this.decodeGeneralPurposeField(currentPosition, remaining);\n      var parsedFields = FieldParser.parseFieldsInGeneralPurpose(info.getNewString());\n      if (parsedFields != null) {\n        buff.append(parsedFields);\n      }\n      if (info.isRemaining()) {\n        remaining = '' + info.getRemainingValue();\n      } else {\n        remaining = null;\n      }\n      if (currentPosition === info.getNewPosition()) {\n        // No step forward!\n        break;\n      }\n      currentPosition = info.getNewPosition();\n    } while (true);\n    return buff.toString();\n  };\n  GeneralAppIdDecoder.prototype.isStillNumeric = function (pos) {\n    // It's numeric if it still has 7 positions\n    // and one of the first 4 bits is \"1\".\n    if (pos + 7 > this.information.getSize()) {\n      return pos + 4 <= this.information.getSize();\n    }\n    for (var i = pos; i < pos + 3; ++i) {\n      if (this.information.get(i)) {\n        return true;\n      }\n    }\n    return this.information.get(pos + 3);\n  };\n  GeneralAppIdDecoder.prototype.decodeNumeric = function (pos) {\n    if (pos + 7 > this.information.getSize()) {\n      var numeric_1 = this.extractNumericValueFromBitArray(pos, 4);\n      if (numeric_1 === 0) {\n        return new DecodedNumeric(this.information.getSize(), DecodedNumeric.FNC1, DecodedNumeric.FNC1);\n      }\n      return new DecodedNumeric(this.information.getSize(), numeric_1 - 1, DecodedNumeric.FNC1);\n    }\n    var numeric = this.extractNumericValueFromBitArray(pos, 7);\n    var digit1 = (numeric - 8) / 11;\n    var digit2 = (numeric - 8) % 11;\n    return new DecodedNumeric(pos + 7, digit1, digit2);\n  };\n  GeneralAppIdDecoder.prototype.extractNumericValueFromBitArray = function (pos, bits) {\n    return GeneralAppIdDecoder.extractNumericValueFromBitArray(this.information, pos, bits);\n  };\n  GeneralAppIdDecoder.extractNumericValueFromBitArray = function (information, pos, bits) {\n    var value = 0;\n    for (var i = 0; i < bits; ++i) {\n      if (information.get(pos + i)) {\n        value |= 1 << bits - i - 1;\n      }\n    }\n    return value;\n  };\n  GeneralAppIdDecoder.prototype.decodeGeneralPurposeField = function (pos, remaining) {\n    // this.buffer.setLength(0);\n    this.buffer.setLengthToZero();\n    if (remaining != null) {\n      this.buffer.append(remaining);\n    }\n    this.current.setPosition(pos);\n    var lastDecoded = this.parseBlocks();\n    if (lastDecoded != null && lastDecoded.isRemaining()) {\n      return new DecodedInformation(this.current.getPosition(), this.buffer.toString(), lastDecoded.getRemainingValue());\n    }\n    return new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n  };\n  GeneralAppIdDecoder.prototype.parseBlocks = function () {\n    var isFinished;\n    var result;\n    do {\n      var initialPosition = this.current.getPosition();\n      if (this.current.isAlpha()) {\n        result = this.parseAlphaBlock();\n        isFinished = result.isFinished();\n      } else if (this.current.isIsoIec646()) {\n        result = this.parseIsoIec646Block();\n        isFinished = result.isFinished();\n      } else {\n        // it must be numeric\n        result = this.parseNumericBlock();\n        isFinished = result.isFinished();\n      }\n      var positionChanged = initialPosition !== this.current.getPosition();\n      if (!positionChanged && !isFinished) {\n        break;\n      }\n    } while (!isFinished);\n    return result.getDecodedInformation();\n  };\n  GeneralAppIdDecoder.prototype.parseNumericBlock = function () {\n    while (this.isStillNumeric(this.current.getPosition())) {\n      var numeric = this.decodeNumeric(this.current.getPosition());\n      this.current.setPosition(numeric.getNewPosition());\n      if (numeric.isFirstDigitFNC1()) {\n        var information = void 0;\n        if (numeric.isSecondDigitFNC1()) {\n          information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n        } else {\n          information = new DecodedInformation(this.current.getPosition(), this.buffer.toString(), numeric.getSecondDigit());\n        }\n        return new BlockParsedResult(true, information);\n      }\n      this.buffer.append(numeric.getFirstDigit());\n      if (numeric.isSecondDigitFNC1()) {\n        var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n        return new BlockParsedResult(true, information);\n      }\n      this.buffer.append(numeric.getSecondDigit());\n    }\n    if (this.isNumericToAlphaNumericLatch(this.current.getPosition())) {\n      this.current.setAlpha();\n      this.current.incrementPosition(4);\n    }\n    return new BlockParsedResult(false);\n  };\n  GeneralAppIdDecoder.prototype.parseIsoIec646Block = function () {\n    while (this.isStillIsoIec646(this.current.getPosition())) {\n      var iso = this.decodeIsoIec646(this.current.getPosition());\n      this.current.setPosition(iso.getNewPosition());\n      if (iso.isFNC1()) {\n        var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n        return new BlockParsedResult(true, information);\n      }\n      this.buffer.append(iso.getValue());\n    }\n    if (this.isAlphaOr646ToNumericLatch(this.current.getPosition())) {\n      this.current.incrementPosition(3);\n      this.current.setNumeric();\n    } else if (this.isAlphaTo646ToAlphaLatch(this.current.getPosition())) {\n      if (this.current.getPosition() + 5 < this.information.getSize()) {\n        this.current.incrementPosition(5);\n      } else {\n        this.current.setPosition(this.information.getSize());\n      }\n      this.current.setAlpha();\n    }\n    return new BlockParsedResult(false);\n  };\n  GeneralAppIdDecoder.prototype.parseAlphaBlock = function () {\n    while (this.isStillAlpha(this.current.getPosition())) {\n      var alpha = this.decodeAlphanumeric(this.current.getPosition());\n      this.current.setPosition(alpha.getNewPosition());\n      if (alpha.isFNC1()) {\n        var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n        return new BlockParsedResult(true, information); // end of the char block\n      }\n      this.buffer.append(alpha.getValue());\n    }\n    if (this.isAlphaOr646ToNumericLatch(this.current.getPosition())) {\n      this.current.incrementPosition(3);\n      this.current.setNumeric();\n    } else if (this.isAlphaTo646ToAlphaLatch(this.current.getPosition())) {\n      if (this.current.getPosition() + 5 < this.information.getSize()) {\n        this.current.incrementPosition(5);\n      } else {\n        this.current.setPosition(this.information.getSize());\n      }\n      this.current.setIsoIec646();\n    }\n    return new BlockParsedResult(false);\n  };\n  GeneralAppIdDecoder.prototype.isStillIsoIec646 = function (pos) {\n    if (pos + 5 > this.information.getSize()) {\n      return false;\n    }\n    var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n    if (fiveBitValue >= 5 && fiveBitValue < 16) {\n      return true;\n    }\n    if (pos + 7 > this.information.getSize()) {\n      return false;\n    }\n    var sevenBitValue = this.extractNumericValueFromBitArray(pos, 7);\n    if (sevenBitValue >= 64 && sevenBitValue < 116) {\n      return true;\n    }\n    if (pos + 8 > this.information.getSize()) {\n      return false;\n    }\n    var eightBitValue = this.extractNumericValueFromBitArray(pos, 8);\n    return eightBitValue >= 232 && eightBitValue < 253;\n  };\n  GeneralAppIdDecoder.prototype.decodeIsoIec646 = function (pos) {\n    var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n    if (fiveBitValue === 15) {\n      return new DecodedChar(pos + 5, DecodedChar.FNC1);\n    }\n    if (fiveBitValue >= 5 && fiveBitValue < 15) {\n      return new DecodedChar(pos + 5, '0' + (fiveBitValue - 5));\n    }\n    var sevenBitValue = this.extractNumericValueFromBitArray(pos, 7);\n    if (sevenBitValue >= 64 && sevenBitValue < 90) {\n      return new DecodedChar(pos + 7, '' + (sevenBitValue + 1));\n    }\n    if (sevenBitValue >= 90 && sevenBitValue < 116) {\n      return new DecodedChar(pos + 7, '' + (sevenBitValue + 7));\n    }\n    var eightBitValue = this.extractNumericValueFromBitArray(pos, 8);\n    var c;\n    switch (eightBitValue) {\n      case 232:\n        c = '!';\n        break;\n      case 233:\n        c = '\"';\n        break;\n      case 234:\n        c = '%';\n        break;\n      case 235:\n        c = '&';\n        break;\n      case 236:\n        c = '\\'';\n        break;\n      case 237:\n        c = '(';\n        break;\n      case 238:\n        c = ')';\n        break;\n      case 239:\n        c = '*';\n        break;\n      case 240:\n        c = '+';\n        break;\n      case 241:\n        c = ',';\n        break;\n      case 242:\n        c = '-';\n        break;\n      case 243:\n        c = '.';\n        break;\n      case 244:\n        c = '/';\n        break;\n      case 245:\n        c = ':';\n        break;\n      case 246:\n        c = ';';\n        break;\n      case 247:\n        c = '<';\n        break;\n      case 248:\n        c = '=';\n        break;\n      case 249:\n        c = '>';\n        break;\n      case 250:\n        c = '?';\n        break;\n      case 251:\n        c = '_';\n        break;\n      case 252:\n        c = ' ';\n        break;\n      default:\n        throw new FormatException();\n    }\n    return new DecodedChar(pos + 8, c);\n  };\n  GeneralAppIdDecoder.prototype.isStillAlpha = function (pos) {\n    if (pos + 5 > this.information.getSize()) {\n      return false;\n    }\n    // We now check if it's a valid 5-bit value (0..9 and FNC1)\n    var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n    if (fiveBitValue >= 5 && fiveBitValue < 16) {\n      return true;\n    }\n    if (pos + 6 > this.information.getSize()) {\n      return false;\n    }\n    var sixBitValue = this.extractNumericValueFromBitArray(pos, 6);\n    return sixBitValue >= 16 && sixBitValue < 63; // 63 not included\n  };\n  GeneralAppIdDecoder.prototype.decodeAlphanumeric = function (pos) {\n    var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n    if (fiveBitValue === 15) {\n      return new DecodedChar(pos + 5, DecodedChar.FNC1);\n    }\n    if (fiveBitValue >= 5 && fiveBitValue < 15) {\n      return new DecodedChar(pos + 5, '0' + (fiveBitValue - 5));\n    }\n    var sixBitValue = this.extractNumericValueFromBitArray(pos, 6);\n    if (sixBitValue >= 32 && sixBitValue < 58) {\n      return new DecodedChar(pos + 6, '' + (sixBitValue + 33));\n    }\n    var c;\n    switch (sixBitValue) {\n      case 58:\n        c = '*';\n        break;\n      case 59:\n        c = ',';\n        break;\n      case 60:\n        c = '-';\n        break;\n      case 61:\n        c = '.';\n        break;\n      case 62:\n        c = '/';\n        break;\n      default:\n        throw new IllegalStateException('Decoding invalid alphanumeric value: ' + sixBitValue);\n    }\n    return new DecodedChar(pos + 6, c);\n  };\n  GeneralAppIdDecoder.prototype.isAlphaTo646ToAlphaLatch = function (pos) {\n    if (pos + 1 > this.information.getSize()) {\n      return false;\n    }\n    for (var i = 0; i < 5 && i + pos < this.information.getSize(); ++i) {\n      if (i === 2) {\n        if (!this.information.get(pos + 2)) {\n          return false;\n        }\n      } else if (this.information.get(pos + i)) {\n        return false;\n      }\n    }\n    return true;\n  };\n  GeneralAppIdDecoder.prototype.isAlphaOr646ToNumericLatch = function (pos) {\n    // Next is alphanumeric if there are 3 positions and they are all zeros\n    if (pos + 3 > this.information.getSize()) {\n      return false;\n    }\n    for (var i = pos; i < pos + 3; ++i) {\n      if (this.information.get(i)) {\n        return false;\n      }\n    }\n    return true;\n  };\n  GeneralAppIdDecoder.prototype.isNumericToAlphaNumericLatch = function (pos) {\n    // Next is alphanumeric if there are 4 positions and they are all zeros, or\n    // if there is a subset of this just before the end of the symbol\n    if (pos + 1 > this.information.getSize()) {\n      return false;\n    }\n    for (var i = 0; i < 4 && i + pos < this.information.getSize(); ++i) {\n      if (this.information.get(pos + i)) {\n        return false;\n      }\n    }\n    return true;\n  };\n  return GeneralAppIdDecoder;\n}();\nexport default GeneralAppIdDecoder;", "map": {"version": 3, "names": ["FormatException", "IllegalStateException", "StringBuilder", "BlockParsedResult", "DecodedChar", "DecodedInformation", "DecodedNumeric", "<PERSON><PERSON><PERSON><PERSON>", "GeneralAppIdDecoder", "information", "buffer", "prototype", "decodeAllCodes", "buff", "initialPosition", "currentPosition", "remaining", "info", "decodeGeneralPurposeField", "parsedFields", "parseFieldsInGeneralPurpose", "getNewString", "append", "isRemaining", "getRemainingValue", "getNewPosition", "toString", "isStillNumeric", "pos", "getSize", "i", "get", "decodeNumeric", "numeric_1", "extractNumericValueFromBitArray", "FNC1", "numeric", "digit1", "digit2", "bits", "value", "setLengthToZero", "current", "setPosition", "lastDecoded", "parseBlocks", "getPosition", "isFinished", "result", "isAlpha", "parseAlphaBlock", "isIsoIec646", "parseIsoIec646Block", "parseNumericBlock", "positionChanged", "getDecodedInformation", "isFirstDigitFNC1", "isSecondDigitFNC1", "getSecondDigit", "getFirstDigit", "isNumericToAlphaNumericLatch", "<PERSON><PERSON><PERSON><PERSON>", "incrementPosition", "isStillIsoIec646", "iso", "decodeIsoIec646", "isFNC1", "getValue", "isAlphaOr646ToNumericLatch", "setNumeric", "isAlphaTo646ToAlphaLatch", "isStillAlpha", "alpha", "decodeAlphanumeric", "setIsoIec646", "fiveBitValue", "sevenBitValue", "eightBitValue", "c", "sixBitValue"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/GeneralAppIdDecoder.js"], "sourcesContent": ["import FormatException from '../../../../FormatException';\nimport IllegalStateException from '../../../../IllegalStateException';\nimport StringBuilder from '../../../../util/StringBuilder';\nimport BlockParsedResult from './BlockParsedResult';\nimport DecodedChar from './DecodedChar';\nimport DecodedInformation from './DecodedInformation';\nimport DecodedNumeric from './DecodedNumeric';\nimport FieldParser from './FieldParser';\nvar GeneralAppIdDecoder = /** @class */ (function () {\n    function GeneralAppIdDecoder(information) {\n        this.buffer = new StringBuilder();\n        this.information = information;\n    }\n    GeneralAppIdDecoder.prototype.decodeAllCodes = function (buff, initialPosition) {\n        var currentPosition = initialPosition;\n        var remaining = null;\n        do {\n            var info = this.decodeGeneralPurposeField(currentPosition, remaining);\n            var parsedFields = FieldParser.parseFieldsInGeneralPurpose(info.getNewString());\n            if (parsedFields != null) {\n                buff.append(parsedFields);\n            }\n            if (info.isRemaining()) {\n                remaining = '' + info.getRemainingValue();\n            }\n            else {\n                remaining = null;\n            }\n            if (currentPosition === info.getNewPosition()) { // No step forward!\n                break;\n            }\n            currentPosition = info.getNewPosition();\n        } while (true);\n        return buff.toString();\n    };\n    GeneralAppIdDecoder.prototype.isStillNumeric = function (pos) {\n        // It's numeric if it still has 7 positions\n        // and one of the first 4 bits is \"1\".\n        if (pos + 7 > this.information.getSize()) {\n            return pos + 4 <= this.information.getSize();\n        }\n        for (var i = pos; i < pos + 3; ++i) {\n            if (this.information.get(i)) {\n                return true;\n            }\n        }\n        return this.information.get(pos + 3);\n    };\n    GeneralAppIdDecoder.prototype.decodeNumeric = function (pos) {\n        if (pos + 7 > this.information.getSize()) {\n            var numeric_1 = this.extractNumericValueFromBitArray(pos, 4);\n            if (numeric_1 === 0) {\n                return new DecodedNumeric(this.information.getSize(), DecodedNumeric.FNC1, DecodedNumeric.FNC1);\n            }\n            return new DecodedNumeric(this.information.getSize(), numeric_1 - 1, DecodedNumeric.FNC1);\n        }\n        var numeric = this.extractNumericValueFromBitArray(pos, 7);\n        var digit1 = (numeric - 8) / 11;\n        var digit2 = (numeric - 8) % 11;\n        return new DecodedNumeric(pos + 7, digit1, digit2);\n    };\n    GeneralAppIdDecoder.prototype.extractNumericValueFromBitArray = function (pos, bits) {\n        return GeneralAppIdDecoder.extractNumericValueFromBitArray(this.information, pos, bits);\n    };\n    GeneralAppIdDecoder.extractNumericValueFromBitArray = function (information, pos, bits) {\n        var value = 0;\n        for (var i = 0; i < bits; ++i) {\n            if (information.get(pos + i)) {\n                value |= 1 << (bits - i - 1);\n            }\n        }\n        return value;\n    };\n    GeneralAppIdDecoder.prototype.decodeGeneralPurposeField = function (pos, remaining) {\n        // this.buffer.setLength(0);\n        this.buffer.setLengthToZero();\n        if (remaining != null) {\n            this.buffer.append(remaining);\n        }\n        this.current.setPosition(pos);\n        var lastDecoded = this.parseBlocks();\n        if (lastDecoded != null && lastDecoded.isRemaining()) {\n            return new DecodedInformation(this.current.getPosition(), this.buffer.toString(), lastDecoded.getRemainingValue());\n        }\n        return new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n    };\n    GeneralAppIdDecoder.prototype.parseBlocks = function () {\n        var isFinished;\n        var result;\n        do {\n            var initialPosition = this.current.getPosition();\n            if (this.current.isAlpha()) {\n                result = this.parseAlphaBlock();\n                isFinished = result.isFinished();\n            }\n            else if (this.current.isIsoIec646()) {\n                result = this.parseIsoIec646Block();\n                isFinished = result.isFinished();\n            }\n            else { // it must be numeric\n                result = this.parseNumericBlock();\n                isFinished = result.isFinished();\n            }\n            var positionChanged = initialPosition !== this.current.getPosition();\n            if (!positionChanged && !isFinished) {\n                break;\n            }\n        } while (!isFinished);\n        return result.getDecodedInformation();\n    };\n    GeneralAppIdDecoder.prototype.parseNumericBlock = function () {\n        while (this.isStillNumeric(this.current.getPosition())) {\n            var numeric = this.decodeNumeric(this.current.getPosition());\n            this.current.setPosition(numeric.getNewPosition());\n            if (numeric.isFirstDigitFNC1()) {\n                var information = void 0;\n                if (numeric.isSecondDigitFNC1()) {\n                    information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n                }\n                else {\n                    information = new DecodedInformation(this.current.getPosition(), this.buffer.toString(), numeric.getSecondDigit());\n                }\n                return new BlockParsedResult(true, information);\n            }\n            this.buffer.append(numeric.getFirstDigit());\n            if (numeric.isSecondDigitFNC1()) {\n                var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n                return new BlockParsedResult(true, information);\n            }\n            this.buffer.append(numeric.getSecondDigit());\n        }\n        if (this.isNumericToAlphaNumericLatch(this.current.getPosition())) {\n            this.current.setAlpha();\n            this.current.incrementPosition(4);\n        }\n        return new BlockParsedResult(false);\n    };\n    GeneralAppIdDecoder.prototype.parseIsoIec646Block = function () {\n        while (this.isStillIsoIec646(this.current.getPosition())) {\n            var iso = this.decodeIsoIec646(this.current.getPosition());\n            this.current.setPosition(iso.getNewPosition());\n            if (iso.isFNC1()) {\n                var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n                return new BlockParsedResult(true, information);\n            }\n            this.buffer.append(iso.getValue());\n        }\n        if (this.isAlphaOr646ToNumericLatch(this.current.getPosition())) {\n            this.current.incrementPosition(3);\n            this.current.setNumeric();\n        }\n        else if (this.isAlphaTo646ToAlphaLatch(this.current.getPosition())) {\n            if (this.current.getPosition() + 5 < this.information.getSize()) {\n                this.current.incrementPosition(5);\n            }\n            else {\n                this.current.setPosition(this.information.getSize());\n            }\n            this.current.setAlpha();\n        }\n        return new BlockParsedResult(false);\n    };\n    GeneralAppIdDecoder.prototype.parseAlphaBlock = function () {\n        while (this.isStillAlpha(this.current.getPosition())) {\n            var alpha = this.decodeAlphanumeric(this.current.getPosition());\n            this.current.setPosition(alpha.getNewPosition());\n            if (alpha.isFNC1()) {\n                var information = new DecodedInformation(this.current.getPosition(), this.buffer.toString());\n                return new BlockParsedResult(true, information); // end of the char block\n            }\n            this.buffer.append(alpha.getValue());\n        }\n        if (this.isAlphaOr646ToNumericLatch(this.current.getPosition())) {\n            this.current.incrementPosition(3);\n            this.current.setNumeric();\n        }\n        else if (this.isAlphaTo646ToAlphaLatch(this.current.getPosition())) {\n            if (this.current.getPosition() + 5 < this.information.getSize()) {\n                this.current.incrementPosition(5);\n            }\n            else {\n                this.current.setPosition(this.information.getSize());\n            }\n            this.current.setIsoIec646();\n        }\n        return new BlockParsedResult(false);\n    };\n    GeneralAppIdDecoder.prototype.isStillIsoIec646 = function (pos) {\n        if (pos + 5 > this.information.getSize()) {\n            return false;\n        }\n        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n        if (fiveBitValue >= 5 && fiveBitValue < 16) {\n            return true;\n        }\n        if (pos + 7 > this.information.getSize()) {\n            return false;\n        }\n        var sevenBitValue = this.extractNumericValueFromBitArray(pos, 7);\n        if (sevenBitValue >= 64 && sevenBitValue < 116) {\n            return true;\n        }\n        if (pos + 8 > this.information.getSize()) {\n            return false;\n        }\n        var eightBitValue = this.extractNumericValueFromBitArray(pos, 8);\n        return eightBitValue >= 232 && eightBitValue < 253;\n    };\n    GeneralAppIdDecoder.prototype.decodeIsoIec646 = function (pos) {\n        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n        if (fiveBitValue === 15) {\n            return new DecodedChar(pos + 5, DecodedChar.FNC1);\n        }\n        if (fiveBitValue >= 5 && fiveBitValue < 15) {\n            return new DecodedChar(pos + 5, ('0' + (fiveBitValue - 5)));\n        }\n        var sevenBitValue = this.extractNumericValueFromBitArray(pos, 7);\n        if (sevenBitValue >= 64 && sevenBitValue < 90) {\n            return new DecodedChar(pos + 7, ('' + (sevenBitValue + 1)));\n        }\n        if (sevenBitValue >= 90 && sevenBitValue < 116) {\n            return new DecodedChar(pos + 7, ('' + (sevenBitValue + 7)));\n        }\n        var eightBitValue = this.extractNumericValueFromBitArray(pos, 8);\n        var c;\n        switch (eightBitValue) {\n            case 232:\n                c = '!';\n                break;\n            case 233:\n                c = '\"';\n                break;\n            case 234:\n                c = '%';\n                break;\n            case 235:\n                c = '&';\n                break;\n            case 236:\n                c = '\\'';\n                break;\n            case 237:\n                c = '(';\n                break;\n            case 238:\n                c = ')';\n                break;\n            case 239:\n                c = '*';\n                break;\n            case 240:\n                c = '+';\n                break;\n            case 241:\n                c = ',';\n                break;\n            case 242:\n                c = '-';\n                break;\n            case 243:\n                c = '.';\n                break;\n            case 244:\n                c = '/';\n                break;\n            case 245:\n                c = ':';\n                break;\n            case 246:\n                c = ';';\n                break;\n            case 247:\n                c = '<';\n                break;\n            case 248:\n                c = '=';\n                break;\n            case 249:\n                c = '>';\n                break;\n            case 250:\n                c = '?';\n                break;\n            case 251:\n                c = '_';\n                break;\n            case 252:\n                c = ' ';\n                break;\n            default:\n                throw new FormatException();\n        }\n        return new DecodedChar(pos + 8, c);\n    };\n    GeneralAppIdDecoder.prototype.isStillAlpha = function (pos) {\n        if (pos + 5 > this.information.getSize()) {\n            return false;\n        }\n        // We now check if it's a valid 5-bit value (0..9 and FNC1)\n        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n        if (fiveBitValue >= 5 && fiveBitValue < 16) {\n            return true;\n        }\n        if (pos + 6 > this.information.getSize()) {\n            return false;\n        }\n        var sixBitValue = this.extractNumericValueFromBitArray(pos, 6);\n        return sixBitValue >= 16 && sixBitValue < 63; // 63 not included\n    };\n    GeneralAppIdDecoder.prototype.decodeAlphanumeric = function (pos) {\n        var fiveBitValue = this.extractNumericValueFromBitArray(pos, 5);\n        if (fiveBitValue === 15) {\n            return new DecodedChar(pos + 5, DecodedChar.FNC1);\n        }\n        if (fiveBitValue >= 5 && fiveBitValue < 15) {\n            return new DecodedChar(pos + 5, ('0' + (fiveBitValue - 5)));\n        }\n        var sixBitValue = this.extractNumericValueFromBitArray(pos, 6);\n        if (sixBitValue >= 32 && sixBitValue < 58) {\n            return new DecodedChar(pos + 6, ('' + (sixBitValue + 33)));\n        }\n        var c;\n        switch (sixBitValue) {\n            case 58:\n                c = '*';\n                break;\n            case 59:\n                c = ',';\n                break;\n            case 60:\n                c = '-';\n                break;\n            case 61:\n                c = '.';\n                break;\n            case 62:\n                c = '/';\n                break;\n            default:\n                throw new IllegalStateException('Decoding invalid alphanumeric value: ' + sixBitValue);\n        }\n        return new DecodedChar(pos + 6, c);\n    };\n    GeneralAppIdDecoder.prototype.isAlphaTo646ToAlphaLatch = function (pos) {\n        if (pos + 1 > this.information.getSize()) {\n            return false;\n        }\n        for (var i = 0; i < 5 && i + pos < this.information.getSize(); ++i) {\n            if (i === 2) {\n                if (!this.information.get(pos + 2)) {\n                    return false;\n                }\n            }\n            else if (this.information.get(pos + i)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    GeneralAppIdDecoder.prototype.isAlphaOr646ToNumericLatch = function (pos) {\n        // Next is alphanumeric if there are 3 positions and they are all zeros\n        if (pos + 3 > this.information.getSize()) {\n            return false;\n        }\n        for (var i = pos; i < pos + 3; ++i) {\n            if (this.information.get(i)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    GeneralAppIdDecoder.prototype.isNumericToAlphaNumericLatch = function (pos) {\n        // Next is alphanumeric if there are 4 positions and they are all zeros, or\n        // if there is a subset of this just before the end of the symbol\n        if (pos + 1 > this.information.getSize()) {\n            return false;\n        }\n        for (var i = 0; i < 4 && i + pos < this.information.getSize(); ++i) {\n            if (this.information.get(pos + i)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    return GeneralAppIdDecoder;\n}());\nexport default GeneralAppIdDecoder;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,6BAA6B;AACzD,OAAOC,qBAAqB,MAAM,mCAAmC;AACrE,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,IAAIC,mBAAmB,GAAG,aAAe,YAAY;EACjD,SAASA,mBAAmBA,CAACC,WAAW,EAAE;IACtC,IAAI,CAACC,MAAM,GAAG,IAAIR,aAAa,CAAC,CAAC;IACjC,IAAI,CAACO,WAAW,GAAGA,WAAW;EAClC;EACAD,mBAAmB,CAACG,SAAS,CAACC,cAAc,GAAG,UAAUC,IAAI,EAAEC,eAAe,EAAE;IAC5E,IAAIC,eAAe,GAAGD,eAAe;IACrC,IAAIE,SAAS,GAAG,IAAI;IACpB,GAAG;MACC,IAAIC,IAAI,GAAG,IAAI,CAACC,yBAAyB,CAACH,eAAe,EAAEC,SAAS,CAAC;MACrE,IAAIG,YAAY,GAAGZ,WAAW,CAACa,2BAA2B,CAACH,IAAI,CAACI,YAAY,CAAC,CAAC,CAAC;MAC/E,IAAIF,YAAY,IAAI,IAAI,EAAE;QACtBN,IAAI,CAACS,MAAM,CAACH,YAAY,CAAC;MAC7B;MACA,IAAIF,IAAI,CAACM,WAAW,CAAC,CAAC,EAAE;QACpBP,SAAS,GAAG,EAAE,GAAGC,IAAI,CAACO,iBAAiB,CAAC,CAAC;MAC7C,CAAC,MACI;QACDR,SAAS,GAAG,IAAI;MACpB;MACA,IAAID,eAAe,KAAKE,IAAI,CAACQ,cAAc,CAAC,CAAC,EAAE;QAAE;QAC7C;MACJ;MACAV,eAAe,GAAGE,IAAI,CAACQ,cAAc,CAAC,CAAC;IAC3C,CAAC,QAAQ,IAAI;IACb,OAAOZ,IAAI,CAACa,QAAQ,CAAC,CAAC;EAC1B,CAAC;EACDlB,mBAAmB,CAACG,SAAS,CAACgB,cAAc,GAAG,UAAUC,GAAG,EAAE;IAC1D;IACA;IACA,IAAIA,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAOD,GAAG,GAAG,CAAC,IAAI,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC;IAChD;IACA,KAAK,IAAIC,CAAC,GAAGF,GAAG,EAAEE,CAAC,GAAGF,GAAG,GAAG,CAAC,EAAE,EAAEE,CAAC,EAAE;MAChC,IAAI,IAAI,CAACrB,WAAW,CAACsB,GAAG,CAACD,CAAC,CAAC,EAAE;QACzB,OAAO,IAAI;MACf;IACJ;IACA,OAAO,IAAI,CAACrB,WAAW,CAACsB,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC;EACxC,CAAC;EACDpB,mBAAmB,CAACG,SAAS,CAACqB,aAAa,GAAG,UAAUJ,GAAG,EAAE;IACzD,IAAIA,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,IAAII,SAAS,GAAG,IAAI,CAACC,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;MAC5D,IAAIK,SAAS,KAAK,CAAC,EAAE;QACjB,OAAO,IAAI3B,cAAc,CAAC,IAAI,CAACG,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAEvB,cAAc,CAAC6B,IAAI,EAAE7B,cAAc,CAAC6B,IAAI,CAAC;MACnG;MACA,OAAO,IAAI7B,cAAc,CAAC,IAAI,CAACG,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAEI,SAAS,GAAG,CAAC,EAAE3B,cAAc,CAAC6B,IAAI,CAAC;IAC7F;IACA,IAAIC,OAAO,GAAG,IAAI,CAACF,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAC1D,IAAIS,MAAM,GAAG,CAACD,OAAO,GAAG,CAAC,IAAI,EAAE;IAC/B,IAAIE,MAAM,GAAG,CAACF,OAAO,GAAG,CAAC,IAAI,EAAE;IAC/B,OAAO,IAAI9B,cAAc,CAACsB,GAAG,GAAG,CAAC,EAAES,MAAM,EAAEC,MAAM,CAAC;EACtD,CAAC;EACD9B,mBAAmB,CAACG,SAAS,CAACuB,+BAA+B,GAAG,UAAUN,GAAG,EAAEW,IAAI,EAAE;IACjF,OAAO/B,mBAAmB,CAAC0B,+BAA+B,CAAC,IAAI,CAACzB,WAAW,EAAEmB,GAAG,EAAEW,IAAI,CAAC;EAC3F,CAAC;EACD/B,mBAAmB,CAAC0B,+BAA+B,GAAG,UAAUzB,WAAW,EAAEmB,GAAG,EAAEW,IAAI,EAAE;IACpF,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGS,IAAI,EAAE,EAAET,CAAC,EAAE;MAC3B,IAAIrB,WAAW,CAACsB,GAAG,CAACH,GAAG,GAAGE,CAAC,CAAC,EAAE;QAC1BU,KAAK,IAAI,CAAC,IAAKD,IAAI,GAAGT,CAAC,GAAG,CAAE;MAChC;IACJ;IACA,OAAOU,KAAK;EAChB,CAAC;EACDhC,mBAAmB,CAACG,SAAS,CAACO,yBAAyB,GAAG,UAAUU,GAAG,EAAEZ,SAAS,EAAE;IAChF;IACA,IAAI,CAACN,MAAM,CAAC+B,eAAe,CAAC,CAAC;IAC7B,IAAIzB,SAAS,IAAI,IAAI,EAAE;MACnB,IAAI,CAACN,MAAM,CAACY,MAAM,CAACN,SAAS,CAAC;IACjC;IACA,IAAI,CAAC0B,OAAO,CAACC,WAAW,CAACf,GAAG,CAAC;IAC7B,IAAIgB,WAAW,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;IACpC,IAAID,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACrB,WAAW,CAAC,CAAC,EAAE;MAClD,OAAO,IAAIlB,kBAAkB,CAAC,IAAI,CAACqC,OAAO,CAACI,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpC,MAAM,CAACgB,QAAQ,CAAC,CAAC,EAAEkB,WAAW,CAACpB,iBAAiB,CAAC,CAAC,CAAC;IACtH;IACA,OAAO,IAAInB,kBAAkB,CAAC,IAAI,CAACqC,OAAO,CAACI,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpC,MAAM,CAACgB,QAAQ,CAAC,CAAC,CAAC;EACrF,CAAC;EACDlB,mBAAmB,CAACG,SAAS,CAACkC,WAAW,GAAG,YAAY;IACpD,IAAIE,UAAU;IACd,IAAIC,MAAM;IACV,GAAG;MACC,IAAIlC,eAAe,GAAG,IAAI,CAAC4B,OAAO,CAACI,WAAW,CAAC,CAAC;MAChD,IAAI,IAAI,CAACJ,OAAO,CAACO,OAAO,CAAC,CAAC,EAAE;QACxBD,MAAM,GAAG,IAAI,CAACE,eAAe,CAAC,CAAC;QAC/BH,UAAU,GAAGC,MAAM,CAACD,UAAU,CAAC,CAAC;MACpC,CAAC,MACI,IAAI,IAAI,CAACL,OAAO,CAACS,WAAW,CAAC,CAAC,EAAE;QACjCH,MAAM,GAAG,IAAI,CAACI,mBAAmB,CAAC,CAAC;QACnCL,UAAU,GAAGC,MAAM,CAACD,UAAU,CAAC,CAAC;MACpC,CAAC,MACI;QAAE;QACHC,MAAM,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;QACjCN,UAAU,GAAGC,MAAM,CAACD,UAAU,CAAC,CAAC;MACpC;MACA,IAAIO,eAAe,GAAGxC,eAAe,KAAK,IAAI,CAAC4B,OAAO,CAACI,WAAW,CAAC,CAAC;MACpE,IAAI,CAACQ,eAAe,IAAI,CAACP,UAAU,EAAE;QACjC;MACJ;IACJ,CAAC,QAAQ,CAACA,UAAU;IACpB,OAAOC,MAAM,CAACO,qBAAqB,CAAC,CAAC;EACzC,CAAC;EACD/C,mBAAmB,CAACG,SAAS,CAAC0C,iBAAiB,GAAG,YAAY;IAC1D,OAAO,IAAI,CAAC1B,cAAc,CAAC,IAAI,CAACe,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,EAAE;MACpD,IAAIV,OAAO,GAAG,IAAI,CAACJ,aAAa,CAAC,IAAI,CAACU,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC;MAC5D,IAAI,CAACJ,OAAO,CAACC,WAAW,CAACP,OAAO,CAACX,cAAc,CAAC,CAAC,CAAC;MAClD,IAAIW,OAAO,CAACoB,gBAAgB,CAAC,CAAC,EAAE;QAC5B,IAAI/C,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI2B,OAAO,CAACqB,iBAAiB,CAAC,CAAC,EAAE;UAC7BhD,WAAW,GAAG,IAAIJ,kBAAkB,CAAC,IAAI,CAACqC,OAAO,CAACI,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpC,MAAM,CAACgB,QAAQ,CAAC,CAAC,CAAC;QAC5F,CAAC,MACI;UACDjB,WAAW,GAAG,IAAIJ,kBAAkB,CAAC,IAAI,CAACqC,OAAO,CAACI,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpC,MAAM,CAACgB,QAAQ,CAAC,CAAC,EAAEU,OAAO,CAACsB,cAAc,CAAC,CAAC,CAAC;QACtH;QACA,OAAO,IAAIvD,iBAAiB,CAAC,IAAI,EAAEM,WAAW,CAAC;MACnD;MACA,IAAI,CAACC,MAAM,CAACY,MAAM,CAACc,OAAO,CAACuB,aAAa,CAAC,CAAC,CAAC;MAC3C,IAAIvB,OAAO,CAACqB,iBAAiB,CAAC,CAAC,EAAE;QAC7B,IAAIhD,WAAW,GAAG,IAAIJ,kBAAkB,CAAC,IAAI,CAACqC,OAAO,CAACI,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpC,MAAM,CAACgB,QAAQ,CAAC,CAAC,CAAC;QAC5F,OAAO,IAAIvB,iBAAiB,CAAC,IAAI,EAAEM,WAAW,CAAC;MACnD;MACA,IAAI,CAACC,MAAM,CAACY,MAAM,CAACc,OAAO,CAACsB,cAAc,CAAC,CAAC,CAAC;IAChD;IACA,IAAI,IAAI,CAACE,4BAA4B,CAAC,IAAI,CAAClB,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,EAAE;MAC/D,IAAI,CAACJ,OAAO,CAACmB,QAAQ,CAAC,CAAC;MACvB,IAAI,CAACnB,OAAO,CAACoB,iBAAiB,CAAC,CAAC,CAAC;IACrC;IACA,OAAO,IAAI3D,iBAAiB,CAAC,KAAK,CAAC;EACvC,CAAC;EACDK,mBAAmB,CAACG,SAAS,CAACyC,mBAAmB,GAAG,YAAY;IAC5D,OAAO,IAAI,CAACW,gBAAgB,CAAC,IAAI,CAACrB,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,EAAE;MACtD,IAAIkB,GAAG,GAAG,IAAI,CAACC,eAAe,CAAC,IAAI,CAACvB,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC;MAC1D,IAAI,CAACJ,OAAO,CAACC,WAAW,CAACqB,GAAG,CAACvC,cAAc,CAAC,CAAC,CAAC;MAC9C,IAAIuC,GAAG,CAACE,MAAM,CAAC,CAAC,EAAE;QACd,IAAIzD,WAAW,GAAG,IAAIJ,kBAAkB,CAAC,IAAI,CAACqC,OAAO,CAACI,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpC,MAAM,CAACgB,QAAQ,CAAC,CAAC,CAAC;QAC5F,OAAO,IAAIvB,iBAAiB,CAAC,IAAI,EAAEM,WAAW,CAAC;MACnD;MACA,IAAI,CAACC,MAAM,CAACY,MAAM,CAAC0C,GAAG,CAACG,QAAQ,CAAC,CAAC,CAAC;IACtC;IACA,IAAI,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAAC1B,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,EAAE;MAC7D,IAAI,CAACJ,OAAO,CAACoB,iBAAiB,CAAC,CAAC,CAAC;MACjC,IAAI,CAACpB,OAAO,CAAC2B,UAAU,CAAC,CAAC;IAC7B,CAAC,MACI,IAAI,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAAC5B,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,EAAE;MAChE,IAAI,IAAI,CAACJ,OAAO,CAACI,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACrC,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;QAC7D,IAAI,CAACa,OAAO,CAACoB,iBAAiB,CAAC,CAAC,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAACpB,OAAO,CAACC,WAAW,CAAC,IAAI,CAAClC,WAAW,CAACoB,OAAO,CAAC,CAAC,CAAC;MACxD;MACA,IAAI,CAACa,OAAO,CAACmB,QAAQ,CAAC,CAAC;IAC3B;IACA,OAAO,IAAI1D,iBAAiB,CAAC,KAAK,CAAC;EACvC,CAAC;EACDK,mBAAmB,CAACG,SAAS,CAACuC,eAAe,GAAG,YAAY;IACxD,OAAO,IAAI,CAACqB,YAAY,CAAC,IAAI,CAAC7B,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,EAAE;MAClD,IAAI0B,KAAK,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAAC/B,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC;MAC/D,IAAI,CAACJ,OAAO,CAACC,WAAW,CAAC6B,KAAK,CAAC/C,cAAc,CAAC,CAAC,CAAC;MAChD,IAAI+C,KAAK,CAACN,MAAM,CAAC,CAAC,EAAE;QAChB,IAAIzD,WAAW,GAAG,IAAIJ,kBAAkB,CAAC,IAAI,CAACqC,OAAO,CAACI,WAAW,CAAC,CAAC,EAAE,IAAI,CAACpC,MAAM,CAACgB,QAAQ,CAAC,CAAC,CAAC;QAC5F,OAAO,IAAIvB,iBAAiB,CAAC,IAAI,EAAEM,WAAW,CAAC,CAAC,CAAC;MACrD;MACA,IAAI,CAACC,MAAM,CAACY,MAAM,CAACkD,KAAK,CAACL,QAAQ,CAAC,CAAC,CAAC;IACxC;IACA,IAAI,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAAC1B,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,EAAE;MAC7D,IAAI,CAACJ,OAAO,CAACoB,iBAAiB,CAAC,CAAC,CAAC;MACjC,IAAI,CAACpB,OAAO,CAAC2B,UAAU,CAAC,CAAC;IAC7B,CAAC,MACI,IAAI,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAAC5B,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC,EAAE;MAChE,IAAI,IAAI,CAACJ,OAAO,CAACI,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACrC,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;QAC7D,IAAI,CAACa,OAAO,CAACoB,iBAAiB,CAAC,CAAC,CAAC;MACrC,CAAC,MACI;QACD,IAAI,CAACpB,OAAO,CAACC,WAAW,CAAC,IAAI,CAAClC,WAAW,CAACoB,OAAO,CAAC,CAAC,CAAC;MACxD;MACA,IAAI,CAACa,OAAO,CAACgC,YAAY,CAAC,CAAC;IAC/B;IACA,OAAO,IAAIvE,iBAAiB,CAAC,KAAK,CAAC;EACvC,CAAC;EACDK,mBAAmB,CAACG,SAAS,CAACoD,gBAAgB,GAAG,UAAUnC,GAAG,EAAE;IAC5D,IAAIA,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAI8C,YAAY,GAAG,IAAI,CAACzC,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAC/D,IAAI+C,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;MACxC,OAAO,IAAI;IACf;IACA,IAAI/C,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAI+C,aAAa,GAAG,IAAI,CAAC1C,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAChE,IAAIgD,aAAa,IAAI,EAAE,IAAIA,aAAa,GAAG,GAAG,EAAE;MAC5C,OAAO,IAAI;IACf;IACA,IAAIhD,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAIgD,aAAa,GAAG,IAAI,CAAC3C,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAChE,OAAOiD,aAAa,IAAI,GAAG,IAAIA,aAAa,GAAG,GAAG;EACtD,CAAC;EACDrE,mBAAmB,CAACG,SAAS,CAACsD,eAAe,GAAG,UAAUrC,GAAG,EAAE;IAC3D,IAAI+C,YAAY,GAAG,IAAI,CAACzC,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAC/D,IAAI+C,YAAY,KAAK,EAAE,EAAE;MACrB,OAAO,IAAIvE,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAExB,WAAW,CAAC+B,IAAI,CAAC;IACrD;IACA,IAAIwC,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;MACxC,OAAO,IAAIvE,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAG,GAAG,IAAI+C,YAAY,GAAG,CAAC,CAAE,CAAC;IAC/D;IACA,IAAIC,aAAa,GAAG,IAAI,CAAC1C,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAChE,IAAIgD,aAAa,IAAI,EAAE,IAAIA,aAAa,GAAG,EAAE,EAAE;MAC3C,OAAO,IAAIxE,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAG,EAAE,IAAIgD,aAAa,GAAG,CAAC,CAAE,CAAC;IAC/D;IACA,IAAIA,aAAa,IAAI,EAAE,IAAIA,aAAa,GAAG,GAAG,EAAE;MAC5C,OAAO,IAAIxE,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAG,EAAE,IAAIgD,aAAa,GAAG,CAAC,CAAE,CAAC;IAC/D;IACA,IAAIC,aAAa,GAAG,IAAI,CAAC3C,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAChE,IAAIkD,CAAC;IACL,QAAQD,aAAa;MACjB,KAAK,GAAG;QACJC,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,IAAI;QACR;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,GAAG;QACJA,CAAC,GAAG,GAAG;QACP;MACJ;QACI,MAAM,IAAI9E,eAAe,CAAC,CAAC;IACnC;IACA,OAAO,IAAII,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAEkD,CAAC,CAAC;EACtC,CAAC;EACDtE,mBAAmB,CAACG,SAAS,CAAC4D,YAAY,GAAG,UAAU3C,GAAG,EAAE;IACxD,IAAIA,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA;IACA,IAAI8C,YAAY,GAAG,IAAI,CAACzC,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAC/D,IAAI+C,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;MACxC,OAAO,IAAI;IACf;IACA,IAAI/C,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAIkD,WAAW,GAAG,IAAI,CAAC7C,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAC9D,OAAOmD,WAAW,IAAI,EAAE,IAAIA,WAAW,GAAG,EAAE,CAAC,CAAC;EAClD,CAAC;EACDvE,mBAAmB,CAACG,SAAS,CAAC8D,kBAAkB,GAAG,UAAU7C,GAAG,EAAE;IAC9D,IAAI+C,YAAY,GAAG,IAAI,CAACzC,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAC/D,IAAI+C,YAAY,KAAK,EAAE,EAAE;MACrB,OAAO,IAAIvE,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAExB,WAAW,CAAC+B,IAAI,CAAC;IACrD;IACA,IAAIwC,YAAY,IAAI,CAAC,IAAIA,YAAY,GAAG,EAAE,EAAE;MACxC,OAAO,IAAIvE,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAG,GAAG,IAAI+C,YAAY,GAAG,CAAC,CAAE,CAAC;IAC/D;IACA,IAAII,WAAW,GAAG,IAAI,CAAC7C,+BAA+B,CAACN,GAAG,EAAE,CAAC,CAAC;IAC9D,IAAImD,WAAW,IAAI,EAAE,IAAIA,WAAW,GAAG,EAAE,EAAE;MACvC,OAAO,IAAI3E,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAG,EAAE,IAAImD,WAAW,GAAG,EAAE,CAAE,CAAC;IAC9D;IACA,IAAID,CAAC;IACL,QAAQC,WAAW;MACf,KAAK,EAAE;QACHD,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,EAAE;QACHA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,EAAE;QACHA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,EAAE;QACHA,CAAC,GAAG,GAAG;QACP;MACJ,KAAK,EAAE;QACHA,CAAC,GAAG,GAAG;QACP;MACJ;QACI,MAAM,IAAI7E,qBAAqB,CAAC,uCAAuC,GAAG8E,WAAW,CAAC;IAC9F;IACA,OAAO,IAAI3E,WAAW,CAACwB,GAAG,GAAG,CAAC,EAAEkD,CAAC,CAAC;EACtC,CAAC;EACDtE,mBAAmB,CAACG,SAAS,CAAC2D,wBAAwB,GAAG,UAAU1C,GAAG,EAAE;IACpE,IAAIA,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGF,GAAG,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE,EAAEC,CAAC,EAAE;MAChE,IAAIA,CAAC,KAAK,CAAC,EAAE;QACT,IAAI,CAAC,IAAI,CAACrB,WAAW,CAACsB,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC,EAAE;UAChC,OAAO,KAAK;QAChB;MACJ,CAAC,MACI,IAAI,IAAI,CAACnB,WAAW,CAACsB,GAAG,CAACH,GAAG,GAAGE,CAAC,CAAC,EAAE;QACpC,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDtB,mBAAmB,CAACG,SAAS,CAACyD,0BAA0B,GAAG,UAAUxC,GAAG,EAAE;IACtE;IACA,IAAIA,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,KAAK,IAAIC,CAAC,GAAGF,GAAG,EAAEE,CAAC,GAAGF,GAAG,GAAG,CAAC,EAAE,EAAEE,CAAC,EAAE;MAChC,IAAI,IAAI,CAACrB,WAAW,CAACsB,GAAG,CAACD,CAAC,CAAC,EAAE;QACzB,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDtB,mBAAmB,CAACG,SAAS,CAACiD,4BAA4B,GAAG,UAAUhC,GAAG,EAAE;IACxE;IACA;IACA,IAAIA,GAAG,GAAG,CAAC,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAGF,GAAG,GAAG,IAAI,CAACnB,WAAW,CAACoB,OAAO,CAAC,CAAC,EAAE,EAAEC,CAAC,EAAE;MAChE,IAAI,IAAI,CAACrB,WAAW,CAACsB,GAAG,CAACH,GAAG,GAAGE,CAAC,CAAC,EAAE;QAC/B,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD,OAAOtB,mBAAmB;AAC9B,CAAC,CAAC,CAAE;AACJ,eAAeA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}