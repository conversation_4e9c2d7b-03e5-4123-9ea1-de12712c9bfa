const mongoose = require('mongoose');
const User = require('./models/User');
const Role = require('./models/Role');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/uni-core-business-suite', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function checkUsers() {
  try {
    console.log('👥 Checking users...');
    
    const users = await User.find({}).populate('role');
    console.log(`📊 Found ${users.length} users`);
    
    users.forEach((user, index) => {
      console.log(`\n👤 User ${index + 1}:`);
      console.log(`  ID: ${user._id}`);
      console.log(`  Username: ${user.username}`);
      console.log(`  Email: ${user.email}`);
      console.log(`  Role: ${user.role?.name || 'No role'}`);
      console.log(`  Active: ${user.isActive}`);
      
      // Generate a sample token for this user
      const sampleToken = `simple_token_${user._id}_${Date.now()}`;
      console.log(`  Sample Token: ${sampleToken}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkUsers();
