{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.qrcode.detector {*/\nimport ResultPoint from '../../ResultPoint';\n/**\n * <p>Encapsulates an alignment pattern, which are the smaller square patterns found in\n * all but the simplest QR Codes.</p>\n *\n * <AUTHOR> Owen\n */\nvar AlignmentPattern = /** @class */function (_super) {\n  __extends(AlignmentPattern, _super);\n  function AlignmentPattern(posX /*float*/, posY /*float*/, estimatedModuleSize /*float*/) {\n    var _this = _super.call(this, posX, posY) || this;\n    _this.estimatedModuleSize = estimatedModuleSize;\n    return _this;\n  }\n  /**\n   * <p>Determines if this alignment pattern \"about equals\" an alignment pattern at the stated\n   * position and size -- meaning, it is at nearly the same center with nearly the same size.</p>\n   */\n  AlignmentPattern.prototype.aboutEquals = function (moduleSize /*float*/, i /*float*/, j /*float*/) {\n    if (Math.abs(i - this.getY()) <= moduleSize && Math.abs(j - this.getX()) <= moduleSize) {\n      var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);\n      return moduleSizeDiff <= 1.0 || moduleSizeDiff <= this.estimatedModuleSize;\n    }\n    return false;\n  };\n  /**\n   * Combines this object's current estimate of a finder pattern position and module size\n   * with a new estimate. It returns a new {@code FinderPattern} containing an average of the two.\n   */\n  AlignmentPattern.prototype.combineEstimate = function (i /*float*/, j /*float*/, newModuleSize /*float*/) {\n    var combinedX = (this.getX() + j) / 2.0;\n    var combinedY = (this.getY() + i) / 2.0;\n    var combinedModuleSize = (this.estimatedModuleSize + newModuleSize) / 2.0;\n    return new AlignmentPattern(combinedX, combinedY, combinedModuleSize);\n  };\n  return AlignmentPattern;\n}(ResultPoint);\nexport default AlignmentPattern;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "ResultPoint", "AlignmentPattern", "_super", "posX", "posY", "estimatedModuleSize", "_this", "call", "aboutEquals", "moduleSize", "i", "j", "Math", "abs", "getY", "getX", "moduleSizeDiff", "combineEstimate", "newModuleSize", "combinedX", "combinedY", "combinedModuleSize"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/detector/AlignmentPattern.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.qrcode.detector {*/\nimport ResultPoint from '../../ResultPoint';\n/**\n * <p>Encapsulates an alignment pattern, which are the smaller square patterns found in\n * all but the simplest QR Codes.</p>\n *\n * <AUTHOR> Owen\n */\nvar AlignmentPattern = /** @class */ (function (_super) {\n    __extends(AlignmentPattern, _super);\n    function AlignmentPattern(posX /*float*/, posY /*float*/, estimatedModuleSize /*float*/) {\n        var _this = _super.call(this, posX, posY) || this;\n        _this.estimatedModuleSize = estimatedModuleSize;\n        return _this;\n    }\n    /**\n     * <p>Determines if this alignment pattern \"about equals\" an alignment pattern at the stated\n     * position and size -- meaning, it is at nearly the same center with nearly the same size.</p>\n     */\n    AlignmentPattern.prototype.aboutEquals = function (moduleSize /*float*/, i /*float*/, j /*float*/) {\n        if (Math.abs(i - this.getY()) <= moduleSize && Math.abs(j - this.getX()) <= moduleSize) {\n            var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);\n            return moduleSizeDiff <= 1.0 || moduleSizeDiff <= this.estimatedModuleSize;\n        }\n        return false;\n    };\n    /**\n     * Combines this object's current estimate of a finder pattern position and module size\n     * with a new estimate. It returns a new {@code FinderPattern} containing an average of the two.\n     */\n    AlignmentPattern.prototype.combineEstimate = function (i /*float*/, j /*float*/, newModuleSize /*float*/) {\n        var combinedX = (this.getX() + j) / 2.0;\n        var combinedY = (this.getY() + i) / 2.0;\n        var combinedModuleSize = (this.estimatedModuleSize + newModuleSize) / 2.0;\n        return new AlignmentPattern(combinedX, combinedY, combinedModuleSize);\n    };\n    return AlignmentPattern;\n}(ResultPoint));\nexport default AlignmentPattern;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,WAAW,MAAM,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,gBAAgB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACpDhB,SAAS,CAACe,gBAAgB,EAAEC,MAAM,CAAC;EACnC,SAASD,gBAAgBA,CAACE,IAAI,CAAC,WAAWC,IAAI,CAAC,WAAWC,mBAAmB,CAAC,WAAW;IACrF,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,EAAEJ,IAAI,EAAEC,IAAI,CAAC,IAAI,IAAI;IACjDE,KAAK,CAACD,mBAAmB,GAAGA,mBAAmB;IAC/C,OAAOC,KAAK;EAChB;EACA;AACJ;AACA;AACA;EACIL,gBAAgB,CAACH,SAAS,CAACU,WAAW,GAAG,UAAUC,UAAU,CAAC,WAAWC,CAAC,CAAC,WAAWC,CAAC,CAAC,WAAW;IAC/F,IAAIC,IAAI,CAACC,GAAG,CAACH,CAAC,GAAG,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC,IAAIL,UAAU,IAAIG,IAAI,CAACC,GAAG,CAACF,CAAC,GAAG,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC,IAAIN,UAAU,EAAE;MACpF,IAAIO,cAAc,GAAGJ,IAAI,CAACC,GAAG,CAACJ,UAAU,GAAG,IAAI,CAACJ,mBAAmB,CAAC;MACpE,OAAOW,cAAc,IAAI,GAAG,IAAIA,cAAc,IAAI,IAAI,CAACX,mBAAmB;IAC9E;IACA,OAAO,KAAK;EAChB,CAAC;EACD;AACJ;AACA;AACA;EACIJ,gBAAgB,CAACH,SAAS,CAACmB,eAAe,GAAG,UAAUP,CAAC,CAAC,WAAWC,CAAC,CAAC,WAAWO,aAAa,CAAC,WAAW;IACtG,IAAIC,SAAS,GAAG,CAAC,IAAI,CAACJ,IAAI,CAAC,CAAC,GAAGJ,CAAC,IAAI,GAAG;IACvC,IAAIS,SAAS,GAAG,CAAC,IAAI,CAACN,IAAI,CAAC,CAAC,GAAGJ,CAAC,IAAI,GAAG;IACvC,IAAIW,kBAAkB,GAAG,CAAC,IAAI,CAAChB,mBAAmB,GAAGa,aAAa,IAAI,GAAG;IACzE,OAAO,IAAIjB,gBAAgB,CAACkB,SAAS,EAAEC,SAAS,EAAEC,kBAAkB,CAAC;EACzE,CAAC;EACD,OAAOpB,gBAAgB;AAC3B,CAAC,CAACD,WAAW,CAAE;AACf,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}