{"ast": null, "code": "/**\n * Ponyfill for Java's Integer class.\n */\nvar Integer = /** @class */function () {\n  function Integer() {}\n  Integer.numberOfTrailingZeros = function (i) {\n    var y;\n    if (i === 0) return 32;\n    var n = 31;\n    y = i << 16;\n    if (y !== 0) {\n      n -= 16;\n      i = y;\n    }\n    y = i << 8;\n    if (y !== 0) {\n      n -= 8;\n      i = y;\n    }\n    y = i << 4;\n    if (y !== 0) {\n      n -= 4;\n      i = y;\n    }\n    y = i << 2;\n    if (y !== 0) {\n      n -= 2;\n      i = y;\n    }\n    return n - (i << 1 >>> 31);\n  };\n  Integer.numberOfLeadingZeros = function (i) {\n    // HD, Figure 5-6\n    if (i === 0) {\n      return 32;\n    }\n    var n = 1;\n    if (i >>> 16 === 0) {\n      n += 16;\n      i <<= 16;\n    }\n    if (i >>> 24 === 0) {\n      n += 8;\n      i <<= 8;\n    }\n    if (i >>> 28 === 0) {\n      n += 4;\n      i <<= 4;\n    }\n    if (i >>> 30 === 0) {\n      n += 2;\n      i <<= 2;\n    }\n    n -= i >>> 31;\n    return n;\n  };\n  Integer.toHexString = function (i) {\n    return i.toString(16);\n  };\n  Integer.toBinaryString = function (intNumber) {\n    return String(parseInt(String(intNumber), 2));\n  };\n  // Returns the number of one-bits in the two's complement binary representation of the specified int value. This function is sometimes referred to as the population count.\n  // Returns:\n  // the number of one-bits in the two's complement binary representation of the specified int value.\n  Integer.bitCount = function (i) {\n    // HD, Figure 5-2\n    i = i - (i >>> 1 & 0x55555555);\n    i = (i & 0x33333333) + (i >>> 2 & 0x33333333);\n    i = i + (i >>> 4) & 0x0f0f0f0f;\n    i = i + (i >>> 8);\n    i = i + (i >>> 16);\n    return i & 0x3f;\n  };\n  Integer.truncDivision = function (dividend, divisor) {\n    return Math.trunc(dividend / divisor);\n  };\n  /**\n   * Converts A string to an integer.\n   * @param s A string to convert into a number.\n   * @param radix A value between 2 and 36 that specifies the base of the number in numString. If this argument is not supplied, strings with a prefix of '0x' are considered hexadecimal. All other strings are considered decimal.\n   */\n  Integer.parseInt = function (num, radix) {\n    if (radix === void 0) {\n      radix = undefined;\n    }\n    return parseInt(num, radix);\n  };\n  Integer.MIN_VALUE_32_BITS = -2147483648;\n  Integer.MAX_VALUE = Number.MAX_SAFE_INTEGER;\n  return Integer;\n}();\nexport default Integer;", "map": {"version": 3, "names": ["Integer", "numberOfTrailingZeros", "i", "y", "n", "numberOfLeadingZeros", "toHexString", "toString", "toBinaryString", "intNumber", "String", "parseInt", "bitCount", "truncDivision", "dividend", "divisor", "Math", "trunc", "num", "radix", "undefined", "MIN_VALUE_32_BITS", "MAX_VALUE", "Number", "MAX_SAFE_INTEGER"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/Integer.js"], "sourcesContent": ["/**\n * Ponyfill for Java's Integer class.\n */\nvar Integer = /** @class */ (function () {\n    function Integer() {\n    }\n    Integer.numberOfTrailingZeros = function (i) {\n        var y;\n        if (i === 0)\n            return 32;\n        var n = 31;\n        y = i << 16;\n        if (y !== 0) {\n            n -= 16;\n            i = y;\n        }\n        y = i << 8;\n        if (y !== 0) {\n            n -= 8;\n            i = y;\n        }\n        y = i << 4;\n        if (y !== 0) {\n            n -= 4;\n            i = y;\n        }\n        y = i << 2;\n        if (y !== 0) {\n            n -= 2;\n            i = y;\n        }\n        return n - ((i << 1) >>> 31);\n    };\n    Integer.numberOfLeadingZeros = function (i) {\n        // HD, Figure 5-6\n        if (i === 0) {\n            return 32;\n        }\n        var n = 1;\n        if (i >>> 16 === 0) {\n            n += 16;\n            i <<= 16;\n        }\n        if (i >>> 24 === 0) {\n            n += 8;\n            i <<= 8;\n        }\n        if (i >>> 28 === 0) {\n            n += 4;\n            i <<= 4;\n        }\n        if (i >>> 30 === 0) {\n            n += 2;\n            i <<= 2;\n        }\n        n -= i >>> 31;\n        return n;\n    };\n    Integer.toHexString = function (i) {\n        return i.toString(16);\n    };\n    Integer.toBinaryString = function (intNumber) {\n        return String(parseInt(String(intNumber), 2));\n    };\n    // Returns the number of one-bits in the two's complement binary representation of the specified int value. This function is sometimes referred to as the population count.\n    // Returns:\n    // the number of one-bits in the two's complement binary representation of the specified int value.\n    Integer.bitCount = function (i) {\n        // HD, Figure 5-2\n        i = i - ((i >>> 1) & 0x55555555);\n        i = (i & 0x33333333) + ((i >>> 2) & 0x33333333);\n        i = (i + (i >>> 4)) & 0x0f0f0f0f;\n        i = i + (i >>> 8);\n        i = i + (i >>> 16);\n        return i & 0x3f;\n    };\n    Integer.truncDivision = function (dividend, divisor) {\n        return Math.trunc(dividend / divisor);\n    };\n    /**\n     * Converts A string to an integer.\n     * @param s A string to convert into a number.\n     * @param radix A value between 2 and 36 that specifies the base of the number in numString. If this argument is not supplied, strings with a prefix of '0x' are considered hexadecimal. All other strings are considered decimal.\n     */\n    Integer.parseInt = function (num, radix) {\n        if (radix === void 0) { radix = undefined; }\n        return parseInt(num, radix);\n    };\n    Integer.MIN_VALUE_32_BITS = -2147483648;\n    Integer.MAX_VALUE = Number.MAX_SAFE_INTEGER;\n    return Integer;\n}());\nexport default Integer;\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG,CACnB;EACAA,OAAO,CAACC,qBAAqB,GAAG,UAAUC,CAAC,EAAE;IACzC,IAAIC,CAAC;IACL,IAAID,CAAC,KAAK,CAAC,EACP,OAAO,EAAE;IACb,IAAIE,CAAC,GAAG,EAAE;IACVD,CAAC,GAAGD,CAAC,IAAI,EAAE;IACX,IAAIC,CAAC,KAAK,CAAC,EAAE;MACTC,CAAC,IAAI,EAAE;MACPF,CAAC,GAAGC,CAAC;IACT;IACAA,CAAC,GAAGD,CAAC,IAAI,CAAC;IACV,IAAIC,CAAC,KAAK,CAAC,EAAE;MACTC,CAAC,IAAI,CAAC;MACNF,CAAC,GAAGC,CAAC;IACT;IACAA,CAAC,GAAGD,CAAC,IAAI,CAAC;IACV,IAAIC,CAAC,KAAK,CAAC,EAAE;MACTC,CAAC,IAAI,CAAC;MACNF,CAAC,GAAGC,CAAC;IACT;IACAA,CAAC,GAAGD,CAAC,IAAI,CAAC;IACV,IAAIC,CAAC,KAAK,CAAC,EAAE;MACTC,CAAC,IAAI,CAAC;MACNF,CAAC,GAAGC,CAAC;IACT;IACA,OAAOC,CAAC,IAAKF,CAAC,IAAI,CAAC,KAAM,EAAE,CAAC;EAChC,CAAC;EACDF,OAAO,CAACK,oBAAoB,GAAG,UAAUH,CAAC,EAAE;IACxC;IACA,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,OAAO,EAAE;IACb;IACA,IAAIE,CAAC,GAAG,CAAC;IACT,IAAIF,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;MAChBE,CAAC,IAAI,EAAE;MACPF,CAAC,KAAK,EAAE;IACZ;IACA,IAAIA,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;MAChBE,CAAC,IAAI,CAAC;MACNF,CAAC,KAAK,CAAC;IACX;IACA,IAAIA,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;MAChBE,CAAC,IAAI,CAAC;MACNF,CAAC,KAAK,CAAC;IACX;IACA,IAAIA,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE;MAChBE,CAAC,IAAI,CAAC;MACNF,CAAC,KAAK,CAAC;IACX;IACAE,CAAC,IAAIF,CAAC,KAAK,EAAE;IACb,OAAOE,CAAC;EACZ,CAAC;EACDJ,OAAO,CAACM,WAAW,GAAG,UAAUJ,CAAC,EAAE;IAC/B,OAAOA,CAAC,CAACK,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EACDP,OAAO,CAACQ,cAAc,GAAG,UAAUC,SAAS,EAAE;IAC1C,OAAOC,MAAM,CAACC,QAAQ,CAACD,MAAM,CAACD,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;EACjD,CAAC;EACD;EACA;EACA;EACAT,OAAO,CAACY,QAAQ,GAAG,UAAUV,CAAC,EAAE;IAC5B;IACAA,CAAC,GAAGA,CAAC,IAAKA,CAAC,KAAK,CAAC,GAAI,UAAU,CAAC;IAChCA,CAAC,GAAG,CAACA,CAAC,GAAG,UAAU,KAAMA,CAAC,KAAK,CAAC,GAAI,UAAU,CAAC;IAC/CA,CAAC,GAAIA,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC,GAAI,UAAU;IAChCA,CAAC,GAAGA,CAAC,IAAIA,CAAC,KAAK,CAAC,CAAC;IACjBA,CAAC,GAAGA,CAAC,IAAIA,CAAC,KAAK,EAAE,CAAC;IAClB,OAAOA,CAAC,GAAG,IAAI;EACnB,CAAC;EACDF,OAAO,CAACa,aAAa,GAAG,UAAUC,QAAQ,EAAEC,OAAO,EAAE;IACjD,OAAOC,IAAI,CAACC,KAAK,CAACH,QAAQ,GAAGC,OAAO,CAAC;EACzC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIf,OAAO,CAACW,QAAQ,GAAG,UAAUO,GAAG,EAAEC,KAAK,EAAE;IACrC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAGC,SAAS;IAAE;IAC3C,OAAOT,QAAQ,CAACO,GAAG,EAAEC,KAAK,CAAC;EAC/B,CAAC;EACDnB,OAAO,CAACqB,iBAAiB,GAAG,CAAC,UAAU;EACvCrB,OAAO,CAACsB,SAAS,GAAGC,MAAM,CAACC,gBAAgB;EAC3C,OAAOxB,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}