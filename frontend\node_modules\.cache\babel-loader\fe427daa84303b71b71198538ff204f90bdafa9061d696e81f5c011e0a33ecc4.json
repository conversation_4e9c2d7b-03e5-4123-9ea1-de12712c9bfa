{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport ArgumentException from '../../ArgumentException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nexport var ErrorCorrectionLevelValues;\n(function (ErrorCorrectionLevelValues) {\n  ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"L\"] = 0] = \"L\";\n  ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"M\"] = 1] = \"M\";\n  ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"Q\"] = 2] = \"Q\";\n  ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"H\"] = 3] = \"H\";\n})(ErrorCorrectionLevelValues || (ErrorCorrectionLevelValues = {}));\n/**\n * <p>See ISO 18004:2006, 6.5.1. This enum encapsulates the four error correction levels\n * defined by the QR code standard.</p>\n *\n * <AUTHOR> Owen\n */\nvar ErrorCorrectionLevel = /** @class */function () {\n  function ErrorCorrectionLevel(value, stringValue, bits /*int*/) {\n    this.value = value;\n    this.stringValue = stringValue;\n    this.bits = bits;\n    ErrorCorrectionLevel.FOR_BITS.set(bits, this);\n    ErrorCorrectionLevel.FOR_VALUE.set(value, this);\n  }\n  ErrorCorrectionLevel.prototype.getValue = function () {\n    return this.value;\n  };\n  ErrorCorrectionLevel.prototype.getBits = function () {\n    return this.bits;\n  };\n  ErrorCorrectionLevel.fromString = function (s) {\n    switch (s) {\n      case 'L':\n        return ErrorCorrectionLevel.L;\n      case 'M':\n        return ErrorCorrectionLevel.M;\n      case 'Q':\n        return ErrorCorrectionLevel.Q;\n      case 'H':\n        return ErrorCorrectionLevel.H;\n      default:\n        throw new ArgumentException(s + 'not available');\n    }\n  };\n  ErrorCorrectionLevel.prototype.toString = function () {\n    return this.stringValue;\n  };\n  ErrorCorrectionLevel.prototype.equals = function (o) {\n    if (!(o instanceof ErrorCorrectionLevel)) {\n      return false;\n    }\n    var other = o;\n    return this.value === other.value;\n  };\n  /**\n   * @param bits int containing the two bits encoding a QR Code's error correction level\n   * @return ErrorCorrectionLevel representing the encoded error correction level\n   */\n  ErrorCorrectionLevel.forBits = function (bits /*int*/) {\n    if (bits < 0 || bits >= ErrorCorrectionLevel.FOR_BITS.size) {\n      throw new IllegalArgumentException();\n    }\n    return ErrorCorrectionLevel.FOR_BITS.get(bits);\n  };\n  ErrorCorrectionLevel.FOR_BITS = new Map();\n  ErrorCorrectionLevel.FOR_VALUE = new Map();\n  /** L = ~7% correction */\n  ErrorCorrectionLevel.L = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.L, 'L', 0x01);\n  /** M = ~15% correction */\n  ErrorCorrectionLevel.M = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.M, 'M', 0x00);\n  /** Q = ~25% correction */\n  ErrorCorrectionLevel.Q = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.Q, 'Q', 0x03);\n  /** H = ~30% correction */\n  ErrorCorrectionLevel.H = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.H, 'H', 0x02);\n  return ErrorCorrectionLevel;\n}();\nexport default ErrorCorrectionLevel;", "map": {"version": 3, "names": ["ArgumentException", "IllegalArgumentException", "ErrorCorrectionLevelValues", "ErrorCorrectionLevel", "value", "stringValue", "bits", "FOR_BITS", "set", "FOR_VALUE", "prototype", "getValue", "getBits", "fromString", "s", "L", "M", "Q", "H", "toString", "equals", "o", "other", "forBits", "size", "get", "Map"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/ErrorCorrectionLevel.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.decoder {*/\nimport ArgumentException from '../../ArgumentException';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nexport var ErrorCorrectionLevelValues;\n(function (ErrorCorrectionLevelValues) {\n    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"L\"] = 0] = \"L\";\n    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"M\"] = 1] = \"M\";\n    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"Q\"] = 2] = \"Q\";\n    ErrorCorrectionLevelValues[ErrorCorrectionLevelValues[\"H\"] = 3] = \"H\";\n})(ErrorCorrectionLevelValues || (ErrorCorrectionLevelValues = {}));\n/**\n * <p>See ISO 18004:2006, 6.5.1. This enum encapsulates the four error correction levels\n * defined by the QR code standard.</p>\n *\n * <AUTHOR> Owen\n */\nvar ErrorCorrectionLevel = /** @class */ (function () {\n    function ErrorCorrectionLevel(value, stringValue, bits /*int*/) {\n        this.value = value;\n        this.stringValue = stringValue;\n        this.bits = bits;\n        ErrorCorrectionLevel.FOR_BITS.set(bits, this);\n        ErrorCorrectionLevel.FOR_VALUE.set(value, this);\n    }\n    ErrorCorrectionLevel.prototype.getValue = function () {\n        return this.value;\n    };\n    ErrorCorrectionLevel.prototype.getBits = function () {\n        return this.bits;\n    };\n    ErrorCorrectionLevel.fromString = function (s) {\n        switch (s) {\n            case 'L': return ErrorCorrectionLevel.L;\n            case 'M': return ErrorCorrectionLevel.M;\n            case 'Q': return ErrorCorrectionLevel.Q;\n            case 'H': return ErrorCorrectionLevel.H;\n            default: throw new ArgumentException(s + 'not available');\n        }\n    };\n    ErrorCorrectionLevel.prototype.toString = function () {\n        return this.stringValue;\n    };\n    ErrorCorrectionLevel.prototype.equals = function (o) {\n        if (!(o instanceof ErrorCorrectionLevel)) {\n            return false;\n        }\n        var other = o;\n        return this.value === other.value;\n    };\n    /**\n     * @param bits int containing the two bits encoding a QR Code's error correction level\n     * @return ErrorCorrectionLevel representing the encoded error correction level\n     */\n    ErrorCorrectionLevel.forBits = function (bits /*int*/) {\n        if (bits < 0 || bits >= ErrorCorrectionLevel.FOR_BITS.size) {\n            throw new IllegalArgumentException();\n        }\n        return ErrorCorrectionLevel.FOR_BITS.get(bits);\n    };\n    ErrorCorrectionLevel.FOR_BITS = new Map();\n    ErrorCorrectionLevel.FOR_VALUE = new Map();\n    /** L = ~7% correction */\n    ErrorCorrectionLevel.L = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.L, 'L', 0x01);\n    /** M = ~15% correction */\n    ErrorCorrectionLevel.M = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.M, 'M', 0x00);\n    /** Q = ~25% correction */\n    ErrorCorrectionLevel.Q = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.Q, 'Q', 0x03);\n    /** H = ~30% correction */\n    ErrorCorrectionLevel.H = new ErrorCorrectionLevel(ErrorCorrectionLevelValues.H, 'H', 0x02);\n    return ErrorCorrectionLevel;\n}());\nexport default ErrorCorrectionLevel;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,wBAAwB,MAAM,gCAAgC;AACrE,OAAO,IAAIC,0BAA0B;AACrC,CAAC,UAAUA,0BAA0B,EAAE;EACnCA,0BAA0B,CAACA,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrEA,0BAA0B,CAACA,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrEA,0BAA0B,CAACA,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;EACrEA,0BAA0B,CAACA,0BAA0B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG;AACzE,CAAC,EAAEA,0BAA0B,KAAKA,0BAA0B,GAAG,CAAC,CAAC,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,aAAe,YAAY;EAClD,SAASA,oBAAoBA,CAACC,KAAK,EAAEC,WAAW,EAAEC,IAAI,CAAC,SAAS;IAC5D,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChBH,oBAAoB,CAACI,QAAQ,CAACC,GAAG,CAACF,IAAI,EAAE,IAAI,CAAC;IAC7CH,oBAAoB,CAACM,SAAS,CAACD,GAAG,CAACJ,KAAK,EAAE,IAAI,CAAC;EACnD;EACAD,oBAAoB,CAACO,SAAS,CAACC,QAAQ,GAAG,YAAY;IAClD,OAAO,IAAI,CAACP,KAAK;EACrB,CAAC;EACDD,oBAAoB,CAACO,SAAS,CAACE,OAAO,GAAG,YAAY;IACjD,OAAO,IAAI,CAACN,IAAI;EACpB,CAAC;EACDH,oBAAoB,CAACU,UAAU,GAAG,UAAUC,CAAC,EAAE;IAC3C,QAAQA,CAAC;MACL,KAAK,GAAG;QAAE,OAAOX,oBAAoB,CAACY,CAAC;MACvC,KAAK,GAAG;QAAE,OAAOZ,oBAAoB,CAACa,CAAC;MACvC,KAAK,GAAG;QAAE,OAAOb,oBAAoB,CAACc,CAAC;MACvC,KAAK,GAAG;QAAE,OAAOd,oBAAoB,CAACe,CAAC;MACvC;QAAS,MAAM,IAAIlB,iBAAiB,CAACc,CAAC,GAAG,eAAe,CAAC;IAC7D;EACJ,CAAC;EACDX,oBAAoB,CAACO,SAAS,CAACS,QAAQ,GAAG,YAAY;IAClD,OAAO,IAAI,CAACd,WAAW;EAC3B,CAAC;EACDF,oBAAoB,CAACO,SAAS,CAACU,MAAM,GAAG,UAAUC,CAAC,EAAE;IACjD,IAAI,EAAEA,CAAC,YAAYlB,oBAAoB,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAImB,KAAK,GAAGD,CAAC;IACb,OAAO,IAAI,CAACjB,KAAK,KAAKkB,KAAK,CAAClB,KAAK;EACrC,CAAC;EACD;AACJ;AACA;AACA;EACID,oBAAoB,CAACoB,OAAO,GAAG,UAAUjB,IAAI,CAAC,SAAS;IACnD,IAAIA,IAAI,GAAG,CAAC,IAAIA,IAAI,IAAIH,oBAAoB,CAACI,QAAQ,CAACiB,IAAI,EAAE;MACxD,MAAM,IAAIvB,wBAAwB,CAAC,CAAC;IACxC;IACA,OAAOE,oBAAoB,CAACI,QAAQ,CAACkB,GAAG,CAACnB,IAAI,CAAC;EAClD,CAAC;EACDH,oBAAoB,CAACI,QAAQ,GAAG,IAAImB,GAAG,CAAC,CAAC;EACzCvB,oBAAoB,CAACM,SAAS,GAAG,IAAIiB,GAAG,CAAC,CAAC;EAC1C;EACAvB,oBAAoB,CAACY,CAAC,GAAG,IAAIZ,oBAAoB,CAACD,0BAA0B,CAACa,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;EAC1F;EACAZ,oBAAoB,CAACa,CAAC,GAAG,IAAIb,oBAAoB,CAACD,0BAA0B,CAACc,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;EAC1F;EACAb,oBAAoB,CAACc,CAAC,GAAG,IAAId,oBAAoB,CAACD,0BAA0B,CAACe,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;EAC1F;EACAd,oBAAoB,CAACe,CAAC,GAAG,IAAIf,oBAAoB,CAACD,0BAA0B,CAACgB,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;EAC1F,OAAOf,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,eAAeA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}