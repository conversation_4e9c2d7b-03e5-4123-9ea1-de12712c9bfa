{"ast": null, "code": "import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { useUtils } from \"./useUtils.js\";\n/**\n * Hooks making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nexport const useValueWithTimezone = ({\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange,\n  valueManager\n}) => {\n  const utils = useUtils();\n  const firstDefaultValue = React.useRef(defaultValue);\n  const inputValue = valueProp ?? firstDefaultValue.current ?? valueManager.emptyValue;\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, inputValue), [utils, valueManager, inputValue]);\n  const setInputTimezone = useEventCallback(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  let timezoneToRender;\n  if (timezoneProp) {\n    timezoneToRender = timezoneProp;\n  } else if (inputTimezone) {\n    timezoneToRender = inputTimezone;\n  } else if (referenceDate) {\n    timezoneToRender = utils.getTimezone(referenceDate);\n  } else {\n    timezoneToRender = 'default';\n  }\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, inputValue), [valueManager, utils, timezoneToRender, inputValue]);\n  const handleValueChange = useEventCallback((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    onChange?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\n\n/**\n * Wrapper around `useControlled` and `useValueWithTimezone`\n */\nexport const useControlledValueWithTimezone = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const [valueWithInputTimezone, setValue] = useControlled({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const onChange = useEventCallback((newValue, ...otherParams) => {\n    setValue(newValue);\n    onChangeProp?.(newValue, ...otherParams);\n  });\n  return useValueWithTimezone({\n    timezone: timezoneProp,\n    value: valueWithInputTimezone,\n    defaultValue: undefined,\n    referenceDate,\n    onChange,\n    valueManager\n  });\n};", "map": {"version": 3, "names": ["React", "useEventCallback", "useControlled", "useUtils", "useValueWithTimezone", "timezone", "timezoneProp", "value", "valueProp", "defaultValue", "referenceDate", "onChange", "valueManager", "utils", "firstDefaultValue", "useRef", "inputValue", "current", "emptyValue", "inputTimezone", "useMemo", "getTimezone", "setInputTimezone", "newValue", "setTimezone", "timezoneToRender", "valueWithTimezoneToRender", "handleValueChange", "otherParams", "newValueWithInputTimezone", "useControlledValueWithTimezone", "name", "onChangeProp", "valueWithInputTimezone", "setValue", "state", "controlled", "default", "undefined"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/useValueWithTimezone.js"], "sourcesContent": ["import * as React from 'react';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport useControlled from '@mui/utils/useControlled';\nimport { useUtils } from \"./useUtils.js\";\n/**\n * Hooks making sure that:\n * - The value returned by `onChange` always have the timezone of `props.value` or `props.defaultValue` if defined\n * - The value rendered is always the one from `props.timezone` if defined\n */\nexport const useValueWithTimezone = ({\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange,\n  valueManager\n}) => {\n  const utils = useUtils();\n  const firstDefaultValue = React.useRef(defaultValue);\n  const inputValue = valueProp ?? firstDefaultValue.current ?? valueManager.emptyValue;\n  const inputTimezone = React.useMemo(() => valueManager.getTimezone(utils, inputValue), [utils, valueManager, inputValue]);\n  const setInputTimezone = useEventCallback(newValue => {\n    if (inputTimezone == null) {\n      return newValue;\n    }\n    return valueManager.setTimezone(utils, inputTimezone, newValue);\n  });\n  let timezoneToRender;\n  if (timezoneProp) {\n    timezoneToRender = timezoneProp;\n  } else if (inputTimezone) {\n    timezoneToRender = inputTimezone;\n  } else if (referenceDate) {\n    timezoneToRender = utils.getTimezone(referenceDate);\n  } else {\n    timezoneToRender = 'default';\n  }\n  const valueWithTimezoneToRender = React.useMemo(() => valueManager.setTimezone(utils, timezoneToRender, inputValue), [valueManager, utils, timezoneToRender, inputValue]);\n  const handleValueChange = useEventCallback((newValue, ...otherParams) => {\n    const newValueWithInputTimezone = setInputTimezone(newValue);\n    onChange?.(newValueWithInputTimezone, ...otherParams);\n  });\n  return {\n    value: valueWithTimezoneToRender,\n    handleValueChange,\n    timezone: timezoneToRender\n  };\n};\n\n/**\n * Wrapper around `useControlled` and `useValueWithTimezone`\n */\nexport const useControlledValueWithTimezone = ({\n  name,\n  timezone: timezoneProp,\n  value: valueProp,\n  defaultValue,\n  referenceDate,\n  onChange: onChangeProp,\n  valueManager\n}) => {\n  const [valueWithInputTimezone, setValue] = useControlled({\n    name,\n    state: 'value',\n    controlled: valueProp,\n    default: defaultValue ?? valueManager.emptyValue\n  });\n  const onChange = useEventCallback((newValue, ...otherParams) => {\n    setValue(newValue);\n    onChangeProp?.(newValue, ...otherParams);\n  });\n  return useValueWithTimezone({\n    timezone: timezoneProp,\n    value: valueWithInputTimezone,\n    defaultValue: undefined,\n    referenceDate,\n    onChange,\n    valueManager\n  });\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,QAAQ,QAAQ,eAAe;AACxC;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAAC;EACnCC,QAAQ,EAAEC,YAAY;EACtBC,KAAK,EAAEC,SAAS;EAChBC,YAAY;EACZC,aAAa;EACbC,QAAQ;EACRC;AACF,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAGV,QAAQ,CAAC,CAAC;EACxB,MAAMW,iBAAiB,GAAGd,KAAK,CAACe,MAAM,CAACN,YAAY,CAAC;EACpD,MAAMO,UAAU,GAAGR,SAAS,IAAIM,iBAAiB,CAACG,OAAO,IAAIL,YAAY,CAACM,UAAU;EACpF,MAAMC,aAAa,GAAGnB,KAAK,CAACoB,OAAO,CAAC,MAAMR,YAAY,CAACS,WAAW,CAACR,KAAK,EAAEG,UAAU,CAAC,EAAE,CAACH,KAAK,EAAED,YAAY,EAAEI,UAAU,CAAC,CAAC;EACzH,MAAMM,gBAAgB,GAAGrB,gBAAgB,CAACsB,QAAQ,IAAI;IACpD,IAAIJ,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOI,QAAQ;IACjB;IACA,OAAOX,YAAY,CAACY,WAAW,CAACX,KAAK,EAAEM,aAAa,EAAEI,QAAQ,CAAC;EACjE,CAAC,CAAC;EACF,IAAIE,gBAAgB;EACpB,IAAInB,YAAY,EAAE;IAChBmB,gBAAgB,GAAGnB,YAAY;EACjC,CAAC,MAAM,IAAIa,aAAa,EAAE;IACxBM,gBAAgB,GAAGN,aAAa;EAClC,CAAC,MAAM,IAAIT,aAAa,EAAE;IACxBe,gBAAgB,GAAGZ,KAAK,CAACQ,WAAW,CAACX,aAAa,CAAC;EACrD,CAAC,MAAM;IACLe,gBAAgB,GAAG,SAAS;EAC9B;EACA,MAAMC,yBAAyB,GAAG1B,KAAK,CAACoB,OAAO,CAAC,MAAMR,YAAY,CAACY,WAAW,CAACX,KAAK,EAAEY,gBAAgB,EAAET,UAAU,CAAC,EAAE,CAACJ,YAAY,EAAEC,KAAK,EAAEY,gBAAgB,EAAET,UAAU,CAAC,CAAC;EACzK,MAAMW,iBAAiB,GAAG1B,gBAAgB,CAAC,CAACsB,QAAQ,EAAE,GAAGK,WAAW,KAAK;IACvE,MAAMC,yBAAyB,GAAGP,gBAAgB,CAACC,QAAQ,CAAC;IAC5DZ,QAAQ,GAAGkB,yBAAyB,EAAE,GAAGD,WAAW,CAAC;EACvD,CAAC,CAAC;EACF,OAAO;IACLrB,KAAK,EAAEmB,yBAAyB;IAChCC,iBAAiB;IACjBtB,QAAQ,EAAEoB;EACZ,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMK,8BAA8B,GAAGA,CAAC;EAC7CC,IAAI;EACJ1B,QAAQ,EAAEC,YAAY;EACtBC,KAAK,EAAEC,SAAS;EAChBC,YAAY;EACZC,aAAa;EACbC,QAAQ,EAAEqB,YAAY;EACtBpB;AACF,CAAC,KAAK;EACJ,MAAM,CAACqB,sBAAsB,EAAEC,QAAQ,CAAC,GAAGhC,aAAa,CAAC;IACvD6B,IAAI;IACJI,KAAK,EAAE,OAAO;IACdC,UAAU,EAAE5B,SAAS;IACrB6B,OAAO,EAAE5B,YAAY,IAAIG,YAAY,CAACM;EACxC,CAAC,CAAC;EACF,MAAMP,QAAQ,GAAGV,gBAAgB,CAAC,CAACsB,QAAQ,EAAE,GAAGK,WAAW,KAAK;IAC9DM,QAAQ,CAACX,QAAQ,CAAC;IAClBS,YAAY,GAAGT,QAAQ,EAAE,GAAGK,WAAW,CAAC;EAC1C,CAAC,CAAC;EACF,OAAOxB,oBAAoB,CAAC;IAC1BC,QAAQ,EAAEC,YAAY;IACtBC,KAAK,EAAE0B,sBAAsB;IAC7BxB,YAAY,EAAE6B,SAAS;IACvB5B,aAAa;IACbC,QAAQ;IACRC;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}