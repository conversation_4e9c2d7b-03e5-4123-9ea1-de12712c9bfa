import { useState, useEffect } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import { CssBaseline } from "@mui/material";
import {
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  Settings as SettingsIcon,
  Business as BusinessIcon,
  Inventory,
  Category as CategoryIcon,
  Receipt as ReceiptIcon,
  AccountBalance as AccountBalanceIcon,
  List as ListIcon,
  Store as StoreIcon,
  Undo as UndoIcon,
  AssignmentReturn as ReturnIcon,
  Contacts as ContactsIcon,
  ShoppingBag as PurchasesIcon,
  Storefront as SalesIcon,
  PersonAdd as AddPersonIcon,
  Add as AddIcon,
  Assessment as StockTrackingIcon,
  Security as SecurityIcon,
  Group as UsersIcon,
  QrCode as QrCodeIcon,
  Payment as PaymentIcon
} from "@mui/icons-material";

// Import Layout component
import Layout from "./components/Layout";

// Import pages and components
import Dashboard from "./pages/Dashboard";

import AddCustomerPage from "./pages/AddCustomerPage";
import AddVendorPage from "./pages/AddVendorPage";
import UserManagementPage from "./pages/UserManagementPage";
import UsersPage from "./pages/UsersPage";
import RolesPage from "./pages/RolesPage";
import LoginPage from "./pages/LoginPage";
import ManageOrders from "./components/ManageOrders";
import CustomerList from "./components/CustomerList";
import SettingsPage from "./pages/SettingsPage";
import CompanySetupPage from "./pages/CompanySetupPage";
import BankAccountsPage from "./pages/BankAccountsPage";
import ItemsPage from "./pages/ItemsPage";
import ContactsPage from "./pages/ContactsPage";
import PurchasesPage from "./pages/PurchasesPage";
import SalesPage from "./pages/SalesPage";
import ManageCategories from "./components/ManageCategories";
import ManageItems from "./components/ManageItems";
import OrderListPage from "./pages/OrderListPage";
import VendorList from "./components/VendorList";
import ManagePurchaseInvoices from "./components/ManagePurchaseInvoices";
import PurchaseInvoiceList from "./components/PurchaseInvoiceList";
import StockTracking from "./components/StockTracking";
import ManagePurchaseReturns from "./components/ManagePurchaseReturns";
import PurchaseReturnList from "./components/PurchaseReturnList";
import ManageSalesReturns from "./components/ManageSalesReturns";
import SalesReturnList from "./components/SalesReturnList";
import BarcodeGenerator from "./components/BarcodeGenerator";
import AccountingPage from "./pages/AccountingPage";
import ChartOfAccounts from "./components/ChartOfAccounts";
import GeneralLedger from "./components/GeneralLedger";
import SubsidiaryLedger from "./components/SubsidiaryLedger";
import AddBankAccountPage from "./pages/AddBankAccountPage";
import BankPaymentsPage from "./pages/BankPaymentsPage";
import ManageBankPaymentPage from "./pages/ManageBankPaymentPage";

// Define menu items with icons and paths
const menuItems = [
  {
    title: "Dashboard",
    icon: <DashboardIcon />,
    path: "/",
  },
  {
    title: "Contacts",
    icon: <ContactsIcon />,
    path: "/contacts", // Landing page for contacts
    subItems: [
      {
        title: "Customer List",
        icon: <PeopleIcon />,
        path: "/customers",
      },
      {
        title: "Add Customer",
        icon: <AddPersonIcon />,
        path: "/add-customer",
      },
      {
        title: "Vendor List",
        icon: <StoreIcon />,
        path: "/vendors",
      },
      {
        title: "Add Vendor",
        icon: <BusinessIcon />,
        path: "/add-vendor",
      },
    ],
  },
  {
    title: "Purchases",
    icon: <PurchasesIcon />,
    path: "/purchases", // Landing page for purchases
    subItems: [
      {
        title: "Invoice List",
        icon: <ListIcon />,
        path: "/purchase-invoices",
      },
      {
        title: "Create Invoice",
        icon: <AddIcon />,
        path: "/create-purchase-invoice",
      },
      {
        title: "Return List",
        icon: <UndoIcon />,
        path: "/purchase-returns",
      },
      {
        title: "Create Return",
        icon: <AddIcon />,
        path: "/create-purchase-return",
      },
    ],
  },
  {
    title: "Sales",
    icon: <SalesIcon />,
    path: "/sales", // Landing page for sales
    subItems: [
      {
        title: "Order List",
        icon: <ListIcon />,
        path: "/orders",
      },
      {
        title: "Create Order",
        icon: <AddIcon />,
        path: "/manage-order",
      },
      {
        title: "Return List",
        icon: <ReturnIcon />,
        path: "/sales-returns",
      },
      {
        title: "Create Return",
        icon: <AddIcon />,
        path: "/create-sales-return",
      },
    ],
  },
  {
    title: "Stock Tracking",
    icon: <StockTrackingIcon />,
    path: "/stock-tracking",
  },
  {
    title: "Accounting & Finance",
    icon: <AccountBalanceIcon />,
    path: "/accounting",
    subItems: [
      {
        title: "Chart of Accounts",
        icon: <AccountBalanceIcon />,
        path: "/accounting/chart-of-accounts",
      },
      {
        title: "General Ledger",
        icon: <ReceiptIcon />,
        path: "/accounting/general-ledger",
      },
      {
        title: "Bank Accounts",
        icon: <AccountBalanceIcon />,
        path: "/bank-accounts",
      },
      {
        title: "Bank Payments",
        icon: <PaymentIcon />,
        path: "/accounting/bank-payments",
      },
      {
        title: "Bank Receipts",
        icon: <PaymentIcon />,
        path: "/accounting/bank-receipts",
      },
      {
        title: "Cash Payments",
        icon: <PaymentIcon />,
        path: "/accounting/cash-payments",
      },
      {
        title: "Cash Receipts",
        icon: <PaymentIcon />,
        path: "/accounting/cash-receipts",
      },
    ],
  },
  {
    title: "Products & Services",
    icon: <Inventory />,
    path: "/items-page",
    subItems: [
      {
        title: "All Items",
        icon: <Inventory />,
        path: "/items-page",
      },
      {
        title: "Categories",
        icon: <CategoryIcon />,
        path: "/manage-categories",
      },
      {
        title: "Manage Items",
        icon: <ReceiptIcon />,
        path: "/manage-items",
      },
      {
        title: "Barcode Generator",
        icon: <QrCodeIcon />,
        path: "/barcode-generator",
      },
    ],
  },
  {
    title: "Settings",
    icon: <SettingsIcon />,
    path: "/settings",
    subItems: [
      {
        title: "General Settings",
        icon: <SettingsIcon />,
        path: "/settings",
      },
      {
        title: "Company Setup",
        icon: <BusinessIcon />,
        path: "/company-setup",
      },
    ],
  },
  {
    title: "User Management",
    icon: <SecurityIcon />,
    path: "/user-management",
    subItems: [
      {
        title: "Users",
        icon: <UsersIcon />,
        path: "/users",
      },
      {
        title: "Roles & Permissions",
        icon: <SecurityIcon />,
        path: "/roles",
      },
    ],
  },
];

const App = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const token = localStorage.getItem("authToken");
    const userData = localStorage.getItem("currentUser");

    if (token && userData) {
      try {
        const user = JSON.parse(userData);
        setCurrentUser(user);
        setIsAuthenticated(true);
      } catch (error) {
        console.error("Error parsing user data:", error);
        localStorage.removeItem("authToken");
        localStorage.removeItem("currentUser");
      }
    }
    setLoading(false);
  }, []);

  const handleLogin = (user, token) => {
    setCurrentUser(user);
    setIsAuthenticated(true);
    localStorage.setItem("authToken", token);
    localStorage.setItem("currentUser", JSON.stringify(user));
  };

  const updateCurrentUser = (updatedUser) => {
    setCurrentUser(updatedUser);
    localStorage.setItem("currentUser", JSON.stringify(updatedUser));
  };

  const handleLogout = () => {
    setCurrentUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem("authToken");
    localStorage.removeItem("currentUser");
  };

  if (loading) {
    return <CssBaseline />;
  }

  if (!isAuthenticated) {
    return (
      <>
        <CssBaseline />
        <Router>
          <Routes>
            <Route path="/login" element={<LoginPage onLogin={handleLogin} />} />
            <Route path="*" element={<Navigate to="/login" replace />} />
          </Routes>
        </Router>
      </>
    );
  }

  return (
    <>
      <CssBaseline />
      <Router>
        <Layout menuItems={menuItems} currentUser={currentUser} onLogout={handleLogout} onUserUpdate={updateCurrentUser}>
          <Routes>
            <Route path="/login" element={<Navigate to="/" replace />} />
            <Route path="/" element={<Dashboard />} />

            {/* Landing Pages for Grouped Menu Items */}
            <Route path="/contacts" element={<ContactsPage />} />
            <Route path="/purchases" element={<PurchasesPage />} />
            <Route path="/sales" element={<SalesPage />} />
            <Route path="/accounting" element={<AccountingPage />} />
            <Route path="/user-management" element={<UserManagementPage />} />

            {/* Individual Pages */}
            <Route path="/add-customer" element={<AddCustomerPage />} />
            <Route path="/add-vendor" element={<AddVendorPage />} />
            <Route path="/manage-order" element={<ManageOrders />} />
            <Route path="/orders" element={<OrderListPage />} />
            <Route path="/customers" element={<CustomerList />} />

            {/* Vendor Management Routes */}
            <Route path="/vendors" element={<VendorList />} />

            {/* Purchase Invoice Routes */}
            <Route path="/purchase-invoices" element={<PurchaseInvoiceList />} />
            <Route path="/create-purchase-invoice" element={<ManagePurchaseInvoices />} />

            {/* Purchase Return Routes */}
            <Route path="/purchase-returns" element={<PurchaseReturnList />} />
            <Route path="/create-purchase-return" element={<ManagePurchaseReturns />} />

            {/* Sales Return Routes */}
            <Route path="/sales-returns" element={<SalesReturnList />} />
            <Route path="/create-sales-return" element={<ManageSalesReturns />} />

            {/* Barcode Generator Route */}
            <Route path="/barcode-generator" element={<BarcodeGenerator />} />

            {/* Accounting Routes */}
            <Route path="/accounting/chart-of-accounts" element={<ChartOfAccounts />} />
            <Route path="/accounting/general-ledger" element={<GeneralLedger />} />
            <Route path="/accounting/subsidiary-ledgers" element={<SubsidiaryLedger />} />
            <Route path="/accounting/bank-payments" element={<BankPaymentsPage />} />
            <Route path="/manage-bank-payment" element={<ManageBankPaymentPage />} />
            <Route path="/ledger/:accountId" element={<GeneralLedger />} />

            <Route path="/stock-tracking" element={<StockTracking />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/company-setup" element={<CompanySetupPage />} />
            <Route path="/bank-accounts" element={<BankAccountsPage />} />
            <Route path="/add-bank-account" element={<AddBankAccountPage />} />

            {/* Product & Services Management Routes */}
            <Route path="/items-page" element={<ItemsPage />} />
            <Route path="/manage-categories" element={<ManageCategories />} />
            <Route path="/manage-items" element={<ManageItems />} />

            {/* User Management Routes */}
            <Route path="/users" element={<UsersPage />} />
            <Route path="/add-user" element={<UsersPage />} />
            <Route path="/roles" element={<RolesPage />} />
            <Route path="/add-role" element={<RolesPage />} />

          </Routes>
        </Layout>
      </Router>
    </>
  );
};

export default App;
