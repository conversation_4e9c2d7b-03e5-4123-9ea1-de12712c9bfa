{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport GenericGF from './GenericGF';\nimport GenericGFPoly from './GenericGFPoly';\nimport ReedSolomonException from '../../ReedSolomonException';\nimport IllegalStateException from '../../IllegalStateException';\n/**\n * <p>Implements Reed-Solomon decoding, as the name implies.</p>\n *\n * <p>The algorithm will not be explained here, but the following references were helpful\n * in creating this implementation:</p>\n *\n * <ul>\n * <li><PERSON>.\n * <a href=\"http://www.cs.cmu.edu/afs/cs.cmu.edu/project/pscico-guyb/realworld/www/rs_decode.ps\">\n * \"Decoding Reed-Solomon Codes\"</a> (see discussion of Forney's Formula)</li>\n * <li>J.I. Hall. <a href=\"www.mth.msu.edu/~jhall/classes/codenotes/GRS.pdf\">\n * \"Chapter 5. Generalized Reed-Solomon Codes\"</a>\n * (see discussion of Euclidean algorithm)</li>\n * </ul>\n *\n * <p>Much credit is due to William Rucklidge since portions of this code are an indirect\n * port of his C++ Reed-Solomon implementation.</p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> Rucklidge\n * <AUTHOR>\n */\nvar ReedSolomonDecoder = /** @class */function () {\n  function ReedSolomonDecoder(field) {\n    this.field = field;\n  }\n  /**\n   * <p>Decodes given set of received codewords, which include both data and error-correction\n   * codewords. Really, this means it uses Reed-Solomon to detect and correct errors, in-place,\n   * in the input.</p>\n   *\n   * @param received data and error-correction codewords\n   * @param twoS number of error-correction codewords available\n   * @throws ReedSolomonException if decoding fails for any reason\n   */\n  ReedSolomonDecoder.prototype.decode = function (received, twoS /*int*/) {\n    var field = this.field;\n    var poly = new GenericGFPoly(field, received);\n    var syndromeCoefficients = new Int32Array(twoS);\n    var noError = true;\n    for (var i = 0; i < twoS; i++) {\n      var evalResult = poly.evaluateAt(field.exp(i + field.getGeneratorBase()));\n      syndromeCoefficients[syndromeCoefficients.length - 1 - i] = evalResult;\n      if (evalResult !== 0) {\n        noError = false;\n      }\n    }\n    if (noError) {\n      return;\n    }\n    var syndrome = new GenericGFPoly(field, syndromeCoefficients);\n    var sigmaOmega = this.runEuclideanAlgorithm(field.buildMonomial(twoS, 1), syndrome, twoS);\n    var sigma = sigmaOmega[0];\n    var omega = sigmaOmega[1];\n    var errorLocations = this.findErrorLocations(sigma);\n    var errorMagnitudes = this.findErrorMagnitudes(omega, errorLocations);\n    for (var i = 0; i < errorLocations.length; i++) {\n      var position = received.length - 1 - field.log(errorLocations[i]);\n      if (position < 0) {\n        throw new ReedSolomonException('Bad error location');\n      }\n      received[position] = GenericGF.addOrSubtract(received[position], errorMagnitudes[i]);\n    }\n  };\n  ReedSolomonDecoder.prototype.runEuclideanAlgorithm = function (a, b, R /*int*/) {\n    // Assume a's degree is >= b's\n    if (a.getDegree() < b.getDegree()) {\n      var temp = a;\n      a = b;\n      b = temp;\n    }\n    var field = this.field;\n    var rLast = a;\n    var r = b;\n    var tLast = field.getZero();\n    var t = field.getOne();\n    // Run Euclidean algorithm until r's degree is less than R/2\n    while (r.getDegree() >= (R / 2 | 0)) {\n      var rLastLast = rLast;\n      var tLastLast = tLast;\n      rLast = r;\n      tLast = t;\n      // Divide rLastLast by rLast, with quotient in q and remainder in r\n      if (rLast.isZero()) {\n        // Oops, Euclidean algorithm already terminated?\n        throw new ReedSolomonException('r_{i-1} was zero');\n      }\n      r = rLastLast;\n      var q = field.getZero();\n      var denominatorLeadingTerm = rLast.getCoefficient(rLast.getDegree());\n      var dltInverse = field.inverse(denominatorLeadingTerm);\n      while (r.getDegree() >= rLast.getDegree() && !r.isZero()) {\n        var degreeDiff = r.getDegree() - rLast.getDegree();\n        var scale = field.multiply(r.getCoefficient(r.getDegree()), dltInverse);\n        q = q.addOrSubtract(field.buildMonomial(degreeDiff, scale));\n        r = r.addOrSubtract(rLast.multiplyByMonomial(degreeDiff, scale));\n      }\n      t = q.multiply(tLast).addOrSubtract(tLastLast);\n      if (r.getDegree() >= rLast.getDegree()) {\n        throw new IllegalStateException('Division algorithm failed to reduce polynomial?');\n      }\n    }\n    var sigmaTildeAtZero = t.getCoefficient(0);\n    if (sigmaTildeAtZero === 0) {\n      throw new ReedSolomonException('sigmaTilde(0) was zero');\n    }\n    var inverse = field.inverse(sigmaTildeAtZero);\n    var sigma = t.multiplyScalar(inverse);\n    var omega = r.multiplyScalar(inverse);\n    return [sigma, omega];\n  };\n  ReedSolomonDecoder.prototype.findErrorLocations = function (errorLocator) {\n    // This is a direct application of Chien's search\n    var numErrors = errorLocator.getDegree();\n    if (numErrors === 1) {\n      // shortcut\n      return Int32Array.from([errorLocator.getCoefficient(1)]);\n    }\n    var result = new Int32Array(numErrors);\n    var e = 0;\n    var field = this.field;\n    for (var i = 1; i < field.getSize() && e < numErrors; i++) {\n      if (errorLocator.evaluateAt(i) === 0) {\n        result[e] = field.inverse(i);\n        e++;\n      }\n    }\n    if (e !== numErrors) {\n      throw new ReedSolomonException('Error locator degree does not match number of roots');\n    }\n    return result;\n  };\n  ReedSolomonDecoder.prototype.findErrorMagnitudes = function (errorEvaluator, errorLocations) {\n    // This is directly applying Forney's Formula\n    var s = errorLocations.length;\n    var result = new Int32Array(s);\n    var field = this.field;\n    for (var i = 0; i < s; i++) {\n      var xiInverse = field.inverse(errorLocations[i]);\n      var denominator = 1;\n      for (var j = 0; j < s; j++) {\n        if (i !== j) {\n          // denominator = field.multiply(denominator,\n          //    GenericGF.addOrSubtract(1, field.multiply(errorLocations[j], xiInverse)))\n          // Above should work but fails on some Apple and Linux JDKs due to a Hotspot bug.\n          // Below is a funny-looking workaround from Steven Parkes\n          var term = field.multiply(errorLocations[j], xiInverse);\n          var termPlus1 = (term & 0x1) === 0 ? term | 1 : term & ~1;\n          denominator = field.multiply(denominator, termPlus1);\n        }\n      }\n      result[i] = field.multiply(errorEvaluator.evaluateAt(xiInverse), field.inverse(denominator));\n      if (field.getGeneratorBase() !== 0) {\n        result[i] = field.multiply(result[i], xiInverse);\n      }\n    }\n    return result;\n  };\n  return ReedSolomonDecoder;\n}();\nexport default ReedSolomonDecoder;", "map": {"version": 3, "names": ["GenericGF", "GenericGFPoly", "ReedSolomonException", "IllegalStateException", "ReedSolomonDecoder", "field", "prototype", "decode", "received", "twoS", "poly", "syndromeCoefficients", "Int32Array", "noError", "i", "evalResult", "evaluateAt", "exp", "getGeneratorBase", "length", "syndrome", "sigmaOmega", "runEuclideanAlgorithm", "buildMonomial", "sigma", "omega", "errorLocations", "findErrorLocations", "errorMagnitudes", "findErrorMagnitudes", "position", "log", "addOrSubtract", "a", "b", "R", "getDegree", "temp", "rLast", "r", "tLast", "getZero", "t", "getOne", "rLastLast", "tLastLast", "isZero", "q", "denominatorLeadingTerm", "getCoefficient", "dltInverse", "inverse", "degreeDiff", "scale", "multiply", "multiplyByMonomial", "sigmaTildeAtZero", "multiplyScalar", "errorLocator", "numErrors", "from", "result", "e", "getSize", "errorEvaluator", "s", "xiInverse", "denominator", "j", "term", "termPlus1"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/reedsolomon/ReedSolomonDecoder.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport GenericGF from './GenericGF';\nimport GenericGFPoly from './GenericGFPoly';\nimport ReedSolomonException from '../../ReedSolomonException';\nimport IllegalStateException from '../../IllegalStateException';\n/**\n * <p>Implements Reed-Solomon decoding, as the name implies.</p>\n *\n * <p>The algorithm will not be explained here, but the following references were helpful\n * in creating this implementation:</p>\n *\n * <ul>\n * <li><PERSON>.\n * <a href=\"http://www.cs.cmu.edu/afs/cs.cmu.edu/project/pscico-guyb/realworld/www/rs_decode.ps\">\n * \"Decoding Reed-Solomon Codes\"</a> (see discussion of Forney's Formula)</li>\n * <li>J.I. Hall. <a href=\"www.mth.msu.edu/~jhall/classes/codenotes/GRS.pdf\">\n * \"Chapter 5. Generalized Reed-Solomon Codes\"</a>\n * (see discussion of Euclidean algorithm)</li>\n * </ul>\n *\n * <p>Much credit is due to William Rucklidge since portions of this code are an indirect\n * port of his C++ Reed-Solomon implementation.</p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> Rucklidge\n * <AUTHOR>\n */\nvar ReedSolomonDecoder = /** @class */ (function () {\n    function ReedSolomonDecoder(field) {\n        this.field = field;\n    }\n    /**\n     * <p>Decodes given set of received codewords, which include both data and error-correction\n     * codewords. Really, this means it uses Reed-Solomon to detect and correct errors, in-place,\n     * in the input.</p>\n     *\n     * @param received data and error-correction codewords\n     * @param twoS number of error-correction codewords available\n     * @throws ReedSolomonException if decoding fails for any reason\n     */\n    ReedSolomonDecoder.prototype.decode = function (received, twoS /*int*/) {\n        var field = this.field;\n        var poly = new GenericGFPoly(field, received);\n        var syndromeCoefficients = new Int32Array(twoS);\n        var noError = true;\n        for (var i = 0; i < twoS; i++) {\n            var evalResult = poly.evaluateAt(field.exp(i + field.getGeneratorBase()));\n            syndromeCoefficients[syndromeCoefficients.length - 1 - i] = evalResult;\n            if (evalResult !== 0) {\n                noError = false;\n            }\n        }\n        if (noError) {\n            return;\n        }\n        var syndrome = new GenericGFPoly(field, syndromeCoefficients);\n        var sigmaOmega = this.runEuclideanAlgorithm(field.buildMonomial(twoS, 1), syndrome, twoS);\n        var sigma = sigmaOmega[0];\n        var omega = sigmaOmega[1];\n        var errorLocations = this.findErrorLocations(sigma);\n        var errorMagnitudes = this.findErrorMagnitudes(omega, errorLocations);\n        for (var i = 0; i < errorLocations.length; i++) {\n            var position = received.length - 1 - field.log(errorLocations[i]);\n            if (position < 0) {\n                throw new ReedSolomonException('Bad error location');\n            }\n            received[position] = GenericGF.addOrSubtract(received[position], errorMagnitudes[i]);\n        }\n    };\n    ReedSolomonDecoder.prototype.runEuclideanAlgorithm = function (a, b, R /*int*/) {\n        // Assume a's degree is >= b's\n        if (a.getDegree() < b.getDegree()) {\n            var temp = a;\n            a = b;\n            b = temp;\n        }\n        var field = this.field;\n        var rLast = a;\n        var r = b;\n        var tLast = field.getZero();\n        var t = field.getOne();\n        // Run Euclidean algorithm until r's degree is less than R/2\n        while (r.getDegree() >= (R / 2 | 0)) {\n            var rLastLast = rLast;\n            var tLastLast = tLast;\n            rLast = r;\n            tLast = t;\n            // Divide rLastLast by rLast, with quotient in q and remainder in r\n            if (rLast.isZero()) {\n                // Oops, Euclidean algorithm already terminated?\n                throw new ReedSolomonException('r_{i-1} was zero');\n            }\n            r = rLastLast;\n            var q = field.getZero();\n            var denominatorLeadingTerm = rLast.getCoefficient(rLast.getDegree());\n            var dltInverse = field.inverse(denominatorLeadingTerm);\n            while (r.getDegree() >= rLast.getDegree() && !r.isZero()) {\n                var degreeDiff = r.getDegree() - rLast.getDegree();\n                var scale = field.multiply(r.getCoefficient(r.getDegree()), dltInverse);\n                q = q.addOrSubtract(field.buildMonomial(degreeDiff, scale));\n                r = r.addOrSubtract(rLast.multiplyByMonomial(degreeDiff, scale));\n            }\n            t = q.multiply(tLast).addOrSubtract(tLastLast);\n            if (r.getDegree() >= rLast.getDegree()) {\n                throw new IllegalStateException('Division algorithm failed to reduce polynomial?');\n            }\n        }\n        var sigmaTildeAtZero = t.getCoefficient(0);\n        if (sigmaTildeAtZero === 0) {\n            throw new ReedSolomonException('sigmaTilde(0) was zero');\n        }\n        var inverse = field.inverse(sigmaTildeAtZero);\n        var sigma = t.multiplyScalar(inverse);\n        var omega = r.multiplyScalar(inverse);\n        return [sigma, omega];\n    };\n    ReedSolomonDecoder.prototype.findErrorLocations = function (errorLocator) {\n        // This is a direct application of Chien's search\n        var numErrors = errorLocator.getDegree();\n        if (numErrors === 1) { // shortcut\n            return Int32Array.from([errorLocator.getCoefficient(1)]);\n        }\n        var result = new Int32Array(numErrors);\n        var e = 0;\n        var field = this.field;\n        for (var i = 1; i < field.getSize() && e < numErrors; i++) {\n            if (errorLocator.evaluateAt(i) === 0) {\n                result[e] = field.inverse(i);\n                e++;\n            }\n        }\n        if (e !== numErrors) {\n            throw new ReedSolomonException('Error locator degree does not match number of roots');\n        }\n        return result;\n    };\n    ReedSolomonDecoder.prototype.findErrorMagnitudes = function (errorEvaluator, errorLocations) {\n        // This is directly applying Forney's Formula\n        var s = errorLocations.length;\n        var result = new Int32Array(s);\n        var field = this.field;\n        for (var i = 0; i < s; i++) {\n            var xiInverse = field.inverse(errorLocations[i]);\n            var denominator = 1;\n            for (var j = 0; j < s; j++) {\n                if (i !== j) {\n                    // denominator = field.multiply(denominator,\n                    //    GenericGF.addOrSubtract(1, field.multiply(errorLocations[j], xiInverse)))\n                    // Above should work but fails on some Apple and Linux JDKs due to a Hotspot bug.\n                    // Below is a funny-looking workaround from Steven Parkes\n                    var term = field.multiply(errorLocations[j], xiInverse);\n                    var termPlus1 = (term & 0x1) === 0 ? term | 1 : term & ~1;\n                    denominator = field.multiply(denominator, termPlus1);\n                }\n            }\n            result[i] = field.multiply(errorEvaluator.evaluateAt(xiInverse), field.inverse(denominator));\n            if (field.getGeneratorBase() !== 0) {\n                result[i] = field.multiply(result[i], xiInverse);\n            }\n        }\n        return result;\n    };\n    return ReedSolomonDecoder;\n}());\nexport default ReedSolomonDecoder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,oBAAoB,MAAM,4BAA4B;AAC7D,OAAOC,qBAAqB,MAAM,6BAA6B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,kBAAkB,GAAG,aAAe,YAAY;EAChD,SAASA,kBAAkBA,CAACC,KAAK,EAAE;IAC/B,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACID,kBAAkB,CAACE,SAAS,CAACC,MAAM,GAAG,UAAUC,QAAQ,EAAEC,IAAI,CAAC,SAAS;IACpE,IAAIJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIK,IAAI,GAAG,IAAIT,aAAa,CAACI,KAAK,EAAEG,QAAQ,CAAC;IAC7C,IAAIG,oBAAoB,GAAG,IAAIC,UAAU,CAACH,IAAI,CAAC;IAC/C,IAAII,OAAO,GAAG,IAAI;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,EAAEK,CAAC,EAAE,EAAE;MAC3B,IAAIC,UAAU,GAAGL,IAAI,CAACM,UAAU,CAACX,KAAK,CAACY,GAAG,CAACH,CAAC,GAAGT,KAAK,CAACa,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzEP,oBAAoB,CAACA,oBAAoB,CAACQ,MAAM,GAAG,CAAC,GAAGL,CAAC,CAAC,GAAGC,UAAU;MACtE,IAAIA,UAAU,KAAK,CAAC,EAAE;QAClBF,OAAO,GAAG,KAAK;MACnB;IACJ;IACA,IAAIA,OAAO,EAAE;MACT;IACJ;IACA,IAAIO,QAAQ,GAAG,IAAInB,aAAa,CAACI,KAAK,EAAEM,oBAAoB,CAAC;IAC7D,IAAIU,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACjB,KAAK,CAACkB,aAAa,CAACd,IAAI,EAAE,CAAC,CAAC,EAAEW,QAAQ,EAAEX,IAAI,CAAC;IACzF,IAAIe,KAAK,GAAGH,UAAU,CAAC,CAAC,CAAC;IACzB,IAAII,KAAK,GAAGJ,UAAU,CAAC,CAAC,CAAC;IACzB,IAAIK,cAAc,GAAG,IAAI,CAACC,kBAAkB,CAACH,KAAK,CAAC;IACnD,IAAII,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAACJ,KAAK,EAAEC,cAAc,CAAC;IACrE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,cAAc,CAACP,MAAM,EAAEL,CAAC,EAAE,EAAE;MAC5C,IAAIgB,QAAQ,GAAGtB,QAAQ,CAACW,MAAM,GAAG,CAAC,GAAGd,KAAK,CAAC0B,GAAG,CAACL,cAAc,CAACZ,CAAC,CAAC,CAAC;MACjE,IAAIgB,QAAQ,GAAG,CAAC,EAAE;QACd,MAAM,IAAI5B,oBAAoB,CAAC,oBAAoB,CAAC;MACxD;MACAM,QAAQ,CAACsB,QAAQ,CAAC,GAAG9B,SAAS,CAACgC,aAAa,CAACxB,QAAQ,CAACsB,QAAQ,CAAC,EAAEF,eAAe,CAACd,CAAC,CAAC,CAAC;IACxF;EACJ,CAAC;EACDV,kBAAkB,CAACE,SAAS,CAACgB,qBAAqB,GAAG,UAAUW,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,SAAS;IAC5E;IACA,IAAIF,CAAC,CAACG,SAAS,CAAC,CAAC,GAAGF,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE;MAC/B,IAAIC,IAAI,GAAGJ,CAAC;MACZA,CAAC,GAAGC,CAAC;MACLA,CAAC,GAAGG,IAAI;IACZ;IACA,IAAIhC,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIiC,KAAK,GAAGL,CAAC;IACb,IAAIM,CAAC,GAAGL,CAAC;IACT,IAAIM,KAAK,GAAGnC,KAAK,CAACoC,OAAO,CAAC,CAAC;IAC3B,IAAIC,CAAC,GAAGrC,KAAK,CAACsC,MAAM,CAAC,CAAC;IACtB;IACA,OAAOJ,CAAC,CAACH,SAAS,CAAC,CAAC,KAAKD,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MACjC,IAAIS,SAAS,GAAGN,KAAK;MACrB,IAAIO,SAAS,GAAGL,KAAK;MACrBF,KAAK,GAAGC,CAAC;MACTC,KAAK,GAAGE,CAAC;MACT;MACA,IAAIJ,KAAK,CAACQ,MAAM,CAAC,CAAC,EAAE;QAChB;QACA,MAAM,IAAI5C,oBAAoB,CAAC,kBAAkB,CAAC;MACtD;MACAqC,CAAC,GAAGK,SAAS;MACb,IAAIG,CAAC,GAAG1C,KAAK,CAACoC,OAAO,CAAC,CAAC;MACvB,IAAIO,sBAAsB,GAAGV,KAAK,CAACW,cAAc,CAACX,KAAK,CAACF,SAAS,CAAC,CAAC,CAAC;MACpE,IAAIc,UAAU,GAAG7C,KAAK,CAAC8C,OAAO,CAACH,sBAAsB,CAAC;MACtD,OAAOT,CAAC,CAACH,SAAS,CAAC,CAAC,IAAIE,KAAK,CAACF,SAAS,CAAC,CAAC,IAAI,CAACG,CAAC,CAACO,MAAM,CAAC,CAAC,EAAE;QACtD,IAAIM,UAAU,GAAGb,CAAC,CAACH,SAAS,CAAC,CAAC,GAAGE,KAAK,CAACF,SAAS,CAAC,CAAC;QAClD,IAAIiB,KAAK,GAAGhD,KAAK,CAACiD,QAAQ,CAACf,CAAC,CAACU,cAAc,CAACV,CAAC,CAACH,SAAS,CAAC,CAAC,CAAC,EAAEc,UAAU,CAAC;QACvEH,CAAC,GAAGA,CAAC,CAACf,aAAa,CAAC3B,KAAK,CAACkB,aAAa,CAAC6B,UAAU,EAAEC,KAAK,CAAC,CAAC;QAC3Dd,CAAC,GAAGA,CAAC,CAACP,aAAa,CAACM,KAAK,CAACiB,kBAAkB,CAACH,UAAU,EAAEC,KAAK,CAAC,CAAC;MACpE;MACAX,CAAC,GAAGK,CAAC,CAACO,QAAQ,CAACd,KAAK,CAAC,CAACR,aAAa,CAACa,SAAS,CAAC;MAC9C,IAAIN,CAAC,CAACH,SAAS,CAAC,CAAC,IAAIE,KAAK,CAACF,SAAS,CAAC,CAAC,EAAE;QACpC,MAAM,IAAIjC,qBAAqB,CAAC,iDAAiD,CAAC;MACtF;IACJ;IACA,IAAIqD,gBAAgB,GAAGd,CAAC,CAACO,cAAc,CAAC,CAAC,CAAC;IAC1C,IAAIO,gBAAgB,KAAK,CAAC,EAAE;MACxB,MAAM,IAAItD,oBAAoB,CAAC,wBAAwB,CAAC;IAC5D;IACA,IAAIiD,OAAO,GAAG9C,KAAK,CAAC8C,OAAO,CAACK,gBAAgB,CAAC;IAC7C,IAAIhC,KAAK,GAAGkB,CAAC,CAACe,cAAc,CAACN,OAAO,CAAC;IACrC,IAAI1B,KAAK,GAAGc,CAAC,CAACkB,cAAc,CAACN,OAAO,CAAC;IACrC,OAAO,CAAC3B,KAAK,EAAEC,KAAK,CAAC;EACzB,CAAC;EACDrB,kBAAkB,CAACE,SAAS,CAACqB,kBAAkB,GAAG,UAAU+B,YAAY,EAAE;IACtE;IACA,IAAIC,SAAS,GAAGD,YAAY,CAACtB,SAAS,CAAC,CAAC;IACxC,IAAIuB,SAAS,KAAK,CAAC,EAAE;MAAE;MACnB,OAAO/C,UAAU,CAACgD,IAAI,CAAC,CAACF,YAAY,CAACT,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D;IACA,IAAIY,MAAM,GAAG,IAAIjD,UAAU,CAAC+C,SAAS,CAAC;IACtC,IAAIG,CAAC,GAAG,CAAC;IACT,IAAIzD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,CAAC0D,OAAO,CAAC,CAAC,IAAID,CAAC,GAAGH,SAAS,EAAE7C,CAAC,EAAE,EAAE;MACvD,IAAI4C,YAAY,CAAC1C,UAAU,CAACF,CAAC,CAAC,KAAK,CAAC,EAAE;QAClC+C,MAAM,CAACC,CAAC,CAAC,GAAGzD,KAAK,CAAC8C,OAAO,CAACrC,CAAC,CAAC;QAC5BgD,CAAC,EAAE;MACP;IACJ;IACA,IAAIA,CAAC,KAAKH,SAAS,EAAE;MACjB,MAAM,IAAIzD,oBAAoB,CAAC,qDAAqD,CAAC;IACzF;IACA,OAAO2D,MAAM;EACjB,CAAC;EACDzD,kBAAkB,CAACE,SAAS,CAACuB,mBAAmB,GAAG,UAAUmC,cAAc,EAAEtC,cAAc,EAAE;IACzF;IACA,IAAIuC,CAAC,GAAGvC,cAAc,CAACP,MAAM;IAC7B,IAAI0C,MAAM,GAAG,IAAIjD,UAAU,CAACqD,CAAC,CAAC;IAC9B,IAAI5D,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmD,CAAC,EAAEnD,CAAC,EAAE,EAAE;MACxB,IAAIoD,SAAS,GAAG7D,KAAK,CAAC8C,OAAO,CAACzB,cAAc,CAACZ,CAAC,CAAC,CAAC;MAChD,IAAIqD,WAAW,GAAG,CAAC;MACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;QACxB,IAAItD,CAAC,KAAKsD,CAAC,EAAE;UACT;UACA;UACA;UACA;UACA,IAAIC,IAAI,GAAGhE,KAAK,CAACiD,QAAQ,CAAC5B,cAAc,CAAC0C,CAAC,CAAC,EAAEF,SAAS,CAAC;UACvD,IAAII,SAAS,GAAG,CAACD,IAAI,GAAG,GAAG,MAAM,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;UACzDF,WAAW,GAAG9D,KAAK,CAACiD,QAAQ,CAACa,WAAW,EAAEG,SAAS,CAAC;QACxD;MACJ;MACAT,MAAM,CAAC/C,CAAC,CAAC,GAAGT,KAAK,CAACiD,QAAQ,CAACU,cAAc,CAAChD,UAAU,CAACkD,SAAS,CAAC,EAAE7D,KAAK,CAAC8C,OAAO,CAACgB,WAAW,CAAC,CAAC;MAC5F,IAAI9D,KAAK,CAACa,gBAAgB,CAAC,CAAC,KAAK,CAAC,EAAE;QAChC2C,MAAM,CAAC/C,CAAC,CAAC,GAAGT,KAAK,CAACiD,QAAQ,CAACO,MAAM,CAAC/C,CAAC,CAAC,EAAEoD,SAAS,CAAC;MACpD;IACJ;IACA,OAAOL,MAAM;EACjB,CAAC;EACD,OAAOzD,kBAAkB;AAC7B,CAAC,CAAC,CAAE;AACJ,eAAeA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}