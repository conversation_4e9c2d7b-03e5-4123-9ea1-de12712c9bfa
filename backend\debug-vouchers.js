const mongoose = require('mongoose');
const PaymentVoucher = require('./models/PaymentVoucher');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/uni-core-business-suite', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function debugVouchers() {
  try {
    console.log('🔍 Checking payment vouchers...');
    
    const vouchers = await PaymentVoucher.find({}).limit(5);
    console.log(`📊 Found ${vouchers.length} vouchers`);
    
    vouchers.forEach((voucher, index) => {
      console.log(`\n📄 Voucher ${index + 1}:`);
      console.log(`  ID: ${voucher._id}`);
      console.log(`  Number: ${voucher.voucherNumber}`);
      console.log(`  Status: ${voucher.status}`);
      console.log(`  VoucherDate: ${voucher.voucherDate}`);
      console.log(`  TransactionDate: ${voucher.transactionDate}`);
      console.log(`  Amount: ${voucher.amount}`);
      console.log(`  CreatedAt: ${voucher.createdAt}`);
    });
    
    // Check for vouchers with missing required fields
    const vouchersWithMissingFields = await PaymentVoucher.find({
      $or: [
        { voucherDate: { $exists: false } },
        { voucherDate: null },
        { transactionDate: { $exists: false } },
        { transactionDate: null }
      ]
    });
    
    console.log(`\n⚠️  Found ${vouchersWithMissingFields.length} vouchers with missing required fields`);
    
    vouchersWithMissingFields.forEach((voucher, index) => {
      console.log(`\n❌ Voucher ${index + 1} with missing fields:`);
      console.log(`  ID: ${voucher._id}`);
      console.log(`  Number: ${voucher.voucherNumber}`);
      console.log(`  VoucherDate: ${voucher.voucherDate}`);
      console.log(`  TransactionDate: ${voucher.transactionDate}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    mongoose.connection.close();
  }
}

debugVouchers();
