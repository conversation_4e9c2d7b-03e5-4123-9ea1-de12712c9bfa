{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.qrcode.detector {*/\nimport ResultPoint from '../../ResultPoint';\n/**\n * <p>Encapsulates a finder pattern, which are the three square patterns found in\n * the corners of QR Codes. It also encapsulates a count of similar finder patterns,\n * as a convenience to the finder's bookkeeping.</p>\n *\n * <AUTHOR> Owen\n */\nvar FinderPattern = /** @class */function (_super) {\n  __extends(FinderPattern, _super);\n  // FinderPattern(posX: number/*float*/, posY: number/*float*/, estimatedModuleSize: number/*float*/) {\n  //   this(posX, posY, estimatedModuleSize, 1)\n  // }\n  function FinderPattern(posX /*float*/, posY /*float*/, estimatedModuleSize /*float*/, count /*int*/) {\n    var _this = _super.call(this, posX, posY) || this;\n    _this.estimatedModuleSize = estimatedModuleSize;\n    _this.count = count;\n    if (undefined === count) {\n      _this.count = 1;\n    }\n    return _this;\n  }\n  FinderPattern.prototype.getEstimatedModuleSize = function () {\n    return this.estimatedModuleSize;\n  };\n  FinderPattern.prototype.getCount = function () {\n    return this.count;\n  };\n  /*\n  void incrementCount() {\n    this.count++\n  }\n   */\n  /**\n   * <p>Determines if this finder pattern \"about equals\" a finder pattern at the stated\n   * position and size -- meaning, it is at nearly the same center with nearly the same size.</p>\n   */\n  FinderPattern.prototype.aboutEquals = function (moduleSize /*float*/, i /*float*/, j /*float*/) {\n    if (Math.abs(i - this.getY()) <= moduleSize && Math.abs(j - this.getX()) <= moduleSize) {\n      var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);\n      return moduleSizeDiff <= 1.0 || moduleSizeDiff <= this.estimatedModuleSize;\n    }\n    return false;\n  };\n  /**\n   * Combines this object's current estimate of a finder pattern position and module size\n   * with a new estimate. It returns a new {@code FinderPattern} containing a weighted average\n   * based on count.\n   */\n  FinderPattern.prototype.combineEstimate = function (i /*float*/, j /*float*/, newModuleSize /*float*/) {\n    var combinedCount = this.count + 1;\n    var combinedX = (this.count * this.getX() + j) / combinedCount;\n    var combinedY = (this.count * this.getY() + i) / combinedCount;\n    var combinedModuleSize = (this.count * this.estimatedModuleSize + newModuleSize) / combinedCount;\n    return new FinderPattern(combinedX, combinedY, combinedModuleSize, combinedCount);\n  };\n  return FinderPattern;\n}(ResultPoint);\nexport default FinderPattern;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "ResultPoint", "FinderPattern", "_super", "posX", "posY", "estimatedModuleSize", "count", "_this", "call", "undefined", "getEstimatedModuleSize", "getCount", "aboutEquals", "moduleSize", "i", "j", "Math", "abs", "getY", "getX", "moduleSizeDiff", "combineEstimate", "newModuleSize", "combinedCount", "combinedX", "combinedY", "combinedModuleSize"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPattern.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.qrcode.detector {*/\nimport ResultPoint from '../../ResultPoint';\n/**\n * <p>Encapsulates a finder pattern, which are the three square patterns found in\n * the corners of QR Codes. It also encapsulates a count of similar finder patterns,\n * as a convenience to the finder's bookkeeping.</p>\n *\n * <AUTHOR> Owen\n */\nvar FinderPattern = /** @class */ (function (_super) {\n    __extends(FinderPattern, _super);\n    // FinderPattern(posX: number/*float*/, posY: number/*float*/, estimatedModuleSize: number/*float*/) {\n    //   this(posX, posY, estimatedModuleSize, 1)\n    // }\n    function FinderPattern(posX /*float*/, posY /*float*/, estimatedModuleSize /*float*/, count /*int*/) {\n        var _this = _super.call(this, posX, posY) || this;\n        _this.estimatedModuleSize = estimatedModuleSize;\n        _this.count = count;\n        if (undefined === count) {\n            _this.count = 1;\n        }\n        return _this;\n    }\n    FinderPattern.prototype.getEstimatedModuleSize = function () {\n        return this.estimatedModuleSize;\n    };\n    FinderPattern.prototype.getCount = function () {\n        return this.count;\n    };\n    /*\n    void incrementCount() {\n      this.count++\n    }\n     */\n    /**\n     * <p>Determines if this finder pattern \"about equals\" a finder pattern at the stated\n     * position and size -- meaning, it is at nearly the same center with nearly the same size.</p>\n     */\n    FinderPattern.prototype.aboutEquals = function (moduleSize /*float*/, i /*float*/, j /*float*/) {\n        if (Math.abs(i - this.getY()) <= moduleSize && Math.abs(j - this.getX()) <= moduleSize) {\n            var moduleSizeDiff = Math.abs(moduleSize - this.estimatedModuleSize);\n            return moduleSizeDiff <= 1.0 || moduleSizeDiff <= this.estimatedModuleSize;\n        }\n        return false;\n    };\n    /**\n     * Combines this object's current estimate of a finder pattern position and module size\n     * with a new estimate. It returns a new {@code FinderPattern} containing a weighted average\n     * based on count.\n     */\n    FinderPattern.prototype.combineEstimate = function (i /*float*/, j /*float*/, newModuleSize /*float*/) {\n        var combinedCount = this.count + 1;\n        var combinedX = (this.count * this.getX() + j) / combinedCount;\n        var combinedY = (this.count * this.getY() + i) / combinedCount;\n        var combinedModuleSize = (this.count * this.estimatedModuleSize + newModuleSize) / combinedCount;\n        return new FinderPattern(combinedX, combinedY, combinedModuleSize, combinedCount);\n    };\n    return FinderPattern;\n}(ResultPoint));\nexport default FinderPattern;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,WAAW,MAAM,mBAAmB;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,aAAe,UAAUC,MAAM,EAAE;EACjDhB,SAAS,CAACe,aAAa,EAAEC,MAAM,CAAC;EAChC;EACA;EACA;EACA,SAASD,aAAaA,CAACE,IAAI,CAAC,WAAWC,IAAI,CAAC,WAAWC,mBAAmB,CAAC,WAAWC,KAAK,CAAC,SAAS;IACjG,IAAIC,KAAK,GAAGL,MAAM,CAACM,IAAI,CAAC,IAAI,EAAEL,IAAI,EAAEC,IAAI,CAAC,IAAI,IAAI;IACjDG,KAAK,CAACF,mBAAmB,GAAGA,mBAAmB;IAC/CE,KAAK,CAACD,KAAK,GAAGA,KAAK;IACnB,IAAIG,SAAS,KAAKH,KAAK,EAAE;MACrBC,KAAK,CAACD,KAAK,GAAG,CAAC;IACnB;IACA,OAAOC,KAAK;EAChB;EACAN,aAAa,CAACH,SAAS,CAACY,sBAAsB,GAAG,YAAY;IACzD,OAAO,IAAI,CAACL,mBAAmB;EACnC,CAAC;EACDJ,aAAa,CAACH,SAAS,CAACa,QAAQ,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACL,KAAK;EACrB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI;AACJ;AACA;AACA;EACIL,aAAa,CAACH,SAAS,CAACc,WAAW,GAAG,UAAUC,UAAU,CAAC,WAAWC,CAAC,CAAC,WAAWC,CAAC,CAAC,WAAW;IAC5F,IAAIC,IAAI,CAACC,GAAG,CAACH,CAAC,GAAG,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC,IAAIL,UAAU,IAAIG,IAAI,CAACC,GAAG,CAACF,CAAC,GAAG,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC,IAAIN,UAAU,EAAE;MACpF,IAAIO,cAAc,GAAGJ,IAAI,CAACC,GAAG,CAACJ,UAAU,GAAG,IAAI,CAACR,mBAAmB,CAAC;MACpE,OAAOe,cAAc,IAAI,GAAG,IAAIA,cAAc,IAAI,IAAI,CAACf,mBAAmB;IAC9E;IACA,OAAO,KAAK;EAChB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIJ,aAAa,CAACH,SAAS,CAACuB,eAAe,GAAG,UAAUP,CAAC,CAAC,WAAWC,CAAC,CAAC,WAAWO,aAAa,CAAC,WAAW;IACnG,IAAIC,aAAa,GAAG,IAAI,CAACjB,KAAK,GAAG,CAAC;IAClC,IAAIkB,SAAS,GAAG,CAAC,IAAI,CAAClB,KAAK,GAAG,IAAI,CAACa,IAAI,CAAC,CAAC,GAAGJ,CAAC,IAAIQ,aAAa;IAC9D,IAAIE,SAAS,GAAG,CAAC,IAAI,CAACnB,KAAK,GAAG,IAAI,CAACY,IAAI,CAAC,CAAC,GAAGJ,CAAC,IAAIS,aAAa;IAC9D,IAAIG,kBAAkB,GAAG,CAAC,IAAI,CAACpB,KAAK,GAAG,IAAI,CAACD,mBAAmB,GAAGiB,aAAa,IAAIC,aAAa;IAChG,OAAO,IAAItB,aAAa,CAACuB,SAAS,EAAEC,SAAS,EAAEC,kBAAkB,EAAEH,aAAa,CAAC;EACrF,CAAC;EACD,OAAOtB,aAAa;AACxB,CAAC,CAACD,WAAW,CAAE;AACf,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}