const mongoose = require('mongoose');
const PaymentVoucher = require('./models/PaymentVoucher');

// Connect to MongoDB
mongoose.connect('mongodb://127.0.0.1:27017/uni-core-business-suite', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function fixVoucherDates() {
  try {
    console.log('🔧 Fixing payment vouchers with missing dates...');
    
    // Find vouchers with missing required fields
    const vouchersToFix = await PaymentVoucher.find({
      $or: [
        { voucherDate: { $exists: false } },
        { voucherDate: null },
        { voucherDate: undefined }
      ]
    });
    
    console.log(`📊 Found ${vouchersToFix.length} vouchers to fix`);
    
    for (const voucher of vouchersToFix) {
      console.log(`\n🔧 Fixing voucher ${voucher.voucherNumber} (${voucher._id})`);
      
      // Set voucherDate to transactionDate if available, otherwise use createdAt
      const fixedVoucherDate = voucher.transactionDate || voucher.createdAt || new Date();
      
      // Update the voucher directly in the database
      await PaymentVoucher.updateOne(
        { _id: voucher._id },
        { 
          $set: { 
            voucherDate: fixedVoucherDate,
            // Also ensure transactionDate is set
            transactionDate: voucher.transactionDate || fixedVoucherDate
          }
        }
      );
      
      console.log(`✅ Fixed voucher ${voucher.voucherNumber}:`);
      console.log(`   VoucherDate: ${fixedVoucherDate}`);
      console.log(`   TransactionDate: ${voucher.transactionDate || fixedVoucherDate}`);
    }
    
    console.log('\n🎉 All vouchers fixed successfully!');
    
    // Verify the fix
    const remainingBrokenVouchers = await PaymentVoucher.find({
      $or: [
        { voucherDate: { $exists: false } },
        { voucherDate: null },
        { voucherDate: undefined }
      ]
    });
    
    console.log(`\n✅ Verification: ${remainingBrokenVouchers.length} vouchers still have missing dates`);
    
  } catch (error) {
    console.error('❌ Error fixing vouchers:', error);
  } finally {
    mongoose.connection.close();
  }
}

fixVoucherDates();
