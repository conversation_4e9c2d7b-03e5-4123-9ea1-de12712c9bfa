{"ast": null, "code": "/*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// import java.util.List;\n/**\n * <AUTHOR>\n */\nvar PDF417DetectorResult = /** @class */function () {\n  function PDF417DetectorResult(bits, points) {\n    this.bits = bits;\n    this.points = points;\n  }\n  PDF417DetectorResult.prototype.getBits = function () {\n    return this.bits;\n  };\n  PDF417DetectorResult.prototype.getPoints = function () {\n    return this.points;\n  };\n  return PDF417DetectorResult;\n}();\nexport default PDF417DetectorResult;", "map": {"version": 3, "names": ["PDF417DetectorResult", "bits", "points", "prototype", "getBits", "getPoints"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/detector/PDF417DetectorResult.js"], "sourcesContent": ["/*\n* Copyright 2007 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// import java.util.List;\n/**\n * <AUTHOR>\n */\nvar PDF417DetectorResult = /** @class */ (function () {\n    function PDF417DetectorResult(bits, points) {\n        this.bits = bits;\n        this.points = points;\n    }\n    PDF417DetectorResult.prototype.getBits = function () {\n        return this.bits;\n    };\n    PDF417DetectorResult.prototype.getPoints = function () {\n        return this.points;\n    };\n    return PDF417DetectorResult;\n}());\nexport default PDF417DetectorResult;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,oBAAoB,GAAG,aAAe,YAAY;EAClD,SAASA,oBAAoBA,CAACC,IAAI,EAAEC,MAAM,EAAE;IACxC,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAF,oBAAoB,CAACG,SAAS,CAACC,OAAO,GAAG,YAAY;IACjD,OAAO,IAAI,CAACH,IAAI;EACpB,CAAC;EACDD,oBAAoB,CAACG,SAAS,CAACE,SAAS,GAAG,YAAY;IACnD,OAAO,IAAI,CAACH,MAAM;EACtB,CAAC;EACD,OAAOF,oBAAoB;AAC/B,CAAC,CAAC,CAAE;AACJ,eAAeA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}