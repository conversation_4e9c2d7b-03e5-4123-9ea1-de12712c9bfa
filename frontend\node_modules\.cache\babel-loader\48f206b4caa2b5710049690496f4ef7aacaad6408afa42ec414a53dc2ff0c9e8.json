{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing {*/\nimport './InvertedLuminanceSource'; // required because of circular dependencies between LuminanceSource and InvertedLuminanceSource\nimport InvertedLuminanceSource from './InvertedLuminanceSource';\nimport LuminanceSource from './LuminanceSource';\nimport System from './util/System';\nimport IllegalArgumentException from './IllegalArgumentException';\n/**\n * This class is used to help decode images from files which arrive as RGB data from\n * an ARGB pixel array. It does not support rotation.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR>\n */\nvar RGBLuminanceSource = /** @class */function (_super) {\n  __extends(RGBLuminanceSource, _super);\n  function RGBLuminanceSource(luminances, width /*int*/, height /*int*/, dataWidth /*int*/, dataHeight /*int*/, left /*int*/, top /*int*/) {\n    var _this = _super.call(this, width, height) || this;\n    _this.dataWidth = dataWidth;\n    _this.dataHeight = dataHeight;\n    _this.left = left;\n    _this.top = top;\n    if (luminances.BYTES_PER_ELEMENT === 4) {\n      // Int32Array\n      var size = width * height;\n      var luminancesUint8Array = new Uint8ClampedArray(size);\n      for (var offset = 0; offset < size; offset++) {\n        var pixel = luminances[offset];\n        var r = pixel >> 16 & 0xff; // red\n        var g2 = pixel >> 7 & 0x1fe; // 2 * green\n        var b = pixel & 0xff; // blue\n        // Calculate green-favouring average cheaply\n        luminancesUint8Array[offset] = /*(byte) */(r + g2 + b) / 4 & 0xFF;\n      }\n      _this.luminances = luminancesUint8Array;\n    } else {\n      _this.luminances = luminances;\n    }\n    if (undefined === dataWidth) {\n      _this.dataWidth = width;\n    }\n    if (undefined === dataHeight) {\n      _this.dataHeight = height;\n    }\n    if (undefined === left) {\n      _this.left = 0;\n    }\n    if (undefined === top) {\n      _this.top = 0;\n    }\n    if (_this.left + width > _this.dataWidth || _this.top + height > _this.dataHeight) {\n      throw new IllegalArgumentException('Crop rectangle does not fit within image data.');\n    }\n    return _this;\n  }\n  /*@Override*/\n  RGBLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n    if (y < 0 || y >= this.getHeight()) {\n      throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n    }\n    var width = this.getWidth();\n    if (row === null || row === undefined || row.length < width) {\n      row = new Uint8ClampedArray(width);\n    }\n    var offset = (y + this.top) * this.dataWidth + this.left;\n    System.arraycopy(this.luminances, offset, row, 0, width);\n    return row;\n  };\n  /*@Override*/\n  RGBLuminanceSource.prototype.getMatrix = function () {\n    var width = this.getWidth();\n    var height = this.getHeight();\n    // If the caller asks for the entire underlying image, save the copy and give them the\n    // original data. The docs specifically warn that result.length must be ignored.\n    if (width === this.dataWidth && height === this.dataHeight) {\n      return this.luminances;\n    }\n    var area = width * height;\n    var matrix = new Uint8ClampedArray(area);\n    var inputOffset = this.top * this.dataWidth + this.left;\n    // If the width matches the full width of the underlying data, perform a single copy.\n    if (width === this.dataWidth) {\n      System.arraycopy(this.luminances, inputOffset, matrix, 0, area);\n      return matrix;\n    }\n    // Otherwise copy one cropped row at a time.\n    for (var y = 0; y < height; y++) {\n      var outputOffset = y * width;\n      System.arraycopy(this.luminances, inputOffset, matrix, outputOffset, width);\n      inputOffset += this.dataWidth;\n    }\n    return matrix;\n  };\n  /*@Override*/\n  RGBLuminanceSource.prototype.isCropSupported = function () {\n    return true;\n  };\n  /*@Override*/\n  RGBLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n    return new RGBLuminanceSource(this.luminances, width, height, this.dataWidth, this.dataHeight, this.left + left, this.top + top);\n  };\n  RGBLuminanceSource.prototype.invert = function () {\n    return new InvertedLuminanceSource(this);\n  };\n  return RGBLuminanceSource;\n}(LuminanceSource);\nexport default RGBLuminanceSource;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "InvertedLuminanceSource", "LuminanceSource", "System", "IllegalArgumentException", "RGBLuminanceSource", "_super", "luminances", "width", "height", "dataWidth", "dataHeight", "left", "top", "_this", "call", "BYTES_PER_ELEMENT", "size", "luminancesUint8Array", "Uint8ClampedArray", "offset", "pixel", "r", "g2", "undefined", "getRow", "y", "row", "getHeight", "getWidth", "length", "arraycopy", "getMatrix", "area", "matrix", "inputOffset", "outputOffset", "isCropSupported", "crop", "invert"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/RGBLuminanceSource.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing {*/\nimport './InvertedLuminanceSource'; // required because of circular dependencies between LuminanceSource and InvertedLuminanceSource\nimport InvertedLuminanceSource from './InvertedLuminanceSource';\nimport LuminanceSource from './LuminanceSource';\nimport System from './util/System';\nimport IllegalArgumentException from './IllegalArgumentException';\n/**\n * This class is used to help decode images from files which arrive as RGB data from\n * an ARGB pixel array. It does not support rotation.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR>\n */\nvar RGBLuminanceSource = /** @class */ (function (_super) {\n    __extends(RGBLuminanceSource, _super);\n    function RGBLuminanceSource(luminances, width /*int*/, height /*int*/, dataWidth /*int*/, dataHeight /*int*/, left /*int*/, top /*int*/) {\n        var _this = _super.call(this, width, height) || this;\n        _this.dataWidth = dataWidth;\n        _this.dataHeight = dataHeight;\n        _this.left = left;\n        _this.top = top;\n        if (luminances.BYTES_PER_ELEMENT === 4) { // Int32Array\n            var size = width * height;\n            var luminancesUint8Array = new Uint8ClampedArray(size);\n            for (var offset = 0; offset < size; offset++) {\n                var pixel = luminances[offset];\n                var r = (pixel >> 16) & 0xff; // red\n                var g2 = (pixel >> 7) & 0x1fe; // 2 * green\n                var b = pixel & 0xff; // blue\n                // Calculate green-favouring average cheaply\n                luminancesUint8Array[offset] = /*(byte) */ ((r + g2 + b) / 4) & 0xFF;\n            }\n            _this.luminances = luminancesUint8Array;\n        }\n        else {\n            _this.luminances = luminances;\n        }\n        if (undefined === dataWidth) {\n            _this.dataWidth = width;\n        }\n        if (undefined === dataHeight) {\n            _this.dataHeight = height;\n        }\n        if (undefined === left) {\n            _this.left = 0;\n        }\n        if (undefined === top) {\n            _this.top = 0;\n        }\n        if (_this.left + width > _this.dataWidth || _this.top + height > _this.dataHeight) {\n            throw new IllegalArgumentException('Crop rectangle does not fit within image data.');\n        }\n        return _this;\n    }\n    /*@Override*/\n    RGBLuminanceSource.prototype.getRow = function (y /*int*/, row) {\n        if (y < 0 || y >= this.getHeight()) {\n            throw new IllegalArgumentException('Requested row is outside the image: ' + y);\n        }\n        var width = this.getWidth();\n        if (row === null || row === undefined || row.length < width) {\n            row = new Uint8ClampedArray(width);\n        }\n        var offset = (y + this.top) * this.dataWidth + this.left;\n        System.arraycopy(this.luminances, offset, row, 0, width);\n        return row;\n    };\n    /*@Override*/\n    RGBLuminanceSource.prototype.getMatrix = function () {\n        var width = this.getWidth();\n        var height = this.getHeight();\n        // If the caller asks for the entire underlying image, save the copy and give them the\n        // original data. The docs specifically warn that result.length must be ignored.\n        if (width === this.dataWidth && height === this.dataHeight) {\n            return this.luminances;\n        }\n        var area = width * height;\n        var matrix = new Uint8ClampedArray(area);\n        var inputOffset = this.top * this.dataWidth + this.left;\n        // If the width matches the full width of the underlying data, perform a single copy.\n        if (width === this.dataWidth) {\n            System.arraycopy(this.luminances, inputOffset, matrix, 0, area);\n            return matrix;\n        }\n        // Otherwise copy one cropped row at a time.\n        for (var y = 0; y < height; y++) {\n            var outputOffset = y * width;\n            System.arraycopy(this.luminances, inputOffset, matrix, outputOffset, width);\n            inputOffset += this.dataWidth;\n        }\n        return matrix;\n    };\n    /*@Override*/\n    RGBLuminanceSource.prototype.isCropSupported = function () {\n        return true;\n    };\n    /*@Override*/\n    RGBLuminanceSource.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        return new RGBLuminanceSource(this.luminances, width, height, this.dataWidth, this.dataHeight, this.left + left, this.top + top);\n    };\n    RGBLuminanceSource.prototype.invert = function () {\n        return new InvertedLuminanceSource(this);\n    };\n    return RGBLuminanceSource;\n}(LuminanceSource));\nexport default RGBLuminanceSource;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAO,2BAA2B,CAAC,CAAC;AACpC,OAAOI,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,MAAM,MAAM,eAAe;AAClC,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,kBAAkB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACtDnB,SAAS,CAACkB,kBAAkB,EAAEC,MAAM,CAAC;EACrC,SAASD,kBAAkBA,CAACE,UAAU,EAAEC,KAAK,CAAC,SAASC,MAAM,CAAC,SAASC,SAAS,CAAC,SAASC,UAAU,CAAC,SAASC,IAAI,CAAC,SAASC,GAAG,CAAC,SAAS;IACrI,IAAIC,KAAK,GAAGR,MAAM,CAACS,IAAI,CAAC,IAAI,EAAEP,KAAK,EAAEC,MAAM,CAAC,IAAI,IAAI;IACpDK,KAAK,CAACJ,SAAS,GAAGA,SAAS;IAC3BI,KAAK,CAACH,UAAU,GAAGA,UAAU;IAC7BG,KAAK,CAACF,IAAI,GAAGA,IAAI;IACjBE,KAAK,CAACD,GAAG,GAAGA,GAAG;IACf,IAAIN,UAAU,CAACS,iBAAiB,KAAK,CAAC,EAAE;MAAE;MACtC,IAAIC,IAAI,GAAGT,KAAK,GAAGC,MAAM;MACzB,IAAIS,oBAAoB,GAAG,IAAIC,iBAAiB,CAACF,IAAI,CAAC;MACtD,KAAK,IAAIG,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGH,IAAI,EAAEG,MAAM,EAAE,EAAE;QAC1C,IAAIC,KAAK,GAAGd,UAAU,CAACa,MAAM,CAAC;QAC9B,IAAIE,CAAC,GAAID,KAAK,IAAI,EAAE,GAAI,IAAI,CAAC,CAAC;QAC9B,IAAIE,EAAE,GAAIF,KAAK,IAAI,CAAC,GAAI,KAAK,CAAC,CAAC;QAC/B,IAAI/B,CAAC,GAAG+B,KAAK,GAAG,IAAI,CAAC,CAAC;QACtB;QACAH,oBAAoB,CAACE,MAAM,CAAC,GAAG,WAAa,CAACE,CAAC,GAAGC,EAAE,GAAGjC,CAAC,IAAI,CAAC,GAAI,IAAI;MACxE;MACAwB,KAAK,CAACP,UAAU,GAAGW,oBAAoB;IAC3C,CAAC,MACI;MACDJ,KAAK,CAACP,UAAU,GAAGA,UAAU;IACjC;IACA,IAAIiB,SAAS,KAAKd,SAAS,EAAE;MACzBI,KAAK,CAACJ,SAAS,GAAGF,KAAK;IAC3B;IACA,IAAIgB,SAAS,KAAKb,UAAU,EAAE;MAC1BG,KAAK,CAACH,UAAU,GAAGF,MAAM;IAC7B;IACA,IAAIe,SAAS,KAAKZ,IAAI,EAAE;MACpBE,KAAK,CAACF,IAAI,GAAG,CAAC;IAClB;IACA,IAAIY,SAAS,KAAKX,GAAG,EAAE;MACnBC,KAAK,CAACD,GAAG,GAAG,CAAC;IACjB;IACA,IAAIC,KAAK,CAACF,IAAI,GAAGJ,KAAK,GAAGM,KAAK,CAACJ,SAAS,IAAII,KAAK,CAACD,GAAG,GAAGJ,MAAM,GAAGK,KAAK,CAACH,UAAU,EAAE;MAC/E,MAAM,IAAIP,wBAAwB,CAAC,gDAAgD,CAAC;IACxF;IACA,OAAOU,KAAK;EAChB;EACA;EACAT,kBAAkB,CAACN,SAAS,CAAC0B,MAAM,GAAG,UAAUC,CAAC,CAAC,SAASC,GAAG,EAAE;IAC5D,IAAID,CAAC,GAAG,CAAC,IAAIA,CAAC,IAAI,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE;MAChC,MAAM,IAAIxB,wBAAwB,CAAC,sCAAsC,GAAGsB,CAAC,CAAC;IAClF;IACA,IAAIlB,KAAK,GAAG,IAAI,CAACqB,QAAQ,CAAC,CAAC;IAC3B,IAAIF,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKH,SAAS,IAAIG,GAAG,CAACG,MAAM,GAAGtB,KAAK,EAAE;MACzDmB,GAAG,GAAG,IAAIR,iBAAiB,CAACX,KAAK,CAAC;IACtC;IACA,IAAIY,MAAM,GAAG,CAACM,CAAC,GAAG,IAAI,CAACb,GAAG,IAAI,IAAI,CAACH,SAAS,GAAG,IAAI,CAACE,IAAI;IACxDT,MAAM,CAAC4B,SAAS,CAAC,IAAI,CAACxB,UAAU,EAAEa,MAAM,EAAEO,GAAG,EAAE,CAAC,EAAEnB,KAAK,CAAC;IACxD,OAAOmB,GAAG;EACd,CAAC;EACD;EACAtB,kBAAkB,CAACN,SAAS,CAACiC,SAAS,GAAG,YAAY;IACjD,IAAIxB,KAAK,GAAG,IAAI,CAACqB,QAAQ,CAAC,CAAC;IAC3B,IAAIpB,MAAM,GAAG,IAAI,CAACmB,SAAS,CAAC,CAAC;IAC7B;IACA;IACA,IAAIpB,KAAK,KAAK,IAAI,CAACE,SAAS,IAAID,MAAM,KAAK,IAAI,CAACE,UAAU,EAAE;MACxD,OAAO,IAAI,CAACJ,UAAU;IAC1B;IACA,IAAI0B,IAAI,GAAGzB,KAAK,GAAGC,MAAM;IACzB,IAAIyB,MAAM,GAAG,IAAIf,iBAAiB,CAACc,IAAI,CAAC;IACxC,IAAIE,WAAW,GAAG,IAAI,CAACtB,GAAG,GAAG,IAAI,CAACH,SAAS,GAAG,IAAI,CAACE,IAAI;IACvD;IACA,IAAIJ,KAAK,KAAK,IAAI,CAACE,SAAS,EAAE;MAC1BP,MAAM,CAAC4B,SAAS,CAAC,IAAI,CAACxB,UAAU,EAAE4B,WAAW,EAAED,MAAM,EAAE,CAAC,EAAED,IAAI,CAAC;MAC/D,OAAOC,MAAM;IACjB;IACA;IACA,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,MAAM,EAAEiB,CAAC,EAAE,EAAE;MAC7B,IAAIU,YAAY,GAAGV,CAAC,GAAGlB,KAAK;MAC5BL,MAAM,CAAC4B,SAAS,CAAC,IAAI,CAACxB,UAAU,EAAE4B,WAAW,EAAED,MAAM,EAAEE,YAAY,EAAE5B,KAAK,CAAC;MAC3E2B,WAAW,IAAI,IAAI,CAACzB,SAAS;IACjC;IACA,OAAOwB,MAAM;EACjB,CAAC;EACD;EACA7B,kBAAkB,CAACN,SAAS,CAACsC,eAAe,GAAG,YAAY;IACvD,OAAO,IAAI;EACf,CAAC;EACD;EACAhC,kBAAkB,CAACN,SAAS,CAACuC,IAAI,GAAG,UAAU1B,IAAI,CAAC,SAASC,GAAG,CAAC,SAASL,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IACpG,OAAO,IAAIJ,kBAAkB,CAAC,IAAI,CAACE,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAE,IAAI,CAACC,SAAS,EAAE,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,IAAI,GAAGA,IAAI,EAAE,IAAI,CAACC,GAAG,GAAGA,GAAG,CAAC;EACpI,CAAC;EACDR,kBAAkB,CAACN,SAAS,CAACwC,MAAM,GAAG,YAAY;IAC9C,OAAO,IAAItC,uBAAuB,CAAC,IAAI,CAAC;EAC5C,CAAC;EACD,OAAOI,kBAAkB;AAC7B,CAAC,CAACH,eAAe,CAAE;AACnB,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}