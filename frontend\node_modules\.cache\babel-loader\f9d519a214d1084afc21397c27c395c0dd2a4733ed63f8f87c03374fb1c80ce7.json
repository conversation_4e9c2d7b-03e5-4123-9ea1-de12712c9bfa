{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Learning Projects\\\\uni-core-business-suite_v3\\\\frontend\\\\src\\\\components\\\\BankPaymentVoucherForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Card, CardContent, Typography, Button, Grid, TextField, FormControl, InputLabel, Select, MenuItem, Alert, Divider, Chip, Autocomplete, FormHelperText } from \"@mui/material\";\nimport { Save as SaveIcon, Check as ApproveIcon, PostAdd as PostIcon, Cancel as CancelIcon } from \"@mui/icons-material\";\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\nimport dayjs from \"dayjs\";\nimport axios from \"../utils/axiosConfig\";\nimport { formatCurrency, formatAccountingNumber, parseAccountingNumber, fixDecimalPlaces } from \"../utils/numberUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BankPaymentVoucherForm = ({\n  voucherId,\n  readOnly = false,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [voucher, setVoucher] = useState({\n    voucherType: 'BP',\n    // Bank Payment\n    voucherDate: dayjs(),\n    // Voucher creation date\n    transactionDate: dayjs(),\n    // Actual transaction date\n    amount: '',\n    fromAccountId: '',\n    // Bank account (credit)\n    toAccountId: '',\n    // Debit account (vendor, expense, etc.)\n    relatedPartyType: '',\n    relatedPartyId: '',\n    relatedPartyName: '',\n    paymentMethod: 'Bank Transfer',\n    chequeNumber: '',\n    chequeDate: null,\n    bankReference: '',\n    narration: '',\n    description: '',\n    status: 'Draft',\n    referenceDocuments: []\n  });\n\n  // State for formatted amount display\n  const [displayAmount, setDisplayAmount] = useState('');\n\n  // Flag to prevent amount override when loading existing voucher\n  const [isLoadingExistingVoucher, setIsLoadingExistingVoucher] = useState(false);\n  const [accounts, setAccounts] = useState([]);\n  const [bankAccounts, setBankAccounts] = useState([]);\n  const [vendors, setVendors] = useState([]);\n  const [purchaseInvoices, setPurchaseInvoices] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [selectedVendor, setSelectedVendor] = useState(null);\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\n  const paymentMethods = ['Bank Transfer', 'Cheque', 'Online Transfer', 'Card', 'Other'];\n  const statusOptions = ['Draft', 'Approved', 'Posted', 'Cancelled'];\n  useEffect(() => {\n    fetchInitialData();\n    if (voucherId) {\n      fetchVoucher();\n    }\n  }, [voucherId]);\n\n  // Fetch vendor's purchase invoices when vendor is selected\n  useEffect(() => {\n    if (selectedVendor) {\n      fetchVendorInvoices(selectedVendor._id);\n    } else {\n      setPurchaseInvoices([]);\n      setSelectedInvoice(null);\n    }\n  }, [selectedVendor]);\n\n  // Update amount when invoice is selected (but not when loading existing voucher)\n  useEffect(() => {\n    if (selectedInvoice && !isLoadingExistingVoucher) {\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\n\n      // Create a detailed narration\n      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber || 'Unknown'}`;\n\n      // If there are returns, add details\n      if (selectedInvoice.hasReturns && selectedInvoice.returnDetails) {\n        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\n        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount || 0)}, Returns: ${formatCurrency(selectedInvoice.returnAmount || 0)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount || 0)})`;\n      }\n      const fixedAmount = fixDecimalPlaces(remainingAmount);\n      setVoucher(prev => ({\n        ...prev,\n        amount: fixedAmount,\n        narration\n      }));\n      setDisplayAmount(formatAccountingNumber(fixedAmount));\n    }\n  }, [selectedInvoice, isLoadingExistingVoucher]);\n\n  // Add error handling for ResizeObserver\n  useEffect(() => {\n    // Suppress ResizeObserver loop error\n    const originalError = window.console.error;\n    window.console.error = (...args) => {\n      var _args$, _args$$includes;\n      if ((_args$ = args[0]) !== null && _args$ !== void 0 && (_args$$includes = _args$.includes) !== null && _args$$includes !== void 0 && _args$$includes.call(_args$, 'ResizeObserver loop')) {\n        // Ignore ResizeObserver loop errors\n        return;\n      }\n      originalError(...args);\n    };\n    return () => {\n      window.console.error = originalError;\n    };\n  }, []);\n  const fetchInitialData = async () => {\n    try {\n      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([axios.get(\"/api/accounts?active=true\"), axios.get(\"/api/vendors\"), axios.get(\"/api/bank-accounts\")]);\n      setAccounts(accountsRes.data);\n      setVendors(vendorsRes.data);\n\n      // Map bank accounts to their corresponding chart of accounts entries\n      const bankAccs = bankAccountsRes.data;\n      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);\n\n      // Get bank account details from chart of accounts\n      if (bankAccountIds.length > 0) {\n        const bankAccountsDetails = accountsRes.data.filter(acc => bankAccountIds.includes(acc._id));\n        setBankAccounts(bankAccountsDetails);\n      }\n    } catch (error) {\n      console.error(\"Error fetching initial data:\", error);\n    }\n  };\n  const fetchVoucher = async () => {\n    try {\n      var _voucherData$fromAcco, _voucherData$toAccoun;\n      setLoading(true);\n      setIsLoadingExistingVoucher(true); // Prevent amount override\n\n      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);\n      const voucherData = response.data;\n      const fixedAmount = fixDecimalPlaces(voucherData.amount);\n      setVoucher({\n        ...voucherData,\n        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt || new Date()),\n        transactionDate: dayjs(voucherData.transactionDate || new Date()),\n        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,\n        fromAccountId: ((_voucherData$fromAcco = voucherData.fromAccountId) === null || _voucherData$fromAcco === void 0 ? void 0 : _voucherData$fromAcco._id) || voucherData.fromAccountId,\n        toAccountId: ((_voucherData$toAccoun = voucherData.toAccountId) === null || _voucherData$toAccoun === void 0 ? void 0 : _voucherData$toAccoun._id) || voucherData.toAccountId,\n        amount: fixedAmount\n      });\n      setDisplayAmount(formatAccountingNumber(fixedAmount));\n\n      // If this is a vendor payment, fetch the vendor and invoice details\n      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {\n        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);\n        setSelectedVendor(vendorRes.data);\n\n        // If there's a reference to a purchase invoice\n        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {\n          const invoiceRef = voucherData.referenceDocuments.find(doc => doc.documentType === 'PurchaseInvoice');\n          if (invoiceRef && invoiceRef.documentId) {\n            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);\n            setSelectedInvoice(invoiceRes.data);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching voucher:\", error);\n    } finally {\n      setLoading(false);\n      setIsLoadingExistingVoucher(false); // Reset flag after loading is complete\n    }\n  };\n  const fetchVendorInvoices = async vendorId => {\n    try {\n      console.log(`Fetching invoices for vendor: ${vendorId}`);\n      // Fetch unpaid or partially paid invoices for this vendor\n      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);\n      console.log('Vendor invoices response:', response.data);\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        setPurchaseInvoices(response.data);\n      } else {\n        console.log('No unpaid invoices found for this vendor');\n        setPurchaseInvoices([]);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error fetching vendor invoices:\", error);\n      alert(`Failed to fetch vendor invoices: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message}`);\n      setPurchaseInvoices([]);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    if (field === 'amount') {\n      // Handle amount field with accounting format\n      const parsedAmount = parseAccountingNumber(value);\n      setVoucher(prev => ({\n        ...prev,\n        [field]: parsedAmount\n      }));\n      setDisplayAmount(value); // Keep the user's input for display\n    } else {\n      setVoucher(prev => ({\n        ...prev,\n        [field]: value\n      }));\n    }\n\n    // Clear error when field is updated\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n\n  // Handle amount field blur to format the display\n  const handleAmountBlur = () => {\n    if (voucher.amount) {\n      const formattedAmount = formatAccountingNumber(voucher.amount);\n      setDisplayAmount(formattedAmount);\n    }\n  };\n\n  // Handle amount field focus to show raw number\n  const handleAmountFocus = () => {\n    setDisplayAmount(voucher.amount);\n  };\n  const handleVendorChange = vendor => {\n    setSelectedVendor(vendor);\n    setSelectedInvoice(null); // Clear selected invoice when vendor changes\n\n    if (vendor) {\n      // Update voucher with vendor details\n      setVoucher(prev => ({\n        ...prev,\n        relatedPartyType: 'Vendor',\n        relatedPartyId: vendor._id,\n        relatedPartyName: vendor.name,\n        toAccountId: vendor.accountId || '',\n        // Set vendor's account as debit account\n        amount: '',\n        // Clear amount when vendor changes\n        narration: '',\n        // Clear narration when vendor changes\n        referenceDocuments: [] // Clear reference documents\n      }));\n    } else {\n      // Clear vendor-related fields\n      setVoucher(prev => ({\n        ...prev,\n        relatedPartyType: '',\n        relatedPartyId: '',\n        relatedPartyName: '',\n        toAccountId: '',\n        amount: '',\n        narration: '',\n        referenceDocuments: []\n      }));\n    }\n  };\n  const handleInvoiceChange = invoice => {\n    setSelectedInvoice(invoice);\n    if (invoice) {\n      console.log('Selected invoice:', invoice);\n\n      // Use the adjusted remaining amount that accounts for returns\n      const remainingAmount = invoice.remainingAmount;\n      console.log(`Using adjusted remaining amount: ${remainingAmount}`);\n\n      // Create a detailed narration\n      let narration = `Payment against invoice ${invoice.invoiceNumber}`;\n\n      // If there are returns, add details\n      if (invoice.hasReturns) {\n        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\n        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;\n      }\n\n      // Update amount field with remaining amount (fixed to 2 decimals)\n      const fixedAmount = fixDecimalPlaces(remainingAmount);\n      setVoucher(prev => ({\n        ...prev,\n        amount: fixedAmount,\n        narration\n      }));\n      setDisplayAmount(formatAccountingNumber(fixedAmount));\n\n      // Update reference documents\n      const referenceDoc = {\n        documentType: 'PurchaseInvoice',\n        documentId: invoice._id,\n        documentNumber: invoice.invoiceNumber,\n        allocatedAmount: remainingAmount,\n        originalAmount: invoice.originalAmount,\n        returnAmount: invoice.returnAmount,\n        adjustedAmount: invoice.adjustedAmount\n      };\n      setVoucher(prev => ({\n        ...prev,\n        referenceDocuments: [referenceDoc]\n      }));\n    } else {\n      setVoucher(prev => ({\n        ...prev,\n        referenceDocuments: []\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!voucher.fromAccountId) newErrors.fromAccountId = \"Bank is required\";\n    if (!voucher.toAccountId) newErrors.toAccountId = \"Title (debit account) is required\";\n    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {\n      newErrors.amount = \"Valid amount is required\";\n    }\n    if (!voucher.voucherDate) newErrors.voucherDate = \"Voucher date is required\";\n    if (!voucher.transactionDate) newErrors.transactionDate = \"Transaction date is required\";\n    if (!voucher.narration) newErrors.narration = \"Narration is required\";\n\n    // Vendor-invoice linkage validation\n    if (selectedVendor && selectedInvoice) {\n      // Ensure the selected invoice belongs to the selected vendor\n      if (selectedInvoice.vendorId !== selectedVendor._id) {\n        newErrors.invoice = \"Selected invoice does not belong to the selected vendor\";\n      }\n\n      // Ensure payment amount doesn't exceed remaining amount\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\n      const paymentAmount = parseFloat(voucher.amount) || 0;\n      if (paymentAmount > remainingAmount) {\n        newErrors.amount = `Payment amount cannot exceed remaining amount of ${remainingAmount.toFixed(2)}`;\n      }\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSave = async () => {\n    if (!validateForm()) return;\n    try {\n      setLoading(true);\n      const voucherData = {\n        ...voucher,\n        voucherDate: voucher.voucherDate.toISOString(),\n        transactionDate: voucher.transactionDate.toISOString(),\n        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,\n        amount: parseFloat(voucher.amount)\n      };\n      let response;\n      if (voucherId) {\n        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);\n      } else {\n        response = await axios.post(\"/api/payment-vouchers\", voucherData);\n      }\n      if (onSave) onSave(response.data.voucher);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error(\"Error saving voucher:\", error);\n      alert(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Error saving bank payment voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApprove = async () => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\n      if (onSave) onSave();\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error(\"Error approving voucher:\", error);\n      alert(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || \"Error approving voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePost = async () => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\n      if (onSave) onSave();\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error(\"Error posting voucher:\", error);\n      alert(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || \"Error posting voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');\n  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';\n  const canPost = !readOnly && voucherId && voucher.status === 'Approved';\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n      dateAdapter: AdapterDayjs,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Bank Payment Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this), voucherId && /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Voucher No.\",\n                value: voucher.voucherNumber || '',\n                margin: \"normal\",\n                InputProps: {\n                  readOnly: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                label: \"Voucher Date\",\n                value: voucher.voucherDate,\n                onChange: newValue => handleInputChange('voucherDate', newValue),\n                format: \"DD/MMM/YYYY\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  fullWidth: true,\n                  margin: \"normal\",\n                  error: !!errors.voucherDate,\n                  helperText: errors.voucherDate,\n                  InputProps: {\n                    ...params.InputProps,\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this),\n                readOnly: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 461,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                label: \"Transaction Date\",\n                value: voucher.transactionDate,\n                onChange: newValue => handleInputChange('transactionDate', newValue),\n                format: \"DD/MMM/YYYY\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  fullWidth: true,\n                  margin: \"normal\",\n                  error: !!errors.transactionDate,\n                  helperText: errors.transactionDate,\n                  InputProps: {\n                    ...params.InputProps,\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this),\n                readOnly: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Transaction ID\",\n                value: voucher.bankReference || '',\n                onChange: e => handleInputChange('bankReference', e.target.value),\n                margin: \"normal\",\n                InputProps: {\n                  readOnly: !canEdit\n                },\n                helperText: \"Optional field for internal reference or payment ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.status,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.status || 'Draft',\n                  onChange: e => handleInputChange('status', e.target.value),\n                  label: \"Status\",\n                  disabled: !canEdit,\n                  children: statusOptions.map(status => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: status,\n                    children: status\n                  }, status, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 525,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 19\n                }, this), errors.status && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.status\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 531,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 516,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.fromAccountId,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Bank\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.fromAccountId,\n                  onChange: e => handleInputChange('fromAccountId', e.target.value),\n                  label: \"Bank\",\n                  disabled: !canEdit,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Select Bank\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 549,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 548,\n                    columnNumber: 21\n                  }, this), bankAccounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: account._id,\n                    children: account.accountName\n                  }, account._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 542,\n                  columnNumber: 19\n                }, this), errors.fromAccountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.fromAccountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Payment Method\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.paymentMethod,\n                  onChange: e => handleInputChange('paymentMethod', e.target.value),\n                  label: \"Payment Method\",\n                  disabled: !canEdit,\n                  children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: method,\n                    children: method\n                  }, method, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 565,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 563,\n                columnNumber: 17\n              }, this), voucher.paymentMethod === 'Cheque' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Cheque Number\",\n                  value: voucher.chequeNumber || '',\n                  onChange: e => handleInputChange('chequeNumber', e.target.value),\n                  margin: \"normal\",\n                  InputProps: {\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                  label: \"Cheque Date\",\n                  value: voucher.chequeDate,\n                  onChange: newValue => handleInputChange('chequeDate', newValue),\n                  renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                    ...params,\n                    fullWidth: true,\n                    margin: \"normal\",\n                    InputProps: {\n                      ...params.InputProps,\n                      readOnly: !canEdit\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 25\n                  }, this),\n                  readOnly: !canEdit\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Payment Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: vendors,\n                getOptionLabel: vendor => vendor.name || '',\n                value: selectedVendor,\n                onChange: (event, newValue) => handleVendorChange(newValue),\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Vendor\",\n                  margin: \"normal\",\n                  fullWidth: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 629,\n                  columnNumber: 21\n                }, this),\n                disabled: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 17\n              }, this), selectedVendor && /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: purchaseInvoices,\n                getOptionLabel: invoice => {\n                  if (!invoice) return '';\n                  const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;\n                  const invoiceDate = new Date(invoice.invoiceDate).toLocaleDateString('en-GB', {\n                    day: '2-digit',\n                    month: 'short',\n                    year: 'numeric'\n                  });\n\n                  // If there are returns, show the adjusted amount\n                  if (invoice.hasReturns) {\n                    return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} (Returns Applied)`;\n                  }\n\n                  // Otherwise just show the remaining amount\n                  return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} outstanding`;\n                },\n                value: selectedInvoice,\n                onChange: (event, newValue) => handleInvoiceChange(newValue),\n                renderOption: (props, invoice) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%',\n                      maxWidth: '100%',\n                      overflow: 'hidden'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"medium\",\n                      noWrap: true,\n                      children: [invoice.invoiceNumber, \" - \", formatCurrency(invoice.remainingAmount), \" outstanding\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 666,\n                      columnNumber: 27\n                    }, this), invoice.hasReturns && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        display: \"block\",\n                        noWrap: true,\n                        children: [\"Original: \", formatCurrency(invoice.originalAmount), \" | Returns: \", formatCurrency(invoice.returnAmount), \" | Adjusted: \", formatCurrency(invoice.adjustedAmount)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 671,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"primary\",\n                        display: \"block\",\n                        noWrap: true,\n                        children: [invoice.returnDetails.length, \" return(s) applied -\", invoice.returnDetails.map((ret, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [ret.returnNumber, \" (\", ret.status, \")\", idx < invoice.returnDetails.length - 1 ? ', ' : '']\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 679,\n                          columnNumber: 35\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 676,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 665,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 664,\n                  columnNumber: 23\n                }, this),\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Purchase Invoice\",\n                  margin: \"normal\",\n                  fullWidth: true,\n                  error: purchaseInvoices.length === 0 || !!errors.invoice,\n                  helperText: errors.invoice || (purchaseInvoices.length === 0 ? \"No unpaid invoices found for this vendor\" : \"Optional: Select invoice for payment tracking. Partial payments are supported.\")\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 691,\n                  columnNumber: 23\n                }, this),\n                disabled: !canEdit || purchaseInvoices.length === 0,\n                noOptionsText: \"No unpaid invoices found\",\n                ListboxProps: {\n                  style: {\n                    maxHeight: '200px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 641,\n                columnNumber: 19\n              }, this), !selectedVendor && /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.toAccountId,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Title\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 719,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.toAccountId,\n                  onChange: e => handleInputChange('toAccountId', e.target.value),\n                  label: \"Title\",\n                  disabled: !canEdit,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Select Account\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 727,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 726,\n                    columnNumber: 23\n                  }, this), accounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: account._id,\n                    children: [account.accountCode, \" - \", account.accountName]\n                  }, account._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 730,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 720,\n                  columnNumber: 21\n                }, this), errors.toAccountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.toAccountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 736,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Amount\",\n                value: displayAmount,\n                onChange: e => handleInputChange('amount', e.target.value),\n                onBlur: handleAmountBlur,\n                onFocus: handleAmountFocus,\n                margin: \"normal\",\n                error: !!errors.amount,\n                helperText: errors.amount || \"Amount will be formatted with commas and 2 decimal places\",\n                InputProps: {\n                  readOnly: !canEdit,\n                  inputProps: {\n                    style: {\n                      textAlign: 'right'\n                    }\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Narration\",\n                value: voucher.narration,\n                onChange: e => handleInputChange('narration', e.target.value),\n                margin: \"normal\",\n                multiline: true,\n                rows: 2,\n                error: !!errors.narration,\n                helperText: errors.narration,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Description\",\n                value: voucher.description || '',\n                onChange: e => handleInputChange('description', e.target.value),\n                margin: \"normal\",\n                multiline: true,\n                rows: 2,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 615,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              gap: 2,\n              mt: 2\n            },\n            children: [onCancel && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 794,\n                columnNumber: 30\n              }, this),\n              onClick: onCancel,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 791,\n              columnNumber: 17\n            }, this), canEdit && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 805,\n                columnNumber: 30\n              }, this),\n              onClick: handleSave,\n              disabled: loading,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 17\n            }, this), canApprove && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"warning\",\n              startIcon: /*#__PURE__*/_jsxDEV(ApproveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 817,\n                columnNumber: 30\n              }, this),\n              onClick: handleApprove,\n              disabled: loading,\n              children: \"Approve\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 814,\n              columnNumber: 17\n            }, this), canPost && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              startIcon: /*#__PURE__*/_jsxDEV(PostIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 30\n              }, this),\n              onClick: handlePost,\n              disabled: loading,\n              children: \"Post to Ledger\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 789,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 788,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 438,\n    columnNumber: 5\n  }, this);\n};\n_s(BankPaymentVoucherForm, \"8TnDNW/1flJCyz0HEJe0B16Oemc=\");\n_c = BankPaymentVoucherForm;\nexport default BankPaymentVoucherForm;\nvar _c;\n$RefreshReg$(_c, \"BankPaymentVoucherForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Grid", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Divider", "Chip", "Autocomplete", "FormHelperText", "Save", "SaveIcon", "Check", "ApproveIcon", "PostAdd", "PostIcon", "Cancel", "CancelIcon", "DatePicker", "LocalizationProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayjs", "axios", "formatCurrency", "formatAccountingNumber", "parseAccountingNumber", "fixDecimalPlaces", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BankPaymentVoucherForm", "voucherId", "readOnly", "onSave", "onCancel", "_s", "voucher", "setVoucher", "voucherType", "voucherDate", "transactionDate", "amount", "fromAccountId", "toAccountId", "relatedPartyType", "relatedPartyId", "relatedPartyName", "paymentMethod", "chequeNumber", "chequeDate", "bankReference", "narration", "description", "status", "referenceDocuments", "displayAmount", "setDisplayAmount", "isLoadingExistingVoucher", "setIsLoadingExistingVoucher", "accounts", "setAccounts", "bankAccounts", "setBankAccounts", "vendors", "setVendors", "purchaseInvoices", "setPurchaseInvoices", "loading", "setLoading", "errors", "setErrors", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVendor", "selectedInvoice", "setSelectedInvoice", "paymentMethods", "statusOptions", "fetchInitialData", "fetchVoucher", "fetchVendorInvoices", "_id", "remainingAmount", "invoiceNumber", "hasReturns", "returnDetails", "returnStatuses", "map", "ret", "returnNumber", "join", "originalAmount", "returnAmount", "adjustedAmount", "fixedAmount", "prev", "originalError", "window", "console", "error", "args", "_args$", "_args$$includes", "includes", "call", "accountsRes", "vendorsRes", "bankAccountsRes", "Promise", "all", "get", "data", "bankAccs", "bankAccountIds", "bank", "accountId", "filter", "id", "length", "bankAccountsDetails", "acc", "_voucherData$fromAcco", "_voucherData$toAccoun", "response", "voucherData", "createdAt", "Date", "vendorRes", "invoiceRef", "find", "doc", "documentType", "documentId", "invoiceRes", "vendorId", "log", "Array", "isArray", "_error$response", "_error$response$data", "alert", "message", "handleInputChange", "field", "value", "parsedAmount", "handleAmountBlur", "formattedAmount", "handleAmountFocus", "handleVendorChange", "vendor", "name", "handleInvoiceChange", "invoice", "referenceDoc", "documentNumber", "allocatedAmount", "validateForm", "newErrors", "isNaN", "parseFloat", "paymentAmount", "toFixed", "Object", "keys", "handleSave", "toISOString", "put", "post", "_error$response2", "_error$response2$data", "handleApprove", "_error$response3", "_error$response3$data", "handlePost", "_error$response4", "_error$response4$data", "canEdit", "canApprove", "canPost", "children", "dateAdapter", "container", "spacing", "item", "xs", "md", "variant", "sx", "mb", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "label", "voucherNumber", "margin", "InputProps", "onChange", "newValue", "format", "renderInput", "params", "helperText", "e", "target", "disabled", "account", "accountName", "method", "options", "getOptionLabel", "event", "invoiceLabel", "invoiceDate", "toLocaleDateString", "day", "month", "year", "renderOption", "props", "width", "max<PERSON><PERSON><PERSON>", "overflow", "fontWeight", "noWrap", "color", "display", "idx", "noOptionsText", "ListboxProps", "style", "maxHeight", "accountCode", "onBlur", "onFocus", "inputProps", "textAlign", "multiline", "rows", "justifyContent", "gap", "mt", "startIcon", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/components/BankPaymentVoucherForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  Button,\r\n  Grid,\r\n  TextField,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Alert,\r\n  Divider,\r\n  Chip,\r\n  Autocomplete,\r\n  FormHelperText\r\n} from \"@mui/material\";\r\nimport {\r\n  Save as SaveIcon,\r\n  Check as ApproveIcon,\r\n  PostAdd as PostIcon,\r\n  Cancel as CancelIcon\r\n} from \"@mui/icons-material\";\r\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\r\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\r\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\r\nimport dayjs from \"dayjs\";\r\nimport axios from \"../utils/axiosConfig\";\r\nimport { formatCurrency, formatAccountingNumber, parseAccountingNumber, fixDecimalPlaces } from \"../utils/numberUtils\";\r\n\r\nconst BankPaymentVoucherForm = ({ voucherId, readOnly = false, onSave, onCancel }) => {\r\n  const [voucher, setVoucher] = useState({\r\n    voucherType: 'BP', // Bank Payment\r\n    voucherDate: dayjs(), // Voucher creation date\r\n    transactionDate: dayjs(), // Actual transaction date\r\n    amount: '',\r\n    fromAccountId: '', // Bank account (credit)\r\n    toAccountId: '', // Debit account (vendor, expense, etc.)\r\n    relatedPartyType: '',\r\n    relatedPartyId: '',\r\n    relatedPartyName: '',\r\n    paymentMethod: 'Bank Transfer',\r\n    chequeNumber: '',\r\n    chequeDate: null,\r\n    bankReference: '',\r\n    narration: '',\r\n    description: '',\r\n    status: 'Draft',\r\n    referenceDocuments: []\r\n  });\r\n\r\n  // State for formatted amount display\r\n  const [displayAmount, setDisplayAmount] = useState('');\r\n\r\n  // Flag to prevent amount override when loading existing voucher\r\n  const [isLoadingExistingVoucher, setIsLoadingExistingVoucher] = useState(false);\r\n\r\n  const [accounts, setAccounts] = useState([]);\r\n  const [bankAccounts, setBankAccounts] = useState([]);\r\n  const [vendors, setVendors] = useState([]);\r\n  const [purchaseInvoices, setPurchaseInvoices] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [errors, setErrors] = useState({});\r\n  const [selectedVendor, setSelectedVendor] = useState(null);\r\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\r\n\r\n  const paymentMethods = [\r\n    'Bank Transfer',\r\n    'Cheque',\r\n    'Online Transfer',\r\n    'Card',\r\n    'Other'\r\n  ];\r\n\r\n  const statusOptions = [\r\n    'Draft',\r\n    'Approved',\r\n    'Posted',\r\n    'Cancelled'\r\n  ];\r\n\r\n  useEffect(() => {\r\n    fetchInitialData();\r\n    if (voucherId) {\r\n      fetchVoucher();\r\n    }\r\n  }, [voucherId]);\r\n\r\n  // Fetch vendor's purchase invoices when vendor is selected\r\n  useEffect(() => {\r\n    if (selectedVendor) {\r\n      fetchVendorInvoices(selectedVendor._id);\r\n    } else {\r\n      setPurchaseInvoices([]);\r\n      setSelectedInvoice(null);\r\n    }\r\n  }, [selectedVendor]);\r\n\r\n  // Update amount when invoice is selected (but not when loading existing voucher)\r\n  useEffect(() => {\r\n    if (selectedInvoice && !isLoadingExistingVoucher) {\r\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\r\n\r\n      // Create a detailed narration\r\n      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber || 'Unknown'}`;\r\n\r\n      // If there are returns, add details\r\n      if (selectedInvoice.hasReturns && selectedInvoice.returnDetails) {\r\n        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\r\n        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount || 0)}, Returns: ${formatCurrency(selectedInvoice.returnAmount || 0)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount || 0)})`;\r\n      }\r\n\r\n      const fixedAmount = fixDecimalPlaces(remainingAmount);\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        amount: fixedAmount,\r\n        narration\r\n      }));\r\n      setDisplayAmount(formatAccountingNumber(fixedAmount));\r\n    }\r\n  }, [selectedInvoice, isLoadingExistingVoucher]);\r\n\r\n  // Add error handling for ResizeObserver\r\n  useEffect(() => {\r\n    // Suppress ResizeObserver loop error\r\n    const originalError = window.console.error;\r\n    window.console.error = (...args) => {\r\n      if (args[0]?.includes?.('ResizeObserver loop')) {\r\n        // Ignore ResizeObserver loop errors\r\n        return;\r\n      }\r\n      originalError(...args);\r\n    };\r\n\r\n    return () => {\r\n      window.console.error = originalError;\r\n    };\r\n  }, []);\r\n\r\n  const fetchInitialData = async () => {\r\n    try {\r\n      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([\r\n        axios.get(\"/api/accounts?active=true\"),\r\n        axios.get(\"/api/vendors\"),\r\n        axios.get(\"/api/bank-accounts\")\r\n      ]);\r\n\r\n      setAccounts(accountsRes.data);\r\n      setVendors(vendorsRes.data);\r\n      \r\n      // Map bank accounts to their corresponding chart of accounts entries\r\n      const bankAccs = bankAccountsRes.data;\r\n      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);\r\n      \r\n      // Get bank account details from chart of accounts\r\n      if (bankAccountIds.length > 0) {\r\n        const bankAccountsDetails = accountsRes.data.filter(acc => \r\n          bankAccountIds.includes(acc._id)\r\n        );\r\n        setBankAccounts(bankAccountsDetails);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching initial data:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchVoucher = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setIsLoadingExistingVoucher(true); // Prevent amount override\r\n\r\n      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);\r\n      const voucherData = response.data;\r\n\r\n      const fixedAmount = fixDecimalPlaces(voucherData.amount);\r\n      setVoucher({\r\n        ...voucherData,\r\n        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt || new Date()),\r\n        transactionDate: dayjs(voucherData.transactionDate || new Date()),\r\n        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,\r\n        fromAccountId: voucherData.fromAccountId?._id || voucherData.fromAccountId,\r\n        toAccountId: voucherData.toAccountId?._id || voucherData.toAccountId,\r\n        amount: fixedAmount\r\n      });\r\n      setDisplayAmount(formatAccountingNumber(fixedAmount));\r\n\r\n      // If this is a vendor payment, fetch the vendor and invoice details\r\n      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {\r\n        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);\r\n        setSelectedVendor(vendorRes.data);\r\n\r\n        // If there's a reference to a purchase invoice\r\n        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {\r\n          const invoiceRef = voucherData.referenceDocuments.find(doc =>\r\n            doc.documentType === 'PurchaseInvoice'\r\n          );\r\n\r\n          if (invoiceRef && invoiceRef.documentId) {\r\n            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);\r\n            setSelectedInvoice(invoiceRes.data);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching voucher:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n      setIsLoadingExistingVoucher(false); // Reset flag after loading is complete\r\n    }\r\n  };\r\n\r\n  const fetchVendorInvoices = async (vendorId) => {\r\n    try {\r\n      console.log(`Fetching invoices for vendor: ${vendorId}`);\r\n      // Fetch unpaid or partially paid invoices for this vendor\r\n      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);\r\n      console.log('Vendor invoices response:', response.data);\r\n      \r\n      if (Array.isArray(response.data) && response.data.length > 0) {\r\n        setPurchaseInvoices(response.data);\r\n      } else {\r\n        console.log('No unpaid invoices found for this vendor');\r\n        setPurchaseInvoices([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching vendor invoices:\", error);\r\n      alert(`Failed to fetch vendor invoices: ${error.response?.data?.message || error.message}`);\r\n      setPurchaseInvoices([]);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    if (field === 'amount') {\r\n      // Handle amount field with accounting format\r\n      const parsedAmount = parseAccountingNumber(value);\r\n      setVoucher(prev => ({ ...prev, [field]: parsedAmount }));\r\n      setDisplayAmount(value); // Keep the user's input for display\r\n    } else {\r\n      setVoucher(prev => ({ ...prev, [field]: value }));\r\n    }\r\n\r\n    // Clear error when field is updated\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: null }));\r\n    }\r\n  };\r\n\r\n  // Handle amount field blur to format the display\r\n  const handleAmountBlur = () => {\r\n    if (voucher.amount) {\r\n      const formattedAmount = formatAccountingNumber(voucher.amount);\r\n      setDisplayAmount(formattedAmount);\r\n    }\r\n  };\r\n\r\n  // Handle amount field focus to show raw number\r\n  const handleAmountFocus = () => {\r\n    setDisplayAmount(voucher.amount);\r\n  };\r\n\r\n  const handleVendorChange = (vendor) => {\r\n    setSelectedVendor(vendor);\r\n    setSelectedInvoice(null); // Clear selected invoice when vendor changes\r\n\r\n    if (vendor) {\r\n      // Update voucher with vendor details\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        relatedPartyType: 'Vendor',\r\n        relatedPartyId: vendor._id,\r\n        relatedPartyName: vendor.name,\r\n        toAccountId: vendor.accountId || '', // Set vendor's account as debit account\r\n        amount: '', // Clear amount when vendor changes\r\n        narration: '', // Clear narration when vendor changes\r\n        referenceDocuments: [] // Clear reference documents\r\n      }));\r\n    } else {\r\n      // Clear vendor-related fields\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        relatedPartyType: '',\r\n        relatedPartyId: '',\r\n        relatedPartyName: '',\r\n        toAccountId: '',\r\n        amount: '',\r\n        narration: '',\r\n        referenceDocuments: []\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleInvoiceChange = (invoice) => {\r\n    setSelectedInvoice(invoice);\r\n    \r\n    if (invoice) {\r\n      console.log('Selected invoice:', invoice);\r\n      \r\n      // Use the adjusted remaining amount that accounts for returns\r\n      const remainingAmount = invoice.remainingAmount;\r\n      \r\n      console.log(`Using adjusted remaining amount: ${remainingAmount}`);\r\n      \r\n      // Create a detailed narration\r\n      let narration = `Payment against invoice ${invoice.invoiceNumber}`;\r\n      \r\n      // If there are returns, add details\r\n      if (invoice.hasReturns) {\r\n        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\r\n        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;\r\n      }\r\n      \r\n      // Update amount field with remaining amount (fixed to 2 decimals)\r\n      const fixedAmount = fixDecimalPlaces(remainingAmount);\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        amount: fixedAmount,\r\n        narration\r\n      }));\r\n      setDisplayAmount(formatAccountingNumber(fixedAmount));\r\n      \r\n      // Update reference documents\r\n      const referenceDoc = {\r\n        documentType: 'PurchaseInvoice',\r\n        documentId: invoice._id,\r\n        documentNumber: invoice.invoiceNumber,\r\n        allocatedAmount: remainingAmount,\r\n        originalAmount: invoice.originalAmount,\r\n        returnAmount: invoice.returnAmount,\r\n        adjustedAmount: invoice.adjustedAmount\r\n      };\r\n      \r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        referenceDocuments: [referenceDoc]\r\n      }));\r\n    } else {\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        referenceDocuments: []\r\n      }));\r\n    }\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n\r\n    if (!voucher.fromAccountId) newErrors.fromAccountId = \"Bank is required\";\r\n    if (!voucher.toAccountId) newErrors.toAccountId = \"Title (debit account) is required\";\r\n    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {\r\n      newErrors.amount = \"Valid amount is required\";\r\n    }\r\n    if (!voucher.voucherDate) newErrors.voucherDate = \"Voucher date is required\";\r\n    if (!voucher.transactionDate) newErrors.transactionDate = \"Transaction date is required\";\r\n    if (!voucher.narration) newErrors.narration = \"Narration is required\";\r\n\r\n    // Vendor-invoice linkage validation\r\n    if (selectedVendor && selectedInvoice) {\r\n      // Ensure the selected invoice belongs to the selected vendor\r\n      if (selectedInvoice.vendorId !== selectedVendor._id) {\r\n        newErrors.invoice = \"Selected invoice does not belong to the selected vendor\";\r\n      }\r\n\r\n      // Ensure payment amount doesn't exceed remaining amount\r\n      const remainingAmount = selectedInvoice.remainingAmount || 0;\r\n      const paymentAmount = parseFloat(voucher.amount) || 0;\r\n      if (paymentAmount > remainingAmount) {\r\n        newErrors.amount = `Payment amount cannot exceed remaining amount of ${remainingAmount.toFixed(2)}`;\r\n      }\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!validateForm()) return;\r\n    \r\n    try {\r\n      setLoading(true);\r\n      \r\n      const voucherData = {\r\n        ...voucher,\r\n        voucherDate: voucher.voucherDate.toISOString(),\r\n        transactionDate: voucher.transactionDate.toISOString(),\r\n        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,\r\n        amount: parseFloat(voucher.amount)\r\n      };\r\n\r\n      let response;\r\n      if (voucherId) {\r\n        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);\r\n      } else {\r\n        response = await axios.post(\"/api/payment-vouchers\", voucherData);\r\n      }\r\n\r\n      if (onSave) onSave(response.data.voucher);\r\n    } catch (error) {\r\n      console.error(\"Error saving voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error saving bank payment voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleApprove = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\r\n      if (onSave) onSave();\r\n    } catch (error) {\r\n      console.error(\"Error approving voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error approving voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePost = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\r\n      if (onSave) onSave();\r\n    } catch (error) {\r\n      console.error(\"Error posting voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error posting voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');\r\n  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';\r\n  const canPost = !readOnly && voucherId && voucher.status === 'Approved';\r\n\r\n  return (\r\n    <Box>\r\n      <LocalizationProvider dateAdapter={AdapterDayjs}>\r\n        <Grid container spacing={3}>\r\n          {/* Left Column */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\r\n              <CardContent>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Bank Payment Details\r\n                </Typography>\r\n                \r\n                {/* Voucher Number (shown only for existing vouchers) */}\r\n                {voucherId && (\r\n                  <TextField\r\n                    fullWidth\r\n                    label=\"Voucher No.\"\r\n                    value={voucher.voucherNumber || ''}\r\n                    margin=\"normal\"\r\n                    InputProps={{ readOnly: true }}\r\n                  />\r\n                )}\r\n\r\n                {/* Voucher Date */}\r\n                <DatePicker\r\n                  label=\"Voucher Date\"\r\n                  value={voucher.voucherDate}\r\n                  onChange={(newValue) => handleInputChange('voucherDate', newValue)}\r\n                  format=\"DD/MMM/YYYY\"\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      fullWidth\r\n                      margin=\"normal\"\r\n                      error={!!errors.voucherDate}\r\n                      helperText={errors.voucherDate}\r\n                      InputProps={{\r\n                        ...params.InputProps,\r\n                        readOnly: !canEdit\r\n                      }}\r\n                    />\r\n                  )}\r\n                  readOnly={!canEdit}\r\n                />\r\n\r\n                {/* Transaction Date */}\r\n                <DatePicker\r\n                  label=\"Transaction Date\"\r\n                  value={voucher.transactionDate}\r\n                  onChange={(newValue) => handleInputChange('transactionDate', newValue)}\r\n                  format=\"DD/MMM/YYYY\"\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      fullWidth\r\n                      margin=\"normal\"\r\n                      error={!!errors.transactionDate}\r\n                      helperText={errors.transactionDate}\r\n                      InputProps={{\r\n                        ...params.InputProps,\r\n                        readOnly: !canEdit\r\n                      }}\r\n                    />\r\n                  )}\r\n                  readOnly={!canEdit}\r\n                />\r\n                \r\n                {/* Transaction ID / Reference */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Transaction ID\"\r\n                  value={voucher.bankReference || ''}\r\n                  onChange={(e) => handleInputChange('bankReference', e.target.value)}\r\n                  margin=\"normal\"\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                  helperText=\"Optional field for internal reference or payment ID\"\r\n                />\r\n\r\n                {/* Status */}\r\n                <FormControl fullWidth margin=\"normal\" error={!!errors.status}>\r\n                  <InputLabel>Status</InputLabel>\r\n                  <Select\r\n                    value={voucher.status || 'Draft'}\r\n                    onChange={(e) => handleInputChange('status', e.target.value)}\r\n                    label=\"Status\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    {statusOptions.map((status) => (\r\n                      <MenuItem key={status} value={status}>\r\n                        {status}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                  {errors.status && (\r\n                    <FormHelperText>{errors.status}</FormHelperText>\r\n                  )}\r\n                </FormControl>\r\n\r\n                {/* Bank Account (Credit Account) */}\r\n                <FormControl\r\n                  fullWidth\r\n                  margin=\"normal\"\r\n                  error={!!errors.fromAccountId}\r\n                >\r\n                  <InputLabel>Bank</InputLabel>\r\n                  <Select\r\n                    value={voucher.fromAccountId}\r\n                    onChange={(e) => handleInputChange('fromAccountId', e.target.value)}\r\n                    label=\"Bank\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    <MenuItem value=\"\">\r\n                      <em>Select Bank</em>\r\n                    </MenuItem>\r\n                    {bankAccounts.map((account) => (\r\n                      <MenuItem key={account._id} value={account._id}>\r\n                        {account.accountName}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                  {errors.fromAccountId && (\r\n                    <FormHelperText>{errors.fromAccountId}</FormHelperText>\r\n                  )}\r\n                </FormControl>\r\n                \r\n                {/* Payment Method */}\r\n                <FormControl fullWidth margin=\"normal\">\r\n                  <InputLabel>Payment Method</InputLabel>\r\n                  <Select\r\n                    value={voucher.paymentMethod}\r\n                    onChange={(e) => handleInputChange('paymentMethod', e.target.value)}\r\n                    label=\"Payment Method\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    {paymentMethods.map((method) => (\r\n                      <MenuItem key={method} value={method}>\r\n                        {method}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n                \r\n                {/* Cheque Details (shown only for cheque payments) */}\r\n                {voucher.paymentMethod === 'Cheque' && (\r\n                  <>\r\n                    <TextField\r\n                      fullWidth\r\n                      label=\"Cheque Number\"\r\n                      value={voucher.chequeNumber || ''}\r\n                      onChange={(e) => handleInputChange('chequeNumber', e.target.value)}\r\n                      margin=\"normal\"\r\n                      InputProps={{ readOnly: !canEdit }}\r\n                    />\r\n                    \r\n                    <DatePicker\r\n                      label=\"Cheque Date\"\r\n                      value={voucher.chequeDate}\r\n                      onChange={(newValue) => handleInputChange('chequeDate', newValue)}\r\n                      renderInput={(params) => (\r\n                        <TextField\r\n                          {...params}\r\n                          fullWidth\r\n                          margin=\"normal\"\r\n                          InputProps={{\r\n                            ...params.InputProps,\r\n                            readOnly: !canEdit\r\n                          }}\r\n                        />\r\n                      )}\r\n                      readOnly={!canEdit}\r\n                    />\r\n                  </>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          \r\n          {/* Right Column */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\r\n              <CardContent>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Payment Details\r\n                </Typography>\r\n                \r\n                {/* Vendor Selection */}\r\n                <Autocomplete\r\n                  options={vendors}\r\n                  getOptionLabel={(vendor) => vendor.name || ''}\r\n                  value={selectedVendor}\r\n                  onChange={(event, newValue) => handleVendorChange(newValue)}\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      label=\"Vendor\"\r\n                      margin=\"normal\"\r\n                      fullWidth\r\n                    />\r\n                  )}\r\n                  disabled={!canEdit}\r\n                />\r\n                \r\n                {/* Purchase Invoice Selection (shown only if vendor is selected) */}\r\n                {selectedVendor && (\r\n                  <Autocomplete\r\n                    options={purchaseInvoices}\r\n                    getOptionLabel={(invoice) => {\r\n                      if (!invoice) return '';\r\n\r\n                      const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;\r\n                      const invoiceDate = new Date(invoice.invoiceDate).toLocaleDateString('en-GB', {\r\n                        day: '2-digit',\r\n                        month: 'short',\r\n                        year: 'numeric'\r\n                      });\r\n\r\n                      // If there are returns, show the adjusted amount\r\n                      if (invoice.hasReturns) {\r\n                        return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} (Returns Applied)`;\r\n                      }\r\n\r\n                      // Otherwise just show the remaining amount\r\n                      return `${invoiceLabel} (${invoiceDate}) - ${formatCurrency(invoice.remainingAmount)} outstanding`;\r\n                    }}\r\n                    value={selectedInvoice}\r\n                    onChange={(event, newValue) => handleInvoiceChange(newValue)}\r\n                    renderOption={(props, invoice) => (\r\n                      <li {...props}>\r\n                        <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>\r\n                          <Typography variant=\"body1\" fontWeight=\"medium\" noWrap>\r\n                            {invoice.invoiceNumber} - {formatCurrency(invoice.remainingAmount)} outstanding\r\n                          </Typography>\r\n                          {invoice.hasReturns && (\r\n                            <>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" noWrap>\r\n                                Original: {formatCurrency(invoice.originalAmount)} | \r\n                                Returns: {formatCurrency(invoice.returnAmount)} | \r\n                                Adjusted: {formatCurrency(invoice.adjustedAmount)}\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"primary\" display=\"block\" noWrap>\r\n                                {invoice.returnDetails.length} return(s) applied - \r\n                                {invoice.returnDetails.map((ret, idx) => (\r\n                                  <span key={idx}>\r\n                                    {ret.returnNumber} ({ret.status})\r\n                                    {idx < invoice.returnDetails.length - 1 ? ', ' : ''}\r\n                                  </span>\r\n                                ))}\r\n                              </Typography>\r\n                            </>\r\n                          )}\r\n                        </Box>\r\n                      </li>\r\n                    )}\r\n                    renderInput={(params) => (\r\n                      <TextField\r\n                        {...params}\r\n                        label=\"Purchase Invoice\"\r\n                        margin=\"normal\"\r\n                        fullWidth\r\n                        error={purchaseInvoices.length === 0 || !!errors.invoice}\r\n                        helperText={\r\n                          errors.invoice ||\r\n                          (purchaseInvoices.length === 0 ? \"No unpaid invoices found for this vendor\" :\r\n                          \"Optional: Select invoice for payment tracking. Partial payments are supported.\")\r\n                        }\r\n                      />\r\n                    )}\r\n                    disabled={!canEdit || purchaseInvoices.length === 0}\r\n                    noOptionsText=\"No unpaid invoices found\"\r\n                    ListboxProps={{\r\n                      style: { maxHeight: '200px' }\r\n                    }}\r\n                  />\r\n                )}\r\n                \r\n                {/* Title (Debit Account - shown if no vendor is selected) */}\r\n                {!selectedVendor && (\r\n                  <FormControl\r\n                    fullWidth\r\n                    margin=\"normal\"\r\n                    error={!!errors.toAccountId}\r\n                  >\r\n                    <InputLabel>Title</InputLabel>\r\n                    <Select\r\n                      value={voucher.toAccountId}\r\n                      onChange={(e) => handleInputChange('toAccountId', e.target.value)}\r\n                      label=\"Title\"\r\n                      disabled={!canEdit}\r\n                    >\r\n                      <MenuItem value=\"\">\r\n                        <em>Select Account</em>\r\n                      </MenuItem>\r\n                      {accounts.map((account) => (\r\n                        <MenuItem key={account._id} value={account._id}>\r\n                          {account.accountCode} - {account.accountName}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                    {errors.toAccountId && (\r\n                      <FormHelperText>{errors.toAccountId}</FormHelperText>\r\n                    )}\r\n                  </FormControl>\r\n                )}\r\n                \r\n                {/* Amount */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Amount\"\r\n                  value={displayAmount}\r\n                  onChange={(e) => handleInputChange('amount', e.target.value)}\r\n                  onBlur={handleAmountBlur}\r\n                  onFocus={handleAmountFocus}\r\n                  margin=\"normal\"\r\n                  error={!!errors.amount}\r\n                  helperText={errors.amount || \"Amount will be formatted with commas and 2 decimal places\"}\r\n                  InputProps={{\r\n                    readOnly: !canEdit,\r\n                    inputProps: { style: { textAlign: 'right' } }\r\n                  }}\r\n                />\r\n                \r\n                {/* Narration */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Narration\"\r\n                  value={voucher.narration}\r\n                  onChange={(e) => handleInputChange('narration', e.target.value)}\r\n                  margin=\"normal\"\r\n                  multiline\r\n                  rows={2}\r\n                  error={!!errors.narration}\r\n                  helperText={errors.narration}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n                \r\n                {/* Description */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Description\"\r\n                  value={voucher.description || ''}\r\n                  onChange={(e) => handleInputChange('description', e.target.value)}\r\n                  margin=\"normal\"\r\n                  multiline\r\n                  rows={2}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          \r\n          {/* Action Buttons */}\r\n          <Grid item xs={12}>\r\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>\r\n              {onCancel && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  color=\"secondary\"\r\n                  startIcon={<CancelIcon />}\r\n                  onClick={onCancel}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n              )}\r\n              \r\n              {canEdit && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  startIcon={<SaveIcon />}\r\n                  onClick={handleSave}\r\n                  disabled={loading}\r\n                >\r\n                  Save\r\n                </Button>\r\n              )}\r\n              \r\n              {canApprove && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"warning\"\r\n                  startIcon={<ApproveIcon />}\r\n                  onClick={handleApprove}\r\n                  disabled={loading}\r\n                >\r\n                  Approve\r\n                </Button>\r\n              )}\r\n              \r\n              {canPost && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"success\"\r\n                  startIcon={<PostIcon />}\r\n                  onClick={handlePost}\r\n                  disabled={loading}\r\n                >\r\n                  Post to Ledger\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          </Grid>\r\n        </Grid>\r\n      </LocalizationProvider>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default BankPaymentVoucherForm; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,YAAY,EACZC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,WAAW,EACpBC,OAAO,IAAIC,QAAQ,EACnBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,cAAc,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvH,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,QAAQ,GAAG,KAAK;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC;IACrC+C,WAAW,EAAE,IAAI;IAAE;IACnBC,WAAW,EAAEnB,KAAK,CAAC,CAAC;IAAE;IACtBoB,eAAe,EAAEpB,KAAK,CAAC,CAAC;IAAE;IAC1BqB,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE,EAAE;IAAE;IACnBC,WAAW,EAAE,EAAE;IAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,eAAe;IAC9BC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,EAAE,CAAC;;EAEtD;EACA,MAAM,CAACkE,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EAE/E,MAAM,CAACoE,QAAQ,EAAEC,WAAW,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsE,YAAY,EAAEC,eAAe,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACwE,OAAO,EAAEC,UAAU,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0E,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAAC4E,OAAO,EAAEC,UAAU,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8E,MAAM,EAAEC,SAAS,CAAC,GAAG/E,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgF,cAAc,EAAEC,iBAAiB,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACkF,eAAe,EAAEC,kBAAkB,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAMoF,cAAc,GAAG,CACrB,eAAe,EACf,QAAQ,EACR,iBAAiB,EACjB,MAAM,EACN,OAAO,CACR;EAED,MAAMC,aAAa,GAAG,CACpB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,WAAW,CACZ;EAEDpF,SAAS,CAAC,MAAM;IACdqF,gBAAgB,CAAC,CAAC;IAClB,IAAI9C,SAAS,EAAE;MACb+C,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC/C,SAAS,CAAC,CAAC;;EAEf;EACAvC,SAAS,CAAC,MAAM;IACd,IAAI+E,cAAc,EAAE;MAClBQ,mBAAmB,CAACR,cAAc,CAACS,GAAG,CAAC;IACzC,CAAC,MAAM;MACLd,mBAAmB,CAAC,EAAE,CAAC;MACvBQ,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;;EAEpB;EACA/E,SAAS,CAAC,MAAM;IACd,IAAIiF,eAAe,IAAI,CAAChB,wBAAwB,EAAE;MAChD,MAAMwB,eAAe,GAAGR,eAAe,CAACQ,eAAe,IAAI,CAAC;;MAE5D;MACA,IAAI9B,SAAS,GAAG,2BAA2BsB,eAAe,CAACS,aAAa,IAAI,SAAS,EAAE;;MAEvF;MACA,IAAIT,eAAe,CAACU,UAAU,IAAIV,eAAe,CAACW,aAAa,EAAE;QAC/D,MAAMC,cAAc,GAAGZ,eAAe,CAACW,aAAa,CAACE,GAAG,CAACC,GAAG,IAAI,GAAGA,GAAG,CAACC,YAAY,KAAKD,GAAG,CAAClC,MAAM,GAAG,CAAC,CAACoC,IAAI,CAAC,IAAI,CAAC;QACjHtC,SAAS,GAAG,2BAA2BsB,eAAe,CAACS,aAAa,eAAe5D,cAAc,CAACmD,eAAe,CAACiB,cAAc,IAAI,CAAC,CAAC,cAAcpE,cAAc,CAACmD,eAAe,CAACkB,YAAY,IAAI,CAAC,CAAC,KAAKN,cAAc,gBAAgB/D,cAAc,CAACmD,eAAe,CAACmB,cAAc,IAAI,CAAC,CAAC,GAAG;MAChS;MAEA,MAAMC,WAAW,GAAGpE,gBAAgB,CAACwD,eAAe,CAAC;MACrD5C,UAAU,CAACyD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPrD,MAAM,EAAEoD,WAAW;QACnB1C;MACF,CAAC,CAAC,CAAC;MACHK,gBAAgB,CAACjC,sBAAsB,CAACsE,WAAW,CAAC,CAAC;IACvD;EACF,CAAC,EAAE,CAACpB,eAAe,EAAEhB,wBAAwB,CAAC,CAAC;;EAE/C;EACAjE,SAAS,CAAC,MAAM;IACd;IACA,MAAMuG,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACC,KAAK;IAC1CF,MAAM,CAACC,OAAO,CAACC,KAAK,GAAG,CAAC,GAAGC,IAAI,KAAK;MAAA,IAAAC,MAAA,EAAAC,eAAA;MAClC,KAAAD,MAAA,GAAID,IAAI,CAAC,CAAC,CAAC,cAAAC,MAAA,gBAAAC,eAAA,GAAPD,MAAA,CAASE,QAAQ,cAAAD,eAAA,eAAjBA,eAAA,CAAAE,IAAA,CAAAH,MAAA,EAAoB,qBAAqB,CAAC,EAAE;QAC9C;QACA;MACF;MACAL,aAAa,CAAC,GAAGI,IAAI,CAAC;IACxB,CAAC;IAED,OAAO,MAAM;MACXH,MAAM,CAACC,OAAO,CAACC,KAAK,GAAGH,aAAa;IACtC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMlB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM,CAAC2B,WAAW,EAAEC,UAAU,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnEvF,KAAK,CAACwF,GAAG,CAAC,2BAA2B,CAAC,EACtCxF,KAAK,CAACwF,GAAG,CAAC,cAAc,CAAC,EACzBxF,KAAK,CAACwF,GAAG,CAAC,oBAAoB,CAAC,CAChC,CAAC;MAEFjD,WAAW,CAAC4C,WAAW,CAACM,IAAI,CAAC;MAC7B9C,UAAU,CAACyC,UAAU,CAACK,IAAI,CAAC;;MAE3B;MACA,MAAMC,QAAQ,GAAGL,eAAe,CAACI,IAAI;MACrC,MAAME,cAAc,GAAGD,QAAQ,CAACzB,GAAG,CAAC2B,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC;;MAE5E;MACA,IAAIJ,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMC,mBAAmB,GAAGd,WAAW,CAACM,IAAI,CAACK,MAAM,CAACI,GAAG,IACrDP,cAAc,CAACV,QAAQ,CAACiB,GAAG,CAACvC,GAAG,CACjC,CAAC;QACDlB,eAAe,CAACwD,mBAAmB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMpB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MAAA,IAAA0C,qBAAA,EAAAC,qBAAA;MACFrD,UAAU,CAAC,IAAI,CAAC;MAChBV,2BAA2B,CAAC,IAAI,CAAC,CAAC,CAAC;;MAEnC,MAAMgE,QAAQ,GAAG,MAAMrG,KAAK,CAACwF,GAAG,CAAC,yBAAyB9E,SAAS,EAAE,CAAC;MACtE,MAAM4F,WAAW,GAAGD,QAAQ,CAACZ,IAAI;MAEjC,MAAMjB,WAAW,GAAGpE,gBAAgB,CAACkG,WAAW,CAAClF,MAAM,CAAC;MACxDJ,UAAU,CAAC;QACT,GAAGsF,WAAW;QACdpF,WAAW,EAAEoF,WAAW,CAACpF,WAAW,GAAGnB,KAAK,CAACuG,WAAW,CAACpF,WAAW,CAAC,GAAGnB,KAAK,CAACuG,WAAW,CAACC,SAAS,IAAI,IAAIC,IAAI,CAAC,CAAC,CAAC;QAClHrF,eAAe,EAAEpB,KAAK,CAACuG,WAAW,CAACnF,eAAe,IAAI,IAAIqF,IAAI,CAAC,CAAC,CAAC;QACjE5E,UAAU,EAAE0E,WAAW,CAAC1E,UAAU,GAAG7B,KAAK,CAACuG,WAAW,CAAC1E,UAAU,CAAC,GAAG,IAAI;QACzEP,aAAa,EAAE,EAAA8E,qBAAA,GAAAG,WAAW,CAACjF,aAAa,cAAA8E,qBAAA,uBAAzBA,qBAAA,CAA2BxC,GAAG,KAAI2C,WAAW,CAACjF,aAAa;QAC1EC,WAAW,EAAE,EAAA8E,qBAAA,GAAAE,WAAW,CAAChF,WAAW,cAAA8E,qBAAA,uBAAvBA,qBAAA,CAAyBzC,GAAG,KAAI2C,WAAW,CAAChF,WAAW;QACpEF,MAAM,EAAEoD;MACV,CAAC,CAAC;MACFrC,gBAAgB,CAACjC,sBAAsB,CAACsE,WAAW,CAAC,CAAC;;MAErD;MACA,IAAI8B,WAAW,CAAC/E,gBAAgB,KAAK,QAAQ,IAAI+E,WAAW,CAAC9E,cAAc,EAAE;QAC3E,MAAMiF,SAAS,GAAG,MAAMzG,KAAK,CAACwF,GAAG,CAAC,gBAAgBc,WAAW,CAAC9E,cAAc,EAAE,CAAC;QAC/E2B,iBAAiB,CAACsD,SAAS,CAAChB,IAAI,CAAC;;QAEjC;QACA,IAAIa,WAAW,CAACrE,kBAAkB,IAAIqE,WAAW,CAACrE,kBAAkB,CAAC+D,MAAM,GAAG,CAAC,EAAE;UAC/E,MAAMU,UAAU,GAAGJ,WAAW,CAACrE,kBAAkB,CAAC0E,IAAI,CAACC,GAAG,IACxDA,GAAG,CAACC,YAAY,KAAK,iBACvB,CAAC;UAED,IAAIH,UAAU,IAAIA,UAAU,CAACI,UAAU,EAAE;YACvC,MAAMC,UAAU,GAAG,MAAM/G,KAAK,CAACwF,GAAG,CAAC,0BAA0BkB,UAAU,CAACI,UAAU,EAAE,CAAC;YACrFzD,kBAAkB,CAAC0D,UAAU,CAACtB,IAAI,CAAC;UACrC;QACF;MACF;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;MACjBV,2BAA2B,CAAC,KAAK,CAAC,CAAC,CAAC;IACtC;EACF,CAAC;EAED,MAAMqB,mBAAmB,GAAG,MAAOsD,QAAQ,IAAK;IAC9C,IAAI;MACFpC,OAAO,CAACqC,GAAG,CAAC,iCAAiCD,QAAQ,EAAE,CAAC;MACxD;MACA,MAAMX,QAAQ,GAAG,MAAMrG,KAAK,CAACwF,GAAG,CAAC,yCAAyCwB,QAAQ,EAAE,CAAC;MACrFpC,OAAO,CAACqC,GAAG,CAAC,2BAA2B,EAAEZ,QAAQ,CAACZ,IAAI,CAAC;MAEvD,IAAIyB,KAAK,CAACC,OAAO,CAACd,QAAQ,CAACZ,IAAI,CAAC,IAAIY,QAAQ,CAACZ,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;QAC5DnD,mBAAmB,CAACwD,QAAQ,CAACZ,IAAI,CAAC;MACpC,CAAC,MAAM;QACLb,OAAO,CAACqC,GAAG,CAAC,0CAA0C,CAAC;QACvDpE,mBAAmB,CAAC,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA,IAAAuC,eAAA,EAAAC,oBAAA;MACdzC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDyC,KAAK,CAAC,oCAAoC,EAAAF,eAAA,GAAAvC,KAAK,CAACwB,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB3B,IAAI,cAAA4B,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAI1C,KAAK,CAAC0C,OAAO,EAAE,CAAC;MAC3F1E,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAM2E,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C,IAAID,KAAK,KAAK,QAAQ,EAAE;MACtB;MACA,MAAME,YAAY,GAAGxH,qBAAqB,CAACuH,KAAK,CAAC;MACjD1G,UAAU,CAACyD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACgD,KAAK,GAAGE;MAAa,CAAC,CAAC,CAAC;MACxDxF,gBAAgB,CAACuF,KAAK,CAAC,CAAC,CAAC;IAC3B,CAAC,MAAM;MACL1G,UAAU,CAACyD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACgD,KAAK,GAAGC;MAAM,CAAC,CAAC,CAAC;IACnD;;IAEA;IACA,IAAI1E,MAAM,CAACyE,KAAK,CAAC,EAAE;MACjBxE,SAAS,CAACwB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACgD,KAAK,GAAG;MAAK,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI7G,OAAO,CAACK,MAAM,EAAE;MAClB,MAAMyG,eAAe,GAAG3H,sBAAsB,CAACa,OAAO,CAACK,MAAM,CAAC;MAC9De,gBAAgB,CAAC0F,eAAe,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B3F,gBAAgB,CAACpB,OAAO,CAACK,MAAM,CAAC;EAClC,CAAC;EAED,MAAM2G,kBAAkB,GAAIC,MAAM,IAAK;IACrC7E,iBAAiB,CAAC6E,MAAM,CAAC;IACzB3E,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE1B,IAAI2E,MAAM,EAAE;MACV;MACAhH,UAAU,CAACyD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPlD,gBAAgB,EAAE,QAAQ;QAC1BC,cAAc,EAAEwG,MAAM,CAACrE,GAAG;QAC1BlC,gBAAgB,EAAEuG,MAAM,CAACC,IAAI;QAC7B3G,WAAW,EAAE0G,MAAM,CAACnC,SAAS,IAAI,EAAE;QAAE;QACrCzE,MAAM,EAAE,EAAE;QAAE;QACZU,SAAS,EAAE,EAAE;QAAE;QACfG,kBAAkB,EAAE,EAAE,CAAC;MACzB,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACAjB,UAAU,CAACyD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPlD,gBAAgB,EAAE,EAAE;QACpBC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBH,WAAW,EAAE,EAAE;QACfF,MAAM,EAAE,EAAE;QACVU,SAAS,EAAE,EAAE;QACbG,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMiG,mBAAmB,GAAIC,OAAO,IAAK;IACvC9E,kBAAkB,CAAC8E,OAAO,CAAC;IAE3B,IAAIA,OAAO,EAAE;MACXvD,OAAO,CAACqC,GAAG,CAAC,mBAAmB,EAAEkB,OAAO,CAAC;;MAEzC;MACA,MAAMvE,eAAe,GAAGuE,OAAO,CAACvE,eAAe;MAE/CgB,OAAO,CAACqC,GAAG,CAAC,oCAAoCrD,eAAe,EAAE,CAAC;;MAElE;MACA,IAAI9B,SAAS,GAAG,2BAA2BqG,OAAO,CAACtE,aAAa,EAAE;;MAElE;MACA,IAAIsE,OAAO,CAACrE,UAAU,EAAE;QACtB,MAAME,cAAc,GAAGmE,OAAO,CAACpE,aAAa,CAACE,GAAG,CAACC,GAAG,IAAI,GAAGA,GAAG,CAACC,YAAY,KAAKD,GAAG,CAAClC,MAAM,GAAG,CAAC,CAACoC,IAAI,CAAC,IAAI,CAAC;QACzGtC,SAAS,GAAG,2BAA2BqG,OAAO,CAACtE,aAAa,eAAe5D,cAAc,CAACkI,OAAO,CAAC9D,cAAc,CAAC,cAAcpE,cAAc,CAACkI,OAAO,CAAC7D,YAAY,CAAC,KAAKN,cAAc,gBAAgB/D,cAAc,CAACkI,OAAO,CAAC5D,cAAc,CAAC,GAAG;MACjP;;MAEA;MACA,MAAMC,WAAW,GAAGpE,gBAAgB,CAACwD,eAAe,CAAC;MACrD5C,UAAU,CAACyD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPrD,MAAM,EAAEoD,WAAW;QACnB1C;MACF,CAAC,CAAC,CAAC;MACHK,gBAAgB,CAACjC,sBAAsB,CAACsE,WAAW,CAAC,CAAC;;MAErD;MACA,MAAM4D,YAAY,GAAG;QACnBvB,YAAY,EAAE,iBAAiB;QAC/BC,UAAU,EAAEqB,OAAO,CAACxE,GAAG;QACvB0E,cAAc,EAAEF,OAAO,CAACtE,aAAa;QACrCyE,eAAe,EAAE1E,eAAe;QAChCS,cAAc,EAAE8D,OAAO,CAAC9D,cAAc;QACtCC,YAAY,EAAE6D,OAAO,CAAC7D,YAAY;QAClCC,cAAc,EAAE4D,OAAO,CAAC5D;MAC1B,CAAC;MAEDvD,UAAU,CAACyD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPxC,kBAAkB,EAAE,CAACmG,YAAY;MACnC,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLpH,UAAU,CAACyD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPxC,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMsG,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACzH,OAAO,CAACM,aAAa,EAAEmH,SAAS,CAACnH,aAAa,GAAG,kBAAkB;IACxE,IAAI,CAACN,OAAO,CAACO,WAAW,EAAEkH,SAAS,CAAClH,WAAW,GAAG,mCAAmC;IACrF,IAAI,CAACP,OAAO,CAACK,MAAM,IAAIqH,KAAK,CAACC,UAAU,CAAC3H,OAAO,CAACK,MAAM,CAAC,CAAC,IAAIsH,UAAU,CAAC3H,OAAO,CAACK,MAAM,CAAC,IAAI,CAAC,EAAE;MAC3FoH,SAAS,CAACpH,MAAM,GAAG,0BAA0B;IAC/C;IACA,IAAI,CAACL,OAAO,CAACG,WAAW,EAAEsH,SAAS,CAACtH,WAAW,GAAG,0BAA0B;IAC5E,IAAI,CAACH,OAAO,CAACI,eAAe,EAAEqH,SAAS,CAACrH,eAAe,GAAG,8BAA8B;IACxF,IAAI,CAACJ,OAAO,CAACe,SAAS,EAAE0G,SAAS,CAAC1G,SAAS,GAAG,uBAAuB;;IAErE;IACA,IAAIoB,cAAc,IAAIE,eAAe,EAAE;MACrC;MACA,IAAIA,eAAe,CAAC4D,QAAQ,KAAK9D,cAAc,CAACS,GAAG,EAAE;QACnD6E,SAAS,CAACL,OAAO,GAAG,yDAAyD;MAC/E;;MAEA;MACA,MAAMvE,eAAe,GAAGR,eAAe,CAACQ,eAAe,IAAI,CAAC;MAC5D,MAAM+E,aAAa,GAAGD,UAAU,CAAC3H,OAAO,CAACK,MAAM,CAAC,IAAI,CAAC;MACrD,IAAIuH,aAAa,GAAG/E,eAAe,EAAE;QACnC4E,SAAS,CAACpH,MAAM,GAAG,oDAAoDwC,eAAe,CAACgF,OAAO,CAAC,CAAC,CAAC,EAAE;MACrG;IACF;IAEA3F,SAAS,CAACuF,SAAS,CAAC;IACpB,OAAOK,MAAM,CAACC,IAAI,CAACN,SAAS,CAAC,CAACxC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAM+C,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACR,YAAY,CAAC,CAAC,EAAE;IAErB,IAAI;MACFxF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMuD,WAAW,GAAG;QAClB,GAAGvF,OAAO;QACVG,WAAW,EAAEH,OAAO,CAACG,WAAW,CAAC8H,WAAW,CAAC,CAAC;QAC9C7H,eAAe,EAAEJ,OAAO,CAACI,eAAe,CAAC6H,WAAW,CAAC,CAAC;QACtDpH,UAAU,EAAEb,OAAO,CAACa,UAAU,GAAGb,OAAO,CAACa,UAAU,CAACoH,WAAW,CAAC,CAAC,GAAG,IAAI;QACxE5H,MAAM,EAAEsH,UAAU,CAAC3H,OAAO,CAACK,MAAM;MACnC,CAAC;MAED,IAAIiF,QAAQ;MACZ,IAAI3F,SAAS,EAAE;QACb2F,QAAQ,GAAG,MAAMrG,KAAK,CAACiJ,GAAG,CAAC,yBAAyBvI,SAAS,EAAE,EAAE4F,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLD,QAAQ,GAAG,MAAMrG,KAAK,CAACkJ,IAAI,CAAC,uBAAuB,EAAE5C,WAAW,CAAC;MACnE;MAEA,IAAI1F,MAAM,EAAEA,MAAM,CAACyF,QAAQ,CAACZ,IAAI,CAAC1E,OAAO,CAAC;IAC3C,CAAC,CAAC,OAAO8D,KAAK,EAAE;MAAA,IAAAsE,gBAAA,EAAAC,qBAAA;MACdxE,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CyC,KAAK,CAAC,EAAA6B,gBAAA,GAAAtE,KAAK,CAACwB,QAAQ,cAAA8C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1D,IAAI,cAAA2D,qBAAA,uBAApBA,qBAAA,CAAsB7B,OAAO,KAAI,mCAAmC,CAAC;IAC7E,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFtG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM/C,KAAK,CAACiJ,GAAG,CAAC,yBAAyBvI,SAAS,UAAU,CAAC;MAC7D,IAAIE,MAAM,EAAEA,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOiE,KAAK,EAAE;MAAA,IAAAyE,gBAAA,EAAAC,qBAAA;MACd3E,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDyC,KAAK,CAAC,EAAAgC,gBAAA,GAAAzE,KAAK,CAACwB,QAAQ,cAAAiD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7D,IAAI,cAAA8D,qBAAA,uBAApBA,qBAAA,CAAsBhC,OAAO,KAAI,yBAAyB,CAAC;IACnE,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyG,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFzG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM/C,KAAK,CAACiJ,GAAG,CAAC,yBAAyBvI,SAAS,OAAO,CAAC;MAC1D,IAAIE,MAAM,EAAEA,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOiE,KAAK,EAAE;MAAA,IAAA4E,gBAAA,EAAAC,qBAAA;MACd9E,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CyC,KAAK,CAAC,EAAAmC,gBAAA,GAAA5E,KAAK,CAACwB,QAAQ,cAAAoD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhE,IAAI,cAAAiE,qBAAA,uBAApBA,qBAAA,CAAsBnC,OAAO,KAAI,uBAAuB,CAAC;IACjE,CAAC,SAAS;MACRxE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4G,OAAO,GAAG,CAAChJ,QAAQ,KAAK,CAACD,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,OAAO,CAAC;EACvE,MAAM4H,UAAU,GAAG,CAACjJ,QAAQ,IAAID,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,OAAO;EACvE,MAAM6H,OAAO,GAAG,CAAClJ,QAAQ,IAAID,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,UAAU;EAEvE,oBACE1B,OAAA,CAAClC,GAAG;IAAA0L,QAAA,eACFxJ,OAAA,CAACT,oBAAoB;MAACkK,WAAW,EAAEjK,YAAa;MAAAgK,QAAA,eAC9CxJ,OAAA,CAAC7B,IAAI;QAACuL,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAH,QAAA,gBAEzBxJ,OAAA,CAAC7B,IAAI;UAACyL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACvBxJ,OAAA,CAACjC,IAAI;YAACgM,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACrCxJ,OAAA,CAAChC,WAAW;cAAAwL,QAAA,gBACVxJ,OAAA,CAAC/B,UAAU;gBAAC8L,OAAO,EAAC,IAAI;gBAACG,YAAY;gBAAAV,QAAA,EAAC;cAEtC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAGZlK,SAAS,iBACRJ,OAAA,CAAC5B,SAAS;gBACRmM,SAAS;gBACTC,KAAK,EAAC,aAAa;gBACnBpD,KAAK,EAAE3G,OAAO,CAACgK,aAAa,IAAI,EAAG;gBACnCC,MAAM,EAAC,QAAQ;gBACfC,UAAU,EAAE;kBAAEtK,QAAQ,EAAE;gBAAK;cAAE;gBAAA8J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACF,eAGDtK,OAAA,CAACV,UAAU;gBACTkL,KAAK,EAAC,cAAc;gBACpBpD,KAAK,EAAE3G,OAAO,CAACG,WAAY;gBAC3BgK,QAAQ,EAAGC,QAAQ,IAAK3D,iBAAiB,CAAC,aAAa,EAAE2D,QAAQ,CAAE;gBACnEC,MAAM,EAAC,aAAa;gBACpBC,WAAW,EAAGC,MAAM,iBAClBhL,OAAA,CAAC5B,SAAS;kBAAA,GACJ4M,MAAM;kBACVT,SAAS;kBACTG,MAAM,EAAC,QAAQ;kBACfnG,KAAK,EAAE,CAAC,CAAC7B,MAAM,CAAC9B,WAAY;kBAC5BqK,UAAU,EAAEvI,MAAM,CAAC9B,WAAY;kBAC/B+J,UAAU,EAAE;oBACV,GAAGK,MAAM,CAACL,UAAU;oBACpBtK,QAAQ,EAAE,CAACgJ;kBACb;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFjK,QAAQ,EAAE,CAACgJ;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGFtK,OAAA,CAACV,UAAU;gBACTkL,KAAK,EAAC,kBAAkB;gBACxBpD,KAAK,EAAE3G,OAAO,CAACI,eAAgB;gBAC/B+J,QAAQ,EAAGC,QAAQ,IAAK3D,iBAAiB,CAAC,iBAAiB,EAAE2D,QAAQ,CAAE;gBACvEC,MAAM,EAAC,aAAa;gBACpBC,WAAW,EAAGC,MAAM,iBAClBhL,OAAA,CAAC5B,SAAS;kBAAA,GACJ4M,MAAM;kBACVT,SAAS;kBACTG,MAAM,EAAC,QAAQ;kBACfnG,KAAK,EAAE,CAAC,CAAC7B,MAAM,CAAC7B,eAAgB;kBAChCoK,UAAU,EAAEvI,MAAM,CAAC7B,eAAgB;kBACnC8J,UAAU,EAAE;oBACV,GAAGK,MAAM,CAACL,UAAU;oBACpBtK,QAAQ,EAAE,CAACgJ;kBACb;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFjK,QAAQ,EAAE,CAACgJ;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGFtK,OAAA,CAAC5B,SAAS;gBACRmM,SAAS;gBACTC,KAAK,EAAC,gBAAgB;gBACtBpD,KAAK,EAAE3G,OAAO,CAACc,aAAa,IAAI,EAAG;gBACnCqJ,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,eAAe,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;gBACpEsD,MAAM,EAAC,QAAQ;gBACfC,UAAU,EAAE;kBAAEtK,QAAQ,EAAE,CAACgJ;gBAAQ,CAAE;gBACnC4B,UAAU,EAAC;cAAqD;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjE,CAAC,eAGFtK,OAAA,CAAC3B,WAAW;gBAACkM,SAAS;gBAACG,MAAM,EAAC,QAAQ;gBAACnG,KAAK,EAAE,CAAC,CAAC7B,MAAM,CAAChB,MAAO;gBAAA8H,QAAA,gBAC5DxJ,OAAA,CAAC1B,UAAU;kBAAAkL,QAAA,EAAC;gBAAM;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/BtK,OAAA,CAACzB,MAAM;kBACL6I,KAAK,EAAE3G,OAAO,CAACiB,MAAM,IAAI,OAAQ;kBACjCkJ,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,QAAQ,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;kBAC7DoD,KAAK,EAAC,QAAQ;kBACdY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,EAElBvG,aAAa,CAACU,GAAG,CAAEjC,MAAM,iBACxB1B,OAAA,CAACxB,QAAQ;oBAAc4I,KAAK,EAAE1F,MAAO;oBAAA8H,QAAA,EAClC9H;kBAAM,GADMA,MAAM;oBAAAyI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACR5H,MAAM,CAAChB,MAAM,iBACZ1B,OAAA,CAACnB,cAAc;kBAAA2K,QAAA,EAAE9G,MAAM,CAAChB;gBAAM;kBAAAyI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CAChD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eAGdtK,OAAA,CAAC3B,WAAW;gBACVkM,SAAS;gBACTG,MAAM,EAAC,QAAQ;gBACfnG,KAAK,EAAE,CAAC,CAAC7B,MAAM,CAAC3B,aAAc;gBAAAyI,QAAA,gBAE9BxJ,OAAA,CAAC1B,UAAU;kBAAAkL,QAAA,EAAC;gBAAI;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7BtK,OAAA,CAACzB,MAAM;kBACL6I,KAAK,EAAE3G,OAAO,CAACM,aAAc;kBAC7B6J,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,eAAe,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;kBACpEoD,KAAK,EAAC,MAAM;kBACZY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,gBAEnBxJ,OAAA,CAACxB,QAAQ;oBAAC4I,KAAK,EAAC,EAAE;oBAAAoC,QAAA,eAChBxJ,OAAA;sBAAAwJ,QAAA,EAAI;oBAAW;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,EACVpI,YAAY,CAACyB,GAAG,CAAE0H,OAAO,iBACxBrL,OAAA,CAACxB,QAAQ;oBAAmB4I,KAAK,EAAEiE,OAAO,CAAChI,GAAI;oBAAAmG,QAAA,EAC5C6B,OAAO,CAACC;kBAAW,GADPD,OAAO,CAAChI,GAAG;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACR5H,MAAM,CAAC3B,aAAa,iBACnBf,OAAA,CAACnB,cAAc;kBAAA2K,QAAA,EAAE9G,MAAM,CAAC3B;gBAAa;kBAAAoJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eAGdtK,OAAA,CAAC3B,WAAW;gBAACkM,SAAS;gBAACG,MAAM,EAAC,QAAQ;gBAAAlB,QAAA,gBACpCxJ,OAAA,CAAC1B,UAAU;kBAAAkL,QAAA,EAAC;gBAAc;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvCtK,OAAA,CAACzB,MAAM;kBACL6I,KAAK,EAAE3G,OAAO,CAACW,aAAc;kBAC7BwJ,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,eAAe,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;kBACpEoD,KAAK,EAAC,gBAAgB;kBACtBY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,EAElBxG,cAAc,CAACW,GAAG,CAAE4H,MAAM,iBACzBvL,OAAA,CAACxB,QAAQ;oBAAc4I,KAAK,EAAEmE,MAAO;oBAAA/B,QAAA,EAClC+B;kBAAM,GADMA,MAAM;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGb7J,OAAO,CAACW,aAAa,KAAK,QAAQ,iBACjCpB,OAAA,CAAAE,SAAA;gBAAAsJ,QAAA,gBACExJ,OAAA,CAAC5B,SAAS;kBACRmM,SAAS;kBACTC,KAAK,EAAC,eAAe;kBACrBpD,KAAK,EAAE3G,OAAO,CAACY,YAAY,IAAI,EAAG;kBAClCuJ,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,cAAc,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;kBACnEsD,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBAAEtK,QAAQ,EAAE,CAACgJ;kBAAQ;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eAEFtK,OAAA,CAACV,UAAU;kBACTkL,KAAK,EAAC,aAAa;kBACnBpD,KAAK,EAAE3G,OAAO,CAACa,UAAW;kBAC1BsJ,QAAQ,EAAGC,QAAQ,IAAK3D,iBAAiB,CAAC,YAAY,EAAE2D,QAAQ,CAAE;kBAClEE,WAAW,EAAGC,MAAM,iBAClBhL,OAAA,CAAC5B,SAAS;oBAAA,GACJ4M,MAAM;oBACVT,SAAS;oBACTG,MAAM,EAAC,QAAQ;oBACfC,UAAU,EAAE;sBACV,GAAGK,MAAM,CAACL,UAAU;sBACpBtK,QAAQ,EAAE,CAACgJ;oBACb;kBAAE;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACD;kBACFjK,QAAQ,EAAE,CAACgJ;gBAAQ;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA,eACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPtK,OAAA,CAAC7B,IAAI;UAACyL,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACvBxJ,OAAA,CAACjC,IAAI;YAACgM,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACrCxJ,OAAA,CAAChC,WAAW;cAAAwL,QAAA,gBACVxJ,OAAA,CAAC/B,UAAU;gBAAC8L,OAAO,EAAC,IAAI;gBAACG,YAAY;gBAAAV,QAAA,EAAC;cAEtC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAGbtK,OAAA,CAACpB,YAAY;gBACX4M,OAAO,EAAEpJ,OAAQ;gBACjBqJ,cAAc,EAAG/D,MAAM,IAAKA,MAAM,CAACC,IAAI,IAAI,EAAG;gBAC9CP,KAAK,EAAExE,cAAe;gBACtBgI,QAAQ,EAAEA,CAACc,KAAK,EAAEb,QAAQ,KAAKpD,kBAAkB,CAACoD,QAAQ,CAAE;gBAC5DE,WAAW,EAAGC,MAAM,iBAClBhL,OAAA,CAAC5B,SAAS;kBAAA,GACJ4M,MAAM;kBACVR,KAAK,EAAC,QAAQ;kBACdE,MAAM,EAAC,QAAQ;kBACfH,SAAS;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACD;gBACFc,QAAQ,EAAE,CAAC/B;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EAGD1H,cAAc,iBACb5C,OAAA,CAACpB,YAAY;gBACX4M,OAAO,EAAElJ,gBAAiB;gBAC1BmJ,cAAc,EAAG5D,OAAO,IAAK;kBAC3B,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;kBAEvB,MAAM8D,YAAY,GAAG,GAAG9D,OAAO,CAACtE,aAAa,IAAI,SAAS,EAAE;kBAC5D,MAAMqI,WAAW,GAAG,IAAI1F,IAAI,CAAC2B,OAAO,CAAC+D,WAAW,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;oBAC5EC,GAAG,EAAE,SAAS;oBACdC,KAAK,EAAE,OAAO;oBACdC,IAAI,EAAE;kBACR,CAAC,CAAC;;kBAEF;kBACA,IAAInE,OAAO,CAACrE,UAAU,EAAE;oBACtB,OAAO,GAAGmI,YAAY,KAAKC,WAAW,OAAOjM,cAAc,CAACkI,OAAO,CAACvE,eAAe,CAAC,oBAAoB;kBAC1G;;kBAEA;kBACA,OAAO,GAAGqI,YAAY,KAAKC,WAAW,OAAOjM,cAAc,CAACkI,OAAO,CAACvE,eAAe,CAAC,cAAc;gBACpG,CAAE;gBACF8D,KAAK,EAAEtE,eAAgB;gBACvB8H,QAAQ,EAAEA,CAACc,KAAK,EAAEb,QAAQ,KAAKjD,mBAAmB,CAACiD,QAAQ,CAAE;gBAC7DoB,YAAY,EAAEA,CAACC,KAAK,EAAErE,OAAO,kBAC3B7H,OAAA;kBAAA,GAAQkM,KAAK;kBAAA1C,QAAA,eACXxJ,OAAA,CAAClC,GAAG;oBAACkM,EAAE,EAAE;sBAAEmC,KAAK,EAAE,MAAM;sBAAEC,QAAQ,EAAE,MAAM;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAA7C,QAAA,gBAC/DxJ,OAAA,CAAC/B,UAAU;sBAAC8L,OAAO,EAAC,OAAO;sBAACuC,UAAU,EAAC,QAAQ;sBAACC,MAAM;sBAAA/C,QAAA,GACnD3B,OAAO,CAACtE,aAAa,EAAC,KAAG,EAAC5D,cAAc,CAACkI,OAAO,CAACvE,eAAe,CAAC,EAAC,cACrE;oBAAA;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,EACZzC,OAAO,CAACrE,UAAU,iBACjBxD,OAAA,CAAAE,SAAA;sBAAAsJ,QAAA,gBACExJ,OAAA,CAAC/B,UAAU;wBAAC8L,OAAO,EAAC,SAAS;wBAACyC,KAAK,EAAC,gBAAgB;wBAACC,OAAO,EAAC,OAAO;wBAACF,MAAM;wBAAA/C,QAAA,GAAC,YAChE,EAAC7J,cAAc,CAACkI,OAAO,CAAC9D,cAAc,CAAC,EAAC,cACzC,EAACpE,cAAc,CAACkI,OAAO,CAAC7D,YAAY,CAAC,EAAC,eACrC,EAACrE,cAAc,CAACkI,OAAO,CAAC5D,cAAc,CAAC;sBAAA;wBAAAkG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACbtK,OAAA,CAAC/B,UAAU;wBAAC8L,OAAO,EAAC,SAAS;wBAACyC,KAAK,EAAC,SAAS;wBAACC,OAAO,EAAC,OAAO;wBAACF,MAAM;wBAAA/C,QAAA,GACjE3B,OAAO,CAACpE,aAAa,CAACiC,MAAM,EAAC,sBAC9B,EAACmC,OAAO,CAACpE,aAAa,CAACE,GAAG,CAAC,CAACC,GAAG,EAAE8I,GAAG,kBAClC1M,OAAA;0BAAAwJ,QAAA,GACG5F,GAAG,CAACC,YAAY,EAAC,IAAE,EAACD,GAAG,CAAClC,MAAM,EAAC,GAChC,EAACgL,GAAG,GAAG7E,OAAO,CAACpE,aAAa,CAACiC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE;wBAAA,GAF1CgH,GAAG;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAGR,CACP,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA,eACb,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACJ;gBACFS,WAAW,EAAGC,MAAM,iBAClBhL,OAAA,CAAC5B,SAAS;kBAAA,GACJ4M,MAAM;kBACVR,KAAK,EAAC,kBAAkB;kBACxBE,MAAM,EAAC,QAAQ;kBACfH,SAAS;kBACThG,KAAK,EAAEjC,gBAAgB,CAACoD,MAAM,KAAK,CAAC,IAAI,CAAC,CAAChD,MAAM,CAACmF,OAAQ;kBACzDoD,UAAU,EACRvI,MAAM,CAACmF,OAAO,KACbvF,gBAAgB,CAACoD,MAAM,KAAK,CAAC,GAAG,0CAA0C,GAC3E,gFAAgF;gBACjF;kBAAAyE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CACD;gBACFc,QAAQ,EAAE,CAAC/B,OAAO,IAAI/G,gBAAgB,CAACoD,MAAM,KAAK,CAAE;gBACpDiH,aAAa,EAAC,0BAA0B;gBACxCC,YAAY,EAAE;kBACZC,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAC9B;cAAE;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EAGA,CAAC1H,cAAc,iBACd5C,OAAA,CAAC3B,WAAW;gBACVkM,SAAS;gBACTG,MAAM,EAAC,QAAQ;gBACfnG,KAAK,EAAE,CAAC,CAAC7B,MAAM,CAAC1B,WAAY;gBAAAwI,QAAA,gBAE5BxJ,OAAA,CAAC1B,UAAU;kBAAAkL,QAAA,EAAC;gBAAK;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC9BtK,OAAA,CAACzB,MAAM;kBACL6I,KAAK,EAAE3G,OAAO,CAACO,WAAY;kBAC3B4J,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,aAAa,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;kBAClEoD,KAAK,EAAC,OAAO;kBACbY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,gBAEnBxJ,OAAA,CAACxB,QAAQ;oBAAC4I,KAAK,EAAC,EAAE;oBAAAoC,QAAA,eAChBxJ,OAAA;sBAAAwJ,QAAA,EAAI;oBAAc;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACVtI,QAAQ,CAAC2B,GAAG,CAAE0H,OAAO,iBACpBrL,OAAA,CAACxB,QAAQ;oBAAmB4I,KAAK,EAAEiE,OAAO,CAAChI,GAAI;oBAAAmG,QAAA,GAC5C6B,OAAO,CAAC0B,WAAW,EAAC,KAAG,EAAC1B,OAAO,CAACC,WAAW;kBAAA,GAD/BD,OAAO,CAAChI,GAAG;oBAAA8G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACR5H,MAAM,CAAC1B,WAAW,iBACjBhB,OAAA,CAACnB,cAAc;kBAAA2K,QAAA,EAAE9G,MAAM,CAAC1B;gBAAW;kBAAAmJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACrD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CACd,eAGDtK,OAAA,CAAC5B,SAAS;gBACRmM,SAAS;gBACTC,KAAK,EAAC,QAAQ;gBACdpD,KAAK,EAAExF,aAAc;gBACrBgJ,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,QAAQ,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;gBAC7D4F,MAAM,EAAE1F,gBAAiB;gBACzB2F,OAAO,EAAEzF,iBAAkB;gBAC3BkD,MAAM,EAAC,QAAQ;gBACfnG,KAAK,EAAE,CAAC,CAAC7B,MAAM,CAAC5B,MAAO;gBACvBmK,UAAU,EAAEvI,MAAM,CAAC5B,MAAM,IAAI,2DAA4D;gBACzF6J,UAAU,EAAE;kBACVtK,QAAQ,EAAE,CAACgJ,OAAO;kBAClB6D,UAAU,EAAE;oBAAEL,KAAK,EAAE;sBAAEM,SAAS,EAAE;oBAAQ;kBAAE;gBAC9C;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGFtK,OAAA,CAAC5B,SAAS;gBACRmM,SAAS;gBACTC,KAAK,EAAC,WAAW;gBACjBpD,KAAK,EAAE3G,OAAO,CAACe,SAAU;gBACzBoJ,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,WAAW,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;gBAChEsD,MAAM,EAAC,QAAQ;gBACf0C,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACR9I,KAAK,EAAE,CAAC,CAAC7B,MAAM,CAAClB,SAAU;gBAC1ByJ,UAAU,EAAEvI,MAAM,CAAClB,SAAU;gBAC7BmJ,UAAU,EAAE;kBAAEtK,QAAQ,EAAE,CAACgJ;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAGFtK,OAAA,CAAC5B,SAAS;gBACRmM,SAAS;gBACTC,KAAK,EAAC,aAAa;gBACnBpD,KAAK,EAAE3G,OAAO,CAACgB,WAAW,IAAI,EAAG;gBACjCmJ,QAAQ,EAAGM,CAAC,IAAKhE,iBAAiB,CAAC,aAAa,EAAEgE,CAAC,CAACC,MAAM,CAAC/D,KAAK,CAAE;gBAClEsD,MAAM,EAAC,QAAQ;gBACf0C,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACR1C,UAAU,EAAE;kBAAEtK,QAAQ,EAAE,CAACgJ;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPtK,OAAA,CAAC7B,IAAI;UAACyL,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAL,QAAA,eAChBxJ,OAAA,CAAClC,GAAG;YAACkM,EAAE,EAAE;cAAEyC,OAAO,EAAE,MAAM;cAAEa,cAAc,EAAE,UAAU;cAAEC,GAAG,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAhE,QAAA,GACrEjJ,QAAQ,iBACPP,OAAA,CAAC9B,MAAM;cACL6L,OAAO,EAAC,UAAU;cAClByC,KAAK,EAAC,WAAW;cACjBiB,SAAS,eAAEzN,OAAA,CAACX,UAAU;gBAAA8K,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BoD,OAAO,EAAEnN,QAAS;cAAAiJ,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAjB,OAAO,iBACNrJ,OAAA,CAAC9B,MAAM;cACL6L,OAAO,EAAC,WAAW;cACnByC,KAAK,EAAC,SAAS;cACfiB,SAAS,eAAEzN,OAAA,CAACjB,QAAQ;gBAAAoL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBoD,OAAO,EAAEjF,UAAW;cACpB2C,QAAQ,EAAE5I,OAAQ;cAAAgH,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAhB,UAAU,iBACTtJ,OAAA,CAAC9B,MAAM;cACL6L,OAAO,EAAC,WAAW;cACnByC,KAAK,EAAC,SAAS;cACfiB,SAAS,eAAEzN,OAAA,CAACf,WAAW;gBAAAkL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BoD,OAAO,EAAE3E,aAAc;cACvBqC,QAAQ,EAAE5I,OAAQ;cAAAgH,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAf,OAAO,iBACNvJ,OAAA,CAAC9B,MAAM;cACL6L,OAAO,EAAC,WAAW;cACnByC,KAAK,EAAC,SAAS;cACfiB,SAAS,eAAEzN,OAAA,CAACb,QAAQ;gBAAAgL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxBoD,OAAO,EAAExE,UAAW;cACpBkC,QAAQ,EAAE5I,OAAQ;cAAAgH,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEV,CAAC;AAAC9J,EAAA,CAzyBIL,sBAAsB;AAAAwN,EAAA,GAAtBxN,sBAAsB;AA2yB5B,eAAeA,sBAAsB;AAAC,IAAAwN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}