{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\nexport var ModeValues;\n(function (ModeValues) {\n  ModeValues[ModeValues[\"TERMINATOR\"] = 0] = \"TERMINATOR\";\n  ModeValues[ModeValues[\"NUMERIC\"] = 1] = \"NUMERIC\";\n  ModeValues[ModeValues[\"ALPHANUMERIC\"] = 2] = \"ALPHANUMERIC\";\n  ModeValues[ModeValues[\"STRUCTURED_APPEND\"] = 3] = \"STRUCTURED_APPEND\";\n  ModeValues[ModeValues[\"BYTE\"] = 4] = \"BYTE\";\n  ModeValues[ModeValues[\"ECI\"] = 5] = \"ECI\";\n  ModeValues[ModeValues[\"KANJI\"] = 6] = \"KANJI\";\n  ModeValues[ModeValues[\"FNC1_FIRST_POSITION\"] = 7] = \"FNC1_FIRST_POSITION\";\n  ModeValues[ModeValues[\"FNC1_SECOND_POSITION\"] = 8] = \"FNC1_SECOND_POSITION\";\n  /** See GBT 18284-2000; \"Hanzi\" is a transliteration of this mode name. */\n  ModeValues[ModeValues[\"HANZI\"] = 9] = \"HANZI\";\n})(ModeValues || (ModeValues = {}));\n/**\n * <p>See ISO 18004:2006, 6.4.1, Tables 2 and 3. This enum encapsulates the various modes in which\n * data can be encoded to bits in the QR code standard.</p>\n *\n * <AUTHOR> Owen\n */\nvar Mode = /** @class */function () {\n  function Mode(value, stringValue, characterCountBitsForVersions, bits /*int*/) {\n    this.value = value;\n    this.stringValue = stringValue;\n    this.characterCountBitsForVersions = characterCountBitsForVersions;\n    this.bits = bits;\n    Mode.FOR_BITS.set(bits, this);\n    Mode.FOR_VALUE.set(value, this);\n  }\n  /**\n   * @param bits four bits encoding a QR Code data mode\n   * @return Mode encoded by these bits\n   * @throws IllegalArgumentException if bits do not correspond to a known mode\n   */\n  Mode.forBits = function (bits /*int*/) {\n    var mode = Mode.FOR_BITS.get(bits);\n    if (undefined === mode) {\n      throw new IllegalArgumentException();\n    }\n    return mode;\n  };\n  /**\n   * @param version version in question\n   * @return number of bits used, in this QR Code symbol {@link Version}, to encode the\n   *         count of characters that will follow encoded in this Mode\n   */\n  Mode.prototype.getCharacterCountBits = function (version) {\n    var versionNumber = version.getVersionNumber();\n    var offset;\n    if (versionNumber <= 9) {\n      offset = 0;\n    } else if (versionNumber <= 26) {\n      offset = 1;\n    } else {\n      offset = 2;\n    }\n    return this.characterCountBitsForVersions[offset];\n  };\n  Mode.prototype.getValue = function () {\n    return this.value;\n  };\n  Mode.prototype.getBits = function () {\n    return this.bits;\n  };\n  Mode.prototype.equals = function (o) {\n    if (!(o instanceof Mode)) {\n      return false;\n    }\n    var other = o;\n    return this.value === other.value;\n  };\n  Mode.prototype.toString = function () {\n    return this.stringValue;\n  };\n  Mode.FOR_BITS = new Map();\n  Mode.FOR_VALUE = new Map();\n  Mode.TERMINATOR = new Mode(ModeValues.TERMINATOR, 'TERMINATOR', Int32Array.from([0, 0, 0]), 0x00); // Not really a mode...\n  Mode.NUMERIC = new Mode(ModeValues.NUMERIC, 'NUMERIC', Int32Array.from([10, 12, 14]), 0x01);\n  Mode.ALPHANUMERIC = new Mode(ModeValues.ALPHANUMERIC, 'ALPHANUMERIC', Int32Array.from([9, 11, 13]), 0x02);\n  Mode.STRUCTURED_APPEND = new Mode(ModeValues.STRUCTURED_APPEND, 'STRUCTURED_APPEND', Int32Array.from([0, 0, 0]), 0x03); // Not supported\n  Mode.BYTE = new Mode(ModeValues.BYTE, 'BYTE', Int32Array.from([8, 16, 16]), 0x04);\n  Mode.ECI = new Mode(ModeValues.ECI, 'ECI', Int32Array.from([0, 0, 0]), 0x07); // character counts don't apply\n  Mode.KANJI = new Mode(ModeValues.KANJI, 'KANJI', Int32Array.from([8, 10, 12]), 0x08);\n  Mode.FNC1_FIRST_POSITION = new Mode(ModeValues.FNC1_FIRST_POSITION, 'FNC1_FIRST_POSITION', Int32Array.from([0, 0, 0]), 0x05);\n  Mode.FNC1_SECOND_POSITION = new Mode(ModeValues.FNC1_SECOND_POSITION, 'FNC1_SECOND_POSITION', Int32Array.from([0, 0, 0]), 0x09);\n  /** See GBT 18284-2000; \"Hanzi\" is a transliteration of this mode name. */\n  Mode.HANZI = new Mode(ModeValues.HANZI, 'HANZI', Int32Array.from([8, 10, 12]), 0x0D);\n  return Mode;\n}();\nexport default Mode;", "map": {"version": 3, "names": ["IllegalArgumentException", "ModeValues", "Mode", "value", "stringValue", "characterCountBitsForVersions", "bits", "FOR_BITS", "set", "FOR_VALUE", "forBits", "mode", "get", "undefined", "prototype", "getCharacterCountBits", "version", "versionNumber", "getVersionNumber", "offset", "getValue", "getBits", "equals", "o", "other", "toString", "Map", "TERMINATOR", "Int32Array", "from", "NUMERIC", "ALPHANUMERIC", "STRUCTURED_APPEND", "BYTE", "ECI", "KANJI", "FNC1_FIRST_POSITION", "FNC1_SECOND_POSITION", "HANZI"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/decoder/Mode.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from '../../IllegalArgumentException';\nexport var ModeValues;\n(function (ModeValues) {\n    ModeValues[ModeValues[\"TERMINATOR\"] = 0] = \"TERMINATOR\";\n    ModeValues[ModeValues[\"NUMERIC\"] = 1] = \"NUMERIC\";\n    ModeValues[ModeValues[\"ALPHANUMERIC\"] = 2] = \"ALPHANUMERIC\";\n    ModeValues[ModeValues[\"STRUCTURED_APPEND\"] = 3] = \"STRUCTURED_APPEND\";\n    ModeValues[ModeValues[\"BYTE\"] = 4] = \"BYTE\";\n    ModeValues[ModeValues[\"ECI\"] = 5] = \"ECI\";\n    ModeValues[ModeValues[\"KANJI\"] = 6] = \"KANJI\";\n    ModeValues[ModeValues[\"FNC1_FIRST_POSITION\"] = 7] = \"FNC1_FIRST_POSITION\";\n    ModeValues[ModeValues[\"FNC1_SECOND_POSITION\"] = 8] = \"FNC1_SECOND_POSITION\";\n    /** See GBT 18284-2000; \"Hanzi\" is a transliteration of this mode name. */\n    ModeValues[ModeValues[\"HANZI\"] = 9] = \"HANZI\";\n})(ModeValues || (ModeValues = {}));\n/**\n * <p>See ISO 18004:2006, 6.4.1, Tables 2 and 3. This enum encapsulates the various modes in which\n * data can be encoded to bits in the QR code standard.</p>\n *\n * <AUTHOR> Owen\n */\nvar Mode = /** @class */ (function () {\n    function Mode(value, stringValue, characterCountBitsForVersions, bits /*int*/) {\n        this.value = value;\n        this.stringValue = stringValue;\n        this.characterCountBitsForVersions = characterCountBitsForVersions;\n        this.bits = bits;\n        Mode.FOR_BITS.set(bits, this);\n        Mode.FOR_VALUE.set(value, this);\n    }\n    /**\n     * @param bits four bits encoding a QR Code data mode\n     * @return Mode encoded by these bits\n     * @throws IllegalArgumentException if bits do not correspond to a known mode\n     */\n    Mode.forBits = function (bits /*int*/) {\n        var mode = Mode.FOR_BITS.get(bits);\n        if (undefined === mode) {\n            throw new IllegalArgumentException();\n        }\n        return mode;\n    };\n    /**\n     * @param version version in question\n     * @return number of bits used, in this QR Code symbol {@link Version}, to encode the\n     *         count of characters that will follow encoded in this Mode\n     */\n    Mode.prototype.getCharacterCountBits = function (version) {\n        var versionNumber = version.getVersionNumber();\n        var offset;\n        if (versionNumber <= 9) {\n            offset = 0;\n        }\n        else if (versionNumber <= 26) {\n            offset = 1;\n        }\n        else {\n            offset = 2;\n        }\n        return this.characterCountBitsForVersions[offset];\n    };\n    Mode.prototype.getValue = function () {\n        return this.value;\n    };\n    Mode.prototype.getBits = function () {\n        return this.bits;\n    };\n    Mode.prototype.equals = function (o) {\n        if (!(o instanceof Mode)) {\n            return false;\n        }\n        var other = o;\n        return this.value === other.value;\n    };\n    Mode.prototype.toString = function () {\n        return this.stringValue;\n    };\n    Mode.FOR_BITS = new Map();\n    Mode.FOR_VALUE = new Map();\n    Mode.TERMINATOR = new Mode(ModeValues.TERMINATOR, 'TERMINATOR', Int32Array.from([0, 0, 0]), 0x00); // Not really a mode...\n    Mode.NUMERIC = new Mode(ModeValues.NUMERIC, 'NUMERIC', Int32Array.from([10, 12, 14]), 0x01);\n    Mode.ALPHANUMERIC = new Mode(ModeValues.ALPHANUMERIC, 'ALPHANUMERIC', Int32Array.from([9, 11, 13]), 0x02);\n    Mode.STRUCTURED_APPEND = new Mode(ModeValues.STRUCTURED_APPEND, 'STRUCTURED_APPEND', Int32Array.from([0, 0, 0]), 0x03); // Not supported\n    Mode.BYTE = new Mode(ModeValues.BYTE, 'BYTE', Int32Array.from([8, 16, 16]), 0x04);\n    Mode.ECI = new Mode(ModeValues.ECI, 'ECI', Int32Array.from([0, 0, 0]), 0x07); // character counts don't apply\n    Mode.KANJI = new Mode(ModeValues.KANJI, 'KANJI', Int32Array.from([8, 10, 12]), 0x08);\n    Mode.FNC1_FIRST_POSITION = new Mode(ModeValues.FNC1_FIRST_POSITION, 'FNC1_FIRST_POSITION', Int32Array.from([0, 0, 0]), 0x05);\n    Mode.FNC1_SECOND_POSITION = new Mode(ModeValues.FNC1_SECOND_POSITION, 'FNC1_SECOND_POSITION', Int32Array.from([0, 0, 0]), 0x09);\n    /** See GBT 18284-2000; \"Hanzi\" is a transliteration of this mode name. */\n    Mode.HANZI = new Mode(ModeValues.HANZI, 'HANZI', Int32Array.from([8, 10, 12]), 0x0D);\n    return Mode;\n}());\nexport default Mode;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,wBAAwB,MAAM,gCAAgC;AACrE,OAAO,IAAIC,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACnBA,UAAU,CAACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACvDA,UAAU,CAACA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACjDA,UAAU,CAACA,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc;EAC3DA,UAAU,CAACA,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;EACrEA,UAAU,CAACA,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC3CA,UAAU,CAACA,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;EACzCA,UAAU,CAACA,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC7CA,UAAU,CAACA,UAAU,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB;EACzEA,UAAU,CAACA,UAAU,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,GAAG,sBAAsB;EAC3E;EACAA,UAAU,CAACA,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;AACjD,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,IAAI,GAAG,aAAe,YAAY;EAClC,SAASA,IAAIA,CAACC,KAAK,EAAEC,WAAW,EAAEC,6BAA6B,EAAEC,IAAI,CAAC,SAAS;IAC3E,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,6BAA6B,GAAGA,6BAA6B;IAClE,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChBJ,IAAI,CAACK,QAAQ,CAACC,GAAG,CAACF,IAAI,EAAE,IAAI,CAAC;IAC7BJ,IAAI,CAACO,SAAS,CAACD,GAAG,CAACL,KAAK,EAAE,IAAI,CAAC;EACnC;EACA;AACJ;AACA;AACA;AACA;EACID,IAAI,CAACQ,OAAO,GAAG,UAAUJ,IAAI,CAAC,SAAS;IACnC,IAAIK,IAAI,GAAGT,IAAI,CAACK,QAAQ,CAACK,GAAG,CAACN,IAAI,CAAC;IAClC,IAAIO,SAAS,KAAKF,IAAI,EAAE;MACpB,MAAM,IAAIX,wBAAwB,CAAC,CAAC;IACxC;IACA,OAAOW,IAAI;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIT,IAAI,CAACY,SAAS,CAACC,qBAAqB,GAAG,UAAUC,OAAO,EAAE;IACtD,IAAIC,aAAa,GAAGD,OAAO,CAACE,gBAAgB,CAAC,CAAC;IAC9C,IAAIC,MAAM;IACV,IAAIF,aAAa,IAAI,CAAC,EAAE;MACpBE,MAAM,GAAG,CAAC;IACd,CAAC,MACI,IAAIF,aAAa,IAAI,EAAE,EAAE;MAC1BE,MAAM,GAAG,CAAC;IACd,CAAC,MACI;MACDA,MAAM,GAAG,CAAC;IACd;IACA,OAAO,IAAI,CAACd,6BAA6B,CAACc,MAAM,CAAC;EACrD,CAAC;EACDjB,IAAI,CAACY,SAAS,CAACM,QAAQ,GAAG,YAAY;IAClC,OAAO,IAAI,CAACjB,KAAK;EACrB,CAAC;EACDD,IAAI,CAACY,SAAS,CAACO,OAAO,GAAG,YAAY;IACjC,OAAO,IAAI,CAACf,IAAI;EACpB,CAAC;EACDJ,IAAI,CAACY,SAAS,CAACQ,MAAM,GAAG,UAAUC,CAAC,EAAE;IACjC,IAAI,EAAEA,CAAC,YAAYrB,IAAI,CAAC,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAIsB,KAAK,GAAGD,CAAC;IACb,OAAO,IAAI,CAACpB,KAAK,KAAKqB,KAAK,CAACrB,KAAK;EACrC,CAAC;EACDD,IAAI,CAACY,SAAS,CAACW,QAAQ,GAAG,YAAY;IAClC,OAAO,IAAI,CAACrB,WAAW;EAC3B,CAAC;EACDF,IAAI,CAACK,QAAQ,GAAG,IAAImB,GAAG,CAAC,CAAC;EACzBxB,IAAI,CAACO,SAAS,GAAG,IAAIiB,GAAG,CAAC,CAAC;EAC1BxB,IAAI,CAACyB,UAAU,GAAG,IAAIzB,IAAI,CAACD,UAAU,CAAC0B,UAAU,EAAE,YAAY,EAAEC,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACnG3B,IAAI,CAAC4B,OAAO,GAAG,IAAI5B,IAAI,CAACD,UAAU,CAAC6B,OAAO,EAAE,SAAS,EAAEF,UAAU,CAACC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EAC3F3B,IAAI,CAAC6B,YAAY,GAAG,IAAI7B,IAAI,CAACD,UAAU,CAAC8B,YAAY,EAAE,cAAc,EAAEH,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EACzG3B,IAAI,CAAC8B,iBAAiB,GAAG,IAAI9B,IAAI,CAACD,UAAU,CAAC+B,iBAAiB,EAAE,mBAAmB,EAAEJ,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACxH3B,IAAI,CAAC+B,IAAI,GAAG,IAAI/B,IAAI,CAACD,UAAU,CAACgC,IAAI,EAAE,MAAM,EAAEL,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EACjF3B,IAAI,CAACgC,GAAG,GAAG,IAAIhC,IAAI,CAACD,UAAU,CAACiC,GAAG,EAAE,KAAK,EAAEN,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EAC9E3B,IAAI,CAACiC,KAAK,GAAG,IAAIjC,IAAI,CAACD,UAAU,CAACkC,KAAK,EAAE,OAAO,EAAEP,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EACpF3B,IAAI,CAACkC,mBAAmB,GAAG,IAAIlC,IAAI,CAACD,UAAU,CAACmC,mBAAmB,EAAE,qBAAqB,EAAER,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EAC5H3B,IAAI,CAACmC,oBAAoB,GAAG,IAAInC,IAAI,CAACD,UAAU,CAACoC,oBAAoB,EAAE,sBAAsB,EAAET,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;EAC/H;EACA3B,IAAI,CAACoC,KAAK,GAAG,IAAIpC,IAAI,CAACD,UAAU,CAACqC,KAAK,EAAE,OAAO,EAAEV,UAAU,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EACpF,OAAO3B,IAAI;AACf,CAAC,CAAC,CAAE;AACJ,eAAeA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}