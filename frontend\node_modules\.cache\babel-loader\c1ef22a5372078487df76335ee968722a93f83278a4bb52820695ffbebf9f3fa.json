{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nvar Barcode = function Barcode(data, options) {\n  _classCallCheck(this, Barcode);\n  this.data = data;\n  this.text = options.text || data;\n  this.options = options;\n};\nexports.default = Barcode;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "Barcode", "data", "options", "text", "default"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/Barcode.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Barcode = function Barcode(data, options) {\n\t_classCallCheck(this, Barcode);\n\n\tthis.data = data;\n\tthis.text = options.text || data;\n\tthis.options = options;\n};\n\nexports.default = Barcode;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,SAASC,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC7CN,eAAe,CAAC,IAAI,EAAEI,OAAO,CAAC;EAE9B,IAAI,CAACC,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACE,IAAI,GAAGD,OAAO,CAACC,IAAI,IAAIF,IAAI;EAChC,IAAI,CAACC,OAAO,GAAGA,OAAO;AACvB,CAAC;AAEDR,OAAO,CAACU,OAAO,GAAGJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}