{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Learning Projects\\\\uni-core-business-suite_v3\\\\frontend\\\\src\\\\pages\\\\ManageBankPaymentPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { Box, Card, CardContent, Typography, Button, CircularProgress, Alert } from '@mui/material';\nimport { ArrowBack as ArrowBackIcon } from '@mui/icons-material';\nimport BankPaymentVoucherForm from '../components/BankPaymentVoucherForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ManageBankPaymentPage = () => {\n  _s();\n  var _location$state, _location$state2;\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // Get voucherId from URL params or location state\n  const searchParams = new URLSearchParams(location.search);\n  const voucherId = searchParams.get('id') || ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.voucherId);\n  const readOnly = searchParams.get('view') === 'true' || ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.readOnly);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const handleSave = async savedVoucher => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Show success message\n      alert(voucherId ? 'Bank payment updated successfully!' : 'Bank payment created successfully!');\n\n      // Navigate back to bank payments list\n      navigate('/bank-payments');\n    } catch (error) {\n      console.error('Error saving bank payment:', error);\n      setError(error.message || 'Failed to save bank payment');\n      alert('Failed to save bank payment');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCancel = () => {\n    navigate('/bank-payments');\n  };\n  const getPageTitle = () => {\n    if (readOnly) return 'View Bank Payment';\n    if (voucherId) return 'Edit Bank Payment';\n    return 'Create New Bank Payment';\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBackIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 22\n        }, this),\n        onClick: handleCancel,\n        sx: {\n          mr: 2\n        },\n        children: \"Back to Bank Payments\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        children: getPageTitle()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 3\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 9\n    }, this), loading && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: /*#__PURE__*/_jsxDEV(BankPaymentVoucherForm, {\n          voucherId: voucherId,\n          readOnly: readOnly,\n          onSave: handleSave,\n          onCancel: handleCancel\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(ManageBankPaymentPage, \"IAeMJc036dN+iUb0+mhKQfsnqLg=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = ManageBankPaymentPage;\nexport default ManageBankPaymentPage;\nvar _c;\n$RefreshReg$(_c, \"ManageBankPaymentPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "ArrowBack", "ArrowBackIcon", "BankPaymentVoucherForm", "jsxDEV", "_jsxDEV", "ManageBankPaymentPage", "_s", "_location$state", "_location$state2", "navigate", "location", "searchParams", "URLSearchParams", "search", "voucherId", "get", "state", "readOnly", "loading", "setLoading", "error", "setError", "handleSave", "savedVoucher", "alert", "console", "message", "handleCancel", "getPageTitle", "sx", "p", "children", "display", "alignItems", "mb", "startIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "mr", "variant", "component", "severity", "justifyContent", "my", "onSave", "onCancel", "_c", "$RefreshReg$"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/pages/ManageBankPaymentPage.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport {\n  Box,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  CircularProgress,\n  Alert\n} from '@mui/material';\nimport { ArrowBack as ArrowBackIcon } from '@mui/icons-material';\nimport BankPaymentVoucherForm from '../components/BankPaymentVoucherForm';\n\nconst ManageBankPaymentPage = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  \n  // Get voucherId from URL params or location state\n  const searchParams = new URLSearchParams(location.search);\n  const voucherId = searchParams.get('id') || location.state?.voucherId;\n  const readOnly = searchParams.get('view') === 'true' || location.state?.readOnly;\n  \n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n\n  const handleSave = async (savedVoucher) => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // Show success message\n      alert(voucherId ? 'Bank payment updated successfully!' : 'Bank payment created successfully!');\n      \n      // Navigate back to bank payments list\n      navigate('/bank-payments');\n      \n    } catch (error) {\n      console.error('Error saving bank payment:', error);\n      setError(error.message || 'Failed to save bank payment');\n      alert('Failed to save bank payment');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancel = () => {\n    navigate('/bank-payments');\n  };\n\n  const getPageTitle = () => {\n    if (readOnly) return 'View Bank Payment';\n    if (voucherId) return 'Edit Bank Payment';\n    return 'Create New Bank Payment';\n  };\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n        <Button\n          startIcon={<ArrowBackIcon />}\n          onClick={handleCancel}\n          sx={{ mr: 2 }}\n        >\n          Back to Bank Payments\n        </Button>\n        <Typography variant=\"h4\" component=\"h1\">\n          {getPageTitle()}\n        </Typography>\n      </Box>\n\n      {/* Error Alert */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Loading Indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* Main Content */}\n      <Card>\n        <CardContent>\n          <BankPaymentVoucherForm\n            voucherId={voucherId}\n            readOnly={readOnly}\n            onSave={handleSave}\n            onCancel={handleCancel}\n          />\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default ManageBankPaymentPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SAASC,SAAS,IAAIC,aAAa,QAAQ,qBAAqB;AAChE,OAAOC,sBAAsB,MAAM,sCAAsC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EAClC,MAAMC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMmB,YAAY,GAAG,IAAIC,eAAe,CAACF,QAAQ,CAACG,MAAM,CAAC;EACzD,MAAMC,SAAS,GAAGH,YAAY,CAACI,GAAG,CAAC,IAAI,CAAC,MAAAR,eAAA,GAAIG,QAAQ,CAACM,KAAK,cAAAT,eAAA,uBAAdA,eAAA,CAAgBO,SAAS;EACrE,MAAMG,QAAQ,GAAGN,YAAY,CAACI,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM,MAAAP,gBAAA,GAAIE,QAAQ,CAACM,KAAK,cAAAR,gBAAA,uBAAdA,gBAAA,CAAgBS,QAAQ;EAEhF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8B,KAAK,EAAEC,QAAQ,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMgC,UAAU,GAAG,MAAOC,YAAY,IAAK;IACzC,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACAG,KAAK,CAACV,SAAS,GAAG,oCAAoC,GAAG,oCAAoC,CAAC;;MAE9F;MACAL,QAAQ,CAAC,gBAAgB,CAAC;IAE5B,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAACD,KAAK,CAACM,OAAO,IAAI,6BAA6B,CAAC;MACxDF,KAAK,CAAC,6BAA6B,CAAC;IACtC,CAAC,SAAS;MACRL,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACzBlB,QAAQ,CAAC,gBAAgB,CAAC;EAC5B,CAAC;EAED,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIX,QAAQ,EAAE,OAAO,mBAAmB;IACxC,IAAIH,SAAS,EAAE,OAAO,mBAAmB;IACzC,OAAO,yBAAyB;EAClC,CAAC;EAED,oBACEV,OAAA,CAACX,GAAG;IAACoC,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAEhB3B,OAAA,CAACX,GAAG;MAACoC,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACxD3B,OAAA,CAACP,MAAM;QACLsC,SAAS,eAAE/B,OAAA,CAACH,aAAa;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7BC,OAAO,EAAEb,YAAa;QACtBE,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,EACf;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnC,OAAA,CAACR,UAAU;QAAC8C,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,IAAI;QAAAZ,QAAA,EACpCH,YAAY,CAAC;MAAC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGLnB,KAAK,iBACJhB,OAAA,CAACL,KAAK;MAAC6C,QAAQ,EAAC,OAAO;MAACf,EAAE,EAAE;QAAEK,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,EACnCX;IAAK;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGArB,OAAO,iBACNd,OAAA,CAACX,GAAG;MAACoC,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEa,cAAc,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAf,QAAA,eAC5D3B,OAAA,CAACN,gBAAgB;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,eAGDnC,OAAA,CAACV,IAAI;MAAAqC,QAAA,eACH3B,OAAA,CAACT,WAAW;QAAAoC,QAAA,eACV3B,OAAA,CAACF,sBAAsB;UACrBY,SAAS,EAAEA,SAAU;UACrBG,QAAQ,EAAEA,QAAS;UACnB8B,MAAM,EAAEzB,UAAW;UACnB0B,QAAQ,EAAErB;QAAa;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjC,EAAA,CArFID,qBAAqB;EAAA,QACRd,WAAW,EACXC,WAAW;AAAA;AAAAyD,EAAA,GAFxB5C,qBAAqB;AAuF3B,eAAeA,qBAAqB;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}