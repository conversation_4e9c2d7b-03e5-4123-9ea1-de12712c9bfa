import React, { useState, useEffect } from "react";
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Tooltip
} from "@mui/material";
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Check as ApproveIcon,
  PostAdd as PostIcon
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import axios from "../utils/axiosConfig";
import { formatCurrency } from "../utils/numberUtils";
import Breadcrumb from "../components/Breadcrumb";

const BankPaymentsPage = () => {
  const navigate = useNavigate();
  const [vouchers, setVouchers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalVouchers, setTotalVouchers] = useState(0);

  useEffect(() => {
    fetchVouchers();
  }, [page, rowsPerPage]);

  const fetchVouchers = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/payment-vouchers?voucherType=BP&page=${page + 1}&limit=${rowsPerPage}`);
      setVouchers(response.data.vouchers);
      setTotalVouchers(response.data.pagination.totalVouchers);
    } catch (error) {
      console.error("Error fetching bank payment vouchers:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };



  const handleDeleteVoucher = async (voucherId) => {
    if (window.confirm("Are you sure you want to delete this voucher?")) {
      try {
        await axios.delete(`/api/payment-vouchers/${voucherId}`);
        fetchVouchers();
      } catch (error) {
        console.error("Error deleting voucher:", error);
        alert("Failed to delete voucher");
      }
    }
  };

  const handleApproveVoucher = async (voucherId) => {
    try {
      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);
      fetchVouchers();
    } catch (error) {
      console.error("Error approving voucher:", error);
      alert("Failed to approve voucher");
    }
  };

  const handlePostVoucher = async (voucherId) => {
    try {
      await axios.put(`/api/payment-vouchers/${voucherId}/post`);
      fetchVouchers();
    } catch (error) {
      console.error("Error posting voucher:", error);
      alert("Failed to post voucher");
    }
  };

  const getStatusChip = (status) => {
    const statusConfig = {
      Draft: { color: 'default', label: 'Draft' },
      Approved: { color: 'warning', label: 'Approved' },
      Posted: { color: 'success', label: 'Posted' },
      Cancelled: { color: 'error', label: 'Cancelled' }
    };
    
    const config = statusConfig[status] || { color: 'default', label: status };
    return <Chip size="small" color={config.color} label={config.label} />;
  };

  return (
    <Box>
      <Breadcrumb
        items={[
          { label: "Home", link: "/" },
          { label: "Accounting", link: "/accounting" },
          { label: "Bank Payments", link: "/accounting/bank-payments" }
        ]}
      />
      
      {/* Header */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <Box>
              <Typography variant="h4" gutterBottom>
                Bank Payments
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Record payments made through bank accounts
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/manage-bank-payment')}
            >
              New Bank Payment
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Voucher List */}
      <Card>
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Voucher No.</TableCell>
                  <TableCell>Date</TableCell>
                  <TableCell>Bank Account</TableCell>
                  <TableCell>Paid To</TableCell>
                  <TableCell align="right">Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">Loading...</TableCell>
                  </TableRow>
                ) : vouchers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">No bank payment vouchers found</TableCell>
                  </TableRow>
                ) : (
                  vouchers.map((voucher) => (
                    <TableRow key={voucher._id}>
                      <TableCell>{voucher.voucherNumber}</TableCell>
                      <TableCell>{new Date(voucher.transactionDate).toLocaleDateString()}</TableCell>
                      <TableCell>{voucher.fromAccountId?.accountName}</TableCell>
                      <TableCell>{voucher.toAccountId?.accountName}</TableCell>
                      <TableCell align="right">{formatCurrency(voucher.amount)}</TableCell>
                      <TableCell>{getStatusChip(voucher.status)}</TableCell>
                      <TableCell align="center">
                        <Tooltip title="View">
                          <IconButton
                            size="small"
                            color="primary"
                            onClick={() => navigate(`/manage-bank-payment?id=${voucher._id}&view=true`)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        
                        {voucher.status === 'Draft' && (
                          <>
                            <Tooltip title="Edit">
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => navigate(`/manage-bank-payment?id=${voucher._id}`)}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="Delete">
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleDeleteVoucher(voucher._id)}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                            
                            <Tooltip title="Approve">
                              <IconButton
                                size="small"
                                color="warning"
                                onClick={() => handleApproveVoucher(voucher._id)}
                              >
                                <ApproveIcon />
                              </IconButton>
                            </Tooltip>
                          </>
                        )}
                        
                        {voucher.status === 'Approved' && (
                          <Tooltip title="Post to Ledger">
                            <IconButton
                              size="small"
                              color="success"
                              onClick={() => handlePostVoucher(voucher._id)}
                            >
                              <PostIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={totalVouchers}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </CardContent>
      </Card>


    </Box>
  );
};

export default BankPaymentsPage; 