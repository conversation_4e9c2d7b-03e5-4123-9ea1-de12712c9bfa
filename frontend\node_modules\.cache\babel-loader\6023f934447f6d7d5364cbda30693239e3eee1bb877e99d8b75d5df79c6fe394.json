{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport GenericGFPoly from './GenericGFPoly';\nimport AbstractGenericGF from './AbstractGenericGF';\nimport Integer from '../../util/Integer';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nimport ArithmeticException from '../../ArithmeticException';\n/**\n * <p>This class contains utility methods for performing mathematical operations over\n * the Galois Fields. Operations use a given primitive polynomial in calculations.</p>\n *\n * <p>Throughout this package, elements of the GF are represented as an {@code int}\n * for convenience and speed (but at the cost of memory).\n * </p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> Olivier\n */\nvar GenericGF = /** @class */function (_super) {\n  __extends(GenericGF, _super);\n  /**\n   * Create a representation of GF(size) using the given primitive polynomial.\n   *\n   * @param primitive irreducible polynomial whose coefficients are represented by\n   *  the bits of an int, where the least-significant bit represents the constant\n   *  coefficient\n   * @param size the size of the field\n   * @param b the factor b in the generator polynomial can be 0- or 1-based\n   *  (g(x) = (x+a^b)(x+a^(b+1))...(x+a^(b+2t-1))).\n   *  In most cases it should be 1, but for QR code it is 0.\n   */\n  function GenericGF(primitive /*int*/, size /*int*/, generatorBase /*int*/) {\n    var _this = _super.call(this) || this;\n    _this.primitive = primitive;\n    _this.size = size;\n    _this.generatorBase = generatorBase;\n    var expTable = new Int32Array(size);\n    var x = 1;\n    for (var i = 0; i < size; i++) {\n      expTable[i] = x;\n      x *= 2; // we're assuming the generator alpha is 2\n      if (x >= size) {\n        x ^= primitive;\n        x &= size - 1;\n      }\n    }\n    _this.expTable = expTable;\n    var logTable = new Int32Array(size);\n    for (var i = 0; i < size - 1; i++) {\n      logTable[expTable[i]] = i;\n    }\n    _this.logTable = logTable;\n    // logTable[0] == 0 but this should never be used\n    _this.zero = new GenericGFPoly(_this, Int32Array.from([0]));\n    _this.one = new GenericGFPoly(_this, Int32Array.from([1]));\n    return _this;\n  }\n  GenericGF.prototype.getZero = function () {\n    return this.zero;\n  };\n  GenericGF.prototype.getOne = function () {\n    return this.one;\n  };\n  /**\n   * @return the monomial representing coefficient * x^degree\n   */\n  GenericGF.prototype.buildMonomial = function (degree /*int*/, coefficient /*int*/) {\n    if (degree < 0) {\n      throw new IllegalArgumentException();\n    }\n    if (coefficient === 0) {\n      return this.zero;\n    }\n    var coefficients = new Int32Array(degree + 1);\n    coefficients[0] = coefficient;\n    return new GenericGFPoly(this, coefficients);\n  };\n  /**\n   * @return multiplicative inverse of a\n   */\n  GenericGF.prototype.inverse = function (a /*int*/) {\n    if (a === 0) {\n      throw new ArithmeticException();\n    }\n    return this.expTable[this.size - this.logTable[a] - 1];\n  };\n  /**\n   * @return product of a and b in GF(size)\n   */\n  GenericGF.prototype.multiply = function (a /*int*/, b /*int*/) {\n    if (a === 0 || b === 0) {\n      return 0;\n    }\n    return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.size - 1)];\n  };\n  GenericGF.prototype.getSize = function () {\n    return this.size;\n  };\n  GenericGF.prototype.getGeneratorBase = function () {\n    return this.generatorBase;\n  };\n  /*@Override*/\n  GenericGF.prototype.toString = function () {\n    return 'GF(0x' + Integer.toHexString(this.primitive) + ',' + this.size + ')';\n  };\n  GenericGF.prototype.equals = function (o) {\n    return o === this;\n  };\n  GenericGF.AZTEC_DATA_12 = new GenericGF(0x1069, 4096, 1); // x^12 + x^6 + x^5 + x^3 + 1\n  GenericGF.AZTEC_DATA_10 = new GenericGF(0x409, 1024, 1); // x^10 + x^3 + 1\n  GenericGF.AZTEC_DATA_6 = new GenericGF(0x43, 64, 1); // x^6 + x + 1\n  GenericGF.AZTEC_PARAM = new GenericGF(0x13, 16, 1); // x^4 + x + 1\n  GenericGF.QR_CODE_FIELD_256 = new GenericGF(0x011d, 256, 0); // x^8 + x^4 + x^3 + x^2 + 1\n  GenericGF.DATA_MATRIX_FIELD_256 = new GenericGF(0x012d, 256, 1); // x^8 + x^5 + x^3 + x^2 + 1\n  GenericGF.AZTEC_DATA_8 = GenericGF.DATA_MATRIX_FIELD_256;\n  GenericGF.MAXICODE_FIELD_64 = GenericGF.AZTEC_DATA_6;\n  return GenericGF;\n}(AbstractGenericGF);\nexport default GenericGF;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "GenericGFPoly", "AbstractGenericGF", "Integer", "IllegalArgumentException", "ArithmeticException", "GenericGF", "_super", "primitive", "size", "generatorBase", "_this", "call", "expTable", "Int32Array", "x", "i", "logTable", "zero", "from", "one", "getZero", "getOne", "buildMonomial", "degree", "coefficient", "coefficients", "inverse", "a", "multiply", "getSize", "getGeneratorBase", "toString", "toHexString", "equals", "o", "AZTEC_DATA_12", "AZTEC_DATA_10", "AZTEC_DATA_6", "AZTEC_PARAM", "QR_CODE_FIELD_256", "DATA_MATRIX_FIELD_256", "AZTEC_DATA_8", "MAXICODE_FIELD_64"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/reedsolomon/GenericGF.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.common.reedsolomon {*/\nimport GenericGFPoly from './GenericGFPoly';\nimport AbstractGenericGF from './AbstractGenericGF';\nimport Integer from '../../util/Integer';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nimport ArithmeticException from '../../ArithmeticException';\n/**\n * <p>This class contains utility methods for performing mathematical operations over\n * the Galois Fields. Operations use a given primitive polynomial in calculations.</p>\n *\n * <p>Throughout this package, elements of the GF are represented as an {@code int}\n * for convenience and speed (but at the cost of memory).\n * </p>\n *\n * <AUTHOR> Owen\n * <AUTHOR> Olivier\n */\nvar GenericGF = /** @class */ (function (_super) {\n    __extends(GenericGF, _super);\n    /**\n     * Create a representation of GF(size) using the given primitive polynomial.\n     *\n     * @param primitive irreducible polynomial whose coefficients are represented by\n     *  the bits of an int, where the least-significant bit represents the constant\n     *  coefficient\n     * @param size the size of the field\n     * @param b the factor b in the generator polynomial can be 0- or 1-based\n     *  (g(x) = (x+a^b)(x+a^(b+1))...(x+a^(b+2t-1))).\n     *  In most cases it should be 1, but for QR code it is 0.\n     */\n    function GenericGF(primitive /*int*/, size /*int*/, generatorBase /*int*/) {\n        var _this = _super.call(this) || this;\n        _this.primitive = primitive;\n        _this.size = size;\n        _this.generatorBase = generatorBase;\n        var expTable = new Int32Array(size);\n        var x = 1;\n        for (var i = 0; i < size; i++) {\n            expTable[i] = x;\n            x *= 2; // we're assuming the generator alpha is 2\n            if (x >= size) {\n                x ^= primitive;\n                x &= size - 1;\n            }\n        }\n        _this.expTable = expTable;\n        var logTable = new Int32Array(size);\n        for (var i = 0; i < size - 1; i++) {\n            logTable[expTable[i]] = i;\n        }\n        _this.logTable = logTable;\n        // logTable[0] == 0 but this should never be used\n        _this.zero = new GenericGFPoly(_this, Int32Array.from([0]));\n        _this.one = new GenericGFPoly(_this, Int32Array.from([1]));\n        return _this;\n    }\n    GenericGF.prototype.getZero = function () {\n        return this.zero;\n    };\n    GenericGF.prototype.getOne = function () {\n        return this.one;\n    };\n    /**\n     * @return the monomial representing coefficient * x^degree\n     */\n    GenericGF.prototype.buildMonomial = function (degree /*int*/, coefficient /*int*/) {\n        if (degree < 0) {\n            throw new IllegalArgumentException();\n        }\n        if (coefficient === 0) {\n            return this.zero;\n        }\n        var coefficients = new Int32Array(degree + 1);\n        coefficients[0] = coefficient;\n        return new GenericGFPoly(this, coefficients);\n    };\n    /**\n     * @return multiplicative inverse of a\n     */\n    GenericGF.prototype.inverse = function (a /*int*/) {\n        if (a === 0) {\n            throw new ArithmeticException();\n        }\n        return this.expTable[this.size - this.logTable[a] - 1];\n    };\n    /**\n     * @return product of a and b in GF(size)\n     */\n    GenericGF.prototype.multiply = function (a /*int*/, b /*int*/) {\n        if (a === 0 || b === 0) {\n            return 0;\n        }\n        return this.expTable[(this.logTable[a] + this.logTable[b]) % (this.size - 1)];\n    };\n    GenericGF.prototype.getSize = function () {\n        return this.size;\n    };\n    GenericGF.prototype.getGeneratorBase = function () {\n        return this.generatorBase;\n    };\n    /*@Override*/\n    GenericGF.prototype.toString = function () {\n        return ('GF(0x' + Integer.toHexString(this.primitive) + ',' + this.size + ')');\n    };\n    GenericGF.prototype.equals = function (o) {\n        return o === this;\n    };\n    GenericGF.AZTEC_DATA_12 = new GenericGF(0x1069, 4096, 1); // x^12 + x^6 + x^5 + x^3 + 1\n    GenericGF.AZTEC_DATA_10 = new GenericGF(0x409, 1024, 1); // x^10 + x^3 + 1\n    GenericGF.AZTEC_DATA_6 = new GenericGF(0x43, 64, 1); // x^6 + x + 1\n    GenericGF.AZTEC_PARAM = new GenericGF(0x13, 16, 1); // x^4 + x + 1\n    GenericGF.QR_CODE_FIELD_256 = new GenericGF(0x011d, 256, 0); // x^8 + x^4 + x^3 + x^2 + 1\n    GenericGF.DATA_MATRIX_FIELD_256 = new GenericGF(0x012d, 256, 1); // x^8 + x^5 + x^3 + x^2 + 1\n    GenericGF.AZTEC_DATA_8 = GenericGF.DATA_MATRIX_FIELD_256;\n    GenericGF.MAXICODE_FIELD_64 = GenericGF.AZTEC_DATA_6;\n    return GenericGF;\n}(AbstractGenericGF));\nexport default GenericGF;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAOC,wBAAwB,MAAM,gCAAgC;AACrE,OAAOC,mBAAmB,MAAM,2BAA2B;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC7CpB,SAAS,CAACmB,SAAS,EAAEC,MAAM,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,SAASD,SAASA,CAACE,SAAS,CAAC,SAASC,IAAI,CAAC,SAASC,aAAa,CAAC,SAAS;IACvE,IAAIC,KAAK,GAAGJ,MAAM,CAACK,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACH,SAAS,GAAGA,SAAS;IAC3BG,KAAK,CAACF,IAAI,GAAGA,IAAI;IACjBE,KAAK,CAACD,aAAa,GAAGA,aAAa;IACnC,IAAIG,QAAQ,GAAG,IAAIC,UAAU,CAACL,IAAI,CAAC;IACnC,IAAIM,CAAC,GAAG,CAAC;IACT,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,EAAEO,CAAC,EAAE,EAAE;MAC3BH,QAAQ,CAACG,CAAC,CAAC,GAAGD,CAAC;MACfA,CAAC,IAAI,CAAC,CAAC,CAAC;MACR,IAAIA,CAAC,IAAIN,IAAI,EAAE;QACXM,CAAC,IAAIP,SAAS;QACdO,CAAC,IAAIN,IAAI,GAAG,CAAC;MACjB;IACJ;IACAE,KAAK,CAACE,QAAQ,GAAGA,QAAQ;IACzB,IAAII,QAAQ,GAAG,IAAIH,UAAU,CAACL,IAAI,CAAC;IACnC,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;MAC/BC,QAAQ,CAACJ,QAAQ,CAACG,CAAC,CAAC,CAAC,GAAGA,CAAC;IAC7B;IACAL,KAAK,CAACM,QAAQ,GAAGA,QAAQ;IACzB;IACAN,KAAK,CAACO,IAAI,GAAG,IAAIjB,aAAa,CAACU,KAAK,EAAEG,UAAU,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3DR,KAAK,CAACS,GAAG,GAAG,IAAInB,aAAa,CAACU,KAAK,EAAEG,UAAU,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,OAAOR,KAAK;EAChB;EACAL,SAAS,CAACP,SAAS,CAACsB,OAAO,GAAG,YAAY;IACtC,OAAO,IAAI,CAACH,IAAI;EACpB,CAAC;EACDZ,SAAS,CAACP,SAAS,CAACuB,MAAM,GAAG,YAAY;IACrC,OAAO,IAAI,CAACF,GAAG;EACnB,CAAC;EACD;AACJ;AACA;EACId,SAAS,CAACP,SAAS,CAACwB,aAAa,GAAG,UAAUC,MAAM,CAAC,SAASC,WAAW,CAAC,SAAS;IAC/E,IAAID,MAAM,GAAG,CAAC,EAAE;MACZ,MAAM,IAAIpB,wBAAwB,CAAC,CAAC;IACxC;IACA,IAAIqB,WAAW,KAAK,CAAC,EAAE;MACnB,OAAO,IAAI,CAACP,IAAI;IACpB;IACA,IAAIQ,YAAY,GAAG,IAAIZ,UAAU,CAACU,MAAM,GAAG,CAAC,CAAC;IAC7CE,YAAY,CAAC,CAAC,CAAC,GAAGD,WAAW;IAC7B,OAAO,IAAIxB,aAAa,CAAC,IAAI,EAAEyB,YAAY,CAAC;EAChD,CAAC;EACD;AACJ;AACA;EACIpB,SAAS,CAACP,SAAS,CAAC4B,OAAO,GAAG,UAAUC,CAAC,CAAC,SAAS;IAC/C,IAAIA,CAAC,KAAK,CAAC,EAAE;MACT,MAAM,IAAIvB,mBAAmB,CAAC,CAAC;IACnC;IACA,OAAO,IAAI,CAACQ,QAAQ,CAAC,IAAI,CAACJ,IAAI,GAAG,IAAI,CAACQ,QAAQ,CAACW,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1D,CAAC;EACD;AACJ;AACA;EACItB,SAAS,CAACP,SAAS,CAAC8B,QAAQ,GAAG,UAAUD,CAAC,CAAC,SAAStC,CAAC,CAAC,SAAS;IAC3D,IAAIsC,CAAC,KAAK,CAAC,IAAItC,CAAC,KAAK,CAAC,EAAE;MACpB,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACuB,QAAQ,CAAC,CAAC,IAAI,CAACI,QAAQ,CAACW,CAAC,CAAC,GAAG,IAAI,CAACX,QAAQ,CAAC3B,CAAC,CAAC,KAAK,IAAI,CAACmB,IAAI,GAAG,CAAC,CAAC,CAAC;EACjF,CAAC;EACDH,SAAS,CAACP,SAAS,CAAC+B,OAAO,GAAG,YAAY;IACtC,OAAO,IAAI,CAACrB,IAAI;EACpB,CAAC;EACDH,SAAS,CAACP,SAAS,CAACgC,gBAAgB,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACrB,aAAa;EAC7B,CAAC;EACD;EACAJ,SAAS,CAACP,SAAS,CAACiC,QAAQ,GAAG,YAAY;IACvC,OAAQ,OAAO,GAAG7B,OAAO,CAAC8B,WAAW,CAAC,IAAI,CAACzB,SAAS,CAAC,GAAG,GAAG,GAAG,IAAI,CAACC,IAAI,GAAG,GAAG;EACjF,CAAC;EACDH,SAAS,CAACP,SAAS,CAACmC,MAAM,GAAG,UAAUC,CAAC,EAAE;IACtC,OAAOA,CAAC,KAAK,IAAI;EACrB,CAAC;EACD7B,SAAS,CAAC8B,aAAa,GAAG,IAAI9B,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1DA,SAAS,CAAC+B,aAAa,GAAG,IAAI/B,SAAS,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;EACzDA,SAAS,CAACgC,YAAY,GAAG,IAAIhC,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACrDA,SAAS,CAACiC,WAAW,GAAG,IAAIjC,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EACpDA,SAAS,CAACkC,iBAAiB,GAAG,IAAIlC,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC7DA,SAAS,CAACmC,qBAAqB,GAAG,IAAInC,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EACjEA,SAAS,CAACoC,YAAY,GAAGpC,SAAS,CAACmC,qBAAqB;EACxDnC,SAAS,CAACqC,iBAAiB,GAAGrC,SAAS,CAACgC,YAAY;EACpD,OAAOhC,SAAS;AACpB,CAAC,CAACJ,iBAAiB,CAAE;AACrB,eAAeI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}