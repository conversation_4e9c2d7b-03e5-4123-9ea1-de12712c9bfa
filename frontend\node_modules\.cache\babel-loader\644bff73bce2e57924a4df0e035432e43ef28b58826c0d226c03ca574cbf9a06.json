{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nvar __generator = this && this.__generator || function (thisArg, body) {\n  var _ = {\n      label: 0,\n      sent: function () {\n        if (t[0] & 1) throw t[1];\n        return t[1];\n      },\n      trys: [],\n      ops: []\n    },\n    f,\n    y,\n    t,\n    g;\n  return g = {\n    next: verb(0),\n    \"throw\": verb(1),\n    \"return\": verb(2)\n  }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function () {\n    return this;\n  }), g;\n  function verb(n) {\n    return function (v) {\n      return step([n, v]);\n    };\n  }\n  function step(op) {\n    if (f) throw new TypeError(\"Generator is already executing.\");\n    while (_) try {\n      if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n      if (y = 0, t) op = [op[0] & 2, t.value];\n      switch (op[0]) {\n        case 0:\n        case 1:\n          t = op;\n          break;\n        case 4:\n          _.label++;\n          return {\n            value: op[1],\n            done: false\n          };\n        case 5:\n          _.label++;\n          y = op[1];\n          op = [0];\n          continue;\n        case 7:\n          op = _.ops.pop();\n          _.trys.pop();\n          continue;\n        default:\n          if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {\n            _ = 0;\n            continue;\n          }\n          if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {\n            _.label = op[1];\n            break;\n          }\n          if (op[0] === 6 && _.label < t[1]) {\n            _.label = t[1];\n            t = op;\n            break;\n          }\n          if (t && _.label < t[2]) {\n            _.label = t[2];\n            _.ops.push(op);\n            break;\n          }\n          if (t[2]) _.ops.pop();\n          _.trys.pop();\n          continue;\n      }\n      op = body.call(thisArg, _);\n    } catch (e) {\n      op = [6, e];\n      y = 0;\n    } finally {\n      f = t = 0;\n    }\n    if (op[0] & 5) throw op[1];\n    return {\n      value: op[0] ? op[1] : void 0,\n      done: true\n    };\n  }\n};\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport ArgumentException from '../core/ArgumentException';\nimport BinaryBitmap from '../core/BinaryBitmap';\nimport ChecksumException from '../core/ChecksumException';\nimport HybridBinarizer from '../core/common/HybridBinarizer';\nimport FormatException from '../core/FormatException';\nimport NotFoundException from '../core/NotFoundException';\nimport { HTMLCanvasElementLuminanceSource } from './HTMLCanvasElementLuminanceSource';\nimport { VideoInputDevice } from './VideoInputDevice';\n/**\n * @deprecated Moving to @zxing/browser\n *\n * Base class for browser code reader.\n */\nvar BrowserCodeReader = /** @class */function () {\n  /**\n   * Creates an instance of BrowserCodeReader.\n   * @param {Reader} reader The reader instance to decode the barcode\n   * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent successful decode tries\n   *\n   * @memberOf BrowserCodeReader\n   */\n  function BrowserCodeReader(reader, timeBetweenScansMillis, _hints) {\n    if (timeBetweenScansMillis === void 0) {\n      timeBetweenScansMillis = 500;\n    }\n    this.reader = reader;\n    this.timeBetweenScansMillis = timeBetweenScansMillis;\n    this._hints = _hints;\n    /**\n     * This will break the loop.\n     */\n    this._stopContinuousDecode = false;\n    /**\n     * This will break the loop.\n     */\n    this._stopAsyncDecode = false;\n    /**\n     * Delay time between decode attempts made by the scanner.\n     */\n    this._timeBetweenDecodingAttempts = 0;\n  }\n  Object.defineProperty(BrowserCodeReader.prototype, \"hasNavigator\", {\n    /**\n     * If navigator is present.\n     */\n    get: function () {\n      return typeof navigator !== 'undefined';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(BrowserCodeReader.prototype, \"isMediaDevicesSuported\", {\n    /**\n     * If mediaDevices under navigator is supported.\n     */\n    get: function () {\n      return this.hasNavigator && !!navigator.mediaDevices;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(BrowserCodeReader.prototype, \"canEnumerateDevices\", {\n    /**\n     * If enumerateDevices under navigator is supported.\n     */\n    get: function () {\n      return !!(this.isMediaDevicesSuported && navigator.mediaDevices.enumerateDevices);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(BrowserCodeReader.prototype, \"timeBetweenDecodingAttempts\", {\n    /** Time between two decoding tries in milli seconds. */\n    get: function () {\n      return this._timeBetweenDecodingAttempts;\n    },\n    /**\n     * Change the time span the decoder waits between two decoding tries.\n     *\n     * @param {number} millis Time between two decoding tries in milli seconds.\n     */\n    set: function (millis) {\n      this._timeBetweenDecodingAttempts = millis < 0 ? 0 : millis;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(BrowserCodeReader.prototype, \"hints\", {\n    /**\n     * Sets the hints.\n     */\n    get: function () {\n      return this._hints;\n    },\n    /**\n     * Sets the hints.\n     */\n    set: function (hints) {\n      this._hints = hints || null;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * Lists all the available video input devices.\n   */\n  BrowserCodeReader.prototype.listVideoInputDevices = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var devices, videoDevices, devices_1, devices_1_1, device, kind, deviceId, label, groupId, videoDevice;\n      var e_1, _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            if (!this.hasNavigator) {\n              throw new Error(\"Can't enumerate devices, navigator is not present.\");\n            }\n            if (!this.canEnumerateDevices) {\n              throw new Error(\"Can't enumerate devices, method not supported.\");\n            }\n            return [4 /*yield*/, navigator.mediaDevices.enumerateDevices()];\n          case 1:\n            devices = _b.sent();\n            videoDevices = [];\n            try {\n              for (devices_1 = __values(devices), devices_1_1 = devices_1.next(); !devices_1_1.done; devices_1_1 = devices_1.next()) {\n                device = devices_1_1.value;\n                kind = device.kind === 'video' ? 'videoinput' : device.kind;\n                if (kind !== 'videoinput') {\n                  continue;\n                }\n                deviceId = device.deviceId || device.id;\n                label = device.label || \"Video device \" + (videoDevices.length + 1);\n                groupId = device.groupId;\n                videoDevice = {\n                  deviceId: deviceId,\n                  label: label,\n                  kind: kind,\n                  groupId: groupId\n                };\n                videoDevices.push(videoDevice);\n              }\n            } catch (e_1_1) {\n              e_1 = {\n                error: e_1_1\n              };\n            } finally {\n              try {\n                if (devices_1_1 && !devices_1_1.done && (_a = devices_1.return)) _a.call(devices_1);\n              } finally {\n                if (e_1) throw e_1.error;\n              }\n            }\n            return [2 /*return*/, videoDevices];\n        }\n      });\n    });\n  };\n  /**\n   * Obtain the list of available devices with type 'videoinput'.\n   *\n   * @returns {Promise<VideoInputDevice[]>} an array of available video input devices\n   *\n   * @memberOf BrowserCodeReader\n   *\n   * @deprecated Use `listVideoInputDevices` instead.\n   */\n  BrowserCodeReader.prototype.getVideoInputDevices = function () {\n    return __awaiter(this, void 0, void 0, function () {\n      var devices;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.listVideoInputDevices()];\n          case 1:\n            devices = _a.sent();\n            return [2 /*return*/, devices.map(function (d) {\n              return new VideoInputDevice(d.deviceId, d.label);\n            })];\n        }\n      });\n    });\n  };\n  /**\n   * Let's you find a device using it's Id.\n   */\n  BrowserCodeReader.prototype.findDeviceById = function (deviceId) {\n    return __awaiter(this, void 0, void 0, function () {\n      var devices;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.listVideoInputDevices()];\n          case 1:\n            devices = _a.sent();\n            if (!devices) {\n              return [2 /*return*/, null];\n            }\n            return [2 /*return*/, devices.find(function (x) {\n              return x.deviceId === deviceId;\n            })];\n        }\n      });\n    });\n  };\n  /**\n   * Decodes the barcode from the device specified by deviceId while showing the video in the specified video element.\n   *\n   * @param deviceId the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n   * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n   * @returns The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   *\n   * @deprecated Use `decodeOnceFromVideoDevice` instead.\n   */\n  BrowserCodeReader.prototype.decodeFromInputVideoDevice = function (deviceId, videoSource) {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.decodeOnceFromVideoDevice(deviceId, videoSource)];\n          case 1:\n            return [2 /*return*/, _a.sent()];\n        }\n      });\n    });\n  };\n  /**\n   * In one attempt, tries to decode the barcode from the device specified by deviceId while showing the video in the specified video element.\n   *\n   * @param deviceId the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n   * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n   * @returns The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.decodeOnceFromVideoDevice = function (deviceId, videoSource) {\n    return __awaiter(this, void 0, void 0, function () {\n      var videoConstraints, constraints;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            this.reset();\n            if (!deviceId) {\n              videoConstraints = {\n                facingMode: 'environment'\n              };\n            } else {\n              videoConstraints = {\n                deviceId: {\n                  exact: deviceId\n                }\n              };\n            }\n            constraints = {\n              video: videoConstraints\n            };\n            return [4 /*yield*/, this.decodeOnceFromConstraints(constraints, videoSource)];\n          case 1:\n            return [2 /*return*/, _a.sent()];\n        }\n      });\n    });\n  };\n  /**\n   * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n   *\n   * @param constraints the media stream constraints to get s valid media stream to decode from\n   * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n   * @returns The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.decodeOnceFromConstraints = function (constraints, videoSource) {\n    return __awaiter(this, void 0, void 0, function () {\n      var stream;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];\n          case 1:\n            stream = _a.sent();\n            return [4 /*yield*/, this.decodeOnceFromStream(stream, videoSource)];\n          case 2:\n            return [2 /*return*/, _a.sent()];\n        }\n      });\n    });\n  };\n  /**\n   * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n   *\n   * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n   * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n   * @returns {Promise<Result>} The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.decodeOnceFromStream = function (stream, videoSource) {\n    return __awaiter(this, void 0, void 0, function () {\n      var video, result;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            this.reset();\n            return [4 /*yield*/, this.attachStreamToVideo(stream, videoSource)];\n          case 1:\n            video = _a.sent();\n            return [4 /*yield*/, this.decodeOnce(video)];\n          case 2:\n            result = _a.sent();\n            return [2 /*return*/, result];\n        }\n      });\n    });\n  };\n  /**\n   * Continuously decodes the barcode from the device specified by device while showing the video in the specified video element.\n   *\n   * @param {string|null} [deviceId] the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n   * @param {string|HTMLVideoElement|null} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n   * @returns {Promise<void>}\n   *\n   * @memberOf BrowserCodeReader\n   *\n   * @deprecated Use `decodeFromVideoDevice` instead.\n   */\n  BrowserCodeReader.prototype.decodeFromInputVideoDeviceContinuously = function (deviceId, videoSource, callbackFn) {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, this.decodeFromVideoDevice(deviceId, videoSource, callbackFn)];\n          case 1:\n            return [2 /*return*/, _a.sent()];\n        }\n      });\n    });\n  };\n  /**\n   * Continuously tries to decode the barcode from the device specified by device while showing the video in the specified video element.\n   *\n   * @param {string|null} [deviceId] the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n   * @param {string|HTMLVideoElement|null} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n   * @returns {Promise<void>}\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.decodeFromVideoDevice = function (deviceId, videoSource, callbackFn) {\n    return __awaiter(this, void 0, void 0, function () {\n      var videoConstraints, constraints;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            if (!deviceId) {\n              videoConstraints = {\n                facingMode: 'environment'\n              };\n            } else {\n              videoConstraints = {\n                deviceId: {\n                  exact: deviceId\n                }\n              };\n            }\n            constraints = {\n              video: videoConstraints\n            };\n            return [4 /*yield*/, this.decodeFromConstraints(constraints, videoSource, callbackFn)];\n          case 1:\n            return [2 /*return*/, _a.sent()];\n        }\n      });\n    });\n  };\n  /**\n   * Continuously tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n   *\n   * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n   * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n   * @returns {Promise<Result>} The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.decodeFromConstraints = function (constraints, videoSource, callbackFn) {\n    return __awaiter(this, void 0, void 0, function () {\n      var stream;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];\n          case 1:\n            stream = _a.sent();\n            return [4 /*yield*/, this.decodeFromStream(stream, videoSource, callbackFn)];\n          case 2:\n            return [2 /*return*/, _a.sent()];\n        }\n      });\n    });\n  };\n  /**\n   * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n   *\n   * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n   * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n   * @returns {Promise<Result>} The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.decodeFromStream = function (stream, videoSource, callbackFn) {\n    return __awaiter(this, void 0, void 0, function () {\n      var video;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            this.reset();\n            return [4 /*yield*/, this.attachStreamToVideo(stream, videoSource)];\n          case 1:\n            video = _a.sent();\n            return [4 /*yield*/, this.decodeContinuously(video, callbackFn)];\n          case 2:\n            return [2 /*return*/, _a.sent()];\n        }\n      });\n    });\n  };\n  /**\n   * Breaks the decoding loop.\n   */\n  BrowserCodeReader.prototype.stopAsyncDecode = function () {\n    this._stopAsyncDecode = true;\n  };\n  /**\n   * Breaks the decoding loop.\n   */\n  BrowserCodeReader.prototype.stopContinuousDecode = function () {\n    this._stopContinuousDecode = true;\n  };\n  /**\n   * Sets the new stream and request a new decoding-with-delay.\n   *\n   * @param stream The stream to be shown in the video element.\n   * @param decodeFn A callback for the decode method.\n   */\n  BrowserCodeReader.prototype.attachStreamToVideo = function (stream, videoSource) {\n    return __awaiter(this, void 0, void 0, function () {\n      var videoElement;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            videoElement = this.prepareVideoElement(videoSource);\n            this.addVideoSource(videoElement, stream);\n            this.videoElement = videoElement;\n            this.stream = stream;\n            return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n          case 1:\n            _a.sent();\n            return [2 /*return*/, videoElement];\n        }\n      });\n    });\n  };\n  /**\n   *\n   * @param videoElement\n   */\n  BrowserCodeReader.prototype.playVideoOnLoadAsync = function (videoElement) {\n    var _this = this;\n    return new Promise(function (resolve, reject) {\n      return _this.playVideoOnLoad(videoElement, function () {\n        return resolve();\n      });\n    });\n  };\n  /**\n   * Binds listeners and callbacks to the videoElement.\n   *\n   * @param element\n   * @param callbackFn\n   */\n  BrowserCodeReader.prototype.playVideoOnLoad = function (element, callbackFn) {\n    var _this = this;\n    this.videoEndedListener = function () {\n      return _this.stopStreams();\n    };\n    this.videoCanPlayListener = function () {\n      return _this.tryPlayVideo(element);\n    };\n    element.addEventListener('ended', this.videoEndedListener);\n    element.addEventListener('canplay', this.videoCanPlayListener);\n    element.addEventListener('playing', callbackFn);\n    // if canplay was already fired, we won't know when to play, so just give it a try\n    this.tryPlayVideo(element);\n  };\n  /**\n   * Checks if the given video element is currently playing.\n   */\n  BrowserCodeReader.prototype.isVideoPlaying = function (video) {\n    return video.currentTime > 0 && !video.paused && !video.ended && video.readyState > 2;\n  };\n  /**\n   * Just tries to play the video and logs any errors.\n   * The play call is only made is the video is not already playing.\n   */\n  BrowserCodeReader.prototype.tryPlayVideo = function (videoElement) {\n    return __awaiter(this, void 0, void 0, function () {\n      var _a;\n      return __generator(this, function (_b) {\n        switch (_b.label) {\n          case 0:\n            if (this.isVideoPlaying(videoElement)) {\n              console.warn('Trying to play video that is already playing.');\n              return [2 /*return*/];\n            }\n            _b.label = 1;\n          case 1:\n            _b.trys.push([1, 3,, 4]);\n            return [4 /*yield*/, videoElement.play()];\n          case 2:\n            _b.sent();\n            return [3 /*break*/, 4];\n          case 3:\n            _a = _b.sent();\n            console.warn('It was not possible to play the video.');\n            return [3 /*break*/, 4];\n          case 4:\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  /**\n   * Searches and validates a media element.\n   */\n  BrowserCodeReader.prototype.getMediaElement = function (mediaElementId, type) {\n    var mediaElement = document.getElementById(mediaElementId);\n    if (!mediaElement) {\n      throw new ArgumentException(\"element with id '\" + mediaElementId + \"' not found\");\n    }\n    if (mediaElement.nodeName.toLowerCase() !== type.toLowerCase()) {\n      throw new ArgumentException(\"element with id '\" + mediaElementId + \"' must be an \" + type + \" element\");\n    }\n    return mediaElement;\n  };\n  /**\n   * Decodes the barcode from an image.\n   *\n   * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n   * @param {string} [url]\n   * @returns {Promise<Result>} The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.decodeFromImage = function (source, url) {\n    if (!source && !url) {\n      throw new ArgumentException('either imageElement with a src set or an url must be provided');\n    }\n    if (url && !source) {\n      return this.decodeFromImageUrl(url);\n    }\n    return this.decodeFromImageElement(source);\n  };\n  /**\n   * Decodes the barcode from a video.\n   *\n   * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n   * @param {string} [url]\n   * @returns {Promise<Result>} The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.decodeFromVideo = function (source, url) {\n    if (!source && !url) {\n      throw new ArgumentException('Either an element with a src set or an URL must be provided');\n    }\n    if (url && !source) {\n      return this.decodeFromVideoUrl(url);\n    }\n    return this.decodeFromVideoElement(source);\n  };\n  /**\n   * Decodes continuously the barcode from a video.\n   *\n   * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n   * @param {string} [url]\n   * @returns {Promise<Result>} The decoding result.\n   *\n   * @memberOf BrowserCodeReader\n   *\n   * @experimental\n   */\n  BrowserCodeReader.prototype.decodeFromVideoContinuously = function (source, url, callbackFn) {\n    if (undefined === source && undefined === url) {\n      throw new ArgumentException('Either an element with a src set or an URL must be provided');\n    }\n    if (url && !source) {\n      return this.decodeFromVideoUrlContinuously(url, callbackFn);\n    }\n    return this.decodeFromVideoElementContinuously(source, callbackFn);\n  };\n  /**\n   * Decodes something from an image HTML element.\n   */\n  BrowserCodeReader.prototype.decodeFromImageElement = function (source) {\n    if (!source) {\n      throw new ArgumentException('An image element must be provided.');\n    }\n    this.reset();\n    var element = this.prepareImageElement(source);\n    this.imageElement = element;\n    var task;\n    if (this.isImageLoaded(element)) {\n      task = this.decodeOnce(element, false, true);\n    } else {\n      task = this._decodeOnLoadImage(element);\n    }\n    return task;\n  };\n  /**\n   * Decodes something from an image HTML element.\n   */\n  BrowserCodeReader.prototype.decodeFromVideoElement = function (source) {\n    var element = this._decodeFromVideoElementSetup(source);\n    return this._decodeOnLoadVideo(element);\n  };\n  /**\n   * Decodes something from an image HTML element.\n   */\n  BrowserCodeReader.prototype.decodeFromVideoElementContinuously = function (source, callbackFn) {\n    var element = this._decodeFromVideoElementSetup(source);\n    return this._decodeOnLoadVideoContinuously(element, callbackFn);\n  };\n  /**\n   * Sets up the video source so it can be decoded when loaded.\n   *\n   * @param source The video source element.\n   */\n  BrowserCodeReader.prototype._decodeFromVideoElementSetup = function (source) {\n    if (!source) {\n      throw new ArgumentException('A video element must be provided.');\n    }\n    this.reset();\n    var element = this.prepareVideoElement(source);\n    // defines the video element before starts decoding\n    this.videoElement = element;\n    return element;\n  };\n  /**\n   * Decodes an image from a URL.\n   */\n  BrowserCodeReader.prototype.decodeFromImageUrl = function (url) {\n    if (!url) {\n      throw new ArgumentException('An URL must be provided.');\n    }\n    this.reset();\n    var element = this.prepareImageElement();\n    this.imageElement = element;\n    var decodeTask = this._decodeOnLoadImage(element);\n    element.src = url;\n    return decodeTask;\n  };\n  /**\n   * Decodes an image from a URL.\n   */\n  BrowserCodeReader.prototype.decodeFromVideoUrl = function (url) {\n    if (!url) {\n      throw new ArgumentException('An URL must be provided.');\n    }\n    this.reset();\n    // creates a new element\n    var element = this.prepareVideoElement();\n    var decodeTask = this.decodeFromVideoElement(element);\n    element.src = url;\n    return decodeTask;\n  };\n  /**\n   * Decodes an image from a URL.\n   *\n   * @experimental\n   */\n  BrowserCodeReader.prototype.decodeFromVideoUrlContinuously = function (url, callbackFn) {\n    if (!url) {\n      throw new ArgumentException('An URL must be provided.');\n    }\n    this.reset();\n    // creates a new element\n    var element = this.prepareVideoElement();\n    var decodeTask = this.decodeFromVideoElementContinuously(element, callbackFn);\n    element.src = url;\n    return decodeTask;\n  };\n  BrowserCodeReader.prototype._decodeOnLoadImage = function (element) {\n    var _this = this;\n    return new Promise(function (resolve, reject) {\n      _this.imageLoadedListener = function () {\n        return _this.decodeOnce(element, false, true).then(resolve, reject);\n      };\n      element.addEventListener('load', _this.imageLoadedListener);\n    });\n  };\n  BrowserCodeReader.prototype._decodeOnLoadVideo = function (videoElement) {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            // plays the video\n            return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n          case 1:\n            // plays the video\n            _a.sent();\n            return [4 /*yield*/, this.decodeOnce(videoElement)];\n          case 2:\n            // starts decoding after played the video\n            return [2 /*return*/, _a.sent()];\n        }\n      });\n    });\n  };\n  BrowserCodeReader.prototype._decodeOnLoadVideoContinuously = function (videoElement, callbackFn) {\n    return __awaiter(this, void 0, void 0, function () {\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            // plays the video\n            return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n          case 1:\n            // plays the video\n            _a.sent();\n            // starts decoding after played the video\n            this.decodeContinuously(videoElement, callbackFn);\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  BrowserCodeReader.prototype.isImageLoaded = function (img) {\n    // During the onload event, IE correctly identifies any images that\n    // weren’t downloaded as not complete. Others should too. Gecko-based\n    // browsers act like NS4 in that they report this incorrectly.\n    if (!img.complete) {\n      return false;\n    }\n    // However, they do have two very useful properties: naturalWidth and\n    // naturalHeight. These give the true size of the image. If it failed\n    // to load, either of these should be zero.\n    if (img.naturalWidth === 0) {\n      return false;\n    }\n    // No other way of checking: assume it’s ok.\n    return true;\n  };\n  BrowserCodeReader.prototype.prepareImageElement = function (imageSource) {\n    var imageElement;\n    if (typeof imageSource === 'undefined') {\n      imageElement = document.createElement('img');\n      imageElement.width = 200;\n      imageElement.height = 200;\n    }\n    if (typeof imageSource === 'string') {\n      imageElement = this.getMediaElement(imageSource, 'img');\n    }\n    if (imageSource instanceof HTMLImageElement) {\n      imageElement = imageSource;\n    }\n    return imageElement;\n  };\n  /**\n   * Sets a HTMLVideoElement for scanning or creates a new one.\n   *\n   * @param videoSource The HTMLVideoElement to be set.\n   */\n  BrowserCodeReader.prototype.prepareVideoElement = function (videoSource) {\n    var videoElement;\n    if (!videoSource && typeof document !== 'undefined') {\n      videoElement = document.createElement('video');\n      videoElement.width = 200;\n      videoElement.height = 200;\n    }\n    if (typeof videoSource === 'string') {\n      videoElement = this.getMediaElement(videoSource, 'video');\n    }\n    if (videoSource instanceof HTMLVideoElement) {\n      videoElement = videoSource;\n    }\n    // Needed for iOS 11\n    videoElement.setAttribute('autoplay', 'true');\n    videoElement.setAttribute('muted', 'true');\n    videoElement.setAttribute('playsinline', 'true');\n    return videoElement;\n  };\n  /**\n   * Tries to decode from the video input until it finds some value.\n   */\n  BrowserCodeReader.prototype.decodeOnce = function (element, retryIfNotFound, retryIfChecksumOrFormatError) {\n    var _this = this;\n    if (retryIfNotFound === void 0) {\n      retryIfNotFound = true;\n    }\n    if (retryIfChecksumOrFormatError === void 0) {\n      retryIfChecksumOrFormatError = true;\n    }\n    this._stopAsyncDecode = false;\n    var loop = function (resolve, reject) {\n      if (_this._stopAsyncDecode) {\n        reject(new NotFoundException('Video stream has ended before any code could be detected.'));\n        _this._stopAsyncDecode = undefined;\n        return;\n      }\n      try {\n        var result = _this.decode(element);\n        resolve(result);\n      } catch (e) {\n        var ifNotFound = retryIfNotFound && e instanceof NotFoundException;\n        var isChecksumOrFormatError = e instanceof ChecksumException || e instanceof FormatException;\n        var ifChecksumOrFormat = isChecksumOrFormatError && retryIfChecksumOrFormatError;\n        if (ifNotFound || ifChecksumOrFormat) {\n          // trying again\n          return setTimeout(loop, _this._timeBetweenDecodingAttempts, resolve, reject);\n        }\n        reject(e);\n      }\n    };\n    return new Promise(function (resolve, reject) {\n      return loop(resolve, reject);\n    });\n  };\n  /**\n   * Continuously decodes from video input.\n   */\n  BrowserCodeReader.prototype.decodeContinuously = function (element, callbackFn) {\n    var _this = this;\n    this._stopContinuousDecode = false;\n    var loop = function () {\n      if (_this._stopContinuousDecode) {\n        _this._stopContinuousDecode = undefined;\n        return;\n      }\n      try {\n        var result = _this.decode(element);\n        callbackFn(result, null);\n        setTimeout(loop, _this.timeBetweenScansMillis);\n      } catch (e) {\n        callbackFn(null, e);\n        var isChecksumOrFormatError = e instanceof ChecksumException || e instanceof FormatException;\n        var isNotFound = e instanceof NotFoundException;\n        if (isChecksumOrFormatError || isNotFound) {\n          // trying again\n          setTimeout(loop, _this._timeBetweenDecodingAttempts);\n        }\n      }\n    };\n    loop();\n  };\n  /**\n   * Gets the BinaryBitmap for ya! (and decodes it)\n   */\n  BrowserCodeReader.prototype.decode = function (element) {\n    // get binary bitmap for decode function\n    var binaryBitmap = this.createBinaryBitmap(element);\n    return this.decodeBitmap(binaryBitmap);\n  };\n  /**\n   * Creates a binaryBitmap based in some image source.\n   *\n   * @param mediaElement HTML element containing drawable image source.\n   */\n  BrowserCodeReader.prototype.createBinaryBitmap = function (mediaElement) {\n    var ctx = this.getCaptureCanvasContext(mediaElement);\n    // doing a scan with inverted colors on the second scan should only happen for video elements\n    var doAutoInvert = false;\n    if (mediaElement instanceof HTMLVideoElement) {\n      this.drawFrameOnCanvas(mediaElement);\n      doAutoInvert = true;\n    } else {\n      this.drawImageOnCanvas(mediaElement);\n    }\n    var canvas = this.getCaptureCanvas(mediaElement);\n    var luminanceSource = new HTMLCanvasElementLuminanceSource(canvas, doAutoInvert);\n    var hybridBinarizer = new HybridBinarizer(luminanceSource);\n    return new BinaryBitmap(hybridBinarizer);\n  };\n  /**\n   *\n   */\n  BrowserCodeReader.prototype.getCaptureCanvasContext = function (mediaElement) {\n    if (!this.captureCanvasContext) {\n      var elem = this.getCaptureCanvas(mediaElement);\n      var ctx = void 0;\n      try {\n        ctx = elem.getContext('2d', {\n          willReadFrequently: true\n        });\n      } catch (e) {\n        ctx = elem.getContext('2d');\n      }\n      this.captureCanvasContext = ctx;\n    }\n    return this.captureCanvasContext;\n  };\n  /**\n   *\n   */\n  BrowserCodeReader.prototype.getCaptureCanvas = function (mediaElement) {\n    if (!this.captureCanvas) {\n      var elem = this.createCaptureCanvas(mediaElement);\n      this.captureCanvas = elem;\n    }\n    return this.captureCanvas;\n  };\n  /**\n   * Overwriting this allows you to manipulate the next frame in anyway you want before decode.\n   */\n  BrowserCodeReader.prototype.drawFrameOnCanvas = function (srcElement, dimensions, canvasElementContext) {\n    if (dimensions === void 0) {\n      dimensions = {\n        sx: 0,\n        sy: 0,\n        sWidth: srcElement.videoWidth,\n        sHeight: srcElement.videoHeight,\n        dx: 0,\n        dy: 0,\n        dWidth: srcElement.videoWidth,\n        dHeight: srcElement.videoHeight\n      };\n    }\n    if (canvasElementContext === void 0) {\n      canvasElementContext = this.captureCanvasContext;\n    }\n    canvasElementContext.drawImage(srcElement, dimensions.sx, dimensions.sy, dimensions.sWidth, dimensions.sHeight, dimensions.dx, dimensions.dy, dimensions.dWidth, dimensions.dHeight);\n  };\n  /**\n   * Ovewriting this allows you to manipulate the snapshot image in anyway you want before decode.\n   */\n  BrowserCodeReader.prototype.drawImageOnCanvas = function (srcElement, dimensions, canvasElementContext) {\n    if (dimensions === void 0) {\n      dimensions = {\n        sx: 0,\n        sy: 0,\n        sWidth: srcElement.naturalWidth,\n        sHeight: srcElement.naturalHeight,\n        dx: 0,\n        dy: 0,\n        dWidth: srcElement.naturalWidth,\n        dHeight: srcElement.naturalHeight\n      };\n    }\n    if (canvasElementContext === void 0) {\n      canvasElementContext = this.captureCanvasContext;\n    }\n    canvasElementContext.drawImage(srcElement, dimensions.sx, dimensions.sy, dimensions.sWidth, dimensions.sHeight, dimensions.dx, dimensions.dy, dimensions.dWidth, dimensions.dHeight);\n  };\n  /**\n   * Call the encapsulated readers decode\n   */\n  BrowserCodeReader.prototype.decodeBitmap = function (binaryBitmap) {\n    return this.reader.decode(binaryBitmap, this._hints);\n  };\n  /**\n   * 🖌 Prepares the canvas for capture and scan frames.\n   */\n  BrowserCodeReader.prototype.createCaptureCanvas = function (mediaElement) {\n    if (typeof document === 'undefined') {\n      this._destroyCaptureCanvas();\n      return null;\n    }\n    var canvasElement = document.createElement('canvas');\n    var width;\n    var height;\n    if (typeof mediaElement !== 'undefined') {\n      if (mediaElement instanceof HTMLVideoElement) {\n        width = mediaElement.videoWidth;\n        height = mediaElement.videoHeight;\n      } else if (mediaElement instanceof HTMLImageElement) {\n        width = mediaElement.naturalWidth || mediaElement.width;\n        height = mediaElement.naturalHeight || mediaElement.height;\n      }\n    }\n    canvasElement.style.width = width + 'px';\n    canvasElement.style.height = height + 'px';\n    canvasElement.width = width;\n    canvasElement.height = height;\n    return canvasElement;\n  };\n  /**\n   * Stops the continuous scan and cleans the stream.\n   */\n  BrowserCodeReader.prototype.stopStreams = function () {\n    if (this.stream) {\n      this.stream.getVideoTracks().forEach(function (t) {\n        return t.stop();\n      });\n      this.stream = undefined;\n    }\n    if (this._stopAsyncDecode === false) {\n      this.stopAsyncDecode();\n    }\n    if (this._stopContinuousDecode === false) {\n      this.stopContinuousDecode();\n    }\n  };\n  /**\n   * Resets the code reader to the initial state. Cancels any ongoing barcode scanning from video or camera.\n   *\n   * @memberOf BrowserCodeReader\n   */\n  BrowserCodeReader.prototype.reset = function () {\n    // stops the camera, preview and scan 🔴\n    this.stopStreams();\n    // clean and forget about HTML elements\n    this._destroyVideoElement();\n    this._destroyImageElement();\n    this._destroyCaptureCanvas();\n  };\n  BrowserCodeReader.prototype._destroyVideoElement = function () {\n    if (!this.videoElement) {\n      return;\n    }\n    // first gives freedon to the element 🕊\n    if (typeof this.videoEndedListener !== 'undefined') {\n      this.videoElement.removeEventListener('ended', this.videoEndedListener);\n    }\n    if (typeof this.videoPlayingEventListener !== 'undefined') {\n      this.videoElement.removeEventListener('playing', this.videoPlayingEventListener);\n    }\n    if (typeof this.videoCanPlayListener !== 'undefined') {\n      this.videoElement.removeEventListener('loadedmetadata', this.videoCanPlayListener);\n    }\n    // then forgets about that element 😢\n    this.cleanVideoSource(this.videoElement);\n    this.videoElement = undefined;\n  };\n  BrowserCodeReader.prototype._destroyImageElement = function () {\n    if (!this.imageElement) {\n      return;\n    }\n    // first gives freedon to the element 🕊\n    if (undefined !== this.imageLoadedListener) {\n      this.imageElement.removeEventListener('load', this.imageLoadedListener);\n    }\n    // then forget about that element 😢\n    this.imageElement.src = undefined;\n    this.imageElement.removeAttribute('src');\n    this.imageElement = undefined;\n  };\n  /**\n   * Cleans canvas references 🖌\n   */\n  BrowserCodeReader.prototype._destroyCaptureCanvas = function () {\n    // then forget about that element 😢\n    this.captureCanvasContext = undefined;\n    this.captureCanvas = undefined;\n  };\n  /**\n   * Defines what the videoElement src will be.\n   *\n   * @param videoElement\n   * @param stream\n   */\n  BrowserCodeReader.prototype.addVideoSource = function (videoElement, stream) {\n    // Older browsers may not have `srcObject`\n    try {\n      // @note Throws Exception if interrupted by a new loaded request\n      videoElement.srcObject = stream;\n    } catch (err) {\n      // @note Avoid using this in new browsers, as it is going away.\n      // @ts-ignore\n      videoElement.src = URL.createObjectURL(stream);\n    }\n  };\n  /**\n   * Unbinds a HTML video src property.\n   *\n   * @param videoElement\n   */\n  BrowserCodeReader.prototype.cleanVideoSource = function (videoElement) {\n    try {\n      videoElement.srcObject = null;\n    } catch (err) {\n      videoElement.src = '';\n    }\n    this.videoElement.removeAttribute('src');\n  };\n  return BrowserCodeReader;\n}();\nexport { BrowserCodeReader };", "map": {"version": 3, "names": ["__awaiter", "thisArg", "_arguments", "P", "generator", "adopt", "value", "resolve", "Promise", "reject", "fulfilled", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__generator", "body", "_", "label", "sent", "t", "trys", "ops", "f", "y", "g", "verb", "Symbol", "iterator", "n", "v", "op", "TypeError", "call", "pop", "length", "push", "__values", "o", "s", "m", "i", "ArgumentException", "BinaryBitmap", "ChecksumException", "HybridBinarizer", "FormatException", "NotFoundException", "HTMLCanvasElementLuminanceSource", "VideoInputDevice", "B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reader", "timeBetweenScansMillis", "_hints", "_stopContinuousDecode", "_stopAsyncDecode", "_timeBetweenDecodingAttempts", "Object", "defineProperty", "prototype", "get", "navigator", "enumerable", "configurable", "hasNavigator", "mediaDevices", "isMediaDevicesSuported", "enumerateDevices", "set", "millis", "hints", "listVideoInputDevices", "devices", "videoDevices", "devices_1", "devices_1_1", "device", "kind", "deviceId", "groupId", "videoDevice", "e_1", "_a", "_b", "Error", "canEnumerateDevices", "id", "e_1_1", "error", "return", "getVideoInputDevices", "map", "d", "findDeviceById", "find", "x", "decodeFromInputVideoDevice", "videoSource", "decodeOnceFromVideoDevice", "videoConstraints", "constraints", "reset", "facingMode", "exact", "video", "decodeOnceFromConstraints", "stream", "getUserMedia", "decodeOnceFromStream", "attachStreamToVideo", "decodeOnce", "decodeFromInputVideoDeviceContinuously", "callbackFn", "decodeFromVideoDevice", "decodeFromConstraints", "decodeFromStream", "decodeContinuously", "stopAsyncDecode", "stopContinuousDecode", "videoElement", "prepareVideoElement", "addVideoSource", "playVideoOnLoadAsync", "_this", "playVideoOnLoad", "element", "videoEndedListener", "stopStreams", "videoCanPlayListener", "tryPlayVideo", "addEventListener", "isVideoPlaying", "currentTime", "paused", "ended", "readyState", "console", "warn", "play", "getMediaElement", "mediaElementId", "type", "mediaElement", "document", "getElementById", "nodeName", "toLowerCase", "decodeFromImage", "source", "url", "decodeFromImageUrl", "decodeFromImageElement", "decodeFromVideo", "decodeFromVideoUrl", "decodeFromVideoElement", "decodeFromVideoContinuously", "undefined", "decodeFromVideoUrlContinuously", "decodeFromVideoElementContinuously", "prepareImageElement", "imageElement", "task", "isImageLoaded", "_decodeOnLoadImage", "_decodeFromVideoElementSetup", "_decodeOnLoadVideo", "_decodeOnLoadVideoContinuously", "decodeTask", "src", "imageLoadedListener", "img", "complete", "naturalWidth", "imageSource", "createElement", "width", "height", "HTMLImageElement", "HTMLVideoElement", "setAttribute", "retryIfNotFound", "retryIfChecksumOrFormatError", "loop", "decode", "ifNotFound", "isChecksumOrFormatError", "ifChecksumOrFormat", "setTimeout", "isNotFound", "binaryBitmap", "createBinaryBitmap", "decodeBitmap", "ctx", "getCaptureCanvasContext", "doAutoInvert", "drawFrameOnCanvas", "drawImageOnCanvas", "canvas", "getCaptureCanvas", "luminanceSource", "hybridBinarizer", "captureCanvasContext", "elem", "getContext", "willReadFrequently", "<PERSON><PERSON><PERSON><PERSON>", "createCaptureCanvas", "srcElement", "dimensions", "canvasElementContext", "sx", "sy", "sWidth", "videoWidth", "sHeight", "videoHeight", "dx", "dy", "dWidth", "dHeight", "drawImage", "naturalHeight", "_destroyCaptureCanvas", "canvasElement", "style", "getVideoTracks", "for<PERSON>ach", "stop", "_destroyVideoElement", "_destroyImageElement", "removeEventListener", "videoPlayingEventListener", "cleanVideoSource", "removeAttribute", "srcObject", "err", "URL", "createObjectURL"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/browser/BrowserCodeReader.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport ArgumentException from '../core/ArgumentException';\nimport BinaryBitmap from '../core/BinaryBitmap';\nimport ChecksumException from '../core/ChecksumException';\nimport HybridBinarizer from '../core/common/HybridBinarizer';\nimport FormatException from '../core/FormatException';\nimport NotFoundException from '../core/NotFoundException';\nimport { HTMLCanvasElementLuminanceSource } from './HTMLCanvasElementLuminanceSource';\nimport { VideoInputDevice } from './VideoInputDevice';\n/**\n * @deprecated Moving to @zxing/browser\n *\n * Base class for browser code reader.\n */\nvar BrowserCodeReader = /** @class */ (function () {\n    /**\n     * Creates an instance of BrowserCodeReader.\n     * @param {Reader} reader The reader instance to decode the barcode\n     * @param {number} [timeBetweenScansMillis=500] the time delay between subsequent successful decode tries\n     *\n     * @memberOf BrowserCodeReader\n     */\n    function BrowserCodeReader(reader, timeBetweenScansMillis, _hints) {\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        this.reader = reader;\n        this.timeBetweenScansMillis = timeBetweenScansMillis;\n        this._hints = _hints;\n        /**\n         * This will break the loop.\n         */\n        this._stopContinuousDecode = false;\n        /**\n         * This will break the loop.\n         */\n        this._stopAsyncDecode = false;\n        /**\n         * Delay time between decode attempts made by the scanner.\n         */\n        this._timeBetweenDecodingAttempts = 0;\n    }\n    Object.defineProperty(BrowserCodeReader.prototype, \"hasNavigator\", {\n        /**\n         * If navigator is present.\n         */\n        get: function () {\n            return typeof navigator !== 'undefined';\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BrowserCodeReader.prototype, \"isMediaDevicesSuported\", {\n        /**\n         * If mediaDevices under navigator is supported.\n         */\n        get: function () {\n            return this.hasNavigator && !!navigator.mediaDevices;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BrowserCodeReader.prototype, \"canEnumerateDevices\", {\n        /**\n         * If enumerateDevices under navigator is supported.\n         */\n        get: function () {\n            return !!(this.isMediaDevicesSuported && navigator.mediaDevices.enumerateDevices);\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BrowserCodeReader.prototype, \"timeBetweenDecodingAttempts\", {\n        /** Time between two decoding tries in milli seconds. */\n        get: function () {\n            return this._timeBetweenDecodingAttempts;\n        },\n        /**\n         * Change the time span the decoder waits between two decoding tries.\n         *\n         * @param {number} millis Time between two decoding tries in milli seconds.\n         */\n        set: function (millis) {\n            this._timeBetweenDecodingAttempts = millis < 0 ? 0 : millis;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(BrowserCodeReader.prototype, \"hints\", {\n        /**\n         * Sets the hints.\n         */\n        get: function () {\n            return this._hints;\n        },\n        /**\n         * Sets the hints.\n         */\n        set: function (hints) {\n            this._hints = hints || null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Lists all the available video input devices.\n     */\n    BrowserCodeReader.prototype.listVideoInputDevices = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var devices, videoDevices, devices_1, devices_1_1, device, kind, deviceId, label, groupId, videoDevice;\n            var e_1, _a;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (!this.hasNavigator) {\n                            throw new Error(\"Can't enumerate devices, navigator is not present.\");\n                        }\n                        if (!this.canEnumerateDevices) {\n                            throw new Error(\"Can't enumerate devices, method not supported.\");\n                        }\n                        return [4 /*yield*/, navigator.mediaDevices.enumerateDevices()];\n                    case 1:\n                        devices = _b.sent();\n                        videoDevices = [];\n                        try {\n                            for (devices_1 = __values(devices), devices_1_1 = devices_1.next(); !devices_1_1.done; devices_1_1 = devices_1.next()) {\n                                device = devices_1_1.value;\n                                kind = device.kind === 'video' ? 'videoinput' : device.kind;\n                                if (kind !== 'videoinput') {\n                                    continue;\n                                }\n                                deviceId = device.deviceId || device.id;\n                                label = device.label || \"Video device \" + (videoDevices.length + 1);\n                                groupId = device.groupId;\n                                videoDevice = { deviceId: deviceId, label: label, kind: kind, groupId: groupId };\n                                videoDevices.push(videoDevice);\n                            }\n                        }\n                        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n                        finally {\n                            try {\n                                if (devices_1_1 && !devices_1_1.done && (_a = devices_1.return)) _a.call(devices_1);\n                            }\n                            finally { if (e_1) throw e_1.error; }\n                        }\n                        return [2 /*return*/, videoDevices];\n                }\n            });\n        });\n    };\n    /**\n     * Obtain the list of available devices with type 'videoinput'.\n     *\n     * @returns {Promise<VideoInputDevice[]>} an array of available video input devices\n     *\n     * @memberOf BrowserCodeReader\n     *\n     * @deprecated Use `listVideoInputDevices` instead.\n     */\n    BrowserCodeReader.prototype.getVideoInputDevices = function () {\n        return __awaiter(this, void 0, void 0, function () {\n            var devices;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.listVideoInputDevices()];\n                    case 1:\n                        devices = _a.sent();\n                        return [2 /*return*/, devices.map(function (d) { return new VideoInputDevice(d.deviceId, d.label); })];\n                }\n            });\n        });\n    };\n    /**\n     * Let's you find a device using it's Id.\n     */\n    BrowserCodeReader.prototype.findDeviceById = function (deviceId) {\n        return __awaiter(this, void 0, void 0, function () {\n            var devices;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.listVideoInputDevices()];\n                    case 1:\n                        devices = _a.sent();\n                        if (!devices) {\n                            return [2 /*return*/, null];\n                        }\n                        return [2 /*return*/, devices.find(function (x) { return x.deviceId === deviceId; })];\n                }\n            });\n        });\n    };\n    /**\n     * Decodes the barcode from the device specified by deviceId while showing the video in the specified video element.\n     *\n     * @param deviceId the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     *\n     * @deprecated Use `decodeOnceFromVideoDevice` instead.\n     */\n    BrowserCodeReader.prototype.decodeFromInputVideoDevice = function (deviceId, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.decodeOnceFromVideoDevice(deviceId, videoSource)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * In one attempt, tries to decode the barcode from the device specified by deviceId while showing the video in the specified video element.\n     *\n     * @param deviceId the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeOnceFromVideoDevice = function (deviceId, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            var videoConstraints, constraints;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.reset();\n                        if (!deviceId) {\n                            videoConstraints = { facingMode: 'environment' };\n                        }\n                        else {\n                            videoConstraints = { deviceId: { exact: deviceId } };\n                        }\n                        constraints = { video: videoConstraints };\n                        return [4 /*yield*/, this.decodeOnceFromConstraints(constraints, videoSource)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n     *\n     * @param constraints the media stream constraints to get s valid media stream to decode from\n     * @param video the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeOnceFromConstraints = function (constraints, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            var stream;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];\n                    case 1:\n                        stream = _a.sent();\n                        return [4 /*yield*/, this.decodeOnceFromStream(stream, videoSource)];\n                    case 2: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n     *\n     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeOnceFromStream = function (stream, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            var video, result;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.reset();\n                        return [4 /*yield*/, this.attachStreamToVideo(stream, videoSource)];\n                    case 1:\n                        video = _a.sent();\n                        return [4 /*yield*/, this.decodeOnce(video)];\n                    case 2:\n                        result = _a.sent();\n                        return [2 /*return*/, result];\n                }\n            });\n        });\n    };\n    /**\n     * Continuously decodes the barcode from the device specified by device while showing the video in the specified video element.\n     *\n     * @param {string|null} [deviceId] the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n     * @param {string|HTMLVideoElement|null} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<void>}\n     *\n     * @memberOf BrowserCodeReader\n     *\n     * @deprecated Use `decodeFromVideoDevice` instead.\n     */\n    BrowserCodeReader.prototype.decodeFromInputVideoDeviceContinuously = function (deviceId, videoSource, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.decodeFromVideoDevice(deviceId, videoSource, callbackFn)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * Continuously tries to decode the barcode from the device specified by device while showing the video in the specified video element.\n     *\n     * @param {string|null} [deviceId] the id of one of the devices obtained after calling getVideoInputDevices. Can be undefined, in this case it will decode from one of the available devices, preffering the main camera (environment facing) if available.\n     * @param {string|HTMLVideoElement|null} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<void>}\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromVideoDevice = function (deviceId, videoSource, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            var videoConstraints, constraints;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        if (!deviceId) {\n                            videoConstraints = { facingMode: 'environment' };\n                        }\n                        else {\n                            videoConstraints = { deviceId: { exact: deviceId } };\n                        }\n                        constraints = { video: videoConstraints };\n                        return [4 /*yield*/, this.decodeFromConstraints(constraints, videoSource, callbackFn)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * Continuously tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n     *\n     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromConstraints = function (constraints, videoSource, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            var stream;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, navigator.mediaDevices.getUserMedia(constraints)];\n                    case 1:\n                        stream = _a.sent();\n                        return [4 /*yield*/, this.decodeFromStream(stream, videoSource, callbackFn)];\n                    case 2: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * In one attempt, tries to decode the barcode from a stream obtained from the given constraints while showing the video in the specified video element.\n     *\n     * @param {MediaStream} [constraints] the media stream constraints to get s valid media stream to decode from\n     * @param {string|HTMLVideoElement} [video] the video element in page where to show the video while decoding. Can be either an element id or directly an HTMLVideoElement. Can be undefined, in which case no video will be shown.\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromStream = function (stream, videoSource, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            var video;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        this.reset();\n                        return [4 /*yield*/, this.attachStreamToVideo(stream, videoSource)];\n                    case 1:\n                        video = _a.sent();\n                        return [4 /*yield*/, this.decodeContinuously(video, callbackFn)];\n                    case 2: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * Breaks the decoding loop.\n     */\n    BrowserCodeReader.prototype.stopAsyncDecode = function () {\n        this._stopAsyncDecode = true;\n    };\n    /**\n     * Breaks the decoding loop.\n     */\n    BrowserCodeReader.prototype.stopContinuousDecode = function () {\n        this._stopContinuousDecode = true;\n    };\n    /**\n     * Sets the new stream and request a new decoding-with-delay.\n     *\n     * @param stream The stream to be shown in the video element.\n     * @param decodeFn A callback for the decode method.\n     */\n    BrowserCodeReader.prototype.attachStreamToVideo = function (stream, videoSource) {\n        return __awaiter(this, void 0, void 0, function () {\n            var videoElement;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        videoElement = this.prepareVideoElement(videoSource);\n                        this.addVideoSource(videoElement, stream);\n                        this.videoElement = videoElement;\n                        this.stream = stream;\n                        return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/, videoElement];\n                }\n            });\n        });\n    };\n    /**\n     *\n     * @param videoElement\n     */\n    BrowserCodeReader.prototype.playVideoOnLoadAsync = function (videoElement) {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            return _this.playVideoOnLoad(videoElement, function () { return resolve(); });\n        });\n    };\n    /**\n     * Binds listeners and callbacks to the videoElement.\n     *\n     * @param element\n     * @param callbackFn\n     */\n    BrowserCodeReader.prototype.playVideoOnLoad = function (element, callbackFn) {\n        var _this = this;\n        this.videoEndedListener = function () { return _this.stopStreams(); };\n        this.videoCanPlayListener = function () { return _this.tryPlayVideo(element); };\n        element.addEventListener('ended', this.videoEndedListener);\n        element.addEventListener('canplay', this.videoCanPlayListener);\n        element.addEventListener('playing', callbackFn);\n        // if canplay was already fired, we won't know when to play, so just give it a try\n        this.tryPlayVideo(element);\n    };\n    /**\n     * Checks if the given video element is currently playing.\n     */\n    BrowserCodeReader.prototype.isVideoPlaying = function (video) {\n        return (video.currentTime > 0 &&\n            !video.paused &&\n            !video.ended &&\n            video.readyState > 2);\n    };\n    /**\n     * Just tries to play the video and logs any errors.\n     * The play call is only made is the video is not already playing.\n     */\n    BrowserCodeReader.prototype.tryPlayVideo = function (videoElement) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _a;\n            return __generator(this, function (_b) {\n                switch (_b.label) {\n                    case 0:\n                        if (this.isVideoPlaying(videoElement)) {\n                            console.warn('Trying to play video that is already playing.');\n                            return [2 /*return*/];\n                        }\n                        _b.label = 1;\n                    case 1:\n                        _b.trys.push([1, 3, , 4]);\n                        return [4 /*yield*/, videoElement.play()];\n                    case 2:\n                        _b.sent();\n                        return [3 /*break*/, 4];\n                    case 3:\n                        _a = _b.sent();\n                        console.warn('It was not possible to play the video.');\n                        return [3 /*break*/, 4];\n                    case 4: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * Searches and validates a media element.\n     */\n    BrowserCodeReader.prototype.getMediaElement = function (mediaElementId, type) {\n        var mediaElement = document.getElementById(mediaElementId);\n        if (!mediaElement) {\n            throw new ArgumentException(\"element with id '\" + mediaElementId + \"' not found\");\n        }\n        if (mediaElement.nodeName.toLowerCase() !== type.toLowerCase()) {\n            throw new ArgumentException(\"element with id '\" + mediaElementId + \"' must be an \" + type + \" element\");\n        }\n        return mediaElement;\n    };\n    /**\n     * Decodes the barcode from an image.\n     *\n     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n     * @param {string} [url]\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromImage = function (source, url) {\n        if (!source && !url) {\n            throw new ArgumentException('either imageElement with a src set or an url must be provided');\n        }\n        if (url && !source) {\n            return this.decodeFromImageUrl(url);\n        }\n        return this.decodeFromImageElement(source);\n    };\n    /**\n     * Decodes the barcode from a video.\n     *\n     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n     * @param {string} [url]\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.decodeFromVideo = function (source, url) {\n        if (!source && !url) {\n            throw new ArgumentException('Either an element with a src set or an URL must be provided');\n        }\n        if (url && !source) {\n            return this.decodeFromVideoUrl(url);\n        }\n        return this.decodeFromVideoElement(source);\n    };\n    /**\n     * Decodes continuously the barcode from a video.\n     *\n     * @param {(string|HTMLImageElement)} [source] The image element that can be either an element id or the element itself. Can be undefined in which case the decoding will be done from the imageUrl parameter.\n     * @param {string} [url]\n     * @returns {Promise<Result>} The decoding result.\n     *\n     * @memberOf BrowserCodeReader\n     *\n     * @experimental\n     */\n    BrowserCodeReader.prototype.decodeFromVideoContinuously = function (source, url, callbackFn) {\n        if (undefined === source && undefined === url) {\n            throw new ArgumentException('Either an element with a src set or an URL must be provided');\n        }\n        if (url && !source) {\n            return this.decodeFromVideoUrlContinuously(url, callbackFn);\n        }\n        return this.decodeFromVideoElementContinuously(source, callbackFn);\n    };\n    /**\n     * Decodes something from an image HTML element.\n     */\n    BrowserCodeReader.prototype.decodeFromImageElement = function (source) {\n        if (!source) {\n            throw new ArgumentException('An image element must be provided.');\n        }\n        this.reset();\n        var element = this.prepareImageElement(source);\n        this.imageElement = element;\n        var task;\n        if (this.isImageLoaded(element)) {\n            task = this.decodeOnce(element, false, true);\n        }\n        else {\n            task = this._decodeOnLoadImage(element);\n        }\n        return task;\n    };\n    /**\n     * Decodes something from an image HTML element.\n     */\n    BrowserCodeReader.prototype.decodeFromVideoElement = function (source) {\n        var element = this._decodeFromVideoElementSetup(source);\n        return this._decodeOnLoadVideo(element);\n    };\n    /**\n     * Decodes something from an image HTML element.\n     */\n    BrowserCodeReader.prototype.decodeFromVideoElementContinuously = function (source, callbackFn) {\n        var element = this._decodeFromVideoElementSetup(source);\n        return this._decodeOnLoadVideoContinuously(element, callbackFn);\n    };\n    /**\n     * Sets up the video source so it can be decoded when loaded.\n     *\n     * @param source The video source element.\n     */\n    BrowserCodeReader.prototype._decodeFromVideoElementSetup = function (source) {\n        if (!source) {\n            throw new ArgumentException('A video element must be provided.');\n        }\n        this.reset();\n        var element = this.prepareVideoElement(source);\n        // defines the video element before starts decoding\n        this.videoElement = element;\n        return element;\n    };\n    /**\n     * Decodes an image from a URL.\n     */\n    BrowserCodeReader.prototype.decodeFromImageUrl = function (url) {\n        if (!url) {\n            throw new ArgumentException('An URL must be provided.');\n        }\n        this.reset();\n        var element = this.prepareImageElement();\n        this.imageElement = element;\n        var decodeTask = this._decodeOnLoadImage(element);\n        element.src = url;\n        return decodeTask;\n    };\n    /**\n     * Decodes an image from a URL.\n     */\n    BrowserCodeReader.prototype.decodeFromVideoUrl = function (url) {\n        if (!url) {\n            throw new ArgumentException('An URL must be provided.');\n        }\n        this.reset();\n        // creates a new element\n        var element = this.prepareVideoElement();\n        var decodeTask = this.decodeFromVideoElement(element);\n        element.src = url;\n        return decodeTask;\n    };\n    /**\n     * Decodes an image from a URL.\n     *\n     * @experimental\n     */\n    BrowserCodeReader.prototype.decodeFromVideoUrlContinuously = function (url, callbackFn) {\n        if (!url) {\n            throw new ArgumentException('An URL must be provided.');\n        }\n        this.reset();\n        // creates a new element\n        var element = this.prepareVideoElement();\n        var decodeTask = this.decodeFromVideoElementContinuously(element, callbackFn);\n        element.src = url;\n        return decodeTask;\n    };\n    BrowserCodeReader.prototype._decodeOnLoadImage = function (element) {\n        var _this = this;\n        return new Promise(function (resolve, reject) {\n            _this.imageLoadedListener = function () {\n                return _this.decodeOnce(element, false, true).then(resolve, reject);\n            };\n            element.addEventListener('load', _this.imageLoadedListener);\n        });\n    };\n    BrowserCodeReader.prototype._decodeOnLoadVideo = function (videoElement) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: \n                    // plays the video\n                    return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n                    case 1:\n                        // plays the video\n                        _a.sent();\n                        return [4 /*yield*/, this.decodeOnce(videoElement)];\n                    case 2: \n                    // starts decoding after played the video\n                    return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    BrowserCodeReader.prototype._decodeOnLoadVideoContinuously = function (videoElement, callbackFn) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: \n                    // plays the video\n                    return [4 /*yield*/, this.playVideoOnLoadAsync(videoElement)];\n                    case 1:\n                        // plays the video\n                        _a.sent();\n                        // starts decoding after played the video\n                        this.decodeContinuously(videoElement, callbackFn);\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    BrowserCodeReader.prototype.isImageLoaded = function (img) {\n        // During the onload event, IE correctly identifies any images that\n        // weren’t downloaded as not complete. Others should too. Gecko-based\n        // browsers act like NS4 in that they report this incorrectly.\n        if (!img.complete) {\n            return false;\n        }\n        // However, they do have two very useful properties: naturalWidth and\n        // naturalHeight. These give the true size of the image. If it failed\n        // to load, either of these should be zero.\n        if (img.naturalWidth === 0) {\n            return false;\n        }\n        // No other way of checking: assume it’s ok.\n        return true;\n    };\n    BrowserCodeReader.prototype.prepareImageElement = function (imageSource) {\n        var imageElement;\n        if (typeof imageSource === 'undefined') {\n            imageElement = document.createElement('img');\n            imageElement.width = 200;\n            imageElement.height = 200;\n        }\n        if (typeof imageSource === 'string') {\n            imageElement = this.getMediaElement(imageSource, 'img');\n        }\n        if (imageSource instanceof HTMLImageElement) {\n            imageElement = imageSource;\n        }\n        return imageElement;\n    };\n    /**\n     * Sets a HTMLVideoElement for scanning or creates a new one.\n     *\n     * @param videoSource The HTMLVideoElement to be set.\n     */\n    BrowserCodeReader.prototype.prepareVideoElement = function (videoSource) {\n        var videoElement;\n        if (!videoSource && typeof document !== 'undefined') {\n            videoElement = document.createElement('video');\n            videoElement.width = 200;\n            videoElement.height = 200;\n        }\n        if (typeof videoSource === 'string') {\n            videoElement = (this.getMediaElement(videoSource, 'video'));\n        }\n        if (videoSource instanceof HTMLVideoElement) {\n            videoElement = videoSource;\n        }\n        // Needed for iOS 11\n        videoElement.setAttribute('autoplay', 'true');\n        videoElement.setAttribute('muted', 'true');\n        videoElement.setAttribute('playsinline', 'true');\n        return videoElement;\n    };\n    /**\n     * Tries to decode from the video input until it finds some value.\n     */\n    BrowserCodeReader.prototype.decodeOnce = function (element, retryIfNotFound, retryIfChecksumOrFormatError) {\n        var _this = this;\n        if (retryIfNotFound === void 0) { retryIfNotFound = true; }\n        if (retryIfChecksumOrFormatError === void 0) { retryIfChecksumOrFormatError = true; }\n        this._stopAsyncDecode = false;\n        var loop = function (resolve, reject) {\n            if (_this._stopAsyncDecode) {\n                reject(new NotFoundException('Video stream has ended before any code could be detected.'));\n                _this._stopAsyncDecode = undefined;\n                return;\n            }\n            try {\n                var result = _this.decode(element);\n                resolve(result);\n            }\n            catch (e) {\n                var ifNotFound = retryIfNotFound && e instanceof NotFoundException;\n                var isChecksumOrFormatError = e instanceof ChecksumException || e instanceof FormatException;\n                var ifChecksumOrFormat = isChecksumOrFormatError && retryIfChecksumOrFormatError;\n                if (ifNotFound || ifChecksumOrFormat) {\n                    // trying again\n                    return setTimeout(loop, _this._timeBetweenDecodingAttempts, resolve, reject);\n                }\n                reject(e);\n            }\n        };\n        return new Promise(function (resolve, reject) { return loop(resolve, reject); });\n    };\n    /**\n     * Continuously decodes from video input.\n     */\n    BrowserCodeReader.prototype.decodeContinuously = function (element, callbackFn) {\n        var _this = this;\n        this._stopContinuousDecode = false;\n        var loop = function () {\n            if (_this._stopContinuousDecode) {\n                _this._stopContinuousDecode = undefined;\n                return;\n            }\n            try {\n                var result = _this.decode(element);\n                callbackFn(result, null);\n                setTimeout(loop, _this.timeBetweenScansMillis);\n            }\n            catch (e) {\n                callbackFn(null, e);\n                var isChecksumOrFormatError = e instanceof ChecksumException || e instanceof FormatException;\n                var isNotFound = e instanceof NotFoundException;\n                if (isChecksumOrFormatError || isNotFound) {\n                    // trying again\n                    setTimeout(loop, _this._timeBetweenDecodingAttempts);\n                }\n            }\n        };\n        loop();\n    };\n    /**\n     * Gets the BinaryBitmap for ya! (and decodes it)\n     */\n    BrowserCodeReader.prototype.decode = function (element) {\n        // get binary bitmap for decode function\n        var binaryBitmap = this.createBinaryBitmap(element);\n        return this.decodeBitmap(binaryBitmap);\n    };\n    /**\n     * Creates a binaryBitmap based in some image source.\n     *\n     * @param mediaElement HTML element containing drawable image source.\n     */\n    BrowserCodeReader.prototype.createBinaryBitmap = function (mediaElement) {\n        var ctx = this.getCaptureCanvasContext(mediaElement);\n        // doing a scan with inverted colors on the second scan should only happen for video elements\n        var doAutoInvert = false;\n        if (mediaElement instanceof HTMLVideoElement) {\n            this.drawFrameOnCanvas(mediaElement);\n            doAutoInvert = true;\n        }\n        else {\n            this.drawImageOnCanvas(mediaElement);\n        }\n        var canvas = this.getCaptureCanvas(mediaElement);\n        var luminanceSource = new HTMLCanvasElementLuminanceSource(canvas, doAutoInvert);\n        var hybridBinarizer = new HybridBinarizer(luminanceSource);\n        return new BinaryBitmap(hybridBinarizer);\n    };\n    /**\n     *\n     */\n    BrowserCodeReader.prototype.getCaptureCanvasContext = function (mediaElement) {\n        if (!this.captureCanvasContext) {\n            var elem = this.getCaptureCanvas(mediaElement);\n            var ctx = void 0;\n            try {\n                ctx = elem.getContext('2d', { willReadFrequently: true });\n            }\n            catch (e) {\n                ctx = elem.getContext('2d');\n            }\n            this.captureCanvasContext = ctx;\n        }\n        return this.captureCanvasContext;\n    };\n    /**\n     *\n     */\n    BrowserCodeReader.prototype.getCaptureCanvas = function (mediaElement) {\n        if (!this.captureCanvas) {\n            var elem = this.createCaptureCanvas(mediaElement);\n            this.captureCanvas = elem;\n        }\n        return this.captureCanvas;\n    };\n    /**\n     * Overwriting this allows you to manipulate the next frame in anyway you want before decode.\n     */\n    BrowserCodeReader.prototype.drawFrameOnCanvas = function (srcElement, dimensions, canvasElementContext) {\n        if (dimensions === void 0) { dimensions = {\n            sx: 0,\n            sy: 0,\n            sWidth: srcElement.videoWidth,\n            sHeight: srcElement.videoHeight,\n            dx: 0,\n            dy: 0,\n            dWidth: srcElement.videoWidth,\n            dHeight: srcElement.videoHeight,\n        }; }\n        if (canvasElementContext === void 0) { canvasElementContext = this.captureCanvasContext; }\n        canvasElementContext.drawImage(srcElement, dimensions.sx, dimensions.sy, dimensions.sWidth, dimensions.sHeight, dimensions.dx, dimensions.dy, dimensions.dWidth, dimensions.dHeight);\n    };\n    /**\n     * Ovewriting this allows you to manipulate the snapshot image in anyway you want before decode.\n     */\n    BrowserCodeReader.prototype.drawImageOnCanvas = function (srcElement, dimensions, canvasElementContext) {\n        if (dimensions === void 0) { dimensions = {\n            sx: 0,\n            sy: 0,\n            sWidth: srcElement.naturalWidth,\n            sHeight: srcElement.naturalHeight,\n            dx: 0,\n            dy: 0,\n            dWidth: srcElement.naturalWidth,\n            dHeight: srcElement.naturalHeight,\n        }; }\n        if (canvasElementContext === void 0) { canvasElementContext = this.captureCanvasContext; }\n        canvasElementContext.drawImage(srcElement, dimensions.sx, dimensions.sy, dimensions.sWidth, dimensions.sHeight, dimensions.dx, dimensions.dy, dimensions.dWidth, dimensions.dHeight);\n    };\n    /**\n     * Call the encapsulated readers decode\n     */\n    BrowserCodeReader.prototype.decodeBitmap = function (binaryBitmap) {\n        return this.reader.decode(binaryBitmap, this._hints);\n    };\n    /**\n     * 🖌 Prepares the canvas for capture and scan frames.\n     */\n    BrowserCodeReader.prototype.createCaptureCanvas = function (mediaElement) {\n        if (typeof document === 'undefined') {\n            this._destroyCaptureCanvas();\n            return null;\n        }\n        var canvasElement = document.createElement('canvas');\n        var width;\n        var height;\n        if (typeof mediaElement !== 'undefined') {\n            if (mediaElement instanceof HTMLVideoElement) {\n                width = mediaElement.videoWidth;\n                height = mediaElement.videoHeight;\n            }\n            else if (mediaElement instanceof HTMLImageElement) {\n                width = mediaElement.naturalWidth || mediaElement.width;\n                height = mediaElement.naturalHeight || mediaElement.height;\n            }\n        }\n        canvasElement.style.width = width + 'px';\n        canvasElement.style.height = height + 'px';\n        canvasElement.width = width;\n        canvasElement.height = height;\n        return canvasElement;\n    };\n    /**\n     * Stops the continuous scan and cleans the stream.\n     */\n    BrowserCodeReader.prototype.stopStreams = function () {\n        if (this.stream) {\n            this.stream.getVideoTracks().forEach(function (t) { return t.stop(); });\n            this.stream = undefined;\n        }\n        if (this._stopAsyncDecode === false) {\n            this.stopAsyncDecode();\n        }\n        if (this._stopContinuousDecode === false) {\n            this.stopContinuousDecode();\n        }\n    };\n    /**\n     * Resets the code reader to the initial state. Cancels any ongoing barcode scanning from video or camera.\n     *\n     * @memberOf BrowserCodeReader\n     */\n    BrowserCodeReader.prototype.reset = function () {\n        // stops the camera, preview and scan 🔴\n        this.stopStreams();\n        // clean and forget about HTML elements\n        this._destroyVideoElement();\n        this._destroyImageElement();\n        this._destroyCaptureCanvas();\n    };\n    BrowserCodeReader.prototype._destroyVideoElement = function () {\n        if (!this.videoElement) {\n            return;\n        }\n        // first gives freedon to the element 🕊\n        if (typeof this.videoEndedListener !== 'undefined') {\n            this.videoElement.removeEventListener('ended', this.videoEndedListener);\n        }\n        if (typeof this.videoPlayingEventListener !== 'undefined') {\n            this.videoElement.removeEventListener('playing', this.videoPlayingEventListener);\n        }\n        if (typeof this.videoCanPlayListener !== 'undefined') {\n            this.videoElement.removeEventListener('loadedmetadata', this.videoCanPlayListener);\n        }\n        // then forgets about that element 😢\n        this.cleanVideoSource(this.videoElement);\n        this.videoElement = undefined;\n    };\n    BrowserCodeReader.prototype._destroyImageElement = function () {\n        if (!this.imageElement) {\n            return;\n        }\n        // first gives freedon to the element 🕊\n        if (undefined !== this.imageLoadedListener) {\n            this.imageElement.removeEventListener('load', this.imageLoadedListener);\n        }\n        // then forget about that element 😢\n        this.imageElement.src = undefined;\n        this.imageElement.removeAttribute('src');\n        this.imageElement = undefined;\n    };\n    /**\n     * Cleans canvas references 🖌\n     */\n    BrowserCodeReader.prototype._destroyCaptureCanvas = function () {\n        // then forget about that element 😢\n        this.captureCanvasContext = undefined;\n        this.captureCanvas = undefined;\n    };\n    /**\n     * Defines what the videoElement src will be.\n     *\n     * @param videoElement\n     * @param stream\n     */\n    BrowserCodeReader.prototype.addVideoSource = function (videoElement, stream) {\n        // Older browsers may not have `srcObject`\n        try {\n            // @note Throws Exception if interrupted by a new loaded request\n            videoElement.srcObject = stream;\n        }\n        catch (err) {\n            // @note Avoid using this in new browsers, as it is going away.\n            // @ts-ignore\n            videoElement.src = URL.createObjectURL(stream);\n        }\n    };\n    /**\n     * Unbinds a HTML video src property.\n     *\n     * @param videoElement\n     */\n    BrowserCodeReader.prototype.cleanVideoSource = function (videoElement) {\n        try {\n            videoElement.srcObject = null;\n        }\n        catch (err) {\n            videoElement.src = '';\n        }\n        this.videoElement.removeAttribute('src');\n    };\n    return BrowserCodeReader;\n}());\nexport { BrowserCodeReader };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAK,UAAUC,OAAO,EAAEC,UAAU,EAAEC,CAAC,EAAEC,SAAS,EAAE;EACrF,SAASC,KAAKA,CAACC,KAAK,EAAE;IAAE,OAAOA,KAAK,YAAYH,CAAC,GAAGG,KAAK,GAAG,IAAIH,CAAC,CAAC,UAAUI,OAAO,EAAE;MAAEA,OAAO,CAACD,KAAK,CAAC;IAAE,CAAC,CAAC;EAAE;EAC3G,OAAO,KAAKH,CAAC,KAAKA,CAAC,GAAGK,OAAO,CAAC,EAAE,UAAUD,OAAO,EAAEE,MAAM,EAAE;IACvD,SAASC,SAASA,CAACJ,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAACQ,IAAI,CAACN,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC1F,SAASC,QAAQA,CAACR,KAAK,EAAE;MAAE,IAAI;QAAEK,IAAI,CAACP,SAAS,CAAC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAC;MAAE,CAAC,CAAC,OAAOO,CAAC,EAAE;QAAEJ,MAAM,CAACI,CAAC,CAAC;MAAE;IAAE;IAC7F,SAASF,IAAIA,CAACI,MAAM,EAAE;MAAEA,MAAM,CAACC,IAAI,GAAGT,OAAO,CAACQ,MAAM,CAACT,KAAK,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,KAAK,CAAC,CAACW,IAAI,CAACP,SAAS,EAAEI,QAAQ,CAAC;IAAE;IAC7GH,IAAI,CAAC,CAACP,SAAS,GAAGA,SAAS,CAACc,KAAK,CAACjB,OAAO,EAAEC,UAAU,IAAI,EAAE,CAAC,EAAEU,IAAI,CAAC,CAAC,CAAC;EACzE,CAAC,CAAC;AACN,CAAC;AACD,IAAIO,WAAW,GAAI,IAAI,IAAI,IAAI,CAACA,WAAW,IAAK,UAAUlB,OAAO,EAAEmB,IAAI,EAAE;EACrE,IAAIC,CAAC,GAAG;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE,SAAAA,CAAA,EAAW;QAAE,IAAIC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAMA,CAAC,CAAC,CAAC,CAAC;QAAE,OAAOA,CAAC,CAAC,CAAC,CAAC;MAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEJ,CAAC;IAAEK,CAAC;EAChH,OAAOA,CAAC,GAAG;IAAEjB,IAAI,EAAEkB,IAAI,CAAC,CAAC,CAAC;IAAE,OAAO,EAAEA,IAAI,CAAC,CAAC,CAAC;IAAE,QAAQ,EAAEA,IAAI,CAAC,CAAC;EAAE,CAAC,EAAE,OAAOC,MAAM,KAAK,UAAU,KAAKF,CAAC,CAACE,MAAM,CAACC,QAAQ,CAAC,GAAG,YAAW;IAAE,OAAO,IAAI;EAAE,CAAC,CAAC,EAAEH,CAAC;EACxJ,SAASC,IAAIA,CAACG,CAAC,EAAE;IAAE,OAAO,UAAUC,CAAC,EAAE;MAAE,OAAOvB,IAAI,CAAC,CAACsB,CAAC,EAAEC,CAAC,CAAC,CAAC;IAAE,CAAC;EAAE;EACjE,SAASvB,IAAIA,CAACwB,EAAE,EAAE;IACd,IAAIR,CAAC,EAAE,MAAM,IAAIS,SAAS,CAAC,iCAAiC,CAAC;IAC7D,OAAOf,CAAC,EAAE,IAAI;MACV,IAAIM,CAAC,GAAG,CAAC,EAAEC,CAAC,KAAKJ,CAAC,GAAGW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGP,CAAC,CAAC,QAAQ,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC,GAAGP,CAAC,CAAC,OAAO,CAAC,KAAK,CAACJ,CAAC,GAAGI,CAAC,CAAC,QAAQ,CAAC,KAAKJ,CAAC,CAACa,IAAI,CAACT,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,CAAC,CAAChB,IAAI,CAAC,IAAI,CAAC,CAACY,CAAC,GAAGA,CAAC,CAACa,IAAI,CAACT,CAAC,EAAEO,EAAE,CAAC,CAAC,CAAC,CAAC,EAAEnB,IAAI,EAAE,OAAOQ,CAAC;MAC5J,IAAII,CAAC,GAAG,CAAC,EAAEJ,CAAC,EAAEW,EAAE,GAAG,CAACA,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEX,CAAC,CAAClB,KAAK,CAAC;MACvC,QAAQ6B,EAAE,CAAC,CAAC,CAAC;QACT,KAAK,CAAC;QAAE,KAAK,CAAC;UAAEX,CAAC,GAAGW,EAAE;UAAE;QACxB,KAAK,CAAC;UAAEd,CAAC,CAACC,KAAK,EAAE;UAAE,OAAO;YAAEhB,KAAK,EAAE6B,EAAE,CAAC,CAAC,CAAC;YAAEnB,IAAI,EAAE;UAAM,CAAC;QACvD,KAAK,CAAC;UAAEK,CAAC,CAACC,KAAK,EAAE;UAAEM,CAAC,GAAGO,EAAE,CAAC,CAAC,CAAC;UAAEA,EAAE,GAAG,CAAC,CAAC,CAAC;UAAE;QACxC,KAAK,CAAC;UAAEA,EAAE,GAAGd,CAAC,CAACK,GAAG,CAACY,GAAG,CAAC,CAAC;UAAEjB,CAAC,CAACI,IAAI,CAACa,GAAG,CAAC,CAAC;UAAE;QACxC;UACI,IAAI,EAAEd,CAAC,GAAGH,CAAC,CAACI,IAAI,EAAED,CAAC,GAAGA,CAAC,CAACe,MAAM,GAAG,CAAC,IAAIf,CAAC,CAACA,CAAC,CAACe,MAAM,GAAG,CAAC,CAAC,CAAC,KAAKJ,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;YAAEd,CAAC,GAAG,CAAC;YAAE;UAAU;UAC3G,IAAIc,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAACX,CAAC,IAAKW,EAAE,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAC,IAAIW,EAAE,CAAC,CAAC,CAAC,GAAGX,CAAC,CAAC,CAAC,CAAE,CAAC,EAAE;YAAEH,CAAC,CAACC,KAAK,GAAGa,EAAE,CAAC,CAAC,CAAC;YAAE;UAAO;UACrF,IAAIA,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAId,CAAC,CAACC,KAAK,GAAGE,CAAC,CAAC,CAAC,CAAC,EAAE;YAAEH,CAAC,CAACC,KAAK,GAAGE,CAAC,CAAC,CAAC,CAAC;YAAEA,CAAC,GAAGW,EAAE;YAAE;UAAO;UACpE,IAAIX,CAAC,IAAIH,CAAC,CAACC,KAAK,GAAGE,CAAC,CAAC,CAAC,CAAC,EAAE;YAAEH,CAAC,CAACC,KAAK,GAAGE,CAAC,CAAC,CAAC,CAAC;YAAEH,CAAC,CAACK,GAAG,CAACc,IAAI,CAACL,EAAE,CAAC;YAAE;UAAO;UAClE,IAAIX,CAAC,CAAC,CAAC,CAAC,EAAEH,CAAC,CAACK,GAAG,CAACY,GAAG,CAAC,CAAC;UACrBjB,CAAC,CAACI,IAAI,CAACa,GAAG,CAAC,CAAC;UAAE;MACtB;MACAH,EAAE,GAAGf,IAAI,CAACiB,IAAI,CAACpC,OAAO,EAAEoB,CAAC,CAAC;IAC9B,CAAC,CAAC,OAAOR,CAAC,EAAE;MAAEsB,EAAE,GAAG,CAAC,CAAC,EAAEtB,CAAC,CAAC;MAAEe,CAAC,GAAG,CAAC;IAAE,CAAC,SAAS;MAAED,CAAC,GAAGH,CAAC,GAAG,CAAC;IAAE;IACzD,IAAIW,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAMA,EAAE,CAAC,CAAC,CAAC;IAAE,OAAO;MAAE7B,KAAK,EAAE6B,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;MAAEnB,IAAI,EAAE;IAAK,CAAC;EACpF;AACJ,CAAC;AACD,IAAIyB,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOZ,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEY,CAAC,GAAGD,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEE,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACP,IAAI,CAACK,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACH,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1C3B,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAI8B,CAAC,IAAIG,CAAC,IAAIH,CAAC,CAACH,MAAM,EAAEG,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAEpC,KAAK,EAAEoC,CAAC,IAAIA,CAAC,CAACG,CAAC,EAAE,CAAC;QAAE7B,IAAI,EAAE,CAAC0B;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIN,SAAS,CAACO,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOG,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAOC,eAAe,MAAM,gCAAgC;AAC5D,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,SAASC,gCAAgC,QAAQ,oCAAoC;AACrF,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD;AACA;AACA;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,aAAe,YAAY;EAC/C;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASA,iBAAiBA,CAACC,MAAM,EAAEC,sBAAsB,EAAEC,MAAM,EAAE;IAC/D,IAAID,sBAAsB,KAAK,KAAK,CAAC,EAAE;MAAEA,sBAAsB,GAAG,GAAG;IAAE;IACvE,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,sBAAsB,GAAGA,sBAAsB;IACpD,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB;AACR;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC;AACR;AACA;IACQ,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B;AACR;AACA;IACQ,IAAI,CAACC,4BAA4B,GAAG,CAAC;EACzC;EACAC,MAAM,CAACC,cAAc,CAACR,iBAAiB,CAACS,SAAS,EAAE,cAAc,EAAE;IAC/D;AACR;AACA;IACQC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,OAAOC,SAAS,KAAK,WAAW;IAC3C,CAAC;IACDC,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFN,MAAM,CAACC,cAAc,CAACR,iBAAiB,CAACS,SAAS,EAAE,wBAAwB,EAAE;IACzE;AACR;AACA;IACQC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACI,YAAY,IAAI,CAAC,CAACH,SAAS,CAACI,YAAY;IACxD,CAAC;IACDH,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFN,MAAM,CAACC,cAAc,CAACR,iBAAiB,CAACS,SAAS,EAAE,qBAAqB,EAAE;IACtE;AACR;AACA;IACQC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,CAAC,EAAE,IAAI,CAACM,sBAAsB,IAAIL,SAAS,CAACI,YAAY,CAACE,gBAAgB,CAAC;IACrF,CAAC;IACDL,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFN,MAAM,CAACC,cAAc,CAACR,iBAAiB,CAACS,SAAS,EAAE,6BAA6B,EAAE;IAC9E;IACAC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACJ,4BAA4B;IAC5C,CAAC;IACD;AACR;AACA;AACA;AACA;IACQY,GAAG,EAAE,SAAAA,CAAUC,MAAM,EAAE;MACnB,IAAI,CAACb,4BAA4B,GAAGa,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGA,MAAM;IAC/D,CAAC;IACDP,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACFN,MAAM,CAACC,cAAc,CAACR,iBAAiB,CAACS,SAAS,EAAE,OAAO,EAAE;IACxD;AACR;AACA;IACQC,GAAG,EAAE,SAAAA,CAAA,EAAY;MACb,OAAO,IAAI,CAACP,MAAM;IACtB,CAAC;IACD;AACR;AACA;IACQe,GAAG,EAAE,SAAAA,CAAUE,KAAK,EAAE;MAClB,IAAI,CAACjB,MAAM,GAAGiB,KAAK,IAAI,IAAI;IAC/B,CAAC;IACDR,UAAU,EAAE,KAAK;IACjBC,YAAY,EAAE;EAClB,CAAC,CAAC;EACF;AACJ;AACA;EACIb,iBAAiB,CAACS,SAAS,CAACY,qBAAqB,GAAG,YAAY;IAC5D,OAAO3E,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI4E,OAAO,EAAEC,YAAY,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE5D,KAAK,EAAE6D,OAAO,EAAEC,WAAW;MACtG,IAAIC,GAAG,EAAEC,EAAE;MACX,OAAOnE,WAAW,CAAC,IAAI,EAAE,UAAUoE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAACjE,KAAK;UACZ,KAAK,CAAC;YACF,IAAI,CAAC,IAAI,CAAC8C,YAAY,EAAE;cACpB,MAAM,IAAIoB,KAAK,CAAC,oDAAoD,CAAC;YACzE;YACA,IAAI,CAAC,IAAI,CAACC,mBAAmB,EAAE;cAC3B,MAAM,IAAID,KAAK,CAAC,gDAAgD,CAAC;YACrE;YACA,OAAO,CAAC,CAAC,CAAC,WAAWvB,SAAS,CAACI,YAAY,CAACE,gBAAgB,CAAC,CAAC,CAAC;UACnE,KAAK,CAAC;YACFK,OAAO,GAAGW,EAAE,CAAChE,IAAI,CAAC,CAAC;YACnBsD,YAAY,GAAG,EAAE;YACjB,IAAI;cACA,KAAKC,SAAS,GAAGrC,QAAQ,CAACmC,OAAO,CAAC,EAAEG,WAAW,GAAGD,SAAS,CAAClE,IAAI,CAAC,CAAC,EAAE,CAACmE,WAAW,CAAC/D,IAAI,EAAE+D,WAAW,GAAGD,SAAS,CAAClE,IAAI,CAAC,CAAC,EAAE;gBACnHoE,MAAM,GAAGD,WAAW,CAACzE,KAAK;gBAC1B2E,IAAI,GAAGD,MAAM,CAACC,IAAI,KAAK,OAAO,GAAG,YAAY,GAAGD,MAAM,CAACC,IAAI;gBAC3D,IAAIA,IAAI,KAAK,YAAY,EAAE;kBACvB;gBACJ;gBACAC,QAAQ,GAAGF,MAAM,CAACE,QAAQ,IAAIF,MAAM,CAACU,EAAE;gBACvCpE,KAAK,GAAG0D,MAAM,CAAC1D,KAAK,IAAI,eAAe,IAAIuD,YAAY,CAACtC,MAAM,GAAG,CAAC,CAAC;gBACnE4C,OAAO,GAAGH,MAAM,CAACG,OAAO;gBACxBC,WAAW,GAAG;kBAAEF,QAAQ,EAAEA,QAAQ;kBAAE5D,KAAK,EAAEA,KAAK;kBAAE2D,IAAI,EAAEA,IAAI;kBAAEE,OAAO,EAAEA;gBAAQ,CAAC;gBAChFN,YAAY,CAACrC,IAAI,CAAC4C,WAAW,CAAC;cAClC;YACJ,CAAC,CACD,OAAOO,KAAK,EAAE;cAAEN,GAAG,GAAG;gBAAEO,KAAK,EAAED;cAAM,CAAC;YAAE,CAAC,SACjC;cACJ,IAAI;gBACA,IAAIZ,WAAW,IAAI,CAACA,WAAW,CAAC/D,IAAI,KAAKsE,EAAE,GAAGR,SAAS,CAACe,MAAM,CAAC,EAAEP,EAAE,CAACjD,IAAI,CAACyC,SAAS,CAAC;cACvF,CAAC,SACO;gBAAE,IAAIO,GAAG,EAAE,MAAMA,GAAG,CAACO,KAAK;cAAE;YACxC;YACA,OAAO,CAAC,CAAC,CAAC,YAAYf,YAAY,CAAC;QAC3C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIvB,iBAAiB,CAACS,SAAS,CAAC+B,oBAAoB,GAAG,YAAY;IAC3D,OAAO9F,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI4E,OAAO;MACX,OAAOzD,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACqD,qBAAqB,CAAC,CAAC,CAAC;UAC1D,KAAK,CAAC;YACFC,OAAO,GAAGU,EAAE,CAAC/D,IAAI,CAAC,CAAC;YACnB,OAAO,CAAC,CAAC,CAAC,YAAYqD,OAAO,CAACmB,GAAG,CAAC,UAAUC,CAAC,EAAE;cAAE,OAAO,IAAI3C,gBAAgB,CAAC2C,CAAC,CAACd,QAAQ,EAAEc,CAAC,CAAC1E,KAAK,CAAC;YAAE,CAAC,CAAC,CAAC;QAC9G;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACIgC,iBAAiB,CAACS,SAAS,CAACkC,cAAc,GAAG,UAAUf,QAAQ,EAAE;IAC7D,OAAOlF,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI4E,OAAO;MACX,OAAOzD,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACqD,qBAAqB,CAAC,CAAC,CAAC;UAC1D,KAAK,CAAC;YACFC,OAAO,GAAGU,EAAE,CAAC/D,IAAI,CAAC,CAAC;YACnB,IAAI,CAACqD,OAAO,EAAE;cACV,OAAO,CAAC,CAAC,CAAC,YAAY,IAAI,CAAC;YAC/B;YACA,OAAO,CAAC,CAAC,CAAC,YAAYA,OAAO,CAACsB,IAAI,CAAC,UAAUC,CAAC,EAAE;cAAE,OAAOA,CAAC,CAACjB,QAAQ,KAAKA,QAAQ;YAAE,CAAC,CAAC,CAAC;QAC7F;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5B,iBAAiB,CAACS,SAAS,CAACqC,0BAA0B,GAAG,UAAUlB,QAAQ,EAAEmB,WAAW,EAAE;IACtF,OAAOrG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,OAAOmB,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACgF,yBAAyB,CAACpB,QAAQ,EAAEmB,WAAW,CAAC,CAAC;UACnF,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,YAAYf,EAAE,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,iBAAiB,CAACS,SAAS,CAACuC,yBAAyB,GAAG,UAAUpB,QAAQ,EAAEmB,WAAW,EAAE;IACrF,OAAOrG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAIuG,gBAAgB,EAAEC,WAAW;MACjC,OAAOrF,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YACF,IAAI,CAACmF,KAAK,CAAC,CAAC;YACZ,IAAI,CAACvB,QAAQ,EAAE;cACXqB,gBAAgB,GAAG;gBAAEG,UAAU,EAAE;cAAc,CAAC;YACpD,CAAC,MACI;cACDH,gBAAgB,GAAG;gBAAErB,QAAQ,EAAE;kBAAEyB,KAAK,EAAEzB;gBAAS;cAAE,CAAC;YACxD;YACAsB,WAAW,GAAG;cAAEI,KAAK,EAAEL;YAAiB,CAAC;YACzC,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACM,yBAAyB,CAACL,WAAW,EAAEH,WAAW,CAAC,CAAC;UAClF,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,YAAYf,EAAE,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,iBAAiB,CAACS,SAAS,CAAC8C,yBAAyB,GAAG,UAAUL,WAAW,EAAEH,WAAW,EAAE;IACxF,OAAOrG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI8G,MAAM;MACV,OAAO3F,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,WAAW2C,SAAS,CAACI,YAAY,CAAC0C,YAAY,CAACP,WAAW,CAAC,CAAC;UAC9E,KAAK,CAAC;YACFM,MAAM,GAAGxB,EAAE,CAAC/D,IAAI,CAAC,CAAC;YAClB,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACyF,oBAAoB,CAACF,MAAM,EAAET,WAAW,CAAC,CAAC;UACxE,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,YAAYf,EAAE,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,iBAAiB,CAACS,SAAS,CAACiD,oBAAoB,GAAG,UAAUF,MAAM,EAAET,WAAW,EAAE;IAC9E,OAAOrG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI4G,KAAK,EAAE7F,MAAM;MACjB,OAAOI,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YACF,IAAI,CAACmF,KAAK,CAAC,CAAC;YACZ,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACQ,mBAAmB,CAACH,MAAM,EAAET,WAAW,CAAC,CAAC;UACvE,KAAK,CAAC;YACFO,KAAK,GAAGtB,EAAE,CAAC/D,IAAI,CAAC,CAAC;YACjB,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC2F,UAAU,CAACN,KAAK,CAAC,CAAC;UAChD,KAAK,CAAC;YACF7F,MAAM,GAAGuE,EAAE,CAAC/D,IAAI,CAAC,CAAC;YAClB,OAAO,CAAC,CAAC,CAAC,YAAYR,MAAM,CAAC;QACrC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIuC,iBAAiB,CAACS,SAAS,CAACoD,sCAAsC,GAAG,UAAUjC,QAAQ,EAAEmB,WAAW,EAAEe,UAAU,EAAE;IAC9G,OAAOpH,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,OAAOmB,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC+F,qBAAqB,CAACnC,QAAQ,EAAEmB,WAAW,EAAEe,UAAU,CAAC,CAAC;UAC3F,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,YAAY9B,EAAE,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,iBAAiB,CAACS,SAAS,CAACsD,qBAAqB,GAAG,UAAUnC,QAAQ,EAAEmB,WAAW,EAAEe,UAAU,EAAE;IAC7F,OAAOpH,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAIuG,gBAAgB,EAAEC,WAAW;MACjC,OAAOrF,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YACF,IAAI,CAAC4D,QAAQ,EAAE;cACXqB,gBAAgB,GAAG;gBAAEG,UAAU,EAAE;cAAc,CAAC;YACpD,CAAC,MACI;cACDH,gBAAgB,GAAG;gBAAErB,QAAQ,EAAE;kBAAEyB,KAAK,EAAEzB;gBAAS;cAAE,CAAC;YACxD;YACAsB,WAAW,GAAG;cAAEI,KAAK,EAAEL;YAAiB,CAAC;YACzC,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACe,qBAAqB,CAACd,WAAW,EAAEH,WAAW,EAAEe,UAAU,CAAC,CAAC;UAC1F,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,YAAY9B,EAAE,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,iBAAiB,CAACS,SAAS,CAACuD,qBAAqB,GAAG,UAAUd,WAAW,EAAEH,WAAW,EAAEe,UAAU,EAAE;IAChG,OAAOpH,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI8G,MAAM;MACV,OAAO3F,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,WAAW2C,SAAS,CAACI,YAAY,CAAC0C,YAAY,CAACP,WAAW,CAAC,CAAC;UAC9E,KAAK,CAAC;YACFM,MAAM,GAAGxB,EAAE,CAAC/D,IAAI,CAAC,CAAC;YAClB,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACgG,gBAAgB,CAACT,MAAM,EAAET,WAAW,EAAEe,UAAU,CAAC,CAAC;UAChF,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,YAAY9B,EAAE,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI+B,iBAAiB,CAACS,SAAS,CAACwD,gBAAgB,GAAG,UAAUT,MAAM,EAAET,WAAW,EAAEe,UAAU,EAAE;IACtF,OAAOpH,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI4G,KAAK;MACT,OAAOzF,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YACF,IAAI,CAACmF,KAAK,CAAC,CAAC;YACZ,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACQ,mBAAmB,CAACH,MAAM,EAAET,WAAW,CAAC,CAAC;UACvE,KAAK,CAAC;YACFO,KAAK,GAAGtB,EAAE,CAAC/D,IAAI,CAAC,CAAC;YACjB,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACiG,kBAAkB,CAACZ,KAAK,EAAEQ,UAAU,CAAC,CAAC;UACpE,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,YAAY9B,EAAE,CAAC/D,IAAI,CAAC,CAAC,CAAC;QAC5C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACI+B,iBAAiB,CAACS,SAAS,CAAC0D,eAAe,GAAG,YAAY;IACtD,IAAI,CAAC9D,gBAAgB,GAAG,IAAI;EAChC,CAAC;EACD;AACJ;AACA;EACIL,iBAAiB,CAACS,SAAS,CAAC2D,oBAAoB,GAAG,YAAY;IAC3D,IAAI,CAAChE,qBAAqB,GAAG,IAAI;EACrC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIJ,iBAAiB,CAACS,SAAS,CAACkD,mBAAmB,GAAG,UAAUH,MAAM,EAAET,WAAW,EAAE;IAC7E,OAAOrG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAI2H,YAAY;MAChB,OAAOxG,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YACFqG,YAAY,GAAG,IAAI,CAACC,mBAAmB,CAACvB,WAAW,CAAC;YACpD,IAAI,CAACwB,cAAc,CAACF,YAAY,EAAEb,MAAM,CAAC;YACzC,IAAI,CAACa,YAAY,GAAGA,YAAY;YAChC,IAAI,CAACb,MAAM,GAAGA,MAAM;YACpB,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACgB,oBAAoB,CAACH,YAAY,CAAC,CAAC;UACjE,KAAK,CAAC;YACFrC,EAAE,CAAC/D,IAAI,CAAC,CAAC;YACT,OAAO,CAAC,CAAC,CAAC,YAAYoG,YAAY,CAAC;QAC3C;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;EACIrE,iBAAiB,CAACS,SAAS,CAAC+D,oBAAoB,GAAG,UAAUH,YAAY,EAAE;IACvE,IAAII,KAAK,GAAG,IAAI;IAChB,OAAO,IAAIvH,OAAO,CAAC,UAAUD,OAAO,EAAEE,MAAM,EAAE;MAC1C,OAAOsH,KAAK,CAACC,eAAe,CAACL,YAAY,EAAE,YAAY;QAAE,OAAOpH,OAAO,CAAC,CAAC;MAAE,CAAC,CAAC;IACjF,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI+C,iBAAiB,CAACS,SAAS,CAACiE,eAAe,GAAG,UAAUC,OAAO,EAAEb,UAAU,EAAE;IACzE,IAAIW,KAAK,GAAG,IAAI;IAChB,IAAI,CAACG,kBAAkB,GAAG,YAAY;MAAE,OAAOH,KAAK,CAACI,WAAW,CAAC,CAAC;IAAE,CAAC;IACrE,IAAI,CAACC,oBAAoB,GAAG,YAAY;MAAE,OAAOL,KAAK,CAACM,YAAY,CAACJ,OAAO,CAAC;IAAE,CAAC;IAC/EA,OAAO,CAACK,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACJ,kBAAkB,CAAC;IAC1DD,OAAO,CAACK,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACF,oBAAoB,CAAC;IAC9DH,OAAO,CAACK,gBAAgB,CAAC,SAAS,EAAElB,UAAU,CAAC;IAC/C;IACA,IAAI,CAACiB,YAAY,CAACJ,OAAO,CAAC;EAC9B,CAAC;EACD;AACJ;AACA;EACI3E,iBAAiB,CAACS,SAAS,CAACwE,cAAc,GAAG,UAAU3B,KAAK,EAAE;IAC1D,OAAQA,KAAK,CAAC4B,WAAW,GAAG,CAAC,IACzB,CAAC5B,KAAK,CAAC6B,MAAM,IACb,CAAC7B,KAAK,CAAC8B,KAAK,IACZ9B,KAAK,CAAC+B,UAAU,GAAG,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;EACIrF,iBAAiB,CAACS,SAAS,CAACsE,YAAY,GAAG,UAAUV,YAAY,EAAE;IAC/D,OAAO3H,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,IAAIsF,EAAE;MACN,OAAOnE,WAAW,CAAC,IAAI,EAAE,UAAUoE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAACjE,KAAK;UACZ,KAAK,CAAC;YACF,IAAI,IAAI,CAACiH,cAAc,CAACZ,YAAY,CAAC,EAAE;cACnCiB,OAAO,CAACC,IAAI,CAAC,+CAA+C,CAAC;cAC7D,OAAO,CAAC,CAAC,CAAC,WAAW;YACzB;YACAtD,EAAE,CAACjE,KAAK,GAAG,CAAC;UAChB,KAAK,CAAC;YACFiE,EAAE,CAAC9D,IAAI,CAACe,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAI,CAAC,CAAC,CAAC;YACzB,OAAO,CAAC,CAAC,CAAC,WAAWmF,YAAY,CAACmB,IAAI,CAAC,CAAC,CAAC;UAC7C,KAAK,CAAC;YACFvD,EAAE,CAAChE,IAAI,CAAC,CAAC;YACT,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;UAC3B,KAAK,CAAC;YACF+D,EAAE,GAAGC,EAAE,CAAChE,IAAI,CAAC,CAAC;YACdqH,OAAO,CAACC,IAAI,CAAC,wCAAwC,CAAC;YACtD,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;UAC3B,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC,CAAC,WAAW;QACjC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD;AACJ;AACA;EACIvF,iBAAiB,CAACS,SAAS,CAACgF,eAAe,GAAG,UAAUC,cAAc,EAAEC,IAAI,EAAE;IAC1E,IAAIC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAACJ,cAAc,CAAC;IAC1D,IAAI,CAACE,YAAY,EAAE;MACf,MAAM,IAAIpG,iBAAiB,CAAC,mBAAmB,GAAGkG,cAAc,GAAG,aAAa,CAAC;IACrF;IACA,IAAIE,YAAY,CAACG,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAKL,IAAI,CAACK,WAAW,CAAC,CAAC,EAAE;MAC5D,MAAM,IAAIxG,iBAAiB,CAAC,mBAAmB,GAAGkG,cAAc,GAAG,eAAe,GAAGC,IAAI,GAAG,UAAU,CAAC;IAC3G;IACA,OAAOC,YAAY;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI5F,iBAAiB,CAACS,SAAS,CAACwF,eAAe,GAAG,UAAUC,MAAM,EAAEC,GAAG,EAAE;IACjE,IAAI,CAACD,MAAM,IAAI,CAACC,GAAG,EAAE;MACjB,MAAM,IAAI3G,iBAAiB,CAAC,+DAA+D,CAAC;IAChG;IACA,IAAI2G,GAAG,IAAI,CAACD,MAAM,EAAE;MAChB,OAAO,IAAI,CAACE,kBAAkB,CAACD,GAAG,CAAC;IACvC;IACA,OAAO,IAAI,CAACE,sBAAsB,CAACH,MAAM,CAAC;EAC9C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlG,iBAAiB,CAACS,SAAS,CAAC6F,eAAe,GAAG,UAAUJ,MAAM,EAAEC,GAAG,EAAE;IACjE,IAAI,CAACD,MAAM,IAAI,CAACC,GAAG,EAAE;MACjB,MAAM,IAAI3G,iBAAiB,CAAC,6DAA6D,CAAC;IAC9F;IACA,IAAI2G,GAAG,IAAI,CAACD,MAAM,EAAE;MAChB,OAAO,IAAI,CAACK,kBAAkB,CAACJ,GAAG,CAAC;IACvC;IACA,OAAO,IAAI,CAACK,sBAAsB,CAACN,MAAM,CAAC;EAC9C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIlG,iBAAiB,CAACS,SAAS,CAACgG,2BAA2B,GAAG,UAAUP,MAAM,EAAEC,GAAG,EAAErC,UAAU,EAAE;IACzF,IAAI4C,SAAS,KAAKR,MAAM,IAAIQ,SAAS,KAAKP,GAAG,EAAE;MAC3C,MAAM,IAAI3G,iBAAiB,CAAC,6DAA6D,CAAC;IAC9F;IACA,IAAI2G,GAAG,IAAI,CAACD,MAAM,EAAE;MAChB,OAAO,IAAI,CAACS,8BAA8B,CAACR,GAAG,EAAErC,UAAU,CAAC;IAC/D;IACA,OAAO,IAAI,CAAC8C,kCAAkC,CAACV,MAAM,EAAEpC,UAAU,CAAC;EACtE,CAAC;EACD;AACJ;AACA;EACI9D,iBAAiB,CAACS,SAAS,CAAC4F,sBAAsB,GAAG,UAAUH,MAAM,EAAE;IACnE,IAAI,CAACA,MAAM,EAAE;MACT,MAAM,IAAI1G,iBAAiB,CAAC,oCAAoC,CAAC;IACrE;IACA,IAAI,CAAC2D,KAAK,CAAC,CAAC;IACZ,IAAIwB,OAAO,GAAG,IAAI,CAACkC,mBAAmB,CAACX,MAAM,CAAC;IAC9C,IAAI,CAACY,YAAY,GAAGnC,OAAO;IAC3B,IAAIoC,IAAI;IACR,IAAI,IAAI,CAACC,aAAa,CAACrC,OAAO,CAAC,EAAE;MAC7BoC,IAAI,GAAG,IAAI,CAACnD,UAAU,CAACe,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC;IAChD,CAAC,MACI;MACDoC,IAAI,GAAG,IAAI,CAACE,kBAAkB,CAACtC,OAAO,CAAC;IAC3C;IACA,OAAOoC,IAAI;EACf,CAAC;EACD;AACJ;AACA;EACI/G,iBAAiB,CAACS,SAAS,CAAC+F,sBAAsB,GAAG,UAAUN,MAAM,EAAE;IACnE,IAAIvB,OAAO,GAAG,IAAI,CAACuC,4BAA4B,CAAChB,MAAM,CAAC;IACvD,OAAO,IAAI,CAACiB,kBAAkB,CAACxC,OAAO,CAAC;EAC3C,CAAC;EACD;AACJ;AACA;EACI3E,iBAAiB,CAACS,SAAS,CAACmG,kCAAkC,GAAG,UAAUV,MAAM,EAAEpC,UAAU,EAAE;IAC3F,IAAIa,OAAO,GAAG,IAAI,CAACuC,4BAA4B,CAAChB,MAAM,CAAC;IACvD,OAAO,IAAI,CAACkB,8BAA8B,CAACzC,OAAO,EAAEb,UAAU,CAAC;EACnE,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI9D,iBAAiB,CAACS,SAAS,CAACyG,4BAA4B,GAAG,UAAUhB,MAAM,EAAE;IACzE,IAAI,CAACA,MAAM,EAAE;MACT,MAAM,IAAI1G,iBAAiB,CAAC,mCAAmC,CAAC;IACpE;IACA,IAAI,CAAC2D,KAAK,CAAC,CAAC;IACZ,IAAIwB,OAAO,GAAG,IAAI,CAACL,mBAAmB,CAAC4B,MAAM,CAAC;IAC9C;IACA,IAAI,CAAC7B,YAAY,GAAGM,OAAO;IAC3B,OAAOA,OAAO;EAClB,CAAC;EACD;AACJ;AACA;EACI3E,iBAAiB,CAACS,SAAS,CAAC2F,kBAAkB,GAAG,UAAUD,GAAG,EAAE;IAC5D,IAAI,CAACA,GAAG,EAAE;MACN,MAAM,IAAI3G,iBAAiB,CAAC,0BAA0B,CAAC;IAC3D;IACA,IAAI,CAAC2D,KAAK,CAAC,CAAC;IACZ,IAAIwB,OAAO,GAAG,IAAI,CAACkC,mBAAmB,CAAC,CAAC;IACxC,IAAI,CAACC,YAAY,GAAGnC,OAAO;IAC3B,IAAI0C,UAAU,GAAG,IAAI,CAACJ,kBAAkB,CAACtC,OAAO,CAAC;IACjDA,OAAO,CAAC2C,GAAG,GAAGnB,GAAG;IACjB,OAAOkB,UAAU;EACrB,CAAC;EACD;AACJ;AACA;EACIrH,iBAAiB,CAACS,SAAS,CAAC8F,kBAAkB,GAAG,UAAUJ,GAAG,EAAE;IAC5D,IAAI,CAACA,GAAG,EAAE;MACN,MAAM,IAAI3G,iBAAiB,CAAC,0BAA0B,CAAC;IAC3D;IACA,IAAI,CAAC2D,KAAK,CAAC,CAAC;IACZ;IACA,IAAIwB,OAAO,GAAG,IAAI,CAACL,mBAAmB,CAAC,CAAC;IACxC,IAAI+C,UAAU,GAAG,IAAI,CAACb,sBAAsB,CAAC7B,OAAO,CAAC;IACrDA,OAAO,CAAC2C,GAAG,GAAGnB,GAAG;IACjB,OAAOkB,UAAU;EACrB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIrH,iBAAiB,CAACS,SAAS,CAACkG,8BAA8B,GAAG,UAAUR,GAAG,EAAErC,UAAU,EAAE;IACpF,IAAI,CAACqC,GAAG,EAAE;MACN,MAAM,IAAI3G,iBAAiB,CAAC,0BAA0B,CAAC;IAC3D;IACA,IAAI,CAAC2D,KAAK,CAAC,CAAC;IACZ;IACA,IAAIwB,OAAO,GAAG,IAAI,CAACL,mBAAmB,CAAC,CAAC;IACxC,IAAI+C,UAAU,GAAG,IAAI,CAACT,kCAAkC,CAACjC,OAAO,EAAEb,UAAU,CAAC;IAC7Ea,OAAO,CAAC2C,GAAG,GAAGnB,GAAG;IACjB,OAAOkB,UAAU;EACrB,CAAC;EACDrH,iBAAiB,CAACS,SAAS,CAACwG,kBAAkB,GAAG,UAAUtC,OAAO,EAAE;IAChE,IAAIF,KAAK,GAAG,IAAI;IAChB,OAAO,IAAIvH,OAAO,CAAC,UAAUD,OAAO,EAAEE,MAAM,EAAE;MAC1CsH,KAAK,CAAC8C,mBAAmB,GAAG,YAAY;QACpC,OAAO9C,KAAK,CAACb,UAAU,CAACe,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAChH,IAAI,CAACV,OAAO,EAAEE,MAAM,CAAC;MACvE,CAAC;MACDwH,OAAO,CAACK,gBAAgB,CAAC,MAAM,EAAEP,KAAK,CAAC8C,mBAAmB,CAAC;IAC/D,CAAC,CAAC;EACN,CAAC;EACDvH,iBAAiB,CAACS,SAAS,CAAC0G,kBAAkB,GAAG,UAAU9C,YAAY,EAAE;IACrE,OAAO3H,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,OAAOmB,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YACN;YACA,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACwG,oBAAoB,CAACH,YAAY,CAAC,CAAC;UAC7D,KAAK,CAAC;YACF;YACArC,EAAE,CAAC/D,IAAI,CAAC,CAAC;YACT,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC2F,UAAU,CAACS,YAAY,CAAC,CAAC;UACvD,KAAK,CAAC;YACN;YACA,OAAO,CAAC,CAAC,CAAC,YAAYrC,EAAE,CAAC/D,IAAI,CAAC,CAAC,CAAC;QACpC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD+B,iBAAiB,CAACS,SAAS,CAAC2G,8BAA8B,GAAG,UAAU/C,YAAY,EAAEP,UAAU,EAAE;IAC7F,OAAOpH,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAC/C,OAAOmB,WAAW,CAAC,IAAI,EAAE,UAAUmE,EAAE,EAAE;QACnC,QAAQA,EAAE,CAAChE,KAAK;UACZ,KAAK,CAAC;YACN;YACA,OAAO,CAAC,CAAC,CAAC,WAAW,IAAI,CAACwG,oBAAoB,CAACH,YAAY,CAAC,CAAC;UAC7D,KAAK,CAAC;YACF;YACArC,EAAE,CAAC/D,IAAI,CAAC,CAAC;YACT;YACA,IAAI,CAACiG,kBAAkB,CAACG,YAAY,EAAEP,UAAU,CAAC;YACjD,OAAO,CAAC,CAAC,CAAC,WAAW;QAC7B;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EACD9D,iBAAiB,CAACS,SAAS,CAACuG,aAAa,GAAG,UAAUQ,GAAG,EAAE;IACvD;IACA;IACA;IACA,IAAI,CAACA,GAAG,CAACC,QAAQ,EAAE;MACf,OAAO,KAAK;IAChB;IACA;IACA;IACA;IACA,IAAID,GAAG,CAACE,YAAY,KAAK,CAAC,EAAE;MACxB,OAAO,KAAK;IAChB;IACA;IACA,OAAO,IAAI;EACf,CAAC;EACD1H,iBAAiB,CAACS,SAAS,CAACoG,mBAAmB,GAAG,UAAUc,WAAW,EAAE;IACrE,IAAIb,YAAY;IAChB,IAAI,OAAOa,WAAW,KAAK,WAAW,EAAE;MACpCb,YAAY,GAAGjB,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC;MAC5Cd,YAAY,CAACe,KAAK,GAAG,GAAG;MACxBf,YAAY,CAACgB,MAAM,GAAG,GAAG;IAC7B;IACA,IAAI,OAAOH,WAAW,KAAK,QAAQ,EAAE;MACjCb,YAAY,GAAG,IAAI,CAACrB,eAAe,CAACkC,WAAW,EAAE,KAAK,CAAC;IAC3D;IACA,IAAIA,WAAW,YAAYI,gBAAgB,EAAE;MACzCjB,YAAY,GAAGa,WAAW;IAC9B;IACA,OAAOb,YAAY;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI9G,iBAAiB,CAACS,SAAS,CAAC6D,mBAAmB,GAAG,UAAUvB,WAAW,EAAE;IACrE,IAAIsB,YAAY;IAChB,IAAI,CAACtB,WAAW,IAAI,OAAO8C,QAAQ,KAAK,WAAW,EAAE;MACjDxB,YAAY,GAAGwB,QAAQ,CAAC+B,aAAa,CAAC,OAAO,CAAC;MAC9CvD,YAAY,CAACwD,KAAK,GAAG,GAAG;MACxBxD,YAAY,CAACyD,MAAM,GAAG,GAAG;IAC7B;IACA,IAAI,OAAO/E,WAAW,KAAK,QAAQ,EAAE;MACjCsB,YAAY,GAAI,IAAI,CAACoB,eAAe,CAAC1C,WAAW,EAAE,OAAO,CAAE;IAC/D;IACA,IAAIA,WAAW,YAAYiF,gBAAgB,EAAE;MACzC3D,YAAY,GAAGtB,WAAW;IAC9B;IACA;IACAsB,YAAY,CAAC4D,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC;IAC7C5D,YAAY,CAAC4D,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;IAC1C5D,YAAY,CAAC4D,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAChD,OAAO5D,YAAY;EACvB,CAAC;EACD;AACJ;AACA;EACIrE,iBAAiB,CAACS,SAAS,CAACmD,UAAU,GAAG,UAAUe,OAAO,EAAEuD,eAAe,EAAEC,4BAA4B,EAAE;IACvG,IAAI1D,KAAK,GAAG,IAAI;IAChB,IAAIyD,eAAe,KAAK,KAAK,CAAC,EAAE;MAAEA,eAAe,GAAG,IAAI;IAAE;IAC1D,IAAIC,4BAA4B,KAAK,KAAK,CAAC,EAAE;MAAEA,4BAA4B,GAAG,IAAI;IAAE;IACpF,IAAI,CAAC9H,gBAAgB,GAAG,KAAK;IAC7B,IAAI+H,IAAI,GAAG,SAAAA,CAAUnL,OAAO,EAAEE,MAAM,EAAE;MAClC,IAAIsH,KAAK,CAACpE,gBAAgB,EAAE;QACxBlD,MAAM,CAAC,IAAI0C,iBAAiB,CAAC,2DAA2D,CAAC,CAAC;QAC1F4E,KAAK,CAACpE,gBAAgB,GAAGqG,SAAS;QAClC;MACJ;MACA,IAAI;QACA,IAAIjJ,MAAM,GAAGgH,KAAK,CAAC4D,MAAM,CAAC1D,OAAO,CAAC;QAClC1H,OAAO,CAACQ,MAAM,CAAC;MACnB,CAAC,CACD,OAAOF,CAAC,EAAE;QACN,IAAI+K,UAAU,GAAGJ,eAAe,IAAI3K,CAAC,YAAYsC,iBAAiB;QAClE,IAAI0I,uBAAuB,GAAGhL,CAAC,YAAYmC,iBAAiB,IAAInC,CAAC,YAAYqC,eAAe;QAC5F,IAAI4I,kBAAkB,GAAGD,uBAAuB,IAAIJ,4BAA4B;QAChF,IAAIG,UAAU,IAAIE,kBAAkB,EAAE;UAClC;UACA,OAAOC,UAAU,CAACL,IAAI,EAAE3D,KAAK,CAACnE,4BAA4B,EAAErD,OAAO,EAAEE,MAAM,CAAC;QAChF;QACAA,MAAM,CAACI,CAAC,CAAC;MACb;IACJ,CAAC;IACD,OAAO,IAAIL,OAAO,CAAC,UAAUD,OAAO,EAAEE,MAAM,EAAE;MAAE,OAAOiL,IAAI,CAACnL,OAAO,EAAEE,MAAM,CAAC;IAAE,CAAC,CAAC;EACpF,CAAC;EACD;AACJ;AACA;EACI6C,iBAAiB,CAACS,SAAS,CAACyD,kBAAkB,GAAG,UAAUS,OAAO,EAAEb,UAAU,EAAE;IAC5E,IAAIW,KAAK,GAAG,IAAI;IAChB,IAAI,CAACrE,qBAAqB,GAAG,KAAK;IAClC,IAAIgI,IAAI,GAAG,SAAAA,CAAA,EAAY;MACnB,IAAI3D,KAAK,CAACrE,qBAAqB,EAAE;QAC7BqE,KAAK,CAACrE,qBAAqB,GAAGsG,SAAS;QACvC;MACJ;MACA,IAAI;QACA,IAAIjJ,MAAM,GAAGgH,KAAK,CAAC4D,MAAM,CAAC1D,OAAO,CAAC;QAClCb,UAAU,CAACrG,MAAM,EAAE,IAAI,CAAC;QACxBgL,UAAU,CAACL,IAAI,EAAE3D,KAAK,CAACvE,sBAAsB,CAAC;MAClD,CAAC,CACD,OAAO3C,CAAC,EAAE;QACNuG,UAAU,CAAC,IAAI,EAAEvG,CAAC,CAAC;QACnB,IAAIgL,uBAAuB,GAAGhL,CAAC,YAAYmC,iBAAiB,IAAInC,CAAC,YAAYqC,eAAe;QAC5F,IAAI8I,UAAU,GAAGnL,CAAC,YAAYsC,iBAAiB;QAC/C,IAAI0I,uBAAuB,IAAIG,UAAU,EAAE;UACvC;UACAD,UAAU,CAACL,IAAI,EAAE3D,KAAK,CAACnE,4BAA4B,CAAC;QACxD;MACJ;IACJ,CAAC;IACD8H,IAAI,CAAC,CAAC;EACV,CAAC;EACD;AACJ;AACA;EACIpI,iBAAiB,CAACS,SAAS,CAAC4H,MAAM,GAAG,UAAU1D,OAAO,EAAE;IACpD;IACA,IAAIgE,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACjE,OAAO,CAAC;IACnD,OAAO,IAAI,CAACkE,YAAY,CAACF,YAAY,CAAC;EAC1C,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI3I,iBAAiB,CAACS,SAAS,CAACmI,kBAAkB,GAAG,UAAUhD,YAAY,EAAE;IACrE,IAAIkD,GAAG,GAAG,IAAI,CAACC,uBAAuB,CAACnD,YAAY,CAAC;IACpD;IACA,IAAIoD,YAAY,GAAG,KAAK;IACxB,IAAIpD,YAAY,YAAYoC,gBAAgB,EAAE;MAC1C,IAAI,CAACiB,iBAAiB,CAACrD,YAAY,CAAC;MACpCoD,YAAY,GAAG,IAAI;IACvB,CAAC,MACI;MACD,IAAI,CAACE,iBAAiB,CAACtD,YAAY,CAAC;IACxC;IACA,IAAIuD,MAAM,GAAG,IAAI,CAACC,gBAAgB,CAACxD,YAAY,CAAC;IAChD,IAAIyD,eAAe,GAAG,IAAIvJ,gCAAgC,CAACqJ,MAAM,EAAEH,YAAY,CAAC;IAChF,IAAIM,eAAe,GAAG,IAAI3J,eAAe,CAAC0J,eAAe,CAAC;IAC1D,OAAO,IAAI5J,YAAY,CAAC6J,eAAe,CAAC;EAC5C,CAAC;EACD;AACJ;AACA;EACItJ,iBAAiB,CAACS,SAAS,CAACsI,uBAAuB,GAAG,UAAUnD,YAAY,EAAE;IAC1E,IAAI,CAAC,IAAI,CAAC2D,oBAAoB,EAAE;MAC5B,IAAIC,IAAI,GAAG,IAAI,CAACJ,gBAAgB,CAACxD,YAAY,CAAC;MAC9C,IAAIkD,GAAG,GAAG,KAAK,CAAC;MAChB,IAAI;QACAA,GAAG,GAAGU,IAAI,CAACC,UAAU,CAAC,IAAI,EAAE;UAAEC,kBAAkB,EAAE;QAAK,CAAC,CAAC;MAC7D,CAAC,CACD,OAAOnM,CAAC,EAAE;QACNuL,GAAG,GAAGU,IAAI,CAACC,UAAU,CAAC,IAAI,CAAC;MAC/B;MACA,IAAI,CAACF,oBAAoB,GAAGT,GAAG;IACnC;IACA,OAAO,IAAI,CAACS,oBAAoB;EACpC,CAAC;EACD;AACJ;AACA;EACIvJ,iBAAiB,CAACS,SAAS,CAAC2I,gBAAgB,GAAG,UAAUxD,YAAY,EAAE;IACnE,IAAI,CAAC,IAAI,CAAC+D,aAAa,EAAE;MACrB,IAAIH,IAAI,GAAG,IAAI,CAACI,mBAAmB,CAAChE,YAAY,CAAC;MACjD,IAAI,CAAC+D,aAAa,GAAGH,IAAI;IAC7B;IACA,OAAO,IAAI,CAACG,aAAa;EAC7B,CAAC;EACD;AACJ;AACA;EACI3J,iBAAiB,CAACS,SAAS,CAACwI,iBAAiB,GAAG,UAAUY,UAAU,EAAEC,UAAU,EAAEC,oBAAoB,EAAE;IACpG,IAAID,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG;QACtCE,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAEL,UAAU,CAACM,UAAU;QAC7BC,OAAO,EAAEP,UAAU,CAACQ,WAAW;QAC/BC,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAEX,UAAU,CAACM,UAAU;QAC7BM,OAAO,EAAEZ,UAAU,CAACQ;MACxB,CAAC;IAAE;IACH,IAAIN,oBAAoB,KAAK,KAAK,CAAC,EAAE;MAAEA,oBAAoB,GAAG,IAAI,CAACR,oBAAoB;IAAE;IACzFQ,oBAAoB,CAACW,SAAS,CAACb,UAAU,EAAEC,UAAU,CAACE,EAAE,EAAEF,UAAU,CAACG,EAAE,EAAEH,UAAU,CAACI,MAAM,EAAEJ,UAAU,CAACM,OAAO,EAAEN,UAAU,CAACQ,EAAE,EAAER,UAAU,CAACS,EAAE,EAAET,UAAU,CAACU,MAAM,EAAEV,UAAU,CAACW,OAAO,CAAC;EACxL,CAAC;EACD;AACJ;AACA;EACIzK,iBAAiB,CAACS,SAAS,CAACyI,iBAAiB,GAAG,UAAUW,UAAU,EAAEC,UAAU,EAAEC,oBAAoB,EAAE;IACpG,IAAID,UAAU,KAAK,KAAK,CAAC,EAAE;MAAEA,UAAU,GAAG;QACtCE,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAEL,UAAU,CAACnC,YAAY;QAC/B0C,OAAO,EAAEP,UAAU,CAACc,aAAa;QACjCL,EAAE,EAAE,CAAC;QACLC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAEX,UAAU,CAACnC,YAAY;QAC/B+C,OAAO,EAAEZ,UAAU,CAACc;MACxB,CAAC;IAAE;IACH,IAAIZ,oBAAoB,KAAK,KAAK,CAAC,EAAE;MAAEA,oBAAoB,GAAG,IAAI,CAACR,oBAAoB;IAAE;IACzFQ,oBAAoB,CAACW,SAAS,CAACb,UAAU,EAAEC,UAAU,CAACE,EAAE,EAAEF,UAAU,CAACG,EAAE,EAAEH,UAAU,CAACI,MAAM,EAAEJ,UAAU,CAACM,OAAO,EAAEN,UAAU,CAACQ,EAAE,EAAER,UAAU,CAACS,EAAE,EAAET,UAAU,CAACU,MAAM,EAAEV,UAAU,CAACW,OAAO,CAAC;EACxL,CAAC;EACD;AACJ;AACA;EACIzK,iBAAiB,CAACS,SAAS,CAACoI,YAAY,GAAG,UAAUF,YAAY,EAAE;IAC/D,OAAO,IAAI,CAAC1I,MAAM,CAACoI,MAAM,CAACM,YAAY,EAAE,IAAI,CAACxI,MAAM,CAAC;EACxD,CAAC;EACD;AACJ;AACA;EACIH,iBAAiB,CAACS,SAAS,CAACmJ,mBAAmB,GAAG,UAAUhE,YAAY,EAAE;IACtE,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACjC,IAAI,CAAC+E,qBAAqB,CAAC,CAAC;MAC5B,OAAO,IAAI;IACf;IACA,IAAIC,aAAa,GAAGhF,QAAQ,CAAC+B,aAAa,CAAC,QAAQ,CAAC;IACpD,IAAIC,KAAK;IACT,IAAIC,MAAM;IACV,IAAI,OAAOlC,YAAY,KAAK,WAAW,EAAE;MACrC,IAAIA,YAAY,YAAYoC,gBAAgB,EAAE;QAC1CH,KAAK,GAAGjC,YAAY,CAACuE,UAAU;QAC/BrC,MAAM,GAAGlC,YAAY,CAACyE,WAAW;MACrC,CAAC,MACI,IAAIzE,YAAY,YAAYmC,gBAAgB,EAAE;QAC/CF,KAAK,GAAGjC,YAAY,CAAC8B,YAAY,IAAI9B,YAAY,CAACiC,KAAK;QACvDC,MAAM,GAAGlC,YAAY,CAAC+E,aAAa,IAAI/E,YAAY,CAACkC,MAAM;MAC9D;IACJ;IACA+C,aAAa,CAACC,KAAK,CAACjD,KAAK,GAAGA,KAAK,GAAG,IAAI;IACxCgD,aAAa,CAACC,KAAK,CAAChD,MAAM,GAAGA,MAAM,GAAG,IAAI;IAC1C+C,aAAa,CAAChD,KAAK,GAAGA,KAAK;IAC3BgD,aAAa,CAAC/C,MAAM,GAAGA,MAAM;IAC7B,OAAO+C,aAAa;EACxB,CAAC;EACD;AACJ;AACA;EACI7K,iBAAiB,CAACS,SAAS,CAACoE,WAAW,GAAG,YAAY;IAClD,IAAI,IAAI,CAACrB,MAAM,EAAE;MACb,IAAI,CAACA,MAAM,CAACuH,cAAc,CAAC,CAAC,CAACC,OAAO,CAAC,UAAU9M,CAAC,EAAE;QAAE,OAAOA,CAAC,CAAC+M,IAAI,CAAC,CAAC;MAAE,CAAC,CAAC;MACvE,IAAI,CAACzH,MAAM,GAAGkD,SAAS;IAC3B;IACA,IAAI,IAAI,CAACrG,gBAAgB,KAAK,KAAK,EAAE;MACjC,IAAI,CAAC8D,eAAe,CAAC,CAAC;IAC1B;IACA,IAAI,IAAI,CAAC/D,qBAAqB,KAAK,KAAK,EAAE;MACtC,IAAI,CAACgE,oBAAoB,CAAC,CAAC;IAC/B;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIpE,iBAAiB,CAACS,SAAS,CAAC0C,KAAK,GAAG,YAAY;IAC5C;IACA,IAAI,CAAC0B,WAAW,CAAC,CAAC;IAClB;IACA,IAAI,CAACqG,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACP,qBAAqB,CAAC,CAAC;EAChC,CAAC;EACD5K,iBAAiB,CAACS,SAAS,CAACyK,oBAAoB,GAAG,YAAY;IAC3D,IAAI,CAAC,IAAI,CAAC7G,YAAY,EAAE;MACpB;IACJ;IACA;IACA,IAAI,OAAO,IAAI,CAACO,kBAAkB,KAAK,WAAW,EAAE;MAChD,IAAI,CAACP,YAAY,CAAC+G,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACxG,kBAAkB,CAAC;IAC3E;IACA,IAAI,OAAO,IAAI,CAACyG,yBAAyB,KAAK,WAAW,EAAE;MACvD,IAAI,CAAChH,YAAY,CAAC+G,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACC,yBAAyB,CAAC;IACpF;IACA,IAAI,OAAO,IAAI,CAACvG,oBAAoB,KAAK,WAAW,EAAE;MAClD,IAAI,CAACT,YAAY,CAAC+G,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAACtG,oBAAoB,CAAC;IACtF;IACA;IACA,IAAI,CAACwG,gBAAgB,CAAC,IAAI,CAACjH,YAAY,CAAC;IACxC,IAAI,CAACA,YAAY,GAAGqC,SAAS;EACjC,CAAC;EACD1G,iBAAiB,CAACS,SAAS,CAAC0K,oBAAoB,GAAG,YAAY;IAC3D,IAAI,CAAC,IAAI,CAACrE,YAAY,EAAE;MACpB;IACJ;IACA;IACA,IAAIJ,SAAS,KAAK,IAAI,CAACa,mBAAmB,EAAE;MACxC,IAAI,CAACT,YAAY,CAACsE,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC7D,mBAAmB,CAAC;IAC3E;IACA;IACA,IAAI,CAACT,YAAY,CAACQ,GAAG,GAAGZ,SAAS;IACjC,IAAI,CAACI,YAAY,CAACyE,eAAe,CAAC,KAAK,CAAC;IACxC,IAAI,CAACzE,YAAY,GAAGJ,SAAS;EACjC,CAAC;EACD;AACJ;AACA;EACI1G,iBAAiB,CAACS,SAAS,CAACmK,qBAAqB,GAAG,YAAY;IAC5D;IACA,IAAI,CAACrB,oBAAoB,GAAG7C,SAAS;IACrC,IAAI,CAACiD,aAAa,GAAGjD,SAAS;EAClC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI1G,iBAAiB,CAACS,SAAS,CAAC8D,cAAc,GAAG,UAAUF,YAAY,EAAEb,MAAM,EAAE;IACzE;IACA,IAAI;MACA;MACAa,YAAY,CAACmH,SAAS,GAAGhI,MAAM;IACnC,CAAC,CACD,OAAOiI,GAAG,EAAE;MACR;MACA;MACApH,YAAY,CAACiD,GAAG,GAAGoE,GAAG,CAACC,eAAe,CAACnI,MAAM,CAAC;IAClD;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIxD,iBAAiB,CAACS,SAAS,CAAC6K,gBAAgB,GAAG,UAAUjH,YAAY,EAAE;IACnE,IAAI;MACAA,YAAY,CAACmH,SAAS,GAAG,IAAI;IACjC,CAAC,CACD,OAAOC,GAAG,EAAE;MACRpH,YAAY,CAACiD,GAAG,GAAG,EAAE;IACzB;IACA,IAAI,CAACjD,YAAY,CAACkH,eAAe,CAAC,KAAK,CAAC;EAC5C,CAAC;EACD,OAAOvL,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AACJ,SAASA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}