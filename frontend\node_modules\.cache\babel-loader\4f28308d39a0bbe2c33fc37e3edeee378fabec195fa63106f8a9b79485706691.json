{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BitArray from '../../common/BitArray';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nimport StringUtils from '../../common/StringUtils';\nimport BitMatrix from '../../common/BitMatrix';\nimport AztecCode from './AztecCode';\nimport ReedSolomonEncoder from '../../common/reedsolomon/ReedSolomonEncoder';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport Integer from '../../util/Integer';\n/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.aztec.encoder;\n// import com.google.zxing.common.BitArray;\n// import com.google.zxing.common.BitMatrix;\n// import com.google.zxing.common.reedsolomon.GenericGF;\n// import com.google.zxing.common.reedsolomon.ReedSolomonEncoder;\n/**\n * Generates Aztec 2D barcodes.\n *\n * <AUTHOR> Abdullaev\n */\nvar Encoder = /** @class */function () {\n  function Encoder() {}\n  /**\n   * Encodes the given binary content as an Aztec symbol\n   *\n   * @param data input data string\n   * @return Aztec symbol matrix with metadata\n   */\n  Encoder.encodeBytes = function (data) {\n    return Encoder.encode(data, Encoder.DEFAULT_EC_PERCENT, Encoder.DEFAULT_AZTEC_LAYERS);\n  };\n  /**\n   * Encodes the given binary content as an Aztec symbol\n   *\n   * @param data input data string\n   * @param minECCPercent minimal percentage of error check words (According to ISO/IEC 24778:2008,\n   *                      a minimum of 23% + 3 words is recommended)\n   * @param userSpecifiedLayers if non-zero, a user-specified value for the number of layers\n   * @return Aztec symbol matrix with metadata\n   */\n  Encoder.encode = function (data, minECCPercent, userSpecifiedLayers) {\n    // High-level encode\n    var bits = new HighLevelEncoder(data).encode();\n    // stuff bits and choose symbol size\n    var eccBits = Integer.truncDivision(bits.getSize() * minECCPercent, 100) + 11;\n    var totalSizeBits = bits.getSize() + eccBits;\n    var compact;\n    var layers;\n    var totalBitsInLayer;\n    var wordSize;\n    var stuffedBits;\n    if (userSpecifiedLayers !== Encoder.DEFAULT_AZTEC_LAYERS) {\n      compact = userSpecifiedLayers < 0;\n      layers = Math.abs(userSpecifiedLayers);\n      if (layers > (compact ? Encoder.MAX_NB_BITS_COMPACT : Encoder.MAX_NB_BITS)) {\n        throw new IllegalArgumentException(StringUtils.format('Illegal value %s for layers', userSpecifiedLayers));\n      }\n      totalBitsInLayer = Encoder.totalBitsInLayer(layers, compact);\n      wordSize = Encoder.WORD_SIZE[layers];\n      var usableBitsInLayers = totalBitsInLayer - totalBitsInLayer % wordSize;\n      stuffedBits = Encoder.stuffBits(bits, wordSize);\n      if (stuffedBits.getSize() + eccBits > usableBitsInLayers) {\n        throw new IllegalArgumentException('Data to large for user specified layer');\n      }\n      if (compact && stuffedBits.getSize() > wordSize * 64) {\n        // Compact format only allows 64 data words, though C4 can hold more words than that\n        throw new IllegalArgumentException('Data to large for user specified layer');\n      }\n    } else {\n      wordSize = 0;\n      stuffedBits = null;\n      // We look at the possible table sizes in the order Compact1, Compact2, Compact3,\n      // Compact4, Normal4,...  Normal(i) for i < 4 isn't typically used since Compact(i+1)\n      // is the same size, but has more data.\n      for (var i /*int*/ = 0;; i++) {\n        if (i > Encoder.MAX_NB_BITS) {\n          throw new IllegalArgumentException('Data too large for an Aztec code');\n        }\n        compact = i <= 3;\n        layers = compact ? i + 1 : i;\n        totalBitsInLayer = Encoder.totalBitsInLayer(layers, compact);\n        if (totalSizeBits > totalBitsInLayer) {\n          continue;\n        }\n        // [Re]stuff the bits if this is the first opportunity, or if the\n        // wordSize has changed\n        if (stuffedBits == null || wordSize !== Encoder.WORD_SIZE[layers]) {\n          wordSize = Encoder.WORD_SIZE[layers];\n          stuffedBits = Encoder.stuffBits(bits, wordSize);\n        }\n        var usableBitsInLayers = totalBitsInLayer - totalBitsInLayer % wordSize;\n        if (compact && stuffedBits.getSize() > wordSize * 64) {\n          // Compact format only allows 64 data words, though C4 can hold more words than that\n          continue;\n        }\n        if (stuffedBits.getSize() + eccBits <= usableBitsInLayers) {\n          break;\n        }\n      }\n    }\n    var messageBits = Encoder.generateCheckWords(stuffedBits, totalBitsInLayer, wordSize);\n    // generate mode message\n    var messageSizeInWords = stuffedBits.getSize() / wordSize;\n    var modeMessage = Encoder.generateModeMessage(compact, layers, messageSizeInWords);\n    // allocate symbol\n    var baseMatrixSize = (compact ? 11 : 14) + layers * 4; // not including alignment lines\n    var alignmentMap = new Int32Array(baseMatrixSize);\n    var matrixSize;\n    if (compact) {\n      // no alignment marks in compact mode, alignmentMap is a no-op\n      matrixSize = baseMatrixSize;\n      for (var i /*int*/ = 0; i < alignmentMap.length; i++) {\n        alignmentMap[i] = i;\n      }\n    } else {\n      matrixSize = baseMatrixSize + 1 + 2 * Integer.truncDivision(Integer.truncDivision(baseMatrixSize, 2) - 1, 15);\n      var origCenter = Integer.truncDivision(baseMatrixSize, 2);\n      var center = Integer.truncDivision(matrixSize, 2);\n      for (var i /*int*/ = 0; i < origCenter; i++) {\n        var newOffset = i + Integer.truncDivision(i, 15);\n        alignmentMap[origCenter - i - 1] = center - newOffset - 1;\n        alignmentMap[origCenter + i] = center + newOffset + 1;\n      }\n    }\n    var matrix = new BitMatrix(matrixSize);\n    // draw data bits\n    for (var i /*int*/ = 0, rowOffset = 0; i < layers; i++) {\n      var rowSize = (layers - i) * 4 + (compact ? 9 : 12);\n      for (var j /*int*/ = 0; j < rowSize; j++) {\n        var columnOffset = j * 2;\n        for (var k /*int*/ = 0; k < 2; k++) {\n          if (messageBits.get(rowOffset + columnOffset + k)) {\n            matrix.set(alignmentMap[i * 2 + k], alignmentMap[i * 2 + j]);\n          }\n          if (messageBits.get(rowOffset + rowSize * 2 + columnOffset + k)) {\n            matrix.set(alignmentMap[i * 2 + j], alignmentMap[baseMatrixSize - 1 - i * 2 - k]);\n          }\n          if (messageBits.get(rowOffset + rowSize * 4 + columnOffset + k)) {\n            matrix.set(alignmentMap[baseMatrixSize - 1 - i * 2 - k], alignmentMap[baseMatrixSize - 1 - i * 2 - j]);\n          }\n          if (messageBits.get(rowOffset + rowSize * 6 + columnOffset + k)) {\n            matrix.set(alignmentMap[baseMatrixSize - 1 - i * 2 - j], alignmentMap[i * 2 + k]);\n          }\n        }\n      }\n      rowOffset += rowSize * 8;\n    }\n    // draw mode message\n    Encoder.drawModeMessage(matrix, compact, matrixSize, modeMessage);\n    // draw alignment marks\n    if (compact) {\n      Encoder.drawBullsEye(matrix, Integer.truncDivision(matrixSize, 2), 5);\n    } else {\n      Encoder.drawBullsEye(matrix, Integer.truncDivision(matrixSize, 2), 7);\n      for (var i /*int*/ = 0, j = 0; i < Integer.truncDivision(baseMatrixSize, 2) - 1; i += 15, j += 16) {\n        for (var k /*int*/ = Integer.truncDivision(matrixSize, 2) & 1; k < matrixSize; k += 2) {\n          matrix.set(Integer.truncDivision(matrixSize, 2) - j, k);\n          matrix.set(Integer.truncDivision(matrixSize, 2) + j, k);\n          matrix.set(k, Integer.truncDivision(matrixSize, 2) - j);\n          matrix.set(k, Integer.truncDivision(matrixSize, 2) + j);\n        }\n      }\n    }\n    var aztec = new AztecCode();\n    aztec.setCompact(compact);\n    aztec.setSize(matrixSize);\n    aztec.setLayers(layers);\n    aztec.setCodeWords(messageSizeInWords);\n    aztec.setMatrix(matrix);\n    return aztec;\n  };\n  Encoder.drawBullsEye = function (matrix, center, size) {\n    for (var i /*int*/ = 0; i < size; i += 2) {\n      for (var j /*int*/ = center - i; j <= center + i; j++) {\n        matrix.set(j, center - i);\n        matrix.set(j, center + i);\n        matrix.set(center - i, j);\n        matrix.set(center + i, j);\n      }\n    }\n    matrix.set(center - size, center - size);\n    matrix.set(center - size + 1, center - size);\n    matrix.set(center - size, center - size + 1);\n    matrix.set(center + size, center - size);\n    matrix.set(center + size, center - size + 1);\n    matrix.set(center + size, center + size - 1);\n  };\n  Encoder.generateModeMessage = function (compact, layers, messageSizeInWords) {\n    var modeMessage = new BitArray();\n    if (compact) {\n      modeMessage.appendBits(layers - 1, 2);\n      modeMessage.appendBits(messageSizeInWords - 1, 6);\n      modeMessage = Encoder.generateCheckWords(modeMessage, 28, 4);\n    } else {\n      modeMessage.appendBits(layers - 1, 5);\n      modeMessage.appendBits(messageSizeInWords - 1, 11);\n      modeMessage = Encoder.generateCheckWords(modeMessage, 40, 4);\n    }\n    return modeMessage;\n  };\n  Encoder.drawModeMessage = function (matrix, compact, matrixSize, modeMessage) {\n    var center = Integer.truncDivision(matrixSize, 2);\n    if (compact) {\n      for (var i /*int*/ = 0; i < 7; i++) {\n        var offset = center - 3 + i;\n        if (modeMessage.get(i)) {\n          matrix.set(offset, center - 5);\n        }\n        if (modeMessage.get(i + 7)) {\n          matrix.set(center + 5, offset);\n        }\n        if (modeMessage.get(20 - i)) {\n          matrix.set(offset, center + 5);\n        }\n        if (modeMessage.get(27 - i)) {\n          matrix.set(center - 5, offset);\n        }\n      }\n    } else {\n      for (var i /*int*/ = 0; i < 10; i++) {\n        var offset = center - 5 + i + Integer.truncDivision(i, 5);\n        if (modeMessage.get(i)) {\n          matrix.set(offset, center - 7);\n        }\n        if (modeMessage.get(i + 10)) {\n          matrix.set(center + 7, offset);\n        }\n        if (modeMessage.get(29 - i)) {\n          matrix.set(offset, center + 7);\n        }\n        if (modeMessage.get(39 - i)) {\n          matrix.set(center - 7, offset);\n        }\n      }\n    }\n  };\n  Encoder.generateCheckWords = function (bitArray, totalBits, wordSize) {\n    var e_1, _a;\n    // bitArray is guaranteed to be a multiple of the wordSize, so no padding needed\n    var messageSizeInWords = bitArray.getSize() / wordSize;\n    var rs = new ReedSolomonEncoder(Encoder.getGF(wordSize));\n    var totalWords = Integer.truncDivision(totalBits, wordSize);\n    var messageWords = Encoder.bitsToWords(bitArray, wordSize, totalWords);\n    rs.encode(messageWords, totalWords - messageSizeInWords);\n    var startPad = totalBits % wordSize;\n    var messageBits = new BitArray();\n    messageBits.appendBits(0, startPad);\n    try {\n      for (var _b = __values(Array.from(messageWords)), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var messageWord = _c.value /*: int*/;\n        messageBits.appendBits(messageWord, wordSize);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    return messageBits;\n  };\n  Encoder.bitsToWords = function (stuffedBits, wordSize, totalWords) {\n    var message = new Int32Array(totalWords);\n    var i;\n    var n;\n    for (i = 0, n = stuffedBits.getSize() / wordSize; i < n; i++) {\n      var value = 0;\n      for (var j /*int*/ = 0; j < wordSize; j++) {\n        value |= stuffedBits.get(i * wordSize + j) ? 1 << wordSize - j - 1 : 0;\n      }\n      message[i] = value;\n    }\n    return message;\n  };\n  Encoder.getGF = function (wordSize) {\n    switch (wordSize) {\n      case 4:\n        return GenericGF.AZTEC_PARAM;\n      case 6:\n        return GenericGF.AZTEC_DATA_6;\n      case 8:\n        return GenericGF.AZTEC_DATA_8;\n      case 10:\n        return GenericGF.AZTEC_DATA_10;\n      case 12:\n        return GenericGF.AZTEC_DATA_12;\n      default:\n        throw new IllegalArgumentException('Unsupported word size ' + wordSize);\n    }\n  };\n  Encoder.stuffBits = function (bits, wordSize) {\n    var out = new BitArray();\n    var n = bits.getSize();\n    var mask = (1 << wordSize) - 2;\n    for (var i /*int*/ = 0; i < n; i += wordSize) {\n      var word = 0;\n      for (var j /*int*/ = 0; j < wordSize; j++) {\n        if (i + j >= n || bits.get(i + j)) {\n          word |= 1 << wordSize - 1 - j;\n        }\n      }\n      if ((word & mask) === mask) {\n        out.appendBits(word & mask, wordSize);\n        i--;\n      } else if ((word & mask) === 0) {\n        out.appendBits(word | 1, wordSize);\n        i--;\n      } else {\n        out.appendBits(word, wordSize);\n      }\n    }\n    return out;\n  };\n  Encoder.totalBitsInLayer = function (layers, compact) {\n    return ((compact ? 88 : 112) + 16 * layers) * layers;\n  };\n  Encoder.DEFAULT_EC_PERCENT = 33; // default minimal percentage of error check words\n  Encoder.DEFAULT_AZTEC_LAYERS = 0;\n  Encoder.MAX_NB_BITS = 32;\n  Encoder.MAX_NB_BITS_COMPACT = 4;\n  Encoder.WORD_SIZE = Int32Array.from([4, 6, 6, 8, 8, 8, 8, 8, 8, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 12, 12, 12, 12, 12, 12, 12, 12, 12, 12]);\n  return Encoder;\n}();\nexport default Encoder;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BitArray", "IllegalArgumentException", "StringUtils", "BitMatrix", "AztecCode", "ReedSolomonEncoder", "GenericGF", "HighLevelEncoder", "Integer", "Encoder", "encodeBytes", "data", "encode", "DEFAULT_EC_PERCENT", "DEFAULT_AZTEC_LAYERS", "minECCPercent", "userSpecifiedLayers", "bits", "eccBits", "truncDivision", "getSize", "totalSizeBits", "compact", "layers", "totalBitsInLayer", "wordSize", "stuffedBits", "Math", "abs", "MAX_NB_BITS_COMPACT", "MAX_NB_BITS", "format", "WORD_SIZE", "usableBitsInLayers", "stuffBits", "messageBits", "generateCheckWords", "messageSizeInWords", "modeMessage", "generateModeMessage", "baseMatrixSize", "alignmentMap", "Int32Array", "matrixSize", "origCenter", "center", "newOffset", "matrix", "rowOffset", "rowSize", "j", "columnOffset", "k", "get", "set", "drawModeMessage", "drawBullsEye", "aztec", "setCompact", "setSize", "setLayers", "setCodeWords", "setMatrix", "size", "appendBits", "offset", "bitArray", "totalBits", "e_1", "_a", "rs", "getGF", "totalWords", "messageWords", "bitsToWords", "startPad", "_b", "Array", "from", "_c", "messageWord", "e_1_1", "error", "return", "message", "n", "AZTEC_PARAM", "AZTEC_DATA_6", "AZTEC_DATA_8", "AZTEC_DATA_10", "AZTEC_DATA_12", "out", "mask", "word"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/Encoder.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nimport BitArray from '../../common/BitArray';\nimport IllegalArgumentException from '../../IllegalArgumentException';\nimport StringUtils from '../../common/StringUtils';\nimport BitMatrix from '../../common/BitMatrix';\nimport AztecCode from './AztecCode';\nimport ReedSolomonEncoder from '../../common/reedsolomon/ReedSolomonEncoder';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport Integer from '../../util/Integer';\n/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// package com.google.zxing.aztec.encoder;\n// import com.google.zxing.common.BitArray;\n// import com.google.zxing.common.BitMatrix;\n// import com.google.zxing.common.reedsolomon.GenericGF;\n// import com.google.zxing.common.reedsolomon.ReedSolomonEncoder;\n/**\n * Generates Aztec 2D barcodes.\n *\n * <AUTHOR> Abdullaev\n */\nvar Encoder = /** @class */ (function () {\n    function Encoder() {\n    }\n    /**\n     * Encodes the given binary content as an Aztec symbol\n     *\n     * @param data input data string\n     * @return Aztec symbol matrix with metadata\n     */\n    Encoder.encodeBytes = function (data) {\n        return Encoder.encode(data, Encoder.DEFAULT_EC_PERCENT, Encoder.DEFAULT_AZTEC_LAYERS);\n    };\n    /**\n     * Encodes the given binary content as an Aztec symbol\n     *\n     * @param data input data string\n     * @param minECCPercent minimal percentage of error check words (According to ISO/IEC 24778:2008,\n     *                      a minimum of 23% + 3 words is recommended)\n     * @param userSpecifiedLayers if non-zero, a user-specified value for the number of layers\n     * @return Aztec symbol matrix with metadata\n     */\n    Encoder.encode = function (data, minECCPercent, userSpecifiedLayers) {\n        // High-level encode\n        var bits = new HighLevelEncoder(data).encode();\n        // stuff bits and choose symbol size\n        var eccBits = Integer.truncDivision((bits.getSize() * minECCPercent), 100) + 11;\n        var totalSizeBits = bits.getSize() + eccBits;\n        var compact;\n        var layers;\n        var totalBitsInLayer;\n        var wordSize;\n        var stuffedBits;\n        if (userSpecifiedLayers !== Encoder.DEFAULT_AZTEC_LAYERS) {\n            compact = userSpecifiedLayers < 0;\n            layers = Math.abs(userSpecifiedLayers);\n            if (layers > (compact ? Encoder.MAX_NB_BITS_COMPACT : Encoder.MAX_NB_BITS)) {\n                throw new IllegalArgumentException(StringUtils.format('Illegal value %s for layers', userSpecifiedLayers));\n            }\n            totalBitsInLayer = Encoder.totalBitsInLayer(layers, compact);\n            wordSize = Encoder.WORD_SIZE[layers];\n            var usableBitsInLayers = totalBitsInLayer - (totalBitsInLayer % wordSize);\n            stuffedBits = Encoder.stuffBits(bits, wordSize);\n            if (stuffedBits.getSize() + eccBits > usableBitsInLayers) {\n                throw new IllegalArgumentException('Data to large for user specified layer');\n            }\n            if (compact && stuffedBits.getSize() > wordSize * 64) {\n                // Compact format only allows 64 data words, though C4 can hold more words than that\n                throw new IllegalArgumentException('Data to large for user specified layer');\n            }\n        }\n        else {\n            wordSize = 0;\n            stuffedBits = null;\n            // We look at the possible table sizes in the order Compact1, Compact2, Compact3,\n            // Compact4, Normal4,...  Normal(i) for i < 4 isn't typically used since Compact(i+1)\n            // is the same size, but has more data.\n            for (var i /*int*/ = 0;; i++) {\n                if (i > Encoder.MAX_NB_BITS) {\n                    throw new IllegalArgumentException('Data too large for an Aztec code');\n                }\n                compact = i <= 3;\n                layers = compact ? i + 1 : i;\n                totalBitsInLayer = Encoder.totalBitsInLayer(layers, compact);\n                if (totalSizeBits > totalBitsInLayer) {\n                    continue;\n                }\n                // [Re]stuff the bits if this is the first opportunity, or if the\n                // wordSize has changed\n                if (stuffedBits == null || wordSize !== Encoder.WORD_SIZE[layers]) {\n                    wordSize = Encoder.WORD_SIZE[layers];\n                    stuffedBits = Encoder.stuffBits(bits, wordSize);\n                }\n                var usableBitsInLayers = totalBitsInLayer - (totalBitsInLayer % wordSize);\n                if (compact && stuffedBits.getSize() > wordSize * 64) {\n                    // Compact format only allows 64 data words, though C4 can hold more words than that\n                    continue;\n                }\n                if (stuffedBits.getSize() + eccBits <= usableBitsInLayers) {\n                    break;\n                }\n            }\n        }\n        var messageBits = Encoder.generateCheckWords(stuffedBits, totalBitsInLayer, wordSize);\n        // generate mode message\n        var messageSizeInWords = stuffedBits.getSize() / wordSize;\n        var modeMessage = Encoder.generateModeMessage(compact, layers, messageSizeInWords);\n        // allocate symbol\n        var baseMatrixSize = (compact ? 11 : 14) + layers * 4; // not including alignment lines\n        var alignmentMap = new Int32Array(baseMatrixSize);\n        var matrixSize;\n        if (compact) {\n            // no alignment marks in compact mode, alignmentMap is a no-op\n            matrixSize = baseMatrixSize;\n            for (var i /*int*/ = 0; i < alignmentMap.length; i++) {\n                alignmentMap[i] = i;\n            }\n        }\n        else {\n            matrixSize = baseMatrixSize + 1 + 2 * Integer.truncDivision((Integer.truncDivision(baseMatrixSize, 2) - 1), 15);\n            var origCenter = Integer.truncDivision(baseMatrixSize, 2);\n            var center = Integer.truncDivision(matrixSize, 2);\n            for (var i /*int*/ = 0; i < origCenter; i++) {\n                var newOffset = i + Integer.truncDivision(i, 15);\n                alignmentMap[origCenter - i - 1] = center - newOffset - 1;\n                alignmentMap[origCenter + i] = center + newOffset + 1;\n            }\n        }\n        var matrix = new BitMatrix(matrixSize);\n        // draw data bits\n        for (var i /*int*/ = 0, rowOffset = 0; i < layers; i++) {\n            var rowSize = (layers - i) * 4 + (compact ? 9 : 12);\n            for (var j /*int*/ = 0; j < rowSize; j++) {\n                var columnOffset = j * 2;\n                for (var k /*int*/ = 0; k < 2; k++) {\n                    if (messageBits.get(rowOffset + columnOffset + k)) {\n                        matrix.set(alignmentMap[i * 2 + k], alignmentMap[i * 2 + j]);\n                    }\n                    if (messageBits.get(rowOffset + rowSize * 2 + columnOffset + k)) {\n                        matrix.set(alignmentMap[i * 2 + j], alignmentMap[baseMatrixSize - 1 - i * 2 - k]);\n                    }\n                    if (messageBits.get(rowOffset + rowSize * 4 + columnOffset + k)) {\n                        matrix.set(alignmentMap[baseMatrixSize - 1 - i * 2 - k], alignmentMap[baseMatrixSize - 1 - i * 2 - j]);\n                    }\n                    if (messageBits.get(rowOffset + rowSize * 6 + columnOffset + k)) {\n                        matrix.set(alignmentMap[baseMatrixSize - 1 - i * 2 - j], alignmentMap[i * 2 + k]);\n                    }\n                }\n            }\n            rowOffset += rowSize * 8;\n        }\n        // draw mode message\n        Encoder.drawModeMessage(matrix, compact, matrixSize, modeMessage);\n        // draw alignment marks\n        if (compact) {\n            Encoder.drawBullsEye(matrix, Integer.truncDivision(matrixSize, 2), 5);\n        }\n        else {\n            Encoder.drawBullsEye(matrix, Integer.truncDivision(matrixSize, 2), 7);\n            for (var i /*int*/ = 0, j = 0; i < Integer.truncDivision(baseMatrixSize, 2) - 1; i += 15, j += 16) {\n                for (var k /*int*/ = Integer.truncDivision(matrixSize, 2) & 1; k < matrixSize; k += 2) {\n                    matrix.set(Integer.truncDivision(matrixSize, 2) - j, k);\n                    matrix.set(Integer.truncDivision(matrixSize, 2) + j, k);\n                    matrix.set(k, Integer.truncDivision(matrixSize, 2) - j);\n                    matrix.set(k, Integer.truncDivision(matrixSize, 2) + j);\n                }\n            }\n        }\n        var aztec = new AztecCode();\n        aztec.setCompact(compact);\n        aztec.setSize(matrixSize);\n        aztec.setLayers(layers);\n        aztec.setCodeWords(messageSizeInWords);\n        aztec.setMatrix(matrix);\n        return aztec;\n    };\n    Encoder.drawBullsEye = function (matrix, center, size) {\n        for (var i /*int*/ = 0; i < size; i += 2) {\n            for (var j /*int*/ = center - i; j <= center + i; j++) {\n                matrix.set(j, center - i);\n                matrix.set(j, center + i);\n                matrix.set(center - i, j);\n                matrix.set(center + i, j);\n            }\n        }\n        matrix.set(center - size, center - size);\n        matrix.set(center - size + 1, center - size);\n        matrix.set(center - size, center - size + 1);\n        matrix.set(center + size, center - size);\n        matrix.set(center + size, center - size + 1);\n        matrix.set(center + size, center + size - 1);\n    };\n    Encoder.generateModeMessage = function (compact, layers, messageSizeInWords) {\n        var modeMessage = new BitArray();\n        if (compact) {\n            modeMessage.appendBits(layers - 1, 2);\n            modeMessage.appendBits(messageSizeInWords - 1, 6);\n            modeMessage = Encoder.generateCheckWords(modeMessage, 28, 4);\n        }\n        else {\n            modeMessage.appendBits(layers - 1, 5);\n            modeMessage.appendBits(messageSizeInWords - 1, 11);\n            modeMessage = Encoder.generateCheckWords(modeMessage, 40, 4);\n        }\n        return modeMessage;\n    };\n    Encoder.drawModeMessage = function (matrix, compact, matrixSize, modeMessage) {\n        var center = Integer.truncDivision(matrixSize, 2);\n        if (compact) {\n            for (var i /*int*/ = 0; i < 7; i++) {\n                var offset = center - 3 + i;\n                if (modeMessage.get(i)) {\n                    matrix.set(offset, center - 5);\n                }\n                if (modeMessage.get(i + 7)) {\n                    matrix.set(center + 5, offset);\n                }\n                if (modeMessage.get(20 - i)) {\n                    matrix.set(offset, center + 5);\n                }\n                if (modeMessage.get(27 - i)) {\n                    matrix.set(center - 5, offset);\n                }\n            }\n        }\n        else {\n            for (var i /*int*/ = 0; i < 10; i++) {\n                var offset = center - 5 + i + Integer.truncDivision(i, 5);\n                if (modeMessage.get(i)) {\n                    matrix.set(offset, center - 7);\n                }\n                if (modeMessage.get(i + 10)) {\n                    matrix.set(center + 7, offset);\n                }\n                if (modeMessage.get(29 - i)) {\n                    matrix.set(offset, center + 7);\n                }\n                if (modeMessage.get(39 - i)) {\n                    matrix.set(center - 7, offset);\n                }\n            }\n        }\n    };\n    Encoder.generateCheckWords = function (bitArray, totalBits, wordSize) {\n        var e_1, _a;\n        // bitArray is guaranteed to be a multiple of the wordSize, so no padding needed\n        var messageSizeInWords = bitArray.getSize() / wordSize;\n        var rs = new ReedSolomonEncoder(Encoder.getGF(wordSize));\n        var totalWords = Integer.truncDivision(totalBits, wordSize);\n        var messageWords = Encoder.bitsToWords(bitArray, wordSize, totalWords);\n        rs.encode(messageWords, totalWords - messageSizeInWords);\n        var startPad = totalBits % wordSize;\n        var messageBits = new BitArray();\n        messageBits.appendBits(0, startPad);\n        try {\n            for (var _b = __values(Array.from(messageWords)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var messageWord = _c.value /*: int*/;\n                messageBits.appendBits(messageWord, wordSize);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return messageBits;\n    };\n    Encoder.bitsToWords = function (stuffedBits, wordSize, totalWords) {\n        var message = new Int32Array(totalWords);\n        var i;\n        var n;\n        for (i = 0, n = stuffedBits.getSize() / wordSize; i < n; i++) {\n            var value = 0;\n            for (var j /*int*/ = 0; j < wordSize; j++) {\n                value |= stuffedBits.get(i * wordSize + j) ? (1 << wordSize - j - 1) : 0;\n            }\n            message[i] = value;\n        }\n        return message;\n    };\n    Encoder.getGF = function (wordSize) {\n        switch (wordSize) {\n            case 4:\n                return GenericGF.AZTEC_PARAM;\n            case 6:\n                return GenericGF.AZTEC_DATA_6;\n            case 8:\n                return GenericGF.AZTEC_DATA_8;\n            case 10:\n                return GenericGF.AZTEC_DATA_10;\n            case 12:\n                return GenericGF.AZTEC_DATA_12;\n            default:\n                throw new IllegalArgumentException('Unsupported word size ' + wordSize);\n        }\n    };\n    Encoder.stuffBits = function (bits, wordSize) {\n        var out = new BitArray();\n        var n = bits.getSize();\n        var mask = (1 << wordSize) - 2;\n        for (var i /*int*/ = 0; i < n; i += wordSize) {\n            var word = 0;\n            for (var j /*int*/ = 0; j < wordSize; j++) {\n                if (i + j >= n || bits.get(i + j)) {\n                    word |= 1 << (wordSize - 1 - j);\n                }\n            }\n            if ((word & mask) === mask) {\n                out.appendBits(word & mask, wordSize);\n                i--;\n            }\n            else if ((word & mask) === 0) {\n                out.appendBits(word | 1, wordSize);\n                i--;\n            }\n            else {\n                out.appendBits(word, wordSize);\n            }\n        }\n        return out;\n    };\n    Encoder.totalBitsInLayer = function (layers, compact) {\n        return ((compact ? 88 : 112) + 16 * layers) * layers;\n    };\n    Encoder.DEFAULT_EC_PERCENT = 33; // default minimal percentage of error check words\n    Encoder.DEFAULT_AZTEC_LAYERS = 0;\n    Encoder.MAX_NB_BITS = 32;\n    Encoder.MAX_NB_BITS_COMPACT = 4;\n    Encoder.WORD_SIZE = Int32Array.from([\n        4, 6, 6, 8, 8, 8, 8, 8, 8, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10,\n        12, 12, 12, 12, 12, 12, 12, 12, 12, 12\n    ]);\n    return Encoder;\n}());\nexport default Encoder;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD,OAAOW,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,wBAAwB,MAAM,gCAAgC;AACrE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,OAAO,MAAM,oBAAoB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAG,aAAe,YAAY;EACrC,SAASA,OAAOA,CAAA,EAAG,CACnB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIA,OAAO,CAACC,WAAW,GAAG,UAAUC,IAAI,EAAE;IAClC,OAAOF,OAAO,CAACG,MAAM,CAACD,IAAI,EAAEF,OAAO,CAACI,kBAAkB,EAAEJ,OAAO,CAACK,oBAAoB,CAAC;EACzF,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIL,OAAO,CAACG,MAAM,GAAG,UAAUD,IAAI,EAAEI,aAAa,EAAEC,mBAAmB,EAAE;IACjE;IACA,IAAIC,IAAI,GAAG,IAAIV,gBAAgB,CAACI,IAAI,CAAC,CAACC,MAAM,CAAC,CAAC;IAC9C;IACA,IAAIM,OAAO,GAAGV,OAAO,CAACW,aAAa,CAAEF,IAAI,CAACG,OAAO,CAAC,CAAC,GAAGL,aAAa,EAAG,GAAG,CAAC,GAAG,EAAE;IAC/E,IAAIM,aAAa,GAAGJ,IAAI,CAACG,OAAO,CAAC,CAAC,GAAGF,OAAO;IAC5C,IAAII,OAAO;IACX,IAAIC,MAAM;IACV,IAAIC,gBAAgB;IACpB,IAAIC,QAAQ;IACZ,IAAIC,WAAW;IACf,IAAIV,mBAAmB,KAAKP,OAAO,CAACK,oBAAoB,EAAE;MACtDQ,OAAO,GAAGN,mBAAmB,GAAG,CAAC;MACjCO,MAAM,GAAGI,IAAI,CAACC,GAAG,CAACZ,mBAAmB,CAAC;MACtC,IAAIO,MAAM,IAAID,OAAO,GAAGb,OAAO,CAACoB,mBAAmB,GAAGpB,OAAO,CAACqB,WAAW,CAAC,EAAE;QACxE,MAAM,IAAI7B,wBAAwB,CAACC,WAAW,CAAC6B,MAAM,CAAC,6BAA6B,EAAEf,mBAAmB,CAAC,CAAC;MAC9G;MACAQ,gBAAgB,GAAGf,OAAO,CAACe,gBAAgB,CAACD,MAAM,EAAED,OAAO,CAAC;MAC5DG,QAAQ,GAAGhB,OAAO,CAACuB,SAAS,CAACT,MAAM,CAAC;MACpC,IAAIU,kBAAkB,GAAGT,gBAAgB,GAAIA,gBAAgB,GAAGC,QAAS;MACzEC,WAAW,GAAGjB,OAAO,CAACyB,SAAS,CAACjB,IAAI,EAAEQ,QAAQ,CAAC;MAC/C,IAAIC,WAAW,CAACN,OAAO,CAAC,CAAC,GAAGF,OAAO,GAAGe,kBAAkB,EAAE;QACtD,MAAM,IAAIhC,wBAAwB,CAAC,wCAAwC,CAAC;MAChF;MACA,IAAIqB,OAAO,IAAII,WAAW,CAACN,OAAO,CAAC,CAAC,GAAGK,QAAQ,GAAG,EAAE,EAAE;QAClD;QACA,MAAM,IAAIxB,wBAAwB,CAAC,wCAAwC,CAAC;MAChF;IACJ,CAAC,MACI;MACDwB,QAAQ,GAAG,CAAC;MACZC,WAAW,GAAG,IAAI;MAClB;MACA;MACA;MACA,KAAK,IAAIjC,CAAC,CAAC,UAAU,CAAC,GAAGA,CAAC,EAAE,EAAE;QAC1B,IAAIA,CAAC,GAAGgB,OAAO,CAACqB,WAAW,EAAE;UACzB,MAAM,IAAI7B,wBAAwB,CAAC,kCAAkC,CAAC;QAC1E;QACAqB,OAAO,GAAG7B,CAAC,IAAI,CAAC;QAChB8B,MAAM,GAAGD,OAAO,GAAG7B,CAAC,GAAG,CAAC,GAAGA,CAAC;QAC5B+B,gBAAgB,GAAGf,OAAO,CAACe,gBAAgB,CAACD,MAAM,EAAED,OAAO,CAAC;QAC5D,IAAID,aAAa,GAAGG,gBAAgB,EAAE;UAClC;QACJ;QACA;QACA;QACA,IAAIE,WAAW,IAAI,IAAI,IAAID,QAAQ,KAAKhB,OAAO,CAACuB,SAAS,CAACT,MAAM,CAAC,EAAE;UAC/DE,QAAQ,GAAGhB,OAAO,CAACuB,SAAS,CAACT,MAAM,CAAC;UACpCG,WAAW,GAAGjB,OAAO,CAACyB,SAAS,CAACjB,IAAI,EAAEQ,QAAQ,CAAC;QACnD;QACA,IAAIQ,kBAAkB,GAAGT,gBAAgB,GAAIA,gBAAgB,GAAGC,QAAS;QACzE,IAAIH,OAAO,IAAII,WAAW,CAACN,OAAO,CAAC,CAAC,GAAGK,QAAQ,GAAG,EAAE,EAAE;UAClD;UACA;QACJ;QACA,IAAIC,WAAW,CAACN,OAAO,CAAC,CAAC,GAAGF,OAAO,IAAIe,kBAAkB,EAAE;UACvD;QACJ;MACJ;IACJ;IACA,IAAIE,WAAW,GAAG1B,OAAO,CAAC2B,kBAAkB,CAACV,WAAW,EAAEF,gBAAgB,EAAEC,QAAQ,CAAC;IACrF;IACA,IAAIY,kBAAkB,GAAGX,WAAW,CAACN,OAAO,CAAC,CAAC,GAAGK,QAAQ;IACzD,IAAIa,WAAW,GAAG7B,OAAO,CAAC8B,mBAAmB,CAACjB,OAAO,EAAEC,MAAM,EAAEc,kBAAkB,CAAC;IAClF;IACA,IAAIG,cAAc,GAAG,CAAClB,OAAO,GAAG,EAAE,GAAG,EAAE,IAAIC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvD,IAAIkB,YAAY,GAAG,IAAIC,UAAU,CAACF,cAAc,CAAC;IACjD,IAAIG,UAAU;IACd,IAAIrB,OAAO,EAAE;MACT;MACAqB,UAAU,GAAGH,cAAc;MAC3B,KAAK,IAAI/C,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGgD,YAAY,CAAC9C,MAAM,EAAEF,CAAC,EAAE,EAAE;QAClDgD,YAAY,CAAChD,CAAC,CAAC,GAAGA,CAAC;MACvB;IACJ,CAAC,MACI;MACDkD,UAAU,GAAGH,cAAc,GAAG,CAAC,GAAG,CAAC,GAAGhC,OAAO,CAACW,aAAa,CAAEX,OAAO,CAACW,aAAa,CAACqB,cAAc,EAAE,CAAC,CAAC,GAAG,CAAC,EAAG,EAAE,CAAC;MAC/G,IAAII,UAAU,GAAGpC,OAAO,CAACW,aAAa,CAACqB,cAAc,EAAE,CAAC,CAAC;MACzD,IAAIK,MAAM,GAAGrC,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC;MACjD,KAAK,IAAIlD,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGmD,UAAU,EAAEnD,CAAC,EAAE,EAAE;QACzC,IAAIqD,SAAS,GAAGrD,CAAC,GAAGe,OAAO,CAACW,aAAa,CAAC1B,CAAC,EAAE,EAAE,CAAC;QAChDgD,YAAY,CAACG,UAAU,GAAGnD,CAAC,GAAG,CAAC,CAAC,GAAGoD,MAAM,GAAGC,SAAS,GAAG,CAAC;QACzDL,YAAY,CAACG,UAAU,GAAGnD,CAAC,CAAC,GAAGoD,MAAM,GAAGC,SAAS,GAAG,CAAC;MACzD;IACJ;IACA,IAAIC,MAAM,GAAG,IAAI5C,SAAS,CAACwC,UAAU,CAAC;IACtC;IACA,KAAK,IAAIlD,CAAC,CAAC,UAAU,CAAC,EAAEuD,SAAS,GAAG,CAAC,EAAEvD,CAAC,GAAG8B,MAAM,EAAE9B,CAAC,EAAE,EAAE;MACpD,IAAIwD,OAAO,GAAG,CAAC1B,MAAM,GAAG9B,CAAC,IAAI,CAAC,IAAI6B,OAAO,GAAG,CAAC,GAAG,EAAE,CAAC;MACnD,KAAK,IAAI4B,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGD,OAAO,EAAEC,CAAC,EAAE,EAAE;QACtC,IAAIC,YAAY,GAAGD,CAAC,GAAG,CAAC;QACxB,KAAK,IAAIE,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAChC,IAAIjB,WAAW,CAACkB,GAAG,CAACL,SAAS,GAAGG,YAAY,GAAGC,CAAC,CAAC,EAAE;YAC/CL,MAAM,CAACO,GAAG,CAACb,YAAY,CAAChD,CAAC,GAAG,CAAC,GAAG2D,CAAC,CAAC,EAAEX,YAAY,CAAChD,CAAC,GAAG,CAAC,GAAGyD,CAAC,CAAC,CAAC;UAChE;UACA,IAAIf,WAAW,CAACkB,GAAG,CAACL,SAAS,GAAGC,OAAO,GAAG,CAAC,GAAGE,YAAY,GAAGC,CAAC,CAAC,EAAE;YAC7DL,MAAM,CAACO,GAAG,CAACb,YAAY,CAAChD,CAAC,GAAG,CAAC,GAAGyD,CAAC,CAAC,EAAET,YAAY,CAACD,cAAc,GAAG,CAAC,GAAG/C,CAAC,GAAG,CAAC,GAAG2D,CAAC,CAAC,CAAC;UACrF;UACA,IAAIjB,WAAW,CAACkB,GAAG,CAACL,SAAS,GAAGC,OAAO,GAAG,CAAC,GAAGE,YAAY,GAAGC,CAAC,CAAC,EAAE;YAC7DL,MAAM,CAACO,GAAG,CAACb,YAAY,CAACD,cAAc,GAAG,CAAC,GAAG/C,CAAC,GAAG,CAAC,GAAG2D,CAAC,CAAC,EAAEX,YAAY,CAACD,cAAc,GAAG,CAAC,GAAG/C,CAAC,GAAG,CAAC,GAAGyD,CAAC,CAAC,CAAC;UAC1G;UACA,IAAIf,WAAW,CAACkB,GAAG,CAACL,SAAS,GAAGC,OAAO,GAAG,CAAC,GAAGE,YAAY,GAAGC,CAAC,CAAC,EAAE;YAC7DL,MAAM,CAACO,GAAG,CAACb,YAAY,CAACD,cAAc,GAAG,CAAC,GAAG/C,CAAC,GAAG,CAAC,GAAGyD,CAAC,CAAC,EAAET,YAAY,CAAChD,CAAC,GAAG,CAAC,GAAG2D,CAAC,CAAC,CAAC;UACrF;QACJ;MACJ;MACAJ,SAAS,IAAIC,OAAO,GAAG,CAAC;IAC5B;IACA;IACAxC,OAAO,CAAC8C,eAAe,CAACR,MAAM,EAAEzB,OAAO,EAAEqB,UAAU,EAAEL,WAAW,CAAC;IACjE;IACA,IAAIhB,OAAO,EAAE;MACTb,OAAO,CAAC+C,YAAY,CAACT,MAAM,EAAEvC,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACzE,CAAC,MACI;MACDlC,OAAO,CAAC+C,YAAY,CAACT,MAAM,EAAEvC,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACrE,KAAK,IAAIlD,CAAC,CAAC,UAAU,CAAC,EAAEyD,CAAC,GAAG,CAAC,EAAEzD,CAAC,GAAGe,OAAO,CAACW,aAAa,CAACqB,cAAc,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE/C,CAAC,IAAI,EAAE,EAAEyD,CAAC,IAAI,EAAE,EAAE;QAC/F,KAAK,IAAIE,CAAC,CAAC,UAAU5C,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,EAAES,CAAC,GAAGT,UAAU,EAAES,CAAC,IAAI,CAAC,EAAE;UACnFL,MAAM,CAACO,GAAG,CAAC9C,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC,GAAGO,CAAC,EAAEE,CAAC,CAAC;UACvDL,MAAM,CAACO,GAAG,CAAC9C,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC,GAAGO,CAAC,EAAEE,CAAC,CAAC;UACvDL,MAAM,CAACO,GAAG,CAACF,CAAC,EAAE5C,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC,GAAGO,CAAC,CAAC;UACvDH,MAAM,CAACO,GAAG,CAACF,CAAC,EAAE5C,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC,GAAGO,CAAC,CAAC;QAC3D;MACJ;IACJ;IACA,IAAIO,KAAK,GAAG,IAAIrD,SAAS,CAAC,CAAC;IAC3BqD,KAAK,CAACC,UAAU,CAACpC,OAAO,CAAC;IACzBmC,KAAK,CAACE,OAAO,CAAChB,UAAU,CAAC;IACzBc,KAAK,CAACG,SAAS,CAACrC,MAAM,CAAC;IACvBkC,KAAK,CAACI,YAAY,CAACxB,kBAAkB,CAAC;IACtCoB,KAAK,CAACK,SAAS,CAACf,MAAM,CAAC;IACvB,OAAOU,KAAK;EAChB,CAAC;EACDhD,OAAO,CAAC+C,YAAY,GAAG,UAAUT,MAAM,EAAEF,MAAM,EAAEkB,IAAI,EAAE;IACnD,KAAK,IAAItE,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGsE,IAAI,EAAEtE,CAAC,IAAI,CAAC,EAAE;MACtC,KAAK,IAAIyD,CAAC,CAAC,UAAUL,MAAM,GAAGpD,CAAC,EAAEyD,CAAC,IAAIL,MAAM,GAAGpD,CAAC,EAAEyD,CAAC,EAAE,EAAE;QACnDH,MAAM,CAACO,GAAG,CAACJ,CAAC,EAAEL,MAAM,GAAGpD,CAAC,CAAC;QACzBsD,MAAM,CAACO,GAAG,CAACJ,CAAC,EAAEL,MAAM,GAAGpD,CAAC,CAAC;QACzBsD,MAAM,CAACO,GAAG,CAACT,MAAM,GAAGpD,CAAC,EAAEyD,CAAC,CAAC;QACzBH,MAAM,CAACO,GAAG,CAACT,MAAM,GAAGpD,CAAC,EAAEyD,CAAC,CAAC;MAC7B;IACJ;IACAH,MAAM,CAACO,GAAG,CAACT,MAAM,GAAGkB,IAAI,EAAElB,MAAM,GAAGkB,IAAI,CAAC;IACxChB,MAAM,CAACO,GAAG,CAACT,MAAM,GAAGkB,IAAI,GAAG,CAAC,EAAElB,MAAM,GAAGkB,IAAI,CAAC;IAC5ChB,MAAM,CAACO,GAAG,CAACT,MAAM,GAAGkB,IAAI,EAAElB,MAAM,GAAGkB,IAAI,GAAG,CAAC,CAAC;IAC5ChB,MAAM,CAACO,GAAG,CAACT,MAAM,GAAGkB,IAAI,EAAElB,MAAM,GAAGkB,IAAI,CAAC;IACxChB,MAAM,CAACO,GAAG,CAACT,MAAM,GAAGkB,IAAI,EAAElB,MAAM,GAAGkB,IAAI,GAAG,CAAC,CAAC;IAC5ChB,MAAM,CAACO,GAAG,CAACT,MAAM,GAAGkB,IAAI,EAAElB,MAAM,GAAGkB,IAAI,GAAG,CAAC,CAAC;EAChD,CAAC;EACDtD,OAAO,CAAC8B,mBAAmB,GAAG,UAAUjB,OAAO,EAAEC,MAAM,EAAEc,kBAAkB,EAAE;IACzE,IAAIC,WAAW,GAAG,IAAItC,QAAQ,CAAC,CAAC;IAChC,IAAIsB,OAAO,EAAE;MACTgB,WAAW,CAAC0B,UAAU,CAACzC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;MACrCe,WAAW,CAAC0B,UAAU,CAAC3B,kBAAkB,GAAG,CAAC,EAAE,CAAC,CAAC;MACjDC,WAAW,GAAG7B,OAAO,CAAC2B,kBAAkB,CAACE,WAAW,EAAE,EAAE,EAAE,CAAC,CAAC;IAChE,CAAC,MACI;MACDA,WAAW,CAAC0B,UAAU,CAACzC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;MACrCe,WAAW,CAAC0B,UAAU,CAAC3B,kBAAkB,GAAG,CAAC,EAAE,EAAE,CAAC;MAClDC,WAAW,GAAG7B,OAAO,CAAC2B,kBAAkB,CAACE,WAAW,EAAE,EAAE,EAAE,CAAC,CAAC;IAChE;IACA,OAAOA,WAAW;EACtB,CAAC;EACD7B,OAAO,CAAC8C,eAAe,GAAG,UAAUR,MAAM,EAAEzB,OAAO,EAAEqB,UAAU,EAAEL,WAAW,EAAE;IAC1E,IAAIO,MAAM,GAAGrC,OAAO,CAACW,aAAa,CAACwB,UAAU,EAAE,CAAC,CAAC;IACjD,IAAIrB,OAAO,EAAE;MACT,KAAK,IAAI7B,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAChC,IAAIwE,MAAM,GAAGpB,MAAM,GAAG,CAAC,GAAGpD,CAAC;QAC3B,IAAI6C,WAAW,CAACe,GAAG,CAAC5D,CAAC,CAAC,EAAE;UACpBsD,MAAM,CAACO,GAAG,CAACW,MAAM,EAAEpB,MAAM,GAAG,CAAC,CAAC;QAClC;QACA,IAAIP,WAAW,CAACe,GAAG,CAAC5D,CAAC,GAAG,CAAC,CAAC,EAAE;UACxBsD,MAAM,CAACO,GAAG,CAACT,MAAM,GAAG,CAAC,EAAEoB,MAAM,CAAC;QAClC;QACA,IAAI3B,WAAW,CAACe,GAAG,CAAC,EAAE,GAAG5D,CAAC,CAAC,EAAE;UACzBsD,MAAM,CAACO,GAAG,CAACW,MAAM,EAAEpB,MAAM,GAAG,CAAC,CAAC;QAClC;QACA,IAAIP,WAAW,CAACe,GAAG,CAAC,EAAE,GAAG5D,CAAC,CAAC,EAAE;UACzBsD,MAAM,CAACO,GAAG,CAACT,MAAM,GAAG,CAAC,EAAEoB,MAAM,CAAC;QAClC;MACJ;IACJ,CAAC,MACI;MACD,KAAK,IAAIxE,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACjC,IAAIwE,MAAM,GAAGpB,MAAM,GAAG,CAAC,GAAGpD,CAAC,GAAGe,OAAO,CAACW,aAAa,CAAC1B,CAAC,EAAE,CAAC,CAAC;QACzD,IAAI6C,WAAW,CAACe,GAAG,CAAC5D,CAAC,CAAC,EAAE;UACpBsD,MAAM,CAACO,GAAG,CAACW,MAAM,EAAEpB,MAAM,GAAG,CAAC,CAAC;QAClC;QACA,IAAIP,WAAW,CAACe,GAAG,CAAC5D,CAAC,GAAG,EAAE,CAAC,EAAE;UACzBsD,MAAM,CAACO,GAAG,CAACT,MAAM,GAAG,CAAC,EAAEoB,MAAM,CAAC;QAClC;QACA,IAAI3B,WAAW,CAACe,GAAG,CAAC,EAAE,GAAG5D,CAAC,CAAC,EAAE;UACzBsD,MAAM,CAACO,GAAG,CAACW,MAAM,EAAEpB,MAAM,GAAG,CAAC,CAAC;QAClC;QACA,IAAIP,WAAW,CAACe,GAAG,CAAC,EAAE,GAAG5D,CAAC,CAAC,EAAE;UACzBsD,MAAM,CAACO,GAAG,CAACT,MAAM,GAAG,CAAC,EAAEoB,MAAM,CAAC;QAClC;MACJ;IACJ;EACJ,CAAC;EACDxD,OAAO,CAAC2B,kBAAkB,GAAG,UAAU8B,QAAQ,EAAEC,SAAS,EAAE1C,QAAQ,EAAE;IAClE,IAAI2C,GAAG,EAAEC,EAAE;IACX;IACA,IAAIhC,kBAAkB,GAAG6B,QAAQ,CAAC9C,OAAO,CAAC,CAAC,GAAGK,QAAQ;IACtD,IAAI6C,EAAE,GAAG,IAAIjE,kBAAkB,CAACI,OAAO,CAAC8D,KAAK,CAAC9C,QAAQ,CAAC,CAAC;IACxD,IAAI+C,UAAU,GAAGhE,OAAO,CAACW,aAAa,CAACgD,SAAS,EAAE1C,QAAQ,CAAC;IAC3D,IAAIgD,YAAY,GAAGhE,OAAO,CAACiE,WAAW,CAACR,QAAQ,EAAEzC,QAAQ,EAAE+C,UAAU,CAAC;IACtEF,EAAE,CAAC1D,MAAM,CAAC6D,YAAY,EAAED,UAAU,GAAGnC,kBAAkB,CAAC;IACxD,IAAIsC,QAAQ,GAAGR,SAAS,GAAG1C,QAAQ;IACnC,IAAIU,WAAW,GAAG,IAAInC,QAAQ,CAAC,CAAC;IAChCmC,WAAW,CAAC6B,UAAU,CAAC,CAAC,EAAEW,QAAQ,CAAC;IACnC,IAAI;MACA,KAAK,IAAIC,EAAE,GAAGzF,QAAQ,CAAC0F,KAAK,CAACC,IAAI,CAACL,YAAY,CAAC,CAAC,EAAEM,EAAE,GAAGH,EAAE,CAAChF,IAAI,CAAC,CAAC,EAAE,CAACmF,EAAE,CAACjF,IAAI,EAAEiF,EAAE,GAAGH,EAAE,CAAChF,IAAI,CAAC,CAAC,EAAE;QACxF,IAAIoF,WAAW,GAAGD,EAAE,CAAClF,KAAK,CAAC;QAC3BsC,WAAW,CAAC6B,UAAU,CAACgB,WAAW,EAAEvD,QAAQ,CAAC;MACjD;IACJ,CAAC,CACD,OAAOwD,KAAK,EAAE;MAAEb,GAAG,GAAG;QAAEc,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,EAAE,IAAI,CAACA,EAAE,CAACjF,IAAI,KAAKuE,EAAE,GAAGO,EAAE,CAACO,MAAM,CAAC,EAAEd,EAAE,CAAC3E,IAAI,CAACkF,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIR,GAAG,EAAE,MAAMA,GAAG,CAACc,KAAK;MAAE;IACxC;IACA,OAAO/C,WAAW;EACtB,CAAC;EACD1B,OAAO,CAACiE,WAAW,GAAG,UAAUhD,WAAW,EAAED,QAAQ,EAAE+C,UAAU,EAAE;IAC/D,IAAIY,OAAO,GAAG,IAAI1C,UAAU,CAAC8B,UAAU,CAAC;IACxC,IAAI/E,CAAC;IACL,IAAI4F,CAAC;IACL,KAAK5F,CAAC,GAAG,CAAC,EAAE4F,CAAC,GAAG3D,WAAW,CAACN,OAAO,CAAC,CAAC,GAAGK,QAAQ,EAAEhC,CAAC,GAAG4F,CAAC,EAAE5F,CAAC,EAAE,EAAE;MAC1D,IAAII,KAAK,GAAG,CAAC;MACb,KAAK,IAAIqD,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGzB,QAAQ,EAAEyB,CAAC,EAAE,EAAE;QACvCrD,KAAK,IAAI6B,WAAW,CAAC2B,GAAG,CAAC5D,CAAC,GAAGgC,QAAQ,GAAGyB,CAAC,CAAC,GAAI,CAAC,IAAIzB,QAAQ,GAAGyB,CAAC,GAAG,CAAC,GAAI,CAAC;MAC5E;MACAkC,OAAO,CAAC3F,CAAC,CAAC,GAAGI,KAAK;IACtB;IACA,OAAOuF,OAAO;EAClB,CAAC;EACD3E,OAAO,CAAC8D,KAAK,GAAG,UAAU9C,QAAQ,EAAE;IAChC,QAAQA,QAAQ;MACZ,KAAK,CAAC;QACF,OAAOnB,SAAS,CAACgF,WAAW;MAChC,KAAK,CAAC;QACF,OAAOhF,SAAS,CAACiF,YAAY;MACjC,KAAK,CAAC;QACF,OAAOjF,SAAS,CAACkF,YAAY;MACjC,KAAK,EAAE;QACH,OAAOlF,SAAS,CAACmF,aAAa;MAClC,KAAK,EAAE;QACH,OAAOnF,SAAS,CAACoF,aAAa;MAClC;QACI,MAAM,IAAIzF,wBAAwB,CAAC,wBAAwB,GAAGwB,QAAQ,CAAC;IAC/E;EACJ,CAAC;EACDhB,OAAO,CAACyB,SAAS,GAAG,UAAUjB,IAAI,EAAEQ,QAAQ,EAAE;IAC1C,IAAIkE,GAAG,GAAG,IAAI3F,QAAQ,CAAC,CAAC;IACxB,IAAIqF,CAAC,GAAGpE,IAAI,CAACG,OAAO,CAAC,CAAC;IACtB,IAAIwE,IAAI,GAAG,CAAC,CAAC,IAAInE,QAAQ,IAAI,CAAC;IAC9B,KAAK,IAAIhC,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAG4F,CAAC,EAAE5F,CAAC,IAAIgC,QAAQ,EAAE;MAC1C,IAAIoE,IAAI,GAAG,CAAC;MACZ,KAAK,IAAI3C,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGzB,QAAQ,EAAEyB,CAAC,EAAE,EAAE;QACvC,IAAIzD,CAAC,GAAGyD,CAAC,IAAImC,CAAC,IAAIpE,IAAI,CAACoC,GAAG,CAAC5D,CAAC,GAAGyD,CAAC,CAAC,EAAE;UAC/B2C,IAAI,IAAI,CAAC,IAAKpE,QAAQ,GAAG,CAAC,GAAGyB,CAAE;QACnC;MACJ;MACA,IAAI,CAAC2C,IAAI,GAAGD,IAAI,MAAMA,IAAI,EAAE;QACxBD,GAAG,CAAC3B,UAAU,CAAC6B,IAAI,GAAGD,IAAI,EAAEnE,QAAQ,CAAC;QACrChC,CAAC,EAAE;MACP,CAAC,MACI,IAAI,CAACoG,IAAI,GAAGD,IAAI,MAAM,CAAC,EAAE;QAC1BD,GAAG,CAAC3B,UAAU,CAAC6B,IAAI,GAAG,CAAC,EAAEpE,QAAQ,CAAC;QAClChC,CAAC,EAAE;MACP,CAAC,MACI;QACDkG,GAAG,CAAC3B,UAAU,CAAC6B,IAAI,EAAEpE,QAAQ,CAAC;MAClC;IACJ;IACA,OAAOkE,GAAG;EACd,CAAC;EACDlF,OAAO,CAACe,gBAAgB,GAAG,UAAUD,MAAM,EAAED,OAAO,EAAE;IAClD,OAAO,CAAC,CAACA,OAAO,GAAG,EAAE,GAAG,GAAG,IAAI,EAAE,GAAGC,MAAM,IAAIA,MAAM;EACxD,CAAC;EACDd,OAAO,CAACI,kBAAkB,GAAG,EAAE,CAAC,CAAC;EACjCJ,OAAO,CAACK,oBAAoB,GAAG,CAAC;EAChCL,OAAO,CAACqB,WAAW,GAAG,EAAE;EACxBrB,OAAO,CAACoB,mBAAmB,GAAG,CAAC;EAC/BpB,OAAO,CAACuB,SAAS,GAAGU,UAAU,CAACoC,IAAI,CAAC,CAChC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EACjF,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACzC,CAAC;EACF,OAAOrE,OAAO;AAClB,CAAC,CAAC,CAAE;AACJ,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}