// Utility functions for number formatting

/**
 * Format number in accounting format (e.g., 1,000,000.00)
 * @param {number} value - The number to format
 * @param {number} decimals - Number of decimal places (default: 2)
 * @returns {string} - Formatted number string
 */
export const formatCurrency = (value, decimals = 2) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.00';
  }
  
  return Number(value).toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
};

/**
 * Parse formatted currency string back to number
 * @param {string} value - The formatted currency string
 * @returns {number} - Parsed number
 */
export const parseCurrency = (value) => {
  if (!value || typeof value !== 'string') {
    return 0;
  }
  
  // Remove commas and parse as float
  return parseFloat(value.replace(/,/g, '')) || 0;
};

/**
 * Calculate percentage of a value
 * @param {number} value - The base value
 * @param {number} percentage - The percentage to calculate
 * @returns {number} - Calculated percentage amount
 */
export const calculatePercentage = (value, percentage) => {
  if (!value || !percentage) return 0;
  return (value * percentage) / 100;
};

/**
 * Calculate purchase invoice totals
 * @param {Array} items - Array of invoice items
 * @param {number} discountPercentage - Discount percentage
 * @param {number} discountAmount - Fixed discount amount
 * @param {number} salesTaxPercentage - Sales tax percentage
 * @param {number} withholdingTaxPercentage - Withholding tax percentage
 * @returns {Object} - Object containing all calculated totals
 */
export const calculateInvoiceTotals = (
  items = [],
  discountPercentage = 0,
  discountAmount = 0,
  salesTaxPercentage = 0,
  withholdingTaxPercentage = 0
) => {
  // Calculate subtotal from items
  const subtotal = items.reduce((sum, item) => {
    const quantity = parseFloat(item.quantity) || 0;
    const price = parseFloat(item.purchasePrice) || 0;
    return sum + (quantity * price);
  }, 0);

  // Calculate discount
  const percentageDiscount = calculatePercentage(subtotal, discountPercentage);
  const totalDiscount = percentageDiscount + (parseFloat(discountAmount) || 0);
  const totalAfterDiscount = subtotal - totalDiscount;

  // Calculate sales tax (applied on total after discount)
  const salesTaxAmount = calculatePercentage(totalAfterDiscount, salesTaxPercentage);
  const totalAfterSalesTax = totalAfterDiscount + salesTaxAmount;

  // Calculate withholding tax (applied on total after sales tax)
  const withholdingTaxAmount = calculatePercentage(totalAfterSalesTax, withholdingTaxPercentage);
  
  // Final payable amount
  const payableAmount = totalAfterSalesTax - withholdingTaxAmount;

  return {
    subtotal: Math.round(subtotal * 100) / 100,
    totalDiscount: Math.round(totalDiscount * 100) / 100,
    totalAfterDiscount: Math.round(totalAfterDiscount * 100) / 100,
    salesTaxAmount: Math.round(salesTaxAmount * 100) / 100,
    totalAfterSalesTax: Math.round(totalAfterSalesTax * 100) / 100,
    withholdingTaxAmount: Math.round(withholdingTaxAmount * 100) / 100,
    payableAmount: Math.round(payableAmount * 100) / 100
  };
};

/**
 * Format percentage for display
 * @param {number} value - The percentage value
 * @returns {string} - Formatted percentage string
 */
export const formatPercentage = (value) => {
  if (value === null || value === undefined || isNaN(value)) {
    return '0%';
  }
  return `${Number(value).toFixed(2)}%`;
};

/**
 * Format number for accounting display (with commas, 2 decimals)
 * @param {number|string} value - The number to format
 * @returns {string} - Formatted number string
 */
export const formatAccountingNumber = (value) => {
  // Handle null, undefined, empty string, but allow 0
  if (value === null || value === undefined || value === '') return '';

  const num = parseFloat(value);
  if (isNaN(num)) return String(value); // Return as string if not a valid number

  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(num);
};

/**
 * Parse accounting formatted number back to decimal
 * @param {string} value - The formatted number string
 * @returns {string} - Parsed number as string with 2 decimals
 */
export const parseAccountingNumber = (value) => {
  if (!value) return '';
  // Remove commas and parse
  const cleaned = value.toString().replace(/,/g, '');
  const num = parseFloat(cleaned);
  return isNaN(num) ? '' : num.toFixed(2);
};

/**
 * Ensure amount has exactly 2 decimal places
 * @param {number|string} amount - The amount to fix
 * @returns {string} - Amount with 2 decimal places
 */
export const fixDecimalPlaces = (amount) => {
  // Handle null, undefined, empty string, but allow 0
  if (amount === null || amount === undefined || amount === '') return '0.00';

  const num = parseFloat(amount);
  return isNaN(num) ? '0.00' : num.toFixed(2);
};
