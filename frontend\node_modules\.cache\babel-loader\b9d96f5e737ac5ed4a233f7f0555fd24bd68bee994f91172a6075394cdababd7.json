{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport addOneClass from 'dom-helpers/addClass';\nimport removeOneClass from 'dom-helpers/removeClass';\nimport React from 'react';\nimport Transition from './Transition';\nimport { classNamesShape } from './utils/PropTypes';\nimport { forceReflow } from './utils/reflow';\nvar _addClass = function addClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return addOneClass(node, c);\n  });\n};\nvar removeClass = function removeClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return removeOneClass(node, c);\n  });\n};\n/**\n * A transition component inspired by the excellent\n * [ng-animate](https://docs.angularjs.org/api/ngAnimate) library, you should\n * use it if you're using CSS transitions or animations. It's built upon the\n * [`Transition`](https://reactcommunity.org/react-transition-group/transition)\n * component, so it inherits all of its props.\n *\n * `CSSTransition` applies a pair of class names during the `appear`, `enter`,\n * and `exit` states of the transition. The first class is applied and then a\n * second `*-active` class in order to activate the CSS transition. After the\n * transition, matching `*-done` class names are applied to persist the\n * transition state.\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <CSSTransition in={inProp} timeout={200} classNames=\"my-node\">\n *         <div>\n *           {\"I'll receive my-node-* classes\"}\n *         </div>\n *       </CSSTransition>\n *       <button type=\"button\" onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the `in` prop is set to `true`, the child component will first receive\n * the class `example-enter`, then the `example-enter-active` will be added in\n * the next tick. `CSSTransition` [forces a\n * reflow](https://github.com/reactjs/react-transition-group/blob/5007303e729a74be66a21c3e2205e4916821524b/src/CSSTransition.js#L208-L215)\n * between before adding the `example-enter-active`. This is an important trick\n * because it allows us to transition between `example-enter` and\n * `example-enter-active` even though they were added immediately one after\n * another. Most notably, this is what makes it possible for us to animate\n * _appearance_.\n *\n * ```css\n * .my-node-enter {\n *   opacity: 0;\n * }\n * .my-node-enter-active {\n *   opacity: 1;\n *   transition: opacity 200ms;\n * }\n * .my-node-exit {\n *   opacity: 1;\n * }\n * .my-node-exit-active {\n *   opacity: 0;\n *   transition: opacity 200ms;\n * }\n * ```\n *\n * `*-active` classes represent which styles you want to animate **to**, so it's\n * important to add `transition` declaration only to them, otherwise transitions\n * might not behave as intended! This might not be obvious when the transitions\n * are symmetrical, i.e. when `*-enter-active` is the same as `*-exit`, like in\n * the example above (minus `transition`), but it becomes apparent in more\n * complex transitions.\n *\n * **Note**: If you're using the\n * [`appear`](http://reactcommunity.org/react-transition-group/transition#Transition-prop-appear)\n * prop, make sure to define styles for `.appear-*` classes as well.\n */\n\nvar CSSTransition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(CSSTransition, _React$Component);\n  function CSSTransition() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.appliedClasses = {\n      appear: {},\n      enter: {},\n      exit: {}\n    };\n    _this.onEnter = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument = _this.resolveArguments(maybeNode, maybeAppearing),\n        node = _this$resolveArgument[0],\n        appearing = _this$resolveArgument[1];\n      _this.removeClasses(node, 'exit');\n      _this.addClass(node, appearing ? 'appear' : 'enter', 'base');\n      if (_this.props.onEnter) {\n        _this.props.onEnter(maybeNode, maybeAppearing);\n      }\n    };\n    _this.onEntering = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument2 = _this.resolveArguments(maybeNode, maybeAppearing),\n        node = _this$resolveArgument2[0],\n        appearing = _this$resolveArgument2[1];\n      var type = appearing ? 'appear' : 'enter';\n      _this.addClass(node, type, 'active');\n      if (_this.props.onEntering) {\n        _this.props.onEntering(maybeNode, maybeAppearing);\n      }\n    };\n    _this.onEntered = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument3 = _this.resolveArguments(maybeNode, maybeAppearing),\n        node = _this$resolveArgument3[0],\n        appearing = _this$resolveArgument3[1];\n      var type = appearing ? 'appear' : 'enter';\n      _this.removeClasses(node, type);\n      _this.addClass(node, type, 'done');\n      if (_this.props.onEntered) {\n        _this.props.onEntered(maybeNode, maybeAppearing);\n      }\n    };\n    _this.onExit = function (maybeNode) {\n      var _this$resolveArgument4 = _this.resolveArguments(maybeNode),\n        node = _this$resolveArgument4[0];\n      _this.removeClasses(node, 'appear');\n      _this.removeClasses(node, 'enter');\n      _this.addClass(node, 'exit', 'base');\n      if (_this.props.onExit) {\n        _this.props.onExit(maybeNode);\n      }\n    };\n    _this.onExiting = function (maybeNode) {\n      var _this$resolveArgument5 = _this.resolveArguments(maybeNode),\n        node = _this$resolveArgument5[0];\n      _this.addClass(node, 'exit', 'active');\n      if (_this.props.onExiting) {\n        _this.props.onExiting(maybeNode);\n      }\n    };\n    _this.onExited = function (maybeNode) {\n      var _this$resolveArgument6 = _this.resolveArguments(maybeNode),\n        node = _this$resolveArgument6[0];\n      _this.removeClasses(node, 'exit');\n      _this.addClass(node, 'exit', 'done');\n      if (_this.props.onExited) {\n        _this.props.onExited(maybeNode);\n      }\n    };\n    _this.resolveArguments = function (maybeNode, maybeAppearing) {\n      return _this.props.nodeRef ? [_this.props.nodeRef.current, maybeNode] // here `maybeNode` is actually `appearing`\n      : [maybeNode, maybeAppearing];\n    };\n    _this.getClassNames = function (type) {\n      var classNames = _this.props.classNames;\n      var isStringClassNames = typeof classNames === 'string';\n      var prefix = isStringClassNames && classNames ? classNames + \"-\" : '';\n      var baseClassName = isStringClassNames ? \"\" + prefix + type : classNames[type];\n      var activeClassName = isStringClassNames ? baseClassName + \"-active\" : classNames[type + \"Active\"];\n      var doneClassName = isStringClassNames ? baseClassName + \"-done\" : classNames[type + \"Done\"];\n      return {\n        baseClassName: baseClassName,\n        activeClassName: activeClassName,\n        doneClassName: doneClassName\n      };\n    };\n    return _this;\n  }\n  var _proto = CSSTransition.prototype;\n  _proto.addClass = function addClass(node, type, phase) {\n    var className = this.getClassNames(type)[phase + \"ClassName\"];\n    var _this$getClassNames = this.getClassNames('enter'),\n      doneClassName = _this$getClassNames.doneClassName;\n    if (type === 'appear' && phase === 'done' && doneClassName) {\n      className += \" \" + doneClassName;\n    } // This is to force a repaint,\n    // which is necessary in order to transition styles when adding a class name.\n\n    if (phase === 'active') {\n      if (node) forceReflow(node);\n    }\n    if (className) {\n      this.appliedClasses[type][phase] = className;\n      _addClass(node, className);\n    }\n  };\n  _proto.removeClasses = function removeClasses(node, type) {\n    var _this$appliedClasses$ = this.appliedClasses[type],\n      baseClassName = _this$appliedClasses$.base,\n      activeClassName = _this$appliedClasses$.active,\n      doneClassName = _this$appliedClasses$.done;\n    this.appliedClasses[type] = {};\n    if (baseClassName) {\n      removeClass(node, baseClassName);\n    }\n    if (activeClassName) {\n      removeClass(node, activeClassName);\n    }\n    if (doneClassName) {\n      removeClass(node, doneClassName);\n    }\n  };\n  _proto.render = function render() {\n    var _this$props = this.props,\n      _ = _this$props.classNames,\n      props = _objectWithoutPropertiesLoose(_this$props, [\"classNames\"]);\n    return /*#__PURE__*/React.createElement(Transition, _extends({}, props, {\n      onEnter: this.onEnter,\n      onEntered: this.onEntered,\n      onEntering: this.onEntering,\n      onExit: this.onExit,\n      onExiting: this.onExiting,\n      onExited: this.onExited\n    }));\n  };\n  return CSSTransition;\n}(React.Component);\nCSSTransition.defaultProps = {\n  classNames: ''\n};\nCSSTransition.propTypes = process.env.NODE_ENV !== \"production\" ? _extends({}, Transition.propTypes, {\n  /**\n   * The animation classNames applied to the component as it appears, enters,\n   * exits or has finished the transition. A single name can be provided, which\n   * will be suffixed for each stage, e.g. `classNames=\"fade\"` applies:\n   *\n   * - `fade-appear`, `fade-appear-active`, `fade-appear-done`\n   * - `fade-enter`, `fade-enter-active`, `fade-enter-done`\n   * - `fade-exit`, `fade-exit-active`, `fade-exit-done`\n   *\n   * A few details to note about how these classes are applied:\n   *\n   * 1. They are _joined_ with the ones that are already defined on the child\n   *    component, so if you want to add some base styles, you can use\n   *    `className` without worrying that it will be overridden.\n   *\n   * 2. If the transition component mounts with `in={false}`, no classes are\n   *    applied yet. You might be expecting `*-exit-done`, but if you think\n   *    about it, a component cannot finish exiting if it hasn't entered yet.\n   *\n   * 2. `fade-appear-done` and `fade-enter-done` will _both_ be applied. This\n   *    allows you to define different behavior for when appearing is done and\n   *    when regular entering is done, using selectors like\n   *    `.fade-enter-done:not(.fade-appear-done)`. For example, you could apply\n   *    an epic entrance animation when element first appears in the DOM using\n   *    [Animate.css](https://daneden.github.io/animate.css/). Otherwise you can\n   *    simply use `fade-enter-done` for defining both cases.\n   *\n   * Each individual classNames can also be specified independently like:\n   *\n   * ```js\n   * classNames={{\n   *  appear: 'my-appear',\n   *  appearActive: 'my-active-appear',\n   *  appearDone: 'my-done-appear',\n   *  enter: 'my-enter',\n   *  enterActive: 'my-active-enter',\n   *  enterDone: 'my-done-enter',\n   *  exit: 'my-exit',\n   *  exitActive: 'my-active-exit',\n   *  exitDone: 'my-done-exit',\n   * }}\n   * ```\n   *\n   * If you want to set these classes using CSS Modules:\n   *\n   * ```js\n   * import styles from './styles.css';\n   * ```\n   *\n   * you might want to use camelCase in your CSS file, that way could simply\n   * spread them instead of listing them one by one:\n   *\n   * ```js\n   * classNames={{ ...styles }}\n   * ```\n   *\n   * @type {string | {\n   *  appear?: string,\n   *  appearActive?: string,\n   *  appearDone?: string,\n   *  enter?: string,\n   *  enterActive?: string,\n   *  enterDone?: string,\n   *  exit?: string,\n   *  exitActive?: string,\n   *  exitDone?: string,\n   * }}\n   */\n  classNames: classNamesShape,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or 'appear' class is\n   * applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEnter: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter-active' or\n   * 'appear-active' class is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or\n   * 'appear' classes are **removed** and the `done` class is added to the DOM node.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntered: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' class is\n   * applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExit: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit-active' is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExiting: PropTypes.func,\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' classes\n   * are **removed** and the `exit-done` class is added to the DOM node.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExited: PropTypes.func\n}) : {};\nexport default CSSTransition;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_inherits<PERSON><PERSON>e", "PropTypes", "addOneClass", "removeOneClass", "React", "Transition", "classNamesShape", "forceReflow", "_addClass", "addClass", "node", "classes", "split", "for<PERSON>ach", "c", "removeClass", "CSSTransition", "_React$Component", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "appliedClasses", "appear", "enter", "exit", "onEnter", "maybeNode", "maybeAppearing", "_this$resolveArgument", "resolveArguments", "appearing", "removeClasses", "props", "onEntering", "_this$resolveArgument2", "type", "onEntered", "_this$resolveArgument3", "onExit", "_this$resolveArgument4", "onExiting", "_this$resolveArgument5", "onExited", "_this$resolveArgument6", "nodeRef", "current", "getClassNames", "classNames", "isStringClassNames", "prefix", "baseClassName", "activeClassName", "doneClassName", "_proto", "prototype", "phase", "className", "_this$getClassNames", "_this$appliedClasses$", "base", "active", "done", "render", "_this$props", "_", "createElement", "Component", "defaultProps", "propTypes", "process", "env", "NODE_ENV", "func"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/react-transition-group/esm/CSSTransition.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport addOneClass from 'dom-helpers/addClass';\nimport removeOneClass from 'dom-helpers/removeClass';\nimport React from 'react';\nimport Transition from './Transition';\nimport { classNamesShape } from './utils/PropTypes';\nimport { forceReflow } from './utils/reflow';\n\nvar _addClass = function addClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return addOneClass(node, c);\n  });\n};\n\nvar removeClass = function removeClass(node, classes) {\n  return node && classes && classes.split(' ').forEach(function (c) {\n    return removeOneClass(node, c);\n  });\n};\n/**\n * A transition component inspired by the excellent\n * [ng-animate](https://docs.angularjs.org/api/ngAnimate) library, you should\n * use it if you're using CSS transitions or animations. It's built upon the\n * [`Transition`](https://reactcommunity.org/react-transition-group/transition)\n * component, so it inherits all of its props.\n *\n * `CSSTransition` applies a pair of class names during the `appear`, `enter`,\n * and `exit` states of the transition. The first class is applied and then a\n * second `*-active` class in order to activate the CSS transition. After the\n * transition, matching `*-done` class names are applied to persist the\n * transition state.\n *\n * ```jsx\n * function App() {\n *   const [inProp, setInProp] = useState(false);\n *   return (\n *     <div>\n *       <CSSTransition in={inProp} timeout={200} classNames=\"my-node\">\n *         <div>\n *           {\"I'll receive my-node-* classes\"}\n *         </div>\n *       </CSSTransition>\n *       <button type=\"button\" onClick={() => setInProp(true)}>\n *         Click to Enter\n *       </button>\n *     </div>\n *   );\n * }\n * ```\n *\n * When the `in` prop is set to `true`, the child component will first receive\n * the class `example-enter`, then the `example-enter-active` will be added in\n * the next tick. `CSSTransition` [forces a\n * reflow](https://github.com/reactjs/react-transition-group/blob/5007303e729a74be66a21c3e2205e4916821524b/src/CSSTransition.js#L208-L215)\n * between before adding the `example-enter-active`. This is an important trick\n * because it allows us to transition between `example-enter` and\n * `example-enter-active` even though they were added immediately one after\n * another. Most notably, this is what makes it possible for us to animate\n * _appearance_.\n *\n * ```css\n * .my-node-enter {\n *   opacity: 0;\n * }\n * .my-node-enter-active {\n *   opacity: 1;\n *   transition: opacity 200ms;\n * }\n * .my-node-exit {\n *   opacity: 1;\n * }\n * .my-node-exit-active {\n *   opacity: 0;\n *   transition: opacity 200ms;\n * }\n * ```\n *\n * `*-active` classes represent which styles you want to animate **to**, so it's\n * important to add `transition` declaration only to them, otherwise transitions\n * might not behave as intended! This might not be obvious when the transitions\n * are symmetrical, i.e. when `*-enter-active` is the same as `*-exit`, like in\n * the example above (minus `transition`), but it becomes apparent in more\n * complex transitions.\n *\n * **Note**: If you're using the\n * [`appear`](http://reactcommunity.org/react-transition-group/transition#Transition-prop-appear)\n * prop, make sure to define styles for `.appear-*` classes as well.\n */\n\n\nvar CSSTransition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(CSSTransition, _React$Component);\n\n  function CSSTransition() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.appliedClasses = {\n      appear: {},\n      enter: {},\n      exit: {}\n    };\n\n    _this.onEnter = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument = _this.resolveArguments(maybeNode, maybeAppearing),\n          node = _this$resolveArgument[0],\n          appearing = _this$resolveArgument[1];\n\n      _this.removeClasses(node, 'exit');\n\n      _this.addClass(node, appearing ? 'appear' : 'enter', 'base');\n\n      if (_this.props.onEnter) {\n        _this.props.onEnter(maybeNode, maybeAppearing);\n      }\n    };\n\n    _this.onEntering = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument2 = _this.resolveArguments(maybeNode, maybeAppearing),\n          node = _this$resolveArgument2[0],\n          appearing = _this$resolveArgument2[1];\n\n      var type = appearing ? 'appear' : 'enter';\n\n      _this.addClass(node, type, 'active');\n\n      if (_this.props.onEntering) {\n        _this.props.onEntering(maybeNode, maybeAppearing);\n      }\n    };\n\n    _this.onEntered = function (maybeNode, maybeAppearing) {\n      var _this$resolveArgument3 = _this.resolveArguments(maybeNode, maybeAppearing),\n          node = _this$resolveArgument3[0],\n          appearing = _this$resolveArgument3[1];\n\n      var type = appearing ? 'appear' : 'enter';\n\n      _this.removeClasses(node, type);\n\n      _this.addClass(node, type, 'done');\n\n      if (_this.props.onEntered) {\n        _this.props.onEntered(maybeNode, maybeAppearing);\n      }\n    };\n\n    _this.onExit = function (maybeNode) {\n      var _this$resolveArgument4 = _this.resolveArguments(maybeNode),\n          node = _this$resolveArgument4[0];\n\n      _this.removeClasses(node, 'appear');\n\n      _this.removeClasses(node, 'enter');\n\n      _this.addClass(node, 'exit', 'base');\n\n      if (_this.props.onExit) {\n        _this.props.onExit(maybeNode);\n      }\n    };\n\n    _this.onExiting = function (maybeNode) {\n      var _this$resolveArgument5 = _this.resolveArguments(maybeNode),\n          node = _this$resolveArgument5[0];\n\n      _this.addClass(node, 'exit', 'active');\n\n      if (_this.props.onExiting) {\n        _this.props.onExiting(maybeNode);\n      }\n    };\n\n    _this.onExited = function (maybeNode) {\n      var _this$resolveArgument6 = _this.resolveArguments(maybeNode),\n          node = _this$resolveArgument6[0];\n\n      _this.removeClasses(node, 'exit');\n\n      _this.addClass(node, 'exit', 'done');\n\n      if (_this.props.onExited) {\n        _this.props.onExited(maybeNode);\n      }\n    };\n\n    _this.resolveArguments = function (maybeNode, maybeAppearing) {\n      return _this.props.nodeRef ? [_this.props.nodeRef.current, maybeNode] // here `maybeNode` is actually `appearing`\n      : [maybeNode, maybeAppearing];\n    };\n\n    _this.getClassNames = function (type) {\n      var classNames = _this.props.classNames;\n      var isStringClassNames = typeof classNames === 'string';\n      var prefix = isStringClassNames && classNames ? classNames + \"-\" : '';\n      var baseClassName = isStringClassNames ? \"\" + prefix + type : classNames[type];\n      var activeClassName = isStringClassNames ? baseClassName + \"-active\" : classNames[type + \"Active\"];\n      var doneClassName = isStringClassNames ? baseClassName + \"-done\" : classNames[type + \"Done\"];\n      return {\n        baseClassName: baseClassName,\n        activeClassName: activeClassName,\n        doneClassName: doneClassName\n      };\n    };\n\n    return _this;\n  }\n\n  var _proto = CSSTransition.prototype;\n\n  _proto.addClass = function addClass(node, type, phase) {\n    var className = this.getClassNames(type)[phase + \"ClassName\"];\n\n    var _this$getClassNames = this.getClassNames('enter'),\n        doneClassName = _this$getClassNames.doneClassName;\n\n    if (type === 'appear' && phase === 'done' && doneClassName) {\n      className += \" \" + doneClassName;\n    } // This is to force a repaint,\n    // which is necessary in order to transition styles when adding a class name.\n\n\n    if (phase === 'active') {\n      if (node) forceReflow(node);\n    }\n\n    if (className) {\n      this.appliedClasses[type][phase] = className;\n\n      _addClass(node, className);\n    }\n  };\n\n  _proto.removeClasses = function removeClasses(node, type) {\n    var _this$appliedClasses$ = this.appliedClasses[type],\n        baseClassName = _this$appliedClasses$.base,\n        activeClassName = _this$appliedClasses$.active,\n        doneClassName = _this$appliedClasses$.done;\n    this.appliedClasses[type] = {};\n\n    if (baseClassName) {\n      removeClass(node, baseClassName);\n    }\n\n    if (activeClassName) {\n      removeClass(node, activeClassName);\n    }\n\n    if (doneClassName) {\n      removeClass(node, doneClassName);\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        _ = _this$props.classNames,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"classNames\"]);\n\n    return /*#__PURE__*/React.createElement(Transition, _extends({}, props, {\n      onEnter: this.onEnter,\n      onEntered: this.onEntered,\n      onEntering: this.onEntering,\n      onExit: this.onExit,\n      onExiting: this.onExiting,\n      onExited: this.onExited\n    }));\n  };\n\n  return CSSTransition;\n}(React.Component);\n\nCSSTransition.defaultProps = {\n  classNames: ''\n};\nCSSTransition.propTypes = process.env.NODE_ENV !== \"production\" ? _extends({}, Transition.propTypes, {\n  /**\n   * The animation classNames applied to the component as it appears, enters,\n   * exits or has finished the transition. A single name can be provided, which\n   * will be suffixed for each stage, e.g. `classNames=\"fade\"` applies:\n   *\n   * - `fade-appear`, `fade-appear-active`, `fade-appear-done`\n   * - `fade-enter`, `fade-enter-active`, `fade-enter-done`\n   * - `fade-exit`, `fade-exit-active`, `fade-exit-done`\n   *\n   * A few details to note about how these classes are applied:\n   *\n   * 1. They are _joined_ with the ones that are already defined on the child\n   *    component, so if you want to add some base styles, you can use\n   *    `className` without worrying that it will be overridden.\n   *\n   * 2. If the transition component mounts with `in={false}`, no classes are\n   *    applied yet. You might be expecting `*-exit-done`, but if you think\n   *    about it, a component cannot finish exiting if it hasn't entered yet.\n   *\n   * 2. `fade-appear-done` and `fade-enter-done` will _both_ be applied. This\n   *    allows you to define different behavior for when appearing is done and\n   *    when regular entering is done, using selectors like\n   *    `.fade-enter-done:not(.fade-appear-done)`. For example, you could apply\n   *    an epic entrance animation when element first appears in the DOM using\n   *    [Animate.css](https://daneden.github.io/animate.css/). Otherwise you can\n   *    simply use `fade-enter-done` for defining both cases.\n   *\n   * Each individual classNames can also be specified independently like:\n   *\n   * ```js\n   * classNames={{\n   *  appear: 'my-appear',\n   *  appearActive: 'my-active-appear',\n   *  appearDone: 'my-done-appear',\n   *  enter: 'my-enter',\n   *  enterActive: 'my-active-enter',\n   *  enterDone: 'my-done-enter',\n   *  exit: 'my-exit',\n   *  exitActive: 'my-active-exit',\n   *  exitDone: 'my-done-exit',\n   * }}\n   * ```\n   *\n   * If you want to set these classes using CSS Modules:\n   *\n   * ```js\n   * import styles from './styles.css';\n   * ```\n   *\n   * you might want to use camelCase in your CSS file, that way could simply\n   * spread them instead of listing them one by one:\n   *\n   * ```js\n   * classNames={{ ...styles }}\n   * ```\n   *\n   * @type {string | {\n   *  appear?: string,\n   *  appearActive?: string,\n   *  appearDone?: string,\n   *  enter?: string,\n   *  enterActive?: string,\n   *  enterDone?: string,\n   *  exit?: string,\n   *  exitActive?: string,\n   *  exitDone?: string,\n   * }}\n   */\n  classNames: classNamesShape,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or 'appear' class is\n   * applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEnter: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter-active' or\n   * 'appear-active' class is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntering: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'enter' or\n   * 'appear' classes are **removed** and the `done` class is added to the DOM node.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed.\n   *\n   * @type Function(node: HtmlElement, isAppearing: bool)\n   */\n  onEntered: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' class is\n   * applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExit: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit-active' is applied.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExiting: PropTypes.func,\n\n  /**\n   * A `<Transition>` callback fired immediately after the 'exit' classes\n   * are **removed** and the `exit-done` class is added to the DOM node.\n   *\n   * **Note**: when `nodeRef` prop is passed, `node` is not passed\n   *\n   * @type Function(node: HtmlElement)\n   */\n  onExited: PropTypes.func\n}) : {};\nexport default CSSTransition;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,gBAAgB;AAE5C,IAAIC,SAAS,GAAG,SAASC,QAAQA,CAACC,IAAI,EAAEC,OAAO,EAAE;EAC/C,OAAOD,IAAI,IAAIC,OAAO,IAAIA,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,CAAC,EAAE;IAChE,OAAOZ,WAAW,CAACQ,IAAI,EAAEI,CAAC,CAAC;EAC7B,CAAC,CAAC;AACJ,CAAC;AAED,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACL,IAAI,EAAEC,OAAO,EAAE;EACpD,OAAOD,IAAI,IAAIC,OAAO,IAAIA,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,CAAC,EAAE;IAChE,OAAOX,cAAc,CAACO,IAAI,EAAEI,CAAC,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,IAAIE,aAAa,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC3DjB,cAAc,CAACgB,aAAa,EAAEC,gBAAgB,CAAC;EAE/C,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK;IAET,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,gBAAgB,CAACQ,IAAI,CAACC,KAAK,CAACT,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC,IAAI,IAAI;IAClFJ,KAAK,CAACU,cAAc,GAAG;MACrBC,MAAM,EAAE,CAAC,CAAC;MACVC,KAAK,EAAE,CAAC,CAAC;MACTC,IAAI,EAAE,CAAC;IACT,CAAC;IAEDb,KAAK,CAACc,OAAO,GAAG,UAAUC,SAAS,EAAEC,cAAc,EAAE;MACnD,IAAIC,qBAAqB,GAAGjB,KAAK,CAACkB,gBAAgB,CAACH,SAAS,EAAEC,cAAc,CAAC;QACzExB,IAAI,GAAGyB,qBAAqB,CAAC,CAAC,CAAC;QAC/BE,SAAS,GAAGF,qBAAqB,CAAC,CAAC,CAAC;MAExCjB,KAAK,CAACoB,aAAa,CAAC5B,IAAI,EAAE,MAAM,CAAC;MAEjCQ,KAAK,CAACT,QAAQ,CAACC,IAAI,EAAE2B,SAAS,GAAG,QAAQ,GAAG,OAAO,EAAE,MAAM,CAAC;MAE5D,IAAInB,KAAK,CAACqB,KAAK,CAACP,OAAO,EAAE;QACvBd,KAAK,CAACqB,KAAK,CAACP,OAAO,CAACC,SAAS,EAAEC,cAAc,CAAC;MAChD;IACF,CAAC;IAEDhB,KAAK,CAACsB,UAAU,GAAG,UAAUP,SAAS,EAAEC,cAAc,EAAE;MACtD,IAAIO,sBAAsB,GAAGvB,KAAK,CAACkB,gBAAgB,CAACH,SAAS,EAAEC,cAAc,CAAC;QAC1ExB,IAAI,GAAG+B,sBAAsB,CAAC,CAAC,CAAC;QAChCJ,SAAS,GAAGI,sBAAsB,CAAC,CAAC,CAAC;MAEzC,IAAIC,IAAI,GAAGL,SAAS,GAAG,QAAQ,GAAG,OAAO;MAEzCnB,KAAK,CAACT,QAAQ,CAACC,IAAI,EAAEgC,IAAI,EAAE,QAAQ,CAAC;MAEpC,IAAIxB,KAAK,CAACqB,KAAK,CAACC,UAAU,EAAE;QAC1BtB,KAAK,CAACqB,KAAK,CAACC,UAAU,CAACP,SAAS,EAAEC,cAAc,CAAC;MACnD;IACF,CAAC;IAEDhB,KAAK,CAACyB,SAAS,GAAG,UAAUV,SAAS,EAAEC,cAAc,EAAE;MACrD,IAAIU,sBAAsB,GAAG1B,KAAK,CAACkB,gBAAgB,CAACH,SAAS,EAAEC,cAAc,CAAC;QAC1ExB,IAAI,GAAGkC,sBAAsB,CAAC,CAAC,CAAC;QAChCP,SAAS,GAAGO,sBAAsB,CAAC,CAAC,CAAC;MAEzC,IAAIF,IAAI,GAAGL,SAAS,GAAG,QAAQ,GAAG,OAAO;MAEzCnB,KAAK,CAACoB,aAAa,CAAC5B,IAAI,EAAEgC,IAAI,CAAC;MAE/BxB,KAAK,CAACT,QAAQ,CAACC,IAAI,EAAEgC,IAAI,EAAE,MAAM,CAAC;MAElC,IAAIxB,KAAK,CAACqB,KAAK,CAACI,SAAS,EAAE;QACzBzB,KAAK,CAACqB,KAAK,CAACI,SAAS,CAACV,SAAS,EAAEC,cAAc,CAAC;MAClD;IACF,CAAC;IAEDhB,KAAK,CAAC2B,MAAM,GAAG,UAAUZ,SAAS,EAAE;MAClC,IAAIa,sBAAsB,GAAG5B,KAAK,CAACkB,gBAAgB,CAACH,SAAS,CAAC;QAC1DvB,IAAI,GAAGoC,sBAAsB,CAAC,CAAC,CAAC;MAEpC5B,KAAK,CAACoB,aAAa,CAAC5B,IAAI,EAAE,QAAQ,CAAC;MAEnCQ,KAAK,CAACoB,aAAa,CAAC5B,IAAI,EAAE,OAAO,CAAC;MAElCQ,KAAK,CAACT,QAAQ,CAACC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;MAEpC,IAAIQ,KAAK,CAACqB,KAAK,CAACM,MAAM,EAAE;QACtB3B,KAAK,CAACqB,KAAK,CAACM,MAAM,CAACZ,SAAS,CAAC;MAC/B;IACF,CAAC;IAEDf,KAAK,CAAC6B,SAAS,GAAG,UAAUd,SAAS,EAAE;MACrC,IAAIe,sBAAsB,GAAG9B,KAAK,CAACkB,gBAAgB,CAACH,SAAS,CAAC;QAC1DvB,IAAI,GAAGsC,sBAAsB,CAAC,CAAC,CAAC;MAEpC9B,KAAK,CAACT,QAAQ,CAACC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC;MAEtC,IAAIQ,KAAK,CAACqB,KAAK,CAACQ,SAAS,EAAE;QACzB7B,KAAK,CAACqB,KAAK,CAACQ,SAAS,CAACd,SAAS,CAAC;MAClC;IACF,CAAC;IAEDf,KAAK,CAAC+B,QAAQ,GAAG,UAAUhB,SAAS,EAAE;MACpC,IAAIiB,sBAAsB,GAAGhC,KAAK,CAACkB,gBAAgB,CAACH,SAAS,CAAC;QAC1DvB,IAAI,GAAGwC,sBAAsB,CAAC,CAAC,CAAC;MAEpChC,KAAK,CAACoB,aAAa,CAAC5B,IAAI,EAAE,MAAM,CAAC;MAEjCQ,KAAK,CAACT,QAAQ,CAACC,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;MAEpC,IAAIQ,KAAK,CAACqB,KAAK,CAACU,QAAQ,EAAE;QACxB/B,KAAK,CAACqB,KAAK,CAACU,QAAQ,CAAChB,SAAS,CAAC;MACjC;IACF,CAAC;IAEDf,KAAK,CAACkB,gBAAgB,GAAG,UAAUH,SAAS,EAAEC,cAAc,EAAE;MAC5D,OAAOhB,KAAK,CAACqB,KAAK,CAACY,OAAO,GAAG,CAACjC,KAAK,CAACqB,KAAK,CAACY,OAAO,CAACC,OAAO,EAAEnB,SAAS,CAAC,CAAC;MAAA,EACpE,CAACA,SAAS,EAAEC,cAAc,CAAC;IAC/B,CAAC;IAEDhB,KAAK,CAACmC,aAAa,GAAG,UAAUX,IAAI,EAAE;MACpC,IAAIY,UAAU,GAAGpC,KAAK,CAACqB,KAAK,CAACe,UAAU;MACvC,IAAIC,kBAAkB,GAAG,OAAOD,UAAU,KAAK,QAAQ;MACvD,IAAIE,MAAM,GAAGD,kBAAkB,IAAID,UAAU,GAAGA,UAAU,GAAG,GAAG,GAAG,EAAE;MACrE,IAAIG,aAAa,GAAGF,kBAAkB,GAAG,EAAE,GAAGC,MAAM,GAAGd,IAAI,GAAGY,UAAU,CAACZ,IAAI,CAAC;MAC9E,IAAIgB,eAAe,GAAGH,kBAAkB,GAAGE,aAAa,GAAG,SAAS,GAAGH,UAAU,CAACZ,IAAI,GAAG,QAAQ,CAAC;MAClG,IAAIiB,aAAa,GAAGJ,kBAAkB,GAAGE,aAAa,GAAG,OAAO,GAAGH,UAAU,CAACZ,IAAI,GAAG,MAAM,CAAC;MAC5F,OAAO;QACLe,aAAa,EAAEA,aAAa;QAC5BC,eAAe,EAAEA,eAAe;QAChCC,aAAa,EAAEA;MACjB,CAAC;IACH,CAAC;IAED,OAAOzC,KAAK;EACd;EAEA,IAAI0C,MAAM,GAAG5C,aAAa,CAAC6C,SAAS;EAEpCD,MAAM,CAACnD,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAEgC,IAAI,EAAEoB,KAAK,EAAE;IACrD,IAAIC,SAAS,GAAG,IAAI,CAACV,aAAa,CAACX,IAAI,CAAC,CAACoB,KAAK,GAAG,WAAW,CAAC;IAE7D,IAAIE,mBAAmB,GAAG,IAAI,CAACX,aAAa,CAAC,OAAO,CAAC;MACjDM,aAAa,GAAGK,mBAAmB,CAACL,aAAa;IAErD,IAAIjB,IAAI,KAAK,QAAQ,IAAIoB,KAAK,KAAK,MAAM,IAAIH,aAAa,EAAE;MAC1DI,SAAS,IAAI,GAAG,GAAGJ,aAAa;IAClC,CAAC,CAAC;IACF;;IAGA,IAAIG,KAAK,KAAK,QAAQ,EAAE;MACtB,IAAIpD,IAAI,EAAEH,WAAW,CAACG,IAAI,CAAC;IAC7B;IAEA,IAAIqD,SAAS,EAAE;MACb,IAAI,CAACnC,cAAc,CAACc,IAAI,CAAC,CAACoB,KAAK,CAAC,GAAGC,SAAS;MAE5CvD,SAAS,CAACE,IAAI,EAAEqD,SAAS,CAAC;IAC5B;EACF,CAAC;EAEDH,MAAM,CAACtB,aAAa,GAAG,SAASA,aAAaA,CAAC5B,IAAI,EAAEgC,IAAI,EAAE;IACxD,IAAIuB,qBAAqB,GAAG,IAAI,CAACrC,cAAc,CAACc,IAAI,CAAC;MACjDe,aAAa,GAAGQ,qBAAqB,CAACC,IAAI;MAC1CR,eAAe,GAAGO,qBAAqB,CAACE,MAAM;MAC9CR,aAAa,GAAGM,qBAAqB,CAACG,IAAI;IAC9C,IAAI,CAACxC,cAAc,CAACc,IAAI,CAAC,GAAG,CAAC,CAAC;IAE9B,IAAIe,aAAa,EAAE;MACjB1C,WAAW,CAACL,IAAI,EAAE+C,aAAa,CAAC;IAClC;IAEA,IAAIC,eAAe,EAAE;MACnB3C,WAAW,CAACL,IAAI,EAAEgD,eAAe,CAAC;IACpC;IAEA,IAAIC,aAAa,EAAE;MACjB5C,WAAW,CAACL,IAAI,EAAEiD,aAAa,CAAC;IAClC;EACF,CAAC;EAEDC,MAAM,CAACS,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAIC,WAAW,GAAG,IAAI,CAAC/B,KAAK;MACxBgC,CAAC,GAAGD,WAAW,CAAChB,UAAU;MAC1Bf,KAAK,GAAGxC,6BAA6B,CAACuE,WAAW,EAAE,CAAC,YAAY,CAAC,CAAC;IAEtE,OAAO,aAAalE,KAAK,CAACoE,aAAa,CAACnE,UAAU,EAAEP,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,EAAE;MACtEP,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBW,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBH,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BK,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBE,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBE,QAAQ,EAAE,IAAI,CAACA;IACjB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAOjC,aAAa;AACtB,CAAC,CAACZ,KAAK,CAACqE,SAAS,CAAC;AAElBzD,aAAa,CAAC0D,YAAY,GAAG;EAC3BpB,UAAU,EAAE;AACd,CAAC;AACDtC,aAAa,CAAC2D,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhF,QAAQ,CAAC,CAAC,CAAC,EAAEO,UAAU,CAACsE,SAAS,EAAE;EACnG;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACErB,UAAU,EAAEhD,eAAe;EAE3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE0B,OAAO,EAAE/B,SAAS,CAAC8E,IAAI;EAEvB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEvC,UAAU,EAAEvC,SAAS,CAAC8E,IAAI;EAE1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEpC,SAAS,EAAE1C,SAAS,CAAC8E,IAAI;EAEzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACElC,MAAM,EAAE5C,SAAS,CAAC8E,IAAI;EAEtB;AACF;AACA;AACA;AACA;AACA;AACA;EACEhC,SAAS,EAAE9C,SAAS,CAAC8E,IAAI;EAEzB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE9B,QAAQ,EAAEhD,SAAS,CAAC8E;AACtB,CAAC,CAAC,GAAG,CAAC,CAAC;AACP,eAAe/D,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}