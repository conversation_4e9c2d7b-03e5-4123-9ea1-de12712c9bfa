# PowerShell script to test item code update flow

Write-Host "🧪 Testing Complete Item Code Update Flow..." -ForegroundColor Cyan
Write-Host ""

# Step 1: Login as developer
Write-Host "1️⃣ Logging in as developer..." -ForegroundColor Yellow
$loginBody = @{
    username = "developer"
    password = "developer123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/auth/login" -Method Post -Body $loginBody -ContentType "application/json"
    Write-Host "   ✅ Login successful: $($loginResponse.user.username) ($($loginResponse.user.role.name))" -ForegroundColor Green
    Write-Host "   📋 Total permissions: $($loginResponse.user.role.permissions.Count)" -ForegroundColor Green
    
    # Check for items.edit_code permission
    $hasEditCodePermission = $loginResponse.user.role.permissions | Where-Object { $_.name -eq "items.edit_code" }
    if ($hasEditCodePermission) {
        Write-Host "   🔑 Has items.edit_code permission: ✅ YES" -ForegroundColor Green
    } else {
        Write-Host "   🔑 Has items.edit_code permission: ❌ NO" -ForegroundColor Red
        exit 1
    }
    
    $token = $loginResponse.token
    $headers = @{ Authorization = "Bearer $token" }
    
} catch {
    Write-Host "❌ Login failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 2: Get an item to test with
Write-Host ""
Write-Host "2️⃣ Getting test item..." -ForegroundColor Yellow
try {
    $itemsResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/items" -Method Get -Headers $headers
    if ($itemsResponse.Count -eq 0) {
        Write-Host "❌ No items found" -ForegroundColor Red
        exit 1
    }
    
    $testItem = $itemsResponse[0]
    Write-Host "   ✅ Test item: $($testItem.itemCode) - $($testItem.name)" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Failed to get items: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Step 3: Prepare update data with new item code
$timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds().ToString().Substring(7)
$newItemCode = "$($testItem.itemCode)_TEST_$timestamp"

Write-Host ""
Write-Host "3️⃣ Testing item code update: $($testItem.itemCode) → $newItemCode" -ForegroundColor Yellow

$updateData = @{
    itemCode = $newItemCode
    name = "$($testItem.name) (Code Updated)"
    type = $testItem.type
    categoryId = $testItem.categoryId
    categoryName = $testItem.categoryName
    description = "$($testItem.description) - Updated for testing"
    salePrice = $testItem.salePrice
    purchasePrice = $testItem.purchasePrice
    openingStock = $testItem.openingStock
    reorderLevel = $testItem.reorderLevel
} | ConvertTo-Json

# Step 4: Perform the update
Write-Host "   📤 Sending update request..." -ForegroundColor Cyan
try {
    $updateResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/items/$($testItem.itemCode)" -Method Put -Body $updateData -ContentType "application/json" -Headers $headers
    
    Write-Host "   ✅ Update successful!" -ForegroundColor Green
    Write-Host "   📝 Original code: $($testItem.itemCode)" -ForegroundColor Green
    Write-Host "   📝 New code: $($updateResponse.itemCode)" -ForegroundColor Green
    Write-Host "   📝 Name: $($updateResponse.name)" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Update failed: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $errorStream = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($errorStream)
        $errorBody = $reader.ReadToEnd()
        Write-Host "Error details: $errorBody" -ForegroundColor Red
    }
    exit 1
}

# Step 5: Verify the change
Write-Host ""
Write-Host "4️⃣ Verifying the update..." -ForegroundColor Yellow
try {
    $verifyResponse = Invoke-RestMethod -Uri "http://localhost:5000/api/items" -Method Get -Headers $headers
    
    $foundWithNewCode = $verifyResponse | Where-Object { $_.itemCode -eq $newItemCode }
    $foundWithOldCode = $verifyResponse | Where-Object { $_.itemCode -eq $testItem.itemCode }
    
    if ($foundWithNewCode) {
        Write-Host "   🔍 Item found with new code ($newItemCode): ✅ YES" -ForegroundColor Green
    } else {
        Write-Host "   🔍 Item found with new code ($newItemCode): ❌ NO" -ForegroundColor Red
    }
    
    if ($foundWithOldCode) {
        Write-Host "   🔍 Item found with old code ($($testItem.itemCode)): ❌ YES (BAD)" -ForegroundColor Red
    } else {
        Write-Host "   🔍 Item found with old code ($($testItem.itemCode)): ✅ NO (GOOD)" -ForegroundColor Green
    }
    
    if ($foundWithNewCode -and -not $foundWithOldCode) {
        Write-Host "   ✅ Verification successful - item code properly updated!" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Verification failed - item code not properly updated" -ForegroundColor Red
    }
    
} catch {
    Write-Host "   ❌ Verification error: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 6: Revert changes
Write-Host ""
Write-Host "5️⃣ Reverting changes..." -ForegroundColor Yellow
$revertData = @{
    itemCode = $testItem.itemCode
    name = $testItem.name
    type = $testItem.type
    categoryId = $testItem.categoryId
    categoryName = $testItem.categoryName
    description = $testItem.description
    salePrice = $testItem.salePrice
    purchasePrice = $testItem.purchasePrice
    openingStock = $testItem.openingStock
    reorderLevel = $testItem.reorderLevel
} | ConvertTo-Json

try {
    Invoke-RestMethod -Uri "http://localhost:5000/api/items/$newItemCode" -Method Put -Body $revertData -ContentType "application/json" -Headers $headers | Out-Null
    Write-Host "   ✅ Changes reverted successfully" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️ Failed to revert changes: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "✅ Complete flow test finished!" -ForegroundColor Green
