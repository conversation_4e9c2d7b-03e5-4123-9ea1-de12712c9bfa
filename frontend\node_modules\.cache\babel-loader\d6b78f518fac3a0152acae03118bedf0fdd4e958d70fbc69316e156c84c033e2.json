{"ast": null, "code": "import BitArray from '../../../common/BitArray';\nvar BitArrayBuilder = /** @class */function () {\n  function BitArrayBuilder() {}\n  BitArrayBuilder.buildBitArray = function (pairs) {\n    var charNumber = pairs.length * 2 - 1;\n    if (pairs[pairs.length - 1].getRightChar() == null) {\n      charNumber -= 1;\n    }\n    var size = 12 * charNumber;\n    var binary = new BitArray(size);\n    var accPos = 0;\n    var firstPair = pairs[0];\n    var firstValue = firstPair.getRightChar().getValue();\n    for (var i = 11; i >= 0; --i) {\n      if ((firstValue & 1 << i) !== 0) {\n        binary.set(accPos);\n      }\n      accPos++;\n    }\n    for (var i = 1; i < pairs.length; ++i) {\n      var currentPair = pairs[i];\n      var leftValue = currentPair.getLeftChar().getValue();\n      for (var j = 11; j >= 0; --j) {\n        if ((leftValue & 1 << j) !== 0) {\n          binary.set(accPos);\n        }\n        accPos++;\n      }\n      if (currentPair.getRightChar() !== null) {\n        var rightValue = currentPair.getRightChar().getValue();\n        for (var j = 11; j >= 0; --j) {\n          if ((rightValue & 1 << j) !== 0) {\n            binary.set(accPos);\n          }\n          accPos++;\n        }\n      }\n    }\n    return binary;\n  };\n  return BitArrayBuilder;\n}();\nexport default BitArrayBuilder;", "map": {"version": 3, "names": ["BitArray", "BitArrayBuilder", "buildBitArray", "pairs", "char<PERSON><PERSON>ber", "length", "getRightChar", "size", "binary", "accPos", "firstPair", "firstValue", "getValue", "i", "set", "currentPair", "leftValue", "getLeftChar", "j", "rightValue"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/BitArrayBuilder.js"], "sourcesContent": ["import BitArray from '../../../common/BitArray';\nvar BitArrayBuilder = /** @class */ (function () {\n    function BitArrayBuilder() {\n    }\n    BitArrayBuilder.buildBitArray = function (pairs) {\n        var charNumber = pairs.length * 2 - 1;\n        if (pairs[pairs.length - 1].getRightChar() == null) {\n            charNumber -= 1;\n        }\n        var size = 12 * charNumber;\n        var binary = new BitArray(size);\n        var accPos = 0;\n        var firstPair = pairs[0];\n        var firstValue = firstPair.getRightChar().getValue();\n        for (var i = 11; i >= 0; --i) {\n            if ((firstValue & (1 << i)) !== 0) {\n                binary.set(accPos);\n            }\n            accPos++;\n        }\n        for (var i = 1; i < pairs.length; ++i) {\n            var currentPair = pairs[i];\n            var leftValue = currentPair.getLeftChar().getValue();\n            for (var j = 11; j >= 0; --j) {\n                if ((leftValue & (1 << j)) !== 0) {\n                    binary.set(accPos);\n                }\n                accPos++;\n            }\n            if (currentPair.getRightChar() !== null) {\n                var rightValue = currentPair.getRightChar().getValue();\n                for (var j = 11; j >= 0; --j) {\n                    if ((rightValue & (1 << j)) !== 0) {\n                        binary.set(accPos);\n                    }\n                    accPos++;\n                }\n            }\n        }\n        return binary;\n    };\n    return BitArrayBuilder;\n}());\nexport default BitArrayBuilder;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,0BAA0B;AAC/C,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG,CAC3B;EACAA,eAAe,CAACC,aAAa,GAAG,UAAUC,KAAK,EAAE;IAC7C,IAAIC,UAAU,GAAGD,KAAK,CAACE,MAAM,GAAG,CAAC,GAAG,CAAC;IACrC,IAAIF,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,IAAI,IAAI,EAAE;MAChDF,UAAU,IAAI,CAAC;IACnB;IACA,IAAIG,IAAI,GAAG,EAAE,GAAGH,UAAU;IAC1B,IAAII,MAAM,GAAG,IAAIR,QAAQ,CAACO,IAAI,CAAC;IAC/B,IAAIE,MAAM,GAAG,CAAC;IACd,IAAIC,SAAS,GAAGP,KAAK,CAAC,CAAC,CAAC;IACxB,IAAIQ,UAAU,GAAGD,SAAS,CAACJ,YAAY,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC;IACpD,KAAK,IAAIC,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAC1B,IAAI,CAACF,UAAU,GAAI,CAAC,IAAIE,CAAE,MAAM,CAAC,EAAE;QAC/BL,MAAM,CAACM,GAAG,CAACL,MAAM,CAAC;MACtB;MACAA,MAAM,EAAE;IACZ;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,KAAK,CAACE,MAAM,EAAE,EAAEQ,CAAC,EAAE;MACnC,IAAIE,WAAW,GAAGZ,KAAK,CAACU,CAAC,CAAC;MAC1B,IAAIG,SAAS,GAAGD,WAAW,CAACE,WAAW,CAAC,CAAC,CAACL,QAAQ,CAAC,CAAC;MACpD,KAAK,IAAIM,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC1B,IAAI,CAACF,SAAS,GAAI,CAAC,IAAIE,CAAE,MAAM,CAAC,EAAE;UAC9BV,MAAM,CAACM,GAAG,CAACL,MAAM,CAAC;QACtB;QACAA,MAAM,EAAE;MACZ;MACA,IAAIM,WAAW,CAACT,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE;QACrC,IAAIa,UAAU,GAAGJ,WAAW,CAACT,YAAY,CAAC,CAAC,CAACM,QAAQ,CAAC,CAAC;QACtD,KAAK,IAAIM,CAAC,GAAG,EAAE,EAAEA,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UAC1B,IAAI,CAACC,UAAU,GAAI,CAAC,IAAID,CAAE,MAAM,CAAC,EAAE;YAC/BV,MAAM,CAACM,GAAG,CAACL,MAAM,CAAC;UACtB;UACAA,MAAM,EAAE;QACZ;MACJ;IACJ;IACA,OAAOD,MAAM;EACjB,CAAC;EACD,OAAOP,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}