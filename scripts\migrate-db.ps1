# MongoDB Migration Script
# This script migrates data from customer_management to uni-core-business-suite

# Step 1: Export data from the old database
Write-Host "Exporting data from customer_management database..."
mongodump --db customer_management --out ./mongodb_backup

# Step 2: Import data to the new database
Write-Host "Importing data to uni-core-business-suite database..."
mongorestore --db uni-core-business-suite --dir ./mongodb_backup/customer_management

Write-Host "Migration completed!"
Write-Host "You can now use the uni-core-business-suite database with your application."
Write-Host "If everything works correctly, you can drop the old database using MongoDB Compass or the mongo shell."
