{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// import DataMatrixWriter from './datamatrix/DataMatrixWriter'\n// import CodaBarWriter from './oned/CodaBarWriter'\n// import Code128Writer from './oned/Code128Writer'\n// import Code39Writer from './oned/Code39Writer'\n// import Code93Writer from './oned/Code93Writer'\n// import EAN13Writer from './oned/EAN13Writer'\n// import EAN8Writer from './oned/EAN8Writer'\n// import ITFWriter from './oned/ITFWriter'\n// import UPCAWriter from './oned/UPCAWriter'\n// import UPCEWriter from './oned/UPCEWriter'\n// import PDF417Writer from './pdf417/PDF417Writer'\nimport QRCodeWriter from './qrcode/QRCodeWriter';\nimport BarcodeFormat from './BarcodeFormat';\nimport IllegalArgumentException from './IllegalArgumentException';\n/*import java.util.Map;*/\n/**\n * This is a factory class which finds the appropriate Writer subclass for the BarcodeFormat\n * requested and encodes the barcode with the supplied contents.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar MultiFormatWriter = /** @class */function () {\n  function MultiFormatWriter() {}\n  /*@Override*/\n  // public encode(contents: string,\n  //                         format: BarcodeFormat,\n  //                         width: number /*int*/,\n  //                         height: number /*int*/): BitMatrix /*throws WriterException */ {\n  //   return encode(contents, format, width, height, null)\n  // }\n  /*@Override*/\n  MultiFormatWriter.prototype.encode = function (contents, format, width /*int*/, height /*int*/, hints) {\n    var writer;\n    switch (format) {\n      // case BarcodeFormat.EAN_8:\n      //   writer = new EAN8Writer()\n      //   break\n      // case BarcodeFormat.UPC_E:\n      //   writer = new UPCEWriter()\n      //   break\n      // case BarcodeFormat.EAN_13:\n      //   writer = new EAN13Writer()\n      //   break\n      // case BarcodeFormat.UPC_A:\n      //   writer = new UPCAWriter()\n      //   break\n      case BarcodeFormat.QR_CODE:\n        writer = new QRCodeWriter();\n        break;\n      // case BarcodeFormat.CODE_39:\n      //   writer = new Code39Writer()\n      //   break\n      // case BarcodeFormat.CODE_93:\n      //   writer = new Code93Writer()\n      //   break\n      // case BarcodeFormat.CODE_128:\n      //   writer = new Code128Writer()\n      //   break\n      // case BarcodeFormat.ITF:\n      //   writer = new ITFWriter()\n      //   break\n      // case BarcodeFormat.PDF_417:\n      //   writer = new PDF417Writer()\n      //   break\n      // case BarcodeFormat.CODABAR:\n      //   writer = new CodaBarWriter()\n      //   break\n      // case BarcodeFormat.DATA_MATRIX:\n      //   writer = new DataMatrixWriter()\n      //   break\n      // case BarcodeFormat.AZTEC:\n      //   writer = new AztecWriter()\n      //   break\n      default:\n        throw new IllegalArgumentException('No encoder available for format ' + format);\n    }\n    return writer.encode(contents, format, width, height, hints);\n  };\n  return MultiFormatWriter;\n}();\nexport default MultiFormatWriter;", "map": {"version": 3, "names": ["QRCodeWriter", "BarcodeFormat", "IllegalArgumentException", "MultiFormatWriter", "prototype", "encode", "contents", "format", "width", "height", "hints", "writer", "QR_CODE"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/MultiFormatWriter.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n// import DataMatrixWriter from './datamatrix/DataMatrixWriter'\n// import CodaBarWriter from './oned/CodaBarWriter'\n// import Code128Writer from './oned/Code128Writer'\n// import Code39Writer from './oned/Code39Writer'\n// import Code93Writer from './oned/Code93Writer'\n// import EAN13Writer from './oned/EAN13Writer'\n// import EAN8Writer from './oned/EAN8Writer'\n// import ITFWriter from './oned/ITFWriter'\n// import UPCAWriter from './oned/UPCAWriter'\n// import UPCEWriter from './oned/UPCEWriter'\n// import PDF417Writer from './pdf417/PDF417Writer'\nimport QRCodeWriter from './qrcode/QRCodeWriter';\nimport BarcodeFormat from './BarcodeFormat';\nimport IllegalArgumentException from './IllegalArgumentException';\n/*import java.util.Map;*/\n/**\n * This is a factory class which finds the appropriate Writer subclass for the BarcodeFormat\n * requested and encodes the barcode with the supplied contents.\n *\n * <AUTHOR> (Daniel Switkin)\n */\nvar MultiFormatWriter = /** @class */ (function () {\n    function MultiFormatWriter() {\n    }\n    /*@Override*/\n    // public encode(contents: string,\n    //                         format: BarcodeFormat,\n    //                         width: number /*int*/,\n    //                         height: number /*int*/): BitMatrix /*throws WriterException */ {\n    //   return encode(contents, format, width, height, null)\n    // }\n    /*@Override*/\n    MultiFormatWriter.prototype.encode = function (contents, format, width /*int*/, height /*int*/, hints) {\n        var writer;\n        switch (format) {\n            // case BarcodeFormat.EAN_8:\n            //   writer = new EAN8Writer()\n            //   break\n            // case BarcodeFormat.UPC_E:\n            //   writer = new UPCEWriter()\n            //   break\n            // case BarcodeFormat.EAN_13:\n            //   writer = new EAN13Writer()\n            //   break\n            // case BarcodeFormat.UPC_A:\n            //   writer = new UPCAWriter()\n            //   break\n            case BarcodeFormat.QR_CODE:\n                writer = new QRCodeWriter();\n                break;\n            // case BarcodeFormat.CODE_39:\n            //   writer = new Code39Writer()\n            //   break\n            // case BarcodeFormat.CODE_93:\n            //   writer = new Code93Writer()\n            //   break\n            // case BarcodeFormat.CODE_128:\n            //   writer = new Code128Writer()\n            //   break\n            // case BarcodeFormat.ITF:\n            //   writer = new ITFWriter()\n            //   break\n            // case BarcodeFormat.PDF_417:\n            //   writer = new PDF417Writer()\n            //   break\n            // case BarcodeFormat.CODABAR:\n            //   writer = new CodaBarWriter()\n            //   break\n            // case BarcodeFormat.DATA_MATRIX:\n            //   writer = new DataMatrixWriter()\n            //   break\n            // case BarcodeFormat.AZTEC:\n            //   writer = new AztecWriter()\n            //   break\n            default:\n                throw new IllegalArgumentException('No encoder available for format ' + format);\n        }\n        return writer.encode(contents, format, width, height, hints);\n    };\n    return MultiFormatWriter;\n}());\nexport default MultiFormatWriter;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,YAAY,MAAM,uBAAuB;AAChD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,aAAe,YAAY;EAC/C,SAASA,iBAAiBA,CAAA,EAAG,CAC7B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAA,iBAAiB,CAACC,SAAS,CAACC,MAAM,GAAG,UAAUC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,CAAC,SAASC,MAAM,CAAC,SAASC,KAAK,EAAE;IACnG,IAAIC,MAAM;IACV,QAAQJ,MAAM;MACV;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAKN,aAAa,CAACW,OAAO;QACtBD,MAAM,GAAG,IAAIX,YAAY,CAAC,CAAC;QAC3B;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACI,MAAM,IAAIE,wBAAwB,CAAC,kCAAkC,GAAGK,MAAM,CAAC;IACvF;IACA,OAAOI,MAAM,CAACN,MAAM,CAACC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,CAAC;EAChE,CAAC;EACD,OAAOP,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AACJ,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}