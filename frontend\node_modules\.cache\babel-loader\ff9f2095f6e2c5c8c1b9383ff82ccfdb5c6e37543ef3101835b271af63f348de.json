{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from './IllegalArgumentException';\nvar BinaryBitmap = /** @class */function () {\n  function BinaryBitmap(binarizer) {\n    this.binarizer = binarizer;\n    if (binarizer === null) {\n      throw new IllegalArgumentException('Binarizer must be non-null.');\n    }\n  }\n  /**\n   * @return The width of the bitmap.\n   */\n  BinaryBitmap.prototype.getWidth = function () {\n    return this.binarizer.getWidth();\n  };\n  /**\n   * @return The height of the bitmap.\n   */\n  BinaryBitmap.prototype.getHeight = function () {\n    return this.binarizer.getHeight();\n  };\n  /**\n   * Converts one row of luminance data to 1 bit data. May actually do the conversion, or return\n   * cached data. Callers should assume this method is expensive and call it as seldom as possible.\n   * This method is intended for decoding 1D barcodes and may choose to apply sharpening.\n   *\n   * @param y The row to fetch, which must be in [0, bitmap height)\n   * @param row An optional preallocated array. If null or too small, it will be ignored.\n   *            If used, the Binarizer will call BitArray.clear(). Always use the returned object.\n   * @return The array of bits for this row (true means black).\n   * @throws NotFoundException if row can't be binarized\n   */\n  BinaryBitmap.prototype.getBlackRow = function (y /*int*/, row) {\n    return this.binarizer.getBlackRow(y, row);\n  };\n  /**\n   * Converts a 2D array of luminance data to 1 bit. As above, assume this method is expensive\n   * and do not call it repeatedly. This method is intended for decoding 2D barcodes and may or\n   * may not apply sharpening. Therefore, a row from this matrix may not be identical to one\n   * fetched using getBlackRow(), so don't mix and match between them.\n   *\n   * @return The 2D array of bits for the image (true means black).\n   * @throws NotFoundException if image can't be binarized to make a matrix\n   */\n  BinaryBitmap.prototype.getBlackMatrix = function () {\n    // The matrix is created on demand the first time it is requested, then cached. There are two\n    // reasons for this:\n    // 1. This work will never be done if the caller only installs 1D Reader objects, or if a\n    //    1D Reader finds a barcode before the 2D Readers run.\n    // 2. This work will only be done once even if the caller installs multiple 2D Readers.\n    if (this.matrix === null || this.matrix === undefined) {\n      this.matrix = this.binarizer.getBlackMatrix();\n    }\n    return this.matrix;\n  };\n  /**\n   * @return Whether this bitmap can be cropped.\n   */\n  BinaryBitmap.prototype.isCropSupported = function () {\n    return this.binarizer.getLuminanceSource().isCropSupported();\n  };\n  /**\n   * Returns a new object with cropped image data. Implementations may keep a reference to the\n   * original data rather than a copy. Only callable if isCropSupported() is true.\n   *\n   * @param left The left coordinate, which must be in [0,getWidth())\n   * @param top The top coordinate, which must be in [0,getHeight())\n   * @param width The width of the rectangle to crop.\n   * @param height The height of the rectangle to crop.\n   * @return A cropped version of this object.\n   */\n  BinaryBitmap.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n    var newSource = this.binarizer.getLuminanceSource().crop(left, top, width, height);\n    return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n  };\n  /**\n   * @return Whether this bitmap supports counter-clockwise rotation.\n   */\n  BinaryBitmap.prototype.isRotateSupported = function () {\n    return this.binarizer.getLuminanceSource().isRotateSupported();\n  };\n  /**\n   * Returns a new object with rotated image data by 90 degrees counterclockwise.\n   * Only callable if {@link #isRotateSupported()} is true.\n   *\n   * @return A rotated version of this object.\n   */\n  BinaryBitmap.prototype.rotateCounterClockwise = function () {\n    var newSource = this.binarizer.getLuminanceSource().rotateCounterClockwise();\n    return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n  };\n  /**\n   * Returns a new object with rotated image data by 45 degrees counterclockwise.\n   * Only callable if {@link #isRotateSupported()} is true.\n   *\n   * @return A rotated version of this object.\n   */\n  BinaryBitmap.prototype.rotateCounterClockwise45 = function () {\n    var newSource = this.binarizer.getLuminanceSource().rotateCounterClockwise45();\n    return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n  };\n  /*@Override*/\n  BinaryBitmap.prototype.toString = function () {\n    try {\n      return this.getBlackMatrix().toString();\n    } catch (e /*: NotFoundException*/) {\n      return '';\n    }\n  };\n  return BinaryBitmap;\n}();\nexport default BinaryBitmap;", "map": {"version": 3, "names": ["IllegalArgumentException", "BinaryBitmap", "binarizer", "prototype", "getWidth", "getHeight", "getBlackRow", "y", "row", "getBlackMatrix", "matrix", "undefined", "isCropSupported", "getLuminanceSource", "crop", "left", "top", "width", "height", "newSource", "createBinarizer", "isRotateSupported", "rotateCounterClockwise", "rotateCounterClockwise45", "toString", "e"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/BinaryBitmap.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport IllegalArgumentException from './IllegalArgumentException';\nvar BinaryBitmap = /** @class */ (function () {\n    function BinaryBitmap(binarizer) {\n        this.binarizer = binarizer;\n        if (binarizer === null) {\n            throw new IllegalArgumentException('Binarizer must be non-null.');\n        }\n    }\n    /**\n     * @return The width of the bitmap.\n     */\n    BinaryBitmap.prototype.getWidth = function () {\n        return this.binarizer.getWidth();\n    };\n    /**\n     * @return The height of the bitmap.\n     */\n    BinaryBitmap.prototype.getHeight = function () {\n        return this.binarizer.getHeight();\n    };\n    /**\n     * Converts one row of luminance data to 1 bit data. May actually do the conversion, or return\n     * cached data. Callers should assume this method is expensive and call it as seldom as possible.\n     * This method is intended for decoding 1D barcodes and may choose to apply sharpening.\n     *\n     * @param y The row to fetch, which must be in [0, bitmap height)\n     * @param row An optional preallocated array. If null or too small, it will be ignored.\n     *            If used, the Binarizer will call BitArray.clear(). Always use the returned object.\n     * @return The array of bits for this row (true means black).\n     * @throws NotFoundException if row can't be binarized\n     */\n    BinaryBitmap.prototype.getBlackRow = function (y /*int*/, row) {\n        return this.binarizer.getBlackRow(y, row);\n    };\n    /**\n     * Converts a 2D array of luminance data to 1 bit. As above, assume this method is expensive\n     * and do not call it repeatedly. This method is intended for decoding 2D barcodes and may or\n     * may not apply sharpening. Therefore, a row from this matrix may not be identical to one\n     * fetched using getBlackRow(), so don't mix and match between them.\n     *\n     * @return The 2D array of bits for the image (true means black).\n     * @throws NotFoundException if image can't be binarized to make a matrix\n     */\n    BinaryBitmap.prototype.getBlackMatrix = function () {\n        // The matrix is created on demand the first time it is requested, then cached. There are two\n        // reasons for this:\n        // 1. This work will never be done if the caller only installs 1D Reader objects, or if a\n        //    1D Reader finds a barcode before the 2D Readers run.\n        // 2. This work will only be done once even if the caller installs multiple 2D Readers.\n        if (this.matrix === null || this.matrix === undefined) {\n            this.matrix = this.binarizer.getBlackMatrix();\n        }\n        return this.matrix;\n    };\n    /**\n     * @return Whether this bitmap can be cropped.\n     */\n    BinaryBitmap.prototype.isCropSupported = function () {\n        return this.binarizer.getLuminanceSource().isCropSupported();\n    };\n    /**\n     * Returns a new object with cropped image data. Implementations may keep a reference to the\n     * original data rather than a copy. Only callable if isCropSupported() is true.\n     *\n     * @param left The left coordinate, which must be in [0,getWidth())\n     * @param top The top coordinate, which must be in [0,getHeight())\n     * @param width The width of the rectangle to crop.\n     * @param height The height of the rectangle to crop.\n     * @return A cropped version of this object.\n     */\n    BinaryBitmap.prototype.crop = function (left /*int*/, top /*int*/, width /*int*/, height /*int*/) {\n        var newSource = this.binarizer.getLuminanceSource().crop(left, top, width, height);\n        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n    };\n    /**\n     * @return Whether this bitmap supports counter-clockwise rotation.\n     */\n    BinaryBitmap.prototype.isRotateSupported = function () {\n        return this.binarizer.getLuminanceSource().isRotateSupported();\n    };\n    /**\n     * Returns a new object with rotated image data by 90 degrees counterclockwise.\n     * Only callable if {@link #isRotateSupported()} is true.\n     *\n     * @return A rotated version of this object.\n     */\n    BinaryBitmap.prototype.rotateCounterClockwise = function () {\n        var newSource = this.binarizer.getLuminanceSource().rotateCounterClockwise();\n        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n    };\n    /**\n     * Returns a new object with rotated image data by 45 degrees counterclockwise.\n     * Only callable if {@link #isRotateSupported()} is true.\n     *\n     * @return A rotated version of this object.\n     */\n    BinaryBitmap.prototype.rotateCounterClockwise45 = function () {\n        var newSource = this.binarizer.getLuminanceSource().rotateCounterClockwise45();\n        return new BinaryBitmap(this.binarizer.createBinarizer(newSource));\n    };\n    /*@Override*/\n    BinaryBitmap.prototype.toString = function () {\n        try {\n            return this.getBlackMatrix().toString();\n        }\n        catch (e /*: NotFoundException*/) {\n            return '';\n        }\n    };\n    return BinaryBitmap;\n}());\nexport default BinaryBitmap;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,wBAAwB,MAAM,4BAA4B;AACjE,IAAIC,YAAY,GAAG,aAAe,YAAY;EAC1C,SAASA,YAAYA,CAACC,SAAS,EAAE;IAC7B,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAIA,SAAS,KAAK,IAAI,EAAE;MACpB,MAAM,IAAIF,wBAAwB,CAAC,6BAA6B,CAAC;IACrE;EACJ;EACA;AACJ;AACA;EACIC,YAAY,CAACE,SAAS,CAACC,QAAQ,GAAG,YAAY;IAC1C,OAAO,IAAI,CAACF,SAAS,CAACE,QAAQ,CAAC,CAAC;EACpC,CAAC;EACD;AACJ;AACA;EACIH,YAAY,CAACE,SAAS,CAACE,SAAS,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACH,SAAS,CAACG,SAAS,CAAC,CAAC;EACrC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIJ,YAAY,CAACE,SAAS,CAACG,WAAW,GAAG,UAAUC,CAAC,CAAC,SAASC,GAAG,EAAE;IAC3D,OAAO,IAAI,CAACN,SAAS,CAACI,WAAW,CAACC,CAAC,EAAEC,GAAG,CAAC;EAC7C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIP,YAAY,CAACE,SAAS,CAACM,cAAc,GAAG,YAAY;IAChD;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACC,MAAM,KAAK,IAAI,IAAI,IAAI,CAACA,MAAM,KAAKC,SAAS,EAAE;MACnD,IAAI,CAACD,MAAM,GAAG,IAAI,CAACR,SAAS,CAACO,cAAc,CAAC,CAAC;IACjD;IACA,OAAO,IAAI,CAACC,MAAM;EACtB,CAAC;EACD;AACJ;AACA;EACIT,YAAY,CAACE,SAAS,CAACS,eAAe,GAAG,YAAY;IACjD,OAAO,IAAI,CAACV,SAAS,CAACW,kBAAkB,CAAC,CAAC,CAACD,eAAe,CAAC,CAAC;EAChE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIX,YAAY,CAACE,SAAS,CAACW,IAAI,GAAG,UAAUC,IAAI,CAAC,SAASC,GAAG,CAAC,SAASC,KAAK,CAAC,SAASC,MAAM,CAAC,SAAS;IAC9F,IAAIC,SAAS,GAAG,IAAI,CAACjB,SAAS,CAACW,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAACC,IAAI,EAAEC,GAAG,EAAEC,KAAK,EAAEC,MAAM,CAAC;IAClF,OAAO,IAAIjB,YAAY,CAAC,IAAI,CAACC,SAAS,CAACkB,eAAe,CAACD,SAAS,CAAC,CAAC;EACtE,CAAC;EACD;AACJ;AACA;EACIlB,YAAY,CAACE,SAAS,CAACkB,iBAAiB,GAAG,YAAY;IACnD,OAAO,IAAI,CAACnB,SAAS,CAACW,kBAAkB,CAAC,CAAC,CAACQ,iBAAiB,CAAC,CAAC;EAClE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIpB,YAAY,CAACE,SAAS,CAACmB,sBAAsB,GAAG,YAAY;IACxD,IAAIH,SAAS,GAAG,IAAI,CAACjB,SAAS,CAACW,kBAAkB,CAAC,CAAC,CAACS,sBAAsB,CAAC,CAAC;IAC5E,OAAO,IAAIrB,YAAY,CAAC,IAAI,CAACC,SAAS,CAACkB,eAAe,CAACD,SAAS,CAAC,CAAC;EACtE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIlB,YAAY,CAACE,SAAS,CAACoB,wBAAwB,GAAG,YAAY;IAC1D,IAAIJ,SAAS,GAAG,IAAI,CAACjB,SAAS,CAACW,kBAAkB,CAAC,CAAC,CAACU,wBAAwB,CAAC,CAAC;IAC9E,OAAO,IAAItB,YAAY,CAAC,IAAI,CAACC,SAAS,CAACkB,eAAe,CAACD,SAAS,CAAC,CAAC;EACtE,CAAC;EACD;EACAlB,YAAY,CAACE,SAAS,CAACqB,QAAQ,GAAG,YAAY;IAC1C,IAAI;MACA,OAAO,IAAI,CAACf,cAAc,CAAC,CAAC,CAACe,QAAQ,CAAC,CAAC;IAC3C,CAAC,CACD,OAAOC,CAAC,CAAC,yBAAyB;MAC9B,OAAO,EAAE;IACb;EACJ,CAAC;EACD,OAAOxB,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}