import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';
import { ArrowBack as ArrowBackIcon } from '@mui/icons-material';
import BankPaymentVoucherForm from '../components/BankPaymentVoucherForm';

const ManageBankPaymentPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Get voucherId from URL params or location state
  const searchParams = new URLSearchParams(location.search);
  const voucherId = searchParams.get('id') || location.state?.voucherId;
  const readOnly = searchParams.get('view') === 'true' || location.state?.readOnly;
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSave = async (savedVoucher) => {
    try {
      setLoading(true);
      setError(null);
      
      // Show success message
      alert(voucherId ? 'Bank payment updated successfully!' : 'Bank payment created successfully!');
      
      // Navigate back to bank payments list
      navigate('/bank-payments');
      
    } catch (error) {
      console.error('Error saving bank payment:', error);
      setError(error.message || 'Failed to save bank payment');
      alert('Failed to save bank payment');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate('/bank-payments');
  };

  const getPageTitle = () => {
    if (readOnly) return 'View Bank Payment';
    if (voucherId) return 'Edit Bank Payment';
    return 'Create New Bank Payment';
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={handleCancel}
          sx={{ mr: 2 }}
        >
          Back to Bank Payments
        </Button>
        <Typography variant="h4" component="h1">
          {getPageTitle()}
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading Indicator */}
      {loading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Main Content */}
      <Card>
        <CardContent>
          <BankPaymentVoucherForm
            voucherId={voucherId}
            readOnly={readOnly}
            onSave={handleSave}
            onCancel={handleCancel}
          />
        </CardContent>
      </Card>
    </Box>
  );
};

export default ManageBankPaymentPage;
