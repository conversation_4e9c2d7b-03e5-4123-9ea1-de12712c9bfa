{"ast": null, "code": "import StringBuilder from '../../util/StringBuilder';\nimport SymbolInfo from './SymbolInfo';\nvar EncoderContext = /** @class */function () {\n  function EncoderContext(msg) {\n    this.msg = msg;\n    this.pos = 0;\n    this.skipAtEnd = 0;\n    // From this point on Strings are not Unicode anymore!\n    var msgBinary = msg.split('').map(function (c) {\n      return c.charCodeAt(0);\n    });\n    var sb = new StringBuilder();\n    for (var i = 0, c = msgBinary.length; i < c; i++) {\n      var ch = String.fromCharCode(msgBinary[i] & 0xff);\n      if (ch === '?' && msg.charAt(i) !== '?') {\n        throw new Error('Message contains characters outside ISO-8859-1 encoding.');\n      }\n      sb.append(ch);\n    }\n    this.msg = sb.toString(); // Not Unicode here!\n    this.shape = 0 /* FORCE_NONE */;\n    this.codewords = new StringBuilder();\n    this.newEncoding = -1;\n  }\n  EncoderContext.prototype.setSymbolShape = function (shape) {\n    this.shape = shape;\n  };\n  EncoderContext.prototype.setSizeConstraints = function (minSize, maxSize) {\n    this.minSize = minSize;\n    this.maxSize = maxSize;\n  };\n  EncoderContext.prototype.getMessage = function () {\n    return this.msg;\n  };\n  EncoderContext.prototype.setSkipAtEnd = function (count) {\n    this.skipAtEnd = count;\n  };\n  EncoderContext.prototype.getCurrentChar = function () {\n    return this.msg.charCodeAt(this.pos);\n  };\n  EncoderContext.prototype.getCurrent = function () {\n    return this.msg.charCodeAt(this.pos);\n  };\n  EncoderContext.prototype.getCodewords = function () {\n    return this.codewords;\n  };\n  EncoderContext.prototype.writeCodewords = function (codewords) {\n    this.codewords.append(codewords);\n  };\n  EncoderContext.prototype.writeCodeword = function (codeword) {\n    this.codewords.append(codeword);\n  };\n  EncoderContext.prototype.getCodewordCount = function () {\n    return this.codewords.length();\n  };\n  EncoderContext.prototype.getNewEncoding = function () {\n    return this.newEncoding;\n  };\n  EncoderContext.prototype.signalEncoderChange = function (encoding) {\n    this.newEncoding = encoding;\n  };\n  EncoderContext.prototype.resetEncoderSignal = function () {\n    this.newEncoding = -1;\n  };\n  EncoderContext.prototype.hasMoreCharacters = function () {\n    return this.pos < this.getTotalMessageCharCount();\n  };\n  EncoderContext.prototype.getTotalMessageCharCount = function () {\n    return this.msg.length - this.skipAtEnd;\n  };\n  EncoderContext.prototype.getRemainingCharacters = function () {\n    return this.getTotalMessageCharCount() - this.pos;\n  };\n  EncoderContext.prototype.getSymbolInfo = function () {\n    return this.symbolInfo;\n  };\n  EncoderContext.prototype.updateSymbolInfo = function (len) {\n    if (len === void 0) {\n      len = this.getCodewordCount();\n    }\n    if (this.symbolInfo == null || len > this.symbolInfo.getDataCapacity()) {\n      this.symbolInfo = SymbolInfo.lookup(len, this.shape, this.minSize, this.maxSize, true);\n    }\n  };\n  EncoderContext.prototype.resetSymbolInfo = function () {\n    this.symbolInfo = null;\n  };\n  return EncoderContext;\n}();\nexport { EncoderContext };", "map": {"version": 3, "names": ["StringBuilder", "SymbolInfo", "EncoderContext", "msg", "pos", "skipAtEnd", "msgBinary", "split", "map", "c", "charCodeAt", "sb", "i", "length", "ch", "String", "fromCharCode", "char<PERSON>t", "Error", "append", "toString", "shape", "codewords", "newEncoding", "prototype", "setSymbolShape", "setSizeConstraints", "minSize", "maxSize", "getMessage", "setSkipAtEnd", "count", "getCurrentChar", "get<PERSON>urrent", "getCodewords", "writeCodewords", "writeCodeword", "codeword", "getCodewordCount", "getNewEncoding", "signalEncoderChange", "encoding", "resetEncoderSignal", "hasMoreCharacters", "getTotalMessageCharCount", "getRemainingCharacters", "getSymbolInfo", "symbolInfo", "updateSymbolInfo", "len", "getDataCapacity", "lookup", "resetSymbolInfo"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/EncoderContext.js"], "sourcesContent": ["import StringBuilder from '../../util/StringBuilder';\nimport SymbolInfo from './SymbolInfo';\nvar EncoderContext = /** @class */ (function () {\n    function EncoderContext(msg) {\n        this.msg = msg;\n        this.pos = 0;\n        this.skipAtEnd = 0;\n        // From this point on Strings are not Unicode anymore!\n        var msgBinary = msg.split('').map(function (c) { return c.charCodeAt(0); });\n        var sb = new StringBuilder();\n        for (var i = 0, c = msgBinary.length; i < c; i++) {\n            var ch = String.fromCharCode(msgBinary[i] & 0xff);\n            if (ch === '?' && msg.charAt(i) !== '?') {\n                throw new Error('Message contains characters outside ISO-8859-1 encoding.');\n            }\n            sb.append(ch);\n        }\n        this.msg = sb.toString(); // Not Unicode here!\n        this.shape = 0 /* FORCE_NONE */;\n        this.codewords = new StringBuilder();\n        this.newEncoding = -1;\n    }\n    EncoderContext.prototype.setSymbolShape = function (shape) {\n        this.shape = shape;\n    };\n    EncoderContext.prototype.setSizeConstraints = function (minSize, maxSize) {\n        this.minSize = minSize;\n        this.maxSize = maxSize;\n    };\n    EncoderContext.prototype.getMessage = function () {\n        return this.msg;\n    };\n    EncoderContext.prototype.setSkipAtEnd = function (count) {\n        this.skipAtEnd = count;\n    };\n    EncoderContext.prototype.getCurrentChar = function () {\n        return this.msg.charCodeAt(this.pos);\n    };\n    EncoderContext.prototype.getCurrent = function () {\n        return this.msg.charCodeAt(this.pos);\n    };\n    EncoderContext.prototype.getCodewords = function () {\n        return this.codewords;\n    };\n    EncoderContext.prototype.writeCodewords = function (codewords) {\n        this.codewords.append(codewords);\n    };\n    EncoderContext.prototype.writeCodeword = function (codeword) {\n        this.codewords.append(codeword);\n    };\n    EncoderContext.prototype.getCodewordCount = function () {\n        return this.codewords.length();\n    };\n    EncoderContext.prototype.getNewEncoding = function () {\n        return this.newEncoding;\n    };\n    EncoderContext.prototype.signalEncoderChange = function (encoding) {\n        this.newEncoding = encoding;\n    };\n    EncoderContext.prototype.resetEncoderSignal = function () {\n        this.newEncoding = -1;\n    };\n    EncoderContext.prototype.hasMoreCharacters = function () {\n        return this.pos < this.getTotalMessageCharCount();\n    };\n    EncoderContext.prototype.getTotalMessageCharCount = function () {\n        return this.msg.length - this.skipAtEnd;\n    };\n    EncoderContext.prototype.getRemainingCharacters = function () {\n        return this.getTotalMessageCharCount() - this.pos;\n    };\n    EncoderContext.prototype.getSymbolInfo = function () {\n        return this.symbolInfo;\n    };\n    EncoderContext.prototype.updateSymbolInfo = function (len) {\n        if (len === void 0) { len = this.getCodewordCount(); }\n        if (this.symbolInfo == null || len > this.symbolInfo.getDataCapacity()) {\n            this.symbolInfo = SymbolInfo.lookup(len, this.shape, this.minSize, this.maxSize, true);\n        }\n    };\n    EncoderContext.prototype.resetSymbolInfo = function () {\n        this.symbolInfo = null;\n    };\n    return EncoderContext;\n}());\nexport { EncoderContext };\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0BAA0B;AACpD,OAAOC,UAAU,MAAM,cAAc;AACrC,IAAIC,cAAc,GAAG,aAAe,YAAY;EAC5C,SAASA,cAAcA,CAACC,GAAG,EAAE;IACzB,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAG,CAAC;IACZ,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB;IACA,IAAIC,SAAS,GAAGH,GAAG,CAACI,KAAK,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;MAAE,OAAOA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;IAC3E,IAAIC,EAAE,GAAG,IAAIX,aAAa,CAAC,CAAC;IAC5B,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEH,CAAC,GAAGH,SAAS,CAACO,MAAM,EAAED,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MAC9C,IAAIE,EAAE,GAAGC,MAAM,CAACC,YAAY,CAACV,SAAS,CAACM,CAAC,CAAC,GAAG,IAAI,CAAC;MACjD,IAAIE,EAAE,KAAK,GAAG,IAAIX,GAAG,CAACc,MAAM,CAACL,CAAC,CAAC,KAAK,GAAG,EAAE;QACrC,MAAM,IAAIM,KAAK,CAAC,0DAA0D,CAAC;MAC/E;MACAP,EAAE,CAACQ,MAAM,CAACL,EAAE,CAAC;IACjB;IACA,IAAI,CAACX,GAAG,GAAGQ,EAAE,CAACS,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;IACf,IAAI,CAACC,SAAS,GAAG,IAAItB,aAAa,CAAC,CAAC;IACpC,IAAI,CAACuB,WAAW,GAAG,CAAC,CAAC;EACzB;EACArB,cAAc,CAACsB,SAAS,CAACC,cAAc,GAAG,UAAUJ,KAAK,EAAE;IACvD,IAAI,CAACA,KAAK,GAAGA,KAAK;EACtB,CAAC;EACDnB,cAAc,CAACsB,SAAS,CAACE,kBAAkB,GAAG,UAAUC,OAAO,EAAEC,OAAO,EAAE;IACtE,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B,CAAC;EACD1B,cAAc,CAACsB,SAAS,CAACK,UAAU,GAAG,YAAY;IAC9C,OAAO,IAAI,CAAC1B,GAAG;EACnB,CAAC;EACDD,cAAc,CAACsB,SAAS,CAACM,YAAY,GAAG,UAAUC,KAAK,EAAE;IACrD,IAAI,CAAC1B,SAAS,GAAG0B,KAAK;EAC1B,CAAC;EACD7B,cAAc,CAACsB,SAAS,CAACQ,cAAc,GAAG,YAAY;IAClD,OAAO,IAAI,CAAC7B,GAAG,CAACO,UAAU,CAAC,IAAI,CAACN,GAAG,CAAC;EACxC,CAAC;EACDF,cAAc,CAACsB,SAAS,CAACS,UAAU,GAAG,YAAY;IAC9C,OAAO,IAAI,CAAC9B,GAAG,CAACO,UAAU,CAAC,IAAI,CAACN,GAAG,CAAC;EACxC,CAAC;EACDF,cAAc,CAACsB,SAAS,CAACU,YAAY,GAAG,YAAY;IAChD,OAAO,IAAI,CAACZ,SAAS;EACzB,CAAC;EACDpB,cAAc,CAACsB,SAAS,CAACW,cAAc,GAAG,UAAUb,SAAS,EAAE;IAC3D,IAAI,CAACA,SAAS,CAACH,MAAM,CAACG,SAAS,CAAC;EACpC,CAAC;EACDpB,cAAc,CAACsB,SAAS,CAACY,aAAa,GAAG,UAAUC,QAAQ,EAAE;IACzD,IAAI,CAACf,SAAS,CAACH,MAAM,CAACkB,QAAQ,CAAC;EACnC,CAAC;EACDnC,cAAc,CAACsB,SAAS,CAACc,gBAAgB,GAAG,YAAY;IACpD,OAAO,IAAI,CAAChB,SAAS,CAACT,MAAM,CAAC,CAAC;EAClC,CAAC;EACDX,cAAc,CAACsB,SAAS,CAACe,cAAc,GAAG,YAAY;IAClD,OAAO,IAAI,CAAChB,WAAW;EAC3B,CAAC;EACDrB,cAAc,CAACsB,SAAS,CAACgB,mBAAmB,GAAG,UAAUC,QAAQ,EAAE;IAC/D,IAAI,CAAClB,WAAW,GAAGkB,QAAQ;EAC/B,CAAC;EACDvC,cAAc,CAACsB,SAAS,CAACkB,kBAAkB,GAAG,YAAY;IACtD,IAAI,CAACnB,WAAW,GAAG,CAAC,CAAC;EACzB,CAAC;EACDrB,cAAc,CAACsB,SAAS,CAACmB,iBAAiB,GAAG,YAAY;IACrD,OAAO,IAAI,CAACvC,GAAG,GAAG,IAAI,CAACwC,wBAAwB,CAAC,CAAC;EACrD,CAAC;EACD1C,cAAc,CAACsB,SAAS,CAACoB,wBAAwB,GAAG,YAAY;IAC5D,OAAO,IAAI,CAACzC,GAAG,CAACU,MAAM,GAAG,IAAI,CAACR,SAAS;EAC3C,CAAC;EACDH,cAAc,CAACsB,SAAS,CAACqB,sBAAsB,GAAG,YAAY;IAC1D,OAAO,IAAI,CAACD,wBAAwB,CAAC,CAAC,GAAG,IAAI,CAACxC,GAAG;EACrD,CAAC;EACDF,cAAc,CAACsB,SAAS,CAACsB,aAAa,GAAG,YAAY;IACjD,OAAO,IAAI,CAACC,UAAU;EAC1B,CAAC;EACD7C,cAAc,CAACsB,SAAS,CAACwB,gBAAgB,GAAG,UAAUC,GAAG,EAAE;IACvD,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;MAAEA,GAAG,GAAG,IAAI,CAACX,gBAAgB,CAAC,CAAC;IAAE;IACrD,IAAI,IAAI,CAACS,UAAU,IAAI,IAAI,IAAIE,GAAG,GAAG,IAAI,CAACF,UAAU,CAACG,eAAe,CAAC,CAAC,EAAE;MACpE,IAAI,CAACH,UAAU,GAAG9C,UAAU,CAACkD,MAAM,CAACF,GAAG,EAAE,IAAI,CAAC5B,KAAK,EAAE,IAAI,CAACM,OAAO,EAAE,IAAI,CAACC,OAAO,EAAE,IAAI,CAAC;IAC1F;EACJ,CAAC;EACD1B,cAAc,CAACsB,SAAS,CAAC4B,eAAe,GAAG,YAAY;IACnD,IAAI,CAACL,UAAU,GAAG,IAAI;EAC1B,CAAC;EACD,OAAO7C,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}