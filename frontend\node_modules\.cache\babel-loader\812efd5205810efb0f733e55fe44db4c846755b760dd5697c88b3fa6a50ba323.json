{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.common {*/\nimport Binarizer from '../Binarizer';\nimport BitArray from './BitArray';\nimport BitMatrix from './BitMatrix';\nimport NotFoundException from '../NotFoundException';\n/**\n * This Binarizer implementation uses the old ZXing global histogram approach. It is suitable\n * for low-end mobile devices which don't have enough CPU or memory to use a local thresholding\n * algorithm. However, because it picks a global black point, it cannot handle difficult shadows\n * and gradients.\n *\n * Faster mobile devices and all desktop applications should probably use HybridBinarizer instead.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n */\nvar GlobalHistogramBinarizer = /** @class */function (_super) {\n  __extends(GlobalHistogramBinarizer, _super);\n  function GlobalHistogramBinarizer(source) {\n    var _this = _super.call(this, source) || this;\n    _this.luminances = GlobalHistogramBinarizer.EMPTY;\n    _this.buckets = new Int32Array(GlobalHistogramBinarizer.LUMINANCE_BUCKETS);\n    return _this;\n  }\n  // Applies simple sharpening to the row data to improve performance of the 1D Readers.\n  /*@Override*/\n  GlobalHistogramBinarizer.prototype.getBlackRow = function (y /*int*/, row) {\n    var source = this.getLuminanceSource();\n    var width = source.getWidth();\n    if (row === undefined || row === null || row.getSize() < width) {\n      row = new BitArray(width);\n    } else {\n      row.clear();\n    }\n    this.initArrays(width);\n    var localLuminances = source.getRow(y, this.luminances);\n    var localBuckets = this.buckets;\n    for (var x = 0; x < width; x++) {\n      localBuckets[(localLuminances[x] & 0xff) >> GlobalHistogramBinarizer.LUMINANCE_SHIFT]++;\n    }\n    var blackPoint = GlobalHistogramBinarizer.estimateBlackPoint(localBuckets);\n    if (width < 3) {\n      // Special case for very small images\n      for (var x = 0; x < width; x++) {\n        if ((localLuminances[x] & 0xff) < blackPoint) {\n          row.set(x);\n        }\n      }\n    } else {\n      var left = localLuminances[0] & 0xff;\n      var center = localLuminances[1] & 0xff;\n      for (var x = 1; x < width - 1; x++) {\n        var right = localLuminances[x + 1] & 0xff;\n        // A simple -1 4 -1 box filter with a weight of 2.\n        if ((center * 4 - left - right) / 2 < blackPoint) {\n          row.set(x);\n        }\n        left = center;\n        center = right;\n      }\n    }\n    return row;\n  };\n  // Does not sharpen the data, as this call is intended to only be used by 2D Readers.\n  /*@Override*/\n  GlobalHistogramBinarizer.prototype.getBlackMatrix = function () {\n    var source = this.getLuminanceSource();\n    var width = source.getWidth();\n    var height = source.getHeight();\n    var matrix = new BitMatrix(width, height);\n    // Quickly calculates the histogram by sampling four rows from the image. This proved to be\n    // more robust on the blackbox tests than sampling a diagonal as we used to do.\n    this.initArrays(width);\n    var localBuckets = this.buckets;\n    for (var y = 1; y < 5; y++) {\n      var row = Math.floor(height * y / 5);\n      var localLuminances_1 = source.getRow(row, this.luminances);\n      var right = Math.floor(width * 4 / 5);\n      for (var x = Math.floor(width / 5); x < right; x++) {\n        var pixel = localLuminances_1[x] & 0xff;\n        localBuckets[pixel >> GlobalHistogramBinarizer.LUMINANCE_SHIFT]++;\n      }\n    }\n    var blackPoint = GlobalHistogramBinarizer.estimateBlackPoint(localBuckets);\n    // We delay reading the entire image luminance until the black point estimation succeeds.\n    // Although we end up reading four rows twice, it is consistent with our motto of\n    // \"fail quickly\" which is necessary for continuous scanning.\n    var localLuminances = source.getMatrix();\n    for (var y = 0; y < height; y++) {\n      var offset = y * width;\n      for (var x = 0; x < width; x++) {\n        var pixel = localLuminances[offset + x] & 0xff;\n        if (pixel < blackPoint) {\n          matrix.set(x, y);\n        }\n      }\n    }\n    return matrix;\n  };\n  /*@Override*/\n  GlobalHistogramBinarizer.prototype.createBinarizer = function (source) {\n    return new GlobalHistogramBinarizer(source);\n  };\n  GlobalHistogramBinarizer.prototype.initArrays = function (luminanceSize /*int*/) {\n    if (this.luminances.length < luminanceSize) {\n      this.luminances = new Uint8ClampedArray(luminanceSize);\n    }\n    var buckets = this.buckets;\n    for (var x = 0; x < GlobalHistogramBinarizer.LUMINANCE_BUCKETS; x++) {\n      buckets[x] = 0;\n    }\n  };\n  GlobalHistogramBinarizer.estimateBlackPoint = function (buckets) {\n    // Find the tallest peak in the histogram.\n    var numBuckets = buckets.length;\n    var maxBucketCount = 0;\n    var firstPeak = 0;\n    var firstPeakSize = 0;\n    for (var x = 0; x < numBuckets; x++) {\n      if (buckets[x] > firstPeakSize) {\n        firstPeak = x;\n        firstPeakSize = buckets[x];\n      }\n      if (buckets[x] > maxBucketCount) {\n        maxBucketCount = buckets[x];\n      }\n    }\n    // Find the second-tallest peak which is somewhat far from the tallest peak.\n    var secondPeak = 0;\n    var secondPeakScore = 0;\n    for (var x = 0; x < numBuckets; x++) {\n      var distanceToBiggest = x - firstPeak;\n      // Encourage more distant second peaks by multiplying by square of distance.\n      var score = buckets[x] * distanceToBiggest * distanceToBiggest;\n      if (score > secondPeakScore) {\n        secondPeak = x;\n        secondPeakScore = score;\n      }\n    }\n    // Make sure firstPeak corresponds to the black peak.\n    if (firstPeak > secondPeak) {\n      var temp = firstPeak;\n      firstPeak = secondPeak;\n      secondPeak = temp;\n    }\n    // If there is too little contrast in the image to pick a meaningful black point, throw rather\n    // than waste time trying to decode the image, and risk false positives.\n    if (secondPeak - firstPeak <= numBuckets / 16) {\n      throw new NotFoundException();\n    }\n    // Find a valley between them that is low and closer to the white peak.\n    var bestValley = secondPeak - 1;\n    var bestValleyScore = -1;\n    for (var x = secondPeak - 1; x > firstPeak; x--) {\n      var fromFirst = x - firstPeak;\n      var score = fromFirst * fromFirst * (secondPeak - x) * (maxBucketCount - buckets[x]);\n      if (score > bestValleyScore) {\n        bestValley = x;\n        bestValleyScore = score;\n      }\n    }\n    return bestValley << GlobalHistogramBinarizer.LUMINANCE_SHIFT;\n  };\n  GlobalHistogramBinarizer.LUMINANCE_BITS = 5;\n  GlobalHistogramBinarizer.LUMINANCE_SHIFT = 8 - GlobalHistogramBinarizer.LUMINANCE_BITS;\n  GlobalHistogramBinarizer.LUMINANCE_BUCKETS = 1 << GlobalHistogramBinarizer.LUMINANCE_BITS;\n  GlobalHistogramBinarizer.EMPTY = Uint8ClampedArray.from([0]);\n  return GlobalHistogramBinarizer;\n}(Binarizer);\nexport default GlobalHistogramBinarizer;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "Binarizer", "BitArray", "BitMatrix", "NotFoundException", "GlobalHistogramBinarizer", "_super", "source", "_this", "call", "luminances", "EMPTY", "buckets", "Int32Array", "LUMINANCE_BUCKETS", "getBlackRow", "y", "row", "getLuminanceSource", "width", "getWidth", "undefined", "getSize", "clear", "initArrays", "localLuminances", "getRow", "localBuckets", "x", "LUMINANCE_SHIFT", "blackPoint", "estimateBlackPoint", "set", "left", "center", "right", "getBlackMatrix", "height", "getHeight", "matrix", "Math", "floor", "localLuminances_1", "pixel", "getMatrix", "offset", "createBinarizer", "luminanceSize", "length", "Uint8ClampedArray", "numBuckets", "maxBucketCount", "firstPeak", "firstPeakSize", "secondPeak", "secondPeakScore", "distanceToBiggest", "score", "temp", "bestValley", "bestValleyScore", "fromFirst", "LUMINANCE_BITS", "from"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/GlobalHistogramBinarizer.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.common {*/\nimport Binarizer from '../Binarizer';\nimport BitArray from './BitArray';\nimport BitMatrix from './BitMatrix';\nimport NotFoundException from '../NotFoundException';\n/**\n * This Binarizer implementation uses the old ZXing global histogram approach. It is suitable\n * for low-end mobile devices which don't have enough CPU or memory to use a local thresholding\n * algorithm. However, because it picks a global black point, it cannot handle difficult shadows\n * and gradients.\n *\n * Faster mobile devices and all desktop applications should probably use HybridBinarizer instead.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n */\nvar GlobalHistogramBinarizer = /** @class */ (function (_super) {\n    __extends(GlobalHistogramBinarizer, _super);\n    function GlobalHistogramBinarizer(source) {\n        var _this = _super.call(this, source) || this;\n        _this.luminances = GlobalHistogramBinarizer.EMPTY;\n        _this.buckets = new Int32Array(GlobalHistogramBinarizer.LUMINANCE_BUCKETS);\n        return _this;\n    }\n    // Applies simple sharpening to the row data to improve performance of the 1D Readers.\n    /*@Override*/\n    GlobalHistogramBinarizer.prototype.getBlackRow = function (y /*int*/, row) {\n        var source = this.getLuminanceSource();\n        var width = source.getWidth();\n        if (row === undefined || row === null || row.getSize() < width) {\n            row = new BitArray(width);\n        }\n        else {\n            row.clear();\n        }\n        this.initArrays(width);\n        var localLuminances = source.getRow(y, this.luminances);\n        var localBuckets = this.buckets;\n        for (var x = 0; x < width; x++) {\n            localBuckets[(localLuminances[x] & 0xff) >> GlobalHistogramBinarizer.LUMINANCE_SHIFT]++;\n        }\n        var blackPoint = GlobalHistogramBinarizer.estimateBlackPoint(localBuckets);\n        if (width < 3) {\n            // Special case for very small images\n            for (var x = 0; x < width; x++) {\n                if ((localLuminances[x] & 0xff) < blackPoint) {\n                    row.set(x);\n                }\n            }\n        }\n        else {\n            var left = localLuminances[0] & 0xff;\n            var center = localLuminances[1] & 0xff;\n            for (var x = 1; x < width - 1; x++) {\n                var right = localLuminances[x + 1] & 0xff;\n                // A simple -1 4 -1 box filter with a weight of 2.\n                if (((center * 4) - left - right) / 2 < blackPoint) {\n                    row.set(x);\n                }\n                left = center;\n                center = right;\n            }\n        }\n        return row;\n    };\n    // Does not sharpen the data, as this call is intended to only be used by 2D Readers.\n    /*@Override*/\n    GlobalHistogramBinarizer.prototype.getBlackMatrix = function () {\n        var source = this.getLuminanceSource();\n        var width = source.getWidth();\n        var height = source.getHeight();\n        var matrix = new BitMatrix(width, height);\n        // Quickly calculates the histogram by sampling four rows from the image. This proved to be\n        // more robust on the blackbox tests than sampling a diagonal as we used to do.\n        this.initArrays(width);\n        var localBuckets = this.buckets;\n        for (var y = 1; y < 5; y++) {\n            var row = Math.floor((height * y) / 5);\n            var localLuminances_1 = source.getRow(row, this.luminances);\n            var right = Math.floor((width * 4) / 5);\n            for (var x = Math.floor(width / 5); x < right; x++) {\n                var pixel = localLuminances_1[x] & 0xff;\n                localBuckets[pixel >> GlobalHistogramBinarizer.LUMINANCE_SHIFT]++;\n            }\n        }\n        var blackPoint = GlobalHistogramBinarizer.estimateBlackPoint(localBuckets);\n        // We delay reading the entire image luminance until the black point estimation succeeds.\n        // Although we end up reading four rows twice, it is consistent with our motto of\n        // \"fail quickly\" which is necessary for continuous scanning.\n        var localLuminances = source.getMatrix();\n        for (var y = 0; y < height; y++) {\n            var offset = y * width;\n            for (var x = 0; x < width; x++) {\n                var pixel = localLuminances[offset + x] & 0xff;\n                if (pixel < blackPoint) {\n                    matrix.set(x, y);\n                }\n            }\n        }\n        return matrix;\n    };\n    /*@Override*/\n    GlobalHistogramBinarizer.prototype.createBinarizer = function (source) {\n        return new GlobalHistogramBinarizer(source);\n    };\n    GlobalHistogramBinarizer.prototype.initArrays = function (luminanceSize /*int*/) {\n        if (this.luminances.length < luminanceSize) {\n            this.luminances = new Uint8ClampedArray(luminanceSize);\n        }\n        var buckets = this.buckets;\n        for (var x = 0; x < GlobalHistogramBinarizer.LUMINANCE_BUCKETS; x++) {\n            buckets[x] = 0;\n        }\n    };\n    GlobalHistogramBinarizer.estimateBlackPoint = function (buckets) {\n        // Find the tallest peak in the histogram.\n        var numBuckets = buckets.length;\n        var maxBucketCount = 0;\n        var firstPeak = 0;\n        var firstPeakSize = 0;\n        for (var x = 0; x < numBuckets; x++) {\n            if (buckets[x] > firstPeakSize) {\n                firstPeak = x;\n                firstPeakSize = buckets[x];\n            }\n            if (buckets[x] > maxBucketCount) {\n                maxBucketCount = buckets[x];\n            }\n        }\n        // Find the second-tallest peak which is somewhat far from the tallest peak.\n        var secondPeak = 0;\n        var secondPeakScore = 0;\n        for (var x = 0; x < numBuckets; x++) {\n            var distanceToBiggest = x - firstPeak;\n            // Encourage more distant second peaks by multiplying by square of distance.\n            var score = buckets[x] * distanceToBiggest * distanceToBiggest;\n            if (score > secondPeakScore) {\n                secondPeak = x;\n                secondPeakScore = score;\n            }\n        }\n        // Make sure firstPeak corresponds to the black peak.\n        if (firstPeak > secondPeak) {\n            var temp = firstPeak;\n            firstPeak = secondPeak;\n            secondPeak = temp;\n        }\n        // If there is too little contrast in the image to pick a meaningful black point, throw rather\n        // than waste time trying to decode the image, and risk false positives.\n        if (secondPeak - firstPeak <= numBuckets / 16) {\n            throw new NotFoundException();\n        }\n        // Find a valley between them that is low and closer to the white peak.\n        var bestValley = secondPeak - 1;\n        var bestValleyScore = -1;\n        for (var x = secondPeak - 1; x > firstPeak; x--) {\n            var fromFirst = x - firstPeak;\n            var score = fromFirst * fromFirst * (secondPeak - x) * (maxBucketCount - buckets[x]);\n            if (score > bestValleyScore) {\n                bestValley = x;\n                bestValleyScore = score;\n            }\n        }\n        return bestValley << GlobalHistogramBinarizer.LUMINANCE_SHIFT;\n    };\n    GlobalHistogramBinarizer.LUMINANCE_BITS = 5;\n    GlobalHistogramBinarizer.LUMINANCE_SHIFT = 8 - GlobalHistogramBinarizer.LUMINANCE_BITS;\n    GlobalHistogramBinarizer.LUMINANCE_BUCKETS = 1 << GlobalHistogramBinarizer.LUMINANCE_BITS;\n    GlobalHistogramBinarizer.EMPTY = Uint8ClampedArray.from([0]);\n    return GlobalHistogramBinarizer;\n}(Binarizer));\nexport default GlobalHistogramBinarizer;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,SAAS,MAAM,cAAc;AACpC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,wBAAwB,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC5DnB,SAAS,CAACkB,wBAAwB,EAAEC,MAAM,CAAC;EAC3C,SAASD,wBAAwBA,CAACE,MAAM,EAAE;IACtC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,EAAEF,MAAM,CAAC,IAAI,IAAI;IAC7CC,KAAK,CAACE,UAAU,GAAGL,wBAAwB,CAACM,KAAK;IACjDH,KAAK,CAACI,OAAO,GAAG,IAAIC,UAAU,CAACR,wBAAwB,CAACS,iBAAiB,CAAC;IAC1E,OAAON,KAAK;EAChB;EACA;EACA;EACAH,wBAAwB,CAACN,SAAS,CAACgB,WAAW,GAAG,UAAUC,CAAC,CAAC,SAASC,GAAG,EAAE;IACvE,IAAIV,MAAM,GAAG,IAAI,CAACW,kBAAkB,CAAC,CAAC;IACtC,IAAIC,KAAK,GAAGZ,MAAM,CAACa,QAAQ,CAAC,CAAC;IAC7B,IAAIH,GAAG,KAAKI,SAAS,IAAIJ,GAAG,KAAK,IAAI,IAAIA,GAAG,CAACK,OAAO,CAAC,CAAC,GAAGH,KAAK,EAAE;MAC5DF,GAAG,GAAG,IAAIf,QAAQ,CAACiB,KAAK,CAAC;IAC7B,CAAC,MACI;MACDF,GAAG,CAACM,KAAK,CAAC,CAAC;IACf;IACA,IAAI,CAACC,UAAU,CAACL,KAAK,CAAC;IACtB,IAAIM,eAAe,GAAGlB,MAAM,CAACmB,MAAM,CAACV,CAAC,EAAE,IAAI,CAACN,UAAU,CAAC;IACvD,IAAIiB,YAAY,GAAG,IAAI,CAACf,OAAO;IAC/B,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,EAAES,CAAC,EAAE,EAAE;MAC5BD,YAAY,CAAC,CAACF,eAAe,CAACG,CAAC,CAAC,GAAG,IAAI,KAAKvB,wBAAwB,CAACwB,eAAe,CAAC,EAAE;IAC3F;IACA,IAAIC,UAAU,GAAGzB,wBAAwB,CAAC0B,kBAAkB,CAACJ,YAAY,CAAC;IAC1E,IAAIR,KAAK,GAAG,CAAC,EAAE;MACX;MACA,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,EAAES,CAAC,EAAE,EAAE;QAC5B,IAAI,CAACH,eAAe,CAACG,CAAC,CAAC,GAAG,IAAI,IAAIE,UAAU,EAAE;UAC1Cb,GAAG,CAACe,GAAG,CAACJ,CAAC,CAAC;QACd;MACJ;IACJ,CAAC,MACI;MACD,IAAIK,IAAI,GAAGR,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI;MACpC,IAAIS,MAAM,GAAGT,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI;MACtC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,GAAG,CAAC,EAAES,CAAC,EAAE,EAAE;QAChC,IAAIO,KAAK,GAAGV,eAAe,CAACG,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;QACzC;QACA,IAAI,CAAEM,MAAM,GAAG,CAAC,GAAID,IAAI,GAAGE,KAAK,IAAI,CAAC,GAAGL,UAAU,EAAE;UAChDb,GAAG,CAACe,GAAG,CAACJ,CAAC,CAAC;QACd;QACAK,IAAI,GAAGC,MAAM;QACbA,MAAM,GAAGC,KAAK;MAClB;IACJ;IACA,OAAOlB,GAAG;EACd,CAAC;EACD;EACA;EACAZ,wBAAwB,CAACN,SAAS,CAACqC,cAAc,GAAG,YAAY;IAC5D,IAAI7B,MAAM,GAAG,IAAI,CAACW,kBAAkB,CAAC,CAAC;IACtC,IAAIC,KAAK,GAAGZ,MAAM,CAACa,QAAQ,CAAC,CAAC;IAC7B,IAAIiB,MAAM,GAAG9B,MAAM,CAAC+B,SAAS,CAAC,CAAC;IAC/B,IAAIC,MAAM,GAAG,IAAIpC,SAAS,CAACgB,KAAK,EAAEkB,MAAM,CAAC;IACzC;IACA;IACA,IAAI,CAACb,UAAU,CAACL,KAAK,CAAC;IACtB,IAAIQ,YAAY,GAAG,IAAI,CAACf,OAAO;IAC/B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAIC,GAAG,GAAGuB,IAAI,CAACC,KAAK,CAAEJ,MAAM,GAAGrB,CAAC,GAAI,CAAC,CAAC;MACtC,IAAI0B,iBAAiB,GAAGnC,MAAM,CAACmB,MAAM,CAACT,GAAG,EAAE,IAAI,CAACP,UAAU,CAAC;MAC3D,IAAIyB,KAAK,GAAGK,IAAI,CAACC,KAAK,CAAEtB,KAAK,GAAG,CAAC,GAAI,CAAC,CAAC;MACvC,KAAK,IAAIS,CAAC,GAAGY,IAAI,CAACC,KAAK,CAACtB,KAAK,GAAG,CAAC,CAAC,EAAES,CAAC,GAAGO,KAAK,EAAEP,CAAC,EAAE,EAAE;QAChD,IAAIe,KAAK,GAAGD,iBAAiB,CAACd,CAAC,CAAC,GAAG,IAAI;QACvCD,YAAY,CAACgB,KAAK,IAAItC,wBAAwB,CAACwB,eAAe,CAAC,EAAE;MACrE;IACJ;IACA,IAAIC,UAAU,GAAGzB,wBAAwB,CAAC0B,kBAAkB,CAACJ,YAAY,CAAC;IAC1E;IACA;IACA;IACA,IAAIF,eAAe,GAAGlB,MAAM,CAACqC,SAAS,CAAC,CAAC;IACxC,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,MAAM,EAAErB,CAAC,EAAE,EAAE;MAC7B,IAAI6B,MAAM,GAAG7B,CAAC,GAAGG,KAAK;MACtB,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,KAAK,EAAES,CAAC,EAAE,EAAE;QAC5B,IAAIe,KAAK,GAAGlB,eAAe,CAACoB,MAAM,GAAGjB,CAAC,CAAC,GAAG,IAAI;QAC9C,IAAIe,KAAK,GAAGb,UAAU,EAAE;UACpBS,MAAM,CAACP,GAAG,CAACJ,CAAC,EAAEZ,CAAC,CAAC;QACpB;MACJ;IACJ;IACA,OAAOuB,MAAM;EACjB,CAAC;EACD;EACAlC,wBAAwB,CAACN,SAAS,CAAC+C,eAAe,GAAG,UAAUvC,MAAM,EAAE;IACnE,OAAO,IAAIF,wBAAwB,CAACE,MAAM,CAAC;EAC/C,CAAC;EACDF,wBAAwB,CAACN,SAAS,CAACyB,UAAU,GAAG,UAAUuB,aAAa,CAAC,SAAS;IAC7E,IAAI,IAAI,CAACrC,UAAU,CAACsC,MAAM,GAAGD,aAAa,EAAE;MACxC,IAAI,CAACrC,UAAU,GAAG,IAAIuC,iBAAiB,CAACF,aAAa,CAAC;IAC1D;IACA,IAAInC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,wBAAwB,CAACS,iBAAiB,EAAEc,CAAC,EAAE,EAAE;MACjEhB,OAAO,CAACgB,CAAC,CAAC,GAAG,CAAC;IAClB;EACJ,CAAC;EACDvB,wBAAwB,CAAC0B,kBAAkB,GAAG,UAAUnB,OAAO,EAAE;IAC7D;IACA,IAAIsC,UAAU,GAAGtC,OAAO,CAACoC,MAAM;IAC/B,IAAIG,cAAc,GAAG,CAAC;IACtB,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,aAAa,GAAG,CAAC;IACrB,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,UAAU,EAAEtB,CAAC,EAAE,EAAE;MACjC,IAAIhB,OAAO,CAACgB,CAAC,CAAC,GAAGyB,aAAa,EAAE;QAC5BD,SAAS,GAAGxB,CAAC;QACbyB,aAAa,GAAGzC,OAAO,CAACgB,CAAC,CAAC;MAC9B;MACA,IAAIhB,OAAO,CAACgB,CAAC,CAAC,GAAGuB,cAAc,EAAE;QAC7BA,cAAc,GAAGvC,OAAO,CAACgB,CAAC,CAAC;MAC/B;IACJ;IACA;IACA,IAAI0B,UAAU,GAAG,CAAC;IAClB,IAAIC,eAAe,GAAG,CAAC;IACvB,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,UAAU,EAAEtB,CAAC,EAAE,EAAE;MACjC,IAAI4B,iBAAiB,GAAG5B,CAAC,GAAGwB,SAAS;MACrC;MACA,IAAIK,KAAK,GAAG7C,OAAO,CAACgB,CAAC,CAAC,GAAG4B,iBAAiB,GAAGA,iBAAiB;MAC9D,IAAIC,KAAK,GAAGF,eAAe,EAAE;QACzBD,UAAU,GAAG1B,CAAC;QACd2B,eAAe,GAAGE,KAAK;MAC3B;IACJ;IACA;IACA,IAAIL,SAAS,GAAGE,UAAU,EAAE;MACxB,IAAII,IAAI,GAAGN,SAAS;MACpBA,SAAS,GAAGE,UAAU;MACtBA,UAAU,GAAGI,IAAI;IACrB;IACA;IACA;IACA,IAAIJ,UAAU,GAAGF,SAAS,IAAIF,UAAU,GAAG,EAAE,EAAE;MAC3C,MAAM,IAAI9C,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA,IAAIuD,UAAU,GAAGL,UAAU,GAAG,CAAC;IAC/B,IAAIM,eAAe,GAAG,CAAC,CAAC;IACxB,KAAK,IAAIhC,CAAC,GAAG0B,UAAU,GAAG,CAAC,EAAE1B,CAAC,GAAGwB,SAAS,EAAExB,CAAC,EAAE,EAAE;MAC7C,IAAIiC,SAAS,GAAGjC,CAAC,GAAGwB,SAAS;MAC7B,IAAIK,KAAK,GAAGI,SAAS,GAAGA,SAAS,IAAIP,UAAU,GAAG1B,CAAC,CAAC,IAAIuB,cAAc,GAAGvC,OAAO,CAACgB,CAAC,CAAC,CAAC;MACpF,IAAI6B,KAAK,GAAGG,eAAe,EAAE;QACzBD,UAAU,GAAG/B,CAAC;QACdgC,eAAe,GAAGH,KAAK;MAC3B;IACJ;IACA,OAAOE,UAAU,IAAItD,wBAAwB,CAACwB,eAAe;EACjE,CAAC;EACDxB,wBAAwB,CAACyD,cAAc,GAAG,CAAC;EAC3CzD,wBAAwB,CAACwB,eAAe,GAAG,CAAC,GAAGxB,wBAAwB,CAACyD,cAAc;EACtFzD,wBAAwB,CAACS,iBAAiB,GAAG,CAAC,IAAIT,wBAAwB,CAACyD,cAAc;EACzFzD,wBAAwB,CAACM,KAAK,GAAGsC,iBAAiB,CAACc,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5D,OAAO1D,wBAAwB;AACnC,CAAC,CAACJ,SAAS,CAAE;AACb,eAAeI,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}