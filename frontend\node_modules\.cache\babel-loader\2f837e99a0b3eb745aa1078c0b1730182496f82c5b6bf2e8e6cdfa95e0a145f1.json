{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.Arrays;*/\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport Arrays from '../util/Arrays';\nimport Integer from '../util/Integer';\nimport System from '../util/System';\n/**\n * <p>A simple, fast array of bits, represented compactly by an array of ints internally.</p>\n *\n * <AUTHOR>\n */\nvar BitArray /*implements Cloneable*/ = /** @class */function () {\n  // public constructor() {\n  //   this.size = 0\n  //   this.bits = new Int32Array(1)\n  // }\n  // public constructor(size?: number /*int*/) {\n  //   if (undefined === size) {\n  //     this.size = 0\n  //   } else {\n  //     this.size = size\n  //   }\n  //   this.bits = this.makeArray(size)\n  // }\n  // For testing only\n  function BitArray(size /*int*/, bits) {\n    if (undefined === size) {\n      this.size = 0;\n      this.bits = new Int32Array(1);\n    } else {\n      this.size = size;\n      if (undefined === bits || null === bits) {\n        this.bits = BitArray.makeArray(size);\n      } else {\n        this.bits = bits;\n      }\n    }\n  }\n  BitArray.prototype.getSize = function () {\n    return this.size;\n  };\n  BitArray.prototype.getSizeInBytes = function () {\n    return Math.floor((this.size + 7) / 8);\n  };\n  BitArray.prototype.ensureCapacity = function (size /*int*/) {\n    if (size > this.bits.length * 32) {\n      var newBits = BitArray.makeArray(size);\n      System.arraycopy(this.bits, 0, newBits, 0, this.bits.length);\n      this.bits = newBits;\n    }\n  };\n  /**\n   * @param i bit to get\n   * @return true iff bit i is set\n   */\n  BitArray.prototype.get = function (i /*int*/) {\n    return (this.bits[Math.floor(i / 32)] & 1 << (i & 0x1F)) !== 0;\n  };\n  /**\n   * Sets bit i.\n   *\n   * @param i bit to set\n   */\n  BitArray.prototype.set = function (i /*int*/) {\n    this.bits[Math.floor(i / 32)] |= 1 << (i & 0x1F);\n  };\n  /**\n   * Flips bit i.\n   *\n   * @param i bit to set\n   */\n  BitArray.prototype.flip = function (i /*int*/) {\n    this.bits[Math.floor(i / 32)] ^= 1 << (i & 0x1F);\n  };\n  /**\n   * @param from first bit to check\n   * @return index of first bit that is set, starting from the given index, or size if none are set\n   *  at or beyond this given index\n   * @see #getNextUnset(int)\n   */\n  BitArray.prototype.getNextSet = function (from /*int*/) {\n    var size = this.size;\n    if (from >= size) {\n      return size;\n    }\n    var bits = this.bits;\n    var bitsOffset = Math.floor(from / 32);\n    var currentBits = bits[bitsOffset];\n    // mask off lesser bits first\n    currentBits &= ~((1 << (from & 0x1F)) - 1);\n    var length = bits.length;\n    while (currentBits === 0) {\n      if (++bitsOffset === length) {\n        return size;\n      }\n      currentBits = bits[bitsOffset];\n    }\n    var result = bitsOffset * 32 + Integer.numberOfTrailingZeros(currentBits);\n    return result > size ? size : result;\n  };\n  /**\n   * @param from index to start looking for unset bit\n   * @return index of next unset bit, or {@code size} if none are unset until the end\n   * @see #getNextSet(int)\n   */\n  BitArray.prototype.getNextUnset = function (from /*int*/) {\n    var size = this.size;\n    if (from >= size) {\n      return size;\n    }\n    var bits = this.bits;\n    var bitsOffset = Math.floor(from / 32);\n    var currentBits = ~bits[bitsOffset];\n    // mask off lesser bits first\n    currentBits &= ~((1 << (from & 0x1F)) - 1);\n    var length = bits.length;\n    while (currentBits === 0) {\n      if (++bitsOffset === length) {\n        return size;\n      }\n      currentBits = ~bits[bitsOffset];\n    }\n    var result = bitsOffset * 32 + Integer.numberOfTrailingZeros(currentBits);\n    return result > size ? size : result;\n  };\n  /**\n   * Sets a block of 32 bits, starting at bit i.\n   *\n   * @param i first bit to set\n   * @param newBits the new value of the next 32 bits. Note again that the least-significant bit\n   * corresponds to bit i, the next-least-significant to i+1, and so on.\n   */\n  BitArray.prototype.setBulk = function (i /*int*/, newBits /*int*/) {\n    this.bits[Math.floor(i / 32)] = newBits;\n  };\n  /**\n   * Sets a range of bits.\n   *\n   * @param start start of range, inclusive.\n   * @param end end of range, exclusive\n   */\n  BitArray.prototype.setRange = function (start /*int*/, end /*int*/) {\n    if (end < start || start < 0 || end > this.size) {\n      throw new IllegalArgumentException();\n    }\n    if (end === start) {\n      return;\n    }\n    end--; // will be easier to treat this as the last actually set bit -- inclusive\n    var firstInt = Math.floor(start / 32);\n    var lastInt = Math.floor(end / 32);\n    var bits = this.bits;\n    for (var i = firstInt; i <= lastInt; i++) {\n      var firstBit = i > firstInt ? 0 : start & 0x1F;\n      var lastBit = i < lastInt ? 31 : end & 0x1F;\n      // Ones from firstBit to lastBit, inclusive\n      var mask = (2 << lastBit) - (1 << firstBit);\n      bits[i] |= mask;\n    }\n  };\n  /**\n   * Clears all bits (sets to false).\n   */\n  BitArray.prototype.clear = function () {\n    var max = this.bits.length;\n    var bits = this.bits;\n    for (var i = 0; i < max; i++) {\n      bits[i] = 0;\n    }\n  };\n  /**\n   * Efficient method to check if a range of bits is set, or not set.\n   *\n   * @param start start of range, inclusive.\n   * @param end end of range, exclusive\n   * @param value if true, checks that bits in range are set, otherwise checks that they are not set\n   * @return true iff all bits are set or not set in range, according to value argument\n   * @throws IllegalArgumentException if end is less than start or the range is not contained in the array\n   */\n  BitArray.prototype.isRange = function (start /*int*/, end /*int*/, value) {\n    if (end < start || start < 0 || end > this.size) {\n      throw new IllegalArgumentException();\n    }\n    if (end === start) {\n      return true; // empty range matches\n    }\n    end--; // will be easier to treat this as the last actually set bit -- inclusive\n    var firstInt = Math.floor(start / 32);\n    var lastInt = Math.floor(end / 32);\n    var bits = this.bits;\n    for (var i = firstInt; i <= lastInt; i++) {\n      var firstBit = i > firstInt ? 0 : start & 0x1F;\n      var lastBit = i < lastInt ? 31 : end & 0x1F;\n      // Ones from firstBit to lastBit, inclusive\n      var mask = (2 << lastBit) - (1 << firstBit) & 0xFFFFFFFF;\n      // TYPESCRIPTPORT: & 0xFFFFFFFF added to discard anything after 32 bits, as ES has 53 bits\n      // Return false if we're looking for 1s and the masked bits[i] isn't all 1s (is: that,\n      // equals the mask, or we're looking for 0s and the masked portion is not all 0s\n      if ((bits[i] & mask) !== (value ? mask : 0)) {\n        return false;\n      }\n    }\n    return true;\n  };\n  BitArray.prototype.appendBit = function (bit) {\n    this.ensureCapacity(this.size + 1);\n    if (bit) {\n      this.bits[Math.floor(this.size / 32)] |= 1 << (this.size & 0x1F);\n    }\n    this.size++;\n  };\n  /**\n   * Appends the least-significant bits, from value, in order from most-significant to\n   * least-significant. For example, appending 6 bits from 0x000001E will append the bits\n   * 0, 1, 1, 1, 1, 0 in that order.\n   *\n   * @param value {@code int} containing bits to append\n   * @param numBits bits from value to append\n   */\n  BitArray.prototype.appendBits = function (value /*int*/, numBits /*int*/) {\n    if (numBits < 0 || numBits > 32) {\n      throw new IllegalArgumentException('Num bits must be between 0 and 32');\n    }\n    this.ensureCapacity(this.size + numBits);\n    // const appendBit = this.appendBit;\n    for (var numBitsLeft = numBits; numBitsLeft > 0; numBitsLeft--) {\n      this.appendBit((value >> numBitsLeft - 1 & 0x01) === 1);\n    }\n  };\n  BitArray.prototype.appendBitArray = function (other) {\n    var otherSize = other.size;\n    this.ensureCapacity(this.size + otherSize);\n    // const appendBit = this.appendBit;\n    for (var i = 0; i < otherSize; i++) {\n      this.appendBit(other.get(i));\n    }\n  };\n  BitArray.prototype.xor = function (other) {\n    if (this.size !== other.size) {\n      throw new IllegalArgumentException('Sizes don\\'t match');\n    }\n    var bits = this.bits;\n    for (var i = 0, length_1 = bits.length; i < length_1; i++) {\n      // The last int could be incomplete (i.e. not have 32 bits in\n      // it) but there is no problem since 0 XOR 0 == 0.\n      bits[i] ^= other.bits[i];\n    }\n  };\n  /**\n   *\n   * @param bitOffset first bit to start writing\n   * @param array array to write into. Bytes are written most-significant byte first. This is the opposite\n   *  of the internal representation, which is exposed by {@link #getBitArray()}\n   * @param offset position in array to start writing\n   * @param numBytes how many bytes to write\n   */\n  BitArray.prototype.toBytes = function (bitOffset /*int*/, array, offset /*int*/, numBytes /*int*/) {\n    for (var i = 0; i < numBytes; i++) {\n      var theByte = 0;\n      for (var j = 0; j < 8; j++) {\n        if (this.get(bitOffset)) {\n          theByte |= 1 << 7 - j;\n        }\n        bitOffset++;\n      }\n      array[offset + i] = /*(byte)*/theByte;\n    }\n  };\n  /**\n   * @return underlying array of ints. The first element holds the first 32 bits, and the least\n   *         significant bit is bit 0.\n   */\n  BitArray.prototype.getBitArray = function () {\n    return this.bits;\n  };\n  /**\n   * Reverses all bits in the array.\n   */\n  BitArray.prototype.reverse = function () {\n    var newBits = new Int32Array(this.bits.length);\n    // reverse all int's first\n    var len = Math.floor((this.size - 1) / 32);\n    var oldBitsLen = len + 1;\n    var bits = this.bits;\n    for (var i = 0; i < oldBitsLen; i++) {\n      var x = bits[i];\n      x = x >> 1 & 0x55555555 | (x & 0x55555555) << 1;\n      x = x >> 2 & 0x33333333 | (x & 0x33333333) << 2;\n      x = x >> 4 & 0x0f0f0f0f | (x & 0x0f0f0f0f) << 4;\n      x = x >> 8 & 0x00ff00ff | (x & 0x00ff00ff) << 8;\n      x = x >> 16 & 0x0000ffff | (x & 0x0000ffff) << 16;\n      newBits[len - i] = /*(int)*/x;\n    }\n    // now correct the int's if the bit size isn't a multiple of 32\n    if (this.size !== oldBitsLen * 32) {\n      var leftOffset = oldBitsLen * 32 - this.size;\n      var currentInt = newBits[0] >>> leftOffset;\n      for (var i = 1; i < oldBitsLen; i++) {\n        var nextInt = newBits[i];\n        currentInt |= nextInt << 32 - leftOffset;\n        newBits[i - 1] = currentInt;\n        currentInt = nextInt >>> leftOffset;\n      }\n      newBits[oldBitsLen - 1] = currentInt;\n    }\n    this.bits = newBits;\n  };\n  BitArray.makeArray = function (size /*int*/) {\n    return new Int32Array(Math.floor((size + 31) / 32));\n  };\n  /*@Override*/\n  BitArray.prototype.equals = function (o) {\n    if (!(o instanceof BitArray)) {\n      return false;\n    }\n    var other = o;\n    return this.size === other.size && Arrays.equals(this.bits, other.bits);\n  };\n  /*@Override*/\n  BitArray.prototype.hashCode = function () {\n    return 31 * this.size + Arrays.hashCode(this.bits);\n  };\n  /*@Override*/\n  BitArray.prototype.toString = function () {\n    var result = '';\n    for (var i = 0, size = this.size; i < size; i++) {\n      if ((i & 0x07) === 0) {\n        result += ' ';\n      }\n      result += this.get(i) ? 'X' : '.';\n    }\n    return result;\n  };\n  /*@Override*/\n  BitArray.prototype.clone = function () {\n    return new BitArray(this.size, this.bits.slice());\n  };\n  /**\n   * converts to boolean array.\n   */\n  BitArray.prototype.toArray = function () {\n    var result = [];\n    for (var i = 0, size = this.size; i < size; i++) {\n      result.push(this.get(i));\n    }\n    return result;\n  };\n  return BitArray;\n}();\nexport default BitArray;", "map": {"version": 3, "names": ["IllegalArgumentException", "<PERSON><PERSON><PERSON>", "Integer", "System", "BitArray", "size", "bits", "undefined", "Int32Array", "makeArray", "prototype", "getSize", "getSizeInBytes", "Math", "floor", "ensureCapacity", "length", "newBits", "arraycopy", "get", "i", "set", "flip", "getNextSet", "from", "bitsOffset", "currentBits", "result", "numberOfTrailingZeros", "getNextUnset", "setBulk", "setRang<PERSON>", "start", "end", "firstInt", "lastInt", "firstBit", "lastBit", "mask", "clear", "max", "isRange", "value", "appendBit", "bit", "appendBits", "numBits", "numBitsLeft", "appendBitArray", "other", "otherSize", "xor", "length_1", "toBytes", "bitOffset", "array", "offset", "numBytes", "theByte", "j", "getBitArray", "reverse", "len", "oldBitsLen", "x", "leftOffset", "currentInt", "nextInt", "equals", "o", "hashCode", "toString", "clone", "slice", "toArray", "push"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/BitArray.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.util.Arrays;*/\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport Arrays from '../util/Arrays';\nimport Integer from '../util/Integer';\nimport System from '../util/System';\n/**\n * <p>A simple, fast array of bits, represented compactly by an array of ints internally.</p>\n *\n * <AUTHOR>\n */\nvar BitArray /*implements Cloneable*/ = /** @class */ (function () {\n    // public constructor() {\n    //   this.size = 0\n    //   this.bits = new Int32Array(1)\n    // }\n    // public constructor(size?: number /*int*/) {\n    //   if (undefined === size) {\n    //     this.size = 0\n    //   } else {\n    //     this.size = size\n    //   }\n    //   this.bits = this.makeArray(size)\n    // }\n    // For testing only\n    function BitArray(size /*int*/, bits) {\n        if (undefined === size) {\n            this.size = 0;\n            this.bits = new Int32Array(1);\n        }\n        else {\n            this.size = size;\n            if (undefined === bits || null === bits) {\n                this.bits = BitArray.makeArray(size);\n            }\n            else {\n                this.bits = bits;\n            }\n        }\n    }\n    BitArray.prototype.getSize = function () {\n        return this.size;\n    };\n    BitArray.prototype.getSizeInBytes = function () {\n        return Math.floor((this.size + 7) / 8);\n    };\n    BitArray.prototype.ensureCapacity = function (size /*int*/) {\n        if (size > this.bits.length * 32) {\n            var newBits = BitArray.makeArray(size);\n            System.arraycopy(this.bits, 0, newBits, 0, this.bits.length);\n            this.bits = newBits;\n        }\n    };\n    /**\n     * @param i bit to get\n     * @return true iff bit i is set\n     */\n    BitArray.prototype.get = function (i /*int*/) {\n        return (this.bits[Math.floor(i / 32)] & (1 << (i & 0x1F))) !== 0;\n    };\n    /**\n     * Sets bit i.\n     *\n     * @param i bit to set\n     */\n    BitArray.prototype.set = function (i /*int*/) {\n        this.bits[Math.floor(i / 32)] |= 1 << (i & 0x1F);\n    };\n    /**\n     * Flips bit i.\n     *\n     * @param i bit to set\n     */\n    BitArray.prototype.flip = function (i /*int*/) {\n        this.bits[Math.floor(i / 32)] ^= 1 << (i & 0x1F);\n    };\n    /**\n     * @param from first bit to check\n     * @return index of first bit that is set, starting from the given index, or size if none are set\n     *  at or beyond this given index\n     * @see #getNextUnset(int)\n     */\n    BitArray.prototype.getNextSet = function (from /*int*/) {\n        var size = this.size;\n        if (from >= size) {\n            return size;\n        }\n        var bits = this.bits;\n        var bitsOffset = Math.floor(from / 32);\n        var currentBits = bits[bitsOffset];\n        // mask off lesser bits first\n        currentBits &= ~((1 << (from & 0x1F)) - 1);\n        var length = bits.length;\n        while (currentBits === 0) {\n            if (++bitsOffset === length) {\n                return size;\n            }\n            currentBits = bits[bitsOffset];\n        }\n        var result = (bitsOffset * 32) + Integer.numberOfTrailingZeros(currentBits);\n        return result > size ? size : result;\n    };\n    /**\n     * @param from index to start looking for unset bit\n     * @return index of next unset bit, or {@code size} if none are unset until the end\n     * @see #getNextSet(int)\n     */\n    BitArray.prototype.getNextUnset = function (from /*int*/) {\n        var size = this.size;\n        if (from >= size) {\n            return size;\n        }\n        var bits = this.bits;\n        var bitsOffset = Math.floor(from / 32);\n        var currentBits = ~bits[bitsOffset];\n        // mask off lesser bits first\n        currentBits &= ~((1 << (from & 0x1F)) - 1);\n        var length = bits.length;\n        while (currentBits === 0) {\n            if (++bitsOffset === length) {\n                return size;\n            }\n            currentBits = ~bits[bitsOffset];\n        }\n        var result = (bitsOffset * 32) + Integer.numberOfTrailingZeros(currentBits);\n        return result > size ? size : result;\n    };\n    /**\n     * Sets a block of 32 bits, starting at bit i.\n     *\n     * @param i first bit to set\n     * @param newBits the new value of the next 32 bits. Note again that the least-significant bit\n     * corresponds to bit i, the next-least-significant to i+1, and so on.\n     */\n    BitArray.prototype.setBulk = function (i /*int*/, newBits /*int*/) {\n        this.bits[Math.floor(i / 32)] = newBits;\n    };\n    /**\n     * Sets a range of bits.\n     *\n     * @param start start of range, inclusive.\n     * @param end end of range, exclusive\n     */\n    BitArray.prototype.setRange = function (start /*int*/, end /*int*/) {\n        if (end < start || start < 0 || end > this.size) {\n            throw new IllegalArgumentException();\n        }\n        if (end === start) {\n            return;\n        }\n        end--; // will be easier to treat this as the last actually set bit -- inclusive\n        var firstInt = Math.floor(start / 32);\n        var lastInt = Math.floor(end / 32);\n        var bits = this.bits;\n        for (var i = firstInt; i <= lastInt; i++) {\n            var firstBit = i > firstInt ? 0 : start & 0x1F;\n            var lastBit = i < lastInt ? 31 : end & 0x1F;\n            // Ones from firstBit to lastBit, inclusive\n            var mask = (2 << lastBit) - (1 << firstBit);\n            bits[i] |= mask;\n        }\n    };\n    /**\n     * Clears all bits (sets to false).\n     */\n    BitArray.prototype.clear = function () {\n        var max = this.bits.length;\n        var bits = this.bits;\n        for (var i = 0; i < max; i++) {\n            bits[i] = 0;\n        }\n    };\n    /**\n     * Efficient method to check if a range of bits is set, or not set.\n     *\n     * @param start start of range, inclusive.\n     * @param end end of range, exclusive\n     * @param value if true, checks that bits in range are set, otherwise checks that they are not set\n     * @return true iff all bits are set or not set in range, according to value argument\n     * @throws IllegalArgumentException if end is less than start or the range is not contained in the array\n     */\n    BitArray.prototype.isRange = function (start /*int*/, end /*int*/, value) {\n        if (end < start || start < 0 || end > this.size) {\n            throw new IllegalArgumentException();\n        }\n        if (end === start) {\n            return true; // empty range matches\n        }\n        end--; // will be easier to treat this as the last actually set bit -- inclusive\n        var firstInt = Math.floor(start / 32);\n        var lastInt = Math.floor(end / 32);\n        var bits = this.bits;\n        for (var i = firstInt; i <= lastInt; i++) {\n            var firstBit = i > firstInt ? 0 : start & 0x1F;\n            var lastBit = i < lastInt ? 31 : end & 0x1F;\n            // Ones from firstBit to lastBit, inclusive\n            var mask = (2 << lastBit) - (1 << firstBit) & 0xFFFFFFFF;\n            // TYPESCRIPTPORT: & 0xFFFFFFFF added to discard anything after 32 bits, as ES has 53 bits\n            // Return false if we're looking for 1s and the masked bits[i] isn't all 1s (is: that,\n            // equals the mask, or we're looking for 0s and the masked portion is not all 0s\n            if ((bits[i] & mask) !== (value ? mask : 0)) {\n                return false;\n            }\n        }\n        return true;\n    };\n    BitArray.prototype.appendBit = function (bit) {\n        this.ensureCapacity(this.size + 1);\n        if (bit) {\n            this.bits[Math.floor(this.size / 32)] |= 1 << (this.size & 0x1F);\n        }\n        this.size++;\n    };\n    /**\n     * Appends the least-significant bits, from value, in order from most-significant to\n     * least-significant. For example, appending 6 bits from 0x000001E will append the bits\n     * 0, 1, 1, 1, 1, 0 in that order.\n     *\n     * @param value {@code int} containing bits to append\n     * @param numBits bits from value to append\n     */\n    BitArray.prototype.appendBits = function (value /*int*/, numBits /*int*/) {\n        if (numBits < 0 || numBits > 32) {\n            throw new IllegalArgumentException('Num bits must be between 0 and 32');\n        }\n        this.ensureCapacity(this.size + numBits);\n        // const appendBit = this.appendBit;\n        for (var numBitsLeft = numBits; numBitsLeft > 0; numBitsLeft--) {\n            this.appendBit(((value >> (numBitsLeft - 1)) & 0x01) === 1);\n        }\n    };\n    BitArray.prototype.appendBitArray = function (other) {\n        var otherSize = other.size;\n        this.ensureCapacity(this.size + otherSize);\n        // const appendBit = this.appendBit;\n        for (var i = 0; i < otherSize; i++) {\n            this.appendBit(other.get(i));\n        }\n    };\n    BitArray.prototype.xor = function (other) {\n        if (this.size !== other.size) {\n            throw new IllegalArgumentException('Sizes don\\'t match');\n        }\n        var bits = this.bits;\n        for (var i = 0, length_1 = bits.length; i < length_1; i++) {\n            // The last int could be incomplete (i.e. not have 32 bits in\n            // it) but there is no problem since 0 XOR 0 == 0.\n            bits[i] ^= other.bits[i];\n        }\n    };\n    /**\n     *\n     * @param bitOffset first bit to start writing\n     * @param array array to write into. Bytes are written most-significant byte first. This is the opposite\n     *  of the internal representation, which is exposed by {@link #getBitArray()}\n     * @param offset position in array to start writing\n     * @param numBytes how many bytes to write\n     */\n    BitArray.prototype.toBytes = function (bitOffset /*int*/, array, offset /*int*/, numBytes /*int*/) {\n        for (var i = 0; i < numBytes; i++) {\n            var theByte = 0;\n            for (var j = 0; j < 8; j++) {\n                if (this.get(bitOffset)) {\n                    theByte |= 1 << (7 - j);\n                }\n                bitOffset++;\n            }\n            array[offset + i] = /*(byte)*/ theByte;\n        }\n    };\n    /**\n     * @return underlying array of ints. The first element holds the first 32 bits, and the least\n     *         significant bit is bit 0.\n     */\n    BitArray.prototype.getBitArray = function () {\n        return this.bits;\n    };\n    /**\n     * Reverses all bits in the array.\n     */\n    BitArray.prototype.reverse = function () {\n        var newBits = new Int32Array(this.bits.length);\n        // reverse all int's first\n        var len = Math.floor((this.size - 1) / 32);\n        var oldBitsLen = len + 1;\n        var bits = this.bits;\n        for (var i = 0; i < oldBitsLen; i++) {\n            var x = bits[i];\n            x = ((x >> 1) & 0x55555555) | ((x & 0x55555555) << 1);\n            x = ((x >> 2) & 0x33333333) | ((x & 0x33333333) << 2);\n            x = ((x >> 4) & 0x0f0f0f0f) | ((x & 0x0f0f0f0f) << 4);\n            x = ((x >> 8) & 0x00ff00ff) | ((x & 0x00ff00ff) << 8);\n            x = ((x >> 16) & 0x0000ffff) | ((x & 0x0000ffff) << 16);\n            newBits[len - i] = /*(int)*/ x;\n        }\n        // now correct the int's if the bit size isn't a multiple of 32\n        if (this.size !== oldBitsLen * 32) {\n            var leftOffset = oldBitsLen * 32 - this.size;\n            var currentInt = newBits[0] >>> leftOffset;\n            for (var i = 1; i < oldBitsLen; i++) {\n                var nextInt = newBits[i];\n                currentInt |= nextInt << (32 - leftOffset);\n                newBits[i - 1] = currentInt;\n                currentInt = nextInt >>> leftOffset;\n            }\n            newBits[oldBitsLen - 1] = currentInt;\n        }\n        this.bits = newBits;\n    };\n    BitArray.makeArray = function (size /*int*/) {\n        return new Int32Array(Math.floor((size + 31) / 32));\n    };\n    /*@Override*/\n    BitArray.prototype.equals = function (o) {\n        if (!(o instanceof BitArray)) {\n            return false;\n        }\n        var other = o;\n        return this.size === other.size && Arrays.equals(this.bits, other.bits);\n    };\n    /*@Override*/\n    BitArray.prototype.hashCode = function () {\n        return 31 * this.size + Arrays.hashCode(this.bits);\n    };\n    /*@Override*/\n    BitArray.prototype.toString = function () {\n        var result = '';\n        for (var i = 0, size = this.size; i < size; i++) {\n            if ((i & 0x07) === 0) {\n                result += ' ';\n            }\n            result += this.get(i) ? 'X' : '.';\n        }\n        return result;\n    };\n    /*@Override*/\n    BitArray.prototype.clone = function () {\n        return new BitArray(this.size, this.bits.slice());\n    };\n    /**\n     * converts to boolean array.\n     */\n    BitArray.prototype.toArray = function () {\n        var result = [];\n        for (var i = 0, size = this.size; i < size; i++) {\n            result.push(this.get(i));\n        }\n        return result;\n    };\n    return BitArray;\n}());\nexport default BitArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,wBAAwB,MAAM,6BAA6B;AAClE,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,MAAM,MAAM,gBAAgB;AACnC;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,CAAC,2BAA2B,aAAe,YAAY;EAC/D;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASA,QAAQA,CAACC,IAAI,CAAC,SAASC,IAAI,EAAE;IAClC,IAAIC,SAAS,KAAKF,IAAI,EAAE;MACpB,IAAI,CAACA,IAAI,GAAG,CAAC;MACb,IAAI,CAACC,IAAI,GAAG,IAAIE,UAAU,CAAC,CAAC,CAAC;IACjC,CAAC,MACI;MACD,IAAI,CAACH,IAAI,GAAGA,IAAI;MAChB,IAAIE,SAAS,KAAKD,IAAI,IAAI,IAAI,KAAKA,IAAI,EAAE;QACrC,IAAI,CAACA,IAAI,GAAGF,QAAQ,CAACK,SAAS,CAACJ,IAAI,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACC,IAAI,GAAGA,IAAI;MACpB;IACJ;EACJ;EACAF,QAAQ,CAACM,SAAS,CAACC,OAAO,GAAG,YAAY;IACrC,OAAO,IAAI,CAACN,IAAI;EACpB,CAAC;EACDD,QAAQ,CAACM,SAAS,CAACE,cAAc,GAAG,YAAY;IAC5C,OAAOC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAI,CAACT,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;EAC1C,CAAC;EACDD,QAAQ,CAACM,SAAS,CAACK,cAAc,GAAG,UAAUV,IAAI,CAAC,SAAS;IACxD,IAAIA,IAAI,GAAG,IAAI,CAACC,IAAI,CAACU,MAAM,GAAG,EAAE,EAAE;MAC9B,IAAIC,OAAO,GAAGb,QAAQ,CAACK,SAAS,CAACJ,IAAI,CAAC;MACtCF,MAAM,CAACe,SAAS,CAAC,IAAI,CAACZ,IAAI,EAAE,CAAC,EAAEW,OAAO,EAAE,CAAC,EAAE,IAAI,CAACX,IAAI,CAACU,MAAM,CAAC;MAC5D,IAAI,CAACV,IAAI,GAAGW,OAAO;IACvB;EACJ,CAAC;EACD;AACJ;AACA;AACA;EACIb,QAAQ,CAACM,SAAS,CAACS,GAAG,GAAG,UAAUC,CAAC,CAAC,SAAS;IAC1C,OAAO,CAAC,IAAI,CAACd,IAAI,CAACO,IAAI,CAACC,KAAK,CAACM,CAAC,GAAG,EAAE,CAAC,CAAC,GAAI,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAE,MAAM,CAAC;EACpE,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIhB,QAAQ,CAACM,SAAS,CAACW,GAAG,GAAG,UAAUD,CAAC,CAAC,SAAS;IAC1C,IAAI,CAACd,IAAI,CAACO,IAAI,CAACC,KAAK,CAACM,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAC;EACpD,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIhB,QAAQ,CAACM,SAAS,CAACY,IAAI,GAAG,UAAUF,CAAC,CAAC,SAAS;IAC3C,IAAI,CAACd,IAAI,CAACO,IAAI,CAACC,KAAK,CAACM,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAKA,CAAC,GAAG,IAAI,CAAC;EACpD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIhB,QAAQ,CAACM,SAAS,CAACa,UAAU,GAAG,UAAUC,IAAI,CAAC,SAAS;IACpD,IAAInB,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAImB,IAAI,IAAInB,IAAI,EAAE;MACd,OAAOA,IAAI;IACf;IACA,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAImB,UAAU,GAAGZ,IAAI,CAACC,KAAK,CAACU,IAAI,GAAG,EAAE,CAAC;IACtC,IAAIE,WAAW,GAAGpB,IAAI,CAACmB,UAAU,CAAC;IAClC;IACAC,WAAW,IAAI,EAAE,CAAC,CAAC,KAAKF,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,IAAIR,MAAM,GAAGV,IAAI,CAACU,MAAM;IACxB,OAAOU,WAAW,KAAK,CAAC,EAAE;MACtB,IAAI,EAAED,UAAU,KAAKT,MAAM,EAAE;QACzB,OAAOX,IAAI;MACf;MACAqB,WAAW,GAAGpB,IAAI,CAACmB,UAAU,CAAC;IAClC;IACA,IAAIE,MAAM,GAAIF,UAAU,GAAG,EAAE,GAAIvB,OAAO,CAAC0B,qBAAqB,CAACF,WAAW,CAAC;IAC3E,OAAOC,MAAM,GAAGtB,IAAI,GAAGA,IAAI,GAAGsB,MAAM;EACxC,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIvB,QAAQ,CAACM,SAAS,CAACmB,YAAY,GAAG,UAAUL,IAAI,CAAC,SAAS;IACtD,IAAInB,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAImB,IAAI,IAAInB,IAAI,EAAE;MACd,OAAOA,IAAI;IACf;IACA,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAImB,UAAU,GAAGZ,IAAI,CAACC,KAAK,CAACU,IAAI,GAAG,EAAE,CAAC;IACtC,IAAIE,WAAW,GAAG,CAACpB,IAAI,CAACmB,UAAU,CAAC;IACnC;IACAC,WAAW,IAAI,EAAE,CAAC,CAAC,KAAKF,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C,IAAIR,MAAM,GAAGV,IAAI,CAACU,MAAM;IACxB,OAAOU,WAAW,KAAK,CAAC,EAAE;MACtB,IAAI,EAAED,UAAU,KAAKT,MAAM,EAAE;QACzB,OAAOX,IAAI;MACf;MACAqB,WAAW,GAAG,CAACpB,IAAI,CAACmB,UAAU,CAAC;IACnC;IACA,IAAIE,MAAM,GAAIF,UAAU,GAAG,EAAE,GAAIvB,OAAO,CAAC0B,qBAAqB,CAACF,WAAW,CAAC;IAC3E,OAAOC,MAAM,GAAGtB,IAAI,GAAGA,IAAI,GAAGsB,MAAM;EACxC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIvB,QAAQ,CAACM,SAAS,CAACoB,OAAO,GAAG,UAAUV,CAAC,CAAC,SAASH,OAAO,CAAC,SAAS;IAC/D,IAAI,CAACX,IAAI,CAACO,IAAI,CAACC,KAAK,CAACM,CAAC,GAAG,EAAE,CAAC,CAAC,GAAGH,OAAO;EAC3C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIb,QAAQ,CAACM,SAAS,CAACqB,QAAQ,GAAG,UAAUC,KAAK,CAAC,SAASC,GAAG,CAAC,SAAS;IAChE,IAAIA,GAAG,GAAGD,KAAK,IAAIA,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAG,IAAI,CAAC5B,IAAI,EAAE;MAC7C,MAAM,IAAIL,wBAAwB,CAAC,CAAC;IACxC;IACA,IAAIiC,GAAG,KAAKD,KAAK,EAAE;MACf;IACJ;IACAC,GAAG,EAAE,CAAC,CAAC;IACP,IAAIC,QAAQ,GAAGrB,IAAI,CAACC,KAAK,CAACkB,KAAK,GAAG,EAAE,CAAC;IACrC,IAAIG,OAAO,GAAGtB,IAAI,CAACC,KAAK,CAACmB,GAAG,GAAG,EAAE,CAAC;IAClC,IAAI3B,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,KAAK,IAAIc,CAAC,GAAGc,QAAQ,EAAEd,CAAC,IAAIe,OAAO,EAAEf,CAAC,EAAE,EAAE;MACtC,IAAIgB,QAAQ,GAAGhB,CAAC,GAAGc,QAAQ,GAAG,CAAC,GAAGF,KAAK,GAAG,IAAI;MAC9C,IAAIK,OAAO,GAAGjB,CAAC,GAAGe,OAAO,GAAG,EAAE,GAAGF,GAAG,GAAG,IAAI;MAC3C;MACA,IAAIK,IAAI,GAAG,CAAC,CAAC,IAAID,OAAO,KAAK,CAAC,IAAID,QAAQ,CAAC;MAC3C9B,IAAI,CAACc,CAAC,CAAC,IAAIkB,IAAI;IACnB;EACJ,CAAC;EACD;AACJ;AACA;EACIlC,QAAQ,CAACM,SAAS,CAAC6B,KAAK,GAAG,YAAY;IACnC,IAAIC,GAAG,GAAG,IAAI,CAAClC,IAAI,CAACU,MAAM;IAC1B,IAAIV,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,GAAG,EAAEpB,CAAC,EAAE,EAAE;MAC1Bd,IAAI,CAACc,CAAC,CAAC,GAAG,CAAC;IACf;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIhB,QAAQ,CAACM,SAAS,CAAC+B,OAAO,GAAG,UAAUT,KAAK,CAAC,SAASC,GAAG,CAAC,SAASS,KAAK,EAAE;IACtE,IAAIT,GAAG,GAAGD,KAAK,IAAIA,KAAK,GAAG,CAAC,IAAIC,GAAG,GAAG,IAAI,CAAC5B,IAAI,EAAE;MAC7C,MAAM,IAAIL,wBAAwB,CAAC,CAAC;IACxC;IACA,IAAIiC,GAAG,KAAKD,KAAK,EAAE;MACf,OAAO,IAAI,CAAC,CAAC;IACjB;IACAC,GAAG,EAAE,CAAC,CAAC;IACP,IAAIC,QAAQ,GAAGrB,IAAI,CAACC,KAAK,CAACkB,KAAK,GAAG,EAAE,CAAC;IACrC,IAAIG,OAAO,GAAGtB,IAAI,CAACC,KAAK,CAACmB,GAAG,GAAG,EAAE,CAAC;IAClC,IAAI3B,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,KAAK,IAAIc,CAAC,GAAGc,QAAQ,EAAEd,CAAC,IAAIe,OAAO,EAAEf,CAAC,EAAE,EAAE;MACtC,IAAIgB,QAAQ,GAAGhB,CAAC,GAAGc,QAAQ,GAAG,CAAC,GAAGF,KAAK,GAAG,IAAI;MAC9C,IAAIK,OAAO,GAAGjB,CAAC,GAAGe,OAAO,GAAG,EAAE,GAAGF,GAAG,GAAG,IAAI;MAC3C;MACA,IAAIK,IAAI,GAAG,CAAC,CAAC,IAAID,OAAO,KAAK,CAAC,IAAID,QAAQ,CAAC,GAAG,UAAU;MACxD;MACA;MACA;MACA,IAAI,CAAC9B,IAAI,CAACc,CAAC,CAAC,GAAGkB,IAAI,OAAOI,KAAK,GAAGJ,IAAI,GAAG,CAAC,CAAC,EAAE;QACzC,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACDlC,QAAQ,CAACM,SAAS,CAACiC,SAAS,GAAG,UAAUC,GAAG,EAAE;IAC1C,IAAI,CAAC7B,cAAc,CAAC,IAAI,CAACV,IAAI,GAAG,CAAC,CAAC;IAClC,IAAIuC,GAAG,EAAE;MACL,IAAI,CAACtC,IAAI,CAACO,IAAI,CAACC,KAAK,CAAC,IAAI,CAACT,IAAI,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAACA,IAAI,GAAG,IAAI,CAAC;IACpE;IACA,IAAI,CAACA,IAAI,EAAE;EACf,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACID,QAAQ,CAACM,SAAS,CAACmC,UAAU,GAAG,UAAUH,KAAK,CAAC,SAASI,OAAO,CAAC,SAAS;IACtE,IAAIA,OAAO,GAAG,CAAC,IAAIA,OAAO,GAAG,EAAE,EAAE;MAC7B,MAAM,IAAI9C,wBAAwB,CAAC,mCAAmC,CAAC;IAC3E;IACA,IAAI,CAACe,cAAc,CAAC,IAAI,CAACV,IAAI,GAAGyC,OAAO,CAAC;IACxC;IACA,KAAK,IAAIC,WAAW,GAAGD,OAAO,EAAEC,WAAW,GAAG,CAAC,EAAEA,WAAW,EAAE,EAAE;MAC5D,IAAI,CAACJ,SAAS,CAAC,CAAED,KAAK,IAAKK,WAAW,GAAG,CAAE,GAAI,IAAI,MAAM,CAAC,CAAC;IAC/D;EACJ,CAAC;EACD3C,QAAQ,CAACM,SAAS,CAACsC,cAAc,GAAG,UAAUC,KAAK,EAAE;IACjD,IAAIC,SAAS,GAAGD,KAAK,CAAC5C,IAAI;IAC1B,IAAI,CAACU,cAAc,CAAC,IAAI,CAACV,IAAI,GAAG6C,SAAS,CAAC;IAC1C;IACA,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,SAAS,EAAE9B,CAAC,EAAE,EAAE;MAChC,IAAI,CAACuB,SAAS,CAACM,KAAK,CAAC9B,GAAG,CAACC,CAAC,CAAC,CAAC;IAChC;EACJ,CAAC;EACDhB,QAAQ,CAACM,SAAS,CAACyC,GAAG,GAAG,UAAUF,KAAK,EAAE;IACtC,IAAI,IAAI,CAAC5C,IAAI,KAAK4C,KAAK,CAAC5C,IAAI,EAAE;MAC1B,MAAM,IAAIL,wBAAwB,CAAC,oBAAoB,CAAC;IAC5D;IACA,IAAIM,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEgC,QAAQ,GAAG9C,IAAI,CAACU,MAAM,EAAEI,CAAC,GAAGgC,QAAQ,EAAEhC,CAAC,EAAE,EAAE;MACvD;MACA;MACAd,IAAI,CAACc,CAAC,CAAC,IAAI6B,KAAK,CAAC3C,IAAI,CAACc,CAAC,CAAC;IAC5B;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIhB,QAAQ,CAACM,SAAS,CAAC2C,OAAO,GAAG,UAAUC,SAAS,CAAC,SAASC,KAAK,EAAEC,MAAM,CAAC,SAASC,QAAQ,CAAC,SAAS;IAC/F,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,QAAQ,EAAErC,CAAC,EAAE,EAAE;MAC/B,IAAIsC,OAAO,GAAG,CAAC;MACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QACxB,IAAI,IAAI,CAACxC,GAAG,CAACmC,SAAS,CAAC,EAAE;UACrBI,OAAO,IAAI,CAAC,IAAK,CAAC,GAAGC,CAAE;QAC3B;QACAL,SAAS,EAAE;MACf;MACAC,KAAK,CAACC,MAAM,GAAGpC,CAAC,CAAC,GAAG,UAAWsC,OAAO;IAC1C;EACJ,CAAC;EACD;AACJ;AACA;AACA;EACItD,QAAQ,CAACM,SAAS,CAACkD,WAAW,GAAG,YAAY;IACzC,OAAO,IAAI,CAACtD,IAAI;EACpB,CAAC;EACD;AACJ;AACA;EACIF,QAAQ,CAACM,SAAS,CAACmD,OAAO,GAAG,YAAY;IACrC,IAAI5C,OAAO,GAAG,IAAIT,UAAU,CAAC,IAAI,CAACF,IAAI,CAACU,MAAM,CAAC;IAC9C;IACA,IAAI8C,GAAG,GAAGjD,IAAI,CAACC,KAAK,CAAC,CAAC,IAAI,CAACT,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;IAC1C,IAAI0D,UAAU,GAAGD,GAAG,GAAG,CAAC;IACxB,IAAIxD,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,UAAU,EAAE3C,CAAC,EAAE,EAAE;MACjC,IAAI4C,CAAC,GAAG1D,IAAI,CAACc,CAAC,CAAC;MACf4C,CAAC,GAAKA,CAAC,IAAI,CAAC,GAAI,UAAU,GAAK,CAACA,CAAC,GAAG,UAAU,KAAK,CAAE;MACrDA,CAAC,GAAKA,CAAC,IAAI,CAAC,GAAI,UAAU,GAAK,CAACA,CAAC,GAAG,UAAU,KAAK,CAAE;MACrDA,CAAC,GAAKA,CAAC,IAAI,CAAC,GAAI,UAAU,GAAK,CAACA,CAAC,GAAG,UAAU,KAAK,CAAE;MACrDA,CAAC,GAAKA,CAAC,IAAI,CAAC,GAAI,UAAU,GAAK,CAACA,CAAC,GAAG,UAAU,KAAK,CAAE;MACrDA,CAAC,GAAKA,CAAC,IAAI,EAAE,GAAI,UAAU,GAAK,CAACA,CAAC,GAAG,UAAU,KAAK,EAAG;MACvD/C,OAAO,CAAC6C,GAAG,GAAG1C,CAAC,CAAC,GAAG,SAAU4C,CAAC;IAClC;IACA;IACA,IAAI,IAAI,CAAC3D,IAAI,KAAK0D,UAAU,GAAG,EAAE,EAAE;MAC/B,IAAIE,UAAU,GAAGF,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC1D,IAAI;MAC5C,IAAI6D,UAAU,GAAGjD,OAAO,CAAC,CAAC,CAAC,KAAKgD,UAAU;MAC1C,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2C,UAAU,EAAE3C,CAAC,EAAE,EAAE;QACjC,IAAI+C,OAAO,GAAGlD,OAAO,CAACG,CAAC,CAAC;QACxB8C,UAAU,IAAIC,OAAO,IAAK,EAAE,GAAGF,UAAW;QAC1ChD,OAAO,CAACG,CAAC,GAAG,CAAC,CAAC,GAAG8C,UAAU;QAC3BA,UAAU,GAAGC,OAAO,KAAKF,UAAU;MACvC;MACAhD,OAAO,CAAC8C,UAAU,GAAG,CAAC,CAAC,GAAGG,UAAU;IACxC;IACA,IAAI,CAAC5D,IAAI,GAAGW,OAAO;EACvB,CAAC;EACDb,QAAQ,CAACK,SAAS,GAAG,UAAUJ,IAAI,CAAC,SAAS;IACzC,OAAO,IAAIG,UAAU,CAACK,IAAI,CAACC,KAAK,CAAC,CAACT,IAAI,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;EACvD,CAAC;EACD;EACAD,QAAQ,CAACM,SAAS,CAAC0D,MAAM,GAAG,UAAUC,CAAC,EAAE;IACrC,IAAI,EAAEA,CAAC,YAAYjE,QAAQ,CAAC,EAAE;MAC1B,OAAO,KAAK;IAChB;IACA,IAAI6C,KAAK,GAAGoB,CAAC;IACb,OAAO,IAAI,CAAChE,IAAI,KAAK4C,KAAK,CAAC5C,IAAI,IAAIJ,MAAM,CAACmE,MAAM,CAAC,IAAI,CAAC9D,IAAI,EAAE2C,KAAK,CAAC3C,IAAI,CAAC;EAC3E,CAAC;EACD;EACAF,QAAQ,CAACM,SAAS,CAAC4D,QAAQ,GAAG,YAAY;IACtC,OAAO,EAAE,GAAG,IAAI,CAACjE,IAAI,GAAGJ,MAAM,CAACqE,QAAQ,CAAC,IAAI,CAAChE,IAAI,CAAC;EACtD,CAAC;EACD;EACAF,QAAQ,CAACM,SAAS,CAAC6D,QAAQ,GAAG,YAAY;IACtC,IAAI5C,MAAM,GAAG,EAAE;IACf,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEf,IAAI,GAAG,IAAI,CAACA,IAAI,EAAEe,CAAC,GAAGf,IAAI,EAAEe,CAAC,EAAE,EAAE;MAC7C,IAAI,CAACA,CAAC,GAAG,IAAI,MAAM,CAAC,EAAE;QAClBO,MAAM,IAAI,GAAG;MACjB;MACAA,MAAM,IAAI,IAAI,CAACR,GAAG,CAACC,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;IACrC;IACA,OAAOO,MAAM;EACjB,CAAC;EACD;EACAvB,QAAQ,CAACM,SAAS,CAAC8D,KAAK,GAAG,YAAY;IACnC,OAAO,IAAIpE,QAAQ,CAAC,IAAI,CAACC,IAAI,EAAE,IAAI,CAACC,IAAI,CAACmE,KAAK,CAAC,CAAC,CAAC;EACrD,CAAC;EACD;AACJ;AACA;EACIrE,QAAQ,CAACM,SAAS,CAACgE,OAAO,GAAG,YAAY;IACrC,IAAI/C,MAAM,GAAG,EAAE;IACf,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEf,IAAI,GAAG,IAAI,CAACA,IAAI,EAAEe,CAAC,GAAGf,IAAI,EAAEe,CAAC,EAAE,EAAE;MAC7CO,MAAM,CAACgD,IAAI,CAAC,IAAI,CAACxD,GAAG,CAACC,CAAC,CAAC,CAAC;IAC5B;IACA,OAAOO,MAAM;EACjB,CAAC;EACD,OAAOvB,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}