{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"label\", \"notched\", \"shrink\"];\nimport * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst OutlineRoot = styled('fieldset', {\n  name: 'MuiPickersOutlinedInput',\n  slot: 'NotchedOutline',\n  overridesResolver: (props, styles) => styles.notchedOutline\n})(({\n  theme\n}) => {\n  const borderColor = theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.23)' : 'rgba(255, 255, 255, 0.23)';\n  return {\n    textAlign: 'left',\n    position: 'absolute',\n    bottom: 0,\n    right: 0,\n    top: -5,\n    left: 0,\n    margin: 0,\n    padding: '0 8px',\n    pointerEvents: 'none',\n    borderRadius: 'inherit',\n    borderStyle: 'solid',\n    borderWidth: 1,\n    overflow: 'hidden',\n    minWidth: '0%',\n    borderColor: theme.vars ? `rgba(${theme.vars.palette.common.onBackgroundChannel} / 0.23)` : borderColor\n  };\n});\nconst OutlineLabel = styled('span')(({\n  theme\n}) => ({\n  fontFamily: theme.typography.fontFamily,\n  fontSize: 'inherit'\n}));\nconst OutlineLegend = styled('legend')(({\n  theme\n}) => ({\n  float: 'unset',\n  // Fix conflict with bootstrap\n  width: 'auto',\n  // Fix conflict with bootstrap\n  overflow: 'hidden',\n  // Fix Horizontal scroll when label too long\n  variants: [{\n    props: {\n      withLabel: false\n    },\n    style: {\n      padding: 0,\n      lineHeight: '11px',\n      // sync with `height` in `legend` styles\n      transition: theme.transitions.create('width', {\n        duration: 150,\n        easing: theme.transitions.easing.easeOut\n      })\n    }\n  }, {\n    props: {\n      withLabel: true\n    },\n    style: {\n      display: 'block',\n      // Fix conflict with normalize.css and sanitize.css\n      padding: 0,\n      height: 11,\n      // sync with `lineHeight` in `legend` styles\n      fontSize: '0.75em',\n      visibility: 'hidden',\n      maxWidth: 0.01,\n      transition: theme.transitions.create('max-width', {\n        duration: 50,\n        easing: theme.transitions.easing.easeOut\n      }),\n      whiteSpace: 'nowrap',\n      '& > span': {\n        paddingLeft: 5,\n        paddingRight: 5,\n        display: 'inline-block',\n        opacity: 0,\n        visibility: 'visible'\n      }\n    }\n  }, {\n    props: {\n      withLabel: true,\n      notched: true\n    },\n    style: {\n      maxWidth: '100%',\n      transition: theme.transitions.create('max-width', {\n        duration: 100,\n        easing: theme.transitions.easing.easeOut,\n        delay: 50\n      })\n    }\n  }]\n}));\n\n/**\n * @ignore - internal component.\n */\nexport default function Outline(props) {\n  const {\n      className,\n      label\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const withLabel = label != null && label !== '';\n  const ownerState = _extends({}, props, {\n    withLabel\n  });\n  return /*#__PURE__*/_jsx(OutlineRoot, _extends({\n    \"aria-hidden\": true,\n    className: className\n  }, other, {\n    ownerState: ownerState,\n    children: /*#__PURE__*/_jsx(OutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(OutlineLabel, {\n        children: label\n      }) : /*#__PURE__*/\n      // notranslate needed while Google Translate will not fix zero-width space issue\n      _jsx(OutlineLabel, {\n        className: \"notranslate\",\n        children: \"\\u200B\"\n      })\n    })\n  }));\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "styled", "jsx", "_jsx", "OutlineRoot", "name", "slot", "overridesResolver", "props", "styles", "notchedOutline", "theme", "borderColor", "palette", "mode", "textAlign", "position", "bottom", "right", "top", "left", "margin", "padding", "pointerEvents", "borderRadius", "borderStyle", "borderWidth", "overflow", "min<PERSON><PERSON><PERSON>", "vars", "common", "onBackgroundChannel", "OutlineLabel", "fontFamily", "typography", "fontSize", "OutlineLegend", "float", "width", "variants", "<PERSON><PERSON><PERSON><PERSON>", "style", "lineHeight", "transition", "transitions", "create", "duration", "easing", "easeOut", "display", "height", "visibility", "max<PERSON><PERSON><PERSON>", "whiteSpace", "paddingLeft", "paddingRight", "opacity", "notched", "delay", "Outline", "className", "label", "other", "ownerState", "children"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/PickersTextField/PickersOutlinedInput/Outline.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"label\", \"notched\", \"shrink\"];\nimport * as React from 'react';\nimport { styled } from '@mui/material/styles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst OutlineRoot = styled('fieldset', {\n  name: 'MuiPickersOutlinedInput',\n  slot: 'NotchedOutline',\n  overridesResolver: (props, styles) => styles.notchedOutline\n})(({\n  theme\n}) => {\n  const borderColor = theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.23)' : 'rgba(255, 255, 255, 0.23)';\n  return {\n    textAlign: 'left',\n    position: 'absolute',\n    bottom: 0,\n    right: 0,\n    top: -5,\n    left: 0,\n    margin: 0,\n    padding: '0 8px',\n    pointerEvents: 'none',\n    borderRadius: 'inherit',\n    borderStyle: 'solid',\n    borderWidth: 1,\n    overflow: 'hidden',\n    minWidth: '0%',\n    borderColor: theme.vars ? `rgba(${theme.vars.palette.common.onBackgroundChannel} / 0.23)` : borderColor\n  };\n});\nconst OutlineLabel = styled('span')(({\n  theme\n}) => ({\n  fontFamily: theme.typography.fontFamily,\n  fontSize: 'inherit'\n}));\nconst OutlineLegend = styled('legend')(({\n  theme\n}) => ({\n  float: 'unset',\n  // Fix conflict with bootstrap\n  width: 'auto',\n  // Fix conflict with bootstrap\n  overflow: 'hidden',\n  // Fix Horizontal scroll when label too long\n  variants: [{\n    props: {\n      withLabel: false\n    },\n    style: {\n      padding: 0,\n      lineHeight: '11px',\n      // sync with `height` in `legend` styles\n      transition: theme.transitions.create('width', {\n        duration: 150,\n        easing: theme.transitions.easing.easeOut\n      })\n    }\n  }, {\n    props: {\n      withLabel: true\n    },\n    style: {\n      display: 'block',\n      // Fix conflict with normalize.css and sanitize.css\n      padding: 0,\n      height: 11,\n      // sync with `lineHeight` in `legend` styles\n      fontSize: '0.75em',\n      visibility: 'hidden',\n      maxWidth: 0.01,\n      transition: theme.transitions.create('max-width', {\n        duration: 50,\n        easing: theme.transitions.easing.easeOut\n      }),\n      whiteSpace: 'nowrap',\n      '& > span': {\n        paddingLeft: 5,\n        paddingRight: 5,\n        display: 'inline-block',\n        opacity: 0,\n        visibility: 'visible'\n      }\n    }\n  }, {\n    props: {\n      withLabel: true,\n      notched: true\n    },\n    style: {\n      maxWidth: '100%',\n      transition: theme.transitions.create('max-width', {\n        duration: 100,\n        easing: theme.transitions.easing.easeOut,\n        delay: 50\n      })\n    }\n  }]\n}));\n\n/**\n * @ignore - internal component.\n */\nexport default function Outline(props) {\n  const {\n      className,\n      label\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const withLabel = label != null && label !== '';\n  const ownerState = _extends({}, props, {\n    withLabel\n  });\n  return /*#__PURE__*/_jsx(OutlineRoot, _extends({\n    \"aria-hidden\": true,\n    className: className\n  }, other, {\n    ownerState: ownerState,\n    children: /*#__PURE__*/_jsx(OutlineLegend, {\n      ownerState: ownerState,\n      children: withLabel ? /*#__PURE__*/_jsx(OutlineLabel, {\n        children: label\n      }) :\n      /*#__PURE__*/\n      // notranslate needed while Google Translate will not fix zero-width space issue\n      _jsx(OutlineLabel, {\n        className: \"notranslate\",\n        children: \"\\u200B\"\n      })\n    })\n  }));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC;AACzE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAGH,MAAM,CAAC,UAAU,EAAE;EACrCI,IAAI,EAAE,yBAAyB;EAC/BC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAAC,CAAC;EACFC;AACF,CAAC,KAAK;EACJ,MAAMC,WAAW,GAAGD,KAAK,CAACE,OAAO,CAACC,IAAI,KAAK,OAAO,GAAG,qBAAqB,GAAG,2BAA2B;EACxG,OAAO;IACLC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC,CAAC;IACPC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,OAAO;IAChBC,aAAa,EAAE,MAAM;IACrBC,YAAY,EAAE,SAAS;IACvBC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,CAAC;IACdC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,IAAI;IACdhB,WAAW,EAAED,KAAK,CAACkB,IAAI,GAAG,QAAQlB,KAAK,CAACkB,IAAI,CAAChB,OAAO,CAACiB,MAAM,CAACC,mBAAmB,UAAU,GAAGnB;EAC9F,CAAC;AACH,CAAC,CAAC;AACF,MAAMoB,YAAY,GAAG/B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;EACnCU;AACF,CAAC,MAAM;EACLsB,UAAU,EAAEtB,KAAK,CAACuB,UAAU,CAACD,UAAU;EACvCE,QAAQ,EAAE;AACZ,CAAC,CAAC,CAAC;AACH,MAAMC,aAAa,GAAGnC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;EACtCU;AACF,CAAC,MAAM;EACL0B,KAAK,EAAE,OAAO;EACd;EACAC,KAAK,EAAE,MAAM;EACb;EACAX,QAAQ,EAAE,QAAQ;EAClB;EACAY,QAAQ,EAAE,CAAC;IACT/B,KAAK,EAAE;MACLgC,SAAS,EAAE;IACb,CAAC;IACDC,KAAK,EAAE;MACLnB,OAAO,EAAE,CAAC;MACVoB,UAAU,EAAE,MAAM;MAClB;MACAC,UAAU,EAAEhC,KAAK,CAACiC,WAAW,CAACC,MAAM,CAAC,OAAO,EAAE;QAC5CC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAEpC,KAAK,CAACiC,WAAW,CAACG,MAAM,CAACC;MACnC,CAAC;IACH;EACF,CAAC,EAAE;IACDxC,KAAK,EAAE;MACLgC,SAAS,EAAE;IACb,CAAC;IACDC,KAAK,EAAE;MACLQ,OAAO,EAAE,OAAO;MAChB;MACA3B,OAAO,EAAE,CAAC;MACV4B,MAAM,EAAE,EAAE;MACV;MACAf,QAAQ,EAAE,QAAQ;MAClBgB,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,IAAI;MACdT,UAAU,EAAEhC,KAAK,CAACiC,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;QAChDC,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAEpC,KAAK,CAACiC,WAAW,CAACG,MAAM,CAACC;MACnC,CAAC,CAAC;MACFK,UAAU,EAAE,QAAQ;MACpB,UAAU,EAAE;QACVC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfN,OAAO,EAAE,cAAc;QACvBO,OAAO,EAAE,CAAC;QACVL,UAAU,EAAE;MACd;IACF;EACF,CAAC,EAAE;IACD3C,KAAK,EAAE;MACLgC,SAAS,EAAE,IAAI;MACfiB,OAAO,EAAE;IACX,CAAC;IACDhB,KAAK,EAAE;MACLW,QAAQ,EAAE,MAAM;MAChBT,UAAU,EAAEhC,KAAK,CAACiC,WAAW,CAACC,MAAM,CAAC,WAAW,EAAE;QAChDC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAEpC,KAAK,CAACiC,WAAW,CAACG,MAAM,CAACC,OAAO;QACxCU,KAAK,EAAE;MACT,CAAC;IACH;EACF,CAAC;AACH,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA,eAAe,SAASC,OAAOA,CAACnD,KAAK,EAAE;EACrC,MAAM;MACFoD,SAAS;MACTC;IACF,CAAC,GAAGrD,KAAK;IACTsD,KAAK,GAAGhE,6BAA6B,CAACU,KAAK,EAAET,SAAS,CAAC;EACzD,MAAMyC,SAAS,GAAGqB,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,EAAE;EAC/C,MAAME,UAAU,GAAGlE,QAAQ,CAAC,CAAC,CAAC,EAAEW,KAAK,EAAE;IACrCgC;EACF,CAAC,CAAC;EACF,OAAO,aAAarC,IAAI,CAACC,WAAW,EAAEP,QAAQ,CAAC;IAC7C,aAAa,EAAE,IAAI;IACnB+D,SAAS,EAAEA;EACb,CAAC,EAAEE,KAAK,EAAE;IACRC,UAAU,EAAEA,UAAU;IACtBC,QAAQ,EAAE,aAAa7D,IAAI,CAACiC,aAAa,EAAE;MACzC2B,UAAU,EAAEA,UAAU;MACtBC,QAAQ,EAAExB,SAAS,GAAG,aAAarC,IAAI,CAAC6B,YAAY,EAAE;QACpDgC,QAAQ,EAAEH;MACZ,CAAC,CAAC,GACF;MACA;MACA1D,IAAI,CAAC6B,YAAY,EAAE;QACjB4B,SAAS,EAAE,aAAa;QACxBI,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC;EACH,CAAC,CAAC,CAAC;AACL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}