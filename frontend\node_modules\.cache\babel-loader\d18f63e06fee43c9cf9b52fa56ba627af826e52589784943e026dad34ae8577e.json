{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.aztec.encoder;\n// import java.util.Deque;\n// import java.util.LinkedList;\n// import com.google.zxing.common.BitArray;\nimport BitArray from '../../common/BitArray';\nimport * as TokenHelpers from './TokenHelpers';\nimport * as C from './EncoderConstants';\nimport * as LatchTable from './LatchTable';\nimport * as ShiftTable from './ShiftTable';\nimport StringUtils from '../../common/StringUtils';\n/**\n * State represents all information about a sequence necessary to generate the current output.\n * Note that a state is immutable.\n */\nvar State = /** @class */function () {\n  function State(token, mode, binaryBytes, bitCount) {\n    this.token = token;\n    this.mode = mode;\n    this.binaryShiftByteCount = binaryBytes;\n    this.bitCount = bitCount;\n    // Make sure we match the token\n    // int binaryShiftBitCount = (binaryShiftByteCount * 8) +\n    //    (binaryShiftByteCount === 0 ? 0 :\n    //     binaryShiftByteCount <= 31 ? 10 :\n    //     binaryShiftByteCount <= 62 ? 20 : 21);\n    // assert this.bitCount === token.getTotalBitCount() + binaryShiftBitCount;\n  }\n  State.prototype.getMode = function () {\n    return this.mode;\n  };\n  State.prototype.getToken = function () {\n    return this.token;\n  };\n  State.prototype.getBinaryShiftByteCount = function () {\n    return this.binaryShiftByteCount;\n  };\n  State.prototype.getBitCount = function () {\n    return this.bitCount;\n  };\n  // Create a new state representing this state with a latch to a (not\n  // necessary different) mode, and then a code.\n  State.prototype.latchAndAppend = function (mode, value) {\n    // assert binaryShiftByteCount === 0;\n    var bitCount = this.bitCount;\n    var token = this.token;\n    if (mode !== this.mode) {\n      var latch = LatchTable.LATCH_TABLE[this.mode][mode];\n      token = TokenHelpers.add(token, latch & 0xffff, latch >> 16);\n      bitCount += latch >> 16;\n    }\n    var latchModeBitCount = mode === C.MODE_DIGIT ? 4 : 5;\n    token = TokenHelpers.add(token, value, latchModeBitCount);\n    return new State(token, mode, 0, bitCount + latchModeBitCount);\n  };\n  // Create a new state representing this state, with a temporary shift\n  // to a different mode to output a single value.\n  State.prototype.shiftAndAppend = function (mode, value) {\n    // assert binaryShiftByteCount === 0 && this.mode !== mode;\n    var token = this.token;\n    var thisModeBitCount = this.mode === C.MODE_DIGIT ? 4 : 5;\n    // Shifts exist only to UPPER and PUNCT, both with tokens size 5.\n    token = TokenHelpers.add(token, ShiftTable.SHIFT_TABLE[this.mode][mode], thisModeBitCount);\n    token = TokenHelpers.add(token, value, 5);\n    return new State(token, this.mode, 0, this.bitCount + thisModeBitCount + 5);\n  };\n  // Create a new state representing this state, but an additional character\n  // output in Binary Shift mode.\n  State.prototype.addBinaryShiftChar = function (index) {\n    var token = this.token;\n    var mode = this.mode;\n    var bitCount = this.bitCount;\n    if (this.mode === C.MODE_PUNCT || this.mode === C.MODE_DIGIT) {\n      // assert binaryShiftByteCount === 0;\n      var latch = LatchTable.LATCH_TABLE[mode][C.MODE_UPPER];\n      token = TokenHelpers.add(token, latch & 0xffff, latch >> 16);\n      bitCount += latch >> 16;\n      mode = C.MODE_UPPER;\n    }\n    var deltaBitCount = this.binaryShiftByteCount === 0 || this.binaryShiftByteCount === 31 ? 18 : this.binaryShiftByteCount === 62 ? 9 : 8;\n    var result = new State(token, mode, this.binaryShiftByteCount + 1, bitCount + deltaBitCount);\n    if (result.binaryShiftByteCount === 2047 + 31) {\n      // The string is as long as it's allowed to be.  We should end it.\n      result = result.endBinaryShift(index + 1);\n    }\n    return result;\n  };\n  // Create the state identical to this one, but we are no longer in\n  // Binary Shift mode.\n  State.prototype.endBinaryShift = function (index) {\n    if (this.binaryShiftByteCount === 0) {\n      return this;\n    }\n    var token = this.token;\n    token = TokenHelpers.addBinaryShift(token, index - this.binaryShiftByteCount, this.binaryShiftByteCount);\n    // assert token.getTotalBitCount() === this.bitCount;\n    return new State(token, this.mode, 0, this.bitCount);\n  };\n  // Returns true if \"this\" state is better (equal: or) to be in than \"that\"\n  // state under all possible circumstances.\n  State.prototype.isBetterThanOrEqualTo = function (other) {\n    var newModeBitCount = this.bitCount + (LatchTable.LATCH_TABLE[this.mode][other.mode] >> 16);\n    if (this.binaryShiftByteCount < other.binaryShiftByteCount) {\n      // add additional B/S encoding cost of other, if any\n      newModeBitCount += State.calculateBinaryShiftCost(other) - State.calculateBinaryShiftCost(this);\n    } else if (this.binaryShiftByteCount > other.binaryShiftByteCount && other.binaryShiftByteCount > 0) {\n      // maximum possible additional cost (it: h)\n      newModeBitCount += 10;\n    }\n    return newModeBitCount <= other.bitCount;\n  };\n  State.prototype.toBitArray = function (text) {\n    var e_1, _a;\n    // Reverse the tokens, so that they are in the order that they should\n    // be output\n    var symbols = [];\n    for (var token = this.endBinaryShift(text.length).token; token !== null; token = token.getPrevious()) {\n      symbols.unshift(token);\n    }\n    var bitArray = new BitArray();\n    try {\n      // Add each token to the result.\n      for (var symbols_1 = __values(symbols), symbols_1_1 = symbols_1.next(); !symbols_1_1.done; symbols_1_1 = symbols_1.next()) {\n        var symbol = symbols_1_1.value;\n        symbol.appendTo(bitArray, text);\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (symbols_1_1 && !symbols_1_1.done && (_a = symbols_1.return)) _a.call(symbols_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    // assert bitArray.getSize() === this.bitCount;\n    return bitArray;\n  };\n  /**\n   * @Override\n   */\n  State.prototype.toString = function () {\n    return StringUtils.format('%s bits=%d bytes=%d', C.MODE_NAMES[this.mode], this.bitCount, this.binaryShiftByteCount);\n  };\n  State.calculateBinaryShiftCost = function (state) {\n    if (state.binaryShiftByteCount > 62) {\n      return 21; // B/S with extended length\n    }\n    if (state.binaryShiftByteCount > 31) {\n      return 20; // two B/S\n    }\n    if (state.binaryShiftByteCount > 0) {\n      return 10; // one B/S\n    }\n    return 0;\n  };\n  State.INITIAL_STATE = new State(C.EMPTY_TOKEN, C.MODE_UPPER, 0, 0);\n  return State;\n}();\nexport default State;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "BitArray", "TokenHelpers", "C", "LatchTable", "ShiftTable", "StringUtils", "State", "token", "mode", "binaryBytes", "bitCount", "binaryShiftByteCount", "prototype", "getMode", "getToken", "getBinaryShiftByteCount", "getBitCount", "latchAndAppend", "latch", "LATCH_TABLE", "add", "latchModeBitCount", "MODE_DIGIT", "shiftAndAppend", "thisModeBitCount", "SHIFT_TABLE", "addBinaryShiftChar", "index", "MODE_PUNCT", "MODE_UPPER", "deltaBitCount", "result", "endBinaryShift", "addBinaryShift", "isBetterThanOrEqualTo", "other", "newModeBitCount", "calculateBinaryShiftCost", "toBitArray", "text", "e_1", "_a", "symbols", "getPrevious", "unshift", "bitArray", "symbols_1", "symbols_1_1", "symbol", "appendTo", "e_1_1", "error", "return", "toString", "format", "MODE_NAMES", "state", "INITIAL_STATE", "EMPTY_TOKEN"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/State.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.aztec.encoder;\n// import java.util.Deque;\n// import java.util.LinkedList;\n// import com.google.zxing.common.BitArray;\nimport BitArray from '../../common/BitArray';\nimport * as TokenHelpers from './TokenHelpers';\nimport * as C from './EncoderConstants';\nimport * as LatchTable from './LatchTable';\nimport * as ShiftTable from './ShiftTable';\nimport StringUtils from '../../common/StringUtils';\n/**\n * State represents all information about a sequence necessary to generate the current output.\n * Note that a state is immutable.\n */\nvar State = /** @class */ (function () {\n    function State(token, mode, binaryBytes, bitCount) {\n        this.token = token;\n        this.mode = mode;\n        this.binaryShiftByteCount = binaryBytes;\n        this.bitCount = bitCount;\n        // Make sure we match the token\n        // int binaryShiftBitCount = (binaryShiftByteCount * 8) +\n        //    (binaryShiftByteCount === 0 ? 0 :\n        //     binaryShiftByteCount <= 31 ? 10 :\n        //     binaryShiftByteCount <= 62 ? 20 : 21);\n        // assert this.bitCount === token.getTotalBitCount() + binaryShiftBitCount;\n    }\n    State.prototype.getMode = function () {\n        return this.mode;\n    };\n    State.prototype.getToken = function () {\n        return this.token;\n    };\n    State.prototype.getBinaryShiftByteCount = function () {\n        return this.binaryShiftByteCount;\n    };\n    State.prototype.getBitCount = function () {\n        return this.bitCount;\n    };\n    // Create a new state representing this state with a latch to a (not\n    // necessary different) mode, and then a code.\n    State.prototype.latchAndAppend = function (mode, value) {\n        // assert binaryShiftByteCount === 0;\n        var bitCount = this.bitCount;\n        var token = this.token;\n        if (mode !== this.mode) {\n            var latch = LatchTable.LATCH_TABLE[this.mode][mode];\n            token = TokenHelpers.add(token, latch & 0xffff, latch >> 16);\n            bitCount += latch >> 16;\n        }\n        var latchModeBitCount = mode === C.MODE_DIGIT ? 4 : 5;\n        token = TokenHelpers.add(token, value, latchModeBitCount);\n        return new State(token, mode, 0, bitCount + latchModeBitCount);\n    };\n    // Create a new state representing this state, with a temporary shift\n    // to a different mode to output a single value.\n    State.prototype.shiftAndAppend = function (mode, value) {\n        // assert binaryShiftByteCount === 0 && this.mode !== mode;\n        var token = this.token;\n        var thisModeBitCount = this.mode === C.MODE_DIGIT ? 4 : 5;\n        // Shifts exist only to UPPER and PUNCT, both with tokens size 5.\n        token = TokenHelpers.add(token, ShiftTable.SHIFT_TABLE[this.mode][mode], thisModeBitCount);\n        token = TokenHelpers.add(token, value, 5);\n        return new State(token, this.mode, 0, this.bitCount + thisModeBitCount + 5);\n    };\n    // Create a new state representing this state, but an additional character\n    // output in Binary Shift mode.\n    State.prototype.addBinaryShiftChar = function (index) {\n        var token = this.token;\n        var mode = this.mode;\n        var bitCount = this.bitCount;\n        if (this.mode === C.MODE_PUNCT || this.mode === C.MODE_DIGIT) {\n            // assert binaryShiftByteCount === 0;\n            var latch = LatchTable.LATCH_TABLE[mode][C.MODE_UPPER];\n            token = TokenHelpers.add(token, latch & 0xffff, latch >> 16);\n            bitCount += latch >> 16;\n            mode = C.MODE_UPPER;\n        }\n        var deltaBitCount = this.binaryShiftByteCount === 0 || this.binaryShiftByteCount === 31\n            ? 18\n            : this.binaryShiftByteCount === 62\n                ? 9\n                : 8;\n        var result = new State(token, mode, this.binaryShiftByteCount + 1, bitCount + deltaBitCount);\n        if (result.binaryShiftByteCount === 2047 + 31) {\n            // The string is as long as it's allowed to be.  We should end it.\n            result = result.endBinaryShift(index + 1);\n        }\n        return result;\n    };\n    // Create the state identical to this one, but we are no longer in\n    // Binary Shift mode.\n    State.prototype.endBinaryShift = function (index) {\n        if (this.binaryShiftByteCount === 0) {\n            return this;\n        }\n        var token = this.token;\n        token = TokenHelpers.addBinaryShift(token, index - this.binaryShiftByteCount, this.binaryShiftByteCount);\n        // assert token.getTotalBitCount() === this.bitCount;\n        return new State(token, this.mode, 0, this.bitCount);\n    };\n    // Returns true if \"this\" state is better (equal: or) to be in than \"that\"\n    // state under all possible circumstances.\n    State.prototype.isBetterThanOrEqualTo = function (other) {\n        var newModeBitCount = this.bitCount + (LatchTable.LATCH_TABLE[this.mode][other.mode] >> 16);\n        if (this.binaryShiftByteCount < other.binaryShiftByteCount) {\n            // add additional B/S encoding cost of other, if any\n            newModeBitCount +=\n                State.calculateBinaryShiftCost(other) -\n                    State.calculateBinaryShiftCost(this);\n        }\n        else if (this.binaryShiftByteCount > other.binaryShiftByteCount &&\n            other.binaryShiftByteCount > 0) {\n            // maximum possible additional cost (it: h)\n            newModeBitCount += 10;\n        }\n        return newModeBitCount <= other.bitCount;\n    };\n    State.prototype.toBitArray = function (text) {\n        var e_1, _a;\n        // Reverse the tokens, so that they are in the order that they should\n        // be output\n        var symbols = [];\n        for (var token = this.endBinaryShift(text.length).token; token !== null; token = token.getPrevious()) {\n            symbols.unshift(token);\n        }\n        var bitArray = new BitArray();\n        try {\n            // Add each token to the result.\n            for (var symbols_1 = __values(symbols), symbols_1_1 = symbols_1.next(); !symbols_1_1.done; symbols_1_1 = symbols_1.next()) {\n                var symbol = symbols_1_1.value;\n                symbol.appendTo(bitArray, text);\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (symbols_1_1 && !symbols_1_1.done && (_a = symbols_1.return)) _a.call(symbols_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        // assert bitArray.getSize() === this.bitCount;\n        return bitArray;\n    };\n    /**\n     * @Override\n     */\n    State.prototype.toString = function () {\n        return StringUtils.format('%s bits=%d bytes=%d', C.MODE_NAMES[this.mode], this.bitCount, this.binaryShiftByteCount);\n    };\n    State.calculateBinaryShiftCost = function (state) {\n        if (state.binaryShiftByteCount > 62) {\n            return 21; // B/S with extended length\n        }\n        if (state.binaryShiftByteCount > 31) {\n            return 20; // two B/S\n        }\n        if (state.binaryShiftByteCount > 0) {\n            return 10; // one B/S\n        }\n        return 0;\n    };\n    State.INITIAL_STATE = new State(C.EMPTY_TOKEN, C.MODE_UPPER, 0, 0);\n    return State;\n}());\nexport default State;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA;AACA;AACA,OAAOW,QAAQ,MAAM,uBAAuB;AAC5C,OAAO,KAAKC,YAAY,MAAM,gBAAgB;AAC9C,OAAO,KAAKC,CAAC,MAAM,oBAAoB;AACvC,OAAO,KAAKC,UAAU,MAAM,cAAc;AAC1C,OAAO,KAAKC,UAAU,MAAM,cAAc;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD;AACA;AACA;AACA;AACA,IAAIC,KAAK,GAAG,aAAe,YAAY;EACnC,SAASA,KAAKA,CAACC,KAAK,EAAEC,IAAI,EAAEC,WAAW,EAAEC,QAAQ,EAAE;IAC/C,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,oBAAoB,GAAGF,WAAW;IACvC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;IACA;IACA;IACA;IACA;IACA;EACJ;EACAJ,KAAK,CAACM,SAAS,CAACC,OAAO,GAAG,YAAY;IAClC,OAAO,IAAI,CAACL,IAAI;EACpB,CAAC;EACDF,KAAK,CAACM,SAAS,CAACE,QAAQ,GAAG,YAAY;IACnC,OAAO,IAAI,CAACP,KAAK;EACrB,CAAC;EACDD,KAAK,CAACM,SAAS,CAACG,uBAAuB,GAAG,YAAY;IAClD,OAAO,IAAI,CAACJ,oBAAoB;EACpC,CAAC;EACDL,KAAK,CAACM,SAAS,CAACI,WAAW,GAAG,YAAY;IACtC,OAAO,IAAI,CAACN,QAAQ;EACxB,CAAC;EACD;EACA;EACAJ,KAAK,CAACM,SAAS,CAACK,cAAc,GAAG,UAAUT,IAAI,EAAEX,KAAK,EAAE;IACpD;IACA,IAAIa,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAIH,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;MACpB,IAAIU,KAAK,GAAGf,UAAU,CAACgB,WAAW,CAAC,IAAI,CAACX,IAAI,CAAC,CAACA,IAAI,CAAC;MACnDD,KAAK,GAAGN,YAAY,CAACmB,GAAG,CAACb,KAAK,EAAEW,KAAK,GAAG,MAAM,EAAEA,KAAK,IAAI,EAAE,CAAC;MAC5DR,QAAQ,IAAIQ,KAAK,IAAI,EAAE;IAC3B;IACA,IAAIG,iBAAiB,GAAGb,IAAI,KAAKN,CAAC,CAACoB,UAAU,GAAG,CAAC,GAAG,CAAC;IACrDf,KAAK,GAAGN,YAAY,CAACmB,GAAG,CAACb,KAAK,EAAEV,KAAK,EAAEwB,iBAAiB,CAAC;IACzD,OAAO,IAAIf,KAAK,CAACC,KAAK,EAAEC,IAAI,EAAE,CAAC,EAAEE,QAAQ,GAAGW,iBAAiB,CAAC;EAClE,CAAC;EACD;EACA;EACAf,KAAK,CAACM,SAAS,CAACW,cAAc,GAAG,UAAUf,IAAI,EAAEX,KAAK,EAAE;IACpD;IACA,IAAIU,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIiB,gBAAgB,GAAG,IAAI,CAAChB,IAAI,KAAKN,CAAC,CAACoB,UAAU,GAAG,CAAC,GAAG,CAAC;IACzD;IACAf,KAAK,GAAGN,YAAY,CAACmB,GAAG,CAACb,KAAK,EAAEH,UAAU,CAACqB,WAAW,CAAC,IAAI,CAACjB,IAAI,CAAC,CAACA,IAAI,CAAC,EAAEgB,gBAAgB,CAAC;IAC1FjB,KAAK,GAAGN,YAAY,CAACmB,GAAG,CAACb,KAAK,EAAEV,KAAK,EAAE,CAAC,CAAC;IACzC,OAAO,IAAIS,KAAK,CAACC,KAAK,EAAE,IAAI,CAACC,IAAI,EAAE,CAAC,EAAE,IAAI,CAACE,QAAQ,GAAGc,gBAAgB,GAAG,CAAC,CAAC;EAC/E,CAAC;EACD;EACA;EACAlB,KAAK,CAACM,SAAS,CAACc,kBAAkB,GAAG,UAAUC,KAAK,EAAE;IAClD,IAAIpB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIC,IAAI,GAAG,IAAI,CAACA,IAAI;IACpB,IAAIE,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5B,IAAI,IAAI,CAACF,IAAI,KAAKN,CAAC,CAAC0B,UAAU,IAAI,IAAI,CAACpB,IAAI,KAAKN,CAAC,CAACoB,UAAU,EAAE;MAC1D;MACA,IAAIJ,KAAK,GAAGf,UAAU,CAACgB,WAAW,CAACX,IAAI,CAAC,CAACN,CAAC,CAAC2B,UAAU,CAAC;MACtDtB,KAAK,GAAGN,YAAY,CAACmB,GAAG,CAACb,KAAK,EAAEW,KAAK,GAAG,MAAM,EAAEA,KAAK,IAAI,EAAE,CAAC;MAC5DR,QAAQ,IAAIQ,KAAK,IAAI,EAAE;MACvBV,IAAI,GAAGN,CAAC,CAAC2B,UAAU;IACvB;IACA,IAAIC,aAAa,GAAG,IAAI,CAACnB,oBAAoB,KAAK,CAAC,IAAI,IAAI,CAACA,oBAAoB,KAAK,EAAE,GACjF,EAAE,GACF,IAAI,CAACA,oBAAoB,KAAK,EAAE,GAC5B,CAAC,GACD,CAAC;IACX,IAAIoB,MAAM,GAAG,IAAIzB,KAAK,CAACC,KAAK,EAAEC,IAAI,EAAE,IAAI,CAACG,oBAAoB,GAAG,CAAC,EAAED,QAAQ,GAAGoB,aAAa,CAAC;IAC5F,IAAIC,MAAM,CAACpB,oBAAoB,KAAK,IAAI,GAAG,EAAE,EAAE;MAC3C;MACAoB,MAAM,GAAGA,MAAM,CAACC,cAAc,CAACL,KAAK,GAAG,CAAC,CAAC;IAC7C;IACA,OAAOI,MAAM;EACjB,CAAC;EACD;EACA;EACAzB,KAAK,CAACM,SAAS,CAACoB,cAAc,GAAG,UAAUL,KAAK,EAAE;IAC9C,IAAI,IAAI,CAAChB,oBAAoB,KAAK,CAAC,EAAE;MACjC,OAAO,IAAI;IACf;IACA,IAAIJ,KAAK,GAAG,IAAI,CAACA,KAAK;IACtBA,KAAK,GAAGN,YAAY,CAACgC,cAAc,CAAC1B,KAAK,EAAEoB,KAAK,GAAG,IAAI,CAAChB,oBAAoB,EAAE,IAAI,CAACA,oBAAoB,CAAC;IACxG;IACA,OAAO,IAAIL,KAAK,CAACC,KAAK,EAAE,IAAI,CAACC,IAAI,EAAE,CAAC,EAAE,IAAI,CAACE,QAAQ,CAAC;EACxD,CAAC;EACD;EACA;EACAJ,KAAK,CAACM,SAAS,CAACsB,qBAAqB,GAAG,UAAUC,KAAK,EAAE;IACrD,IAAIC,eAAe,GAAG,IAAI,CAAC1B,QAAQ,IAAIP,UAAU,CAACgB,WAAW,CAAC,IAAI,CAACX,IAAI,CAAC,CAAC2B,KAAK,CAAC3B,IAAI,CAAC,IAAI,EAAE,CAAC;IAC3F,IAAI,IAAI,CAACG,oBAAoB,GAAGwB,KAAK,CAACxB,oBAAoB,EAAE;MACxD;MACAyB,eAAe,IACX9B,KAAK,CAAC+B,wBAAwB,CAACF,KAAK,CAAC,GACjC7B,KAAK,CAAC+B,wBAAwB,CAAC,IAAI,CAAC;IAChD,CAAC,MACI,IAAI,IAAI,CAAC1B,oBAAoB,GAAGwB,KAAK,CAACxB,oBAAoB,IAC3DwB,KAAK,CAACxB,oBAAoB,GAAG,CAAC,EAAE;MAChC;MACAyB,eAAe,IAAI,EAAE;IACzB;IACA,OAAOA,eAAe,IAAID,KAAK,CAACzB,QAAQ;EAC5C,CAAC;EACDJ,KAAK,CAACM,SAAS,CAAC0B,UAAU,GAAG,UAAUC,IAAI,EAAE;IACzC,IAAIC,GAAG,EAAEC,EAAE;IACX;IACA;IACA,IAAIC,OAAO,GAAG,EAAE;IAChB,KAAK,IAAInC,KAAK,GAAG,IAAI,CAACyB,cAAc,CAACO,IAAI,CAAC5C,MAAM,CAAC,CAACY,KAAK,EAAEA,KAAK,KAAK,IAAI,EAAEA,KAAK,GAAGA,KAAK,CAACoC,WAAW,CAAC,CAAC,EAAE;MAClGD,OAAO,CAACE,OAAO,CAACrC,KAAK,CAAC;IAC1B;IACA,IAAIsC,QAAQ,GAAG,IAAI7C,QAAQ,CAAC,CAAC;IAC7B,IAAI;MACA;MACA,KAAK,IAAI8C,SAAS,GAAG3D,QAAQ,CAACuD,OAAO,CAAC,EAAEK,WAAW,GAAGD,SAAS,CAAClD,IAAI,CAAC,CAAC,EAAE,CAACmD,WAAW,CAACjD,IAAI,EAAEiD,WAAW,GAAGD,SAAS,CAAClD,IAAI,CAAC,CAAC,EAAE;QACvH,IAAIoD,MAAM,GAAGD,WAAW,CAAClD,KAAK;QAC9BmD,MAAM,CAACC,QAAQ,CAACJ,QAAQ,EAAEN,IAAI,CAAC;MACnC;IACJ,CAAC,CACD,OAAOW,KAAK,EAAE;MAAEV,GAAG,GAAG;QAAEW,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,WAAW,IAAI,CAACA,WAAW,CAACjD,IAAI,KAAK2C,EAAE,GAAGK,SAAS,CAACM,MAAM,CAAC,EAAEX,EAAE,CAAC/C,IAAI,CAACoD,SAAS,CAAC;MACvF,CAAC,SACO;QAAE,IAAIN,GAAG,EAAE,MAAMA,GAAG,CAACW,KAAK;MAAE;IACxC;IACA;IACA,OAAON,QAAQ;EACnB,CAAC;EACD;AACJ;AACA;EACIvC,KAAK,CAACM,SAAS,CAACyC,QAAQ,GAAG,YAAY;IACnC,OAAOhD,WAAW,CAACiD,MAAM,CAAC,qBAAqB,EAAEpD,CAAC,CAACqD,UAAU,CAAC,IAAI,CAAC/C,IAAI,CAAC,EAAE,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACC,oBAAoB,CAAC;EACvH,CAAC;EACDL,KAAK,CAAC+B,wBAAwB,GAAG,UAAUmB,KAAK,EAAE;IAC9C,IAAIA,KAAK,CAAC7C,oBAAoB,GAAG,EAAE,EAAE;MACjC,OAAO,EAAE,CAAC,CAAC;IACf;IACA,IAAI6C,KAAK,CAAC7C,oBAAoB,GAAG,EAAE,EAAE;MACjC,OAAO,EAAE,CAAC,CAAC;IACf;IACA,IAAI6C,KAAK,CAAC7C,oBAAoB,GAAG,CAAC,EAAE;MAChC,OAAO,EAAE,CAAC,CAAC;IACf;IACA,OAAO,CAAC;EACZ,CAAC;EACDL,KAAK,CAACmD,aAAa,GAAG,IAAInD,KAAK,CAACJ,CAAC,CAACwD,WAAW,EAAExD,CAAC,CAAC2B,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;EAClE,OAAOvB,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}