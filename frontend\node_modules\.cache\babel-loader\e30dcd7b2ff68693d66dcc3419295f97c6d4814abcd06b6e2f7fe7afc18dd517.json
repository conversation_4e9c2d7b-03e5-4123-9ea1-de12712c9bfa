{"ast": null, "code": "var _jsxFileName = \"D:\\\\My Learning Projects\\\\uni-core-business-suite_v3\\\\frontend\\\\src\\\\components\\\\BankPaymentVoucherForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Box, Card, CardContent, Typography, Button, Grid, TextField, FormControl, InputLabel, Select, MenuItem, Alert, Divider, Chip, Autocomplete, FormHelperText } from \"@mui/material\";\nimport { Save as SaveIcon, Check as ApproveIcon, PostAdd as PostIcon, Cancel as CancelIcon } from \"@mui/icons-material\";\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\nimport dayjs from \"dayjs\";\nimport axios from \"../utils/axiosConfig\";\nimport { formatCurrency } from \"../utils/numberUtils\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BankPaymentVoucherForm = ({\n  voucherId,\n  readOnly = false,\n  onSave,\n  onCancel\n}) => {\n  _s();\n  const [voucher, setVoucher] = useState({\n    voucherType: 'BP',\n    // Bank Payment\n    voucherDate: dayjs(),\n    // Voucher creation date\n    transactionDate: dayjs(),\n    // Actual transaction date\n    amount: '',\n    fromAccountId: '',\n    // Bank account (credit)\n    toAccountId: '',\n    // Debit account (vendor, expense, etc.)\n    relatedPartyType: '',\n    relatedPartyId: '',\n    relatedPartyName: '',\n    paymentMethod: 'Bank Transfer',\n    chequeNumber: '',\n    chequeDate: null,\n    bankReference: '',\n    narration: '',\n    description: '',\n    status: 'Draft',\n    referenceDocuments: []\n  });\n  const [accounts, setAccounts] = useState([]);\n  const [bankAccounts, setBankAccounts] = useState([]);\n  const [vendors, setVendors] = useState([]);\n  const [purchaseInvoices, setPurchaseInvoices] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [selectedVendor, setSelectedVendor] = useState(null);\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\n  const paymentMethods = ['Bank Transfer', 'Cheque', 'Online Transfer', 'Card', 'Other'];\n  useEffect(() => {\n    fetchInitialData();\n    if (voucherId) {\n      fetchVoucher();\n    }\n  }, [voucherId]);\n\n  // Fetch vendor's purchase invoices when vendor is selected\n  useEffect(() => {\n    if (selectedVendor) {\n      fetchVendorInvoices(selectedVendor._id);\n    } else {\n      setPurchaseInvoices([]);\n      setSelectedInvoice(null);\n    }\n  }, [selectedVendor]);\n\n  // Update amount when invoice is selected\n  useEffect(() => {\n    if (selectedInvoice) {\n      const remainingAmount = selectedInvoice.remainingAmount;\n\n      // Create a detailed narration\n      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber}`;\n\n      // If there are returns, add details\n      if (selectedInvoice.hasReturns) {\n        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\n        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount)}, Returns: ${formatCurrency(selectedInvoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount)})`;\n      }\n      setVoucher(prev => ({\n        ...prev,\n        amount: remainingAmount.toString(),\n        narration\n      }));\n    }\n  }, [selectedInvoice]);\n\n  // Add error handling for ResizeObserver\n  useEffect(() => {\n    // Suppress ResizeObserver loop error\n    const originalError = window.console.error;\n    window.console.error = (...args) => {\n      var _args$, _args$$includes;\n      if ((_args$ = args[0]) !== null && _args$ !== void 0 && (_args$$includes = _args$.includes) !== null && _args$$includes !== void 0 && _args$$includes.call(_args$, 'ResizeObserver loop')) {\n        // Ignore ResizeObserver loop errors\n        return;\n      }\n      originalError(...args);\n    };\n    return () => {\n      window.console.error = originalError;\n    };\n  }, []);\n  const fetchInitialData = async () => {\n    try {\n      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([axios.get(\"/api/accounts?active=true\"), axios.get(\"/api/vendors\"), axios.get(\"/api/bank-accounts\")]);\n      setAccounts(accountsRes.data);\n      setVendors(vendorsRes.data);\n\n      // Map bank accounts to their corresponding chart of accounts entries\n      const bankAccs = bankAccountsRes.data;\n      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);\n\n      // Get bank account details from chart of accounts\n      if (bankAccountIds.length > 0) {\n        const bankAccountsDetails = accountsRes.data.filter(acc => bankAccountIds.includes(acc._id));\n        setBankAccounts(bankAccountsDetails);\n      }\n    } catch (error) {\n      console.error(\"Error fetching initial data:\", error);\n    }\n  };\n  const fetchVoucher = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);\n      const voucherData = response.data;\n      setVoucher({\n        ...voucherData,\n        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt),\n        transactionDate: dayjs(voucherData.transactionDate),\n        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,\n        fromAccountId: voucherData.fromAccountId._id,\n        toAccountId: voucherData.toAccountId._id\n      });\n\n      // If this is a vendor payment, fetch the vendor and invoice details\n      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {\n        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);\n        setSelectedVendor(vendorRes.data);\n\n        // If there's a reference to a purchase invoice\n        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {\n          const invoiceRef = voucherData.referenceDocuments.find(doc => doc.documentType === 'PurchaseInvoice');\n          if (invoiceRef && invoiceRef.documentId) {\n            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);\n            setSelectedInvoice(invoiceRes.data);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching voucher:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchVendorInvoices = async vendorId => {\n    try {\n      console.log(`Fetching invoices for vendor: ${vendorId}`);\n      // Fetch unpaid or partially paid invoices for this vendor\n      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);\n      console.log('Vendor invoices response:', response.data);\n      if (Array.isArray(response.data) && response.data.length > 0) {\n        setPurchaseInvoices(response.data);\n      } else {\n        console.log('No unpaid invoices found for this vendor');\n        setPurchaseInvoices([]);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error(\"Error fetching vendor invoices:\", error);\n      alert(`Failed to fetch vendor invoices: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message}`);\n      setPurchaseInvoices([]);\n    }\n  };\n  const handleInputChange = (field, value) => {\n    setVoucher(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear error when field is updated\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: null\n      }));\n    }\n  };\n  const handleVendorChange = vendor => {\n    setSelectedVendor(vendor);\n    if (vendor) {\n      // Update voucher with vendor details\n      setVoucher(prev => ({\n        ...prev,\n        relatedPartyType: 'Vendor',\n        relatedPartyId: vendor._id,\n        relatedPartyName: vendor.name,\n        toAccountId: vendor.accountId || '' // Set vendor's account as debit account\n      }));\n    } else {\n      // Clear vendor-related fields\n      setVoucher(prev => ({\n        ...prev,\n        relatedPartyType: '',\n        relatedPartyId: '',\n        relatedPartyName: '',\n        toAccountId: ''\n      }));\n    }\n  };\n  const handleInvoiceChange = invoice => {\n    setSelectedInvoice(invoice);\n    if (invoice) {\n      console.log('Selected invoice:', invoice);\n\n      // Use the adjusted remaining amount that accounts for returns\n      const remainingAmount = invoice.remainingAmount;\n      console.log(`Using adjusted remaining amount: ${remainingAmount}`);\n\n      // Create a detailed narration\n      let narration = `Payment against invoice ${invoice.invoiceNumber}`;\n\n      // If there are returns, add details\n      if (invoice.hasReturns) {\n        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\n        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;\n      }\n\n      // Update amount field with remaining amount\n      setVoucher(prev => ({\n        ...prev,\n        amount: remainingAmount.toString(),\n        narration\n      }));\n\n      // Update reference documents\n      const referenceDoc = {\n        documentType: 'PurchaseInvoice',\n        documentId: invoice._id,\n        documentNumber: invoice.invoiceNumber,\n        allocatedAmount: remainingAmount,\n        originalAmount: invoice.originalAmount,\n        returnAmount: invoice.returnAmount,\n        adjustedAmount: invoice.adjustedAmount\n      };\n      setVoucher(prev => ({\n        ...prev,\n        referenceDocuments: [referenceDoc]\n      }));\n    } else {\n      setVoucher(prev => ({\n        ...prev,\n        referenceDocuments: []\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!voucher.fromAccountId) newErrors.fromAccountId = \"Bank account is required\";\n    if (!voucher.toAccountId) newErrors.toAccountId = \"Debit account is required\";\n    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {\n      newErrors.amount = \"Valid amount is required\";\n    }\n    if (!voucher.transactionDate) newErrors.transactionDate = \"Transaction date is required\";\n    if (!voucher.narration) newErrors.narration = \"Narration is required\";\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSave = async () => {\n    if (!validateForm()) return;\n    try {\n      setLoading(true);\n      const voucherData = {\n        ...voucher,\n        transactionDate: voucher.transactionDate.toISOString(),\n        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,\n        amount: parseFloat(voucher.amount)\n      };\n      let response;\n      if (voucherId) {\n        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);\n      } else {\n        response = await axios.post(\"/api/payment-vouchers\", voucherData);\n      }\n      if (onSave) onSave(response.data.voucher);\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error(\"Error saving voucher:\", error);\n      alert(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || \"Error saving bank payment voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleApprove = async () => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\n      if (onSave) onSave();\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error(\"Error approving voucher:\", error);\n      alert(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || \"Error approving voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handlePost = async () => {\n    try {\n      setLoading(true);\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\n      if (onSave) onSave();\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      console.error(\"Error posting voucher:\", error);\n      alert(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || \"Error posting voucher\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');\n  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';\n  const canPost = !readOnly && voucherId && voucher.status === 'Approved';\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: /*#__PURE__*/_jsxDEV(LocalizationProvider, {\n      dateAdapter: AdapterDayjs,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Bank Payment Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 17\n              }, this), voucherId && /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Voucher No.\",\n                value: voucher.voucherNumber || '',\n                margin: \"normal\",\n                InputProps: {\n                  readOnly: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                label: \"Voucher Date\",\n                value: voucher.voucherDate,\n                onChange: newValue => handleInputChange('voucherDate', newValue),\n                format: \"DD/MMM/YYYY\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  fullWidth: true,\n                  margin: \"normal\",\n                  error: !!errors.voucherDate,\n                  helperText: errors.voucherDate,\n                  InputProps: {\n                    ...params.InputProps,\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 21\n                }, this),\n                readOnly: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                label: \"Transaction Date\",\n                value: voucher.transactionDate,\n                onChange: newValue => handleInputChange('transactionDate', newValue),\n                format: \"DD/MMM/YYYY\",\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  fullWidth: true,\n                  margin: \"normal\",\n                  error: !!errors.transactionDate,\n                  helperText: errors.transactionDate,\n                  InputProps: {\n                    ...params.InputProps,\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 21\n                }, this),\n                readOnly: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Transaction ID / Reference\",\n                value: voucher.bankReference || '',\n                onChange: e => handleInputChange('bankReference', e.target.value),\n                margin: \"normal\",\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.fromAccountId,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Bank Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.fromAccountId,\n                  onChange: e => handleInputChange('fromAccountId', e.target.value),\n                  label: \"Bank Account\",\n                  disabled: !canEdit,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Select Bank Account\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 461,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 21\n                  }, this), bankAccounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: account._id,\n                    children: account.accountName\n                  }, account._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 464,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), errors.fromAccountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.fromAccountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Payment Method\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.paymentMethod,\n                  onChange: e => handleInputChange('paymentMethod', e.target.value),\n                  label: \"Payment Method\",\n                  disabled: !canEdit,\n                  children: paymentMethods.map(method => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: method,\n                    children: method\n                  }, method, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 484,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this), voucher.paymentMethod === 'Cheque' && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Cheque Number\",\n                  value: voucher.chequeNumber || '',\n                  onChange: e => handleInputChange('chequeNumber', e.target.value),\n                  margin: \"normal\",\n                  InputProps: {\n                    readOnly: !canEdit\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                  label: \"Cheque Date\",\n                  value: voucher.chequeDate,\n                  onChange: newValue => handleInputChange('chequeDate', newValue),\n                  renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                    ...params,\n                    fullWidth: true,\n                    margin: \"normal\",\n                    InputProps: {\n                      ...params.InputProps,\n                      readOnly: !canEdit\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 508,\n                    columnNumber: 25\n                  }, this),\n                  readOnly: !canEdit\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 503,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            variant: \"outlined\",\n            sx: {\n              mb: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(CardContent, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                gutterBottom: true,\n                children: \"Payment Details\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: vendors,\n                getOptionLabel: vendor => vendor.name || '',\n                value: selectedVendor,\n                onChange: (event, newValue) => handleVendorChange(newValue),\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Vendor\",\n                  margin: \"normal\",\n                  fullWidth: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 541,\n                  columnNumber: 21\n                }, this),\n                disabled: !canEdit\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 535,\n                columnNumber: 17\n              }, this), selectedVendor && /*#__PURE__*/_jsxDEV(Autocomplete, {\n                options: purchaseInvoices,\n                getOptionLabel: invoice => {\n                  if (!invoice) return '';\n                  const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;\n\n                  // If there are returns, show the adjusted amount\n                  if (invoice.hasReturns) {\n                    return `${invoiceLabel} - ${formatCurrency(invoice.remainingAmount)} (Adjusted for returns)`;\n                  }\n\n                  // Otherwise just show the remaining amount\n                  return `${invoiceLabel} - ${formatCurrency(invoice.remainingAmount)} outstanding`;\n                },\n                value: selectedInvoice,\n                onChange: (event, newValue) => handleInvoiceChange(newValue),\n                renderOption: (props, invoice) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  ...props,\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      width: '100%',\n                      maxWidth: '100%',\n                      overflow: 'hidden'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      fontWeight: \"medium\",\n                      noWrap: true,\n                      children: [invoice.invoiceNumber, \" - \", formatCurrency(invoice.remainingAmount), \" outstanding\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 573,\n                      columnNumber: 27\n                    }, this), invoice.hasReturns && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"text.secondary\",\n                        display: \"block\",\n                        noWrap: true,\n                        children: [\"Original: \", formatCurrency(invoice.originalAmount), \" | Returns: \", formatCurrency(invoice.returnAmount), \" | Adjusted: \", formatCurrency(invoice.adjustedAmount)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 578,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        color: \"primary\",\n                        display: \"block\",\n                        noWrap: true,\n                        children: [invoice.returnDetails.length, \" return(s) applied -\", invoice.returnDetails.map((ret, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: [ret.returnNumber, \" (\", ret.status, \")\", idx < invoice.returnDetails.length - 1 ? ', ' : '']\n                        }, idx, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 586,\n                          columnNumber: 35\n                        }, this))]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 583,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 572,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 571,\n                  columnNumber: 23\n                }, this),\n                renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n                  ...params,\n                  label: \"Purchase Invoice\",\n                  margin: \"normal\",\n                  fullWidth: true,\n                  error: purchaseInvoices.length === 0,\n                  helperText: purchaseInvoices.length === 0 ? \"No unpaid invoices found for this vendor\" : \"\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 23\n                }, this),\n                disabled: !canEdit || purchaseInvoices.length === 0,\n                noOptionsText: \"No unpaid invoices found\",\n                ListboxProps: {\n                  style: {\n                    maxHeight: '200px'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 19\n              }, this), !selectedVendor && /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                margin: \"normal\",\n                error: !!errors.toAccountId,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"Debit Account\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 622,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: voucher.toAccountId,\n                  onChange: e => handleInputChange('toAccountId', e.target.value),\n                  label: \"Debit Account\",\n                  disabled: !canEdit,\n                  children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: \"\",\n                    children: /*#__PURE__*/_jsxDEV(\"em\", {\n                      children: \"Select Account\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 630,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 629,\n                    columnNumber: 23\n                  }, this), accounts.map(account => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: account._id,\n                    children: [account.accountCode, \" - \", account.accountName]\n                  }, account._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 633,\n                    columnNumber: 25\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 623,\n                  columnNumber: 21\n                }, this), errors.toAccountId && /*#__PURE__*/_jsxDEV(FormHelperText, {\n                  children: errors.toAccountId\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 617,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Amount\",\n                type: \"number\",\n                value: voucher.amount,\n                onChange: e => handleInputChange('amount', e.target.value),\n                margin: \"normal\",\n                error: !!errors.amount,\n                helperText: errors.amount,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 645,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Narration\",\n                value: voucher.narration,\n                onChange: e => handleInputChange('narration', e.target.value),\n                margin: \"normal\",\n                multiline: true,\n                rows: 2,\n                error: !!errors.narration,\n                helperText: errors.narration,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 658,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"Description\",\n                value: voucher.description || '',\n                onChange: e => handleInputChange('description', e.target.value),\n                margin: \"normal\",\n                multiline: true,\n                rows: 2,\n                InputProps: {\n                  readOnly: !canEdit\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 672,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              gap: 2,\n              mt: 2\n            },\n            children: [onCancel && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outlined\",\n              color: \"secondary\",\n              startIcon: /*#__PURE__*/_jsxDEV(CancelIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 30\n              }, this),\n              onClick: onCancel,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 17\n            }, this), canEdit && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"primary\",\n              startIcon: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 704,\n                columnNumber: 30\n              }, this),\n              onClick: handleSave,\n              disabled: loading,\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 701,\n              columnNumber: 17\n            }, this), canApprove && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"warning\",\n              startIcon: /*#__PURE__*/_jsxDEV(ApproveIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 716,\n                columnNumber: 30\n              }, this),\n              onClick: handleApprove,\n              disabled: loading,\n              children: \"Approve\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 17\n            }, this), canPost && /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"success\",\n              startIcon: /*#__PURE__*/_jsxDEV(PostIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 30\n              }, this),\n              onClick: handlePost,\n              disabled: loading,\n              children: \"Post to Ledger\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 725,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 687,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 371,\n    columnNumber: 5\n  }, this);\n};\n_s(BankPaymentVoucherForm, \"buRCZTx5Ubo79tZQuzwSeBiGLKU=\");\n_c = BankPaymentVoucherForm;\nexport default BankPaymentVoucherForm;\nvar _c;\n$RefreshReg$(_c, \"BankPaymentVoucherForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Grid", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "<PERSON><PERSON>", "Divider", "Chip", "Autocomplete", "FormHelperText", "Save", "SaveIcon", "Check", "ApproveIcon", "PostAdd", "PostIcon", "Cancel", "CancelIcon", "DatePicker", "LocalizationProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayjs", "axios", "formatCurrency", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BankPaymentVoucherForm", "voucherId", "readOnly", "onSave", "onCancel", "_s", "voucher", "setVoucher", "voucherType", "voucherDate", "transactionDate", "amount", "fromAccountId", "toAccountId", "relatedPartyType", "relatedPartyId", "relatedPartyName", "paymentMethod", "chequeNumber", "chequeDate", "bankReference", "narration", "description", "status", "referenceDocuments", "accounts", "setAccounts", "bankAccounts", "setBankAccounts", "vendors", "setVendors", "purchaseInvoices", "setPurchaseInvoices", "loading", "setLoading", "errors", "setErrors", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVendor", "selectedInvoice", "setSelectedInvoice", "paymentMethods", "fetchInitialData", "fetchVoucher", "fetchVendorInvoices", "_id", "remainingAmount", "invoiceNumber", "hasReturns", "returnStatuses", "returnDetails", "map", "ret", "returnNumber", "join", "originalAmount", "returnAmount", "adjustedAmount", "prev", "toString", "originalError", "window", "console", "error", "args", "_args$", "_args$$includes", "includes", "call", "accountsRes", "vendorsRes", "bankAccountsRes", "Promise", "all", "get", "data", "bankAccs", "bankAccountIds", "bank", "accountId", "filter", "id", "length", "bankAccountsDetails", "acc", "response", "voucherData", "createdAt", "vendorRes", "invoiceRef", "find", "doc", "documentType", "documentId", "invoiceRes", "vendorId", "log", "Array", "isArray", "_error$response", "_error$response$data", "alert", "message", "handleInputChange", "field", "value", "handleVendorChange", "vendor", "name", "handleInvoiceChange", "invoice", "referenceDoc", "documentNumber", "allocatedAmount", "validateForm", "newErrors", "isNaN", "parseFloat", "Object", "keys", "handleSave", "toISOString", "put", "post", "_error$response2", "_error$response2$data", "handleApprove", "_error$response3", "_error$response3$data", "handlePost", "_error$response4", "_error$response4$data", "canEdit", "canApprove", "canPost", "children", "dateAdapter", "container", "spacing", "item", "xs", "md", "variant", "sx", "mb", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "label", "voucherNumber", "margin", "InputProps", "onChange", "newValue", "format", "renderInput", "params", "helperText", "e", "target", "disabled", "account", "accountName", "method", "options", "getOptionLabel", "event", "invoiceLabel", "renderOption", "props", "width", "max<PERSON><PERSON><PERSON>", "overflow", "fontWeight", "noWrap", "color", "display", "idx", "noOptionsText", "ListboxProps", "style", "maxHeight", "accountCode", "type", "multiline", "rows", "justifyContent", "gap", "mt", "startIcon", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/components/BankPaymentVoucherForm.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Box,\r\n  Card,\r\n  CardContent,\r\n  Typography,\r\n  Button,\r\n  Grid,\r\n  TextField,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Alert,\r\n  Divider,\r\n  Chip,\r\n  Autocomplete,\r\n  FormHelperText\r\n} from \"@mui/material\";\r\nimport {\r\n  Save as SaveIcon,\r\n  Check as ApproveIcon,\r\n  PostAdd as PostIcon,\r\n  Cancel as CancelIcon\r\n} from \"@mui/icons-material\";\r\nimport { DatePicker } from \"@mui/x-date-pickers/DatePicker\";\r\nimport { LocalizationProvider } from \"@mui/x-date-pickers/LocalizationProvider\";\r\nimport { AdapterDayjs } from \"@mui/x-date-pickers/AdapterDayjs\";\r\nimport dayjs from \"dayjs\";\r\nimport axios from \"../utils/axiosConfig\";\r\nimport { formatCurrency } from \"../utils/numberUtils\";\r\n\r\nconst BankPaymentVoucherForm = ({ voucherId, readOnly = false, onSave, onCancel }) => {\r\n  const [voucher, setVoucher] = useState({\r\n    voucherType: 'BP', // Bank Payment\r\n    voucherDate: dayjs(), // Voucher creation date\r\n    transactionDate: dayjs(), // Actual transaction date\r\n    amount: '',\r\n    fromAccountId: '', // Bank account (credit)\r\n    toAccountId: '', // Debit account (vendor, expense, etc.)\r\n    relatedPartyType: '',\r\n    relatedPartyId: '',\r\n    relatedPartyName: '',\r\n    paymentMethod: 'Bank Transfer',\r\n    chequeNumber: '',\r\n    chequeDate: null,\r\n    bankReference: '',\r\n    narration: '',\r\n    description: '',\r\n    status: 'Draft',\r\n    referenceDocuments: []\r\n  });\r\n\r\n  const [accounts, setAccounts] = useState([]);\r\n  const [bankAccounts, setBankAccounts] = useState([]);\r\n  const [vendors, setVendors] = useState([]);\r\n  const [purchaseInvoices, setPurchaseInvoices] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [errors, setErrors] = useState({});\r\n  const [selectedVendor, setSelectedVendor] = useState(null);\r\n  const [selectedInvoice, setSelectedInvoice] = useState(null);\r\n\r\n  const paymentMethods = [\r\n    'Bank Transfer',\r\n    'Cheque',\r\n    'Online Transfer',\r\n    'Card',\r\n    'Other'\r\n  ];\r\n\r\n  useEffect(() => {\r\n    fetchInitialData();\r\n    if (voucherId) {\r\n      fetchVoucher();\r\n    }\r\n  }, [voucherId]);\r\n\r\n  // Fetch vendor's purchase invoices when vendor is selected\r\n  useEffect(() => {\r\n    if (selectedVendor) {\r\n      fetchVendorInvoices(selectedVendor._id);\r\n    } else {\r\n      setPurchaseInvoices([]);\r\n      setSelectedInvoice(null);\r\n    }\r\n  }, [selectedVendor]);\r\n\r\n  // Update amount when invoice is selected\r\n  useEffect(() => {\r\n    if (selectedInvoice) {\r\n      const remainingAmount = selectedInvoice.remainingAmount;\r\n      \r\n      // Create a detailed narration\r\n      let narration = `Payment against invoice ${selectedInvoice.invoiceNumber}`;\r\n      \r\n      // If there are returns, add details\r\n      if (selectedInvoice.hasReturns) {\r\n        const returnStatuses = selectedInvoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\r\n        narration = `Payment against invoice ${selectedInvoice.invoiceNumber} (Original: ${formatCurrency(selectedInvoice.originalAmount)}, Returns: ${formatCurrency(selectedInvoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(selectedInvoice.adjustedAmount)})`;\r\n      }\r\n      \r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        amount: remainingAmount.toString(),\r\n        narration\r\n      }));\r\n    }\r\n  }, [selectedInvoice]);\r\n\r\n  // Add error handling for ResizeObserver\r\n  useEffect(() => {\r\n    // Suppress ResizeObserver loop error\r\n    const originalError = window.console.error;\r\n    window.console.error = (...args) => {\r\n      if (args[0]?.includes?.('ResizeObserver loop')) {\r\n        // Ignore ResizeObserver loop errors\r\n        return;\r\n      }\r\n      originalError(...args);\r\n    };\r\n\r\n    return () => {\r\n      window.console.error = originalError;\r\n    };\r\n  }, []);\r\n\r\n  const fetchInitialData = async () => {\r\n    try {\r\n      const [accountsRes, vendorsRes, bankAccountsRes] = await Promise.all([\r\n        axios.get(\"/api/accounts?active=true\"),\r\n        axios.get(\"/api/vendors\"),\r\n        axios.get(\"/api/bank-accounts\")\r\n      ]);\r\n\r\n      setAccounts(accountsRes.data);\r\n      setVendors(vendorsRes.data);\r\n      \r\n      // Map bank accounts to their corresponding chart of accounts entries\r\n      const bankAccs = bankAccountsRes.data;\r\n      const bankAccountIds = bankAccs.map(bank => bank.accountId).filter(id => id);\r\n      \r\n      // Get bank account details from chart of accounts\r\n      if (bankAccountIds.length > 0) {\r\n        const bankAccountsDetails = accountsRes.data.filter(acc => \r\n          bankAccountIds.includes(acc._id)\r\n        );\r\n        setBankAccounts(bankAccountsDetails);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching initial data:\", error);\r\n    }\r\n  };\r\n\r\n  const fetchVoucher = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await axios.get(`/api/payment-vouchers/${voucherId}`);\r\n      const voucherData = response.data;\r\n      \r\n      setVoucher({\r\n        ...voucherData,\r\n        voucherDate: voucherData.voucherDate ? dayjs(voucherData.voucherDate) : dayjs(voucherData.createdAt),\r\n        transactionDate: dayjs(voucherData.transactionDate),\r\n        chequeDate: voucherData.chequeDate ? dayjs(voucherData.chequeDate) : null,\r\n        fromAccountId: voucherData.fromAccountId._id,\r\n        toAccountId: voucherData.toAccountId._id\r\n      });\r\n\r\n      // If this is a vendor payment, fetch the vendor and invoice details\r\n      if (voucherData.relatedPartyType === 'Vendor' && voucherData.relatedPartyId) {\r\n        const vendorRes = await axios.get(`/api/vendors/${voucherData.relatedPartyId}`);\r\n        setSelectedVendor(vendorRes.data);\r\n        \r\n        // If there's a reference to a purchase invoice\r\n        if (voucherData.referenceDocuments && voucherData.referenceDocuments.length > 0) {\r\n          const invoiceRef = voucherData.referenceDocuments.find(doc => \r\n            doc.documentType === 'PurchaseInvoice'\r\n          );\r\n          \r\n          if (invoiceRef && invoiceRef.documentId) {\r\n            const invoiceRes = await axios.get(`/api/purchase-invoices/${invoiceRef.documentId}`);\r\n            setSelectedInvoice(invoiceRes.data);\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching voucher:\", error);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchVendorInvoices = async (vendorId) => {\r\n    try {\r\n      console.log(`Fetching invoices for vendor: ${vendorId}`);\r\n      // Fetch unpaid or partially paid invoices for this vendor\r\n      const response = await axios.get(`/api/payment-vouchers/vendor-invoices/${vendorId}`);\r\n      console.log('Vendor invoices response:', response.data);\r\n      \r\n      if (Array.isArray(response.data) && response.data.length > 0) {\r\n        setPurchaseInvoices(response.data);\r\n      } else {\r\n        console.log('No unpaid invoices found for this vendor');\r\n        setPurchaseInvoices([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching vendor invoices:\", error);\r\n      alert(`Failed to fetch vendor invoices: ${error.response?.data?.message || error.message}`);\r\n      setPurchaseInvoices([]);\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (field, value) => {\r\n    setVoucher(prev => ({ ...prev, [field]: value }));\r\n    \r\n    // Clear error when field is updated\r\n    if (errors[field]) {\r\n      setErrors(prev => ({ ...prev, [field]: null }));\r\n    }\r\n  };\r\n\r\n  const handleVendorChange = (vendor) => {\r\n    setSelectedVendor(vendor);\r\n    \r\n    if (vendor) {\r\n      // Update voucher with vendor details\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        relatedPartyType: 'Vendor',\r\n        relatedPartyId: vendor._id,\r\n        relatedPartyName: vendor.name,\r\n        toAccountId: vendor.accountId || '', // Set vendor's account as debit account\r\n      }));\r\n    } else {\r\n      // Clear vendor-related fields\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        relatedPartyType: '',\r\n        relatedPartyId: '',\r\n        relatedPartyName: '',\r\n        toAccountId: ''\r\n      }));\r\n    }\r\n  };\r\n\r\n  const handleInvoiceChange = (invoice) => {\r\n    setSelectedInvoice(invoice);\r\n    \r\n    if (invoice) {\r\n      console.log('Selected invoice:', invoice);\r\n      \r\n      // Use the adjusted remaining amount that accounts for returns\r\n      const remainingAmount = invoice.remainingAmount;\r\n      \r\n      console.log(`Using adjusted remaining amount: ${remainingAmount}`);\r\n      \r\n      // Create a detailed narration\r\n      let narration = `Payment against invoice ${invoice.invoiceNumber}`;\r\n      \r\n      // If there are returns, add details\r\n      if (invoice.hasReturns) {\r\n        const returnStatuses = invoice.returnDetails.map(ret => `${ret.returnNumber} (${ret.status})`).join(', ');\r\n        narration = `Payment against invoice ${invoice.invoiceNumber} (Original: ${formatCurrency(invoice.originalAmount)}, Returns: ${formatCurrency(invoice.returnAmount)} [${returnStatuses}], Adjusted: ${formatCurrency(invoice.adjustedAmount)})`;\r\n      }\r\n      \r\n      // Update amount field with remaining amount\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        amount: remainingAmount.toString(),\r\n        narration\r\n      }));\r\n      \r\n      // Update reference documents\r\n      const referenceDoc = {\r\n        documentType: 'PurchaseInvoice',\r\n        documentId: invoice._id,\r\n        documentNumber: invoice.invoiceNumber,\r\n        allocatedAmount: remainingAmount,\r\n        originalAmount: invoice.originalAmount,\r\n        returnAmount: invoice.returnAmount,\r\n        adjustedAmount: invoice.adjustedAmount\r\n      };\r\n      \r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        referenceDocuments: [referenceDoc]\r\n      }));\r\n    } else {\r\n      setVoucher(prev => ({\r\n        ...prev,\r\n        referenceDocuments: []\r\n      }));\r\n    }\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n    \r\n    if (!voucher.fromAccountId) newErrors.fromAccountId = \"Bank account is required\";\r\n    if (!voucher.toAccountId) newErrors.toAccountId = \"Debit account is required\";\r\n    if (!voucher.amount || isNaN(parseFloat(voucher.amount)) || parseFloat(voucher.amount) <= 0) {\r\n      newErrors.amount = \"Valid amount is required\";\r\n    }\r\n    if (!voucher.transactionDate) newErrors.transactionDate = \"Transaction date is required\";\r\n    if (!voucher.narration) newErrors.narration = \"Narration is required\";\r\n    \r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSave = async () => {\r\n    if (!validateForm()) return;\r\n    \r\n    try {\r\n      setLoading(true);\r\n      \r\n      const voucherData = {\r\n        ...voucher,\r\n        transactionDate: voucher.transactionDate.toISOString(),\r\n        chequeDate: voucher.chequeDate ? voucher.chequeDate.toISOString() : null,\r\n        amount: parseFloat(voucher.amount)\r\n      };\r\n\r\n      let response;\r\n      if (voucherId) {\r\n        response = await axios.put(`/api/payment-vouchers/${voucherId}`, voucherData);\r\n      } else {\r\n        response = await axios.post(\"/api/payment-vouchers\", voucherData);\r\n      }\r\n\r\n      if (onSave) onSave(response.data.voucher);\r\n    } catch (error) {\r\n      console.error(\"Error saving voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error saving bank payment voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleApprove = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/approve`);\r\n      if (onSave) onSave();\r\n    } catch (error) {\r\n      console.error(\"Error approving voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error approving voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePost = async () => {\r\n    try {\r\n      setLoading(true);\r\n      await axios.put(`/api/payment-vouchers/${voucherId}/post`);\r\n      if (onSave) onSave();\r\n    } catch (error) {\r\n      console.error(\"Error posting voucher:\", error);\r\n      alert(error.response?.data?.message || \"Error posting voucher\");\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const canEdit = !readOnly && (!voucherId || voucher.status === 'Draft');\r\n  const canApprove = !readOnly && voucherId && voucher.status === 'Draft';\r\n  const canPost = !readOnly && voucherId && voucher.status === 'Approved';\r\n\r\n  return (\r\n    <Box>\r\n      <LocalizationProvider dateAdapter={AdapterDayjs}>\r\n        <Grid container spacing={3}>\r\n          {/* Left Column */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\r\n              <CardContent>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Bank Payment Details\r\n                </Typography>\r\n                \r\n                {/* Voucher Number (shown only for existing vouchers) */}\r\n                {voucherId && (\r\n                  <TextField\r\n                    fullWidth\r\n                    label=\"Voucher No.\"\r\n                    value={voucher.voucherNumber || ''}\r\n                    margin=\"normal\"\r\n                    InputProps={{ readOnly: true }}\r\n                  />\r\n                )}\r\n\r\n                {/* Voucher Date */}\r\n                <DatePicker\r\n                  label=\"Voucher Date\"\r\n                  value={voucher.voucherDate}\r\n                  onChange={(newValue) => handleInputChange('voucherDate', newValue)}\r\n                  format=\"DD/MMM/YYYY\"\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      fullWidth\r\n                      margin=\"normal\"\r\n                      error={!!errors.voucherDate}\r\n                      helperText={errors.voucherDate}\r\n                      InputProps={{\r\n                        ...params.InputProps,\r\n                        readOnly: !canEdit\r\n                      }}\r\n                    />\r\n                  )}\r\n                  readOnly={!canEdit}\r\n                />\r\n\r\n                {/* Transaction Date */}\r\n                <DatePicker\r\n                  label=\"Transaction Date\"\r\n                  value={voucher.transactionDate}\r\n                  onChange={(newValue) => handleInputChange('transactionDate', newValue)}\r\n                  format=\"DD/MMM/YYYY\"\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      fullWidth\r\n                      margin=\"normal\"\r\n                      error={!!errors.transactionDate}\r\n                      helperText={errors.transactionDate}\r\n                      InputProps={{\r\n                        ...params.InputProps,\r\n                        readOnly: !canEdit\r\n                      }}\r\n                    />\r\n                  )}\r\n                  readOnly={!canEdit}\r\n                />\r\n                \r\n                {/* Transaction ID / Reference */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Transaction ID / Reference\"\r\n                  value={voucher.bankReference || ''}\r\n                  onChange={(e) => handleInputChange('bankReference', e.target.value)}\r\n                  margin=\"normal\"\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n                \r\n                {/* Bank Account (Credit Account) */}\r\n                <FormControl \r\n                  fullWidth \r\n                  margin=\"normal\"\r\n                  error={!!errors.fromAccountId}\r\n                >\r\n                  <InputLabel>Bank Account</InputLabel>\r\n                  <Select\r\n                    value={voucher.fromAccountId}\r\n                    onChange={(e) => handleInputChange('fromAccountId', e.target.value)}\r\n                    label=\"Bank Account\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    <MenuItem value=\"\">\r\n                      <em>Select Bank Account</em>\r\n                    </MenuItem>\r\n                    {bankAccounts.map((account) => (\r\n                      <MenuItem key={account._id} value={account._id}>\r\n                        {account.accountName}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                  {errors.fromAccountId && (\r\n                    <FormHelperText>{errors.fromAccountId}</FormHelperText>\r\n                  )}\r\n                </FormControl>\r\n                \r\n                {/* Payment Method */}\r\n                <FormControl fullWidth margin=\"normal\">\r\n                  <InputLabel>Payment Method</InputLabel>\r\n                  <Select\r\n                    value={voucher.paymentMethod}\r\n                    onChange={(e) => handleInputChange('paymentMethod', e.target.value)}\r\n                    label=\"Payment Method\"\r\n                    disabled={!canEdit}\r\n                  >\r\n                    {paymentMethods.map((method) => (\r\n                      <MenuItem key={method} value={method}>\r\n                        {method}\r\n                      </MenuItem>\r\n                    ))}\r\n                  </Select>\r\n                </FormControl>\r\n                \r\n                {/* Cheque Details (shown only for cheque payments) */}\r\n                {voucher.paymentMethod === 'Cheque' && (\r\n                  <>\r\n                    <TextField\r\n                      fullWidth\r\n                      label=\"Cheque Number\"\r\n                      value={voucher.chequeNumber || ''}\r\n                      onChange={(e) => handleInputChange('chequeNumber', e.target.value)}\r\n                      margin=\"normal\"\r\n                      InputProps={{ readOnly: !canEdit }}\r\n                    />\r\n                    \r\n                    <DatePicker\r\n                      label=\"Cheque Date\"\r\n                      value={voucher.chequeDate}\r\n                      onChange={(newValue) => handleInputChange('chequeDate', newValue)}\r\n                      renderInput={(params) => (\r\n                        <TextField\r\n                          {...params}\r\n                          fullWidth\r\n                          margin=\"normal\"\r\n                          InputProps={{\r\n                            ...params.InputProps,\r\n                            readOnly: !canEdit\r\n                          }}\r\n                        />\r\n                      )}\r\n                      readOnly={!canEdit}\r\n                    />\r\n                  </>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          \r\n          {/* Right Column */}\r\n          <Grid item xs={12} md={6}>\r\n            <Card variant=\"outlined\" sx={{ mb: 2 }}>\r\n              <CardContent>\r\n                <Typography variant=\"h6\" gutterBottom>\r\n                  Payment Details\r\n                </Typography>\r\n                \r\n                {/* Vendor Selection */}\r\n                <Autocomplete\r\n                  options={vendors}\r\n                  getOptionLabel={(vendor) => vendor.name || ''}\r\n                  value={selectedVendor}\r\n                  onChange={(event, newValue) => handleVendorChange(newValue)}\r\n                  renderInput={(params) => (\r\n                    <TextField\r\n                      {...params}\r\n                      label=\"Vendor\"\r\n                      margin=\"normal\"\r\n                      fullWidth\r\n                    />\r\n                  )}\r\n                  disabled={!canEdit}\r\n                />\r\n                \r\n                {/* Purchase Invoice Selection (shown only if vendor is selected) */}\r\n                {selectedVendor && (\r\n                  <Autocomplete\r\n                    options={purchaseInvoices}\r\n                    getOptionLabel={(invoice) => {\r\n                      if (!invoice) return '';\r\n                      \r\n                      const invoiceLabel = `${invoice.invoiceNumber || 'Unknown'}`;\r\n                      \r\n                      // If there are returns, show the adjusted amount\r\n                      if (invoice.hasReturns) {\r\n                        return `${invoiceLabel} - ${formatCurrency(invoice.remainingAmount)} (Adjusted for returns)`;\r\n                      }\r\n                      \r\n                      // Otherwise just show the remaining amount\r\n                      return `${invoiceLabel} - ${formatCurrency(invoice.remainingAmount)} outstanding`;\r\n                    }}\r\n                    value={selectedInvoice}\r\n                    onChange={(event, newValue) => handleInvoiceChange(newValue)}\r\n                    renderOption={(props, invoice) => (\r\n                      <li {...props}>\r\n                        <Box sx={{ width: '100%', maxWidth: '100%', overflow: 'hidden' }}>\r\n                          <Typography variant=\"body1\" fontWeight=\"medium\" noWrap>\r\n                            {invoice.invoiceNumber} - {formatCurrency(invoice.remainingAmount)} outstanding\r\n                          </Typography>\r\n                          {invoice.hasReturns && (\r\n                            <>\r\n                              <Typography variant=\"caption\" color=\"text.secondary\" display=\"block\" noWrap>\r\n                                Original: {formatCurrency(invoice.originalAmount)} | \r\n                                Returns: {formatCurrency(invoice.returnAmount)} | \r\n                                Adjusted: {formatCurrency(invoice.adjustedAmount)}\r\n                              </Typography>\r\n                              <Typography variant=\"caption\" color=\"primary\" display=\"block\" noWrap>\r\n                                {invoice.returnDetails.length} return(s) applied - \r\n                                {invoice.returnDetails.map((ret, idx) => (\r\n                                  <span key={idx}>\r\n                                    {ret.returnNumber} ({ret.status})\r\n                                    {idx < invoice.returnDetails.length - 1 ? ', ' : ''}\r\n                                  </span>\r\n                                ))}\r\n                              </Typography>\r\n                            </>\r\n                          )}\r\n                        </Box>\r\n                      </li>\r\n                    )}\r\n                    renderInput={(params) => (\r\n                      <TextField\r\n                        {...params}\r\n                        label=\"Purchase Invoice\"\r\n                        margin=\"normal\"\r\n                        fullWidth\r\n                        error={purchaseInvoices.length === 0}\r\n                        helperText={purchaseInvoices.length === 0 ? \"No unpaid invoices found for this vendor\" : \"\"}\r\n                      />\r\n                    )}\r\n                    disabled={!canEdit || purchaseInvoices.length === 0}\r\n                    noOptionsText=\"No unpaid invoices found\"\r\n                    ListboxProps={{\r\n                      style: { maxHeight: '200px' }\r\n                    }}\r\n                  />\r\n                )}\r\n                \r\n                {/* Debit Account (shown if no vendor is selected) */}\r\n                {!selectedVendor && (\r\n                  <FormControl \r\n                    fullWidth \r\n                    margin=\"normal\"\r\n                    error={!!errors.toAccountId}\r\n                  >\r\n                    <InputLabel>Debit Account</InputLabel>\r\n                    <Select\r\n                      value={voucher.toAccountId}\r\n                      onChange={(e) => handleInputChange('toAccountId', e.target.value)}\r\n                      label=\"Debit Account\"\r\n                      disabled={!canEdit}\r\n                    >\r\n                      <MenuItem value=\"\">\r\n                        <em>Select Account</em>\r\n                      </MenuItem>\r\n                      {accounts.map((account) => (\r\n                        <MenuItem key={account._id} value={account._id}>\r\n                          {account.accountCode} - {account.accountName}\r\n                        </MenuItem>\r\n                      ))}\r\n                    </Select>\r\n                    {errors.toAccountId && (\r\n                      <FormHelperText>{errors.toAccountId}</FormHelperText>\r\n                    )}\r\n                  </FormControl>\r\n                )}\r\n                \r\n                {/* Amount */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Amount\"\r\n                  type=\"number\"\r\n                  value={voucher.amount}\r\n                  onChange={(e) => handleInputChange('amount', e.target.value)}\r\n                  margin=\"normal\"\r\n                  error={!!errors.amount}\r\n                  helperText={errors.amount}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n                \r\n                {/* Narration */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Narration\"\r\n                  value={voucher.narration}\r\n                  onChange={(e) => handleInputChange('narration', e.target.value)}\r\n                  margin=\"normal\"\r\n                  multiline\r\n                  rows={2}\r\n                  error={!!errors.narration}\r\n                  helperText={errors.narration}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n                \r\n                {/* Description */}\r\n                <TextField\r\n                  fullWidth\r\n                  label=\"Description\"\r\n                  value={voucher.description || ''}\r\n                  onChange={(e) => handleInputChange('description', e.target.value)}\r\n                  margin=\"normal\"\r\n                  multiline\r\n                  rows={2}\r\n                  InputProps={{ readOnly: !canEdit }}\r\n                />\r\n              </CardContent>\r\n            </Card>\r\n          </Grid>\r\n          \r\n          {/* Action Buttons */}\r\n          <Grid item xs={12}>\r\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2, mt: 2 }}>\r\n              {onCancel && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  color=\"secondary\"\r\n                  startIcon={<CancelIcon />}\r\n                  onClick={onCancel}\r\n                >\r\n                  Cancel\r\n                </Button>\r\n              )}\r\n              \r\n              {canEdit && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  startIcon={<SaveIcon />}\r\n                  onClick={handleSave}\r\n                  disabled={loading}\r\n                >\r\n                  Save\r\n                </Button>\r\n              )}\r\n              \r\n              {canApprove && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"warning\"\r\n                  startIcon={<ApproveIcon />}\r\n                  onClick={handleApprove}\r\n                  disabled={loading}\r\n                >\r\n                  Approve\r\n                </Button>\r\n              )}\r\n              \r\n              {canPost && (\r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"success\"\r\n                  startIcon={<PostIcon />}\r\n                  onClick={handlePost}\r\n                  disabled={loading}\r\n                >\r\n                  Post to Ledger\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          </Grid>\r\n        </Grid>\r\n      </LocalizationProvider>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default BankPaymentVoucherForm; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,YAAY,EACZC,cAAc,QACT,eAAe;AACtB,SACEC,IAAI,IAAIC,QAAQ,EAChBC,KAAK,IAAIC,WAAW,EACpBC,OAAO,IAAIC,QAAQ,EACnBC,MAAM,IAAIC,UAAU,QACf,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,gCAAgC;AAC3D,SAASC,oBAAoB,QAAQ,0CAA0C;AAC/E,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,cAAc,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAMC,sBAAsB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,QAAQ,GAAG,KAAK;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC;IACrC4C,WAAW,EAAE,IAAI;IAAE;IACnBC,WAAW,EAAEhB,KAAK,CAAC,CAAC;IAAE;IACtBiB,eAAe,EAAEjB,KAAK,CAAC,CAAC;IAAE;IAC1BkB,MAAM,EAAE,EAAE;IACVC,aAAa,EAAE,EAAE;IAAE;IACnBC,WAAW,EAAE,EAAE;IAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpBC,aAAa,EAAE,eAAe;IAC9BC,YAAY,EAAE,EAAE;IAChBC,UAAU,EAAE,IAAI;IAChBC,aAAa,EAAE,EAAE;IACjBC,SAAS,EAAE,EAAE;IACbC,WAAW,EAAE,EAAE;IACfC,MAAM,EAAE,OAAO;IACfC,kBAAkB,EAAE;EACtB,CAAC,CAAC;EAEF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiE,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACqE,OAAO,EAAEC,UAAU,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuE,MAAM,EAAEC,SAAS,CAAC,GAAGxE,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACyE,cAAc,EAAEC,iBAAiB,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EAE5D,MAAM6E,cAAc,GAAG,CACrB,eAAe,EACf,QAAQ,EACR,iBAAiB,EACjB,MAAM,EACN,OAAO,CACR;EAED5E,SAAS,CAAC,MAAM;IACd6E,gBAAgB,CAAC,CAAC;IAClB,IAAIzC,SAAS,EAAE;MACb0C,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAC1C,SAAS,CAAC,CAAC;;EAEf;EACApC,SAAS,CAAC,MAAM;IACd,IAAIwE,cAAc,EAAE;MAClBO,mBAAmB,CAACP,cAAc,CAACQ,GAAG,CAAC;IACzC,CAAC,MAAM;MACLb,mBAAmB,CAAC,EAAE,CAAC;MACvBQ,kBAAkB,CAAC,IAAI,CAAC;IAC1B;EACF,CAAC,EAAE,CAACH,cAAc,CAAC,CAAC;;EAEpB;EACAxE,SAAS,CAAC,MAAM;IACd,IAAI0E,eAAe,EAAE;MACnB,MAAMO,eAAe,GAAGP,eAAe,CAACO,eAAe;;MAEvD;MACA,IAAIzB,SAAS,GAAG,2BAA2BkB,eAAe,CAACQ,aAAa,EAAE;;MAE1E;MACA,IAAIR,eAAe,CAACS,UAAU,EAAE;QAC9B,MAAMC,cAAc,GAAGV,eAAe,CAACW,aAAa,CAACC,GAAG,CAACC,GAAG,IAAI,GAAGA,GAAG,CAACC,YAAY,KAAKD,GAAG,CAAC7B,MAAM,GAAG,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAAC;QACjHjC,SAAS,GAAG,2BAA2BkB,eAAe,CAACQ,aAAa,eAAepD,cAAc,CAAC4C,eAAe,CAACgB,cAAc,CAAC,cAAc5D,cAAc,CAAC4C,eAAe,CAACiB,YAAY,CAAC,KAAKP,cAAc,gBAAgBtD,cAAc,CAAC4C,eAAe,CAACkB,cAAc,CAAC,GAAG;MACjR;MAEAlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP/C,MAAM,EAAEmC,eAAe,CAACa,QAAQ,CAAC,CAAC;QAClCtC;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE,CAACkB,eAAe,CAAC,CAAC;;EAErB;EACA1E,SAAS,CAAC,MAAM;IACd;IACA,MAAM+F,aAAa,GAAGC,MAAM,CAACC,OAAO,CAACC,KAAK;IAC1CF,MAAM,CAACC,OAAO,CAACC,KAAK,GAAG,CAAC,GAAGC,IAAI,KAAK;MAAA,IAAAC,MAAA,EAAAC,eAAA;MAClC,KAAAD,MAAA,GAAID,IAAI,CAAC,CAAC,CAAC,cAAAC,MAAA,gBAAAC,eAAA,GAAPD,MAAA,CAASE,QAAQ,cAAAD,eAAA,eAAjBA,eAAA,CAAAE,IAAA,CAAAH,MAAA,EAAoB,qBAAqB,CAAC,EAAE;QAC9C;QACA;MACF;MACAL,aAAa,CAAC,GAAGI,IAAI,CAAC;IACxB,CAAC;IAED,OAAO,MAAM;MACXH,MAAM,CAACC,OAAO,CAACC,KAAK,GAAGH,aAAa;IACtC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMlB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM,CAAC2B,WAAW,EAAEC,UAAU,EAAEC,eAAe,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnE/E,KAAK,CAACgF,GAAG,CAAC,2BAA2B,CAAC,EACtChF,KAAK,CAACgF,GAAG,CAAC,cAAc,CAAC,EACzBhF,KAAK,CAACgF,GAAG,CAAC,oBAAoB,CAAC,CAChC,CAAC;MAEFhD,WAAW,CAAC2C,WAAW,CAACM,IAAI,CAAC;MAC7B7C,UAAU,CAACwC,UAAU,CAACK,IAAI,CAAC;;MAE3B;MACA,MAAMC,QAAQ,GAAGL,eAAe,CAACI,IAAI;MACrC,MAAME,cAAc,GAAGD,QAAQ,CAACzB,GAAG,CAAC2B,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC;;MAE5E;MACA,IAAIJ,cAAc,CAACK,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMC,mBAAmB,GAAGd,WAAW,CAACM,IAAI,CAACK,MAAM,CAACI,GAAG,IACrDP,cAAc,CAACV,QAAQ,CAACiB,GAAG,CAACvC,GAAG,CACjC,CAAC;QACDjB,eAAe,CAACuD,mBAAmB,CAAC;MACtC;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMpB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFT,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmD,QAAQ,GAAG,MAAM3F,KAAK,CAACgF,GAAG,CAAC,yBAAyBzE,SAAS,EAAE,CAAC;MACtE,MAAMqF,WAAW,GAAGD,QAAQ,CAACV,IAAI;MAEjCpE,UAAU,CAAC;QACT,GAAG+E,WAAW;QACd7E,WAAW,EAAE6E,WAAW,CAAC7E,WAAW,GAAGhB,KAAK,CAAC6F,WAAW,CAAC7E,WAAW,CAAC,GAAGhB,KAAK,CAAC6F,WAAW,CAACC,SAAS,CAAC;QACpG7E,eAAe,EAAEjB,KAAK,CAAC6F,WAAW,CAAC5E,eAAe,CAAC;QACnDS,UAAU,EAAEmE,WAAW,CAACnE,UAAU,GAAG1B,KAAK,CAAC6F,WAAW,CAACnE,UAAU,CAAC,GAAG,IAAI;QACzEP,aAAa,EAAE0E,WAAW,CAAC1E,aAAa,CAACiC,GAAG;QAC5ChC,WAAW,EAAEyE,WAAW,CAACzE,WAAW,CAACgC;MACvC,CAAC,CAAC;;MAEF;MACA,IAAIyC,WAAW,CAACxE,gBAAgB,KAAK,QAAQ,IAAIwE,WAAW,CAACvE,cAAc,EAAE;QAC3E,MAAMyE,SAAS,GAAG,MAAM9F,KAAK,CAACgF,GAAG,CAAC,gBAAgBY,WAAW,CAACvE,cAAc,EAAE,CAAC;QAC/EuB,iBAAiB,CAACkD,SAAS,CAACb,IAAI,CAAC;;QAEjC;QACA,IAAIW,WAAW,CAAC9D,kBAAkB,IAAI8D,WAAW,CAAC9D,kBAAkB,CAAC0D,MAAM,GAAG,CAAC,EAAE;UAC/E,MAAMO,UAAU,GAAGH,WAAW,CAAC9D,kBAAkB,CAACkE,IAAI,CAACC,GAAG,IACxDA,GAAG,CAACC,YAAY,KAAK,iBACvB,CAAC;UAED,IAAIH,UAAU,IAAIA,UAAU,CAACI,UAAU,EAAE;YACvC,MAAMC,UAAU,GAAG,MAAMpG,KAAK,CAACgF,GAAG,CAAC,0BAA0Be,UAAU,CAACI,UAAU,EAAE,CAAC;YACrFrD,kBAAkB,CAACsD,UAAU,CAACnB,IAAI,CAAC;UACrC;QACF;MACF;IACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMU,mBAAmB,GAAG,MAAOmD,QAAQ,IAAK;IAC9C,IAAI;MACFjC,OAAO,CAACkC,GAAG,CAAC,iCAAiCD,QAAQ,EAAE,CAAC;MACxD;MACA,MAAMV,QAAQ,GAAG,MAAM3F,KAAK,CAACgF,GAAG,CAAC,yCAAyCqB,QAAQ,EAAE,CAAC;MACrFjC,OAAO,CAACkC,GAAG,CAAC,2BAA2B,EAAEX,QAAQ,CAACV,IAAI,CAAC;MAEvD,IAAIsB,KAAK,CAACC,OAAO,CAACb,QAAQ,CAACV,IAAI,CAAC,IAAIU,QAAQ,CAACV,IAAI,CAACO,MAAM,GAAG,CAAC,EAAE;QAC5DlD,mBAAmB,CAACqD,QAAQ,CAACV,IAAI,CAAC;MACpC,CAAC,MAAM;QACLb,OAAO,CAACkC,GAAG,CAAC,0CAA0C,CAAC;QACvDhE,mBAAmB,CAAC,EAAE,CAAC;MACzB;IACF,CAAC,CAAC,OAAO+B,KAAK,EAAE;MAAA,IAAAoC,eAAA,EAAAC,oBAAA;MACdtC,OAAO,CAACC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvDsC,KAAK,CAAC,oCAAoC,EAAAF,eAAA,GAAApC,KAAK,CAACsB,QAAQ,cAAAc,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBxB,IAAI,cAAAyB,oBAAA,uBAApBA,oBAAA,CAAsBE,OAAO,KAAIvC,KAAK,CAACuC,OAAO,EAAE,CAAC;MAC3FtE,mBAAmB,CAAC,EAAE,CAAC;IACzB;EACF,CAAC;EAED,MAAMuE,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1ClG,UAAU,CAACmD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAAC8C,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAEjD;IACA,IAAItE,MAAM,CAACqE,KAAK,CAAC,EAAE;MACjBpE,SAAS,CAACsB,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAAC8C,KAAK,GAAG;MAAK,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EAED,MAAME,kBAAkB,GAAIC,MAAM,IAAK;IACrCrE,iBAAiB,CAACqE,MAAM,CAAC;IAEzB,IAAIA,MAAM,EAAE;MACV;MACApG,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP5C,gBAAgB,EAAE,QAAQ;QAC1BC,cAAc,EAAE4F,MAAM,CAAC9D,GAAG;QAC1B7B,gBAAgB,EAAE2F,MAAM,CAACC,IAAI;QAC7B/F,WAAW,EAAE8F,MAAM,CAAC5B,SAAS,IAAI,EAAE,CAAE;MACvC,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACAxE,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP5C,gBAAgB,EAAE,EAAE;QACpBC,cAAc,EAAE,EAAE;QAClBC,gBAAgB,EAAE,EAAE;QACpBH,WAAW,EAAE;MACf,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMgG,mBAAmB,GAAIC,OAAO,IAAK;IACvCtE,kBAAkB,CAACsE,OAAO,CAAC;IAE3B,IAAIA,OAAO,EAAE;MACXhD,OAAO,CAACkC,GAAG,CAAC,mBAAmB,EAAEc,OAAO,CAAC;;MAEzC;MACA,MAAMhE,eAAe,GAAGgE,OAAO,CAAChE,eAAe;MAE/CgB,OAAO,CAACkC,GAAG,CAAC,oCAAoClD,eAAe,EAAE,CAAC;;MAElE;MACA,IAAIzB,SAAS,GAAG,2BAA2ByF,OAAO,CAAC/D,aAAa,EAAE;;MAElE;MACA,IAAI+D,OAAO,CAAC9D,UAAU,EAAE;QACtB,MAAMC,cAAc,GAAG6D,OAAO,CAAC5D,aAAa,CAACC,GAAG,CAACC,GAAG,IAAI,GAAGA,GAAG,CAACC,YAAY,KAAKD,GAAG,CAAC7B,MAAM,GAAG,CAAC,CAAC+B,IAAI,CAAC,IAAI,CAAC;QACzGjC,SAAS,GAAG,2BAA2ByF,OAAO,CAAC/D,aAAa,eAAepD,cAAc,CAACmH,OAAO,CAACvD,cAAc,CAAC,cAAc5D,cAAc,CAACmH,OAAO,CAACtD,YAAY,CAAC,KAAKP,cAAc,gBAAgBtD,cAAc,CAACmH,OAAO,CAACrD,cAAc,CAAC,GAAG;MACjP;;MAEA;MACAlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACP/C,MAAM,EAAEmC,eAAe,CAACa,QAAQ,CAAC,CAAC;QAClCtC;MACF,CAAC,CAAC,CAAC;;MAEH;MACA,MAAM0F,YAAY,GAAG;QACnBnB,YAAY,EAAE,iBAAiB;QAC/BC,UAAU,EAAEiB,OAAO,CAACjE,GAAG;QACvBmE,cAAc,EAAEF,OAAO,CAAC/D,aAAa;QACrCkE,eAAe,EAAEnE,eAAe;QAChCS,cAAc,EAAEuD,OAAO,CAACvD,cAAc;QACtCC,YAAY,EAAEsD,OAAO,CAACtD,YAAY;QAClCC,cAAc,EAAEqD,OAAO,CAACrD;MAC1B,CAAC;MAEDlD,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPlC,kBAAkB,EAAE,CAACuF,YAAY;MACnC,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLxG,UAAU,CAACmD,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPlC,kBAAkB,EAAE;MACtB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAM0F,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC7G,OAAO,CAACM,aAAa,EAAEuG,SAAS,CAACvG,aAAa,GAAG,0BAA0B;IAChF,IAAI,CAACN,OAAO,CAACO,WAAW,EAAEsG,SAAS,CAACtG,WAAW,GAAG,2BAA2B;IAC7E,IAAI,CAACP,OAAO,CAACK,MAAM,IAAIyG,KAAK,CAACC,UAAU,CAAC/G,OAAO,CAACK,MAAM,CAAC,CAAC,IAAI0G,UAAU,CAAC/G,OAAO,CAACK,MAAM,CAAC,IAAI,CAAC,EAAE;MAC3FwG,SAAS,CAACxG,MAAM,GAAG,0BAA0B;IAC/C;IACA,IAAI,CAACL,OAAO,CAACI,eAAe,EAAEyG,SAAS,CAACzG,eAAe,GAAG,8BAA8B;IACxF,IAAI,CAACJ,OAAO,CAACe,SAAS,EAAE8F,SAAS,CAAC9F,SAAS,GAAG,uBAAuB;IAErEe,SAAS,CAAC+E,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACjC,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMsC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI,CAACN,YAAY,CAAC,CAAC,EAAE;IAErB,IAAI;MACFhF,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMoD,WAAW,GAAG;QAClB,GAAGhF,OAAO;QACVI,eAAe,EAAEJ,OAAO,CAACI,eAAe,CAAC+G,WAAW,CAAC,CAAC;QACtDtG,UAAU,EAAEb,OAAO,CAACa,UAAU,GAAGb,OAAO,CAACa,UAAU,CAACsG,WAAW,CAAC,CAAC,GAAG,IAAI;QACxE9G,MAAM,EAAE0G,UAAU,CAAC/G,OAAO,CAACK,MAAM;MACnC,CAAC;MAED,IAAI0E,QAAQ;MACZ,IAAIpF,SAAS,EAAE;QACboF,QAAQ,GAAG,MAAM3F,KAAK,CAACgI,GAAG,CAAC,yBAAyBzH,SAAS,EAAE,EAAEqF,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLD,QAAQ,GAAG,MAAM3F,KAAK,CAACiI,IAAI,CAAC,uBAAuB,EAAErC,WAAW,CAAC;MACnE;MAEA,IAAInF,MAAM,EAAEA,MAAM,CAACkF,QAAQ,CAACV,IAAI,CAACrE,OAAO,CAAC;IAC3C,CAAC,CAAC,OAAOyD,KAAK,EAAE;MAAA,IAAA6D,gBAAA,EAAAC,qBAAA;MACd/D,OAAO,CAACC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CsC,KAAK,CAAC,EAAAuB,gBAAA,GAAA7D,KAAK,CAACsB,QAAQ,cAAAuC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjD,IAAI,cAAAkD,qBAAA,uBAApBA,qBAAA,CAAsBvB,OAAO,KAAI,mCAAmC,CAAC;IAC7E,CAAC,SAAS;MACRpE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4F,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF5F,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMxC,KAAK,CAACgI,GAAG,CAAC,yBAAyBzH,SAAS,UAAU,CAAC;MAC7D,IAAIE,MAAM,EAAEA,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4D,KAAK,EAAE;MAAA,IAAAgE,gBAAA,EAAAC,qBAAA;MACdlE,OAAO,CAACC,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDsC,KAAK,CAAC,EAAA0B,gBAAA,GAAAhE,KAAK,CAACsB,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpD,IAAI,cAAAqD,qBAAA,uBAApBA,qBAAA,CAAsB1B,OAAO,KAAI,yBAAyB,CAAC;IACnE,CAAC,SAAS;MACRpE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+F,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF/F,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMxC,KAAK,CAACgI,GAAG,CAAC,yBAAyBzH,SAAS,OAAO,CAAC;MAC1D,IAAIE,MAAM,EAAEA,MAAM,CAAC,CAAC;IACtB,CAAC,CAAC,OAAO4D,KAAK,EAAE;MAAA,IAAAmE,gBAAA,EAAAC,qBAAA;MACdrE,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CsC,KAAK,CAAC,EAAA6B,gBAAA,GAAAnE,KAAK,CAACsB,QAAQ,cAAA6C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvD,IAAI,cAAAwD,qBAAA,uBAApBA,qBAAA,CAAsB7B,OAAO,KAAI,uBAAuB,CAAC;IACjE,CAAC,SAAS;MACRpE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkG,OAAO,GAAG,CAAClI,QAAQ,KAAK,CAACD,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,OAAO,CAAC;EACvE,MAAM8G,UAAU,GAAG,CAACnI,QAAQ,IAAID,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,OAAO;EACvE,MAAM+G,OAAO,GAAG,CAACpI,QAAQ,IAAID,SAAS,IAAIK,OAAO,CAACiB,MAAM,KAAK,UAAU;EAEvE,oBACE1B,OAAA,CAAC/B,GAAG;IAAAyK,QAAA,eACF1I,OAAA,CAACN,oBAAoB;MAACiJ,WAAW,EAAEhJ,YAAa;MAAA+I,QAAA,eAC9C1I,OAAA,CAAC1B,IAAI;QAACsK,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAH,QAAA,gBAEzB1I,OAAA,CAAC1B,IAAI;UAACwK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACvB1I,OAAA,CAAC9B,IAAI;YAAC+K,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACrC1I,OAAA,CAAC7B,WAAW;cAAAuK,QAAA,gBACV1I,OAAA,CAAC5B,UAAU;gBAAC6K,OAAO,EAAC,IAAI;gBAACG,YAAY;gBAAAV,QAAA,EAAC;cAEtC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAGZpJ,SAAS,iBACRJ,OAAA,CAACzB,SAAS;gBACRkL,SAAS;gBACTC,KAAK,EAAC,aAAa;gBACnB9C,KAAK,EAAEnG,OAAO,CAACkJ,aAAa,IAAI,EAAG;gBACnCC,MAAM,EAAC,QAAQ;gBACfC,UAAU,EAAE;kBAAExJ,QAAQ,EAAE;gBAAK;cAAE;gBAAAgJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACF,eAGDxJ,OAAA,CAACP,UAAU;gBACTiK,KAAK,EAAC,cAAc;gBACpB9C,KAAK,EAAEnG,OAAO,CAACG,WAAY;gBAC3BkJ,QAAQ,EAAGC,QAAQ,IAAKrD,iBAAiB,CAAC,aAAa,EAAEqD,QAAQ,CAAE;gBACnEC,MAAM,EAAC,aAAa;gBACpBC,WAAW,EAAGC,MAAM,iBAClBlK,OAAA,CAACzB,SAAS;kBAAA,GACJ2L,MAAM;kBACVT,SAAS;kBACTG,MAAM,EAAC,QAAQ;kBACf1F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAAC1B,WAAY;kBAC5BuJ,UAAU,EAAE7H,MAAM,CAAC1B,WAAY;kBAC/BiJ,UAAU,EAAE;oBACV,GAAGK,MAAM,CAACL,UAAU;oBACpBxJ,QAAQ,EAAE,CAACkI;kBACb;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFnJ,QAAQ,EAAE,CAACkI;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGFxJ,OAAA,CAACP,UAAU;gBACTiK,KAAK,EAAC,kBAAkB;gBACxB9C,KAAK,EAAEnG,OAAO,CAACI,eAAgB;gBAC/BiJ,QAAQ,EAAGC,QAAQ,IAAKrD,iBAAiB,CAAC,iBAAiB,EAAEqD,QAAQ,CAAE;gBACvEC,MAAM,EAAC,aAAa;gBACpBC,WAAW,EAAGC,MAAM,iBAClBlK,OAAA,CAACzB,SAAS;kBAAA,GACJ2L,MAAM;kBACVT,SAAS;kBACTG,MAAM,EAAC,QAAQ;kBACf1F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACzB,eAAgB;kBAChCsJ,UAAU,EAAE7H,MAAM,CAACzB,eAAgB;kBACnCgJ,UAAU,EAAE;oBACV,GAAGK,MAAM,CAACL,UAAU;oBACpBxJ,QAAQ,EAAE,CAACkI;kBACb;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACD;gBACFnJ,QAAQ,EAAE,CAACkI;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eAGFxJ,OAAA,CAACzB,SAAS;gBACRkL,SAAS;gBACTC,KAAK,EAAC,4BAA4B;gBAClC9C,KAAK,EAAEnG,OAAO,CAACc,aAAa,IAAI,EAAG;gBACnCuI,QAAQ,EAAGM,CAAC,IAAK1D,iBAAiB,CAAC,eAAe,EAAE0D,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAE;gBACpEgD,MAAM,EAAC,QAAQ;gBACfC,UAAU,EAAE;kBAAExJ,QAAQ,EAAE,CAACkI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAGFxJ,OAAA,CAACxB,WAAW;gBACViL,SAAS;gBACTG,MAAM,EAAC,QAAQ;gBACf1F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACvB,aAAc;gBAAA2H,QAAA,gBAE9B1I,OAAA,CAACvB,UAAU;kBAAAiK,QAAA,EAAC;gBAAY;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrCxJ,OAAA,CAACtB,MAAM;kBACLkI,KAAK,EAAEnG,OAAO,CAACM,aAAc;kBAC7B+I,QAAQ,EAAGM,CAAC,IAAK1D,iBAAiB,CAAC,eAAe,EAAE0D,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAE;kBACpE8C,KAAK,EAAC,cAAc;kBACpBY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,gBAEnB1I,OAAA,CAACrB,QAAQ;oBAACiI,KAAK,EAAC,EAAE;oBAAA8B,QAAA,eAChB1I,OAAA;sBAAA0I,QAAA,EAAI;oBAAmB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC,EACV1H,YAAY,CAACwB,GAAG,CAAEiH,OAAO,iBACxBvK,OAAA,CAACrB,QAAQ;oBAAmBiI,KAAK,EAAE2D,OAAO,CAACvH,GAAI;oBAAA0F,QAAA,EAC5C6B,OAAO,CAACC;kBAAW,GADPD,OAAO,CAACvH,GAAG;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRlH,MAAM,CAACvB,aAAa,iBACnBf,OAAA,CAAChB,cAAc;kBAAA0J,QAAA,EAAEpG,MAAM,CAACvB;gBAAa;kBAAAsI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACvD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CAAC,eAGdxJ,OAAA,CAACxB,WAAW;gBAACiL,SAAS;gBAACG,MAAM,EAAC,QAAQ;gBAAAlB,QAAA,gBACpC1I,OAAA,CAACvB,UAAU;kBAAAiK,QAAA,EAAC;gBAAc;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvCxJ,OAAA,CAACtB,MAAM;kBACLkI,KAAK,EAAEnG,OAAO,CAACW,aAAc;kBAC7B0I,QAAQ,EAAGM,CAAC,IAAK1D,iBAAiB,CAAC,eAAe,EAAE0D,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAE;kBACpE8C,KAAK,EAAC,gBAAgB;kBACtBY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,EAElB9F,cAAc,CAACU,GAAG,CAAEmH,MAAM,iBACzBzK,OAAA,CAACrB,QAAQ;oBAAciI,KAAK,EAAE6D,MAAO;oBAAA/B,QAAA,EAClC+B;kBAAM,GADMA,MAAM;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEX,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGb/I,OAAO,CAACW,aAAa,KAAK,QAAQ,iBACjCpB,OAAA,CAAAE,SAAA;gBAAAwI,QAAA,gBACE1I,OAAA,CAACzB,SAAS;kBACRkL,SAAS;kBACTC,KAAK,EAAC,eAAe;kBACrB9C,KAAK,EAAEnG,OAAO,CAACY,YAAY,IAAI,EAAG;kBAClCyI,QAAQ,EAAGM,CAAC,IAAK1D,iBAAiB,CAAC,cAAc,EAAE0D,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAE;kBACnEgD,MAAM,EAAC,QAAQ;kBACfC,UAAU,EAAE;oBAAExJ,QAAQ,EAAE,CAACkI;kBAAQ;gBAAE;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eAEFxJ,OAAA,CAACP,UAAU;kBACTiK,KAAK,EAAC,aAAa;kBACnB9C,KAAK,EAAEnG,OAAO,CAACa,UAAW;kBAC1BwI,QAAQ,EAAGC,QAAQ,IAAKrD,iBAAiB,CAAC,YAAY,EAAEqD,QAAQ,CAAE;kBAClEE,WAAW,EAAGC,MAAM,iBAClBlK,OAAA,CAACzB,SAAS;oBAAA,GACJ2L,MAAM;oBACVT,SAAS;oBACTG,MAAM,EAAC,QAAQ;oBACfC,UAAU,EAAE;sBACV,GAAGK,MAAM,CAACL,UAAU;sBACpBxJ,QAAQ,EAAE,CAACkI;oBACb;kBAAE;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACD;kBACFnJ,QAAQ,EAAE,CAACkI;gBAAQ;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA,eACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPxJ,OAAA,CAAC1B,IAAI;UAACwK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAN,QAAA,eACvB1I,OAAA,CAAC9B,IAAI;YAAC+K,OAAO,EAAC,UAAU;YAACC,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,eACrC1I,OAAA,CAAC7B,WAAW;cAAAuK,QAAA,gBACV1I,OAAA,CAAC5B,UAAU;gBAAC6K,OAAO,EAAC,IAAI;gBAACG,YAAY;gBAAAV,QAAA,EAAC;cAEtC;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAGbxJ,OAAA,CAACjB,YAAY;gBACX2L,OAAO,EAAE1I,OAAQ;gBACjB2I,cAAc,EAAG7D,MAAM,IAAKA,MAAM,CAACC,IAAI,IAAI,EAAG;gBAC9CH,KAAK,EAAEpE,cAAe;gBACtBsH,QAAQ,EAAEA,CAACc,KAAK,EAAEb,QAAQ,KAAKlD,kBAAkB,CAACkD,QAAQ,CAAE;gBAC5DE,WAAW,EAAGC,MAAM,iBAClBlK,OAAA,CAACzB,SAAS;kBAAA,GACJ2L,MAAM;kBACVR,KAAK,EAAC,QAAQ;kBACdE,MAAM,EAAC,QAAQ;kBACfH,SAAS;gBAAA;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CACD;gBACFc,QAAQ,EAAE,CAAC/B;cAAQ;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EAGDhH,cAAc,iBACbxC,OAAA,CAACjB,YAAY;gBACX2L,OAAO,EAAExI,gBAAiB;gBAC1ByI,cAAc,EAAG1D,OAAO,IAAK;kBAC3B,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;kBAEvB,MAAM4D,YAAY,GAAG,GAAG5D,OAAO,CAAC/D,aAAa,IAAI,SAAS,EAAE;;kBAE5D;kBACA,IAAI+D,OAAO,CAAC9D,UAAU,EAAE;oBACtB,OAAO,GAAG0H,YAAY,MAAM/K,cAAc,CAACmH,OAAO,CAAChE,eAAe,CAAC,yBAAyB;kBAC9F;;kBAEA;kBACA,OAAO,GAAG4H,YAAY,MAAM/K,cAAc,CAACmH,OAAO,CAAChE,eAAe,CAAC,cAAc;gBACnF,CAAE;gBACF2D,KAAK,EAAElE,eAAgB;gBACvBoH,QAAQ,EAAEA,CAACc,KAAK,EAAEb,QAAQ,KAAK/C,mBAAmB,CAAC+C,QAAQ,CAAE;gBAC7De,YAAY,EAAEA,CAACC,KAAK,EAAE9D,OAAO,kBAC3BjH,OAAA;kBAAA,GAAQ+K,KAAK;kBAAArC,QAAA,eACX1I,OAAA,CAAC/B,GAAG;oBAACiL,EAAE,EAAE;sBAAE8B,KAAK,EAAE,MAAM;sBAAEC,QAAQ,EAAE,MAAM;sBAAEC,QAAQ,EAAE;oBAAS,CAAE;oBAAAxC,QAAA,gBAC/D1I,OAAA,CAAC5B,UAAU;sBAAC6K,OAAO,EAAC,OAAO;sBAACkC,UAAU,EAAC,QAAQ;sBAACC,MAAM;sBAAA1C,QAAA,GACnDzB,OAAO,CAAC/D,aAAa,EAAC,KAAG,EAACpD,cAAc,CAACmH,OAAO,CAAChE,eAAe,CAAC,EAAC,cACrE;oBAAA;sBAAAoG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,EACZvC,OAAO,CAAC9D,UAAU,iBACjBnD,OAAA,CAAAE,SAAA;sBAAAwI,QAAA,gBACE1I,OAAA,CAAC5B,UAAU;wBAAC6K,OAAO,EAAC,SAAS;wBAACoC,KAAK,EAAC,gBAAgB;wBAACC,OAAO,EAAC,OAAO;wBAACF,MAAM;wBAAA1C,QAAA,GAAC,YAChE,EAAC5I,cAAc,CAACmH,OAAO,CAACvD,cAAc,CAAC,EAAC,cACzC,EAAC5D,cAAc,CAACmH,OAAO,CAACtD,YAAY,CAAC,EAAC,eACrC,EAAC7D,cAAc,CAACmH,OAAO,CAACrD,cAAc,CAAC;sBAAA;wBAAAyF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvC,CAAC,eACbxJ,OAAA,CAAC5B,UAAU;wBAAC6K,OAAO,EAAC,SAAS;wBAACoC,KAAK,EAAC,SAAS;wBAACC,OAAO,EAAC,OAAO;wBAACF,MAAM;wBAAA1C,QAAA,GACjEzB,OAAO,CAAC5D,aAAa,CAACgC,MAAM,EAAC,sBAC9B,EAAC4B,OAAO,CAAC5D,aAAa,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEgI,GAAG,kBAClCvL,OAAA;0BAAA0I,QAAA,GACGnF,GAAG,CAACC,YAAY,EAAC,IAAE,EAACD,GAAG,CAAC7B,MAAM,EAAC,GAChC,EAAC6J,GAAG,GAAGtE,OAAO,CAAC5D,aAAa,CAACgC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE;wBAAA,GAF1CkG,GAAG;0BAAAlC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAGR,CACP,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ,CAAC;oBAAA,eACb,CACH;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CACJ;gBACFS,WAAW,EAAGC,MAAM,iBAClBlK,OAAA,CAACzB,SAAS;kBAAA,GACJ2L,MAAM;kBACVR,KAAK,EAAC,kBAAkB;kBACxBE,MAAM,EAAC,QAAQ;kBACfH,SAAS;kBACTvF,KAAK,EAAEhC,gBAAgB,CAACmD,MAAM,KAAK,CAAE;kBACrC8E,UAAU,EAAEjI,gBAAgB,CAACmD,MAAM,KAAK,CAAC,GAAG,0CAA0C,GAAG;gBAAG;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7F,CACD;gBACFc,QAAQ,EAAE,CAAC/B,OAAO,IAAIrG,gBAAgB,CAACmD,MAAM,KAAK,CAAE;gBACpDmG,aAAa,EAAC,0BAA0B;gBACxCC,YAAY,EAAE;kBACZC,KAAK,EAAE;oBAAEC,SAAS,EAAE;kBAAQ;gBAC9B;cAAE;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EAGA,CAAChH,cAAc,iBACdxC,OAAA,CAACxB,WAAW;gBACViL,SAAS;gBACTG,MAAM,EAAC,QAAQ;gBACf1F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACtB,WAAY;gBAAA0H,QAAA,gBAE5B1I,OAAA,CAACvB,UAAU;kBAAAiK,QAAA,EAAC;gBAAa;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtCxJ,OAAA,CAACtB,MAAM;kBACLkI,KAAK,EAAEnG,OAAO,CAACO,WAAY;kBAC3B8I,QAAQ,EAAGM,CAAC,IAAK1D,iBAAiB,CAAC,aAAa,EAAE0D,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAE;kBAClE8C,KAAK,EAAC,eAAe;kBACrBY,QAAQ,EAAE,CAAC/B,OAAQ;kBAAAG,QAAA,gBAEnB1I,OAAA,CAACrB,QAAQ;oBAACiI,KAAK,EAAC,EAAE;oBAAA8B,QAAA,eAChB1I,OAAA;sBAAA0I,QAAA,EAAI;oBAAc;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC,EACV5H,QAAQ,CAAC0B,GAAG,CAAEiH,OAAO,iBACpBvK,OAAA,CAACrB,QAAQ;oBAAmBiI,KAAK,EAAE2D,OAAO,CAACvH,GAAI;oBAAA0F,QAAA,GAC5C6B,OAAO,CAACqB,WAAW,EAAC,KAAG,EAACrB,OAAO,CAACC,WAAW;kBAAA,GAD/BD,OAAO,CAACvH,GAAG;oBAAAqG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EACRlH,MAAM,CAACtB,WAAW,iBACjBhB,OAAA,CAAChB,cAAc;kBAAA0J,QAAA,EAAEpG,MAAM,CAACtB;gBAAW;kBAAAqI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiB,CACrD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACU,CACd,eAGDxJ,OAAA,CAACzB,SAAS;gBACRkL,SAAS;gBACTC,KAAK,EAAC,QAAQ;gBACdmC,IAAI,EAAC,QAAQ;gBACbjF,KAAK,EAAEnG,OAAO,CAACK,MAAO;gBACtBgJ,QAAQ,EAAGM,CAAC,IAAK1D,iBAAiB,CAAC,QAAQ,EAAE0D,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAE;gBAC7DgD,MAAM,EAAC,QAAQ;gBACf1F,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACxB,MAAO;gBACvBqJ,UAAU,EAAE7H,MAAM,CAACxB,MAAO;gBAC1B+I,UAAU,EAAE;kBAAExJ,QAAQ,EAAE,CAACkI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAGFxJ,OAAA,CAACzB,SAAS;gBACRkL,SAAS;gBACTC,KAAK,EAAC,WAAW;gBACjB9C,KAAK,EAAEnG,OAAO,CAACe,SAAU;gBACzBsI,QAAQ,EAAGM,CAAC,IAAK1D,iBAAiB,CAAC,WAAW,EAAE0D,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAE;gBAChEgD,MAAM,EAAC,QAAQ;gBACfkC,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACR7H,KAAK,EAAE,CAAC,CAAC5B,MAAM,CAACd,SAAU;gBAC1B2I,UAAU,EAAE7H,MAAM,CAACd,SAAU;gBAC7BqI,UAAU,EAAE;kBAAExJ,QAAQ,EAAE,CAACkI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eAGFxJ,OAAA,CAACzB,SAAS;gBACRkL,SAAS;gBACTC,KAAK,EAAC,aAAa;gBACnB9C,KAAK,EAAEnG,OAAO,CAACgB,WAAW,IAAI,EAAG;gBACjCqI,QAAQ,EAAGM,CAAC,IAAK1D,iBAAiB,CAAC,aAAa,EAAE0D,CAAC,CAACC,MAAM,CAACzD,KAAK,CAAE;gBAClEgD,MAAM,EAAC,QAAQ;gBACfkC,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRlC,UAAU,EAAE;kBAAExJ,QAAQ,EAAE,CAACkI;gBAAQ;cAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPxJ,OAAA,CAAC1B,IAAI;UAACwK,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAL,QAAA,eAChB1I,OAAA,CAAC/B,GAAG;YAACiL,EAAE,EAAE;cAAEoC,OAAO,EAAE,MAAM;cAAEU,cAAc,EAAE,UAAU;cAAEC,GAAG,EAAE,CAAC;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAxD,QAAA,GACrEnI,QAAQ,iBACPP,OAAA,CAAC3B,MAAM;cACL4K,OAAO,EAAC,UAAU;cAClBoC,KAAK,EAAC,WAAW;cACjBc,SAAS,eAAEnM,OAAA,CAACR,UAAU;gBAAA6J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1B4C,OAAO,EAAE7L,QAAS;cAAAmI,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAjB,OAAO,iBACNvI,OAAA,CAAC3B,MAAM;cACL4K,OAAO,EAAC,WAAW;cACnBoC,KAAK,EAAC,SAAS;cACfc,SAAS,eAAEnM,OAAA,CAACd,QAAQ;gBAAAmK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxB4C,OAAO,EAAEzE,UAAW;cACpB2C,QAAQ,EAAElI,OAAQ;cAAAsG,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAhB,UAAU,iBACTxI,OAAA,CAAC3B,MAAM;cACL4K,OAAO,EAAC,WAAW;cACnBoC,KAAK,EAAC,SAAS;cACfc,SAAS,eAAEnM,OAAA,CAACZ,WAAW;gBAAAiK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3B4C,OAAO,EAAEnE,aAAc;cACvBqC,QAAQ,EAAElI,OAAQ;cAAAsG,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT,EAEAf,OAAO,iBACNzI,OAAA,CAAC3B,MAAM;cACL4K,OAAO,EAAC,WAAW;cACnBoC,KAAK,EAAC,SAAS;cACfc,SAAS,eAAEnM,OAAA,CAACV,QAAQ;gBAAA+J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACxB4C,OAAO,EAAEhE,UAAW;cACpBkC,QAAQ,EAAElI,OAAQ;cAAAsG,QAAA,EACnB;YAED;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACa;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpB,CAAC;AAEV,CAAC;AAAChJ,EAAA,CApsBIL,sBAAsB;AAAAkM,EAAA,GAAtBlM,sBAAsB;AAssB5B,eAAeA,sBAAsB;AAAC,IAAAkM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}