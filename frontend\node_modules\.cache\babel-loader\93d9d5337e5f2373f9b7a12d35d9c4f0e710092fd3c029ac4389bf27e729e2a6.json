{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates the result of detecting a barcode in an image. This includes the raw\n * matrix of black/white pixels corresponding to the barcode, and possibly points of interest\n * in the image, like the location of finder patterns or corners of the barcode in the image.</p>\n *\n * <AUTHOR>\n */\nvar DetectorResult = /** @class */function () {\n  function DetectorResult(bits, points) {\n    this.bits = bits;\n    this.points = points;\n  }\n  DetectorResult.prototype.getBits = function () {\n    return this.bits;\n  };\n  DetectorResult.prototype.getPoints = function () {\n    return this.points;\n  };\n  return DetectorResult;\n}();\nexport default DetectorResult;", "map": {"version": 3, "names": ["DetectorResult", "bits", "points", "prototype", "getBits", "getPoints"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/DetectorResult.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/**\n * <p>Encapsulates the result of detecting a barcode in an image. This includes the raw\n * matrix of black/white pixels corresponding to the barcode, and possibly points of interest\n * in the image, like the location of finder patterns or corners of the barcode in the image.</p>\n *\n * <AUTHOR>\n */\nvar DetectorResult = /** @class */ (function () {\n    function DetectorResult(bits, points) {\n        this.bits = bits;\n        this.points = points;\n    }\n    DetectorResult.prototype.getBits = function () {\n        return this.bits;\n    };\n    DetectorResult.prototype.getPoints = function () {\n        return this.points;\n    };\n    return DetectorResult;\n}());\nexport default DetectorResult;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,cAAc,GAAG,aAAe,YAAY;EAC5C,SAASA,cAAcA,CAACC,IAAI,EAAEC,MAAM,EAAE;IAClC,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAF,cAAc,CAACG,SAAS,CAACC,OAAO,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACH,IAAI;EACpB,CAAC;EACDD,cAAc,CAACG,SAAS,CAACE,SAAS,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACH,MAAM;EACtB,CAAC;EACD,OAAOF,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}