{"ast": null, "code": "/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport Formatter from '../../util/Formatter';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResult = /** @class */function () {\n  function DetectionResult(barcodeMetadata, boundingBox) {\n    /*final*/this.ADJUST_ROW_NUMBER_SKIP = 2;\n    this.barcodeMetadata = barcodeMetadata;\n    this.barcodeColumnCount = barcodeMetadata.getColumnCount();\n    this.boundingBox = boundingBox;\n    // this.detectionResultColumns = new DetectionResultColumn[this.barcodeColumnCount + 2];\n    this.detectionResultColumns = new Array(this.barcodeColumnCount + 2);\n  }\n  DetectionResult.prototype.getDetectionResultColumns = function () {\n    this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]);\n    this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount + 1]);\n    var unadjustedCodewordCount = PDF417Common.MAX_CODEWORDS_IN_BARCODE;\n    var previousUnadjustedCount;\n    do {\n      previousUnadjustedCount = unadjustedCodewordCount;\n      unadjustedCodewordCount = this.adjustRowNumbersAndGetCount();\n    } while (unadjustedCodewordCount > 0 && unadjustedCodewordCount < previousUnadjustedCount);\n    return this.detectionResultColumns;\n  };\n  DetectionResult.prototype.adjustIndicatorColumnRowNumbers = function (detectionResultColumn) {\n    if (detectionResultColumn != null) {\n      detectionResultColumn.adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata);\n    }\n  };\n  // TODO ensure that no detected codewords with unknown row number are left\n  // we should be able to estimate the row height and use it as a hint for the row number\n  // we should also fill the rows top to bottom and bottom to top\n  /**\n   * @return number of codewords which don't have a valid row number. Note that the count is not accurate as codewords\n   * will be counted several times. It just serves as an indicator to see when we can stop adjusting row numbers\n   */\n  DetectionResult.prototype.adjustRowNumbersAndGetCount = function () {\n    var unadjustedCount = this.adjustRowNumbersByRow();\n    if (unadjustedCount === 0) {\n      return 0;\n    }\n    for (var barcodeColumn /*int*/ = 1; barcodeColumn < this.barcodeColumnCount + 1; barcodeColumn++) {\n      var codewords = this.detectionResultColumns[barcodeColumn].getCodewords();\n      for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n        if (codewords[codewordsRow] == null) {\n          continue;\n        }\n        if (!codewords[codewordsRow].hasValidRowNumber()) {\n          this.adjustRowNumbers(barcodeColumn, codewordsRow, codewords);\n        }\n      }\n    }\n    return unadjustedCount;\n  };\n  DetectionResult.prototype.adjustRowNumbersByRow = function () {\n    this.adjustRowNumbersFromBothRI();\n    // TODO we should only do full row adjustments if row numbers of left and right row indicator column match.\n    // Maybe it's even better to calculated the height (rows: d) and divide it by the number of barcode\n    // rows. This, together with the LRI and RRI row numbers should allow us to get a good estimate where a row\n    // number starts and ends.\n    var unadjustedCount = this.adjustRowNumbersFromLRI();\n    return unadjustedCount + this.adjustRowNumbersFromRRI();\n  };\n  DetectionResult.prototype.adjustRowNumbersFromBothRI = function () {\n    if (this.detectionResultColumns[0] == null || this.detectionResultColumns[this.barcodeColumnCount + 1] == null) {\n      return;\n    }\n    var LRIcodewords = this.detectionResultColumns[0].getCodewords();\n    var RRIcodewords = this.detectionResultColumns[this.barcodeColumnCount + 1].getCodewords();\n    for (var codewordsRow /*int*/ = 0; codewordsRow < LRIcodewords.length; codewordsRow++) {\n      if (LRIcodewords[codewordsRow] != null && RRIcodewords[codewordsRow] != null && LRIcodewords[codewordsRow].getRowNumber() === RRIcodewords[codewordsRow].getRowNumber()) {\n        for (var barcodeColumn /*int*/ = 1; barcodeColumn <= this.barcodeColumnCount; barcodeColumn++) {\n          var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n          if (codeword == null) {\n            continue;\n          }\n          codeword.setRowNumber(LRIcodewords[codewordsRow].getRowNumber());\n          if (!codeword.hasValidRowNumber()) {\n            this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow] = null;\n          }\n        }\n      }\n    }\n  };\n  DetectionResult.prototype.adjustRowNumbersFromRRI = function () {\n    if (this.detectionResultColumns[this.barcodeColumnCount + 1] == null) {\n      return 0;\n    }\n    var unadjustedCount = 0;\n    var codewords = this.detectionResultColumns[this.barcodeColumnCount + 1].getCodewords();\n    for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n      if (codewords[codewordsRow] == null) {\n        continue;\n      }\n      var rowIndicatorRowNumber = codewords[codewordsRow].getRowNumber();\n      var invalidRowCounts = 0;\n      for (var barcodeColumn /*int*/ = this.barcodeColumnCount + 1; barcodeColumn > 0 && invalidRowCounts < this.ADJUST_ROW_NUMBER_SKIP; barcodeColumn--) {\n        var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n        if (codeword != null) {\n          invalidRowCounts = DetectionResult.adjustRowNumberIfValid(rowIndicatorRowNumber, invalidRowCounts, codeword);\n          if (!codeword.hasValidRowNumber()) {\n            unadjustedCount++;\n          }\n        }\n      }\n    }\n    return unadjustedCount;\n  };\n  DetectionResult.prototype.adjustRowNumbersFromLRI = function () {\n    if (this.detectionResultColumns[0] == null) {\n      return 0;\n    }\n    var unadjustedCount = 0;\n    var codewords = this.detectionResultColumns[0].getCodewords();\n    for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n      if (codewords[codewordsRow] == null) {\n        continue;\n      }\n      var rowIndicatorRowNumber = codewords[codewordsRow].getRowNumber();\n      var invalidRowCounts = 0;\n      for (var barcodeColumn /*int*/ = 1; barcodeColumn < this.barcodeColumnCount + 1 && invalidRowCounts < this.ADJUST_ROW_NUMBER_SKIP; barcodeColumn++) {\n        var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n        if (codeword != null) {\n          invalidRowCounts = DetectionResult.adjustRowNumberIfValid(rowIndicatorRowNumber, invalidRowCounts, codeword);\n          if (!codeword.hasValidRowNumber()) {\n            unadjustedCount++;\n          }\n        }\n      }\n    }\n    return unadjustedCount;\n  };\n  DetectionResult.adjustRowNumberIfValid = function (rowIndicatorRowNumber, invalidRowCounts, codeword) {\n    if (codeword == null) {\n      return invalidRowCounts;\n    }\n    if (!codeword.hasValidRowNumber()) {\n      if (codeword.isValidRowNumber(rowIndicatorRowNumber)) {\n        codeword.setRowNumber(rowIndicatorRowNumber);\n        invalidRowCounts = 0;\n      } else {\n        ++invalidRowCounts;\n      }\n    }\n    return invalidRowCounts;\n  };\n  DetectionResult.prototype.adjustRowNumbers = function (barcodeColumn, codewordsRow, codewords) {\n    var e_1, _a;\n    if (this.detectionResultColumns[barcodeColumn - 1] == null) {\n      return;\n    }\n    var codeword = codewords[codewordsRow];\n    var previousColumnCodewords = this.detectionResultColumns[barcodeColumn - 1].getCodewords();\n    var nextColumnCodewords = previousColumnCodewords;\n    if (this.detectionResultColumns[barcodeColumn + 1] != null) {\n      nextColumnCodewords = this.detectionResultColumns[barcodeColumn + 1].getCodewords();\n    }\n    // let otherCodewords: Codeword[] = new Codeword[14];\n    var otherCodewords = new Array(14);\n    otherCodewords[2] = previousColumnCodewords[codewordsRow];\n    otherCodewords[3] = nextColumnCodewords[codewordsRow];\n    if (codewordsRow > 0) {\n      otherCodewords[0] = codewords[codewordsRow - 1];\n      otherCodewords[4] = previousColumnCodewords[codewordsRow - 1];\n      otherCodewords[5] = nextColumnCodewords[codewordsRow - 1];\n    }\n    if (codewordsRow > 1) {\n      otherCodewords[8] = codewords[codewordsRow - 2];\n      otherCodewords[10] = previousColumnCodewords[codewordsRow - 2];\n      otherCodewords[11] = nextColumnCodewords[codewordsRow - 2];\n    }\n    if (codewordsRow < codewords.length - 1) {\n      otherCodewords[1] = codewords[codewordsRow + 1];\n      otherCodewords[6] = previousColumnCodewords[codewordsRow + 1];\n      otherCodewords[7] = nextColumnCodewords[codewordsRow + 1];\n    }\n    if (codewordsRow < codewords.length - 2) {\n      otherCodewords[9] = codewords[codewordsRow + 2];\n      otherCodewords[12] = previousColumnCodewords[codewordsRow + 2];\n      otherCodewords[13] = nextColumnCodewords[codewordsRow + 2];\n    }\n    try {\n      for (var otherCodewords_1 = __values(otherCodewords), otherCodewords_1_1 = otherCodewords_1.next(); !otherCodewords_1_1.done; otherCodewords_1_1 = otherCodewords_1.next()) {\n        var otherCodeword = otherCodewords_1_1.value;\n        if (DetectionResult.adjustRowNumber(codeword, otherCodeword)) {\n          return;\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (otherCodewords_1_1 && !otherCodewords_1_1.done && (_a = otherCodewords_1.return)) _a.call(otherCodewords_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n  };\n  /**\n   * @return true, if row number was adjusted, false otherwise\n   */\n  DetectionResult.adjustRowNumber = function (codeword, otherCodeword) {\n    if (otherCodeword == null) {\n      return false;\n    }\n    if (otherCodeword.hasValidRowNumber() && otherCodeword.getBucket() === codeword.getBucket()) {\n      codeword.setRowNumber(otherCodeword.getRowNumber());\n      return true;\n    }\n    return false;\n  };\n  DetectionResult.prototype.getBarcodeColumnCount = function () {\n    return this.barcodeColumnCount;\n  };\n  DetectionResult.prototype.getBarcodeRowCount = function () {\n    return this.barcodeMetadata.getRowCount();\n  };\n  DetectionResult.prototype.getBarcodeECLevel = function () {\n    return this.barcodeMetadata.getErrorCorrectionLevel();\n  };\n  DetectionResult.prototype.setBoundingBox = function (boundingBox) {\n    this.boundingBox = boundingBox;\n  };\n  DetectionResult.prototype.getBoundingBox = function () {\n    return this.boundingBox;\n  };\n  DetectionResult.prototype.setDetectionResultColumn = function (barcodeColumn, detectionResultColumn) {\n    this.detectionResultColumns[barcodeColumn] = detectionResultColumn;\n  };\n  DetectionResult.prototype.getDetectionResultColumn = function (barcodeColumn) {\n    return this.detectionResultColumns[barcodeColumn];\n  };\n  // @Override\n  DetectionResult.prototype.toString = function () {\n    var rowIndicatorColumn = this.detectionResultColumns[0];\n    if (rowIndicatorColumn == null) {\n      rowIndicatorColumn = this.detectionResultColumns[this.barcodeColumnCount + 1];\n    }\n    // try (\n    var formatter = new Formatter();\n    // ) {\n    for (var codewordsRow /*int*/ = 0; codewordsRow < rowIndicatorColumn.getCodewords().length; codewordsRow++) {\n      formatter.format('CW %3d:', codewordsRow);\n      for (var barcodeColumn /*int*/ = 0; barcodeColumn < this.barcodeColumnCount + 2; barcodeColumn++) {\n        if (this.detectionResultColumns[barcodeColumn] == null) {\n          formatter.format('    |   ');\n          continue;\n        }\n        var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n        if (codeword == null) {\n          formatter.format('    |   ');\n          continue;\n        }\n        formatter.format(' %3d|%3d', codeword.getRowNumber(), codeword.getValue());\n      }\n      formatter.format('%n');\n    }\n    return formatter.toString();\n    // }\n  };\n  return DetectionResult;\n}();\nexport default DetectionResult;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "PDF417<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "DetectionResult", "barcodeMetadata", "boundingBox", "ADJUST_ROW_NUMBER_SKIP", "barcodeColumnCount", "getColumnCount", "detectionResultColumns", "Array", "prototype", "getDetectionResultColumns", "adjustIndicatorColumnRowNumbers", "unadjustedCodewordCount", "MAX_CODEWORDS_IN_BARCODE", "previousUnadjustedCount", "adjustRowNumbersAndGetCount", "detectionResultColumn", "adjustCompleteIndicatorColumnRowNumbers", "unadjustedCount", "adjustRowNumbersByRow", "barcodeColumn", "codewords", "getCodewords", "codewordsRow", "hasValidRowNumber", "adjustRowNumbers", "adjustRowNumbersFromBothRI", "adjustRowNumbersFromLRI", "adjustRowNumbersFromRRI", "LRIcodewords", "RRIcodewords", "getRowNumber", "codeword", "setRowNumber", "rowIndicatorRowNumber", "invalidRowCounts", "adjustRowNumberIfValid", "isValidRowNumber", "e_1", "_a", "previousColumnCodewords", "nextColumnCodewords", "otherCodewords", "otherCodewords_1", "otherCodewords_1_1", "otherCodeword", "adjustRowNumber", "e_1_1", "error", "return", "getBucket", "getBarcodeColumnCount", "getBarcodeRowCount", "getRowCount", "getBarcodeECLevel", "getErrorCorrectionLevel", "setBoundingBox", "getBoundingBox", "setDetectionResultColumn", "getDetectionResultColumn", "toString", "rowIndicatorColumn", "formatter", "format", "getValue"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/DetectionResult.js"], "sourcesContent": ["/*\n * Copyright 2013 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport Formatter from '../../util/Formatter';\n/**\n * <AUTHOR> Grau\n */\nvar DetectionResult = /** @class */ (function () {\n    function DetectionResult(barcodeMetadata, boundingBox) {\n        /*final*/ this.ADJUST_ROW_NUMBER_SKIP = 2;\n        this.barcodeMetadata = barcodeMetadata;\n        this.barcodeColumnCount = barcodeMetadata.getColumnCount();\n        this.boundingBox = boundingBox;\n        // this.detectionResultColumns = new DetectionResultColumn[this.barcodeColumnCount + 2];\n        this.detectionResultColumns = new Array(this.barcodeColumnCount + 2);\n    }\n    DetectionResult.prototype.getDetectionResultColumns = function () {\n        this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[0]);\n        this.adjustIndicatorColumnRowNumbers(this.detectionResultColumns[this.barcodeColumnCount + 1]);\n        var unadjustedCodewordCount = PDF417Common.MAX_CODEWORDS_IN_BARCODE;\n        var previousUnadjustedCount;\n        do {\n            previousUnadjustedCount = unadjustedCodewordCount;\n            unadjustedCodewordCount = this.adjustRowNumbersAndGetCount();\n        } while (unadjustedCodewordCount > 0 && unadjustedCodewordCount < previousUnadjustedCount);\n        return this.detectionResultColumns;\n    };\n    DetectionResult.prototype.adjustIndicatorColumnRowNumbers = function (detectionResultColumn) {\n        if (detectionResultColumn != null) {\n            detectionResultColumn\n                .adjustCompleteIndicatorColumnRowNumbers(this.barcodeMetadata);\n        }\n    };\n    // TODO ensure that no detected codewords with unknown row number are left\n    // we should be able to estimate the row height and use it as a hint for the row number\n    // we should also fill the rows top to bottom and bottom to top\n    /**\n     * @return number of codewords which don't have a valid row number. Note that the count is not accurate as codewords\n     * will be counted several times. It just serves as an indicator to see when we can stop adjusting row numbers\n     */\n    DetectionResult.prototype.adjustRowNumbersAndGetCount = function () {\n        var unadjustedCount = this.adjustRowNumbersByRow();\n        if (unadjustedCount === 0) {\n            return 0;\n        }\n        for (var barcodeColumn /*int*/ = 1; barcodeColumn < this.barcodeColumnCount + 1; barcodeColumn++) {\n            var codewords = this.detectionResultColumns[barcodeColumn].getCodewords();\n            for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n                if (codewords[codewordsRow] == null) {\n                    continue;\n                }\n                if (!codewords[codewordsRow].hasValidRowNumber()) {\n                    this.adjustRowNumbers(barcodeColumn, codewordsRow, codewords);\n                }\n            }\n        }\n        return unadjustedCount;\n    };\n    DetectionResult.prototype.adjustRowNumbersByRow = function () {\n        this.adjustRowNumbersFromBothRI();\n        // TODO we should only do full row adjustments if row numbers of left and right row indicator column match.\n        // Maybe it's even better to calculated the height (rows: d) and divide it by the number of barcode\n        // rows. This, together with the LRI and RRI row numbers should allow us to get a good estimate where a row\n        // number starts and ends.\n        var unadjustedCount = this.adjustRowNumbersFromLRI();\n        return unadjustedCount + this.adjustRowNumbersFromRRI();\n    };\n    DetectionResult.prototype.adjustRowNumbersFromBothRI = function () {\n        if (this.detectionResultColumns[0] == null || this.detectionResultColumns[this.barcodeColumnCount + 1] == null) {\n            return;\n        }\n        var LRIcodewords = this.detectionResultColumns[0].getCodewords();\n        var RRIcodewords = this.detectionResultColumns[this.barcodeColumnCount + 1].getCodewords();\n        for (var codewordsRow /*int*/ = 0; codewordsRow < LRIcodewords.length; codewordsRow++) {\n            if (LRIcodewords[codewordsRow] != null &&\n                RRIcodewords[codewordsRow] != null &&\n                LRIcodewords[codewordsRow].getRowNumber() === RRIcodewords[codewordsRow].getRowNumber()) {\n                for (var barcodeColumn /*int*/ = 1; barcodeColumn <= this.barcodeColumnCount; barcodeColumn++) {\n                    var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n                    if (codeword == null) {\n                        continue;\n                    }\n                    codeword.setRowNumber(LRIcodewords[codewordsRow].getRowNumber());\n                    if (!codeword.hasValidRowNumber()) {\n                        this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow] = null;\n                    }\n                }\n            }\n        }\n    };\n    DetectionResult.prototype.adjustRowNumbersFromRRI = function () {\n        if (this.detectionResultColumns[this.barcodeColumnCount + 1] == null) {\n            return 0;\n        }\n        var unadjustedCount = 0;\n        var codewords = this.detectionResultColumns[this.barcodeColumnCount + 1].getCodewords();\n        for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n            if (codewords[codewordsRow] == null) {\n                continue;\n            }\n            var rowIndicatorRowNumber = codewords[codewordsRow].getRowNumber();\n            var invalidRowCounts = 0;\n            for (var barcodeColumn /*int*/ = this.barcodeColumnCount + 1; barcodeColumn > 0 && invalidRowCounts < this.ADJUST_ROW_NUMBER_SKIP; barcodeColumn--) {\n                var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n                if (codeword != null) {\n                    invalidRowCounts = DetectionResult.adjustRowNumberIfValid(rowIndicatorRowNumber, invalidRowCounts, codeword);\n                    if (!codeword.hasValidRowNumber()) {\n                        unadjustedCount++;\n                    }\n                }\n            }\n        }\n        return unadjustedCount;\n    };\n    DetectionResult.prototype.adjustRowNumbersFromLRI = function () {\n        if (this.detectionResultColumns[0] == null) {\n            return 0;\n        }\n        var unadjustedCount = 0;\n        var codewords = this.detectionResultColumns[0].getCodewords();\n        for (var codewordsRow /*int*/ = 0; codewordsRow < codewords.length; codewordsRow++) {\n            if (codewords[codewordsRow] == null) {\n                continue;\n            }\n            var rowIndicatorRowNumber = codewords[codewordsRow].getRowNumber();\n            var invalidRowCounts = 0;\n            for (var barcodeColumn /*int*/ = 1; barcodeColumn < this.barcodeColumnCount + 1 && invalidRowCounts < this.ADJUST_ROW_NUMBER_SKIP; barcodeColumn++) {\n                var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n                if (codeword != null) {\n                    invalidRowCounts = DetectionResult.adjustRowNumberIfValid(rowIndicatorRowNumber, invalidRowCounts, codeword);\n                    if (!codeword.hasValidRowNumber()) {\n                        unadjustedCount++;\n                    }\n                }\n            }\n        }\n        return unadjustedCount;\n    };\n    DetectionResult.adjustRowNumberIfValid = function (rowIndicatorRowNumber, invalidRowCounts, codeword) {\n        if (codeword == null) {\n            return invalidRowCounts;\n        }\n        if (!codeword.hasValidRowNumber()) {\n            if (codeword.isValidRowNumber(rowIndicatorRowNumber)) {\n                codeword.setRowNumber(rowIndicatorRowNumber);\n                invalidRowCounts = 0;\n            }\n            else {\n                ++invalidRowCounts;\n            }\n        }\n        return invalidRowCounts;\n    };\n    DetectionResult.prototype.adjustRowNumbers = function (barcodeColumn, codewordsRow, codewords) {\n        var e_1, _a;\n        if (this.detectionResultColumns[barcodeColumn - 1] == null) {\n            return;\n        }\n        var codeword = codewords[codewordsRow];\n        var previousColumnCodewords = this.detectionResultColumns[barcodeColumn - 1].getCodewords();\n        var nextColumnCodewords = previousColumnCodewords;\n        if (this.detectionResultColumns[barcodeColumn + 1] != null) {\n            nextColumnCodewords = this.detectionResultColumns[barcodeColumn + 1].getCodewords();\n        }\n        // let otherCodewords: Codeword[] = new Codeword[14];\n        var otherCodewords = new Array(14);\n        otherCodewords[2] = previousColumnCodewords[codewordsRow];\n        otherCodewords[3] = nextColumnCodewords[codewordsRow];\n        if (codewordsRow > 0) {\n            otherCodewords[0] = codewords[codewordsRow - 1];\n            otherCodewords[4] = previousColumnCodewords[codewordsRow - 1];\n            otherCodewords[5] = nextColumnCodewords[codewordsRow - 1];\n        }\n        if (codewordsRow > 1) {\n            otherCodewords[8] = codewords[codewordsRow - 2];\n            otherCodewords[10] = previousColumnCodewords[codewordsRow - 2];\n            otherCodewords[11] = nextColumnCodewords[codewordsRow - 2];\n        }\n        if (codewordsRow < codewords.length - 1) {\n            otherCodewords[1] = codewords[codewordsRow + 1];\n            otherCodewords[6] = previousColumnCodewords[codewordsRow + 1];\n            otherCodewords[7] = nextColumnCodewords[codewordsRow + 1];\n        }\n        if (codewordsRow < codewords.length - 2) {\n            otherCodewords[9] = codewords[codewordsRow + 2];\n            otherCodewords[12] = previousColumnCodewords[codewordsRow + 2];\n            otherCodewords[13] = nextColumnCodewords[codewordsRow + 2];\n        }\n        try {\n            for (var otherCodewords_1 = __values(otherCodewords), otherCodewords_1_1 = otherCodewords_1.next(); !otherCodewords_1_1.done; otherCodewords_1_1 = otherCodewords_1.next()) {\n                var otherCodeword = otherCodewords_1_1.value;\n                if (DetectionResult.adjustRowNumber(codeword, otherCodeword)) {\n                    return;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (otherCodewords_1_1 && !otherCodewords_1_1.done && (_a = otherCodewords_1.return)) _a.call(otherCodewords_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    /**\n     * @return true, if row number was adjusted, false otherwise\n     */\n    DetectionResult.adjustRowNumber = function (codeword, otherCodeword) {\n        if (otherCodeword == null) {\n            return false;\n        }\n        if (otherCodeword.hasValidRowNumber() && otherCodeword.getBucket() === codeword.getBucket()) {\n            codeword.setRowNumber(otherCodeword.getRowNumber());\n            return true;\n        }\n        return false;\n    };\n    DetectionResult.prototype.getBarcodeColumnCount = function () {\n        return this.barcodeColumnCount;\n    };\n    DetectionResult.prototype.getBarcodeRowCount = function () {\n        return this.barcodeMetadata.getRowCount();\n    };\n    DetectionResult.prototype.getBarcodeECLevel = function () {\n        return this.barcodeMetadata.getErrorCorrectionLevel();\n    };\n    DetectionResult.prototype.setBoundingBox = function (boundingBox) {\n        this.boundingBox = boundingBox;\n    };\n    DetectionResult.prototype.getBoundingBox = function () {\n        return this.boundingBox;\n    };\n    DetectionResult.prototype.setDetectionResultColumn = function (barcodeColumn, detectionResultColumn) {\n        this.detectionResultColumns[barcodeColumn] = detectionResultColumn;\n    };\n    DetectionResult.prototype.getDetectionResultColumn = function (barcodeColumn) {\n        return this.detectionResultColumns[barcodeColumn];\n    };\n    // @Override\n    DetectionResult.prototype.toString = function () {\n        var rowIndicatorColumn = this.detectionResultColumns[0];\n        if (rowIndicatorColumn == null) {\n            rowIndicatorColumn = this.detectionResultColumns[this.barcodeColumnCount + 1];\n        }\n        // try (\n        var formatter = new Formatter();\n        // ) {\n        for (var codewordsRow /*int*/ = 0; codewordsRow < rowIndicatorColumn.getCodewords().length; codewordsRow++) {\n            formatter.format('CW %3d:', codewordsRow);\n            for (var barcodeColumn /*int*/ = 0; barcodeColumn < this.barcodeColumnCount + 2; barcodeColumn++) {\n                if (this.detectionResultColumns[barcodeColumn] == null) {\n                    formatter.format('    |   ');\n                    continue;\n                }\n                var codeword = this.detectionResultColumns[barcodeColumn].getCodewords()[codewordsRow];\n                if (codeword == null) {\n                    formatter.format('    |   ');\n                    continue;\n                }\n                formatter.format(' %3d|%3d', codeword.getRowNumber(), codeword.getValue());\n            }\n            formatter.format('%n');\n        }\n        return formatter.toString();\n        // }\n    };\n    return DetectionResult;\n}());\nexport default DetectionResult;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA,OAAOW,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,SAAS,MAAM,sBAAsB;AAC5C;AACA;AACA;AACA,IAAIC,eAAe,GAAG,aAAe,YAAY;EAC7C,SAASA,eAAeA,CAACC,eAAe,EAAEC,WAAW,EAAE;IACnD,SAAU,IAAI,CAACC,sBAAsB,GAAG,CAAC;IACzC,IAAI,CAACF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACG,kBAAkB,GAAGH,eAAe,CAACI,cAAc,CAAC,CAAC;IAC1D,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACI,sBAAsB,GAAG,IAAIC,KAAK,CAAC,IAAI,CAACH,kBAAkB,GAAG,CAAC,CAAC;EACxE;EACAJ,eAAe,CAACQ,SAAS,CAACC,yBAAyB,GAAG,YAAY;IAC9D,IAAI,CAACC,+BAA+B,CAAC,IAAI,CAACJ,sBAAsB,CAAC,CAAC,CAAC,CAAC;IACpE,IAAI,CAACI,+BAA+B,CAAC,IAAI,CAACJ,sBAAsB,CAAC,IAAI,CAACF,kBAAkB,GAAG,CAAC,CAAC,CAAC;IAC9F,IAAIO,uBAAuB,GAAGb,YAAY,CAACc,wBAAwB;IACnE,IAAIC,uBAAuB;IAC3B,GAAG;MACCA,uBAAuB,GAAGF,uBAAuB;MACjDA,uBAAuB,GAAG,IAAI,CAACG,2BAA2B,CAAC,CAAC;IAChE,CAAC,QAAQH,uBAAuB,GAAG,CAAC,IAAIA,uBAAuB,GAAGE,uBAAuB;IACzF,OAAO,IAAI,CAACP,sBAAsB;EACtC,CAAC;EACDN,eAAe,CAACQ,SAAS,CAACE,+BAA+B,GAAG,UAAUK,qBAAqB,EAAE;IACzF,IAAIA,qBAAqB,IAAI,IAAI,EAAE;MAC/BA,qBAAqB,CAChBC,uCAAuC,CAAC,IAAI,CAACf,eAAe,CAAC;IACtE;EACJ,CAAC;EACD;EACA;EACA;EACA;AACJ;AACA;AACA;EACID,eAAe,CAACQ,SAAS,CAACM,2BAA2B,GAAG,YAAY;IAChE,IAAIG,eAAe,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,IAAID,eAAe,KAAK,CAAC,EAAE;MACvB,OAAO,CAAC;IACZ;IACA,KAAK,IAAIE,aAAa,CAAC,UAAU,CAAC,EAAEA,aAAa,GAAG,IAAI,CAACf,kBAAkB,GAAG,CAAC,EAAEe,aAAa,EAAE,EAAE;MAC9F,IAAIC,SAAS,GAAG,IAAI,CAACd,sBAAsB,CAACa,aAAa,CAAC,CAACE,YAAY,CAAC,CAAC;MACzE,KAAK,IAAIC,YAAY,CAAC,UAAU,CAAC,EAAEA,YAAY,GAAGF,SAAS,CAAC3B,MAAM,EAAE6B,YAAY,EAAE,EAAE;QAChF,IAAIF,SAAS,CAACE,YAAY,CAAC,IAAI,IAAI,EAAE;UACjC;QACJ;QACA,IAAI,CAACF,SAAS,CAACE,YAAY,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAE;UAC9C,IAAI,CAACC,gBAAgB,CAACL,aAAa,EAAEG,YAAY,EAAEF,SAAS,CAAC;QACjE;MACJ;IACJ;IACA,OAAOH,eAAe;EAC1B,CAAC;EACDjB,eAAe,CAACQ,SAAS,CAACU,qBAAqB,GAAG,YAAY;IAC1D,IAAI,CAACO,0BAA0B,CAAC,CAAC;IACjC;IACA;IACA;IACA;IACA,IAAIR,eAAe,GAAG,IAAI,CAACS,uBAAuB,CAAC,CAAC;IACpD,OAAOT,eAAe,GAAG,IAAI,CAACU,uBAAuB,CAAC,CAAC;EAC3D,CAAC;EACD3B,eAAe,CAACQ,SAAS,CAACiB,0BAA0B,GAAG,YAAY;IAC/D,IAAI,IAAI,CAACnB,sBAAsB,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,IAAI,CAACA,sBAAsB,CAAC,IAAI,CAACF,kBAAkB,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE;MAC5G;IACJ;IACA,IAAIwB,YAAY,GAAG,IAAI,CAACtB,sBAAsB,CAAC,CAAC,CAAC,CAACe,YAAY,CAAC,CAAC;IAChE,IAAIQ,YAAY,GAAG,IAAI,CAACvB,sBAAsB,CAAC,IAAI,CAACF,kBAAkB,GAAG,CAAC,CAAC,CAACiB,YAAY,CAAC,CAAC;IAC1F,KAAK,IAAIC,YAAY,CAAC,UAAU,CAAC,EAAEA,YAAY,GAAGM,YAAY,CAACnC,MAAM,EAAE6B,YAAY,EAAE,EAAE;MACnF,IAAIM,YAAY,CAACN,YAAY,CAAC,IAAI,IAAI,IAClCO,YAAY,CAACP,YAAY,CAAC,IAAI,IAAI,IAClCM,YAAY,CAACN,YAAY,CAAC,CAACQ,YAAY,CAAC,CAAC,KAAKD,YAAY,CAACP,YAAY,CAAC,CAACQ,YAAY,CAAC,CAAC,EAAE;QACzF,KAAK,IAAIX,aAAa,CAAC,UAAU,CAAC,EAAEA,aAAa,IAAI,IAAI,CAACf,kBAAkB,EAAEe,aAAa,EAAE,EAAE;UAC3F,IAAIY,QAAQ,GAAG,IAAI,CAACzB,sBAAsB,CAACa,aAAa,CAAC,CAACE,YAAY,CAAC,CAAC,CAACC,YAAY,CAAC;UACtF,IAAIS,QAAQ,IAAI,IAAI,EAAE;YAClB;UACJ;UACAA,QAAQ,CAACC,YAAY,CAACJ,YAAY,CAACN,YAAY,CAAC,CAACQ,YAAY,CAAC,CAAC,CAAC;UAChE,IAAI,CAACC,QAAQ,CAACR,iBAAiB,CAAC,CAAC,EAAE;YAC/B,IAAI,CAACjB,sBAAsB,CAACa,aAAa,CAAC,CAACE,YAAY,CAAC,CAAC,CAACC,YAAY,CAAC,GAAG,IAAI;UAClF;QACJ;MACJ;IACJ;EACJ,CAAC;EACDtB,eAAe,CAACQ,SAAS,CAACmB,uBAAuB,GAAG,YAAY;IAC5D,IAAI,IAAI,CAACrB,sBAAsB,CAAC,IAAI,CAACF,kBAAkB,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE;MAClE,OAAO,CAAC;IACZ;IACA,IAAIa,eAAe,GAAG,CAAC;IACvB,IAAIG,SAAS,GAAG,IAAI,CAACd,sBAAsB,CAAC,IAAI,CAACF,kBAAkB,GAAG,CAAC,CAAC,CAACiB,YAAY,CAAC,CAAC;IACvF,KAAK,IAAIC,YAAY,CAAC,UAAU,CAAC,EAAEA,YAAY,GAAGF,SAAS,CAAC3B,MAAM,EAAE6B,YAAY,EAAE,EAAE;MAChF,IAAIF,SAAS,CAACE,YAAY,CAAC,IAAI,IAAI,EAAE;QACjC;MACJ;MACA,IAAIW,qBAAqB,GAAGb,SAAS,CAACE,YAAY,CAAC,CAACQ,YAAY,CAAC,CAAC;MAClE,IAAII,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAIf,aAAa,CAAC,UAAU,IAAI,CAACf,kBAAkB,GAAG,CAAC,EAAEe,aAAa,GAAG,CAAC,IAAIe,gBAAgB,GAAG,IAAI,CAAC/B,sBAAsB,EAAEgB,aAAa,EAAE,EAAE;QAChJ,IAAIY,QAAQ,GAAG,IAAI,CAACzB,sBAAsB,CAACa,aAAa,CAAC,CAACE,YAAY,CAAC,CAAC,CAACC,YAAY,CAAC;QACtF,IAAIS,QAAQ,IAAI,IAAI,EAAE;UAClBG,gBAAgB,GAAGlC,eAAe,CAACmC,sBAAsB,CAACF,qBAAqB,EAAEC,gBAAgB,EAAEH,QAAQ,CAAC;UAC5G,IAAI,CAACA,QAAQ,CAACR,iBAAiB,CAAC,CAAC,EAAE;YAC/BN,eAAe,EAAE;UACrB;QACJ;MACJ;IACJ;IACA,OAAOA,eAAe;EAC1B,CAAC;EACDjB,eAAe,CAACQ,SAAS,CAACkB,uBAAuB,GAAG,YAAY;IAC5D,IAAI,IAAI,CAACpB,sBAAsB,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MACxC,OAAO,CAAC;IACZ;IACA,IAAIW,eAAe,GAAG,CAAC;IACvB,IAAIG,SAAS,GAAG,IAAI,CAACd,sBAAsB,CAAC,CAAC,CAAC,CAACe,YAAY,CAAC,CAAC;IAC7D,KAAK,IAAIC,YAAY,CAAC,UAAU,CAAC,EAAEA,YAAY,GAAGF,SAAS,CAAC3B,MAAM,EAAE6B,YAAY,EAAE,EAAE;MAChF,IAAIF,SAAS,CAACE,YAAY,CAAC,IAAI,IAAI,EAAE;QACjC;MACJ;MACA,IAAIW,qBAAqB,GAAGb,SAAS,CAACE,YAAY,CAAC,CAACQ,YAAY,CAAC,CAAC;MAClE,IAAII,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAIf,aAAa,CAAC,UAAU,CAAC,EAAEA,aAAa,GAAG,IAAI,CAACf,kBAAkB,GAAG,CAAC,IAAI8B,gBAAgB,GAAG,IAAI,CAAC/B,sBAAsB,EAAEgB,aAAa,EAAE,EAAE;QAChJ,IAAIY,QAAQ,GAAG,IAAI,CAACzB,sBAAsB,CAACa,aAAa,CAAC,CAACE,YAAY,CAAC,CAAC,CAACC,YAAY,CAAC;QACtF,IAAIS,QAAQ,IAAI,IAAI,EAAE;UAClBG,gBAAgB,GAAGlC,eAAe,CAACmC,sBAAsB,CAACF,qBAAqB,EAAEC,gBAAgB,EAAEH,QAAQ,CAAC;UAC5G,IAAI,CAACA,QAAQ,CAACR,iBAAiB,CAAC,CAAC,EAAE;YAC/BN,eAAe,EAAE;UACrB;QACJ;MACJ;IACJ;IACA,OAAOA,eAAe;EAC1B,CAAC;EACDjB,eAAe,CAACmC,sBAAsB,GAAG,UAAUF,qBAAqB,EAAEC,gBAAgB,EAAEH,QAAQ,EAAE;IAClG,IAAIA,QAAQ,IAAI,IAAI,EAAE;MAClB,OAAOG,gBAAgB;IAC3B;IACA,IAAI,CAACH,QAAQ,CAACR,iBAAiB,CAAC,CAAC,EAAE;MAC/B,IAAIQ,QAAQ,CAACK,gBAAgB,CAACH,qBAAqB,CAAC,EAAE;QAClDF,QAAQ,CAACC,YAAY,CAACC,qBAAqB,CAAC;QAC5CC,gBAAgB,GAAG,CAAC;MACxB,CAAC,MACI;QACD,EAAEA,gBAAgB;MACtB;IACJ;IACA,OAAOA,gBAAgB;EAC3B,CAAC;EACDlC,eAAe,CAACQ,SAAS,CAACgB,gBAAgB,GAAG,UAAUL,aAAa,EAAEG,YAAY,EAAEF,SAAS,EAAE;IAC3F,IAAIiB,GAAG,EAAEC,EAAE;IACX,IAAI,IAAI,CAAChC,sBAAsB,CAACa,aAAa,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE;MACxD;IACJ;IACA,IAAIY,QAAQ,GAAGX,SAAS,CAACE,YAAY,CAAC;IACtC,IAAIiB,uBAAuB,GAAG,IAAI,CAACjC,sBAAsB,CAACa,aAAa,GAAG,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;IAC3F,IAAImB,mBAAmB,GAAGD,uBAAuB;IACjD,IAAI,IAAI,CAACjC,sBAAsB,CAACa,aAAa,GAAG,CAAC,CAAC,IAAI,IAAI,EAAE;MACxDqB,mBAAmB,GAAG,IAAI,CAAClC,sBAAsB,CAACa,aAAa,GAAG,CAAC,CAAC,CAACE,YAAY,CAAC,CAAC;IACvF;IACA;IACA,IAAIoB,cAAc,GAAG,IAAIlC,KAAK,CAAC,EAAE,CAAC;IAClCkC,cAAc,CAAC,CAAC,CAAC,GAAGF,uBAAuB,CAACjB,YAAY,CAAC;IACzDmB,cAAc,CAAC,CAAC,CAAC,GAAGD,mBAAmB,CAAClB,YAAY,CAAC;IACrD,IAAIA,YAAY,GAAG,CAAC,EAAE;MAClBmB,cAAc,CAAC,CAAC,CAAC,GAAGrB,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;MAC/CmB,cAAc,CAAC,CAAC,CAAC,GAAGF,uBAAuB,CAACjB,YAAY,GAAG,CAAC,CAAC;MAC7DmB,cAAc,CAAC,CAAC,CAAC,GAAGD,mBAAmB,CAAClB,YAAY,GAAG,CAAC,CAAC;IAC7D;IACA,IAAIA,YAAY,GAAG,CAAC,EAAE;MAClBmB,cAAc,CAAC,CAAC,CAAC,GAAGrB,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;MAC/CmB,cAAc,CAAC,EAAE,CAAC,GAAGF,uBAAuB,CAACjB,YAAY,GAAG,CAAC,CAAC;MAC9DmB,cAAc,CAAC,EAAE,CAAC,GAAGD,mBAAmB,CAAClB,YAAY,GAAG,CAAC,CAAC;IAC9D;IACA,IAAIA,YAAY,GAAGF,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACrCgD,cAAc,CAAC,CAAC,CAAC,GAAGrB,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;MAC/CmB,cAAc,CAAC,CAAC,CAAC,GAAGF,uBAAuB,CAACjB,YAAY,GAAG,CAAC,CAAC;MAC7DmB,cAAc,CAAC,CAAC,CAAC,GAAGD,mBAAmB,CAAClB,YAAY,GAAG,CAAC,CAAC;IAC7D;IACA,IAAIA,YAAY,GAAGF,SAAS,CAAC3B,MAAM,GAAG,CAAC,EAAE;MACrCgD,cAAc,CAAC,CAAC,CAAC,GAAGrB,SAAS,CAACE,YAAY,GAAG,CAAC,CAAC;MAC/CmB,cAAc,CAAC,EAAE,CAAC,GAAGF,uBAAuB,CAACjB,YAAY,GAAG,CAAC,CAAC;MAC9DmB,cAAc,CAAC,EAAE,CAAC,GAAGD,mBAAmB,CAAClB,YAAY,GAAG,CAAC,CAAC;IAC9D;IACA,IAAI;MACA,KAAK,IAAIoB,gBAAgB,GAAGzD,QAAQ,CAACwD,cAAc,CAAC,EAAEE,kBAAkB,GAAGD,gBAAgB,CAAChD,IAAI,CAAC,CAAC,EAAE,CAACiD,kBAAkB,CAAC/C,IAAI,EAAE+C,kBAAkB,GAAGD,gBAAgB,CAAChD,IAAI,CAAC,CAAC,EAAE;QACxK,IAAIkD,aAAa,GAAGD,kBAAkB,CAAChD,KAAK;QAC5C,IAAIK,eAAe,CAAC6C,eAAe,CAACd,QAAQ,EAAEa,aAAa,CAAC,EAAE;UAC1D;QACJ;MACJ;IACJ,CAAC,CACD,OAAOE,KAAK,EAAE;MAAET,GAAG,GAAG;QAAEU,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,kBAAkB,IAAI,CAACA,kBAAkB,CAAC/C,IAAI,KAAK0C,EAAE,GAAGI,gBAAgB,CAACM,MAAM,CAAC,EAAEV,EAAE,CAAC9C,IAAI,CAACkD,gBAAgB,CAAC;MACnH,CAAC,SACO;QAAE,IAAIL,GAAG,EAAE,MAAMA,GAAG,CAACU,KAAK;MAAE;IACxC;EACJ,CAAC;EACD;AACJ;AACA;EACI/C,eAAe,CAAC6C,eAAe,GAAG,UAAUd,QAAQ,EAAEa,aAAa,EAAE;IACjE,IAAIA,aAAa,IAAI,IAAI,EAAE;MACvB,OAAO,KAAK;IAChB;IACA,IAAIA,aAAa,CAACrB,iBAAiB,CAAC,CAAC,IAAIqB,aAAa,CAACK,SAAS,CAAC,CAAC,KAAKlB,QAAQ,CAACkB,SAAS,CAAC,CAAC,EAAE;MACzFlB,QAAQ,CAACC,YAAY,CAACY,aAAa,CAACd,YAAY,CAAC,CAAC,CAAC;MACnD,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB,CAAC;EACD9B,eAAe,CAACQ,SAAS,CAAC0C,qBAAqB,GAAG,YAAY;IAC1D,OAAO,IAAI,CAAC9C,kBAAkB;EAClC,CAAC;EACDJ,eAAe,CAACQ,SAAS,CAAC2C,kBAAkB,GAAG,YAAY;IACvD,OAAO,IAAI,CAAClD,eAAe,CAACmD,WAAW,CAAC,CAAC;EAC7C,CAAC;EACDpD,eAAe,CAACQ,SAAS,CAAC6C,iBAAiB,GAAG,YAAY;IACtD,OAAO,IAAI,CAACpD,eAAe,CAACqD,uBAAuB,CAAC,CAAC;EACzD,CAAC;EACDtD,eAAe,CAACQ,SAAS,CAAC+C,cAAc,GAAG,UAAUrD,WAAW,EAAE;IAC9D,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC,CAAC;EACDF,eAAe,CAACQ,SAAS,CAACgD,cAAc,GAAG,YAAY;IACnD,OAAO,IAAI,CAACtD,WAAW;EAC3B,CAAC;EACDF,eAAe,CAACQ,SAAS,CAACiD,wBAAwB,GAAG,UAAUtC,aAAa,EAAEJ,qBAAqB,EAAE;IACjG,IAAI,CAACT,sBAAsB,CAACa,aAAa,CAAC,GAAGJ,qBAAqB;EACtE,CAAC;EACDf,eAAe,CAACQ,SAAS,CAACkD,wBAAwB,GAAG,UAAUvC,aAAa,EAAE;IAC1E,OAAO,IAAI,CAACb,sBAAsB,CAACa,aAAa,CAAC;EACrD,CAAC;EACD;EACAnB,eAAe,CAACQ,SAAS,CAACmD,QAAQ,GAAG,YAAY;IAC7C,IAAIC,kBAAkB,GAAG,IAAI,CAACtD,sBAAsB,CAAC,CAAC,CAAC;IACvD,IAAIsD,kBAAkB,IAAI,IAAI,EAAE;MAC5BA,kBAAkB,GAAG,IAAI,CAACtD,sBAAsB,CAAC,IAAI,CAACF,kBAAkB,GAAG,CAAC,CAAC;IACjF;IACA;IACA,IAAIyD,SAAS,GAAG,IAAI9D,SAAS,CAAC,CAAC;IAC/B;IACA,KAAK,IAAIuB,YAAY,CAAC,UAAU,CAAC,EAAEA,YAAY,GAAGsC,kBAAkB,CAACvC,YAAY,CAAC,CAAC,CAAC5B,MAAM,EAAE6B,YAAY,EAAE,EAAE;MACxGuC,SAAS,CAACC,MAAM,CAAC,SAAS,EAAExC,YAAY,CAAC;MACzC,KAAK,IAAIH,aAAa,CAAC,UAAU,CAAC,EAAEA,aAAa,GAAG,IAAI,CAACf,kBAAkB,GAAG,CAAC,EAAEe,aAAa,EAAE,EAAE;QAC9F,IAAI,IAAI,CAACb,sBAAsB,CAACa,aAAa,CAAC,IAAI,IAAI,EAAE;UACpD0C,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC;UAC5B;QACJ;QACA,IAAI/B,QAAQ,GAAG,IAAI,CAACzB,sBAAsB,CAACa,aAAa,CAAC,CAACE,YAAY,CAAC,CAAC,CAACC,YAAY,CAAC;QACtF,IAAIS,QAAQ,IAAI,IAAI,EAAE;UAClB8B,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC;UAC5B;QACJ;QACAD,SAAS,CAACC,MAAM,CAAC,UAAU,EAAE/B,QAAQ,CAACD,YAAY,CAAC,CAAC,EAAEC,QAAQ,CAACgC,QAAQ,CAAC,CAAC,CAAC;MAC9E;MACAF,SAAS,CAACC,MAAM,CAAC,IAAI,CAAC;IAC1B;IACA,OAAOD,SAAS,CAACF,QAAQ,CAAC,CAAC;IAC3B;EACJ,CAAC;EACD,OAAO3D,eAAe;AAC1B,CAAC,CAAC,CAAE;AACJ,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}