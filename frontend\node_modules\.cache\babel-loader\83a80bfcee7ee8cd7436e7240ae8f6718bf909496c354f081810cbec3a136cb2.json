{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport Result from '../Result';\nimport NotFoundException from '../NotFoundException';\nimport EAN13Reader from './EAN13Reader';\nimport UPCEANReader from './UPCEANReader';\n/**\n * Encapsulates functionality and implementation that is common to all families\n * of one-dimensional barcodes.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Sam Rudloff)\n *\n * @source https://github.com/zxing/zxing/blob/3c96923276dd5785d58eb970b6ba3f80d36a9505/core/src/main/java/com/google/zxing/oned/UPCAReader.java\n *\n * @experimental\n */\nvar UPCAReader = /** @class */function (_super) {\n  __extends(UPCAReader, _super);\n  function UPCAReader() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.ean13Reader = new EAN13Reader();\n    return _this;\n  }\n  // @Override\n  UPCAReader.prototype.getBarcodeFormat = function () {\n    return BarcodeFormat.UPC_A;\n  };\n  // Note that we don't try rotation without the try harder flag, even if rotation was supported.\n  // @Override\n  UPCAReader.prototype.decode = function (image, hints) {\n    return this.maybeReturnResult(this.ean13Reader.decode(image));\n  };\n  // @Override\n  UPCAReader.prototype.decodeRow = function (rowNumber, row, hints) {\n    return this.maybeReturnResult(this.ean13Reader.decodeRow(rowNumber, row, hints));\n  };\n  // @Override\n  UPCAReader.prototype.decodeMiddle = function (row, startRange, resultString) {\n    return this.ean13Reader.decodeMiddle(row, startRange, resultString);\n  };\n  UPCAReader.prototype.maybeReturnResult = function (result) {\n    var text = result.getText();\n    if (text.charAt(0) === '0') {\n      var upcaResult = new Result(text.substring(1), null, null, result.getResultPoints(), BarcodeFormat.UPC_A);\n      if (result.getResultMetadata() != null) {\n        upcaResult.putAllMetadata(result.getResultMetadata());\n      }\n      return upcaResult;\n    } else {\n      throw new NotFoundException();\n    }\n  };\n  UPCAReader.prototype.reset = function () {\n    this.ean13Reader.reset();\n  };\n  return UPCAReader;\n}(UPCEANReader);\nexport default UPCAReader;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "BarcodeFormat", "Result", "NotFoundException", "EAN13Reader", "UPCEANReader", "UPCAReader", "_super", "_this", "apply", "arguments", "ean13<PERSON><PERSON>er", "getBarcodeFormat", "UPC_A", "decode", "image", "hints", "maybeReturnResult", "decodeRow", "rowNumber", "row", "decodeMiddle", "startRange", "resultString", "result", "text", "getText", "char<PERSON>t", "upcaResult", "substring", "getResultPoints", "getResultMetadata", "putAllMetadata", "reset"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/UPCAReader.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n/*namespace com.google.zxing.oned {*/\nimport BarcodeFormat from '../BarcodeFormat';\nimport Result from '../Result';\nimport NotFoundException from '../NotFoundException';\nimport EAN13Reader from './EAN13Reader';\nimport UPCEANReader from './UPCEANReader';\n/**\n * Encapsulates functionality and implementation that is common to all families\n * of one-dimensional barcodes.\n *\n * <AUTHOR> (Daniel Switkin)\n * <AUTHOR> Owen\n * <AUTHOR> (Sam Rudloff)\n *\n * @source https://github.com/zxing/zxing/blob/3c96923276dd5785d58eb970b6ba3f80d36a9505/core/src/main/java/com/google/zxing/oned/UPCAReader.java\n *\n * @experimental\n */\nvar UPCAReader = /** @class */ (function (_super) {\n    __extends(UPCAReader, _super);\n    function UPCAReader() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.ean13Reader = new EAN13Reader();\n        return _this;\n    }\n    // @Override\n    UPCAReader.prototype.getBarcodeFormat = function () {\n        return BarcodeFormat.UPC_A;\n    };\n    // Note that we don't try rotation without the try harder flag, even if rotation was supported.\n    // @Override\n    UPCAReader.prototype.decode = function (image, hints) {\n        return this.maybeReturnResult(this.ean13Reader.decode(image));\n    };\n    // @Override\n    UPCAReader.prototype.decodeRow = function (rowNumber, row, hints) {\n        return this.maybeReturnResult(this.ean13Reader.decodeRow(rowNumber, row, hints));\n    };\n    // @Override\n    UPCAReader.prototype.decodeMiddle = function (row, startRange, resultString) {\n        return this.ean13Reader.decodeMiddle(row, startRange, resultString);\n    };\n    UPCAReader.prototype.maybeReturnResult = function (result) {\n        var text = result.getText();\n        if (text.charAt(0) === '0') {\n            var upcaResult = new Result(text.substring(1), null, null, result.getResultPoints(), BarcodeFormat.UPC_A);\n            if (result.getResultMetadata() != null) {\n                upcaResult.putAllMetadata(result.getResultMetadata());\n            }\n            return upcaResult;\n        }\n        else {\n            throw new NotFoundException();\n        }\n    };\n    UPCAReader.prototype.reset = function () {\n        this.ean13Reader.reset();\n    };\n    return UPCAReader;\n}(UPCEANReader));\nexport default UPCAReader;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA,OAAOI,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC9CpB,SAAS,CAACmB,UAAU,EAAEC,MAAM,CAAC;EAC7B,SAASD,UAAUA,CAAA,EAAG;IAClB,IAAIE,KAAK,GAAGD,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;IACpEF,KAAK,CAACG,WAAW,GAAG,IAAIP,WAAW,CAAC,CAAC;IACrC,OAAOI,KAAK;EAChB;EACA;EACAF,UAAU,CAACP,SAAS,CAACa,gBAAgB,GAAG,YAAY;IAChD,OAAOX,aAAa,CAACY,KAAK;EAC9B,CAAC;EACD;EACA;EACAP,UAAU,CAACP,SAAS,CAACe,MAAM,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAClD,OAAO,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACN,WAAW,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC;EACjE,CAAC;EACD;EACAT,UAAU,CAACP,SAAS,CAACmB,SAAS,GAAG,UAAUC,SAAS,EAAEC,GAAG,EAAEJ,KAAK,EAAE;IAC9D,OAAO,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACN,WAAW,CAACO,SAAS,CAACC,SAAS,EAAEC,GAAG,EAAEJ,KAAK,CAAC,CAAC;EACpF,CAAC;EACD;EACAV,UAAU,CAACP,SAAS,CAACsB,YAAY,GAAG,UAAUD,GAAG,EAAEE,UAAU,EAAEC,YAAY,EAAE;IACzE,OAAO,IAAI,CAACZ,WAAW,CAACU,YAAY,CAACD,GAAG,EAAEE,UAAU,EAAEC,YAAY,CAAC;EACvE,CAAC;EACDjB,UAAU,CAACP,SAAS,CAACkB,iBAAiB,GAAG,UAAUO,MAAM,EAAE;IACvD,IAAIC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAAC,CAAC;IAC3B,IAAID,IAAI,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACxB,IAAIC,UAAU,GAAG,IAAI1B,MAAM,CAACuB,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAEL,MAAM,CAACM,eAAe,CAAC,CAAC,EAAE7B,aAAa,CAACY,KAAK,CAAC;MACzG,IAAIW,MAAM,CAACO,iBAAiB,CAAC,CAAC,IAAI,IAAI,EAAE;QACpCH,UAAU,CAACI,cAAc,CAACR,MAAM,CAACO,iBAAiB,CAAC,CAAC,CAAC;MACzD;MACA,OAAOH,UAAU;IACrB,CAAC,MACI;MACD,MAAM,IAAIzB,iBAAiB,CAAC,CAAC;IACjC;EACJ,CAAC;EACDG,UAAU,CAACP,SAAS,CAACkC,KAAK,GAAG,YAAY;IACrC,IAAI,CAACtB,WAAW,CAACsB,KAAK,CAAC,CAAC;EAC5B,CAAC;EACD,OAAO3B,UAAU;AACrB,CAAC,CAACD,YAAY,CAAE;AAChB,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}