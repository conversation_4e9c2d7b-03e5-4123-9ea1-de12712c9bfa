{"ast": null, "code": "/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport ResultPoint from '../../ResultPoint';\nimport AztecDetectorResult from '../AztecDetectorResult';\nimport MathUtils from '../../common/detector/MathUtils';\nimport WhiteRectangleDetector from '../../common/detector/WhiteRectangleDetector';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport NotFoundException from '../../NotFoundException';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport Integer from '../../util/Integer';\nvar Point = /** @class */function () {\n  function Point(x, y) {\n    this.x = x;\n    this.y = y;\n  }\n  Point.prototype.toResultPoint = function () {\n    return new ResultPoint(this.getX(), this.getY());\n  };\n  Point.prototype.getX = function () {\n    return this.x;\n  };\n  Point.prototype.getY = function () {\n    return this.y;\n  };\n  return Point;\n}();\nexport { Point };\n/**\n * Encapsulates logic that can detect an Aztec Code in an image, even if the Aztec Code\n * is rotated or skewed, or partially obscured.\n *\n * <AUTHOR> Olivier\n * <AUTHOR> Yellin\n */\nvar Detector = /** @class */function () {\n  function Detector(image) {\n    this.EXPECTED_CORNER_BITS = new Int32Array([0xee0, 0x1dc, 0x83b, 0x707]);\n    this.image = image;\n  }\n  Detector.prototype.detect = function () {\n    return this.detectMirror(false);\n  };\n  /**\n   * Detects an Aztec Code in an image.\n   *\n   * @param isMirror if true, image is a mirror-image of original\n   * @return {@link AztecDetectorResult} encapsulating results of detecting an Aztec Code\n   * @throws NotFoundException if no Aztec Code can be found\n   */\n  Detector.prototype.detectMirror = function (isMirror) {\n    // 1. Get the center of the aztec matrix\n    var pCenter = this.getMatrixCenter();\n    // 2. Get the center points of the four diagonal points just outside the bull's eye\n    //  [topRight, bottomRight, bottomLeft, topLeft]\n    var bullsEyeCorners = this.getBullsEyeCorners(pCenter);\n    if (isMirror) {\n      var temp = bullsEyeCorners[0];\n      bullsEyeCorners[0] = bullsEyeCorners[2];\n      bullsEyeCorners[2] = temp;\n    }\n    // 3. Get the size of the matrix and other parameters from the bull's eye\n    this.extractParameters(bullsEyeCorners);\n    // 4. Sample the grid\n    var bits = this.sampleGrid(this.image, bullsEyeCorners[this.shift % 4], bullsEyeCorners[(this.shift + 1) % 4], bullsEyeCorners[(this.shift + 2) % 4], bullsEyeCorners[(this.shift + 3) % 4]);\n    // 5. Get the corners of the matrix.\n    var corners = this.getMatrixCornerPoints(bullsEyeCorners);\n    return new AztecDetectorResult(bits, corners, this.compact, this.nbDataBlocks, this.nbLayers);\n  };\n  /**\n   * Extracts the number of data layers and data blocks from the layer around the bull's eye.\n   *\n   * @param bullsEyeCorners the array of bull's eye corners\n   * @throws NotFoundException in case of too many errors or invalid parameters\n   */\n  Detector.prototype.extractParameters = function (bullsEyeCorners) {\n    if (!this.isValidPoint(bullsEyeCorners[0]) || !this.isValidPoint(bullsEyeCorners[1]) || !this.isValidPoint(bullsEyeCorners[2]) || !this.isValidPoint(bullsEyeCorners[3])) {\n      throw new NotFoundException();\n    }\n    var length = 2 * this.nbCenterLayers;\n    // Get the bits around the bull's eye\n    var sides = new Int32Array([this.sampleLine(bullsEyeCorners[0], bullsEyeCorners[1], length), this.sampleLine(bullsEyeCorners[1], bullsEyeCorners[2], length), this.sampleLine(bullsEyeCorners[2], bullsEyeCorners[3], length), this.sampleLine(bullsEyeCorners[3], bullsEyeCorners[0], length) // Top\n    ]);\n    // bullsEyeCorners[shift] is the corner of the bulls'eye that has three\n    // orientation marks.\n    // sides[shift] is the row/column that goes from the corner with three\n    // orientation marks to the corner with two.\n    this.shift = this.getRotation(sides, length);\n    // Flatten the parameter bits into a single 28- or 40-bit long\n    var parameterData = 0;\n    for (var i = 0; i < 4; i++) {\n      var side = sides[(this.shift + i) % 4];\n      if (this.compact) {\n        // Each side of the form ..XXXXXXX. where Xs are parameter data\n        parameterData <<= 7;\n        parameterData += side >> 1 & 0x7F;\n      } else {\n        // Each side of the form ..XXXXX.XXXXX. where Xs are parameter data\n        parameterData <<= 10;\n        parameterData += (side >> 2 & 0x1f << 5) + (side >> 1 & 0x1F);\n      }\n    }\n    // Corrects parameter data using RS.  Returns just the data portion\n    // without the error correction.\n    var correctedData = this.getCorrectedParameterData(parameterData, this.compact);\n    if (this.compact) {\n      // 8 bits:  2 bits layers and 6 bits data blocks\n      this.nbLayers = (correctedData >> 6) + 1;\n      this.nbDataBlocks = (correctedData & 0x3F) + 1;\n    } else {\n      // 16 bits:  5 bits layers and 11 bits data blocks\n      this.nbLayers = (correctedData >> 11) + 1;\n      this.nbDataBlocks = (correctedData & 0x7FF) + 1;\n    }\n  };\n  Detector.prototype.getRotation = function (sides, length) {\n    // In a normal pattern, we expect to See\n    //   **    .*             D       A\n    //   *      *\n    //\n    //   .      *\n    //   ..    ..             C       B\n    //\n    // Grab the 3 bits from each of the sides the form the locator pattern and concatenate\n    // into a 12-bit integer.  Start with the bit at A\n    var cornerBits = 0;\n    sides.forEach(function (side, idx, arr) {\n      // XX......X where X's are orientation marks\n      var t = (side >> length - 2 << 1) + (side & 1);\n      cornerBits = (cornerBits << 3) + t;\n    });\n    // for (var side in sides) {\n    //     // XX......X where X's are orientation marks\n    //     var t = ((side >> (length - 2)) << 1) + (side & 1);\n    //     cornerBits = (cornerBits << 3) + t;\n    // }\n    // Mov the bottom bit to the top, so that the three bits of the locator pattern at A are\n    // together.  cornerBits is now:\n    //  3 orientation bits at A || 3 orientation bits at B || ... || 3 orientation bits at D\n    cornerBits = ((cornerBits & 1) << 11) + (cornerBits >> 1);\n    // The result shift indicates which element of BullsEyeCorners[] goes into the top-left\n    // corner. Since the four rotation values have a Hamming distance of 8, we\n    // can easily tolerate two errors.\n    for (var shift = 0; shift < 4; shift++) {\n      if (Integer.bitCount(cornerBits ^ this.EXPECTED_CORNER_BITS[shift]) <= 2) {\n        return shift;\n      }\n    }\n    throw new NotFoundException();\n  };\n  /**\n   * Corrects the parameter bits using Reed-Solomon algorithm.\n   *\n   * @param parameterData parameter bits\n   * @param compact true if this is a compact Aztec code\n   * @throws NotFoundException if the array contains too many errors\n   */\n  Detector.prototype.getCorrectedParameterData = function (parameterData, compact) {\n    var numCodewords;\n    var numDataCodewords;\n    if (compact) {\n      numCodewords = 7;\n      numDataCodewords = 2;\n    } else {\n      numCodewords = 10;\n      numDataCodewords = 4;\n    }\n    var numECCodewords = numCodewords - numDataCodewords;\n    var parameterWords = new Int32Array(numCodewords);\n    for (var i = numCodewords - 1; i >= 0; --i) {\n      parameterWords[i] = parameterData & 0xF;\n      parameterData >>= 4;\n    }\n    try {\n      var rsDecoder = new ReedSolomonDecoder(GenericGF.AZTEC_PARAM);\n      rsDecoder.decode(parameterWords, numECCodewords);\n    } catch (ignored) {\n      throw new NotFoundException();\n    }\n    // Toss the error correction.  Just return the data as an integer\n    var result = 0;\n    for (var i = 0; i < numDataCodewords; i++) {\n      result = (result << 4) + parameterWords[i];\n    }\n    return result;\n  };\n  /**\n   * Finds the corners of a bull-eye centered on the passed point.\n   * This returns the centers of the diagonal points just outside the bull's eye\n   * Returns [topRight, bottomRight, bottomLeft, topLeft]\n   *\n   * @param pCenter Center point\n   * @return The corners of the bull-eye\n   * @throws NotFoundException If no valid bull-eye can be found\n   */\n  Detector.prototype.getBullsEyeCorners = function (pCenter) {\n    var pina = pCenter;\n    var pinb = pCenter;\n    var pinc = pCenter;\n    var pind = pCenter;\n    var color = true;\n    for (this.nbCenterLayers = 1; this.nbCenterLayers < 9; this.nbCenterLayers++) {\n      var pouta = this.getFirstDifferent(pina, color, 1, -1);\n      var poutb = this.getFirstDifferent(pinb, color, 1, 1);\n      var poutc = this.getFirstDifferent(pinc, color, -1, 1);\n      var poutd = this.getFirstDifferent(pind, color, -1, -1);\n      // d      a\n      //\n      // c      b\n      if (this.nbCenterLayers > 2) {\n        var q = this.distancePoint(poutd, pouta) * this.nbCenterLayers / (this.distancePoint(pind, pina) * (this.nbCenterLayers + 2));\n        if (q < 0.75 || q > 1.25 || !this.isWhiteOrBlackRectangle(pouta, poutb, poutc, poutd)) {\n          break;\n        }\n      }\n      pina = pouta;\n      pinb = poutb;\n      pinc = poutc;\n      pind = poutd;\n      color = !color;\n    }\n    if (this.nbCenterLayers !== 5 && this.nbCenterLayers !== 7) {\n      throw new NotFoundException();\n    }\n    this.compact = this.nbCenterLayers === 5;\n    // Expand the square by .5 pixel in each direction so that we're on the border\n    // between the white square and the black square\n    var pinax = new ResultPoint(pina.getX() + 0.5, pina.getY() - 0.5);\n    var pinbx = new ResultPoint(pinb.getX() + 0.5, pinb.getY() + 0.5);\n    var pincx = new ResultPoint(pinc.getX() - 0.5, pinc.getY() + 0.5);\n    var pindx = new ResultPoint(pind.getX() - 0.5, pind.getY() - 0.5);\n    // Expand the square so that its corners are the centers of the points\n    // just outside the bull's eye.\n    return this.expandSquare([pinax, pinbx, pincx, pindx], 2 * this.nbCenterLayers - 3, 2 * this.nbCenterLayers);\n  };\n  /**\n   * Finds a candidate center point of an Aztec code from an image\n   *\n   * @return the center point\n   */\n  Detector.prototype.getMatrixCenter = function () {\n    var pointA;\n    var pointB;\n    var pointC;\n    var pointD;\n    // Get a white rectangle that can be the border of the matrix in center bull's eye or\n    try {\n      var cornerPoints = new WhiteRectangleDetector(this.image).detect();\n      pointA = cornerPoints[0];\n      pointB = cornerPoints[1];\n      pointC = cornerPoints[2];\n      pointD = cornerPoints[3];\n    } catch (e) {\n      // This exception can be in case the initial rectangle is white\n      // In that case, surely in the bull's eye, we try to expand the rectangle.\n      var cx_1 = this.image.getWidth() / 2;\n      var cy_1 = this.image.getHeight() / 2;\n      pointA = this.getFirstDifferent(new Point(cx_1 + 7, cy_1 - 7), false, 1, -1).toResultPoint();\n      pointB = this.getFirstDifferent(new Point(cx_1 + 7, cy_1 + 7), false, 1, 1).toResultPoint();\n      pointC = this.getFirstDifferent(new Point(cx_1 - 7, cy_1 + 7), false, -1, 1).toResultPoint();\n      pointD = this.getFirstDifferent(new Point(cx_1 - 7, cy_1 - 7), false, -1, -1).toResultPoint();\n    }\n    // Compute the center of the rectangle\n    var cx = MathUtils.round((pointA.getX() + pointD.getX() + pointB.getX() + pointC.getX()) / 4.0);\n    var cy = MathUtils.round((pointA.getY() + pointD.getY() + pointB.getY() + pointC.getY()) / 4.0);\n    // Redetermine the white rectangle starting from previously computed center.\n    // This will ensure that we end up with a white rectangle in center bull's eye\n    // in order to compute a more accurate center.\n    try {\n      var cornerPoints = new WhiteRectangleDetector(this.image, 15, cx, cy).detect();\n      pointA = cornerPoints[0];\n      pointB = cornerPoints[1];\n      pointC = cornerPoints[2];\n      pointD = cornerPoints[3];\n    } catch (e) {\n      // This exception can be in case the initial rectangle is white\n      // In that case we try to expand the rectangle.\n      pointA = this.getFirstDifferent(new Point(cx + 7, cy - 7), false, 1, -1).toResultPoint();\n      pointB = this.getFirstDifferent(new Point(cx + 7, cy + 7), false, 1, 1).toResultPoint();\n      pointC = this.getFirstDifferent(new Point(cx - 7, cy + 7), false, -1, 1).toResultPoint();\n      pointD = this.getFirstDifferent(new Point(cx - 7, cy - 7), false, -1, -1).toResultPoint();\n    }\n    // Recompute the center of the rectangle\n    cx = MathUtils.round((pointA.getX() + pointD.getX() + pointB.getX() + pointC.getX()) / 4.0);\n    cy = MathUtils.round((pointA.getY() + pointD.getY() + pointB.getY() + pointC.getY()) / 4.0);\n    return new Point(cx, cy);\n  };\n  /**\n   * Gets the Aztec code corners from the bull's eye corners and the parameters.\n   *\n   * @param bullsEyeCorners the array of bull's eye corners\n   * @return the array of aztec code corners\n   */\n  Detector.prototype.getMatrixCornerPoints = function (bullsEyeCorners) {\n    return this.expandSquare(bullsEyeCorners, 2 * this.nbCenterLayers, this.getDimension());\n  };\n  /**\n   * Creates a BitMatrix by sampling the provided image.\n   * topLeft, topRight, bottomRight, and bottomLeft are the centers of the squares on the\n   * diagonal just outside the bull's eye.\n   */\n  Detector.prototype.sampleGrid = function (image, topLeft, topRight, bottomRight, bottomLeft) {\n    var sampler = GridSamplerInstance.getInstance();\n    var dimension = this.getDimension();\n    var low = dimension / 2 - this.nbCenterLayers;\n    var high = dimension / 2 + this.nbCenterLayers;\n    return sampler.sampleGrid(image, dimension, dimension, low, low,\n    // topleft\n    high, low,\n    // topright\n    high, high,\n    // bottomright\n    low, high,\n    // bottomleft\n    topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRight.getX(), bottomRight.getY(), bottomLeft.getX(), bottomLeft.getY());\n  };\n  /**\n   * Samples a line.\n   *\n   * @param p1   start point (inclusive)\n   * @param p2   end point (exclusive)\n   * @param size number of bits\n   * @return the array of bits as an int (first bit is high-order bit of result)\n   */\n  Detector.prototype.sampleLine = function (p1, p2, size) {\n    var result = 0;\n    var d = this.distanceResultPoint(p1, p2);\n    var moduleSize = d / size;\n    var px = p1.getX();\n    var py = p1.getY();\n    var dx = moduleSize * (p2.getX() - p1.getX()) / d;\n    var dy = moduleSize * (p2.getY() - p1.getY()) / d;\n    for (var i = 0; i < size; i++) {\n      if (this.image.get(MathUtils.round(px + i * dx), MathUtils.round(py + i * dy))) {\n        result |= 1 << size - i - 1;\n      }\n    }\n    return result;\n  };\n  /**\n   * @return true if the border of the rectangle passed in parameter is compound of white points only\n   *         or black points only\n   */\n  Detector.prototype.isWhiteOrBlackRectangle = function (p1, p2, p3, p4) {\n    var corr = 3;\n    p1 = new Point(p1.getX() - corr, p1.getY() + corr);\n    p2 = new Point(p2.getX() - corr, p2.getY() - corr);\n    p3 = new Point(p3.getX() + corr, p3.getY() - corr);\n    p4 = new Point(p4.getX() + corr, p4.getY() + corr);\n    var cInit = this.getColor(p4, p1);\n    if (cInit === 0) {\n      return false;\n    }\n    var c = this.getColor(p1, p2);\n    if (c !== cInit) {\n      return false;\n    }\n    c = this.getColor(p2, p3);\n    if (c !== cInit) {\n      return false;\n    }\n    c = this.getColor(p3, p4);\n    return c === cInit;\n  };\n  /**\n   * Gets the color of a segment\n   *\n   * @return 1 if segment more than 90% black, -1 if segment is more than 90% white, 0 else\n   */\n  Detector.prototype.getColor = function (p1, p2) {\n    var d = this.distancePoint(p1, p2);\n    var dx = (p2.getX() - p1.getX()) / d;\n    var dy = (p2.getY() - p1.getY()) / d;\n    var error = 0;\n    var px = p1.getX();\n    var py = p1.getY();\n    var colorModel = this.image.get(p1.getX(), p1.getY());\n    var iMax = Math.ceil(d);\n    for (var i = 0; i < iMax; i++) {\n      px += dx;\n      py += dy;\n      if (this.image.get(MathUtils.round(px), MathUtils.round(py)) !== colorModel) {\n        error++;\n      }\n    }\n    var errRatio = error / d;\n    if (errRatio > 0.1 && errRatio < 0.9) {\n      return 0;\n    }\n    return errRatio <= 0.1 === colorModel ? 1 : -1;\n  };\n  /**\n   * Gets the coordinate of the first point with a different color in the given direction\n   */\n  Detector.prototype.getFirstDifferent = function (init, color, dx, dy) {\n    var x = init.getX() + dx;\n    var y = init.getY() + dy;\n    while (this.isValid(x, y) && this.image.get(x, y) === color) {\n      x += dx;\n      y += dy;\n    }\n    x -= dx;\n    y -= dy;\n    while (this.isValid(x, y) && this.image.get(x, y) === color) {\n      x += dx;\n    }\n    x -= dx;\n    while (this.isValid(x, y) && this.image.get(x, y) === color) {\n      y += dy;\n    }\n    y -= dy;\n    return new Point(x, y);\n  };\n  /**\n   * Expand the square represented by the corner points by pushing out equally in all directions\n   *\n   * @param cornerPoints the corners of the square, which has the bull's eye at its center\n   * @param oldSide the original length of the side of the square in the target bit matrix\n   * @param newSide the new length of the size of the square in the target bit matrix\n   * @return the corners of the expanded square\n   */\n  Detector.prototype.expandSquare = function (cornerPoints, oldSide, newSide) {\n    var ratio = newSide / (2.0 * oldSide);\n    var dx = cornerPoints[0].getX() - cornerPoints[2].getX();\n    var dy = cornerPoints[0].getY() - cornerPoints[2].getY();\n    var centerx = (cornerPoints[0].getX() + cornerPoints[2].getX()) / 2.0;\n    var centery = (cornerPoints[0].getY() + cornerPoints[2].getY()) / 2.0;\n    var result0 = new ResultPoint(centerx + ratio * dx, centery + ratio * dy);\n    var result2 = new ResultPoint(centerx - ratio * dx, centery - ratio * dy);\n    dx = cornerPoints[1].getX() - cornerPoints[3].getX();\n    dy = cornerPoints[1].getY() - cornerPoints[3].getY();\n    centerx = (cornerPoints[1].getX() + cornerPoints[3].getX()) / 2.0;\n    centery = (cornerPoints[1].getY() + cornerPoints[3].getY()) / 2.0;\n    var result1 = new ResultPoint(centerx + ratio * dx, centery + ratio * dy);\n    var result3 = new ResultPoint(centerx - ratio * dx, centery - ratio * dy);\n    var results = [result0, result1, result2, result3];\n    return results;\n  };\n  Detector.prototype.isValid = function (x, y) {\n    return x >= 0 && x < this.image.getWidth() && y > 0 && y < this.image.getHeight();\n  };\n  Detector.prototype.isValidPoint = function (point) {\n    var x = MathUtils.round(point.getX());\n    var y = MathUtils.round(point.getY());\n    return this.isValid(x, y);\n  };\n  Detector.prototype.distancePoint = function (a, b) {\n    return MathUtils.distance(a.getX(), a.getY(), b.getX(), b.getY());\n  };\n  Detector.prototype.distanceResultPoint = function (a, b) {\n    return MathUtils.distance(a.getX(), a.getY(), b.getX(), b.getY());\n  };\n  Detector.prototype.getDimension = function () {\n    if (this.compact) {\n      return 4 * this.nbLayers + 11;\n    }\n    if (this.nbLayers <= 4) {\n      return 4 * this.nbLayers + 15;\n    }\n    return 4 * this.nbLayers + 2 * (Integer.truncDivision(this.nbLayers - 4, 8) + 1) + 15;\n  };\n  return Detector;\n}();\nexport default Detector;", "map": {"version": 3, "names": ["ResultPoint", "AztecDetectorResult", "MathUtils", "WhiteRectangleDetector", "GenericGF", "ReedSolomonDecoder", "NotFoundException", "GridSamplerInstance", "Integer", "Point", "x", "y", "prototype", "toResultPoint", "getX", "getY", "Detector", "image", "EXPECTED_CORNER_BITS", "Int32Array", "detect", "detectMirror", "is<PERSON><PERSON><PERSON><PERSON>", "pCenter", "getMatrixCenter", "bullsEyeCorners", "getBullsEyeCorners", "temp", "extractParameters", "bits", "sampleGrid", "shift", "corners", "getMatrixCornerPoints", "compact", "nbDataBlocks", "nbLayers", "isValidPoint", "length", "nbCenterLayers", "sides", "sampleLine", "getRotation", "parameterData", "i", "side", "correctedData", "getCorrectedParameterData", "cornerBits", "for<PERSON>ach", "idx", "arr", "t", "bitCount", "numCodewords", "numDataCodewords", "numECCodewords", "parameterWords", "rsDecoder", "AZTEC_PARAM", "decode", "ignored", "result", "pina", "pinb", "pinc", "pind", "color", "pouta", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "poutb", "poutc", "poutd", "q", "distancePoint", "isWhiteOrBlackRectangle", "pinax", "pinbx", "pincx", "pindx", "expandSquare", "pointA", "pointB", "pointC", "pointD", "cornerPoints", "e", "cx_1", "getWidth", "cy_1", "getHeight", "cx", "round", "cy", "getDimension", "topLeft", "topRight", "bottomRight", "bottomLeft", "sampler", "getInstance", "dimension", "low", "high", "p1", "p2", "size", "d", "distanceResultPoint", "moduleSize", "px", "py", "dx", "dy", "get", "p3", "p4", "corr", "cInit", "getColor", "c", "error", "colorModel", "iMax", "Math", "ceil", "errRatio", "init", "<PERSON><PERSON><PERSON><PERSON>", "oldSide", "newSide", "ratio", "centerx", "centery", "result0", "result2", "result1", "result3", "results", "point", "a", "b", "distance", "truncDivision"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/detector/Detector.js"], "sourcesContent": ["/*\n * Copyright 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport ResultPoint from '../../ResultPoint';\nimport AztecDetectorResult from '../AztecDetectorResult';\nimport MathUtils from '../../common/detector/MathUtils';\nimport WhiteRectangleDetector from '../../common/detector/WhiteRectangleDetector';\nimport GenericGF from '../../common/reedsolomon/GenericGF';\nimport ReedSolomonDecoder from '../../common/reedsolomon/ReedSolomonDecoder';\nimport NotFoundException from '../../NotFoundException';\nimport GridSamplerInstance from '../../common/GridSamplerInstance';\nimport Integer from '../../util/Integer';\nvar Point = /** @class */ (function () {\n    function Point(x, y) {\n        this.x = x;\n        this.y = y;\n    }\n    Point.prototype.toResultPoint = function () {\n        return new ResultPoint(this.getX(), this.getY());\n    };\n    Point.prototype.getX = function () {\n        return this.x;\n    };\n    Point.prototype.getY = function () {\n        return this.y;\n    };\n    return Point;\n}());\nexport { Point };\n/**\n * Encapsulates logic that can detect an Aztec Code in an image, even if the Aztec Code\n * is rotated or skewed, or partially obscured.\n *\n * <AUTHOR> Olivier\n * <AUTHOR> Yellin\n */\nvar Detector = /** @class */ (function () {\n    function Detector(image) {\n        this.EXPECTED_CORNER_BITS = new Int32Array([\n            0xee0,\n            0x1dc,\n            0x83b,\n            0x707,\n        ]);\n        this.image = image;\n    }\n    Detector.prototype.detect = function () {\n        return this.detectMirror(false);\n    };\n    /**\n     * Detects an Aztec Code in an image.\n     *\n     * @param isMirror if true, image is a mirror-image of original\n     * @return {@link AztecDetectorResult} encapsulating results of detecting an Aztec Code\n     * @throws NotFoundException if no Aztec Code can be found\n     */\n    Detector.prototype.detectMirror = function (isMirror) {\n        // 1. Get the center of the aztec matrix\n        var pCenter = this.getMatrixCenter();\n        // 2. Get the center points of the four diagonal points just outside the bull's eye\n        //  [topRight, bottomRight, bottomLeft, topLeft]\n        var bullsEyeCorners = this.getBullsEyeCorners(pCenter);\n        if (isMirror) {\n            var temp = bullsEyeCorners[0];\n            bullsEyeCorners[0] = bullsEyeCorners[2];\n            bullsEyeCorners[2] = temp;\n        }\n        // 3. Get the size of the matrix and other parameters from the bull's eye\n        this.extractParameters(bullsEyeCorners);\n        // 4. Sample the grid\n        var bits = this.sampleGrid(this.image, bullsEyeCorners[this.shift % 4], bullsEyeCorners[(this.shift + 1) % 4], bullsEyeCorners[(this.shift + 2) % 4], bullsEyeCorners[(this.shift + 3) % 4]);\n        // 5. Get the corners of the matrix.\n        var corners = this.getMatrixCornerPoints(bullsEyeCorners);\n        return new AztecDetectorResult(bits, corners, this.compact, this.nbDataBlocks, this.nbLayers);\n    };\n    /**\n     * Extracts the number of data layers and data blocks from the layer around the bull's eye.\n     *\n     * @param bullsEyeCorners the array of bull's eye corners\n     * @throws NotFoundException in case of too many errors or invalid parameters\n     */\n    Detector.prototype.extractParameters = function (bullsEyeCorners) {\n        if (!this.isValidPoint(bullsEyeCorners[0]) || !this.isValidPoint(bullsEyeCorners[1]) ||\n            !this.isValidPoint(bullsEyeCorners[2]) || !this.isValidPoint(bullsEyeCorners[3])) {\n            throw new NotFoundException();\n        }\n        var length = 2 * this.nbCenterLayers;\n        // Get the bits around the bull's eye\n        var sides = new Int32Array([\n            this.sampleLine(bullsEyeCorners[0], bullsEyeCorners[1], length),\n            this.sampleLine(bullsEyeCorners[1], bullsEyeCorners[2], length),\n            this.sampleLine(bullsEyeCorners[2], bullsEyeCorners[3], length),\n            this.sampleLine(bullsEyeCorners[3], bullsEyeCorners[0], length) // Top\n        ]);\n        // bullsEyeCorners[shift] is the corner of the bulls'eye that has three\n        // orientation marks.\n        // sides[shift] is the row/column that goes from the corner with three\n        // orientation marks to the corner with two.\n        this.shift = this.getRotation(sides, length);\n        // Flatten the parameter bits into a single 28- or 40-bit long\n        var parameterData = 0;\n        for (var i = 0; i < 4; i++) {\n            var side = sides[(this.shift + i) % 4];\n            if (this.compact) {\n                // Each side of the form ..XXXXXXX. where Xs are parameter data\n                parameterData <<= 7;\n                parameterData += (side >> 1) & 0x7F;\n            }\n            else {\n                // Each side of the form ..XXXXX.XXXXX. where Xs are parameter data\n                parameterData <<= 10;\n                parameterData += ((side >> 2) & (0x1f << 5)) + ((side >> 1) & 0x1F);\n            }\n        }\n        // Corrects parameter data using RS.  Returns just the data portion\n        // without the error correction.\n        var correctedData = this.getCorrectedParameterData(parameterData, this.compact);\n        if (this.compact) {\n            // 8 bits:  2 bits layers and 6 bits data blocks\n            this.nbLayers = (correctedData >> 6) + 1;\n            this.nbDataBlocks = (correctedData & 0x3F) + 1;\n        }\n        else {\n            // 16 bits:  5 bits layers and 11 bits data blocks\n            this.nbLayers = (correctedData >> 11) + 1;\n            this.nbDataBlocks = (correctedData & 0x7FF) + 1;\n        }\n    };\n    Detector.prototype.getRotation = function (sides, length) {\n        // In a normal pattern, we expect to See\n        //   **    .*             D       A\n        //   *      *\n        //\n        //   .      *\n        //   ..    ..             C       B\n        //\n        // Grab the 3 bits from each of the sides the form the locator pattern and concatenate\n        // into a 12-bit integer.  Start with the bit at A\n        var cornerBits = 0;\n        sides.forEach(function (side, idx, arr) {\n            // XX......X where X's are orientation marks\n            var t = ((side >> (length - 2)) << 1) + (side & 1);\n            cornerBits = (cornerBits << 3) + t;\n        });\n        // for (var side in sides) {\n        //     // XX......X where X's are orientation marks\n        //     var t = ((side >> (length - 2)) << 1) + (side & 1);\n        //     cornerBits = (cornerBits << 3) + t;\n        // }\n        // Mov the bottom bit to the top, so that the three bits of the locator pattern at A are\n        // together.  cornerBits is now:\n        //  3 orientation bits at A || 3 orientation bits at B || ... || 3 orientation bits at D\n        cornerBits = ((cornerBits & 1) << 11) + (cornerBits >> 1);\n        // The result shift indicates which element of BullsEyeCorners[] goes into the top-left\n        // corner. Since the four rotation values have a Hamming distance of 8, we\n        // can easily tolerate two errors.\n        for (var shift = 0; shift < 4; shift++) {\n            if (Integer.bitCount(cornerBits ^ this.EXPECTED_CORNER_BITS[shift]) <= 2) {\n                return shift;\n            }\n        }\n        throw new NotFoundException();\n    };\n    /**\n     * Corrects the parameter bits using Reed-Solomon algorithm.\n     *\n     * @param parameterData parameter bits\n     * @param compact true if this is a compact Aztec code\n     * @throws NotFoundException if the array contains too many errors\n     */\n    Detector.prototype.getCorrectedParameterData = function (parameterData, compact) {\n        var numCodewords;\n        var numDataCodewords;\n        if (compact) {\n            numCodewords = 7;\n            numDataCodewords = 2;\n        }\n        else {\n            numCodewords = 10;\n            numDataCodewords = 4;\n        }\n        var numECCodewords = numCodewords - numDataCodewords;\n        var parameterWords = new Int32Array(numCodewords);\n        for (var i = numCodewords - 1; i >= 0; --i) {\n            parameterWords[i] = parameterData & 0xF;\n            parameterData >>= 4;\n        }\n        try {\n            var rsDecoder = new ReedSolomonDecoder(GenericGF.AZTEC_PARAM);\n            rsDecoder.decode(parameterWords, numECCodewords);\n        }\n        catch (ignored) {\n            throw new NotFoundException();\n        }\n        // Toss the error correction.  Just return the data as an integer\n        var result = 0;\n        for (var i = 0; i < numDataCodewords; i++) {\n            result = (result << 4) + parameterWords[i];\n        }\n        return result;\n    };\n    /**\n     * Finds the corners of a bull-eye centered on the passed point.\n     * This returns the centers of the diagonal points just outside the bull's eye\n     * Returns [topRight, bottomRight, bottomLeft, topLeft]\n     *\n     * @param pCenter Center point\n     * @return The corners of the bull-eye\n     * @throws NotFoundException If no valid bull-eye can be found\n     */\n    Detector.prototype.getBullsEyeCorners = function (pCenter) {\n        var pina = pCenter;\n        var pinb = pCenter;\n        var pinc = pCenter;\n        var pind = pCenter;\n        var color = true;\n        for (this.nbCenterLayers = 1; this.nbCenterLayers < 9; this.nbCenterLayers++) {\n            var pouta = this.getFirstDifferent(pina, color, 1, -1);\n            var poutb = this.getFirstDifferent(pinb, color, 1, 1);\n            var poutc = this.getFirstDifferent(pinc, color, -1, 1);\n            var poutd = this.getFirstDifferent(pind, color, -1, -1);\n            // d      a\n            //\n            // c      b\n            if (this.nbCenterLayers > 2) {\n                var q = (this.distancePoint(poutd, pouta) * this.nbCenterLayers) / (this.distancePoint(pind, pina) * (this.nbCenterLayers + 2));\n                if (q < 0.75 || q > 1.25 || !this.isWhiteOrBlackRectangle(pouta, poutb, poutc, poutd)) {\n                    break;\n                }\n            }\n            pina = pouta;\n            pinb = poutb;\n            pinc = poutc;\n            pind = poutd;\n            color = !color;\n        }\n        if (this.nbCenterLayers !== 5 && this.nbCenterLayers !== 7) {\n            throw new NotFoundException();\n        }\n        this.compact = this.nbCenterLayers === 5;\n        // Expand the square by .5 pixel in each direction so that we're on the border\n        // between the white square and the black square\n        var pinax = new ResultPoint(pina.getX() + 0.5, pina.getY() - 0.5);\n        var pinbx = new ResultPoint(pinb.getX() + 0.5, pinb.getY() + 0.5);\n        var pincx = new ResultPoint(pinc.getX() - 0.5, pinc.getY() + 0.5);\n        var pindx = new ResultPoint(pind.getX() - 0.5, pind.getY() - 0.5);\n        // Expand the square so that its corners are the centers of the points\n        // just outside the bull's eye.\n        return this.expandSquare([pinax, pinbx, pincx, pindx], 2 * this.nbCenterLayers - 3, 2 * this.nbCenterLayers);\n    };\n    /**\n     * Finds a candidate center point of an Aztec code from an image\n     *\n     * @return the center point\n     */\n    Detector.prototype.getMatrixCenter = function () {\n        var pointA;\n        var pointB;\n        var pointC;\n        var pointD;\n        // Get a white rectangle that can be the border of the matrix in center bull's eye or\n        try {\n            var cornerPoints = new WhiteRectangleDetector(this.image).detect();\n            pointA = cornerPoints[0];\n            pointB = cornerPoints[1];\n            pointC = cornerPoints[2];\n            pointD = cornerPoints[3];\n        }\n        catch (e) {\n            // This exception can be in case the initial rectangle is white\n            // In that case, surely in the bull's eye, we try to expand the rectangle.\n            var cx_1 = this.image.getWidth() / 2;\n            var cy_1 = this.image.getHeight() / 2;\n            pointA = this.getFirstDifferent(new Point(cx_1 + 7, cy_1 - 7), false, 1, -1).toResultPoint();\n            pointB = this.getFirstDifferent(new Point(cx_1 + 7, cy_1 + 7), false, 1, 1).toResultPoint();\n            pointC = this.getFirstDifferent(new Point(cx_1 - 7, cy_1 + 7), false, -1, 1).toResultPoint();\n            pointD = this.getFirstDifferent(new Point(cx_1 - 7, cy_1 - 7), false, -1, -1).toResultPoint();\n        }\n        // Compute the center of the rectangle\n        var cx = MathUtils.round((pointA.getX() + pointD.getX() + pointB.getX() + pointC.getX()) / 4.0);\n        var cy = MathUtils.round((pointA.getY() + pointD.getY() + pointB.getY() + pointC.getY()) / 4.0);\n        // Redetermine the white rectangle starting from previously computed center.\n        // This will ensure that we end up with a white rectangle in center bull's eye\n        // in order to compute a more accurate center.\n        try {\n            var cornerPoints = new WhiteRectangleDetector(this.image, 15, cx, cy).detect();\n            pointA = cornerPoints[0];\n            pointB = cornerPoints[1];\n            pointC = cornerPoints[2];\n            pointD = cornerPoints[3];\n        }\n        catch (e) {\n            // This exception can be in case the initial rectangle is white\n            // In that case we try to expand the rectangle.\n            pointA = this.getFirstDifferent(new Point(cx + 7, cy - 7), false, 1, -1).toResultPoint();\n            pointB = this.getFirstDifferent(new Point(cx + 7, cy + 7), false, 1, 1).toResultPoint();\n            pointC = this.getFirstDifferent(new Point(cx - 7, cy + 7), false, -1, 1).toResultPoint();\n            pointD = this.getFirstDifferent(new Point(cx - 7, cy - 7), false, -1, -1).toResultPoint();\n        }\n        // Recompute the center of the rectangle\n        cx = MathUtils.round((pointA.getX() + pointD.getX() + pointB.getX() + pointC.getX()) / 4.0);\n        cy = MathUtils.round((pointA.getY() + pointD.getY() + pointB.getY() + pointC.getY()) / 4.0);\n        return new Point(cx, cy);\n    };\n    /**\n     * Gets the Aztec code corners from the bull's eye corners and the parameters.\n     *\n     * @param bullsEyeCorners the array of bull's eye corners\n     * @return the array of aztec code corners\n     */\n    Detector.prototype.getMatrixCornerPoints = function (bullsEyeCorners) {\n        return this.expandSquare(bullsEyeCorners, 2 * this.nbCenterLayers, this.getDimension());\n    };\n    /**\n     * Creates a BitMatrix by sampling the provided image.\n     * topLeft, topRight, bottomRight, and bottomLeft are the centers of the squares on the\n     * diagonal just outside the bull's eye.\n     */\n    Detector.prototype.sampleGrid = function (image, topLeft, topRight, bottomRight, bottomLeft) {\n        var sampler = GridSamplerInstance.getInstance();\n        var dimension = this.getDimension();\n        var low = dimension / 2 - this.nbCenterLayers;\n        var high = dimension / 2 + this.nbCenterLayers;\n        return sampler.sampleGrid(image, dimension, dimension, low, low, // topleft\n        high, low, // topright\n        high, high, // bottomright\n        low, high, // bottomleft\n        topLeft.getX(), topLeft.getY(), topRight.getX(), topRight.getY(), bottomRight.getX(), bottomRight.getY(), bottomLeft.getX(), bottomLeft.getY());\n    };\n    /**\n     * Samples a line.\n     *\n     * @param p1   start point (inclusive)\n     * @param p2   end point (exclusive)\n     * @param size number of bits\n     * @return the array of bits as an int (first bit is high-order bit of result)\n     */\n    Detector.prototype.sampleLine = function (p1, p2, size) {\n        var result = 0;\n        var d = this.distanceResultPoint(p1, p2);\n        var moduleSize = d / size;\n        var px = p1.getX();\n        var py = p1.getY();\n        var dx = moduleSize * (p2.getX() - p1.getX()) / d;\n        var dy = moduleSize * (p2.getY() - p1.getY()) / d;\n        for (var i = 0; i < size; i++) {\n            if (this.image.get(MathUtils.round(px + i * dx), MathUtils.round(py + i * dy))) {\n                result |= 1 << (size - i - 1);\n            }\n        }\n        return result;\n    };\n    /**\n     * @return true if the border of the rectangle passed in parameter is compound of white points only\n     *         or black points only\n     */\n    Detector.prototype.isWhiteOrBlackRectangle = function (p1, p2, p3, p4) {\n        var corr = 3;\n        p1 = new Point(p1.getX() - corr, p1.getY() + corr);\n        p2 = new Point(p2.getX() - corr, p2.getY() - corr);\n        p3 = new Point(p3.getX() + corr, p3.getY() - corr);\n        p4 = new Point(p4.getX() + corr, p4.getY() + corr);\n        var cInit = this.getColor(p4, p1);\n        if (cInit === 0) {\n            return false;\n        }\n        var c = this.getColor(p1, p2);\n        if (c !== cInit) {\n            return false;\n        }\n        c = this.getColor(p2, p3);\n        if (c !== cInit) {\n            return false;\n        }\n        c = this.getColor(p3, p4);\n        return c === cInit;\n    };\n    /**\n     * Gets the color of a segment\n     *\n     * @return 1 if segment more than 90% black, -1 if segment is more than 90% white, 0 else\n     */\n    Detector.prototype.getColor = function (p1, p2) {\n        var d = this.distancePoint(p1, p2);\n        var dx = (p2.getX() - p1.getX()) / d;\n        var dy = (p2.getY() - p1.getY()) / d;\n        var error = 0;\n        var px = p1.getX();\n        var py = p1.getY();\n        var colorModel = this.image.get(p1.getX(), p1.getY());\n        var iMax = Math.ceil(d);\n        for (var i = 0; i < iMax; i++) {\n            px += dx;\n            py += dy;\n            if (this.image.get(MathUtils.round(px), MathUtils.round(py)) !== colorModel) {\n                error++;\n            }\n        }\n        var errRatio = error / d;\n        if (errRatio > 0.1 && errRatio < 0.9) {\n            return 0;\n        }\n        return (errRatio <= 0.1) === colorModel ? 1 : -1;\n    };\n    /**\n     * Gets the coordinate of the first point with a different color in the given direction\n     */\n    Detector.prototype.getFirstDifferent = function (init, color, dx, dy) {\n        var x = init.getX() + dx;\n        var y = init.getY() + dy;\n        while (this.isValid(x, y) && this.image.get(x, y) === color) {\n            x += dx;\n            y += dy;\n        }\n        x -= dx;\n        y -= dy;\n        while (this.isValid(x, y) && this.image.get(x, y) === color) {\n            x += dx;\n        }\n        x -= dx;\n        while (this.isValid(x, y) && this.image.get(x, y) === color) {\n            y += dy;\n        }\n        y -= dy;\n        return new Point(x, y);\n    };\n    /**\n     * Expand the square represented by the corner points by pushing out equally in all directions\n     *\n     * @param cornerPoints the corners of the square, which has the bull's eye at its center\n     * @param oldSide the original length of the side of the square in the target bit matrix\n     * @param newSide the new length of the size of the square in the target bit matrix\n     * @return the corners of the expanded square\n     */\n    Detector.prototype.expandSquare = function (cornerPoints, oldSide, newSide) {\n        var ratio = newSide / (2.0 * oldSide);\n        var dx = cornerPoints[0].getX() - cornerPoints[2].getX();\n        var dy = cornerPoints[0].getY() - cornerPoints[2].getY();\n        var centerx = (cornerPoints[0].getX() + cornerPoints[2].getX()) / 2.0;\n        var centery = (cornerPoints[0].getY() + cornerPoints[2].getY()) / 2.0;\n        var result0 = new ResultPoint(centerx + ratio * dx, centery + ratio * dy);\n        var result2 = new ResultPoint(centerx - ratio * dx, centery - ratio * dy);\n        dx = cornerPoints[1].getX() - cornerPoints[3].getX();\n        dy = cornerPoints[1].getY() - cornerPoints[3].getY();\n        centerx = (cornerPoints[1].getX() + cornerPoints[3].getX()) / 2.0;\n        centery = (cornerPoints[1].getY() + cornerPoints[3].getY()) / 2.0;\n        var result1 = new ResultPoint(centerx + ratio * dx, centery + ratio * dy);\n        var result3 = new ResultPoint(centerx - ratio * dx, centery - ratio * dy);\n        var results = [result0, result1, result2, result3];\n        return results;\n    };\n    Detector.prototype.isValid = function (x, y) {\n        return x >= 0 && x < this.image.getWidth() && y > 0 && y < this.image.getHeight();\n    };\n    Detector.prototype.isValidPoint = function (point) {\n        var x = MathUtils.round(point.getX());\n        var y = MathUtils.round(point.getY());\n        return this.isValid(x, y);\n    };\n    Detector.prototype.distancePoint = function (a, b) {\n        return MathUtils.distance(a.getX(), a.getY(), b.getX(), b.getY());\n    };\n    Detector.prototype.distanceResultPoint = function (a, b) {\n        return MathUtils.distance(a.getX(), a.getY(), b.getX(), b.getY());\n    };\n    Detector.prototype.getDimension = function () {\n        if (this.compact) {\n            return 4 * this.nbLayers + 11;\n        }\n        if (this.nbLayers <= 4) {\n            return 4 * this.nbLayers + 15;\n        }\n        return 4 * this.nbLayers + 2 * (Integer.truncDivision((this.nbLayers - 4), 8) + 1) + 15;\n    };\n    return Detector;\n}());\nexport default Detector;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,mBAAmB,MAAM,wBAAwB;AACxD,OAAOC,SAAS,MAAM,iCAAiC;AACvD,OAAOC,sBAAsB,MAAM,8CAA8C;AACjF,OAAOC,SAAS,MAAM,oCAAoC;AAC1D,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,IAAIC,KAAK,GAAG,aAAe,YAAY;EACnC,SAASA,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACjB,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;EACd;EACAF,KAAK,CAACG,SAAS,CAACC,aAAa,GAAG,YAAY;IACxC,OAAO,IAAIb,WAAW,CAAC,IAAI,CAACc,IAAI,CAAC,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;EACpD,CAAC;EACDN,KAAK,CAACG,SAAS,CAACE,IAAI,GAAG,YAAY;IAC/B,OAAO,IAAI,CAACJ,CAAC;EACjB,CAAC;EACDD,KAAK,CAACG,SAAS,CAACG,IAAI,GAAG,YAAY;IAC/B,OAAO,IAAI,CAACJ,CAAC;EACjB,CAAC;EACD,OAAOF,KAAK;AAChB,CAAC,CAAC,CAAE;AACJ,SAASA,KAAK;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIO,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAACC,KAAK,EAAE;IACrB,IAAI,CAACC,oBAAoB,GAAG,IAAIC,UAAU,CAAC,CACvC,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,CACR,CAAC;IACF,IAAI,CAACF,KAAK,GAAGA,KAAK;EACtB;EACAD,QAAQ,CAACJ,SAAS,CAACQ,MAAM,GAAG,YAAY;IACpC,OAAO,IAAI,CAACC,YAAY,CAAC,KAAK,CAAC;EACnC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIL,QAAQ,CAACJ,SAAS,CAACS,YAAY,GAAG,UAAUC,QAAQ,EAAE;IAClD;IACA,IAAIC,OAAO,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACpC;IACA;IACA,IAAIC,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAACH,OAAO,CAAC;IACtD,IAAID,QAAQ,EAAE;MACV,IAAIK,IAAI,GAAGF,eAAe,CAAC,CAAC,CAAC;MAC7BA,eAAe,CAAC,CAAC,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC;MACvCA,eAAe,CAAC,CAAC,CAAC,GAAGE,IAAI;IAC7B;IACA;IACA,IAAI,CAACC,iBAAiB,CAACH,eAAe,CAAC;IACvC;IACA,IAAII,IAAI,GAAG,IAAI,CAACC,UAAU,CAAC,IAAI,CAACb,KAAK,EAAEQ,eAAe,CAAC,IAAI,CAACM,KAAK,GAAG,CAAC,CAAC,EAAEN,eAAe,CAAC,CAAC,IAAI,CAACM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAEN,eAAe,CAAC,CAAC,IAAI,CAACM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAEN,eAAe,CAAC,CAAC,IAAI,CAACM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5L;IACA,IAAIC,OAAO,GAAG,IAAI,CAACC,qBAAqB,CAACR,eAAe,CAAC;IACzD,OAAO,IAAIxB,mBAAmB,CAAC4B,IAAI,EAAEG,OAAO,EAAE,IAAI,CAACE,OAAO,EAAE,IAAI,CAACC,YAAY,EAAE,IAAI,CAACC,QAAQ,CAAC;EACjG,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIpB,QAAQ,CAACJ,SAAS,CAACgB,iBAAiB,GAAG,UAAUH,eAAe,EAAE;IAC9D,IAAI,CAAC,IAAI,CAACY,YAAY,CAACZ,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACY,YAAY,CAACZ,eAAe,CAAC,CAAC,CAAC,CAAC,IAChF,CAAC,IAAI,CAACY,YAAY,CAACZ,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACY,YAAY,CAACZ,eAAe,CAAC,CAAC,CAAC,CAAC,EAAE;MAClF,MAAM,IAAInB,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAIgC,MAAM,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc;IACpC;IACA,IAAIC,KAAK,GAAG,IAAIrB,UAAU,CAAC,CACvB,IAAI,CAACsB,UAAU,CAAChB,eAAe,CAAC,CAAC,CAAC,EAAEA,eAAe,CAAC,CAAC,CAAC,EAAEa,MAAM,CAAC,EAC/D,IAAI,CAACG,UAAU,CAAChB,eAAe,CAAC,CAAC,CAAC,EAAEA,eAAe,CAAC,CAAC,CAAC,EAAEa,MAAM,CAAC,EAC/D,IAAI,CAACG,UAAU,CAAChB,eAAe,CAAC,CAAC,CAAC,EAAEA,eAAe,CAAC,CAAC,CAAC,EAAEa,MAAM,CAAC,EAC/D,IAAI,CAACG,UAAU,CAAChB,eAAe,CAAC,CAAC,CAAC,EAAEA,eAAe,CAAC,CAAC,CAAC,EAAEa,MAAM,CAAC,CAAC;IAAA,CACnE,CAAC;IACF;IACA;IACA;IACA;IACA,IAAI,CAACP,KAAK,GAAG,IAAI,CAACW,WAAW,CAACF,KAAK,EAAEF,MAAM,CAAC;IAC5C;IACA,IAAIK,aAAa,GAAG,CAAC;IACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAIC,IAAI,GAAGL,KAAK,CAAC,CAAC,IAAI,CAACT,KAAK,GAAGa,CAAC,IAAI,CAAC,CAAC;MACtC,IAAI,IAAI,CAACV,OAAO,EAAE;QACd;QACAS,aAAa,KAAK,CAAC;QACnBA,aAAa,IAAKE,IAAI,IAAI,CAAC,GAAI,IAAI;MACvC,CAAC,MACI;QACD;QACAF,aAAa,KAAK,EAAE;QACpBA,aAAa,IAAI,CAAEE,IAAI,IAAI,CAAC,GAAK,IAAI,IAAI,CAAE,KAAMA,IAAI,IAAI,CAAC,GAAI,IAAI,CAAC;MACvE;IACJ;IACA;IACA;IACA,IAAIC,aAAa,GAAG,IAAI,CAACC,yBAAyB,CAACJ,aAAa,EAAE,IAAI,CAACT,OAAO,CAAC;IAC/E,IAAI,IAAI,CAACA,OAAO,EAAE;MACd;MACA,IAAI,CAACE,QAAQ,GAAG,CAACU,aAAa,IAAI,CAAC,IAAI,CAAC;MACxC,IAAI,CAACX,YAAY,GAAG,CAACW,aAAa,GAAG,IAAI,IAAI,CAAC;IAClD,CAAC,MACI;MACD;MACA,IAAI,CAACV,QAAQ,GAAG,CAACU,aAAa,IAAI,EAAE,IAAI,CAAC;MACzC,IAAI,CAACX,YAAY,GAAG,CAACW,aAAa,GAAG,KAAK,IAAI,CAAC;IACnD;EACJ,CAAC;EACD9B,QAAQ,CAACJ,SAAS,CAAC8B,WAAW,GAAG,UAAUF,KAAK,EAAEF,MAAM,EAAE;IACtD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIU,UAAU,GAAG,CAAC;IAClBR,KAAK,CAACS,OAAO,CAAC,UAAUJ,IAAI,EAAEK,GAAG,EAAEC,GAAG,EAAE;MACpC;MACA,IAAIC,CAAC,GAAG,CAAEP,IAAI,IAAKP,MAAM,GAAG,CAAE,IAAK,CAAC,KAAKO,IAAI,GAAG,CAAC,CAAC;MAClDG,UAAU,GAAG,CAACA,UAAU,IAAI,CAAC,IAAII,CAAC;IACtC,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAJ,UAAU,GAAG,CAAC,CAACA,UAAU,GAAG,CAAC,KAAK,EAAE,KAAKA,UAAU,IAAI,CAAC,CAAC;IACzD;IACA;IACA;IACA,KAAK,IAAIjB,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,EAAEA,KAAK,EAAE,EAAE;MACpC,IAAIvB,OAAO,CAAC6C,QAAQ,CAACL,UAAU,GAAG,IAAI,CAAC9B,oBAAoB,CAACa,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;QACtE,OAAOA,KAAK;MAChB;IACJ;IACA,MAAM,IAAIzB,iBAAiB,CAAC,CAAC;EACjC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIU,QAAQ,CAACJ,SAAS,CAACmC,yBAAyB,GAAG,UAAUJ,aAAa,EAAET,OAAO,EAAE;IAC7E,IAAIoB,YAAY;IAChB,IAAIC,gBAAgB;IACpB,IAAIrB,OAAO,EAAE;MACToB,YAAY,GAAG,CAAC;MAChBC,gBAAgB,GAAG,CAAC;IACxB,CAAC,MACI;MACDD,YAAY,GAAG,EAAE;MACjBC,gBAAgB,GAAG,CAAC;IACxB;IACA,IAAIC,cAAc,GAAGF,YAAY,GAAGC,gBAAgB;IACpD,IAAIE,cAAc,GAAG,IAAItC,UAAU,CAACmC,YAAY,CAAC;IACjD,KAAK,IAAIV,CAAC,GAAGU,YAAY,GAAG,CAAC,EAAEV,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACxCa,cAAc,CAACb,CAAC,CAAC,GAAGD,aAAa,GAAG,GAAG;MACvCA,aAAa,KAAK,CAAC;IACvB;IACA,IAAI;MACA,IAAIe,SAAS,GAAG,IAAIrD,kBAAkB,CAACD,SAAS,CAACuD,WAAW,CAAC;MAC7DD,SAAS,CAACE,MAAM,CAACH,cAAc,EAAED,cAAc,CAAC;IACpD,CAAC,CACD,OAAOK,OAAO,EAAE;MACZ,MAAM,IAAIvD,iBAAiB,CAAC,CAAC;IACjC;IACA;IACA,IAAIwD,MAAM,GAAG,CAAC;IACd,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,gBAAgB,EAAEX,CAAC,EAAE,EAAE;MACvCkB,MAAM,GAAG,CAACA,MAAM,IAAI,CAAC,IAAIL,cAAc,CAACb,CAAC,CAAC;IAC9C;IACA,OAAOkB,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI9C,QAAQ,CAACJ,SAAS,CAACc,kBAAkB,GAAG,UAAUH,OAAO,EAAE;IACvD,IAAIwC,IAAI,GAAGxC,OAAO;IAClB,IAAIyC,IAAI,GAAGzC,OAAO;IAClB,IAAI0C,IAAI,GAAG1C,OAAO;IAClB,IAAI2C,IAAI,GAAG3C,OAAO;IAClB,IAAI4C,KAAK,GAAG,IAAI;IAChB,KAAK,IAAI,CAAC5B,cAAc,GAAG,CAAC,EAAE,IAAI,CAACA,cAAc,GAAG,CAAC,EAAE,IAAI,CAACA,cAAc,EAAE,EAAE;MAC1E,IAAI6B,KAAK,GAAG,IAAI,CAACC,iBAAiB,CAACN,IAAI,EAAEI,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;MACtD,IAAIG,KAAK,GAAG,IAAI,CAACD,iBAAiB,CAACL,IAAI,EAAEG,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MACrD,IAAII,KAAK,GAAG,IAAI,CAACF,iBAAiB,CAACJ,IAAI,EAAEE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACtD,IAAIK,KAAK,GAAG,IAAI,CAACH,iBAAiB,CAACH,IAAI,EAAEC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACvD;MACA;MACA;MACA,IAAI,IAAI,CAAC5B,cAAc,GAAG,CAAC,EAAE;QACzB,IAAIkC,CAAC,GAAI,IAAI,CAACC,aAAa,CAACF,KAAK,EAAEJ,KAAK,CAAC,GAAG,IAAI,CAAC7B,cAAc,IAAK,IAAI,CAACmC,aAAa,CAACR,IAAI,EAAEH,IAAI,CAAC,IAAI,IAAI,CAACxB,cAAc,GAAG,CAAC,CAAC,CAAC;QAC/H,IAAIkC,CAAC,GAAG,IAAI,IAAIA,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAACE,uBAAuB,CAACP,KAAK,EAAEE,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC,EAAE;UACnF;QACJ;MACJ;MACAT,IAAI,GAAGK,KAAK;MACZJ,IAAI,GAAGM,KAAK;MACZL,IAAI,GAAGM,KAAK;MACZL,IAAI,GAAGM,KAAK;MACZL,KAAK,GAAG,CAACA,KAAK;IAClB;IACA,IAAI,IAAI,CAAC5B,cAAc,KAAK,CAAC,IAAI,IAAI,CAACA,cAAc,KAAK,CAAC,EAAE;MACxD,MAAM,IAAIjC,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAI,CAAC4B,OAAO,GAAG,IAAI,CAACK,cAAc,KAAK,CAAC;IACxC;IACA;IACA,IAAIqC,KAAK,GAAG,IAAI5E,WAAW,CAAC+D,IAAI,CAACjD,IAAI,CAAC,CAAC,GAAG,GAAG,EAAEiD,IAAI,CAAChD,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;IACjE,IAAI8D,KAAK,GAAG,IAAI7E,WAAW,CAACgE,IAAI,CAAClD,IAAI,CAAC,CAAC,GAAG,GAAG,EAAEkD,IAAI,CAACjD,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;IACjE,IAAI+D,KAAK,GAAG,IAAI9E,WAAW,CAACiE,IAAI,CAACnD,IAAI,CAAC,CAAC,GAAG,GAAG,EAAEmD,IAAI,CAAClD,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;IACjE,IAAIgE,KAAK,GAAG,IAAI/E,WAAW,CAACkE,IAAI,CAACpD,IAAI,CAAC,CAAC,GAAG,GAAG,EAAEoD,IAAI,CAACnD,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;IACjE;IACA;IACA,OAAO,IAAI,CAACiE,YAAY,CAAC,CAACJ,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,CAAC,EAAE,CAAC,GAAG,IAAI,CAACxC,cAAc,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAACA,cAAc,CAAC;EAChH,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIvB,QAAQ,CAACJ,SAAS,CAACY,eAAe,GAAG,YAAY;IAC7C,IAAIyD,MAAM;IACV,IAAIC,MAAM;IACV,IAAIC,MAAM;IACV,IAAIC,MAAM;IACV;IACA,IAAI;MACA,IAAIC,YAAY,GAAG,IAAIlF,sBAAsB,CAAC,IAAI,CAACc,KAAK,CAAC,CAACG,MAAM,CAAC,CAAC;MAClE6D,MAAM,GAAGI,YAAY,CAAC,CAAC,CAAC;MACxBH,MAAM,GAAGG,YAAY,CAAC,CAAC,CAAC;MACxBF,MAAM,GAAGE,YAAY,CAAC,CAAC,CAAC;MACxBD,MAAM,GAAGC,YAAY,CAAC,CAAC,CAAC;IAC5B,CAAC,CACD,OAAOC,CAAC,EAAE;MACN;MACA;MACA,IAAIC,IAAI,GAAG,IAAI,CAACtE,KAAK,CAACuE,QAAQ,CAAC,CAAC,GAAG,CAAC;MACpC,IAAIC,IAAI,GAAG,IAAI,CAACxE,KAAK,CAACyE,SAAS,CAAC,CAAC,GAAG,CAAC;MACrCT,MAAM,GAAG,IAAI,CAACZ,iBAAiB,CAAC,IAAI5D,KAAK,CAAC8E,IAAI,GAAG,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC5E,aAAa,CAAC,CAAC;MAC5FqE,MAAM,GAAG,IAAI,CAACb,iBAAiB,CAAC,IAAI5D,KAAK,CAAC8E,IAAI,GAAG,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC5E,aAAa,CAAC,CAAC;MAC3FsE,MAAM,GAAG,IAAI,CAACd,iBAAiB,CAAC,IAAI5D,KAAK,CAAC8E,IAAI,GAAG,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC5E,aAAa,CAAC,CAAC;MAC5FuE,MAAM,GAAG,IAAI,CAACf,iBAAiB,CAAC,IAAI5D,KAAK,CAAC8E,IAAI,GAAG,CAAC,EAAEE,IAAI,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC5E,aAAa,CAAC,CAAC;IACjG;IACA;IACA,IAAI8E,EAAE,GAAGzF,SAAS,CAAC0F,KAAK,CAAC,CAACX,MAAM,CAACnE,IAAI,CAAC,CAAC,GAAGsE,MAAM,CAACtE,IAAI,CAAC,CAAC,GAAGoE,MAAM,CAACpE,IAAI,CAAC,CAAC,GAAGqE,MAAM,CAACrE,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC;IAC/F,IAAI+E,EAAE,GAAG3F,SAAS,CAAC0F,KAAK,CAAC,CAACX,MAAM,CAAClE,IAAI,CAAC,CAAC,GAAGqE,MAAM,CAACrE,IAAI,CAAC,CAAC,GAAGmE,MAAM,CAACnE,IAAI,CAAC,CAAC,GAAGoE,MAAM,CAACpE,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC;IAC/F;IACA;IACA;IACA,IAAI;MACA,IAAIsE,YAAY,GAAG,IAAIlF,sBAAsB,CAAC,IAAI,CAACc,KAAK,EAAE,EAAE,EAAE0E,EAAE,EAAEE,EAAE,CAAC,CAACzE,MAAM,CAAC,CAAC;MAC9E6D,MAAM,GAAGI,YAAY,CAAC,CAAC,CAAC;MACxBH,MAAM,GAAGG,YAAY,CAAC,CAAC,CAAC;MACxBF,MAAM,GAAGE,YAAY,CAAC,CAAC,CAAC;MACxBD,MAAM,GAAGC,YAAY,CAAC,CAAC,CAAC;IAC5B,CAAC,CACD,OAAOC,CAAC,EAAE;MACN;MACA;MACAL,MAAM,GAAG,IAAI,CAACZ,iBAAiB,CAAC,IAAI5D,KAAK,CAACkF,EAAE,GAAG,CAAC,EAAEE,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAChF,aAAa,CAAC,CAAC;MACxFqE,MAAM,GAAG,IAAI,CAACb,iBAAiB,CAAC,IAAI5D,KAAK,CAACkF,EAAE,GAAG,CAAC,EAAEE,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAChF,aAAa,CAAC,CAAC;MACvFsE,MAAM,GAAG,IAAI,CAACd,iBAAiB,CAAC,IAAI5D,KAAK,CAACkF,EAAE,GAAG,CAAC,EAAEE,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChF,aAAa,CAAC,CAAC;MACxFuE,MAAM,GAAG,IAAI,CAACf,iBAAiB,CAAC,IAAI5D,KAAK,CAACkF,EAAE,GAAG,CAAC,EAAEE,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAChF,aAAa,CAAC,CAAC;IAC7F;IACA;IACA8E,EAAE,GAAGzF,SAAS,CAAC0F,KAAK,CAAC,CAACX,MAAM,CAACnE,IAAI,CAAC,CAAC,GAAGsE,MAAM,CAACtE,IAAI,CAAC,CAAC,GAAGoE,MAAM,CAACpE,IAAI,CAAC,CAAC,GAAGqE,MAAM,CAACrE,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC;IAC3F+E,EAAE,GAAG3F,SAAS,CAAC0F,KAAK,CAAC,CAACX,MAAM,CAAClE,IAAI,CAAC,CAAC,GAAGqE,MAAM,CAACrE,IAAI,CAAC,CAAC,GAAGmE,MAAM,CAACnE,IAAI,CAAC,CAAC,GAAGoE,MAAM,CAACpE,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC;IAC3F,OAAO,IAAIN,KAAK,CAACkF,EAAE,EAAEE,EAAE,CAAC;EAC5B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI7E,QAAQ,CAACJ,SAAS,CAACqB,qBAAqB,GAAG,UAAUR,eAAe,EAAE;IAClE,OAAO,IAAI,CAACuD,YAAY,CAACvD,eAAe,EAAE,CAAC,GAAG,IAAI,CAACc,cAAc,EAAE,IAAI,CAACuD,YAAY,CAAC,CAAC,CAAC;EAC3F,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI9E,QAAQ,CAACJ,SAAS,CAACkB,UAAU,GAAG,UAAUb,KAAK,EAAE8E,OAAO,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAE;IACzF,IAAIC,OAAO,GAAG5F,mBAAmB,CAAC6F,WAAW,CAAC,CAAC;IAC/C,IAAIC,SAAS,GAAG,IAAI,CAACP,YAAY,CAAC,CAAC;IACnC,IAAIQ,GAAG,GAAGD,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC9D,cAAc;IAC7C,IAAIgE,IAAI,GAAGF,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC9D,cAAc;IAC9C,OAAO4D,OAAO,CAACrE,UAAU,CAACb,KAAK,EAAEoF,SAAS,EAAEA,SAAS,EAAEC,GAAG,EAAEA,GAAG;IAAE;IACjEC,IAAI,EAAED,GAAG;IAAE;IACXC,IAAI,EAAEA,IAAI;IAAE;IACZD,GAAG,EAAEC,IAAI;IAAE;IACXR,OAAO,CAACjF,IAAI,CAAC,CAAC,EAAEiF,OAAO,CAAChF,IAAI,CAAC,CAAC,EAAEiF,QAAQ,CAAClF,IAAI,CAAC,CAAC,EAAEkF,QAAQ,CAACjF,IAAI,CAAC,CAAC,EAAEkF,WAAW,CAACnF,IAAI,CAAC,CAAC,EAAEmF,WAAW,CAAClF,IAAI,CAAC,CAAC,EAAEmF,UAAU,CAACpF,IAAI,CAAC,CAAC,EAAEoF,UAAU,CAACnF,IAAI,CAAC,CAAC,CAAC;EACnJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQ,CAACJ,SAAS,CAAC6B,UAAU,GAAG,UAAU+D,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAE;IACpD,IAAI5C,MAAM,GAAG,CAAC;IACd,IAAI6C,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAACJ,EAAE,EAAEC,EAAE,CAAC;IACxC,IAAII,UAAU,GAAGF,CAAC,GAAGD,IAAI;IACzB,IAAII,EAAE,GAAGN,EAAE,CAAC1F,IAAI,CAAC,CAAC;IAClB,IAAIiG,EAAE,GAAGP,EAAE,CAACzF,IAAI,CAAC,CAAC;IAClB,IAAIiG,EAAE,GAAGH,UAAU,IAAIJ,EAAE,CAAC3F,IAAI,CAAC,CAAC,GAAG0F,EAAE,CAAC1F,IAAI,CAAC,CAAC,CAAC,GAAG6F,CAAC;IACjD,IAAIM,EAAE,GAAGJ,UAAU,IAAIJ,EAAE,CAAC1F,IAAI,CAAC,CAAC,GAAGyF,EAAE,CAACzF,IAAI,CAAC,CAAC,CAAC,GAAG4F,CAAC;IACjD,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8D,IAAI,EAAE9D,CAAC,EAAE,EAAE;MAC3B,IAAI,IAAI,CAAC3B,KAAK,CAACiG,GAAG,CAAChH,SAAS,CAAC0F,KAAK,CAACkB,EAAE,GAAGlE,CAAC,GAAGoE,EAAE,CAAC,EAAE9G,SAAS,CAAC0F,KAAK,CAACmB,EAAE,GAAGnE,CAAC,GAAGqE,EAAE,CAAC,CAAC,EAAE;QAC5EnD,MAAM,IAAI,CAAC,IAAK4C,IAAI,GAAG9D,CAAC,GAAG,CAAE;MACjC;IACJ;IACA,OAAOkB,MAAM;EACjB,CAAC;EACD;AACJ;AACA;AACA;EACI9C,QAAQ,CAACJ,SAAS,CAAC+D,uBAAuB,GAAG,UAAU6B,EAAE,EAAEC,EAAE,EAAEU,EAAE,EAAEC,EAAE,EAAE;IACnE,IAAIC,IAAI,GAAG,CAAC;IACZb,EAAE,GAAG,IAAI/F,KAAK,CAAC+F,EAAE,CAAC1F,IAAI,CAAC,CAAC,GAAGuG,IAAI,EAAEb,EAAE,CAACzF,IAAI,CAAC,CAAC,GAAGsG,IAAI,CAAC;IAClDZ,EAAE,GAAG,IAAIhG,KAAK,CAACgG,EAAE,CAAC3F,IAAI,CAAC,CAAC,GAAGuG,IAAI,EAAEZ,EAAE,CAAC1F,IAAI,CAAC,CAAC,GAAGsG,IAAI,CAAC;IAClDF,EAAE,GAAG,IAAI1G,KAAK,CAAC0G,EAAE,CAACrG,IAAI,CAAC,CAAC,GAAGuG,IAAI,EAAEF,EAAE,CAACpG,IAAI,CAAC,CAAC,GAAGsG,IAAI,CAAC;IAClDD,EAAE,GAAG,IAAI3G,KAAK,CAAC2G,EAAE,CAACtG,IAAI,CAAC,CAAC,GAAGuG,IAAI,EAAED,EAAE,CAACrG,IAAI,CAAC,CAAC,GAAGsG,IAAI,CAAC;IAClD,IAAIC,KAAK,GAAG,IAAI,CAACC,QAAQ,CAACH,EAAE,EAAEZ,EAAE,CAAC;IACjC,IAAIc,KAAK,KAAK,CAAC,EAAE;MACb,OAAO,KAAK;IAChB;IACA,IAAIE,CAAC,GAAG,IAAI,CAACD,QAAQ,CAACf,EAAE,EAAEC,EAAE,CAAC;IAC7B,IAAIe,CAAC,KAAKF,KAAK,EAAE;MACb,OAAO,KAAK;IAChB;IACAE,CAAC,GAAG,IAAI,CAACD,QAAQ,CAACd,EAAE,EAAEU,EAAE,CAAC;IACzB,IAAIK,CAAC,KAAKF,KAAK,EAAE;MACb,OAAO,KAAK;IAChB;IACAE,CAAC,GAAG,IAAI,CAACD,QAAQ,CAACJ,EAAE,EAAEC,EAAE,CAAC;IACzB,OAAOI,CAAC,KAAKF,KAAK;EACtB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACItG,QAAQ,CAACJ,SAAS,CAAC2G,QAAQ,GAAG,UAAUf,EAAE,EAAEC,EAAE,EAAE;IAC5C,IAAIE,CAAC,GAAG,IAAI,CAACjC,aAAa,CAAC8B,EAAE,EAAEC,EAAE,CAAC;IAClC,IAAIO,EAAE,GAAG,CAACP,EAAE,CAAC3F,IAAI,CAAC,CAAC,GAAG0F,EAAE,CAAC1F,IAAI,CAAC,CAAC,IAAI6F,CAAC;IACpC,IAAIM,EAAE,GAAG,CAACR,EAAE,CAAC1F,IAAI,CAAC,CAAC,GAAGyF,EAAE,CAACzF,IAAI,CAAC,CAAC,IAAI4F,CAAC;IACpC,IAAIc,KAAK,GAAG,CAAC;IACb,IAAIX,EAAE,GAAGN,EAAE,CAAC1F,IAAI,CAAC,CAAC;IAClB,IAAIiG,EAAE,GAAGP,EAAE,CAACzF,IAAI,CAAC,CAAC;IAClB,IAAI2G,UAAU,GAAG,IAAI,CAACzG,KAAK,CAACiG,GAAG,CAACV,EAAE,CAAC1F,IAAI,CAAC,CAAC,EAAE0F,EAAE,CAACzF,IAAI,CAAC,CAAC,CAAC;IACrD,IAAI4G,IAAI,GAAGC,IAAI,CAACC,IAAI,CAAClB,CAAC,CAAC;IACvB,KAAK,IAAI/D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+E,IAAI,EAAE/E,CAAC,EAAE,EAAE;MAC3BkE,EAAE,IAAIE,EAAE;MACRD,EAAE,IAAIE,EAAE;MACR,IAAI,IAAI,CAAChG,KAAK,CAACiG,GAAG,CAAChH,SAAS,CAAC0F,KAAK,CAACkB,EAAE,CAAC,EAAE5G,SAAS,CAAC0F,KAAK,CAACmB,EAAE,CAAC,CAAC,KAAKW,UAAU,EAAE;QACzED,KAAK,EAAE;MACX;IACJ;IACA,IAAIK,QAAQ,GAAGL,KAAK,GAAGd,CAAC;IACxB,IAAImB,QAAQ,GAAG,GAAG,IAAIA,QAAQ,GAAG,GAAG,EAAE;MAClC,OAAO,CAAC;IACZ;IACA,OAAQA,QAAQ,IAAI,GAAG,KAAMJ,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;EACpD,CAAC;EACD;AACJ;AACA;EACI1G,QAAQ,CAACJ,SAAS,CAACyD,iBAAiB,GAAG,UAAU0D,IAAI,EAAE5D,KAAK,EAAE6C,EAAE,EAAEC,EAAE,EAAE;IAClE,IAAIvG,CAAC,GAAGqH,IAAI,CAACjH,IAAI,CAAC,CAAC,GAAGkG,EAAE;IACxB,IAAIrG,CAAC,GAAGoH,IAAI,CAAChH,IAAI,CAAC,CAAC,GAAGkG,EAAE;IACxB,OAAO,IAAI,CAACe,OAAO,CAACtH,CAAC,EAAEC,CAAC,CAAC,IAAI,IAAI,CAACM,KAAK,CAACiG,GAAG,CAACxG,CAAC,EAAEC,CAAC,CAAC,KAAKwD,KAAK,EAAE;MACzDzD,CAAC,IAAIsG,EAAE;MACPrG,CAAC,IAAIsG,EAAE;IACX;IACAvG,CAAC,IAAIsG,EAAE;IACPrG,CAAC,IAAIsG,EAAE;IACP,OAAO,IAAI,CAACe,OAAO,CAACtH,CAAC,EAAEC,CAAC,CAAC,IAAI,IAAI,CAACM,KAAK,CAACiG,GAAG,CAACxG,CAAC,EAAEC,CAAC,CAAC,KAAKwD,KAAK,EAAE;MACzDzD,CAAC,IAAIsG,EAAE;IACX;IACAtG,CAAC,IAAIsG,EAAE;IACP,OAAO,IAAI,CAACgB,OAAO,CAACtH,CAAC,EAAEC,CAAC,CAAC,IAAI,IAAI,CAACM,KAAK,CAACiG,GAAG,CAACxG,CAAC,EAAEC,CAAC,CAAC,KAAKwD,KAAK,EAAE;MACzDxD,CAAC,IAAIsG,EAAE;IACX;IACAtG,CAAC,IAAIsG,EAAE;IACP,OAAO,IAAIxG,KAAK,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC1B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIK,QAAQ,CAACJ,SAAS,CAACoE,YAAY,GAAG,UAAUK,YAAY,EAAE4C,OAAO,EAAEC,OAAO,EAAE;IACxE,IAAIC,KAAK,GAAGD,OAAO,IAAI,GAAG,GAAGD,OAAO,CAAC;IACrC,IAAIjB,EAAE,GAAG3B,YAAY,CAAC,CAAC,CAAC,CAACvE,IAAI,CAAC,CAAC,GAAGuE,YAAY,CAAC,CAAC,CAAC,CAACvE,IAAI,CAAC,CAAC;IACxD,IAAImG,EAAE,GAAG5B,YAAY,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC,CAAC,GAAGsE,YAAY,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC,CAAC;IACxD,IAAIqH,OAAO,GAAG,CAAC/C,YAAY,CAAC,CAAC,CAAC,CAACvE,IAAI,CAAC,CAAC,GAAGuE,YAAY,CAAC,CAAC,CAAC,CAACvE,IAAI,CAAC,CAAC,IAAI,GAAG;IACrE,IAAIuH,OAAO,GAAG,CAAChD,YAAY,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC,CAAC,GAAGsE,YAAY,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC,CAAC,IAAI,GAAG;IACrE,IAAIuH,OAAO,GAAG,IAAItI,WAAW,CAACoI,OAAO,GAAGD,KAAK,GAAGnB,EAAE,EAAEqB,OAAO,GAAGF,KAAK,GAAGlB,EAAE,CAAC;IACzE,IAAIsB,OAAO,GAAG,IAAIvI,WAAW,CAACoI,OAAO,GAAGD,KAAK,GAAGnB,EAAE,EAAEqB,OAAO,GAAGF,KAAK,GAAGlB,EAAE,CAAC;IACzED,EAAE,GAAG3B,YAAY,CAAC,CAAC,CAAC,CAACvE,IAAI,CAAC,CAAC,GAAGuE,YAAY,CAAC,CAAC,CAAC,CAACvE,IAAI,CAAC,CAAC;IACpDmG,EAAE,GAAG5B,YAAY,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC,CAAC,GAAGsE,YAAY,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC,CAAC;IACpDqH,OAAO,GAAG,CAAC/C,YAAY,CAAC,CAAC,CAAC,CAACvE,IAAI,CAAC,CAAC,GAAGuE,YAAY,CAAC,CAAC,CAAC,CAACvE,IAAI,CAAC,CAAC,IAAI,GAAG;IACjEuH,OAAO,GAAG,CAAChD,YAAY,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC,CAAC,GAAGsE,YAAY,CAAC,CAAC,CAAC,CAACtE,IAAI,CAAC,CAAC,IAAI,GAAG;IACjE,IAAIyH,OAAO,GAAG,IAAIxI,WAAW,CAACoI,OAAO,GAAGD,KAAK,GAAGnB,EAAE,EAAEqB,OAAO,GAAGF,KAAK,GAAGlB,EAAE,CAAC;IACzE,IAAIwB,OAAO,GAAG,IAAIzI,WAAW,CAACoI,OAAO,GAAGD,KAAK,GAAGnB,EAAE,EAAEqB,OAAO,GAAGF,KAAK,GAAGlB,EAAE,CAAC;IACzE,IAAIyB,OAAO,GAAG,CAACJ,OAAO,EAAEE,OAAO,EAAED,OAAO,EAAEE,OAAO,CAAC;IAClD,OAAOC,OAAO;EAClB,CAAC;EACD1H,QAAQ,CAACJ,SAAS,CAACoH,OAAO,GAAG,UAAUtH,CAAC,EAAEC,CAAC,EAAE;IACzC,OAAOD,CAAC,IAAI,CAAC,IAAIA,CAAC,GAAG,IAAI,CAACO,KAAK,CAACuE,QAAQ,CAAC,CAAC,IAAI7E,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,IAAI,CAACM,KAAK,CAACyE,SAAS,CAAC,CAAC;EACrF,CAAC;EACD1E,QAAQ,CAACJ,SAAS,CAACyB,YAAY,GAAG,UAAUsG,KAAK,EAAE;IAC/C,IAAIjI,CAAC,GAAGR,SAAS,CAAC0F,KAAK,CAAC+C,KAAK,CAAC7H,IAAI,CAAC,CAAC,CAAC;IACrC,IAAIH,CAAC,GAAGT,SAAS,CAAC0F,KAAK,CAAC+C,KAAK,CAAC5H,IAAI,CAAC,CAAC,CAAC;IACrC,OAAO,IAAI,CAACiH,OAAO,CAACtH,CAAC,EAAEC,CAAC,CAAC;EAC7B,CAAC;EACDK,QAAQ,CAACJ,SAAS,CAAC8D,aAAa,GAAG,UAAUkE,CAAC,EAAEC,CAAC,EAAE;IAC/C,OAAO3I,SAAS,CAAC4I,QAAQ,CAACF,CAAC,CAAC9H,IAAI,CAAC,CAAC,EAAE8H,CAAC,CAAC7H,IAAI,CAAC,CAAC,EAAE8H,CAAC,CAAC/H,IAAI,CAAC,CAAC,EAAE+H,CAAC,CAAC9H,IAAI,CAAC,CAAC,CAAC;EACrE,CAAC;EACDC,QAAQ,CAACJ,SAAS,CAACgG,mBAAmB,GAAG,UAAUgC,CAAC,EAAEC,CAAC,EAAE;IACrD,OAAO3I,SAAS,CAAC4I,QAAQ,CAACF,CAAC,CAAC9H,IAAI,CAAC,CAAC,EAAE8H,CAAC,CAAC7H,IAAI,CAAC,CAAC,EAAE8H,CAAC,CAAC/H,IAAI,CAAC,CAAC,EAAE+H,CAAC,CAAC9H,IAAI,CAAC,CAAC,CAAC;EACrE,CAAC;EACDC,QAAQ,CAACJ,SAAS,CAACkF,YAAY,GAAG,YAAY;IAC1C,IAAI,IAAI,CAAC5D,OAAO,EAAE;MACd,OAAO,CAAC,GAAG,IAAI,CAACE,QAAQ,GAAG,EAAE;IACjC;IACA,IAAI,IAAI,CAACA,QAAQ,IAAI,CAAC,EAAE;MACpB,OAAO,CAAC,GAAG,IAAI,CAACA,QAAQ,GAAG,EAAE;IACjC;IACA,OAAO,CAAC,GAAG,IAAI,CAACA,QAAQ,GAAG,CAAC,IAAI5B,OAAO,CAACuI,aAAa,CAAE,IAAI,CAAC3G,QAAQ,GAAG,CAAC,EAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;EAC3F,CAAC;EACD,OAAOpB,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}