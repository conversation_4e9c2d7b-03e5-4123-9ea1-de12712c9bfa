{"ast": null, "code": "/*\n * Copyright (c) 1994, 2010, Oracle and/or its affiliates. All rights reserved.\n * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.\n *\n * This code is free software; you can redistribute it and/or modify it\n * under the terms of the GNU General Public License version 2 only, as\n * published by the Free Software Foundation.  Oracle designates this\n * particular file as subject to the \"Classpath\" exception as provided\n * by Oracle in the LICENSE file that accompanied this code.\n *\n * This code is distributed in the hope that it will be useful, but WITHOUT\n * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or\n * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License\n * version 2 for more details (a copy is included in the LICENSE file that\n * accompanied this code).\n *\n * You should have received a copy of the GNU General Public License version\n * 2 along with this work; if not, write to the Free Software Foundation,\n * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.\n *\n * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA\n * or visit www.oracle.com if you need additional information or have any\n * questions.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n// package java.io;\n// import java.util.Arrays;\nimport Arrays from './Arrays';\nimport OutputStream from './OutputStream';\nimport Integer from './Integer';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport OutOfMemoryError from '../OutOfMemoryError';\nimport System from './System';\nimport IndexOutOfBoundsException from '../IndexOutOfBoundsException';\n/**\n * This class implements an output stream in which the data is\n * written into a byte array. The buffer automatically grows as data\n * is written to it.\n * The data can be retrieved using <code>toByteArray()</code> and\n * <code>toString()</code>.\n * <p>\n * Closing a <tt>ByteArrayOutputStream</tt> has no effect. The methods in\n * this class can be called after the stream has been closed without\n * generating an <tt>IOException</tt>.\n *\n * <AUTHOR> van Hoff\n * @since   JDK1.0\n */\nvar ByteArrayOutputStream = /** @class */function (_super) {\n  __extends(ByteArrayOutputStream, _super);\n  /**\n   * Creates a new byte array output stream. The buffer capacity is\n   * initially 32 bytes, though its size increases if necessary.\n   */\n  // public constructor() {\n  //     this(32);\n  // }\n  /**\n   * Creates a new byte array output stream, with a buffer capacity of\n   * the specified size, in bytes.\n   *\n   * @param   size   the initial size.\n   * @exception  IllegalArgumentException if size is negative.\n   */\n  function ByteArrayOutputStream(size) {\n    if (size === void 0) {\n      size = 32;\n    }\n    var _this = _super.call(this) || this;\n    /**\n     * The number of valid bytes in the buffer.\n     */\n    _this.count = 0;\n    if (size < 0) {\n      throw new IllegalArgumentException('Negative initial size: ' + size);\n    }\n    _this.buf = new Uint8Array(size);\n    return _this;\n  }\n  /**\n   * Increases the capacity if necessary to ensure that it can hold\n   * at least the number of elements specified by the minimum\n   * capacity argument.\n   *\n   * @param minCapacity the desired minimum capacity\n   * @throws OutOfMemoryError if {@code minCapacity < 0}.  This is\n   * interpreted as a request for the unsatisfiably large capacity\n   * {@code (long) Integer.MAX_VALUE + (minCapacity - Integer.MAX_VALUE)}.\n   */\n  ByteArrayOutputStream.prototype.ensureCapacity = function (minCapacity) {\n    // overflow-conscious code\n    if (minCapacity - this.buf.length > 0) this.grow(minCapacity);\n  };\n  /**\n   * Increases the capacity to ensure that it can hold at least the\n   * number of elements specified by the minimum capacity argument.\n   *\n   * @param minCapacity the desired minimum capacity\n   */\n  ByteArrayOutputStream.prototype.grow = function (minCapacity) {\n    // overflow-conscious code\n    var oldCapacity = this.buf.length;\n    var newCapacity = oldCapacity << 1;\n    if (newCapacity - minCapacity < 0) newCapacity = minCapacity;\n    if (newCapacity < 0) {\n      if (minCapacity < 0)\n        // overflow\n        throw new OutOfMemoryError();\n      newCapacity = Integer.MAX_VALUE;\n    }\n    this.buf = Arrays.copyOfUint8Array(this.buf, newCapacity);\n  };\n  /**\n   * Writes the specified byte to this byte array output stream.\n   *\n   * @param   b   the byte to be written.\n   */\n  ByteArrayOutputStream.prototype.write = function (b) {\n    this.ensureCapacity(this.count + 1);\n    this.buf[this.count] = /*(byte)*/b;\n    this.count += 1;\n  };\n  /**\n   * Writes <code>len</code> bytes from the specified byte array\n   * starting at offset <code>off</code> to this byte array output stream.\n   *\n   * @param   b     the data.\n   * @param   off   the start offset in the data.\n   * @param   len   the number of bytes to write.\n   */\n  ByteArrayOutputStream.prototype.writeBytesOffset = function (b, off, len) {\n    if (off < 0 || off > b.length || len < 0 || off + len - b.length > 0) {\n      throw new IndexOutOfBoundsException();\n    }\n    this.ensureCapacity(this.count + len);\n    System.arraycopy(b, off, this.buf, this.count, len);\n    this.count += len;\n  };\n  /**\n   * Writes the complete contents of this byte array output stream to\n   * the specified output stream argument, as if by calling the output\n   * stream's write method using <code>out.write(buf, 0, count)</code>.\n   *\n   * @param      out   the output stream to which to write the data.\n   * @exception  IOException  if an I/O error occurs.\n   */\n  ByteArrayOutputStream.prototype.writeTo = function (out) {\n    out.writeBytesOffset(this.buf, 0, this.count);\n  };\n  /**\n   * Resets the <code>count</code> field of this byte array output\n   * stream to zero, so that all currently accumulated output in the\n   * output stream is discarded. The output stream can be used again,\n   * reusing the already allocated buffer space.\n   *\n   * @see     java.io.ByteArrayInputStream#count\n   */\n  ByteArrayOutputStream.prototype.reset = function () {\n    this.count = 0;\n  };\n  /**\n   * Creates a newly allocated byte array. Its size is the current\n   * size of this output stream and the valid contents of the buffer\n   * have been copied into it.\n   *\n   * @return  the current contents of this output stream, as a byte array.\n   * @see     java.io.ByteArrayOutputStream#size()\n   */\n  ByteArrayOutputStream.prototype.toByteArray = function () {\n    return Arrays.copyOfUint8Array(this.buf, this.count);\n  };\n  /**\n   * Returns the current size of the buffer.\n   *\n   * @return  the value of the <code>count</code> field, which is the number\n   *          of valid bytes in this output stream.\n   * @see     java.io.ByteArrayOutputStream#count\n   */\n  ByteArrayOutputStream.prototype.size = function () {\n    return this.count;\n  };\n  ByteArrayOutputStream.prototype.toString = function (param) {\n    if (!param) {\n      return this.toString_void();\n    }\n    if (typeof param === 'string') {\n      return this.toString_string(param);\n    }\n    return this.toString_number(param);\n  };\n  /**\n   * Converts the buffer's contents into a string decoding bytes using the\n   * platform's default character set. The length of the new <tt>String</tt>\n   * is a function of the character set, and hence may not be equal to the\n   * size of the buffer.\n   *\n   * <p> This method always replaces malformed-input and unmappable-character\n   * sequences with the default replacement string for the platform's\n   * default character set. The {@linkplain java.nio.charset.CharsetDecoder}\n   * class should be used when more control over the decoding process is\n   * required.\n   *\n   * @return String decoded from the buffer's contents.\n   * @since  JDK1.1\n   */\n  ByteArrayOutputStream.prototype.toString_void = function () {\n    return new String(this.buf /*, 0, this.count*/).toString();\n  };\n  /**\n   * Converts the buffer's contents into a string by decoding the bytes using\n   * the specified {@link java.nio.charset.Charset charsetName}. The length of\n   * the new <tt>String</tt> is a function of the charset, and hence may not be\n   * equal to the length of the byte array.\n   *\n   * <p> This method always replaces malformed-input and unmappable-character\n   * sequences with this charset's default replacement string. The {@link\n   * java.nio.charset.CharsetDecoder} class should be used when more control\n   * over the decoding process is required.\n   *\n   * @param  charsetName  the name of a supported\n   *              {@linkplain java.nio.charset.Charset </code>charset<code>}\n   * @return String decoded from the buffer's contents.\n   * @exception  UnsupportedEncodingException\n   *             If the named charset is not supported\n   * @since   JDK1.1\n   */\n  ByteArrayOutputStream.prototype.toString_string = function (charsetName) {\n    return new String(this.buf /*, 0, this.count, charsetName*/).toString();\n  };\n  /**\n   * Creates a newly allocated string. Its size is the current size of\n   * the output stream and the valid contents of the buffer have been\n   * copied into it. Each character <i>c</i> in the resulting string is\n   * constructed from the corresponding element <i>b</i> in the byte\n   * array such that:\n   * <blockquote><pre>\n   *     c == (char)(((hibyte &amp; 0xff) &lt;&lt; 8) | (b &amp; 0xff))\n   * </pre></blockquote>\n   *\n   * @deprecated This method does not properly convert bytes into characters.\n   * As of JDK&nbsp;1.1, the preferred way to do this is via the\n   * <code>toString(String enc)</code> method, which takes an encoding-name\n   * argument, or the <code>toString()</code> method, which uses the\n   * platform's default character encoding.\n   *\n   * @param      hibyte    the high byte of each resulting Unicode character.\n   * @return     the current contents of the output stream, as a string.\n   * @see        java.io.ByteArrayOutputStream#size()\n   * @see        java.io.ByteArrayOutputStream#toString(String)\n   * @see        java.io.ByteArrayOutputStream#toString()\n   */\n  // @Deprecated\n  ByteArrayOutputStream.prototype.toString_number = function (hibyte) {\n    return new String(this.buf /*, hibyte, 0, this.count*/).toString();\n  };\n  /**\n   * Closing a <tt>ByteArrayOutputStream</tt> has no effect. The methods in\n   * this class can be called after the stream has been closed without\n   * generating an <tt>IOException</tt>.\n   * <p>\n   *\n   * @throws IOException\n   */\n  ByteArrayOutputStream.prototype.close = function () {};\n  return ByteArrayOutputStream;\n}(OutputStream);\nexport default ByteArrayOutputStream;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "<PERSON><PERSON><PERSON>", "OutputStream", "Integer", "IllegalArgumentException", "OutOfMemoryError", "System", "IndexOutOfBoundsException", "ByteArrayOutputStream", "_super", "size", "_this", "call", "count", "buf", "Uint8Array", "ensureCapacity", "minCapacity", "length", "grow", "oldCapacity", "newCapacity", "MAX_VALUE", "copyOfUint8Array", "write", "writeBytesOffset", "off", "len", "arraycopy", "writeTo", "out", "reset", "toByteArray", "toString", "param", "toString_void", "toString_string", "toString_number", "String", "charset<PERSON><PERSON>", "hibyte", "close"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/ByteArrayOutputStream.js"], "sourcesContent": ["/*\n * Copyright (c) 1994, 2010, Oracle and/or its affiliates. All rights reserved.\n * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.\n *\n * This code is free software; you can redistribute it and/or modify it\n * under the terms of the GNU General Public License version 2 only, as\n * published by the Free Software Foundation.  Oracle designates this\n * particular file as subject to the \"Classpath\" exception as provided\n * by Oracle in the LICENSE file that accompanied this code.\n *\n * This code is distributed in the hope that it will be useful, but WITHOUT\n * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or\n * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License\n * version 2 for more details (a copy is included in the LICENSE file that\n * accompanied this code).\n *\n * You should have received a copy of the GNU General Public License version\n * 2 along with this work; if not, write to the Free Software Foundation,\n * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.\n *\n * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA\n * or visit www.oracle.com if you need additional information or have any\n * questions.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n// package java.io;\n// import java.util.Arrays;\nimport Arrays from './Arrays';\nimport OutputStream from './OutputStream';\nimport Integer from './Integer';\nimport IllegalArgumentException from '../IllegalArgumentException';\nimport OutOfMemoryError from '../OutOfMemoryError';\nimport System from './System';\nimport IndexOutOfBoundsException from '../IndexOutOfBoundsException';\n/**\n * This class implements an output stream in which the data is\n * written into a byte array. The buffer automatically grows as data\n * is written to it.\n * The data can be retrieved using <code>toByteArray()</code> and\n * <code>toString()</code>.\n * <p>\n * Closing a <tt>ByteArrayOutputStream</tt> has no effect. The methods in\n * this class can be called after the stream has been closed without\n * generating an <tt>IOException</tt>.\n *\n * <AUTHOR> van Hoff\n * @since   JDK1.0\n */\nvar ByteArrayOutputStream = /** @class */ (function (_super) {\n    __extends(ByteArrayOutputStream, _super);\n    /**\n     * Creates a new byte array output stream. The buffer capacity is\n     * initially 32 bytes, though its size increases if necessary.\n     */\n    // public constructor() {\n    //     this(32);\n    // }\n    /**\n     * Creates a new byte array output stream, with a buffer capacity of\n     * the specified size, in bytes.\n     *\n     * @param   size   the initial size.\n     * @exception  IllegalArgumentException if size is negative.\n     */\n    function ByteArrayOutputStream(size) {\n        if (size === void 0) { size = 32; }\n        var _this = _super.call(this) || this;\n        /**\n         * The number of valid bytes in the buffer.\n         */\n        _this.count = 0;\n        if (size < 0) {\n            throw new IllegalArgumentException('Negative initial size: '\n                + size);\n        }\n        _this.buf = new Uint8Array(size);\n        return _this;\n    }\n    /**\n     * Increases the capacity if necessary to ensure that it can hold\n     * at least the number of elements specified by the minimum\n     * capacity argument.\n     *\n     * @param minCapacity the desired minimum capacity\n     * @throws OutOfMemoryError if {@code minCapacity < 0}.  This is\n     * interpreted as a request for the unsatisfiably large capacity\n     * {@code (long) Integer.MAX_VALUE + (minCapacity - Integer.MAX_VALUE)}.\n     */\n    ByteArrayOutputStream.prototype.ensureCapacity = function (minCapacity) {\n        // overflow-conscious code\n        if (minCapacity - this.buf.length > 0)\n            this.grow(minCapacity);\n    };\n    /**\n     * Increases the capacity to ensure that it can hold at least the\n     * number of elements specified by the minimum capacity argument.\n     *\n     * @param minCapacity the desired minimum capacity\n     */\n    ByteArrayOutputStream.prototype.grow = function (minCapacity) {\n        // overflow-conscious code\n        var oldCapacity = this.buf.length;\n        var newCapacity = oldCapacity << 1;\n        if (newCapacity - minCapacity < 0)\n            newCapacity = minCapacity;\n        if (newCapacity < 0) {\n            if (minCapacity < 0) // overflow\n                throw new OutOfMemoryError();\n            newCapacity = Integer.MAX_VALUE;\n        }\n        this.buf = Arrays.copyOfUint8Array(this.buf, newCapacity);\n    };\n    /**\n     * Writes the specified byte to this byte array output stream.\n     *\n     * @param   b   the byte to be written.\n     */\n    ByteArrayOutputStream.prototype.write = function (b) {\n        this.ensureCapacity(this.count + 1);\n        this.buf[this.count] = /*(byte)*/ b;\n        this.count += 1;\n    };\n    /**\n     * Writes <code>len</code> bytes from the specified byte array\n     * starting at offset <code>off</code> to this byte array output stream.\n     *\n     * @param   b     the data.\n     * @param   off   the start offset in the data.\n     * @param   len   the number of bytes to write.\n     */\n    ByteArrayOutputStream.prototype.writeBytesOffset = function (b, off, len) {\n        if ((off < 0) || (off > b.length) || (len < 0) ||\n            ((off + len) - b.length > 0)) {\n            throw new IndexOutOfBoundsException();\n        }\n        this.ensureCapacity(this.count + len);\n        System.arraycopy(b, off, this.buf, this.count, len);\n        this.count += len;\n    };\n    /**\n     * Writes the complete contents of this byte array output stream to\n     * the specified output stream argument, as if by calling the output\n     * stream's write method using <code>out.write(buf, 0, count)</code>.\n     *\n     * @param      out   the output stream to which to write the data.\n     * @exception  IOException  if an I/O error occurs.\n     */\n    ByteArrayOutputStream.prototype.writeTo = function (out) {\n        out.writeBytesOffset(this.buf, 0, this.count);\n    };\n    /**\n     * Resets the <code>count</code> field of this byte array output\n     * stream to zero, so that all currently accumulated output in the\n     * output stream is discarded. The output stream can be used again,\n     * reusing the already allocated buffer space.\n     *\n     * @see     java.io.ByteArrayInputStream#count\n     */\n    ByteArrayOutputStream.prototype.reset = function () {\n        this.count = 0;\n    };\n    /**\n     * Creates a newly allocated byte array. Its size is the current\n     * size of this output stream and the valid contents of the buffer\n     * have been copied into it.\n     *\n     * @return  the current contents of this output stream, as a byte array.\n     * @see     java.io.ByteArrayOutputStream#size()\n     */\n    ByteArrayOutputStream.prototype.toByteArray = function () {\n        return Arrays.copyOfUint8Array(this.buf, this.count);\n    };\n    /**\n     * Returns the current size of the buffer.\n     *\n     * @return  the value of the <code>count</code> field, which is the number\n     *          of valid bytes in this output stream.\n     * @see     java.io.ByteArrayOutputStream#count\n     */\n    ByteArrayOutputStream.prototype.size = function () {\n        return this.count;\n    };\n    ByteArrayOutputStream.prototype.toString = function (param) {\n        if (!param) {\n            return this.toString_void();\n        }\n        if (typeof param === 'string') {\n            return this.toString_string(param);\n        }\n        return this.toString_number(param);\n    };\n    /**\n     * Converts the buffer's contents into a string decoding bytes using the\n     * platform's default character set. The length of the new <tt>String</tt>\n     * is a function of the character set, and hence may not be equal to the\n     * size of the buffer.\n     *\n     * <p> This method always replaces malformed-input and unmappable-character\n     * sequences with the default replacement string for the platform's\n     * default character set. The {@linkplain java.nio.charset.CharsetDecoder}\n     * class should be used when more control over the decoding process is\n     * required.\n     *\n     * @return String decoded from the buffer's contents.\n     * @since  JDK1.1\n     */\n    ByteArrayOutputStream.prototype.toString_void = function () {\n        return new String(this.buf /*, 0, this.count*/).toString();\n    };\n    /**\n     * Converts the buffer's contents into a string by decoding the bytes using\n     * the specified {@link java.nio.charset.Charset charsetName}. The length of\n     * the new <tt>String</tt> is a function of the charset, and hence may not be\n     * equal to the length of the byte array.\n     *\n     * <p> This method always replaces malformed-input and unmappable-character\n     * sequences with this charset's default replacement string. The {@link\n     * java.nio.charset.CharsetDecoder} class should be used when more control\n     * over the decoding process is required.\n     *\n     * @param  charsetName  the name of a supported\n     *              {@linkplain java.nio.charset.Charset </code>charset<code>}\n     * @return String decoded from the buffer's contents.\n     * @exception  UnsupportedEncodingException\n     *             If the named charset is not supported\n     * @since   JDK1.1\n     */\n    ByteArrayOutputStream.prototype.toString_string = function (charsetName) {\n        return new String(this.buf /*, 0, this.count, charsetName*/).toString();\n    };\n    /**\n     * Creates a newly allocated string. Its size is the current size of\n     * the output stream and the valid contents of the buffer have been\n     * copied into it. Each character <i>c</i> in the resulting string is\n     * constructed from the corresponding element <i>b</i> in the byte\n     * array such that:\n     * <blockquote><pre>\n     *     c == (char)(((hibyte &amp; 0xff) &lt;&lt; 8) | (b &amp; 0xff))\n     * </pre></blockquote>\n     *\n     * @deprecated This method does not properly convert bytes into characters.\n     * As of JDK&nbsp;1.1, the preferred way to do this is via the\n     * <code>toString(String enc)</code> method, which takes an encoding-name\n     * argument, or the <code>toString()</code> method, which uses the\n     * platform's default character encoding.\n     *\n     * @param      hibyte    the high byte of each resulting Unicode character.\n     * @return     the current contents of the output stream, as a string.\n     * @see        java.io.ByteArrayOutputStream#size()\n     * @see        java.io.ByteArrayOutputStream#toString(String)\n     * @see        java.io.ByteArrayOutputStream#toString()\n     */\n    // @Deprecated\n    ByteArrayOutputStream.prototype.toString_number = function (hibyte) {\n        return new String(this.buf /*, hibyte, 0, this.count*/).toString();\n    };\n    /**\n     * Closing a <tt>ByteArrayOutputStream</tt> has no effect. The methods in\n     * this class can be called after the stream has been closed without\n     * generating an <tt>IOException</tt>.\n     * <p>\n     *\n     * @throws IOException\n     */\n    ByteArrayOutputStream.prototype.close = function () {\n    };\n    return ByteArrayOutputStream;\n}(OutputStream));\nexport default ByteArrayOutputStream;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA;AACA,OAAOI,MAAM,MAAM,UAAU;AAC7B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,wBAAwB,MAAM,6BAA6B;AAClE,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,yBAAyB,MAAM,8BAA8B;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,qBAAqB,GAAG,aAAe,UAAUC,MAAM,EAAE;EACzDtB,SAAS,CAACqB,qBAAqB,EAAEC,MAAM,CAAC;EACxC;AACJ;AACA;AACA;EACI;EACA;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,SAASD,qBAAqBA,CAACE,IAAI,EAAE;IACjC,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MAAEA,IAAI,GAAG,EAAE;IAAE;IAClC,IAAIC,KAAK,GAAGF,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrC;AACR;AACA;IACQD,KAAK,CAACE,KAAK,GAAG,CAAC;IACf,IAAIH,IAAI,GAAG,CAAC,EAAE;MACV,MAAM,IAAIN,wBAAwB,CAAC,yBAAyB,GACtDM,IAAI,CAAC;IACf;IACAC,KAAK,CAACG,GAAG,GAAG,IAAIC,UAAU,CAACL,IAAI,CAAC;IAChC,OAAOC,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIH,qBAAqB,CAACT,SAAS,CAACiB,cAAc,GAAG,UAAUC,WAAW,EAAE;IACpE;IACA,IAAIA,WAAW,GAAG,IAAI,CAACH,GAAG,CAACI,MAAM,GAAG,CAAC,EACjC,IAAI,CAACC,IAAI,CAACF,WAAW,CAAC;EAC9B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIT,qBAAqB,CAACT,SAAS,CAACoB,IAAI,GAAG,UAAUF,WAAW,EAAE;IAC1D;IACA,IAAIG,WAAW,GAAG,IAAI,CAACN,GAAG,CAACI,MAAM;IACjC,IAAIG,WAAW,GAAGD,WAAW,IAAI,CAAC;IAClC,IAAIC,WAAW,GAAGJ,WAAW,GAAG,CAAC,EAC7BI,WAAW,GAAGJ,WAAW;IAC7B,IAAII,WAAW,GAAG,CAAC,EAAE;MACjB,IAAIJ,WAAW,GAAG,CAAC;QAAE;QACjB,MAAM,IAAIZ,gBAAgB,CAAC,CAAC;MAChCgB,WAAW,GAAGlB,OAAO,CAACmB,SAAS;IACnC;IACA,IAAI,CAACR,GAAG,GAAGb,MAAM,CAACsB,gBAAgB,CAAC,IAAI,CAACT,GAAG,EAAEO,WAAW,CAAC;EAC7D,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIb,qBAAqB,CAACT,SAAS,CAACyB,KAAK,GAAG,UAAUlC,CAAC,EAAE;IACjD,IAAI,CAAC0B,cAAc,CAAC,IAAI,CAACH,KAAK,GAAG,CAAC,CAAC;IACnC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACD,KAAK,CAAC,GAAG,UAAWvB,CAAC;IACnC,IAAI,CAACuB,KAAK,IAAI,CAAC;EACnB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIL,qBAAqB,CAACT,SAAS,CAAC0B,gBAAgB,GAAG,UAAUnC,CAAC,EAAEoC,GAAG,EAAEC,GAAG,EAAE;IACtE,IAAKD,GAAG,GAAG,CAAC,IAAMA,GAAG,GAAGpC,CAAC,CAAC4B,MAAO,IAAKS,GAAG,GAAG,CAAE,IACxCD,GAAG,GAAGC,GAAG,GAAIrC,CAAC,CAAC4B,MAAM,GAAG,CAAE,EAAE;MAC9B,MAAM,IAAIX,yBAAyB,CAAC,CAAC;IACzC;IACA,IAAI,CAACS,cAAc,CAAC,IAAI,CAACH,KAAK,GAAGc,GAAG,CAAC;IACrCrB,MAAM,CAACsB,SAAS,CAACtC,CAAC,EAAEoC,GAAG,EAAE,IAAI,CAACZ,GAAG,EAAE,IAAI,CAACD,KAAK,EAAEc,GAAG,CAAC;IACnD,IAAI,CAACd,KAAK,IAAIc,GAAG;EACrB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACInB,qBAAqB,CAACT,SAAS,CAAC8B,OAAO,GAAG,UAAUC,GAAG,EAAE;IACrDA,GAAG,CAACL,gBAAgB,CAAC,IAAI,CAACX,GAAG,EAAE,CAAC,EAAE,IAAI,CAACD,KAAK,CAAC;EACjD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIL,qBAAqB,CAACT,SAAS,CAACgC,KAAK,GAAG,YAAY;IAChD,IAAI,CAAClB,KAAK,GAAG,CAAC;EAClB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIL,qBAAqB,CAACT,SAAS,CAACiC,WAAW,GAAG,YAAY;IACtD,OAAO/B,MAAM,CAACsB,gBAAgB,CAAC,IAAI,CAACT,GAAG,EAAE,IAAI,CAACD,KAAK,CAAC;EACxD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIL,qBAAqB,CAACT,SAAS,CAACW,IAAI,GAAG,YAAY;IAC/C,OAAO,IAAI,CAACG,KAAK;EACrB,CAAC;EACDL,qBAAqB,CAACT,SAAS,CAACkC,QAAQ,GAAG,UAAUC,KAAK,EAAE;IACxD,IAAI,CAACA,KAAK,EAAE;MACR,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC;IAC/B;IACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;MAC3B,OAAO,IAAI,CAACE,eAAe,CAACF,KAAK,CAAC;IACtC;IACA,OAAO,IAAI,CAACG,eAAe,CAACH,KAAK,CAAC;EACtC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI1B,qBAAqB,CAACT,SAAS,CAACoC,aAAa,GAAG,YAAY;IACxD,OAAO,IAAIG,MAAM,CAAC,IAAI,CAACxB,GAAG,CAAC,mBAAmB,CAAC,CAACmB,QAAQ,CAAC,CAAC;EAC9D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIzB,qBAAqB,CAACT,SAAS,CAACqC,eAAe,GAAG,UAAUG,WAAW,EAAE;IACrE,OAAO,IAAID,MAAM,CAAC,IAAI,CAACxB,GAAG,CAAC,gCAAgC,CAAC,CAACmB,QAAQ,CAAC,CAAC;EAC3E,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACAzB,qBAAqB,CAACT,SAAS,CAACsC,eAAe,GAAG,UAAUG,MAAM,EAAE;IAChE,OAAO,IAAIF,MAAM,CAAC,IAAI,CAACxB,GAAG,CAAC,2BAA2B,CAAC,CAACmB,QAAQ,CAAC,CAAC;EACtE,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIzB,qBAAqB,CAACT,SAAS,CAAC0C,KAAK,GAAG,YAAY,CACpD,CAAC;EACD,OAAOjC,qBAAqB;AAChC,CAAC,CAACN,YAAY,CAAE;AAChB,eAAeM,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}