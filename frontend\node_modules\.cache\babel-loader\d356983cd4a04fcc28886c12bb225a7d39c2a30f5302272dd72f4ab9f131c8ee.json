{"ast": null, "code": "import DefaultGridSampler from './DefaultGridSampler';\nvar GridSamplerInstance = /** @class */function () {\n  function GridSamplerInstance() {}\n  /**\n   * Sets the implementation of GridSampler used by the library. One global\n   * instance is stored, which may sound problematic. But, the implementation provided\n   * ought to be appropriate for the entire platform, and all uses of this library\n   * in the whole lifetime of the JVM. For instance, an Android activity can swap in\n   * an implementation that takes advantage of native platform libraries.\n   *\n   * @param newGridSampler The platform-specific object to install.\n   */\n  GridSamplerInstance.setGridSampler = function (newGridSampler) {\n    GridSamplerInstance.gridSampler = newGridSampler;\n  };\n  /**\n   * @return the current implementation of GridSampler\n   */\n  GridSamplerInstance.getInstance = function () {\n    return GridSamplerInstance.gridSampler;\n  };\n  GridSamplerInstance.gridSampler = new DefaultGridSampler();\n  return GridSamplerInstance;\n}();\nexport default GridSamplerInstance;", "map": {"version": 3, "names": ["DefaultGridSampler", "GridSamplerInstance", "setGridSampler", "newGridSampler", "gridSampler", "getInstance"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/GridSamplerInstance.js"], "sourcesContent": ["import DefaultGridSampler from './DefaultGridSampler';\nvar GridSamplerInstance = /** @class */ (function () {\n    function GridSamplerInstance() {\n    }\n    /**\n     * Sets the implementation of GridSampler used by the library. One global\n     * instance is stored, which may sound problematic. But, the implementation provided\n     * ought to be appropriate for the entire platform, and all uses of this library\n     * in the whole lifetime of the JVM. For instance, an Android activity can swap in\n     * an implementation that takes advantage of native platform libraries.\n     *\n     * @param newGridSampler The platform-specific object to install.\n     */\n    GridSamplerInstance.setGridSampler = function (newGridSampler) {\n        GridSamplerInstance.gridSampler = newGridSampler;\n    };\n    /**\n     * @return the current implementation of GridSampler\n     */\n    GridSamplerInstance.getInstance = function () {\n        return GridSamplerInstance.gridSampler;\n    };\n    GridSamplerInstance.gridSampler = new DefaultGridSampler();\n    return GridSamplerInstance;\n}());\nexport default GridSamplerInstance;\n"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,sBAAsB;AACrD,IAAIC,mBAAmB,GAAG,aAAe,YAAY;EACjD,SAASA,mBAAmBA,CAAA,EAAG,CAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,mBAAmB,CAACC,cAAc,GAAG,UAAUC,cAAc,EAAE;IAC3DF,mBAAmB,CAACG,WAAW,GAAGD,cAAc;EACpD,CAAC;EACD;AACJ;AACA;EACIF,mBAAmB,CAACI,WAAW,GAAG,YAAY;IAC1C,OAAOJ,mBAAmB,CAACG,WAAW;EAC1C,CAAC;EACDH,mBAAmB,CAACG,WAAW,GAAG,IAAIJ,kBAAkB,CAAC,CAAC;EAC1D,OAAOC,mBAAmB;AAC9B,CAAC,CAAC,CAAE;AACJ,eAAeA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}