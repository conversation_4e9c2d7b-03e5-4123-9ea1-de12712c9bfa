{"ast": null, "code": "/*\n * Copyright 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\n// package com.google.zxing.pdf417.decoder.ec;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../../PDF417Common';\nimport ModulusPoly from './ModulusPoly';\nimport IllegalArgumentException from '../../../IllegalArgumentException';\nimport ModulusBase from './ModulusBase';\n/**\n * <p>A field based on powers of a generator integer, modulo some modulus.</p>\n *\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.GenericGF\n */\nvar ModulusGF = /** @class */function (_super) {\n  __extends(ModulusGF, _super);\n  // private /*final*/ modulus: /*int*/ number;\n  function ModulusGF(modulus, generator) {\n    var _this = _super.call(this) || this;\n    _this.modulus = modulus;\n    _this.expTable = new Int32Array(modulus);\n    _this.logTable = new Int32Array(modulus);\n    var x = /*int*/1;\n    for (var i /*int*/ = 0; i < modulus; i++) {\n      _this.expTable[i] = x;\n      x = x * generator % modulus;\n    }\n    for (var i /*int*/ = 0; i < modulus - 1; i++) {\n      _this.logTable[_this.expTable[i]] = i;\n    }\n    // logTable[0] == 0 but this should never be used\n    _this.zero = new ModulusPoly(_this, new Int32Array([0]));\n    _this.one = new ModulusPoly(_this, new Int32Array([1]));\n    return _this;\n  }\n  ModulusGF.prototype.getZero = function () {\n    return this.zero;\n  };\n  ModulusGF.prototype.getOne = function () {\n    return this.one;\n  };\n  ModulusGF.prototype.buildMonomial = function (degree, coefficient) {\n    if (degree < 0) {\n      throw new IllegalArgumentException();\n    }\n    if (coefficient === 0) {\n      return this.zero;\n    }\n    var coefficients = new Int32Array(degree + 1);\n    coefficients[0] = coefficient;\n    return new ModulusPoly(this, coefficients);\n  };\n  ModulusGF.PDF417_GF = new ModulusGF(PDF417Common.NUMBER_OF_CODEWORDS, 3);\n  return ModulusGF;\n}(ModulusBase);\nexport default ModulusGF;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "PDF417<PERSON><PERSON><PERSON>", "ModulusPoly", "IllegalArgumentException", "ModulusBase", "ModulusGF", "_super", "modulus", "generator", "_this", "call", "expTable", "Int32Array", "logTable", "x", "i", "zero", "one", "getZero", "getOne", "buildMonomial", "degree", "coefficient", "coefficients", "PDF417_GF", "NUMBER_OF_CODEWORDS"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/ec/ModulusGF.js"], "sourcesContent": ["/*\n * Copyright 2012 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n// package com.google.zxing.pdf417.decoder.ec;\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../../PDF417Common';\nimport ModulusPoly from './ModulusPoly';\nimport IllegalArgumentException from '../../../IllegalArgumentException';\nimport ModulusBase from './ModulusBase';\n/**\n * <p>A field based on powers of a generator integer, modulo some modulus.</p>\n *\n * <AUTHOR> Owen\n * @see com.google.zxing.common.reedsolomon.GenericGF\n */\nvar ModulusGF = /** @class */ (function (_super) {\n    __extends(ModulusGF, _super);\n    // private /*final*/ modulus: /*int*/ number;\n    function ModulusGF(modulus, generator) {\n        var _this = _super.call(this) || this;\n        _this.modulus = modulus;\n        _this.expTable = new Int32Array(modulus);\n        _this.logTable = new Int32Array(modulus);\n        var x = /*int*/ 1;\n        for (var i /*int*/ = 0; i < modulus; i++) {\n            _this.expTable[i] = x;\n            x = (x * generator) % modulus;\n        }\n        for (var i /*int*/ = 0; i < modulus - 1; i++) {\n            _this.logTable[_this.expTable[i]] = i;\n        }\n        // logTable[0] == 0 but this should never be used\n        _this.zero = new ModulusPoly(_this, new Int32Array([0]));\n        _this.one = new ModulusPoly(_this, new Int32Array([1]));\n        return _this;\n    }\n    ModulusGF.prototype.getZero = function () {\n        return this.zero;\n    };\n    ModulusGF.prototype.getOne = function () {\n        return this.one;\n    };\n    ModulusGF.prototype.buildMonomial = function (degree, coefficient) {\n        if (degree < 0) {\n            throw new IllegalArgumentException();\n        }\n        if (coefficient === 0) {\n            return this.zero;\n        }\n        var coefficients = new Int32Array(degree + 1);\n        coefficients[0] = coefficient;\n        return new ModulusPoly(this, coefficients);\n    };\n    ModulusGF.PDF417_GF = new ModulusGF(PDF417Common.NUMBER_OF_CODEWORDS, 3);\n    return ModulusGF;\n}(ModulusBase));\nexport default ModulusGF;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ;AACA;AACA,OAAOI,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,wBAAwB,MAAM,mCAAmC;AACxE,OAAOC,WAAW,MAAM,eAAe;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC7CnB,SAAS,CAACkB,SAAS,EAAEC,MAAM,CAAC;EAC5B;EACA,SAASD,SAASA,CAACE,OAAO,EAAEC,SAAS,EAAE;IACnC,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACrCD,KAAK,CAACF,OAAO,GAAGA,OAAO;IACvBE,KAAK,CAACE,QAAQ,GAAG,IAAIC,UAAU,CAACL,OAAO,CAAC;IACxCE,KAAK,CAACI,QAAQ,GAAG,IAAID,UAAU,CAACL,OAAO,CAAC;IACxC,IAAIO,CAAC,GAAG,OAAQ,CAAC;IACjB,KAAK,IAAIC,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGR,OAAO,EAAEQ,CAAC,EAAE,EAAE;MACtCN,KAAK,CAACE,QAAQ,CAACI,CAAC,CAAC,GAAGD,CAAC;MACrBA,CAAC,GAAIA,CAAC,GAAGN,SAAS,GAAID,OAAO;IACjC;IACA,KAAK,IAAIQ,CAAC,CAAC,UAAU,CAAC,EAAEA,CAAC,GAAGR,OAAO,GAAG,CAAC,EAAEQ,CAAC,EAAE,EAAE;MAC1CN,KAAK,CAACI,QAAQ,CAACJ,KAAK,CAACE,QAAQ,CAACI,CAAC,CAAC,CAAC,GAAGA,CAAC;IACzC;IACA;IACAN,KAAK,CAACO,IAAI,GAAG,IAAId,WAAW,CAACO,KAAK,EAAE,IAAIG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxDH,KAAK,CAACQ,GAAG,GAAG,IAAIf,WAAW,CAACO,KAAK,EAAE,IAAIG,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvD,OAAOH,KAAK;EAChB;EACAJ,SAAS,CAACN,SAAS,CAACmB,OAAO,GAAG,YAAY;IACtC,OAAO,IAAI,CAACF,IAAI;EACpB,CAAC;EACDX,SAAS,CAACN,SAAS,CAACoB,MAAM,GAAG,YAAY;IACrC,OAAO,IAAI,CAACF,GAAG;EACnB,CAAC;EACDZ,SAAS,CAACN,SAAS,CAACqB,aAAa,GAAG,UAAUC,MAAM,EAAEC,WAAW,EAAE;IAC/D,IAAID,MAAM,GAAG,CAAC,EAAE;MACZ,MAAM,IAAIlB,wBAAwB,CAAC,CAAC;IACxC;IACA,IAAImB,WAAW,KAAK,CAAC,EAAE;MACnB,OAAO,IAAI,CAACN,IAAI;IACpB;IACA,IAAIO,YAAY,GAAG,IAAIX,UAAU,CAACS,MAAM,GAAG,CAAC,CAAC;IAC7CE,YAAY,CAAC,CAAC,CAAC,GAAGD,WAAW;IAC7B,OAAO,IAAIpB,WAAW,CAAC,IAAI,EAAEqB,YAAY,CAAC;EAC9C,CAAC;EACDlB,SAAS,CAACmB,SAAS,GAAG,IAAInB,SAAS,CAACJ,YAAY,CAACwB,mBAAmB,EAAE,CAAC,CAAC;EACxE,OAAOpB,SAAS;AACpB,CAAC,CAACD,WAAW,CAAE;AACf,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}