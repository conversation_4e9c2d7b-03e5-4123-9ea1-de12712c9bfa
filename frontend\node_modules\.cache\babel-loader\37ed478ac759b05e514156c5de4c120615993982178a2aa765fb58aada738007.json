{"ast": null, "code": "import StringBuilder from '../../util/StringBuilder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { C40_ENCODATION, LATCH_TO_C40, ASCII_ENCODATION, C40_UNLATCH } from './constants';\nvar C40Encoder = /** @class */function () {\n  function C40Encoder() {}\n  C40Encoder.prototype.getEncodingMode = function () {\n    return C40_ENCODATION;\n  };\n  C40Encoder.prototype.encodeMaximal = function (context) {\n    var buffer = new StringBuilder();\n    var lastCharSize = 0;\n    var backtrackStartPosition = context.pos;\n    var backtrackBufferLength = 0;\n    while (context.hasMoreCharacters()) {\n      var c = context.getCurrentChar();\n      context.pos++;\n      lastCharSize = this.encodeChar(c, buffer);\n      if (buffer.length() % 3 === 0) {\n        backtrackStartPosition = context.pos;\n        backtrackBufferLength = buffer.length();\n      }\n    }\n    if (backtrackBufferLength !== buffer.length()) {\n      var unwritten = Math.floor(buffer.length() / 3 * 2);\n      var curCodewordCount = Math.floor(context.getCodewordCount() + unwritten + 1); // +1 for the latch to C40\n      context.updateSymbolInfo(curCodewordCount);\n      var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n      var rest = Math.floor(buffer.length() % 3);\n      if (rest === 2 && available !== 2 || rest === 1 && (lastCharSize > 3 || available !== 1)) {\n        // buffer.setLength(backtrackBufferLength);\n        context.pos = backtrackStartPosition;\n      }\n    }\n    if (buffer.length() > 0) {\n      context.writeCodeword(LATCH_TO_C40);\n    }\n    this.handleEOD(context, buffer);\n  };\n  C40Encoder.prototype.encode = function (context) {\n    // step C\n    var buffer = new StringBuilder();\n    while (context.hasMoreCharacters()) {\n      var c = context.getCurrentChar();\n      context.pos++;\n      var lastCharSize = this.encodeChar(c, buffer);\n      var unwritten = Math.floor(buffer.length() / 3) * 2;\n      var curCodewordCount = context.getCodewordCount() + unwritten;\n      context.updateSymbolInfo(curCodewordCount);\n      var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n      if (!context.hasMoreCharacters()) {\n        // Avoid having a single C40 value in the last triplet\n        var removed = new StringBuilder();\n        if (buffer.length() % 3 === 2 && available !== 2) {\n          lastCharSize = this.backtrackOneCharacter(context, buffer, removed, lastCharSize);\n        }\n        while (buffer.length() % 3 === 1 && (lastCharSize > 3 || available !== 1)) {\n          lastCharSize = this.backtrackOneCharacter(context, buffer, removed, lastCharSize);\n        }\n        break;\n      }\n      var count = buffer.length();\n      if (count % 3 === 0) {\n        var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n        if (newMode !== this.getEncodingMode()) {\n          // Return to ASCII encodation, which will actually handle latch to new mode\n          context.signalEncoderChange(ASCII_ENCODATION);\n          break;\n        }\n      }\n    }\n    this.handleEOD(context, buffer);\n  };\n  C40Encoder.prototype.backtrackOneCharacter = function (context, buffer, removed, lastCharSize) {\n    var count = buffer.length();\n    var test = buffer.toString().substring(0, count - lastCharSize);\n    buffer.setLengthToZero();\n    buffer.append(test);\n    // buffer.delete(count - lastCharSize, count);\n    /*for (let i = count - lastCharSize; i < count; i++) {\n      buffer.deleteCharAt(i);\n    }*/\n    context.pos--;\n    var c = context.getCurrentChar();\n    lastCharSize = this.encodeChar(c, removed);\n    context.resetSymbolInfo(); // Deal with possible reduction in symbol size\n    return lastCharSize;\n  };\n  C40Encoder.prototype.writeNextTriplet = function (context, buffer) {\n    context.writeCodewords(this.encodeToCodewords(buffer.toString()));\n    var test = buffer.toString().substring(3);\n    buffer.setLengthToZero();\n    buffer.append(test);\n    // buffer.delete(0, 3);\n    /*for (let i = 0; i < 3; i++) {\n      buffer.deleteCharAt(i);\n    }*/\n  };\n  /**\n   * Handle \"end of data\" situations\n   *\n   * @param context the encoder context\n   * @param buffer  the buffer with the remaining encoded characters\n   */\n  C40Encoder.prototype.handleEOD = function (context, buffer) {\n    var unwritten = Math.floor(buffer.length() / 3 * 2);\n    var rest = buffer.length() % 3;\n    var curCodewordCount = context.getCodewordCount() + unwritten;\n    context.updateSymbolInfo(curCodewordCount);\n    var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n    if (rest === 2) {\n      buffer.append('\\0'); // Shift 1\n      while (buffer.length() >= 3) {\n        this.writeNextTriplet(context, buffer);\n      }\n      if (context.hasMoreCharacters()) {\n        context.writeCodeword(C40_UNLATCH);\n      }\n    } else if (available === 1 && rest === 1) {\n      while (buffer.length() >= 3) {\n        this.writeNextTriplet(context, buffer);\n      }\n      if (context.hasMoreCharacters()) {\n        context.writeCodeword(C40_UNLATCH);\n      }\n      // else no unlatch\n      context.pos--;\n    } else if (rest === 0) {\n      while (buffer.length() >= 3) {\n        this.writeNextTriplet(context, buffer);\n      }\n      if (available > 0 || context.hasMoreCharacters()) {\n        context.writeCodeword(C40_UNLATCH);\n      }\n    } else {\n      throw new Error('Unexpected case. Please report!');\n    }\n    context.signalEncoderChange(ASCII_ENCODATION);\n  };\n  C40Encoder.prototype.encodeChar = function (c, sb) {\n    if (c === ' '.charCodeAt(0)) {\n      sb.append(3);\n      return 1;\n    }\n    if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n      sb.append(c - 48 + 4);\n      return 1;\n    }\n    if (c >= 'A'.charCodeAt(0) && c <= 'Z'.charCodeAt(0)) {\n      sb.append(c - 65 + 14);\n      return 1;\n    }\n    if (c < ' '.charCodeAt(0)) {\n      sb.append(0); // Shift 1 Set\n      sb.append(c);\n      return 2;\n    }\n    if (c <= '/'.charCodeAt(0)) {\n      sb.append(1); // Shift 2 Set\n      sb.append(c - 33);\n      return 2;\n    }\n    if (c <= '@'.charCodeAt(0)) {\n      sb.append(1); // Shift 2 Set\n      sb.append(c - 58 + 15);\n      return 2;\n    }\n    if (c <= '_'.charCodeAt(0)) {\n      sb.append(1); // Shift 2 Set\n      sb.append(c - 91 + 22);\n      return 2;\n    }\n    if (c <= 127) {\n      sb.append(2); // Shift 3 Set\n      sb.append(c - 96);\n      return 2;\n    }\n    sb.append(1 + \"\\u001E\"); // Shift 2, Upper Shift\n    var len = 2;\n    len += this.encodeChar(c - 128, sb);\n    return len;\n  };\n  C40Encoder.prototype.encodeToCodewords = function (sb) {\n    var v = 1600 * sb.charCodeAt(0) + 40 * sb.charCodeAt(1) + sb.charCodeAt(2) + 1;\n    var cw1 = v / 256;\n    var cw2 = v % 256;\n    var result = new StringBuilder();\n    result.append(cw1);\n    result.append(cw2);\n    return result.toString();\n  };\n  return C40Encoder;\n}();\nexport { C40Encoder };", "map": {"version": 3, "names": ["StringBuilder", "HighLevelEncoder", "C40_ENCODATION", "LATCH_TO_C40", "ASCII_ENCODATION", "C40_UNLATCH", "C40Encoder", "prototype", "getEncodingMode", "encodeMaximal", "context", "buffer", "lastCharSize", "backtrackStartPosition", "pos", "backtrackBuffer<PERSON><PERSON>th", "hasMoreCharacters", "c", "getCurrentChar", "encodeChar", "length", "unwritten", "Math", "floor", "curCodewordCount", "getCodewordCount", "updateSymbolInfo", "available", "getSymbolInfo", "getDataCapacity", "rest", "writeCodeword", "handleEOD", "encode", "removed", "backtrackOneCharacter", "count", "newMode", "lookAheadTest", "getMessage", "signalEncoderChange", "test", "toString", "substring", "setLengthToZero", "append", "resetSymbolInfo", "writeNextTriplet", "writeCodewords", "encodeToCodewords", "Error", "sb", "charCodeAt", "len", "v", "cw1", "cw2", "result"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/C40Encoder.js"], "sourcesContent": ["import StringBuilder from '../../util/StringBuilder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { C40_ENCODATION, LATCH_TO_C40, ASCII_ENCODATION, C40_UNLATCH, } from './constants';\nvar C40Encoder = /** @class */ (function () {\n    function C40Encoder() {\n    }\n    C40Encoder.prototype.getEncodingMode = function () {\n        return C40_ENCODATION;\n    };\n    C40Encoder.prototype.encodeMaximal = function (context) {\n        var buffer = new StringBuilder();\n        var lastCharSize = 0;\n        var backtrackStartPosition = context.pos;\n        var backtrackBufferLength = 0;\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            context.pos++;\n            lastCharSize = this.encodeChar(c, buffer);\n            if (buffer.length() % 3 === 0) {\n                backtrackStartPosition = context.pos;\n                backtrackBufferLength = buffer.length();\n            }\n        }\n        if (backtrackBufferLength !== buffer.length()) {\n            var unwritten = Math.floor((buffer.length() / 3) * 2);\n            var curCodewordCount = Math.floor(context.getCodewordCount() + unwritten + 1); // +1 for the latch to C40\n            context.updateSymbolInfo(curCodewordCount);\n            var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n            var rest = Math.floor(buffer.length() % 3);\n            if ((rest === 2 && available !== 2) ||\n                (rest === 1 && (lastCharSize > 3 || available !== 1))) {\n                // buffer.setLength(backtrackBufferLength);\n                context.pos = backtrackStartPosition;\n            }\n        }\n        if (buffer.length() > 0) {\n            context.writeCodeword(LATCH_TO_C40);\n        }\n        this.handleEOD(context, buffer);\n    };\n    C40Encoder.prototype.encode = function (context) {\n        // step C\n        var buffer = new StringBuilder();\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            context.pos++;\n            var lastCharSize = this.encodeChar(c, buffer);\n            var unwritten = Math.floor(buffer.length() / 3) * 2;\n            var curCodewordCount = context.getCodewordCount() + unwritten;\n            context.updateSymbolInfo(curCodewordCount);\n            var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n            if (!context.hasMoreCharacters()) {\n                // Avoid having a single C40 value in the last triplet\n                var removed = new StringBuilder();\n                if (buffer.length() % 3 === 2 && available !== 2) {\n                    lastCharSize = this.backtrackOneCharacter(context, buffer, removed, lastCharSize);\n                }\n                while (buffer.length() % 3 === 1 &&\n                    (lastCharSize > 3 || available !== 1)) {\n                    lastCharSize = this.backtrackOneCharacter(context, buffer, removed, lastCharSize);\n                }\n                break;\n            }\n            var count = buffer.length();\n            if (count % 3 === 0) {\n                var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n                if (newMode !== this.getEncodingMode()) {\n                    // Return to ASCII encodation, which will actually handle latch to new mode\n                    context.signalEncoderChange(ASCII_ENCODATION);\n                    break;\n                }\n            }\n        }\n        this.handleEOD(context, buffer);\n    };\n    C40Encoder.prototype.backtrackOneCharacter = function (context, buffer, removed, lastCharSize) {\n        var count = buffer.length();\n        var test = buffer.toString().substring(0, count - lastCharSize);\n        buffer.setLengthToZero();\n        buffer.append(test);\n        // buffer.delete(count - lastCharSize, count);\n        /*for (let i = count - lastCharSize; i < count; i++) {\n          buffer.deleteCharAt(i);\n        }*/\n        context.pos--;\n        var c = context.getCurrentChar();\n        lastCharSize = this.encodeChar(c, removed);\n        context.resetSymbolInfo(); // Deal with possible reduction in symbol size\n        return lastCharSize;\n    };\n    C40Encoder.prototype.writeNextTriplet = function (context, buffer) {\n        context.writeCodewords(this.encodeToCodewords(buffer.toString()));\n        var test = buffer.toString().substring(3);\n        buffer.setLengthToZero();\n        buffer.append(test);\n        // buffer.delete(0, 3);\n        /*for (let i = 0; i < 3; i++) {\n          buffer.deleteCharAt(i);\n        }*/\n    };\n    /**\n     * Handle \"end of data\" situations\n     *\n     * @param context the encoder context\n     * @param buffer  the buffer with the remaining encoded characters\n     */\n    C40Encoder.prototype.handleEOD = function (context, buffer) {\n        var unwritten = Math.floor((buffer.length() / 3) * 2);\n        var rest = buffer.length() % 3;\n        var curCodewordCount = context.getCodewordCount() + unwritten;\n        context.updateSymbolInfo(curCodewordCount);\n        var available = context.getSymbolInfo().getDataCapacity() - curCodewordCount;\n        if (rest === 2) {\n            buffer.append('\\0'); // Shift 1\n            while (buffer.length() >= 3) {\n                this.writeNextTriplet(context, buffer);\n            }\n            if (context.hasMoreCharacters()) {\n                context.writeCodeword(C40_UNLATCH);\n            }\n        }\n        else if (available === 1 && rest === 1) {\n            while (buffer.length() >= 3) {\n                this.writeNextTriplet(context, buffer);\n            }\n            if (context.hasMoreCharacters()) {\n                context.writeCodeword(C40_UNLATCH);\n            }\n            // else no unlatch\n            context.pos--;\n        }\n        else if (rest === 0) {\n            while (buffer.length() >= 3) {\n                this.writeNextTriplet(context, buffer);\n            }\n            if (available > 0 || context.hasMoreCharacters()) {\n                context.writeCodeword(C40_UNLATCH);\n            }\n        }\n        else {\n            throw new Error('Unexpected case. Please report!');\n        }\n        context.signalEncoderChange(ASCII_ENCODATION);\n    };\n    C40Encoder.prototype.encodeChar = function (c, sb) {\n        if (c === ' '.charCodeAt(0)) {\n            sb.append(3);\n            return 1;\n        }\n        if (c >= '0'.charCodeAt(0) && c <= '9'.charCodeAt(0)) {\n            sb.append(c - 48 + 4);\n            return 1;\n        }\n        if (c >= 'A'.charCodeAt(0) && c <= 'Z'.charCodeAt(0)) {\n            sb.append(c - 65 + 14);\n            return 1;\n        }\n        if (c < ' '.charCodeAt(0)) {\n            sb.append(0); // Shift 1 Set\n            sb.append(c);\n            return 2;\n        }\n        if (c <= '/'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 33);\n            return 2;\n        }\n        if (c <= '@'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 58 + 15);\n            return 2;\n        }\n        if (c <= '_'.charCodeAt(0)) {\n            sb.append(1); // Shift 2 Set\n            sb.append(c - 91 + 22);\n            return 2;\n        }\n        if (c <= 127) {\n            sb.append(2); // Shift 3 Set\n            sb.append(c - 96);\n            return 2;\n        }\n        sb.append(1 + \"\\u001E\"); // Shift 2, Upper Shift\n        var len = 2;\n        len += this.encodeChar(c - 128, sb);\n        return len;\n    };\n    C40Encoder.prototype.encodeToCodewords = function (sb) {\n        var v = 1600 * sb.charCodeAt(0) + 40 * sb.charCodeAt(1) + sb.charCodeAt(2) + 1;\n        var cw1 = v / 256;\n        var cw2 = v % 256;\n        var result = new StringBuilder();\n        result.append(cw1);\n        result.append(cw2);\n        return result.toString();\n    };\n    return C40Encoder;\n}());\nexport { C40Encoder };\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0BAA0B;AACpD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,cAAc,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,WAAW,QAAS,aAAa;AAC1F,IAAIC,UAAU,GAAG,aAAe,YAAY;EACxC,SAASA,UAAUA,CAAA,EAAG,CACtB;EACAA,UAAU,CAACC,SAAS,CAACC,eAAe,GAAG,YAAY;IAC/C,OAAON,cAAc;EACzB,CAAC;EACDI,UAAU,CAACC,SAAS,CAACE,aAAa,GAAG,UAAUC,OAAO,EAAE;IACpD,IAAIC,MAAM,GAAG,IAAIX,aAAa,CAAC,CAAC;IAChC,IAAIY,YAAY,GAAG,CAAC;IACpB,IAAIC,sBAAsB,GAAGH,OAAO,CAACI,GAAG;IACxC,IAAIC,qBAAqB,GAAG,CAAC;IAC7B,OAAOL,OAAO,CAACM,iBAAiB,CAAC,CAAC,EAAE;MAChC,IAAIC,CAAC,GAAGP,OAAO,CAACQ,cAAc,CAAC,CAAC;MAChCR,OAAO,CAACI,GAAG,EAAE;MACbF,YAAY,GAAG,IAAI,CAACO,UAAU,CAACF,CAAC,EAAEN,MAAM,CAAC;MACzC,IAAIA,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QAC3BP,sBAAsB,GAAGH,OAAO,CAACI,GAAG;QACpCC,qBAAqB,GAAGJ,MAAM,CAACS,MAAM,CAAC,CAAC;MAC3C;IACJ;IACA,IAAIL,qBAAqB,KAAKJ,MAAM,CAACS,MAAM,CAAC,CAAC,EAAE;MAC3C,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAEZ,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC;MACrD,IAAII,gBAAgB,GAAGF,IAAI,CAACC,KAAK,CAACb,OAAO,CAACe,gBAAgB,CAAC,CAAC,GAAGJ,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;MAC/EX,OAAO,CAACgB,gBAAgB,CAACF,gBAAgB,CAAC;MAC1C,IAAIG,SAAS,GAAGjB,OAAO,CAACkB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,GAAGL,gBAAgB;MAC5E,IAAIM,IAAI,GAAGR,IAAI,CAACC,KAAK,CAACZ,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;MAC1C,IAAKU,IAAI,KAAK,CAAC,IAAIH,SAAS,KAAK,CAAC,IAC7BG,IAAI,KAAK,CAAC,KAAKlB,YAAY,GAAG,CAAC,IAAIe,SAAS,KAAK,CAAC,CAAE,EAAE;QACvD;QACAjB,OAAO,CAACI,GAAG,GAAGD,sBAAsB;MACxC;IACJ;IACA,IAAIF,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;MACrBV,OAAO,CAACqB,aAAa,CAAC5B,YAAY,CAAC;IACvC;IACA,IAAI,CAAC6B,SAAS,CAACtB,OAAO,EAAEC,MAAM,CAAC;EACnC,CAAC;EACDL,UAAU,CAACC,SAAS,CAAC0B,MAAM,GAAG,UAAUvB,OAAO,EAAE;IAC7C;IACA,IAAIC,MAAM,GAAG,IAAIX,aAAa,CAAC,CAAC;IAChC,OAAOU,OAAO,CAACM,iBAAiB,CAAC,CAAC,EAAE;MAChC,IAAIC,CAAC,GAAGP,OAAO,CAACQ,cAAc,CAAC,CAAC;MAChCR,OAAO,CAACI,GAAG,EAAE;MACb,IAAIF,YAAY,GAAG,IAAI,CAACO,UAAU,CAACF,CAAC,EAAEN,MAAM,CAAC;MAC7C,IAAIU,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACZ,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MACnD,IAAII,gBAAgB,GAAGd,OAAO,CAACe,gBAAgB,CAAC,CAAC,GAAGJ,SAAS;MAC7DX,OAAO,CAACgB,gBAAgB,CAACF,gBAAgB,CAAC;MAC1C,IAAIG,SAAS,GAAGjB,OAAO,CAACkB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,GAAGL,gBAAgB;MAC5E,IAAI,CAACd,OAAO,CAACM,iBAAiB,CAAC,CAAC,EAAE;QAC9B;QACA,IAAIkB,OAAO,GAAG,IAAIlC,aAAa,CAAC,CAAC;QACjC,IAAIW,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAIO,SAAS,KAAK,CAAC,EAAE;UAC9Cf,YAAY,GAAG,IAAI,CAACuB,qBAAqB,CAACzB,OAAO,EAAEC,MAAM,EAAEuB,OAAO,EAAEtB,YAAY,CAAC;QACrF;QACA,OAAOD,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,KAC3BR,YAAY,GAAG,CAAC,IAAIe,SAAS,KAAK,CAAC,CAAC,EAAE;UACvCf,YAAY,GAAG,IAAI,CAACuB,qBAAqB,CAACzB,OAAO,EAAEC,MAAM,EAAEuB,OAAO,EAAEtB,YAAY,CAAC;QACrF;QACA;MACJ;MACA,IAAIwB,KAAK,GAAGzB,MAAM,CAACS,MAAM,CAAC,CAAC;MAC3B,IAAIgB,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE;QACjB,IAAIC,OAAO,GAAGpC,gBAAgB,CAACqC,aAAa,CAAC5B,OAAO,CAAC6B,UAAU,CAAC,CAAC,EAAE7B,OAAO,CAACI,GAAG,EAAE,IAAI,CAACN,eAAe,CAAC,CAAC,CAAC;QACvG,IAAI6B,OAAO,KAAK,IAAI,CAAC7B,eAAe,CAAC,CAAC,EAAE;UACpC;UACAE,OAAO,CAAC8B,mBAAmB,CAACpC,gBAAgB,CAAC;UAC7C;QACJ;MACJ;IACJ;IACA,IAAI,CAAC4B,SAAS,CAACtB,OAAO,EAAEC,MAAM,CAAC;EACnC,CAAC;EACDL,UAAU,CAACC,SAAS,CAAC4B,qBAAqB,GAAG,UAAUzB,OAAO,EAAEC,MAAM,EAAEuB,OAAO,EAAEtB,YAAY,EAAE;IAC3F,IAAIwB,KAAK,GAAGzB,MAAM,CAACS,MAAM,CAAC,CAAC;IAC3B,IAAIqB,IAAI,GAAG9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,EAAEP,KAAK,GAAGxB,YAAY,CAAC;IAC/DD,MAAM,CAACiC,eAAe,CAAC,CAAC;IACxBjC,MAAM,CAACkC,MAAM,CAACJ,IAAI,CAAC;IACnB;IACA;AACR;AACA;IACQ/B,OAAO,CAACI,GAAG,EAAE;IACb,IAAIG,CAAC,GAAGP,OAAO,CAACQ,cAAc,CAAC,CAAC;IAChCN,YAAY,GAAG,IAAI,CAACO,UAAU,CAACF,CAAC,EAAEiB,OAAO,CAAC;IAC1CxB,OAAO,CAACoC,eAAe,CAAC,CAAC,CAAC,CAAC;IAC3B,OAAOlC,YAAY;EACvB,CAAC;EACDN,UAAU,CAACC,SAAS,CAACwC,gBAAgB,GAAG,UAAUrC,OAAO,EAAEC,MAAM,EAAE;IAC/DD,OAAO,CAACsC,cAAc,CAAC,IAAI,CAACC,iBAAiB,CAACtC,MAAM,CAAC+B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACjE,IAAID,IAAI,GAAG9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;IACzChC,MAAM,CAACiC,eAAe,CAAC,CAAC;IACxBjC,MAAM,CAACkC,MAAM,CAACJ,IAAI,CAAC;IACnB;IACA;AACR;AACA;EACI,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACInC,UAAU,CAACC,SAAS,CAACyB,SAAS,GAAG,UAAUtB,OAAO,EAAEC,MAAM,EAAE;IACxD,IAAIU,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAEZ,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC;IACrD,IAAIU,IAAI,GAAGnB,MAAM,CAACS,MAAM,CAAC,CAAC,GAAG,CAAC;IAC9B,IAAII,gBAAgB,GAAGd,OAAO,CAACe,gBAAgB,CAAC,CAAC,GAAGJ,SAAS;IAC7DX,OAAO,CAACgB,gBAAgB,CAACF,gBAAgB,CAAC;IAC1C,IAAIG,SAAS,GAAGjB,OAAO,CAACkB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,GAAGL,gBAAgB;IAC5E,IAAIM,IAAI,KAAK,CAAC,EAAE;MACZnB,MAAM,CAACkC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;MACrB,OAAOlC,MAAM,CAACS,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC2B,gBAAgB,CAACrC,OAAO,EAAEC,MAAM,CAAC;MAC1C;MACA,IAAID,OAAO,CAACM,iBAAiB,CAAC,CAAC,EAAE;QAC7BN,OAAO,CAACqB,aAAa,CAAC1B,WAAW,CAAC;MACtC;IACJ,CAAC,MACI,IAAIsB,SAAS,KAAK,CAAC,IAAIG,IAAI,KAAK,CAAC,EAAE;MACpC,OAAOnB,MAAM,CAACS,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC2B,gBAAgB,CAACrC,OAAO,EAAEC,MAAM,CAAC;MAC1C;MACA,IAAID,OAAO,CAACM,iBAAiB,CAAC,CAAC,EAAE;QAC7BN,OAAO,CAACqB,aAAa,CAAC1B,WAAW,CAAC;MACtC;MACA;MACAK,OAAO,CAACI,GAAG,EAAE;IACjB,CAAC,MACI,IAAIgB,IAAI,KAAK,CAAC,EAAE;MACjB,OAAOnB,MAAM,CAACS,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;QACzB,IAAI,CAAC2B,gBAAgB,CAACrC,OAAO,EAAEC,MAAM,CAAC;MAC1C;MACA,IAAIgB,SAAS,GAAG,CAAC,IAAIjB,OAAO,CAACM,iBAAiB,CAAC,CAAC,EAAE;QAC9CN,OAAO,CAACqB,aAAa,CAAC1B,WAAW,CAAC;MACtC;IACJ,CAAC,MACI;MACD,MAAM,IAAI6C,KAAK,CAAC,iCAAiC,CAAC;IACtD;IACAxC,OAAO,CAAC8B,mBAAmB,CAACpC,gBAAgB,CAAC;EACjD,CAAC;EACDE,UAAU,CAACC,SAAS,CAACY,UAAU,GAAG,UAAUF,CAAC,EAAEkC,EAAE,EAAE;IAC/C,IAAIlC,CAAC,KAAK,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAE;MACzBD,EAAE,CAACN,MAAM,CAAC,CAAC,CAAC;MACZ,OAAO,CAAC;IACZ;IACA,IAAI5B,CAAC,IAAI,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,IAAInC,CAAC,IAAI,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAE;MAClDD,EAAE,CAACN,MAAM,CAAC5B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;MACrB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,IAAInC,CAAC,IAAI,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAE;MAClDD,EAAE,CAACN,MAAM,CAAC5B,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;MACtB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,GAAG,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAE;MACvBD,EAAE,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdM,EAAE,CAACN,MAAM,CAAC5B,CAAC,CAAC;MACZ,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAE;MACxBD,EAAE,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdM,EAAE,CAACN,MAAM,CAAC5B,CAAC,GAAG,EAAE,CAAC;MACjB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAE;MACxBD,EAAE,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdM,EAAE,CAACN,MAAM,CAAC5B,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;MACtB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,CAACmC,UAAU,CAAC,CAAC,CAAC,EAAE;MACxBD,EAAE,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdM,EAAE,CAACN,MAAM,CAAC5B,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;MACtB,OAAO,CAAC;IACZ;IACA,IAAIA,CAAC,IAAI,GAAG,EAAE;MACVkC,EAAE,CAACN,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACdM,EAAE,CAACN,MAAM,CAAC5B,CAAC,GAAG,EAAE,CAAC;MACjB,OAAO,CAAC;IACZ;IACAkC,EAAE,CAACN,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC;IACzB,IAAIQ,GAAG,GAAG,CAAC;IACXA,GAAG,IAAI,IAAI,CAAClC,UAAU,CAACF,CAAC,GAAG,GAAG,EAAEkC,EAAE,CAAC;IACnC,OAAOE,GAAG;EACd,CAAC;EACD/C,UAAU,CAACC,SAAS,CAAC0C,iBAAiB,GAAG,UAAUE,EAAE,EAAE;IACnD,IAAIG,CAAC,GAAG,IAAI,GAAGH,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,GAAGD,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,GAAGD,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;IAC9E,IAAIG,GAAG,GAAGD,CAAC,GAAG,GAAG;IACjB,IAAIE,GAAG,GAAGF,CAAC,GAAG,GAAG;IACjB,IAAIG,MAAM,GAAG,IAAIzD,aAAa,CAAC,CAAC;IAChCyD,MAAM,CAACZ,MAAM,CAACU,GAAG,CAAC;IAClBE,MAAM,CAACZ,MAAM,CAACW,GAAG,CAAC;IAClB,OAAOC,MAAM,CAACf,QAAQ,CAAC,CAAC;EAC5B,CAAC;EACD,OAAOpC,UAAU;AACrB,CAAC,CAAC,CAAE;AACJ,SAASA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}