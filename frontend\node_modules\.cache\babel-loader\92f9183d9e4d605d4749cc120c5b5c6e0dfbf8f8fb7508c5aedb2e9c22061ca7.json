{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport MultiFormatReader from '../core/MultiFormatReader';\nvar BrowserMultiFormatReader = /** @class */function (_super) {\n  __extends(BrowserMultiFormatReader, _super);\n  function BrowserMultiFormatReader(hints, timeBetweenScansMillis) {\n    if (hints === void 0) {\n      hints = null;\n    }\n    if (timeBetweenScansMillis === void 0) {\n      timeBetweenScansMillis = 500;\n    }\n    var _this = this;\n    var reader = new MultiFormatReader();\n    reader.setHints(hints);\n    _this = _super.call(this, reader, timeBetweenScansMillis) || this;\n    return _this;\n  }\n  /**\n   * Overwrite decodeBitmap to call decodeWithState, which will pay\n   * attention to the hints set in the constructor function\n   */\n  BrowserMultiFormatReader.prototype.decodeBitmap = function (binaryBitmap) {\n    return this.reader.decodeWithState(binaryBitmap);\n  };\n  return BrowserMultiFormatReader;\n}(BrowserCodeReader);\nexport { BrowserMultiFormatReader };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MultiFormatReader", "BrowserMultiFormatReader", "_super", "hints", "timeBetweenScansMillis", "_this", "reader", "setHints", "call", "decodeBitmap", "binaryBitmap", "decodeWithState"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/browser/BrowserMultiFormatReader.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { BrowserCodeReader } from './BrowserCodeReader';\nimport MultiFormatReader from '../core/MultiFormatReader';\nvar BrowserMultiFormatReader = /** @class */ (function (_super) {\n    __extends(BrowserMultiFormatReader, _super);\n    function BrowserMultiFormatReader(hints, timeBetweenScansMillis) {\n        if (hints === void 0) { hints = null; }\n        if (timeBetweenScansMillis === void 0) { timeBetweenScansMillis = 500; }\n        var _this = this;\n        var reader = new MultiFormatReader();\n        reader.setHints(hints);\n        _this = _super.call(this, reader, timeBetweenScansMillis) || this;\n        return _this;\n    }\n    /**\n     * Overwrite decodeBitmap to call decodeWithState, which will pay\n     * attention to the hints set in the constructor function\n     */\n    BrowserMultiFormatReader.prototype.decodeBitmap = function (binaryBitmap) {\n        return this.reader.decodeWithState(binaryBitmap);\n    };\n    return BrowserMultiFormatReader;\n}(BrowserCodeReader));\nexport { BrowserMultiFormatReader };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,SAASI,iBAAiB,QAAQ,qBAAqB;AACvD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,IAAIC,wBAAwB,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC5DjB,SAAS,CAACgB,wBAAwB,EAAEC,MAAM,CAAC;EAC3C,SAASD,wBAAwBA,CAACE,KAAK,EAAEC,sBAAsB,EAAE;IAC7D,IAAID,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,IAAI;IAAE;IACtC,IAAIC,sBAAsB,KAAK,KAAK,CAAC,EAAE;MAAEA,sBAAsB,GAAG,GAAG;IAAE;IACvE,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAIC,MAAM,GAAG,IAAIN,iBAAiB,CAAC,CAAC;IACpCM,MAAM,CAACC,QAAQ,CAACJ,KAAK,CAAC;IACtBE,KAAK,GAAGH,MAAM,CAACM,IAAI,CAAC,IAAI,EAAEF,MAAM,EAAEF,sBAAsB,CAAC,IAAI,IAAI;IACjE,OAAOC,KAAK;EAChB;EACA;AACJ;AACA;AACA;EACIJ,wBAAwB,CAACJ,SAAS,CAACY,YAAY,GAAG,UAAUC,YAAY,EAAE;IACtE,OAAO,IAAI,CAACJ,MAAM,CAACK,eAAe,CAACD,YAAY,CAAC;EACpD,CAAC;EACD,OAAOT,wBAAwB;AACnC,CAAC,CAACF,iBAAiB,CAAE;AACrB,SAASE,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}