{"ast": null, "code": "/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.nio.charset.Charset;*/\n/*import java.util.Map;*/\nimport DecodeHintType from '../DecodeHintType';\nimport CharacterSetECI from './CharacterSetECI';\nimport StringEncoding from '../util/StringEncoding';\n/**\n * Common string-related functions.\n *\n * <AUTHOR>\n * <AUTHOR> <PERSON>re\n */\nvar StringUtils = /** @class */function () {\n  function StringUtils() {}\n  // SHIFT_JIS.equalsIgnoreCase(PLATFORM_DEFAULT_ENCODING) ||\n  // EUC_JP.equalsIgnoreCase(PLATFORM_DEFAULT_ENCODING);\n  StringUtils.castAsNonUtf8Char = function (code, encoding) {\n    if (encoding === void 0) {\n      encoding = null;\n    }\n    // ISO 8859-1 is the Java default as UTF-8 is JavaScripts\n    // you can see this method as a Java version of String.fromCharCode\n    var e = encoding ? encoding.getName() : this.ISO88591;\n    // use passed format (fromCharCode will return UTF8 encoding)\n    return StringEncoding.decode(new Uint8Array([code]), e);\n  };\n  /**\n   * @param bytes bytes encoding a string, whose encoding should be guessed\n   * @param hints decode hints if applicable\n   * @return name of guessed encoding; at the moment will only guess one of:\n   *  {@link #SHIFT_JIS}, {@link #UTF8}, {@link #ISO88591}, or the platform\n   *  default encoding if none of these can possibly be correct\n   */\n  StringUtils.guessEncoding = function (bytes, hints) {\n    if (hints !== null && hints !== undefined && undefined !== hints.get(DecodeHintType.CHARACTER_SET)) {\n      return hints.get(DecodeHintType.CHARACTER_SET).toString();\n    }\n    // For now, merely tries to distinguish ISO-8859-1, UTF-8 and Shift_JIS,\n    // which should be by far the most common encodings.\n    var length = bytes.length;\n    var canBeISO88591 = true;\n    var canBeShiftJIS = true;\n    var canBeUTF8 = true;\n    var utf8BytesLeft = 0;\n    // int utf8LowChars = 0\n    var utf2BytesChars = 0;\n    var utf3BytesChars = 0;\n    var utf4BytesChars = 0;\n    var sjisBytesLeft = 0;\n    // int sjisLowChars = 0\n    var sjisKatakanaChars = 0;\n    // int sjisDoubleBytesChars = 0\n    var sjisCurKatakanaWordLength = 0;\n    var sjisCurDoubleBytesWordLength = 0;\n    var sjisMaxKatakanaWordLength = 0;\n    var sjisMaxDoubleBytesWordLength = 0;\n    // int isoLowChars = 0\n    // int isoHighChars = 0\n    var isoHighOther = 0;\n    var utf8bom = bytes.length > 3 && bytes[0] === /*(byte) */0xEF && bytes[1] === /*(byte) */0xBB && bytes[2] === /*(byte) */0xBF;\n    for (var i = 0; i < length && (canBeISO88591 || canBeShiftJIS || canBeUTF8); i++) {\n      var value = bytes[i] & 0xFF;\n      // UTF-8 stuff\n      if (canBeUTF8) {\n        if (utf8BytesLeft > 0) {\n          if ((value & 0x80) === 0) {\n            canBeUTF8 = false;\n          } else {\n            utf8BytesLeft--;\n          }\n        } else if ((value & 0x80) !== 0) {\n          if ((value & 0x40) === 0) {\n            canBeUTF8 = false;\n          } else {\n            utf8BytesLeft++;\n            if ((value & 0x20) === 0) {\n              utf2BytesChars++;\n            } else {\n              utf8BytesLeft++;\n              if ((value & 0x10) === 0) {\n                utf3BytesChars++;\n              } else {\n                utf8BytesLeft++;\n                if ((value & 0x08) === 0) {\n                  utf4BytesChars++;\n                } else {\n                  canBeUTF8 = false;\n                }\n              }\n            }\n          }\n        } // else {\n        // utf8LowChars++\n        // }\n      }\n      // ISO-8859-1 stuff\n      if (canBeISO88591) {\n        if (value > 0x7F && value < 0xA0) {\n          canBeISO88591 = false;\n        } else if (value > 0x9F) {\n          if (value < 0xC0 || value === 0xD7 || value === 0xF7) {\n            isoHighOther++;\n          } // else {\n          // isoHighChars++\n          // }\n        } // else {\n        // isoLowChars++\n        // }\n      }\n      // Shift_JIS stuff\n      if (canBeShiftJIS) {\n        if (sjisBytesLeft > 0) {\n          if (value < 0x40 || value === 0x7F || value > 0xFC) {\n            canBeShiftJIS = false;\n          } else {\n            sjisBytesLeft--;\n          }\n        } else if (value === 0x80 || value === 0xA0 || value > 0xEF) {\n          canBeShiftJIS = false;\n        } else if (value > 0xA0 && value < 0xE0) {\n          sjisKatakanaChars++;\n          sjisCurDoubleBytesWordLength = 0;\n          sjisCurKatakanaWordLength++;\n          if (sjisCurKatakanaWordLength > sjisMaxKatakanaWordLength) {\n            sjisMaxKatakanaWordLength = sjisCurKatakanaWordLength;\n          }\n        } else if (value > 0x7F) {\n          sjisBytesLeft++;\n          // sjisDoubleBytesChars++\n          sjisCurKatakanaWordLength = 0;\n          sjisCurDoubleBytesWordLength++;\n          if (sjisCurDoubleBytesWordLength > sjisMaxDoubleBytesWordLength) {\n            sjisMaxDoubleBytesWordLength = sjisCurDoubleBytesWordLength;\n          }\n        } else {\n          // sjisLowChars++\n          sjisCurKatakanaWordLength = 0;\n          sjisCurDoubleBytesWordLength = 0;\n        }\n      }\n    }\n    if (canBeUTF8 && utf8BytesLeft > 0) {\n      canBeUTF8 = false;\n    }\n    if (canBeShiftJIS && sjisBytesLeft > 0) {\n      canBeShiftJIS = false;\n    }\n    // Easy -- if there is BOM or at least 1 valid not-single byte character (and no evidence it can't be UTF-8), done\n    if (canBeUTF8 && (utf8bom || utf2BytesChars + utf3BytesChars + utf4BytesChars > 0)) {\n      return StringUtils.UTF8;\n    }\n    // Easy -- if assuming Shift_JIS or at least 3 valid consecutive not-ascii characters (and no evidence it can't be), done\n    if (canBeShiftJIS && (StringUtils.ASSUME_SHIFT_JIS || sjisMaxKatakanaWordLength >= 3 || sjisMaxDoubleBytesWordLength >= 3)) {\n      return StringUtils.SHIFT_JIS;\n    }\n    // Distinguishing Shift_JIS and ISO-8859-1 can be a little tough for short words. The crude heuristic is:\n    // - If we saw\n    //   - only two consecutive katakana chars in the whole text, or\n    //   - at least 10% of bytes that could be \"upper\" not-alphanumeric Latin1,\n    // - then we conclude Shift_JIS, else ISO-8859-1\n    if (canBeISO88591 && canBeShiftJIS) {\n      return sjisMaxKatakanaWordLength === 2 && sjisKatakanaChars === 2 || isoHighOther * 10 >= length ? StringUtils.SHIFT_JIS : StringUtils.ISO88591;\n    }\n    // Otherwise, try in order ISO-8859-1, Shift JIS, UTF-8 and fall back to default platform encoding\n    if (canBeISO88591) {\n      return StringUtils.ISO88591;\n    }\n    if (canBeShiftJIS) {\n      return StringUtils.SHIFT_JIS;\n    }\n    if (canBeUTF8) {\n      return StringUtils.UTF8;\n    }\n    // Otherwise, we take a wild guess with platform encoding\n    return StringUtils.PLATFORM_DEFAULT_ENCODING;\n  };\n  /**\n   *\n   * @see https://stackoverflow.com/a/13439711/4367683\n   *\n   * @param append The new string to append.\n   * @param args Argumets values to be formated.\n   */\n  StringUtils.format = function (append) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    var i = -1;\n    function callback(exp, p0, p1, p2, p3, p4) {\n      if (exp === '%%') return '%';\n      if (args[++i] === undefined) return undefined;\n      exp = p2 ? parseInt(p2.substr(1)) : undefined;\n      var base = p3 ? parseInt(p3.substr(1)) : undefined;\n      var val;\n      switch (p4) {\n        case 's':\n          val = args[i];\n          break;\n        case 'c':\n          val = args[i][0];\n          break;\n        case 'f':\n          val = parseFloat(args[i]).toFixed(exp);\n          break;\n        case 'p':\n          val = parseFloat(args[i]).toPrecision(exp);\n          break;\n        case 'e':\n          val = parseFloat(args[i]).toExponential(exp);\n          break;\n        case 'x':\n          val = parseInt(args[i]).toString(base ? base : 16);\n          break;\n        case 'd':\n          val = parseFloat(parseInt(args[i], base ? base : 10).toPrecision(exp)).toFixed(0);\n          break;\n      }\n      val = typeof val === 'object' ? JSON.stringify(val) : (+val).toString(base);\n      var size = parseInt(p1); /* padding size */\n      var ch = p1 && p1[0] + '' === '0' ? '0' : ' '; /* isnull? */\n      while (val.length < size) val = p0 !== undefined ? val + ch : ch + val; /* isminus? */\n      return val;\n    }\n    var regex = /%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;\n    return append.replace(regex, callback);\n  };\n  /**\n   *\n   */\n  StringUtils.getBytes = function (str, encoding) {\n    return StringEncoding.encode(str, encoding);\n  };\n  /**\n   * Returns the charcode at the specified index or at index zero.\n   */\n  StringUtils.getCharCode = function (str, index) {\n    if (index === void 0) {\n      index = 0;\n    }\n    return str.charCodeAt(index);\n  };\n  /**\n   * Returns char for given charcode\n   */\n  StringUtils.getCharAt = function (charCode) {\n    return String.fromCharCode(charCode);\n  };\n  StringUtils.SHIFT_JIS = CharacterSetECI.SJIS.getName(); // \"SJIS\"\n  StringUtils.GB2312 = 'GB2312';\n  StringUtils.ISO88591 = CharacterSetECI.ISO8859_1.getName(); // \"ISO8859_1\"\n  StringUtils.EUC_JP = 'EUC_JP';\n  StringUtils.UTF8 = CharacterSetECI.UTF8.getName(); // \"UTF8\"\n  StringUtils.PLATFORM_DEFAULT_ENCODING = StringUtils.UTF8; // \"UTF8\"//Charset.defaultCharset().name()\n  StringUtils.ASSUME_SHIFT_JIS = false;\n  return StringUtils;\n}();\nexport default StringUtils;", "map": {"version": 3, "names": ["DecodeHintType", "CharacterSetECI", "StringEncoding", "StringUtils", "castAsNonUtf8Char", "code", "encoding", "e", "getName", "ISO88591", "decode", "Uint8Array", "guessEncoding", "bytes", "hints", "undefined", "get", "CHARACTER_SET", "toString", "length", "canBeISO88591", "canBeShiftJIS", "canBeUTF8", "utf8BytesLeft", "utf2BytesChars", "utf3BytesChars", "utf4BytesChars", "sjisBytesLeft", "sjisKatakanaChars", "sjisCurKatakanaWordLength", "sjisCurDoubleBytesWordLength", "sjisMaxKatakanaWordLength", "sjisMaxDoubleBytesWordLength", "isoHighOther", "utf8bom", "i", "value", "UTF8", "ASSUME_SHIFT_JIS", "SHIFT_JIS", "PLATFORM_DEFAULT_ENCODING", "format", "append", "args", "_i", "arguments", "callback", "exp", "p0", "p1", "p2", "p3", "p4", "parseInt", "substr", "base", "val", "parseFloat", "toFixed", "toPrecision", "toExponential", "JSON", "stringify", "size", "ch", "regex", "replace", "getBytes", "str", "encode", "getCharCode", "index", "charCodeAt", "getCharAt", "charCode", "String", "fromCharCode", "SJIS", "GB2312", "ISO8859_1", "EUC_JP"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/common/StringUtils.js"], "sourcesContent": ["/*\n * Copyright (C) 2010 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.common {*/\n/*import java.nio.charset.Charset;*/\n/*import java.util.Map;*/\nimport DecodeHintType from '../DecodeHintType';\nimport CharacterSetECI from './CharacterSetECI';\nimport StringEncoding from '../util/StringEncoding';\n/**\n * Common string-related functions.\n *\n * <AUTHOR>\n * <AUTHOR> <PERSON>re\n */\nvar StringUtils = /** @class */ (function () {\n    function StringUtils() {\n    }\n    // SHIFT_JIS.equalsIgnoreCase(PLATFORM_DEFAULT_ENCODING) ||\n    // EUC_JP.equalsIgnoreCase(PLATFORM_DEFAULT_ENCODING);\n    StringUtils.castAsNonUtf8Char = function (code, encoding) {\n        if (encoding === void 0) { encoding = null; }\n        // ISO 8859-1 is the Java default as UTF-8 is JavaScripts\n        // you can see this method as a Java version of String.fromCharCode\n        var e = encoding ? encoding.getName() : this.ISO88591;\n        // use passed format (fromCharCode will return UTF8 encoding)\n        return StringEncoding.decode(new Uint8Array([code]), e);\n    };\n    /**\n     * @param bytes bytes encoding a string, whose encoding should be guessed\n     * @param hints decode hints if applicable\n     * @return name of guessed encoding; at the moment will only guess one of:\n     *  {@link #SHIFT_JIS}, {@link #UTF8}, {@link #ISO88591}, or the platform\n     *  default encoding if none of these can possibly be correct\n     */\n    StringUtils.guessEncoding = function (bytes, hints) {\n        if (hints !== null && hints !== undefined && undefined !== hints.get(DecodeHintType.CHARACTER_SET)) {\n            return hints.get(DecodeHintType.CHARACTER_SET).toString();\n        }\n        // For now, merely tries to distinguish ISO-8859-1, UTF-8 and Shift_JIS,\n        // which should be by far the most common encodings.\n        var length = bytes.length;\n        var canBeISO88591 = true;\n        var canBeShiftJIS = true;\n        var canBeUTF8 = true;\n        var utf8BytesLeft = 0;\n        // int utf8LowChars = 0\n        var utf2BytesChars = 0;\n        var utf3BytesChars = 0;\n        var utf4BytesChars = 0;\n        var sjisBytesLeft = 0;\n        // int sjisLowChars = 0\n        var sjisKatakanaChars = 0;\n        // int sjisDoubleBytesChars = 0\n        var sjisCurKatakanaWordLength = 0;\n        var sjisCurDoubleBytesWordLength = 0;\n        var sjisMaxKatakanaWordLength = 0;\n        var sjisMaxDoubleBytesWordLength = 0;\n        // int isoLowChars = 0\n        // int isoHighChars = 0\n        var isoHighOther = 0;\n        var utf8bom = bytes.length > 3 &&\n            bytes[0] === /*(byte) */ 0xEF &&\n            bytes[1] === /*(byte) */ 0xBB &&\n            bytes[2] === /*(byte) */ 0xBF;\n        for (var i = 0; i < length && (canBeISO88591 || canBeShiftJIS || canBeUTF8); i++) {\n            var value = bytes[i] & 0xFF;\n            // UTF-8 stuff\n            if (canBeUTF8) {\n                if (utf8BytesLeft > 0) {\n                    if ((value & 0x80) === 0) {\n                        canBeUTF8 = false;\n                    }\n                    else {\n                        utf8BytesLeft--;\n                    }\n                }\n                else if ((value & 0x80) !== 0) {\n                    if ((value & 0x40) === 0) {\n                        canBeUTF8 = false;\n                    }\n                    else {\n                        utf8BytesLeft++;\n                        if ((value & 0x20) === 0) {\n                            utf2BytesChars++;\n                        }\n                        else {\n                            utf8BytesLeft++;\n                            if ((value & 0x10) === 0) {\n                                utf3BytesChars++;\n                            }\n                            else {\n                                utf8BytesLeft++;\n                                if ((value & 0x08) === 0) {\n                                    utf4BytesChars++;\n                                }\n                                else {\n                                    canBeUTF8 = false;\n                                }\n                            }\n                        }\n                    }\n                } // else {\n                // utf8LowChars++\n                // }\n            }\n            // ISO-8859-1 stuff\n            if (canBeISO88591) {\n                if (value > 0x7F && value < 0xA0) {\n                    canBeISO88591 = false;\n                }\n                else if (value > 0x9F) {\n                    if (value < 0xC0 || value === 0xD7 || value === 0xF7) {\n                        isoHighOther++;\n                    } // else {\n                    // isoHighChars++\n                    // }\n                } // else {\n                // isoLowChars++\n                // }\n            }\n            // Shift_JIS stuff\n            if (canBeShiftJIS) {\n                if (sjisBytesLeft > 0) {\n                    if (value < 0x40 || value === 0x7F || value > 0xFC) {\n                        canBeShiftJIS = false;\n                    }\n                    else {\n                        sjisBytesLeft--;\n                    }\n                }\n                else if (value === 0x80 || value === 0xA0 || value > 0xEF) {\n                    canBeShiftJIS = false;\n                }\n                else if (value > 0xA0 && value < 0xE0) {\n                    sjisKatakanaChars++;\n                    sjisCurDoubleBytesWordLength = 0;\n                    sjisCurKatakanaWordLength++;\n                    if (sjisCurKatakanaWordLength > sjisMaxKatakanaWordLength) {\n                        sjisMaxKatakanaWordLength = sjisCurKatakanaWordLength;\n                    }\n                }\n                else if (value > 0x7F) {\n                    sjisBytesLeft++;\n                    // sjisDoubleBytesChars++\n                    sjisCurKatakanaWordLength = 0;\n                    sjisCurDoubleBytesWordLength++;\n                    if (sjisCurDoubleBytesWordLength > sjisMaxDoubleBytesWordLength) {\n                        sjisMaxDoubleBytesWordLength = sjisCurDoubleBytesWordLength;\n                    }\n                }\n                else {\n                    // sjisLowChars++\n                    sjisCurKatakanaWordLength = 0;\n                    sjisCurDoubleBytesWordLength = 0;\n                }\n            }\n        }\n        if (canBeUTF8 && utf8BytesLeft > 0) {\n            canBeUTF8 = false;\n        }\n        if (canBeShiftJIS && sjisBytesLeft > 0) {\n            canBeShiftJIS = false;\n        }\n        // Easy -- if there is BOM or at least 1 valid not-single byte character (and no evidence it can't be UTF-8), done\n        if (canBeUTF8 && (utf8bom || utf2BytesChars + utf3BytesChars + utf4BytesChars > 0)) {\n            return StringUtils.UTF8;\n        }\n        // Easy -- if assuming Shift_JIS or at least 3 valid consecutive not-ascii characters (and no evidence it can't be), done\n        if (canBeShiftJIS && (StringUtils.ASSUME_SHIFT_JIS || sjisMaxKatakanaWordLength >= 3 || sjisMaxDoubleBytesWordLength >= 3)) {\n            return StringUtils.SHIFT_JIS;\n        }\n        // Distinguishing Shift_JIS and ISO-8859-1 can be a little tough for short words. The crude heuristic is:\n        // - If we saw\n        //   - only two consecutive katakana chars in the whole text, or\n        //   - at least 10% of bytes that could be \"upper\" not-alphanumeric Latin1,\n        // - then we conclude Shift_JIS, else ISO-8859-1\n        if (canBeISO88591 && canBeShiftJIS) {\n            return (sjisMaxKatakanaWordLength === 2 && sjisKatakanaChars === 2) || isoHighOther * 10 >= length\n                ? StringUtils.SHIFT_JIS : StringUtils.ISO88591;\n        }\n        // Otherwise, try in order ISO-8859-1, Shift JIS, UTF-8 and fall back to default platform encoding\n        if (canBeISO88591) {\n            return StringUtils.ISO88591;\n        }\n        if (canBeShiftJIS) {\n            return StringUtils.SHIFT_JIS;\n        }\n        if (canBeUTF8) {\n            return StringUtils.UTF8;\n        }\n        // Otherwise, we take a wild guess with platform encoding\n        return StringUtils.PLATFORM_DEFAULT_ENCODING;\n    };\n    /**\n     *\n     * @see https://stackoverflow.com/a/13439711/4367683\n     *\n     * @param append The new string to append.\n     * @param args Argumets values to be formated.\n     */\n    StringUtils.format = function (append) {\n        var args = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            args[_i - 1] = arguments[_i];\n        }\n        var i = -1;\n        function callback(exp, p0, p1, p2, p3, p4) {\n            if (exp === '%%')\n                return '%';\n            if (args[++i] === undefined)\n                return undefined;\n            exp = p2 ? parseInt(p2.substr(1)) : undefined;\n            var base = p3 ? parseInt(p3.substr(1)) : undefined;\n            var val;\n            switch (p4) {\n                case 's':\n                    val = args[i];\n                    break;\n                case 'c':\n                    val = args[i][0];\n                    break;\n                case 'f':\n                    val = parseFloat(args[i]).toFixed(exp);\n                    break;\n                case 'p':\n                    val = parseFloat(args[i]).toPrecision(exp);\n                    break;\n                case 'e':\n                    val = parseFloat(args[i]).toExponential(exp);\n                    break;\n                case 'x':\n                    val = parseInt(args[i]).toString(base ? base : 16);\n                    break;\n                case 'd':\n                    val = parseFloat(parseInt(args[i], base ? base : 10).toPrecision(exp)).toFixed(0);\n                    break;\n            }\n            val = typeof val === 'object' ? JSON.stringify(val) : (+val).toString(base);\n            var size = parseInt(p1); /* padding size */\n            var ch = p1 && (p1[0] + '') === '0' ? '0' : ' '; /* isnull? */\n            while (val.length < size)\n                val = p0 !== undefined ? val + ch : ch + val; /* isminus? */\n            return val;\n        }\n        var regex = /%(-)?(0?[0-9]+)?([.][0-9]+)?([#][0-9]+)?([scfpexd%])/g;\n        return append.replace(regex, callback);\n    };\n    /**\n     *\n     */\n    StringUtils.getBytes = function (str, encoding) {\n        return StringEncoding.encode(str, encoding);\n    };\n    /**\n     * Returns the charcode at the specified index or at index zero.\n     */\n    StringUtils.getCharCode = function (str, index) {\n        if (index === void 0) { index = 0; }\n        return str.charCodeAt(index);\n    };\n    /**\n     * Returns char for given charcode\n     */\n    StringUtils.getCharAt = function (charCode) {\n        return String.fromCharCode(charCode);\n    };\n    StringUtils.SHIFT_JIS = CharacterSetECI.SJIS.getName(); // \"SJIS\"\n    StringUtils.GB2312 = 'GB2312';\n    StringUtils.ISO88591 = CharacterSetECI.ISO8859_1.getName(); // \"ISO8859_1\"\n    StringUtils.EUC_JP = 'EUC_JP';\n    StringUtils.UTF8 = CharacterSetECI.UTF8.getName(); // \"UTF8\"\n    StringUtils.PLATFORM_DEFAULT_ENCODING = StringUtils.UTF8; // \"UTF8\"//Charset.defaultCharset().name()\n    StringUtils.ASSUME_SHIFT_JIS = false;\n    return StringUtils;\n}());\nexport default StringUtils;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,wBAAwB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG,CACvB;EACA;EACA;EACAA,WAAW,CAACC,iBAAiB,GAAG,UAAUC,IAAI,EAAEC,QAAQ,EAAE;IACtD,IAAIA,QAAQ,KAAK,KAAK,CAAC,EAAE;MAAEA,QAAQ,GAAG,IAAI;IAAE;IAC5C;IACA;IACA,IAAIC,CAAC,GAAGD,QAAQ,GAAGA,QAAQ,CAACE,OAAO,CAAC,CAAC,GAAG,IAAI,CAACC,QAAQ;IACrD;IACA,OAAOP,cAAc,CAACQ,MAAM,CAAC,IAAIC,UAAU,CAAC,CAACN,IAAI,CAAC,CAAC,EAAEE,CAAC,CAAC;EAC3D,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIJ,WAAW,CAACS,aAAa,GAAG,UAAUC,KAAK,EAAEC,KAAK,EAAE;IAChD,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAIA,SAAS,KAAKD,KAAK,CAACE,GAAG,CAAChB,cAAc,CAACiB,aAAa,CAAC,EAAE;MAChG,OAAOH,KAAK,CAACE,GAAG,CAAChB,cAAc,CAACiB,aAAa,CAAC,CAACC,QAAQ,CAAC,CAAC;IAC7D;IACA;IACA;IACA,IAAIC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACzB,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,aAAa,GAAG,IAAI;IACxB,IAAIC,SAAS,GAAG,IAAI;IACpB,IAAIC,aAAa,GAAG,CAAC;IACrB;IACA,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIC,cAAc,GAAG,CAAC;IACtB,IAAIC,aAAa,GAAG,CAAC;IACrB;IACA,IAAIC,iBAAiB,GAAG,CAAC;IACzB;IACA,IAAIC,yBAAyB,GAAG,CAAC;IACjC,IAAIC,4BAA4B,GAAG,CAAC;IACpC,IAAIC,yBAAyB,GAAG,CAAC;IACjC,IAAIC,4BAA4B,GAAG,CAAC;IACpC;IACA;IACA,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,OAAO,GAAGrB,KAAK,CAACM,MAAM,GAAG,CAAC,IAC1BN,KAAK,CAAC,CAAC,CAAC,KAAK,WAAY,IAAI,IAC7BA,KAAK,CAAC,CAAC,CAAC,KAAK,WAAY,IAAI,IAC7BA,KAAK,CAAC,CAAC,CAAC,KAAK,WAAY,IAAI;IACjC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGhB,MAAM,KAAKC,aAAa,IAAIC,aAAa,IAAIC,SAAS,CAAC,EAAEa,CAAC,EAAE,EAAE;MAC9E,IAAIC,KAAK,GAAGvB,KAAK,CAACsB,CAAC,CAAC,GAAG,IAAI;MAC3B;MACA,IAAIb,SAAS,EAAE;QACX,IAAIC,aAAa,GAAG,CAAC,EAAE;UACnB,IAAI,CAACa,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;YACtBd,SAAS,GAAG,KAAK;UACrB,CAAC,MACI;YACDC,aAAa,EAAE;UACnB;QACJ,CAAC,MACI,IAAI,CAACa,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;UAC3B,IAAI,CAACA,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;YACtBd,SAAS,GAAG,KAAK;UACrB,CAAC,MACI;YACDC,aAAa,EAAE;YACf,IAAI,CAACa,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;cACtBZ,cAAc,EAAE;YACpB,CAAC,MACI;cACDD,aAAa,EAAE;cACf,IAAI,CAACa,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;gBACtBX,cAAc,EAAE;cACpB,CAAC,MACI;gBACDF,aAAa,EAAE;gBACf,IAAI,CAACa,KAAK,GAAG,IAAI,MAAM,CAAC,EAAE;kBACtBV,cAAc,EAAE;gBACpB,CAAC,MACI;kBACDJ,SAAS,GAAG,KAAK;gBACrB;cACJ;YACJ;UACJ;QACJ,CAAC,CAAC;QACF;QACA;MACJ;MACA;MACA,IAAIF,aAAa,EAAE;QACf,IAAIgB,KAAK,GAAG,IAAI,IAAIA,KAAK,GAAG,IAAI,EAAE;UAC9BhB,aAAa,GAAG,KAAK;QACzB,CAAC,MACI,IAAIgB,KAAK,GAAG,IAAI,EAAE;UACnB,IAAIA,KAAK,GAAG,IAAI,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,EAAE;YAClDH,YAAY,EAAE;UAClB,CAAC,CAAC;UACF;UACA;QACJ,CAAC,CAAC;QACF;QACA;MACJ;MACA;MACA,IAAIZ,aAAa,EAAE;QACf,IAAIM,aAAa,GAAG,CAAC,EAAE;UACnB,IAAIS,KAAK,GAAG,IAAI,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,IAAI,EAAE;YAChDf,aAAa,GAAG,KAAK;UACzB,CAAC,MACI;YACDM,aAAa,EAAE;UACnB;QACJ,CAAC,MACI,IAAIS,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,IAAI,EAAE;UACvDf,aAAa,GAAG,KAAK;QACzB,CAAC,MACI,IAAIe,KAAK,GAAG,IAAI,IAAIA,KAAK,GAAG,IAAI,EAAE;UACnCR,iBAAiB,EAAE;UACnBE,4BAA4B,GAAG,CAAC;UAChCD,yBAAyB,EAAE;UAC3B,IAAIA,yBAAyB,GAAGE,yBAAyB,EAAE;YACvDA,yBAAyB,GAAGF,yBAAyB;UACzD;QACJ,CAAC,MACI,IAAIO,KAAK,GAAG,IAAI,EAAE;UACnBT,aAAa,EAAE;UACf;UACAE,yBAAyB,GAAG,CAAC;UAC7BC,4BAA4B,EAAE;UAC9B,IAAIA,4BAA4B,GAAGE,4BAA4B,EAAE;YAC7DA,4BAA4B,GAAGF,4BAA4B;UAC/D;QACJ,CAAC,MACI;UACD;UACAD,yBAAyB,GAAG,CAAC;UAC7BC,4BAA4B,GAAG,CAAC;QACpC;MACJ;IACJ;IACA,IAAIR,SAAS,IAAIC,aAAa,GAAG,CAAC,EAAE;MAChCD,SAAS,GAAG,KAAK;IACrB;IACA,IAAID,aAAa,IAAIM,aAAa,GAAG,CAAC,EAAE;MACpCN,aAAa,GAAG,KAAK;IACzB;IACA;IACA,IAAIC,SAAS,KAAKY,OAAO,IAAIV,cAAc,GAAGC,cAAc,GAAGC,cAAc,GAAG,CAAC,CAAC,EAAE;MAChF,OAAOvB,WAAW,CAACkC,IAAI;IAC3B;IACA;IACA,IAAIhB,aAAa,KAAKlB,WAAW,CAACmC,gBAAgB,IAAIP,yBAAyB,IAAI,CAAC,IAAIC,4BAA4B,IAAI,CAAC,CAAC,EAAE;MACxH,OAAO7B,WAAW,CAACoC,SAAS;IAChC;IACA;IACA;IACA;IACA;IACA;IACA,IAAInB,aAAa,IAAIC,aAAa,EAAE;MAChC,OAAQU,yBAAyB,KAAK,CAAC,IAAIH,iBAAiB,KAAK,CAAC,IAAKK,YAAY,GAAG,EAAE,IAAId,MAAM,GAC5FhB,WAAW,CAACoC,SAAS,GAAGpC,WAAW,CAACM,QAAQ;IACtD;IACA;IACA,IAAIW,aAAa,EAAE;MACf,OAAOjB,WAAW,CAACM,QAAQ;IAC/B;IACA,IAAIY,aAAa,EAAE;MACf,OAAOlB,WAAW,CAACoC,SAAS;IAChC;IACA,IAAIjB,SAAS,EAAE;MACX,OAAOnB,WAAW,CAACkC,IAAI;IAC3B;IACA;IACA,OAAOlC,WAAW,CAACqC,yBAAyB;EAChD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;EACIrC,WAAW,CAACsC,MAAM,GAAG,UAAUC,MAAM,EAAE;IACnC,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAAC1B,MAAM,EAAEyB,EAAE,EAAE,EAAE;MAC1CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAChC;IACA,IAAIT,CAAC,GAAG,CAAC,CAAC;IACV,SAASW,QAAQA,CAACC,GAAG,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;MACvC,IAAIL,GAAG,KAAK,IAAI,EACZ,OAAO,GAAG;MACd,IAAIJ,IAAI,CAAC,EAAER,CAAC,CAAC,KAAKpB,SAAS,EACvB,OAAOA,SAAS;MACpBgC,GAAG,GAAGG,EAAE,GAAGG,QAAQ,CAACH,EAAE,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGvC,SAAS;MAC7C,IAAIwC,IAAI,GAAGJ,EAAE,GAAGE,QAAQ,CAACF,EAAE,CAACG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGvC,SAAS;MAClD,IAAIyC,GAAG;MACP,QAAQJ,EAAE;QACN,KAAK,GAAG;UACJI,GAAG,GAAGb,IAAI,CAACR,CAAC,CAAC;UACb;QACJ,KAAK,GAAG;UACJqB,GAAG,GAAGb,IAAI,CAACR,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB;QACJ,KAAK,GAAG;UACJqB,GAAG,GAAGC,UAAU,CAACd,IAAI,CAACR,CAAC,CAAC,CAAC,CAACuB,OAAO,CAACX,GAAG,CAAC;UACtC;QACJ,KAAK,GAAG;UACJS,GAAG,GAAGC,UAAU,CAACd,IAAI,CAACR,CAAC,CAAC,CAAC,CAACwB,WAAW,CAACZ,GAAG,CAAC;UAC1C;QACJ,KAAK,GAAG;UACJS,GAAG,GAAGC,UAAU,CAACd,IAAI,CAACR,CAAC,CAAC,CAAC,CAACyB,aAAa,CAACb,GAAG,CAAC;UAC5C;QACJ,KAAK,GAAG;UACJS,GAAG,GAAGH,QAAQ,CAACV,IAAI,CAACR,CAAC,CAAC,CAAC,CAACjB,QAAQ,CAACqC,IAAI,GAAGA,IAAI,GAAG,EAAE,CAAC;UAClD;QACJ,KAAK,GAAG;UACJC,GAAG,GAAGC,UAAU,CAACJ,QAAQ,CAACV,IAAI,CAACR,CAAC,CAAC,EAAEoB,IAAI,GAAGA,IAAI,GAAG,EAAE,CAAC,CAACI,WAAW,CAACZ,GAAG,CAAC,CAAC,CAACW,OAAO,CAAC,CAAC,CAAC;UACjF;MACR;MACAF,GAAG,GAAG,OAAOA,GAAG,KAAK,QAAQ,GAAGK,IAAI,CAACC,SAAS,CAACN,GAAG,CAAC,GAAG,CAAC,CAACA,GAAG,EAAEtC,QAAQ,CAACqC,IAAI,CAAC;MAC3E,IAAIQ,IAAI,GAAGV,QAAQ,CAACJ,EAAE,CAAC,CAAC,CAAC;MACzB,IAAIe,EAAE,GAAGf,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,KAAM,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;MACjD,OAAOO,GAAG,CAACrC,MAAM,GAAG4C,IAAI,EACpBP,GAAG,GAAGR,EAAE,KAAKjC,SAAS,GAAGyC,GAAG,GAAGQ,EAAE,GAAGA,EAAE,GAAGR,GAAG,CAAC,CAAC;MAClD,OAAOA,GAAG;IACd;IACA,IAAIS,KAAK,GAAG,uDAAuD;IACnE,OAAOvB,MAAM,CAACwB,OAAO,CAACD,KAAK,EAAEnB,QAAQ,CAAC;EAC1C,CAAC;EACD;AACJ;AACA;EACI3C,WAAW,CAACgE,QAAQ,GAAG,UAAUC,GAAG,EAAE9D,QAAQ,EAAE;IAC5C,OAAOJ,cAAc,CAACmE,MAAM,CAACD,GAAG,EAAE9D,QAAQ,CAAC;EAC/C,CAAC;EACD;AACJ;AACA;EACIH,WAAW,CAACmE,WAAW,GAAG,UAAUF,GAAG,EAAEG,KAAK,EAAE;IAC5C,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MAAEA,KAAK,GAAG,CAAC;IAAE;IACnC,OAAOH,GAAG,CAACI,UAAU,CAACD,KAAK,CAAC;EAChC,CAAC;EACD;AACJ;AACA;EACIpE,WAAW,CAACsE,SAAS,GAAG,UAAUC,QAAQ,EAAE;IACxC,OAAOC,MAAM,CAACC,YAAY,CAACF,QAAQ,CAAC;EACxC,CAAC;EACDvE,WAAW,CAACoC,SAAS,GAAGtC,eAAe,CAAC4E,IAAI,CAACrE,OAAO,CAAC,CAAC,CAAC,CAAC;EACxDL,WAAW,CAAC2E,MAAM,GAAG,QAAQ;EAC7B3E,WAAW,CAACM,QAAQ,GAAGR,eAAe,CAAC8E,SAAS,CAACvE,OAAO,CAAC,CAAC,CAAC,CAAC;EAC5DL,WAAW,CAAC6E,MAAM,GAAG,QAAQ;EAC7B7E,WAAW,CAACkC,IAAI,GAAGpC,eAAe,CAACoC,IAAI,CAAC7B,OAAO,CAAC,CAAC,CAAC,CAAC;EACnDL,WAAW,CAACqC,yBAAyB,GAAGrC,WAAW,CAACkC,IAAI,CAAC,CAAC;EAC1DlC,WAAW,CAACmC,gBAAgB,GAAG,KAAK;EACpC,OAAOnC,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}