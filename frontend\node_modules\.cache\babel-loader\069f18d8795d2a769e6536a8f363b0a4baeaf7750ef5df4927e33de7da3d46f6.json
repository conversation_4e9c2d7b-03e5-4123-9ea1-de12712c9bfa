{"ast": null, "code": "import IndexOutOfBoundsException from '../IndexOutOfBoundsException';\nimport NullPointerException from '../NullPointerException';\n/*\n * Copyright (c) 1994, 2004, Oracle and/or its affiliates. All rights reserved.\n * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.\n *\n * This code is free software; you can redistribute it and/or modify it\n * under the terms of the GNU General Public License version 2 only, as\n * published by the Free Software Foundation.  Oracle designates this\n * particular file as subject to the \"Classpath\" exception as provided\n * by <PERSON> in the LICENSE file that accompanied this code.\n *\n * This code is distributed in the hope that it will be useful, but WITHOUT\n * ANY WARRANTY; without even the implied warranty of ME<PERSON>HANT<PERSON>ILITY or\n * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License\n * version 2 for more details (a copy is included in the LICENSE file that\n * accompanied this code).\n *\n * You should have received a copy of the GNU General Public License version\n * 2 along with this work; if not, write to the Free Software Foundation,\n * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.\n *\n * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA\n * or visit www.oracle.com if you need additional information or have any\n * questions.\n */\n// package java.io;\n/**\n * This abstract class is the superclass of all classes representing\n * an output stream of bytes. An output stream accepts output bytes\n * and sends them to some sink.\n * <p>\n * Applications that need to define a subclass of\n * <code>OutputStream</code> must always provide at least a method\n * that writes one byte of output.\n *\n * <AUTHOR> van Hoff\n * @see     java.io.BufferedOutputStream\n * @see     java.io.ByteArrayOutputStream\n * @see     java.io.DataOutputStream\n * @see     java.io.FilterOutputStream\n * @see     java.io.InputStream\n * @see     java.io.OutputStream#write(int)\n * @since   JDK1.0\n */\nvar OutputStream /*implements Closeable, Flushable*/ = /** @class */function () {\n  function OutputStream() {}\n  /**\n   * Writes <code>b.length</code> bytes from the specified byte array\n   * to this output stream. The general contract for <code>write(b)</code>\n   * is that it should have exactly the same effect as the call\n   * <code>write(b, 0, b.length)</code>.\n   *\n   * @param      b   the data.\n   * @exception  IOException  if an I/O error occurs.\n   * @see        java.io.OutputStream#write(byte[], int, int)\n   */\n  OutputStream.prototype.writeBytes = function (b) {\n    this.writeBytesOffset(b, 0, b.length);\n  };\n  /**\n   * Writes <code>len</code> bytes from the specified byte array\n   * starting at offset <code>off</code> to this output stream.\n   * The general contract for <code>write(b, off, len)</code> is that\n   * some of the bytes in the array <code>b</code> are written to the\n   * output stream in order; element <code>b[off]</code> is the first\n   * byte written and <code>b[off+len-1]</code> is the last byte written\n   * by this operation.\n   * <p>\n   * The <code>write</code> method of <code>OutputStream</code> calls\n   * the write method of one argument on each of the bytes to be\n   * written out. Subclasses are encouraged to override this method and\n   * provide a more efficient implementation.\n   * <p>\n   * If <code>b</code> is <code>null</code>, a\n   * <code>NullPointerException</code> is thrown.\n   * <p>\n   * If <code>off</code> is negative, or <code>len</code> is negative, or\n   * <code>off+len</code> is greater than the length of the array\n   * <code>b</code>, then an <tt>IndexOutOfBoundsException</tt> is thrown.\n   *\n   * @param      b     the data.\n   * @param      off   the start offset in the data.\n   * @param      len   the number of bytes to write.\n   * @exception  IOException  if an I/O error occurs. In particular,\n   *             an <code>IOException</code> is thrown if the output\n   *             stream is closed.\n   */\n  OutputStream.prototype.writeBytesOffset = function (b, off, len) {\n    if (b == null) {\n      throw new NullPointerException();\n    } else if (off < 0 || off > b.length || len < 0 || off + len > b.length || off + len < 0) {\n      throw new IndexOutOfBoundsException();\n    } else if (len === 0) {\n      return;\n    }\n    for (var i = 0; i < len; i++) {\n      this.write(b[off + i]);\n    }\n  };\n  /**\n   * Flushes this output stream and forces any buffered output bytes\n   * to be written out. The general contract of <code>flush</code> is\n   * that calling it is an indication that, if any bytes previously\n   * written have been buffered by the implementation of the output\n   * stream, such bytes should immediately be written to their\n   * intended destination.\n   * <p>\n   * If the intended destination of this stream is an abstraction provided by\n   * the underlying operating system, for example a file, then flushing the\n   * stream guarantees only that bytes previously written to the stream are\n   * passed to the operating system for writing; it does not guarantee that\n   * they are actually written to a physical device such as a disk drive.\n   * <p>\n   * The <code>flush</code> method of <code>OutputStream</code> does nothing.\n   *\n   * @exception  IOException  if an I/O error occurs.\n   */\n  OutputStream.prototype.flush = function () {};\n  /**\n   * Closes this output stream and releases any system resources\n   * associated with this stream. The general contract of <code>close</code>\n   * is that it closes the output stream. A closed stream cannot perform\n   * output operations and cannot be reopened.\n   * <p>\n   * The <code>close</code> method of <code>OutputStream</code> does nothing.\n   *\n   * @exception  IOException  if an I/O error occurs.\n   */\n  OutputStream.prototype.close = function () {};\n  return OutputStream;\n}();\nexport default OutputStream;", "map": {"version": 3, "names": ["IndexOutOfBoundsException", "NullPointerException", "OutputStream", "prototype", "writeBytes", "b", "writeBytesOffset", "length", "off", "len", "i", "write", "flush", "close"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/OutputStream.js"], "sourcesContent": ["import IndexOutOfBoundsException from '../IndexOutOfBoundsException';\nimport NullPointerException from '../NullPointerException';\n/*\n * Copyright (c) 1994, 2004, Oracle and/or its affiliates. All rights reserved.\n * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.\n *\n * This code is free software; you can redistribute it and/or modify it\n * under the terms of the GNU General Public License version 2 only, as\n * published by the Free Software Foundation.  Oracle designates this\n * particular file as subject to the \"Classpath\" exception as provided\n * by <PERSON> in the LICENSE file that accompanied this code.\n *\n * This code is distributed in the hope that it will be useful, but WITHOUT\n * ANY WARRANTY; without even the implied warranty of ME<PERSON>HANT<PERSON>ILITY or\n * FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License\n * version 2 for more details (a copy is included in the LICENSE file that\n * accompanied this code).\n *\n * You should have received a copy of the GNU General Public License version\n * 2 along with this work; if not, write to the Free Software Foundation,\n * Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.\n *\n * Please contact Oracle, 500 Oracle Parkway, Redwood Shores, CA 94065 USA\n * or visit www.oracle.com if you need additional information or have any\n * questions.\n */\n// package java.io;\n/**\n * This abstract class is the superclass of all classes representing\n * an output stream of bytes. An output stream accepts output bytes\n * and sends them to some sink.\n * <p>\n * Applications that need to define a subclass of\n * <code>OutputStream</code> must always provide at least a method\n * that writes one byte of output.\n *\n * <AUTHOR> van Hoff\n * @see     java.io.BufferedOutputStream\n * @see     java.io.ByteArrayOutputStream\n * @see     java.io.DataOutputStream\n * @see     java.io.FilterOutputStream\n * @see     java.io.InputStream\n * @see     java.io.OutputStream#write(int)\n * @since   JDK1.0\n */\nvar OutputStream /*implements Closeable, Flushable*/ = /** @class */ (function () {\n    function OutputStream() {\n    }\n    /**\n     * Writes <code>b.length</code> bytes from the specified byte array\n     * to this output stream. The general contract for <code>write(b)</code>\n     * is that it should have exactly the same effect as the call\n     * <code>write(b, 0, b.length)</code>.\n     *\n     * @param      b   the data.\n     * @exception  IOException  if an I/O error occurs.\n     * @see        java.io.OutputStream#write(byte[], int, int)\n     */\n    OutputStream.prototype.writeBytes = function (b) {\n        this.writeBytesOffset(b, 0, b.length);\n    };\n    /**\n     * Writes <code>len</code> bytes from the specified byte array\n     * starting at offset <code>off</code> to this output stream.\n     * The general contract for <code>write(b, off, len)</code> is that\n     * some of the bytes in the array <code>b</code> are written to the\n     * output stream in order; element <code>b[off]</code> is the first\n     * byte written and <code>b[off+len-1]</code> is the last byte written\n     * by this operation.\n     * <p>\n     * The <code>write</code> method of <code>OutputStream</code> calls\n     * the write method of one argument on each of the bytes to be\n     * written out. Subclasses are encouraged to override this method and\n     * provide a more efficient implementation.\n     * <p>\n     * If <code>b</code> is <code>null</code>, a\n     * <code>NullPointerException</code> is thrown.\n     * <p>\n     * If <code>off</code> is negative, or <code>len</code> is negative, or\n     * <code>off+len</code> is greater than the length of the array\n     * <code>b</code>, then an <tt>IndexOutOfBoundsException</tt> is thrown.\n     *\n     * @param      b     the data.\n     * @param      off   the start offset in the data.\n     * @param      len   the number of bytes to write.\n     * @exception  IOException  if an I/O error occurs. In particular,\n     *             an <code>IOException</code> is thrown if the output\n     *             stream is closed.\n     */\n    OutputStream.prototype.writeBytesOffset = function (b, off, len) {\n        if (b == null) {\n            throw new NullPointerException();\n        }\n        else if ((off < 0) || (off > b.length) || (len < 0) ||\n            ((off + len) > b.length) || ((off + len) < 0)) {\n            throw new IndexOutOfBoundsException();\n        }\n        else if (len === 0) {\n            return;\n        }\n        for (var i = 0; i < len; i++) {\n            this.write(b[off + i]);\n        }\n    };\n    /**\n     * Flushes this output stream and forces any buffered output bytes\n     * to be written out. The general contract of <code>flush</code> is\n     * that calling it is an indication that, if any bytes previously\n     * written have been buffered by the implementation of the output\n     * stream, such bytes should immediately be written to their\n     * intended destination.\n     * <p>\n     * If the intended destination of this stream is an abstraction provided by\n     * the underlying operating system, for example a file, then flushing the\n     * stream guarantees only that bytes previously written to the stream are\n     * passed to the operating system for writing; it does not guarantee that\n     * they are actually written to a physical device such as a disk drive.\n     * <p>\n     * The <code>flush</code> method of <code>OutputStream</code> does nothing.\n     *\n     * @exception  IOException  if an I/O error occurs.\n     */\n    OutputStream.prototype.flush = function () {\n    };\n    /**\n     * Closes this output stream and releases any system resources\n     * associated with this stream. The general contract of <code>close</code>\n     * is that it closes the output stream. A closed stream cannot perform\n     * output operations and cannot be reopened.\n     * <p>\n     * The <code>close</code> method of <code>OutputStream</code> does nothing.\n     *\n     * @exception  IOException  if an I/O error occurs.\n     */\n    OutputStream.prototype.close = function () {\n    };\n    return OutputStream;\n}());\nexport default OutputStream;\n"], "mappings": "AAAA,OAAOA,yBAAyB,MAAM,8BAA8B;AACpE,OAAOC,oBAAoB,MAAM,yBAAyB;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,CAAC,sCAAsC,aAAe,YAAY;EAC9E,SAASA,YAAYA,CAAA,EAAG,CACxB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,YAAY,CAACC,SAAS,CAACC,UAAU,GAAG,UAAUC,CAAC,EAAE;IAC7C,IAAI,CAACC,gBAAgB,CAACD,CAAC,EAAE,CAAC,EAAEA,CAAC,CAACE,MAAM,CAAC;EACzC,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIL,YAAY,CAACC,SAAS,CAACG,gBAAgB,GAAG,UAAUD,CAAC,EAAEG,GAAG,EAAEC,GAAG,EAAE;IAC7D,IAAIJ,CAAC,IAAI,IAAI,EAAE;MACX,MAAM,IAAIJ,oBAAoB,CAAC,CAAC;IACpC,CAAC,MACI,IAAKO,GAAG,GAAG,CAAC,IAAMA,GAAG,GAAGH,CAAC,CAACE,MAAO,IAAKE,GAAG,GAAG,CAAE,IAC7CD,GAAG,GAAGC,GAAG,GAAIJ,CAAC,CAACE,MAAO,IAAMC,GAAG,GAAGC,GAAG,GAAI,CAAE,EAAE;MAC/C,MAAM,IAAIT,yBAAyB,CAAC,CAAC;IACzC,CAAC,MACI,IAAIS,GAAG,KAAK,CAAC,EAAE;MAChB;IACJ;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,EAAEC,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACC,KAAK,CAACN,CAAC,CAACG,GAAG,GAAGE,CAAC,CAAC,CAAC;IAC1B;EACJ,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIR,YAAY,CAACC,SAAS,CAACS,KAAK,GAAG,YAAY,CAC3C,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIV,YAAY,CAACC,SAAS,CAACU,KAAK,GAAG,YAAY,CAC3C,CAAC;EACD,OAAOX,YAAY;AACvB,CAAC,CAAC,CAAE;AACJ,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}