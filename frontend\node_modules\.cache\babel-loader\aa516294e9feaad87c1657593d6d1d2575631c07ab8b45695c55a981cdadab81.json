{"ast": null, "code": "/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.encoder {*/\nvar BlockPair = /** @class */function () {\n  function BlockPair(dataBytes, errorCorrectionBytes) {\n    this.dataBytes = dataBytes;\n    this.errorCorrectionBytes = errorCorrectionBytes;\n  }\n  BlockPair.prototype.getDataBytes = function () {\n    return this.dataBytes;\n  };\n  BlockPair.prototype.getErrorCorrectionBytes = function () {\n    return this.errorCorrectionBytes;\n  };\n  return BlockPair;\n}();\nexport default BlockPair;", "map": {"version": 3, "names": ["BlockPair", "dataBytes", "errorCorrectionBytes", "prototype", "getDataBytes", "getErrorCorrectionBytes"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/encoder/BlockPair.js"], "sourcesContent": ["/*\n * Copyright 2008 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing.qrcode.encoder {*/\nvar BlockPair = /** @class */ (function () {\n    function BlockPair(dataBytes, errorCorrectionBytes) {\n        this.dataBytes = dataBytes;\n        this.errorCorrectionBytes = errorCorrectionBytes;\n    }\n    BlockPair.prototype.getDataBytes = function () {\n        return this.dataBytes;\n    };\n    BlockPair.prototype.getErrorCorrectionBytes = function () {\n        return this.errorCorrectionBytes;\n    };\n    return BlockPair;\n}());\nexport default BlockPair;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAACC,SAAS,EAAEC,oBAAoB,EAAE;IAChD,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,oBAAoB,GAAGA,oBAAoB;EACpD;EACAF,SAAS,CAACG,SAAS,CAACC,YAAY,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACH,SAAS;EACzB,CAAC;EACDD,SAAS,CAACG,SAAS,CAACE,uBAAuB,GAAG,YAAY;IACtD,OAAO,IAAI,CAACH,oBAAoB;EACpC,CAAC;EACD,OAAOF,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}