{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport System from './util/System';\n/**\n * <p>Encapsulates the result of decoding a barcode within an image.</p>\n *\n * <AUTHOR>\n */\nvar Result = /** @class */function () {\n  // public constructor(private text: string,\n  //               Uint8Array rawBytes,\n  //               ResultPoconst resultPoints: Int32Array,\n  //               BarcodeFormat format) {\n  //   this(text, rawBytes, resultPoints, format, System.currentTimeMillis())\n  // }\n  // public constructor(text: string,\n  //               Uint8Array rawBytes,\n  //               ResultPoconst resultPoints: Int32Array,\n  //               BarcodeFormat format,\n  //               long timestamp) {\n  //   this(text, rawBytes, rawBytes == null ? 0 : 8 * rawBytes.length,\n  //        resultPoints, format, timestamp)\n  // }\n  function Result(text, rawBytes, numBits, resultPoints, format, timestamp) {\n    if (numBits === void 0) {\n      numBits = rawBytes == null ? 0 : 8 * rawBytes.length;\n    }\n    if (timestamp === void 0) {\n      timestamp = System.currentTimeMillis();\n    }\n    this.text = text;\n    this.rawBytes = rawBytes;\n    this.numBits = numBits;\n    this.resultPoints = resultPoints;\n    this.format = format;\n    this.timestamp = timestamp;\n    this.text = text;\n    this.rawBytes = rawBytes;\n    if (undefined === numBits || null === numBits) {\n      this.numBits = rawBytes === null || rawBytes === undefined ? 0 : 8 * rawBytes.length;\n    } else {\n      this.numBits = numBits;\n    }\n    this.resultPoints = resultPoints;\n    this.format = format;\n    this.resultMetadata = null;\n    if (undefined === timestamp || null === timestamp) {\n      this.timestamp = System.currentTimeMillis();\n    } else {\n      this.timestamp = timestamp;\n    }\n  }\n  /**\n   * @return raw text encoded by the barcode\n   */\n  Result.prototype.getText = function () {\n    return this.text;\n  };\n  /**\n   * @return raw bytes encoded by the barcode, if applicable, otherwise {@code null}\n   */\n  Result.prototype.getRawBytes = function () {\n    return this.rawBytes;\n  };\n  /**\n   * @return how many bits of {@link #getRawBytes()} are valid; typically 8 times its length\n   * @since 3.3.0\n   */\n  Result.prototype.getNumBits = function () {\n    return this.numBits;\n  };\n  /**\n   * @return points related to the barcode in the image. These are typically points\n   *         identifying finder patterns or the corners of the barcode. The exact meaning is\n   *         specific to the type of barcode that was decoded.\n   */\n  Result.prototype.getResultPoints = function () {\n    return this.resultPoints;\n  };\n  /**\n   * @return {@link BarcodeFormat} representing the format of the barcode that was decoded\n   */\n  Result.prototype.getBarcodeFormat = function () {\n    return this.format;\n  };\n  /**\n   * @return {@link Map} mapping {@link ResultMetadataType} keys to values. May be\n   *   {@code null}. This contains optional metadata about what was detected about the barcode,\n   *   like orientation.\n   */\n  Result.prototype.getResultMetadata = function () {\n    return this.resultMetadata;\n  };\n  Result.prototype.putMetadata = function (type, value) {\n    if (this.resultMetadata === null) {\n      this.resultMetadata = new Map();\n    }\n    this.resultMetadata.set(type, value);\n  };\n  Result.prototype.putAllMetadata = function (metadata) {\n    if (metadata !== null) {\n      if (this.resultMetadata === null) {\n        this.resultMetadata = metadata;\n      } else {\n        this.resultMetadata = new Map(metadata);\n      }\n    }\n  };\n  Result.prototype.addResultPoints = function (newPoints) {\n    var oldPoints = this.resultPoints;\n    if (oldPoints === null) {\n      this.resultPoints = newPoints;\n    } else if (newPoints !== null && newPoints.length > 0) {\n      var allPoints = new Array(oldPoints.length + newPoints.length);\n      System.arraycopy(oldPoints, 0, allPoints, 0, oldPoints.length);\n      System.arraycopy(newPoints, 0, allPoints, oldPoints.length, newPoints.length);\n      this.resultPoints = allPoints;\n    }\n  };\n  Result.prototype.getTimestamp = function () {\n    return this.timestamp;\n  };\n  /*@Override*/\n  Result.prototype.toString = function () {\n    return this.text;\n  };\n  return Result;\n}();\nexport default Result;", "map": {"version": 3, "names": ["System", "Result", "text", "rawBytes", "numBits", "resultPoints", "format", "timestamp", "length", "currentTimeMillis", "undefined", "resultMetadata", "prototype", "getText", "getRawBytes", "getNumBits", "getResultPoints", "getBarcodeFormat", "getResultMetadata", "putMetadata", "type", "value", "Map", "set", "putAllMetadata", "metadata", "addResultPoints", "newPoints", "oldPoints", "allPoints", "Array", "arraycopy", "getTimestamp", "toString"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/Result.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport System from './util/System';\n/**\n * <p>Encapsulates the result of decoding a barcode within an image.</p>\n *\n * <AUTHOR>\n */\nvar Result = /** @class */ (function () {\n    // public constructor(private text: string,\n    //               Uint8Array rawBytes,\n    //               ResultPoconst resultPoints: Int32Array,\n    //               BarcodeFormat format) {\n    //   this(text, rawBytes, resultPoints, format, System.currentTimeMillis())\n    // }\n    // public constructor(text: string,\n    //               Uint8Array rawBytes,\n    //               ResultPoconst resultPoints: Int32Array,\n    //               BarcodeFormat format,\n    //               long timestamp) {\n    //   this(text, rawBytes, rawBytes == null ? 0 : 8 * rawBytes.length,\n    //        resultPoints, format, timestamp)\n    // }\n    function Result(text, rawBytes, numBits, resultPoints, format, timestamp) {\n        if (numBits === void 0) { numBits = rawBytes == null ? 0 : 8 * rawBytes.length; }\n        if (timestamp === void 0) { timestamp = System.currentTimeMillis(); }\n        this.text = text;\n        this.rawBytes = rawBytes;\n        this.numBits = numBits;\n        this.resultPoints = resultPoints;\n        this.format = format;\n        this.timestamp = timestamp;\n        this.text = text;\n        this.rawBytes = rawBytes;\n        if (undefined === numBits || null === numBits) {\n            this.numBits = (rawBytes === null || rawBytes === undefined) ? 0 : 8 * rawBytes.length;\n        }\n        else {\n            this.numBits = numBits;\n        }\n        this.resultPoints = resultPoints;\n        this.format = format;\n        this.resultMetadata = null;\n        if (undefined === timestamp || null === timestamp) {\n            this.timestamp = System.currentTimeMillis();\n        }\n        else {\n            this.timestamp = timestamp;\n        }\n    }\n    /**\n     * @return raw text encoded by the barcode\n     */\n    Result.prototype.getText = function () {\n        return this.text;\n    };\n    /**\n     * @return raw bytes encoded by the barcode, if applicable, otherwise {@code null}\n     */\n    Result.prototype.getRawBytes = function () {\n        return this.rawBytes;\n    };\n    /**\n     * @return how many bits of {@link #getRawBytes()} are valid; typically 8 times its length\n     * @since 3.3.0\n     */\n    Result.prototype.getNumBits = function () {\n        return this.numBits;\n    };\n    /**\n     * @return points related to the barcode in the image. These are typically points\n     *         identifying finder patterns or the corners of the barcode. The exact meaning is\n     *         specific to the type of barcode that was decoded.\n     */\n    Result.prototype.getResultPoints = function () {\n        return this.resultPoints;\n    };\n    /**\n     * @return {@link BarcodeFormat} representing the format of the barcode that was decoded\n     */\n    Result.prototype.getBarcodeFormat = function () {\n        return this.format;\n    };\n    /**\n     * @return {@link Map} mapping {@link ResultMetadataType} keys to values. May be\n     *   {@code null}. This contains optional metadata about what was detected about the barcode,\n     *   like orientation.\n     */\n    Result.prototype.getResultMetadata = function () {\n        return this.resultMetadata;\n    };\n    Result.prototype.putMetadata = function (type, value) {\n        if (this.resultMetadata === null) {\n            this.resultMetadata = new Map();\n        }\n        this.resultMetadata.set(type, value);\n    };\n    Result.prototype.putAllMetadata = function (metadata) {\n        if (metadata !== null) {\n            if (this.resultMetadata === null) {\n                this.resultMetadata = metadata;\n            }\n            else {\n                this.resultMetadata = new Map(metadata);\n            }\n        }\n    };\n    Result.prototype.addResultPoints = function (newPoints) {\n        var oldPoints = this.resultPoints;\n        if (oldPoints === null) {\n            this.resultPoints = newPoints;\n        }\n        else if (newPoints !== null && newPoints.length > 0) {\n            var allPoints = new Array(oldPoints.length + newPoints.length);\n            System.arraycopy(oldPoints, 0, allPoints, 0, oldPoints.length);\n            System.arraycopy(newPoints, 0, allPoints, oldPoints.length, newPoints.length);\n            this.resultPoints = allPoints;\n        }\n    };\n    Result.prototype.getTimestamp = function () {\n        return this.timestamp;\n    };\n    /*@Override*/\n    Result.prototype.toString = function () {\n        return this.text;\n    };\n    return Result;\n}());\nexport default Result;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,MAAM,MAAM,eAAe;AAClC;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG,aAAe,YAAY;EACpC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASA,MAAMA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAE;IACtE,IAAIH,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAGD,QAAQ,IAAI,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ,CAACK,MAAM;IAAE;IAChF,IAAID,SAAS,KAAK,KAAK,CAAC,EAAE;MAAEA,SAAS,GAAGP,MAAM,CAACS,iBAAiB,CAAC,CAAC;IAAE;IACpE,IAAI,CAACP,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACL,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAIO,SAAS,KAAKN,OAAO,IAAI,IAAI,KAAKA,OAAO,EAAE;MAC3C,IAAI,CAACA,OAAO,GAAID,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAKO,SAAS,GAAI,CAAC,GAAG,CAAC,GAAGP,QAAQ,CAACK,MAAM;IAC1F,CAAC,MACI;MACD,IAAI,CAACJ,OAAO,GAAGA,OAAO;IAC1B;IACA,IAAI,CAACC,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACK,cAAc,GAAG,IAAI;IAC1B,IAAID,SAAS,KAAKH,SAAS,IAAI,IAAI,KAAKA,SAAS,EAAE;MAC/C,IAAI,CAACA,SAAS,GAAGP,MAAM,CAACS,iBAAiB,CAAC,CAAC;IAC/C,CAAC,MACI;MACD,IAAI,CAACF,SAAS,GAAGA,SAAS;IAC9B;EACJ;EACA;AACJ;AACA;EACIN,MAAM,CAACW,SAAS,CAACC,OAAO,GAAG,YAAY;IACnC,OAAO,IAAI,CAACX,IAAI;EACpB,CAAC;EACD;AACJ;AACA;EACID,MAAM,CAACW,SAAS,CAACE,WAAW,GAAG,YAAY;IACvC,OAAO,IAAI,CAACX,QAAQ;EACxB,CAAC;EACD;AACJ;AACA;AACA;EACIF,MAAM,CAACW,SAAS,CAACG,UAAU,GAAG,YAAY;IACtC,OAAO,IAAI,CAACX,OAAO;EACvB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIH,MAAM,CAACW,SAAS,CAACI,eAAe,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACX,YAAY;EAC5B,CAAC;EACD;AACJ;AACA;EACIJ,MAAM,CAACW,SAAS,CAACK,gBAAgB,GAAG,YAAY;IAC5C,OAAO,IAAI,CAACX,MAAM;EACtB,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIL,MAAM,CAACW,SAAS,CAACM,iBAAiB,GAAG,YAAY;IAC7C,OAAO,IAAI,CAACP,cAAc;EAC9B,CAAC;EACDV,MAAM,CAACW,SAAS,CAACO,WAAW,GAAG,UAAUC,IAAI,EAAEC,KAAK,EAAE;IAClD,IAAI,IAAI,CAACV,cAAc,KAAK,IAAI,EAAE;MAC9B,IAAI,CAACA,cAAc,GAAG,IAAIW,GAAG,CAAC,CAAC;IACnC;IACA,IAAI,CAACX,cAAc,CAACY,GAAG,CAACH,IAAI,EAAEC,KAAK,CAAC;EACxC,CAAC;EACDpB,MAAM,CAACW,SAAS,CAACY,cAAc,GAAG,UAAUC,QAAQ,EAAE;IAClD,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACnB,IAAI,IAAI,CAACd,cAAc,KAAK,IAAI,EAAE;QAC9B,IAAI,CAACA,cAAc,GAAGc,QAAQ;MAClC,CAAC,MACI;QACD,IAAI,CAACd,cAAc,GAAG,IAAIW,GAAG,CAACG,QAAQ,CAAC;MAC3C;IACJ;EACJ,CAAC;EACDxB,MAAM,CAACW,SAAS,CAACc,eAAe,GAAG,UAAUC,SAAS,EAAE;IACpD,IAAIC,SAAS,GAAG,IAAI,CAACvB,YAAY;IACjC,IAAIuB,SAAS,KAAK,IAAI,EAAE;MACpB,IAAI,CAACvB,YAAY,GAAGsB,SAAS;IACjC,CAAC,MACI,IAAIA,SAAS,KAAK,IAAI,IAAIA,SAAS,CAACnB,MAAM,GAAG,CAAC,EAAE;MACjD,IAAIqB,SAAS,GAAG,IAAIC,KAAK,CAACF,SAAS,CAACpB,MAAM,GAAGmB,SAAS,CAACnB,MAAM,CAAC;MAC9DR,MAAM,CAAC+B,SAAS,CAACH,SAAS,EAAE,CAAC,EAAEC,SAAS,EAAE,CAAC,EAAED,SAAS,CAACpB,MAAM,CAAC;MAC9DR,MAAM,CAAC+B,SAAS,CAACJ,SAAS,EAAE,CAAC,EAAEE,SAAS,EAAED,SAAS,CAACpB,MAAM,EAAEmB,SAAS,CAACnB,MAAM,CAAC;MAC7E,IAAI,CAACH,YAAY,GAAGwB,SAAS;IACjC;EACJ,CAAC;EACD5B,MAAM,CAACW,SAAS,CAACoB,YAAY,GAAG,YAAY;IACxC,OAAO,IAAI,CAACzB,SAAS;EACzB,CAAC;EACD;EACAN,MAAM,CAACW,SAAS,CAACqB,QAAQ,GAAG,YAAY;IACpC,OAAO,IAAI,CAAC/B,IAAI;EACpB,CAAC;EACD,OAAOD,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}