{"ast": null, "code": "var Collections = /** @class */function () {\n  function Collections() {}\n  /**\n   * The singletonList(T) method is used to return an immutable list containing only the specified object.\n   */\n  Collections.singletonList = function (item) {\n    return [item];\n  };\n  /**\n   * The min(Collection<? extends T>, Comparator<? super T>) method is used to return the minimum element of the given collection, according to the order induced by the specified comparator.\n   */\n  Collections.min = function (collection, comparator) {\n    return collection.sort(comparator)[0];\n  };\n  return Collections;\n}();\nexport default Collections;", "map": {"version": 3, "names": ["Collections", "singletonList", "item", "min", "collection", "comparator", "sort"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/Collections.js"], "sourcesContent": ["var Collections = /** @class */ (function () {\n    function Collections() {\n    }\n    /**\n     * The singletonList(T) method is used to return an immutable list containing only the specified object.\n     */\n    Collections.singletonList = function (item) {\n        return [item];\n    };\n    /**\n     * The min(Collection<? extends T>, Comparator<? super T>) method is used to return the minimum element of the given collection, according to the order induced by the specified comparator.\n     */\n    Collections.min = function (collection, comparator) {\n        return collection.sort(comparator)[0];\n    };\n    return Collections;\n}());\nexport default Collections;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAG,aAAe,YAAY;EACzC,SAASA,WAAWA,CAAA,EAAG,CACvB;EACA;AACJ;AACA;EACIA,WAAW,CAACC,aAAa,GAAG,UAAUC,IAAI,EAAE;IACxC,OAAO,CAACA,IAAI,CAAC;EACjB,CAAC;EACD;AACJ;AACA;EACIF,WAAW,CAACG,GAAG,GAAG,UAAUC,UAAU,EAAEC,UAAU,EAAE;IAChD,OAAOD,UAAU,CAACE,IAAI,CAACD,UAAU,CAAC,CAAC,CAAC,CAAC;EACzC,CAAC;EACD,OAAOL,WAAW;AACtB,CAAC,CAAC,CAAE;AACJ,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}