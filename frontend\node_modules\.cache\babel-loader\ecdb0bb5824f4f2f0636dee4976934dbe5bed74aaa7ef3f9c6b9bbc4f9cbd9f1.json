{"ast": null, "code": "var BlockParsedResult = /** @class */function () {\n  function BlockParsedResult(finished, decodedInformation) {\n    if (decodedInformation) {\n      this.decodedInformation = null;\n    } else {\n      this.finished = finished;\n      this.decodedInformation = decodedInformation;\n    }\n  }\n  BlockParsedResult.prototype.getDecodedInformation = function () {\n    return this.decodedInformation;\n  };\n  BlockParsedResult.prototype.isFinished = function () {\n    return this.finished;\n  };\n  return BlockParsedResult;\n}();\nexport default BlockParsedResult;", "map": {"version": 3, "names": ["BlockParsedResult", "finished", "decodedInformation", "prototype", "getDecodedInformation", "isFinished"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/expanded/decoders/BlockParsedResult.js"], "sourcesContent": ["var BlockParsedResult = /** @class */ (function () {\n    function BlockParsedResult(finished, decodedInformation) {\n        if (decodedInformation) {\n            this.decodedInformation = null;\n        }\n        else {\n            this.finished = finished;\n            this.decodedInformation = decodedInformation;\n        }\n    }\n    BlockParsedResult.prototype.getDecodedInformation = function () {\n        return this.decodedInformation;\n    };\n    BlockParsedResult.prototype.isFinished = function () {\n        return this.finished;\n    };\n    return BlockParsedResult;\n}());\nexport default BlockParsedResult;\n"], "mappings": "AAAA,IAAIA,iBAAiB,GAAG,aAAe,YAAY;EAC/C,SAASA,iBAAiBA,CAACC,QAAQ,EAAEC,kBAAkB,EAAE;IACrD,IAAIA,kBAAkB,EAAE;MACpB,IAAI,CAACA,kBAAkB,GAAG,IAAI;IAClC,CAAC,MACI;MACD,IAAI,CAACD,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAChD;EACJ;EACAF,iBAAiB,CAACG,SAAS,CAACC,qBAAqB,GAAG,YAAY;IAC5D,OAAO,IAAI,CAACF,kBAAkB;EAClC,CAAC;EACDF,iBAAiB,CAACG,SAAS,CAACE,UAAU,GAAG,YAAY;IACjD,OAAO,IAAI,CAACJ,QAAQ;EACxB,CAAC;EACD,OAAOD,iBAAiB;AAC5B,CAAC,CAAC,CAAE;AACJ,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}