{"ast": null, "code": "import StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { BASE256_ENCODATION, ASCII_ENCODATION } from './constants';\nvar Base256Encoder = /** @class */function () {\n  function Base256Encoder() {}\n  Base256Encoder.prototype.getEncodingMode = function () {\n    return BASE256_ENCODATION;\n  };\n  Base256Encoder.prototype.encode = function (context) {\n    var buffer = new StringBuilder();\n    buffer.append(0); // Initialize length field\n    while (context.hasMoreCharacters()) {\n      var c = context.getCurrentChar();\n      buffer.append(c);\n      context.pos++;\n      var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n      if (newMode !== this.getEncodingMode()) {\n        // Return to ASCII encodation, which will actually handle latch to new mode\n        context.signalEncoderChange(ASCII_ENCODATION);\n        break;\n      }\n    }\n    var dataCount = buffer.length() - 1;\n    var lengthFieldSize = 1;\n    var currentSize = context.getCodewordCount() + dataCount + lengthFieldSize;\n    context.updateSymbolInfo(currentSize);\n    var mustPad = context.getSymbolInfo().getDataCapacity() - currentSize > 0;\n    if (context.hasMoreCharacters() || mustPad) {\n      if (dataCount <= 249) {\n        buffer.setCharAt(0, StringUtils.getCharAt(dataCount));\n      } else if (dataCount <= 1555) {\n        buffer.setCharAt(0, StringUtils.getCharAt(Math.floor(dataCount / 250) + 249));\n        buffer.insert(1, StringUtils.getCharAt(dataCount % 250));\n      } else {\n        throw new Error('Message length not in valid ranges: ' + dataCount);\n      }\n    }\n    for (var i = 0, c = buffer.length(); i < c; i++) {\n      context.writeCodeword(this.randomize255State(buffer.charAt(i).charCodeAt(0), context.getCodewordCount() + 1));\n    }\n  };\n  Base256Encoder.prototype.randomize255State = function (ch, codewordPosition) {\n    var pseudoRandom = 149 * codewordPosition % 255 + 1;\n    var tempVariable = ch + pseudoRandom;\n    if (tempVariable <= 255) {\n      return tempVariable;\n    } else {\n      return tempVariable - 256;\n    }\n  };\n  return Base256Encoder;\n}();\nexport { Base256Encoder };", "map": {"version": 3, "names": ["StringUtils", "StringBuilder", "HighLevelEncoder", "BASE256_ENCODATION", "ASCII_ENCODATION", "Base256Encoder", "prototype", "getEncodingMode", "encode", "context", "buffer", "append", "hasMoreCharacters", "c", "getCurrentChar", "pos", "newMode", "lookAheadTest", "getMessage", "signalEncoderChange", "dataCount", "length", "lengthFieldSize", "currentSize", "getCodewordCount", "updateSymbolInfo", "mustPad", "getSymbolInfo", "getDataCapacity", "setCharAt", "getCharAt", "Math", "floor", "insert", "Error", "i", "writeCodeword", "randomize255State", "char<PERSON>t", "charCodeAt", "ch", "codewordPosition", "pseudoRandom", "tempVariable"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/datamatrix/encoder/Base256Encoder.js"], "sourcesContent": ["import StringUtils from '../../common/StringUtils';\nimport StringBuilder from '../../util/StringBuilder';\nimport HighLevelEncoder from './HighLevelEncoder';\nimport { BASE256_ENCODATION, ASCII_ENCODATION } from './constants';\nvar Base256Encoder = /** @class */ (function () {\n    function Base256Encoder() {\n    }\n    Base256Encoder.prototype.getEncodingMode = function () {\n        return BASE256_ENCODATION;\n    };\n    Base256Encoder.prototype.encode = function (context) {\n        var buffer = new StringBuilder();\n        buffer.append(0); // Initialize length field\n        while (context.hasMoreCharacters()) {\n            var c = context.getCurrentChar();\n            buffer.append(c);\n            context.pos++;\n            var newMode = HighLevelEncoder.lookAheadTest(context.getMessage(), context.pos, this.getEncodingMode());\n            if (newMode !== this.getEncodingMode()) {\n                // Return to ASCII encodation, which will actually handle latch to new mode\n                context.signalEncoderChange(ASCII_ENCODATION);\n                break;\n            }\n        }\n        var dataCount = buffer.length() - 1;\n        var lengthFieldSize = 1;\n        var currentSize = context.getCodewordCount() + dataCount + lengthFieldSize;\n        context.updateSymbolInfo(currentSize);\n        var mustPad = context.getSymbolInfo().getDataCapacity() - currentSize > 0;\n        if (context.hasMoreCharacters() || mustPad) {\n            if (dataCount <= 249) {\n                buffer.setCharAt(0, StringUtils.getCharAt(dataCount));\n            }\n            else if (dataCount <= 1555) {\n                buffer.setCharAt(0, StringUtils.getCharAt(Math.floor(dataCount / 250) + 249));\n                buffer.insert(1, StringUtils.getCharAt(dataCount % 250));\n            }\n            else {\n                throw new Error('Message length not in valid ranges: ' + dataCount);\n            }\n        }\n        for (var i = 0, c = buffer.length(); i < c; i++) {\n            context.writeCodeword(this.randomize255State(buffer.charAt(i).charCodeAt(0), context.getCodewordCount() + 1));\n        }\n    };\n    Base256Encoder.prototype.randomize255State = function (ch, codewordPosition) {\n        var pseudoRandom = ((149 * codewordPosition) % 255) + 1;\n        var tempVariable = ch + pseudoRandom;\n        if (tempVariable <= 255) {\n            return tempVariable;\n        }\n        else {\n            return tempVariable - 256;\n        }\n    };\n    return Base256Encoder;\n}());\nexport { Base256Encoder };\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,0BAA0B;AAClD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,aAAa;AAClE,IAAIC,cAAc,GAAG,aAAe,YAAY;EAC5C,SAASA,cAAcA,CAAA,EAAG,CAC1B;EACAA,cAAc,CAACC,SAAS,CAACC,eAAe,GAAG,YAAY;IACnD,OAAOJ,kBAAkB;EAC7B,CAAC;EACDE,cAAc,CAACC,SAAS,CAACE,MAAM,GAAG,UAAUC,OAAO,EAAE;IACjD,IAAIC,MAAM,GAAG,IAAIT,aAAa,CAAC,CAAC;IAChCS,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAClB,OAAOF,OAAO,CAACG,iBAAiB,CAAC,CAAC,EAAE;MAChC,IAAIC,CAAC,GAAGJ,OAAO,CAACK,cAAc,CAAC,CAAC;MAChCJ,MAAM,CAACC,MAAM,CAACE,CAAC,CAAC;MAChBJ,OAAO,CAACM,GAAG,EAAE;MACb,IAAIC,OAAO,GAAGd,gBAAgB,CAACe,aAAa,CAACR,OAAO,CAACS,UAAU,CAAC,CAAC,EAAET,OAAO,CAACM,GAAG,EAAE,IAAI,CAACR,eAAe,CAAC,CAAC,CAAC;MACvG,IAAIS,OAAO,KAAK,IAAI,CAACT,eAAe,CAAC,CAAC,EAAE;QACpC;QACAE,OAAO,CAACU,mBAAmB,CAACf,gBAAgB,CAAC;QAC7C;MACJ;IACJ;IACA,IAAIgB,SAAS,GAAGV,MAAM,CAACW,MAAM,CAAC,CAAC,GAAG,CAAC;IACnC,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIC,WAAW,GAAGd,OAAO,CAACe,gBAAgB,CAAC,CAAC,GAAGJ,SAAS,GAAGE,eAAe;IAC1Eb,OAAO,CAACgB,gBAAgB,CAACF,WAAW,CAAC;IACrC,IAAIG,OAAO,GAAGjB,OAAO,CAACkB,aAAa,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,GAAGL,WAAW,GAAG,CAAC;IACzE,IAAId,OAAO,CAACG,iBAAiB,CAAC,CAAC,IAAIc,OAAO,EAAE;MACxC,IAAIN,SAAS,IAAI,GAAG,EAAE;QAClBV,MAAM,CAACmB,SAAS,CAAC,CAAC,EAAE7B,WAAW,CAAC8B,SAAS,CAACV,SAAS,CAAC,CAAC;MACzD,CAAC,MACI,IAAIA,SAAS,IAAI,IAAI,EAAE;QACxBV,MAAM,CAACmB,SAAS,CAAC,CAAC,EAAE7B,WAAW,CAAC8B,SAAS,CAACC,IAAI,CAACC,KAAK,CAACZ,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;QAC7EV,MAAM,CAACuB,MAAM,CAAC,CAAC,EAAEjC,WAAW,CAAC8B,SAAS,CAACV,SAAS,GAAG,GAAG,CAAC,CAAC;MAC5D,CAAC,MACI;QACD,MAAM,IAAIc,KAAK,CAAC,sCAAsC,GAAGd,SAAS,CAAC;MACvE;IACJ;IACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEtB,CAAC,GAAGH,MAAM,CAACW,MAAM,CAAC,CAAC,EAAEc,CAAC,GAAGtB,CAAC,EAAEsB,CAAC,EAAE,EAAE;MAC7C1B,OAAO,CAAC2B,aAAa,CAAC,IAAI,CAACC,iBAAiB,CAAC3B,MAAM,CAAC4B,MAAM,CAACH,CAAC,CAAC,CAACI,UAAU,CAAC,CAAC,CAAC,EAAE9B,OAAO,CAACe,gBAAgB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACjH;EACJ,CAAC;EACDnB,cAAc,CAACC,SAAS,CAAC+B,iBAAiB,GAAG,UAAUG,EAAE,EAAEC,gBAAgB,EAAE;IACzE,IAAIC,YAAY,GAAK,GAAG,GAAGD,gBAAgB,GAAI,GAAG,GAAI,CAAC;IACvD,IAAIE,YAAY,GAAGH,EAAE,GAAGE,YAAY;IACpC,IAAIC,YAAY,IAAI,GAAG,EAAE;MACrB,OAAOA,YAAY;IACvB,CAAC,MACI;MACD,OAAOA,YAAY,GAAG,GAAG;IAC7B;EACJ,CAAC;EACD,OAAOtC,cAAc;AACzB,CAAC,CAAC,CAAE;AACJ,SAASA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}