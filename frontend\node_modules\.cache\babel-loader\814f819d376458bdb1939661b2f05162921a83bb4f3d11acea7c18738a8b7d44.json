{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport { CustomError } from 'ts-custom-error';\n/**\n * Custom Error class of type Exception.\n */\nvar Exception = /** @class */function (_super) {\n  __extends(Exception, _super);\n  /**\n   * Allows Exception to be constructed directly\n   * with some message and prototype definition.\n   */\n  function Exception(message) {\n    if (message === void 0) {\n      message = undefined;\n    }\n    var _this = _super.call(this, message) || this;\n    _this.message = message;\n    return _this;\n  }\n  Exception.prototype.getKind = function () {\n    var ex = this.constructor;\n    return ex.kind;\n  };\n  /**\n   * It's typed as string so it can be extended and overriden.\n   */\n  Exception.kind = 'Exception';\n  return Exception;\n}(CustomError);\nexport default Exception;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "CustomError", "Exception", "_super", "message", "undefined", "_this", "call", "<PERSON><PERSON><PERSON>", "ex", "kind"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/Exception.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport { CustomError } from 'ts-custom-error';\n/**\n * Custom Error class of type Exception.\n */\nvar Exception = /** @class */ (function (_super) {\n    __extends(Exception, _super);\n    /**\n     * Allows Exception to be constructed directly\n     * with some message and prototype definition.\n     */\n    function Exception(message) {\n        if (message === void 0) { message = undefined; }\n        var _this = _super.call(this, message) || this;\n        _this.message = message;\n        return _this;\n    }\n    Exception.prototype.getKind = function () {\n        var ex = this.constructor;\n        return ex.kind;\n    };\n    /**\n     * It's typed as string so it can be extended and overriden.\n     */\n    Exception.kind = 'Exception';\n    return Exception;\n}(CustomError));\nexport default Exception;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,SAASI,WAAW,QAAQ,iBAAiB;AAC7C;AACA;AACA;AACA,IAAIC,SAAS,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC7ChB,SAAS,CAACe,SAAS,EAAEC,MAAM,CAAC;EAC5B;AACJ;AACA;AACA;EACI,SAASD,SAASA,CAACE,OAAO,EAAE;IACxB,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MAAEA,OAAO,GAAGC,SAAS;IAAE;IAC/C,IAAIC,KAAK,GAAGH,MAAM,CAACI,IAAI,CAAC,IAAI,EAAEH,OAAO,CAAC,IAAI,IAAI;IAC9CE,KAAK,CAACF,OAAO,GAAGA,OAAO;IACvB,OAAOE,KAAK;EAChB;EACAJ,SAAS,CAACH,SAAS,CAACS,OAAO,GAAG,YAAY;IACtC,IAAIC,EAAE,GAAG,IAAI,CAACX,WAAW;IACzB,OAAOW,EAAE,CAACC,IAAI;EAClB,CAAC;EACD;AACJ;AACA;EACIR,SAAS,CAACQ,IAAI,GAAG,WAAW;EAC5B,OAAOR,SAAS;AACpB,CAAC,CAACD,WAAW,CAAE;AACf,eAAeC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}