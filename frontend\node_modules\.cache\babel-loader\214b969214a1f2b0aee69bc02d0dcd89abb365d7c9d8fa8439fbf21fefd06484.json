{"ast": null, "code": "/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n/**\n * Aztec 2D code representation\n *\n * <AUTHOR>\n */\nvar AztecCode = /** @class */function () {\n  function AztecCode() {}\n  /**\n   * @return {@code true} if compact instead of full mode\n   */\n  AztecCode.prototype.isCompact = function () {\n    return this.compact;\n  };\n  AztecCode.prototype.setCompact = function (compact) {\n    this.compact = compact;\n  };\n  /**\n   * @return size in pixels (width and height)\n   */\n  AztecCode.prototype.getSize = function () {\n    return this.size;\n  };\n  AztecCode.prototype.setSize = function (size) {\n    this.size = size;\n  };\n  /**\n   * @return number of levels\n   */\n  AztecCode.prototype.getLayers = function () {\n    return this.layers;\n  };\n  AztecCode.prototype.setLayers = function (layers) {\n    this.layers = layers;\n  };\n  /**\n   * @return number of data codewords\n   */\n  AztecCode.prototype.getCodeWords = function () {\n    return this.codeWords;\n  };\n  AztecCode.prototype.setCodeWords = function (codeWords) {\n    this.codeWords = codeWords;\n  };\n  /**\n   * @return the symbol image\n   */\n  AztecCode.prototype.getMatrix = function () {\n    return this.matrix;\n  };\n  AztecCode.prototype.setMatrix = function (matrix) {\n    this.matrix = matrix;\n  };\n  return AztecCode;\n}();\nexport default AztecCode;", "map": {"version": 3, "names": ["AztecCode", "prototype", "isCompact", "compact", "setCompact", "getSize", "size", "setSize", "getLayers", "layers", "setLayers", "getCodeWords", "codeWords", "setCodeWords", "getMatrix", "matrix", "setMatrix"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/aztec/encoder/AztecCode.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n/**\n * Aztec 2D code representation\n *\n * <AUTHOR>\n */\nvar AztecCode = /** @class */ (function () {\n    function AztecCode() {\n    }\n    /**\n     * @return {@code true} if compact instead of full mode\n     */\n    AztecCode.prototype.isCompact = function () {\n        return this.compact;\n    };\n    AztecCode.prototype.setCompact = function (compact) {\n        this.compact = compact;\n    };\n    /**\n     * @return size in pixels (width and height)\n     */\n    AztecCode.prototype.getSize = function () {\n        return this.size;\n    };\n    AztecCode.prototype.setSize = function (size) {\n        this.size = size;\n    };\n    /**\n     * @return number of levels\n     */\n    AztecCode.prototype.getLayers = function () {\n        return this.layers;\n    };\n    AztecCode.prototype.setLayers = function (layers) {\n        this.layers = layers;\n    };\n    /**\n     * @return number of data codewords\n     */\n    AztecCode.prototype.getCodeWords = function () {\n        return this.codeWords;\n    };\n    AztecCode.prototype.setCodeWords = function (codeWords) {\n        this.codeWords = codeWords;\n    };\n    /**\n     * @return the symbol image\n     */\n    AztecCode.prototype.getMatrix = function () {\n        return this.matrix;\n    };\n    AztecCode.prototype.setMatrix = function (matrix) {\n        this.matrix = matrix;\n    };\n    return AztecCode;\n}());\nexport default AztecCode;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,SAAS,GAAG,aAAe,YAAY;EACvC,SAASA,SAASA,CAAA,EAAG,CACrB;EACA;AACJ;AACA;EACIA,SAAS,CAACC,SAAS,CAACC,SAAS,GAAG,YAAY;IACxC,OAAO,IAAI,CAACC,OAAO;EACvB,CAAC;EACDH,SAAS,CAACC,SAAS,CAACG,UAAU,GAAG,UAAUD,OAAO,EAAE;IAChD,IAAI,CAACA,OAAO,GAAGA,OAAO;EAC1B,CAAC;EACD;AACJ;AACA;EACIH,SAAS,CAACC,SAAS,CAACI,OAAO,GAAG,YAAY;IACtC,OAAO,IAAI,CAACC,IAAI;EACpB,CAAC;EACDN,SAAS,CAACC,SAAS,CAACM,OAAO,GAAG,UAAUD,IAAI,EAAE;IAC1C,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB,CAAC;EACD;AACJ;AACA;EACIN,SAAS,CAACC,SAAS,CAACO,SAAS,GAAG,YAAY;IACxC,OAAO,IAAI,CAACC,MAAM;EACtB,CAAC;EACDT,SAAS,CAACC,SAAS,CAACS,SAAS,GAAG,UAAUD,MAAM,EAAE;IAC9C,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB,CAAC;EACD;AACJ;AACA;EACIT,SAAS,CAACC,SAAS,CAACU,YAAY,GAAG,YAAY;IAC3C,OAAO,IAAI,CAACC,SAAS;EACzB,CAAC;EACDZ,SAAS,CAACC,SAAS,CAACY,YAAY,GAAG,UAAUD,SAAS,EAAE;IACpD,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B,CAAC;EACD;AACJ;AACA;EACIZ,SAAS,CAACC,SAAS,CAACa,SAAS,GAAG,YAAY;IACxC,OAAO,IAAI,CAACC,MAAM;EACtB,CAAC;EACDf,SAAS,CAACC,SAAS,CAACe,SAAS,GAAG,UAAUD,MAAM,EAAE;IAC9C,IAAI,CAACA,MAAM,GAAGA,MAAM;EACxB,CAAC;EACD,OAAOf,SAAS;AACpB,CAAC,CAAC,CAAE;AACJ,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}