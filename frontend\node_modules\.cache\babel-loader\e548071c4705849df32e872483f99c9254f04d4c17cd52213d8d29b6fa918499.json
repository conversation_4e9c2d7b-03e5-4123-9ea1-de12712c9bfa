{"ast": null, "code": "/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.common.detector.MathUtils;\nimport MathUtils from '../../common/detector/MathUtils';\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport Float from '../../util/Float';\n/**\n * <AUTHOR>\n * <AUTHOR> GmbH (<EMAIL>)\n */\nvar PDF417CodewordDecoder = /** @class */function () {\n  function PDF417CodewordDecoder() {}\n  /* @note\n   * this action have to be performed before first use of class\n   * - static constructor\n   * working with 32bit float (based from Java logic)\n  */\n  PDF417CodewordDecoder.initialize = function () {\n    // Pre-computes the symbol ratio table.\n    for (/*int*/var i = 0; i < PDF417Common.SYMBOL_TABLE.length; i++) {\n      var currentSymbol = PDF417Common.SYMBOL_TABLE[i];\n      var currentBit = currentSymbol & 0x1;\n      for (/*int*/var j = 0; j < PDF417Common.BARS_IN_MODULE; j++) {\n        var size = 0.0;\n        while ((currentSymbol & 0x1) === currentBit) {\n          size += 1.0;\n          currentSymbol >>= 1;\n        }\n        currentBit = currentSymbol & 0x1;\n        if (!PDF417CodewordDecoder.RATIOS_TABLE[i]) {\n          PDF417CodewordDecoder.RATIOS_TABLE[i] = new Array(PDF417Common.BARS_IN_MODULE);\n        }\n        PDF417CodewordDecoder.RATIOS_TABLE[i][PDF417Common.BARS_IN_MODULE - j - 1] = Math.fround(size / PDF417Common.MODULES_IN_CODEWORD);\n      }\n    }\n    this.bSymbolTableReady = true;\n  };\n  PDF417CodewordDecoder.getDecodedValue = function (moduleBitCount) {\n    var decodedValue = PDF417CodewordDecoder.getDecodedCodewordValue(PDF417CodewordDecoder.sampleBitCounts(moduleBitCount));\n    if (decodedValue !== -1) {\n      return decodedValue;\n    }\n    return PDF417CodewordDecoder.getClosestDecodedValue(moduleBitCount);\n  };\n  PDF417CodewordDecoder.sampleBitCounts = function (moduleBitCount) {\n    var bitCountSum = MathUtils.sum(moduleBitCount);\n    var result = new Int32Array(PDF417Common.BARS_IN_MODULE);\n    var bitCountIndex = 0;\n    var sumPreviousBits = 0;\n    for (/*int*/var i = 0; i < PDF417Common.MODULES_IN_CODEWORD; i++) {\n      var sampleIndex = bitCountSum / (2 * PDF417Common.MODULES_IN_CODEWORD) + i * bitCountSum / PDF417Common.MODULES_IN_CODEWORD;\n      if (sumPreviousBits + moduleBitCount[bitCountIndex] <= sampleIndex) {\n        sumPreviousBits += moduleBitCount[bitCountIndex];\n        bitCountIndex++;\n      }\n      result[bitCountIndex]++;\n    }\n    return result;\n  };\n  PDF417CodewordDecoder.getDecodedCodewordValue = function (moduleBitCount) {\n    var decodedValue = PDF417CodewordDecoder.getBitValue(moduleBitCount);\n    return PDF417Common.getCodeword(decodedValue) === -1 ? -1 : decodedValue;\n  };\n  PDF417CodewordDecoder.getBitValue = function (moduleBitCount) {\n    var result = /*long*/0;\n    for (var /*int*/i = 0; i < moduleBitCount.length; i++) {\n      for (/*int*/var bit = 0; bit < moduleBitCount[i]; bit++) {\n        result = result << 1 | (i % 2 === 0 ? 1 : 0);\n      }\n    }\n    return Math.trunc(result);\n  };\n  // working with 32bit float (as in Java)\n  PDF417CodewordDecoder.getClosestDecodedValue = function (moduleBitCount) {\n    var bitCountSum = MathUtils.sum(moduleBitCount);\n    var bitCountRatios = new Array(PDF417Common.BARS_IN_MODULE);\n    if (bitCountSum > 1) {\n      for (var /*int*/i = 0; i < bitCountRatios.length; i++) {\n        bitCountRatios[i] = Math.fround(moduleBitCount[i] / bitCountSum);\n      }\n    }\n    var bestMatchError = Float.MAX_VALUE;\n    var bestMatch = -1;\n    if (!this.bSymbolTableReady) {\n      PDF417CodewordDecoder.initialize();\n    }\n    for (/*int*/var j = 0; j < PDF417CodewordDecoder.RATIOS_TABLE.length; j++) {\n      var error = 0.0;\n      var ratioTableRow = PDF417CodewordDecoder.RATIOS_TABLE[j];\n      for (/*int*/var k = 0; k < PDF417Common.BARS_IN_MODULE; k++) {\n        var diff = Math.fround(ratioTableRow[k] - bitCountRatios[k]);\n        error += Math.fround(diff * diff);\n        if (error >= bestMatchError) {\n          break;\n        }\n      }\n      if (error < bestMatchError) {\n        bestMatchError = error;\n        bestMatch = PDF417Common.SYMBOL_TABLE[j];\n      }\n    }\n    return bestMatch;\n  };\n  // flag that the table is ready for use\n  PDF417CodewordDecoder.bSymbolTableReady = false;\n  PDF417CodewordDecoder.RATIOS_TABLE = new Array(PDF417Common.SYMBOL_TABLE.length).map(function (x) {\n    return x = new Array(PDF417Common.BARS_IN_MODULE);\n  });\n  return PDF417CodewordDecoder;\n}();\nexport default PDF417CodewordDecoder;", "map": {"version": 3, "names": ["MathUtils", "PDF417<PERSON><PERSON><PERSON>", "Float", "PDF417CodewordDecoder", "initialize", "i", "SYMBOL_TABLE", "length", "currentSymbol", "currentBit", "j", "BARS_IN_MODULE", "size", "RATIOS_TABLE", "Array", "Math", "fround", "MODULES_IN_CODEWORD", "bSymbolTableReady", "getDecodedValue", "moduleBitCount", "decodedValue", "getDecodedCodewordValue", "sampleBitCounts", "getClosestDecodedValue", "bitCountSum", "sum", "result", "Int32Array", "bitCountIndex", "sumPreviousBits", "sampleIndex", "getBitValue", "getCodeword", "bit", "trunc", "bitCountRatios", "bestMatchError", "MAX_VALUE", "bestMatch", "error", "ratioTableRow", "k", "diff", "map", "x"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/pdf417/decoder/PDF417CodewordDecoder.js"], "sourcesContent": ["/*\n* Copyright 2013 ZXing authors\n*\n* Licensed under the Apache License, Version 2.0 (the \"License\");\n* you may not use this file except in compliance with the License.\n* You may obtain a copy of the License at\n*\n*      http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing, software\n* distributed under the License is distributed on an \"AS IS\" BASIS,\n* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n* See the License for the specific language governing permissions and\n* limitations under the License.\n*/\n// package com.google.zxing.pdf417.decoder;\n// import com.google.zxing.common.detector.MathUtils;\nimport MathUtils from '../../common/detector/MathUtils';\n// import com.google.zxing.pdf417.PDF417Common;\nimport PDF417Common from '../PDF417Common';\nimport Float from '../../util/Float';\n/**\n * <AUTHOR>\n * <AUTHOR> GmbH (<EMAIL>)\n */\nvar PDF417CodewordDecoder = /** @class */ (function () {\n    function PDF417CodewordDecoder() {\n    }\n    /* @note\n     * this action have to be performed before first use of class\n     * - static constructor\n     * working with 32bit float (based from Java logic)\n    */\n    PDF417CodewordDecoder.initialize = function () {\n        // Pre-computes the symbol ratio table.\n        for ( /*int*/var i = 0; i < PDF417Common.SYMBOL_TABLE.length; i++) {\n            var currentSymbol = PDF417Common.SYMBOL_TABLE[i];\n            var currentBit = currentSymbol & 0x1;\n            for ( /*int*/var j = 0; j < PDF417Common.BARS_IN_MODULE; j++) {\n                var size = 0.0;\n                while ((currentSymbol & 0x1) === currentBit) {\n                    size += 1.0;\n                    currentSymbol >>= 1;\n                }\n                currentBit = currentSymbol & 0x1;\n                if (!PDF417CodewordDecoder.RATIOS_TABLE[i]) {\n                    PDF417CodewordDecoder.RATIOS_TABLE[i] = new Array(PDF417Common.BARS_IN_MODULE);\n                }\n                PDF417CodewordDecoder.RATIOS_TABLE[i][PDF417Common.BARS_IN_MODULE - j - 1] = Math.fround(size / PDF417Common.MODULES_IN_CODEWORD);\n            }\n        }\n        this.bSymbolTableReady = true;\n    };\n    PDF417CodewordDecoder.getDecodedValue = function (moduleBitCount) {\n        var decodedValue = PDF417CodewordDecoder.getDecodedCodewordValue(PDF417CodewordDecoder.sampleBitCounts(moduleBitCount));\n        if (decodedValue !== -1) {\n            return decodedValue;\n        }\n        return PDF417CodewordDecoder.getClosestDecodedValue(moduleBitCount);\n    };\n    PDF417CodewordDecoder.sampleBitCounts = function (moduleBitCount) {\n        var bitCountSum = MathUtils.sum(moduleBitCount);\n        var result = new Int32Array(PDF417Common.BARS_IN_MODULE);\n        var bitCountIndex = 0;\n        var sumPreviousBits = 0;\n        for ( /*int*/var i = 0; i < PDF417Common.MODULES_IN_CODEWORD; i++) {\n            var sampleIndex = bitCountSum / (2 * PDF417Common.MODULES_IN_CODEWORD) +\n                (i * bitCountSum) / PDF417Common.MODULES_IN_CODEWORD;\n            if (sumPreviousBits + moduleBitCount[bitCountIndex] <= sampleIndex) {\n                sumPreviousBits += moduleBitCount[bitCountIndex];\n                bitCountIndex++;\n            }\n            result[bitCountIndex]++;\n        }\n        return result;\n    };\n    PDF417CodewordDecoder.getDecodedCodewordValue = function (moduleBitCount) {\n        var decodedValue = PDF417CodewordDecoder.getBitValue(moduleBitCount);\n        return PDF417Common.getCodeword(decodedValue) === -1 ? -1 : decodedValue;\n    };\n    PDF417CodewordDecoder.getBitValue = function (moduleBitCount) {\n        var result = /*long*/ 0;\n        for (var /*int*/ i = 0; i < moduleBitCount.length; i++) {\n            for ( /*int*/var bit = 0; bit < moduleBitCount[i]; bit++) {\n                result = (result << 1) | (i % 2 === 0 ? 1 : 0);\n            }\n        }\n        return Math.trunc(result);\n    };\n    // working with 32bit float (as in Java)\n    PDF417CodewordDecoder.getClosestDecodedValue = function (moduleBitCount) {\n        var bitCountSum = MathUtils.sum(moduleBitCount);\n        var bitCountRatios = new Array(PDF417Common.BARS_IN_MODULE);\n        if (bitCountSum > 1) {\n            for (var /*int*/ i = 0; i < bitCountRatios.length; i++) {\n                bitCountRatios[i] = Math.fround(moduleBitCount[i] / bitCountSum);\n            }\n        }\n        var bestMatchError = Float.MAX_VALUE;\n        var bestMatch = -1;\n        if (!this.bSymbolTableReady) {\n            PDF417CodewordDecoder.initialize();\n        }\n        for ( /*int*/var j = 0; j < PDF417CodewordDecoder.RATIOS_TABLE.length; j++) {\n            var error = 0.0;\n            var ratioTableRow = PDF417CodewordDecoder.RATIOS_TABLE[j];\n            for ( /*int*/var k = 0; k < PDF417Common.BARS_IN_MODULE; k++) {\n                var diff = Math.fround(ratioTableRow[k] - bitCountRatios[k]);\n                error += Math.fround(diff * diff);\n                if (error >= bestMatchError) {\n                    break;\n                }\n            }\n            if (error < bestMatchError) {\n                bestMatchError = error;\n                bestMatch = PDF417Common.SYMBOL_TABLE[j];\n            }\n        }\n        return bestMatch;\n    };\n    // flag that the table is ready for use\n    PDF417CodewordDecoder.bSymbolTableReady = false;\n    PDF417CodewordDecoder.RATIOS_TABLE = new Array(PDF417Common.SYMBOL_TABLE.length).map(function (x) { return x = new Array(PDF417Common.BARS_IN_MODULE); });\n    return PDF417CodewordDecoder;\n}());\nexport default PDF417CodewordDecoder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,SAAS,MAAM,iCAAiC;AACvD;AACA,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,KAAK,MAAM,kBAAkB;AACpC;AACA;AACA;AACA;AACA,IAAIC,qBAAqB,GAAG,aAAe,YAAY;EACnD,SAASA,qBAAqBA,CAAA,EAAG,CACjC;EACA;AACJ;AACA;AACA;AACA;EACIA,qBAAqB,CAACC,UAAU,GAAG,YAAY;IAC3C;IACA,KAAM,OAAO,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACK,YAAY,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/D,IAAIG,aAAa,GAAGP,YAAY,CAACK,YAAY,CAACD,CAAC,CAAC;MAChD,IAAII,UAAU,GAAGD,aAAa,GAAG,GAAG;MACpC,KAAM,OAAO,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,YAAY,CAACU,cAAc,EAAED,CAAC,EAAE,EAAE;QAC1D,IAAIE,IAAI,GAAG,GAAG;QACd,OAAO,CAACJ,aAAa,GAAG,GAAG,MAAMC,UAAU,EAAE;UACzCG,IAAI,IAAI,GAAG;UACXJ,aAAa,KAAK,CAAC;QACvB;QACAC,UAAU,GAAGD,aAAa,GAAG,GAAG;QAChC,IAAI,CAACL,qBAAqB,CAACU,YAAY,CAACR,CAAC,CAAC,EAAE;UACxCF,qBAAqB,CAACU,YAAY,CAACR,CAAC,CAAC,GAAG,IAAIS,KAAK,CAACb,YAAY,CAACU,cAAc,CAAC;QAClF;QACAR,qBAAqB,CAACU,YAAY,CAACR,CAAC,CAAC,CAACJ,YAAY,CAACU,cAAc,GAAGD,CAAC,GAAG,CAAC,CAAC,GAAGK,IAAI,CAACC,MAAM,CAACJ,IAAI,GAAGX,YAAY,CAACgB,mBAAmB,CAAC;MACrI;IACJ;IACA,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC,CAAC;EACDf,qBAAqB,CAACgB,eAAe,GAAG,UAAUC,cAAc,EAAE;IAC9D,IAAIC,YAAY,GAAGlB,qBAAqB,CAACmB,uBAAuB,CAACnB,qBAAqB,CAACoB,eAAe,CAACH,cAAc,CAAC,CAAC;IACvH,IAAIC,YAAY,KAAK,CAAC,CAAC,EAAE;MACrB,OAAOA,YAAY;IACvB;IACA,OAAOlB,qBAAqB,CAACqB,sBAAsB,CAACJ,cAAc,CAAC;EACvE,CAAC;EACDjB,qBAAqB,CAACoB,eAAe,GAAG,UAAUH,cAAc,EAAE;IAC9D,IAAIK,WAAW,GAAGzB,SAAS,CAAC0B,GAAG,CAACN,cAAc,CAAC;IAC/C,IAAIO,MAAM,GAAG,IAAIC,UAAU,CAAC3B,YAAY,CAACU,cAAc,CAAC;IACxD,IAAIkB,aAAa,GAAG,CAAC;IACrB,IAAIC,eAAe,GAAG,CAAC;IACvB,KAAM,OAAO,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,CAACgB,mBAAmB,EAAEZ,CAAC,EAAE,EAAE;MAC/D,IAAI0B,WAAW,GAAGN,WAAW,IAAI,CAAC,GAAGxB,YAAY,CAACgB,mBAAmB,CAAC,GACjEZ,CAAC,GAAGoB,WAAW,GAAIxB,YAAY,CAACgB,mBAAmB;MACxD,IAAIa,eAAe,GAAGV,cAAc,CAACS,aAAa,CAAC,IAAIE,WAAW,EAAE;QAChED,eAAe,IAAIV,cAAc,CAACS,aAAa,CAAC;QAChDA,aAAa,EAAE;MACnB;MACAF,MAAM,CAACE,aAAa,CAAC,EAAE;IAC3B;IACA,OAAOF,MAAM;EACjB,CAAC;EACDxB,qBAAqB,CAACmB,uBAAuB,GAAG,UAAUF,cAAc,EAAE;IACtE,IAAIC,YAAY,GAAGlB,qBAAqB,CAAC6B,WAAW,CAACZ,cAAc,CAAC;IACpE,OAAOnB,YAAY,CAACgC,WAAW,CAACZ,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGA,YAAY;EAC5E,CAAC;EACDlB,qBAAqB,CAAC6B,WAAW,GAAG,UAAUZ,cAAc,EAAE;IAC1D,IAAIO,MAAM,GAAG,QAAS,CAAC;IACvB,KAAK,IAAI,OAAQtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,cAAc,CAACb,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpD,KAAM,OAAO,IAAI6B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGd,cAAc,CAACf,CAAC,CAAC,EAAE6B,GAAG,EAAE,EAAE;QACtDP,MAAM,GAAIA,MAAM,IAAI,CAAC,IAAKtB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAClD;IACJ;IACA,OAAOU,IAAI,CAACoB,KAAK,CAACR,MAAM,CAAC;EAC7B,CAAC;EACD;EACAxB,qBAAqB,CAACqB,sBAAsB,GAAG,UAAUJ,cAAc,EAAE;IACrE,IAAIK,WAAW,GAAGzB,SAAS,CAAC0B,GAAG,CAACN,cAAc,CAAC;IAC/C,IAAIgB,cAAc,GAAG,IAAItB,KAAK,CAACb,YAAY,CAACU,cAAc,CAAC;IAC3D,IAAIc,WAAW,GAAG,CAAC,EAAE;MACjB,KAAK,IAAI,OAAQpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+B,cAAc,CAAC7B,MAAM,EAAEF,CAAC,EAAE,EAAE;QACpD+B,cAAc,CAAC/B,CAAC,CAAC,GAAGU,IAAI,CAACC,MAAM,CAACI,cAAc,CAACf,CAAC,CAAC,GAAGoB,WAAW,CAAC;MACpE;IACJ;IACA,IAAIY,cAAc,GAAGnC,KAAK,CAACoC,SAAS;IACpC,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,CAAC,IAAI,CAACrB,iBAAiB,EAAE;MACzBf,qBAAqB,CAACC,UAAU,CAAC,CAAC;IACtC;IACA,KAAM,OAAO,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,qBAAqB,CAACU,YAAY,CAACN,MAAM,EAAEG,CAAC,EAAE,EAAE;MACxE,IAAI8B,KAAK,GAAG,GAAG;MACf,IAAIC,aAAa,GAAGtC,qBAAqB,CAACU,YAAY,CAACH,CAAC,CAAC;MACzD,KAAM,OAAO,IAAIgC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGzC,YAAY,CAACU,cAAc,EAAE+B,CAAC,EAAE,EAAE;QAC1D,IAAIC,IAAI,GAAG5B,IAAI,CAACC,MAAM,CAACyB,aAAa,CAACC,CAAC,CAAC,GAAGN,cAAc,CAACM,CAAC,CAAC,CAAC;QAC5DF,KAAK,IAAIzB,IAAI,CAACC,MAAM,CAAC2B,IAAI,GAAGA,IAAI,CAAC;QACjC,IAAIH,KAAK,IAAIH,cAAc,EAAE;UACzB;QACJ;MACJ;MACA,IAAIG,KAAK,GAAGH,cAAc,EAAE;QACxBA,cAAc,GAAGG,KAAK;QACtBD,SAAS,GAAGtC,YAAY,CAACK,YAAY,CAACI,CAAC,CAAC;MAC5C;IACJ;IACA,OAAO6B,SAAS;EACpB,CAAC;EACD;EACApC,qBAAqB,CAACe,iBAAiB,GAAG,KAAK;EAC/Cf,qBAAqB,CAACU,YAAY,GAAG,IAAIC,KAAK,CAACb,YAAY,CAACK,YAAY,CAACC,MAAM,CAAC,CAACqC,GAAG,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,GAAG,IAAI/B,KAAK,CAACb,YAAY,CAACU,cAAc,CAAC;EAAE,CAAC,CAAC;EACzJ,OAAOR,qBAAqB;AAChC,CAAC,CAAC,CAAE;AACJ,eAAeA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}