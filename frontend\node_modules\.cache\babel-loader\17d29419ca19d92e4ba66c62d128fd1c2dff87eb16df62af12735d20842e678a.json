{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nimport CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Just to make a shortcut between Java code and TS code.\n */\nvar Charset = /** @class */function (_super) {\n  __extends(Charset, _super);\n  function Charset() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Charset.forName = function (name) {\n    return this.getCharacterSetECIByName(name);\n  };\n  return Charset;\n}(CharacterSetECI);\nexport default Charset;", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "hasOwnProperty", "__", "constructor", "prototype", "create", "CharacterSetECI", "Charset", "_super", "apply", "arguments", "forName", "name", "getCharacterSetECIByName"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/Charset.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nimport CharacterSetECI from '../common/CharacterSetECI';\n/**\n * Just to make a shortcut between Java code and TS code.\n */\nvar Charset = /** @class */ (function (_super) {\n    __extends(Charset, _super);\n    function Charset() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Charset.forName = function (name) {\n        return this.getCharacterSetECIByName(name);\n    };\n    return Charset;\n}(CharacterSetECI));\nexport default Charset;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIA,CAAC,CAACM,cAAc,CAACD,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IAC9E,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnBF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASO,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGT,CAAC;IAAE;IACtCA,CAAC,CAACU,SAAS,GAAGT,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACS,MAAM,CAACV,CAAC,CAAC,IAAIO,EAAE,CAACE,SAAS,GAAGT,CAAC,CAACS,SAAS,EAAE,IAAIF,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,OAAOI,eAAe,MAAM,2BAA2B;AACvD;AACA;AACA;AACA,IAAIC,OAAO,GAAG,aAAe,UAAUC,MAAM,EAAE;EAC3ChB,SAAS,CAACe,OAAO,EAAEC,MAAM,CAAC;EAC1B,SAASD,OAAOA,CAAA,EAAG;IACf,OAAOC,MAAM,KAAK,IAAI,IAAIA,MAAM,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,IAAI,IAAI;EACnE;EACAH,OAAO,CAACI,OAAO,GAAG,UAAUC,IAAI,EAAE;IAC9B,OAAO,IAAI,CAACC,wBAAwB,CAACD,IAAI,CAAC;EAC9C,CAAC;EACD,OAAOL,OAAO;AAClB,CAAC,CAACD,eAAe,CAAE;AACnB,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}