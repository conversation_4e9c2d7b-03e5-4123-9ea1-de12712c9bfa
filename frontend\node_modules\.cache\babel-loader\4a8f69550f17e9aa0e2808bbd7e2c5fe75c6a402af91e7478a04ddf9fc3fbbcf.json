{"ast": null, "code": "/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * These are a set of hints that you may pass to Writers to specify their behavior.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar EncodeHintType;\n(function (EncodeHintType) {\n  /**\n   * Specifies what degree of error correction to use, for example in QR Codes.\n   * Type depends on the encoder. For example for QR codes it's type\n   * {@link com.google.zxing.qrcode.decoder.ErrorCorrectionLevel ErrorCorrectionLevel}.\n   * For Aztec it is of type {@link Integer}, representing the minimal percentage of error correction words.\n   * For PDF417 it is of type {@link Integer}, valid values being 0 to 8.\n   * In all cases, it can also be a {@link String} representation of the desired value as well.\n   * Note: an Aztec symbol should have a minimum of 25% EC words.\n   */\n  EncodeHintType[EncodeHintType[\"ERROR_CORRECTION\"] = 0] = \"ERROR_CORRECTION\";\n  /**\n   * Specifies what character encoding to use where applicable (type {@link String})\n   */\n  EncodeHintType[EncodeHintType[\"CHARACTER_SET\"] = 1] = \"CHARACTER_SET\";\n  /**\n   * Specifies the matrix shape for Data Matrix (type {@link com.google.zxing.datamatrix.encoder.SymbolShapeHint})\n   */\n  EncodeHintType[EncodeHintType[\"DATA_MATRIX_SHAPE\"] = 2] = \"DATA_MATRIX_SHAPE\";\n  /**\n   * Specifies whether to use compact mode for Data Matrix (type {@link Boolean}, or \"true\" or \"false\"\n   * {@link String } value).\n   * The compact encoding mode also supports the encoding of characters that are not in the ISO-8859-1\n   * character set via ECIs.\n   * Please note that in that case, the most compact character encoding is chosen for characters in\n   * the input that are not in the ISO-8859-1 character set. Based on experience, some scanners do not\n   * support encodings like cp-1256 (Arabic). In such cases the encoding can be forced to UTF-8 by\n   * means of the {@link #CHARACTER_SET} encoding hint.\n   * Compact encoding also provides GS1-FNC1 support when {@link #GS1_FORMAT} is selected. In this case\n   * group-separator character (ASCII 29 decimal) can be used to encode the positions of FNC1 codewords\n   * for the purpose of delimiting AIs.\n   * This option and {@link #FORCE_C40} are mutually exclusive.\n   */\n  EncodeHintType[EncodeHintType[\"DATA_MATRIX_COMPACT\"] = 3] = \"DATA_MATRIX_COMPACT\";\n  /**\n   * Specifies a minimum barcode size (type {@link Dimension}). Only applicable to Data Matrix now.\n   *\n   * @deprecated use width/height params in\n   * {@link com.google.zxing.datamatrix.DataMatrixWriter#encode(String, BarcodeFormat, int, int)}\n   */\n  /*@Deprecated*/\n  EncodeHintType[EncodeHintType[\"MIN_SIZE\"] = 4] = \"MIN_SIZE\";\n  /**\n   * Specifies a maximum barcode size (type {@link Dimension}). Only applicable to Data Matrix now.\n   *\n   * @deprecated without replacement\n   */\n  /*@Deprecated*/\n  EncodeHintType[EncodeHintType[\"MAX_SIZE\"] = 5] = \"MAX_SIZE\";\n  /**\n   * Specifies margin, in pixels, to use when generating the barcode. The meaning can vary\n   * by format; for example it controls margin before and after the barcode horizontally for\n   * most 1D formats. (Type {@link Integer}, or {@link String} representation of the integer value).\n   */\n  EncodeHintType[EncodeHintType[\"MARGIN\"] = 6] = \"MARGIN\";\n  /**\n   * Specifies whether to use compact mode for PDF417 (type {@link Boolean}, or \"true\" or \"false\"\n   * {@link String} value).\n   */\n  EncodeHintType[EncodeHintType[\"PDF417_COMPACT\"] = 7] = \"PDF417_COMPACT\";\n  /**\n   * Specifies what compaction mode to use for PDF417 (type\n   * {@link com.google.zxing.pdf417.encoder.Compaction Compaction} or {@link String} value of one of its\n   * enum values).\n   */\n  EncodeHintType[EncodeHintType[\"PDF417_COMPACTION\"] = 8] = \"PDF417_COMPACTION\";\n  /**\n   * Specifies the minimum and maximum number of rows and columns for PDF417 (type\n   * {@link com.google.zxing.pdf417.encoder.Dimensions Dimensions}).\n   */\n  EncodeHintType[EncodeHintType[\"PDF417_DIMENSIONS\"] = 9] = \"PDF417_DIMENSIONS\";\n  /**\n   * Specifies the required number of layers for an Aztec code.\n   * A negative number (-1, -2, -3, -4) specifies a compact Aztec code.\n   * 0 indicates to use the minimum number of layers (the default).\n   * A positive number (1, 2, .. 32) specifies a normal (non-compact) Aztec code.\n   * (Type {@link Integer}, or {@link String} representation of the integer value).\n   */\n  EncodeHintType[EncodeHintType[\"AZTEC_LAYERS\"] = 10] = \"AZTEC_LAYERS\";\n  /**\n   * Specifies the exact version of QR code to be encoded.\n   * (Type {@link Integer}, or {@link String} representation of the integer value).\n   */\n  EncodeHintType[EncodeHintType[\"QR_VERSION\"] = 11] = \"QR_VERSION\";\n  /**\n   * Specifies whether the data should be encoded to the GS1 standard (type {@link Boolean}, or \"true\" or \"false\"\n   * {@link String } value).\n   */\n  EncodeHintType[EncodeHintType[\"GS1_FORMAT\"] = 12] = \"GS1_FORMAT\";\n  /**\n   * Forces C40 encoding for data-matrix (type {@link Boolean}, or \"true\" or \"false\") {@link String } value). This\n   * option and {@link #DATA_MATRIX_COMPACT} are mutually exclusive.\n   */\n  EncodeHintType[EncodeHintType[\"FORCE_C40\"] = 13] = \"FORCE_C40\";\n})(EncodeHintType || (EncodeHintType = {}));\nexport default EncodeHintType;", "map": {"version": 3, "names": ["EncodeHintType"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/EncodeHintType.js"], "sourcesContent": ["/*\n * Copyright 2009 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n/*namespace com.google.zxing {*/\n/**\n * These are a set of hints that you may pass to Writers to specify their behavior.\n *\n * <AUTHOR> (<PERSON>)\n */\nvar EncodeHintType;\n(function (EncodeHintType) {\n    /**\n     * Specifies what degree of error correction to use, for example in QR Codes.\n     * Type depends on the encoder. For example for QR codes it's type\n     * {@link com.google.zxing.qrcode.decoder.ErrorCorrectionLevel ErrorCorrectionLevel}.\n     * For Aztec it is of type {@link Integer}, representing the minimal percentage of error correction words.\n     * For PDF417 it is of type {@link Integer}, valid values being 0 to 8.\n     * In all cases, it can also be a {@link String} representation of the desired value as well.\n     * Note: an Aztec symbol should have a minimum of 25% EC words.\n     */\n    EncodeHintType[EncodeHintType[\"ERROR_CORRECTION\"] = 0] = \"ERROR_CORRECTION\";\n    /**\n     * Specifies what character encoding to use where applicable (type {@link String})\n     */\n    EncodeHintType[EncodeHintType[\"CHARACTER_SET\"] = 1] = \"CHARACTER_SET\";\n    /**\n     * Specifies the matrix shape for Data Matrix (type {@link com.google.zxing.datamatrix.encoder.SymbolShapeHint})\n     */\n    EncodeHintType[EncodeHintType[\"DATA_MATRIX_SHAPE\"] = 2] = \"DATA_MATRIX_SHAPE\";\n    /**\n     * Specifies whether to use compact mode for Data Matrix (type {@link Boolean}, or \"true\" or \"false\"\n     * {@link String } value).\n     * The compact encoding mode also supports the encoding of characters that are not in the ISO-8859-1\n     * character set via ECIs.\n     * Please note that in that case, the most compact character encoding is chosen for characters in\n     * the input that are not in the ISO-8859-1 character set. Based on experience, some scanners do not\n     * support encodings like cp-1256 (Arabic). In such cases the encoding can be forced to UTF-8 by\n     * means of the {@link #CHARACTER_SET} encoding hint.\n     * Compact encoding also provides GS1-FNC1 support when {@link #GS1_FORMAT} is selected. In this case\n     * group-separator character (ASCII 29 decimal) can be used to encode the positions of FNC1 codewords\n     * for the purpose of delimiting AIs.\n     * This option and {@link #FORCE_C40} are mutually exclusive.\n     */\n    EncodeHintType[EncodeHintType[\"DATA_MATRIX_COMPACT\"] = 3] = \"DATA_MATRIX_COMPACT\";\n    /**\n     * Specifies a minimum barcode size (type {@link Dimension}). Only applicable to Data Matrix now.\n     *\n     * @deprecated use width/height params in\n     * {@link com.google.zxing.datamatrix.DataMatrixWriter#encode(String, BarcodeFormat, int, int)}\n     */\n    /*@Deprecated*/\n    EncodeHintType[EncodeHintType[\"MIN_SIZE\"] = 4] = \"MIN_SIZE\";\n    /**\n     * Specifies a maximum barcode size (type {@link Dimension}). Only applicable to Data Matrix now.\n     *\n     * @deprecated without replacement\n     */\n    /*@Deprecated*/\n    EncodeHintType[EncodeHintType[\"MAX_SIZE\"] = 5] = \"MAX_SIZE\";\n    /**\n     * Specifies margin, in pixels, to use when generating the barcode. The meaning can vary\n     * by format; for example it controls margin before and after the barcode horizontally for\n     * most 1D formats. (Type {@link Integer}, or {@link String} representation of the integer value).\n     */\n    EncodeHintType[EncodeHintType[\"MARGIN\"] = 6] = \"MARGIN\";\n    /**\n     * Specifies whether to use compact mode for PDF417 (type {@link Boolean}, or \"true\" or \"false\"\n     * {@link String} value).\n     */\n    EncodeHintType[EncodeHintType[\"PDF417_COMPACT\"] = 7] = \"PDF417_COMPACT\";\n    /**\n     * Specifies what compaction mode to use for PDF417 (type\n     * {@link com.google.zxing.pdf417.encoder.Compaction Compaction} or {@link String} value of one of its\n     * enum values).\n     */\n    EncodeHintType[EncodeHintType[\"PDF417_COMPACTION\"] = 8] = \"PDF417_COMPACTION\";\n    /**\n     * Specifies the minimum and maximum number of rows and columns for PDF417 (type\n     * {@link com.google.zxing.pdf417.encoder.Dimensions Dimensions}).\n     */\n    EncodeHintType[EncodeHintType[\"PDF417_DIMENSIONS\"] = 9] = \"PDF417_DIMENSIONS\";\n    /**\n     * Specifies the required number of layers for an Aztec code.\n     * A negative number (-1, -2, -3, -4) specifies a compact Aztec code.\n     * 0 indicates to use the minimum number of layers (the default).\n     * A positive number (1, 2, .. 32) specifies a normal (non-compact) Aztec code.\n     * (Type {@link Integer}, or {@link String} representation of the integer value).\n     */\n    EncodeHintType[EncodeHintType[\"AZTEC_LAYERS\"] = 10] = \"AZTEC_LAYERS\";\n    /**\n     * Specifies the exact version of QR code to be encoded.\n     * (Type {@link Integer}, or {@link String} representation of the integer value).\n     */\n    EncodeHintType[EncodeHintType[\"QR_VERSION\"] = 11] = \"QR_VERSION\";\n    /**\n     * Specifies whether the data should be encoded to the GS1 standard (type {@link Boolean}, or \"true\" or \"false\"\n     * {@link String } value).\n     */\n    EncodeHintType[EncodeHintType[\"GS1_FORMAT\"] = 12] = \"GS1_FORMAT\";\n    /**\n     * Forces C40 encoding for data-matrix (type {@link Boolean}, or \"true\" or \"false\") {@link String } value). This\n     * option and {@link #DATA_MATRIX_COMPACT} are mutually exclusive.\n     */\n    EncodeHintType[EncodeHintType[\"FORCE_C40\"] = 13] = \"FORCE_C40\";\n})(EncodeHintType || (EncodeHintType = {}));\nexport default EncodeHintType;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,cAAc;AAClB,CAAC,UAAUA,cAAc,EAAE;EACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,GAAG,kBAAkB;EAC3E;AACJ;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;EACrE;AACJ;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;EAC7E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB;EACjF;AACJ;AACA;AACA;AACA;AACA;EACI;EACAA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3D;AACJ;AACA;AACA;AACA;EACI;EACAA,cAAc,CAACA,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;EAC3D;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACvD;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,gBAAgB;EACvE;AACJ;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;EAC7E;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAG,mBAAmB;EAC7E;AACJ;AACA;AACA;AACA;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,cAAc;EACpE;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAChE;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,YAAY;EAChE;AACJ;AACA;AACA;EACIA,cAAc,CAACA,cAAc,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC,GAAG,WAAW;AAClE,CAAC,EAAEA,cAAc,KAAKA,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}