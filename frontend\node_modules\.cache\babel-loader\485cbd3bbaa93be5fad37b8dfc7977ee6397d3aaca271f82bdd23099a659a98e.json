{"ast": null, "code": "var System = /** @class */function () {\n  function System() {}\n  // public static void arraycopy(Object src, int srcPos, Object dest, int destPos, int length)\n  /**\n   * Makes a copy of a array.\n   */\n  System.arraycopy = function (src, srcPos, dest, destPos, length) {\n    // TODO: better use split or set?\n    while (length--) {\n      dest[destPos++] = src[srcPos++];\n    }\n  };\n  /**\n   * Returns the current time in milliseconds.\n   */\n  System.currentTimeMillis = function () {\n    return Date.now();\n  };\n  return System;\n}();\nexport default System;", "map": {"version": 3, "names": ["System", "arraycopy", "src", "srcPos", "dest", "destPos", "length", "currentTimeMillis", "Date", "now"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/util/System.js"], "sourcesContent": ["var System = /** @class */ (function () {\n    function System() {\n    }\n    // public static void arraycopy(Object src, int srcPos, Object dest, int destPos, int length)\n    /**\n     * Makes a copy of a array.\n     */\n    System.arraycopy = function (src, srcPos, dest, destPos, length) {\n        // TODO: better use split or set?\n        while (length--) {\n            dest[destPos++] = src[srcPos++];\n        }\n    };\n    /**\n     * Returns the current time in milliseconds.\n     */\n    System.currentTimeMillis = function () {\n        return Date.now();\n    };\n    return System;\n}());\nexport default System;\n"], "mappings": "AAAA,IAAIA,MAAM,GAAG,aAAe,YAAY;EACpC,SAASA,MAAMA,CAAA,EAAG,CAClB;EACA;EACA;AACJ;AACA;EACIA,MAAM,CAACC,SAAS,GAAG,UAAUC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAE;IAC7D;IACA,OAAOA,MAAM,EAAE,EAAE;MACbF,IAAI,CAACC,OAAO,EAAE,CAAC,GAAGH,GAAG,CAACC,MAAM,EAAE,CAAC;IACnC;EACJ,CAAC;EACD;AACJ;AACA;EACIH,MAAM,CAACO,iBAAiB,GAAG,YAAY;IACnC,OAAOC,IAAI,CAACC,GAAG,CAAC,CAAC;EACrB,CAAC;EACD,OAAOT,MAAM;AACjB,CAAC,CAAC,CAAE;AACJ,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}