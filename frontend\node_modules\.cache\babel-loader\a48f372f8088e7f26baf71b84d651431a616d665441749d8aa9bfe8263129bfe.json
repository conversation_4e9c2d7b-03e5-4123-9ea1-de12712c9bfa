{"ast": null, "code": "/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.detector {*/\nimport DecodeHintType from '../../DecodeHintType';\nimport ResultPoint from '../../ResultPoint';\nimport FinderPattern from './FinderPattern';\nimport FinderPatternInfo from './FinderPatternInfo';\nimport NotFoundException from '../../NotFoundException';\n/*import java.io.Serializable;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collections;*/\n/*import java.util.Comparator;*/\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * <p>This class attempts to find finder patterns in a QR Code. Finder patterns are the square\n * markers at three corners of a QR Code.</p>\n *\n * <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.\n *\n * <AUTHOR> Owen\n */\nvar FinderPatternFinder = /** @class */function () {\n  /**\n   * <p>Creates a finder that will search the image for three finder patterns.</p>\n   *\n   * @param image image to search\n   */\n  // public constructor(image: BitMatrix) {\n  //   this(image, null)\n  // }\n  function FinderPatternFinder(image, resultPointCallback) {\n    this.image = image;\n    this.resultPointCallback = resultPointCallback;\n    this.possibleCenters = [];\n    this.crossCheckStateCount = new Int32Array(5);\n    this.resultPointCallback = resultPointCallback;\n  }\n  FinderPatternFinder.prototype.getImage = function () {\n    return this.image;\n  };\n  FinderPatternFinder.prototype.getPossibleCenters = function () {\n    return this.possibleCenters;\n  };\n  FinderPatternFinder.prototype.find = function (hints) {\n    var tryHarder = hints !== null && hints !== undefined && undefined !== hints.get(DecodeHintType.TRY_HARDER);\n    var pureBarcode = hints !== null && hints !== undefined && undefined !== hints.get(DecodeHintType.PURE_BARCODE);\n    var image = this.image;\n    var maxI = image.getHeight();\n    var maxJ = image.getWidth();\n    // We are looking for black/white/black/white/black modules in\n    // 1:1:3:1:1 ratio; this tracks the number of such modules seen so far\n    // Let's assume that the maximum version QR Code we support takes up 1/4 the height of the\n    // image, and then account for the center being 3 modules in size. This gives the smallest\n    // number of pixels the center could be, so skip this often. When trying harder, look for all\n    // QR versions regardless of how dense they are.\n    var iSkip = Math.floor(3 * maxI / (4 * FinderPatternFinder.MAX_MODULES));\n    if (iSkip < FinderPatternFinder.MIN_SKIP || tryHarder) {\n      iSkip = FinderPatternFinder.MIN_SKIP;\n    }\n    var done = false;\n    var stateCount = new Int32Array(5);\n    for (var i = iSkip - 1; i < maxI && !done; i += iSkip) {\n      // Get a row of black/white values\n      stateCount[0] = 0;\n      stateCount[1] = 0;\n      stateCount[2] = 0;\n      stateCount[3] = 0;\n      stateCount[4] = 0;\n      var currentState = 0;\n      for (var j = 0; j < maxJ; j++) {\n        if (image.get(j, i)) {\n          // Black pixel\n          if ((currentState & 1) === 1) {\n            // Counting white pixels\n            currentState++;\n          }\n          stateCount[currentState]++;\n        } else {\n          // White pixel\n          if ((currentState & 1) === 0) {\n            // Counting black pixels\n            if (currentState === 4) {\n              // A winner?\n              if (FinderPatternFinder.foundPatternCross(stateCount)) {\n                // Yes\n                var confirmed = this.handlePossibleCenter(stateCount, i, j, pureBarcode);\n                if (confirmed === true) {\n                  // Start examining every other line. Checking each line turned out to be too\n                  // expensive and didn't improve performance.\n                  iSkip = 2;\n                  if (this.hasSkipped === true) {\n                    done = this.haveMultiplyConfirmedCenters();\n                  } else {\n                    var rowSkip = this.findRowSkip();\n                    if (rowSkip > stateCount[2]) {\n                      // Skip rows between row of lower confirmed center\n                      // and top of presumed third confirmed center\n                      // but back up a bit to get a full chance of detecting\n                      // it, entire width of center of finder pattern\n                      // Skip by rowSkip, but back off by stateCount[2] (size of last center\n                      // of pattern we saw) to be conservative, and also back off by iSkip which\n                      // is about to be re-added\n                      i += rowSkip - stateCount[2] - iSkip;\n                      j = maxJ - 1;\n                    }\n                  }\n                } else {\n                  stateCount[0] = stateCount[2];\n                  stateCount[1] = stateCount[3];\n                  stateCount[2] = stateCount[4];\n                  stateCount[3] = 1;\n                  stateCount[4] = 0;\n                  currentState = 3;\n                  continue;\n                }\n                // Clear state to start looking again\n                currentState = 0;\n                stateCount[0] = 0;\n                stateCount[1] = 0;\n                stateCount[2] = 0;\n                stateCount[3] = 0;\n                stateCount[4] = 0;\n              } else {\n                // No, shift counts back by two\n                stateCount[0] = stateCount[2];\n                stateCount[1] = stateCount[3];\n                stateCount[2] = stateCount[4];\n                stateCount[3] = 1;\n                stateCount[4] = 0;\n                currentState = 3;\n              }\n            } else {\n              stateCount[++currentState]++;\n            }\n          } else {\n            // Counting white pixels\n            stateCount[currentState]++;\n          }\n        }\n      }\n      if (FinderPatternFinder.foundPatternCross(stateCount)) {\n        var confirmed = this.handlePossibleCenter(stateCount, i, maxJ, pureBarcode);\n        if (confirmed === true) {\n          iSkip = stateCount[0];\n          if (this.hasSkipped) {\n            // Found a third one\n            done = this.haveMultiplyConfirmedCenters();\n          }\n        }\n      }\n    }\n    var patternInfo = this.selectBestPatterns();\n    ResultPoint.orderBestPatterns(patternInfo);\n    return new FinderPatternInfo(patternInfo);\n  };\n  /**\n   * Given a count of black/white/black/white/black pixels just seen and an end position,\n   * figures the location of the center of this run.\n   */\n  FinderPatternFinder.centerFromEnd = function (stateCount, end /*int*/) {\n    return end - stateCount[4] - stateCount[3] - stateCount[2] / 2.0;\n  };\n  /**\n   * @param stateCount count of black/white/black/white/black pixels just read\n   * @return true iff the proportions of the counts is close enough to the 1/1/3/1/1 ratios\n   *         used by finder patterns to be considered a match\n   */\n  FinderPatternFinder.foundPatternCross = function (stateCount) {\n    var totalModuleSize = 0;\n    for (var i = 0; i < 5; i++) {\n      var count = stateCount[i];\n      if (count === 0) {\n        return false;\n      }\n      totalModuleSize += count;\n    }\n    if (totalModuleSize < 7) {\n      return false;\n    }\n    var moduleSize = totalModuleSize / 7.0;\n    var maxVariance = moduleSize / 2.0;\n    // Allow less than 50% variance from 1-1-3-1-1 proportions\n    return Math.abs(moduleSize - stateCount[0]) < maxVariance && Math.abs(moduleSize - stateCount[1]) < maxVariance && Math.abs(3.0 * moduleSize - stateCount[2]) < 3 * maxVariance && Math.abs(moduleSize - stateCount[3]) < maxVariance && Math.abs(moduleSize - stateCount[4]) < maxVariance;\n  };\n  FinderPatternFinder.prototype.getCrossCheckStateCount = function () {\n    var crossCheckStateCount = this.crossCheckStateCount;\n    crossCheckStateCount[0] = 0;\n    crossCheckStateCount[1] = 0;\n    crossCheckStateCount[2] = 0;\n    crossCheckStateCount[3] = 0;\n    crossCheckStateCount[4] = 0;\n    return crossCheckStateCount;\n  };\n  /**\n   * After a vertical and horizontal scan finds a potential finder pattern, this method\n   * \"cross-cross-cross-checks\" by scanning down diagonally through the center of the possible\n   * finder pattern to see if the same proportion is detected.\n   *\n   * @param startI row where a finder pattern was detected\n   * @param centerJ center of the section that appears to cross a finder pattern\n   * @param maxCount maximum reasonable number of modules that should be\n   *  observed in any reading state, based on the results of the horizontal scan\n   * @param originalStateCountTotal The original state count total.\n   * @return true if proportions are withing expected limits\n   */\n  FinderPatternFinder.prototype.crossCheckDiagonal = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n    var stateCount = this.getCrossCheckStateCount();\n    // Start counting up, left from center finding black center mass\n    var i = 0;\n    var image = this.image;\n    while (startI >= i && centerJ >= i && image.get(centerJ - i, startI - i)) {\n      stateCount[2]++;\n      i++;\n    }\n    if (startI < i || centerJ < i) {\n      return false;\n    }\n    // Continue up, left finding white space\n    while (startI >= i && centerJ >= i && !image.get(centerJ - i, startI - i) && stateCount[1] <= maxCount) {\n      stateCount[1]++;\n      i++;\n    }\n    // If already too many modules in this state or ran off the edge:\n    if (startI < i || centerJ < i || stateCount[1] > maxCount) {\n      return false;\n    }\n    // Continue up, left finding black border\n    while (startI >= i && centerJ >= i && image.get(centerJ - i, startI - i) && stateCount[0] <= maxCount) {\n      stateCount[0]++;\n      i++;\n    }\n    if (stateCount[0] > maxCount) {\n      return false;\n    }\n    var maxI = image.getHeight();\n    var maxJ = image.getWidth();\n    // Now also count down, right from center\n    i = 1;\n    while (startI + i < maxI && centerJ + i < maxJ && image.get(centerJ + i, startI + i)) {\n      stateCount[2]++;\n      i++;\n    }\n    // Ran off the edge?\n    if (startI + i >= maxI || centerJ + i >= maxJ) {\n      return false;\n    }\n    while (startI + i < maxI && centerJ + i < maxJ && !image.get(centerJ + i, startI + i) && stateCount[3] < maxCount) {\n      stateCount[3]++;\n      i++;\n    }\n    if (startI + i >= maxI || centerJ + i >= maxJ || stateCount[3] >= maxCount) {\n      return false;\n    }\n    while (startI + i < maxI && centerJ + i < maxJ && image.get(centerJ + i, startI + i) && stateCount[4] < maxCount) {\n      stateCount[4]++;\n      i++;\n    }\n    if (stateCount[4] >= maxCount) {\n      return false;\n    }\n    // If we found a finder-pattern-like section, but its size is more than 100% different than\n    // the original, assume it's a false positive\n    var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n    return Math.abs(stateCountTotal - originalStateCountTotal) < 2 * originalStateCountTotal && FinderPatternFinder.foundPatternCross(stateCount);\n  };\n  /**\n   * <p>After a horizontal scan finds a potential finder pattern, this method\n   * \"cross-checks\" by scanning down vertically through the center of the possible\n   * finder pattern to see if the same proportion is detected.</p>\n   *\n   * @param startI row where a finder pattern was detected\n   * @param centerJ center of the section that appears to cross a finder pattern\n   * @param maxCount maximum reasonable number of modules that should be\n   * observed in any reading state, based on the results of the horizontal scan\n   * @return vertical center of finder pattern, or {@link Float#NaN} if not found\n   */\n  FinderPatternFinder.prototype.crossCheckVertical = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n    var image = this.image;\n    var maxI = image.getHeight();\n    var stateCount = this.getCrossCheckStateCount();\n    // Start counting up from center\n    var i = startI;\n    while (i >= 0 && image.get(centerJ, i)) {\n      stateCount[2]++;\n      i--;\n    }\n    if (i < 0) {\n      return NaN;\n    }\n    while (i >= 0 && !image.get(centerJ, i) && stateCount[1] <= maxCount) {\n      stateCount[1]++;\n      i--;\n    }\n    // If already too many modules in this state or ran off the edge:\n    if (i < 0 || stateCount[1] > maxCount) {\n      return NaN;\n    }\n    while (i >= 0 && image.get(centerJ, i) && stateCount[0] <= maxCount) {\n      stateCount[0]++;\n      i--;\n    }\n    if (stateCount[0] > maxCount) {\n      return NaN;\n    }\n    // Now also count down from center\n    i = startI + 1;\n    while (i < maxI && image.get(centerJ, i)) {\n      stateCount[2]++;\n      i++;\n    }\n    if (i === maxI) {\n      return NaN;\n    }\n    while (i < maxI && !image.get(centerJ, i) && stateCount[3] < maxCount) {\n      stateCount[3]++;\n      i++;\n    }\n    if (i === maxI || stateCount[3] >= maxCount) {\n      return NaN;\n    }\n    while (i < maxI && image.get(centerJ, i) && stateCount[4] < maxCount) {\n      stateCount[4]++;\n      i++;\n    }\n    if (stateCount[4] >= maxCount) {\n      return NaN;\n    }\n    // If we found a finder-pattern-like section, but its size is more than 40% different than\n    // the original, assume it's a false positive\n    var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n    if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {\n      return NaN;\n    }\n    return FinderPatternFinder.foundPatternCross(stateCount) ? FinderPatternFinder.centerFromEnd(stateCount, i) : NaN;\n  };\n  /**\n   * <p>Like {@link #crossCheckVertical(int, int, int, int)}, and in fact is basically identical,\n   * except it reads horizontally instead of vertically. This is used to cross-cross\n   * check a vertical cross check and locate the real center of the alignment pattern.</p>\n   */\n  FinderPatternFinder.prototype.crossCheckHorizontal = function (startJ /*int*/, centerI /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n    var image = this.image;\n    var maxJ = image.getWidth();\n    var stateCount = this.getCrossCheckStateCount();\n    var j = startJ;\n    while (j >= 0 && image.get(j, centerI)) {\n      stateCount[2]++;\n      j--;\n    }\n    if (j < 0) {\n      return NaN;\n    }\n    while (j >= 0 && !image.get(j, centerI) && stateCount[1] <= maxCount) {\n      stateCount[1]++;\n      j--;\n    }\n    if (j < 0 || stateCount[1] > maxCount) {\n      return NaN;\n    }\n    while (j >= 0 && image.get(j, centerI) && stateCount[0] <= maxCount) {\n      stateCount[0]++;\n      j--;\n    }\n    if (stateCount[0] > maxCount) {\n      return NaN;\n    }\n    j = startJ + 1;\n    while (j < maxJ && image.get(j, centerI)) {\n      stateCount[2]++;\n      j++;\n    }\n    if (j === maxJ) {\n      return NaN;\n    }\n    while (j < maxJ && !image.get(j, centerI) && stateCount[3] < maxCount) {\n      stateCount[3]++;\n      j++;\n    }\n    if (j === maxJ || stateCount[3] >= maxCount) {\n      return NaN;\n    }\n    while (j < maxJ && image.get(j, centerI) && stateCount[4] < maxCount) {\n      stateCount[4]++;\n      j++;\n    }\n    if (stateCount[4] >= maxCount) {\n      return NaN;\n    }\n    // If we found a finder-pattern-like section, but its size is significantly different than\n    // the original, assume it's a false positive\n    var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n    if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= originalStateCountTotal) {\n      return NaN;\n    }\n    return FinderPatternFinder.foundPatternCross(stateCount) ? FinderPatternFinder.centerFromEnd(stateCount, j) : NaN;\n  };\n  /**\n   * <p>This is called when a horizontal scan finds a possible alignment pattern. It will\n   * cross check with a vertical scan, and if successful, will, ah, cross-cross-check\n   * with another horizontal scan. This is needed primarily to locate the real horizontal\n   * center of the pattern in cases of extreme skew.\n   * And then we cross-cross-cross check with another diagonal scan.</p>\n   *\n   * <p>If that succeeds the finder pattern location is added to a list that tracks\n   * the number of times each location has been nearly-matched as a finder pattern.\n   * Each additional find is more evidence that the location is in fact a finder\n   * pattern center\n   *\n   * @param stateCount reading state module counts from horizontal scan\n   * @param i row where finder pattern may be found\n   * @param j end of possible finder pattern in row\n   * @param pureBarcode true if in \"pure barcode\" mode\n   * @return true if a finder pattern candidate was found this time\n   */\n  FinderPatternFinder.prototype.handlePossibleCenter = function (stateCount, i /*int*/, j /*int*/, pureBarcode) {\n    var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n    var centerJ = FinderPatternFinder.centerFromEnd(stateCount, j);\n    var centerI = this.crossCheckVertical(i, /*(int) */Math.floor(centerJ), stateCount[2], stateCountTotal);\n    if (!isNaN(centerI)) {\n      // Re-cross check\n      centerJ = this.crossCheckHorizontal(/*(int) */Math.floor(centerJ), /*(int) */Math.floor(centerI), stateCount[2], stateCountTotal);\n      if (!isNaN(centerJ) && (!pureBarcode || this.crossCheckDiagonal(/*(int) */Math.floor(centerI), /*(int) */Math.floor(centerJ), stateCount[2], stateCountTotal))) {\n        var estimatedModuleSize = stateCountTotal / 7.0;\n        var found = false;\n        var possibleCenters = this.possibleCenters;\n        for (var index = 0, length_1 = possibleCenters.length; index < length_1; index++) {\n          var center = possibleCenters[index];\n          // Look for about the same center and module size:\n          if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {\n            possibleCenters[index] = center.combineEstimate(centerI, centerJ, estimatedModuleSize);\n            found = true;\n            break;\n          }\n        }\n        if (!found) {\n          var point = new FinderPattern(centerJ, centerI, estimatedModuleSize);\n          possibleCenters.push(point);\n          if (this.resultPointCallback !== null && this.resultPointCallback !== undefined) {\n            this.resultPointCallback.foundPossibleResultPoint(point);\n          }\n        }\n        return true;\n      }\n    }\n    return false;\n  };\n  /**\n   * @return number of rows we could safely skip during scanning, based on the first\n   *         two finder patterns that have been located. In some cases their position will\n   *         allow us to infer that the third pattern must lie below a certain point farther\n   *         down in the image.\n   */\n  FinderPatternFinder.prototype.findRowSkip = function () {\n    var e_1, _a;\n    var max = this.possibleCenters.length;\n    if (max <= 1) {\n      return 0;\n    }\n    var firstConfirmedCenter = null;\n    try {\n      for (var _b = __values(this.possibleCenters), _c = _b.next(); !_c.done; _c = _b.next()) {\n        var center = _c.value;\n        if (center.getCount() >= FinderPatternFinder.CENTER_QUORUM) {\n          if (firstConfirmedCenter == null) {\n            firstConfirmedCenter = center;\n          } else {\n            // We have two confirmed centers\n            // How far down can we skip before resuming looking for the next\n            // pattern? In the worst case, only the difference between the\n            // difference in the x / y coordinates of the two centers.\n            // This is the case where you find top left last.\n            this.hasSkipped = true;\n            return /*(int) */Math.floor((Math.abs(firstConfirmedCenter.getX() - center.getX()) - Math.abs(firstConfirmedCenter.getY() - center.getY())) / 2);\n          }\n        }\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    return 0;\n  };\n  /**\n   * @return true iff we have found at least 3 finder patterns that have been detected\n   *         at least {@link #CENTER_QUORUM} times each, and, the estimated module size of the\n   *         candidates is \"pretty similar\"\n   */\n  FinderPatternFinder.prototype.haveMultiplyConfirmedCenters = function () {\n    var e_2, _a, e_3, _b;\n    var confirmedCount = 0;\n    var totalModuleSize = 0.0;\n    var max = this.possibleCenters.length;\n    try {\n      for (var _c = __values(this.possibleCenters), _d = _c.next(); !_d.done; _d = _c.next()) {\n        var pattern = _d.value;\n        if (pattern.getCount() >= FinderPatternFinder.CENTER_QUORUM) {\n          confirmedCount++;\n          totalModuleSize += pattern.getEstimatedModuleSize();\n        }\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    if (confirmedCount < 3) {\n      return false;\n    }\n    // OK, we have at least 3 confirmed centers, but, it's possible that one is a \"false positive\"\n    // and that we need to keep looking. We detect this by asking if the estimated module sizes\n    // vary too much. We arbitrarily say that when the total deviation from average exceeds\n    // 5% of the total module size estimates, it's too much.\n    var average = totalModuleSize / max;\n    var totalDeviation = 0.0;\n    try {\n      for (var _e = __values(this.possibleCenters), _f = _e.next(); !_f.done; _f = _e.next()) {\n        var pattern = _f.value;\n        totalDeviation += Math.abs(pattern.getEstimatedModuleSize() - average);\n      }\n    } catch (e_3_1) {\n      e_3 = {\n        error: e_3_1\n      };\n    } finally {\n      try {\n        if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n      } finally {\n        if (e_3) throw e_3.error;\n      }\n    }\n    return totalDeviation <= 0.05 * totalModuleSize;\n  };\n  /**\n   * @return the 3 best {@link FinderPattern}s from our list of candidates. The \"best\" are\n   *         those that have been detected at least {@link #CENTER_QUORUM} times, and whose module\n   *         size differs from the average among those patterns the least\n   * @throws NotFoundException if 3 such finder patterns do not exist\n   */\n  FinderPatternFinder.prototype.selectBestPatterns = function () {\n    var e_4, _a, e_5, _b;\n    var startSize = this.possibleCenters.length;\n    if (startSize < 3) {\n      // Couldn't find enough finder patterns\n      throw new NotFoundException();\n    }\n    var possibleCenters = this.possibleCenters;\n    var average;\n    // Filter outlier possibilities whose module size is too different\n    if (startSize > 3) {\n      // But we can only afford to do so if we have at least 4 possibilities to choose from\n      var totalModuleSize = 0.0;\n      var square = 0.0;\n      try {\n        for (var _c = __values(this.possibleCenters), _d = _c.next(); !_d.done; _d = _c.next()) {\n          var center = _d.value;\n          var size = center.getEstimatedModuleSize();\n          totalModuleSize += size;\n          square += size * size;\n        }\n      } catch (e_4_1) {\n        e_4 = {\n          error: e_4_1\n        };\n      } finally {\n        try {\n          if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n        } finally {\n          if (e_4) throw e_4.error;\n        }\n      }\n      average = totalModuleSize / startSize;\n      var stdDev = Math.sqrt(square / startSize - average * average);\n      possibleCenters.sort(\n      /**\n       * <p>Orders by furthest from average</p>\n       */\n      // FurthestFromAverageComparator implements Comparator<FinderPattern>\n      function (center1, center2) {\n        var dA = Math.abs(center2.getEstimatedModuleSize() - average);\n        var dB = Math.abs(center1.getEstimatedModuleSize() - average);\n        return dA < dB ? -1 : dA > dB ? 1 : 0;\n      });\n      var limit = Math.max(0.2 * average, stdDev);\n      for (var i = 0; i < possibleCenters.length && possibleCenters.length > 3; i++) {\n        var pattern = possibleCenters[i];\n        if (Math.abs(pattern.getEstimatedModuleSize() - average) > limit) {\n          possibleCenters.splice(i, 1);\n          i--;\n        }\n      }\n    }\n    if (possibleCenters.length > 3) {\n      // Throw away all but those first size candidate points we found.\n      var totalModuleSize = 0.0;\n      try {\n        for (var possibleCenters_1 = __values(possibleCenters), possibleCenters_1_1 = possibleCenters_1.next(); !possibleCenters_1_1.done; possibleCenters_1_1 = possibleCenters_1.next()) {\n          var possibleCenter = possibleCenters_1_1.value;\n          totalModuleSize += possibleCenter.getEstimatedModuleSize();\n        }\n      } catch (e_5_1) {\n        e_5 = {\n          error: e_5_1\n        };\n      } finally {\n        try {\n          if (possibleCenters_1_1 && !possibleCenters_1_1.done && (_b = possibleCenters_1.return)) _b.call(possibleCenters_1);\n        } finally {\n          if (e_5) throw e_5.error;\n        }\n      }\n      average = totalModuleSize / possibleCenters.length;\n      possibleCenters.sort(\n      /**\n       * <p>Orders by {@link FinderPattern#getCount()}, descending.</p>\n       */\n      // CenterComparator implements Comparator<FinderPattern>\n      function (center1, center2) {\n        if (center2.getCount() === center1.getCount()) {\n          var dA = Math.abs(center2.getEstimatedModuleSize() - average);\n          var dB = Math.abs(center1.getEstimatedModuleSize() - average);\n          return dA < dB ? 1 : dA > dB ? -1 : 0;\n        } else {\n          return center2.getCount() - center1.getCount();\n        }\n      });\n      possibleCenters.splice(3); // this is not realy necessary as we only return first 3 anyway\n    }\n    return [possibleCenters[0], possibleCenters[1], possibleCenters[2]];\n  };\n  FinderPatternFinder.CENTER_QUORUM = 2;\n  FinderPatternFinder.MIN_SKIP = 3; // 1 pixel/module times 3 modules/center\n  FinderPatternFinder.MAX_MODULES = 57; // support up to version 10 for mobile clients\n  return FinderPatternFinder;\n}();\nexport default FinderPatternFinder;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "DecodeHintType", "ResultPoint", "FinderPattern", "FinderPatternInfo", "NotFoundException", "FinderPattern<PERSON>inder", "image", "resultPointCallback", "possibleCenters", "crossCheckStateCount", "Int32Array", "prototype", "getImage", "getPossibleCenters", "find", "hints", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "get", "TRY_HARDER", "pureBarcode", "PURE_BARCODE", "maxI", "getHeight", "maxJ", "getWidth", "iSkip", "Math", "floor", "MAX_MODULES", "MIN_SKIP", "stateCount", "currentState", "j", "foundPatternCross", "confirmed", "handlePossibleCenter", "hasSkipped", "haveMultiplyConfirmedCenters", "rowSkip", "findRowSkip", "patternInfo", "selectBestPatterns", "orderBestPatterns", "centerFromEnd", "end", "totalModuleSize", "count", "moduleSize", "max<PERSON><PERSON>ce", "abs", "getCrossCheckStateCount", "crossCheckDiagonal", "startI", "centerJ", "maxCount", "originalStateCountTotal", "stateCountTotal", "crossCheckVertical", "NaN", "crossCheckHorizontal", "startJ", "centerI", "isNaN", "estimatedModuleSize", "found", "index", "length_1", "center", "aboutEquals", "combineEstimate", "point", "push", "foundPossibleResultPoint", "e_1", "_a", "max", "firstConfirmedCenter", "_b", "_c", "getCount", "CENTER_QUORUM", "getX", "getY", "e_1_1", "error", "return", "e_2", "e_3", "confirmedCount", "_d", "pattern", "getEstimatedModuleSize", "e_2_1", "average", "totalDeviation", "_e", "_f", "e_3_1", "e_4", "e_5", "startSize", "square", "size", "e_4_1", "stdDev", "sqrt", "sort", "center1", "center2", "dA", "dB", "limit", "splice", "possibleCenters_1", "possibleCenters_1_1", "possibleCenter", "e_5_1"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/qrcode/detector/FinderPatternFinder.js"], "sourcesContent": ["/*\n * Copyright 2007 ZXing authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/*namespace com.google.zxing.qrcode.detector {*/\nimport DecodeHintType from '../../DecodeHintType';\nimport ResultPoint from '../../ResultPoint';\nimport FinderPattern from './FinderPattern';\nimport FinderPatternInfo from './FinderPatternInfo';\nimport NotFoundException from '../../NotFoundException';\n/*import java.io.Serializable;*/\n/*import java.util.ArrayList;*/\n/*import java.util.Collections;*/\n/*import java.util.Comparator;*/\n/*import java.util.List;*/\n/*import java.util.Map;*/\n/**\n * <p>This class attempts to find finder patterns in a QR Code. Finder patterns are the square\n * markers at three corners of a QR Code.</p>\n *\n * <p>This class is thread-safe but not reentrant. Each thread must allocate its own object.\n *\n * <AUTHOR> Owen\n */\nvar FinderPatternFinder = /** @class */ (function () {\n    /**\n     * <p>Creates a finder that will search the image for three finder patterns.</p>\n     *\n     * @param image image to search\n     */\n    // public constructor(image: BitMatrix) {\n    //   this(image, null)\n    // }\n    function FinderPatternFinder(image, resultPointCallback) {\n        this.image = image;\n        this.resultPointCallback = resultPointCallback;\n        this.possibleCenters = [];\n        this.crossCheckStateCount = new Int32Array(5);\n        this.resultPointCallback = resultPointCallback;\n    }\n    FinderPatternFinder.prototype.getImage = function () {\n        return this.image;\n    };\n    FinderPatternFinder.prototype.getPossibleCenters = function () {\n        return this.possibleCenters;\n    };\n    FinderPatternFinder.prototype.find = function (hints) {\n        var tryHarder = (hints !== null && hints !== undefined) && undefined !== hints.get(DecodeHintType.TRY_HARDER);\n        var pureBarcode = (hints !== null && hints !== undefined) && undefined !== hints.get(DecodeHintType.PURE_BARCODE);\n        var image = this.image;\n        var maxI = image.getHeight();\n        var maxJ = image.getWidth();\n        // We are looking for black/white/black/white/black modules in\n        // 1:1:3:1:1 ratio; this tracks the number of such modules seen so far\n        // Let's assume that the maximum version QR Code we support takes up 1/4 the height of the\n        // image, and then account for the center being 3 modules in size. This gives the smallest\n        // number of pixels the center could be, so skip this often. When trying harder, look for all\n        // QR versions regardless of how dense they are.\n        var iSkip = Math.floor((3 * maxI) / (4 * FinderPatternFinder.MAX_MODULES));\n        if (iSkip < FinderPatternFinder.MIN_SKIP || tryHarder) {\n            iSkip = FinderPatternFinder.MIN_SKIP;\n        }\n        var done = false;\n        var stateCount = new Int32Array(5);\n        for (var i = iSkip - 1; i < maxI && !done; i += iSkip) {\n            // Get a row of black/white values\n            stateCount[0] = 0;\n            stateCount[1] = 0;\n            stateCount[2] = 0;\n            stateCount[3] = 0;\n            stateCount[4] = 0;\n            var currentState = 0;\n            for (var j = 0; j < maxJ; j++) {\n                if (image.get(j, i)) {\n                    // Black pixel\n                    if ((currentState & 1) === 1) { // Counting white pixels\n                        currentState++;\n                    }\n                    stateCount[currentState]++;\n                }\n                else { // White pixel\n                    if ((currentState & 1) === 0) { // Counting black pixels\n                        if (currentState === 4) { // A winner?\n                            if (FinderPatternFinder.foundPatternCross(stateCount)) { // Yes\n                                var confirmed = this.handlePossibleCenter(stateCount, i, j, pureBarcode);\n                                if (confirmed === true) {\n                                    // Start examining every other line. Checking each line turned out to be too\n                                    // expensive and didn't improve performance.\n                                    iSkip = 2;\n                                    if (this.hasSkipped === true) {\n                                        done = this.haveMultiplyConfirmedCenters();\n                                    }\n                                    else {\n                                        var rowSkip = this.findRowSkip();\n                                        if (rowSkip > stateCount[2]) {\n                                            // Skip rows between row of lower confirmed center\n                                            // and top of presumed third confirmed center\n                                            // but back up a bit to get a full chance of detecting\n                                            // it, entire width of center of finder pattern\n                                            // Skip by rowSkip, but back off by stateCount[2] (size of last center\n                                            // of pattern we saw) to be conservative, and also back off by iSkip which\n                                            // is about to be re-added\n                                            i += rowSkip - stateCount[2] - iSkip;\n                                            j = maxJ - 1;\n                                        }\n                                    }\n                                }\n                                else {\n                                    stateCount[0] = stateCount[2];\n                                    stateCount[1] = stateCount[3];\n                                    stateCount[2] = stateCount[4];\n                                    stateCount[3] = 1;\n                                    stateCount[4] = 0;\n                                    currentState = 3;\n                                    continue;\n                                }\n                                // Clear state to start looking again\n                                currentState = 0;\n                                stateCount[0] = 0;\n                                stateCount[1] = 0;\n                                stateCount[2] = 0;\n                                stateCount[3] = 0;\n                                stateCount[4] = 0;\n                            }\n                            else { // No, shift counts back by two\n                                stateCount[0] = stateCount[2];\n                                stateCount[1] = stateCount[3];\n                                stateCount[2] = stateCount[4];\n                                stateCount[3] = 1;\n                                stateCount[4] = 0;\n                                currentState = 3;\n                            }\n                        }\n                        else {\n                            stateCount[++currentState]++;\n                        }\n                    }\n                    else { // Counting white pixels\n                        stateCount[currentState]++;\n                    }\n                }\n            }\n            if (FinderPatternFinder.foundPatternCross(stateCount)) {\n                var confirmed = this.handlePossibleCenter(stateCount, i, maxJ, pureBarcode);\n                if (confirmed === true) {\n                    iSkip = stateCount[0];\n                    if (this.hasSkipped) {\n                        // Found a third one\n                        done = this.haveMultiplyConfirmedCenters();\n                    }\n                }\n            }\n        }\n        var patternInfo = this.selectBestPatterns();\n        ResultPoint.orderBestPatterns(patternInfo);\n        return new FinderPatternInfo(patternInfo);\n    };\n    /**\n     * Given a count of black/white/black/white/black pixels just seen and an end position,\n     * figures the location of the center of this run.\n     */\n    FinderPatternFinder.centerFromEnd = function (stateCount, end /*int*/) {\n        return (end - stateCount[4] - stateCount[3]) - stateCount[2] / 2.0;\n    };\n    /**\n     * @param stateCount count of black/white/black/white/black pixels just read\n     * @return true iff the proportions of the counts is close enough to the 1/1/3/1/1 ratios\n     *         used by finder patterns to be considered a match\n     */\n    FinderPatternFinder.foundPatternCross = function (stateCount) {\n        var totalModuleSize = 0;\n        for (var i = 0; i < 5; i++) {\n            var count = stateCount[i];\n            if (count === 0) {\n                return false;\n            }\n            totalModuleSize += count;\n        }\n        if (totalModuleSize < 7) {\n            return false;\n        }\n        var moduleSize = totalModuleSize / 7.0;\n        var maxVariance = moduleSize / 2.0;\n        // Allow less than 50% variance from 1-1-3-1-1 proportions\n        return Math.abs(moduleSize - stateCount[0]) < maxVariance &&\n            Math.abs(moduleSize - stateCount[1]) < maxVariance &&\n            Math.abs(3.0 * moduleSize - stateCount[2]) < 3 * maxVariance &&\n            Math.abs(moduleSize - stateCount[3]) < maxVariance &&\n            Math.abs(moduleSize - stateCount[4]) < maxVariance;\n    };\n    FinderPatternFinder.prototype.getCrossCheckStateCount = function () {\n        var crossCheckStateCount = this.crossCheckStateCount;\n        crossCheckStateCount[0] = 0;\n        crossCheckStateCount[1] = 0;\n        crossCheckStateCount[2] = 0;\n        crossCheckStateCount[3] = 0;\n        crossCheckStateCount[4] = 0;\n        return crossCheckStateCount;\n    };\n    /**\n     * After a vertical and horizontal scan finds a potential finder pattern, this method\n     * \"cross-cross-cross-checks\" by scanning down diagonally through the center of the possible\n     * finder pattern to see if the same proportion is detected.\n     *\n     * @param startI row where a finder pattern was detected\n     * @param centerJ center of the section that appears to cross a finder pattern\n     * @param maxCount maximum reasonable number of modules that should be\n     *  observed in any reading state, based on the results of the horizontal scan\n     * @param originalStateCountTotal The original state count total.\n     * @return true if proportions are withing expected limits\n     */\n    FinderPatternFinder.prototype.crossCheckDiagonal = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n        var stateCount = this.getCrossCheckStateCount();\n        // Start counting up, left from center finding black center mass\n        var i = 0;\n        var image = this.image;\n        while (startI >= i && centerJ >= i && image.get(centerJ - i, startI - i)) {\n            stateCount[2]++;\n            i++;\n        }\n        if (startI < i || centerJ < i) {\n            return false;\n        }\n        // Continue up, left finding white space\n        while (startI >= i && centerJ >= i && !image.get(centerJ - i, startI - i) &&\n            stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            i++;\n        }\n        // If already too many modules in this state or ran off the edge:\n        if (startI < i || centerJ < i || stateCount[1] > maxCount) {\n            return false;\n        }\n        // Continue up, left finding black border\n        while (startI >= i && centerJ >= i && image.get(centerJ - i, startI - i) &&\n            stateCount[0] <= maxCount) {\n            stateCount[0]++;\n            i++;\n        }\n        if (stateCount[0] > maxCount) {\n            return false;\n        }\n        var maxI = image.getHeight();\n        var maxJ = image.getWidth();\n        // Now also count down, right from center\n        i = 1;\n        while (startI + i < maxI && centerJ + i < maxJ && image.get(centerJ + i, startI + i)) {\n            stateCount[2]++;\n            i++;\n        }\n        // Ran off the edge?\n        if (startI + i >= maxI || centerJ + i >= maxJ) {\n            return false;\n        }\n        while (startI + i < maxI && centerJ + i < maxJ && !image.get(centerJ + i, startI + i) &&\n            stateCount[3] < maxCount) {\n            stateCount[3]++;\n            i++;\n        }\n        if (startI + i >= maxI || centerJ + i >= maxJ || stateCount[3] >= maxCount) {\n            return false;\n        }\n        while (startI + i < maxI && centerJ + i < maxJ && image.get(centerJ + i, startI + i) &&\n            stateCount[4] < maxCount) {\n            stateCount[4]++;\n            i++;\n        }\n        if (stateCount[4] >= maxCount) {\n            return false;\n        }\n        // If we found a finder-pattern-like section, but its size is more than 100% different than\n        // the original, assume it's a false positive\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] + stateCount[4];\n        return Math.abs(stateCountTotal - originalStateCountTotal) < 2 * originalStateCountTotal &&\n            FinderPatternFinder.foundPatternCross(stateCount);\n    };\n    /**\n     * <p>After a horizontal scan finds a potential finder pattern, this method\n     * \"cross-checks\" by scanning down vertically through the center of the possible\n     * finder pattern to see if the same proportion is detected.</p>\n     *\n     * @param startI row where a finder pattern was detected\n     * @param centerJ center of the section that appears to cross a finder pattern\n     * @param maxCount maximum reasonable number of modules that should be\n     * observed in any reading state, based on the results of the horizontal scan\n     * @return vertical center of finder pattern, or {@link Float#NaN} if not found\n     */\n    FinderPatternFinder.prototype.crossCheckVertical = function (startI /*int*/, centerJ /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n        var image = this.image;\n        var maxI = image.getHeight();\n        var stateCount = this.getCrossCheckStateCount();\n        // Start counting up from center\n        var i = startI;\n        while (i >= 0 && image.get(centerJ, i)) {\n            stateCount[2]++;\n            i--;\n        }\n        if (i < 0) {\n            return NaN;\n        }\n        while (i >= 0 && !image.get(centerJ, i) && stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            i--;\n        }\n        // If already too many modules in this state or ran off the edge:\n        if (i < 0 || stateCount[1] > maxCount) {\n            return NaN;\n        }\n        while (i >= 0 && image.get(centerJ, i) && stateCount[0] <= maxCount) {\n            stateCount[0]++;\n            i--;\n        }\n        if (stateCount[0] > maxCount) {\n            return NaN;\n        }\n        // Now also count down from center\n        i = startI + 1;\n        while (i < maxI && image.get(centerJ, i)) {\n            stateCount[2]++;\n            i++;\n        }\n        if (i === maxI) {\n            return NaN;\n        }\n        while (i < maxI && !image.get(centerJ, i) && stateCount[3] < maxCount) {\n            stateCount[3]++;\n            i++;\n        }\n        if (i === maxI || stateCount[3] >= maxCount) {\n            return NaN;\n        }\n        while (i < maxI && image.get(centerJ, i) && stateCount[4] < maxCount) {\n            stateCount[4]++;\n            i++;\n        }\n        if (stateCount[4] >= maxCount) {\n            return NaN;\n        }\n        // If we found a finder-pattern-like section, but its size is more than 40% different than\n        // the original, assume it's a false positive\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] +\n            stateCount[4];\n        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= 2 * originalStateCountTotal) {\n            return NaN;\n        }\n        return FinderPatternFinder.foundPatternCross(stateCount) ? FinderPatternFinder.centerFromEnd(stateCount, i) : NaN;\n    };\n    /**\n     * <p>Like {@link #crossCheckVertical(int, int, int, int)}, and in fact is basically identical,\n     * except it reads horizontally instead of vertically. This is used to cross-cross\n     * check a vertical cross check and locate the real center of the alignment pattern.</p>\n     */\n    FinderPatternFinder.prototype.crossCheckHorizontal = function (startJ /*int*/, centerI /*int*/, maxCount /*int*/, originalStateCountTotal /*int*/) {\n        var image = this.image;\n        var maxJ = image.getWidth();\n        var stateCount = this.getCrossCheckStateCount();\n        var j = startJ;\n        while (j >= 0 && image.get(j, centerI)) {\n            stateCount[2]++;\n            j--;\n        }\n        if (j < 0) {\n            return NaN;\n        }\n        while (j >= 0 && !image.get(j, centerI) && stateCount[1] <= maxCount) {\n            stateCount[1]++;\n            j--;\n        }\n        if (j < 0 || stateCount[1] > maxCount) {\n            return NaN;\n        }\n        while (j >= 0 && image.get(j, centerI) && stateCount[0] <= maxCount) {\n            stateCount[0]++;\n            j--;\n        }\n        if (stateCount[0] > maxCount) {\n            return NaN;\n        }\n        j = startJ + 1;\n        while (j < maxJ && image.get(j, centerI)) {\n            stateCount[2]++;\n            j++;\n        }\n        if (j === maxJ) {\n            return NaN;\n        }\n        while (j < maxJ && !image.get(j, centerI) && stateCount[3] < maxCount) {\n            stateCount[3]++;\n            j++;\n        }\n        if (j === maxJ || stateCount[3] >= maxCount) {\n            return NaN;\n        }\n        while (j < maxJ && image.get(j, centerI) && stateCount[4] < maxCount) {\n            stateCount[4]++;\n            j++;\n        }\n        if (stateCount[4] >= maxCount) {\n            return NaN;\n        }\n        // If we found a finder-pattern-like section, but its size is significantly different than\n        // the original, assume it's a false positive\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] +\n            stateCount[4];\n        if (5 * Math.abs(stateCountTotal - originalStateCountTotal) >= originalStateCountTotal) {\n            return NaN;\n        }\n        return FinderPatternFinder.foundPatternCross(stateCount) ? FinderPatternFinder.centerFromEnd(stateCount, j) : NaN;\n    };\n    /**\n     * <p>This is called when a horizontal scan finds a possible alignment pattern. It will\n     * cross check with a vertical scan, and if successful, will, ah, cross-cross-check\n     * with another horizontal scan. This is needed primarily to locate the real horizontal\n     * center of the pattern in cases of extreme skew.\n     * And then we cross-cross-cross check with another diagonal scan.</p>\n     *\n     * <p>If that succeeds the finder pattern location is added to a list that tracks\n     * the number of times each location has been nearly-matched as a finder pattern.\n     * Each additional find is more evidence that the location is in fact a finder\n     * pattern center\n     *\n     * @param stateCount reading state module counts from horizontal scan\n     * @param i row where finder pattern may be found\n     * @param j end of possible finder pattern in row\n     * @param pureBarcode true if in \"pure barcode\" mode\n     * @return true if a finder pattern candidate was found this time\n     */\n    FinderPatternFinder.prototype.handlePossibleCenter = function (stateCount, i /*int*/, j /*int*/, pureBarcode) {\n        var stateCountTotal = stateCount[0] + stateCount[1] + stateCount[2] + stateCount[3] +\n            stateCount[4];\n        var centerJ = FinderPatternFinder.centerFromEnd(stateCount, j);\n        var centerI = this.crossCheckVertical(i, /*(int) */ Math.floor(centerJ), stateCount[2], stateCountTotal);\n        if (!isNaN(centerI)) {\n            // Re-cross check\n            centerJ = this.crossCheckHorizontal(/*(int) */ Math.floor(centerJ), /*(int) */ Math.floor(centerI), stateCount[2], stateCountTotal);\n            if (!isNaN(centerJ) &&\n                (!pureBarcode || this.crossCheckDiagonal(/*(int) */ Math.floor(centerI), /*(int) */ Math.floor(centerJ), stateCount[2], stateCountTotal))) {\n                var estimatedModuleSize = stateCountTotal / 7.0;\n                var found = false;\n                var possibleCenters = this.possibleCenters;\n                for (var index = 0, length_1 = possibleCenters.length; index < length_1; index++) {\n                    var center = possibleCenters[index];\n                    // Look for about the same center and module size:\n                    if (center.aboutEquals(estimatedModuleSize, centerI, centerJ)) {\n                        possibleCenters[index] = center.combineEstimate(centerI, centerJ, estimatedModuleSize);\n                        found = true;\n                        break;\n                    }\n                }\n                if (!found) {\n                    var point = new FinderPattern(centerJ, centerI, estimatedModuleSize);\n                    possibleCenters.push(point);\n                    if (this.resultPointCallback !== null && this.resultPointCallback !== undefined) {\n                        this.resultPointCallback.foundPossibleResultPoint(point);\n                    }\n                }\n                return true;\n            }\n        }\n        return false;\n    };\n    /**\n     * @return number of rows we could safely skip during scanning, based on the first\n     *         two finder patterns that have been located. In some cases their position will\n     *         allow us to infer that the third pattern must lie below a certain point farther\n     *         down in the image.\n     */\n    FinderPatternFinder.prototype.findRowSkip = function () {\n        var e_1, _a;\n        var max = this.possibleCenters.length;\n        if (max <= 1) {\n            return 0;\n        }\n        var firstConfirmedCenter = null;\n        try {\n            for (var _b = __values(this.possibleCenters), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var center = _c.value;\n                if (center.getCount() >= FinderPatternFinder.CENTER_QUORUM) {\n                    if (firstConfirmedCenter == null) {\n                        firstConfirmedCenter = center;\n                    }\n                    else {\n                        // We have two confirmed centers\n                        // How far down can we skip before resuming looking for the next\n                        // pattern? In the worst case, only the difference between the\n                        // difference in the x / y coordinates of the two centers.\n                        // This is the case where you find top left last.\n                        this.hasSkipped = true;\n                        return /*(int) */ Math.floor((Math.abs(firstConfirmedCenter.getX() - center.getX()) -\n                            Math.abs(firstConfirmedCenter.getY() - center.getY())) / 2);\n                    }\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return 0;\n    };\n    /**\n     * @return true iff we have found at least 3 finder patterns that have been detected\n     *         at least {@link #CENTER_QUORUM} times each, and, the estimated module size of the\n     *         candidates is \"pretty similar\"\n     */\n    FinderPatternFinder.prototype.haveMultiplyConfirmedCenters = function () {\n        var e_2, _a, e_3, _b;\n        var confirmedCount = 0;\n        var totalModuleSize = 0.0;\n        var max = this.possibleCenters.length;\n        try {\n            for (var _c = __values(this.possibleCenters), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var pattern = _d.value;\n                if (pattern.getCount() >= FinderPatternFinder.CENTER_QUORUM) {\n                    confirmedCount++;\n                    totalModuleSize += pattern.getEstimatedModuleSize();\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        if (confirmedCount < 3) {\n            return false;\n        }\n        // OK, we have at least 3 confirmed centers, but, it's possible that one is a \"false positive\"\n        // and that we need to keep looking. We detect this by asking if the estimated module sizes\n        // vary too much. We arbitrarily say that when the total deviation from average exceeds\n        // 5% of the total module size estimates, it's too much.\n        var average = totalModuleSize / max;\n        var totalDeviation = 0.0;\n        try {\n            for (var _e = __values(this.possibleCenters), _f = _e.next(); !_f.done; _f = _e.next()) {\n                var pattern = _f.value;\n                totalDeviation += Math.abs(pattern.getEstimatedModuleSize() - average);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_f && !_f.done && (_b = _e.return)) _b.call(_e);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        return totalDeviation <= 0.05 * totalModuleSize;\n    };\n    /**\n     * @return the 3 best {@link FinderPattern}s from our list of candidates. The \"best\" are\n     *         those that have been detected at least {@link #CENTER_QUORUM} times, and whose module\n     *         size differs from the average among those patterns the least\n     * @throws NotFoundException if 3 such finder patterns do not exist\n     */\n    FinderPatternFinder.prototype.selectBestPatterns = function () {\n        var e_4, _a, e_5, _b;\n        var startSize = this.possibleCenters.length;\n        if (startSize < 3) {\n            // Couldn't find enough finder patterns\n            throw new NotFoundException();\n        }\n        var possibleCenters = this.possibleCenters;\n        var average;\n        // Filter outlier possibilities whose module size is too different\n        if (startSize > 3) {\n            // But we can only afford to do so if we have at least 4 possibilities to choose from\n            var totalModuleSize = 0.0;\n            var square = 0.0;\n            try {\n                for (var _c = __values(this.possibleCenters), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var center = _d.value;\n                    var size = center.getEstimatedModuleSize();\n                    totalModuleSize += size;\n                    square += size * size;\n                }\n            }\n            catch (e_4_1) { e_4 = { error: e_4_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_4) throw e_4.error; }\n            }\n            average = totalModuleSize / startSize;\n            var stdDev = Math.sqrt(square / startSize - average * average);\n            possibleCenters.sort(\n            /**\n             * <p>Orders by furthest from average</p>\n             */\n            // FurthestFromAverageComparator implements Comparator<FinderPattern>\n            function (center1, center2) {\n                var dA = Math.abs(center2.getEstimatedModuleSize() - average);\n                var dB = Math.abs(center1.getEstimatedModuleSize() - average);\n                return dA < dB ? -1 : dA > dB ? 1 : 0;\n            });\n            var limit = Math.max(0.2 * average, stdDev);\n            for (var i = 0; i < possibleCenters.length && possibleCenters.length > 3; i++) {\n                var pattern = possibleCenters[i];\n                if (Math.abs(pattern.getEstimatedModuleSize() - average) > limit) {\n                    possibleCenters.splice(i, 1);\n                    i--;\n                }\n            }\n        }\n        if (possibleCenters.length > 3) {\n            // Throw away all but those first size candidate points we found.\n            var totalModuleSize = 0.0;\n            try {\n                for (var possibleCenters_1 = __values(possibleCenters), possibleCenters_1_1 = possibleCenters_1.next(); !possibleCenters_1_1.done; possibleCenters_1_1 = possibleCenters_1.next()) {\n                    var possibleCenter = possibleCenters_1_1.value;\n                    totalModuleSize += possibleCenter.getEstimatedModuleSize();\n                }\n            }\n            catch (e_5_1) { e_5 = { error: e_5_1 }; }\n            finally {\n                try {\n                    if (possibleCenters_1_1 && !possibleCenters_1_1.done && (_b = possibleCenters_1.return)) _b.call(possibleCenters_1);\n                }\n                finally { if (e_5) throw e_5.error; }\n            }\n            average = totalModuleSize / possibleCenters.length;\n            possibleCenters.sort(\n            /**\n             * <p>Orders by {@link FinderPattern#getCount()}, descending.</p>\n             */\n            // CenterComparator implements Comparator<FinderPattern>\n            function (center1, center2) {\n                if (center2.getCount() === center1.getCount()) {\n                    var dA = Math.abs(center2.getEstimatedModuleSize() - average);\n                    var dB = Math.abs(center1.getEstimatedModuleSize() - average);\n                    return dA < dB ? 1 : dA > dB ? -1 : 0;\n                }\n                else {\n                    return center2.getCount() - center1.getCount();\n                }\n            });\n            possibleCenters.splice(3); // this is not realy necessary as we only return first 3 anyway\n        }\n        return [\n            possibleCenters[0],\n            possibleCenters[1],\n            possibleCenters[2]\n        ];\n    };\n    FinderPatternFinder.CENTER_QUORUM = 2;\n    FinderPatternFinder.MIN_SKIP = 3; // 1 pixel/module times 3 modules/center\n    FinderPatternFinder.MAX_MODULES = 57; // support up to version 10 for mobile clients\n    return FinderPatternFinder;\n}());\nexport default FinderPatternFinder;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA,OAAOW,cAAc,MAAM,sBAAsB;AACjD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,mBAAmB,GAAG,aAAe,YAAY;EACjD;AACJ;AACA;AACA;AACA;EACI;EACA;EACA;EACA,SAASA,mBAAmBA,CAACC,KAAK,EAAEC,mBAAmB,EAAE;IACrD,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,oBAAoB,GAAG,IAAIC,UAAU,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACH,mBAAmB,GAAGA,mBAAmB;EAClD;EACAF,mBAAmB,CAACM,SAAS,CAACC,QAAQ,GAAG,YAAY;IACjD,OAAO,IAAI,CAACN,KAAK;EACrB,CAAC;EACDD,mBAAmB,CAACM,SAAS,CAACE,kBAAkB,GAAG,YAAY;IAC3D,OAAO,IAAI,CAACL,eAAe;EAC/B,CAAC;EACDH,mBAAmB,CAACM,SAAS,CAACG,IAAI,GAAG,UAAUC,KAAK,EAAE;IAClD,IAAIC,SAAS,GAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAKA,SAAS,KAAKF,KAAK,CAACG,GAAG,CAAClB,cAAc,CAACmB,UAAU,CAAC;IAC7G,IAAIC,WAAW,GAAIL,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAKA,SAAS,KAAKF,KAAK,CAACG,GAAG,CAAClB,cAAc,CAACqB,YAAY,CAAC;IACjH,IAAIf,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIgB,IAAI,GAAGhB,KAAK,CAACiB,SAAS,CAAC,CAAC;IAC5B,IAAIC,IAAI,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,CAAC;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA,IAAIC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAE,CAAC,GAAGN,IAAI,IAAK,CAAC,GAAGjB,mBAAmB,CAACwB,WAAW,CAAC,CAAC;IAC1E,IAAIH,KAAK,GAAGrB,mBAAmB,CAACyB,QAAQ,IAAId,SAAS,EAAE;MACnDU,KAAK,GAAGrB,mBAAmB,CAACyB,QAAQ;IACxC;IACA,IAAIhC,IAAI,GAAG,KAAK;IAChB,IAAIiC,UAAU,GAAG,IAAIrB,UAAU,CAAC,CAAC,CAAC;IAClC,KAAK,IAAIjB,CAAC,GAAGiC,KAAK,GAAG,CAAC,EAAEjC,CAAC,GAAG6B,IAAI,IAAI,CAACxB,IAAI,EAAEL,CAAC,IAAIiC,KAAK,EAAE;MACnD;MACAK,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;MACjB,IAAIC,YAAY,GAAG,CAAC;MACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,IAAI,EAAES,CAAC,EAAE,EAAE;QAC3B,IAAI3B,KAAK,CAACY,GAAG,CAACe,CAAC,EAAExC,CAAC,CAAC,EAAE;UACjB;UACA,IAAI,CAACuC,YAAY,GAAG,CAAC,MAAM,CAAC,EAAE;YAAE;YAC5BA,YAAY,EAAE;UAClB;UACAD,UAAU,CAACC,YAAY,CAAC,EAAE;QAC9B,CAAC,MACI;UAAE;UACH,IAAI,CAACA,YAAY,GAAG,CAAC,MAAM,CAAC,EAAE;YAAE;YAC5B,IAAIA,YAAY,KAAK,CAAC,EAAE;cAAE;cACtB,IAAI3B,mBAAmB,CAAC6B,iBAAiB,CAACH,UAAU,CAAC,EAAE;gBAAE;gBACrD,IAAII,SAAS,GAAG,IAAI,CAACC,oBAAoB,CAACL,UAAU,EAAEtC,CAAC,EAAEwC,CAAC,EAAEb,WAAW,CAAC;gBACxE,IAAIe,SAAS,KAAK,IAAI,EAAE;kBACpB;kBACA;kBACAT,KAAK,GAAG,CAAC;kBACT,IAAI,IAAI,CAACW,UAAU,KAAK,IAAI,EAAE;oBAC1BvC,IAAI,GAAG,IAAI,CAACwC,4BAA4B,CAAC,CAAC;kBAC9C,CAAC,MACI;oBACD,IAAIC,OAAO,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;oBAChC,IAAID,OAAO,GAAGR,UAAU,CAAC,CAAC,CAAC,EAAE;sBACzB;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA;sBACAtC,CAAC,IAAI8C,OAAO,GAAGR,UAAU,CAAC,CAAC,CAAC,GAAGL,KAAK;sBACpCO,CAAC,GAAGT,IAAI,GAAG,CAAC;oBAChB;kBACJ;gBACJ,CAAC,MACI;kBACDO,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;kBAC7BA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;kBAC7BA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;kBAC7BA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;kBACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;kBACjBC,YAAY,GAAG,CAAC;kBAChB;gBACJ;gBACA;gBACAA,YAAY,GAAG,CAAC;gBAChBD,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;gBACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;gBACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;gBACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;gBACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;cACrB,CAAC,MACI;gBAAE;gBACHA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;gBAC7BA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;gBAC7BA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;gBAC7BA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;gBACjBA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;gBACjBC,YAAY,GAAG,CAAC;cACpB;YACJ,CAAC,MACI;cACDD,UAAU,CAAC,EAAEC,YAAY,CAAC,EAAE;YAChC;UACJ,CAAC,MACI;YAAE;YACHD,UAAU,CAACC,YAAY,CAAC,EAAE;UAC9B;QACJ;MACJ;MACA,IAAI3B,mBAAmB,CAAC6B,iBAAiB,CAACH,UAAU,CAAC,EAAE;QACnD,IAAII,SAAS,GAAG,IAAI,CAACC,oBAAoB,CAACL,UAAU,EAAEtC,CAAC,EAAE+B,IAAI,EAAEJ,WAAW,CAAC;QAC3E,IAAIe,SAAS,KAAK,IAAI,EAAE;UACpBT,KAAK,GAAGK,UAAU,CAAC,CAAC,CAAC;UACrB,IAAI,IAAI,CAACM,UAAU,EAAE;YACjB;YACAvC,IAAI,GAAG,IAAI,CAACwC,4BAA4B,CAAC,CAAC;UAC9C;QACJ;MACJ;IACJ;IACA,IAAIG,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC3CzC,WAAW,CAAC0C,iBAAiB,CAACF,WAAW,CAAC;IAC1C,OAAO,IAAItC,iBAAiB,CAACsC,WAAW,CAAC;EAC7C,CAAC;EACD;AACJ;AACA;AACA;EACIpC,mBAAmB,CAACuC,aAAa,GAAG,UAAUb,UAAU,EAAEc,GAAG,CAAC,SAAS;IACnE,OAAQA,GAAG,GAAGd,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAIA,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG;EACtE,CAAC;EACD;AACJ;AACA;AACA;AACA;EACI1B,mBAAmB,CAAC6B,iBAAiB,GAAG,UAAUH,UAAU,EAAE;IAC1D,IAAIe,eAAe,GAAG,CAAC;IACvB,KAAK,IAAIrD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACxB,IAAIsD,KAAK,GAAGhB,UAAU,CAACtC,CAAC,CAAC;MACzB,IAAIsD,KAAK,KAAK,CAAC,EAAE;QACb,OAAO,KAAK;MAChB;MACAD,eAAe,IAAIC,KAAK;IAC5B;IACA,IAAID,eAAe,GAAG,CAAC,EAAE;MACrB,OAAO,KAAK;IAChB;IACA,IAAIE,UAAU,GAAGF,eAAe,GAAG,GAAG;IACtC,IAAIG,WAAW,GAAGD,UAAU,GAAG,GAAG;IAClC;IACA,OAAOrB,IAAI,CAACuB,GAAG,CAACF,UAAU,GAAGjB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGkB,WAAW,IACrDtB,IAAI,CAACuB,GAAG,CAACF,UAAU,GAAGjB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGkB,WAAW,IAClDtB,IAAI,CAACuB,GAAG,CAAC,GAAG,GAAGF,UAAU,GAAGjB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGkB,WAAW,IAC5DtB,IAAI,CAACuB,GAAG,CAACF,UAAU,GAAGjB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGkB,WAAW,IAClDtB,IAAI,CAACuB,GAAG,CAACF,UAAU,GAAGjB,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGkB,WAAW;EAC1D,CAAC;EACD5C,mBAAmB,CAACM,SAAS,CAACwC,uBAAuB,GAAG,YAAY;IAChE,IAAI1C,oBAAoB,GAAG,IAAI,CAACA,oBAAoB;IACpDA,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3BA,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3BA,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3BA,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3BA,oBAAoB,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3B,OAAOA,oBAAoB;EAC/B,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIJ,mBAAmB,CAACM,SAAS,CAACyC,kBAAkB,GAAG,UAAUC,MAAM,CAAC,SAASC,OAAO,CAAC,SAASC,QAAQ,CAAC,SAASC,uBAAuB,CAAC,SAAS;IAC7I,IAAIzB,UAAU,GAAG,IAAI,CAACoB,uBAAuB,CAAC,CAAC;IAC/C;IACA,IAAI1D,CAAC,GAAG,CAAC;IACT,IAAIa,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,OAAO+C,MAAM,IAAI5D,CAAC,IAAI6D,OAAO,IAAI7D,CAAC,IAAIa,KAAK,CAACY,GAAG,CAACoC,OAAO,GAAG7D,CAAC,EAAE4D,MAAM,GAAG5D,CAAC,CAAC,EAAE;MACtEsC,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAI4D,MAAM,GAAG5D,CAAC,IAAI6D,OAAO,GAAG7D,CAAC,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA;IACA,OAAO4D,MAAM,IAAI5D,CAAC,IAAI6D,OAAO,IAAI7D,CAAC,IAAI,CAACa,KAAK,CAACY,GAAG,CAACoC,OAAO,GAAG7D,CAAC,EAAE4D,MAAM,GAAG5D,CAAC,CAAC,IACrEsC,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MAC3BxB,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA;IACA,IAAI4D,MAAM,GAAG5D,CAAC,IAAI6D,OAAO,GAAG7D,CAAC,IAAIsC,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MACvD,OAAO,KAAK;IAChB;IACA;IACA,OAAOF,MAAM,IAAI5D,CAAC,IAAI6D,OAAO,IAAI7D,CAAC,IAAIa,KAAK,CAACY,GAAG,CAACoC,OAAO,GAAG7D,CAAC,EAAE4D,MAAM,GAAG5D,CAAC,CAAC,IACpEsC,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MAC3BxB,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAIsC,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MAC1B,OAAO,KAAK;IAChB;IACA,IAAIjC,IAAI,GAAGhB,KAAK,CAACiB,SAAS,CAAC,CAAC;IAC5B,IAAIC,IAAI,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,CAAC;IAC3B;IACAhC,CAAC,GAAG,CAAC;IACL,OAAO4D,MAAM,GAAG5D,CAAC,GAAG6B,IAAI,IAAIgC,OAAO,GAAG7D,CAAC,GAAG+B,IAAI,IAAIlB,KAAK,CAACY,GAAG,CAACoC,OAAO,GAAG7D,CAAC,EAAE4D,MAAM,GAAG5D,CAAC,CAAC,EAAE;MAClFsC,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA;IACA,IAAI4D,MAAM,GAAG5D,CAAC,IAAI6B,IAAI,IAAIgC,OAAO,GAAG7D,CAAC,IAAI+B,IAAI,EAAE;MAC3C,OAAO,KAAK;IAChB;IACA,OAAO6B,MAAM,GAAG5D,CAAC,GAAG6B,IAAI,IAAIgC,OAAO,GAAG7D,CAAC,GAAG+B,IAAI,IAAI,CAAClB,KAAK,CAACY,GAAG,CAACoC,OAAO,GAAG7D,CAAC,EAAE4D,MAAM,GAAG5D,CAAC,CAAC,IACjFsC,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MAC1BxB,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAI4D,MAAM,GAAG5D,CAAC,IAAI6B,IAAI,IAAIgC,OAAO,GAAG7D,CAAC,IAAI+B,IAAI,IAAIO,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MACxE,OAAO,KAAK;IAChB;IACA,OAAOF,MAAM,GAAG5D,CAAC,GAAG6B,IAAI,IAAIgC,OAAO,GAAG7D,CAAC,GAAG+B,IAAI,IAAIlB,KAAK,CAACY,GAAG,CAACoC,OAAO,GAAG7D,CAAC,EAAE4D,MAAM,GAAG5D,CAAC,CAAC,IAChFsC,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MAC1BxB,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAIsC,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MAC3B,OAAO,KAAK;IAChB;IACA;IACA;IACA,IAAIE,eAAe,GAAG1B,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;IACnG,OAAOJ,IAAI,CAACuB,GAAG,CAACO,eAAe,GAAGD,uBAAuB,CAAC,GAAG,CAAC,GAAGA,uBAAuB,IACpFnD,mBAAmB,CAAC6B,iBAAiB,CAACH,UAAU,CAAC;EACzD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI1B,mBAAmB,CAACM,SAAS,CAAC+C,kBAAkB,GAAG,UAAUL,MAAM,CAAC,SAASC,OAAO,CAAC,SAASC,QAAQ,CAAC,SAASC,uBAAuB,CAAC,SAAS;IAC7I,IAAIlD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIgB,IAAI,GAAGhB,KAAK,CAACiB,SAAS,CAAC,CAAC;IAC5B,IAAIQ,UAAU,GAAG,IAAI,CAACoB,uBAAuB,CAAC,CAAC;IAC/C;IACA,IAAI1D,CAAC,GAAG4D,MAAM;IACd,OAAO5D,CAAC,IAAI,CAAC,IAAIa,KAAK,CAACY,GAAG,CAACoC,OAAO,EAAE7D,CAAC,CAAC,EAAE;MACpCsC,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,GAAG,CAAC,EAAE;MACP,OAAOkE,GAAG;IACd;IACA,OAAOlE,CAAC,IAAI,CAAC,IAAI,CAACa,KAAK,CAACY,GAAG,CAACoC,OAAO,EAAE7D,CAAC,CAAC,IAAIsC,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MAClExB,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA;IACA,IAAIA,CAAC,GAAG,CAAC,IAAIsC,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MACnC,OAAOI,GAAG;IACd;IACA,OAAOlE,CAAC,IAAI,CAAC,IAAIa,KAAK,CAACY,GAAG,CAACoC,OAAO,EAAE7D,CAAC,CAAC,IAAIsC,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MACjExB,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAIsC,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MAC1B,OAAOI,GAAG;IACd;IACA;IACAlE,CAAC,GAAG4D,MAAM,GAAG,CAAC;IACd,OAAO5D,CAAC,GAAG6B,IAAI,IAAIhB,KAAK,CAACY,GAAG,CAACoC,OAAO,EAAE7D,CAAC,CAAC,EAAE;MACtCsC,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,KAAK6B,IAAI,EAAE;MACZ,OAAOqC,GAAG;IACd;IACA,OAAOlE,CAAC,GAAG6B,IAAI,IAAI,CAAChB,KAAK,CAACY,GAAG,CAACoC,OAAO,EAAE7D,CAAC,CAAC,IAAIsC,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MACnExB,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,KAAK6B,IAAI,IAAIS,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MACzC,OAAOI,GAAG;IACd;IACA,OAAOlE,CAAC,GAAG6B,IAAI,IAAIhB,KAAK,CAACY,GAAG,CAACoC,OAAO,EAAE7D,CAAC,CAAC,IAAIsC,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MAClExB,UAAU,CAAC,CAAC,CAAC,EAAE;MACftC,CAAC,EAAE;IACP;IACA,IAAIsC,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MAC3B,OAAOI,GAAG;IACd;IACA;IACA;IACA,IAAIF,eAAe,GAAG1B,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAC/EA,UAAU,CAAC,CAAC,CAAC;IACjB,IAAI,CAAC,GAAGJ,IAAI,CAACuB,GAAG,CAACO,eAAe,GAAGD,uBAAuB,CAAC,IAAI,CAAC,GAAGA,uBAAuB,EAAE;MACxF,OAAOG,GAAG;IACd;IACA,OAAOtD,mBAAmB,CAAC6B,iBAAiB,CAACH,UAAU,CAAC,GAAG1B,mBAAmB,CAACuC,aAAa,CAACb,UAAU,EAAEtC,CAAC,CAAC,GAAGkE,GAAG;EACrH,CAAC;EACD;AACJ;AACA;AACA;AACA;EACItD,mBAAmB,CAACM,SAAS,CAACiD,oBAAoB,GAAG,UAAUC,MAAM,CAAC,SAASC,OAAO,CAAC,SAASP,QAAQ,CAAC,SAASC,uBAAuB,CAAC,SAAS;IAC/I,IAAIlD,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAIkB,IAAI,GAAGlB,KAAK,CAACmB,QAAQ,CAAC,CAAC;IAC3B,IAAIM,UAAU,GAAG,IAAI,CAACoB,uBAAuB,CAAC,CAAC;IAC/C,IAAIlB,CAAC,GAAG4B,MAAM;IACd,OAAO5B,CAAC,IAAI,CAAC,IAAI3B,KAAK,CAACY,GAAG,CAACe,CAAC,EAAE6B,OAAO,CAAC,EAAE;MACpC/B,UAAU,CAAC,CAAC,CAAC,EAAE;MACfE,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,GAAG,CAAC,EAAE;MACP,OAAO0B,GAAG;IACd;IACA,OAAO1B,CAAC,IAAI,CAAC,IAAI,CAAC3B,KAAK,CAACY,GAAG,CAACe,CAAC,EAAE6B,OAAO,CAAC,IAAI/B,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MAClExB,UAAU,CAAC,CAAC,CAAC,EAAE;MACfE,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,GAAG,CAAC,IAAIF,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MACnC,OAAOI,GAAG;IACd;IACA,OAAO1B,CAAC,IAAI,CAAC,IAAI3B,KAAK,CAACY,GAAG,CAACe,CAAC,EAAE6B,OAAO,CAAC,IAAI/B,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MACjExB,UAAU,CAAC,CAAC,CAAC,EAAE;MACfE,CAAC,EAAE;IACP;IACA,IAAIF,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MAC1B,OAAOI,GAAG;IACd;IACA1B,CAAC,GAAG4B,MAAM,GAAG,CAAC;IACd,OAAO5B,CAAC,GAAGT,IAAI,IAAIlB,KAAK,CAACY,GAAG,CAACe,CAAC,EAAE6B,OAAO,CAAC,EAAE;MACtC/B,UAAU,CAAC,CAAC,CAAC,EAAE;MACfE,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,KAAKT,IAAI,EAAE;MACZ,OAAOmC,GAAG;IACd;IACA,OAAO1B,CAAC,GAAGT,IAAI,IAAI,CAAClB,KAAK,CAACY,GAAG,CAACe,CAAC,EAAE6B,OAAO,CAAC,IAAI/B,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MACnExB,UAAU,CAAC,CAAC,CAAC,EAAE;MACfE,CAAC,EAAE;IACP;IACA,IAAIA,CAAC,KAAKT,IAAI,IAAIO,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MACzC,OAAOI,GAAG;IACd;IACA,OAAO1B,CAAC,GAAGT,IAAI,IAAIlB,KAAK,CAACY,GAAG,CAACe,CAAC,EAAE6B,OAAO,CAAC,IAAI/B,UAAU,CAAC,CAAC,CAAC,GAAGwB,QAAQ,EAAE;MAClExB,UAAU,CAAC,CAAC,CAAC,EAAE;MACfE,CAAC,EAAE;IACP;IACA,IAAIF,UAAU,CAAC,CAAC,CAAC,IAAIwB,QAAQ,EAAE;MAC3B,OAAOI,GAAG;IACd;IACA;IACA;IACA,IAAIF,eAAe,GAAG1B,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAC/EA,UAAU,CAAC,CAAC,CAAC;IACjB,IAAI,CAAC,GAAGJ,IAAI,CAACuB,GAAG,CAACO,eAAe,GAAGD,uBAAuB,CAAC,IAAIA,uBAAuB,EAAE;MACpF,OAAOG,GAAG;IACd;IACA,OAAOtD,mBAAmB,CAAC6B,iBAAiB,CAACH,UAAU,CAAC,GAAG1B,mBAAmB,CAACuC,aAAa,CAACb,UAAU,EAAEE,CAAC,CAAC,GAAG0B,GAAG;EACrH,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItD,mBAAmB,CAACM,SAAS,CAACyB,oBAAoB,GAAG,UAAUL,UAAU,EAAEtC,CAAC,CAAC,SAASwC,CAAC,CAAC,SAASb,WAAW,EAAE;IAC1G,IAAIqC,eAAe,GAAG1B,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC,GAC/EA,UAAU,CAAC,CAAC,CAAC;IACjB,IAAIuB,OAAO,GAAGjD,mBAAmB,CAACuC,aAAa,CAACb,UAAU,EAAEE,CAAC,CAAC;IAC9D,IAAI6B,OAAO,GAAG,IAAI,CAACJ,kBAAkB,CAACjE,CAAC,EAAE,UAAWkC,IAAI,CAACC,KAAK,CAAC0B,OAAO,CAAC,EAAEvB,UAAU,CAAC,CAAC,CAAC,EAAE0B,eAAe,CAAC;IACxG,IAAI,CAACM,KAAK,CAACD,OAAO,CAAC,EAAE;MACjB;MACAR,OAAO,GAAG,IAAI,CAACM,oBAAoB,CAAC,UAAWjC,IAAI,CAACC,KAAK,CAAC0B,OAAO,CAAC,EAAE,UAAW3B,IAAI,CAACC,KAAK,CAACkC,OAAO,CAAC,EAAE/B,UAAU,CAAC,CAAC,CAAC,EAAE0B,eAAe,CAAC;MACnI,IAAI,CAACM,KAAK,CAACT,OAAO,CAAC,KACd,CAAClC,WAAW,IAAI,IAAI,CAACgC,kBAAkB,CAAC,UAAWzB,IAAI,CAACC,KAAK,CAACkC,OAAO,CAAC,EAAE,UAAWnC,IAAI,CAACC,KAAK,CAAC0B,OAAO,CAAC,EAAEvB,UAAU,CAAC,CAAC,CAAC,EAAE0B,eAAe,CAAC,CAAC,EAAE;QAC3I,IAAIO,mBAAmB,GAAGP,eAAe,GAAG,GAAG;QAC/C,IAAIQ,KAAK,GAAG,KAAK;QACjB,IAAIzD,eAAe,GAAG,IAAI,CAACA,eAAe;QAC1C,KAAK,IAAI0D,KAAK,GAAG,CAAC,EAAEC,QAAQ,GAAG3D,eAAe,CAACb,MAAM,EAAEuE,KAAK,GAAGC,QAAQ,EAAED,KAAK,EAAE,EAAE;UAC9E,IAAIE,MAAM,GAAG5D,eAAe,CAAC0D,KAAK,CAAC;UACnC;UACA,IAAIE,MAAM,CAACC,WAAW,CAACL,mBAAmB,EAAEF,OAAO,EAAER,OAAO,CAAC,EAAE;YAC3D9C,eAAe,CAAC0D,KAAK,CAAC,GAAGE,MAAM,CAACE,eAAe,CAACR,OAAO,EAAER,OAAO,EAAEU,mBAAmB,CAAC;YACtFC,KAAK,GAAG,IAAI;YACZ;UACJ;QACJ;QACA,IAAI,CAACA,KAAK,EAAE;UACR,IAAIM,KAAK,GAAG,IAAIrE,aAAa,CAACoD,OAAO,EAAEQ,OAAO,EAAEE,mBAAmB,CAAC;UACpExD,eAAe,CAACgE,IAAI,CAACD,KAAK,CAAC;UAC3B,IAAI,IAAI,CAAChE,mBAAmB,KAAK,IAAI,IAAI,IAAI,CAACA,mBAAmB,KAAKU,SAAS,EAAE;YAC7E,IAAI,CAACV,mBAAmB,CAACkE,wBAAwB,CAACF,KAAK,CAAC;UAC5D;QACJ;QACA,OAAO,IAAI;MACf;IACJ;IACA,OAAO,KAAK;EAChB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIlE,mBAAmB,CAACM,SAAS,CAAC6B,WAAW,GAAG,YAAY;IACpD,IAAIkC,GAAG,EAAEC,EAAE;IACX,IAAIC,GAAG,GAAG,IAAI,CAACpE,eAAe,CAACb,MAAM;IACrC,IAAIiF,GAAG,IAAI,CAAC,EAAE;MACV,OAAO,CAAC;IACZ;IACA,IAAIC,oBAAoB,GAAG,IAAI;IAC/B,IAAI;MACA,KAAK,IAAIC,EAAE,GAAG3F,QAAQ,CAAC,IAAI,CAACqB,eAAe,CAAC,EAAEuE,EAAE,GAAGD,EAAE,CAAClF,IAAI,CAAC,CAAC,EAAE,CAACmF,EAAE,CAACjF,IAAI,EAAEiF,EAAE,GAAGD,EAAE,CAAClF,IAAI,CAAC,CAAC,EAAE;QACpF,IAAIwE,MAAM,GAAGW,EAAE,CAAClF,KAAK;QACrB,IAAIuE,MAAM,CAACY,QAAQ,CAAC,CAAC,IAAI3E,mBAAmB,CAAC4E,aAAa,EAAE;UACxD,IAAIJ,oBAAoB,IAAI,IAAI,EAAE;YAC9BA,oBAAoB,GAAGT,MAAM;UACjC,CAAC,MACI;YACD;YACA;YACA;YACA;YACA;YACA,IAAI,CAAC/B,UAAU,GAAG,IAAI;YACtB,OAAO,UAAWV,IAAI,CAACC,KAAK,CAAC,CAACD,IAAI,CAACuB,GAAG,CAAC2B,oBAAoB,CAACK,IAAI,CAAC,CAAC,GAAGd,MAAM,CAACc,IAAI,CAAC,CAAC,CAAC,GAC/EvD,IAAI,CAACuB,GAAG,CAAC2B,oBAAoB,CAACM,IAAI,CAAC,CAAC,GAAGf,MAAM,CAACe,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UACnE;QACJ;MACJ;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEV,GAAG,GAAG;QAAEW,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIL,EAAE,IAAI,CAACA,EAAE,CAACjF,IAAI,KAAK6E,EAAE,GAAGG,EAAE,CAACQ,MAAM,CAAC,EAAEX,EAAE,CAACjF,IAAI,CAACoF,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIJ,GAAG,EAAE,MAAMA,GAAG,CAACW,KAAK;MAAE;IACxC;IACA,OAAO,CAAC;EACZ,CAAC;EACD;AACJ;AACA;AACA;AACA;EACIhF,mBAAmB,CAACM,SAAS,CAAC2B,4BAA4B,GAAG,YAAY;IACrE,IAAIiD,GAAG,EAAEZ,EAAE,EAAEa,GAAG,EAAEV,EAAE;IACpB,IAAIW,cAAc,GAAG,CAAC;IACtB,IAAI3C,eAAe,GAAG,GAAG;IACzB,IAAI8B,GAAG,GAAG,IAAI,CAACpE,eAAe,CAACb,MAAM;IACrC,IAAI;MACA,KAAK,IAAIoF,EAAE,GAAG5F,QAAQ,CAAC,IAAI,CAACqB,eAAe,CAAC,EAAEkF,EAAE,GAAGX,EAAE,CAACnF,IAAI,CAAC,CAAC,EAAE,CAAC8F,EAAE,CAAC5F,IAAI,EAAE4F,EAAE,GAAGX,EAAE,CAACnF,IAAI,CAAC,CAAC,EAAE;QACpF,IAAI+F,OAAO,GAAGD,EAAE,CAAC7F,KAAK;QACtB,IAAI8F,OAAO,CAACX,QAAQ,CAAC,CAAC,IAAI3E,mBAAmB,CAAC4E,aAAa,EAAE;UACzDQ,cAAc,EAAE;UAChB3C,eAAe,IAAI6C,OAAO,CAACC,sBAAsB,CAAC,CAAC;QACvD;MACJ;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEN,GAAG,GAAG;QAAEF,KAAK,EAAEQ;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIH,EAAE,IAAI,CAACA,EAAE,CAAC5F,IAAI,KAAK6E,EAAE,GAAGI,EAAE,CAACO,MAAM,CAAC,EAAEX,EAAE,CAACjF,IAAI,CAACqF,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIQ,GAAG,EAAE,MAAMA,GAAG,CAACF,KAAK;MAAE;IACxC;IACA,IAAII,cAAc,GAAG,CAAC,EAAE;MACpB,OAAO,KAAK;IAChB;IACA;IACA;IACA;IACA;IACA,IAAIK,OAAO,GAAGhD,eAAe,GAAG8B,GAAG;IACnC,IAAImB,cAAc,GAAG,GAAG;IACxB,IAAI;MACA,KAAK,IAAIC,EAAE,GAAG7G,QAAQ,CAAC,IAAI,CAACqB,eAAe,CAAC,EAAEyF,EAAE,GAAGD,EAAE,CAACpG,IAAI,CAAC,CAAC,EAAE,CAACqG,EAAE,CAACnG,IAAI,EAAEmG,EAAE,GAAGD,EAAE,CAACpG,IAAI,CAAC,CAAC,EAAE;QACpF,IAAI+F,OAAO,GAAGM,EAAE,CAACpG,KAAK;QACtBkG,cAAc,IAAIpE,IAAI,CAACuB,GAAG,CAACyC,OAAO,CAACC,sBAAsB,CAAC,CAAC,GAAGE,OAAO,CAAC;MAC1E;IACJ,CAAC,CACD,OAAOI,KAAK,EAAE;MAAEV,GAAG,GAAG;QAAEH,KAAK,EAAEa;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAID,EAAE,IAAI,CAACA,EAAE,CAACnG,IAAI,KAAKgF,EAAE,GAAGkB,EAAE,CAACV,MAAM,CAAC,EAAER,EAAE,CAACpF,IAAI,CAACsG,EAAE,CAAC;MACvD,CAAC,SACO;QAAE,IAAIR,GAAG,EAAE,MAAMA,GAAG,CAACH,KAAK;MAAE;IACxC;IACA,OAAOU,cAAc,IAAI,IAAI,GAAGjD,eAAe;EACnD,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACIzC,mBAAmB,CAACM,SAAS,CAAC+B,kBAAkB,GAAG,YAAY;IAC3D,IAAIyD,GAAG,EAAExB,EAAE,EAAEyB,GAAG,EAAEtB,EAAE;IACpB,IAAIuB,SAAS,GAAG,IAAI,CAAC7F,eAAe,CAACb,MAAM;IAC3C,IAAI0G,SAAS,GAAG,CAAC,EAAE;MACf;MACA,MAAM,IAAIjG,iBAAiB,CAAC,CAAC;IACjC;IACA,IAAII,eAAe,GAAG,IAAI,CAACA,eAAe;IAC1C,IAAIsF,OAAO;IACX;IACA,IAAIO,SAAS,GAAG,CAAC,EAAE;MACf;MACA,IAAIvD,eAAe,GAAG,GAAG;MACzB,IAAIwD,MAAM,GAAG,GAAG;MAChB,IAAI;QACA,KAAK,IAAIvB,EAAE,GAAG5F,QAAQ,CAAC,IAAI,CAACqB,eAAe,CAAC,EAAEkF,EAAE,GAAGX,EAAE,CAACnF,IAAI,CAAC,CAAC,EAAE,CAAC8F,EAAE,CAAC5F,IAAI,EAAE4F,EAAE,GAAGX,EAAE,CAACnF,IAAI,CAAC,CAAC,EAAE;UACpF,IAAIwE,MAAM,GAAGsB,EAAE,CAAC7F,KAAK;UACrB,IAAI0G,IAAI,GAAGnC,MAAM,CAACwB,sBAAsB,CAAC,CAAC;UAC1C9C,eAAe,IAAIyD,IAAI;UACvBD,MAAM,IAAIC,IAAI,GAAGA,IAAI;QACzB;MACJ,CAAC,CACD,OAAOC,KAAK,EAAE;QAAEL,GAAG,GAAG;UAAEd,KAAK,EAAEmB;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAId,EAAE,IAAI,CAACA,EAAE,CAAC5F,IAAI,KAAK6E,EAAE,GAAGI,EAAE,CAACO,MAAM,CAAC,EAAEX,EAAE,CAACjF,IAAI,CAACqF,EAAE,CAAC;QACvD,CAAC,SACO;UAAE,IAAIoB,GAAG,EAAE,MAAMA,GAAG,CAACd,KAAK;QAAE;MACxC;MACAS,OAAO,GAAGhD,eAAe,GAAGuD,SAAS;MACrC,IAAII,MAAM,GAAG9E,IAAI,CAAC+E,IAAI,CAACJ,MAAM,GAAGD,SAAS,GAAGP,OAAO,GAAGA,OAAO,CAAC;MAC9DtF,eAAe,CAACmG,IAAI;MACpB;AACZ;AACA;MACY;MACA,UAAUC,OAAO,EAAEC,OAAO,EAAE;QACxB,IAAIC,EAAE,GAAGnF,IAAI,CAACuB,GAAG,CAAC2D,OAAO,CAACjB,sBAAsB,CAAC,CAAC,GAAGE,OAAO,CAAC;QAC7D,IAAIiB,EAAE,GAAGpF,IAAI,CAACuB,GAAG,CAAC0D,OAAO,CAAChB,sBAAsB,CAAC,CAAC,GAAGE,OAAO,CAAC;QAC7D,OAAOgB,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC,GAAGD,EAAE,GAAGC,EAAE,GAAG,CAAC,GAAG,CAAC;MACzC,CAAC,CAAC;MACF,IAAIC,KAAK,GAAGrF,IAAI,CAACiD,GAAG,CAAC,GAAG,GAAGkB,OAAO,EAAEW,MAAM,CAAC;MAC3C,KAAK,IAAIhH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGe,eAAe,CAACb,MAAM,IAAIa,eAAe,CAACb,MAAM,GAAG,CAAC,EAAEF,CAAC,EAAE,EAAE;QAC3E,IAAIkG,OAAO,GAAGnF,eAAe,CAACf,CAAC,CAAC;QAChC,IAAIkC,IAAI,CAACuB,GAAG,CAACyC,OAAO,CAACC,sBAAsB,CAAC,CAAC,GAAGE,OAAO,CAAC,GAAGkB,KAAK,EAAE;UAC9DxG,eAAe,CAACyG,MAAM,CAACxH,CAAC,EAAE,CAAC,CAAC;UAC5BA,CAAC,EAAE;QACP;MACJ;IACJ;IACA,IAAIe,eAAe,CAACb,MAAM,GAAG,CAAC,EAAE;MAC5B;MACA,IAAImD,eAAe,GAAG,GAAG;MACzB,IAAI;QACA,KAAK,IAAIoE,iBAAiB,GAAG/H,QAAQ,CAACqB,eAAe,CAAC,EAAE2G,mBAAmB,GAAGD,iBAAiB,CAACtH,IAAI,CAAC,CAAC,EAAE,CAACuH,mBAAmB,CAACrH,IAAI,EAAEqH,mBAAmB,GAAGD,iBAAiB,CAACtH,IAAI,CAAC,CAAC,EAAE;UAC/K,IAAIwH,cAAc,GAAGD,mBAAmB,CAACtH,KAAK;UAC9CiD,eAAe,IAAIsE,cAAc,CAACxB,sBAAsB,CAAC,CAAC;QAC9D;MACJ,CAAC,CACD,OAAOyB,KAAK,EAAE;QAAEjB,GAAG,GAAG;UAAEf,KAAK,EAAEgC;QAAM,CAAC;MAAE,CAAC,SACjC;QACJ,IAAI;UACA,IAAIF,mBAAmB,IAAI,CAACA,mBAAmB,CAACrH,IAAI,KAAKgF,EAAE,GAAGoC,iBAAiB,CAAC5B,MAAM,CAAC,EAAER,EAAE,CAACpF,IAAI,CAACwH,iBAAiB,CAAC;QACvH,CAAC,SACO;UAAE,IAAId,GAAG,EAAE,MAAMA,GAAG,CAACf,KAAK;QAAE;MACxC;MACAS,OAAO,GAAGhD,eAAe,GAAGtC,eAAe,CAACb,MAAM;MAClDa,eAAe,CAACmG,IAAI;MACpB;AACZ;AACA;MACY;MACA,UAAUC,OAAO,EAAEC,OAAO,EAAE;QACxB,IAAIA,OAAO,CAAC7B,QAAQ,CAAC,CAAC,KAAK4B,OAAO,CAAC5B,QAAQ,CAAC,CAAC,EAAE;UAC3C,IAAI8B,EAAE,GAAGnF,IAAI,CAACuB,GAAG,CAAC2D,OAAO,CAACjB,sBAAsB,CAAC,CAAC,GAAGE,OAAO,CAAC;UAC7D,IAAIiB,EAAE,GAAGpF,IAAI,CAACuB,GAAG,CAAC0D,OAAO,CAAChB,sBAAsB,CAAC,CAAC,GAAGE,OAAO,CAAC;UAC7D,OAAOgB,EAAE,GAAGC,EAAE,GAAG,CAAC,GAAGD,EAAE,GAAGC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;QACzC,CAAC,MACI;UACD,OAAOF,OAAO,CAAC7B,QAAQ,CAAC,CAAC,GAAG4B,OAAO,CAAC5B,QAAQ,CAAC,CAAC;QAClD;MACJ,CAAC,CAAC;MACFxE,eAAe,CAACyG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;IACA,OAAO,CACHzG,eAAe,CAAC,CAAC,CAAC,EAClBA,eAAe,CAAC,CAAC,CAAC,EAClBA,eAAe,CAAC,CAAC,CAAC,CACrB;EACL,CAAC;EACDH,mBAAmB,CAAC4E,aAAa,GAAG,CAAC;EACrC5E,mBAAmB,CAACyB,QAAQ,GAAG,CAAC,CAAC,CAAC;EAClCzB,mBAAmB,CAACwB,WAAW,GAAG,EAAE,CAAC,CAAC;EACtC,OAAOxB,mBAAmB;AAC9B,CAAC,CAAC,CAAE;AACJ,eAAeA,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}