{"ast": null, "code": "// Utility functions for number formatting\n\n/**\n * Format number in accounting format (e.g., 1,000,000.00)\n * @param {number} value - The number to format\n * @param {number} decimals - Number of decimal places (default: 2)\n * @returns {string} - Formatted number string\n */\nexport const formatCurrency = (value, decimals = 2) => {\n  if (value === null || value === undefined || isNaN(value)) {\n    return '0.00';\n  }\n  return Number(value).toLocaleString('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals\n  });\n};\n\n/**\n * Parse formatted currency string back to number\n * @param {string} value - The formatted currency string\n * @returns {number} - Parsed number\n */\nexport const parseCurrency = value => {\n  if (!value || typeof value !== 'string') {\n    return 0;\n  }\n\n  // Remove commas and parse as float\n  return parseFloat(value.replace(/,/g, '')) || 0;\n};\n\n/**\n * Calculate percentage of a value\n * @param {number} value - The base value\n * @param {number} percentage - The percentage to calculate\n * @returns {number} - Calculated percentage amount\n */\nexport const calculatePercentage = (value, percentage) => {\n  if (!value || !percentage) return 0;\n  return value * percentage / 100;\n};\n\n/**\n * Calculate purchase invoice totals\n * @param {Array} items - Array of invoice items\n * @param {number} discountPercentage - Discount percentage\n * @param {number} discountAmount - Fixed discount amount\n * @param {number} salesTaxPercentage - Sales tax percentage\n * @param {number} withholdingTaxPercentage - Withholding tax percentage\n * @returns {Object} - Object containing all calculated totals\n */\nexport const calculateInvoiceTotals = (items = [], discountPercentage = 0, discountAmount = 0, salesTaxPercentage = 0, withholdingTaxPercentage = 0) => {\n  // Calculate subtotal from items\n  const subtotal = items.reduce((sum, item) => {\n    const quantity = parseFloat(item.quantity) || 0;\n    const price = parseFloat(item.purchasePrice) || 0;\n    return sum + quantity * price;\n  }, 0);\n\n  // Calculate discount\n  const percentageDiscount = calculatePercentage(subtotal, discountPercentage);\n  const totalDiscount = percentageDiscount + (parseFloat(discountAmount) || 0);\n  const totalAfterDiscount = subtotal - totalDiscount;\n\n  // Calculate sales tax (applied on total after discount)\n  const salesTaxAmount = calculatePercentage(totalAfterDiscount, salesTaxPercentage);\n  const totalAfterSalesTax = totalAfterDiscount + salesTaxAmount;\n\n  // Calculate withholding tax (applied on total after sales tax)\n  const withholdingTaxAmount = calculatePercentage(totalAfterSalesTax, withholdingTaxPercentage);\n\n  // Final payable amount\n  const payableAmount = totalAfterSalesTax - withholdingTaxAmount;\n  return {\n    subtotal: Math.round(subtotal * 100) / 100,\n    totalDiscount: Math.round(totalDiscount * 100) / 100,\n    totalAfterDiscount: Math.round(totalAfterDiscount * 100) / 100,\n    salesTaxAmount: Math.round(salesTaxAmount * 100) / 100,\n    totalAfterSalesTax: Math.round(totalAfterSalesTax * 100) / 100,\n    withholdingTaxAmount: Math.round(withholdingTaxAmount * 100) / 100,\n    payableAmount: Math.round(payableAmount * 100) / 100\n  };\n};\n\n/**\n * Format percentage for display\n * @param {number} value - The percentage value\n * @returns {string} - Formatted percentage string\n */\nexport const formatPercentage = value => {\n  if (value === null || value === undefined || isNaN(value)) {\n    return '0%';\n  }\n  return `${Number(value).toFixed(2)}%`;\n};\n\n/**\n * Format number for accounting display (with commas, 2 decimals)\n * @param {number|string} value - The number to format\n * @returns {string} - Formatted number string\n */\nexport const formatAccountingNumber = value => {\n  // Handle null, undefined, empty string, but allow 0\n  if (value === null || value === undefined || value === '') return '';\n  const num = parseFloat(value);\n  if (isNaN(num)) return String(value); // Return as string if not a valid number\n\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(num);\n};\n\n/**\n * Parse accounting formatted number back to decimal\n * @param {string} value - The formatted number string\n * @returns {string} - Parsed number as string with 2 decimals\n */\nexport const parseAccountingNumber = value => {\n  if (!value) return '';\n  // Remove commas and parse\n  const cleaned = value.toString().replace(/,/g, '');\n  const num = parseFloat(cleaned);\n  return isNaN(num) ? '' : num.toFixed(2);\n};\n\n/**\n * Ensure amount has exactly 2 decimal places\n * @param {number|string} amount - The amount to fix\n * @returns {string} - Amount with 2 decimal places\n */\nexport const fixDecimalPlaces = amount => {\n  if (!amount && amount !== 0) return '0.00';\n  const num = parseFloat(amount);\n  return isNaN(num) ? '0.00' : num.toFixed(2);\n};", "map": {"version": 3, "names": ["formatCurrency", "value", "decimals", "undefined", "isNaN", "Number", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "parseCurrency", "parseFloat", "replace", "calculatePercentage", "percentage", "calculateInvoiceTotals", "items", "discountPercentage", "discountAmount", "salesTaxPercentage", "withholdingTaxPercentage", "subtotal", "reduce", "sum", "item", "quantity", "price", "purchasePrice", "percentageDiscount", "totalDiscount", "totalAfterDiscount", "salesTaxAmount", "totalAfterSalesTax", "withholdingTaxAmount", "payableAmount", "Math", "round", "formatPercentage", "toFixed", "formatAccountingNumber", "num", "String", "Intl", "NumberFormat", "format", "parseAccountingNumber", "cleaned", "toString", "fixDecimalPlaces", "amount"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/src/utils/numberUtils.js"], "sourcesContent": ["// Utility functions for number formatting\n\n/**\n * Format number in accounting format (e.g., 1,000,000.00)\n * @param {number} value - The number to format\n * @param {number} decimals - Number of decimal places (default: 2)\n * @returns {string} - Formatted number string\n */\nexport const formatCurrency = (value, decimals = 2) => {\n  if (value === null || value === undefined || isNaN(value)) {\n    return '0.00';\n  }\n  \n  return Number(value).toLocaleString('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals\n  });\n};\n\n/**\n * Parse formatted currency string back to number\n * @param {string} value - The formatted currency string\n * @returns {number} - Parsed number\n */\nexport const parseCurrency = (value) => {\n  if (!value || typeof value !== 'string') {\n    return 0;\n  }\n  \n  // Remove commas and parse as float\n  return parseFloat(value.replace(/,/g, '')) || 0;\n};\n\n/**\n * Calculate percentage of a value\n * @param {number} value - The base value\n * @param {number} percentage - The percentage to calculate\n * @returns {number} - Calculated percentage amount\n */\nexport const calculatePercentage = (value, percentage) => {\n  if (!value || !percentage) return 0;\n  return (value * percentage) / 100;\n};\n\n/**\n * Calculate purchase invoice totals\n * @param {Array} items - Array of invoice items\n * @param {number} discountPercentage - Discount percentage\n * @param {number} discountAmount - Fixed discount amount\n * @param {number} salesTaxPercentage - Sales tax percentage\n * @param {number} withholdingTaxPercentage - Withholding tax percentage\n * @returns {Object} - Object containing all calculated totals\n */\nexport const calculateInvoiceTotals = (\n  items = [],\n  discountPercentage = 0,\n  discountAmount = 0,\n  salesTaxPercentage = 0,\n  withholdingTaxPercentage = 0\n) => {\n  // Calculate subtotal from items\n  const subtotal = items.reduce((sum, item) => {\n    const quantity = parseFloat(item.quantity) || 0;\n    const price = parseFloat(item.purchasePrice) || 0;\n    return sum + (quantity * price);\n  }, 0);\n\n  // Calculate discount\n  const percentageDiscount = calculatePercentage(subtotal, discountPercentage);\n  const totalDiscount = percentageDiscount + (parseFloat(discountAmount) || 0);\n  const totalAfterDiscount = subtotal - totalDiscount;\n\n  // Calculate sales tax (applied on total after discount)\n  const salesTaxAmount = calculatePercentage(totalAfterDiscount, salesTaxPercentage);\n  const totalAfterSalesTax = totalAfterDiscount + salesTaxAmount;\n\n  // Calculate withholding tax (applied on total after sales tax)\n  const withholdingTaxAmount = calculatePercentage(totalAfterSalesTax, withholdingTaxPercentage);\n  \n  // Final payable amount\n  const payableAmount = totalAfterSalesTax - withholdingTaxAmount;\n\n  return {\n    subtotal: Math.round(subtotal * 100) / 100,\n    totalDiscount: Math.round(totalDiscount * 100) / 100,\n    totalAfterDiscount: Math.round(totalAfterDiscount * 100) / 100,\n    salesTaxAmount: Math.round(salesTaxAmount * 100) / 100,\n    totalAfterSalesTax: Math.round(totalAfterSalesTax * 100) / 100,\n    withholdingTaxAmount: Math.round(withholdingTaxAmount * 100) / 100,\n    payableAmount: Math.round(payableAmount * 100) / 100\n  };\n};\n\n/**\n * Format percentage for display\n * @param {number} value - The percentage value\n * @returns {string} - Formatted percentage string\n */\nexport const formatPercentage = (value) => {\n  if (value === null || value === undefined || isNaN(value)) {\n    return '0%';\n  }\n  return `${Number(value).toFixed(2)}%`;\n};\n\n/**\n * Format number for accounting display (with commas, 2 decimals)\n * @param {number|string} value - The number to format\n * @returns {string} - Formatted number string\n */\nexport const formatAccountingNumber = (value) => {\n  // Handle null, undefined, empty string, but allow 0\n  if (value === null || value === undefined || value === '') return '';\n\n  const num = parseFloat(value);\n  if (isNaN(num)) return String(value); // Return as string if not a valid number\n\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2\n  }).format(num);\n};\n\n/**\n * Parse accounting formatted number back to decimal\n * @param {string} value - The formatted number string\n * @returns {string} - Parsed number as string with 2 decimals\n */\nexport const parseAccountingNumber = (value) => {\n  if (!value) return '';\n  // Remove commas and parse\n  const cleaned = value.toString().replace(/,/g, '');\n  const num = parseFloat(cleaned);\n  return isNaN(num) ? '' : num.toFixed(2);\n};\n\n/**\n * Ensure amount has exactly 2 decimal places\n * @param {number|string} amount - The amount to fix\n * @returns {string} - Amount with 2 decimal places\n */\nexport const fixDecimalPlaces = (amount) => {\n  if (!amount && amount !== 0) return '0.00';\n  const num = parseFloat(amount);\n  return isNaN(num) ? '0.00' : num.toFixed(2);\n};\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAACC,KAAK,EAAEC,QAAQ,GAAG,CAAC,KAAK;EACrD,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAIC,KAAK,CAACH,KAAK,CAAC,EAAE;IACzD,OAAO,MAAM;EACf;EAEA,OAAOI,MAAM,CAACJ,KAAK,CAAC,CAACK,cAAc,CAAC,OAAO,EAAE;IAC3CC,qBAAqB,EAAEL,QAAQ;IAC/BM,qBAAqB,EAAEN;EACzB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,aAAa,GAAIR,KAAK,IAAK;EACtC,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAO,CAAC;EACV;;EAEA;EACA,OAAOS,UAAU,CAACT,KAAK,CAACU,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC;AACjD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAACX,KAAK,EAAEY,UAAU,KAAK;EACxD,IAAI,CAACZ,KAAK,IAAI,CAACY,UAAU,EAAE,OAAO,CAAC;EACnC,OAAQZ,KAAK,GAAGY,UAAU,GAAI,GAAG;AACnC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAGA,CACpCC,KAAK,GAAG,EAAE,EACVC,kBAAkB,GAAG,CAAC,EACtBC,cAAc,GAAG,CAAC,EAClBC,kBAAkB,GAAG,CAAC,EACtBC,wBAAwB,GAAG,CAAC,KACzB;EACH;EACA,MAAMC,QAAQ,GAAGL,KAAK,CAACM,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;IAC3C,MAAMC,QAAQ,GAAGd,UAAU,CAACa,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC;IAC/C,MAAMC,KAAK,GAAGf,UAAU,CAACa,IAAI,CAACG,aAAa,CAAC,IAAI,CAAC;IACjD,OAAOJ,GAAG,GAAIE,QAAQ,GAAGC,KAAM;EACjC,CAAC,EAAE,CAAC,CAAC;;EAEL;EACA,MAAME,kBAAkB,GAAGf,mBAAmB,CAACQ,QAAQ,EAAEJ,kBAAkB,CAAC;EAC5E,MAAMY,aAAa,GAAGD,kBAAkB,IAAIjB,UAAU,CAACO,cAAc,CAAC,IAAI,CAAC,CAAC;EAC5E,MAAMY,kBAAkB,GAAGT,QAAQ,GAAGQ,aAAa;;EAEnD;EACA,MAAME,cAAc,GAAGlB,mBAAmB,CAACiB,kBAAkB,EAAEX,kBAAkB,CAAC;EAClF,MAAMa,kBAAkB,GAAGF,kBAAkB,GAAGC,cAAc;;EAE9D;EACA,MAAME,oBAAoB,GAAGpB,mBAAmB,CAACmB,kBAAkB,EAAEZ,wBAAwB,CAAC;;EAE9F;EACA,MAAMc,aAAa,GAAGF,kBAAkB,GAAGC,oBAAoB;EAE/D,OAAO;IACLZ,QAAQ,EAAEc,IAAI,CAACC,KAAK,CAACf,QAAQ,GAAG,GAAG,CAAC,GAAG,GAAG;IAC1CQ,aAAa,EAAEM,IAAI,CAACC,KAAK,CAACP,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;IACpDC,kBAAkB,EAAEK,IAAI,CAACC,KAAK,CAACN,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG;IAC9DC,cAAc,EAAEI,IAAI,CAACC,KAAK,CAACL,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG;IACtDC,kBAAkB,EAAEG,IAAI,CAACC,KAAK,CAACJ,kBAAkB,GAAG,GAAG,CAAC,GAAG,GAAG;IAC9DC,oBAAoB,EAAEE,IAAI,CAACC,KAAK,CAACH,oBAAoB,GAAG,GAAG,CAAC,GAAG,GAAG;IAClEC,aAAa,EAAEC,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,GAAG,CAAC,GAAG;EACnD,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMG,gBAAgB,GAAInC,KAAK,IAAK;EACzC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAIC,KAAK,CAACH,KAAK,CAAC,EAAE;IACzD,OAAO,IAAI;EACb;EACA,OAAO,GAAGI,MAAM,CAACJ,KAAK,CAAC,CAACoC,OAAO,CAAC,CAAC,CAAC,GAAG;AACvC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAIrC,KAAK,IAAK;EAC/C;EACA,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,EAAE,EAAE,OAAO,EAAE;EAEpE,MAAMsC,GAAG,GAAG7B,UAAU,CAACT,KAAK,CAAC;EAC7B,IAAIG,KAAK,CAACmC,GAAG,CAAC,EAAE,OAAOC,MAAM,CAACvC,KAAK,CAAC,CAAC,CAAC;;EAEtC,OAAO,IAAIwC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IACpCnC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC,CAACmC,MAAM,CAACJ,GAAG,CAAC;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,qBAAqB,GAAI3C,KAAK,IAAK;EAC9C,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;EACrB;EACA,MAAM4C,OAAO,GAAG5C,KAAK,CAAC6C,QAAQ,CAAC,CAAC,CAACnC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;EAClD,MAAM4B,GAAG,GAAG7B,UAAU,CAACmC,OAAO,CAAC;EAC/B,OAAOzC,KAAK,CAACmC,GAAG,CAAC,GAAG,EAAE,GAAGA,GAAG,CAACF,OAAO,CAAC,CAAC,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,gBAAgB,GAAIC,MAAM,IAAK;EAC1C,IAAI,CAACA,MAAM,IAAIA,MAAM,KAAK,CAAC,EAAE,OAAO,MAAM;EAC1C,MAAMT,GAAG,GAAG7B,UAAU,CAACsC,MAAM,CAAC;EAC9B,OAAO5C,KAAK,CAACmC,GAAG,CAAC,GAAG,MAAM,GAAGA,GAAG,CAACF,OAAO,CAAC,CAAC,CAAC;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}