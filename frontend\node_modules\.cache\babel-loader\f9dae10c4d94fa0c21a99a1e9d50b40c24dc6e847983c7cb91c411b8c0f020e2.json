{"ast": null, "code": "var __values = this && this.__values || function (o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator,\n    m = s && o[s],\n    i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n    next: function () {\n      if (o && i >= o.length) o = void 0;\n      return {\n        value: o && o[i++],\n        done: !o\n      };\n    }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * RSS util functions.\n */\nvar RSSUtils = /** @class */function () {\n  function RSSUtils() {}\n  RSSUtils.getRSSvalue = function (widths, maxWidth, noNarrow) {\n    var e_1, _a;\n    var n = 0;\n    try {\n      for (var widths_1 = __values(widths), widths_1_1 = widths_1.next(); !widths_1_1.done; widths_1_1 = widths_1.next()) {\n        var width = widths_1_1.value;\n        n += width;\n      }\n    } catch (e_1_1) {\n      e_1 = {\n        error: e_1_1\n      };\n    } finally {\n      try {\n        if (widths_1_1 && !widths_1_1.done && (_a = widths_1.return)) _a.call(widths_1);\n      } finally {\n        if (e_1) throw e_1.error;\n      }\n    }\n    var val = 0;\n    var narrowMask = 0;\n    var elements = widths.length;\n    for (var bar = 0; bar < elements - 1; bar++) {\n      var elmWidth = void 0;\n      for (elmWidth = 1, narrowMask |= 1 << bar; elmWidth < widths[bar]; elmWidth++, narrowMask &= ~(1 << bar)) {\n        var subVal = RSSUtils.combins(n - elmWidth - 1, elements - bar - 2);\n        if (noNarrow && narrowMask === 0 && n - elmWidth - (elements - bar - 1) >= elements - bar - 1) {\n          subVal -= RSSUtils.combins(n - elmWidth - (elements - bar), elements - bar - 2);\n        }\n        if (elements - bar - 1 > 1) {\n          var lessVal = 0;\n          for (var mxwElement = n - elmWidth - (elements - bar - 2); mxwElement > maxWidth; mxwElement--) {\n            lessVal += RSSUtils.combins(n - elmWidth - mxwElement - 1, elements - bar - 3);\n          }\n          subVal -= lessVal * (elements - 1 - bar);\n        } else if (n - elmWidth > maxWidth) {\n          subVal--;\n        }\n        val += subVal;\n      }\n      n -= elmWidth;\n    }\n    return val;\n  };\n  RSSUtils.combins = function (n, r) {\n    var maxDenom;\n    var minDenom;\n    if (n - r > r) {\n      minDenom = r;\n      maxDenom = n - r;\n    } else {\n      minDenom = n - r;\n      maxDenom = r;\n    }\n    var val = 1;\n    var j = 1;\n    for (var i = n; i > maxDenom; i--) {\n      val *= i;\n      if (j <= minDenom) {\n        val /= j;\n        j++;\n      }\n    }\n    while (j <= minDenom) {\n      val /= j;\n      j++;\n    }\n    return val;\n  };\n  return RSSUtils;\n}();\nexport default RSSUtils;", "map": {"version": 3, "names": ["__values", "o", "s", "Symbol", "iterator", "m", "i", "call", "length", "next", "value", "done", "TypeError", "RSSUtils", "getRSSvalue", "widths", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "e_1", "_a", "n", "widths_1", "widths_1_1", "width", "e_1_1", "error", "return", "val", "narrowMask", "elements", "bar", "elm<PERSON><PERSON><PERSON>", "subVal", "combins", "lessVal", "mxwElement", "r", "maxDenom", "minDenom", "j"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@zxing/library/esm/core/oned/rss/RSSUtils.js"], "sourcesContent": ["var __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\n/**\n * RSS util functions.\n */\nvar RSSUtils = /** @class */ (function () {\n    function RSSUtils() {\n    }\n    RSSUtils.getRSSvalue = function (widths, maxWidth, noNarrow) {\n        var e_1, _a;\n        var n = 0;\n        try {\n            for (var widths_1 = __values(widths), widths_1_1 = widths_1.next(); !widths_1_1.done; widths_1_1 = widths_1.next()) {\n                var width = widths_1_1.value;\n                n += width;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (widths_1_1 && !widths_1_1.done && (_a = widths_1.return)) _a.call(widths_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        var val = 0;\n        var narrowMask = 0;\n        var elements = widths.length;\n        for (var bar = 0; bar < elements - 1; bar++) {\n            var elmWidth = void 0;\n            for (elmWidth = 1, narrowMask |= 1 << bar; elmWidth < widths[bar]; elmWidth++, narrowMask &= ~(1 << bar)) {\n                var subVal = RSSUtils.combins(n - elmWidth - 1, elements - bar - 2);\n                if (noNarrow && (narrowMask === 0) && (n - elmWidth - (elements - bar - 1) >= elements - bar - 1)) {\n                    subVal -= RSSUtils.combins(n - elmWidth - (elements - bar), elements - bar - 2);\n                }\n                if (elements - bar - 1 > 1) {\n                    var lessVal = 0;\n                    for (var mxwElement = n - elmWidth - (elements - bar - 2); mxwElement > maxWidth; mxwElement--) {\n                        lessVal += RSSUtils.combins(n - elmWidth - mxwElement - 1, elements - bar - 3);\n                    }\n                    subVal -= lessVal * (elements - 1 - bar);\n                }\n                else if (n - elmWidth > maxWidth) {\n                    subVal--;\n                }\n                val += subVal;\n            }\n            n -= elmWidth;\n        }\n        return val;\n    };\n    RSSUtils.combins = function (n, r) {\n        var maxDenom;\n        var minDenom;\n        if (n - r > r) {\n            minDenom = r;\n            maxDenom = n - r;\n        }\n        else {\n            minDenom = n - r;\n            maxDenom = r;\n        }\n        var val = 1;\n        var j = 1;\n        for (var i = n; i > maxDenom; i--) {\n            val *= i;\n            if (j <= minDenom) {\n                val /= j;\n                j++;\n            }\n        }\n        while ((j <= minDenom)) {\n            val /= j;\n            j++;\n        }\n        return val;\n    };\n    return RSSUtils;\n}());\nexport default RSSUtils;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,UAASC,CAAC,EAAE;EAClD,IAAIC,CAAC,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,QAAQ;IAAEC,CAAC,GAAGH,CAAC,IAAID,CAAC,CAACC,CAAC,CAAC;IAAEI,CAAC,GAAG,CAAC;EAC7E,IAAID,CAAC,EAAE,OAAOA,CAAC,CAACE,IAAI,CAACN,CAAC,CAAC;EACvB,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACO,MAAM,KAAK,QAAQ,EAAE,OAAO;IAC1CC,IAAI,EAAE,SAAAA,CAAA,EAAY;MACd,IAAIR,CAAC,IAAIK,CAAC,IAAIL,CAAC,CAACO,MAAM,EAAEP,CAAC,GAAG,KAAK,CAAC;MAClC,OAAO;QAAES,KAAK,EAAET,CAAC,IAAIA,CAAC,CAACK,CAAC,EAAE,CAAC;QAAEK,IAAI,EAAE,CAACV;MAAE,CAAC;IAC3C;EACJ,CAAC;EACD,MAAM,IAAIW,SAAS,CAACV,CAAC,GAAG,yBAAyB,GAAG,iCAAiC,CAAC;AAC1F,CAAC;AACD;AACA;AACA;AACA,IAAIW,QAAQ,GAAG,aAAe,YAAY;EACtC,SAASA,QAAQA,CAAA,EAAG,CACpB;EACAA,QAAQ,CAACC,WAAW,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACzD,IAAIC,GAAG,EAAEC,EAAE;IACX,IAAIC,CAAC,GAAG,CAAC;IACT,IAAI;MACA,KAAK,IAAIC,QAAQ,GAAGrB,QAAQ,CAACe,MAAM,CAAC,EAAEO,UAAU,GAAGD,QAAQ,CAACZ,IAAI,CAAC,CAAC,EAAE,CAACa,UAAU,CAACX,IAAI,EAAEW,UAAU,GAAGD,QAAQ,CAACZ,IAAI,CAAC,CAAC,EAAE;QAChH,IAAIc,KAAK,GAAGD,UAAU,CAACZ,KAAK;QAC5BU,CAAC,IAAIG,KAAK;MACd;IACJ,CAAC,CACD,OAAOC,KAAK,EAAE;MAAEN,GAAG,GAAG;QAAEO,KAAK,EAAED;MAAM,CAAC;IAAE,CAAC,SACjC;MACJ,IAAI;QACA,IAAIF,UAAU,IAAI,CAACA,UAAU,CAACX,IAAI,KAAKQ,EAAE,GAAGE,QAAQ,CAACK,MAAM,CAAC,EAAEP,EAAE,CAACZ,IAAI,CAACc,QAAQ,CAAC;MACnF,CAAC,SACO;QAAE,IAAIH,GAAG,EAAE,MAAMA,GAAG,CAACO,KAAK;MAAE;IACxC;IACA,IAAIE,GAAG,GAAG,CAAC;IACX,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,QAAQ,GAAGd,MAAM,CAACP,MAAM;IAC5B,KAAK,IAAIsB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGD,QAAQ,GAAG,CAAC,EAAEC,GAAG,EAAE,EAAE;MACzC,IAAIC,QAAQ,GAAG,KAAK,CAAC;MACrB,KAAKA,QAAQ,GAAG,CAAC,EAAEH,UAAU,IAAI,CAAC,IAAIE,GAAG,EAAEC,QAAQ,GAAGhB,MAAM,CAACe,GAAG,CAAC,EAAEC,QAAQ,EAAE,EAAEH,UAAU,IAAI,EAAE,CAAC,IAAIE,GAAG,CAAC,EAAE;QACtG,IAAIE,MAAM,GAAGnB,QAAQ,CAACoB,OAAO,CAACb,CAAC,GAAGW,QAAQ,GAAG,CAAC,EAAEF,QAAQ,GAAGC,GAAG,GAAG,CAAC,CAAC;QACnE,IAAIb,QAAQ,IAAKW,UAAU,KAAK,CAAE,IAAKR,CAAC,GAAGW,QAAQ,IAAIF,QAAQ,GAAGC,GAAG,GAAG,CAAC,CAAC,IAAID,QAAQ,GAAGC,GAAG,GAAG,CAAE,EAAE;UAC/FE,MAAM,IAAInB,QAAQ,CAACoB,OAAO,CAACb,CAAC,GAAGW,QAAQ,IAAIF,QAAQ,GAAGC,GAAG,CAAC,EAAED,QAAQ,GAAGC,GAAG,GAAG,CAAC,CAAC;QACnF;QACA,IAAID,QAAQ,GAAGC,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE;UACxB,IAAII,OAAO,GAAG,CAAC;UACf,KAAK,IAAIC,UAAU,GAAGf,CAAC,GAAGW,QAAQ,IAAIF,QAAQ,GAAGC,GAAG,GAAG,CAAC,CAAC,EAAEK,UAAU,GAAGnB,QAAQ,EAAEmB,UAAU,EAAE,EAAE;YAC5FD,OAAO,IAAIrB,QAAQ,CAACoB,OAAO,CAACb,CAAC,GAAGW,QAAQ,GAAGI,UAAU,GAAG,CAAC,EAAEN,QAAQ,GAAGC,GAAG,GAAG,CAAC,CAAC;UAClF;UACAE,MAAM,IAAIE,OAAO,IAAIL,QAAQ,GAAG,CAAC,GAAGC,GAAG,CAAC;QAC5C,CAAC,MACI,IAAIV,CAAC,GAAGW,QAAQ,GAAGf,QAAQ,EAAE;UAC9BgB,MAAM,EAAE;QACZ;QACAL,GAAG,IAAIK,MAAM;MACjB;MACAZ,CAAC,IAAIW,QAAQ;IACjB;IACA,OAAOJ,GAAG;EACd,CAAC;EACDd,QAAQ,CAACoB,OAAO,GAAG,UAAUb,CAAC,EAAEgB,CAAC,EAAE;IAC/B,IAAIC,QAAQ;IACZ,IAAIC,QAAQ;IACZ,IAAIlB,CAAC,GAAGgB,CAAC,GAAGA,CAAC,EAAE;MACXE,QAAQ,GAAGF,CAAC;MACZC,QAAQ,GAAGjB,CAAC,GAAGgB,CAAC;IACpB,CAAC,MACI;MACDE,QAAQ,GAAGlB,CAAC,GAAGgB,CAAC;MAChBC,QAAQ,GAAGD,CAAC;IAChB;IACA,IAAIT,GAAG,GAAG,CAAC;IACX,IAAIY,CAAC,GAAG,CAAC;IACT,KAAK,IAAIjC,CAAC,GAAGc,CAAC,EAAEd,CAAC,GAAG+B,QAAQ,EAAE/B,CAAC,EAAE,EAAE;MAC/BqB,GAAG,IAAIrB,CAAC;MACR,IAAIiC,CAAC,IAAID,QAAQ,EAAE;QACfX,GAAG,IAAIY,CAAC;QACRA,CAAC,EAAE;MACP;IACJ;IACA,OAAQA,CAAC,IAAID,QAAQ,EAAG;MACpBX,GAAG,IAAIY,CAAC;MACRA,CAAC,EAAE;IACP;IACA,OAAOZ,GAAG;EACd,CAAC;EACD,OAAOd,QAAQ;AACnB,CAAC,CAAC,CAAE;AACJ,eAAeA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}