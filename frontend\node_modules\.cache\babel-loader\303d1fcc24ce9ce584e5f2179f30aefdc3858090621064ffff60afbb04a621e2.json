{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"sx\"];\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useViews } from \"../useViews.js\";\nimport { isTimeView } from \"../../utils/time-utils.js\";\n\n/**\n * Props used to handle the views that are common to all pickers.\n */\n\n/**\n * Props used to handle the views of the pickers.\n */\n\n/**\n * Props used to handle the value of the pickers.\n */\n\n/**\n * Manage the views of all the pickers:\n * - Handles the view switch\n * - Handles the switch between UI views and field views\n * - Handles the focus management when switching views\n */\nexport const usePickerViews = ({\n  props,\n  propsFromPickerValue,\n  additionalViewProps,\n  autoFocusView,\n  rendererInterceptor,\n  fieldRef\n}) => {\n  const {\n    onChange,\n    open,\n    onClose\n  } = propsFromPickerValue;\n  const {\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    viewRenderers,\n    timezone\n  } = props;\n  const propsToForwardToView = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    view,\n    setView,\n    defaultView,\n    focusedView,\n    setFocusedView,\n    setValueAndGoToNextView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onChange,\n    onViewChange,\n    autoFocus: autoFocusView\n  });\n  const {\n    hasUIView,\n    viewModeLookup\n  } = React.useMemo(() => views.reduce((acc, viewForReduce) => {\n    let viewMode;\n    if (viewRenderers[viewForReduce] != null) {\n      viewMode = 'UI';\n    } else {\n      viewMode = 'field';\n    }\n    acc.viewModeLookup[viewForReduce] = viewMode;\n    if (viewMode === 'UI') {\n      acc.hasUIView = true;\n    }\n    return acc;\n  }, {\n    hasUIView: false,\n    viewModeLookup: {}\n  }), [viewRenderers, views]);\n  const timeViewsCount = React.useMemo(() => views.reduce((acc, viewForReduce) => {\n    if (viewRenderers[viewForReduce] != null && isTimeView(viewForReduce)) {\n      return acc + 1;\n    }\n    return acc;\n  }, 0), [viewRenderers, views]);\n  const currentViewMode = viewModeLookup[view];\n  const shouldRestoreFocus = useEventCallback(() => currentViewMode === 'UI');\n  const [popperView, setPopperView] = React.useState(currentViewMode === 'UI' ? view : null);\n  if (popperView !== view && viewModeLookup[view] === 'UI') {\n    setPopperView(view);\n  }\n  useEnhancedEffect(() => {\n    // Handle case of `DateTimePicker` without time renderers\n    if (currentViewMode === 'field' && open) {\n      onClose();\n      setTimeout(() => {\n        fieldRef?.current?.setSelectedSections(view);\n        // focusing the input before the range selection is done\n        // calling it outside of timeout results in an inconsistent behavior between Safari And Chrome\n        fieldRef?.current?.focusField(view);\n      });\n    }\n  }, [view]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEnhancedEffect(() => {\n    if (!open) {\n      return;\n    }\n    let newView = view;\n\n    // If the current view is a field view, go to the last popper view\n    if (currentViewMode === 'field' && popperView != null) {\n      newView = popperView;\n    }\n\n    // If the current view is not the default view and both are UI views\n    if (newView !== defaultView && viewModeLookup[newView] === 'UI' && viewModeLookup[defaultView] === 'UI') {\n      newView = defaultView;\n    }\n    if (newView !== view) {\n      setView(newView);\n    }\n    setFocusedView(newView, true);\n  }, [open]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const layoutProps = {\n    views,\n    view: popperView,\n    onViewChange: setView\n  };\n  return {\n    hasUIView,\n    shouldRestoreFocus,\n    layoutProps,\n    renderCurrentView: () => {\n      if (popperView == null) {\n        return null;\n      }\n      const renderer = viewRenderers[popperView];\n      if (renderer == null) {\n        return null;\n      }\n      const rendererProps = _extends({}, propsToForwardToView, additionalViewProps, propsFromPickerValue, {\n        views,\n        timezone,\n        onChange: setValueAndGoToNextView,\n        view: popperView,\n        onViewChange: setView,\n        focusedView,\n        onFocusedViewChange: setFocusedView,\n        showViewSwitcher: timeViewsCount > 1,\n        timeViewsCount\n      });\n      if (rendererInterceptor) {\n        return rendererInterceptor(viewRenderers, popperView, rendererProps);\n      }\n      return renderer(rendererProps);\n    }\n  };\n};", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useEnhancedEffect", "useEventCallback", "useViews", "isTimeView", "usePickerViews", "props", "propsFromPickerValue", "additionalViewProps", "autoFocusView", "rendererInterceptor", "fieldRef", "onChange", "open", "onClose", "view", "inView", "views", "openTo", "onViewChange", "viewRenderers", "timezone", "propsT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>View", "<PERSON><PERSON><PERSON><PERSON>", "defaultView", "focused<PERSON>iew", "setFocusedView", "setValueAndGoToNextView", "autoFocus", "hasUIView", "viewModeLookup", "useMemo", "reduce", "acc", "viewForReduce", "viewMode", "timeViewsCount", "currentViewMode", "shouldRestoreFocus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setPopperView", "useState", "setTimeout", "current", "setSelectedSections", "focusField", "newView", "layoutProps", "renderCurrentView", "renderer", "rendererProps", "onFocusedViewChange", "showViewSwitcher"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/@mui/x-date-pickers/internals/hooks/usePicker/usePickerViews.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"sx\"];\nimport * as React from 'react';\nimport useEnhancedEffect from '@mui/utils/useEnhancedEffect';\nimport useEventCallback from '@mui/utils/useEventCallback';\nimport { useViews } from \"../useViews.js\";\nimport { isTimeView } from \"../../utils/time-utils.js\";\n\n/**\n * Props used to handle the views that are common to all pickers.\n */\n\n/**\n * Props used to handle the views of the pickers.\n */\n\n/**\n * Props used to handle the value of the pickers.\n */\n\n/**\n * Manage the views of all the pickers:\n * - Handles the view switch\n * - Handles the switch between UI views and field views\n * - Handles the focus management when switching views\n */\nexport const usePickerViews = ({\n  props,\n  propsFromPickerValue,\n  additionalViewProps,\n  autoFocusView,\n  rendererInterceptor,\n  fieldRef\n}) => {\n  const {\n    onChange,\n    open,\n    onClose\n  } = propsFromPickerValue;\n  const {\n    view: inView,\n    views,\n    openTo,\n    onViewChange,\n    viewRenderers,\n    timezone\n  } = props;\n  const propsToForwardToView = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    view,\n    setView,\n    defaultView,\n    focusedView,\n    setFocusedView,\n    setValueAndGoToNextView\n  } = useViews({\n    view: inView,\n    views,\n    openTo,\n    onChange,\n    onViewChange,\n    autoFocus: autoFocusView\n  });\n  const {\n    hasUIView,\n    viewModeLookup\n  } = React.useMemo(() => views.reduce((acc, viewForReduce) => {\n    let viewMode;\n    if (viewRenderers[viewForReduce] != null) {\n      viewMode = 'UI';\n    } else {\n      viewMode = 'field';\n    }\n    acc.viewModeLookup[viewForReduce] = viewMode;\n    if (viewMode === 'UI') {\n      acc.hasUIView = true;\n    }\n    return acc;\n  }, {\n    hasUIView: false,\n    viewModeLookup: {}\n  }), [viewRenderers, views]);\n  const timeViewsCount = React.useMemo(() => views.reduce((acc, viewForReduce) => {\n    if (viewRenderers[viewForReduce] != null && isTimeView(viewForReduce)) {\n      return acc + 1;\n    }\n    return acc;\n  }, 0), [viewRenderers, views]);\n  const currentViewMode = viewModeLookup[view];\n  const shouldRestoreFocus = useEventCallback(() => currentViewMode === 'UI');\n  const [popperView, setPopperView] = React.useState(currentViewMode === 'UI' ? view : null);\n  if (popperView !== view && viewModeLookup[view] === 'UI') {\n    setPopperView(view);\n  }\n  useEnhancedEffect(() => {\n    // Handle case of `DateTimePicker` without time renderers\n    if (currentViewMode === 'field' && open) {\n      onClose();\n      setTimeout(() => {\n        fieldRef?.current?.setSelectedSections(view);\n        // focusing the input before the range selection is done\n        // calling it outside of timeout results in an inconsistent behavior between Safari And Chrome\n        fieldRef?.current?.focusField(view);\n      });\n    }\n  }, [view]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  useEnhancedEffect(() => {\n    if (!open) {\n      return;\n    }\n    let newView = view;\n\n    // If the current view is a field view, go to the last popper view\n    if (currentViewMode === 'field' && popperView != null) {\n      newView = popperView;\n    }\n\n    // If the current view is not the default view and both are UI views\n    if (newView !== defaultView && viewModeLookup[newView] === 'UI' && viewModeLookup[defaultView] === 'UI') {\n      newView = defaultView;\n    }\n    if (newView !== view) {\n      setView(newView);\n    }\n    setFocusedView(newView, true);\n  }, [open]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const layoutProps = {\n    views,\n    view: popperView,\n    onViewChange: setView\n  };\n  return {\n    hasUIView,\n    shouldRestoreFocus,\n    layoutProps,\n    renderCurrentView: () => {\n      if (popperView == null) {\n        return null;\n      }\n      const renderer = viewRenderers[popperView];\n      if (renderer == null) {\n        return null;\n      }\n      const rendererProps = _extends({}, propsToForwardToView, additionalViewProps, propsFromPickerValue, {\n        views,\n        timezone,\n        onChange: setValueAndGoToNextView,\n        view: popperView,\n        onViewChange: setView,\n        focusedView,\n        onFocusedViewChange: setFocusedView,\n        showViewSwitcher: timeViewsCount > 1,\n        timeViewsCount\n      });\n      if (rendererInterceptor) {\n        return rendererInterceptor(viewRenderers, popperView, rendererProps);\n      }\n      return renderer(rendererProps);\n    }\n  };\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC;AACrC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,8BAA8B;AAC5D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,UAAU,QAAQ,2BAA2B;;AAEtD;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAC;EAC7BC,KAAK;EACLC,oBAAoB;EACpBC,mBAAmB;EACnBC,aAAa;EACbC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EACJ,MAAM;IACJC,QAAQ;IACRC,IAAI;IACJC;EACF,CAAC,GAAGP,oBAAoB;EACxB,MAAM;IACJQ,IAAI,EAAEC,MAAM;IACZC,KAAK;IACLC,MAAM;IACNC,YAAY;IACZC,aAAa;IACbC;EACF,CAAC,GAAGf,KAAK;EACT,MAAMgB,oBAAoB,GAAGxB,6BAA6B,CAACQ,KAAK,EAAEP,SAAS,CAAC;EAC5E,MAAM;IACJgB,IAAI;IACJQ,OAAO;IACPC,WAAW;IACXC,WAAW;IACXC,cAAc;IACdC;EACF,CAAC,GAAGxB,QAAQ,CAAC;IACXY,IAAI,EAAEC,MAAM;IACZC,KAAK;IACLC,MAAM;IACNN,QAAQ;IACRO,YAAY;IACZS,SAAS,EAAEnB;EACb,CAAC,CAAC;EACF,MAAM;IACJoB,SAAS;IACTC;EACF,CAAC,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,MAAMd,KAAK,CAACe,MAAM,CAAC,CAACC,GAAG,EAAEC,aAAa,KAAK;IAC3D,IAAIC,QAAQ;IACZ,IAAIf,aAAa,CAACc,aAAa,CAAC,IAAI,IAAI,EAAE;MACxCC,QAAQ,GAAG,IAAI;IACjB,CAAC,MAAM;MACLA,QAAQ,GAAG,OAAO;IACpB;IACAF,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,GAAGC,QAAQ;IAC5C,IAAIA,QAAQ,KAAK,IAAI,EAAE;MACrBF,GAAG,CAACJ,SAAS,GAAG,IAAI;IACtB;IACA,OAAOI,GAAG;EACZ,CAAC,EAAE;IACDJ,SAAS,EAAE,KAAK;IAChBC,cAAc,EAAE,CAAC;EACnB,CAAC,CAAC,EAAE,CAACV,aAAa,EAAEH,KAAK,CAAC,CAAC;EAC3B,MAAMmB,cAAc,GAAGpC,KAAK,CAAC+B,OAAO,CAAC,MAAMd,KAAK,CAACe,MAAM,CAAC,CAACC,GAAG,EAAEC,aAAa,KAAK;IAC9E,IAAId,aAAa,CAACc,aAAa,CAAC,IAAI,IAAI,IAAI9B,UAAU,CAAC8B,aAAa,CAAC,EAAE;MACrE,OAAOD,GAAG,GAAG,CAAC;IAChB;IACA,OAAOA,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,EAAE,CAACb,aAAa,EAAEH,KAAK,CAAC,CAAC;EAC9B,MAAMoB,eAAe,GAAGP,cAAc,CAACf,IAAI,CAAC;EAC5C,MAAMuB,kBAAkB,GAAGpC,gBAAgB,CAAC,MAAMmC,eAAe,KAAK,IAAI,CAAC;EAC3E,MAAM,CAACE,UAAU,EAAEC,aAAa,CAAC,GAAGxC,KAAK,CAACyC,QAAQ,CAACJ,eAAe,KAAK,IAAI,GAAGtB,IAAI,GAAG,IAAI,CAAC;EAC1F,IAAIwB,UAAU,KAAKxB,IAAI,IAAIe,cAAc,CAACf,IAAI,CAAC,KAAK,IAAI,EAAE;IACxDyB,aAAa,CAACzB,IAAI,CAAC;EACrB;EACAd,iBAAiB,CAAC,MAAM;IACtB;IACA,IAAIoC,eAAe,KAAK,OAAO,IAAIxB,IAAI,EAAE;MACvCC,OAAO,CAAC,CAAC;MACT4B,UAAU,CAAC,MAAM;QACf/B,QAAQ,EAAEgC,OAAO,EAAEC,mBAAmB,CAAC7B,IAAI,CAAC;QAC5C;QACA;QACAJ,QAAQ,EAAEgC,OAAO,EAAEE,UAAU,CAAC9B,IAAI,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZd,iBAAiB,CAAC,MAAM;IACtB,IAAI,CAACY,IAAI,EAAE;MACT;IACF;IACA,IAAIiC,OAAO,GAAG/B,IAAI;;IAElB;IACA,IAAIsB,eAAe,KAAK,OAAO,IAAIE,UAAU,IAAI,IAAI,EAAE;MACrDO,OAAO,GAAGP,UAAU;IACtB;;IAEA;IACA,IAAIO,OAAO,KAAKtB,WAAW,IAAIM,cAAc,CAACgB,OAAO,CAAC,KAAK,IAAI,IAAIhB,cAAc,CAACN,WAAW,CAAC,KAAK,IAAI,EAAE;MACvGsB,OAAO,GAAGtB,WAAW;IACvB;IACA,IAAIsB,OAAO,KAAK/B,IAAI,EAAE;MACpBQ,OAAO,CAACuB,OAAO,CAAC;IAClB;IACApB,cAAc,CAACoB,OAAO,EAAE,IAAI,CAAC;EAC/B,CAAC,EAAE,CAACjC,IAAI,CAAC,CAAC,CAAC,CAAC;;EAEZ,MAAMkC,WAAW,GAAG;IAClB9B,KAAK;IACLF,IAAI,EAAEwB,UAAU;IAChBpB,YAAY,EAAEI;EAChB,CAAC;EACD,OAAO;IACLM,SAAS;IACTS,kBAAkB;IAClBS,WAAW;IACXC,iBAAiB,EAAEA,CAAA,KAAM;MACvB,IAAIT,UAAU,IAAI,IAAI,EAAE;QACtB,OAAO,IAAI;MACb;MACA,MAAMU,QAAQ,GAAG7B,aAAa,CAACmB,UAAU,CAAC;MAC1C,IAAIU,QAAQ,IAAI,IAAI,EAAE;QACpB,OAAO,IAAI;MACb;MACA,MAAMC,aAAa,GAAGrD,QAAQ,CAAC,CAAC,CAAC,EAAEyB,oBAAoB,EAAEd,mBAAmB,EAAED,oBAAoB,EAAE;QAClGU,KAAK;QACLI,QAAQ;QACRT,QAAQ,EAAEe,uBAAuB;QACjCZ,IAAI,EAAEwB,UAAU;QAChBpB,YAAY,EAAEI,OAAO;QACrBE,WAAW;QACX0B,mBAAmB,EAAEzB,cAAc;QACnC0B,gBAAgB,EAAEhB,cAAc,GAAG,CAAC;QACpCA;MACF,CAAC,CAAC;MACF,IAAI1B,mBAAmB,EAAE;QACvB,OAAOA,mBAAmB,CAACU,aAAa,EAAEmB,UAAU,EAAEW,aAAa,CAAC;MACtE;MACA,OAAOD,QAAQ,CAACC,aAAa,CAAC;IAChC;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}