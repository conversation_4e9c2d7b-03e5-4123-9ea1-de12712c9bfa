{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _Barcode2 = require(\"../Barcode.js\");\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n} // Encoding documentation\n// https://en.wikipedia.org/wiki/MSI_Barcode#Character_set_and_binary_lookup\n\nvar MSI = function (_Barcode) {\n  _inherits(MSI, _Barcode);\n  function MSI(data, options) {\n    _classCallCheck(this, MSI);\n    return _possibleConstructorReturn(this, (MSI.__proto__ || Object.getPrototypeOf(MSI)).call(this, data, options));\n  }\n  _createClass(MSI, [{\n    key: \"encode\",\n    value: function encode() {\n      // Start bits\n      var ret = \"110\";\n      for (var i = 0; i < this.data.length; i++) {\n        // Convert the character to binary (always 4 binary digits)\n        var digit = parseInt(this.data[i]);\n        var bin = digit.toString(2);\n        bin = addZeroes(bin, 4 - bin.length);\n\n        // Add 100 for every zero and 110 for every 1\n        for (var b = 0; b < bin.length; b++) {\n          ret += bin[b] == \"0\" ? \"100\" : \"110\";\n        }\n      }\n\n      // End bits\n      ret += \"1001\";\n      return {\n        data: ret,\n        text: this.text\n      };\n    }\n  }, {\n    key: \"valid\",\n    value: function valid() {\n      return this.data.search(/^[0-9]+$/) !== -1;\n    }\n  }]);\n  return MSI;\n}(_Barcode3.default);\nfunction addZeroes(number, n) {\n  for (var i = 0; i < n; i++) {\n    number = \"0\" + number;\n  }\n  return number;\n}\nexports.default = MSI;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_Barcode2", "require", "_Barcode3", "_interopRequireDefault", "obj", "__esModule", "default", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "call", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "MSI", "_Barcode", "data", "options", "getPrototypeOf", "encode", "ret", "digit", "parseInt", "bin", "toString", "addZeroes", "b", "text", "valid", "search", "number", "n"], "sources": ["D:/My Learning Projects/uni-core-business-suite_v3/frontend/node_modules/jsbarcode/bin/barcodes/MSI/MSI.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n\tvalue: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _Barcode2 = require(\"../Barcode.js\");\n\nvar _Barcode3 = _interopRequireDefault(_Barcode2);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; } // Encoding documentation\n// https://en.wikipedia.org/wiki/MSI_Barcode#Character_set_and_binary_lookup\n\nvar MSI = function (_Barcode) {\n\t_inherits(MSI, _Barcode);\n\n\tfunction MSI(data, options) {\n\t\t_classCallCheck(this, MSI);\n\n\t\treturn _possibleConstructorReturn(this, (MSI.__proto__ || Object.getPrototypeOf(MSI)).call(this, data, options));\n\t}\n\n\t_createClass(MSI, [{\n\t\tkey: \"encode\",\n\t\tvalue: function encode() {\n\t\t\t// Start bits\n\t\t\tvar ret = \"110\";\n\n\t\t\tfor (var i = 0; i < this.data.length; i++) {\n\t\t\t\t// Convert the character to binary (always 4 binary digits)\n\t\t\t\tvar digit = parseInt(this.data[i]);\n\t\t\t\tvar bin = digit.toString(2);\n\t\t\t\tbin = addZeroes(bin, 4 - bin.length);\n\n\t\t\t\t// Add 100 for every zero and 110 for every 1\n\t\t\t\tfor (var b = 0; b < bin.length; b++) {\n\t\t\t\t\tret += bin[b] == \"0\" ? \"100\" : \"110\";\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// End bits\n\t\t\tret += \"1001\";\n\n\t\t\treturn {\n\t\t\t\tdata: ret,\n\t\t\t\ttext: this.text\n\t\t\t};\n\t\t}\n\t}, {\n\t\tkey: \"valid\",\n\t\tvalue: function valid() {\n\t\t\treturn this.data.search(/^[0-9]+$/) !== -1;\n\t\t}\n\t}]);\n\n\treturn MSI;\n}(_Barcode3.default);\n\nfunction addZeroes(number, n) {\n\tfor (var i = 0; i < n; i++) {\n\t\tnumber = \"0\" + number;\n\t}\n\treturn number;\n}\n\nexports.default = MSI;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC5CC,KAAK,EAAE;AACR,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,SAAS,GAAGC,OAAO,CAAC,eAAe,CAAC;AAExC,IAAIC,SAAS,GAAGC,sBAAsB,CAACH,SAAS,CAAC;AAEjD,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASG,eAAeA,CAACC,QAAQ,EAAEZ,WAAW,EAAE;EAAE,IAAI,EAAEY,QAAQ,YAAYZ,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIa,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAI,CAACD,IAAI,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGD,IAAI;AAAE;AAE/O,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIP,SAAS,CAAC,0DAA0D,GAAG,OAAOO,UAAU,CAAC;EAAE;EAAED,QAAQ,CAAChB,SAAS,GAAGlB,MAAM,CAACoC,MAAM,CAACD,UAAU,IAAIA,UAAU,CAACjB,SAAS,EAAE;IAAEmB,WAAW,EAAE;MAAElC,KAAK,EAAE+B,QAAQ;MAAEvB,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIuB,UAAU,EAAEnC,MAAM,CAACsC,cAAc,GAAGtC,MAAM,CAACsC,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AAAE,CAAC,CAAC;AAC/e;;AAEA,IAAIK,GAAG,GAAG,UAAUC,QAAQ,EAAE;EAC7BR,SAAS,CAACO,GAAG,EAAEC,QAAQ,CAAC;EAExB,SAASD,GAAGA,CAACE,IAAI,EAAEC,OAAO,EAAE;IAC3BjB,eAAe,CAAC,IAAI,EAAEc,GAAG,CAAC;IAE1B,OAAOX,0BAA0B,CAAC,IAAI,EAAE,CAACW,GAAG,CAACD,SAAS,IAAIvC,MAAM,CAAC4C,cAAc,CAACJ,GAAG,CAAC,EAAET,IAAI,CAAC,IAAI,EAAEW,IAAI,EAAEC,OAAO,CAAC,CAAC;EACjH;EAEAvC,YAAY,CAACoC,GAAG,EAAE,CAAC;IAClB1B,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAAS0C,MAAMA,CAAA,EAAG;MACxB;MACA,IAAIC,GAAG,GAAG,KAAK;MAEf,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACkC,IAAI,CAACjC,MAAM,EAAED,CAAC,EAAE,EAAE;QAC1C;QACA,IAAIuC,KAAK,GAAGC,QAAQ,CAAC,IAAI,CAACN,IAAI,CAAClC,CAAC,CAAC,CAAC;QAClC,IAAIyC,GAAG,GAAGF,KAAK,CAACG,QAAQ,CAAC,CAAC,CAAC;QAC3BD,GAAG,GAAGE,SAAS,CAACF,GAAG,EAAE,CAAC,GAAGA,GAAG,CAACxC,MAAM,CAAC;;QAEpC;QACA,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACxC,MAAM,EAAE2C,CAAC,EAAE,EAAE;UACpCN,GAAG,IAAIG,GAAG,CAACG,CAAC,CAAC,IAAI,GAAG,GAAG,KAAK,GAAG,KAAK;QACrC;MACD;;MAEA;MACAN,GAAG,IAAI,MAAM;MAEb,OAAO;QACNJ,IAAI,EAAEI,GAAG;QACTO,IAAI,EAAE,IAAI,CAACA;MACZ,CAAC;IACF;EACD,CAAC,EAAE;IACFvC,GAAG,EAAE,OAAO;IACZX,KAAK,EAAE,SAASmD,KAAKA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACZ,IAAI,CAACa,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC3C;EACD,CAAC,CAAC,CAAC;EAEH,OAAOf,GAAG;AACX,CAAC,CAACnB,SAAS,CAACI,OAAO,CAAC;AAEpB,SAAS0B,SAASA,CAACK,MAAM,EAAEC,CAAC,EAAE;EAC7B,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiD,CAAC,EAAEjD,CAAC,EAAE,EAAE;IAC3BgD,MAAM,GAAG,GAAG,GAAGA,MAAM;EACtB;EACA,OAAOA,MAAM;AACd;AAEAtD,OAAO,CAACuB,OAAO,GAAGe,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}